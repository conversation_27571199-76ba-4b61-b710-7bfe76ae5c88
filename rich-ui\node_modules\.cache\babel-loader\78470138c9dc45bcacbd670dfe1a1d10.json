{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\psarct.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\psarct.js", "mtime": 1719914965838}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPsarct", "query", "request", "url", "method", "params", "getPsarct", "psaRctId", "addPsarct", "data", "updatePsarct", "delPsarct", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/psarct.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询商务订舱列表\r\nexport function listPsarct(query) {\r\n  return request({\r\n    url: '/system/psarct/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询商务订舱详细\r\nexport function getPsarct(psaRctId) {\r\n  return request({\r\n    url: '/system/psarct/' + psaRctId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增商务订舱\r\nexport function addPsarct(data) {\r\n  return request({\r\n    url: '/system/psarct',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改商务订舱\r\nexport function updatePsarct(data) {\r\n  return request({\r\n    url: '/system/psarct',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除商务订舱\r\nexport function delPsarct(psaRctId) {\r\n  return request({\r\n    url: '/system/psarct/' + psaRctId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(psaRctId, status) {\r\n  const data = {\r\n    psaRctId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/psarct/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,SAASA,CAACC,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,SAASA,CAACC,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACD,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,SAASA,CAACJ,QAAQ,EAAE;EAClC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB,GAAGI,QAAQ;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,QAAQ,EAAEM,MAAM,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXF,QAAQ,EAARA,QAAQ;IACRM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}