{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\carrier\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\carrier\\index.vue", "mtime": 1722505520259}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_carrier", "require", "name", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "carrierList", "title", "open", "queryParams", "pageNum", "pageSize", "carrierQuery", "form", "rules", "carrierShortName", "required", "trigger", "watch", "n", "created", "getList", "methods", "_this", "listCarrier", "then", "response", "rows", "cancel", "reset", "carrierId", "carrierIntlCode", "carrierEnName", "carrierLocalName", "trackingWebsite", "locationId", "orderNum", "status", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "get<PERSON>arrier", "serviceTypeIds", "$refs", "location", "remoteMethod", "locationLocalName", "submitForm", "_this3", "validate", "valid", "updateCarrier", "$modal", "msgSuccess", "addCarrier", "handleDelete", "_this4", "carrierIds", "$confirm", "customClass", "del<PERSON><PERSON><PERSON>", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleStatusChange", "_this5", "text", "changeStatus", "cargoTypeId", "getServiceTypeIds", "val", "queryServiceTypeId", "serviceTypeId", "getLocationId", "exports", "_default"], "sources": ["src/views/system/carrier/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"carrierQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.carrierQuery\"\r\n              clearable\r\n              placeholder=\"中英文/简称\"\r\n              style=\"width: 158px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"服务\" prop=\"serviceTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"queryParams.serviceTypeIds\"\r\n                         :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                         style=\"width: 100%\" @return=\"queryServiceTypeId\"/>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:carrier:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:carrier:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:carrier:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"carrierList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column align=\"left\" type=\"selection\" width=\"39px\"/>\r\n          <el-table-column align=\"center\" label=\"通用编码\" prop=\"carrierIntlCode\" width=\"68\"/>\r\n          <el-table-column align=\"left\" label=\"类型名称\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.carrierShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.carrierLocalName }}</a>\r\n              {{ scope.row.carrierEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column key=\"serviceType\" align=\"left\" label=\"服务类型\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip placement=\"top\">\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0;\">\r\n                    {{\r\n                      (scope.row.serviceType != null ? scope.row.serviceType : '')\r\n                    }}\r\n                  </h6>\r\n                </div>\r\n                <div>\r\n                  <h6 style=\"margin: 0;overflow:hidden;text-overflow: ellipsis;\">\r\n                    {{\r\n                      (scope.row.serviceType != null ? scope.row.serviceType : '').substring(0, 33) + ((scope.row.serviceType != null ? scope.row.serviceType : '').length > 33 ? '...' : '')\r\n                    }}\r\n                  </h6></div>\r\n              </el-tooltip>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货物查询追踪网址\" prop=\"trackingWebsite\"/>\r\n          <el-table-column align=\"center\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"区域\" prop=\"location\"/>\r\n          <el-table-column align=\"center\" label=\"排序\" prop=\"orderNum\"/>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:carrier:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:carrier:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改船公司对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\" class=\"edit\">\r\n        <el-form-item label=\"国际通用编码\" prop=\"carrierIntlCode\">\r\n          <el-input v-model=\"form.carrierIntlCode\" placeholder=\"主要承运人国际通用编码\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"简称\" prop=\"carrierShortName\">\r\n          <el-input v-model=\"form.carrierShortName\" placeholder=\"承运人简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"carrierLocalName\">\r\n          <el-input v-model=\"form.carrierLocalName\" placeholder=\"中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"carrierEnName\">\r\n          <el-input v-model=\"form.carrierEnName\" placeholder=\"英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"支持服务类型\" label-width=\"100px\">\r\n          <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.serviceTypeIds\" :type=\"'serviceType'\"\r\n                       @return=\"getServiceTypeIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"货物追踪网址\" prop=\"trackingWebsite\">\r\n          <el-input v-model=\"form.trackingWebsite\" placeholder=\"货物查询追踪网址\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"区域\" prop=\"locationId\">\r\n          <location-select ref=\"location\" :pass=\"form.locationId\" :type=\"'location'\"\r\n                           @return=\"getLocationId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" style=\"width: 100%\" v-model=\"form.orderNum\" placeholder=\"排序\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addCarrier, changeStatus, delCarrier, getCarrier, listCarrier, updateCarrier} from \"@/api/system/carrier\";\r\n\r\nexport default {\r\n  name: \"Carrier\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 船公司表格数据\r\n      carrierList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        carrierQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        carrierShortName: [\r\n          {required: true,  trigger: \"blur\"}\r\n        ],\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询船公司列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCarrier(this.queryParams).then(response => {\r\n        this.carrierList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        carrierId: null,\r\n        carrierIntlCode: null,\r\n        carrierShortName: null,\r\n        carrierEnName: null,\r\n        carrierLocalName: null,\r\n        trackingWebsite: null,\r\n        locationId: null,\r\n        orderNum: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: 0\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.carrierId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加船公司\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const carrierId = row.carrierId || this.ids\r\n      getCarrier(carrierId).then(response => {\r\n        this.form = response.data;\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.open = true;\r\n        this.title = \"修改船公司\";\r\n        this.$refs.location.remoteMethod(row.locationLocalName)\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.carrierId != null) {\r\n            updateCarrier(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCarrier(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const carrierIds = row.carrierId || this.ids;\r\n      this.$confirm('是否确认删除船公司编号为\"' + carrierIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCarrier(carrierIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/carrier/export', {\r\n        ...this.queryParams\r\n      }, `carrier_${new Date().getTime()}.xlsx`)\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要修改状态吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.cargoTypeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    getServiceTypeIds(val) {\r\n      this.form.serviceTypeIds = val\r\n    },\r\n    queryServiceTypeId(val) {\r\n      this.queryParams.serviceTypeId = val\r\n    },\r\n    getLocationId(val) {\r\n      this.form.locationId = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;AAuLA,IAAAA,QAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,gBAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,KAAA;IACAd,UAAA,WAAAA,WAAAe,CAAA;MACA,IAAAA,CAAA;QACA,KAAApB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAsB,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,cACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAvB,OAAA;MACA,IAAAwB,oBAAA,OAAAf,WAAA,EAAAgB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAjB,WAAA,GAAAoB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAlB,KAAA,GAAAqB,QAAA,CAAArB,KAAA;QACAkB,KAAA,CAAAvB,OAAA;MACA;IACA;IACA;IACA4B,MAAA,WAAAA,OAAA;MACA,KAAApB,IAAA;MACA,KAAAqB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAhB,IAAA;QACAiB,SAAA;QACAC,eAAA;QACAhB,gBAAA;QACAiB,aAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAC,OAAA;MACA,KAAAW,OAAA;IACA;IACA,aACA2B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjD,GAAA,GAAAiD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAtB,SAAA;MAAA;MACA,KAAA5B,MAAA,GAAAgD,SAAA,CAAAG,MAAA;MACA,KAAAlD,QAAA,IAAA+C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAzB,KAAA;MACA,KAAArB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA5B,KAAA;MACA,IAAAC,SAAA,GAAA0B,GAAA,CAAA1B,SAAA,SAAA7B,GAAA;MACA,IAAAyD,mBAAA,EAAA5B,SAAA,EAAAL,IAAA,WAAAC,QAAA;QACA+B,MAAA,CAAA5C,IAAA,GAAAa,QAAA,CAAA7B,IAAA;QACA4D,MAAA,CAAA5C,IAAA,CAAA8C,cAAA,GAAAjC,QAAA,CAAAiC,cAAA;QACAF,MAAA,CAAAjD,IAAA;QACAiD,MAAA,CAAAlD,KAAA;QACAkD,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAC,YAAA,CAAAN,GAAA,CAAAO,iBAAA;MACA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAL,KAAA,SAAAM,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAApD,IAAA,CAAAiB,SAAA;YACA,IAAAsC,sBAAA,EAAAH,MAAA,CAAApD,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAAzD,IAAA;cACAyD,MAAA,CAAA5C,OAAA;YACA;UACA;YACA,IAAAkD,mBAAA,EAAAN,MAAA,CAAApD,IAAA,EAAAY,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAAzD,IAAA;cACAyD,MAAA,CAAA5C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAmD,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,IAAAC,UAAA,GAAAlB,GAAA,CAAA1B,SAAA,SAAA7B,GAAA;MACA,KAAA0E,QAAA,mBAAAD,UAAA;QAAAE,WAAA;MAAA,GAAAnD,IAAA;QACA,WAAAoD,mBAAA,EAAAH,UAAA;MACA,GAAAjD,IAAA;QACAgD,MAAA,CAAApD,OAAA;QACAoD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,8BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAzE,WAAA,cAAA0E,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,IAAAC,IAAA,GAAAhC,GAAA,CAAAnB,MAAA;MACA,KAAAsC,QAAA;QAAAC,WAAA;MAAA,GAAAnD,IAAA;QACA,WAAAgE,qBAAA,EAAAjC,GAAA,CAAAkC,WAAA,EAAAlC,GAAA,CAAAnB,MAAA;MACA,GAAAZ,IAAA;QACA8D,MAAA,CAAAlB,MAAA,CAAAC,UAAA,CAAAkB,IAAA;MACA,GAAAV,KAAA;QACAtB,GAAA,CAAAnB,MAAA,GAAAmB,GAAA,CAAAnB,MAAA;MACA;IACA;IACAsD,iBAAA,WAAAA,kBAAAC,GAAA;MACA,KAAA/E,IAAA,CAAA8C,cAAA,GAAAiC,GAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAD,GAAA;MACA,KAAAnF,WAAA,CAAAqF,aAAA,GAAAF,GAAA;IACA;IACAG,aAAA,WAAAA,cAAAH,GAAA;MACA,KAAA/E,IAAA,CAAAsB,UAAA,GAAAyD,GAAA;IACA;EACA;AACA;AAAAI,OAAA,CAAAd,OAAA,GAAAe,QAAA"}]}