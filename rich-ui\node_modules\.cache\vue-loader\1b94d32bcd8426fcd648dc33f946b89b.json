{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\airfreight\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\airfreight\\index.vue", "mtime": 1754876882573}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQppbXBvcnQgZnJlaWdodCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9mcmVpZ2h0IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQWlyZnJlaWdodCIsDQogIGNvbXBvbmVudHM6IHtmcmVpZ2h0fSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdHlwZUlkOiAnMicNCiAgICB9Ow0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/airfreight", "sourcesContent": ["<template>\r\n  <freight :typeId=\"typeId\"/>\r\n</template>\r\n\r\n<script>\r\nimport freight from \"@/views/system/freight\";\r\n\r\nexport default {\r\n  name: \"Airfreight\",\r\n  components: {freight},\r\n  data() {\r\n    return {\r\n      typeId: '2'\r\n    };\r\n  },\r\n}\r\n</script>\r\n"]}]}