{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\dict\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\dict\\index.vue", "mtime": 1743675026894}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_type", "require", "name", "dicts", "data", "loading", "ids", "single", "multiple", "showSearch", "total", "typeList", "title", "open", "date<PERSON><PERSON><PERSON>", "queryParams", "pageNum", "pageSize", "dictName", "undefined", "dictType", "status", "form", "rules", "required", "trigger", "created", "getList", "methods", "_this", "listType", "addDateRange", "then", "response", "rows", "cancel", "reset", "dictTypeId", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "handleSelectionChange", "selection", "map", "item", "length", "handleUpdate", "row", "_this2", "getType", "submitForm", "_this3", "$refs", "validate", "valid", "updateType", "$modal", "msgSuccess", "addType", "handleDelete", "_this4", "dictTypeIds", "$confirm", "customClass", "delType", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleRefreshCache", "_this5", "refreshCache", "$store", "dispatch", "exports", "_default"], "sources": ["src/views/system/dict/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" label-width=\"68px\" size=\"mini\">\r\n      <el-form-item label=\"字典名称\" prop=\"dictName\">\r\n        <el-input\r\n          v-model=\"queryParams.dictName\"\r\n          clearable\r\n          placeholder=\"字典名称\"\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"字典类型\" prop=\"dictType\">\r\n        <el-input\r\n          v-model=\"queryParams.dictType\"\r\n          clearable\r\n          placeholder=\"字典类型\"\r\n          style=\"width: 240px\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          clearable\r\n          placeholder=\"字典状态\"\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_normal_disable\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"创建时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          end-placeholder=\"结束日期\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          style=\"width: 240px\"\r\n          default-time=\"['00:00:00', '23:59:59']\"\r\n          type=\"daterange\"\r\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['system:dict:add']\"\r\n          icon=\"el-icon-plus\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"primary\"\r\n          @click=\"handleAdd\"\r\n        >新增\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['system:dict:edit']\"\r\n          :disabled=\"single\"\r\n          icon=\"el-icon-edit\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"success\"\r\n          @click=\"handleUpdate\"\r\n        >修改\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['system:dict:remove']\"\r\n          :disabled=\"multiple\"\r\n          icon=\"el-icon-delete\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"danger\"\r\n          @click=\"handleDelete\"\r\n        >删除\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['system:dict:export']\"\r\n          icon=\"el-icon-download\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"warning\"\r\n          @click=\"handleExport\"\r\n        >导出\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['system:dict:remove']\"\r\n          icon=\"el-icon-refresh\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"danger\"\r\n          @click=\"handleRefreshCache\"\r\n        >刷新缓存\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table v-loading=\"loading\" :data=\"typeList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n      <el-table-column align=\"center\" label=\"字典编号\" prop=\"dictTypeId\"/>\r\n      <el-table-column :show-tooltip-when-overflow=\"true\" align=\"center\" label=\"字典名称\" prop=\"dictName\"/>\r\n      <el-table-column :show-tooltip-when-overflow=\"true\" align=\"center\" label=\"字典类型\">\r\n        <template slot-scope=\"scope\">\r\n          <router-link :to=\"'/system/dict-data/index/' + scope.row.dictTypeId\" class=\"link-type\">\r\n            <span>{{ scope.row.dictType }}</span>\r\n          </router-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" label=\"状态\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column :show-tooltip-when-overflow=\"true\" align=\"center\" label=\"备注\" prop=\"remark\"/>\r\n      <el-table-column align=\"center\" label=\"创建时间\" prop=\"createTime\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.createTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            v-hasPermi=\"['system:dict:edit']\"\r\n            icon=\"el-icon-edit\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改\r\n          </el-button>\r\n          <el-button\r\n            v-hasPermi=\"['system:dict:remove']\"\r\n            icon=\"el-icon-delete\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :total=\"total\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改参数配置对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"字典名称\" prop=\"dictName\">\r\n          <el-input v-model=\"form.dictName\" placeholder=\"字典名称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"字典类型\" prop=\"dictType\">\r\n          <el-input v-model=\"form.dictType\" placeholder=\"字典类型\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"状态\" prop=\"status\">\r\n          <el-radio-group v-model=\"form.status\">\r\n            <el-radio\r\n              v-for=\"dict in dict.type.sys_normal_disable\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.value\"\r\n            >{{ dict.label }}\r\n            </el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\" placeholder=\"内容\"\r\n                    show-word-limit type=\"textarea\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addType, delType, getType, listType, refreshCache, updateType} from \"@/api/system/dict/type\";\r\n\r\nexport default {\r\n  name: \"Dict\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 字典表格数据\r\n      typeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        dictName: undefined,\r\n        dictType: undefined,\r\n        status: undefined\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        dictName: [\r\n          {required: true,trigger: \"blur\"}\r\n        ],\r\n        dictType: [\r\n          {required: true,  trigger: \"blur\"}\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询字典类型列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listType(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.typeList = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        dictTypeId: undefined,\r\n        dictName: undefined,\r\n        dictType: undefined,\r\n        status: \"0\",\r\n        remark: undefined\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加字典类型\";\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.dictTypeId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const dictTypeId = row.dictTypeId || this.ids\r\n      getType(dictTypeId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改字典类型\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function () {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.dictTypeId != undefined) {\r\n            updateType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const dictTypeIds = row.dictTypeId || this.ids;\r\n      this.$confirm('是否确认删除字典编号为\"' + dictTypeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delType(dictTypeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/dict/type/export', {\r\n        ...this.queryParams\r\n      }, `type_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 刷新缓存按钮操作 */\r\n    handleRefreshCache() {\r\n      refreshCache().then(() => {\r\n        this.$modal.msgSuccess(\"刷新成功\");\r\n        this.$store.dispatch('dict/cleanDict');\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;AA0MA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA,EAAAF;MACA;MACA;MACAG,IAAA;MACA;MACAC,KAAA;QACAL,QAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;QAAA,EACA;QACAL,QAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,cAAA,OAAAC,YAAA,MAAAhB,WAAA,OAAAD,SAAA,GAAAkB,IAAA,WAAAC,QAAA;QACAJ,KAAA,CAAAlB,QAAA,GAAAsB,QAAA,CAAAC,IAAA;QACAL,KAAA,CAAAnB,KAAA,GAAAuB,QAAA,CAAAvB,KAAA;QACAmB,KAAA,CAAAxB,OAAA;MACA,CACA;IACA;IACA;IACA8B,MAAA,WAAAA,OAAA;MACA,KAAAtB,IAAA;MACA,KAAAuB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAd,IAAA;QACAe,UAAA,EAAAlB,SAAA;QACAD,QAAA,EAAAC,SAAA;QACAC,QAAA,EAAAD,SAAA;QACAE,MAAA;QACAiB,MAAA,EAAAnB;MACA;MACA,KAAAoB,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAzB,WAAA,CAAAC,OAAA;MACA,KAAAW,OAAA;IACA;IACA,aACAc,UAAA,WAAAA,WAAA;MACA,KAAA3B,SAAA;MACA,KAAAyB,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAA;MACA,KAAAN,KAAA;MACA,KAAAvB,IAAA;MACA,KAAAD,KAAA;IACA;IACA;IACA+B,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtC,GAAA,GAAAsC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAT,UAAA;MAAA;MACA,KAAA9B,MAAA,GAAAqC,SAAA,CAAAG,MAAA;MACA,KAAAvC,QAAA,IAAAoC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAd,KAAA;MACA,IAAAC,UAAA,GAAAY,GAAA,CAAAZ,UAAA,SAAA/B,GAAA;MACA,IAAA6C,aAAA,EAAAd,UAAA,EAAAL,IAAA,WAAAC,QAAA;QACAiB,MAAA,CAAA5B,IAAA,GAAAW,QAAA,CAAA7B,IAAA;QACA8C,MAAA,CAAArC,IAAA;QACAqC,MAAA,CAAAtC,KAAA;MACA;IACA;IACA;IACAwC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/B,IAAA,CAAAe,UAAA,IAAAlB,SAAA;YACA,IAAAsC,gBAAA,EAAAJ,MAAA,CAAA/B,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAoB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAA1B,OAAA;YACA;UACA;YACA,IAAAiC,aAAA,EAAAP,MAAA,CAAA/B,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAoB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAxC,IAAA;cACAwC,MAAA,CAAA1B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAkC,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,WAAA,GAAAd,GAAA,CAAAZ,UAAA,SAAA/B,GAAA;MACA,KAAA0D,QAAA,kBAAAD,WAAA;QAAAE,WAAA;MAAA,GAAAjC,IAAA;QACA,WAAAkC,aAAA,EAAAH,WAAA;MACA,GAAA/B,IAAA;QACA8B,MAAA,CAAAnC,OAAA;QACAmC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,gCAAAC,cAAA,CAAAC,OAAA,MACA,KAAAxD,WAAA,WAAAyD,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA,eACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,kBAAA,IAAA7C,IAAA;QACA4C,MAAA,CAAAlB,MAAA,CAAAC,UAAA;QACAiB,MAAA,CAAAE,MAAA,CAAAC,QAAA;MACA;IACA;EACA;AACA;AAAAC,OAAA,CAAAT,OAAA,GAAAU,QAAA"}]}