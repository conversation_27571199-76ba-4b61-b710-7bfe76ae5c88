{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\logininfor\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\logininfor\\index.vue", "mtime": 1754876882566}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2NsZWFuTG9naW5pbmZvciwgZGVsTG9naW5pbmZvciwgbGlzdH0gZnJvbSAiQC9hcGkvbW9uaXRvci9sb2dpbmluZm9yIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiTG9naW5pbmZvciIsDQogIGRpY3RzOiBbJ3N5c19jb21tb25fc3RhdHVzJ10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6KGo5qC85pWw5o2uDQogICAgICBsaXN0OiBbXSwNCiAgICAgIC8vIOaXpeacn+iMg+WbtA0KICAgICAgZGF0ZVJhbmdlOiBbXSwNCiAgICAgIC8vIOm7mOiupOaOkuW6jw0KICAgICAgZGVmYXVsdFNvcnQ6IHtwcm9wOiAnbG9naW5UaW1lJywgb3JkZXI6ICdkZXNjZW5kaW5nJ30sDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAxMCwNCiAgICAgICAgaXBhZGRyOiB1bmRlZmluZWQsDQogICAgICAgIHVzZXJOYW1lOiB1bmRlZmluZWQsDQogICAgICAgIHN0YXR1czogdW5kZWZpbmVkDQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6Xor6LnmbvlvZXml6Xlv5fliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3QodGhpcy5hZGREYXRlUmFuZ2UodGhpcy5xdWVyeVBhcmFtcywgdGhpcy5kYXRlUmFuZ2UpKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5kYXRlUmFuZ2UgPSBbXTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuJHJlZnMudGFibGVzLnNvcnQodGhpcy5kZWZhdWx0U29ydC5wcm9wLCB0aGlzLmRlZmF1bHRTb3J0Lm9yZGVyKQ0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICB9LA0KICAgIC8qKiDlpJrpgInmoYbpgInkuK3mlbDmja4gKi8NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmluZm9JZCkNCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaOkuW6j+inpuWPkeS6i+S7tiAqLw0KICAgIGhhbmRsZVNvcnRDaGFuZ2UoY29sdW1uLCBwcm9wLCBvcmRlcikgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5vcmRlckJ5Q29sdW1uID0gY29sdW1uLnByb3A7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzQXNjID0gY29sdW1uLm9yZGVyOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgaW5mb0lkcyA9IHJvdy5pbmZvSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTorr/pl67nvJblj7fkuLoiJyArIGluZm9JZHMgKyAnIueahOaVsOaNrumhue+8nycsICfmj5DnpLonLCB7Y3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJ30pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gZGVsTG9naW5pbmZvcihpbmZvSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmuIXnqbrmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVDbGVhbigpIHsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOa4heepuuaJgOacieeZu+W9leaXpeW/l+aVsOaNrumhue+8nycsICfmj5DnpLonLCB7Y3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJ30pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gY2xlYW5Mb2dpbmluZm9yKCk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heepuuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnbW9uaXRvci9sb2dpbmluZm9yL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYGxvZ2luaW5mb3JfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfQ0KICB9DQp9Ow0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgIA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/logininfor", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" label-width=\"68px\" size=\"mini\">\r\n      <el-form-item label=\"登录地址\" prop=\"ipaddr\">\r\n        <el-input\r\n          v-model=\"queryParams.ipaddr\"\r\n          clearable\r\n          placeholder=\"登录地址\"\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"用户名称\" prop=\"userName\">\r\n        <el-input\r\n          v-model=\"queryParams.userName\"\r\n          clearable\r\n          placeholder=\"用户名称\"\r\n          style=\"width: 240px;\"\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item label=\"状态\" prop=\"status\">\r\n        <el-select\r\n          v-model=\"queryParams.status\"\r\n          clearable\r\n          placeholder=\"登录状态\"\r\n          style=\"width: 240px\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in dict.type.sys_common_status\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"登录时间\">\r\n        <el-date-picker\r\n          v-model=\"dateRange\"\r\n          style=\"width: 240px\"\r\n          value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n          type=\"daterange\"\r\n          range-separator=\"-\"\r\n          start-placeholder=\"开始日期\"\r\n          end-placeholder=\"结束日期\"\r\n          :default-time=\"['00:00:00', '23:59:59']\"\r\n        ></el-date-picker>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['monitor:logininfor:remove']\"\r\n          :disabled=\"multiple\"\r\n          icon=\"el-icon-delete\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"danger\"\r\n          @click=\"handleDelete\"\r\n        >删除\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['monitor:logininfor:remove']\"\r\n          icon=\"el-icon-delete\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"danger\"\r\n          @click=\"handleClean\"\r\n        >清空\r\n        </el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          v-hasPermi=\"['monitor:logininfor:export']\"\r\n          icon=\"el-icon-download\"\r\n          plain\r\n          size=\"mini\"\r\n          type=\"warning\"\r\n          @click=\"handleExport\"\r\n        >导出\r\n        </el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table ref=\"tables\" v-loading=\"loading\" :data=\"list\" :default-sort=\"defaultSort\"\r\n              @selection-change=\"handleSelectionChange\" @sort-change=\"handleSortChange\">\r\n      <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n      <el-table-column align=\"center\" label=\"访问编号\" prop=\"infoId\"/>\r\n      <el-table-column :show-tooltip-when-overflow=\"true\" :sort-orders=\"['descending', 'ascending']\" align=\"center\"\r\n                       label=\"用户名称\" prop=\"userName\"\r\n                       sortable=\"custom\"/>\r\n      <el-table-column :show-tooltip-when-overflow=\"true\" align=\"center\" label=\"登录地址\" prop=\"ipaddr\" width=\"130\"/>\r\n      <el-table-column :show-tooltip-when-overflow=\"true\" align=\"center\" label=\"登录地点\" prop=\"loginLocation\"/>\r\n      <el-table-column :show-tooltip-when-overflow=\"true\" align=\"center\" label=\"浏览器\" prop=\"browser\"/>\r\n      <el-table-column align=\"center\" label=\"操作系统\" prop=\"os\"/>\r\n      <el-table-column align=\"center\" label=\"登录状态\" prop=\"status\">\r\n        <template slot-scope=\"scope\">\r\n          <dict-tag :options=\"dict.type.sys_common_status\" :value=\"scope.row.status\"/>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column align=\"center\" label=\"操作信息\" prop=\"msg\"/>\r\n      <el-table-column :sort-orders=\"['descending', 'ascending']\" align=\"center\" label=\"登录日期\" prop=\"loginTime\"\r\n                       sortable=\"custom\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <span>{{ parseTime(scope.row.loginTime) }}</span>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :total=\"total\"\r\n      @pagination=\"getList\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {cleanLogininfor, delLogininfor, list} from \"@/api/monitor/logininfor\";\r\n\r\nexport default {\r\n  name: \"Logininfor\",\r\n  dicts: ['sys_common_status'],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 表格数据\r\n      list: [],\r\n      // 日期范围\r\n      dateRange: [],\r\n      // 默认排序\r\n      defaultSort: {prop: 'loginTime', order: 'descending'},\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        ipaddr: undefined,\r\n        userName: undefined,\r\n        status: undefined\r\n      }\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询登录日志列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      list(this.addDateRange(this.queryParams, this.dateRange)).then(response => {\r\n          this.list = response.rows;\r\n          this.total = response.total;\r\n          this.loading = false;\r\n        }\r\n      );\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.dateRange = [];\r\n      this.resetForm(\"queryForm\");\r\n      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)\r\n      this.queryParams.pageNum = 1;\r\n    },\r\n    /** 多选框选中数据 */\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.infoId)\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 排序触发事件 */\r\n    handleSortChange(column, prop, order) {\r\n      this.queryParams.orderByColumn = column.prop;\r\n      this.queryParams.isAsc = column.order;\r\n      this.getList();\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const infoIds = row.infoId || this.ids;\r\n      this.$confirm('是否确认删除访问编号为\"' + infoIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delLogininfor(infoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 清空按钮操作 */\r\n    handleClean() {\r\n      this.$confirm('是否确认清空所有登录日志数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return cleanLogininfor();\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"清空成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('monitor/logininfor/export', {\r\n        ...this.queryParams\r\n      }, `logininfor_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n"]}]}