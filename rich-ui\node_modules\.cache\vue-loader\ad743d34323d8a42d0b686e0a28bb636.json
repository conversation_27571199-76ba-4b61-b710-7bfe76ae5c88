{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\Logo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\Logo.vue", "mtime": 1754876882543}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbG9nb0ltZyBmcm9tICdAL2Fzc2V0cy9sb2dvL2xvZ28ucG5nJw0KaW1wb3J0IHZhcmlhYmxlcyBmcm9tICdAL2Fzc2V0cy9zdHlsZXMvdmFyaWFibGVzLnNjc3MnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ1NpZGViYXJMb2dvJywNCiAgcHJvcHM6IHsNCiAgICBjb2xsYXBzZTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHZhcmlhYmxlcygpIHsNCiAgICAgIHJldHVybiB2YXJpYWJsZXM7DQogICAgfSwNCiAgICBzaWRlVGhlbWUoKSB7DQogICAgICByZXR1cm4gdGhpcy4kc3RvcmUuc3RhdGUuc2V0dGluZ3Muc2lkZVRoZW1lDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0aXRsZTogJ+eRnuaXl+ezu+e7nycsDQogICAgICBsb2dvOiBsb2dvSW1nDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["Logo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Logo.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div :class=\"{'collapse':collapse}\" :style=\"{ backgroundColor: sideTheme == 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\"\r\n       class=\"sidebar-logo-container\">\r\n    <transition name=\"sidebarLogoFade\">\r\n      <router-link v-if=\"collapse\" key=\"collapse\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\"/>\r\n        <h1 v-else :style=\"{ color: sideTheme == 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\"\r\n            class=\"sidebar-title\">\r\n          {{ title }} </h1>\r\n      </router-link>\r\n      <router-link v-else key=\"expand\" class=\"sidebar-logo-link\" to=\"/\">\r\n        <img v-if=\"logo\" :src=\"logo\" class=\"sidebar-logo\"/>\r\n        <h1 :style=\"{ color: sideTheme == 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor }\"\r\n            class=\"sidebar-title\">\r\n          {{ title }} </h1>\r\n      </router-link>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport logoImg from '@/assets/logo/logo.png'\r\nimport variables from '@/assets/styles/variables.scss'\r\n\r\nexport default {\r\n  name: 'SidebarLogo',\r\n  props: {\r\n    collapse: {\r\n      type: Boolean,\r\n      required: true\r\n    }\r\n  },\r\n  computed: {\r\n    variables() {\r\n      return variables;\r\n    },\r\n    sideTheme() {\r\n      return this.$store.state.settings.sideTheme\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      title: '瑞旗系统',\r\n      logo: logoImg\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.sidebarLogoFade-enter-active {\r\n  transition: opacity 1.5s;\r\n}\r\n\r\n.sidebarLogoFade-enter,\r\n.sidebarLogoFade-leave-to {\r\n  opacity: 0;\r\n}\r\n\r\n.sidebar-logo-container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 50px;\r\n  line-height: 50px;\r\n  background: #2b2f3a;\r\n  text-align: center;\r\n  overflow: hidden;\r\n\r\n  & .sidebar-logo-link {\r\n    height: 100%;\r\n    width: 100%;\r\n\r\n    & .sidebar-logo {\r\n      height: 32px;\r\n      vertical-align: middle;\r\n      margin-right: 12px;\r\n    }\r\n\r\n    & .sidebar-title {\r\n      display: inline-block;\r\n      margin: 0;\r\n      color: #fff;\r\n      font-weight: 600;\r\n      line-height: 50px;\r\n      font-size: 14px;\r\n      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.collapse {\r\n    .sidebar-logo {\r\n      margin-right: 0;\r\n      width: 54px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}