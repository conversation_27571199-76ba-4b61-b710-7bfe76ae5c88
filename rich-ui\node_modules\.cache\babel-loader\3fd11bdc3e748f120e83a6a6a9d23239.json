{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\permstype.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\permstype.js", "mtime": 1679364874000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPermstype", "query", "request", "url", "method", "params", "getPermstype", "permsId", "addPermstype", "data", "updatePermstype", "delPermstype", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/permstype.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询权限类型列表\r\nexport function listPermstype(query) {\r\n  return request({\r\n    url: '/system/permstype/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询权限类型详细\r\nexport function getPermstype(permsId) {\r\n  return request({\r\n    url: '/system/permstype/' + permsId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增权限类型\r\nexport function addPermstype(data) {\r\n  return request({\r\n    url: '/system/permstype',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改权限类型\r\nexport function updatePermstype(data) {\r\n  return request({\r\n    url: '/system/permstype',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除权限类型\r\nexport function delPermstype(permsId) {\r\n  return request({\r\n    url: '/system/permstype/' + permsId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(permsId, status) {\r\n  const data = {\r\n      permsId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/permstype/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAACC,OAAO,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,OAAO;IACnCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACJ,OAAO,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,OAAO;IACnCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,OAAO,EAAEM,MAAM,EAAE;EAC5C,IAAMJ,IAAI,GAAG;IACTF,OAAO,EAAPA,OAAO;IACTM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}