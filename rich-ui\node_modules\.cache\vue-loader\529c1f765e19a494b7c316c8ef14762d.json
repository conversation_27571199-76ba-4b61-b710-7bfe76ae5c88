{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\index.vue?vue&type=style&index=0&id=0ca12160&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\index.vue", "mtime": 1754876882597}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5jb2x1bW4tdGV4dCB7DQogIG1hcmdpbjogMDsNCiAgcGFkZGluZzogMDsNCiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsNCiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7DQp9DQoNCi5oaWdobGlnaHQtdGV4dCB7DQogIGZvbnQtd2VpZ2h0OiA2MDA7DQogIGZvbnQtc2l6ZTogMTVweA0KfQ0KDQoudW5IaWdobGlnaHQtdGV4dCB7DQogIGNvbG9yOiAjYjdiYmMyOw0KICBtYXJnaW46IDA7DQp9DQoNCi5lbC10YWJsZSAud2FybmluZy1yb3cgew0KICBiYWNrZ3JvdW5kOiBvbGRsYWNlOw0KfQ0KDQouaXRlbSB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQogIG1hcmdpbi1yaWdodDogNDBweDsNCn0NCg0KOjp2LWRlZXAgLmVsLWJhZGdlX19jb250ZW50LmlzLWZpeGVkIHsNCiAgZm9udC1zaXplOiAxMnB4Ow0KICB0b3A6IDBweDsNCiAgcmlnaHQ6IDJweDsNCn0NCg0KDQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi+BA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/rct", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <!--搜索条件-->\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\">\r\n          <el-form-item label=\"单号\" prop=\"rctNo\">\r\n            <el-input v-model=\"queryParams.rctNo\" placeholder=\"操作单号\" @keydown.enter.native=\"handleQuery\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.rctOpDate\" clearable\r\n                            placeholder=\"操作日期\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ATD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ATDDate\" clearable\r\n                            placeholder=\"ATD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ETD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ETDDate\" clearable\r\n                            placeholder=\"ETD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"审核\" prop=\"pasVerifyTime\">\r\n            <el-date-picker v-model=\"queryParams.pasVerifyTime\" clearable\r\n                            placeholder=\"商务审核时间\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isOpAllotted\">\r\n            <el-select v-model=\"queryParams.isOpAllotted\" clearable placeholder=\"操作分配标记\" style=\"width: 100%\">\r\n              <el-option label=\"未分配\" value=\"0\">未分配</el-option>\r\n              <el-option label=\"已分配\" value=\"1\">已分配</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientId\">\r\n            <company-select :multiple=\"false\" :no-parent=\"true\"\r\n                            :pass=\"queryParams.clientId\" :placeholder=\"'客户'\" :role-client=\"'1'\"\r\n                            :roleTypeId=\"1\" @return=\"queryParams.clientId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度\" prop=\"processStatusId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.processStatusId\" :placeholder=\"'进度状态'\"\r\n                         :type=\"'processStatus'\" @return=\"queryParams.processStatusId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"物流\" prop=\"logisticsTypeId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.logisticsTypeId\" :placeholder=\"'物流类型'\"\r\n                         :type=\"'serviceType'\" @return=\"queryParams.logisticsTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"polIds\">\r\n            <location-select :multiple=\"true\" :no-parent=\"true\"\r\n                             :pass=\"queryParams.polIds\" :placeholder=\"'启运港'\" @return=\"queryParams.polIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"目的\" prop=\"destinationPortIds\">\r\n            <location-select :en=\"true\" :multiple=\"true\"\r\n                             :pass=\"queryParams.destinationPortIds\" :placeholder=\"'目的港'\"\r\n                             @return=\"queryParams.destinationPortIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"业务\" prop=\"salesId\">\r\n            <treeselect v-model=\"salesId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务员\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"助理\" prop=\"salesAssistantId\">\r\n            <treeselect v-model=\"salesAssistantId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务助理\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesAssistantId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesAssistantId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isPsaVerified\">\r\n            <el-select v-model=\"queryParams.isPsaVerified\" clearable placeholder=\"商务审核标记\" style=\"width: 100%\">\r\n              <el-option value=\"0\" label=\"已审\">已审</el-option>\r\n              <el-option value=\"1\" label=\"未审\">未审</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"商务\" prop=\"verifyPsaId\">\r\n            <treeselect v-model=\"verifyPsaId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"商务\"\r\n                        @open=\"loadBusinesses\" @select=\"queryParams.verifyPsaId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.verifyPsaId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"opId\">\r\n            <treeselect v-model=\"opId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\"\r\n                        :show-count=\"true\" placeholder=\"操作员\"\r\n                        @open=\"loadOp\" @select=\"queryParams.opId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.opId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :placeholder=\"'收付路径'\"\r\n                         :type=\"'rsPaymentTitle'\" @return=\"queryParams.orderBelongsTo=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"应收\">\r\n            <el-switch\r\n              v-model=\"queryParams.params.closedAccount\" @change=\"handleQuery\"\r\n            >\r\n            </el-switch>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <!--顶部操作按钮-->\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:rct:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:rct:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <dynamic-search\r\n              :search-fields=\"rctSearchFields\"\r\n              :config-type=\"'rct-search'\"\r\n              @reset=\"resetQuery\"\r\n              @search=\"handleQuery($event)\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button @click=\"handleOpenAggregator\">数据汇总</el-button>\r\n            <el-dialog v-dialogDrag v-dialogDragWidth\r\n                       :visible.sync=\"openAggregator\" append-to-body width=\"80%\"\r\n            >\r\n              <!--<data-aggregator :data-source=\"aggregatorRctList\" :field-label-map=\"fieldLabelMap\"/>-->\r\n              <data-aggregator-back-ground :aggregate-function=\"listAggregatorRct\" :data-source=\"aggregatorRctList\"\r\n                                           :config-type=\"'rct-agg'\" :data-source-type=\"'rct'\"\r\n                                           :field-label-map=\"fieldLabelMap\"/>\r\n            </el-dialog>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              icon=\"el-icon-export\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-popover\r\n              v-for=\"(item,index) in [{file:'打印',link:getReconciliationBill,templateList:['对账单']}]\"\r\n              :key=\"index\" placement=\"top\" trigger=\"click\"\r\n              width=\"100\"\r\n            >\r\n              <el-button v-for=\"(item2,index) in item.templateList\" :key=\"index\"\r\n                         size=\"mini\" type=\"primary\"\r\n                         @click=\"item.link(item2)\"\r\n              >{{ item2 }}\r\n              </el-button>\r\n              <el-button slot=\"reference\" size=\"mini\" type=\"primary\"\r\n              >{{ item.file }}\r\n              </el-button>\r\n            </el-popover>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--表格-->\r\n        <el-table v-loading=\"loading\" :data=\"rctList\"\r\n                  stripe @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"dbclick\"\r\n        >\r\n          <el-table-column align=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-badge :value=\"scope.row.opAccept==0?'new':''\" class=\"item\">\r\n                <div style=\"width: 15px\">{{ scope.$index + 1 }}</div>\r\n              </el-badge>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作单号\" prop=\"clientId\" show-overflow-tooltip width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\" style=\"font-size: 18px;height: 23px\"\r\n              >{{ scope.row.rctNo }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"width: 100px;\">{{\r\n                  parseTime(scope.row.rctCreateTime, \"{m}.{d}\") + \" \" + emergencyLevel(scope.row.emergencyLevel) + \" \" + difficultyLevel(scope.row.difficultyLevel)\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"委托单位\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\">\r\n                {{ scope.row.clientSummary ? scope.row.clientSummary.split(\"/\")[1] : null }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"height: 23px\">\r\n                {{\r\n                  (scope.row.orderBelongsTo ? scope.row.orderBelongsTo : \"\") + \" \" + (scope.row.releaseType ? getReleaseType(scope.row.releaseType) : \"\") + \" \" + scope.row.paymentNode\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流类型\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" font-weight: 600;\">{{ scope.row.logisticsTypeEnName }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" color: #b7bbc2;height: 23px\">\r\n                  {{ scope.row.impExpType === \"1\" ? \"出口\" : \"\" }}\r\n                  {{ scope.row.impExpType === \"2\" ? \"进口\" : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"启运港\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box \" style=\" font-size: 15px\">\r\n                  {{ scope.row.pol ? scope.row.pol.split(\"(\")[0] : scope.row.pol }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.pol ? \"(\" + scope.row.pol.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"目的港\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 95px;overflow: hidden\">\r\n                <p class=\"column-text bottom-box highlight-text\" style=\" \">\r\n                  {{ scope.row.destinationPort ? scope.row.destinationPort.split(\"(\")[0] : scope.row.destinationPort }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.destinationPort ? \"(\" + scope.row.destinationPort.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"计费货量\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box highlight-text\" style=\" \">{{ scope.row.revenueTon }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">{{ scope.row.goodsNameSummary }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.blTypeCode ? scope.row.blTypeCode : \"\")\r\n                  }}\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">\r\n                  {{ (scope.row.blFormCode ? scope.row.blFormCode : \"\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订舱\" show-overflow-tooltip width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text bottom-box\"\r\n                   style=\"text-overflow: ellipsis; white-space: nowrap;font-weight: 600;  font-size: 13px\"\r\n                >{{\r\n                    scope.row.carrierEnName\r\n                  }} <span class=\"column-text unHighlight-text\" style=\" font-size: 12px\">{{\r\n                    \"(\" + scope.row.agreementTypeCode + \")\"\r\n                    }}</span></p>\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{ scope.row.supplierName }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <!--<el-table-column align=\"left\" label=\"注意事项\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{ scope.row.newBookingRemark + \"  \" + scope.row.inquiryInnerRemarkSum }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">{{\r\n                    scope.row.opLeaderNotice + \"  \" + scope.row.opInnerRemark\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>-->\r\n          <el-table-column align=\"left\" label=\"入仓与SO号\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.warehousingNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">{{ scope.row.soNo }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单与柜号\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.blNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" height: 23px\">{{ scope.row.sqdContainersSealsSum }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订单状态\" show-overflow-tooltip width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{ processStatus(scope.row.processStatusId) }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流进度\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.podEta ? (\"ATD: \" + parseTime(scope.row.podEta, \"{m}-{d}\")) : (\"ETD: \" + parseTime(scope.row.etd, \"{m}-{d}\")))\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\"height: 23px \">{{\r\n                    (scope.row.destinationPortEta ? (\"ATA: \" + parseTime(scope.row.destinationPortEta, \"{m}-{d}\")) : (\"ETA: \" + parseTime(scope.row.eta, \"{m}-{d}\")))\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"文件进度\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.docStatusA ? (scope.row.docStatusA.split(\":\")[0] + \": \" + scope.row.docStatusA.split(\":\")[1]) : \"\")\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.docStatusB ? (scope.row.docStatusB.split(\":\")[0] + \": \" + scope.row.docStatusB.split(\":\")[1]) : \"\")\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"收款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  <component :is=\"'bankSlip'\" :scope=\"scope\" :type=\"'receive'\" @return=\"getReturn\"/>\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">{{\r\n                    currency(currency(scope.row.dnUsdBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"$\",\r\n                      precision: 2\r\n                    }).format() + \" / \" + currency(currency(scope.row.dnRmbBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"主服务付款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box \">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  <component :is=\"'bankSlip'\" :scope=\"scope\" :type=\"'pay'\" @return=\"getReturn\"/>\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">{{\r\n                    currency(currency(scope.row.cnUsdBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"$\",\r\n                      precision: 2\r\n                    }).format() + \" / \" + currency(currency(scope.row.cnRmbBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"业绩\" width=\"100\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <div style=\"margin-right: 5px\">\r\n                业绩\r\n              </div>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <div :class=\"currency(scope.row.profitInRmb).divide(scope.row.dnInRmb).value<0?'warning':''\"\r\n                   class=\"flex-box\"\r\n                   style=\"margin-right: 5px\"\r\n              >\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{\r\n                    currency(scope.row.profitInRmb, {separator: \",\", symbol: \"¥\", precision: 2}).format()\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">{{\r\n                    currency(scope.row.profitInRmb, {precision: 2}).divide(scope.row.dnInRmb).multiply(100).value + \"%\"\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"业务/助理\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\"overflow: hidden\">{{\r\n                    (getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId))) ? getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId)) : null\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">\r\n                  {{ parseTime(scope.row.newBookingTime, \"{m}.{d}\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"商务审核\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\" width: 55px;overflow: hidden \">{{\r\n                    getName(scope.row.verifyPsaId)\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.psaVerifyTime, \"{m}.{d}\") + \" \" + (scope.row.psaVerifyStatusId == 1 ? \"通过\" : \"驳回\")\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作员\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\" width: 55px;overflow: hidden \">{{\r\n                    getName(scope.row.opId)\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.statusUpdateTime, \"{m}.{d}\") + \" \" + (processStatus(scope.row.processStatusId))\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <!--<el-table-column align=\"left\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:rct:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>-->\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {rctSearchFields} from \"@/config/rctSearchFields\"\r\nimport pinyin from \"js-pinyin\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\"\r\nimport store from \"@/store\"\r\nimport {delRct, listAggregatorRct, listRct, op} from \"@/api/system/rct\"\r\nimport currency from \"currency.js\"\r\nimport bankSlip from \"@/views/system/rct/bankSlip.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport moment from \"moment\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {checkRole} from \"@/utils/permission\"\r\nimport DataAggregator from \"@/views/system/DataAggregator/index.vue\"\r\nimport {rctFieldLabelMap} from \"@/config/rctFieldLabelMap\"\r\nimport {hiprint} from \"@\"\r\nimport dispatchBill from \"@/print-template/dispatchBill\"\r\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\r\nimport reconciliationBill from \"@/print-template/reconciliationBill\"\r\nimport DynamicSearch from \"@/components/DynamicSearch/index.vue\"\r\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\r\n\r\nlet hiprintTemplate\r\n\r\nexport default {\r\n  name: \"Rct\",\r\n  components: {\r\n    DataAggregatorBackGround,\r\n    DynamicSearch, printPreview, DataAggregator, CompanySelect, Treeselect, bankSlip\r\n  },\r\n  data() {\r\n    return {\r\n      rctSearchFields,\r\n      fieldLabelMap: rctFieldLabelMap,\r\n      yourDataSource: [\r\n        {\r\n          date: \"2023-01-01\",\r\n          category: \"Electronics\",\r\n          region: \"North\",\r\n          sales: 1000,\r\n          profit: 200\r\n        }\r\n        /* 你的数据格式示例：\r\n        {\r\n          date: '2023-01-01',\r\n          category: 'Electronics',\r\n          region: 'North',\r\n          sales: 1000,\r\n          profit: 200\r\n        }\r\n        */\r\n      ],\r\n      showLeft: 3,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      statisticsOp: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 操作单列表表格数据\r\n      salesId: null,\r\n      verifyPsaId: null,\r\n      salesAssistantId: null,\r\n      opId: null,\r\n      belongList: [],\r\n      opList: [],\r\n      businessList: [],\r\n      openAggregator: false,\r\n      rctList: [],\r\n      aggregatorRctList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        params: new Map()\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    let load = false\r\n    if (this.$route.query.no) {\r\n      this.queryParams.newBookingNo = this.$route.query.no\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    } else {\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    }\r\n    if (load) {\r\n      this.loadSales()\r\n      this.loadOp()\r\n      this.loadBusinesses()\r\n    }\r\n    this.loadStaffList()\r\n  },\r\n  computed: {\r\n    moment() {\r\n      return moment\r\n    }\r\n  },\r\n  methods: {\r\n    listAggregatorRct(params) {\r\n      params.config = JSON.stringify(params.config)\r\n      this.queryParams.params = params;\r\n      return listAggregatorRct(this.queryParams)\r\n    },\r\n    handleOpenAggregator() {\r\n      this.openAggregator = true\r\n    },\r\n    getReconciliationBill() {\r\n      let data = {}\r\n      let receivableUSD = 0\r\n      let receivableRMB = 0\r\n      let balanceUSD = 0\r\n      let balanceRMB = 0\r\n      data.receivable = \"\"\r\n      data.balance = \"\"\r\n      data.company = this.rctList[0].clientSummary.split(\"/\")[1]\r\n      data.rctList = this._.cloneDeep(this.rctList)\r\n      data.rctList.map(item => {\r\n        item.etd = parseTime(item.etd, \"{m}-{d}\")\r\n        item.eta = parseTime(item.eta, \"{m}-{d}\")\r\n        item.pol = item.pol.split(\"(\")[0]\r\n        item.destinationPort = item.destinationPort.split(\"(\")[0]\r\n        console.log(item.dnRmbBalance)\r\n        receivableUSD = currency(receivableUSD).add(item.dnUsd)\r\n        item.dnUsd = currency(currency(item.dnUsd).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        receivableRMB = currency(receivableUSD).add(item.dnRmb)\r\n        item.dnRmb = currency(currency(item.dnRmb).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        balanceUSD = currency(balanceUSD).add(item.dnUsdBalance)\r\n        item.dnUsdBalance = currency(currency(item.dnUsdBalance).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        balanceRMB = currency(balanceRMB).add(item.dnRmbBalance)\r\n        item.dnRmbBalance = currency(currency(item.dnRmbBalance).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        return item\r\n      })\r\n\r\n      receivableUSD = currency(currency(receivableUSD).value, {\r\n        separator: \",\",\r\n        symbol: \"$\",\r\n        precision: 2\r\n      }).format()\r\n      receivableRMB = currency(currency(receivableRMB).value, {\r\n        separator: \",\",\r\n        symbol: \"￥\",\r\n        precision: 2\r\n      }).format()\r\n      balanceUSD = currency(currency(balanceUSD).value, {\r\n        separator: \",\",\r\n        symbol: \"$\",\r\n        precision: 2\r\n      }).format()\r\n      balanceRMB = currency(currency(balanceRMB).value, {\r\n        separator: \",\",\r\n        symbol: \"￥\",\r\n        precision: 2\r\n      }).format()\r\n      data.receivable = receivableUSD + \"</br>\" + receivableRMB\r\n      data.balance = balanceUSD + \"</br>\" + balanceRMB\r\n\r\n      hiprintTemplate = new hiprint.PrintTemplate({template: reconciliationBill})\r\n      // 打开预览组件\r\n      this.$refs.preView.print(hiprintTemplate, data)\r\n    },\r\n    checkRole,\r\n    parseTime,\r\n    getReturn() {\r\n\r\n    },\r\n    currency,\r\n    tableRowClassName({row, rowIndex}) {\r\n      if (row.opAccept == 0) {\r\n        return \"unconfirmed\"\r\n      }\r\n      return \"\"\r\n    },\r\n    sqdDocDeliveryWay(type) {\r\n      if (type == 1) return \" 境外快递\"\r\n      if (type == 2) return \" 境内快递\"\r\n      if (type == 3) return \" 跑腿\"\r\n      if (type == 4) return \" 业务送达\"\r\n      if (type == 5) return \" 客户自取\"\r\n      if (type == 6) return \" QQ\"\r\n      if (type == 7) return \" 微信\"\r\n      if (type == 8) return \" 电邮\"\r\n      if (type == 9) return \" 公众号\"\r\n      if (type == 10) return \" 承运人系统\"\r\n      if (type == 11) return \" 订舱口系统\"\r\n      if (type == 12) return \" 第三方系统\"\r\n      return \"\"\r\n    },\r\n    getReleaseType(id) {\r\n      if (id == 1) return \"月结\"\r\n      if (id == 2) return \"押放\"\r\n      if (id == 3) return \"票结\"\r\n      if (id == 4) return \"签放\"\r\n      if (id == 5) return \"订金\"\r\n      if (id == 6) return \"预付\"\r\n      if (id == 7) return \"扣货\"\r\n      if (id == 9) return \"居间\"\r\n      return \"\"\r\n    },\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffGivingEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n    logisticsPaymentTerms(v) {\r\n      if (v == 1) return \"月结\"\r\n      if (v == 2) return \"押单\"\r\n      if (v == 3) return \"此票结清\"\r\n      if (v == 4) return \"经理签单\"\r\n      if (v == 5) return \"预收订金\"\r\n      if (v == 6) return \"全额预付\"\r\n      if (v == 7) return \"扣货\"\r\n      if (v == 8) return \"背靠背\"\r\n      return \"\"\r\n    },\r\n    emergencyLevel(v) {\r\n      if (v == 0) return \"预定\"\r\n      if (v == 1) return \"当天\"\r\n      if (v == 2) return \"常规\"\r\n      if (v == 3) return \"紧急\"\r\n      if (v == 4) return \"立即\"\r\n      return \"\"\r\n    },\r\n    difficultyLevel(v) {\r\n      if (v == 0) return \"简易\"\r\n      if (v == 1) return \"标准\"\r\n      if (v == 2) return \"高级\"\r\n      if (v == 3) return \"特别\"\r\n      return \"\"\r\n    },\r\n    processStatus(v) {\r\n      if (v == 1) return \"等待\"\r\n      if (v == 2) return \"进行\"\r\n      if (v == 3) return \"变更\"\r\n      if (v == 4) return \"异常\"\r\n      if (v == 5) return \"质押\"\r\n      if (v == 6) return \"确认\"\r\n      if (v == 7) return \"完成\"\r\n      if (v == 8) return \"取消\"\r\n      if (v == 9) return \"驳回\"\r\n      if (v == 10) return \"回收\"\r\n      return \"\"\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        store.dispatch(\"getOpList\").then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n      }\r\n    },\r\n    loadStaffList() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    /** 查询操作单列表列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      this.queryParams.permissionLevel = this.$store.state.user.permissionLevelList.C\r\n      await listRct(this.queryParams).then(response => {\r\n        this.rctList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery(queryParams) {\r\n      this.queryParams = {\r\n        ...this.queryParams,\r\n        ...queryParams,\r\n        pageNum: 1  // 直接在合并时设置页码为1\r\n      }\r\n\r\n      // 处理rctNo\r\n      this.queryParams.rctNo = this.queryParams.rctNo ? this.queryParams.rctNo.trim() : this.queryParams.rctNo\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // handleStatusChange(row) {\r\n    //   let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n    //   this.$modal.confirm('确认要\"' + text + '吗？').then(function () {\r\n    //     return changeStatus(row.rctId, row.status);\r\n    //   }).then(() => {\r\n    //     this.$modal.msgSuccess(text + \"成功\");\r\n    //   }).catch(function () {\r\n    //     row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n    //   });\r\n    // },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.rctId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {})\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: row.rctId})\r\n    },\r\n    dbclick(row, column, event) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: row.rctId, type: \"op\"})\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const rctIds = row.rctId || this.ids\r\n      this.$confirm(\"是否确认删除操作单列表编号为\\\"\" + rctIds + \"\\\"的数据项？\", \"提示\", {customClass: \"modal-confirm\"}).then(function () {\r\n        return delRct(rctIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/rct/export\", {\r\n        ...this.queryParams\r\n      }, `rct_${new Date().getTime()}.xlsx`)\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.column-text {\r\n  margin: 0;\r\n  padding: 0;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.highlight-text {\r\n  font-weight: 600;\r\n  font-size: 15px\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n.el-table .warning-row {\r\n  background: oldlace;\r\n}\r\n\r\n.item {\r\n  margin-top: 10px;\r\n  margin-right: 40px;\r\n}\r\n\r\n::v-deep .el-badge__content.is-fixed {\r\n  font-size: 12px;\r\n  top: 0px;\r\n  right: 2px;\r\n}\r\n\r\n\r\n</style>\r\n\r\n"]}]}