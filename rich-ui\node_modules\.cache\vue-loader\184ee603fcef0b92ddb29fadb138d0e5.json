{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\docreleaseway\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\docreleaseway\\index.vue", "mtime": 1754876882582}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGREb2NyZWxlYXNld2F5LA0KICBjaGFuZ2VTdGF0dXMsDQogIGRlbERvY3JlbGVhc2V3YXksDQogIGdldERvY3JlbGVhc2V3YXksDQogIGxpc3REb2NyZWxlYXNld2F5LA0KICB1cGRhdGVEb2NyZWxlYXNld2F5DQp9IGZyb20gIkAvYXBpL3N5c3RlbS9kb2NyZWxlYXNld2F5IjsNCmltcG9ydCB7bGlzdERvY2lzc3VldHlwZX0gZnJvbSAiQC9hcGkvc3lzdGVtL2RvY2lzc3VldHlwZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRvY3JlbGVhc2V3YXkiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzaG93TGVmdDogMywNCiAgICAgIHNob3dSaWdodDogMjEsDQogICAgICBkb2Npc3N1ZXR5cGVMaXN0OiBbXSwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDkuqTljZXmlrnlvI/ooajmoLzmlbDmja4NCiAgICAgIGRvY3JlbGVhc2V3YXlMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICByZWxlYXNlV2F5U2hvcnROYW1lOiBudWxsLA0KICAgICAgICByZWxlYXNlV2F5TG9jYWxOYW1lOiBudWxsLA0KICAgICAgICByZWxlYXNlV2F5RW5OYW1lOiBudWxsLA0KICAgICAgICBvcmRlck51bTogbnVsbCwNCiAgICAgICAgc3RhdHVzOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fQ0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd1NlYXJjaChuKSB7DQogICAgICBpZiAobiA9PT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxDQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAzDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0DQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAwDQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICBsaXN0RG9jaXNzdWV0eXBlKHtwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLmRvY2lzc3VldHlwZUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgIH0pOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouS6pOWNleaWueW8j+WIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdERvY3JlbGVhc2V3YXkodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZG9jcmVsZWFzZXdheUxpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWw7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICByZWxlYXNlV2F5SWQ6IG51bGwsDQogICAgICAgIHJlbGVhc2VXYXlTaG9ydE5hbWU6IG51bGwsDQogICAgICAgIHJlbGVhc2VXYXlMb2NhbE5hbWU6IG51bGwsDQogICAgICAgIHJlbGVhc2VXYXlFbk5hbWU6IG51bGwsDQogICAgICAgIG9yZGVyTnVtOiBudWxsLA0KICAgICAgICBzdGF0dXM6ICIwIiwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIGRlbGV0ZUJ5OiBudWxsLA0KICAgICAgICBkZWxldGVUaW1lOiBudWxsLA0KICAgICAgICBkZWxldGVTdGF0dXM6ICIwIg0KICAgICAgfTsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIik7DQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxOw0KICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKTsNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsNCiAgICB9LA0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICflkJfvvJ8nLCAn5o+Q56S6Jywge2N1c3RvbUNsYXNzOiAnbW9kYWwtY29uZmlybSd9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGNoYW5nZVN0YXR1cyhyb3cucmVsZWFzZVdheUlkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7DQogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ucmVsZWFzZVdheUlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDkuqTljZXmlrnlvI8iOw0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIGNvbnN0IHJlbGVhc2VXYXlJZCA9IHJvdy5yZWxlYXNlV2F5SWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldERvY3JlbGVhc2V3YXkocmVsZWFzZVdheUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnkuqTljZXmlrnlvI8iOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ucmVsZWFzZVdheUlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZURvY3JlbGVhc2V3YXkodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGREb2NyZWxlYXNld2F5KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IHJlbGVhc2VXYXlJZHMgPSByb3cucmVsZWFzZVdheUlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5Lqk5Y2V5pa55byP57yW5Y+35Li6IicgKyByZWxlYXNlV2F5SWRzICsgJyLnmoTmlbDmja7pobnvvJ8nLCAn5o+Q56S6Jywge2N1c3RvbUNsYXNzOiAnbW9kYWwtY29uZmlybSd9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbERvY3JlbGVhc2V3YXkocmVsZWFzZVdheUlkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL2RvY3JlbGVhc2V3YXkvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgZG9jcmVsZWFzZXdheV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/docreleaseway", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\"\r\n                 label-width=\"68px\">\r\n          <el-form-item label=\"简称\" prop=\"releaseWayShortName\">\r\n            <el-input\r\n              v-model=\"queryParams.releaseWayShortName\"\r\n              placeholder=\"简称\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"中文名\" prop=\"releaseWayLocalName\">\r\n            <el-input\r\n              v-model=\"queryParams.releaseWayLocalName\"\r\n              placeholder=\"中文名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"英文名\" prop=\"releaseWayEnName\">\r\n            <el-input\r\n              v-model=\"queryParams.releaseWayEnName\"\r\n              placeholder=\"英文名\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"排序\" prop=\"orderNum\">\r\n            <el-input\r\n              v-model=\"queryParams.orderNum\"\r\n              placeholder=\"排序\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:docreleaseway:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['system:docreleaseway:edit']\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:docreleaseway:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:docreleaseway:export']\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"docreleasewayList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column label=\"交单方式\" align=\"center\" prop=\"issueType\"/>\r\n          <el-table-column label=\"简称\" align=\"center\" prop=\"releaseWayShortName\"/>\r\n          <el-table-column label=\"中文名\" align=\"center\" prop=\"releaseWayLocalName\"/>\r\n          <el-table-column label=\"英文名\" align=\"center\" prop=\"releaseWayEnName\"/>\r\n          <el-table-column label=\"排序\" align=\"center\" prop=\"orderNum\"/>\r\n          <el-table-column label=\"状态\" align=\"center\" prop=\"status\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注\" align=\"center\" prop=\"remark\"/>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:docreleaseway:edit']\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:docreleaseway:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改交单方式对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" width=\"500px\"\r\n      append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item label=\"出单方式\" prop=\"issueTypeId\">\r\n          <el-select v-model=\"form.issueTypeId\" placeholder=\"主单出单方式\" style=\"width: 100%\">\r\n            <el-option v-for=\"r in docissuetypeList\"\r\n                       :label=\"r.issueTypeLocalName\"\r\n                       :value=\"r.issueTypeId\"\r\n                       :key=\"r.issueTypeId\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"简称\" prop=\"releaseWayShortName\">\r\n          <el-input v-model=\"form.releaseWayShortName\" placeholder=\"简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"releaseWayLocalName\">\r\n          <el-input v-model=\"form.releaseWayLocalName\" placeholder=\"中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"releaseWayEnName\">\r\n          <el-input v-model=\"form.releaseWayEnName\" placeholder=\"英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" style=\"width: 100%\" v-model=\"form.orderNum\" placeholder=\"排序\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addDocreleaseway,\r\n  changeStatus,\r\n  delDocreleaseway,\r\n  getDocreleaseway,\r\n  listDocreleaseway,\r\n  updateDocreleaseway\r\n} from \"@/api/system/docreleaseway\";\r\nimport {listDocissuetype} from \"@/api/system/docissuetype\";\r\n\r\nexport default {\r\n  name: \"Docreleaseway\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      docissuetypeList: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 交单方式表格数据\r\n      docreleasewayList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        releaseWayShortName: null,\r\n        releaseWayLocalName: null,\r\n        releaseWayEnName: null,\r\n        orderNum: null,\r\n        status: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    listDocissuetype({pageNum: 1, pageSize: 100}).then(response => {\r\n      this.docissuetypeList = response.rows;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询交单方式列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDocreleaseway(this.queryParams).then(response => {\r\n        this.docreleasewayList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        releaseWayId: null,\r\n        releaseWayShortName: null,\r\n        releaseWayLocalName: null,\r\n        releaseWayEnName: null,\r\n        orderNum: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.releaseWayId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.releaseWayId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加交单方式\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const releaseWayId = row.releaseWayId || this.ids\r\n      getDocreleaseway(releaseWayId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改交单方式\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.releaseWayId != null) {\r\n            updateDocreleaseway(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDocreleaseway(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const releaseWayIds = row.releaseWayId || this.ids;\r\n      this.$confirm('是否确认删除交单方式编号为\"' + releaseWayIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delDocreleaseway(releaseWayIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/docreleaseway/export', {\r\n        ...this.queryParams\r\n      }, `docreleaseway_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}