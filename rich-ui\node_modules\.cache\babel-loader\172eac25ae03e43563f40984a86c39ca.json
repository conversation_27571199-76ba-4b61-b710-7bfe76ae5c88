{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\price.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\price.vue", "mtime": 1754876882588}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAicHJpY2UiLAogIHByb3BzOiBbJ3Njb3BlJywgJ3R5cGVJZCddLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaXplOiB0aGlzLiRzdG9yZS5zdGF0ZS5hcHAuc2l6ZSB8fCAnbWluaScKICAgIH07CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["name", "props", "data", "size", "$store", "state", "app", "exports", "default", "_default"], "sources": ["src/views/system/freight/price.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <h2 style=\"margin: 0;font-weight:bold;\" v-if=\"typeId==2\">\r\n      {{\r\n        (scope.row.priceB != null ? scope.row.priceB + (scope.row.priceC != null ? \" / \" : \"\") : \"\")\r\n        + (scope.row.priceC != null ? scope.row.priceC + (scope.row.priceD != null ? \" / \" : \"\") : \"\")\r\n        + (scope.row.priceD != null ? scope.row.priceD + (scope.row.priceE != null ? \" / \" : \"\") : \"\")\r\n        + (scope.row.priceE != null ? scope.row.priceE + (scope.row.priceA != null ? \" / \" : \"\") : \"\")\r\n        + (scope.row.priceA != null ? scope.row.priceA : \"\")\r\n      }}\r\n    </h2>\r\n    <h2 style=\"margin: 0;font-weight:bold;\" v-else>\r\n      {{\r\n        (scope.row.priceB != null ? scope.row.priceB + (scope.row.priceC != null ? \" / \" : \"\") : \"\")\r\n        + (scope.row.priceC != null ? scope.row.priceC + (scope.row.priceD != null ? \" / \" : \"\") : \"\")\r\n        + (scope.row.priceD != null ? scope.row.priceD + (scope.row.priceA != null ? \" / \" : \"\") : \"\")\r\n        + (scope.row.priceA != null ? scope.row.priceA : \"\")\r\n      }}\r\n    </h2>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"price\",\r\n  props: ['scope', 'typeId'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini',\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;eAuBA;EACAA,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;IACA;EACA;AACA;AAAAI,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}