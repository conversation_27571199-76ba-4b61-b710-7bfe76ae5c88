{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue", "mtime": 1754881964240}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRPdXRib3VuZHJlY29yZCwNCiAgY2hhbmdlU3RhdHVzLA0KICBkZWxPdXRib3VuZHJlY29yZCwNCiAgZ2V0T3V0Ym91bmRyZWNvcmQsDQogIGxpc3RPdXRib3VuZHJlY29yZCwNCiAgdXBkYXRlT3V0Ym91bmRyZWNvcmQNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL291dGJvdW5kcmVjb3JkIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIkAvdXRpbHMvcmljaCINCmltcG9ydCB7DQogIGxpc3RJbnZlbnRvcnksDQogIGxpc3RJbnZlbnRvcnlzLA0KICBvdXRib3VuZEludmVudG9yeSwNCiAgcHJlT3V0Ym91bmRJbnZlbnRvcnksDQogIHNldHRsZW1lbnQNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL2ludmVudG9yeSINCmltcG9ydCBtb21lbnQgZnJvbSAibW9tZW50Ig0KaW1wb3J0IGN1cnJlbmN5IGZyb20gImN1cnJlbmN5LmpzIg0KaW1wb3J0IHdhcmVob3VzZVJlY2VpcHQgZnJvbSAiQC9wcmludC10ZW1wbGF0ZS93YXJlaG91c2VSZWNlaXB0Ig0KaW1wb3J0IHtkZWZhdWx0RWxlbWVudFR5cGVQcm92aWRlciwgaGlwcmludH0gZnJvbSAiQCINCmltcG9ydCBwcmludFByZXZpZXcgZnJvbSAiQC92aWV3cy9wcmludC9kZW1vL2Rlc2lnbi9wcmV2aWV3LnZ1ZSINCmltcG9ydCBvdXRib3VuZFBsYW50IGZyb20gIkAvcHJpbnQtdGVtcGxhdGUvb3V0Ym91bmRQbGFudCINCg0KbGV0IGhpcHJpbnRUZW1wbGF0ZQ0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiT3V0Ym91bmRyZWNvcmQiLA0KICBjb21wb25lbnRzOiB7cHJpbnRQcmV2aWV3fSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0xlZnQ6IDAsDQogICAgICBzaG93UmlnaHQ6IDI0LA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgc2VsZWN0T3V0Ym91bmRMaXN0OiBbXSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogZmFsc2UsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5Ye65LuT6K6w5b2V6KGo5qC85pWw5o2uDQogICAgICBvdXRib3VuZHJlY29yZExpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIG91dGJvdW5kTm86IG51bGwsDQogICAgICAgIGNsaWVudENvZGU6IG51bGwsDQogICAgICAgIGNsaWVudE5hbWU6IG51bGwsDQogICAgICAgIG9wZXJhdG9yOiBudWxsLA0KICAgICAgICBjb250YWluZXJUeXBlOiBudWxsLA0KICAgICAgICBjb250YWluZXJObzogbnVsbCwNCiAgICAgICAgc2VhbE5vOiBudWxsLA0KICAgICAgICBvdXRib3VuZERhdGU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiBudWxsLA0KICAgICAgICB3b3JrZXJMb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB3YXJlaG91c2VDb2xsZWN0aW9uOiBudWxsLA0KICAgICAgICBjb2xsZWN0aW9uTm90ZXM6IG51bGwsDQogICAgICAgIHRvdGFsQm94ZXM6IG51bGwsDQogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLA0KICAgICAgICB0b3RhbFJvd3M6IG51bGwsDQogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwNCiAgICAgICAgdW5wYWlkVW5sb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRQYWNrYWdpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBmcmVlU3RhY2tEYXlzOiBudWxsLA0KICAgICAgICBvdmVyZHVlVW5pdFByaWNlOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIG91dGJvdW5kVHlwZTogbnVsbCwNCiAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5TGlzdExvYWRpbmc6IGZhbHNlLA0KICAgICAgc2VhcmNoOiBudWxsLA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBjbGllbnRDb2RlOiBbDQogICAgICAgICAge3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi5a6i5oi35Luj56CB5LiN6IO95Li656m6IiwgdHJpZ2dlcjogImJsdXIifQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgb3V0Ym91bmRGb3JtOiB7DQogICAgICAgIG91dGJvdW5kRGF0ZTogbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIikNCiAgICAgIH0sDQogICAgICBjbGllbnRSb3c6IHt9LA0KICAgICAgb3Blbk91dGJvdW5kOiBmYWxzZSwNCiAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5TGlzdDogW10sDQogICAgICBzZWxlY3RlZENhcmdvRGV0YWlsOiBbXQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93U2VhcmNoKG4pIHsNCiAgICAgIGlmIChuID09PSB0cnVlKSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjENCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjQNCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDANCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCkNCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICB0aGlzLmluaXRQcmludCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBpbml0UHJpbnQoKSB7DQogICAgICBoaXByaW50LmluaXQoew0KICAgICAgICBwcm92aWRlcnM6IFtuZXcgZGVmYXVsdEVsZW1lbnRUeXBlUHJvdmlkZXIoKV0NCiAgICAgIH0pDQogICAgfSwNCiAgICBwcmludE91dGJvdW5kUGxhbnQoKSB7DQogICAgICAvLyDlh4blpIfmiZPljbDmlbDmja4NCiAgICAgIGNvbnN0IHByaW50RGF0YSA9IHsNCiAgICAgICAgdGl0bGU6ICLnkZ7ml5fku5PlupPlh7rku5PorqHliJIiLA0KICAgICAgICAvLyDooajljZXmlbDmja4NCiAgICAgICAgb3V0Ym91bmRObzogdGhpcy5vdXRib3VuZEZvcm0ub3V0Ym91bmRObyB8fCAiIiwNCiAgICAgICAgY3VzdG9tZXJPcmRlck5vOiB0aGlzLm91dGJvdW5kRm9ybS5jdXN0b21lck9yZGVyTm8gfHwgIiIsDQogICAgICAgIGNsaWVudENvZGU6IHRoaXMub3V0Ym91bmRGb3JtLmNsaWVudENvZGUgfHwgIiIsDQogICAgICAgIGNsaWVudE5hbWU6IHRoaXMub3V0Ym91bmRGb3JtLmNsaWVudE5hbWUgfHwgIiIsDQogICAgICAgIHBsYW5uZWRPdXRib3VuZERhdGU6IG1vbWVudCh0aGlzLm91dGJvdW5kRm9ybS5wbGFubmVkT3V0Ym91bmREYXRlKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW0iKSB8fCAiIiwNCiAgICAgICAgb3V0Ym91bmRUeXBlOiB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZFR5cGUgfHwgIiIsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IHRoaXMub3V0Ym91bmRGb3JtLmNvbnRhaW5lclR5cGUgfHwgIiIsDQogICAgICAgIGNhcmdvVHlwZTogdGhpcy5mb3JtLmNhcmdvVHlwZSB8fCAiIiwNCiAgICAgICAgY29udGFpbmVyTm86IHRoaXMub3V0Ym91bmRGb3JtLmNvbnRhaW5lck5vIHx8ICIiLA0KICAgICAgICBzZWFsTm86IHRoaXMub3V0Ym91bmRGb3JtLnNlYWxObyB8fCAiIiwNCiAgICAgICAgcGxhdGVOdW1iZXI6IHRoaXMub3V0Ym91bmRGb3JtLnBsYXRlTnVtYmVyIHx8ICIiLA0KICAgICAgICBkcml2ZXJQaG9uZTogdGhpcy5vdXRib3VuZEZvcm0uZHJpdmVyUGhvbmUgfHwgIiIsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiB0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSB8fCAiIiwNCiAgICAgICAgd2FyZWhvdXNlQ29sbGVjdGlvbjogdGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlQ29sbGVjdGlvbiB8fCAiIiwNCiAgICAgICAgd29ya2VyTG9hZGluZ0ZlZTogdGhpcy5vdXRib3VuZEZvcm0ud29ya2VyTG9hZGluZ0ZlZSB8fCAiIiwNCiAgICAgICAgd2FyZWhvdXNlUGF5OiB0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VQYXkgfHwgIiIsDQogICAgICAgIG9wZXJhdGlvblJlcXVpcmVtZW50OiB0aGlzLm91dGJvdW5kRm9ybS5vcGVyYXRpb25SZXF1aXJlbWVudCB8fCAiIiwNCiAgICAgICAgb3V0Ym91bmROb3RlOiB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZE5vdGUgfHwgIiIsDQogICAgICAgIG9wZXJhdG9yOiB0aGlzLm91dGJvdW5kRm9ybS5vcGVyYXRvciB8fCAiIiwNCiAgICAgICAgb3JkZXJEYXRlOiB0aGlzLm91dGJvdW5kRm9ybS5vcmRlckRhdGUgfHwgIiIsDQogICAgICAgIG91dGJvdW5kSGFuZGxlcjogdGhpcy5vdXRib3VuZEZvcm0ub3V0Ym91bmRIYW5kbGVyIHx8ICIiLA0KICAgICAgICB3YXJlaG91c2VDb25maXJtOiAi4oiaIOW3suehruiupCAiICsgdGhpcy5wYXJzZVRpbWUobmV3IERhdGUoKSwgInt5fS17bX0te2R9IiksDQoNCiAgICAgICAgLy8g5rGH5oC75pWw5o2uDQogICAgICAgIHRvdGFsQm94ZXM6IHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsQm94ZXMgfHwgMCwNCiAgICAgICAgdG90YWxHcm9zc1dlaWdodDogdGhpcy5vdXRib3VuZEZvcm0udG90YWxHcm9zc1dlaWdodCB8fCAwLA0KICAgICAgICB0b3RhbFZvbHVtZTogdGhpcy5vdXRib3VuZEZvcm0udG90YWxWb2x1bWUgfHwgMCwNCiAgICAgICAgdG90YWxTdW1tYXJ5OiBg5Lu25pWwOiAke3RoaXMub3V0Ym91bmRGb3JtLnRvdGFsQm94ZXMgfHwgMH0gLyDmr5vph406ICR7dGhpcy5vdXRib3VuZEZvcm0udG90YWxHcm9zc1dlaWdodCB8fCAwfSAvIOS9k+enrzogJHt0aGlzLm91dGJvdW5kRm9ybS50b3RhbFZvbHVtZSB8fCAwfWAsDQogICAgICAgIHRvdGFsUXVhbnRpdHk6IHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsQm94ZXMgfHwgMCwNCg0KICAgICAgICAvLyDli77pgInnmoTlupPlrZjliJfooagNCiAgICAgICAgaW52ZW50b3J5TGlzdDogdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIHJldHVybiB7DQogICAgICAgICAgICBpbmJvdW5kU2VyaWFsTm86IGl0ZW0uaW5ib3VuZFNlcmlhbE5vIHx8ICIiLA0KICAgICAgICAgICAgY2xpZW50Q29kZTogYCR7aXRlbS5zdWJPcmRlck5vIHx8ICIifSAke2l0ZW0uY29uc2lnbmVlTmFtZSB8fCAiIn1gLA0KICAgICAgICAgICAgdG90YWxCb3hlczogKHRoaXMub3V0Ym91bmRGb3JtLnNxZFNoaXBwaW5nTWFyayB8fCAiIikgKyAiIC8gIiArIChpdGVtLml0ZW1OYW1lIHx8ICIiKSArICIgLyAiICsgKGl0ZW0udG90YWxCb3hlcyB8fCAwKSArICIgLyAiICsgKGl0ZW0udG90YWxHcm9zc1dlaWdodCB8fCAwKSArICJLR1MgLyAiICsgKGl0ZW0udG90YWxWb2x1bWUgfHwgMCkgKyAiQ0JNIiwNCiAgICAgICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IGl0ZW0udG90YWxHcm9zc1dlaWdodCB8fCAwLA0KICAgICAgICAgICAgdG90YWxWb2x1bWU6IGl0ZW0udG90YWxWb2x1bWUgfHwgMCwNCiAgICAgICAgICAgIGRyaXZlckluZm86IGl0ZW0uZHJpdmVySW5mbyB8fCAiIiwNCiAgICAgICAgICAgIG91dGJvdW5kUXVhbnRpdHk6IGl0ZW0udG90YWxCb3hlcyB8fCAwDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KDQogICAgICAvLyDliJvlu7rmiZPljbDmqKHmnb/lubbpooTop4jmiZPljbANCiAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBvdXRib3VuZFBsYW50fSkNCiAgICAgIHRoaXMuJHJlZnMucHJlVmlldy5wcmludChoaXByaW50VGVtcGxhdGUsIHByaW50RGF0YSkNCiAgICB9LA0KICAgIHdhcmVob3VzZUNvbmZpcm0oKSB7DQogICAgICAvLyDmo4Dmn6XlrqLmiLfku6PnoIHmmK/lkKblt7LpgInmi6kNCiAgICAgIGlmICghdGhpcy5vdXRib3VuZEZvcm0uY2xpZW50Q29kZSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqeWuouaItyIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICAvLyDorr7nva7mk43kvZzlkZjkuLrlvZPliY3nlKjmiLcNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLm9wZXJhdG9yID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lLnNwbGl0KCIgIilbMV0NCg0KICAgICAgLy8g6K6+572u5LiL5Y2V5pel5pyf5Li65b2T5YmN5pel5pyfDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5vcmRlckRhdGUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKQ0KDQogICAgICAvLyDmj5DnpLrnoa7orqTmiJDlip8NCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5LuT566h56Gu6K6k5oiQ5YqfIikNCiAgICB9LA0KICAgIC8vIOWKoOi9veWtkOiKgueCueaVsOaNrg0KICAgIGxvYWRDaGlsZEludmVudG9yeSh0cmVlLCB0cmVlTm9kZSwgcmVzb2x2ZSkgew0KICAgICAgLy8g6K6+572u5b2T5YmN6KGM55qE5Yqg6L2954q25oCBDQogICAgICB0aGlzLiRzZXQodHJlZSwgJ2xvYWRpbmcnLCB0cnVlKQ0KDQogICAgICAvLyDkvb/nlKhwYWNrYWdlVG/lrZfmrrXmn6Xor6LlrZDoioLngrkNCiAgICAgIGxpc3RJbnZlbnRvcnkoe3BhY2thZ2VUbzogdHJlZS5pbnZlbnRvcnlJZH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBjb25zdCByb3dzID0gcmVzcG9uc2Uucm93cw0KDQogICAgICAgIC8vIOWFiOWwhuaVsOaNruS8oOmAkue7meihqOagvO+8jOehruS/neWtkOiKgueCuea4suafkw0KICAgICAgICByZXNvbHZlKHJvd3MpDQogICAgICAgIHRyZWUuY2hpbGRyZW4gPSByb3dzDQoNCiAgICAgICAgLy8g5aaC5p6c54i26aG56KKr6YCJ5Lit77yM5Zyo5a2Q6IqC54K55riy5p+T5a6M5oiQ5ZCO6YCJ5Lit5a6D5LusDQogICAgICAgIGlmICh0aGlzLmlkcy5pbmNsdWRlcyh0cmVlLmludmVudG9yeUlkKSkgew0KICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgcm93cy5mb3JFYWNoKGNoaWxkID0+IHsNCiAgICAgICAgICAgICAgaWYgKCF0aGlzLmlkcy5pbmNsdWRlcyhjaGlsZC5pbnZlbnRvcnlJZCkpIHsNCiAgICAgICAgICAgICAgICB0aGlzLmlkcy5wdXNoKGNoaWxkLmludmVudG9yeUlkKQ0KICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0LnB1c2goY2hpbGQpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgLy8g5ZyoVUnkuIrpgInkuK3lrZDpobkNCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy50YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24oY2hpbGQsIHRydWUpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0sIDUwKSAvLyDnrYnlvoVET03mm7TmlrANCiAgICAgICAgfQ0KICAgICAgfSkuZmluYWxseSgoKSA9PiB7DQogICAgICAgIC8vIOaXoOiuuuivt+axguaIkOWKn+i/mOaYr+Wksei0pe+8jOmDvemcgOimgeWFs+mXreWKoOi9veeKtuaAgQ0KICAgICAgICB0aGlzLiRzZXQodHJlZSwgJ2xvYWRpbmcnLCBmYWxzZSkNCiAgICAgIH0pDQogICAgfSwNCiAgICB3YXJlaG91c2VSZW50U2V0dGxlbWVudCgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZEhhbmRsZXIgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWUuc3BsaXQoIiAiKVsxXQ0KICAgICAgdGhpcy5vdXRib3VuZFR5cGUgPSA0DQogICAgICB0aGlzLm9wZW5PdXRib3VuZCA9IHRydWUNCiAgICB9LA0KICAgIGNvdW50U3VtbWFyeSgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnVucmVjZWl2ZWRGcm9tQ3VzdG9tZXIgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLmFkZGl0aW9uYWxTdG9yYWdlRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkVW5sb2FkaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkUGFja2FnaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ubG9naXN0aWNzQWR2YW5jZUZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLm92ZXJkdWVSZW50YWxGZWUpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5kaWZmaWN1bHR5V29ya0ZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkRnJvbUN1c3RvbWVyID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlQ29sbGVjdGlvbikudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLmN1c3RvbWVyUmVjZWl2YWJsZUJhbGFuY2UgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS51bnJlY2VpdmVkRnJvbUN1c3RvbWVyKS5zdWJ0cmFjdCh0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZEZyb21DdXN0b21lcikudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnBheWFibGVUb1dvcmtlciA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkVW5sb2FkaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRQYWNraW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ud29ya2VyTG9hZGluZ0ZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkRnJvbVN1cHBsaWVyID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRTdXBwbGllcikuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkU3RvcmFnZUZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlU2FsZXMgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS51bnJlY2VpdmVkRnJvbUN1c3RvbWVyKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRGcm9tU3VwcGxpZXIpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZUNvc3QgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS5wYXlhYmxlVG9Xb3JrZXIpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5sb2dpc3RpY3NBZHZhbmNlRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlQWR2YW5jZU90aGVyRmVlKS52YWx1ZQ0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0ucHJvbWlzc29yeU5vdGVHcm9zc1Byb2ZpdCA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlU2FsZXMpLnN1YnRyYWN0KHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlQ29zdCkudmFsdWUNCiAgICB9LA0KICAgIGN1cnJlbmN5LA0KICAgIC8qKg0KICAgICAqDQogICAgICogQHBhcmFtIHR5cGUgMDrpooTlh7rku5MvMTrlh7rku5MvMjrnm7TmjqXlh7rku5MNCiAgICAgKi8NCiAgICBvdXRib3VuZENvbmZpcm0odHlwZSkgew0KICAgICAgLy8g5omn6KGM5YmN5YaN5qyh5o+Q6YaSDQogICAgICB0aGlzLiRjb25maXJtKCLnoa7lrpropoEiICsgKHR5cGUgPT09IDAgPyAi6aKE5Ye65LuTIiA6IHR5cGUgPT09IDEgPyAi5Ye65LuTIiA6ICLnm7TmjqXlh7rku5MiKSArICLlkJfvvJ8iLCAi5o+Q56S6Iiwgew0KICAgICAgICBjb25maXJtQnV0dG9uVGV4dDogIuehruWumiIsDQogICAgICAgIGNhbmNlbEJ1dHRvblRleHQ6ICLlj5bmtogiLA0KICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICAvLyDmiaPotKfkuI3lj6/ku6Xlh7rku5MNCiAgICAgICAgdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLmNhcmdvRGVkdWN0aW9uID09IDEpIHsNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuacieaJo+i0p+W6k+WtmOivt+mHjeaWsOWLvumAie+8jOa1geawtOWPt++8miIgKyBpdGVtLmluYm91bmRTZXJpYWxOb1N1YikNCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCg0KICAgICAgICB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaXRlbS5wYXJ0aWFsT3V0Ym91bmRGbGFnID0gTnVtYmVyKGl0ZW0ucGFydGlhbE91dGJvdW5kRmxhZykNCiAgICAgICAgfSkNCg0KICAgICAgICAvLyDmm7TmlrDnrrHmlbDjgIHmr5vph43jgIHkvZPnp68NCiAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0udG90YWxCb3hlcyA9IDANCiAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0udG90YWxHcm9zc1dlaWdodCA9IDANCiAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0udG90YWxWb2x1bWUgPSAwDQogICAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzQ2FyZ29EZXRhaWxzTGlzdCA/IGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsQm94ZXMgPSBjdXJyZW5jeShpdGVtLmJveENvdW50KS5hZGQodGhpcy5vdXRib3VuZEZvcm0udG90YWxCb3hlcykudmFsdWUNCiAgICAgICAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsR3Jvc3NXZWlnaHQgPSBjdXJyZW5jeShpdGVtLnVuaXRHcm9zc1dlaWdodCkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsR3Jvc3NXZWlnaHQpLnZhbHVlDQogICAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS50b3RhbFZvbHVtZSA9IGN1cnJlbmN5KGl0ZW0udW5pdFZvbHVtZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsVm9sdW1lKS52YWx1ZQ0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KQ0KICAgICAgICBpZiAodHlwZSA9PT0gMCkgew0KICAgICAgICAgIGFkZE91dGJvdW5kcmVjb3JkKHRoaXMub3V0Ym91bmRGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIC8vIOWIl+ihqOWFi+mahuS4gOS7vSzmiZPkuIrpooTlh7rku5PmoIflv5cNCiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLnByZU91dGJvdW5kRmxhZyA9ICIxIg0KICAgICAgICAgICAgICBpdGVtLnJzQ2FyZ29EZXRhaWxzTGlzdCA/IGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBpdGVtLnByZU91dGJvdW5kRmxhZyA9ICIxIg0KICAgICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICAgIH0pIDogbnVsbA0KICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgcHJlT3V0Ym91bmRJbnZlbnRvcnkoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi6aKE5Ye65LuT5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSBmYWxzZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09IDEpIHsNCiAgICAgICAgICAvLyDnm7TmjqXlh7rku5MNCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS5wcmVPdXRib3VuZEZsYWcgPSAiMSINCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZERhdGUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKQ0KDQogICAgICAgICAgdXBkYXRlT3V0Ym91bmRyZWNvcmQodGhpcy5vdXRib3VuZEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgY29uc3Qgb3V0Ym91bmRSZWNvcmRJZCA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICAgIC8vIOWIl+ihqOWFi+mahuS4gOS7vSzmiZPkuIrlh7rku5PmoIflv5cNCiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLm91dGJvdW5kUmVjb3JkSWQgPSBvdXRib3VuZFJlY29yZElkDQogICAgICAgICAgICAgIGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0ID8gaXRlbS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCA9IG91dGJvdW5kUmVjb3JkSWQNCiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgIC8vIOWHuuS7kw0KICAgICAgICAgICAgb3V0Ym91bmRJbnZlbnRvcnkoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Ye65LuT5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSBmYWxzZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09IDIpIHsNCiAgICAgICAgICAvLyDnm7TmjqXlh7rku5MNCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS5wcmVPdXRib3VuZEZsYWcgPSAiMCINCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZERhdGUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKQ0KDQogICAgICAgICAgYWRkT3V0Ym91bmRyZWNvcmQodGhpcy5vdXRib3VuZEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgY29uc3Qgb3V0Ym91bmRSZWNvcmRJZCA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgICAgIC8vIOWIl+ihqOWFi+mahuS4gOS7vSzmiZPkuIrlh7rku5PmoIflv5cNCiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLm91dGJvdW5kUmVjb3JkSWQgPSBvdXRib3VuZFJlY29yZElkDQogICAgICAgICAgICAgIGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0ID8gaXRlbS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCA9IG91dGJvdW5kUmVjb3JkSWQNCiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgIC8vIOWHuuS7kw0KICAgICAgICAgICAgb3V0Ym91bmRJbnZlbnRvcnkoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Ye65LuT5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSBmYWxzZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2UgaWYgKHR5cGUgPT09IDMpIHsNCiAgICAgICAgICAvLyDnu5Pnrpfku5Pnp58NCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS5pc1JlbnRTZXR0bGVtZW50ID0gMSAvLyDku5Pnp5/nu5PnrpforrDlvZUNCiAgICAgICAgICBhZGRPdXRib3VuZHJlY29yZCh0aGlzLm91dGJvdW5kRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICBjb25zdCBvdXRib3VuZFJlY29yZElkID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgICAgLy8g5YiX6KGo5YWL6ZqG5LiA5Lu9LOaJk+S4iuWHuuS7k+agh+W/lw0KICAgICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAgIGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCA9IG91dGJvdW5kUmVjb3JkSWQNCiAgICAgICAgICAgICAgaXRlbS5yZW50YWxTZXR0bGVtZW50RGF0ZSA9IHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kRGF0ZQ0KICAgICAgICAgICAgICBpdGVtLnJzQ2FyZ29EZXRhaWxzTGlzdCA/IGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBpdGVtLm91dGJvdW5kUmVjb3JkSWQgPSBvdXRib3VuZFJlY29yZElkDQogICAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgICAgfSkgOiBudWxsDQogICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICBzZXR0bGVtZW50KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIue7k+eul+aIkOWKnyIpDQogICAgICAgICAgICAgIHRoaXMubG9hZFByZU91dGJvdW5kSW52ZW50b3J5TGlzdCgpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc3Qgb3V0Ym91bmRSZWNvcmRJZCA9IHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kUmVjb3JkSWQNCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS5wcmVPdXRib3VuZEZsYWcgPSAiMCINCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZERhdGUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKQ0KICAgICAgICAgIHVwZGF0ZU91dGJvdW5kcmVjb3JkKHRoaXMub3V0Ym91bmRGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIC8vIOWIl+ihqOWFi+mahuS4gOS7vSzmiZPkuIrlh7rku5PmoIflv5cNCiAgICAgICAgICAgIGxldCBkYXRhID0gdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLm91dGJvdW5kUmVjb3JkSWQgPSBvdXRib3VuZFJlY29yZElkDQogICAgICAgICAgICAgIGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0ID8gaXRlbS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCA9IG91dGJvdW5kUmVjb3JkSWQNCiAgICAgICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pDQoNCiAgICAgICAgICAgIC8vIOWHuuS7kw0KICAgICAgICAgICAgb3V0Ym91bmRJbnZlbnRvcnkoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Ye65LuT5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSBmYWxzZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5qC55o2u5Ye65bqT5L+h5oGv5Yqg6L295b6F5Ye65bqT55qE6K6w5b2VDQogICAgbG9hZFByZU91dGJvdW5kSW52ZW50b3J5TGlzdCgpIHsNCiAgICAgIHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0TG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCg0KICAgICAgLy8g5p6E5bu65p+l6K+i5Y+C5pWwDQogICAgICBjb25zdCBxdWVyeVBhcmFtcyA9IHsNCiAgICAgICAgc3FkUGxhbm5lZE91dGJvdW5kRGF0ZTogdGhpcy5vdXRib3VuZEZvcm0ucGxhbm5lZE91dGJvdW5kRGF0ZSwNCiAgICAgICAgY2xpZW50Q29kZTogdGhpcy5vdXRib3VuZEZvcm0uY2xpZW50Q29kZSwNCiAgICAgICAgaW52ZW50b3J5U3RhdHVzOiAiMCINCiAgICAgIH0NCg0KICAgICAgLy8g5qC55o2u5Ye65bqT57G75Z6L5re75Yqg6aKE5Ye65bqT5qCH5b+XDQogICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5wcmVPdXRib3VuZEZsYWcpIHsNCiAgICAgICAgcXVlcnlQYXJhbXMucHJlT3V0Ym91bmRGbGFnID0gdGhpcy5xdWVyeVBhcmFtcy5wcmVPdXRib3VuZEZsYWcNCiAgICAgIH0NCg0KICAgICAgaWYgKHRoaXMucXVlcnlQYXJhbXMucHJlT3V0Ym91bmRSZWNvcmRJZCkgew0KICAgICAgICBxdWVyeVBhcmFtcy5wcmVPdXRib3VuZFJlY29yZElkID0gdGhpcy5xdWVyeVBhcmFtcy5wcmVPdXRib3VuZFJlY29yZElkDQogICAgICB9DQoNCiAgICAgIC8vIOWPkei1t+ivt+axgg0KICAgICAgbGlzdEludmVudG9yeXMocXVlcnlQYXJhbXMpDQogICAgICAgIC50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAvLyDlpITnkIblk43lupTmlbDmja4NCiAgICAgICAgICB0aGlzLnByZU91dGJvdW5kSW52ZW50b3J5TGlzdCA9IHJlc3BvbnNlLnJvd3MuZmlsdGVyKGl0ZW0gPT4gIWl0ZW0ucGFja2FnZVRvKQ0KICAgICAgICAgIHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0ID8gcmVzcG9uc2Uucm93cy5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAvLyDorqHnrpfooaXmlLblhaXku5PotLkNCiAgICAgICAgICAgIGlmIChpdGVtLmluY2x1ZGVzSW5ib3VuZEZlZSA9PT0gMCkgew0KICAgICAgICAgICAgICBjb25zdCByZWNlaXZlZEZlZSA9IE51bWJlcihpdGVtLnJlY2VpdmVkU3RvcmFnZUZlZSB8fCAwKQ0KICAgICAgICAgICAgICBjb25zdCBpbmJvdW5kRmVlID0gTnVtYmVyKGl0ZW0uaW5ib3VuZEZlZSB8fCAwKQ0KICAgICAgICAgICAgICBjb25zdCBkaWZmZXJlbmNlID0gY3VycmVuY3koaW5ib3VuZEZlZSkuc3VidHJhY3QocmVjZWl2ZWRGZWUpLnZhbHVlDQoNCiAgICAgICAgICAgICAgLy8g5Y+q5pyJ5b2T5beu5YC85aSn5LqOMOaXtuaJjeiuvue9ruihpeaUtui0ueeUqA0KICAgICAgICAgICAgICBpdGVtLmFkZGl0aW9uYWxTdG9yYWdlRmVlID0gZGlmZmVyZW5jZSA+IDAgPyBkaWZmZXJlbmNlIDogMA0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgaXRlbS5hZGRpdGlvbmFsU3RvcmFnZUZlZSA9IDANCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5omT5YyF566x77yM5qCH6K6w5Li65pyJ5a2Q6IqC54K5DQogICAgICAgICAgICBpZiAoaXRlbS5wYWNrYWdlUmVjb3JkID09PSAiMSIpIHsNCiAgICAgICAgICAgICAgaXRlbS5oYXNDaGlsZHJlbiA9IHRydWUNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kUmVjb3JkSWQgPT09IGl0ZW0ucHJlT3V0Ym91bmRSZWNvcmRJZCkgew0KICAgICAgICAgICAgICB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5wdXNoKGl0ZW0pDQogICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihpdGVtLCB0cnVlKQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICAgIH0pIDogW10NCg0KICAgICAgICAgIC8vIOabtOaWsOaAu+aVsA0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbCB8fCAwDQoNCiAgICAgICAgICAvLyDlpoLmnpzmmK/mma7pgJrlh7rlupPnsbvlnovvvIzoh6rliqjpgInkuK3pooTlh7rlupPmoIforrDnmoTooYwNCiAgICAgICAgICBpZiAodGhpcy5vdXRib3VuZFR5cGUgPT09IDAgJiYgdGhpcy4kcmVmcy50YWJsZSkgew0KICAgICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgICB0aGlzLnByZU91dGJvdW5kSW52ZW50b3J5TGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGlmIChpdGVtLnByZU91dGJvdW5kRmxhZyA9PT0gMSkgew0KICAgICAgICAgICAgICAgICAgdGhpcy4kcmVmcy50YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24oaXRlbSwgdHJ1ZSkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLliqDovb3pooTlh7rlupPlupPlrZjliJfooajlpLHotKU6IiwgZXJyb3IpDQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yqg6L296aKE5Ye65bqT5bqT5a2Y5YiX6KGo5aSx6LSlIikNCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgIHRoaXMucXVlcnlQYXJhbXMucHJlT3V0Ym91bmRSZWNvcmRJZCA9IG51bGwNCiAgICAgICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByZU91dGJvdW5kRmxhZyA9IG51bGwNCiAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgICAgIHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0TG9hZGluZyA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICAvLyDpgInmi6npooTlh7rku5PorrDlvZUNCiAgICBoYW5kbGVPdXRib3VuZChzZWxlY3RlZFJvd3MpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICB0aGlzLm91dGJvdW5kRm9ybSA9IHNlbGVjdGVkUm93cw0KICAgICAgdGhpcy5vdXRib3VuZFR5cGUgPSAxDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByZU91dGJvdW5kUmVjb3JkSWQgPSB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZFJlY29yZElkDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnByZU91dGJvdW5kRmxhZyA9ICIxIg0KICAgICAgdGhpcy5sb2FkUHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0KCkNCiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgIH0sDQogICAgLy8g5re75Yqg6aKE5Ye65LuT6K6w5b2VDQogICAgaGFuZGxlUHJlT3V0Ym91bmQoKSB7DQogICAgICB0aGlzLm91dGJvdW5kUmVzZXQoKQ0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0ub3V0Ym91bmRIYW5kbGVyID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lLnNwbGl0KCIgIilbMV0NCiAgICAgIHRoaXMub3V0Ym91bmRUeXBlID0gMA0KICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSB0cnVlDQogICAgfSwNCiAgICAvLyDnm7TmjqXlh7rku5MNCiAgICBoYW5kbGVEaXJlY3RPdXRib3VuZCgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZEhhbmRsZXIgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWUuc3BsaXQoIiAiKVsxXQ0KICAgICAgdGhpcy5vdXRib3VuZFR5cGUgPSAyDQogICAgICB0aGlzLm9wZW5PdXRib3VuZCA9IHRydWUNCiAgICB9LA0KICAgIC8vIOe7k+eul+S7k+ennw0KICAgIGhhbmRsZVJlbnRTZXR0bGVtZW50KCkgew0KICAgICAgdGhpcy5vdXRib3VuZFJlc2V0KCkNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kSGFuZGxlciA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZS5zcGxpdCgiICIpWzFdDQogICAgICB0aGlzLm91dGJvdW5kVHlwZSA9IDMNCiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgIH0sDQogICAgcGFyc2VUaW1lLA0KICAgIGhhbmRsZU91dGJvdW5kQ2FyZ29EZXRhaWxTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uLCByb3cpIHsNCiAgICAgIHJvdy5vdXRib3VuZENhcmdvRGV0YWlsc0xpc3QgPSBzZWxlY3Rpb24NCiAgICAgIHRoaXMuc2VsZWN0ZWRDYXJnb0RldGFpbCA9IHNlbGVjdGlvbg0KICAgIH0sDQogICAgLy8g5Yik5pat5b2T5YmN6KGM5piv5ZCm6KKr6YCJ5LitDQogICAgaXNSb3dTZWxlY3RlZChyb3cpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkQ2FyZ29EZXRhaWwuaW5jbHVkZXMocm93KQ0KICAgIH0sDQogICAgZ2V0U3VtbWFyaWVzKHBhcmFtKSB7DQogICAgICBjb25zdCB7Y29sdW1ucywgZGF0YX0gPSBwYXJhbQ0KICAgICAgY29uc3Qgc3VtcyA9IFtdDQogICAgICBjb25zdCBzdGF0aXN0aWNhbEZpZWxkID0gWw0KICAgICAgICAicmVjZWl2ZWRTdXBwbGllciIsICJ0b3RhbEJveGVzIiwgInVucGFpZEluYm91bmRGZWUiLCAidG90YWxHcm9zc1dlaWdodCIsDQogICAgICAgICJ0b3RhbFZvbHVtZSIsICJyZWNlaXZlZFN0b3JhZ2VGZWUiLCAidW5wYWlkVW5sb2FkaW5nRmVlIiwgImxvZ2lzdGljc0FkdmFuY2VGZWUiLA0KICAgICAgICAicmVudGFsQmFsYW5jZUZlZSIsICJvdmVyZHVlUmVudGFsRmVlIiwgImFkZGl0aW9uYWxTdG9yYWdlRmVlIiwgInVucGFpZFVubG9hZGluZ0ZlZSIsDQogICAgICAgICJ1bnBhaWRQYWNraW5nRmVlIiwgInJlY2VpdmVkVW5sb2FkaW5nRmVlIiwgInJlY2VpdmVkUGFja2luZ0ZlZSINCiAgICAgIF0NCiAgICAgIC8vIOaxh+aAu+e7k+aenOWtmOWCqOWvueixoQ0KICAgICAgY29uc3Qgc3VtbWFyeVJlc3VsdHMgPSB7fQ0KICAgICAgY29sdW1ucy5mb3JFYWNoKChjb2x1bW4sIGluZGV4KSA9PiB7DQogICAgICAgIGlmIChpbmRleCA9PT0gMCkgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuaxh+aAuyIgLy8g56ys5LiA5YiX5pi+56S65paH5pysDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgY29uc3QgcHJvcCA9IGNvbHVtbi5wcm9wZXJ0eQ0KICAgICAgICAgIGxldCB0b3RhbCA9IDA7IC8vIOWcqOadoeS7tuWdl+S5i+WJjeWumuS5iXRvdGFs5Y+Y6YePDQoNCiAgICAgICAgICBpZiAocHJvcCA9PT0gInRvdGFsQm94ZXMiIHx8IHByb3AgPT09ICJ0b3RhbFZvbHVtZSIgfHwgcHJvcCA9PT0gInRvdGFsR3Jvc3NXZWlnaHQiKSB7DQogICAgICAgICAgICB0b3RhbCA9IHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0LnJlZHVjZSgoc3VtLCByb3cpID0+IHsNCiAgICAgICAgICAgICAgaWYgKHJvdy5wYWNrYWdlVG8pIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gY3VycmVuY3koc3VtKS5hZGQoTnVtYmVyKHJvd1twcm9wXSkgfHwgMCkudmFsdWUNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICByZXR1cm4gc3VtDQogICAgICAgICAgICB9LCAwKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICB0b3RhbCA9IHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0LnJlZHVjZSgoc3VtLCByb3cpID0+DQogICAgICAgICAgICAgIGN1cnJlbmN5KHN1bSkuYWRkKE51bWJlcihyb3dbcHJvcF0pIHx8IDApLnZhbHVlLCAwKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gdG90YWwNCiAgICAgICAgICAvLyDnjrDlnKjlj6/ku6XlronlhajlnLDkvb/nlKh0b3RhbA0KICAgICAgICAgIHN1bW1hcnlSZXN1bHRzW2NvbHVtbi5wcm9wZXJ0eV0gPSB0b3RhbA0KICAgICAgICB9DQogICAgICB9KQ0KDQogICAgICAvLyDlpoLmnpzpnIDopoHlsIbmsYfmgLvnu5PmnpzotYvlgLzliLDooajljZXlrZfmrrXkuK3vvIzlj6/ku6XlnKjmraTlpITmk43kvZwNCiAgICAgIC8vIOWBh+iuvuihqOWNleWtl+auteeahOWRveWQjeS4jue7n+iuoeWtl+auteS4gOiHtA0KICAgICAgT2JqZWN0LmtleXMoc3VtbWFyeVJlc3VsdHMpLmZvckVhY2goZmllbGQgPT4gew0KICAgICAgICBpZiAodGhpcy5vdXRib3VuZEZvcm0pIHsNCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybVtmaWVsZF0gPSBzdW1tYXJ5UmVzdWx0c1tmaWVsZF0gIC8vIOWwhuaxh+aAu+WAvOi1i+e7meihqOWNleWtl+autQ0KICAgICAgICB9DQogICAgICB9KQ0KDQogICAgICB0aGlzLmNvdW50U3VtbWFyeSgpDQoNCiAgICAgIHJldHVybiBzdW1zDQogICAgfSwNCiAgICBoYW5kbGVPdXRib3VuZFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIC8vIOato+ehruiOt+WPluihqOagvOaVsOaNriAtIOmAmui/h2RhdGHlsZ7mgKcNCiAgICAgIGNvbnN0IHRyZWVEYXRhID0gdGhpcy4kcmVmcy50YWJsZS5zdG9yZS5zdGF0ZXMuZGF0YQ0KICAgICAgLy8g6I635Y+W5LmL5YmN55qE6YCJ5oup54q25oCB77yM55So5LqO5q+U6L6D5Y+Y5YyWDQogICAgICBjb25zdCBwcmV2aW91c0lkcyA9IFsuLi50aGlzLmlkc10NCg0KICAgICAgLy8g5riF56m65b2T5YmN6YCJ5oupDQogICAgICB0aGlzLmlkcyA9IFtdDQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmludmVudG9yeUlkKQ0KICAgICAgLy8g5om+5Ye65paw6YCJ5Lit5ZKM5Y+W5raI6YCJ5Lit55qE6aG5DQogICAgICBjb25zdCBuZXdseVNlbGVjdGVkID0gdGhpcy5pZHMuZmlsdGVyKGlkID0+ICFwcmV2aW91c0lkcy5pbmNsdWRlcyhpZCkpDQogICAgICBjb25zdCBuZXdseURlc2VsZWN0ZWQgPSBwcmV2aW91c0lkcy5maWx0ZXIoaWQgPT4gIXRoaXMuaWRzLmluY2x1ZGVzKGlkKSkNCg0KICAgICAgdGhpcy5zZWxlY3RPdXRib3VuZExpc3QgPSBzZWxlY3Rpb24NCiAgICAgIHRoaXMuJHJlZnMudGFibGUuZG9MYXlvdXQoKSAvLyDliLfmlrDooajmoLzluIPlsYANCg0KICAgICAgLy8g5qC55o2u5LuT56ef57uT566X6Iez77yIcmVudGFsX3NldHRsZW1lbnRfZGF0Ze+8ie+8jOiuoeeul+ivpeadoeW6k+WtmOeahOenn+mHkQ0KICAgICAgLy8g77yIIOWHuuW6k+W9k+WkqS3ku5Pnp5/nu5Pnrpfoh7Mt5YWN56ef5pyfIO+8iSAqIOenn+mHkeWNleS7tw0KICAgICAgc2VsZWN0aW9uLm1hcChpdGVtID0+IHsNCiAgICAgICAgY29uc3QgZGF0ZTEgPSBtb21lbnQodGhpcy5vdXRib3VuZEZvcm0ub3V0Ym91bmREYXRlKQ0KICAgICAgICBjb25zdCBkYXRlMiA9IG1vbWVudChpdGVtLnJlbnRhbFNldHRsZW1lbnREYXRlKQ0KICAgICAgICBpdGVtLnJlbnRhbERheXMgPSBkYXRlMS5kaWZmKGRhdGUyLCAiZGF5cyIpICsgMSAvLyDlt67ot53nmoTlpKnmlbANCiAgICAgICAgbGV0IHZvbHVtbiA9IGl0ZW0udG90YWxWb2x1bWUNCg0KICAgICAgICBpZiAoIU51bWJlci5pc05hTihpdGVtLnJlbnRhbERheXMpICYmIGl0ZW0ucmVudGFsRGF5cyA+IDApIHsNCiAgICAgICAgICAvLyDlh7rku5PmlrnlvI/kuI3mmK/mlbTmn5zmsqHmnInlhY3np5/lpKnmlbANCiAgICAgICAgICBpZiAodGhpcy5vdXRib3VuZEZvcm0ub3V0Ym91bmRUeXBlICE9PSAi5pW05p+cIikgew0KICAgICAgICAgICAgaXRlbS5vdmVyZHVlUmVudGFsRmVlID0gY3VycmVuY3koaXRlbS5yZW50YWxEYXlzKS5tdWx0aXBseShpdGVtLm92ZXJkdWVSZW50YWxVbml0UHJpY2UpLm11bHRpcGx5KHZvbHVtbikudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgbGV0IGRheXMgPSBjdXJyZW5jeShpdGVtLnJlbnRhbERheXMpLnN1YnRyYWN0KGl0ZW0uZnJlZVN0YWNrUGVyaW9kKS52YWx1ZQ0KICAgICAgICAgICAgZGF5cyA9IGRheXMgPiAwID8gZGF5cyA6IDANCiAgICAgICAgICAgIGl0ZW0ucmVudGFsRGF5cyA9IGRheXMNCiAgICAgICAgICAgIGl0ZW0ub3ZlcmR1ZVJlbnRhbEZlZSA9IGN1cnJlbmN5KGRheXMpLm11bHRpcGx5KGl0ZW0ub3ZlcmR1ZVJlbnRhbFVuaXRQcmljZSkubXVsdGlwbHkodm9sdW1uKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWkhOeQhuaWsOmAieS4reeahOaJk+WMheeuse+8muiHquWKqOmAieS4reWFtuWtkOmhuQ0KICAgICAgICBpZiAoaXRlbS5wYWNrYWdlUmVjb3JkID09PSAiMSIgJiYgbmV3bHlTZWxlY3RlZC5pbmNsdWRlcyhpdGVtLmludmVudG9yeUlkKSkgew0KICAgICAgICAgIC8vIOWmguaenOaYr+aWsOmAieS4reeahOaJk+WMheeuseiKgueCuQ0KDQogICAgICAgICAgLy8g5Zyo5qCR5b2i6KGo5qC85pWw5o2u5Lit5om+5Yiw5a+55bqU55qE6IqC54K5DQogICAgICAgICAgY29uc3QgcGFyZW50Tm9kZSA9IHRyZWVEYXRhLmZpbmQobm9kZSA9PiBub2RlLmludmVudG9yeUlkID09PSBpdGVtLmludmVudG9yeUlkKQ0KDQogICAgICAgICAgLy8g5qOA5p+l6IqC54K55piv5ZCm5bey5bGV5byAKOW3suaciWNoaWxkcmVu5bGe5oCn5LiU5pyJ5YaF5a65KQ0KICAgICAgICAgIGlmIChwYXJlbnROb2RlICYmIHBhcmVudE5vZGUuY2hpbGRyZW4gJiYgcGFyZW50Tm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAvLyDlpoLmnpzoioLngrnlt7LlsZXlvIDvvIznm7TmjqXpgInkuK3lhbbmiYDmnInlrZDpobkNCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICBwYXJlbnROb2RlLmNoaWxkcmVuLmZvckVhY2goY2hpbGQgPT4gew0KICAgICAgICAgICAgICAgIGlmICghdGhpcy5pZHMuaW5jbHVkZXMoY2hpbGQuaW52ZW50b3J5SWQpKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLmlkcy5wdXNoKGNoaWxkLmludmVudG9yeUlkKQ0KICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RPdXRib3VuZExpc3QucHVzaChjaGlsZCkNCiAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKGNoaWxkLCB0cnVlKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgIH0sIDUwKSAvLyDnu5nkuIDngrnml7bpl7TorqlVSeabtOaWsA0KICAgICAgICAgIH0gZWxzZSBpZiAocGFyZW50Tm9kZSAmJiAhcGFyZW50Tm9kZS5jaGlsZHJlbkxvYWRlZCAmJiBwYXJlbnROb2RlLmhhc0NoaWxkcmVuKSB7DQogICAgICAgICAgICAvLyDlpoLmnpzoioLngrnmnKrlsZXlvIDkuJTmnKrliqDovb3ov4fkvYbmnInlrZDoioLngrnmoIforrANCiAgICAgICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW5Mb2FkZWQgPSB0cnVlDQoNCiAgICAgICAgICAgIC8vIOaJi+WKqOWxleW8gOihjO+8jOinpuWPkeaHkuWKoOi9vQ0KICAgICAgICAgICAgdGhpcy4kcmVmcy50YWJsZS50b2dnbGVSb3dFeHBhbnNpb24ocGFyZW50Tm9kZSwgdHJ1ZSkNCg0KICAgICAgICAgICAgLy8g55uR5ZCs5a2Q6IqC54K55Yqg6L295a6M5oiQ5ZCO5YaN6YCJ5Lit5a6D5LusDQogICAgICAgICAgICAvLyDov5nph4zliKnnlKjkuoZsb2FkQ2hpbGRJbnZlbnRvcnnmlrnms5XkuK3nmoTpgLvovpHvvIzlroPkvJrlnKjlrZDoioLngrnliqDovb3lkI7lpITnkIbpgInkuK3nirbmgIENCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIC8vIOWkhOeQhuWPlua2iOmAieS4reeahOaJk+WMheeuse+8muWPlua2iOmAieS4reWFtuWtkOmhuQ0KICAgICAgbmV3bHlEZXNlbGVjdGVkLmZvckVhY2gocGFyZW50SWQgPT4gew0KICAgICAgICAvLyDmib7lh7rlr7nlupTnmoTniLboioLngrkNCiAgICAgICAgY29uc3QgcGFyZW50Tm9kZSA9IHRyZWVEYXRhLmZpbmQobm9kZSA9Pg0KICAgICAgICAgIG5vZGUuaW52ZW50b3J5SWQgPT09IHBhcmVudElkICYmIG5vZGUucGFja2FnZVJlY29yZCA9PT0gIjEiDQogICAgICAgICkNCg0KICAgICAgICBpZiAocGFyZW50Tm9kZSAmJiBwYXJlbnROb2RlLmNoaWxkcmVuICYmIHBhcmVudE5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOWPlua2iOmAieS4reaJgOacieWtkOmhuQ0KICAgICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW4uZm9yRWFjaChjaGlsZCA9PiB7DQogICAgICAgICAgICBjb25zdCBjaGlsZEluZGV4ID0gdGhpcy5pZHMuaW5kZXhPZihjaGlsZC5pbnZlbnRvcnlJZCkNCiAgICAgICAgICAgIGlmIChjaGlsZEluZGV4ID4gLTEpIHsNCiAgICAgICAgICAgICAgLy8g5LuO6YCJ5Lit5YiX6KGo5Lit56e76ZmkDQogICAgICAgICAgICAgIHRoaXMuaWRzLnNwbGljZShjaGlsZEluZGV4LCAxKQ0KICAgICAgICAgICAgICBjb25zdCBpdGVtSW5kZXggPSB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5maW5kSW5kZXgoDQogICAgICAgICAgICAgICAgaXRlbSA9PiBpdGVtLmludmVudG9yeUlkID09PSBjaGlsZC5pbnZlbnRvcnlJZA0KICAgICAgICAgICAgICApDQogICAgICAgICAgICAgIGlmIChpdGVtSW5kZXggPiAtMSkgew0KICAgICAgICAgICAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0LnNwbGljZShpdGVtSW5kZXgsIDEpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgLy8g5ZyoVUnkuIrlj5bmtojpgInkuK0NCiAgICAgICAgICAgICAgdGhpcy4kcmVmcy50YWJsZS50b2dnbGVSb3dTZWxlY3Rpb24oY2hpbGQsIGZhbHNlKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIHRoaXMuY291bnRTdW1tYXJ5KCkNCiAgICB9LA0KICAgIHNlbGVjdENvbnRhaW5lclR5cGUodHlwZSkgew0KICAgICAgc3dpdGNoICh0eXBlKSB7DQogICAgICAgIGNhc2UgIjIwR1AiOg0KICAgICAgICAgIHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZVF1b3RlID0gdGhpcy5jbGllbnRSb3cucmF0ZTIwZ3ANCiAgICAgICAgICBicmVhaw0KICAgICAgICBjYXNlICI0MEhRIjoNCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSA9IHRoaXMuY2xpZW50Um93LnJhdGU0MGhxDQogICAgICAgICAgYnJlYWsNCg0KICAgICAgfQ0KICAgIH0sDQogICAgb3V0Ym91bmRDbGllbnQocm93KSB7DQogICAgICB0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSA9IHJvdy5yYXRlTGNsDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5mcmVlU3RhY2tEYXlzID0gcm93LmZyZWVTdGFja1BlcmlvZA0KICAgICAgdGhpcy5jbGllbnRSb3cgPSByb3cNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLm92ZXJkdWVSZW50YWxVbml0UHJpY2UgPSByb3cub3ZlcmR1ZVJlbnQNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLmNsaWVudE5hbWUgPSByb3cuY2xpZW50TmFtZQ0KICAgICAgLy8gdGhpcy5vdXRib3VuZEZvcm0ud29ya2VyTG9hZGluZ0ZlZT1yb3cud29ya2VyTG9hZGluZ0ZlZQ0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKQ0KICAgIH0sDQogICAgLyoqIOafpeivouWHuuS7k+iusOW9leWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsaXN0T3V0Ym91bmRyZWNvcmQodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMub3V0Ym91bmRyZWNvcmRMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICB9LA0KICAgIG91dGJvdW5kUmVzZXQoKSB7DQogICAgICB0aGlzLm91dGJvdW5kRm9ybSA9IHsNCiAgICAgICAgb3V0Ym91bmRSZWNvcmRJZDogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRTdXBwbGllcjogbnVsbCwNCiAgICAgICAgb3V0Ym91bmRObzogbnVsbCwNCiAgICAgICAgY2xpZW50Q29kZTogbnVsbCwNCiAgICAgICAgY2xpZW50TmFtZTogbnVsbCwNCiAgICAgICAgb3BlcmF0b3I6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiBudWxsLA0KICAgICAgICB3b3JrZXJMb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB3YXJlaG91c2VDb2xsZWN0aW9uOiBudWxsLA0KICAgICAgICBjb2xsZWN0aW9uTm90ZXM6IG51bGwsDQogICAgICAgIHRvdGFsQm94ZXM6IG51bGwsDQogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLA0KICAgICAgICB0b3RhbFJvd3M6IG51bGwsDQogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwNCiAgICAgICAgdW5wYWlkVW5sb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRQYWNrYWdpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBvcGVyYXRpb25SZXF1aXJlbWVudDogbnVsbCwNCiAgICAgICAgZnJlZVN0YWNrRGF5czogbnVsbCwNCiAgICAgICAgb3ZlcmR1ZVVuaXRQcmljZTogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRGcm9tU3VwcGxpZXI6IG51bGwsDQogICAgICAgIHVucmVjZWl2ZWRGcm9tQ3VzdG9tZXI6IG51bGwsDQogICAgICAgIHJlY2VpdmVkRnJvbUN1c3RvbWVyOiBudWxsLA0KICAgICAgICBjdXN0b21lclJlY2VpdmFibGVCYWxhbmNlOiBudWxsLA0KICAgICAgICBwYXlhYmxlVG9Xb3JrZXI6IG51bGwsDQogICAgICAgIHByb21pc3NvcnlOb3RlU2FsZXM6IG51bGwsDQogICAgICAgIHByb21pc3NvcnlOb3RlQ29zdDogbnVsbCwNCiAgICAgICAgcHJvbWlzc29yeU5vdGVHcm9zc1Byb2ZpdDogbnVsbCwNCiAgICAgICAgb3V0Ym91bmREYXRlOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKQ0KICAgICAgfQ0KICAgICAgdGhpcy5wcmVPdXRib3VuZEludmVudG9yeUxpc3QgPSBbXQ0KICAgICAgdGhpcy5yZXNldEZvcm0oIm91dGJvdW5kRm9ybSIpDQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgb3V0Ym91bmREYXRlOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKSwNCiAgICAgICAgb3V0Ym91bmRSZWNvcmRJZDogbnVsbCwNCiAgICAgICAgb3V0Ym91bmRObzogbnVsbCwNCiAgICAgICAgY2xpZW50Q29kZTogbnVsbCwNCiAgICAgICAgY2xpZW50TmFtZTogbnVsbCwNCiAgICAgICAgb3BlcmF0b3I6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiBudWxsLA0KICAgICAgICB3b3JrZXJMb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB3YXJlaG91c2VDb2xsZWN0aW9uOiBudWxsLA0KICAgICAgICBjb2xsZWN0aW9uTm90ZXM6IG51bGwsDQogICAgICAgIHRvdGFsQm94ZXM6IG51bGwsDQogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLA0KICAgICAgICB0b3RhbFJvd3M6IG51bGwsDQogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwNCiAgICAgICAgdW5wYWlkVW5sb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRQYWNrYWdpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBmcmVlU3RhY2tEYXlzOiBudWxsLA0KICAgICAgICBvdmVyZHVlVW5pdFByaWNlOiBudWxsDQogICAgICB9DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpDQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIikNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlKHJvdykgew0KICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCLnoa7orqTopoFcIiIgKyB0ZXh0ICsgIuWQl++8nyIpLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gY2hhbmdlU3RhdHVzKHJvdy5vdXRib3VuZFJlY29yZElkLCByb3cuc3RhdHVzKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKQ0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgew0KICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vdXRib3VuZFJlY29yZElkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye65LuT6K6w5b2VIg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgY29uc3Qgb3V0Ym91bmRSZWNvcmRJZCA9IHJvdy5vdXRib3VuZFJlY29yZElkIHx8IHRoaXMuaWRzDQogICAgICBnZXRPdXRib3VuZHJlY29yZChvdXRib3VuZFJlY29yZElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Ye65LuT6K6w5b2VIg0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1sib3V0Ym91bmRGb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5vdXRib3VuZEZvcm0ub3V0Ym91bmRSZWNvcmRJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVPdXRib3VuZHJlY29yZCh0aGlzLm91dGJvdW5kRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRPdXRib3VuZHJlY29yZCh0aGlzLm91dGJvdW5kRm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpDQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBvdXRib3VuZFJlY29yZElkcyA9IHJvdy5vdXRib3VuZFJlY29yZElkIHx8IHRoaXMuaWRzDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCLmmK/lkKbnoa7orqTliKDpmaTlh7rku5PorrDlvZXnvJblj7fkuLpcIiIgKyBvdXRib3VuZFJlY29yZElkcyArICJcIueahOaVsOaNrumhue+8nyIpLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gZGVsT3V0Ym91bmRyZWNvcmQob3V0Ym91bmRSZWNvcmRJZHMpDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIikNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgic3lzdGVtL291dGJvdW5kcmVjb3JkL2V4cG9ydCIsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYG91dGJvdW5kcmVjb3JkXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgLy8g5re75Yqg5pCc57Si5bm25rua5Yqo5Yiw5Yy56YWN6KGM55qE5pa55rOVDQogICAgaGFuZGxlU2VhcmNoRW50ZXIoKSB7DQogICAgICBpZiAoIXRoaXMuc2VhcmNoKSByZXR1cm4NCg0KICAgICAgLy8g5p+l5om+5Yy56YWN55qE6KGM57Si5byVDQogICAgICBjb25zdCBpbmRleCA9IHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0LmZpbmRJbmRleCgNCiAgICAgICAgaXRlbSA9PiB7DQogICAgICAgICAgLy8g56Gu5L+dIGluYm91bmRTZXJpYWxObyDlrZjlnKjkuJTkuLrlrZfnrKbkuLINCiAgICAgICAgICBjb25zdCBzZXJpYWxObyA9IFN0cmluZyhpdGVtLmluYm91bmRTZXJpYWxObyB8fCAiIikNCiAgICAgICAgICBjb25zdCBzZWFyY2hWYWx1ZSA9IFN0cmluZyh0aGlzLnNlYXJjaCkNCiAgICAgICAgICAvLyDmiZPljbDmr4/mrKHmr5TovoPnmoTlgLzvvIzluK7liqnosIPor5UNCiAgICAgICAgICByZXR1cm4gc2VyaWFsTm8uaW5jbHVkZXMoc2VhcmNoVmFsdWUpDQogICAgICAgIH0NCiAgICAgICkNCg0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgLy8g6I635Y+W6KGo5qC8RE9NDQogICAgICAgIGNvbnN0IHRhYmxlID0gdGhpcy4kcmVmcy50YWJsZQ0KDQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAvLyDojrflj5booajmoLznmoTmu5rliqjlrrnlmagNCiAgICAgICAgICBjb25zdCBzY3JvbGxXcmFwcGVyID0gdGFibGUuJGVsLnF1ZXJ5U2VsZWN0b3IoIi5lbC10YWJsZV9fYm9keS13cmFwcGVyIikNCiAgICAgICAgICAvLyDojrflj5bmiYDmnInooYwNCiAgICAgICAgICBjb25zdCByb3dzID0gc2Nyb2xsV3JhcHBlci5xdWVyeVNlbGVjdG9yQWxsKCIuZWwtdGFibGVfX3JvdyIpDQoNCiAgICAgICAgICAvLyDpgY3ljobmiYDmnInooYzvvIzmib7liLDljLnphY3nmoTmtYHmsLTlj7cNCiAgICAgICAgICBsZXQgdGFyZ2V0SW5kZXggPSAtMQ0KICAgICAgICAgIHJvd3MuZm9yRWFjaCgocm93LCBpZHgpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHJvd1RleHQgPSByb3cudGV4dENvbnRlbnQNCiAgICAgICAgICAgIGlmIChyb3dUZXh0LmluY2x1ZGVzKHRoaXMuc2VhcmNoKSkgew0KICAgICAgICAgICAgICB0YXJnZXRJbmRleCA9IGlkeA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICBpZiAodGFyZ2V0SW5kZXggPiAtMSkgew0KICAgICAgICAgICAgY29uc3QgdGFyZ2V0Um93ID0gcm93c1t0YXJnZXRJbmRleF0NCiAgICAgICAgICAgIC8vIOiuoeeul+mcgOimgea7muWKqOeahOS9jee9rg0KICAgICAgICAgICAgY29uc3Qgcm93VG9wID0gdGFyZ2V0Um93Lm9mZnNldFRvcA0KDQogICAgICAgICAgICAvLyDkvb/nlKjlubPmu5Hmu5rliqgNCiAgICAgICAgICAgIHNjcm9sbFdyYXBwZXIuc2Nyb2xsVG8oew0KICAgICAgICAgICAgICB0b3A6IHJvd1RvcCAtIHNjcm9sbFdyYXBwZXIuY2xpZW50SGVpZ2h0IC8gMiwNCiAgICAgICAgICAgICAgYmVoYXZpb3I6ICJzbW9vdGgiDQogICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICAvLyDpq5jkuq7mmL7npLror6XooYwNCiAgICAgICAgICAgIHRhcmdldFJvdy5jbGFzc0xpc3QuYWRkKCJoaWdobGlnaHQtcm93IikNCiAgICAgICAgICAgIC8vIDHnp5LlkI7np7vpmaTpq5jkuq4NCiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICAgICAgICB0YXJnZXRSb3cuY2xhc3NMaXN0LnJlbW92ZSgiaGlnaGxpZ2h0LXJvdyIpDQogICAgICAgICAgICB9LCAyMDAwKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5pyq5om+5Yiw5Yy56YWN55qE6K6w5b2VIikNCiAgICAgIH0NCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgj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file": "index.vue", "sourceRoot": "src/views/system/outbound", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"单号\" prop=\"outboundNo\">\r\n            <el-input\r\n              v-model=\"queryParams.outboundNo\"\r\n              clearable\r\n              placeholder=\"出仓单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clientCode\"\r\n              clearable\r\n              placeholder=\"客户代码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"名称\" prop=\"clientName\">\r\n            <el-input\r\n              v-model=\"queryParams.clientName\"\r\n              clearable\r\n              placeholder=\"客户名称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n            <el-input\r\n              v-model=\"queryParams.containerNo\"\r\n              clearable\r\n              placeholder=\"柜号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"封号\" prop=\"sealNo\">\r\n            <el-input\r\n              v-model=\"queryParams.sealNo\"\r\n              clearable\r\n              placeholder=\"封号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"日期\" prop=\"outboundDate\">\r\n            <el-date-picker v-model=\"queryParams.outboundDate\"\r\n                            clearable style=\"width: 100%\"\r\n                            placeholder=\"出仓日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handlePreOutbound()\"\r\n            >操作预出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleDirectOutbound()\"\r\n            >直接出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleRentSettlement()\"\r\n            >结算仓租\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--出仓记录列表(预出仓记录列表)-->\r\n        <el-table v-loading=\"loading\" :data=\"outboundrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"(selectedRows) =>handleOutbound(selectedRows)\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"出仓单号\" prop=\"outboundNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n          <el-table-column align=\"center\" label=\"客户名称\" prop=\"clientName\"/>\r\n          <el-table-column align=\"center\" label=\"操作员\" prop=\"operator\"/>\r\n          <el-table-column align=\"center\" label=\"柜型\" prop=\"containerType\"/>\r\n          <el-table-column align=\"center\" label=\"柜号\" prop=\"containerNo\"/>\r\n          <el-table-column align=\"center\" label=\"封号\" prop=\"sealNo\"/>\r\n          <el-table-column align=\"center\" label=\"出仓日期\" prop=\"outboundDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.outboundDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"仓库报价\" prop=\"warehouseQuote\"/>\r\n          <el-table-column align=\"center\" label=\"工人装柜费\" prop=\"workerLoadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"仓管代收\" prop=\"warehouseCollection\"/>\r\n          <el-table-column align=\"center\" label=\"代收备注\" prop=\"collectionNotes\"/>\r\n          <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\r\n          <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\r\n          <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\r\n          <el-table-column align=\"center\" label=\"总行数\" prop=\"totalRows\"/>\r\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackagingFee\"/>\r\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"租金平衡费\" prop=\"rentalBalanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"超期仓租\" prop=\"overdueRent\"/>\r\n          <el-table-column align=\"center\" label=\"免堆天数\" prop=\"freeStackDays\"/>\r\n          <el-table-column align=\"center\" label=\"超期单价\" prop=\"overdueUnitPrice\"/>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible.sync=\"openOutbound\"\r\n      append-to-body\r\n      width=\"70%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓日期\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.outboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"下单日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\"\r\n                    >\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table v-loading=\"preOutboundInventoryListLoading\" :data=\"preOutboundInventoryList\"\r\n                        ref=\"table\" :summary-method=\"getSummaries\" max-height=\"300\"\r\n                        show-summary @selection-change=\"handleOutboundSelectionChange\"\r\n                        :load=\"loadChildInventory\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        element-loading-text=\"加载中...\" row-key=\"inventoryId\"\r\n                        style=\"width: 100%;\"\r\n              >\r\n                <el-table-column align=\"center\" fixed type=\"selection\" width=\"28\"/>\r\n                <el-table-column align=\"center\" fixed label=\"序号\" type=\"index\" width=\"28\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{ scope.$index + 1 }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\">\r\n                  <template slot=\"header\" slot-scope=\"scope\">\r\n                    <el-input\r\n                      v-model=\"search\"\r\n                      clearable\r\n                      placeholder=\"输入流水号搜索\"\r\n                      size=\"mini\"\r\n                      @keyup.enter.native=\"handleSearchEnter\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"部分出库\" prop=\"inboundDate\" width=\"50\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-switch v-model=\"scope.row.partialOutboundFlag\"/>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货物明细\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      :disabled=\"scope.row.partialOutboundFlag==0\"\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                          <template slot-scope=\"scope\">\r\n                            <el-input v-model=\"scope.row.boxCount\" :disabled=\"!isRowSelected(scope.row)\"/>\r\n                          </template>\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"体积小计\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"毛重小计\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"最新计租日\" prop=\"inboundDate\" width=\"80\">\r\n                  <template slot-scope=\"scope\">\r\n                    <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付卸货费\" prop=\"receivedUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收打包费\" prop=\"unpaidPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付打包费\" prop=\"receivedPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n                <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n                <el-table-column align=\"center\" label=\"超租天数\" prop=\"rentalDays\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"printOutboundPlant\">打印出仓计划</el-button>\r\n        <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n        <el-button v-else type=\"primary\" @click=\"outboundConfirm(outboundType)\">{{\r\n            outboundType === 3 ? \"结 算\" : \"出 仓\"\r\n          }}</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n        <el-button @click=\"openOutbound = false\">关 闭</el-button>\r\n  </span>\r\n    </el-dialog>\r\n\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord,\r\n  getOutboundrecord,\r\n  listOutboundrecord,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {\r\n  listInventory,\r\n  listInventorys,\r\n  outboundInventory,\r\n  preOutboundInventory,\r\n  settlement\r\n} from \"@/api/system/inventory\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport warehouseReceipt from \"@/print-template/warehouseReceipt\"\r\nimport {defaultElementTypeProvider, hiprint} from \"@\"\r\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\r\nimport outboundPlant from \"@/print-template/outboundPlant\"\r\n\r\nlet hiprintTemplate\r\nexport default {\r\n  name: \"Outboundrecord\",\r\n  components: {printPreview},\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: null,\r\n      preOutboundInventoryListLoading: false,\r\n      search: null,\r\n      // 表单校验\r\n      rules: {\r\n        clientCode: [\r\n          {required: true, message: \"客户代码不能为空\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      outboundForm: {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      },\r\n      clientRow: {},\r\n      openOutbound: false,\r\n      preOutboundInventoryList: [],\r\n      selectedCargoDetail: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  mounted() {\r\n    this.initPrint()\r\n  },\r\n  methods: {\r\n    initPrint() {\r\n      hiprint.init({\r\n        providers: [new defaultElementTypeProvider()]\r\n      })\r\n    },\r\n    printOutboundPlant() {\r\n      // 准备打印数据\r\n      const printData = {\r\n        title: \"瑞旗仓库出仓计划\",\r\n        // 表单数据\r\n        outboundNo: this.outboundForm.outboundNo || \"\",\r\n        customerOrderNo: this.outboundForm.customerOrderNo || \"\",\r\n        clientCode: this.outboundForm.clientCode || \"\",\r\n        clientName: this.outboundForm.clientName || \"\",\r\n        plannedOutboundDate: moment(this.outboundForm.plannedOutboundDate).format(\"yyyy-MM-DD HH:mm\") || \"\",\r\n        outboundType: this.outboundForm.outboundType || \"\",\r\n        containerType: this.outboundForm.containerType || \"\",\r\n        cargoType: this.form.cargoType || \"\",\r\n        containerNo: this.outboundForm.containerNo || \"\",\r\n        sealNo: this.outboundForm.sealNo || \"\",\r\n        plateNumber: this.outboundForm.plateNumber || \"\",\r\n        driverPhone: this.outboundForm.driverPhone || \"\",\r\n        warehouseQuote: this.outboundForm.warehouseQuote || \"\",\r\n        warehouseCollection: this.outboundForm.warehouseCollection || \"\",\r\n        workerLoadingFee: this.outboundForm.workerLoadingFee || \"\",\r\n        warehousePay: this.outboundForm.warehousePay || \"\",\r\n        operationRequirement: this.outboundForm.operationRequirement || \"\",\r\n        outboundNote: this.outboundForm.outboundNote || \"\",\r\n        operator: this.outboundForm.operator || \"\",\r\n        orderDate: this.outboundForm.orderDate || \"\",\r\n        outboundHandler: this.outboundForm.outboundHandler || \"\",\r\n        warehouseConfirm: \"√ 已确认 \" + this.parseTime(new Date(), \"{y}-{m}-{d}\"),\r\n\r\n        // 汇总数据\r\n        totalBoxes: this.outboundForm.totalBoxes || 0,\r\n        totalGrossWeight: this.outboundForm.totalGrossWeight || 0,\r\n        totalVolume: this.outboundForm.totalVolume || 0,\r\n        totalSummary: `件数: ${this.outboundForm.totalBoxes || 0} / 毛重: ${this.outboundForm.totalGrossWeight || 0} / 体积: ${this.outboundForm.totalVolume || 0}`,\r\n        totalQuantity: this.outboundForm.totalBoxes || 0,\r\n\r\n        // 勾选的库存列表\r\n        inventoryList: this.selectOutboundList.map(item => {\r\n          return {\r\n            inboundSerialNo: item.inboundSerialNo || \"\",\r\n            clientCode: `${item.subOrderNo || \"\"} ${item.consigneeName || \"\"}`,\r\n            totalBoxes: (this.outboundForm.sqdShippingMark || \"\") + \" / \" + (item.itemName || \"\") + \" / \" + (item.totalBoxes || 0) + \" / \" + (item.totalGrossWeight || 0) + \"KGS / \" + (item.totalVolume || 0) + \"CBM\",\r\n            totalGrossWeight: item.totalGrossWeight || 0,\r\n            totalVolume: item.totalVolume || 0,\r\n            driverInfo: item.driverInfo || \"\",\r\n            outboundQuantity: item.totalBoxes || 0\r\n          }\r\n        })\r\n      }\r\n\r\n      // 创建打印模板并预览打印\r\n      hiprintTemplate = new hiprint.PrintTemplate({template: outboundPlant})\r\n      this.$refs.preView.print(hiprintTemplate, printData)\r\n    },\r\n    warehouseConfirm() {\r\n      // 检查客户代码是否已选择\r\n      if (!this.outboundForm.clientCode) {\r\n        this.$message.warning(\"请先选择客户\")\r\n        return\r\n      }\r\n\r\n      // 设置操作员为当前用户\r\n      this.outboundForm.operator = this.$store.state.user.name.split(\" \")[1]\r\n\r\n      // 设置下单日期为当前日期\r\n      this.outboundForm.orderDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n      // 提示确认成功\r\n      this.$message.success(\"仓管确认成功\")\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 设置当前行的加载状态\r\n      this.$set(tree, 'loading', true)\r\n\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      }).finally(() => {\r\n        // 无论请求成功还是失败，都需要关闭加载状态\r\n        this.$set(tree, 'loading', false)\r\n      })\r\n    },\r\n    warehouseRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 4\r\n      this.openOutbound = true\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    currency,\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓/2:直接出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      // 执行前再次提醒\r\n      this.$confirm(\"确定要\" + (type === 0 ? \"预出仓\" : type === 1 ? \"出仓\" : \"直接出仓\") + \"吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        // 扣货不可以出仓\r\n        this.selectOutboundList.map(item => {\r\n          if (item.cargoDeduction == 1) {\r\n            this.$message.error(\"有扣货库存请重新勾选，流水号：\" + item.inboundSerialNoSub)\r\n            return\r\n          }\r\n        })\r\n\r\n        this.selectOutboundList.map(item => {\r\n          item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n        })\r\n\r\n        // 更新箱数、毛重、体积\r\n        this.outboundForm.totalBoxes = 0\r\n        this.outboundForm.totalGrossWeight = 0\r\n        this.outboundForm.totalVolume = 0\r\n        this.selectOutboundList.map(item => {\r\n          item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n            this.outboundForm.totalBoxes = currency(item.boxCount).add(this.outboundForm.totalBoxes).value\r\n            this.outboundForm.totalGrossWeight = currency(item.unitGrossWeight).add(this.outboundForm.totalGrossWeight).value\r\n            this.outboundForm.totalVolume = currency(item.unitVolume).add(this.outboundForm.totalVolume).value\r\n            return item\r\n          }) : null\r\n          return item\r\n        })\r\n        if (type === 0) {\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上预出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.preOutboundFlag = \"1\"\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.preOutboundFlag = \"1\"\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            preOutboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"预出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 1) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"1\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 2) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 3) {\r\n          // 结算仓租\r\n          this.outboundForm.isRentSettlement = 1 // 仓租结算记录\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rentalSettlementDate = this.outboundForm.outboundDate\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            settlement(data).then(response => {\r\n              this.$message.success(\"结算成功\")\r\n              this.loadPreOutboundInventoryList()\r\n            })\r\n          })\r\n        } else {\r\n          const outboundRecordId = this.outboundForm.outboundRecordId\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.preOutboundInventoryListLoading = true\r\n      this.loading = true\r\n\r\n      // 构建查询参数\r\n      const queryParams = {\r\n        sqdPlannedOutboundDate: this.outboundForm.plannedOutboundDate,\r\n        clientCode: this.outboundForm.clientCode,\r\n        inventoryStatus: \"0\"\r\n      }\r\n\r\n      // 根据出库类型添加预出库标志\r\n      if (this.queryParams.preOutboundFlag) {\r\n        queryParams.preOutboundFlag = this.queryParams.preOutboundFlag\r\n      }\r\n\r\n      if (this.queryParams.preOutboundRecordId) {\r\n        queryParams.preOutboundRecordId = this.queryParams.preOutboundRecordId\r\n      }\r\n\r\n      // 发起请求\r\n      listInventorys(queryParams)\r\n        .then(response => {\r\n          // 处理响应数据\r\n          this.preOutboundInventoryList = response.rows.filter(item => !item.packageTo)\r\n          this.preOutboundInventoryList ? response.rows.map(item => {\r\n            // 计算补收入仓费\r\n            if (item.includesInboundFee === 0) {\r\n              const receivedFee = Number(item.receivedStorageFee || 0)\r\n              const inboundFee = Number(item.inboundFee || 0)\r\n              const difference = currency(inboundFee).subtract(receivedFee).value\r\n\r\n              // 只有当差值大于0时才设置补收费用\r\n              item.additionalStorageFee = difference > 0 ? difference : 0\r\n            } else {\r\n              item.additionalStorageFee = 0\r\n            }\r\n\r\n            // 如果是打包箱，标记为有子节点\r\n            if (item.packageRecord === \"1\") {\r\n              item.hasChildren = true\r\n            }\r\n\r\n            if (this.outboundForm.outboundRecordId === item.preOutboundRecordId) {\r\n              this.selectOutboundList.push(item)\r\n              this.$nextTick(() => {\r\n                this.$refs.table.toggleRowSelection(item, true)\r\n              })\r\n            }\r\n\r\n            return item\r\n          }) : []\r\n\r\n          // 更新总数\r\n          this.total = response.total || 0\r\n\r\n          // 如果是普通出库类型，自动选中预出库标记的行\r\n          if (this.outboundType === 0 && this.$refs.table) {\r\n            this.$nextTick(() => {\r\n              this.preOutboundInventoryList.forEach(item => {\r\n                if (item.preOutboundFlag === 1) {\r\n                  this.$refs.table.toggleRowSelection(item, true)\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"加载预出库库存列表失败:\", error)\r\n          this.$message.error(\"加载预出库库存列表失败\")\r\n        })\r\n        .finally(() => {\r\n          this.queryParams.preOutboundRecordId = null\r\n          this.queryParams.preOutboundFlag = null\r\n          this.loading = false\r\n          this.preOutboundInventoryListLoading = false\r\n        })\r\n    },\r\n    // 选择预出仓记录\r\n    handleOutbound(selectedRows) {\r\n      this.outboundReset()\r\n      this.outboundForm = selectedRows\r\n      this.outboundType = 1\r\n      this.queryParams.preOutboundRecordId = this.outboundForm.outboundRecordId\r\n      this.queryParams.preOutboundFlag = \"1\"\r\n      this.loadPreOutboundInventoryList()\r\n      this.openOutbound = true\r\n    },\r\n    // 添加预出仓记录\r\n    handlePreOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 0\r\n      this.openOutbound = true\r\n    },\r\n    // 直接出仓\r\n    handleDirectOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 2\r\n      this.openOutbound = true\r\n    },\r\n    // 结算仓租\r\n    handleRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 3\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.outboundCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\"\r\n      ]\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\" // 第一列显示文本\r\n        } else {\r\n          const prop = column.property\r\n          let total = 0; // 在条件块之前定义total变量\r\n\r\n          if (prop === \"totalBoxes\" || prop === \"totalVolume\" || prop === \"totalGrossWeight\") {\r\n            total = this.selectOutboundList.reduce((sum, row) => {\r\n              if (row.packageTo) {\r\n                return currency(sum).add(Number(row[prop]) || 0).value\r\n              }\r\n              return sum\r\n            }, 0)\r\n          } else {\r\n            total = this.selectOutboundList.reduce((sum, row) =>\r\n              currency(sum).add(Number(row[prop]) || 0).value, 0)\r\n          }\r\n\r\n          sums[index] = total\r\n          // 现在可以安全地使用total\r\n          summaryResults[column.property] = total\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n\r\n      return sums\r\n    },\r\n    handleOutboundSelectionChange(selection) {\r\n      // 正确获取表格数据 - 通过data属性\r\n      const treeData = this.$refs.table.store.states.data\r\n      // 获取之前的选择状态，用于比较变化\r\n      const previousIds = [...this.ids]\r\n\r\n      // 清空当前选择\r\n      this.ids = []\r\n      this.ids = selection.map(item => item.inventoryId)\r\n      // 找出新选中和取消选中的项\r\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id))\r\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id))\r\n\r\n      this.selectOutboundList = selection\r\n      this.$refs.table.doLayout() // 刷新表格布局\r\n\r\n      // 根据仓租结算至（rental_settlement_date），计算该条库存的租金\r\n      // （ 出库当天-仓租结算至-免租期 ） * 租金单价\r\n      selection.map(item => {\r\n        const date1 = moment(this.outboundForm.outboundDate)\r\n        const date2 = moment(item.rentalSettlementDate)\r\n        item.rentalDays = date1.diff(date2, \"days\") + 1 // 差距的天数\r\n        let volumn = item.totalVolume\r\n\r\n        if (!Number.isNaN(item.rentalDays) && item.rentalDays > 0) {\r\n          // 出仓方式不是整柜没有免租天数\r\n          if (this.outboundForm.outboundType !== \"整柜\") {\r\n            item.overdueRentalFee = currency(item.rentalDays).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          } else {\r\n            let days = currency(item.rentalDays).subtract(item.freeStackPeriod).value\r\n            days = days > 0 ? days : 0\r\n            item.rentalDays = days\r\n            item.overdueRentalFee = currency(days).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          }\r\n        }\r\n\r\n        // 处理新选中的打包箱：自动选中其子项\r\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\r\n          // 如果是新选中的打包箱节点\r\n\r\n          // 在树形表格数据中找到对应的节点\r\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId)\r\n\r\n          // 检查节点是否已展开(已有children属性且有内容)\r\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n            // 如果节点已展开，直接选中其所有子项\r\n            setTimeout(() => {\r\n              parentNode.children.forEach(child => {\r\n                if (!this.ids.includes(child.inventoryId)) {\r\n                  this.ids.push(child.inventoryId)\r\n                  this.selectOutboundList.push(child)\r\n                  this.$refs.table.toggleRowSelection(child, true)\r\n                }\r\n              })\r\n            }, 50) // 给一点时间让UI更新\r\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\r\n            // 如果节点未展开且未加载过但有子节点标记\r\n            parentNode.childrenLoaded = true\r\n\r\n            // 手动展开行，触发懒加载\r\n            this.$refs.table.toggleRowExpansion(parentNode, true)\r\n\r\n            // 监听子节点加载完成后再选中它们\r\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\r\n          }\r\n        }\r\n      })\r\n\r\n      // 处理取消选中的打包箱：取消选中其子项\r\n      newlyDeselected.forEach(parentId => {\r\n        // 找出对应的父节点\r\n        const parentNode = treeData.find(node =>\r\n          node.inventoryId === parentId && node.packageRecord === \"1\"\r\n        )\r\n\r\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n          // 取消选中所有子项\r\n          parentNode.children.forEach(child => {\r\n            const childIndex = this.ids.indexOf(child.inventoryId)\r\n            if (childIndex > -1) {\r\n              // 从选中列表中移除\r\n              this.ids.splice(childIndex, 1)\r\n              const itemIndex = this.selectOutboundList.findIndex(\r\n                item => item.inventoryId === child.inventoryId\r\n              )\r\n              if (itemIndex > -1) {\r\n                this.selectOutboundList.splice(itemIndex, 1)\r\n              }\r\n              // 在UI上取消选中\r\n              this.$refs.table.toggleRowSelection(child, false)\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.clientRow = row\r\n      this.outboundForm.overdueRentalUnitPrice = row.overdueRent\r\n      this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecord(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        receivedSupplier: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        operationRequirement: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null,\r\n        receivedFromSupplier: null,\r\n        unreceivedFromCustomer: null,\r\n        receivedFromCustomer: null,\r\n        customerReceivableBalance: null,\r\n        payableToWorker: null,\r\n        promissoryNoteSales: null,\r\n        promissoryNoteCost: null,\r\n        promissoryNoteGrossProfit: null,\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\"),\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"outboundForm\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.outboundForm.outboundRecordId != null) {\r\n            updateOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || \"\")\r\n          const searchValue = String(this.search)\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue)\r\n        }\r\n      )\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector(\".el-table__body-wrapper\")\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll(\".el-table__row\")\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx\r\n            }\r\n          })\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex]\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: \"smooth\"\r\n            })\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add(\"highlight-row\")\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove(\"highlight-row\")\r\n            }, 2000)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning(\"未找到匹配的记录\")\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .edit .number .el-input__inner {\r\n  text-align: right;\r\n}\r\n\r\n// 添加高亮样式\r\n::v-deep .highlight-row {\r\n  background-color: #fdf5e6 !important;\r\n  transition: background-color 0.5s;\r\n}\r\n</style>\r\n"]}]}