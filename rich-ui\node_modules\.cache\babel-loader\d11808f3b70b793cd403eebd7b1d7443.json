{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\config\\bankRecordFieldLabelMap.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\config\\bankRecordFieldLabelMap.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["bankRecordFieldLabelMap", "bankRecordId", "name", "display", "aggregated", "align", "width", "bankRecordNo", "isRecievingOrPaying", "sqdPaymentTitleCode", "bankAccountCode", "clearingCompanyId", "sqdClearingCompanyShortname", "chargeTypeId", "chargeDescription", "bankCurrencyCode", "actualBankRecievedAmount", "actualBankPaidAmount", "bankRecievedHandlingFee", "bankPaidHandlingFee", "bankRecievedExchangeLost", "bankPaidExchangeLost", "sqdBillRecievedAmount", "sqdBillPaidAmount", "billRecievedWriteoffAmount", "billPaidWriteoffAmount", "sqdBillRecievedWriteoffBalance", "sqdBillPaidWriteoffBalance", "writeoffStatus", "bankRecordTime", "paymentTypeCode", "voucherNo", "chargeType", "invoiceNo", "bankRecordRemark", "bankRecordByStaffId", "bankRecordUpdateTime", "isBankRecordLocked", "isWriteoffLocked", "sqdChargeIdList", "sqdRaletiveRctList", "sqdRaletiveInvoiceList", "sqdRsStaffId", "writeoffRemark", "writeoffStaffId", "writeoffTime", "slipAmount", "slipDate", "slipFile", "slipConfirmed", "verifyId", "verifyTime", "sqdReimburseId", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/config/bankRecordFieldLabelMap.js"], "sourcesContent": ["export const bankRecordFieldLabelMap = {\r\n  bankRecordId: {\r\n    name: '银行流水ID',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  bankRecordNo: {\r\n    name: '银行流水号',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  isRecievingOrPaying: {\r\n    name: '收支标志',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'center',\r\n    width: '80'\r\n  },\r\n  sqdPaymentTitleCode: {\r\n    name: '所属公司',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  bankAccountCode: {\r\n    name: '银行账户',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  clearingCompanyId: {\r\n    name: '结算公司',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  sqdClearingCompanyShortname: {\r\n    name: '结算公司简称',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  chargeTypeId: {\r\n    name: '费用类型',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '100'\r\n  },\r\n  chargeDescription: {\r\n    name: '费用描述',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '150'\r\n  },\r\n  bankCurrencyCode: {\r\n    name: '银行币种',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '80'\r\n  },\r\n  actualBankRecievedAmount: {\r\n    name: '实收金额',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  actualBankPaidAmount: {\r\n    name: '实付金额',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  bankRecievedHandlingFee: {\r\n    name: '收款手续费',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  bankPaidHandlingFee: {\r\n    name: '付款手续费',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  bankRecievedExchangeLost: {\r\n    name: '收款汇损',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  bankPaidExchangeLost: {\r\n    name: '付款汇损',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  sqdBillRecievedAmount: {\r\n    name: '收款记账',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  sqdBillPaidAmount: {\r\n    name: '付款记账',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  billRecievedWriteoffAmount: {\r\n    name: '收账已销',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  billPaidWriteoffAmount: {\r\n    name: '付账已销',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  sqdBillRecievedWriteoffBalance: {\r\n    name: '收账未销',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  sqdBillPaidWriteoffBalance: {\r\n    name: '付账未销',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  writeoffStatus: {\r\n    name: '销账状态',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'center',\r\n    width: '80'\r\n  },\r\n  bankRecordTime: {\r\n    name: '银行时间',\r\n    display: 'date',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  paymentTypeCode: {\r\n    name: '结算方式',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '100'\r\n  },\r\n  voucherNo: {\r\n    name: '凭证号',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  chargeType: {\r\n    name: '费用类型',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '100'\r\n  },\r\n  invoiceNo: {\r\n    name: '发票号',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  bankRecordRemark: {\r\n    name: '银行流水备注',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '150'\r\n  },\r\n  bankRecordByStaffId: {\r\n    name: '录入人',\r\n    display: 'getName',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '100'\r\n  },\r\n  bankRecordUpdateTime: {\r\n    name: '录入时间',\r\n    display: 'date',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  isBankRecordLocked: {\r\n    name: '录入锁定',\r\n    display: 'boolean',\r\n    aggregated: false,\r\n    align: 'center',\r\n    width: '80'\r\n  },\r\n  isWriteoffLocked: {\r\n    name: '销账锁定',\r\n    display: 'boolean',\r\n    aggregated: false,\r\n    align: 'center',\r\n    width: '80'\r\n  },\r\n  sqdChargeIdList: {\r\n    name: '费用条目',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '150'\r\n  },\r\n  sqdRaletiveRctList: {\r\n    name: '相关操作单',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '150'\r\n  },\r\n  sqdRaletiveInvoiceList: {\r\n    name: '相关发票',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '150'\r\n  },\r\n  sqdRsStaffId: {\r\n    name: '所属员工',\r\n    display: 'getName',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '100'\r\n  },\r\n  writeoffRemark: {\r\n    name: '销账备注',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '150'\r\n  },\r\n  writeoffStaffId: {\r\n    name: '销账人',\r\n    display: 'getName',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '100'\r\n  },\r\n  writeoffTime: {\r\n    name: '销账时间',\r\n    display: 'date',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  slipAmount: {\r\n    name: '水单金额',\r\n    display: 'number',\r\n    aggregated: true,\r\n    align: 'right',\r\n    width: '120'\r\n  },\r\n  slipDate: {\r\n    name: '水单日期',\r\n    display: 'date',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  slipFile: {\r\n    name: '水单文件',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '150'\r\n  },\r\n  slipConfirmed: {\r\n    name: '水单确认',\r\n    display: 'boolean',\r\n    aggregated: false,\r\n    align: 'center',\r\n    width: '80'\r\n  },\r\n  verifyId: {\r\n    name: '审核人',\r\n    display: 'getName',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '100'\r\n  },\r\n  verifyTime: {\r\n    name: '审核时间',\r\n    display: 'date',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  },\r\n  sqdReimburseId: {\r\n    name: '报销记录',\r\n    display: 'text',\r\n    aggregated: false,\r\n    align: 'left',\r\n    width: '120'\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAO,IAAMA,uBAAuB,GAAG;EACrCC,YAAY,EAAE;IACZC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDC,YAAY,EAAE;IACZL,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDE,mBAAmB,EAAE;IACnBN,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC;EACDG,mBAAmB,EAAE;IACnBP,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDI,eAAe,EAAE;IACfR,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDK,iBAAiB,EAAE;IACjBT,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDM,2BAA2B,EAAE;IAC3BV,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDO,YAAY,EAAE;IACZX,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDQ,iBAAiB,EAAE;IACjBZ,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDS,gBAAgB,EAAE;IAChBb,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDU,wBAAwB,EAAE;IACxBd,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDW,oBAAoB,EAAE;IACpBf,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDY,uBAAuB,EAAE;IACvBhB,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDa,mBAAmB,EAAE;IACnBjB,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDc,wBAAwB,EAAE;IACxBlB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDe,oBAAoB,EAAE;IACpBnB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDgB,qBAAqB,EAAE;IACrBpB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDiB,iBAAiB,EAAE;IACjBrB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDkB,0BAA0B,EAAE;IAC1BtB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDmB,sBAAsB,EAAE;IACtBvB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDoB,8BAA8B,EAAE;IAC9BxB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDqB,0BAA0B,EAAE;IAC1BzB,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDsB,cAAc,EAAE;IACd1B,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC;EACDuB,cAAc,EAAE;IACd3B,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDwB,eAAe,EAAE;IACf5B,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDyB,SAAS,EAAE;IACT7B,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD0B,UAAU,EAAE;IACV9B,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD2B,SAAS,EAAE;IACT/B,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD4B,gBAAgB,EAAE;IAChBhC,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD6B,mBAAmB,EAAE;IACnBjC,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD8B,oBAAoB,EAAE;IACpBlC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD+B,kBAAkB,EAAE;IAClBnC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC;EACDgC,gBAAgB,EAAE;IAChBpC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC;EACDiC,eAAe,EAAE;IACfrC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDkC,kBAAkB,EAAE;IAClBtC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDmC,sBAAsB,EAAE;IACtBvC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDoC,YAAY,EAAE;IACZxC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDqC,cAAc,EAAE;IACdzC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDsC,eAAe,EAAE;IACf1C,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDuC,YAAY,EAAE;IACZ3C,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACDwC,UAAU,EAAE;IACV5C,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,QAAQ;IACjBC,UAAU,EAAE,IAAI;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EACDyC,QAAQ,EAAE;IACR7C,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD0C,QAAQ,EAAE;IACR9C,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD2C,aAAa,EAAE;IACb/C,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE;EACT,CAAC;EACD4C,QAAQ,EAAE;IACRhD,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD6C,UAAU,EAAE;IACVjD,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT,CAAC;EACD8C,cAAc,EAAE;IACdlD,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,KAAK;IACjBC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE;EACT;AACF,CAAC;AAAA+C,OAAA,CAAArD,uBAAA,GAAAA,uBAAA"}]}