{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\bankSlip.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\bankSlip.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiDQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCINCmltcG9ydCBDb21wYW55U2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9Db21wYW55U2VsZWN0L2luZGV4LnZ1ZSINCmltcG9ydCB7DQogIGFkZEJhbmtyZWNvcmQsDQogIGRlbEJhbmtyZWNvcmQsDQogIGRlbEltZywNCiAgZ2V0QmFua3JlY29yZCwNCiAgbGlzdEJhbmtyZWNvcmQsDQogIHVwZGF0ZUJhbmtyZWNvcmQNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL2JhbmtyZWNvcmQiDQppbXBvcnQge3BhcnNlVGltZX0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvcmljaCINCmltcG9ydCByZXF1ZXN0IGZyb20gIkAvdXRpbHMvcmVxdWVzdCINCmltcG9ydCBJbWdQcmV2aWV3IGZyb20gIkAvdmlld3Mvc3lzdGVtL3JjdC9pbWdQcmV2aWV3LnZ1ZSINCmltcG9ydCB7dXBkYXRlUmN0fSBmcm9tICJAL2FwaS9zeXN0ZW0vcmN0Ig0KaW1wb3J0IHBpbnlpbiBmcm9tICJqcy1waW55aW4iDQppbXBvcnQge2xpc3RDb21wYW55Tm9QYWdlfSBmcm9tICJAL2FwaS9zeXN0ZW0vY29tcGFueSINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiYmFua1NsaXAiLA0KICBjb21wb25lbnRzOiB7SW1nUHJldmlldywgQ29tcGFueVNlbGVjdCwgVHJlZXNlbGVjdH0sDQogIHByb3BzOiBbInNjb3BlIiwgInR5cGUiXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwDQogICAgICB9LA0KICAgICAgZm9ybToge30sDQogICAgICBydWxlczoge30sDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIHNob3dEZXRhaWw6IGZhbHNlLA0KICAgICAgYmFua1NsaXBPcGVuOiBmYWxzZSwNCiAgICAgIGJhbmtyZWNvcmRMaXN0OiBbXSwNCiAgICAgIHRvdGFsOiAwLA0KICAgICAgbG9hZGluZzogZmFsc2UsDQogICAgICBjb21wYW55TGlzdDogW10sDQogICAgICBmaWxlTGlzdDogW10sDQogICAgICBpbWFnZUZpbGU6IG51bGwsDQogICAgICB1cGxvYWRVcmw6ICIvc3lzdGVtL2JhbmtyZWNvcmQvdXBsb2FkIiwNCiAgICAgIGltZ1VybDogIiIsDQogICAgICBiYW5rU2xpcFByZXZpZXc6IGZhbHNlLA0KICAgICAgcHJldmlld0ltZ09wZW46IGZhbHNlLA0KICAgICAgY2xpZW50czogW10NCiAgICB9DQogIH0sDQogIGJlZm9yZU1vdW50KCkgew0KDQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgaXNMb2NrZWQoKSB7DQogICAgICByZXR1cm4gdGhpcy5mb3JtLmlzQmFua1JlY29yZExvY2tlZCA9PSAxDQogICAgfSwNCiAgICBpc0JhbmtTbGlwQ29uZmlybWVkKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9ybS5zbGlwQ29uZmlybWVkID09IDENCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBsb2FkQ29tcGFueU9wdGlvbnMoKSB7DQogICAgICBsZXQgY29tcGFueUlkcyA9IFt0aGlzLnNjb3BlLnJvdy5jbGllbnRJZF0NCiAgICAgIHRoaXMuc2NvcGUucm93LnJlbGF0aW9uQ2xpZW50SWRMaXN0LnNwbGl0KCIsIikubGVuZ3RoID4gMCA/IHRoaXMuc2NvcGUucm93LnJlbGF0aW9uQ2xpZW50SWRMaXN0LnNwbGl0KCIsIikubWFwKGl0ZW0gPT4gcGFyc2VJbnQoaXRlbSkgPyBjb21wYW55SWRzLnB1c2gocGFyc2VJbnQoaXRlbSkpIDogbnVsbCkgOiBudWxsDQogICAgICBsaXN0Q29tcGFueU5vUGFnZSh7Y29tcGFueUlkczogY29tcGFueUlkc30pLnRoZW4ocmVzcG9uc2UgPT4gew0KDQogICAgICAgIHRoaXMuY2xpZW50cyA9IHJlc3BvbnNlLnJvd3MgLy8g5pu05paw6YCJ6aG55pWw5o2uDQogICAgICB9KQ0KICAgIH0sDQogICAgc2VsZWN0Q29tcGFueShyb3csIG5vZGUpIHsNCiAgICAgIHJvdy5jbGVhcmluZ0NvbXBhbnlJZCA9IG5vZGUuY29tcGFueUlkDQogICAgICByb3cuc3FkQ2xlYXJpbmdDb21wYW55U2hvcnRuYW1lID0gbm9kZS5jb21wYW55U2hvcnROYW1lDQogICAgfSwNCiAgICBhc3luYyBkZWxldGVCYW5rU2xpcChyb3cpIHsNCiAgICAgIC8vIOWIoOmZpOacjeWKoeWZqOS4reeahOWbvueJh+aWh+S7tg0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgZGVsSW1nKHt1cmw6IHJvdy5zbGlwRmlsZX0pDQogICAgICB9IGNhdGNoIChlKSB7DQogICAgICAgIC8vIOabtOaWsOa1geawtOS4reeahOWbvueJh+WcsOWdgA0KICAgICAgICBhd2FpdCB1cGRhdGVCYW5rcmVjb3JkKHsNCiAgICAgICAgICBiYW5rUmVjb3JkSWQ6IHJvdy5iYW5rUmVjb3JkSWQsDQogICAgICAgICAgc2xpcEZpbGU6IG51bGwsDQogICAgICAgICAgaXNSZWNpZXZpbmdPclBheWluZzogdGhpcy50eXBlID09PSAicGF5IiA/ICIxIiA6ICIwIiwNCiAgICAgICAgICBiYW5rUmVjb3JkTm86IHJvdy5iYW5rUmVjb3JkTm8NCiAgICAgICAgfSkNCiAgICAgIH0NCg0KICAgICAgYXdhaXQgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIGN1c3RvbUh0dHBSZXF1ZXN0KG9wdGlvbnMpIHsNCiAgICAgIGNvbnN0IGZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKCkNCiAgICAgIGZvcm1EYXRhLmFwcGVuZCgiZmlsZSIsIG9wdGlvbnMuZmlsZSkNCg0KICAgICAgcmVxdWVzdCh7DQogICAgICAgIHVybDogIi9zeXN0ZW0vYmFua3JlY29yZC91cGxvYWRJbWciLA0KICAgICAgICBtZXRob2Q6ICJwb3N0IiwNCiAgICAgICAgZGF0YTogZm9ybURhdGENCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBvcHRpb25zLm9uU3VjY2VzcyhyZXNwb25zZSwgb3B0aW9ucy5maWxlKQ0KICAgICAgICB0aGlzLmltZ1VybCA9IHJlc3BvbnNlLnVybA0KICAgICAgICB0aGlzLmZvcm0uc2xpcEZpbGUgPSByZXNwb25zZS51cmwNCiAgICAgICAgb3B0aW9ucy5yb3cuc2xpcEZpbGUgPSByZXNwb25zZS51cmwNCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgb3B0aW9ucy5vbkVycm9yKGVycm9yKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZUNoYW5nZShmaWxlLCBmaWxlTGlzdCkgew0KDQogICAgICBjb25zdCBleHRlbnNpb24gPSBmaWxlLm5hbWUuc3Vic3RyaW5nKGZpbGUubmFtZS5sYXN0SW5kZXhPZigiLiIpKQ0KICAgICAgY29uc3QgbmV3RmlsZU5hbWUgPSBgJHt0aGlzLmZvcm0uYmFua1JlY29yZE5vfSR7ZXh0ZW5zaW9ufWANCiAgICAgIHRoaXMuaW1hZ2VGaWxlID0gbmV3IEZpbGUoW2ZpbGUucmF3XSwgbmV3RmlsZU5hbWUsIHt0eXBlOiBmaWxlLnR5cGV9KQ0KICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLlm77niYflt7LpgInmi6kiKQ0KICAgIH0sDQogICAgaGFuZGxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIHRoaXMuZm9ybS5zbGlwRmlsZSA9IHJlc3BvbnNlLnVybA0KICAgIH0sDQogICAgaGFuZGxlRXJyb3IoZXJyLCBmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigiVXBsb2FkIGZhaWxlZDoiLCBlcnIpDQogICAgfSwNCiAgICBoYW5kbGVSZW1vdmUoZmlsZSwgZmlsZUxpc3QpIHsNCiAgICAgIGNvbnNvbGUubG9nKGZpbGUsIGZpbGVMaXN0KQ0KICAgIH0sDQogICAgaGFuZGxlUHJldmlldyhmaWxlKSB7DQogICAgICBjb25zb2xlLmxvZyhmaWxlKQ0KICAgIH0sDQogICAgaGFuZGxlRXhjZWVkKGZpbGVzLCBmaWxlTGlzdCkgew0KICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKGDlvZPliY3pmZDliLbpgInmi6kgMyDkuKrmlofku7bvvIzmnKzmrKHpgInmi6nkuoYgJHtmaWxlcy5sZW5ndGh9IOS4quaWh+S7tu+8jOWFsemAieaLqeS6hiAke2ZpbGVzLmxlbmd0aCArIGZpbGVMaXN0Lmxlbmd0aH0g5Liq5paH5Lu2YCkNCiAgICB9LA0KICAgIGJlZm9yZVJlbW92ZShmaWxlLCBmaWxlTGlzdCkgew0KICAgICAgcmV0dXJuIHRoaXMuJGNvbmZpcm0oYOehruWumuenu+mZpCAke2ZpbGUubmFtZX3vvJ9gKQ0KICAgIH0sDQogICAgdXBsb2FkSW1hZ2Uocm93KSB7DQogICAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlc29sdmUsIHJlamVjdCkgPT4gew0KICAgICAgICB0aGlzLmN1c3RvbUh0dHBSZXF1ZXN0KHsNCiAgICAgICAgICBmaWxlOiB0aGlzLmltYWdlRmlsZSwNCiAgICAgICAgICBvblN1Y2Nlc3M6IChyZXNwb25zZSkgPT4gew0KICAgICAgICAgICAgdGhpcy5oYW5kbGVTdWNjZXNzKHJlc3BvbnNlKQ0KICAgICAgICAgICAgcmVzb2x2ZShyZXNwb25zZSkNCiAgICAgICAgICB9LA0KICAgICAgICAgIG9uRXJyb3I6IChlcnJvcikgPT4gew0KICAgICAgICAgICAgdGhpcy5oYW5kbGVFcnJvcihlcnJvcikNCiAgICAgICAgICAgIHJlamVjdChlcnJvcikNCiAgICAgICAgICB9LA0KICAgICAgICAgIHJvdw0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGFzeW5jIHN1Ym1pdEZvcm0ocm93KSB7DQogICAgICAvLyDlhYjkuIrkvKDlm77niYcNCiAgICAgIGlmICh0aGlzLmltYWdlRmlsZSkgew0KICAgICAgICAvLyBQZXJmb3JtIHRoZSB1cGxvYWQgZmlyc3QgYW5kIHdhaXQgZm9yIGl0IHRvIGNvbXBsZXRlDQogICAgICAgIGF3YWl0IHRoaXMudXBsb2FkSW1hZ2Uocm93KQ0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2cocm93KQ0KICAgICAgdXBkYXRlQmFua3JlY29yZChyb3cpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKQ0KICAgICAgICAvLyB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgfSkNCg0KICAgICAgLy8g5pu05paw5pON5L2c5Y2V5LiK55qE5rC05Y2V5L+h5oGvDQogICAgICBpZiAodGhpcy50eXBlID09PSAicGF5Iikgew0KICAgICAgICB1cGRhdGVSY3Qoe3JjdElkOiB0aGlzLnNjb3BlLnJvdy5yY3RJZCwgc3FkRG5QYXlTbGlwU3RhdHVzOiAi4oiaIn0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdXBkYXRlUmN0KHtyY3RJZDogdGhpcy5zY29wZS5yb3cucmN0SWQsIHNxZERuUmVjZWl2ZVNsaXBTdGF0dXM6ICLiiJoifSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBwYXJzZVRpbWUsDQogICAgY3VycmVuY3ksDQogICAgYXN5bmMgb3BlbkJhbmtTbGlwKCkgew0KICAgICAgYXdhaXQgdGhpcy5nZXRMaXN0KCkNCiAgICAgIHRoaXMuYmFua1NsaXBPcGVuID0gdHJ1ZQ0KICAgIH0sDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RCYW5rcmVjb3JkKHsNCiAgICAgICAgY2xlYXJpbmdDb21wYW55SWQ6IHRoaXMuc2NvcGUucm93LmNsaWVudElkLA0KICAgICAgICBpc1JlY2lldmluZ09yUGF5aW5nOiB0aGlzLnR5cGUgPT09ICJwYXkiID8gIjEiIDogIjAiLA0KICAgICAgICByY3RObzogdGhpcy5zY29wZS5yb3cucmN0Tm8NCiAgICAgIH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICByZXNwb25zZS5yb3dzLm1hcChpdGVtID0+IGl0ZW0uY2xpZW50cyA9IHRoaXMuY2xpZW50cykNCiAgICAgICAgdGhpcy5iYW5rcmVjb3JkTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsID8gcmVzcG9uc2UudG90YWwgOiAwDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICAgIC8vIOW6lOaUtuawtOWNleeKtuaAgSBzcWRfZG5fcmVjZWl2ZV9zbGlwX3N0YXR1cw0KICAgICAgICBpZiAocmVzcG9uc2Uucm93cyAmJiByZXNwb25zZS5yb3dzLmxlbmd0aCA9PT0gMCAmJiB0aGlzLnR5cGUgPT09ICJyZWNlaXZlIiAmJiB0aGlzLnNjb3BlLnJvdy5zcWREblJlY2VpdmVTbGlwU3RhdHVzID09PSBudWxsKSB7DQogICAgICAgICAgdXBkYXRlUmN0KHtyY3RJZDogdGhpcy5zY29wZS5yb3cucmN0SWQsIHNxZERuUmVjZWl2ZVNsaXBTdGF0dXM6ICItIn0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgICAgLy8g5bqU5LuY5rC05Y2V54q25oCBIHNxZF9kbl9wYXlfc2xpcF9zdGF0dXMNCiAgICAgICAgaWYgKHJlc3BvbnNlLnJvd3MgJiYgcmVzcG9uc2Uucm93cy5sZW5ndGggPT09IDAgJiYgdGhpcy50eXBlID09PSAicGF5IiAmJiB0aGlzLnNjb3BlLnJvdy5zcWREblBheVNsaXBTdGF0dXMgPT09IG51bGwpIHsNCiAgICAgICAgICB1cGRhdGVSY3Qoe3JjdElkOiB0aGlzLnNjb3BlLnJvdy5yY3RJZCwgc3FkRG5QYXlTbGlwU3RhdHVzOiAiLSJ9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgc2VsZWN0QmFua0FjY291bnQocm93KSB7DQogICAgICBjb25zb2xlLmxvZyhyb3cpDQogICAgICB0aGlzLmZvcm0uc3FkUGF5bWVudFRpdGxlQ29kZSA9IHJvdy5zcWRCZWxvbmdUb0NvbXBhbnlDb2RlDQogICAgfSwNCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBiYW5rUmVjb3JkSWRzID0gcm93LmJhbmtSZWNvcmRJZCB8fCB0aGlzLmlkcw0KICAgICAgdGhpcy4kY29uZmlybSgi5piv5ZCm56Gu6K6k5Yig6Zmk77yfIiwgJ+aPkOekuicsIHtjdXN0b21DbGFzczogJ21vZGFsLWNvbmZpcm0nfSkudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBkZWxCYW5rcmVjb3JkKGJhbmtSZWNvcmRJZHMpDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIikNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pDQogICAgfSwNCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLmFkZCA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIGNvbnN0IGJhbmtSZWNvcmRJZCA9IHJvdy5iYW5rUmVjb3JkSWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldEJhbmtyZWNvcmQoYmFua1JlY29yZElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLmZvcm0uY2hhcmdlVHlwZSA9ICLorqLljZUiDQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgICAgdGhpcy50aXRsZSA9ICLmn6XnnIvpk7booYzmtYHmsLQt6ZSA6LSm5piO57uGIg0KICAgICAgICB0aGlzLmNvbXBhbnlMaXN0ID0gW3Jlc3BvbnNlLmNvbXBhbnlMaXN0XQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHZlcmlmeSgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0uY2xlYXJpbmdDb21wYW55SWQgPT09IG51bGwpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fovpPlhaXnu5Pnrpflhazlj7giKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybS5pc0JhbmtSZWNvcmRMb2NrZWQgPSAxDQogICAgICB1cGRhdGVCYW5rcmVjb3JkKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgIH0pDQogICAgfSwNCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgYmFua1JlY29yZElkOiBudWxsLA0KICAgICAgICBpc1JlY2lldmluZ09yUGF5aW5nOiBudWxsLA0KICAgICAgICBzcWRQYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBiYW5rQWNjb3VudENvZGU6IG51bGwsDQogICAgICAgIGNsZWFyaW5nQ29tcGFueUlkOiBudWxsLA0KICAgICAgICBzcWRDbGVhcmluZ0NvbXBhbnlTaG9ydG5hbWU6IG51bGwsDQogICAgICAgIGNoYXJnZVR5cGVJZDogbnVsbCwNCiAgICAgICAgY2hhcmdlRGVzY3JpcHRpb246IG51bGwsDQogICAgICAgIGJhbmtDdXJyZW5jeUNvZGU6IG51bGwsDQogICAgICAgIGFjdHVhbEJhbmtSZWNpZXZlZEFtb3VudDogbnVsbCwNCiAgICAgICAgYWN0dWFsQmFua1BhaWRBbW91bnQ6IG51bGwsDQogICAgICAgIGJhbmtSZWNpZXZlZEhhbmRsaW5nRmVlOiBudWxsLA0KICAgICAgICBiYW5rUGFpZEhhbmRsaW5nRmVlOiBudWxsLA0KICAgICAgICBiYW5rUmVjaWV2ZWRFeGNoYW5nZUxvc3Q6IG51bGwsDQogICAgICAgIGJhbmtQYWlkRXhjaGFuZ2VMb3N0OiBudWxsLA0KICAgICAgICBzcWRCaWxsUmVjaWV2ZWRBbW91bnQ6IG51bGwsDQogICAgICAgIHNxZEJpbGxQYWlkQW1vdW50OiBudWxsLA0KICAgICAgICBiaWxsUmVjaWV2ZWRXcml0ZW9mZkFtb3VudDogbnVsbCwNCiAgICAgICAgYmlsbFBhaWRXcml0ZW9mZkFtb3VudDogbnVsbCwNCiAgICAgICAgc3FkQmlsbFJlY2lldmVkV3JpdGVvZmZCYWxhbmNlOiBudWxsLA0KICAgICAgICBzcWRCaWxsUGFpZFdyaXRlb2ZmQmFsYW5jZTogbnVsbCwNCiAgICAgICAgd3JpdGVvZmZTdGF0dXM6ICIwIiwNCiAgICAgICAgYmFua1JlY29yZFRpbWU6IG51bGwsDQogICAgICAgIHBheW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgdm91Y2hlck5vOiBudWxsLA0KICAgICAgICBpbnZvaWNlTm86IG51bGwsDQogICAgICAgIGJhbmtSZWNvcmRSZW1hcms6IG51bGwsDQogICAgICAgIGJhbmtSZWNvcmRCeVN0YWZmSWQ6IG51bGwsDQogICAgICAgIGJhbmtSZWNvcmRVcGRhdGVUaW1lOiBudWxsLA0KICAgICAgICBpc0JhbmtSZWNvcmRMb2NrZWQ6IG51bGwsDQogICAgICAgIGlzV3JpdGVvZmZMb2NrZWQ6IG51bGwsDQogICAgICAgIHNxZENoYXJnZUlkTGlzdDogbnVsbCwNCiAgICAgICAgc3FkUmFsZXRpdmVSY3RMaXN0OiBudWxsLA0KICAgICAgICBzcWRSYWxldGl2ZUludm9pY2VMaXN0OiBudWxsLA0KICAgICAgICBzcWRSc1N0YWZmSWQ6IG51bGwsDQogICAgICAgIHdyaXRlb2ZmUmVtYXJrOiBudWxsLA0KICAgICAgICB3cml0ZW9mZlN0YWZmSWQ6IG51bGwsDQogICAgICAgIHdyaXRlb2ZmVGltZTogbnVsbCwNCiAgICAgICAgY2hhcmdlVHlwZTogIuiuouWNlSINCiAgICAgIH0NCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIikNCiAgICB9LA0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIGxldCBkYXRhID0ge30NCiAgICAgIGRhdGEuaXNSZWNpZXZpbmdPclBheWluZyA9IHRoaXMudHlwZSA9PT0gInBheSIgPyAiMSIgOiAiMCINCiAgICAgIGRhdGEucGF5bWVudFR5cGVDb2RlID0gIlQvVCINCiAgICAgIGRhdGEuY2hhcmdlVHlwZSA9ICLorqLljZUiDQogICAgICBkYXRhLmNoYXJnZVR5cGVJZCA9IDINCiAgICAgIGRhdGEuY2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLnNjb3BlLnJvdy5jbGllbnRJZA0KICAgICAgZGF0YS5zcWRDbGVhcmluZ0NvbXBhbnlTaG9ydG5hbWUgPSB0aGlzLnNjb3BlLnJvdy5jbGllbnRTdW1tYXJ5LnNwbGl0KCIvIilbMV0NCiAgICAgIGRhdGEuc3FkUmFsZXRpdmVSY3RMaXN0ID0gdGhpcy5zY29wZS5yb3cucmN0Tm8NCg0KICAgICAgYWRkQmFua3JlY29yZChkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKQ0KICAgICAgICAvLyB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGlzUmVjaWV2aW5nT3JQYXlpbmdOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIGlkOiBub2RlLnZhbHVlLA0KICAgICAgICBsYWJlbDogbm9kZS5sYWJlbA0KICAgICAgfQ0KICAgIH0sDQogICAgY29tcGFueU5vcm1hbGl6ZXIobm9kZSkgew0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6IG5vZGUuY29tcGFueUlkLA0KICAgICAgICBsYWJlbDogKG5vZGUuY29tcGFueVNob3J0TmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55U2hvcnROYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY29tcGFueUxvY2FsTmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55TG9jYWxOYW1lIDogIiIpICsgIiwiICsgcGlueWluLmdldEZ1bGxDaGFycygobm9kZS5jb21wYW55U2hvcnROYW1lICE9IG51bGwgPyBub2RlLmNvbXBhbnlTaG9ydE5hbWUgOiAiIikgKyAiICIgKyAobm9kZS5jb21wYW55TG9jYWxOYW1lICE9IG51bGwgPyBub2RlLmNvbXBhbnlMb2NhbE5hbWUgOiAiIikpDQogICAgICB9DQogICAgfSwNCiAgICBxdWVyeUNvbXBhbnkobm9kZSkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jb21wYW55ID0gbm9kZS5jb21wYW55U2hvcnROYW1lDQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNvbXBhbnlJZCA9IG5vZGUuY29tcGFueUlkDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9DQogIH0NCg0KfQ0K"}, {"version": 3, "sources": ["bankSlip.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "bankSlip.vue", "sourceRoot": "src/views/system/rct", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-button v-hasPermi=\"['system:company:list']\"\r\n               size=\"mini\"\r\n               type=\"text\"\r\n               :disabled=\"scope.row.opAccept==0\"\r\n               @click=\"openBankSlip\"\r\n    >\r\n      {{\r\n        \"[\" +\r\n        currency(type === \"pay\" ? scope.row.cnInRmb : scope.row.dnInRmb, {\r\n          separator: \",\",\r\n          symbol: \"¥\",\r\n          precision: 2\r\n        }).format()\r\n        + (type === \"pay\" ? (\" \" + (scope.row.sqdDnPaySlipStatus ? scope.row.sqdDnPaySlipStatus : \"-\")) : (\" \" + (scope.row.sqdDnReceiveSlipStatus ? scope.row.sqdDnReceiveSlipStatus : \"-\"))) + \"]\"\r\n      }}\r\n    </el-button>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n      :title=\"type==='pay'?'付款水单':'收款水单'\" :visible.sync=\"bankSlipOpen\"\r\n      append-to-body destroy-on-close\r\n      height=\"60%\" @open=\"loadCompanyOptions\"\r\n      width=\"60%\"\r\n      @close=\"showDetail=false\"\r\n    >\r\n      <el-table v-loading=\"loading\" :data=\"bankrecordList\" highlight-current-row\r\n                stripe style=\"margin-top: 20px;\"\r\n      >\r\n        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n        <el-table-column align=\"center\" label=\"银行流水\" prop=\"bankRecordNo\"/>\r\n        <el-table-column align=\"center\" label=\"收支\" prop=\"isRecievingOrPaying\" width=\"30\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.isRecievingOrPaying == 1 ? \"付\" : \"收\" }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"所属公司\" prop=\"sqdPaymentTitleCode\" width=\"50\"/>\r\n        <el-table-column align=\"center\" label=\"银行账户\" prop=\"bankAccountCode\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ scope.row.bankAccountCode }}\r\n            </div>\r\n            <tree-select v-else :class=\"isLocked?'disable-form':''\"\r\n                         :disabled=\"isLocked || isBankSlipConfirmed\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"scope.row.bankAccountCode\" :placeholder=\"'银行账户'\"\r\n                         :type=\"'companyAccount'\" class=\"edit\"\r\n                         @return=\"scope.row.bankAccountCode=$event\"\r\n                         @returnData=\"scope.row.sqdPaymentTitleCode=$event.sqdBelongToCompanyCode\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"结算公司\" prop=\"sqdClearingCompanyShortname\">\r\n          <template slot-scope=\"scope\">\r\n            <el-select v-model=\"scope.row.clearingCompanyId\"\r\n                       :class=\"(scope.row.isBankRecordLocked == 1 && scope.row.slipConfirmed == 1)?'':'edit'\"\r\n                       :disabled=\"scope.row.isBankRecordLocked == 1 && scope.row.slipConfirmed == 1\"\r\n                       placeholder=\"请选择\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in clients\"\r\n                :key=\"item.companyId\"\r\n                :label=\"item.companyShortName\"\r\n                :value=\"item.companyId\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"银行币种\" prop=\"bankCurrencyCode\" width=\"60\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ scope.row.bankCurrencyCode }}\r\n            </div>\r\n            <tree-select v-else\r\n                         :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                         :disabled=\"isBankSlipConfirmed\"\r\n                         :pass=\"scope.row.bankCurrencyCode\" :placeholder=\"'币种'\"\r\n                         :type=\"'currency'\" class=\"edit\"\r\n                         style=\"width: 100%\" @return=\"scope.row.bankCurrencyCode=$event\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"水单金额\" prop=\"slipAmount\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ scope.row.slipAmount }}\r\n            </div>\r\n            <el-input v-else v-model=\"scope.row.slipAmount\"\r\n                      :class=\"isBankSlipConfirmed?'disable-form':''\" :disabled=\"isBankSlipConfirmed\"\r\n                      class=\"edit\" placeholder=\"水单金额\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"水单\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              <div style=\"display: flex\">\r\n                <el-upload\r\n                  ref=\"bankSlipUpload\"\r\n                  :action=\"uploadUrl\"\r\n                  :auto-upload=\"false\"\r\n                  :disabled=\"!scope.row.bankRecordNo\"\r\n                  :http-request=\"customHttpRequest\"\r\n                  :on-change=\"handleChange\"\r\n                  :on-error=\"handleError\"\r\n                  v-if=\"scope.row.slipConfirmed==0\"\r\n                  :on-success=\"handleSuccess\"\r\n                  :show-file-list=\"false\"\r\n                  action=\"xxx\"\r\n                  class=\"upload-demo\" style=\"flex: 1\"\r\n                >\r\n                  <el-button icon=\"el-icon-top-right\" style=\"color: rgb(103, 194, 58)\" type=\"text\"></el-button>\r\n                </el-upload>\r\n                <img-preview v-if=\"scope.row.slipFile || (scope.row.slipFile && scope.row.slipConfirmed)\" :scope=\"scope\"\r\n                             style=\"flex: 1\"\r\n                />\r\n                <el-button v-if=\"scope.row.slipFile && scope.row.slipConfirmed==0\" icon=\"el-icon-delete\"\r\n                           style=\"flex: 1;color: red\"\r\n                           type=\"text\"\r\n                           @click=\"deleteBankSlip(scope.row)\"\r\n                ></el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"水单日期\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ parseTime(scope.row.slipDate, \"{y}-{m}-{d}\") }}\r\n            </div>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"scope.row.slipDate\"\r\n              :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n              :disabled=\"isBankSlipConfirmed\" class=\"edit\" clearable\r\n              default-time=\"12:00:00\"\r\n              placeholder=\"银行时间\"\r\n              style=\"width: 100%\"\r\n              type=\"date\"\r\n              value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"相关操作单号\" prop=\"sqdRaletiveRctList\"/>\r\n        <el-table-column label=\"水单确认\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.slipConfirmed == 1 ? \"√\" : \"-\" }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column v-if=\"type==='receive'\" align=\"center\" label=\"实收金额\" prop=\"actualBankRecievedAmount\"/>\r\n        <el-table-column v-if=\"type==='pay'\" align=\"center\" label=\"实付金额\" prop=\"actualBankPaidAmount\"/>\r\n\r\n\r\n        <el-table-column align=\"center\" label=\"银行时间\" prop=\"bankRecordTime\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.bankRecordTime, \"{y}-{m}-{d}\") }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"流水审核\" prop=\"isBankRecordLocked\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.isBankRecordLocked == 1 ? \"√\" : \"-\" }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"操作\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\" scope.row.slipConfirmed == 0\"\r\n                       icon=\"el-icon-top-right\" type=\"text\"\r\n                       @click=\"submitForm(scope.row)\"\r\n            ></el-button>\r\n            <el-button v-if=\"scope.row.slipConfirmed == 0\"\r\n                       icon=\"el-icon-delete\" style=\"color: red\"\r\n                       type=\"text\" @click=\"handleDelete(scope.row)\"\r\n            ></el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-button type=\"text\" @click=\"handleAdd\">[+]</el-button>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :total=\"total\"\r\n        @pagination=\"getList\"\r\n      />\r\n\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {\r\n  addBankrecord,\r\n  delBankrecord,\r\n  delImg,\r\n  getBankrecord,\r\n  listBankrecord,\r\n  updateBankrecord\r\n} from \"@/api/system/bankrecord\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport request from \"@/utils/request\"\r\nimport ImgPreview from \"@/views/system/rct/imgPreview.vue\"\r\nimport {updateRct} from \"@/api/system/rct\"\r\nimport pinyin from \"js-pinyin\"\r\nimport {listCompanyNoPage} from \"@/api/system/company\"\r\n\r\nexport default {\r\n  name: \"bankSlip\",\r\n  components: {ImgPreview, CompanySelect, Treeselect},\r\n  props: [\"scope\", \"type\"],\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20\r\n      },\r\n      form: {},\r\n      rules: {},\r\n      open: false,\r\n      showDetail: false,\r\n      bankSlipOpen: false,\r\n      bankrecordList: [],\r\n      total: 0,\r\n      loading: false,\r\n      companyList: [],\r\n      fileList: [],\r\n      imageFile: null,\r\n      uploadUrl: \"/system/bankrecord/upload\",\r\n      imgUrl: \"\",\r\n      bankSlipPreview: false,\r\n      previewImgOpen: false,\r\n      clients: []\r\n    }\r\n  },\r\n  beforeMount() {\r\n\r\n  },\r\n  computed: {\r\n    isLocked() {\r\n      return this.form.isBankRecordLocked == 1\r\n    },\r\n    isBankSlipConfirmed() {\r\n      return this.form.slipConfirmed == 1\r\n    }\r\n  },\r\n  methods: {\r\n    loadCompanyOptions() {\r\n      let companyIds = [this.scope.row.clientId]\r\n      this.scope.row.relationClientIdList.split(\",\").length > 0 ? this.scope.row.relationClientIdList.split(\",\").map(item => parseInt(item) ? companyIds.push(parseInt(item)) : null) : null\r\n      listCompanyNoPage({companyIds: companyIds}).then(response => {\r\n\r\n        this.clients = response.rows // 更新选项数据\r\n      })\r\n    },\r\n    selectCompany(row, node) {\r\n      row.clearingCompanyId = node.companyId\r\n      row.sqdClearingCompanyShortname = node.companyShortName\r\n    },\r\n    async deleteBankSlip(row) {\r\n      // 删除服务器中的图片文件\r\n      try {\r\n        await delImg({url: row.slipFile})\r\n      } catch (e) {\r\n        // 更新流水中的图片地址\r\n        await updateBankrecord({\r\n          bankRecordId: row.bankRecordId,\r\n          slipFile: null,\r\n          isRecievingOrPaying: this.type === \"pay\" ? \"1\" : \"0\",\r\n          bankRecordNo: row.bankRecordNo\r\n        })\r\n      }\r\n\r\n      await this.getList()\r\n    },\r\n    customHttpRequest(options) {\r\n      const formData = new FormData()\r\n      formData.append(\"file\", options.file)\r\n\r\n      request({\r\n        url: \"/system/bankrecord/uploadImg\",\r\n        method: \"post\",\r\n        data: formData\r\n      }).then(response => {\r\n        options.onSuccess(response, options.file)\r\n        this.imgUrl = response.url\r\n        this.form.slipFile = response.url\r\n        options.row.slipFile = response.url\r\n      }).catch(error => {\r\n        options.onError(error)\r\n      })\r\n    },\r\n    handleChange(file, fileList) {\r\n\r\n      const extension = file.name.substring(file.name.lastIndexOf(\".\"))\r\n      const newFileName = `${this.form.bankRecordNo}${extension}`\r\n      this.imageFile = new File([file.raw], newFileName, {type: file.type})\r\n      this.$message.success(\"图片已选择\")\r\n    },\r\n    handleSuccess(response, file, fileList) {\r\n      this.form.slipFile = response.url\r\n    },\r\n    handleError(err, file, fileList) {\r\n      this.$message.error(\"Upload failed:\", err)\r\n    },\r\n    handleRemove(file, fileList) {\r\n      console.log(file, fileList)\r\n    },\r\n    handlePreview(file) {\r\n      console.log(file)\r\n    },\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)\r\n    },\r\n    beforeRemove(file, fileList) {\r\n      return this.$confirm(`确定移除 ${file.name}？`)\r\n    },\r\n    uploadImage(row) {\r\n      return new Promise((resolve, reject) => {\r\n        this.customHttpRequest({\r\n          file: this.imageFile,\r\n          onSuccess: (response) => {\r\n            this.handleSuccess(response)\r\n            resolve(response)\r\n          },\r\n          onError: (error) => {\r\n            this.handleError(error)\r\n            reject(error)\r\n          },\r\n          row\r\n        })\r\n      })\r\n    },\r\n    async submitForm(row) {\r\n      // 先上传图片\r\n      if (this.imageFile) {\r\n        // Perform the upload first and wait for it to complete\r\n        await this.uploadImage(row)\r\n      }\r\n      console.log(row)\r\n      updateBankrecord(row).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        // this.open = false\r\n        this.getList()\r\n      })\r\n\r\n      // 更新操作单上的水单信息\r\n      if (this.type === \"pay\") {\r\n        updateRct({rctId: this.scope.row.rctId, sqdDnPaySlipStatus: \"√\"}).then(response => {\r\n        })\r\n      } else {\r\n        updateRct({rctId: this.scope.row.rctId, sqdDnReceiveSlipStatus: \"√\"}).then(response => {\r\n        })\r\n      }\r\n    },\r\n    parseTime,\r\n    currency,\r\n    async openBankSlip() {\r\n      await this.getList()\r\n      this.bankSlipOpen = true\r\n    },\r\n    getList() {\r\n      this.loading = true\r\n      listBankrecord({\r\n        clearingCompanyId: this.scope.row.clientId,\r\n        isRecievingOrPaying: this.type === \"pay\" ? \"1\" : \"0\",\r\n        rctNo: this.scope.row.rctNo\r\n      }).then(response => {\r\n        response.rows.map(item => item.clients = this.clients)\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total ? response.total : 0\r\n        this.loading = false\r\n        // 应收水单状态 sqd_dn_receive_slip_status\r\n        if (response.rows && response.rows.length === 0 && this.type === \"receive\" && this.scope.row.sqdDnReceiveSlipStatus === null) {\r\n          updateRct({rctId: this.scope.row.rctId, sqdDnReceiveSlipStatus: \"-\"}).then(response => {\r\n          })\r\n        }\r\n        // 应付水单状态 sqd_dn_pay_slip_status\r\n        if (response.rows && response.rows.length === 0 && this.type === \"pay\" && this.scope.row.sqdDnPaySlipStatus === null) {\r\n          updateRct({rctId: this.scope.row.rctId, sqdDnPaySlipStatus: \"-\"}).then(response => {\r\n          })\r\n        }\r\n      })\r\n    },\r\n    selectBankAccount(row) {\r\n      console.log(row)\r\n      this.form.sqdPaymentTitleCode = row.sqdBelongToCompanyCode\r\n    },\r\n    handleDelete(row) {\r\n      const bankRecordIds = row.bankRecordId || this.ids\r\n      this.$confirm(\"是否确认删除？\", '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delBankrecord(bankRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleUpdate(row) {\r\n      this.add = false\r\n      this.reset()\r\n      const bankRecordId = row.bankRecordId || this.ids\r\n      getBankrecord(bankRecordId).then(response => {\r\n        this.form = response.data\r\n        this.form.chargeType = \"订单\"\r\n        this.open = true\r\n        this.title = \"查看银行流水-销账明细\"\r\n        this.companyList = [response.companyList]\r\n      })\r\n    },\r\n    verify() {\r\n      if (this.form.clearingCompanyId === null) {\r\n        this.$message.warning(\"请输入结算公司\")\r\n        return\r\n      }\r\n      this.form.isBankRecordLocked = 1\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$message.success(\"修改成功\")\r\n      })\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        bankRecordId: null,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: \"0\",\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        invoiceNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        chargeType: \"订单\"\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    handleAdd() {\r\n      let data = {}\r\n      data.isRecievingOrPaying = this.type === \"pay\" ? \"1\" : \"0\"\r\n      data.paymentTypeCode = \"T/T\"\r\n      data.chargeType = \"订单\"\r\n      data.chargeTypeId = 2\r\n      data.clearingCompanyId = this.scope.row.clientId\r\n      data.sqdClearingCompanyShortname = this.scope.row.clientSummary.split(\"/\")[1]\r\n      data.sqdRaletiveRctList = this.scope.row.rctNo\r\n\r\n      addBankrecord(data).then(response => {\r\n        this.form = response.data\r\n        this.$modal.msgSuccess(\"新增成功\")\r\n        // this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    isRecievingOrPayingNormalizer(node) {\r\n      return {\r\n        id: node.value,\r\n        label: node.label\r\n      }\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    },\r\n    queryCompany(node) {\r\n      this.queryParams.company = node.companyShortName\r\n      this.queryParams.companyId = node.companyId\r\n      this.handleQuery()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n::v-deep .vue-treeselect__value-container {\r\n  display: block;\r\n}\r\n</style>\r\n"]}]}