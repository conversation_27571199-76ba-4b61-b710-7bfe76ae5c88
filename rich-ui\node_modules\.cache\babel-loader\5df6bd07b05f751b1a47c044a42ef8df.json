{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\server\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\server\\index.vue", "mtime": 1754876882568}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_server", "require", "name", "data", "server", "created", "getList", "openLoading", "methods", "_this", "getServer", "then", "response", "$modal", "closeLoading", "loading", "exports", "default", "_default"], "sources": ["src/views/monitor/server/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-cpu\"></i>CPU</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <thead>\r\n              <tr>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">属性</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">值</div>\r\n                </th>\r\n              </tr>\r\n              </thead>\r\n              <tbody>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">核心数</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.cpu\" class=\"cell\">{{ server.cpu.cpuNum }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">用户使用率</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.cpu\" class=\"cell\">{{ server.cpu.used }}%</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">系统使用率</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.cpu\" class=\"cell\">{{ server.cpu.sys }}%</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">当前空闲率</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.cpu\" class=\"cell\">{{ server.cpu.free }}%</div>\r\n                </td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"><span><i class=\"el-icon-tickets\"></i> 内存</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <thead>\r\n              <tr>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">属性</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">内存</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">JVM</div>\r\n                </th>\r\n              </tr>\r\n              </thead>\r\n              <tbody>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">总内存</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.mem\" class=\"cell\">{{ server.mem.total }}G</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.total }}M</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">已用内存</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.mem\" class=\"cell\">{{ server.mem.used }}G</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.used }}M</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">剩余内存</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.mem\" class=\"cell\">{{ server.mem.free }}G</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.free }}M</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">使用率</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.mem\" :class=\"{'text-danger': server.mem.usage > 80}\" class=\"cell\">\r\n                    {{ server.mem.usage }}%\r\n                  </div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" :class=\"{'text-danger': server.jvm.usage > 80}\" class=\"cell\">\r\n                    {{ server.jvm.usage }}%\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-monitor\"></i> 服务器信息</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <tbody>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">服务器名称</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.sys\" class=\"cell\">{{ server.sys.computerName }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">操作系统</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.sys\" class=\"cell\">{{ server.sys.osName }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">服务器IP</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.sys\" class=\"cell\">{{ server.sys.computerIp }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">系统架构</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.sys\" class=\"cell\">{{ server.sys.osArch }}</div>\r\n                </td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-coffee-cup\"></i>Java虚拟机信息</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;table-layout:fixed;\">\r\n              <tbody>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">Java名称</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.name }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">Java版本</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.version }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">启动时间</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.startTime }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">运行时长</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.runTime }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\" colspan=\"1\">\r\n                  <div class=\"cell\">安装路径</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\" colspan=\"3\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.home }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\" colspan=\"1\">\r\n                  <div class=\"cell\">项目路径</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\" colspan=\"3\">\r\n                  <div v-if=\"server.sys\" class=\"cell\">{{ server.sys.userDir }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\" colspan=\"1\">\r\n                  <div class=\"cell\">运行参数</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\" colspan=\"3\">\r\n                  <div v-if=\"server.jvm\" class=\"cell\">{{ server.jvm.inputArgs }}</div>\r\n                </td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-receiving\"></i> 磁盘状态</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%;\">\r\n              <thead>\r\n              <tr>\r\n                <th class=\"el-table__cell el-table__cell is-leaf\">\r\n                  <div class=\"cell\">盘符路径</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">文件系统</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">盘符类型</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">总大小</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">可用大小</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">已用大小</div>\r\n                </th>\r\n                <th class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">已用百分比</div>\r\n                </th>\r\n              </tr>\r\n              </thead>\r\n              <tbody v-if=\"server.sysFiles\">\r\n              <tr v-for=\"(sysFile, index) in server.sysFiles\" :key=\"index\">\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">{{ sysFile.dirName }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">{{ sysFile.sysTypeName }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">{{ sysFile.typeName }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">{{ sysFile.total }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">{{ sysFile.free }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">{{ sysFile.used }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div :class=\"{'text-danger': sysFile.usage > 80}\" class=\"cell\">{{ sysFile.usage }}%</div>\r\n                </td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getServer} from \"@/api/monitor/server\";\r\n\r\nexport default {\r\n  name: \"Server\",\r\n  data() {\r\n    return {\r\n      // 服务器信息\r\n      server: []\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.openLoading();\r\n  },\r\n  methods: {\r\n    /** 查询服务器信息 */\r\n    getList() {\r\n      getServer().then(response => {\r\n        this.server = response.data;\r\n        this.$modal.closeLoading();\r\n      });\r\n    },\r\n    // 打开加载层\r\n    openLoading() {\r\n      this.$modal.loading(\"正在加载服务监控数据，请稍候！\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;AAgTA,IAAAA,OAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,cACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,iBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAL,MAAA,GAAAQ,QAAA,CAAAT,IAAA;QACAM,KAAA,CAAAI,MAAA,CAAAC,YAAA;MACA;IACA;IACA;IACAP,WAAA,WAAAA,YAAA;MACA,KAAAM,MAAA,CAAAE,OAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}