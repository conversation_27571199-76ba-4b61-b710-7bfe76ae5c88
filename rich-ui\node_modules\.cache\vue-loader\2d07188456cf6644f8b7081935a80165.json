{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue", "mtime": 1754882319639}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRWYXRpbnZvaWNlLA0KICBjaGFuZ2VTdGF0dXMsDQogIGRlbFZhdGludm9pY2UsDQogIGdldFZhdGludm9pY2UsDQogIGxpc3RWYXRpbnZvaWNlLA0KICB1cGRhdGVWYXRpbnZvaWNlLA0KICBnZW5lcmF0ZUludm9pY2VFeGNlbA0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vdmF0SW52b2ljZSINCmltcG9ydCBWYXRpbnZvaWNlRGlhbG9nIGZyb20gJy4vY29tcG9uZW50cy9WYXRpbnZvaWNlRGlhbG9nJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJWYXRpbnZvaWNlIiwNCiAgY29tcG9uZW50czogew0KICAgIFZhdGludm9pY2VEaWFsb2cNCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0xlZnQ6IDAsDQogICAgICBzaG93UmlnaHQ6IDI0LA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOWPkeelqOeZu+iusOihqOagvOaVsOaNrg0KICAgICAgdmF0aW52b2ljZUxpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIGludm9pY2VDb2RlTm86IG51bGwsDQogICAgICAgIGludm9pY2VPZmZpY2FsTm86IG51bGwsDQogICAgICAgIHNhbGVCdXk6IG51bGwsDQogICAgICAgIGludm9pY2VCZWxvbmdzVG86IG51bGwsDQogICAgICAgIHJpY2hCYW5rQ29kZTogbnVsbCwNCiAgICAgICAgcmljaENvbXBhbnlUaXRsZTogbnVsbCwNCiAgICAgICAgcmljaFZhdFNlcmlhbE5vOiBudWxsLA0KICAgICAgICByaWNoQmFua0FjY291bnQ6IG51bGwsDQogICAgICAgIHJpY2hCYW5rRnVsbG5hbWU6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JJZDogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvclNob3J0TmFtZTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvckJhbmtDb2RlOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yRnVsbG5hbWU6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JWYXRTZXJpYWxObzogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvckJhbmtBY2NvdW50OiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yQmFua0Z1bGxuYW1lOiBudWxsLA0KICAgICAgICByY3ROb1N1bW1hcnk6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JSZWZlck5vOiBudWxsLA0KICAgICAgICBvZmZpY2FsQ2hhcmdlTmFtZVN1bW1hcnk6IG51bGwsDQogICAgICAgIGNoYXJnZUN1cnJlbmN5Q29kZTogbnVsbCwNCiAgICAgICAgZG5TdW06IG51bGwsDQogICAgICAgIGRuUmVjaWV2ZWQ6IG51bGwsDQogICAgICAgIGRuQmFsYW5jZTogbnVsbCwNCiAgICAgICAgY25TdW06IG51bGwsDQogICAgICAgIGNuUGFpZDogbnVsbCwNCiAgICAgICAgY25CYWxhbmNlOiBudWxsLA0KICAgICAgICBjaGFyZ2VDbGVhclN0YXR1czogbnVsbCwNCiAgICAgICAgZXhwZWN0ZWRQYXlEYXRlOiBudWxsLA0KICAgICAgICBhcHByb3ZlZFBheURhdGU6IG51bGwsDQogICAgICAgIGFjdHVhbFBheURhdGU6IG51bGwsDQogICAgICAgIHNxZEJhbmtTdGF0ZW1lbnRMaXN0OiBudWxsLA0KICAgICAgICBpbnZvaWNlQ3VycmVuY3lDb2RlOiBudWxsLA0KICAgICAgICBpbnZvaWNlRXhjaGFuZ2VSYXRlOiBudWxsLA0KICAgICAgICBpbnZvaWNlTmV0QW1vdW50OiBudWxsLA0KICAgICAgICB2YXRBbW91bnQ6IG51bGwsDQogICAgICAgIGludm9pY2VWYXRBbW91bnQ6IG51bGwsDQogICAgICAgIHNhbGVOZXRTdW06IG51bGwsDQogICAgICAgIHNhbGVUYXg6IG51bGwsDQogICAgICAgIHNhbGVUYXhUb3RhbDogbnVsbCwNCiAgICAgICAgYnV5TmV0U3VtOiBudWxsLA0KICAgICAgICBidXlUYXg6IG51bGwsDQogICAgICAgIGJ1eVRheFRvdGFsOiBudWxsLA0KICAgICAgICB0YXhDbGFzczogbnVsbCwNCiAgICAgICAgaW52b2ljZVR5cGU6IG51bGwsDQogICAgICAgIGJlbG9uZ3NUb01vbnRoOiBudWxsLA0KICAgICAgICBpbnZvaWNlU3RhdHVzOiBudWxsLA0KICAgICAgICBpbnZvaWNlUmVtYXJrOiBudWxsLA0KICAgICAgICBhcHBseVN0dWZmSWQ6IG51bGwsDQogICAgICAgIGFwcGxpZWRUaW1lOiBudWxsLA0KICAgICAgICBpc3N1ZWRTdHVmZklkOiBudWxsLA0KICAgICAgICBpc3N1ZWRUaW1lOiBudWxsLA0KICAgICAgICB0YXhTdHVmZklkOiBudWxsLA0KICAgICAgICB0YXhEZWNsYXJlVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGludm9pY2VDb2RlTm86IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLlj5HnpajmtYHmsLTlj7fkuI3og73kuLrnqboiLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBzYWxlQnV5OiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi6L+b6ZSA5qCH5b+X77yac2FsZT3plIDpobnvvIxidXk96L+b6aG55LiN6IO95Li656m6IiwNCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIg0KICAgICAgICAgIH0NCiAgICAgICAgXQ0KICAgICAgfSwNCiAgICAgIGludm9pY2VJdGVtTGlzdDogW10sDQogICAgICBjb21wYW55TGlzdDogW10sDQogICAgICBiYW5rQWNjb3VudExpc3Q6IFtdLA0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93U2VhcmNoKG4pIHsNCiAgICAgIGlmIChuID09PSB0cnVlKSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjENCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjQNCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDANCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCkNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZVJvd0NsaWNrKHJvdykgew0KICAgICAgdGhpcy5oYW5kbGVVcGRhdGUocm93KQ0KICAgIH0sDQogICAgLyoqIOafpeivouWPkeelqOeZu+iusOWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBsaXN0VmF0aW52b2ljZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy52YXRpbnZvaWNlTGlzdCA9IHJlc3BvbnNlLnJvd3MNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsDQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGludm9pY2VJZDogbnVsbCwNCiAgICAgICAgaW52b2ljZUNvZGVObzogbnVsbCwNCiAgICAgICAgaW52b2ljZU9mZmljYWxObzogbnVsbCwNCiAgICAgICAgc2FsZUJ1eTogbnVsbCwNCiAgICAgICAgaW52b2ljZUJlbG9uZ3NUbzogbnVsbCwNCiAgICAgICAgcmljaEJhbmtDb2RlOiBudWxsLA0KICAgICAgICByaWNoQ29tcGFueVRpdGxlOiBudWxsLA0KICAgICAgICByaWNoVmF0U2VyaWFsTm86IG51bGwsDQogICAgICAgIHJpY2hCYW5rQWNjb3VudDogbnVsbCwNCiAgICAgICAgcmljaEJhbmtGdWxsbmFtZTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvcklkOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yU2hvcnROYW1lOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yQmFua0NvZGU6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JGdWxsbmFtZTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvclZhdFNlcmlhbE5vOiBudWxsLA0KICAgICAgICBjb29wZXJhdG9yQmFua0FjY291bnQ6IG51bGwsDQogICAgICAgIGNvb3BlcmF0b3JCYW5rRnVsbG5hbWU6IG51bGwsDQogICAgICAgIHJjdE5vU3VtbWFyeTogbnVsbCwNCiAgICAgICAgY29vcGVyYXRvclJlZmVyTm86IG51bGwsDQogICAgICAgIG9mZmljYWxDaGFyZ2VOYW1lU3VtbWFyeTogbnVsbCwNCiAgICAgICAgY2hhcmdlQ3VycmVuY3lDb2RlOiBudWxsLA0KICAgICAgICBkblN1bTogbnVsbCwNCiAgICAgICAgZG5SZWNpZXZlZDogbnVsbCwNCiAgICAgICAgZG5CYWxhbmNlOiBudWxsLA0KICAgICAgICBjblN1bTogbnVsbCwNCiAgICAgICAgY25QYWlkOiBudWxsLA0KICAgICAgICBjbkJhbGFuY2U6IG51bGwsDQogICAgICAgIGNoYXJnZUNsZWFyU3RhdHVzOiAiMCIsDQogICAgICAgIGV4cGVjdGVkUGF5RGF0ZTogbnVsbCwNCiAgICAgICAgYXBwcm92ZWRQYXlEYXRlOiBudWxsLA0KICAgICAgICBhY3R1YWxQYXlEYXRlOiBudWxsLA0KICAgICAgICBzcWRCYW5rU3RhdGVtZW50TGlzdDogbnVsbCwNCiAgICAgICAgaW52b2ljZUN1cnJlbmN5Q29kZTogbnVsbCwNCiAgICAgICAgaW52b2ljZUV4Y2hhbmdlUmF0ZTogbnVsbCwNCiAgICAgICAgaW52b2ljZU5ldEFtb3VudDogbnVsbCwNCiAgICAgICAgdmF0QW1vdW50OiBudWxsLA0KICAgICAgICBpbnZvaWNlVmF0QW1vdW50OiBudWxsLA0KICAgICAgICBzYWxlTmV0U3VtOiBudWxsLA0KICAgICAgICBzYWxlVGF4OiBudWxsLA0KICAgICAgICBzYWxlVGF4VG90YWw6IG51bGwsDQogICAgICAgIGJ1eU5ldFN1bTogbnVsbCwNCiAgICAgICAgYnV5VGF4OiBudWxsLA0KICAgICAgICBidXlUYXhUb3RhbDogbnVsbCwNCiAgICAgICAgdGF4Q2xhc3M6IG51bGwsDQogICAgICAgIGludm9pY2VUeXBlOiBudWxsLA0KICAgICAgICBiZWxvbmdzVG9Nb250aDogbnVsbCwNCiAgICAgICAgaW52b2ljZVN0YXR1czogIjAiLA0KICAgICAgICBpbnZvaWNlUmVtYXJrOiBudWxsLA0KICAgICAgICBhcHBseVN0dWZmSWQ6IG51bGwsDQogICAgICAgIGFwcGxpZWRUaW1lOiBudWxsLA0KICAgICAgICBpc3N1ZWRTdHVmZklkOiBudWxsLA0KICAgICAgICBpc3N1ZWRUaW1lOiBudWxsLA0KICAgICAgICB0YXhTdHVmZklkOiBudWxsLA0KICAgICAgICB0YXhEZWNsYXJlVGltZTogbnVsbCwNCiAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIg0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgi56Gu6K6k6KaBXCIiICsgdGV4dCArICLlkJfvvJ8iKS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGNoYW5nZVN0YXR1cyhyb3cuaW52b2ljZUlkLCByb3cuc3RhdHVzKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKQ0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgew0KICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pbnZvaWNlSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDlj5HnpajnmbvorrAiDQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICBjb25zdCBpbnZvaWNlSWQgPSByb3cuaW52b2ljZUlkIHx8IHRoaXMuaWRzDQogICAgICBnZXRWYXRpbnZvaWNlKGludm9pY2VJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgdGhpcy5jb21wYW55TGlzdCA9IHJlc3BvbnNlLmV4dENvbXBhbnlMaXN0DQogICAgICAgIHRoaXMuYmFua0FjY291bnRMaXN0ID0gcmVzcG9uc2UuYmFzQWNjb3VudExpc3QNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgICB0aGlzLnRpdGxlID0gIuS/ruaUueWPkeelqOeZu+iusCINCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIC8vIOW3suenu+iHs2hhbmRsZURpYWxvZ1N1Ym1pdOaWueazlQ0KICAgIH0sDQoNCiAgICAvKiog5aSE55CG5a+56K+d5qGG5o+Q5LqkICovDQogICAgaGFuZGxlRGlhbG9nU3VibWl0KGZvcm1EYXRhKSB7DQogICAgICBpZiAoZm9ybURhdGEuaW52b2ljZUlkICE9IG51bGwpIHsNCiAgICAgICAgdXBkYXRlVmF0aW52b2ljZShmb3JtRGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICBhZGRWYXRpbnZvaWNlKGZvcm1EYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKQ0KICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBpbnZvaWNlSWRzID0gcm93Lmludm9pY2VJZCB8fCB0aGlzLmlkcw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgi5piv5ZCm56Gu6K6k5Yig6Zmk5Y+R56Wo55m76K6w57yW5Y+35Li6XCIiICsgaW52b2ljZUlkcyArICJcIueahOaVsOaNrumhue+8nyIpLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gZGVsVmF0aW52b2ljZShpbnZvaWNlSWRzKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuW8gOelqOi1hOaWmeaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydFRoZUludm9pY2luZ0luZm9ybWF0aW9uKCkgew0KICAgICAgLy8g5qOA5p+l5piv5ZCm5pyJ6YCJ5Lit55qE5Y+R56WoDQogICAgICBpZiAodGhpcy5pZHMubGVuZ3RoID09PSAwKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCLor7flhYjpgInmi6nopoHlr7zlh7rnmoTlj5HnpagiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgZ2VuZXJhdGVJbnZvaWNlRXhjZWwodGhpcy5pZHMpDQogICAgICAgIC50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAvLyDojrflj5bmlofku7bnmoTlrZfoioLmlbDnu4QgKEFycmF5QnVmZmVyKQ0KICAgICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZQ0KDQogICAgICAgICAgLy8g55Sf5oiQ5paH5Lu25ZCNDQogICAgICAgICAgbGV0IGZpbGVOYW1lID0gYEludm9pY2VzXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgDQoNCiAgICAgICAgICAvLyDliJvlu7rkuIDkuKogQmxvYiDlr7nosaHmnaXlrZjlgqjmlofku7YNCiAgICAgICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2RhdGFdLCB7DQogICAgICAgICAgICB0eXBlOiAiYXBwbGljYXRpb24vdm5kLm9wZW54bWxmb3JtYXRzLW9mZmljZWRvY3VtZW50LnNwcmVhZHNoZWV0bWwuc2hlZXQiICAvLyBFeGNlbCDmlofku7bnsbvlnosNCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgLy8g5Yib5bu65LiA5Liq5Li05pe26ZO+5o6l77yM5qih5ouf54K55Ye75p2l5LiL6L295paH5Lu2DQogICAgICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoImEiKQ0KICAgICAgICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpICAvLyDliJvlu7rkuIDkuKogVVJMIOaMh+WQkSBCbG9iIOWvueixoQ0KICAgICAgICAgIGxpbmsuaHJlZiA9IHVybA0KICAgICAgICAgIGxpbmsuZG93bmxvYWQgPSBmaWxlTmFtZSAgLy8g6K6+572u5LiL6L2955qE5paH5Lu25ZCNDQoNCiAgICAgICAgICAvLyDmqKHmi5/ngrnlh7vpk77mjqXvvIzop6blj5HkuIvovb0NCiAgICAgICAgICBkb2N1bWVudC5ib2R5LmFwcGVuZENoaWxkKGxpbmspDQogICAgICAgICAgbGluay5jbGljaygpDQoNCiAgICAgICAgICAvLyDkuIvovb3lrozmiJDlkI7np7vpmaTpk77mjqXvvIzlubbph4rmlL4gVVJMIOWvueixoQ0KICAgICAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluaykNCiAgICAgICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpDQoNCiAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCJFeGNlbOaWh+S7tuWvvOWHuuaIkOWKnyIpDQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi5paH5Lu25LiL6L295aSx6LSlOiIsIGVycm9yKQ0KICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ0Vycm9yKCJFeGNlbOaWh+S7tuWvvOWHuuWksei0pSIpDQogICAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5a+85Ye65oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRXhwb3J0KCkgew0KICAgICAgdGhpcy5kb3dubG9hZCgic3lzdGVtL3ZhdGludm9pY2UvZXhwb3J0Iiwgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgdmF0aW52b2ljZV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAujBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/vatinvoice", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"发票流水号\" prop=\"invoiceCodeNo\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceCodeNo\"\r\n                clearable\r\n                placeholder=\"发票流水号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票号码\" prop=\"invoiceOfficalNo\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceOfficalNo\"\r\n                clearable\r\n                placeholder=\"发票号码\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属公司\" prop=\"invoiceBelongsTo\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceBelongsTo\"\r\n                clearable\r\n                placeholder=\"所属公司\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账户简称\" prop=\"richBankCode\">\r\n            <el-input\r\n                v-model=\"queryParams.richBankCode\"\r\n                clearable\r\n                placeholder=\"我司账户简称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司发票抬头\" prop=\"richCompanyTitle\">\r\n            <el-input\r\n                v-model=\"queryParams.richCompanyTitle\"\r\n                clearable\r\n                placeholder=\"我司发票抬头\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司纳税人识别号\" prop=\"richVatSerialNo\">\r\n            <el-input\r\n                v-model=\"queryParams.richVatSerialNo\"\r\n                clearable\r\n                placeholder=\"我司纳税人识别号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账号\" prop=\"richBankAccount\">\r\n            <el-input\r\n                v-model=\"queryParams.richBankAccount\"\r\n                clearable\r\n                placeholder=\"我司账号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司银行全称\" prop=\"richBankFullname\">\r\n            <el-input\r\n                v-model=\"queryParams.richBankFullname\"\r\n                clearable\r\n                placeholder=\"我司银行全称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司ID\" prop=\"cooperatorId\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorId\"\r\n                clearable\r\n                placeholder=\"对方公司ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司简称\" prop=\"cooperatorShortName\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorShortName\"\r\n                clearable\r\n                placeholder=\"对方公司简称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账户简称\" prop=\"cooperatorBankCode\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorBankCode\"\r\n                clearable\r\n                placeholder=\"对方账户简称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorCompanyTitle\"\r\n                clearable\r\n                placeholder=\"对方发票抬头\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方纳税人识别号\" prop=\"cooperatorVatSerialNo\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorVatSerialNo\"\r\n                clearable\r\n                placeholder=\"对方纳税人识别号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账号\" prop=\"cooperatorBankAccount\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorBankAccount\"\r\n                clearable\r\n                placeholder=\"对方账号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方银行全称\" prop=\"cooperatorBankFullname\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorBankFullname\"\r\n                clearable\r\n                placeholder=\"对方银行全称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算币种\" prop=\"chargeCurrencyCode\">\r\n            <el-input\r\n                v-model=\"queryParams.chargeCurrencyCode\"\r\n                clearable\r\n                placeholder=\"结算币种\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应收总额\" prop=\"dnSum\">\r\n            <el-input\r\n                v-model=\"queryParams.dnSum\"\r\n                clearable\r\n                placeholder=\"账单应收总额\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已收\" prop=\"dnRecieved\">\r\n            <el-input\r\n                v-model=\"queryParams.dnRecieved\"\r\n                clearable\r\n                placeholder=\"银行已收\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未收\" prop=\"dnBalance\">\r\n            <el-input\r\n                v-model=\"queryParams.dnBalance\"\r\n                clearable\r\n                placeholder=\"银行未收\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应付总额\" prop=\"cnSum\">\r\n            <el-input\r\n                v-model=\"queryParams.cnSum\"\r\n                clearable\r\n                placeholder=\"账单应付总额\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已付\" prop=\"cnPaid\">\r\n            <el-input\r\n                v-model=\"queryParams.cnPaid\"\r\n                clearable\r\n                placeholder=\"银行已付\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未付\" prop=\"cnBalance\">\r\n            <el-input\r\n                v-model=\"queryParams.cnBalance\"\r\n                clearable\r\n                placeholder=\"银行未付\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"期望支付日期\" prop=\"expectedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.expectedPayDate\"\r\n                            clearable\r\n                            placeholder=\"期望支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"批复支付日期\" prop=\"approvedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.approvedPayDate\"\r\n                            clearable\r\n                            placeholder=\"批复支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"实际支付日期\" prop=\"actualPayDate\">\r\n            <el-date-picker v-model=\"queryParams.actualPayDate\"\r\n                            clearable\r\n                            placeholder=\"实际支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"发票币种\" prop=\"invoiceCurrencyCode\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceCurrencyCode\"\r\n                clearable\r\n                placeholder=\"发票币种\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票汇率\" prop=\"invoiceExchangeRate\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceExchangeRate\"\r\n                clearable\r\n                placeholder=\"发票汇率\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"不含税金额\" prop=\"invoiceNetAmount\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceNetAmount\"\r\n                clearable\r\n                placeholder=\"不含税金额\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"税金\" prop=\"vatAmount\">\r\n            <el-input\r\n                v-model=\"queryParams.vatAmount\"\r\n                clearable\r\n                placeholder=\"税金\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"价税合计\" prop=\"invoiceVatAmount\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceVatAmount\"\r\n                clearable\r\n                placeholder=\"价税合计\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项不含税\" prop=\"saleNetSum\">\r\n            <el-input\r\n                v-model=\"queryParams.saleNetSum\"\r\n                clearable\r\n                placeholder=\"销项不含税\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项税金\" prop=\"saleTax\">\r\n            <el-input\r\n                v-model=\"queryParams.saleTax\"\r\n                clearable\r\n                placeholder=\"销项税金\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项含税合计\" prop=\"saleTaxTotal\">\r\n            <el-input\r\n                v-model=\"queryParams.saleTaxTotal\"\r\n                clearable\r\n                placeholder=\"销项含税合计\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项不含税\" prop=\"buyNetSum\">\r\n            <el-input\r\n                v-model=\"queryParams.buyNetSum\"\r\n                clearable\r\n                placeholder=\"进项不含税\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项税金\" prop=\"buyTax\">\r\n            <el-input\r\n                v-model=\"queryParams.buyTax\"\r\n                clearable\r\n                placeholder=\"进项税金\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项含税合计\" prop=\"buyTaxTotal\">\r\n            <el-input\r\n                v-model=\"queryParams.buyTaxTotal\"\r\n                clearable\r\n                placeholder=\"进项含税合计\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票性质\" prop=\"taxClass\">\r\n            <el-input\r\n                v-model=\"queryParams.taxClass\"\r\n                clearable\r\n                placeholder=\"发票性质\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税所属月份\" prop=\"belongsToMonth\">\r\n            <el-input\r\n                v-model=\"queryParams.belongsToMonth\"\r\n                clearable\r\n                placeholder=\"报税所属月份\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请人ID\" prop=\"applyStuffId\">\r\n            <el-input\r\n                v-model=\"queryParams.applyStuffId\"\r\n                clearable\r\n                placeholder=\"申请人ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请时间\" prop=\"appliedTime\">\r\n            <el-date-picker v-model=\"queryParams.appliedTime\"\r\n                            clearable\r\n                            placeholder=\"申请时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"开票人ID\" prop=\"issuedStuffId\">\r\n            <el-input\r\n                v-model=\"queryParams.issuedStuffId\"\r\n                clearable\r\n                placeholder=\"开票人ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"开票时间\" prop=\"issuedTime\">\r\n            <el-date-picker v-model=\"queryParams.issuedTime\"\r\n                            clearable\r\n                            placeholder=\"开票时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"报税人ID\" prop=\"taxStuffId\">\r\n            <el-input\r\n                v-model=\"queryParams.taxStuffId\"\r\n                clearable\r\n                placeholder=\"报税人ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税时间\" prop=\"taxDeclareTime\">\r\n            <el-date-picker v-model=\"queryParams.taxDeclareTime\"\r\n                            clearable\r\n                            placeholder=\"报税时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:add']\"\r\n                icon=\"el-icon-plus\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:edit']\"\r\n                :disabled=\"single\"\r\n                icon=\"el-icon-edit\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:remove']\"\r\n                :disabled=\"multiple\"\r\n                icon=\"el-icon-delete\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:export']\"\r\n                icon=\"el-icon-download\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"warning\"\r\n                @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExportTheInvoicingInformation\"\r\n            >导出开票资料\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"vatinvoiceList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"handleRowClick\">\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"发票流水号\" prop=\"invoiceCodeNo\" width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"发票号码\" prop=\"invoiceOfficalNo\"/>\r\n          <el-table-column align=\"center\" label=\"进销标志\" prop=\"saleBuy\"/>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"invoiceBelongsTo\"/>\r\n          <el-table-column align=\"center\" label=\"我司账户简称\" prop=\"richBankCode\"/>\r\n          <el-table-column align=\"center\" label=\"我司发票抬头\" prop=\"richCompanyTitle\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"我司纳税人识别号\" prop=\"richVatSerialNo\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"我司账号\" prop=\"richBankAccount\" show-overflow-tooltip width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"我司银行全称\" prop=\"richBankFullname\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"对方公司简称\" prop=\"cooperatorShortName\"/>\r\n          <el-table-column align=\"center\" label=\"对方账户简称\" prop=\"cooperatorBankCode\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"对方纳税人识别号\" prop=\"cooperatorVatSerialNo\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"对方账号\" prop=\"cooperatorBankAccount\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"对方银行全称\" prop=\"cooperatorBankFullname\"/>\r\n          <el-table-column align=\"center\" label=\"所属订单汇总\" prop=\"rctNoSummary\"/>\r\n          <el-table-column align=\"center\" label=\"对方单号汇总\" prop=\"cooperatorReferNo\"/>\r\n          <el-table-column align=\"center\" label=\"发票项目汇总\" prop=\"officalChargeNameSummary\"/>\r\n          <el-table-column align=\"center\" label=\"结算币种\" prop=\"chargeCurrencyCode\"/>\r\n          <el-table-column align=\"center\" label=\"账单应收总额\" prop=\"dnSum\"/>\r\n          <el-table-column align=\"center\" label=\"银行已收\" prop=\"dnRecieved\"/>\r\n          <el-table-column align=\"center\" label=\"银行未收\" prop=\"dnBalance\"/>\r\n          <el-table-column align=\"center\" label=\"账单应付总额\" prop=\"cnSum\"/>\r\n          <el-table-column align=\"center\" label=\"银行已付\" prop=\"cnPaid\"/>\r\n          <el-table-column align=\"center\" label=\"银行未付\" prop=\"cnBalance\"/>\r\n          <el-table-column align=\"center\" label=\"销账状态\" prop=\"chargeClearStatus\"/>\r\n          <el-table-column align=\"center\" label=\"期望支付日期\" prop=\"expectedPayDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.expectedPayDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"批复支付日期\" prop=\"approvedPayDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.approvedPayDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实际支付日期\" prop=\"actualPayDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.actualPayDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"对应银行流水\" prop=\"sqdBankStatementList\"/>\r\n          <el-table-column align=\"center\" label=\"发票币种\" prop=\"invoiceCurrencyCode\"/>\r\n          <el-table-column align=\"center\" label=\"发票汇率\" prop=\"invoiceExchangeRate\"/>\r\n          <el-table-column align=\"center\" label=\"不含税金额\" prop=\"invoiceNetAmount\"/>\r\n          <el-table-column align=\"center\" label=\"税金\" prop=\"vatAmount\"/>\r\n          <el-table-column align=\"center\" label=\"价税合计\" prop=\"invoiceVatAmount\"/>\r\n          <el-table-column align=\"center\" label=\"销项不含税\" prop=\"saleNetSum\"/>\r\n          <el-table-column align=\"center\" label=\"销项税金\" prop=\"saleTax\"/>\r\n          <el-table-column align=\"center\" label=\"销项含税合计\" prop=\"saleTaxTotal\"/>\r\n          <el-table-column align=\"center\" label=\"进项不含税\" prop=\"buyNetSum\"/>\r\n          <el-table-column align=\"center\" label=\"进项税金\" prop=\"buyTax\"/>\r\n          <el-table-column align=\"center\" label=\"进项含税合计\" prop=\"buyTaxTotal\"/>\r\n          <el-table-column align=\"center\" label=\"发票性质\" prop=\"taxClass\"/>\r\n          <el-table-column align=\"center\" label=\"发票类型\" prop=\"invoiceType\"/>\r\n          <el-table-column align=\"center\" label=\"报税所属月份\" prop=\"belongsToMonth\"/>\r\n          <el-table-column align=\"center\" label=\"发票状态\" prop=\"invoiceStatus\"/>\r\n          <el-table-column align=\"center\" label=\"备注\" prop=\"invoiceRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"申请人ID\" prop=\"applyStuffId\"/>\r\n          <el-table-column align=\"center\" label=\"申请时间\" prop=\"appliedTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.appliedTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"开票人ID\" prop=\"issuedStuffId\"/>\r\n          <el-table-column align=\"center\" label=\"开票时间\" prop=\"issuedTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.issuedTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"报税人ID\" prop=\"taxStuffId\"/>\r\n          <el-table-column align=\"center\" label=\"报税时间\" prop=\"taxDeclareTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.taxDeclareTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                  v-hasPermi=\"['system:vatinvoice:edit']\"\r\n                  icon=\"el-icon-edit\"\r\n                  size=\"mini\"\r\n                  style=\"margin-right: -8px\"\r\n                  type=\"success\"\r\n                  @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                  v-hasPermi=\"['system:vatinvoice:remove']\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  style=\"margin-right: -8px\"\r\n                  type=\"danger\"\r\n                  @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n            v-show=\"total>0\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :total=\"total\"\r\n            @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改发票登记对话框 -->\r\n    <vatinvoice-dialog\r\n      :form=\"form\"\r\n      :invoice-items=\"invoiceItemList\"\r\n      :bank-account-list=\"bankAccountList\"\r\n      :company-list=\"companyList\"\r\n      :rules=\"rules\"\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      @cancel=\"cancel\"\r\n      @submit=\"handleDialogSubmit\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addVatinvoice,\r\n  changeStatus,\r\n  delVatinvoice,\r\n  getVatinvoice,\r\n  listVatinvoice,\r\n  updateVatinvoice,\r\n  generateInvoiceExcel\r\n} from \"@/api/system/vatInvoice\"\r\nimport VatinvoiceDialog from './components/VatinvoiceDialog'\r\n\r\nexport default {\r\n  name: \"Vatinvoice\",\r\n  components: {\r\n    VatinvoiceDialog\r\n  },\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 发票登记表格数据\r\n      vatinvoiceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: null,\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: null,\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        invoiceCodeNo: [\r\n          {\r\n            required: true,\r\n            message: \"发票流水号不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        saleBuy: [\r\n          {\r\n            required: true,\r\n            message: \"进销标志：sale=销项，buy=进项不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      },\r\n      invoiceItemList: [],\r\n      companyList: [],\r\n      bankAccountList: [],\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    handleRowClick(row) {\r\n      this.handleUpdate(row)\r\n    },\r\n    /** 查询发票登记列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVatinvoice(this.queryParams).then(response => {\r\n        this.vatinvoiceList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        invoiceId: null,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: \"0\",\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: \"0\",\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null,\r\n        createTime: null,\r\n        updateTime: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.invoiceId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.invoiceId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加发票登记\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const invoiceId = row.invoiceId || this.ids\r\n      getVatinvoice(invoiceId).then(response => {\r\n        this.form = response.data\r\n        this.companyList = response.extCompanyList\r\n        this.bankAccountList = response.basAccountList\r\n        this.open = true\r\n        this.title = \"修改发票登记\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 已移至handleDialogSubmit方法\r\n    },\r\n\r\n    /** 处理对话框提交 */\r\n    handleDialogSubmit(formData) {\r\n      if (formData.invoiceId != null) {\r\n        updateVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        addVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const invoiceIds = row.invoiceId || this.ids\r\n      this.$modal.confirm(\"是否确认删除发票登记编号为\\\"\" + invoiceIds + \"\\\"的数据项？\").then(function () {\r\n        return delVatinvoice(invoiceIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出开票资料按钮操作 */\r\n    handleExportTheInvoicingInformation() {\r\n      // 检查是否有选中的发票\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请先选择要导出的发票\")\r\n        return\r\n      }\r\n\r\n      generateInvoiceExcel(this.ids)\r\n        .then(response => {\r\n          // 获取文件的字节数组 (ArrayBuffer)\r\n          const data = response\r\n\r\n          // 生成文件名\r\n          let fileName = `Invoices_${new Date().getTime()}.xlsx`\r\n\r\n          // 创建一个 Blob 对象来存储文件\r\n          const blob = new Blob([data], {\r\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"  // Excel 文件类型\r\n          })\r\n\r\n          // 创建一个临时链接，模拟点击来下载文件\r\n          const link = document.createElement(\"a\")\r\n          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象\r\n          link.href = url\r\n          link.download = fileName  // 设置下载的文件名\r\n\r\n          // 模拟点击链接，触发下载\r\n          document.body.appendChild(link)\r\n          link.click()\r\n\r\n          // 下载完成后移除链接，并释放 URL 对象\r\n          document.body.removeChild(link)\r\n          window.URL.revokeObjectURL(url)\r\n\r\n          this.$modal.msgSuccess(\"Excel文件导出成功\")\r\n        })\r\n        .catch(error => {\r\n          console.error(\"文件下载失败:\", error)\r\n          this.$modal.msgError(\"Excel文件导出失败\")\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/vatinvoice/export\", {\r\n        ...this.queryParams\r\n      }, `vatinvoice_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}