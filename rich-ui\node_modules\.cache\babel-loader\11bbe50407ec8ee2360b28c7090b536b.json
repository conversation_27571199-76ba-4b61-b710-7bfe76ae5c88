{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\dashboard\\mixins\\resize.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\dashboard\\mixins\\resize.js", "mtime": 1754876882563}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_utils", "require", "_default", "data", "$_sidebarElm", "$_resizeHandler", "mounted", "initListener", "activated", "resize", "<PERSON><PERSON><PERSON><PERSON>", "destroyListener", "deactivated", "methods", "$_sidebarResizeHandler", "e", "propertyName", "_this", "debounce", "window", "addEventListener", "document", "getElementsByClassName", "removeEventListener", "chart", "exports", "default"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/dashboard/mixins/resize.js"], "sourcesContent": ["import {debounce} from '@/utils'\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      $_sidebarElm: null,\r\n      $_resizeHandler: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initListener()\r\n  },\r\n  activated() {\r\n    if (!this.$_resizeHandler) {\r\n      // avoid duplication init\r\n      this.initListener()\r\n    }\r\n\r\n    // when keep-alive chart activated, auto resize\r\n    this.resize()\r\n  },\r\n  beforeD<PERSON>roy() {\r\n    this.destroyListener()\r\n  },\r\n  deactivated() {\r\n    this.destroyListener()\r\n  },\r\n  methods: {\r\n    // use $_ for mixins properties\r\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\r\n    $_sidebarResizeHandler(e) {\r\n      if (e.propertyName == 'width') {\r\n        this.$_resizeHandler()\r\n      }\r\n    },\r\n    initListener() {\r\n      this.$_resizeHandler = debounce(() => {\r\n        this.resize()\r\n      }, 100)\r\n      window.addEventListener('resize', this.$_resizeHandler)\r\n\r\n      this.$_sidebarElm = document.getElementsByClassName('sidebar-container')[0]\r\n      this.$_sidebarElm && this.$_sidebarElm.addEventListener('transitionend', this.$_sidebarResizeHandler)\r\n    },\r\n    destroyListener() {\r\n      window.removeEventListener('resize', this.$_resizeHandler)\r\n      this.$_resizeHandler = null\r\n\r\n      this.$_sidebarElm && this.$_sidebarElm.removeEventListener('transitionend', this.$_sidebarResizeHandler)\r\n    },\r\n    resize() {\r\n      const {chart} = this\r\n      chart && chart.resize()\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAAgC,IAAAC,QAAA,GAEjB;EACbC,IAAI,WAAAA,KAAA,EAAG;IACL,OAAO;MACLC,YAAY,EAAE,IAAI;MAClBC,eAAe,EAAE;IACnB,CAAC;EACH,CAAC;EACDC,OAAO,WAAAA,QAAA,EAAG;IACR,IAAI,CAACC,YAAY,CAAC,CAAC;EACrB,CAAC;EACDC,SAAS,WAAAA,UAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACH,eAAe,EAAE;MACzB;MACA,IAAI,CAACE,YAAY,CAAC,CAAC;IACrB;;IAEA;IACA,IAAI,CAACE,MAAM,CAAC,CAAC;EACf,CAAC;EACDC,aAAa,WAAAA,cAAA,EAAG;IACd,IAAI,CAACC,eAAe,CAAC,CAAC;EACxB,CAAC;EACDC,WAAW,WAAAA,YAAA,EAAG;IACZ,IAAI,CAACD,eAAe,CAAC,CAAC;EACxB,CAAC;EACDE,OAAO,EAAE;IACP;IACA;IACAC,sBAAsB,WAAAA,uBAACC,CAAC,EAAE;MACxB,IAAIA,CAAC,CAACC,YAAY,IAAI,OAAO,EAAE;QAC7B,IAAI,CAACX,eAAe,CAAC,CAAC;MACxB;IACF,CAAC;IACDE,YAAY,WAAAA,aAAA,EAAG;MAAA,IAAAU,KAAA;MACb,IAAI,CAACZ,eAAe,GAAG,IAAAa,eAAQ,EAAC,YAAM;QACpCD,KAAI,CAACR,MAAM,CAAC,CAAC;MACf,CAAC,EAAE,GAAG,CAAC;MACPU,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACf,eAAe,CAAC;MAEvD,IAAI,CAACD,YAAY,GAAGiB,QAAQ,CAACC,sBAAsB,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;MAC3E,IAAI,CAAClB,YAAY,IAAI,IAAI,CAACA,YAAY,CAACgB,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACN,sBAAsB,CAAC;IACvG,CAAC;IACDH,eAAe,WAAAA,gBAAA,EAAG;MAChBQ,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAClB,eAAe,CAAC;MAC1D,IAAI,CAACA,eAAe,GAAG,IAAI;MAE3B,IAAI,CAACD,YAAY,IAAI,IAAI,CAACA,YAAY,CAACmB,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACT,sBAAsB,CAAC;IAC1G,CAAC;IACDL,MAAM,WAAAA,OAAA,EAAG;MACP,IAAOe,KAAK,GAAI,IAAI,CAAbA,KAAK;MACZA,KAAK,IAAIA,KAAK,CAACf,MAAM,CAAC,CAAC;IACzB;EACF;AACF,CAAC;AAAAgB,OAAA,CAAAC,OAAA,GAAAxB,QAAA"}]}