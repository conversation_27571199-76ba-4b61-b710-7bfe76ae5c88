import request from '@/utils/request'

// 查询发票登记列表
export function listVatinvoice(query) {
  return request({
    url: '/system/vatinvoice/list',
    method: 'get',
    params: query
  })
}

// 查询发票登记详细
export function getVatinvoice(invoiceId) {
  return request({
    url: '/system/vatinvoice/' + invoiceId,
    method: 'get'
  })
}

// 新增发票登记
export function addVatinvoice(data) {
  return request({
    url: '/system/vatinvoice',
    method: 'post',
    data: data
  })
}

// 修改发票登记
export function updateVatinvoice(data) {
  return request({
    url: '/system/vatinvoice',
    method: 'put',
    data: data
  })
}

// 删除发票登记
export function delVatinvoice(invoiceId) {
  return request({
    url: '/system/vatinvoice/' + invoiceId,
    method: 'delete'
  })
}

// 状态修改
export function changeStatus(invoiceId, status) {
  const data = {
    invoiceId,
    status
  }
  return request({
    url: '/system/vatinvoice/changeStatus',
    method: 'put',
    data: data
  })
}

// 根据rctId查询发票数量
export function countVatinvoiceByRctId(rctId) {
  return request({
    url: '/system/vatinvoice/count/' + rctId,
    method: 'get'
  })
}

// 根据rctId和cooperatorId生成发票编码
export function generateInvoiceCode(rctId, cooperatorId) {
  return request({
    url: `/system/vatinvoice/generateCode/${rctId}/${cooperatorId}`,
    method: 'get'
  })
}

// 生成发票Excel文件
export function generateInvoiceExcel(invoiceIds) {
  return request({
    url: '/system/vatinvoice/generateExcel',
    method: 'put',
    data: invoiceIds,
    responseType: 'arraybuffer'
  })
}
