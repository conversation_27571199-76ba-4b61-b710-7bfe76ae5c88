import request from '@/utils/request'

// 查询操作单列表
export function listRct(query) {
  return request({
    url: '/system/rct/list',
    method: 'get',
    params: query
  })
}

export function listAggregatorRct(query) {
  return request({
    url: '/system/rct/aggregator',
    method: 'get',
    params: query
  })
}

export function listVerifyAggregatorList(query) {
  return request({
    url: '/system/rct/listVerifyAggregatorList',
    method: 'get',
    params: query
  })
}

export function op(query) {
  return request({
    url: '/system/rct/op',
    method: 'get',
    params: query
  })
}

export function listVerifyList(query) {
  return request({
    url: '/system/rct/listVerifyList',
    method: 'get',
    params: query
  })
}

// 查询操作单详细
export function getRct(rctId) {
  return request({
    url: '/system/rct/' + rctId,
    method: 'get'
  })
}

// 新增操作单
export function addRct(data) {
  return request({
    url: '/system/rct',
    method: 'post',
    data: data
  })
}

export function saveAsRct(data) {
  return request({
    url: '/system/rct/saveAs',
    method: 'post',
    data: data
  })
}

// 修改操作单
export function updateRct(data) {
  return request({
    url: '/system/rct',
    method: 'put',
    data: data
  })
}

// 删除操作单
export function delRct(rctId) {
  return request({
    url: '/system/rct/' + rctId,
    method: 'delete'
  })
}

// 状态修改
export function changeStatus(rctId, status) {
  const data = {
    rctId,
    status
  }
  return request({
    url: '/system/rct/changeStatus',
    method: 'put',
    data: data
  })
}

export function addClientMessage(data) {
  return request({
    url: '/system/rct/saveClientMessage',
    method: 'post',
    data: data
  })
}

export function addBasicLogistics(data) {
  return request({
    url: '/system/rct/saveBasicLogistics',
    method: 'post',
    data: data
  })
}

export function addPreCarriage(data) {
  return request({
    url: '/system/rct/savePreCarriage',
    method: 'post',
    data: data
  })
}

export function addExportDeclaration(data) {
  return request({
    url: '/system/rct/saveExportDeclaration',
    method: 'post',
    data: data
  })
}

export function addImportClearance(data) {
  return request({
    url: '/system/rct/saveImportClearance',
    method: 'post',
    data: data
  })
}

export function getRctMon() {
  return request({
    url: '/system/rct/mon',
    method: 'get'
  })
}

export function getRctCFMon() {
  return request({
    url: '/system/rct/CFmon',
    method: 'get'
  })
}

export function getRctRSWHMon() {
  return request({
    url: '/system/rct/RSWHMon',
    method: 'get'
  })
}

export function saveAllService(data) {
  return request({
    url: '/system/rct/saveAllService',
    method: 'post',
    data: data
  })
}

export function saveAsAllService(data) {
  return request({
    url: '/system/rct/saveAsAllService',
    method: 'post',
    data: data
  })
}

export function getRctNoList(companyId) {
  return request({
    url: '/system/rct/listRctNoByCompany/' + companyId,
    method: 'get'
  })
}

// 销账后更新未收未付
export function rctWriteoff(data) {
  return request({
    url: '/system/rct/writeoff',
    method: 'post',
    data: data
  })
}


