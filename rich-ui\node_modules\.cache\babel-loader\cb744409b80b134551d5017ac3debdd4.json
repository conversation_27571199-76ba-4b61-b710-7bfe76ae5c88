{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\generator\\js.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\generator\\js.js", "mtime": 1754876882559}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_index", "_config", "units", "KB", "MB", "GB", "confGlobal", "inheritAttrs", "file", "dialog", "makeUpJs", "conf", "type", "JSON", "parse", "stringify", "dataList", "ruleList", "optionsList", "propsList", "methodList", "mixinMethod", "uploadVarList", "fields", "for<PERSON>ach", "el", "buildAttributes", "script", "buildexport", "join", "buildData", "buildRules", "options", "length", "buildOptions", "dataType", "model", "concat", "vModel", "titleCase", "buildOptionMethod", "props", "buildProps", "action", "tag", "push", "buildBeforeUpload", "buildSubmitUpload", "children", "el2", "list", "minxins", "formBtns", "submitForm", "formRef", "resetForm", "onOpen", "onClose", "close", "handleConfirm", "methods", "Object", "keys", "key", "undefined", "defaultValue", "multiple", "rules", "trigger", "required", "isArray", "message", "placeholder", "label", "regList", "item", "pattern", "eval", "str", "valueKey", "value", "labelKey", "<PERSON><PERSON><PERSON>", "unitNum", "sizeUnit", "rightSizeCode", "acceptCode", "returnList", "fileSize", "accept", "methodName", "data", "selectOptions", "uploadVar", "exportDefault", "formModel", "formRules"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/utils/generator/js.js"], "sourcesContent": ["import {isArray} from 'util'\r\nimport {exportDefault, titleCase} from '@/utils/index'\r\nimport {trigger} from './config'\r\n\r\nconst units = {\r\n  KB: '1024',\r\n  MB: '1024 / 1024',\r\n  GB: '1024 / 1024 / 1024'\r\n}\r\nlet confGlobal\r\nconst inheritAttrs = {\r\n  file: '',\r\n  dialog: 'inheritAttrs: false,'\r\n}\r\n\r\n\r\nexport function makeUpJs(conf, type) {\r\n  confGlobal = conf = JSON.parse(JSON.stringify(conf))\r\n  const dataList = []\r\n  const ruleList = []\r\n  const optionsList = []\r\n  const propsList = []\r\n  const methodList = mixinMethod(type)\r\n  const uploadVarList = []\r\n\r\n  conf.fields.forEach(el => {\r\n    buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\r\n  })\r\n\r\n  const script = buildexport(\r\n    conf,\r\n    type,\r\n    dataList.join('\\n'),\r\n    ruleList.join('\\n'),\r\n    optionsList.join('\\n'),\r\n    uploadVarList.join('\\n'),\r\n    propsList.join('\\n'),\r\n    methodList.join('\\n')\r\n  )\r\n  confGlobal = null\r\n  return script\r\n}\r\n\r\nfunction buildAttributes(el, dataList, ruleList, optionsList, methodList, propsList, uploadVarList) {\r\n  buildData(el, dataList)\r\n  buildRules(el, ruleList)\r\n\r\n  if (el.options && el.options.length) {\r\n    buildOptions(el, optionsList)\r\n    if (el.dataType == 'dynamic') {\r\n      const model = `${el.vModel}Options`\r\n      const options = titleCase(model)\r\n      buildOptionMethod(`get${options}`, model, methodList)\r\n    }\r\n  }\r\n\r\n  if (el.props && el.props.props) {\r\n    buildProps(el, propsList)\r\n  }\r\n\r\n  if (el.action && el.tag == 'el-upload') {\r\n    uploadVarList.push(\r\n      `${el.vModel}Action: '${el.action}',\r\n      ${el.vModel}fileList: [],`\r\n    )\r\n    methodList.push(buildBeforeUpload(el))\r\n    if (!el['auto-upload']) {\r\n      methodList.push(buildSubmitUpload(el))\r\n    }\r\n  }\r\n\r\n  if (el.children) {\r\n    el.children.forEach(el2 => {\r\n      buildAttributes(el2, dataList, ruleList, optionsList, methodList, propsList, uploadVarList)\r\n    })\r\n  }\r\n}\r\n\r\nfunction mixinMethod(type) {\r\n  const list = [];\r\n  const\r\n    minxins = {\r\n      file: confGlobal.formBtns ? {\r\n        submitForm: `submitForm() {\r\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\r\n          if(!valid) return\r\n          // TODO 提交表单\r\n        })\r\n      },`,\r\n        resetForm: `resetForm() {\r\n        this.$refs['${confGlobal.formRef}'].resetFields()\r\n      },`\r\n      } : null,\r\n      dialog: {\r\n        onOpen: 'onOpen() {},',\r\n        onClose: `onClose() {\r\n        this.$refs['${confGlobal.formRef}'].resetFields()\r\n      },`,\r\n        close: `close() {\r\n        this.$emit('update:visible', false)\r\n      },`,\r\n        handleConfirm: `handleConfirm() {\r\n        this.$refs['${confGlobal.formRef}'].validate(valid => {\r\n          if(!valid) return\r\n          this.close()\r\n        })\r\n      },`\r\n      }\r\n    }\r\n\r\n  const methods = minxins[type]\r\n  if (methods) {\r\n    Object.keys(methods).forEach(key => {\r\n      list.push(methods[key])\r\n    })\r\n  }\r\n\r\n  return list\r\n}\r\n\r\nfunction buildData(conf, dataList) {\r\n  if (conf.vModel == undefined) return\r\n  let defaultValue\r\n  if (typeof (conf.defaultValue) == 'string' && !conf.multiple) {\r\n    defaultValue = `'${conf.defaultValue}'`\r\n  } else {\r\n    defaultValue = `${JSON.stringify(conf.defaultValue)}`\r\n  }\r\n  dataList.push(`${conf.vModel}: ${defaultValue},`)\r\n}\r\n\r\nfunction buildRules(conf, ruleList) {\r\n  if (conf.vModel == undefined) return\r\n  const rules = []\r\n  if (trigger[conf.tag]) {\r\n    if (conf.required) {\r\n      const type = isArray(conf.defaultValue) ? 'type: \\'array\\',' : ''\r\n      let message = isArray(conf.defaultValue) ? `请至少选择一个${conf.vModel}` : conf.placeholder\r\n      if (message == undefined) message = `${conf.label}不能为空`\r\n      rules.push(`{ required: true, ${type} message: '${message}', trigger: '${trigger[conf.tag]}' }`)\r\n    }\r\n    if (conf.regList && isArray(conf.regList)) {\r\n      conf.regList.forEach(item => {\r\n        if (item.pattern) {\r\n          rules.push(`{ pattern: ${eval(item.pattern)}, message: '${item.message}', trigger: '${trigger[conf.tag]}' }`)\r\n        }\r\n      })\r\n    }\r\n    ruleList.push(`${conf.vModel}: [${rules.join(',')}],`)\r\n  }\r\n}\r\n\r\nfunction buildOptions(conf, optionsList) {\r\n  if (conf.vModel == undefined) return\r\n  if (conf.dataType == 'dynamic') {\r\n    conf.options = []\r\n  }\r\n  const str = `${conf.vModel}Options: ${JSON.stringify(conf.options)},`\r\n  optionsList.push(str)\r\n}\r\n\r\nfunction buildProps(conf, propsList) {\r\n  if (conf.dataType == 'dynamic') {\r\n    conf.valueKey != 'value' && (conf.props.props.value = conf.valueKey)\r\n    conf.labelKey != 'label' && (conf.props.props.label = conf.labelKey)\r\n    conf.childrenKey != 'children' && (conf.props.props.children = conf.childrenKey)\r\n  }\r\n  const str = `${conf.vModel}Props: ${JSON.stringify(conf.props.props)},`\r\n  propsList.push(str)\r\n}\r\n\r\nfunction buildBeforeUpload(conf) {\r\n  const unitNum = units[conf.sizeUnit];\r\n  let rightSizeCode = '';\r\n  let acceptCode = '';\r\n  const\r\n    returnList = []\r\n  if (conf.fileSize) {\r\n    rightSizeCode = `let isRightSize = file.size / ${unitNum} < ${conf.fileSize}\r\n    if(!isRightSize){\r\n      this.$message.error('文件大小超过 ${conf.fileSize}${conf.sizeUnit}')\r\n    }`\r\n    returnList.push('isRightSize')\r\n  }\r\n  if (conf.accept) {\r\n    acceptCode = `let isAccept = new RegExp('${conf.accept}').test(file.type)\r\n    if(!isAccept){\r\n      this.$message.error('应该选择${conf.accept}类型的文件')\r\n    }`\r\n    returnList.push('isAccept')\r\n  }\r\n  const str = `${conf.vModel}BeforeUpload(file) {\r\n    ${rightSizeCode}\r\n    ${acceptCode}\r\n    return ${returnList.join('&&')}\r\n  },`\r\n  return returnList.length ? str : ''\r\n}\r\n\r\nfunction buildSubmitUpload(conf) {\r\n  const str = `submitUpload() {\r\n    this.$refs['${conf.vModel}'].submit()\r\n  },`\r\n  return str\r\n}\r\n\r\nfunction buildOptionMethod(methodName, model, methodList) {\r\n  const str = `${methodName}() {\r\n    // TODO 发起请求获取数据\r\n    this.${model}\r\n  },`\r\n  methodList.push(str)\r\n}\r\n\r\nfunction buildexport(conf, type, data, rules, selectOptions, uploadVar, props, methods) {\r\n  const str = `${exportDefault}{\r\n  ${inheritAttrs[type]}\r\n  components: {},\r\n  props: [],\r\n  data () {\r\n    return {\r\n      ${conf.formModel}: {\r\n        ${data}\r\n      },\r\n      ${conf.formRules}: {\r\n        ${rules}\r\n      },\r\n      ${uploadVar}\r\n      ${selectOptions}\r\n      ${props}\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created () {},\r\n  mounted () {},\r\n  methods: {\r\n    ${methods}\r\n  }\r\n}`\r\n  return str\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,OAAA,GAAAF,OAAA;AAEA,IAAMG,KAAK,GAAG;EACZC,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,aAAa;EACjBC,EAAE,EAAE;AACN,CAAC;AACD,IAAIC,UAAU;AACd,IAAMC,YAAY,GAAG;EACnBC,IAAI,EAAE,EAAE;EACRC,MAAM,EAAE;AACV,CAAC;AAGM,SAASC,QAAQA,CAACC,IAAI,EAAEC,IAAI,EAAE;EACnCN,UAAU,GAAGK,IAAI,GAAGE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC,CAAC;EACpD,IAAMK,QAAQ,GAAG,EAAE;EACnB,IAAMC,QAAQ,GAAG,EAAE;EACnB,IAAMC,WAAW,GAAG,EAAE;EACtB,IAAMC,SAAS,GAAG,EAAE;EACpB,IAAMC,UAAU,GAAGC,WAAW,CAACT,IAAI,CAAC;EACpC,IAAMU,aAAa,GAAG,EAAE;EAExBX,IAAI,CAACY,MAAM,CAACC,OAAO,CAAC,UAAAC,EAAE,EAAI;IACxBC,eAAe,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;EAC5F,CAAC,CAAC;EAEF,IAAMK,MAAM,GAAGC,WAAW,CACxBjB,IAAI,EACJC,IAAI,EACJI,QAAQ,CAACa,IAAI,CAAC,IAAI,CAAC,EACnBZ,QAAQ,CAACY,IAAI,CAAC,IAAI,CAAC,EACnBX,WAAW,CAACW,IAAI,CAAC,IAAI,CAAC,EACtBP,aAAa,CAACO,IAAI,CAAC,IAAI,CAAC,EACxBV,SAAS,CAACU,IAAI,CAAC,IAAI,CAAC,EACpBT,UAAU,CAACS,IAAI,CAAC,IAAI,CACtB,CAAC;EACDvB,UAAU,GAAG,IAAI;EACjB,OAAOqB,MAAM;AACf;AAEA,SAASD,eAAeA,CAACD,EAAE,EAAET,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,EAAE;EAClGQ,SAAS,CAACL,EAAE,EAAET,QAAQ,CAAC;EACvBe,UAAU,CAACN,EAAE,EAAER,QAAQ,CAAC;EAExB,IAAIQ,EAAE,CAACO,OAAO,IAAIP,EAAE,CAACO,OAAO,CAACC,MAAM,EAAE;IACnCC,YAAY,CAACT,EAAE,EAAEP,WAAW,CAAC;IAC7B,IAAIO,EAAE,CAACU,QAAQ,IAAI,SAAS,EAAE;MAC5B,IAAMC,KAAK,MAAAC,MAAA,CAAMZ,EAAE,CAACa,MAAM,YAAS;MACnC,IAAMN,OAAO,GAAG,IAAAO,gBAAS,EAACH,KAAK,CAAC;MAChCI,iBAAiB,OAAAH,MAAA,CAAOL,OAAO,GAAII,KAAK,EAAEhB,UAAU,CAAC;IACvD;EACF;EAEA,IAAIK,EAAE,CAACgB,KAAK,IAAIhB,EAAE,CAACgB,KAAK,CAACA,KAAK,EAAE;IAC9BC,UAAU,CAACjB,EAAE,EAAEN,SAAS,CAAC;EAC3B;EAEA,IAAIM,EAAE,CAACkB,MAAM,IAAIlB,EAAE,CAACmB,GAAG,IAAI,WAAW,EAAE;IACtCtB,aAAa,CAACuB,IAAI,IAAAR,MAAA,CACbZ,EAAE,CAACa,MAAM,eAAAD,MAAA,CAAYZ,EAAE,CAACkB,MAAM,gBAAAN,MAAA,CAC/BZ,EAAE,CAACa,MAAM,kBACb,CAAC;IACDlB,UAAU,CAACyB,IAAI,CAACC,iBAAiB,CAACrB,EAAE,CAAC,CAAC;IACtC,IAAI,CAACA,EAAE,CAAC,aAAa,CAAC,EAAE;MACtBL,UAAU,CAACyB,IAAI,CAACE,iBAAiB,CAACtB,EAAE,CAAC,CAAC;IACxC;EACF;EAEA,IAAIA,EAAE,CAACuB,QAAQ,EAAE;IACfvB,EAAE,CAACuB,QAAQ,CAACxB,OAAO,CAAC,UAAAyB,GAAG,EAAI;MACzBvB,eAAe,CAACuB,GAAG,EAAEjC,QAAQ,EAAEC,QAAQ,EAAEC,WAAW,EAAEE,UAAU,EAAED,SAAS,EAAEG,aAAa,CAAC;IAC7F,CAAC,CAAC;EACJ;AACF;AAEA,SAASD,WAAWA,CAACT,IAAI,EAAE;EACzB,IAAMsC,IAAI,GAAG,EAAE;EACf,IACEC,OAAO,GAAG;IACR3C,IAAI,EAAEF,UAAU,CAAC8C,QAAQ,GAAG;MAC1BC,UAAU,yCAAAhB,MAAA,CACI/B,UAAU,CAACgD,OAAO,0HAI/B;MACDC,SAAS,wCAAAlB,MAAA,CACK/B,UAAU,CAACgD,OAAO;IAElC,CAAC,GAAG,IAAI;IACR7C,MAAM,EAAE;MACN+C,MAAM,EAAE,cAAc;MACtBC,OAAO,sCAAApB,MAAA,CACO/B,UAAU,CAACgD,OAAO,+BAC/B;MACDI,KAAK,oEAEJ;MACDC,aAAa,4CAAAtB,MAAA,CACC/B,UAAU,CAACgD,OAAO;IAKlC;EACF,CAAC;EAEH,IAAMM,OAAO,GAAGT,OAAO,CAACvC,IAAI,CAAC;EAC7B,IAAIgD,OAAO,EAAE;IACXC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACpC,OAAO,CAAC,UAAAuC,GAAG,EAAI;MAClCb,IAAI,CAACL,IAAI,CAACe,OAAO,CAACG,GAAG,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ;EAEA,OAAOb,IAAI;AACb;AAEA,SAASpB,SAASA,CAACnB,IAAI,EAAEK,QAAQ,EAAE;EACjC,IAAIL,IAAI,CAAC2B,MAAM,IAAI0B,SAAS,EAAE;EAC9B,IAAIC,YAAY;EAChB,IAAI,OAAQtD,IAAI,CAACsD,YAAa,IAAI,QAAQ,IAAI,CAACtD,IAAI,CAACuD,QAAQ,EAAE;IAC5DD,YAAY,OAAA5B,MAAA,CAAO1B,IAAI,CAACsD,YAAY,MAAG;EACzC,CAAC,MAAM;IACLA,YAAY,MAAA5B,MAAA,CAAMxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACsD,YAAY,CAAC,CAAE;EACvD;EACAjD,QAAQ,CAAC6B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,QAAAD,MAAA,CAAK4B,YAAY,MAAG,CAAC;AACnD;AAEA,SAASlC,UAAUA,CAACpB,IAAI,EAAEM,QAAQ,EAAE;EAClC,IAAIN,IAAI,CAAC2B,MAAM,IAAI0B,SAAS,EAAE;EAC9B,IAAMG,KAAK,GAAG,EAAE;EAChB,IAAIC,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,EAAE;IACrB,IAAIjC,IAAI,CAAC0D,QAAQ,EAAE;MACjB,IAAMzD,IAAI,GAAG,IAAA0D,aAAO,EAAC3D,IAAI,CAACsD,YAAY,CAAC,GAAG,kBAAkB,GAAG,EAAE;MACjE,IAAIM,OAAO,GAAG,IAAAD,aAAO,EAAC3D,IAAI,CAACsD,YAAY,CAAC,gDAAA5B,MAAA,CAAa1B,IAAI,CAAC2B,MAAM,IAAK3B,IAAI,CAAC6D,WAAW;MACrF,IAAID,OAAO,IAAIP,SAAS,EAAEO,OAAO,MAAAlC,MAAA,CAAM1B,IAAI,CAAC8D,KAAK,6BAAM;MACvDN,KAAK,CAACtB,IAAI,sBAAAR,MAAA,CAAsBzB,IAAI,iBAAAyB,MAAA,CAAckC,OAAO,mBAAAlC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;IAClG;IACA,IAAIjC,IAAI,CAAC+D,OAAO,IAAI,IAAAJ,aAAO,EAAC3D,IAAI,CAAC+D,OAAO,CAAC,EAAE;MACzC/D,IAAI,CAAC+D,OAAO,CAAClD,OAAO,CAAC,UAAAmD,IAAI,EAAI;QAC3B,IAAIA,IAAI,CAACC,OAAO,EAAE;UAChBT,KAAK,CAACtB,IAAI,eAAAR,MAAA,CAAewC,IAAI,CAACF,IAAI,CAACC,OAAO,CAAC,kBAAAvC,MAAA,CAAesC,IAAI,CAACJ,OAAO,mBAAAlC,MAAA,CAAgB+B,eAAO,CAACzD,IAAI,CAACiC,GAAG,CAAC,QAAK,CAAC;QAC/G;MACF,CAAC,CAAC;IACJ;IACA3B,QAAQ,CAAC4B,IAAI,IAAAR,MAAA,CAAI1B,IAAI,CAAC2B,MAAM,SAAAD,MAAA,CAAM8B,KAAK,CAACtC,IAAI,CAAC,GAAG,CAAC,OAAI,CAAC;EACxD;AACF;AAEA,SAASK,YAAYA,CAACvB,IAAI,EAAEO,WAAW,EAAE;EACvC,IAAIP,IAAI,CAAC2B,MAAM,IAAI0B,SAAS,EAAE;EAC9B,IAAIrD,IAAI,CAACwB,QAAQ,IAAI,SAAS,EAAE;IAC9BxB,IAAI,CAACqB,OAAO,GAAG,EAAE;EACnB;EACA,IAAM8C,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,eAAAD,MAAA,CAAYxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAACqB,OAAO,CAAC,MAAG;EACrEd,WAAW,CAAC2B,IAAI,CAACiC,GAAG,CAAC;AACvB;AAEA,SAASpC,UAAUA,CAAC/B,IAAI,EAAEQ,SAAS,EAAE;EACnC,IAAIR,IAAI,CAACwB,QAAQ,IAAI,SAAS,EAAE;IAC9BxB,IAAI,CAACoE,QAAQ,IAAI,OAAO,KAAKpE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACuC,KAAK,GAAGrE,IAAI,CAACoE,QAAQ,CAAC;IACpEpE,IAAI,CAACsE,QAAQ,IAAI,OAAO,KAAKtE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACgC,KAAK,GAAG9D,IAAI,CAACsE,QAAQ,CAAC;IACpEtE,IAAI,CAACuE,WAAW,IAAI,UAAU,KAAKvE,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAACO,QAAQ,GAAGrC,IAAI,CAACuE,WAAW,CAAC;EAClF;EACA,IAAMJ,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,aAAAD,MAAA,CAAUxB,IAAI,CAACE,SAAS,CAACJ,IAAI,CAAC8B,KAAK,CAACA,KAAK,CAAC,MAAG;EACvEtB,SAAS,CAAC0B,IAAI,CAACiC,GAAG,CAAC;AACrB;AAEA,SAAShC,iBAAiBA,CAACnC,IAAI,EAAE;EAC/B,IAAMwE,OAAO,GAAGjF,KAAK,CAACS,IAAI,CAACyE,QAAQ,CAAC;EACpC,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,UAAU,GAAG,EAAE;EACnB,IACEC,UAAU,GAAG,EAAE;EACjB,IAAI5E,IAAI,CAAC6E,QAAQ,EAAE;IACjBH,aAAa,oCAAAhD,MAAA,CAAoC8C,OAAO,SAAA9C,MAAA,CAAM1B,IAAI,CAAC6E,QAAQ,+FAAAnD,MAAA,CAE3C1B,IAAI,CAAC6E,QAAQ,EAAAnD,MAAA,CAAG1B,IAAI,CAACyE,QAAQ,cAC3D;IACFG,UAAU,CAAC1C,IAAI,CAAC,aAAa,CAAC;EAChC;EACA,IAAIlC,IAAI,CAAC8E,MAAM,EAAE;IACfH,UAAU,iCAAAjD,MAAA,CAAiC1B,IAAI,CAAC8E,MAAM,iGAAApD,MAAA,CAEzB1B,IAAI,CAAC8E,MAAM,4CACtC;IACFF,UAAU,CAAC1C,IAAI,CAAC,UAAU,CAAC;EAC7B;EACA,IAAMiC,GAAG,MAAAzC,MAAA,CAAM1B,IAAI,CAAC2B,MAAM,gCAAAD,MAAA,CACtBgD,aAAa,YAAAhD,MAAA,CACbiD,UAAU,mBAAAjD,MAAA,CACHkD,UAAU,CAAC1D,IAAI,CAAC,IAAI,CAAC,WAC7B;EACH,OAAO0D,UAAU,CAACtD,MAAM,GAAG6C,GAAG,GAAG,EAAE;AACrC;AAEA,SAAS/B,iBAAiBA,CAACpC,IAAI,EAAE;EAC/B,IAAMmE,GAAG,wCAAAzC,MAAA,CACO1B,IAAI,CAAC2B,MAAM,sBACxB;EACH,OAAOwC,GAAG;AACZ;AAEA,SAAStC,iBAAiBA,CAACkD,UAAU,EAAEtD,KAAK,EAAEhB,UAAU,EAAE;EACxD,IAAM0D,GAAG,MAAAzC,MAAA,CAAMqD,UAAU,mFAAArD,MAAA,CAEhBD,KAAK,WACX;EACHhB,UAAU,CAACyB,IAAI,CAACiC,GAAG,CAAC;AACtB;AAEA,SAASlD,WAAWA,CAACjB,IAAI,EAAEC,IAAI,EAAE+E,IAAI,EAAExB,KAAK,EAAEyB,aAAa,EAAEC,SAAS,EAAEpD,KAAK,EAAEmB,OAAO,EAAE;EACtF,IAAMkB,GAAG,MAAAzC,MAAA,CAAMyD,oBAAa,WAAAzD,MAAA,CAC1B9B,YAAY,CAACK,IAAI,CAAC,0EAAAyB,MAAA,CAKd1B,IAAI,CAACoF,SAAS,mBAAA1D,MAAA,CACZsD,IAAI,wBAAAtD,MAAA,CAEN1B,IAAI,CAACqF,SAAS,mBAAA3D,MAAA,CACZ8B,KAAK,wBAAA9B,MAAA,CAEPwD,SAAS,cAAAxD,MAAA,CACTuD,aAAa,cAAAvD,MAAA,CACbI,KAAK,0GAAAJ,MAAA,CAQPuB,OAAO,aAEX;EACA,OAAOkB,GAAG;AACZ"}]}