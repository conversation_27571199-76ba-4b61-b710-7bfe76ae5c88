{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\operationalprocess\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\operationalprocess\\index.vue", "mtime": 1754876882592}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkubWFwLmpzIik7CnZhciBfb3BlcmF0aW9uYWxwcm9jZXNzID0gcmVxdWlyZSgiQC9hcGkvc3lzdGVtL29wZXJhdGlvbmFscHJvY2VzcyIpOwp2YXIgX3Byb2Nlc3MgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vcHJvY2VzcyIpOwp2YXIgX3Byb2Nlc3NzdGF0dXMgPSByZXF1aXJlKCJAL2FwaS9zeXN0ZW0vcHJvY2Vzc3N0YXR1cyIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogIk9wZXJhdGlvbmFscHJvY2VzcyIsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHNob3dMZWZ0OiAzLAogICAgICBzaG93UmlnaHQ6IDIxLAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIC8vIOmdnuWNleS4quemgeeUqAogICAgICBzaW5nbGU6IHRydWUsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IHRydWUsCiAgICAgIC8vIOaAu+adoeaVsAogICAgICB0b3RhbDogMCwKICAgICAgLy8g5pON5L2c6L+b5bqm6KGo5qC85pWw5o2uCiAgICAgIG9wZXJhdGlvbmFscHJvY2Vzc0xpc3Q6IFtdLAogICAgICAvLyDlvLnlh7rlsYLmoIfpopgKICAgICAgdGl0bGU6ICIiLAogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYIKICAgICAgb3BlbjogZmFsc2UsCiAgICAgIC8vIOafpeivouWPguaVsAogICAgICBxdWVyeVBhcmFtczogewogICAgICAgIHBhZ2VOdW06IDEsCiAgICAgICAgcGFnZVNpemU6IDIwLAogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsCiAgICAgICAgcmN0SWQ6IG51bGwsCiAgICAgICAgcHJvY2Vzc0lkOiBudWxsLAogICAgICAgIHByb2Nlc3NTdGF0dXNJZDogbnVsbAogICAgICB9LAogICAgICAvLyDooajljZXlj4LmlbAKICAgICAgZm9ybToge30sCiAgICAgIHByb2Nlc3NMaXN0OiBbXSwKICAgICAgcHJvY2Vzc1N0YXR1c0xpc3Q6IFtdLAogICAgICAvLyDooajljZXmoKHpqowKICAgICAgcnVsZXM6IHt9CiAgICB9OwogIH0sCiAgd2F0Y2g6IHsKICAgIHNob3dTZWFyY2g6IGZ1bmN0aW9uIHNob3dTZWFyY2gobikgewogICAgICBpZiAobiA9PT0gdHJ1ZSkgewogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjE7CiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDM7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5zaG93UmlnaHQgPSAyNDsKICAgICAgICB0aGlzLnNob3dMZWZ0ID0gMDsKICAgICAgfQogICAgfQogIH0sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLmdldExpc3QoKTsKICAgICgwLCBfcHJvY2Vzcy5saXN0UHJvY2VzcykoewogICAgICBwYWdlTnVtOiAxLAogICAgICBwYWdlU2l6ZTogMjAwCiAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5wcm9jZXNzTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICB9KTsKICAgICgwLCBfcHJvY2Vzc3N0YXR1cy5saXN0UHJvY2Vzc3N0YXR1cykoewogICAgICBwYWdlTnVtOiAxLAogICAgICBwYWdlU2l6ZTogMTAwCiAgICB9KS50aGVuKGZ1bmN0aW9uIChyZXNwb25zZSkgewogICAgICBfdGhpcy5wcm9jZXNzU3RhdHVzTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICB9KTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8qKiDmn6Xor6Lmk43kvZzov5vluqbliJfooaggKi9nZXRMaXN0OiBmdW5jdGlvbiBnZXRMaXN0KCkgewogICAgICB2YXIgX3RoaXMyID0gdGhpczsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsKICAgICAgKDAsIF9vcGVyYXRpb25hbHByb2Nlc3MubGlzdE9wZXJhdGlvbmFscHJvY2VzcykodGhpcy5xdWVyeVBhcmFtcykudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczIub3BlcmF0aW9uYWxwcm9jZXNzTGlzdCA9IHJlc3BvbnNlLnJvd3M7CiAgICAgICAgX3RoaXMyLnRvdGFsID0gcmVzcG9uc2UudG90YWw7CiAgICAgICAgX3RoaXMyLmxvYWRpbmcgPSBmYWxzZTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Y+W5raI5oyJ6ZKuCiAgICBjYW5jZWw6IGZ1bmN0aW9uIGNhbmNlbCgpIHsKICAgICAgdGhpcy5vcGVuID0gZmFsc2U7CiAgICAgIHRoaXMucmVzZXQoKTsKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0OiBmdW5jdGlvbiByZXNldCgpIHsKICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIG9wZXJhdGlvbmFsUHJvY2Vzc0lkOiBudWxsLAogICAgICAgIG9wZXJhdGlvbk5vOiBudWxsLAogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsCiAgICAgICAgcmN0SWQ6IG51bGwsCiAgICAgICAgcHJvY2Vzc0lkOiBudWxsLAogICAgICAgIHByb2Nlc3NTdGF0dXNJZDogbnVsbCwKICAgICAgICBoYXBwZW5UaW1lOiBudWxsLAogICAgICAgIHJlbWFyazogbnVsbCwKICAgICAgICBjcmVhdGVCeTogbnVsbCwKICAgICAgICBjcmVhdGVUaW1lOiBudWxsLAogICAgICAgIHVwZGF0ZUJ5OiBudWxsLAogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsCiAgICAgICAgZGVsZXRlQnk6IG51bGwsCiAgICAgICAgZGVsZXRlVGltZTogbnVsbCwKICAgICAgICBkZWxldGVTdGF0dXM6ICIwIgogICAgICB9OwogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOwogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi9oYW5kbGVRdWVyeTogZnVuY3Rpb24gaGFuZGxlUXVlcnkoKSB7CiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7CiAgICAgIHRoaXMuZ2V0TGlzdCgpOwogICAgfSwKICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi9yZXNldFF1ZXJ5OiBmdW5jdGlvbiByZXNldFF1ZXJ5KCkgewogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7CiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKTsKICAgIH0sCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHZhciB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsKICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJ+WQl++8nycsICfmj5DnpLonLCB7CiAgICAgICAgY3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gKDAsIF9vcGVyYXRpb25hbHByb2Nlc3MuY2hhbmdlU3RhdHVzKShyb3cub3BlcmF0aW9uYWxQcm9jZXNzSWQsIHJvdy5zdGF0dXMpOwogICAgICB9KS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpczMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCI7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrgogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7CiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIHJldHVybiBpdGVtLm9wZXJhdGlvbmFsUHJvY2Vzc0lkOwogICAgICB9KTsKICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxOwogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGg7CiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqL2hhbmRsZUFkZDogZnVuY3Rpb24gaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHRoaXMub3BlbiA9IHRydWU7CiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5pON5L2c6L+b5bqmIjsKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovaGFuZGxlVXBkYXRlOiBmdW5jdGlvbiBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICB0aGlzLnJlc2V0KCk7CiAgICAgIHZhciBvcGVyYXRpb25hbFByb2Nlc3NJZCA9IHJvdy5vcGVyYXRpb25hbFByb2Nlc3NJZCB8fCB0aGlzLmlkczsKICAgICAgKDAsIF9vcGVyYXRpb25hbHByb2Nlc3MuZ2V0T3BlcmF0aW9uYWxwcm9jZXNzKShvcGVyYXRpb25hbFByb2Nlc3NJZCkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICBfdGhpczQuZm9ybSA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgX3RoaXM0Lm9wZW4gPSB0cnVlOwogICAgICAgIF90aGlzNC50aXRsZSA9ICLkv67mlLnmk43kvZzov5vluqYiOwogICAgICB9KTsKICAgIH0sCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovc3VibWl0Rm9ybTogZnVuY3Rpb24gc3VibWl0Rm9ybSgpIHsKICAgICAgdmFyIF90aGlzNSA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZShmdW5jdGlvbiAodmFsaWQpIHsKICAgICAgICBpZiAodmFsaWQpIHsKICAgICAgICAgIGlmIChfdGhpczUuZm9ybS5vcGVyYXRpb25hbFByb2Nlc3NJZCAhPSBudWxsKSB7CiAgICAgICAgICAgICgwLCBfb3BlcmF0aW9uYWxwcm9jZXNzLnVwZGF0ZU9wZXJhdGlvbmFscHJvY2VzcykoX3RoaXM1LmZvcm0pLnRoZW4oZnVuY3Rpb24gKHJlc3BvbnNlKSB7CiAgICAgICAgICAgICAgX3RoaXM1LiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsKICAgICAgICAgICAgICBfdGhpczUub3BlbiA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzNS5nZXRMaXN0KCk7CiAgICAgICAgICAgIH0pOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgKDAsIF9vcGVyYXRpb25hbHByb2Nlc3MuYWRkT3BlcmF0aW9uYWxwcm9jZXNzKShfdGhpczUuZm9ybSkudGhlbihmdW5jdGlvbiAocmVzcG9uc2UpIHsKICAgICAgICAgICAgICBfdGhpczUuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOwogICAgICAgICAgICAgIF90aGlzNS5vcGVuID0gZmFsc2U7CiAgICAgICAgICAgICAgX3RoaXM1LmdldExpc3QoKTsKICAgICAgICAgICAgfSk7CiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB2YXIgb3BlcmF0aW9uYWxQcm9jZXNzSWRzID0gcm93Lm9wZXJhdGlvbmFsUHJvY2Vzc0lkIHx8IHRoaXMuaWRzOwogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmk43kvZzov5vluqbnvJblj7fkuLoiJyArIG9wZXJhdGlvbmFsUHJvY2Vzc0lkcyArICci55qE5pWw5o2u6aG577yfJywgJ+aPkOekuicsIHsKICAgICAgICBjdXN0b21DbGFzczogJ21vZGFsLWNvbmZpcm0nCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiAoMCwgX29wZXJhdGlvbmFscHJvY2Vzcy5kZWxPcGVyYXRpb25hbHByb2Nlc3MpKG9wZXJhdGlvbmFsUHJvY2Vzc0lkcyk7CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzNi5nZXRMaXN0KCk7CiAgICAgICAgX3RoaXM2LiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkge30pOwogICAgfSwKICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi9oYW5kbGVFeHBvcnQ6IGZ1bmN0aW9uIGhhbmRsZUV4cG9ydCgpIHsKICAgICAgdGhpcy5kb3dubG9hZCgnc3lzdGVtL29wZXJhdGlvbmFscHJvY2Vzcy9leHBvcnQnLCAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIHRoaXMucXVlcnlQYXJhbXMpLCAib3BlcmF0aW9uYWxwcm9jZXNzXyIuY29uY2F0KG5ldyBEYXRlKCkuZ2V0VGltZSgpLCAiLnhsc3giKSk7CiAgICB9LAogICAgZ2V0U2VydmljZVR5cGVJZDogZnVuY3Rpb24gZ2V0U2VydmljZVR5cGVJZCh2YWwpIHsKICAgICAgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWQgPSB2YWw7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["_operationalprocess", "require", "_process", "_processstatus", "name", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "operationalprocessList", "title", "open", "queryParams", "pageNum", "pageSize", "serviceTypeId", "rctId", "processId", "processStatusId", "form", "processList", "processStatusList", "rules", "watch", "n", "created", "_this", "getList", "listProcess", "then", "response", "rows", "listProcessstatus", "methods", "_this2", "listOperationalprocess", "cancel", "reset", "operationalProcessId", "operationNo", "happenTime", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "row", "_this3", "text", "status", "$confirm", "customClass", "changeStatus", "$modal", "msgSuccess", "catch", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "_this4", "getOperationalprocess", "submitForm", "_this5", "$refs", "validate", "valid", "updateOperationalprocess", "addOperationalprocess", "handleDelete", "_this6", "operationalProcessIds", "delOperationalprocess", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "getServiceTypeId", "val", "exports", "_default"], "sources": ["src/views/system/operationalprocess/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\"\r\n                 label-width=\"68px\">\r\n          <el-form-item label=\"服务类型\" prop=\"serviceTypeId\">\r\n            <el-input\r\n              v-model=\"queryParams.serviceTypeId\"\r\n              placeholder=\"服务类型\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"服务ID\" prop=\"rctId\">\r\n            <el-input\r\n              v-model=\"queryParams.rctId\"\r\n              placeholder=\"服务ID\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度名称\" prop=\"processId\">\r\n            <el-input\r\n              v-model=\"queryParams.processId\"\r\n              placeholder=\"进度名称\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度状态\" prop=\"processStatusId\">\r\n            <el-input\r\n              v-model=\"queryParams.processStatusId\"\r\n              placeholder=\"进度状态\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:operationalprocess:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['system:operationalprocess:edit']\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:operationalprocess:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:operationalprocess:export']\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"operationalprocessList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"center\" label=\"操作单号\" prop=\"operationNo\" show-tooltip-when-overflow width=\"120\"/>\r\n          <el-table-column label=\"服务类型\" align=\"center\" prop=\"serviceType\" width=\"68\"/>\r\n          <el-table-column label=\"服务\" align=\"center\" prop=\"rctId\" width=\"68\"/>\r\n          <el-table-column label=\"进度名称\" align=\"center\" prop=\"process\" width=\"68\"/>\r\n          <el-table-column label=\"进度状态\" align=\"center\" prop=\"processStatus\" width=\"68\"/>\r\n          <el-table-column label=\"发生时间\" align=\"center\" prop=\"happenTime\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注\" align=\"center\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"录入人\" prop=\"updateBy\" show-tooltip-when-overflow width=\"78\"/>\r\n          <el-table-column label=\"录入时间\" align=\"center\" prop=\"updateTime\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:operationalprocess:edit']\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:operationalprocess:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改操作进度对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" width=\"500px\"\r\n      append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item label=\"操作单号\" prop=\"operationNo\">\r\n          <el-input v-model=\"form.operationNo\" placeholder=\"操作单号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务类型\" prop=\"serviceTypeId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.serviceTypeId\"\r\n                       :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                       @return=\"getServiceTypeId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务\" prop=\"rctId\">\r\n          <el-input v-model=\"form.rctId\" placeholder=\"服务ID\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"进度名称\" prop=\"processId\">\r\n          <el-select v-model=\"form.processId\" placeholder=\"进度名称\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in processList\"\r\n              :key=\"dict.processId\"\r\n              :label=\"dict.serviceType+'/'+dict.processLocalName\"\r\n              :value=\"dict.processId\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"进度状态\" prop=\"processStatusId\">\r\n          <el-select v-model=\"form.processStatusId\" placeholder=\"进度状态\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in processStatusList\"\r\n              :key=\"dict.processStatusId\"\r\n              :label=\"dict.processStatusLocalName\"\r\n              :value=\"dict.processStatusId\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"发生时间\" prop=\"happenTime\">\r\n          <el-date-picker clearable\r\n                          v-model=\"form.happenTime\"\r\n                          type=\"date\"\r\n                          value-format=\"yyyy-MM-dd\"\r\n                          placeholder=\"发生时间\"\r\n                          style=\"width: 100%\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOperationalprocess,\r\n  changeStatus,\r\n  delOperationalprocess,\r\n  getOperationalprocess,\r\n  listOperationalprocess,\r\n  updateOperationalprocess\r\n} from \"@/api/system/operationalprocess\";\r\nimport {listProcess} from \"@/api/system/process\";\r\nimport {listProcessstatus} from \"@/api/system/processstatus\";\r\n\r\nexport default {\r\n  name: \"Operationalprocess\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 操作进度表格数据\r\n      operationalprocessList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        serviceTypeId: null,\r\n        rctId: null,\r\n        processId: null,\r\n        processStatusId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      processList: [],\r\n      processStatusList: [],\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    listProcess({pageNum: 1, pageSize: 200}).then(response => {\r\n      this.processList = response.rows;\r\n    });\r\n    listProcessstatus({pageNum: 1, pageSize: 100}).then(response => {\r\n      this.processStatusList = response.rows;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询操作进度列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listOperationalprocess(this.queryParams).then(response => {\r\n        this.operationalprocessList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        operationalProcessId: null,\r\n        operationNo: null,\r\n        serviceTypeId: null,\r\n        rctId: null,\r\n        processId: null,\r\n        processStatusId: null,\r\n        happenTime: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.operationalProcessId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.operationalProcessId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加操作进度\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const operationalProcessId = row.operationalProcessId || this.ids\r\n      getOperationalprocess(operationalProcessId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改操作进度\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.operationalProcessId != null) {\r\n            updateOperationalprocess(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addOperationalprocess(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const operationalProcessIds = row.operationalProcessId || this.ids;\r\n      this.$confirm('是否确认删除操作进度编号为\"' + operationalProcessIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delOperationalprocess(operationalProcessIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/operationalprocess/export', {\r\n        ...this.queryParams\r\n      }, `operationalprocess_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getServiceTypeId(val) {\r\n      this.form.serviceTypeId = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;AA6MA,IAAAA,mBAAA,GAAAC,OAAA;AAQA,IAAAC,QAAA,GAAAD,OAAA;AACA,IAAAE,cAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,sBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,aAAA;QACAC,KAAA;QACAC,SAAA;QACAC,eAAA;MACA;MACA;MACAC,IAAA;MACAC,WAAA;MACAC,iBAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAhB,UAAA,WAAAA,WAAAiB,CAAA;MACA,IAAAA,CAAA;QACA,KAAAtB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAwB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,IAAAC,oBAAA;MAAAf,OAAA;MAAAC,QAAA;IAAA,GAAAe,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAN,WAAA,GAAAU,QAAA,CAAAC,IAAA;IACA;IACA,IAAAC,gCAAA;MAAAnB,OAAA;MAAAC,QAAA;IAAA,GAAAe,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAL,iBAAA,GAAAS,QAAA,CAAAC,IAAA;IACA;EACA;EACAE,OAAA;IACA,eACAN,OAAA,WAAAA,QAAA;MAAA,IAAAO,MAAA;MACA,KAAA/B,OAAA;MACA,IAAAgC,0CAAA,OAAAvB,WAAA,EAAAiB,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAAzB,sBAAA,GAAAqB,QAAA,CAAAC,IAAA;QACAG,MAAA,CAAA1B,KAAA,GAAAsB,QAAA,CAAAtB,KAAA;QACA0B,MAAA,CAAA/B,OAAA;MACA;IACA;IACA;IACAiC,MAAA,WAAAA,OAAA;MACA,KAAAzB,IAAA;MACA,KAAA0B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAlB,IAAA;QACAmB,oBAAA;QACAC,WAAA;QACAxB,aAAA;QACAC,KAAA;QACAC,SAAA;QACAC,eAAA;QACAsB,UAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAtC,WAAA,CAAAC,OAAA;MACA,KAAAc,OAAA;IACA;IACA,aACAwB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,MAAA;MACA,KAAAC,QAAA,UAAAF,IAAA;QAAAG,WAAA;MAAA,GAAA7B,IAAA;QACA,WAAA8B,gCAAA,EAAAN,GAAA,CAAAf,oBAAA,EAAAe,GAAA,CAAAG,MAAA;MACA,GAAA3B,IAAA;QACAyB,MAAA,CAAAM,MAAA,CAAAC,UAAA,CAAAN,IAAA;MACA,GAAAO,KAAA;QACAT,GAAA,CAAAG,MAAA,GAAAH,GAAA,CAAAG,MAAA;MACA;IACA;IACA;IACAO,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA5D,GAAA,GAAA4D,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5B,oBAAA;MAAA;MACA,KAAAjC,MAAA,GAAA2D,SAAA,CAAAG,MAAA;MACA,KAAA7D,QAAA,IAAA0D,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAA/B,KAAA;MACA,KAAA1B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA2D,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAjC,KAAA;MACA,IAAAC,oBAAA,GAAAe,GAAA,CAAAf,oBAAA,SAAAlC,GAAA;MACA,IAAAmE,yCAAA,EAAAjC,oBAAA,EAAAT,IAAA,WAAAC,QAAA;QACAwC,MAAA,CAAAnD,IAAA,GAAAW,QAAA,CAAA9B,IAAA;QACAsE,MAAA,CAAA3D,IAAA;QACA2D,MAAA,CAAA5D,KAAA;MACA;IACA;IACA,WACA8D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAtD,IAAA,CAAAmB,oBAAA;YACA,IAAAuC,4CAAA,EAAAJ,MAAA,CAAAtD,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACA2C,MAAA,CAAAb,MAAA,CAAAC,UAAA;cACAY,MAAA,CAAA9D,IAAA;cACA8D,MAAA,CAAA9C,OAAA;YACA;UACA;YACA,IAAAmD,yCAAA,EAAAL,MAAA,CAAAtD,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACA2C,MAAA,CAAAb,MAAA,CAAAC,UAAA;cACAY,MAAA,CAAA9D,IAAA;cACA8D,MAAA,CAAA9C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAA1B,GAAA;MAAA,IAAA2B,MAAA;MACA,IAAAC,qBAAA,GAAA5B,GAAA,CAAAf,oBAAA,SAAAlC,GAAA;MACA,KAAAqD,QAAA,oBAAAwB,qBAAA;QAAAvB,WAAA;MAAA,GAAA7B,IAAA;QACA,WAAAqD,yCAAA,EAAAD,qBAAA;MACA,GAAApD,IAAA;QACAmD,MAAA,CAAArD,OAAA;QACAqD,MAAA,CAAApB,MAAA,CAAAC,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACAqB,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,yCAAAC,cAAA,CAAAC,OAAA,MACA,KAAA1E,WAAA,yBAAA2E,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAxE,IAAA,CAAAJ,aAAA,GAAA4E,GAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAN,OAAA,GAAAO,QAAA"}]}