{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\menu.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\menu.js", "mtime": 1699839595525}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listMenu", "query", "request", "url", "method", "params", "getMenu", "menuId", "treeselect", "roleMenuTreeselect", "roleId", "addMenu", "data", "updateMenu", "delMenu", "listLimitMenu", "listBasicMenu", "listMenuByRole", "deptId", "positionId"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/menu.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询菜单列表\r\nexport function listMenu(query) {\r\n  return request({\r\n    url: '/system/menu/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询菜单详细\r\nexport function getMenu(menuId) {\r\n  return request({\r\n    url: '/system/menu/' + menuId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 查询菜单下拉树结构\r\nexport function treeselect() {\r\n  return request({\r\n    url: '/system/menu/treeselect',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据角色ID查询菜单下拉树结构\r\nexport function roleMenuTreeselect(roleId) {\r\n  return request({\r\n    url: '/system/menu/roleMenuTreeselect/' + roleId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增菜单\r\nexport function addMenu(data) {\r\n  return request({\r\n    url: '/system/menu',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改菜单\r\nexport function updateMenu(data) {\r\n  return request({\r\n    url: '/system/menu',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除菜单\r\nexport function delMenu(menuId) {\r\n  return request({\r\n    url: '/system/menu/' + menuId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\nexport function listLimitMenu() {\r\n  return request({\r\n    url: '/system/menu/limitRoleMenu',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function listBasicMenu() {\r\n  return request({\r\n    url: '/system/menu/listBasicMenu',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function listMenuByRole(deptId, positionId) {\r\n  return request({\r\n    url: '/system/menu/listMenuByRole',\r\n    method: 'get',\r\n    params: {deptId: deptId, positionId: positionId}\r\n  })\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAAA,EAAG;EAC3B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACC,MAAM,EAAE;EACzC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC,GAAGO,MAAM;IAChDN,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACP,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASW,aAAaA,CAAA,EAAG;EAC9B,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASY,aAAaA,CAAA,EAAG;EAC9B,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASa,cAAcA,CAACC,MAAM,EAAEC,UAAU,EAAE;EACjD,OAAO,IAAAjB,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MAACa,MAAM,EAAEA,MAAM;MAAEC,UAAU,EAAEA;IAAU;EACjD,CAAC,CAAC;AACJ"}]}