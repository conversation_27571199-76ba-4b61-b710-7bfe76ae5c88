{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\tool\\gen.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\tool\\gen.js", "mtime": 1678688095235}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listTable", "query", "request", "url", "method", "params", "listDbTable", "getGenTable", "tableId", "updateGenTable", "data", "importTable", "previewTable", "delTable", "genCode", "tableName", "synchDb"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/tool/gen.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询生成表数据\r\nexport function listTable(query) {\r\n  return request({\r\n    url: '/tool/gen/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询db数据库列表\r\nexport function listDbTable(query) {\r\n  return request({\r\n    url: '/tool/gen/db/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询表详细信息\r\nexport function getGenTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 修改代码生成信息\r\nexport function updateGenTable(data) {\r\n  return request({\r\n    url: '/tool/gen',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 导入表\r\nexport function importTable(data) {\r\n  return request({\r\n    url: '/tool/gen/importTable',\r\n    method: 'post',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 预览生成代码\r\nexport function previewTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/preview/' + tableId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 删除表数据\r\nexport function delTable(tableId) {\r\n  return request({\r\n    url: '/tool/gen/' + tableId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 生成代码（自定义路径）\r\nexport function genCode(tableName) {\r\n  return request({\r\n    url: '/tool/gen/genCode/' + tableName,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 同步数据库\r\nexport function synchDb(tableName) {\r\n  return request({\r\n    url: '/tool/gen/synchDb/' + tableName,\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,SAASA,CAACC,KAAK,EAAE;EAC/B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB;IACrBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACL,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,WAAW;IAChBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,WAAWA,CAACD,IAAI,EAAE;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACJ,OAAO,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGK,OAAO;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,QAAQA,CAACL,OAAO,EAAE;EAChC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,YAAY,GAAGK,OAAO;IAC3BJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,OAAOA,CAACC,SAAS,EAAE;EACjC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGY,SAAS;IACrCX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,OAAOA,CAACD,SAAS,EAAE;EACjC,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGY,SAAS;IACrCX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}