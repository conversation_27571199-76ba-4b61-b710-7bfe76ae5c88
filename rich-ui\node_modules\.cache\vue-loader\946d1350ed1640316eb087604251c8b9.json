{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\RailwayComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\RailwayComponent.vue", "mtime": 1754881964235}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQXVkaXQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvYXVkaXQudnVlIg0KaW1wb3J0IExvZ2lzdGljc1Byb2dyZXNzIGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2xvZ2lzdGljc1Byb2dyZXNzLnZ1ZSINCmltcG9ydCBDaGFyZ2VMaXN0IGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2NoYXJnZUxpc3QudnVlIg0KaW1wb3J0IERlYml0Tm90ZUxpc3QgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvZGViaXROb2RlTGlzdC52dWUiDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlJhaWx3YXlDb21wb25lbnQiLA0KICBjb21wb25lbnRzOiB7DQogICAgRGViaXROb3RlTGlzdCwNCiAgICBBdWRpdCwNCiAgICBMb2dpc3RpY3NQcm9ncmVzcywNCiAgICBDaGFyZ2VMaXN0DQogIH0sDQogIHByb3BzOiB7DQogICAgc2VydmljZUl0ZW06IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9LA0KICAgIC8vIOmTgei3r+acjeWKoeaVsOaNrumbhuWQiA0KICAgIHJhaWx3YXlTZXJ2aWNlczogew0KICAgICAgdHlwZTogW0FycmF5LCBTZXRdLA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIC8vIOihqOWNleaVsOaNrg0KICAgIGZvcm06IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9LA0KICAgIC8vIOaYvuekuuaOp+WItg0KICAgIGJyYW5jaEluZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICBsb2dpc3RpY3NJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgY2hhcmdlSW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGF1ZGl0SW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICAvLyDnirbmgIHmjqfliLYNCiAgICBkaXNhYmxlZDogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBib29raW5nOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIHBzYVZlcmlmeTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICAvLyDmlbDmja7liJfooagNCiAgICBzdXBwbGllckxpc3Q6IHsNCiAgICAgIHR5cGU6IEFycmF5LA0KICAgICAgZGVmYXVsdDogKCkgPT4gW10NCiAgICB9LA0KICAgIGNvbXBhbnlMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICAvLyDmlrDlop7lsZ7mgKfvvIzkuI3lho3kvp3otZYkcGFyZW50DQogICAgZm9sZFN0YXRlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIHNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgc2VydmljZU9iamVjdDogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgZm9ybURpc2FibGU6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgcGF5YWJsZTogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogbnVsbA0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDliKTmlq3mmK/lkKbnpoHnlKjnirbmgIENCiAgICBpc0Rpc2FibGVkKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZGlzYWJsZWQgfHwgdGhpcy5wc2FWZXJpZnkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVBZGREZWJpdE5vdGUoc2VydmljZU9iamVjdCkgew0KICAgICAgbGV0IHJvdyA9IHt9DQogICAgICByb3cuc3FkUmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIHJvdy5yY3RJZCA9IHRoaXMuZm9ybS5yY3RJZA0KICAgICAgcm93LmlzUmVjaWV2aW5nT3JQYXlpbmcgPSAxDQogICAgICByb3cucnNDaGFyZ2VMaXN0ID0gW10NCiAgICAgIHRoaXMuJGVtaXQoJ2FkZERlYml0Tm90ZScsIHJvdywgc2VydmljZU9iamVjdCkNCiAgICB9LA0KICAgIC8vIOiOt+WPluS+m+W6lOWVhumCrueusQ0KICAgIGdldFN1cHBsaWVyRW1haWwoc2VydmljZVR5cGVJZCkgew0KICAgICAgY29uc3Qgc2VydmljZUluc3RhbmNlID0gdGhpcy5nZXRTZXJ2aWNlSW5zdGFuY2Uoc2VydmljZVR5cGVJZCkNCiAgICAgIGlmICghc2VydmljZUluc3RhbmNlIHx8ICFzZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZCkgcmV0dXJuICcnDQoNCiAgICAgIGNvbnN0IHN1cHBsaWVyID0gdGhpcy5zdXBwbGllckxpc3QuZmluZCh2ID0+IHYuY29tcGFueUlkID09PSBzZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZCkNCiAgICAgIHJldHVybiBzdXBwbGllciA/IHN1cHBsaWVyLnN0YWZmRW1haWwgOiAnJw0KICAgIH0sDQogICAgLy8g6I635Y+W5ZCI57qm5pi+56S65paH5pysDQogICAgZ2V0QWdyZWVtZW50RGlzcGxheShzZXJ2aWNlVHlwZUlkKSB7DQogICAgICBjb25zdCBzZXJ2aWNlSW5zdGFuY2UgPSB0aGlzLmdldFNlcnZpY2VJbnN0YW5jZShzZXJ2aWNlVHlwZUlkKQ0KICAgICAgaWYgKCFzZXJ2aWNlSW5zdGFuY2UpIHJldHVybiAnJw0KICAgICAgcmV0dXJuIHNlcnZpY2VJbnN0YW5jZS5hZ3JlZW1lbnRUeXBlQ29kZSArIHNlcnZpY2VJbnN0YW5jZS5hZ3JlZW1lbnRObw0KICAgIH0sDQogICAgLy8g5LqL5Lu26L2s5Y+R57uZ54i257uE5Lu2DQogICAgY2hhbmdlRm9sZChzZXJ2aWNlVHlwZUlkKSB7DQogICAgICB0aGlzLiRlbWl0KCJjaGFuZ2VGb2xkIiwgc2VydmljZVR5cGVJZCkNCiAgICB9LA0KICAgIGdldEZvbGQoc2VydmljZVR5cGVJZCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9sZFN0YXRlDQogICAgfSwNCiAgICBnZXRTZXJ2aWNlSW5zdGFuY2Uoc2VydmljZVR5cGVJZCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2VydmljZUluc3RhbmNlDQogICAgfSwNCiAgICBnZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlcnZpY2VPYmplY3QNCiAgICB9LA0KICAgIGdldFBheWFibGUoc2VydmljZVR5cGVJZCkgew0KICAgICAgcmV0dXJuIHRoaXMucGF5YWJsZQ0KICAgIH0sDQogICAgZ2V0Rm9ybURpc2FibGUoc2VydmljZVR5cGVJZCkgew0KICAgICAgcmV0dXJuIHRoaXMuZm9ybURpc2FibGUNCiAgICB9LA0KICAgIGNoYW5nZVNlcnZpY2VPYmplY3Qoc2VydmljZVR5cGVJZCwgc2VydmljZU9iamVjdCkgew0KICAgICAgdGhpcy4kZW1pdCgiY2hhbmdlU2VydmljZU9iamVjdCIsIHNlcnZpY2VUeXBlSWQsIHNlcnZpY2VPYmplY3QpDQogICAgfSwNCiAgICBhdWRpdENoYXJnZShzZXJ2aWNlVHlwZUlkLCBldmVudCkgew0KICAgICAgdGhpcy4kZW1pdCgiYXVkaXRDaGFyZ2UiLCBzZXJ2aWNlVHlwZUlkLCBldmVudCkNCiAgICB9LA0KICAgIGdlbmVyYXRlRnJlaWdodCh0eXBlMSwgdHlwZTIsIGl0ZW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoImdlbmVyYXRlRnJlaWdodCIsIHR5cGUxLCB0eXBlMiwgaXRlbSkNCiAgICB9LA0KICAgIHBzYUJvb2tpbmdDYW5jZWwoKSB7DQogICAgICB0aGlzLiRlbWl0KCJwc2FCb29raW5nQ2FuY2VsIikNCiAgICB9LA0KICAgIGNvcHlGcmVpZ2h0KGV2ZW50KSB7DQogICAgICB0aGlzLiRlbWl0KCJjb3B5RnJlaWdodCIsIGV2ZW50KQ0KICAgIH0sDQogICAgY2FsY3VsYXRlQ2hhcmdlKHNlcnZpY2VUeXBlSWQsIGV2ZW50LCBpdGVtKSB7DQogICAgICB0aGlzLiRlbWl0KCJjYWxjdWxhdGVDaGFyZ2UiLCBzZXJ2aWNlVHlwZUlkLCBldmVudCwgaXRlbSkNCiAgICB9LA0KICAgIC8vIOeJqea1gei/m+W6puebuOWFs+aWueazlQ0KICAgIGRlbGV0ZUxvZ0l0ZW0oc2VydmljZVR5cGVJZCwgZXZlbnQpIHsNCiAgICAgIGNvbnN0IHNlcnZpY2VPYmplY3QgPSB0aGlzLmdldFNlcnZpY2VPYmplY3Qoc2VydmljZVR5cGVJZCkNCiAgICAgIGlmIChzZXJ2aWNlT2JqZWN0ICYmIHNlcnZpY2VPYmplY3QucnNPcExvZ0xpc3QpIHsNCiAgICAgICAgc2VydmljZU9iamVjdC5yc09wTG9nTGlzdCA9IHNlcnZpY2VPYmplY3QucnNPcExvZ0xpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPT0gZXZlbnQpDQogICAgICB9DQogICAgfSwNCiAgICB1cGRhdGVMb2dMaXN0KHNlcnZpY2VUeXBlSWQsIGV2ZW50KSB7DQogICAgICBjb25zdCBzZXJ2aWNlT2JqZWN0ID0gdGhpcy5nZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpDQogICAgICBpZiAoc2VydmljZU9iamVjdCkgew0KICAgICAgICBzZXJ2aWNlT2JqZWN0LnJzT3BMb2dMaXN0ID0gZXZlbnQNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOi0ueeUqOWIl+ihqOebuOWFs+aWueazlQ0KICAgIGRlbGV0ZUFsbENoYXJnZShzZXJ2aWNlVHlwZUlkKSB7DQogICAgICBjb25zdCBzZXJ2aWNlT2JqZWN0ID0gdGhpcy5nZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpDQogICAgICBpZiAoc2VydmljZU9iamVjdCkgew0KICAgICAgICBzZXJ2aWNlT2JqZWN0LnJzQ2hhcmdlTGlzdCA9IFtdDQogICAgICB9DQogICAgfSwNCiAgICBkZWxldGVDaGFyZ2VJdGVtKHNlcnZpY2VUeXBlSWQsIGV2ZW50KSB7DQogICAgICBjb25zdCBzZXJ2aWNlT2JqZWN0ID0gdGhpcy5nZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpDQogICAgICBpZiAoc2VydmljZU9iamVjdCAmJiBzZXJ2aWNlT2JqZWN0LnJzQ2hhcmdlTGlzdCkgew0KICAgICAgICBzZXJ2aWNlT2JqZWN0LnJzQ2hhcmdlTGlzdCA9IHNlcnZpY2VPYmplY3QucnNDaGFyZ2VMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0gIT09IGV2ZW50KQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["RailwayComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuKA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "RailwayComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"railway-component\">\r\n    <!--铁路-->\r\n    <div class=\"railway-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                getFold(serviceItem.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n              铁路-{{ serviceItem.serviceShortName }}\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"getServiceInstance(serviceItem.serviceTypeId)\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"changeServiceObject(serviceItem.serviceTypeId, $event)\"\r\n            />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(3, serviceItem.serviceTypeId, getServiceObject(serviceItem.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(serviceItem.serviceTypeId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <template #reference>\r\n                    <el-input\r\n                      :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                    />\r\n                  </template>\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(serviceItem.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input :value=\"form.sqdPsaNo\" class=\"disable-form\" disabled/>\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          size=\"mini\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"20\"\r\n                @deleteItem=\"deleteLogItem(serviceItem.serviceTypeId, $event)\"\r\n                @return=\"updateLogList(serviceItem.serviceTypeId, $event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"20\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"getServiceObject(serviceItem.serviceTypeId).rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(getServiceObject(serviceItem.serviceTypeId))\"\r\n              @deleteItem=\"getServiceObject(serviceItem.serviceTypeId).rsDebitNoteList = getServiceObject(serviceItem.serviceTypeId).rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId,$event,getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"RailwayComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 铁路服务数据集合\r\n    railwayServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance || !serviceInstance.supplierId) return ''\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceTypeId) {\r\n      const serviceInstance = this.getServiceInstance(serviceTypeId)\r\n      if (!serviceInstance) return ''\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 事件转发给父组件\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      this.$emit(\"changeServiceObject\", serviceTypeId, serviceObject)\r\n    },\r\n    auditCharge(serviceTypeId, event) {\r\n      this.$emit(\"auditCharge\", serviceTypeId, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Railway组件特定样式\r\n.railway-component {\r\n  width: 100%;\r\n\r\n  .railway-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}