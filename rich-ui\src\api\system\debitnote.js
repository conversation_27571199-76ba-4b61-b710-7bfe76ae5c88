import request from '@/utils/request'

// 查询分账单列表
export function listDebitNote(query) {
  return request({
    url: '/system/debitnote/list',
    method: 'get',
    params: query
  })
}

// 查询分账单详细
export function getDebitNote(debitNoteId) {
  return request({
    url: '/system/debitnote/' + debitNoteId,
    method: 'get'
  })
}

// 新增分账单
export function addDebitNote(data) {
  return request({
    url: '/system/debitnote',
    method: 'post',
    data: data
  })
}

// 修改分账单
export function updateDebitNote(data) {
  return request({
    url: '/system/debitnote',
    method: 'put',
    data: data
  })
}

// 删除分账单
export function delDebitNote(debitNoteIds) {
  return request({
    url: '/system/debitnote/' + debitNoteIds,
    method: 'delete'
  })
}

// 修改分账单状态
export function changeDebitNoteStatus(data) {
  return request({
    url: '/system/debitnote/changeStatus',
    method: 'put',
    data: data
  })
}