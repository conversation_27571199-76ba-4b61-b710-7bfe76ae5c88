<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="showLeft">
        <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="query"
                 size="mini"
        >
          <el-form-item label="单号" prop="outboundNo">
            <el-input
              v-model="queryParams.outboundNo"
              clearable
              placeholder="出仓单号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="客户" prop="clientCode">
            <el-input
              v-model="queryParams.clientCode"
              clearable
              placeholder="客户代码"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="名称" prop="clientName">
            <el-input
              v-model="queryParams.clientName"
              clearable
              placeholder="客户名称"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="柜号" prop="containerNo">
            <el-input
              v-model="queryParams.containerNo"
              clearable
              placeholder="柜号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="封号" prop="sealNo">
            <el-input
              v-model="queryParams.sealNo"
              clearable
              placeholder="封号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="日期" prop="outboundDate">
            <el-date-picker v-model="queryParams.outboundDate"
                            clearable style="width: 100%"
                            placeholder="出仓日期"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="showRight">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:inventory:edit']"
              icon="el-icon-edit"
              plain
              size="mini"
              type="success"
              @click="handlePreOutbound()"
            >操作预出仓
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:inventory:edit']"
              icon="el-icon-edit"
              plain
              size="mini"
              type="success"
              @click="handleDirectOutbound()"
            >直接出仓
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:inventory:edit']"
              icon="el-icon-edit"
              plain
              size="mini"
              type="success"
              @click="handleRentSettlement()"
            >结算仓租
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!--出仓记录列表(预出仓记录列表)-->
        <el-table v-loading="loading" :data="outboundrecordList" @selection-change="handleSelectionChange"
                  @row-dblclick="(selectedRows) =>handleOutbound(selectedRows)"
        >
          <el-table-column align="center" type="selection" width="28"/>
          <el-table-column align="center" label="出仓单号" prop="outboundNo"/>
          <el-table-column align="center" label="客户代码" prop="clientCode"/>
          <el-table-column align="center" label="客户名称" prop="clientName"/>
          <el-table-column align="center" label="操作员" prop="operator"/>
          <el-table-column align="center" label="柜型" prop="containerType"/>
          <el-table-column align="center" label="柜号" prop="containerNo"/>
          <el-table-column align="center" label="封号" prop="sealNo"/>
          <el-table-column align="center" label="出仓日期" prop="outboundDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.outboundDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="仓库报价" prop="warehouseQuote"/>
          <el-table-column align="center" label="工人装柜费" prop="workerLoadingFee"/>
          <el-table-column align="center" label="仓管代收" prop="warehouseCollection"/>
          <el-table-column align="center" label="代收备注" prop="collectionNotes"/>
          <el-table-column align="center" label="总箱数" prop="totalBoxes"/>
          <el-table-column align="center" label="总毛重" prop="totalGrossWeight"/>
          <el-table-column align="center" label="总体积" prop="totalVolume"/>
          <el-table-column align="center" label="总行数" prop="totalRows"/>
          <el-table-column align="center" label="已收入仓费" prop="receivedStorageFee"/>
          <el-table-column align="center" label="未收卸货费" prop="unpaidUnloadingFee"/>
          <el-table-column align="center" label="未收打包费" prop="unpaidPackagingFee"/>
          <el-table-column align="center" label="物流代垫费" prop="logisticsAdvanceFee"/>
          <el-table-column align="center" label="租金平衡费" prop="rentalBalanceFee"/>
          <el-table-column align="center" label="超期仓租" prop="overdueRent"/>
          <el-table-column align="center" label="免堆天数" prop="freeStackDays"/>
          <el-table-column align="center" label="超期单价" prop="overdueUnitPrice"/>
          <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['system:outboundrecord:edit']"
                icon="el-icon-edit"
                size="mini"
                style="margin-right: -8px"
                type="success"
                @click="handleUpdate(scope.row)"
              >修改
              </el-button>
              <el-button
                v-hasPermi="['system:outboundrecord:remove']"
                icon="el-icon-delete"
                size="mini"
                style="margin-right: -8px"
                type="danger"
                @click="handleDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNum"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 出仓对话框 -->
    <el-dialog
      v-dialogDrag
      v-dialogDragWidth :close-on-click-modal="false" :modal-append-to-body="false" :title="'出库单'"
      :visible.sync="openOutbound"
      append-to-body
      width="70%"
    >
      <el-form ref="outboundForm" :model="outboundForm" :rules="rules" class="edit" label-width="80px">
        <el-row :gutter="10">
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="出仓单号" prop="outboundNo">
                  <el-input v-model="outboundForm.outboundNo" class="disable-form" disabled placeholder="出仓单号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户单号" prop="outboundNo">
                  <el-input v-model="outboundForm.customerOrderNo" placeholder="客户单号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户代码" prop="outboundNo">
                  <tree-select :flat="false" :multiple="false" :pass="outboundForm.clientCode"
                               :placeholder="'客户代码'"
                               :type="'warehouseClient'" @return="outboundForm.clientCode=$event"
                               @returnData="outboundClient($event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户名称" prop="outboundNo">
                  <el-input v-model="outboundForm.clientName" class="disable-form" disabled
                            placeholder="客户名称"
                  />
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="出仓日期" prop="inboundDate">
                  <el-date-picker v-model="outboundForm.outboundDate"
                                  clearable
                                  placeholder="计划出仓日期"
                                  style="width: 100%"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="出仓方式" prop="outboundType">
                  <el-select v-model="outboundForm.outboundType" placeholder="出仓方式" style="width: 100%">
                    <el-option label="整柜" value="整柜"></el-option>
                    <el-option label="散货" value="散货"></el-option>
                    <el-option label="快递" value="快递"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="柜型" prop="containerType">
                  <el-select v-model="outboundForm.containerType" style="width: 100%" @change="selectContainerType">
                    <el-option label="20GP" value="20GP"/>
                    <el-option label="20OT" value="20OT"/>
                    <el-option label="20FR" value="20FR"/>
                    <el-option label="TANK" value="TANK"/>
                    <el-option label="40GP" value="40GP"/>
                    <el-option label="40HQ" value="40HQ"/>
                    <el-option label="40NOR" value="40NOR"/>
                    <el-option label="40OT" value="40OT"/>
                    <el-option label="40FR" value="40FR"/>
                    <el-option label="40RH" value="40RH"/>
                    <el-option label="45HQ" value="45HQ"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="货物类型" prop="cargoType">
                  <el-select v-model="form.cargoType" placeholder="请选择货物类型" style="width: 100%">
                    <el-option label="普货" value="普货"></el-option>
                    <el-option label="大件" value="大件"></el-option>
                    <el-option label="鲜活" value="鲜活"></el-option>
                    <el-option label="危品" value="危品"></el-option>
                    <el-option label="冷冻" value="冷冻"></el-option>
                    <el-option label="标记" value="标记"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="柜号" prop="containerNo">
                  <el-input v-model="outboundForm.containerNo" placeholder="柜号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="封号" prop="sealNo">
                  <el-input v-model="outboundForm.sealNo" placeholder="封号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="车牌" prop="plateNumber">
                  <el-input v-model="outboundForm.plateNumber" placeholder="车牌"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="司机电话" prop="driverPhone">
                  <el-input v-model="outboundForm.driverPhone" placeholder="司机电话"/>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="仓库报价" prop="warehouseQuote">
                  <el-input v-model="outboundForm.warehouseQuote" class="number" placeholder="仓库报价"/>
                  <!--<el-col :span="12">
                    <el-input v-model="outboundForm.difficultyWorkFee" class="number" placeholder="困难作业费"/>
                  </el-col>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="仓管代收" prop="outboundNotes">
                  <el-input v-model="outboundForm.warehouseCollection" class="number" placeholder="仓管代收"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="工人装柜费" prop="workerLoadingFee">
                  <el-input v-model="outboundForm.workerLoadingFee" class="number" placeholder="工人装柜费"/>
                  <!--<el-col :span="12">
                    <el-input v-model="outboundForm.warehouseAdvanceOtherFee" class="number"
                              placeholder="仓库代付其他费用"
                    />
                  </el-col>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="仓管代付" prop="outboundNotes">
                  <el-input v-model="outboundForm.warehousePay" class="number" placeholder="仓管代收"/>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="12">
                <el-form-item label="操作要求" prop="operationRequirement">
                  <el-input v-model="outboundForm.operationRequirement" :autosize="{ minRows: 4}" maxlength="250"
                            placeholder="内容"
                            show-word-limit type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="仓管指示" prop="outboundNote">
                  <el-input v-model="outboundForm.outboundNote" :autosize="{ minRows: 4}" maxlength="250"
                            placeholder="内容"
                            show-word-limit type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="操作员" prop="operator">
                  <el-input v-model="outboundForm.operator" class="disable-form" disabled placeholder="操作员"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="下单日期" prop="orderDate">
                  <el-date-picker v-model="outboundForm.orderDate"
                                  class="disable-form" clearable disabled
                                  placeholder="下单日期"
                                  style="width: 100%"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="出仓经手人" prop="outboundHandler">
                  <el-input v-model="outboundForm.outboundHandler" class="disable-form" disabled
                            placeholder="出仓经手人"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-row>
                  <el-col :span="12">
                    <el-button :disabled="outboundForm.clientCode===null" type="primary" @click="warehouseConfirm">
                      {{ "仓管确认" }}
                    </el-button>
                  </el-col>
                  <el-col :span="12">
                    <el-button :disabled="outboundForm.clientCode===null" type="primary"
                               @click="loadPreOutboundInventoryList"
                    >
                      {{ "加载待出库" }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-col>
            </el-col>
          </el-row>
        </el-row>
        <!--库存记录列表(未出库)-->
        <el-row :gutter="10">
          <el-col>
            <el-col>
              <el-table v-loading="preOutboundInventoryListLoading" :data="preOutboundInventoryList"
                        ref="table" :summary-method="getSummaries" max-height="300"
                        show-summary @selection-change="handleOutboundSelectionChange"
                        :load="loadChildInventory" :tree-props="{children: 'children', hasChildren: 'hasChildren'}" lazy
                        element-loading-text="加载中..." row-key="inventoryId"
                        style="width: 100%;"
              >
                <el-table-column align="center" fixed type="selection" width="28"/>
                <el-table-column align="center" fixed label="序号" type="index" width="28">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column align="center" label="入仓流水号" prop="inboundSerialNo" width="120">
                  <template slot="header" slot-scope="scope">
                    <el-input
                      v-model="search"
                      clearable
                      placeholder="输入流水号搜索"
                      size="mini"
                      @keyup.enter.native="handleSearchEnter"
                    />
                  </template>
                </el-table-column>
                <el-table-column align="center" label="部分出库" prop="inboundDate" width="50">
                  <template slot-scope="scope">
                    <el-switch v-model="scope.row.partialOutboundFlag"/>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="货物明细">
                  <template slot-scope="scope">
                    <el-popover
                      :disabled="scope.row.partialOutboundFlag==0"
                      trigger="click"
                      width="800"
                    >
                      <el-table :data="scope.row.rsCargoDetailsList"
                                @selection-change="(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)"
                      >
                        <el-table-column align="center" type="selection" width="28"/>
                        <el-table-column
                          label="唛头"
                          prop="shippingMark"
                          width="150"
                        >
                        </el-table-column>
                        <el-table-column
                          label="货名"
                          prop="itemName"
                          width="150"
                        >
                        </el-table-column>
                        <el-table-column
                          label="箱数"
                          prop="boxCount"
                        >
                          <template slot-scope="scope">
                            <el-input v-model="scope.row.boxCount" :disabled="!isRowSelected(scope.row)"/>
                          </template>
                        </el-table-column>
                        <el-table-column
                          label="包装类型"
                          prop="packageType"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件长"
                          prop="unitLength"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件宽"
                          prop="unitWidth"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件高"
                          prop="unitHeight"
                        >
                        </el-table-column>
                        <el-table-column
                          label="体积小计"
                          prop="unitVolume"
                        >
                        </el-table-column>
                        <el-table-column
                          label="毛重小计"
                          prop="unitGrossWeight"
                        >
                        </el-table-column>
                        <el-table-column
                          label="破损标志"
                          prop="damageStatus"
                        >
                        </el-table-column>
                      </el-table>
                      <el-button slot="reference" style="margin: 0;padding: 5px">查看</el-button>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="最新计租日" prop="inboundDate" width="80">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.rentalSettlementDate, "{y}-{m}-{d}") }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="货代单号" prop="forwarderNo" show-overflow-tooltip/>
                <el-table-column align="center" label="客户代码" prop="clientCode"/>
                <el-table-column align="center" label="箱数" prop="totalBoxes"/>
                <el-table-column align="center" label="毛重" prop="totalGrossWeight"/>
                <el-table-column align="center" label="体积" prop="totalVolume"/>
                <el-table-column align="center" label="已收供应商" prop="receivedSupplier"/>
                <el-table-column align="center" label="已收入仓费" prop="receivedStorageFee"/>
                <el-table-column align="center" label="补收入仓费" prop="additionalStorageFee"/>
                <el-table-column align="center" label="补收卸货费" prop="unpaidUnloadingFee"/>
                <el-table-column align="center" label="应付卸货费" prop="receivedUnloadingFee"/>
                <el-table-column align="center" label="补收打包费" prop="unpaidPackingFee"/>
                <el-table-column align="center" label="应付打包费" prop="receivedPackingFee"/>
                <el-table-column align="center" label="物流代垫费" prop="logisticsAdvanceFee"/>
                <el-table-column align="center" label="免堆期" prop="freeStackPeriod"/>
                <el-table-column align="center" label="超期租金单价" prop="overdueRentalUnitPrice"/>
                <el-table-column align="center" label="超租天数" prop="rentalDays"/>
                <el-table-column align="center" label="超期租金" prop="overdueRentalFee"/>
              </el-table>
            </el-col>
          </el-col>
        </el-row>

        <!--汇总费用-->
        <el-row>
          <el-col :span="4">
            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>
          </el-col>
          <el-col :span="4">
            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>
          </el-col>
          <el-col :span="4">
            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>
          </el-col>
          <el-col :span="4">
            <span>应付工人：{{ outboundForm.payableToWorker }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>
          </el-col>
          <el-col :span="4">
            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>
          </el-col>
          <el-col :span="4">
            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>
          </el-col>
          <el-col :span="4">
            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="printOutboundPlant">打印出仓计划</el-button>
        <el-button v-if="outboundType===0" type="primary" @click="outboundConfirm(0)">确定预出仓</el-button>
        <el-button v-else type="primary" @click="outboundConfirm(outboundType)">{{
            outboundType === 3 ? "结 算" : "出 仓"
          }}</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="openOutbound = false">关 闭</el-button>
  </span>
    </el-dialog>

    <!-- 预览 -->
    <print-preview ref="preView"/>
  </div>
</template>

<script>
import {
  addOutboundrecord,
  changeStatus,
  delOutboundrecord,
  getOutboundrecord,
  listOutboundrecord,
  updateOutboundrecord
} from "@/api/system/outboundrecord"
import {parseTime} from "@/utils/rich"
import {
  listInventory,
  listInventorys,
  outboundInventory,
  preOutboundInventory,
  settlement
} from "@/api/system/inventory"
import moment from "moment"
import currency from "currency.js"
import warehouseReceipt from "@/print-template/warehouseReceipt"
import {defaultElementTypeProvider, hiprint} from "@"
import printPreview from "@/views/print/demo/design/preview.vue"
import outboundPlant from "@/print-template/outboundPlant"

let hiprintTemplate
export default {
  name: "Outboundrecord",
  components: {printPreview},
  data() {
    return {
      showLeft: 0,
      showRight: 24,
      // 遮罩层
      loading: true,
      selectOutboundList: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 出仓记录表格数据
      outboundrecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        outboundNo: null,
        clientCode: null,
        clientName: null,
        operator: null,
        containerType: null,
        containerNo: null,
        sealNo: null,
        outboundDate: null,
        warehouseQuote: null,
        workerLoadingFee: null,
        warehouseCollection: null,
        collectionNotes: null,
        totalBoxes: null,
        totalGrossWeight: null,
        totalVolume: null,
        totalRows: null,
        receivedStorageFee: null,
        unpaidUnloadingFee: null,
        unpaidPackagingFee: null,
        logisticsAdvanceFee: null,
        rentalBalanceFee: null,
        overdueRent: null,
        freeStackDays: null,
        overdueUnitPrice: null
      },
      // 表单参数
      form: {},
      outboundType: null,
      preOutboundInventoryListLoading: false,
      search: null,
      // 表单校验
      rules: {
        clientCode: [
          {required: true, message: "客户代码不能为空", trigger: "blur"}
        ]
      },
      outboundForm: {
        outboundDate: moment().format("yyyy-MM-DD")
      },
      clientRow: {},
      openOutbound: false,
      preOutboundInventoryList: [],
      selectedCargoDetail: []
    }
  },
  watch: {
    showSearch(n) {
      if (n === true) {
        this.showRight = 21
        this.showLeft = 3
      } else {
        this.showRight = 24
        this.showLeft = 0
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.initPrint()
  },
  methods: {
    initPrint() {
      hiprint.init({
        providers: [new defaultElementTypeProvider()]
      })
    },
    printOutboundPlant() {
      // 准备打印数据
      const printData = {
        title: "瑞旗仓库出仓计划",
        // 表单数据
        outboundNo: this.outboundForm.outboundNo || "",
        customerOrderNo: this.outboundForm.customerOrderNo || "",
        clientCode: this.outboundForm.clientCode || "",
        clientName: this.outboundForm.clientName || "",
        plannedOutboundDate: moment(this.outboundForm.plannedOutboundDate).format("yyyy-MM-DD HH:mm") || "",
        outboundType: this.outboundForm.outboundType || "",
        containerType: this.outboundForm.containerType || "",
        cargoType: this.form.cargoType || "",
        containerNo: this.outboundForm.containerNo || "",
        sealNo: this.outboundForm.sealNo || "",
        plateNumber: this.outboundForm.plateNumber || "",
        driverPhone: this.outboundForm.driverPhone || "",
        warehouseQuote: this.outboundForm.warehouseQuote || "",
        warehouseCollection: this.outboundForm.warehouseCollection || "",
        workerLoadingFee: this.outboundForm.workerLoadingFee || "",
        warehousePay: this.outboundForm.warehousePay || "",
        operationRequirement: this.outboundForm.operationRequirement || "",
        outboundNote: this.outboundForm.outboundNote || "",
        operator: this.outboundForm.operator || "",
        orderDate: this.outboundForm.orderDate || "",
        outboundHandler: this.outboundForm.outboundHandler || "",
        warehouseConfirm: "√ 已确认 " + this.parseTime(new Date(), "{y}-{m}-{d}"),

        // 汇总数据
        totalBoxes: this.outboundForm.totalBoxes || 0,
        totalGrossWeight: this.outboundForm.totalGrossWeight || 0,
        totalVolume: this.outboundForm.totalVolume || 0,
        totalSummary: `件数: ${this.outboundForm.totalBoxes || 0} / 毛重: ${this.outboundForm.totalGrossWeight || 0} / 体积: ${this.outboundForm.totalVolume || 0}`,
        totalQuantity: this.outboundForm.totalBoxes || 0,

        // 勾选的库存列表
        inventoryList: this.selectOutboundList.map(item => {
          return {
            inboundSerialNo: item.inboundSerialNo || "",
            clientCode: `${item.subOrderNo || ""} ${item.consigneeName || ""}`,
            totalBoxes: (this.outboundForm.sqdShippingMark || "") + " / " + (item.itemName || "") + " / " + (item.totalBoxes || 0) + " / " + (item.totalGrossWeight || 0) + "KGS / " + (item.totalVolume || 0) + "CBM",
            totalGrossWeight: item.totalGrossWeight || 0,
            totalVolume: item.totalVolume || 0,
            driverInfo: item.driverInfo || "",
            outboundQuantity: item.totalBoxes || 0
          }
        })
      }

      // 创建打印模板并预览打印
      hiprintTemplate = new hiprint.PrintTemplate({template: outboundPlant})
      this.$refs.preView.print(hiprintTemplate, printData)
    },
    warehouseConfirm() {
      // 检查客户代码是否已选择
      if (!this.outboundForm.clientCode) {
        this.$message.warning("请先选择客户")
        return
      }

      // 设置操作员为当前用户
      this.outboundForm.operator = this.$store.state.user.name.split(" ")[1]

      // 设置下单日期为当前日期
      this.outboundForm.orderDate = moment().format("yyyy-MM-DD")

      // 提示确认成功
      this.$message.success("仓管确认成功")
    },
    // 加载子节点数据
    loadChildInventory(tree, treeNode, resolve) {
      // 设置当前行的加载状态
      this.$set(tree, 'loading', true)

      // 使用packageTo字段查询子节点
      listInventory({packageTo: tree.inventoryId}).then(response => {
        const rows = response.rows

        // 先将数据传递给表格，确保子节点渲染
        resolve(rows)
        tree.children = rows

        // 如果父项被选中，在子节点渲染完成后选中它们
        if (this.ids.includes(tree.inventoryId)) {
          setTimeout(() => {
            rows.forEach(child => {
              if (!this.ids.includes(child.inventoryId)) {
                this.ids.push(child.inventoryId)
                this.selectOutboundList.push(child)
              }
              // 在UI上选中子项
              this.$refs.table.toggleRowSelection(child, true)
            })
          }, 50) // 等待DOM更新
        }
      }).finally(() => {
        // 无论请求成功还是失败，都需要关闭加载状态
        this.$set(tree, 'loading', false)
      })
    },
    warehouseRentSettlement() {
      this.outboundReset()
      this.outboundForm.outboundHandler = this.$store.state.user.name.split(" ")[1]
      this.outboundType = 4
      this.openOutbound = true
    },
    countSummary() {
      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value
      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value
      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value
      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value
      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value
      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value
      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value
      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value
    },
    currency,
    /**
     *
     * @param type 0:预出仓/1:出仓/2:直接出仓
     */
    outboundConfirm(type) {
      // 执行前再次提醒
      this.$confirm("确定要" + (type === 0 ? "预出仓" : type === 1 ? "出仓" : "直接出仓") + "吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 扣货不可以出仓
        this.selectOutboundList.map(item => {
          if (item.cargoDeduction == 1) {
            this.$message.error("有扣货库存请重新勾选，流水号：" + item.inboundSerialNoSub)
            return
          }
        })

        this.selectOutboundList.map(item => {
          item.partialOutboundFlag = Number(item.partialOutboundFlag)
        })

        // 更新箱数、毛重、体积
        this.outboundForm.totalBoxes = 0
        this.outboundForm.totalGrossWeight = 0
        this.outboundForm.totalVolume = 0
        this.selectOutboundList.map(item => {
          item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {
            this.outboundForm.totalBoxes = currency(item.boxCount).add(this.outboundForm.totalBoxes).value
            this.outboundForm.totalGrossWeight = currency(item.unitGrossWeight).add(this.outboundForm.totalGrossWeight).value
            this.outboundForm.totalVolume = currency(item.unitVolume).add(this.outboundForm.totalVolume).value
            return item
          }) : null
          return item
        })
        if (type === 0) {
          addOutboundrecord(this.outboundForm).then(response => {
            // 列表克隆一份,打上预出仓标志
            let data = this.selectOutboundList.map(item => {
              item.preOutboundFlag = "1"
              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {
                item.preOutboundFlag = "1"
                return item
              }) : null
              return item
            })

            preOutboundInventory(data).then(response => {
              this.getList()
              this.$message.success("预出仓成功")
              this.openOutbound = false
            })
          })
        } else if (type === 1) {
          // 直接出仓
          this.outboundForm.preOutboundFlag = "1"
          this.outboundForm.outboundDate = moment().format("yyyy-MM-DD")

          updateOutboundrecord(this.outboundForm).then(response => {
            const outboundRecordId = response.data
            // 列表克隆一份,打上出仓标志
            let data = this.selectOutboundList.map(item => {
              item.outboundRecordId = outboundRecordId
              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {
                item.outboundRecordId = outboundRecordId
                return item
              }) : null
              return item
            })

            // 出仓
            outboundInventory(data).then(response => {
              this.getList()
              this.$message.success("出仓成功")
              this.openOutbound = false
            })
          })
        } else if (type === 2) {
          // 直接出仓
          this.outboundForm.preOutboundFlag = "0"
          this.outboundForm.outboundDate = moment().format("yyyy-MM-DD")

          addOutboundrecord(this.outboundForm).then(response => {
            const outboundRecordId = response.data
            // 列表克隆一份,打上出仓标志
            let data = this.selectOutboundList.map(item => {
              item.outboundRecordId = outboundRecordId
              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {
                item.outboundRecordId = outboundRecordId
                return item
              }) : null
              return item
            })

            // 出仓
            outboundInventory(data).then(response => {
              this.getList()
              this.$message.success("出仓成功")
              this.openOutbound = false
            })
          })
        } else if (type === 3) {
          // 结算仓租
          this.outboundForm.isRentSettlement = 1 // 仓租结算记录
          addOutboundrecord(this.outboundForm).then(response => {
            const outboundRecordId = response.data
            // 列表克隆一份,打上出仓标志
            let data = this.selectOutboundList.map(item => {
              item.outboundRecordId = outboundRecordId
              item.rentalSettlementDate = this.outboundForm.outboundDate
              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {
                item.outboundRecordId = outboundRecordId
                return item
              }) : null
              return item
            })

            settlement(data).then(response => {
              this.$message.success("结算成功")
              this.loadPreOutboundInventoryList()
            })
          })
        } else {
          const outboundRecordId = this.outboundForm.outboundRecordId
          this.outboundForm.preOutboundFlag = "0"
          this.outboundForm.outboundDate = moment().format("yyyy-MM-DD")
          updateOutboundrecord(this.outboundForm).then(response => {
            // 列表克隆一份,打上出仓标志
            let data = this.selectOutboundList.map(item => {
              item.outboundRecordId = outboundRecordId
              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {
                item.outboundRecordId = outboundRecordId
                return item
              }) : null
              return item
            })

            // 出仓
            outboundInventory(data).then(response => {
              this.getList()
              this.$message.success("出仓成功")
              this.openOutbound = false
            })
          })
        }
      })
    },
    // 根据出库信息加载待出库的记录
    loadPreOutboundInventoryList() {
      this.preOutboundInventoryListLoading = true
      this.loading = true

      // 构建查询参数
      const queryParams = {
        sqdPlannedOutboundDate: this.outboundForm.plannedOutboundDate,
        clientCode: this.outboundForm.clientCode,
        inventoryStatus: "0"
      }

      // 根据出库类型添加预出库标志
      if (this.queryParams.preOutboundFlag) {
        queryParams.preOutboundFlag = this.queryParams.preOutboundFlag
      }

      if (this.queryParams.preOutboundRecordId) {
        queryParams.preOutboundRecordId = this.queryParams.preOutboundRecordId
      }

      // 发起请求
      listInventorys(queryParams)
        .then(response => {
          // 处理响应数据
          this.preOutboundInventoryList = response.rows.filter(item => !item.packageTo)
          this.preOutboundInventoryList ? response.rows.map(item => {
            // 计算补收入仓费
            if (item.includesInboundFee === 0) {
              const receivedFee = Number(item.receivedStorageFee || 0)
              const inboundFee = Number(item.inboundFee || 0)
              const difference = currency(inboundFee).subtract(receivedFee).value

              // 只有当差值大于0时才设置补收费用
              item.additionalStorageFee = difference > 0 ? difference : 0
            } else {
              item.additionalStorageFee = 0
            }

            // 如果是打包箱，标记为有子节点
            if (item.packageRecord === "1") {
              item.hasChildren = true
            }

            if (this.outboundForm.outboundRecordId === item.preOutboundRecordId) {
              this.selectOutboundList.push(item)
              this.$nextTick(() => {
                this.$refs.table.toggleRowSelection(item, true)
              })
            }

            return item
          }) : []

          // 更新总数
          this.total = response.total || 0

          // 如果是普通出库类型，自动选中预出库标记的行
          if (this.outboundType === 0 && this.$refs.table) {
            this.$nextTick(() => {
              this.preOutboundInventoryList.forEach(item => {
                if (item.preOutboundFlag === 1) {
                  this.$refs.table.toggleRowSelection(item, true)
                }
              })
            })
          }
        })
        .catch(error => {
          console.error("加载预出库库存列表失败:", error)
          this.$message.error("加载预出库库存列表失败")
        })
        .finally(() => {
          this.queryParams.preOutboundRecordId = null
          this.queryParams.preOutboundFlag = null
          this.loading = false
          this.preOutboundInventoryListLoading = false
        })
    },
    // 选择预出仓记录
    handleOutbound(selectedRows) {
      this.outboundReset()
      this.outboundForm = selectedRows
      this.outboundType = 1
      this.queryParams.preOutboundRecordId = this.outboundForm.outboundRecordId
      this.queryParams.preOutboundFlag = "1"
      this.loadPreOutboundInventoryList()
      this.openOutbound = true
    },
    // 添加预出仓记录
    handlePreOutbound() {
      this.outboundReset()
      this.outboundForm.outboundHandler = this.$store.state.user.name.split(" ")[1]
      this.outboundType = 0
      this.openOutbound = true
    },
    // 直接出仓
    handleDirectOutbound() {
      this.outboundReset()
      this.outboundForm.outboundHandler = this.$store.state.user.name.split(" ")[1]
      this.outboundType = 2
      this.openOutbound = true
    },
    // 结算仓租
    handleRentSettlement() {
      this.outboundReset()
      this.outboundForm.outboundHandler = this.$store.state.user.name.split(" ")[1]
      this.outboundType = 3
      this.openOutbound = true
    },
    parseTime,
    handleOutboundCargoDetailSelectionChange(selection, row) {
      row.outboundCargoDetailsList = selection
      this.selectedCargoDetail = selection
    },
    // 判断当前行是否被选中
    isRowSelected(row) {
      return this.selectedCargoDetail.includes(row)
    },
    getSummaries(param) {
      const {columns, data} = param
      const sums = []
      const statisticalField = [
        "receivedSupplier", "totalBoxes", "unpaidInboundFee", "totalGrossWeight",
        "totalVolume", "receivedStorageFee", "unpaidUnloadingFee", "logisticsAdvanceFee",
        "rentalBalanceFee", "overdueRentalFee", "additionalStorageFee", "unpaidUnloadingFee",
        "unpaidPackingFee", "receivedUnloadingFee", "receivedPackingFee"
      ]
      // 汇总结果存储对象
      const summaryResults = {}
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "汇总" // 第一列显示文本
        } else {
          const prop = column.property
          let total = 0; // 在条件块之前定义total变量

          if (prop === "totalBoxes" || prop === "totalVolume" || prop === "totalGrossWeight") {
            total = this.selectOutboundList.reduce((sum, row) => {
              if (row.packageTo) {
                return currency(sum).add(Number(row[prop]) || 0).value
              }
              return sum
            }, 0)
          } else {
            total = this.selectOutboundList.reduce((sum, row) =>
              currency(sum).add(Number(row[prop]) || 0).value, 0)
          }

          sums[index] = total
          // 现在可以安全地使用total
          summaryResults[column.property] = total
        }
      })

      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作
      // 假设表单字段的命名与统计字段一致
      Object.keys(summaryResults).forEach(field => {
        if (this.outboundForm) {
          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段
        }
      })

      this.countSummary()

      return sums
    },
    handleOutboundSelectionChange(selection) {
      // 正确获取表格数据 - 通过data属性
      const treeData = this.$refs.table.store.states.data
      // 获取之前的选择状态，用于比较变化
      const previousIds = [...this.ids]

      // 清空当前选择
      this.ids = []
      this.ids = selection.map(item => item.inventoryId)
      // 找出新选中和取消选中的项
      const newlySelected = this.ids.filter(id => !previousIds.includes(id))
      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id))

      this.selectOutboundList = selection
      this.$refs.table.doLayout() // 刷新表格布局

      // 根据仓租结算至（rental_settlement_date），计算该条库存的租金
      // （ 出库当天-仓租结算至-免租期 ） * 租金单价
      selection.map(item => {
        const date1 = moment(this.outboundForm.outboundDate)
        const date2 = moment(item.rentalSettlementDate)
        item.rentalDays = date1.diff(date2, "days") + 1 // 差距的天数
        let volumn = item.totalVolume

        if (!Number.isNaN(item.rentalDays) && item.rentalDays > 0) {
          // 出仓方式不是整柜没有免租天数
          if (this.outboundForm.outboundType !== "整柜") {
            item.overdueRentalFee = currency(item.rentalDays).multiply(item.overdueRentalUnitPrice).multiply(volumn).value
          } else {
            let days = currency(item.rentalDays).subtract(item.freeStackPeriod).value
            days = days > 0 ? days : 0
            item.rentalDays = days
            item.overdueRentalFee = currency(days).multiply(item.overdueRentalUnitPrice).multiply(volumn).value
          }
        }

        // 处理新选中的打包箱：自动选中其子项
        if (item.packageRecord === "1" && newlySelected.includes(item.inventoryId)) {
          // 如果是新选中的打包箱节点

          // 在树形表格数据中找到对应的节点
          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId)

          // 检查节点是否已展开(已有children属性且有内容)
          if (parentNode && parentNode.children && parentNode.children.length > 0) {
            // 如果节点已展开，直接选中其所有子项
            setTimeout(() => {
              parentNode.children.forEach(child => {
                if (!this.ids.includes(child.inventoryId)) {
                  this.ids.push(child.inventoryId)
                  this.selectOutboundList.push(child)
                  this.$refs.table.toggleRowSelection(child, true)
                }
              })
            }, 50) // 给一点时间让UI更新
          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {
            // 如果节点未展开且未加载过但有子节点标记
            parentNode.childrenLoaded = true

            // 手动展开行，触发懒加载
            this.$refs.table.toggleRowExpansion(parentNode, true)

            // 监听子节点加载完成后再选中它们
            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态
          }
        }
      })

      // 处理取消选中的打包箱：取消选中其子项
      newlyDeselected.forEach(parentId => {
        // 找出对应的父节点
        const parentNode = treeData.find(node =>
          node.inventoryId === parentId && node.packageRecord === "1"
        )

        if (parentNode && parentNode.children && parentNode.children.length > 0) {
          // 取消选中所有子项
          parentNode.children.forEach(child => {
            const childIndex = this.ids.indexOf(child.inventoryId)
            if (childIndex > -1) {
              // 从选中列表中移除
              this.ids.splice(childIndex, 1)
              const itemIndex = this.selectOutboundList.findIndex(
                item => item.inventoryId === child.inventoryId
              )
              if (itemIndex > -1) {
                this.selectOutboundList.splice(itemIndex, 1)
              }
              // 在UI上取消选中
              this.$refs.table.toggleRowSelection(child, false)
            }
          })
        }
      })

      this.countSummary()
    },
    selectContainerType(type) {
      switch (type) {
        case "20GP":
          this.outboundForm.warehouseQuote = this.clientRow.rate20gp
          break
        case "40HQ":
          this.outboundForm.warehouseQuote = this.clientRow.rate40hq
          break

      }
    },
    outboundClient(row) {
      this.outboundForm.warehouseQuote = row.rateLcl
      this.outboundForm.freeStackDays = row.freeStackPeriod
      this.clientRow = row
      this.outboundForm.overdueRentalUnitPrice = row.overdueRent
      this.outboundForm.clientName = row.clientName
      // this.outboundForm.workerLoadingFee=row.workerLoadingFee
      this.$forceUpdate()
    },
    /** 查询出仓记录列表 */
    getList() {
      this.loading = true
      listOutboundrecord(this.queryParams).then(response => {
        this.outboundrecordList = response.rows
        this.total = response.total
      }).finally(() => {
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    outboundReset() {
      this.outboundForm = {
        outboundRecordId: null,
        receivedSupplier: null,
        outboundNo: null,
        clientCode: null,
        clientName: null,
        operator: null,
        containerType: null,
        containerNo: null,
        sealNo: null,
        warehouseQuote: null,
        workerLoadingFee: null,
        warehouseCollection: null,
        collectionNotes: null,
        totalBoxes: null,
        totalGrossWeight: null,
        totalVolume: null,
        totalRows: null,
        receivedStorageFee: null,
        unpaidUnloadingFee: null,
        unpaidPackagingFee: null,
        logisticsAdvanceFee: null,
        rentalBalanceFee: null,
        overdueRent: null,
        operationRequirement: null,
        freeStackDays: null,
        overdueUnitPrice: null,
        receivedFromSupplier: null,
        unreceivedFromCustomer: null,
        receivedFromCustomer: null,
        customerReceivableBalance: null,
        payableToWorker: null,
        promissoryNoteSales: null,
        promissoryNoteCost: null,
        promissoryNoteGrossProfit: null,
        outboundDate: moment().format("yyyy-MM-DD")
      }
      this.preOutboundInventoryList = []
      this.resetForm("outboundForm")
    },
    // 表单重置
    reset() {
      this.form = {
        outboundDate: moment().format("yyyy-MM-DD"),
        outboundRecordId: null,
        outboundNo: null,
        clientCode: null,
        clientName: null,
        operator: null,
        containerType: null,
        containerNo: null,
        sealNo: null,
        warehouseQuote: null,
        workerLoadingFee: null,
        warehouseCollection: null,
        collectionNotes: null,
        totalBoxes: null,
        totalGrossWeight: null,
        totalVolume: null,
        totalRows: null,
        receivedStorageFee: null,
        unpaidUnloadingFee: null,
        unpaidPackagingFee: null,
        logisticsAdvanceFee: null,
        rentalBalanceFee: null,
        overdueRent: null,
        freeStackDays: null,
        overdueUnitPrice: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用"
      this.$modal.confirm("确认要\"" + text + "吗？").then(function () {
        return changeStatus(row.outboundRecordId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0"
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.outboundRecordId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加出仓记录"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const outboundRecordId = row.outboundRecordId || this.ids
      getOutboundrecord(outboundRecordId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改出仓记录"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["outboundForm"].validate(valid => {
        if (valid) {
          if (this.outboundForm.outboundRecordId != null) {
            updateOutboundrecord(this.outboundForm).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addOutboundrecord(this.outboundForm).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const outboundRecordIds = row.outboundRecordId || this.ids
      this.$modal.confirm("是否确认删除出仓记录编号为\"" + outboundRecordIds + "\"的数据项？").then(function () {
        return delOutboundrecord(outboundRecordIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download("system/outboundrecord/export", {
        ...this.queryParams
      }, `outboundrecord_${new Date().getTime()}.xlsx`)
    },
    // 添加搜索并滚动到匹配行的方法
    handleSearchEnter() {
      if (!this.search) return

      // 查找匹配的行索引
      const index = this.preOutboundInventoryList.findIndex(
        item => {
          // 确保 inboundSerialNo 存在且为字符串
          const serialNo = String(item.inboundSerialNo || "")
          const searchValue = String(this.search)
          // 打印每次比较的值，帮助调试
          return serialNo.includes(searchValue)
        }
      )

      if (index > -1) {
        // 获取表格DOM
        const table = this.$refs.table

        this.$nextTick(() => {
          // 获取表格的滚动容器
          const scrollWrapper = table.$el.querySelector(".el-table__body-wrapper")
          // 获取所有行
          const rows = scrollWrapper.querySelectorAll(".el-table__row")

          // 遍历所有行，找到匹配的流水号
          let targetIndex = -1
          rows.forEach((row, idx) => {
            const rowText = row.textContent
            if (rowText.includes(this.search)) {
              targetIndex = idx
            }
          })

          if (targetIndex > -1) {
            const targetRow = rows[targetIndex]
            // 计算需要滚动的位置
            const rowTop = targetRow.offsetTop

            // 使用平滑滚动
            scrollWrapper.scrollTo({
              top: rowTop - scrollWrapper.clientHeight / 2,
              behavior: "smooth"
            })

            // 高亮显示该行
            targetRow.classList.add("highlight-row")
            // 1秒后移除高亮
            setTimeout(() => {
              targetRow.classList.remove("highlight-row")
            }, 2000)
          }
        })
      } else {
        this.$message.warning("未找到匹配的记录")
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .edit .number .el-input__inner {
  text-align: right;
}

// 添加高亮样式
::v-deep .highlight-row {
  background-color: #fdf5e6 !important;
  transition: background-color 0.5s;
}
</style>
