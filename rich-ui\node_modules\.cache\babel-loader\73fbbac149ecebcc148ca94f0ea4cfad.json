{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\index.vue", "mtime": 1739006109898}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quotation", "require", "_store", "_interopRequireDefault", "_js<PERSON><PERSON>yin", "_vueTreeselect", "_logisticsNoInfo", "_preCarriageNoInfo", "_opHistory", "_receivablePayable", "_audit", "_rctold", "_booking", "_rich", "_index", "_rct", "name", "dicts", "props", "components", "PreCarriageNoInfo", "LogisticsNoInfo", "opHistory", "receivablePayable", "audit", "Treeselect", "data", "opList", "businessList", "belongList", "carrierList", "locationOptions", "goodsValue", "grossWeight", "list", "Set", "editOpHistory", "size", "$store", "state", "app", "title", "logisticsType", "carrierId", "carrierIds", "relationClientIds", "verifyPsaId", "salesId", "salesAssistantId", "salesObserverId", "opId", "bookingOpId", "docOpId", "opObserverId", "openGenerateRct", "psaVerify", "logistics", "basicInfo", "noInfo", "type", "open", "loading", "preCarriage", "importClearance", "exportDeclaration", "logisticsProcess", "logisticsNoInfo", "showLogisticsNoInfo", "openLogisticsNoInfo", "logisticsOpHistory", "logisticsReceivablePayableList", "preCarriageNoInfo", "showPreCarriageNoInfo", "openPreCarriageNoInfo", "preCarriageOpHistory", "preCarriageReceivablePayableList", "openExportDeclarationNoInfo", "exportDeclarationNoInfo", "showExportDeclarationNoInfo", "exportDeclarationOpHistory", "exportDeclarationReceivablePayableList", "openImportPassNoInfo", "importClearanceNoInfo", "showImportClearanceNoInfo", "importClearanceOpHistory", "importClearanceReceivablePayableList", "bookingList", "rctList", "form", "logisticsBasicInfo", "preCarriageBasicInfo", "exportDeclarationBasicInfo", "importClearanceBasicInfo", "rct", "leadingCharacter", "month", "noNum", "rctNo", "pageNum", "pageSize", "total", "rules", "chargeList", "watch", "formLogisticsTypeId", "n", "_this", "serviceTypeList", "length", "redisList", "serviceType", "store", "dispatch", "then", "getType", "beforeMount", "_this2", "reset", "getBookingList", "loadSelection", "getRctList", "$route", "query", "id", "getQuotation", "bId", "getBookingDetail", "rId", "getRctDetail", "methods", "_this3", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "response", "logisticsTypeId", "staffId", "clientId", "companyId", "clientRoleId", "companyRoleId", "clientContactor", "extStaffName", "clientContactorTel", "extStaffPhoneNum", "clientContactorEmail", "extStaffEmailEnterprise", "quotationNo", "richNo", "quotationDate", "Date", "impExpTypeId", "imExPort", "goodsNameSummary", "cargoName", "cargoPrice", "goodsCurrencyId", "cargoCurrencyId", "weightUnitId", "cargoUnitId", "polId", "departureId", "destinationPortId", "destinationId", "transitPortId", "transportationTermsId", "revenueTons", "newBookingRemark", "remark", "inquiryNo", "undefined", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "a", "value", "children", "_iterator2", "_step2", "b", "_iterator3", "_step3", "c", "deptId", "err", "e", "f", "cIds", "_iterator4", "_step4", "v", "_iterator7", "_step7", "carrier", "includes", "add", "serviceTypeId", "_iterator8", "_step8", "for<PERSON>ach", "push", "summary", "_iterator5", "quotationFreight", "_step5", "qf", "charge", "quotationCurrency", "toLowerCase", "Number", "quotationRate", "unit", "showClient", "showSupplier", "showQuotationCharge", "showCostCharge", "showQuotationCurrency", "showCostCurrency", "showQuotationUnit", "showCostUnit", "quotationStrategyId", "strategyId", "quotationChargeId", "chargeId", "quotationCharge", "costChargeId", "inquiryChargeId", "costCharge", "inquiryCharge", "quotationUnitId", "unitId", "quotationUnitCode", "unitCode", "quotationUnit", "costUnitId", "inquiryUnitCode", "inquiryRate", "costUnit", "inquiryExchangeRate", "exchangeRate", "inquiryCurrencyCode", "costCurrency", "quotationExchangeRate", "inquiryStrategyId", "quotationTaxRate", "taxRate", "inquiryTaxRate", "client", "company", "supplierSummary", "supplierId", "supplier", "quotationTotal", "quotationAmount", "costTotal", "inquiryAmount", "typeId", "quotation<PERSON><PERSON><PERSON>y", "characteristics", "_iterator6", "_step6", "cargoType", "locationDeparture", "locationDestination", "info", "essentialDetail", "inquiryNotice", "serviceTypeIds", "cargoTypeIds", "preCarriageRegionIds", "locationLoadingIds", "roleIds", "stop", "_this4", "_callee2", "_callee2$", "_context2", "listBooking", "rows", "_this5", "_callee3", "_callee3$", "_context3", "getBooking", "rr", "split", "_iterator9", "_step9", "_iterator10", "_step10", "_iterator11", "_step11", "_iterator12", "_step12", "_iterator13", "_step13", "_iterator14", "_step14", "_iterator15", "_step15", "_iterator16", "_step16", "role", "roleLocalName", "roleId", "_iterator17", "_step17", "_iterator18", "_step18", "rsBookingLogisticsTypeBasicInfo", "rsBookingReceivablePayableList", "rsBookingPreCarriageBasicInfo", "rsBookingExportDeclarationBasicInfo", "rsBookingImportClearanceBasicInfo", "_this6", "_callee4", "_callee4$", "_context4", "listRct", "_this7", "_callee5", "_callee5$", "_context5", "getRct", "_iterator19", "_step19", "_iterator20", "_step20", "_iterator21", "_step21", "_iterator22", "_step22", "_iterator23", "_step23", "_iterator24", "_step24", "_iterator25", "_step25", "_iterator26", "_step26", "_iterator27", "_step27", "_iterator28", "_step28", "rsRctLogisticsTypeBasicInfo", "rsRctReceivablePayableList", "rsOperationalProcessList", "rsRctPreCarriageBasicInfo", "rsRctExportDeclarationBasicInfo", "rsRctImportClearanceBasicInfo", "getServiceTypeList", "val", "_this8", "_callee6", "_iterator29", "_step29", "_iterator30", "_step30", "t", "_callee6$", "_context6", "clear", "$forceUpdate", "_iterator31", "_step31", "_iterator32", "_step32", "getRelationClientIds", "autoCompletion", "re", "num", "test", "replace", "str", "n1", "concat", "$message", "warning", "getNoInfo", "logisticsNo", "details", "preCarriageNo", "_iterator33", "_step33", "_loop", "numInfo", "vKey", "submitForm", "_this9", "toString", "isPsaVerified", "processStatusId", "psaVerifyTime", "parseTime", "$refs", "validate", "valid", "rctId", "emergencyLevel", "urgencyDegree", "difficultyLevel", "releaseType", "releaseTypeId", "paymentTitleCode", "impExpType", "tradingTerms", "tradingTermsId", "logisiticsTerms", "tradingPaymentChannel", "clientContractNo", "clientInvoiceNo", "cargoTypeIdSum", "packageQuantity", "goodsVolume", "volume", "weightUnitCode", "goodsCurrencyCode", "revenueTon", "localBasicPortId", "podId", "cvClosingTime", "siClosingTime", "firstVessel", "firstVoyage", "firstCyOpenTime", "firstCyClosingTime", "firstEtd", "basicVessel", "basicVoyage", "basicFinalGateinTime", "basicEtd", "podEta", "destinationPortEta", "inquiryScheduleSummary", "polBookingAgent", "podHandleAgent", "serviceTypeIdList", "sqdSoNoSum", "sqdMblNoSum", "sqdContainersSealsSum", "sqdIssueType", "sqdExportCustomsType", "sqdTrailerType", "rctProcessStatusSummary", "transportStatusSummary", "docStatusSummary", "paymentReceivingStatusSummary", "paymentPayingStatusSummary", "transportStatus", "doc<PERSON><PERSON>us", "paymentPayingStatus", "rctProcessId", "porcessId", "porcessStatusId", "processStatusTime", "processRemark", "qoutationNo", "qoutationSketch", "qoutationTime", "newBookingNo", "newBookingTime", "inquiryNoticeSum", "inquiryInnerRemarkSum", "opLeaderNotice", "opInnerRemark", "updateRct", "saveAll", "$modal", "msgSuccess", "addRct", "bookingId", "updateBooking", "addBooking", "rejected", "_this10", "$confirm", "confirmButtonText", "cancelButtonText", "customClass", "res", "saveLogistics", "savePreCarriage", "saveExportDeclaration", "saveImportClearance", "_this11", "error", "saveBookingLogistics", "success", "rsRctLogisticsNoInfos", "rsBasicLogistics", "rsServiceInstances", "inquiryLeatestUpdatedTime", "createTime", "maxWeight", "serviceBelongTo", "addBasiclogistics", "_this12", "saveBookingPreCarriage", "rsRctPreCarriageNoInfos", "saveRctPreCarriage", "_this13", "saveBookingExportDeclaration", "saveRctExportDeclaration", "_this14", "saveBookingImportClearance", "saveRctImportClearance", "transformToCharge", "serviceId", "receivablePayableList", "_this15", "map", "obj", "charge1", "sqdServiceTypeId", "sqdRctNo", "isRecievingOrPaying", "clearingCompanyId", "clearingCompanySummary", "dnChargeNameId", "dnCurrencyCode", "quotationCurrencyCode", "dnUnitRate", "dnUnitCode", "dnAmount", "basicCurrencyRate", "dutyRate", "charge2", "clientList", "supplierList", "loadOp", "loadCarrier", "loadSales", "loadBusinesses", "_this16", "_this17", "salesList", "_this18", "serviceTypeCarriers", "_this19", "businessesList", "staffNormalizer", "node", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "label", "isDisabled", "carrierNormalizer", "carrierLocalName", "carrierEnName", "serviceLocalName", "serviceEnName", "carrierIntlCode", "cancel", "rctOpDate", "paymentTypeId", "tradingPaymentChannelId", "logisticsTermsId", "volumeUnitId", "schedule", "validTimeForm", "isMblNeeded", "mblNo", "isUnderAgreementMbl", "isCustomsIntransitMbl", "isSwitchMbl", "isDividedMbl", "mblIssueTypeId", "mblGetWayId", "mblReleaseWayId", "isHblNeeded", "hblNoList", "isUnderAgreementHbl", "isCustomsIntransitHbl", "isSwitchHbl", "isDividedHbl", "hblIssueTypeId", "hblGetWayId", "hblReleaseWayId", "inquiryInnerRemark", "opLeaderRemark", "agreementTypeId", "agreementNo", "readOnly", "updateTime", "deleteTime", "deleteStatus", "deleteBy", "updateBy", "createBy", "soNo", "containerNo", "sealNo", "bookingDetail", "precarriageTime", "cvDeclaringTime", "vgm", "trailer", "shipName", "shipTime", "podETA", "telexReleaseType", "isReleasable", "sendToAgent", "boatId", "logisticsTypeInfoId", "firstCvClosingTime", "basicClosingTime", "opConfirmed", "opConfirmedId", "opConfirmedName", "opConfirmedDate", "financeConfirmed", "financeConfirmedId", "financeConfirmedName", "financeConfirmedDate", "salesConfirmed", "salesConfirmedId", "salesConfirmedName", "salesConfirmedDate", "supplierConfirmed", "supplierConfirmedId", "supplierConfirmedName", "supplierConfirmedDate", "invoiceQueryNo", "preCarriageInfoId", "preCarriageRegionId", "preCarriage<PERSON><PERSON><PERSON>", "preCarriageTime", "preCarriageContact", "preCarriageTel", "preCarriageRemark", "exportDeclarationId", "dispatchRegionId", "dispatchAddress", "dispatchTime", "dispatchContact", "dispatchTel", "dispatchRemark", "dispatchDriverName", "dispatchDriverTel", "dispatchTruckNo", "dispatchTruckRemark", "importClearanceId", "exportCustomsTypeId", "importCustomsTypeId", "resetForm", "handleSelectCarrierIds", "handleDeselectCarrierIds", "filter", "item", "generateRct", "_this20", "getRctMon", "j", "i", "date", "getMonth", "year", "getFullYear", "substring", "confirmRct", "exports", "_default"], "sources": ["src/views/system/document/index.vue"], "sourcesContent": ["<template>\r\n  <div style=\"margin: 15px;width: auto\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"63px\" class=\"edit\">\r\n      <el-row>\r\n        <el-col style=\"width: 75%;margin-right: 10px\">\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"4\" :class=\"type=='booking'?'booking':''\">\r\n              <div style=\"margin: 0 0 15px;padding:0;font-size: 40px;text-align: center;border: 1px solid #76933C;\">\r\n                {{ type === 'booking' ? '委托申请单' : '' }}\r\n                {{ type === 'op' ? '操作单' : '' }}\r\n              </div>\r\n              <el-form-item label=\"操作单号\" prop=\"rctNo\">\r\n                <el-input v-model=\"form.rctNo\" placeholder=\"操作单号\" @focus=\"generateRct(false)\"\r\n                          :disabled=\"type=='booking'||psaVerify\"\r\n                />\r\n                <!--                生成操作单号弹出层-->\r\n                <el-dialog\r\n                  :close-on-click-modal=\"false\"\r\n                  :modal-append-to-body=\"false\"\r\n                  v-dialogDrag v-dialogDragWidth\r\n                  title=\"新增操作单号\" :visible.sync=\"openGenerateRct\"\r\n                  append-to-body width=\"350px\"\r\n                >\r\n                  <el-form ref=\"form\" :model=\"rct\" :rules=\"rules\" label-width=\"65px\" class=\"edit\">\r\n                    <el-form-item label=\"单号规则\">\r\n                      <el-input v-model=\"rct.rules\" placeholder=\"前导字符+2位年份+2位月份+4位序列\" disabled/>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"前导字符\" prop=\"leadingCharacter\">\r\n                      <el-input v-model=\"rct.leadingCharacter\" placeholder=\"前导字符\" disabled/>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"所属月份\">\r\n                      <el-radio-group v-model=\"rct.month\">\r\n                        <el-radio-button label=\"1\">本月单号</el-radio-button>\r\n                        <el-radio-button label=\"2\">显示下月</el-radio-button>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"单号序列\">\r\n                      <el-radio-group v-model=\"rct.noNum\">\r\n                        <el-radio-button label=\"1\">自然序列</el-radio-button>\r\n                        <el-radio-button label=\"2\">手动分配</el-radio-button>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"单号预览\">\r\n                      <div style=\"display: flex\">\r\n                        <el-input v-model=\"rct.rctNo\" :disabled=\"rct.noNum=='1'\"/>\r\n                        <el-button type=\"success\" size=\"mini\" @click=\"generateRct(true)\">\r\n                          {{ rct.noNum == '1' ? '生成' : '' }}\r\n                          {{ rct.noNum == '2' ? '校验' : '' }}\r\n                        </el-button>\r\n                      </div>\r\n                    </el-form-item>\r\n                  </el-form>\r\n                  <div slot=\"footer\" class=\"dialog-footer\">\r\n                    <el-button type=\"primary\" size=\"mini\" @click=\"confirmRct\">确 定</el-button>\r\n                    <el-button size=\"mini\" @click=\"cancel\">取 消</el-button>\r\n                  </div>\r\n                </el-dialog>\r\n              </el-form-item>\r\n              <el-form-item label=\"操作日期\" prop=\"rctOpDate\">\r\n                <el-date-picker style=\"width:100%\" clearable\r\n                                v-model=\"form.rctOpDate\"\r\n                                type=\"date\"\r\n                                :disabled=\"type=='booking'||psaVerify\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                                placeholder=\"操作日期\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"操作员\" prop=\"opId\" :class=\"psaVerify?'booking':''\">\r\n                <treeselect v-model=\"opId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\"\r\n                            :show-count=\"true\" placeholder=\"操作员\" :disabled=\"psaVerify\"\r\n                            @open=\"loadOp\" @select=\"form.opId = $event.staffId\"\r\n                            @input=\"$event==undefined?form.opId = null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"订舱员\" prop=\"bookingOpId\" :class=\"type=='booking'?'booking':''\">\r\n                <treeselect v-model=\"bookingOpId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"opList.filter(v => {return v.role.roleLocalName=='订舱员'})\"\r\n                            :show-count=\"true\" placeholder=\"订舱员\" :disabled=\"type=='booking'||psaVerify\"\r\n                            @open=\"loadOp\" @select=\"form.bookingOpId = $event.staffId\"\r\n                            @input=\"$event==undefined?form.bookingOpId = null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"单证员\" prop=\"docOpId\" :class=\"type=='booking'?'booking':''\">\r\n                <treeselect v-model=\"docOpId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"opList.filter(v => {return v.role.roleLocalName=='单证员'})\"\r\n                            :show-count=\"true\" placeholder=\"单证员\" :disabled=\"type=='booking'||psaVerify\"\r\n                            @open=\"loadOp\" @select=\"form.docOpId = $event.staffId\"\r\n                            @input=\"$event==undefined?form.docOpId = null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"协助操作\" prop=\"opObserverId\" :class=\"type=='booking'?'booking':''\">\r\n                <treeselect v-model=\"opObserverId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"opList\" :show-count=\"true\" placeholder=\"协助操作\"\r\n                            :disabled=\"type=='booking'||psaVerify\"\r\n                            @open=\"loadOp\" @select=\"form.opObserverId = $event.staffId\"\r\n                            @input=\"$event==undefined?form.opObserverId = null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"订舱单号\" prop=\"newBookingNo\">\r\n                <el-input v-model=\"form.newBookingNo\" placeholder=\"订舱申请单号\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"订舱日期\" prop=\"newBookingTime\">\r\n                <el-date-picker style=\"width:100%\" clearable\r\n                                v-model=\"form.newBookingTime\"\r\n                                type=\"date\" :disabled=\"psaVerify\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                                placeholder=\"订舱申请单日期\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n              <el-form-item label=\"报价单号\" prop=\"quotationNo\">\r\n                <el-input v-model=\"form.quotationNo\" placeholder=\"报价单号\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"报价日期\" prop=\"quotationDate\">\r\n                <el-date-picker style=\"width:100%\" clearable\r\n                                v-model=\"form.quotationDate\"\r\n                                type=\"date\" :disabled=\"psaVerify\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                                placeholder=\"报价日期\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"业务员\" prop=\"salesId\">\r\n                <treeselect v-model=\"salesId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :disabled=\"psaVerify\"\r\n                            :options=\"belongList\" :show-count=\"true\" placeholder=\"业务员\"\r\n                            @open=\"loadSales\" @select=\"form.salesId = $event.staffId\"\r\n                            @input=\"$event==undefined?form.salesId = null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"业务助理\" prop=\"salesAssistantId\">\r\n                <treeselect v-model=\"salesAssistantId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :disabled=\"psaVerify\"\r\n                            :options=\"belongList\" :show-count=\"true\" placeholder=\"业务助理\"\r\n                            @open=\"loadSales\" @select=\"form.salesAssistantId = $event.staffId\"\r\n                            @input=\"$event==undefined?form.salesAssistantId=null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"协助业务\" prop=\"salesObserverId\">\r\n                <treeselect v-model=\"salesObserverId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :disabled=\"psaVerify\"\r\n                            :options=\"belongList\" :show-count=\"true\" placeholder=\"协助业务\"\r\n                            @open=\"loadSales\" @select=\"form.salesObserverId=$event.staffId\"\r\n                            @input=\"$event==undefined?form.salesObserverId=null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" :class=\"type=='booking'&&!psaVerify?'booking':''\">\r\n              <el-form-item label=\"商务审核\" prop=\"verifyPsaId\">\r\n                <treeselect v-model=\"verifyPsaId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                            :show-count=\"true\" placeholder=\"商务\"\r\n                            @open=\"loadBusinesses\" @select=\"form.verifyPsaId = $event.staffId\"\r\n                            @input=\"$event==undefined?form.verifyPsaId=null:null\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"审核时间\" prop=\"psaVerifyTime\">\r\n                <el-date-picker style=\"width:100%\" clearable\r\n                                v-model=\"form.psaVerifyTime\"\r\n                                type=\"date\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                                placeholder=\"商务审核时间\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"紧急程度\" prop=\"urgencyDegree\">\r\n                <el-input v-model=\"form.urgencyDegree\" placeholder=\"紧急程度\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"收付方式\" prop=\"paymentTypeId\">\r\n                <tree-select :type=\"'paymentType'\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.paymentTypeId\" :disabled=\"psaVerify\"\r\n                             :placeholder=\"'收付方式'\" @return=\"form.paymentTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"放货方式\" prop=\"releaseTypeId\">\r\n                <tree-select :type=\"'releaseType'\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.releaseTypeId\" :disabled=\"psaVerify\"\r\n                             :placeholder=\"'放货方式'\" @return=\"form.releaseTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"进度状态\" prop=\"processStatusId\">\r\n                <tree-select :type=\"'processStatus'\" :flat=\"false\"\r\n                             :multiple=\"false\" :pass=\"form.processStatusId\"\r\n                             :placeholder=\"'进度状态'\" @return=\"form.processStatusId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"委托单位\" prop=\"clientId\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.clientId\"\r\n                             :disabled=\"psaVerify\" :placeholder=\"'委托单位'\" :type=\"'client'\"\r\n                             v-if=\"$store.state.data.clientList.length>0\" @return=\"form.clientId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"客户角色\" prop=\"clientRoleId\">\r\n                <tree-select :type=\"'companyRole'\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.clientRoleId\" :disabled=\"psaVerify\"\r\n                             @return=\"form.clientRoleId=$event\" :placeholder=\"'客户角色'\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"联系人\" prop=\"clientContactor\">\r\n                <el-input v-model=\"form.clientContactor\" placeholder=\"联系人称谓\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"电话\" prop=\"clientContactorTel\">\r\n                <el-input v-model=\"form.clientContactorTel\" placeholder=\"联系人电话\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"邮箱\" prop=\"clientContactorEmail\">\r\n                <el-input v-model=\"form.clientContactorEmail\" placeholder=\"联系人邮箱\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"关联单位\" prop=\"relationClientIds\">\r\n                <tree-select v-if=\"$store.state.data.clientList.length>0\" :disabled=\"psaVerify\" :flat=\"true\"\r\n                             :multiple=\"true\"\r\n                             :pass=\"relationClientIds\" :placeholder=\"'客户'\" :type=\"'client'\"\r\n                             @return=\"getRelationClientIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"进出口\" prop=\"impExpTypeId\">\r\n                <el-select filterable v-model=\"form.impExpTypeId\" clearable placeholder=\"进出口\" style=\"width: 100%\"\r\n                           :disabled=\"psaVerify\"\r\n                >\r\n                  <el-option :value=\"'1'\" label=\"出口\">出口</el-option>\r\n                  <el-option :value=\"'2'\" label=\"进口\">进口</el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"收汇方式\" prop=\"tradingPaymentChannelId\">\r\n                <tree-select :type=\"'paymentChannels'\" :flat=\"false\"\r\n                             :disabled=\"psaVerify\" :multiple=\"false\"\r\n                             :pass=\"form.tradingPaymentChannelId\"\r\n                             :placeholder=\"'贸易付款方式'\"\r\n                             @returnData=\"form.tradingPaymentChannel=$event.paymentChannelsShortName\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"贸易条款\" prop=\"tradingTermsId\">\r\n                <tree-select :type=\"'tradingTerms'\" :flat=\"false\"\r\n                             :multiple=\"false\" :disabled=\"psaVerify\"\r\n                             :pass=\"form.tradingTermsId\"\r\n                             :placeholder=\"'贸易条款'\" @return=\"form.tradingTermsId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"运输条款\" prop=\"logisticsTermsId\">\r\n                <tree-select :type=\"'transportationTerms'\" :flat=\"false\"\r\n                             :multiple=\"false\" :disabled=\"psaVerify\"\r\n                             :pass=\"form.logisticsTermsId\"\r\n                             :placeholder=\"'运输条款'\" @return=\"form.logisticsTermsId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"合同号\" prop=\"clientContractNo\">\r\n                <el-input v-model=\"form.clientContractNo\" :disabled=\"psaVerify\" placeholder=\"(委托单位)合同号\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"发票号\" prop=\"clientInvoiceNo\">\r\n                <el-input v-model=\"form.clientInvoiceNo\" :disabled=\"psaVerify\" placeholder=\"(委托单位)发票号\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"货名概要\" prop=\"goodsNameSummary\">\r\n                <el-input v-model=\"form.goodsNameSummary\" placeholder=\"货名概要\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"件数\" prop=\"packageQuantity\">\r\n                <el-input-number :controls=\"false\" style=\"width: 100%\" v-model=\"form.packageQuantity\"\r\n                                 placeholder=\"总件数\" :disabled=\"psaVerify\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"毛重\" prop=\"grossWeight\">\r\n                <div style=\"display: flex\">\r\n                  <el-input v-model=\"grossWeight\" placeholder=\"总毛重\" style=\"width: 64%\"\r\n                            @change.native=\"autoCompletion('grossWeight')\" :disabled=\"psaVerify\"\r\n                  />\r\n                  <tree-select :pass=\"form.weightUnitId\" :type=\"'unit'\"\r\n                               :placeholder=\"'重量单位'\" :disabled=\"psaVerify\"\r\n                               @return=\"form.weightUnitId = $event\" style=\"width: 36%\"\r\n                  />\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"总体积\" prop=\"volume\">\r\n                <div style=\"display: flex\">\r\n                  <el-input-number :controls=\"false\" :precision=\"2\" :step=\"0.01\" v-model=\"form.volume\"\r\n                                   placeholder=\"总体积\" style=\"width: 64%\" :disabled=\"psaVerify\"\r\n                  />\r\n                  <tree-select :pass=\"form.volumeUnitId\" :type=\"'unit'\"\r\n                               :placeholder=\"'体积单位'\" :disabled=\"form.volumeUnitId!=null&&!psaVerify\"\r\n                               @return=\"form.volumeUnitId=$event\" style=\"width: 36%\"\r\n                  />\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"货物特征\" prop=\"cargoTypeIds\">\r\n                <tree-select :flat=\"false\" :multiple=\"true\"\r\n                             :pass=\"form.cargoTypeIds\" :placeholder=\"'货物特征'\" :disabled=\"psaVerify\"\r\n                             :type=\"'cargoType'\" @return=\"form.cargoTypeIds=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"货值\" prop=\"goodsValue\">\r\n                <div style=\"display: flex\">\r\n                  <el-input v-model=\"goodsValue\" placeholder=\"总货值\" style=\"width: 64%\"\r\n                            @change.native=\"autoCompletion('goodsValue')\" :disabled=\"psaVerify\"\r\n                  />\r\n                  <tree-select :pass=\"form.goodsCurrencyId\" :type=\"'currency'\"\r\n                               :placeholder=\"'货值币种'\" style=\"width: 36%\" :disabled=\"psaVerify\"\r\n                               @return=\"form.goodsCurrencyId=$event\"\r\n                  />\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"货物限重\" prop=\"maxWeight\">\r\n                <el-input-number :controls=\"false\" style=\"width: 100%\" :precision=\"2\" :step=\"0.01\"\r\n                                 v-model=\"form.maxWeight\" placeholder=\"货物限重\" :disabled=\"psaVerify\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"计费货量\" prop=\"revenueTons\">\r\n                <el-input style=\"width: 100%\" v-model=\"form.revenueTons\" placeholder=\"计费货量\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"物流类型\" prop=\"logisticsTypeId\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.logisticsTypeId\" :placeholder=\"'物流类型'\" :disabled=\"psaVerify\"\r\n                             :type=\"'serviceType'\" :main=\"true\" @return=\"form.logisticsTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"启运港\" prop=\"polId\">\r\n                <location-select :check-port=\"logisticsType\" :multiple=\"false\" :no-parent=\"true\"\r\n                                 :load-options=\"locationOptions\" :disabled=\"psaVerify\"\r\n                                 :pass=\"form.polId\" :placeholder=\"'启运港'\" @return=\"form.polId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"目的港\" prop=\"destinationPortId\">\r\n                <location-select :check-port=\"logisticsType\" :disabled=\"psaVerify\" :en=\"true\"\r\n                                 :load-options=\"locationOptions\" :multiple=\"false\"\r\n                                 :pass=\"form.destinationPortId\" :placeholder=\"'目的港'\"\r\n                                 @return=\"form.destinationPortId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"承运人\" prop=\"carrierIds\">\r\n                <treeselect v-model=\"carrierIds\" :disabled-fuzzy-matching=\"true\" :disable-branch-nodes=\"true\"\r\n                            :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\" :disabled=\"psaVerify\"\r\n                            :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                            placeholder=\"选择承运人\" @deselect=\"handleDeselectCarrierIds\" @open=\"loadCarrier\"\r\n                            @select=\"handleSelectCarrierIds\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.carrier.carrierIntlCode != null ? node.raw.carrier.carrierIntlCode : node.raw.carrier.carrierShortName\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{\r\n                      node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label\r\n                    }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"船期\">\r\n                <el-input v-model=\"form.schedule\" placeholder=\"内容\" :disabled=\"psaVerify\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"有效期\">\r\n                <el-input v-model=\"form.validTimeForm\" :disabled=\"psaVerify\" placeholder=\"内容\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <!--主提单-->\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"4\">\r\n              <el-form-item prop=\"isMblNeeded\" label-width=\"0\" class=\"spc\">\r\n                <el-checkbox-button v-model=\"form.isMblNeeded\" :disabled=\"psaVerify\" false-label=\"0\" true-label=\"1\"\r\n                                    label=\"主提单\" style=\"width: 65px\"\r\n                >\r\n                  主提单\r\n                </el-checkbox-button>\r\n                <el-input v-model=\"form.mblNo\" placeholder=\"主提单号\" v-if=\"form.isMblNeeded\"\r\n                          style=\"margin-left:5px;width: 65%\" :disabled=\"psaVerify\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isMblNeeded\">\r\n              <el-checkbox-button v-model=\"form.isUnderAgreementMbl\" :disabled=\"psaVerify\" false-label=\"0\" label=\"套约\"\r\n                                  style=\"width: 25%\" true-label=\"1\"\r\n              />\r\n              <el-checkbox-button v-model=\"form.isCustomsIntransitMbl\" :disabled=\"psaVerify\" false-label=\"0\"\r\n                                  label=\"清关中转\"\r\n                                  style=\"width: 25%\" true-label=\"1\"\r\n              />\r\n              <el-checkbox-button v-model=\"form.isSwitchMbl\" :disabled=\"psaVerify\" false-label=\"0\" true-label=\"1\"\r\n                                  label=\"转单\" style=\"width: 25%\"\r\n              />\r\n              <el-checkbox-button v-model=\"form.isDividedMbl\" :disabled=\"psaVerify\" false-label=\"0\" true-label=\"1\"\r\n                                  label=\"拆单\" style=\"width: 25%\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isMblNeeded\">\r\n              <el-form-item label=\"出单方式\" prop=\"mblIssueTypeId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\" :multiple=\"false\" :pass=\"form.mblIssueTypeId\"\r\n                             :placeholder=\"'主单出单方式'\" :type=\"'docIssueType'\"\r\n                             @return=\"form.mblIssueTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isMblNeeded\">\r\n              <el-form-item label=\"取单方式\" prop=\"mblGetWayId\">\r\n                <tree-select :type=\"'docReleaseWay'\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.mblGetWayId\" :disabled=\"psaVerify\"\r\n                             :placeholder=\"'主单取单方式'\" @return=\"form.mblGetWayId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isMblNeeded\">\r\n              <el-form-item label=\"交单方式\" prop=\"mblReleaseWayId\">\r\n                <tree-select :type=\"'docReleaseWay'\" :flat=\"false\"\r\n                             :multiple=\"false\" :disabled=\"psaVerify\"\r\n                             :pass=\"form.mblReleaseWayId\"\r\n                             :placeholder=\"'主单交单方式'\" @return=\"form.mblReleaseWayId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <!--货代提单-->\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"4\">\r\n              <el-form-item prop=\"isHblNeeded\" label-width=\"0\" class=\"spc\">\r\n                <el-checkbox-button v-model=\"form.isHblNeeded\" :disabled=\"psaVerify\" false-label=\"0\" label=\"货代提单\"\r\n                                    style=\"width: 65px\" true-label=\"1\"\r\n                >\r\n                  货代提单\r\n                </el-checkbox-button>\r\n                <el-input v-model=\"form.hblNoList\" placeholder=\"货代单号(list)\" v-if=\"form.isHblNeeded\"\r\n                          style=\"margin-left:5px;width: 65%\" :disabled=\"psaVerify\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isHblNeeded\">\r\n              <el-checkbox-button :disabled=\"psaVerify\" false-label=\"0\" label=\"套约\" style=\"width: 25%\" true-label=\"1\"\r\n                                  v-model=\"form.isUnderAgreementHbl\"\r\n              />\r\n              <el-checkbox-button :disabled=\"psaVerify\" false-label=\"0\" label=\"清关中转\" style=\"width: 25%\"\r\n                                  true-label=\"1\"\r\n                                  v-model=\"form.isCustomsIntransitHbl\"\r\n              />\r\n              <el-checkbox-button v-model=\"form.isSwitchHbl\" :disabled=\"psaVerify\" false-label=\"0\" label=\"转单\"\r\n                                  style=\"width: 25%\" true-label=\"1\"\r\n              />\r\n              <el-checkbox-button v-model=\"form.isDividedHbl\" :disabled=\"psaVerify\" false-label=\"0\" label=\"拆单\"\r\n                                  style=\"width: 25%\" true-label=\"1\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isHblNeeded\">\r\n              <el-form-item label=\"出单方式\" prop=\"hblIssueTypeId\">\r\n                <tree-select :type=\"'docIssueType'\" :flat=\"false\"\r\n                             :multiple=\"false\" :disabled=\"psaVerify\"\r\n                             :pass=\"form.hblIssueTypeId\"\r\n                             :placeholder=\"'货代单出单方式'\" @return=\"form.hblIssueTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isHblNeeded\">\r\n              <el-form-item label=\"取单方式\" prop=\"hblGetWayId\">\r\n                <tree-select :type=\"'docReleaseWay'\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.hblGetWayId\" :disabled=\"psaVerify\"\r\n                             :placeholder=\"'主单取单方式'\" @return=\"form.hblGetWayId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\" v-if=\"form.isHblNeeded\">\r\n              <el-form-item label=\"交单方式\" prop=\"hblReleaseWayId\">\r\n                <tree-select :type=\"'docReleaseWay'\" :flat=\"false\"\r\n                             :multiple=\"false\" :disabled=\"psaVerify\"\r\n                             :pass=\"form.hblReleaseWayId\"\r\n                             :placeholder=\"'货代单交单方式'\" @return=\"form.hblReleaseWayId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <!-- 选择服务类型,动态表单展示-->\r\n          <el-form-item label=\"服务类型\" prop=\"serviceTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\"\r\n                         :pass=\"form.serviceTypeIds\" :disabled=\"psaVerify\"\r\n                         :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                         @return=\"getServiceTypeList\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col style=\"width: 24%\">\r\n          <el-row style=\"margin-bottom: 10px\">\r\n            <!-- 订舱的时候显示 订舱单列表-->\r\n            <el-table v-if=\"type=='booking'\" v-loading=\"loading\" :data=\"bookingList\" border>\r\n              <el-table-column width=\"25px\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    style=\"padding: 0;\"\r\n                    @click=\"getBookingDetail(scope.row.bookingId)\"\r\n                  >选定\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"订舱单号\" prop=\"newBookingNo\" show-tooltip-when-overflow\r\n                               width=\"68px\"\r\n              />\r\n              <el-table-column align=\"center\" label=\"客户\" prop=\"clientName\" show-tooltip-when-overflow>\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row.clientName }} {{ scope.row.clientContactor }}\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <!-- 操作单明细的时候显示,加载已有的操作单-->\r\n            <el-table v-if=\"type=='op'\" v-loading=\"loading\" :data=\"rctList\" border>\r\n              <el-table-column width=\"25px\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button\r\n                    size=\"mini\"\r\n                    style=\"padding: 0;\"\r\n                    @click=\"getRctDetail(scope.row.rctId)\"\r\n                  >选定\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"操作单号\" prop=\"rctNo\" show-tooltip-when-overflow width=\"68px\"/>\r\n              <el-table-column align=\"center\" label=\"客户\" prop=\"clientName\" show-tooltip-when-overflow>\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row.clientName }} {{ scope.row.clientContactor }}\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <pagination\r\n              v-show=\"total>0\"\r\n              :total=\"total\"\r\n              :page.sync=\"pageNum\"\r\n              :limit.sync=\"pageSize\"\r\n              :auto-scroll=\"false\"\r\n              @pagination=\"type=='booking'?getBookingList:type=='op'?getRctList:null\"\r\n            />\r\n          </el-row>\r\n          <el-row>\r\n            <!-- 订舱的时候显示 -->\r\n            <el-button type=\"primary\" @click=\"submitForm('saveCopy')\" v-if=\"type=='booking'\" :disabled=\"psaVerify\">\r\n              另存为\r\n            </el-button>\r\n            <!-- 操作明细的时候 -->\r\n            <el-button type=\"primary\" @click=\"submitForm\">{{ psaVerify ? '确认审核' : '保 存' }}</el-button>\r\n            <!-- 审核 -->\r\n            <el-button type=\"warning\" @click=\"rejected\" v-if=\"psaVerify\">驳 回</el-button>\r\n            <el-button @click=\"cancel\" :disabled=\"psaVerify\">重 置</el-button>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <div style=\"margin-top: 10px;margin-bottom: 10px\">\r\n        <el-checkbox-button style=\"width: 100px\" v-model=\"basicInfo\" label=\"基础信息\" :disabled=\"psaVerify\"/>\r\n        <el-checkbox-button v-model=\"receivablePayable\" :disabled=\"psaVerify\" label=\"应收应付\" style=\"width: 100px\"/>\r\n        <!--订舱或者商务已审核会禁用-->\r\n        <el-checkbox-button v-model=\"opHistory\" :disabled=\"type=='booking'||psaVerify\" label=\"操作历史记录\"\r\n                            style=\"width: 100px\"\r\n        />\r\n        <el-checkbox-button style=\"width: 100px\" v-model=\"noInfo\" label=\"编号信息\"\r\n                            :disabled=\"type=='booking'||psaVerify\"\r\n        />\r\n        <el-checkbox-button v-model=\"audit\" :disabled=\"type=='booking'||psaVerify\" label=\"审核信息\"\r\n                            style=\"width: 100px\"\r\n        />\r\n      </div>\r\n\r\n      <!--基础物流-->\r\n      <div v-if=\"list.has('-1')||list.has('1')||list.has('2')||list.has('3')\">\r\n        <!--title-->\r\n        <div style=\"display: flex;margin-top: 10px;margin-bottom: 10px\">\r\n          <a :class=\"{'el-icon-arrow-down':logistics==false,'el-icon-arrow-right':logistics}\"/>\r\n          <h3 style=\"margin: 0\" @click=\"logistics=!logistics\">基础物流</h3>\r\n          <el-button type=\"primary\" style=\"padding: 0;margin-left: 10px\" @click=\"saveLogistics\" :disabled=\"psaVerify\">\r\n            保存\r\n          </el-button>\r\n        </div>\r\n        <!--content-->\r\n        <el-row :gutter=\"10\" style=\"margin-bottom:15px;display:-webkit-box\">\r\n          <div class=\"show\" :class=\"{'visible':logistics==false,'invisible':logistics}\">\r\n            <!--基础信息-->\r\n            <el-col :span=\"3\" :style=\"{'display':basicInfo?'':'none'}\">\r\n              <div class=\"titleStyle\">\r\n                <div class=\"titleText\">基础信息</div>\r\n              </div>\r\n              <div :class=\"{'inactive':basicInfo==false,'active':basicInfo}\">\r\n                <div class=\"titleStyle\">\r\n                  <div style=\"width: 92px\">物流要素</div>\r\n                  <div style=\"width: 66%\">具体信息</div>\r\n                </div>\r\n                <el-form-item label=\"承运人\">\r\n                  <treeselect v-model=\"carrierId\" :disabled-fuzzy-matching=\"true\" :disable-branch-nodes=\"true\"\r\n                              :flat=\"false\" :flatten-search-results=\"true\" :multiple=\"false\"\r\n                              :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                              placeholder=\"选择承运人\" @open=\"loadCarrier\" :disabled=\"psaVerify\"\r\n                              @input=\"$event==undefined?carrierId = null:null\"\r\n                              @select=\"logisticsBasicInfo.carrierId = $event.carrier.carrierId\"\r\n                  >\r\n                    <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                      {{\r\n                        node.raw.carrier.carrierIntlCode != null ? node.raw.carrier.carrierIntlCode : node.raw.carrier.carrierShortName\r\n                      }}\r\n                    </div>\r\n                    <label slot=\"option-label\"\r\n                           slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                           :class=\"labelClassName\"\r\n                    >\r\n                      {{\r\n                        node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label\r\n                      }}\r\n                      <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                    </label>\r\n                  </treeselect>\r\n                </el-form-item>\r\n                <el-form-item label=\"启运港\">\r\n                  <location-select :multiple=\"false\" :pass=\"logisticsBasicInfo.polId\" :check-port=\"logisticsType\"\r\n                                   :load-options=\"locationOptions\" :placeholder=\"'启运港'\" :disabled=\"psaVerify\"\r\n                                   @return=\"logisticsBasicInfo.polId=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"头程截关\" prop=\"firstCvClosingTime\" class=\"labelRight\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.firstCvClosingTime\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"头程截关\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"头程开舱\" prop=\"firstCyOpenTime\" class=\"labelRight\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.firstCyOpenTime\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"头程开舱\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"头程截重\" prop=\"firstCyClosingTime\" class=\"labelRight\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.firstCyClosingTime\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"头程截重\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"头程装船\" prop=\"firstEtd\" class=\"labelRight\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.firstEtd\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"头程装船\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"头程船名\" prop=\"firstVessel\" class=\"labelRight\">\r\n                  <el-input v-model=\"logisticsBasicInfo.firstVessel\" placeholder=\"船名\" :disabled=\"psaVerify\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"头程航次\" prop=\"firstVoyage\" class=\"labelRight\">\r\n                  <el-input v-model=\"logisticsBasicInfo.firstVoyage\" placeholder=\"航次\" :disabled=\"psaVerify\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"境内基港\" prop=\"localBasicPortId\">\r\n                  <location-select :multiple=\"false\" :pass=\"logisticsBasicInfo.localBasicPortId\"\r\n                                   :check-port=\"logisticsType\" :disabled=\"psaVerify\"\r\n                                   :load-options=\"locationOptions\" :placeholder=\"'境内基港'\"\r\n                                   @return=\"logisticsBasicInfo.localBasicPortId=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"基港截关\" prop=\"basicClosingTime\" class=\"labelRight\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.basicClosingTime\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"基港截关\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"基港截重\" prop=\"basicFinalGateinTime\" class=\"labelRight\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.basicFinalGateinTime\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"基港截重\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"基港装船\" prop=\"basicEtd\" class=\"labelRight\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.basicEtd\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"基港装船\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"基港船名\" prop=\"basicVessel\" class=\"labelRight\">\r\n                  <el-input v-model=\"logisticsBasicInfo.basicVessel\" placeholder=\"基港船名\" :disabled=\"psaVerify\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"基港航次\" prop=\"basicVoyage\" class=\"labelRight\">\r\n                  <el-input v-model=\"logisticsBasicInfo.basicVoyage\" placeholder=\"基港航次\" :disabled=\"psaVerify\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"中转港\" prop=\"transitPortId\">\r\n                  <location-select :multiple=\"false\" :pass=\"logisticsBasicInfo.transitPortId\"\r\n                                   :check-port=\"logisticsType\" :disabled=\"psaVerify\"\r\n                                   :load-options=\"locationOptions\" :placeholder=\"'中转港'\"\r\n                                   @return=\"logisticsBasicInfo.transitPortId=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"卸货港\" prop=\"podId\">\r\n                  <location-select :multiple=\"false\" :pass=\"logisticsBasicInfo.podId\" :check-port=\"logisticsType\"\r\n                                   :load-options=\"locationOptions\" :placeholder=\"'卸货港'\" :disabled=\"psaVerify\"\r\n                                   @return=\"logisticsBasicInfo.podId=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"卸货港到达\" prop=\"podEta\" class=\"labelRight\" label-width=\"78px\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"logisticsBasicInfo.podEta\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"卸货港到达\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"目的港\" prop=\"destinationPortId\">\r\n                  <location-select :check-port=\"logisticsType\" :load-options=\"locationOptions\" :multiple=\"false\"\r\n                                   :pass=\"logisticsBasicInfo.destinationPortId\" :disabled=\"psaVerify\"\r\n                                   @return=\"logisticsBasicInfo.destinationPortId=$event\" :placeholder=\"'目的港'\"\r\n                                   :en=\"true\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"目的港到达\" prop=\"destinationPortEta\" class=\"labelRight\" label-width=\"78px\">\r\n                  <el-date-picker v-model=\"logisticsBasicInfo.destinationPortEta\" :disabled=\"psaVerify\" clearable\r\n                                  placeholder=\"目的港到达\" style=\"width:100%\" type=\"date\" value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </div>\r\n            </el-col>\r\n            <el-col>\r\n              <el-row>\r\n                <!--应收应付-->\r\n                <receivable-payable :open-receivable-payable=\"receivablePayable\"\r\n                                    :receivable-payable=\"logisticsReceivablePayableList\"\r\n                                    @return=\"logisticsReceivablePayableList = $event\"\r\n                />\r\n              </el-row>\r\n              <el-row>\r\n                <!--操作历史记录-->\r\n                <op-history :basic-info-id=\"logisticsBasicInfo.logisticsTypeInfoId\" :op-history=\"logisticsOpHistory\"\r\n                            :open-op-history=\"opHistory\" :rct-id=\"form.rctId\"\r\n                            :type-id=\"1\" @return=\"logisticsOpHistory = $event\"\r\n                />\r\n              </el-row>\r\n              <!--编号信息-->\r\n              <el-col :span=\"4\" :style=\"{'display':noInfo?'':'none'}\">\r\n                <div class=\"titleStyle\">\r\n                  <div class=\"titleText\">编号信息</div>\r\n                </div>\r\n                <div :class=\"{'inactive':noInfo==false,'active':noInfo}\">\r\n                  <el-table :data=\"showLogisticsNoInfo\" border>\r\n                    <el-table-column label=\"物流号码\" prop=\"logisticsNo\" width=\"92px\"></el-table-column>\r\n                    <el-table-column label=\"具体信息\" prop=\"details\"></el-table-column>\r\n                  </el-table>\r\n                </div>\r\n                <el-button style=\"padding: 0\" type=\"text\" @click=\"openLogisticsNoInfo=true\">[＋]</el-button>\r\n              </el-col>\r\n              <!--审核信息-->\r\n              <audit :audit=\"audit\" :basic-info=\"logisticsBasicInfo\" @return=\"logisticsBasicInfo = $event\"/>\r\n            </el-col>\r\n          </div>\r\n        </el-row>\r\n        <logisticsNoInfo :open-logistics-no-info=\"openLogisticsNoInfo\" @return=\"getNoInfo('logistics',$event)\"\r\n                         @close=\"openLogisticsNoInfo=false\"\r\n        />\r\n      </div>\r\n\r\n      <!--前程运输-->\r\n      <div v-if=\"list.has('-1')||list.has('4')\">\r\n        <div style=\"display: flex;margin-top: 10px;margin-bottom: 10px\">\r\n          <a :class=\"{'el-icon-arrow-down':preCarriage==false,'el-icon-arrow-right':preCarriage}\"/>\r\n          <h3 style=\"margin: 0\" @click=\"preCarriage=!preCarriage\">前程运输</h3>\r\n          <el-button type=\"primary\" style=\"padding: 0;margin-left: 10px\" @click=\"savePreCarriage\" :disabled=\"psaVerify\">\r\n            保存\r\n          </el-button>\r\n        </div>\r\n        <!--content-->\r\n        <el-row :gutter=\"10\" style=\"margin-bottom:15px;display: -webkit-box\">\r\n          <div class=\"show\" :class=\"{'visible':preCarriage==false,'invisible':preCarriage}\">\r\n            <el-col :span=\"3\" :style=\"{'display':basicInfo?'':'none'}\">\r\n              <div class=\"titleStyle\">\r\n                <div class=\"titleText\">基础信息</div>\r\n              </div>\r\n              <div :class=\"{'inactive':basicInfo==false,'active':basicInfo}\">\r\n                <div class=\"titleStyle\" style=\"border-top:0\">\r\n                  <div style=\"width: 92px\">物流要素</div>\r\n                  <div style=\"width: 66%\">具体信息</div>\r\n                </div>\r\n                <el-form-item label=\"装运区域\" prop=\"preCarriageRegionId\">\r\n                  <location-select :load-options=\"locationOptions\" :multiple=\"false\" :disabled=\"psaVerify\"\r\n                                   :pass=\"preCarriageBasicInfo.preCarriageRegionId\"\r\n                                   @return=\"preCarriageBasicInfo.preCarriageRegionId=$event\" :placeholder=\"'装运区域'\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"装运地址\" prop=\"preCarriageAddress\">\r\n                  <el-input v-model=\"preCarriageBasicInfo.preCarriageAddress\" placeholder=\"装运地址\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"装运时间\" prop=\"preCarriageTime\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"preCarriageBasicInfo.preCarriageTime\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"装运时间\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"装运联系人\" prop=\"preCarriageContact\" label-width=\"78px\">\r\n                  <el-input v-model=\"preCarriageBasicInfo.preCarriageContact\" placeholder=\"装运联系人\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"联系人电话\" prop=\"preCarriageTel\" label-width=\"78px\">\r\n                  <el-input v-model=\"preCarriageBasicInfo.preCarriageTel\" placeholder=\"装运联系人电话\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"装运备注\" prop=\"preCarriageRemark\">\r\n                  <el-input v-model=\"preCarriageBasicInfo.preCarriageRemark\" placeholder=\"装运备注\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n              </div>\r\n            </el-col>\r\n            <el-col>\r\n              <el-row>\r\n                <receivable-payable :open-receivable-payable=\"receivablePayable\"\r\n                                    :receivable-payable=\"preCarriageReceivablePayableList\"\r\n                                    @return=\"preCarriageReceivablePayableList = $event\"\r\n                />\r\n              </el-row>\r\n              <el-row>\r\n                <op-history :basic-info-id=\"preCarriageBasicInfo.preCarriageInfoId\" :op-history=\"preCarriageOpHistory\"\r\n                            :open-op-history=\"opHistory\"\r\n                            :rct-id=\"form.rctId\" :type-id=\"4\"\r\n                            @return=\"preCarriageOpHistory = $event\"\r\n                />\r\n              </el-row>\r\n              <el-col :span=\"4\" :style=\"{'display':noInfo?'':'none'}\">\r\n                <div class=\"titleStyle\">\r\n                  <div class=\"titleText\">编号信息</div>\r\n                </div>\r\n                <div :class=\"{'inactive':noInfo==false,'active':noInfo}\">\r\n                  <el-table :data=\"showPreCarriageNoInfo\" border>\r\n                    <el-table-column label=\"物流号码\" prop=\"preCarriageNo\" width=\"92px\"></el-table-column>\r\n                    <el-table-column label=\"具体信息\" prop=\"details\"></el-table-column>\r\n                  </el-table>\r\n                </div>\r\n                <el-button style=\"padding: 0\" type=\"text\" @click=\"openPreCarriageNoInfo=true\">[＋]</el-button>\r\n              </el-col>\r\n              <audit :audit=\"audit\" :basic-info=\"preCarriageBasicInfo\" @return=\"preCarriageBasicInfo = $event\"/>\r\n            </el-col>\r\n          </div>\r\n        </el-row>\r\n        <PreCarriageNoInfo :open-pre-carriage-no-info=\"openPreCarriageNoInfo\" @return=\"getNoInfo('preCarriage',$event)\"\r\n                           @close=\"openPreCarriageNoInfo=false\"\r\n        />\r\n      </div>\r\n\r\n      <!--出口报关-->\r\n      <div v-if=\"list.has('-1')||list.has('5')\">\r\n        <div style=\"display: flex;margin-top: 10px;margin-bottom: 10px\">\r\n          <a :class=\"{'el-icon-arrow-down':exportDeclaration==false,'el-icon-arrow-right':exportDeclaration}\"/>\r\n          <h3 style=\"margin: 0\" @click=\"exportDeclaration=!exportDeclaration\">出口报关</h3>\r\n          <el-button type=\"primary\" style=\"padding: 0;margin-left: 10px\" @click=\"saveExportDeclaration\"\r\n                     :disabled=\"psaVerify\"\r\n          >保存\r\n          </el-button>\r\n        </div>\r\n        <el-row :gutter=\"10\" style=\"margin-bottom:15px;display: -webkit-box\">\r\n          <div class=\"show\" :class=\"{'visible':exportDeclaration==false,'invisible':exportDeclaration}\">\r\n            <el-col :span=\"3\" :style=\"{'display':basicInfo?'':'none'}\">\r\n              <div class=\"titleStyle\">\r\n                <div class=\"titleText\">基础信息</div>\r\n              </div>\r\n              <div :class=\"{'inactive':basicInfo==false,'active':basicInfo}\">\r\n                <div class=\"titleStyle\" style=\"border-top:0\">\r\n                  <div style=\"width: 92px\">物流要素</div>\r\n                  <div style=\"width: 66%\">具体信息</div>\r\n                </div>\r\n                <el-form-item label=\"派送区域\" prop=\"dispatchRegionId\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchRegionId\" placeholder=\"派送区域\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"派送详址\" prop=\"dispatchAddress\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchAddress\" placeholder=\"派送详址\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"派送时间\" prop=\"dispatchTime\">\r\n                  <el-date-picker style=\"width:100%\" clearable\r\n                                  v-model=\"exportDeclarationBasicInfo.dispatchTime\"\r\n                                  type=\"date\" :disabled=\"psaVerify\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                                  placeholder=\"派送时间\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"派送联系人\" prop=\"dispatchContact\" label-width=\"78px\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchContact\" placeholder=\"派送联系人\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"派送电话\" prop=\"dispatchTel\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchTel\" placeholder=\"派送电话\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"派送备注\" prop=\"dispatchRemark\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchRemark\" placeholder=\"派送备注\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"司机姓名\" prop=\"dispatchDriverName\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchDriverName\" placeholder=\"(派送)司机姓名\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"司机电话\" prop=\"dispatchDriverTel\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchDriverTel\" placeholder=\"(派送)司机电话\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"司机车牌\" prop=\"dispatchTruckNo\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchTruckNo\" placeholder=\"(派送)司机车牌\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"司机备注\" prop=\"dispatchTruckRemark\">\r\n                  <el-input v-model=\"exportDeclarationBasicInfo.dispatchTruckRemark\" placeholder=\"(派送)司机备注\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n              </div>\r\n            </el-col>\r\n            <el-col>\r\n              <el-row>\r\n                <receivable-payable :open-receivable-payable=\"receivablePayable\"\r\n                                    :receivable-payable=\"exportDeclarationReceivablePayableList\"\r\n                                    @return=\"exportDeclarationReceivablePayableList = $event\"\r\n                />\r\n              </el-row>\r\n              <el-row>\r\n                <op-history :basic-info-id=\"exportDeclarationBasicInfo.exportDeclarationId\"\r\n                            :op-history=\"exportDeclarationOpHistory\" :open-op-history=\"opHistory\"\r\n                            :rct-id=\"form.rctId\" :type-id=\"5\"\r\n                            @return=\"exportDeclarationOpHistory = $event\"\r\n                />\r\n              </el-row>\r\n              <el-col :span=\"4\" :style=\"{'display':noInfo?'':'none'}\">\r\n                <div class=\"titleStyle\">\r\n                  <div class=\"titleText\">编号信息</div>\r\n                </div>\r\n                <div :class=\"{'inactive':noInfo==false,'active':noInfo}\">\r\n                  <el-table :data=\"showExportDeclarationNoInfo\" border>\r\n                    <el-table-column label=\"物流号码\" prop=\"exportDeclarationNo\" width=\"92px\"></el-table-column>\r\n                    <el-table-column label=\"具体信息\" prop=\"details\"></el-table-column>\r\n                  </el-table>\r\n                  <el-button style=\"padding: 0\" type=\"text\" @click=\"openExportDeclarationNoInfo=true\">[＋]</el-button>\r\n                </div>\r\n              </el-col>\r\n              <audit :audit=\"audit\" :basic-info=\"exportDeclarationBasicInfo\"\r\n                     @return=\"exportDeclarationBasicInfo = $event\"\r\n              />\r\n            </el-col>\r\n          </div>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!--进口清关-->\r\n      <div v-if=\"list.has('-1')||list.has('6')\">\r\n        <div style=\"display: flex;margin-top: 10px;margin-bottom: 10px\">\r\n          <a :class=\"{'el-icon-arrow-down':importClearance==false,'el-icon-arrow-right':importClearance}\"/>\r\n          <h3 style=\"margin: 0\" @click=\"importClearance=!importClearance\">进口清关</h3>\r\n          <el-button type=\"primary\" style=\"padding: 0;margin-left: 10px\" @click=\"saveImportClearance\"\r\n                     :disabled=\"psaVerify\"\r\n          >保存\r\n          </el-button>\r\n        </div>\r\n        <el-row :gutter=\"10\" style=\"margin-bottom:15px;display: -webkit-box\">\r\n          <div class=\"show\" :class=\"{'visible':importClearance==false,'invisible':importClearance}\">\r\n            <el-col :span=\"3\" :style=\"{'display':basicInfo?'':'none'}\">\r\n              <div class=\"titleStyle\">\r\n                <div class=\"titleText\">基础信息</div>\r\n              </div>\r\n              <div :class=\"{'inactive':basicInfo==false,'active':basicInfo}\">\r\n                <div class=\"titleStyle\" style=\"border-top:0\">\r\n                  <div style=\"width: 92px\">物流要素</div>\r\n                  <div style=\"width: 66%\">具体信息</div>\r\n                </div>\r\n                <el-form-item label=\"报关方式\" prop=\"exportCustomsTypeId\">\r\n                  <el-input v-model=\"importClearanceBasicInfo.exportCustomsTypeId\" placeholder=\"报关方式\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"清关方式\" prop=\"importCustomsTypeId\">\r\n                  <el-input v-model=\"importClearanceBasicInfo.importCustomsTypeId\" placeholder=\"清关方式\"\r\n                            :disabled=\"psaVerify\"\r\n                  />\r\n                </el-form-item>\r\n              </div>\r\n            </el-col>\r\n            <el-col>\r\n              <el-row>\r\n                <receivable-payable :open-receivable-payable=\"receivablePayable\"\r\n                                    :receivable-payable=\"importClearanceReceivablePayableList\"\r\n                                    @return=\"importClearanceReceivablePayableList = $event\"\r\n                />\r\n              </el-row>\r\n              <el-row>\r\n                <op-history :basic-info-id=\"importClearanceBasicInfo.importClearanceId\"\r\n                            :op-history=\"importClearanceOpHistory\" :open-op-history=\"opHistory\"\r\n                            :rct-id=\"form.rctId\" :type-id=\"6\"\r\n                            @return=\"importClearanceOpHistory = $event\"\r\n                />\r\n              </el-row>\r\n              <el-col :span=\"4\" :style=\"{'display':noInfo?'':'none'}\">\r\n                <div class=\"titleStyle\">\r\n                  <div class=\"titleText\">编号信息</div>\r\n                </div>\r\n                <div :class=\"{'inactive':noInfo==false,'active':noInfo}\">\r\n                  <el-table :data=\"showImportClearanceNoInfo\" border>\r\n                    <el-table-column label=\"物流号码\" prop=\"importClearanceNo\" width=\"92px\"></el-table-column>\r\n                    <el-table-column label=\"具体信息\" prop=\"details\"></el-table-column>\r\n                  </el-table>\r\n                  <el-button style=\"padding: 0\" type=\"text\" @click=\"openImportPassNoInfo=true\">[＋]</el-button>\r\n                </div>\r\n              </el-col>\r\n              <audit :audit=\"audit\" :basic-info=\"importClearanceBasicInfo\" @return=\"importClearanceBasicInfo = $event\"/>\r\n            </el-col>\r\n          </div>\r\n        </el-row>\r\n      </div>\r\n\r\n      <el-row :gutter=\"10\" style=\"margin-bottom:15px;display: -webkit-box\" class=\"spc\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"业务报价综述\" prop=\"quotationSummary\" label-width=\"100\">\r\n            <el-input v-model=\"form.quotationSummary\" type=\"textarea\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      show-word-limit maxlength=\"150\" placeholder=\"内容\" :disabled=\"psaVerify\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"业务订舱备注\" prop=\"newBookingRemark\" label-width=\"100\">\r\n            <el-input v-model=\"form.newBookingRemark\" type=\"textarea\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      show-word-limit maxlength=\"150\" placeholder=\"业务订舱备注\" :disabled=\"psaVerify\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"业务须知\" prop=\"inquiryNotice\" label-width=\"100\">\r\n            <el-input v-model=\"form.inquiryNotice\" type=\"textarea\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      show-word-limit maxlength=\"150\" placeholder=\"内容\" :disabled=\"psaVerify\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"商务备注\" prop=\"inquiryInnerRemark\" label-width=\"100\">\r\n            <el-input v-model=\"form.inquiryInnerRemark\" type=\"textarea\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      show-word-limit maxlength=\"150\" placeholder=\"内容\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"操作主管备注\" prop=\"opLeaderRemark\" label-width=\"100\">\r\n            <el-input v-model=\"form.opLeaderRemark\" type=\"textarea\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      show-word-limit maxlength=\"150\" placeholder=\"内容\" :disabled=\"psaVerify\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"操作备注\" prop=\"opInnerRemark\" label-width=\"100\">\r\n            <el-input v-model=\"form.opInnerRemark\" type=\"textarea\" :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                      show-word-limit :disabled=\"psaVerify\"\r\n                      maxlength=\"150\" placeholder=\"内容\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <div>\r\n        <el-form-item label=\"合约类型\" prop=\"agreementTypeId\">\r\n          <el-input v-model=\"form.agreementTypeId\" placeholder=\"合约类型\" :disabled=\"psaVerify\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"合约号\" prop=\"agreementNo\">\r\n          <el-input v-model=\"form.agreementNo\" placeholder=\"合约号\" :disabled=\"psaVerify\"/>\r\n        </el-form-item>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { getQuotation } from '@/api/system/quotation'\r\nimport store from '@/store'\r\nimport pinyin from 'js-pinyin'\r\nimport Treeselect from '@riophae/vue-treeselect'\r\nimport '@riophae/vue-treeselect/dist/vue-treeselect.min.css'\r\nimport LogisticsNoInfo from '@/views/system/document/logisticsNoInfo'\r\nimport PreCarriageNoInfo from '@/views/system/document/preCarriageNoInfo'\r\nimport opHistory from '@/views/system/document/opHistory'\r\nimport receivablePayable from '@/views/system/document/receivablePayable'\r\nimport audit from '@/views/system/document/audit'\r\nimport {\r\n  getRct, getRctMon,\r\n  listRct,\r\n  saveRctExportDeclaration,\r\n  saveRctImportClearance,\r\n  saveRctLogistics,\r\n  saveRctPreCarriage\r\n} from '@/api/system/rctold'\r\nimport {\r\n  addBooking,\r\n  getBooking,\r\n  listBooking,\r\n  saveBookingExportDeclaration,\r\n  saveBookingImportClearance,\r\n  saveBookingLogistics,\r\n  saveBookingPreCarriage,\r\n  updateBooking\r\n} from '@/api/system/booking'\r\nimport { parseTime } from '@/utils/rich'\r\nimport quotation from '@/views/system/quotation/index.vue'\r\nimport {addBasiclogistics, addRct, updateRct} from '@/api/system/rct'\r\n\r\nexport default {\r\n  name: 'Document',\r\n  dicts: ['sys_yes_no'],\r\n  props: ['type'],\r\n  components: {\r\n    PreCarriageNoInfo,\r\n    LogisticsNoInfo,\r\n    opHistory,\r\n    receivablePayable,\r\n    audit,\r\n    Treeselect\r\n  },\r\n  data() {\r\n    return {\r\n      //选择框数据\r\n      opList: [],\r\n      businessList: [],\r\n      belongList: [],\r\n      carrierList: [],\r\n      locationOptions: [],\r\n      goodsValue: null,\r\n      grossWeight: null,\r\n      //过度数据\r\n      list: new Set(),\r\n      editOpHistory: {},\r\n      size: this.$store.state.app.size || 'mini',\r\n      title: '',\r\n      logisticsType: '1',\r\n      carrierId: null,\r\n      carrierIds: [],\r\n      relationClientIds: [],\r\n      verifyPsaId: null,\r\n      salesId: null,\r\n      salesAssistantId: null,\r\n      salesObserverId: null,\r\n      opId: null,\r\n      bookingOpId: null,\r\n      docOpId: null,\r\n      opObserverId: null,\r\n      //逻辑数据\r\n      openGenerateRct: false,\r\n      psaVerify: false,\r\n      logistics: false,\r\n      basicInfo: true,\r\n      noInfo: this.type == 'booking' ? false : true,\r\n      opHistory: this.type == 'booking' ? false : true,\r\n      receivablePayable: true,\r\n      audit: this.type == 'booking' ? false : true,\r\n      open: false,\r\n      loading: false,\r\n      preCarriage: false,\r\n      importClearance: false,\r\n      exportDeclaration: false,\r\n      logisticsProcess: [],\r\n      logisticsNoInfo: [],\r\n      showLogisticsNoInfo: [],\r\n      openLogisticsNoInfo: false,\r\n      logisticsOpHistory: [],\r\n      logisticsReceivablePayableList: [],\r\n      preCarriageNoInfo: [],\r\n      showPreCarriageNoInfo: [],\r\n      openPreCarriageNoInfo: false,\r\n      preCarriageOpHistory: [],\r\n      preCarriageReceivablePayableList: [],\r\n      openExportDeclarationNoInfo: false,\r\n      exportDeclarationNoInfo: [],\r\n      showExportDeclarationNoInfo: [],\r\n      exportDeclarationOpHistory: [],\r\n      exportDeclarationReceivablePayableList: [],\r\n      openImportPassNoInfo: false,\r\n      importClearanceNoInfo: [],\r\n      showImportClearanceNoInfo: [],\r\n      importClearanceOpHistory: [],\r\n      importClearanceReceivablePayableList: [],\r\n      // 表单参数\r\n      bookingList: [],\r\n      rctList: [],\r\n      form: {},\r\n      logisticsBasicInfo: {},\r\n      preCarriageBasicInfo: {},\r\n      exportDeclarationBasicInfo: {},\r\n      importClearanceBasicInfo: {},\r\n      rct: {\r\n        leadingCharacter: 'RCT',\r\n        month: 1,\r\n        noNum: 1,\r\n        rctNo: null\r\n      },\r\n      pageNum: 1,\r\n      pageSize: 20,\r\n      total: 0,\r\n      // 表单校验\r\n      rules: {},\r\n      chargeList: []\r\n    }\r\n\r\n  },\r\n  watch: {\r\n    'form.logisticsTypeId'(n) {\r\n      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {\r\n        store.dispatch('getServiceTypeList').then(() => {\r\n          this.getType(n)\r\n        })\r\n      } else {\r\n        this.getType(n)\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.reset()\r\n    // 通过业务报价中的 订舱申请单跳转过来的\r\n    if (this.type == 'booking') {\r\n      // 业务报价跳转过来的右侧 表格则是订舱单列表\r\n      this.getBookingList().then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 通过操作过程中的操作单明细跳转来的\r\n    if (this.type == 'op') {\r\n      // 如果 是操作单跳转的右侧表格展示的则是操作单列表\r\n      this.getRctList().then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 如果是来自报价列表的订舱申请\r\n    if (this.$route.query.id && this.type == 'booking') {\r\n      this.getQuotation(this.$route.query.id).then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 如果是来自于订舱订舱单列表的修改,则会通过路由传递申请订舱单id--booking\r\n    if (this.$route.query.bId) {\r\n      this.getBookingDetail(this.$route.query.bId).then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 如果是来自于操作单列表修改,则会通过路由传递操作单id--rct\r\n    if (this.$route.query.rId) {\r\n      this.getRctDetail(this.$route.query.rId).then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    if (this.$route.query.psaVerify) {\r\n      this.psaVerify = true\r\n    }\r\n  },\r\n  methods: {\r\n    // 根据传递过来的id查询报价信息\r\n    async getQuotation(id) {\r\n      this.reset()\r\n      await getQuotation(id).then(response => {\r\n        this.form.logisticsTypeId = response.data.logisticsTypeId\r\n        this.form.salesId = response.data.staffId\r\n        this.form.clientId = response.data.companyId\r\n        this.form.clientRoleId = response.data.companyRoleId\r\n        this.form.clientContactor = response.data.extStaffName\r\n        this.form.clientContactorTel = response.data.extStaffPhoneNum\r\n        this.form.clientContactorEmail = response.data.extStaffEmailEnterprise\r\n        this.form.quotationNo = response.data.richNo\r\n        this.form.quotationDate = new Date()\r\n        this.form.impExpTypeId = response.data.imExPort\r\n        this.form.goodsNameSummary = response.data.cargoName\r\n        this.form.goodsValue = response.data.cargoPrice\r\n        this.form.goodsCurrencyId = response.data.cargoCurrencyId\r\n        this.form.grossWeight = response.data.grossWeight\r\n        this.form.weightUnitId = response.data.cargoUnitId\r\n        this.form.polId = response.data.departureId\r\n        this.form.destinationPortId = response.data.destinationId\r\n        this.form.transitPortId = response.data.transportationTermsId\r\n        this.form.revenueTons = response.data.revenueTons\r\n        this.form.newBookingRemark = response.data.remark\r\n        this.form.inquiryNo = response.data.richNo\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.staffId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        let cIds = new Set()\r\n        for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                if (response.data.carrierIds.includes(a.carrier.carrierId)) {\r\n                  cIds.add(a.serviceTypeId)\r\n                }\r\n              }\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                    if (response.data.carrierIds.includes(b.carrier.carrierId)) {\r\n                      cIds.add(b.serviceTypeId)\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n        let summary = ''\r\n        for (const qf of response.quotationFreight) {\r\n          summary += (qf.charge != null ? qf.charge + ':' : '') + ((qf.quotationCurrency != null ? qf.quotationCurrency.toLowerCase() : '') + Number(qf.quotationRate) + (qf.unit != null ? '/' + qf.unit : '')) + '\\n'\r\n          qf.showClient = false\r\n          qf.showSupplier = false\r\n          qf.showQuotationCharge = false\r\n          qf.showCostCharge = false\r\n          qf.showQuotationCurrency = false\r\n          qf.showCostCurrency = false\r\n          qf.showQuotationUnit = false\r\n          qf.showCostUnit = false\r\n          qf.quotationStrategyId = qf.strategyId\r\n          qf.quotationChargeId = qf.chargeId\r\n          qf.quotationCharge = qf.charge\r\n          qf.costChargeId = qf.chargeId\r\n          qf.inquiryChargeId = qf.chargeId\r\n          qf.costCharge = qf.charge\r\n          qf.inquiryCharge = qf.charge\r\n          qf.quotationUnitId = qf.unitId\r\n\r\n          qf.quotationUnitCode = qf.unitCode\r\n          qf.quotationUnit = qf.unit\r\n          qf.costUnitId = qf.unitId\r\n\r\n          qf.inquiryUnitCode = qf.unit\r\n          qf.inquiryRate = qf.inquiryRate\r\n          qf.costUnit = qf.unit\r\n          qf.inquiryExchangeRate = qf.exchangeRate\r\n          qf.inquiryCurrencyCode = qf.costCurrency\r\n          qf.quotationExchangeRate = qf.exchangeRate\r\n          qf.inquiryStrategyId = qf.strategyId\r\n          qf.quotationTaxRate = qf.taxRate\r\n          qf.inquiryTaxRate = qf.taxRate\r\n          qf.clientId = response.data.companyId\r\n          qf.client = response.data.company\r\n          this.form.supplierSummary += qf.supplierId + ','\r\n          qf.supplierId = qf.companyId\r\n          qf.supplier = qf.company\r\n          qf.quotationTotal = Number(qf.quotationRate) * Number(qf.quotationAmount) * Number(qf.quotationExchangeRate) * (1 + Number(qf.quotationTaxRate) / 100)\r\n          qf.costTotal = Number(qf.inquiryRate) * Number(qf.inquiryAmount) * Number(qf.inquiryExchangeRate) * (1 + Number(qf.inquiryTaxRate) / 100)\r\n          if (qf.typeId == '1' || qf.typeId == '2' || qf.typeId == '3') {\r\n            // 添加到物流应收应付 中\r\n            this.logisticsReceivablePayableList.push(qf)\r\n          }\r\n          if (qf.typeId == '4') {\r\n            // 添加到前程运输应收应付中\r\n            this.preCarriageReceivablePayableList.push(qf)\r\n          }\r\n          if (qf.typeId == '5') {\r\n            // 添加到出口报关应收应付中\r\n            this.exportDeclarationReceivablePayableList.push(qf)\r\n          }\r\n        }\r\n        this.form.quotationSummary = summary\r\n        // 注意事项\r\n        let characteristics = ''\r\n        if (response.characteristics) {\r\n          for (const c of response.characteristics) {\r\n            characteristics += (c.serviceType != null ? c.serviceType : '')\r\n              + (c.cargoType != null ? c.cargoType : '')\r\n              + (c.company != null ? c.company : '')\r\n              + (c.locationDeparture != null ? c.locationDeparture : '')\r\n              + (c.locationDestination != null ? c.locationDestination : '')\r\n              + (c.info != null ? c.info : '')\r\n              + (c.essentialDetail != null ? c.essentialDetail : '') + '\\n'\r\n          }\r\n        }\r\n        this.form.inquiryNotice = characteristics\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.form.cargoTypeIds = response.cargoTypeIds\r\n        this.locationOptions = response.locationOptions\r\n        this.form.preCarriageRegionIds = response.locationLoadingIds\r\n        this.form.clientRoleId = response.roleIds[0]\r\n      })\r\n    },\r\n    async getBookingList() {\r\n      this.loading = true\r\n      await listBooking({ pageNum: this.pageNum, pageSize: this.pageSize }).then(response => {\r\n        if (response != '') {\r\n          this.bookingList = response.rows\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    async getBookingDetail(id) {\r\n      this.reset()\r\n      await getBooking(id).then(response => {\r\n        let rr = []\r\n        if (response.data.relationClientIds) {\r\n          response.data.relationClientIds.split(',').forEach(v => {\r\n            rr.push(Number(v))\r\n          })\r\n        }\r\n        this.relationClientIds = rr\r\n        this.grossWeight = response.data.grossWeight\r\n        this.goodsValue = response.data.goodsValue\r\n        this.form = response.data\r\n        this.form.relationClientIds = rr\r\n        let cIds = new Set()\r\n        if (response.data.carrierIds) {\r\n          for (const v of this.carrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                  if (response.data.carrierIds.includes(a.carrier.carrierId)) {\r\n                    cIds.add(a.serviceTypeId)\r\n                  }\r\n                }\r\n                if (a.children != undefined && a.children.length > 0) {\r\n                  for (const b of a.children) {\r\n                    if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                      if (response.data.carrierIds.includes(b.carrier.carrierId)) {\r\n                        cIds.add(b.serviceTypeId)\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.salesId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesAssistantId) {\r\n                      this.salesAssistantId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesObserverId) {\r\n                      this.salesObserverId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.opList != undefined) {\r\n          for (const a of this.opList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (a.role.roleLocalName == '操作员' && b.staffId == response.data.opId) {\r\n                  this.opId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '订舱员' && b.staffId == response.data.bookingOpId) {\r\n                  this.bookingOpId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '单证员' && b.staffId == response.data.docOpId) {\r\n                  this.docOpId = b.roleId\r\n                }\r\n                if (b.staffId == response.data.opObserverId) {\r\n                  this.opObserverId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.businessList != undefined) {\r\n          for (const a of this.businessList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.staffId == response.data.verifyPsaId) {\r\n                  this.verifyPsaId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        // 设置基础物流信息的值\r\n        if (response.data.rsBookingLogisticsTypeBasicInfo != null) {\r\n          this.logisticsBasicInfo = response.data.rsBookingLogisticsTypeBasicInfo\r\n          this.logisticsReceivablePayableList = response.data.rsBookingLogisticsTypeBasicInfo.rsBookingReceivablePayableList\r\n          /* this.logisticsReceivablePayableList.map(logisticsReceivablePayable=>{\r\n            log\r\n          }) */\r\n        }\r\n        // 设置前程运输的值\r\n        if (response.data.rsBookingPreCarriageBasicInfo != null) {\r\n          this.preCarriageBasicInfo = response.data.rsBookingPreCarriageBasicInfo\r\n          this.preCarriageReceivablePayableList = response.data.rsBookingPreCarriageBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        // 设置出口报关的值\r\n        if (response.data.rsBookingExportDeclarationBasicInfo != null) {\r\n          this.exportDeclarationBasicInfo = response.data.rsBookingExportDeclarationBasicInfo\r\n          this.exportDeclarationReceivablePayableList = response.data.rsBookingExportDeclarationBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        // 设置进口清关的值\r\n        if (response.data.rsBookingImportClearanceBasicInfo != null) {\r\n          this.importClearanceBasicInfo = response.data.rsBookingImportClearanceBasicInfo\r\n          this.importClearanceReceivablePayableList = response.data.rsBookingImportClearanceBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        this.locationOptions = response.locationOptions\r\n      })\r\n    },\r\n    async getRctList() {\r\n      this.loading = true\r\n      await listRct({ pageNum: this.pageNum, pageSize: this.pageSize }).then(response => {\r\n        if (response != '') {\r\n          this.rctList = response.rows\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    async getRctDetail(id) {\r\n      this.reset()\r\n      await getRct(id).then(response => {\r\n        this.grossWeight = response.data.grossWeight\r\n        this.goodsValue = response.data.goodsValue\r\n        let cIds = new Set()\r\n        // 承运人\r\n        if (response.data.carrierIds) {\r\n          for (const v of this.carrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                  if (response.data.carrierIds.includes(a.carrier.carrierId)) {\r\n                    cIds.add(a.serviceTypeId)\r\n                  }\r\n                }\r\n                if (a.children != undefined && a.children.length > 0) {\r\n                  for (const b of a.children) {\r\n                    if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                      if (response.data.carrierIds.includes(b.carrier.carrierId)) {\r\n                        cIds.add(b.serviceTypeId)\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n        let rr = []\r\n        // 关联客户\r\n        if (response.data.relationClientIds) {\r\n          response.data.relationClientIds.split(',').forEach(v => {\r\n            rr.push(Number(v))\r\n          })\r\n        }\r\n        this.relationClientIds = rr\r\n        this.form = response.data\r\n        this.form.relationClientIds = rr\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.salesId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesAssistantId) {\r\n                      this.salesAssistantId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesObserverId) {\r\n                      this.salesObserverId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.opList != undefined) {\r\n          for (const a of this.opList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (a.role.roleLocalName == '操作员' && b.staffId == response.data.opId) {\r\n                  this.opId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '订舱员' && b.staffId == response.data.bookingOpId) {\r\n                  this.bookingOpId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '单证员' && b.staffId == response.data.docOpId) {\r\n                  this.docOpId = b.roleId\r\n                }\r\n                if (b.staffId == response.data.opObserverId) {\r\n                  this.opObserverId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.businessList != undefined) {\r\n          for (const a of this.businessList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.staffId == response.data.verifyPsaId) {\r\n                  this.verifyPsaId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (response.data.rsRctLogisticsTypeBasicInfo != null) {\r\n          this.logisticsBasicInfo = response.data.rsRctLogisticsTypeBasicInfo\r\n          this.logisticsReceivablePayableList = response.data.rsRctLogisticsTypeBasicInfo.rsRctReceivablePayableList\r\n          this.logisticsOpHistory = response.data.rsRctLogisticsTypeBasicInfo.rsOperationalProcessList\r\n        }\r\n        if (response.data.rsRctPreCarriageBasicInfo != null) {\r\n          this.preCarriageBasicInfo = response.data.rsRctPreCarriageBasicInfo\r\n          this.preCarriageReceivablePayableList = response.data.rsRctPreCarriageBasicInfo.rsRctReceivablePayableList\r\n          this.preCarriageOpHistory = response.data.rsRctPreCarriageBasicInfo.rsOperationalProcessList\r\n        }\r\n        if (response.data.rsRctExportDeclarationBasicInfo != null) {\r\n          this.exportDeclarationBasicInfo = response.data.rsRctExportDeclarationBasicInfo\r\n          this.exportDeclarationReceivablePayableList = response.data.rsRctExportDeclarationBasicInfo.rsRctReceivablePayableList\r\n          this.exportDeclarationOpHistory = response.data.rsRctExportDeclarationBasicInfo.rsOperationalProcessList\r\n        }\r\n        if (response.data.rsRctImportClearanceBasicInfo != null) {\r\n          this.importClearanceBasicInfo = response.data.rsRctImportClearanceBasicInfo\r\n          this.importClearanceReceivablePayableList = response.data.rsRctImportClearanceBasicInfo.rsRctReceivablePayableList\r\n          this.importClearanceOpHistory = response.data.rsRctImportClearanceBasicInfo.rsOperationalProcessList\r\n        }\r\n        this.locationOptions = response.locationOptions\r\n      })\r\n    },\r\n    async getServiceTypeList(val) {\r\n      if (this.$store.state.data.serviceTypeList.length == 0) {\r\n        await this.$store.dispatch('getServiceTypeList')\r\n      }\r\n      this.list.clear()\r\n      this.form.serviceTypeIds = val\r\n      for (const s of this.$store.state.data.serviceTypeList[0].children) {\r\n        if (val.includes(s.serviceTypeId)) {\r\n          if (s.typeId != null) {\r\n            this.list.add(s.typeId)\r\n          }\r\n        }\r\n        if (s.children) {\r\n          for (const t of s.children) {\r\n            if (val.includes(t.serviceTypeId)) {\r\n              if (s.typeId != null) {\r\n                this.list.add(s.typeId)\r\n              }\r\n              if (t.typeId != null) {\r\n                this.list.add(t.typeId)\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if (val.includes(-1)) {\r\n        this.list.add('-1')\r\n      }\r\n      this.$forceUpdate()\r\n    },\r\n    /**\r\n     * 根据id获取对应的服务类型\r\n     * @param n\r\n     */\r\n    getType(n) {\r\n      for (const s of this.$store.state.data.serviceTypeList[0].children) {\r\n        if (s.serviceTypeId == n) {\r\n          this.logisticsType = s.typeId\r\n        }\r\n        if (s.children) {\r\n          for (const t of s.children) {\r\n            if (t.serviceTypeId == n) {\r\n              this.logisticsType = s.typeId\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    getRelationClientIds(val) {\r\n      this.form.relationClientIds = val\r\n      this.relationClientIds = val\r\n    },\r\n    // 格式化货值与货重\r\n    autoCompletion(val) {\r\n      let re = /\\d{1,3}(?=(\\d{3})+$)/g\r\n      let num = /[0-9]+/g\r\n      if (val == 'grossWeight') {\r\n        if (num.test(this.grossWeight)) {\r\n          this.grossWeight = this.grossWeight.replace(/\\b(0+)/gi, '')\r\n          this.form.grossWeight = this.grossWeight\r\n          let str = this.grossWeight.split('.')\r\n          let n1 = str[0].replace(re, '$&,')\r\n          this.grossWeight = str.length > 1 && str[1] ? `${n1}.${str[1]}` : `${n1}.00`\r\n        } else {\r\n          this.$message.warning('请输入数字')\r\n        }\r\n      }\r\n      if (val == 'goodsValue') {\r\n        if (num.test(this.goodsValue)) {\r\n          this.goodsValue = this.goodsValue.replace(/\\b(0+)/gi, '')\r\n          this.form.goodsValue = this.goodsValue\r\n          let str = this.goodsValue.split('.')\r\n          let n1 = str[0].replace(re, '$&,')\r\n          this.goodsValue = str.length > 1 && str[1] ? `${n1}.${str[1]}` : `${n1}.00`\r\n        } else {\r\n          this.$message.warning('请输入数字')\r\n        }\r\n      }\r\n    },\r\n    getNoInfo(type, val) {\r\n      let list\r\n      if (type == 'logistics') {\r\n        this.logisticsNoInfo = val\r\n        list = this.showLogisticsNoInfo = [\r\n          { type: 'soNo', logisticsNo: 'SO号码', details: '' },\r\n          { type: 'mblNo', logisticsNo: '主提单号', details: '' },\r\n          { type: 'hblNo', logisticsNo: '货代单号', details: '' },\r\n          { type: 'containersInfo', logisticsNo: '柜号信息', details: '' },\r\n          { type: 'shipper', logisticsNo: '发货人', details: '' },\r\n          { type: 'consignee', logisticsNo: '收货人', details: '' },\r\n          { type: 'notifyParty', logisticsNo: '通知人', details: '' },\r\n          { type: 'polBookingAgent', logisticsNo: '启运港放舱代理', details: '' },\r\n          { type: 'podHandleAgent', logisticsNo: '目的港换单代理', details: '' },\r\n          { type: 'shippingMark', logisticsNo: '唛头', details: '' },\r\n          { type: 'goodsDescription', logisticsNo: '货描', details: '' },\r\n          { type: 'blIssueDate', logisticsNo: '签单日期', details: '' },\r\n          { type: 'blIssueLocation', logisticsNo: '签单地点', details: '' }]\r\n\r\n      }\r\n      if (type == 'preCarriage') {\r\n        this.preCarriageNoInfo = val\r\n        list = this.showPreCarriageNoInfo = [\r\n          { type: 'soNo', preCarriageNo: 'SO号码', details: '' },\r\n          { type: 'preCarriageDriverName', preCarriageNo: '司机姓名', details: '' },\r\n          { type: 'preCarriageDriverTel', preCarriageNo: '司机电话', details: '' },\r\n          { type: 'preCarriageTruckNo', preCarriageNo: '司机车牌', details: '' },\r\n          { type: 'preCarriageTruckRemark', preCarriageNo: '司机备注', details: '' },\r\n          { type: 'preCarriageAddress', preCarriageNo: '装柜地址', details: '' },\r\n          { type: 'preCarriageTime', preCarriageNo: '到场时间', details: '' },\r\n          { type: 'containerNo', preCarriageNo: '柜号', details: '' },\r\n          { type: 'containerType', preCarriageNo: '柜型', details: '' },\r\n          { type: 'sealNo', preCarriageNo: '封条', details: '' },\r\n          { type: 'weightPaper', preCarriageNo: '磅单', details: '' }]\r\n      }\r\n      for (let numInfo of list) {\r\n        val.forEach(v => {\r\n          for (const vKey in v) {\r\n            if (vKey == numInfo.type) {\r\n              numInfo.details = numInfo.details == '' ? (v[vKey] != null ? v[vKey] : '') : numInfo.details + (v[vKey] != null ? ',' + v[vKey] : '')\r\n            }\r\n          }\r\n        })\r\n      }\r\n    },\r\n    /** 提交按钮 */\r\n    // 先添加主体信息，得到返回的记录主键，再根据主键插入其相关的信息（前程运输及其应收应付、出口报关及其应收应付等）\r\n    submitForm(v) {\r\n      this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n      if (this.psaVerify) {\r\n        this.form.isPsaVerified = 1\r\n        this.form.processStatusId = 2\r\n        this.form.psaVerifyTime = parseTime(new Date())\r\n      }\r\n      this.$refs['form'].validate(valid => {\r\n        // 如果是操作订舱 或者 询价订舱且商务审核\r\n        // 是否会插入一个操作单记录\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          // 另存为\r\n          if (v == 'saveCopy') {\r\n            this.form.rctId = null\r\n          }\r\n          if (valid) {\r\n            // 组装数据\r\n            let data = {}\r\n            data.clientId = this.form.clientId\r\n            data.clientRoleId = this.form.clientRoleId\r\n            data.clientContactor = this.form.clientContactor\r\n            data.clientContactorTel = this.form.clientContactorTel\r\n            data.clientContactorEmail = this.form.clientContactorEmail\r\n            data.emergencyLevel = this.form.urgencyDegree\r\n            data.difficultyLevel = null\r\n            data.releaseType = this.form.releaseTypeId //?\r\n            data.paymentTitleCode = this.form.paymentTitleCode\r\n            data.impExpType = this.form.impExpTypeId\r\n            data.tradingTerms = this.form.tradingTermsId\r\n            data.logisiticsTerms = this.form.logisiticsTerms\r\n            data.tradingPaymentChannel = this.form.tradingPaymentChannel\r\n            data.clientContractNo = this.form.clientContractNo\r\n            data.clientInvoiceNo = this.form.clientInvoiceNo\r\n            data.cargoTypeIdSum = this.form.cargoTypeIds.toString()\r\n            data.goodsNameSummary = this.form.goodsNameSummary\r\n            data.packageQuantity = this.form.packageQuantity\r\n            data.goodsVolume = this.form.volume\r\n            data.grossWeight = this.grossWeight\r\n            data.weightUnitCode = this.form.weightUnitId\r\n            data.goodsCurrencyCode = this.form.goodsCurrencyId\r\n            data.goodsValue = this.form.goodsValue\r\n            data.logisticsTypeId = this.form.logisticsTypeId\r\n            data.revenueTon = this.form.revenueTons\r\n            data.polId = this.form.polId\r\n            data.localBasicPortId = this.form.localBasicPortId\r\n            data.transitPortId = this.form.transitPortId\r\n            data.podId = this.form.podId\r\n            data.destinationPortId = this.form.destinationPortId\r\n            data.cvClosingTime = this.form.cvClosingTime\r\n            data.siClosingTime = this.form.siClosingTime\r\n            data.firstVessel = this.form.firstVessel\r\n            data.firstVoyage = this.form.firstVoyage\r\n            data.firstCyOpenTime = this.form.firstCyOpenTime\r\n            data.firstCyClosingTime = this.form.firstCyClosingTime\r\n            data.firstEtd = this.form.firstEtd\r\n            data.basicVessel = this.form.basicVessel\r\n            data.basicVoyage = this.form.basicVoyage\r\n            data.basicFinalGateinTime = this.form.basicFinalGateinTime\r\n            data.basicEtd = this.form.basicEtd\r\n            data.podEta = this.form.podEta\r\n            data.destinationPortEta = this.form.destinationPortEta\r\n            // data.carrierId = this.carrierIds\r\n            data.inquiryScheduleSummary = this.form.inquiryScheduleSummary\r\n            data.polBookingAgent = this.form.polBookingAgent\r\n            data.podHandleAgent = this.form.podHandleAgent\r\n            data.serviceTypeIdList = this.form.serviceTypeIdList\r\n            data.sqdSoNoSum = this.form.sqdSoNoSum\r\n            data.sqdMblNoSum = this.form.sqdMblNoSum\r\n            data.sqdContainersSealsSum = this.form.sqdContainersSealsSum\r\n            data.sqdIssueType = this.form.sqdIssueType\r\n            data.sqdExportCustomsType = this.form.sqdExportCustomsType\r\n            data.sqdTrailerType = this.form.sqdTrailerType\r\n            data.rctProcessStatusSummary = this.form.rctProcessStatusSummary\r\n            data.transportStatusSummary = this.form.transportStatusSummary\r\n            data.docStatusSummary = this.form.docStatusSummary\r\n            data.paymentReceivingStatusSummary = this.form.paymentReceivingStatusSummary\r\n            data.paymentPayingStatusSummary = this.form.paymentPayingStatusSummary\r\n            data.transportStatus = this.form.transportStatus\r\n            data.docStatus = this.form.docStatus\r\n            data.paymentPayingStatus = this.form.paymentPayingStatus\r\n            data.rctProcessId = this.form.rctProcessId\r\n            data.porcessId = this.form.porcessId\r\n            data.porcessStatusId = this.form.porcessStatusId\r\n            data.processStatusTime = this.form.processStatusTime\r\n            data.processRemark = this.form.processRemark\r\n            data.qoutationNo = this.form.qoutationNo\r\n            data.qoutationSketch = this.form.qoutationSketch\r\n            data.salesId = this.form.salesId\r\n            data.qoutationTime = this.form.qoutationTime\r\n            data.newBookingNo = this.form.newBookingNo\r\n            data.newBookingRemark = this.form.newBookingRemark\r\n            data.salesAssistantId = this.form.salesAssistantId\r\n            data.salesObserverId = this.form.salesObserverId\r\n            data.newBookingTime = this.form.newBookingTime\r\n            data.inquiryNoticeSum = this.form.inquiryNoticeSum\r\n            data.inquiryInnerRemarkSum = this.form.inquiryInnerRemarkSum\r\n            data.verifyPsaId = this.form.verifyPsaId\r\n            data.psaVerifyTime = this.form.psaVerifyTime\r\n            data.opLeaderNotice = this.form.opLeaderNotice\r\n            data.opInnerRemark = this.form.opInnerRemark\r\n            data.opId = this.form.opId\r\n            data.bookingOpId = this.form.bookingOpId\r\n            data.docOpId = this.form.docOpId\r\n            data.opObserverId = this.form.opObserverId\r\n\r\n            // 修改\r\n            if (this.form.rctId != null) {\r\n              this.form.processStatusId = 3\r\n              // updateRct(this.form).then(response => {\r\n              updateRct(data).then(response => {\r\n                this.saveAll(this.form.rctId)\r\n                this.$modal.msgSuccess('修改成功')\r\n                this.open = false\r\n                this.getRctList()\r\n              })\r\n              // 新增\r\n            } else {\r\n              this.form.processStatusId = 1\r\n              // addRct(this.form).then(response => {\r\n              addRct(data).then(response => {\r\n                this.form.rctId = response.data\r\n                this.saveAll(response.data)\r\n                this.$modal.msgSuccess('新增成功')\r\n                this.open = false\r\n                this.getRctList()\r\n              })\r\n            }\r\n          }\r\n        }\r\n        // 是否会插入一个订舱单记录\r\n        if (this.type == 'booking') {\r\n          if (v == 'saveCopy') {\r\n            this.form.bookingId = null\r\n          }\r\n          if (valid) {\r\n            if (this.form.bookingId != null) {\r\n              this.form.processStatusId = 3\r\n              updateBooking(this.form).then(response => {\r\n                if (!this.psaVerify) {\r\n                  this.saveAll(this.form.bookingId)\r\n                  this.$modal.msgSuccess('修改成功')\r\n                  this.open = false\r\n                  this.getBookingList()\r\n                }\r\n              })\r\n            } else {\r\n              this.form.processStatusId = 1\r\n              addBooking(this.form).then(response => {\r\n                this.form.bookingId = response.data\r\n                this.saveAll(response.data)\r\n                this.$modal.msgSuccess('新增成功')\r\n                this.open = false\r\n                this.getBookingList()\r\n              })\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    rejected() {\r\n      this.$confirm('确认单据后不可更改，是否确认？', '', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning',\r\n        customClass: 'modal-confirm'\r\n      }).then(res => {\r\n        if (res == 'confirm') {\r\n          this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n          this.$refs['form'].validate(valid => {\r\n            this.form.isPsaVerified = 1\r\n            this.form.processStatusId = 9\r\n            if (valid) {\r\n              updateBooking(this.form).then(response => {\r\n                this.$modal.msgSuccess('修改成功')\r\n                this.open = false\r\n                this.getBookingList()\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /**\r\n     * 保存所有基础服务信息\r\n     * @param id rctId/bookingId\r\n     */\r\n    saveAll(id) {\r\n      this.saveLogistics(id)\r\n      this.savePreCarriage(id)\r\n      this.saveExportDeclaration(id)\r\n      this.saveImportClearance(id)\r\n    },\r\n    // 保存基础物流信息,\r\n    saveLogistics(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.logisticsReceivablePayableList.length > 0) {\r\n            this.logisticsBasicInfo.rsBookingReceivablePayableList = this.logisticsReceivablePayableList\r\n          }\r\n          this.logisticsBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.logisticsBasicInfo.typeId = 1\r\n          this.form.rsBookingLogisticsTypeBasicInfo = this.logisticsBasicInfo\r\n          saveBookingLogistics(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.logisticsReceivablePayableList.length > 0) {\r\n            this.logisticsBasicInfo.rsRctReceivablePayableList = this.logisticsReceivablePayableList\r\n          }\r\n          if (this.logisticsNoInfo.length > 0) {\r\n            this.logisticsBasicInfo.rsRctLogisticsNoInfos = this.logisticsNoInfo\r\n          }\r\n          if (this.logisticsOpHistory.length > 0) {\r\n            this.logisticsBasicInfo.rsOperationalProcessList = this.logisticsOpHistory\r\n          }\r\n          this.logisticsBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.logisticsBasicInfo.typeId = 1\r\n          this.form.rsRctLogisticsTypeBasicInfo = this.logisticsBasicInfo\r\n          this.form.rsBasicLogistics = this.logisticsBasicInfo\r\n          // 先记录子实例服务\r\n          let rsServiceInstances = {}\r\n          rsServiceInstances.serviceTypeId = this.logisticsBasicInfo.typeId\r\n          rsServiceInstances.rctId = id\r\n          rsServiceInstances.rctNo = this.form.rctNo\r\n          rsServiceInstances.supplierSummary = this.form.supplierSummary\r\n          // rsServiceInstances.supplierContact=\r\n          rsServiceInstances.inquiryLeatestUpdatedTime = this.form.createTime\r\n          rsServiceInstances.inquiryNo = this.form.inquiryNo\r\n          // rsServiceInstances.agreementTypeCode\r\n          // rsServiceInstances.agreementNo\r\n          rsServiceInstances.maxWeight = this.form.grossWeight\r\n          rsServiceInstances.serviceBelongTo = 'logistics'\r\n          this.form.rsServiceInstances = rsServiceInstances\r\n\r\n          // 服务实例记录与前程运输等子服务一起组成一个事物\r\n          addBasiclogistics(this.form).then(response => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    // 保存前程运输信息\r\n    savePreCarriage(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.preCarriageReceivablePayableList.length > 0) {\r\n            this.preCarriageBasicInfo.rsBookingReceivablePayableList = this.preCarriageReceivablePayableList\r\n          }\r\n          this.preCarriageBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.preCarriageBasicInfo.typeId = 4\r\n          this.form.rsBookingPreCarriageBasicInfo = this.preCarriageBasicInfo\r\n          saveBookingPreCarriage(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.preCarriageReceivablePayableList.length > 0) {\r\n            this.preCarriageBasicInfo.rsRctReceivablePayableList = this.preCarriageReceivablePayableList\r\n          }\r\n          if (this.preCarriageNoInfo.length > 0) {\r\n            this.preCarriageBasicInfo.rsRctPreCarriageNoInfos = this.preCarriageNoInfo\r\n          }\r\n          if (this.preCarriageOpHistory.length > 0) {\r\n            this.preCarriageBasicInfo.rsOperationalProcessList = this.preCarriageOpHistory\r\n          }\r\n          this.preCarriageBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.preCarriageBasicInfo.typeId = 4\r\n          this.form.rsRctPreCarriageBasicInfo = this.preCarriageBasicInfo\r\n          // 先保存子服务实例\r\n          saveRctPreCarriage(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    // 保存出口报关信息\r\n    saveExportDeclaration(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.exportDeclarationReceivablePayableList.length > 0) {\r\n            this.exportDeclarationBasicInfo.rsBookingReceivablePayableList = this.exportDeclarationReceivablePayableList\r\n          }\r\n          this.exportDeclarationBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.exportDeclarationBasicInfo.typeId = 5\r\n          this.form.rsBookingExportDeclarationBasicInfo = this.exportDeclarationBasicInfo\r\n          saveBookingExportDeclaration(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.exportDeclarationReceivablePayableList.length > 0) {\r\n            this.exportDeclarationBasicInfo.rsRctReceivablePayableList = this.exportDeclarationReceivablePayableList\r\n          }\r\n          if (this.exportDeclarationOpHistory.length > 0) {\r\n            this.exportDeclarationBasicInfo.rsOperationalProcessList = this.exportDeclarationOpHistory\r\n          }\r\n          this.exportDeclarationBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.exportDeclarationBasicInfo.typeId = 5\r\n          this.form.rsRctExportDeclarationBasicInfo = this.exportDeclarationBasicInfo\r\n          saveRctExportDeclaration(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    // 保存进口清关信息\r\n    saveImportClearance(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.importClearanceReceivablePayableList.length > 0) {\r\n            this.importClearanceBasicInfo.rsBookingReceivablePayableList = this.importClearanceReceivablePayableList\r\n          }\r\n          this.importClearanceBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.importClearanceBasicInfo.typeId = 6\r\n          this.form.rsBookingImportClearanceBasicInfo = this.importClearanceBasicInfo\r\n          saveBookingImportClearance(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.importClearanceReceivablePayableList.length > 0) {\r\n            this.importClearanceBasicInfo.rsRctReceivablePayableList = this.importClearanceReceivablePayableList\r\n          }\r\n          if (this.importClearanceOpHistory.length > 0) {\r\n            this.importClearanceBasicInfo.rsOperationalProcessList = this.importClearanceOpHistory\r\n          }\r\n          this.importClearanceBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.importClearanceBasicInfo.typeId = 6\r\n          this.form.rsRctImportClearanceBasicInfo = this.importClearanceBasicInfo\r\n          saveRctImportClearance(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    // 将应收应付列表的一条数据装换成charge表的两条数据（应收，应付）\r\n    transformToCharge(serviceId, receivablePayableList) {\r\n      this.logisticsBasicInfo.rsRctReceivablePayableList.map(obj => {\r\n        let charge1 = {}\r\n        charge1.serviceId = response.data\r\n        charge1.sqdServiceTypeId = obj.typeId\r\n        charge1.sqdRctNo = this.form.rctNo\r\n        charge1.isRecievingOrPaying = '0' //应收\r\n        charge1.clearingCompanyId = obj.clientId\r\n        charge1.clearingCompanySummary = ''\r\n        charge1.quotationStrategyId = obj.quotationStrategyId\r\n        charge1.dnChargeNameId = obj.quotationChargeId\r\n        charge1.dnCurrencyCode = obj.quotationCurrencyCode\r\n        charge1.dnUnitRate = obj.quotationRate\r\n        charge1.dnUnitCode = obj.quotationUnitCode\r\n        charge1.dnAmount = obj.quotationAmount\r\n        charge1.basicCurrencyRate = obj.quotationExchangeRate\r\n        charge1.dutyRate = obj.quotationTaxRate\r\n        /* charge1.subtotal= ''\r\n        charge1.clearingCurrencyCode= obj.\r\n        charge1.dnCurrencyReceived= obj.\r\n        charge1.dnCurrencyPaid= obj.\r\n        charge1.dnCurrencyBalance= obj.\r\n        charge1.accountReceivedIdList= obj.\r\n        charge1.accountPaidIdList= obj.\r\n        charge1.logisticsInvoiceIdList= obj. */\r\n        this.chargeList.push(charge1)\r\n        let charge2 = {}\r\n        charge2.serviceId = response.data\r\n        charge2.sqdServiceTypeId = obj.typeId\r\n        charge2.sqdRctNo = this.form.rctNo\r\n        charge2.isRecievingOrPaying = '1' //应付\r\n        charge2.clearingCompanyId = obj.supplierId\r\n        charge2.clearingCompanySummary = ''\r\n        charge2.quotationStrategyId = obj.inquiryStrategyId\r\n        charge2.dnChargeNameId = obj.inquiryChargeId\r\n        charge2.dnCurrencyCode = obj.inquiryCurrencyCode\r\n        charge2.dnUnitRate = obj.inquiryRate\r\n        charge2.dnUnitCode = obj.inquiryUnitCode\r\n        charge2.dnAmount = obj.inquiryAmount\r\n        charge2.basicCurrencyRate = obj.inquiryExchangeRate\r\n        charge2.dutyRate = obj.inquiryTaxRate\r\n        /* charge1.subtotal= ''\r\n        charge1.clearingCurrencyCode= obj.\r\n        charge1.dnCurrencyReceived= obj.\r\n        charge1.dnCurrencyPaid= obj.\r\n        charge1.dnCurrencyBalance= obj.\r\n        charge1.accountReceivedIdList= obj.\r\n        charge1.accountPaidIdList= obj.\r\n        charge1.logisticsInvoiceIdList= obj. */\r\n        this.chargeList.push(charge2)\r\n      })\r\n    },\r\n    // 加载表单中的多选列表的选项\r\n    loadSelection() {\r\n      // 加载货运类型\r\n      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {\r\n        this.$store.dispatch('getServiceTypeList')\r\n      }\r\n      // 加载公司列表\r\n      if (this.$store.state.data.clientList.length == 0 || this.$store.state.data.redisList.client) {\r\n        this.$store.dispatch('getClientList')\r\n      }\r\n      // 加载供应商列表\r\n      if (this.$store.state.data.supplierList.length == 0 || this.$store.state.data.redisList.supplier) {\r\n        this.$store.dispatch('getSupplierList')\r\n      }\r\n      this.loadOp()\r\n      this.loadCarrier()\r\n      this.loadSales()\r\n      this.loadBusinesses()\r\n    },\r\n    // 查询操作部用户\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        store.dispatch('getOpList').then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n      }\r\n    },\r\n    // 查询业务部用户\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch('getSalesList').then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    // 查询船公司列表\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch('getServiceTypeCarriersList').then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    // 查询商务部用户\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch('getBusinessesList').then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + ',' + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + ',' + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + ' ' + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + ' ' + node.staff.staffGivingEnName + ',' + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + ' ' + node.serviceEnName + ',' + pinyin.getFullChars(node.serviceLocalName != undefined ? node.serviceLocalName : '')\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : '') + ' ' + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : '') + ' ' + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : '') + ',' + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : ''))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children\r\n      }\r\n    },\r\n    cancel() {\r\n      this.reset()\r\n      this.loading = false\r\n      this.open = false\r\n      this.openGenerateRct = false\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        rctId: null,\r\n        rctNo: null,\r\n        rctOpDate: null,\r\n        opId: null,\r\n        bookingOpId: null,\r\n        docOpId: null,\r\n        opObserverId: null,\r\n        newBookingNo: null,\r\n        newBookingTime: null,\r\n        quotationNo: null,\r\n        quotationDate: null,\r\n        salesId: null,\r\n        salesAssistantId: null,\r\n        salesObserverId: null,\r\n        verifyPsaId: null,\r\n        psaVerifyTime: null,\r\n        urgencyDegree: null,\r\n        paymentTypeId: null,\r\n        releaseTypeId: null,\r\n        processStatusId: null,\r\n        clientId: null,\r\n        clientRoleId: null,\r\n        clientContactor: null,\r\n        clientContactorTel: null,\r\n        clientContactorEmail: null,\r\n        relationClientIds: [],\r\n        impExpTypeId: null,\r\n        tradingPaymentChannelId: null,\r\n        tradingTermsId: null,\r\n        logisticsTermsId: null,\r\n        clientContractNo: null,\r\n        clientInvoiceNo: null,\r\n        goodsNameSummary: null,\r\n        packageQuantity: null,\r\n        grossWeight: null,\r\n        weightUnitId: null,\r\n        volume: null,\r\n        volumeUnitId: null,\r\n        cargoTypeIds: [],\r\n        goodsValue: null,\r\n        goodsCurrencyId: null,\r\n        maxWeight: null,\r\n        revenueTons: null,\r\n        logisticsTypeId: null,\r\n        polId: null,\r\n        destinationPortId: null,\r\n        carrierIds: [],\r\n        schedule: null,\r\n        validTimeForm: null,\r\n        isMblNeeded: 0,\r\n        mblNo: null,\r\n        isUnderAgreementMbl: 0,\r\n        isCustomsIntransitMbl: 0,\r\n        isSwitchMbl: 0,\r\n        isDividedMbl: 0,\r\n        mblIssueTypeId: null,\r\n        mblGetWayId: null,\r\n        mblReleaseWayId: null,\r\n        isHblNeeded: 0,\r\n        hblNoList: null,\r\n        isUnderAgreementHbl: 0,\r\n        isCustomsIntransitHbl: 0,\r\n        isSwitchHbl: 0,\r\n        isDividedHbl: 0,\r\n        hblIssueTypeId: null,\r\n        hblGetWayId: null,\r\n        hblReleaseWayId: null,\r\n        serviceTypeIds: [],\r\n        quotationSummary: null,\r\n        newBookingRemark: null,\r\n        inquiryNotice: null,\r\n        inquiryInnerRemark: null,\r\n        opLeaderRemark: null,\r\n        opInnerRemark: null,\r\n        agreementTypeId: null,\r\n        agreementNo: null,\r\n        readOnly: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        deleteTime: null,\r\n        deleteStatus: '0',\r\n        deleteBy: null,\r\n        updateBy: null,\r\n        createBy: null,\r\n        soNo: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        bookingDetail: null,\r\n        precarriageTime: null,\r\n        cvClosingTime: null,\r\n        cvDeclaringTime: null,\r\n        vgm: null,\r\n        siClosingTime: null,\r\n        trailer: null,\r\n        shipName: null,\r\n        shipTime: null,\r\n        podETA: null,\r\n        telexReleaseType: null,\r\n        isReleasable: null,\r\n        sendToAgent: null,\r\n        boatId: null,\r\n        remark: null,\r\n        supplierSummary: ''\r\n      }\r\n      this.logisticsBasicInfo = {\r\n        logisticsTypeInfoId: null,\r\n        rctId: null,\r\n        logisticsTypeId: null,\r\n        carrierId: null,\r\n        polId: null,\r\n        firstCvClosingTime: null,\r\n        firstCyOpenTime: null,\r\n        firstCyClosingTime: null,\r\n        firstEtd: null,\r\n        firstVessel: null,\r\n        firstVoyage: null,\r\n        localBasicPortId: null,\r\n        basicClosingTime: null,\r\n        basicFinalGateinTime: null,\r\n        basicEtd: null,\r\n        basicVessel: null,\r\n        basicVoyage: null,\r\n        transitPortId: null,\r\n        podId: null,\r\n        podEta: null,\r\n        destinationPortId: null,\r\n        destinationPortEta: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.preCarriageBasicInfo = {\r\n        preCarriageInfoId: null,\r\n        rctId: null,\r\n        logisticsTypeId: null,\r\n        preCarriageRegionId: null,\r\n        preCarriageAddress: null,\r\n        preCarriageTime: null,\r\n        preCarriageContact: null,\r\n        preCarriageTel: null,\r\n        preCarriageRemark: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.exportDeclarationBasicInfo = {\r\n        exportDeclarationId: null,\r\n        rctId: null,\r\n        logisticsTypeId: null,\r\n        dispatchRegionId: null,\r\n        dispatchAddress: null,\r\n        dispatchTime: null,\r\n        dispatchContact: null,\r\n        dispatchTel: null,\r\n        dispatchRemark: null,\r\n        dispatchDriverName: null,\r\n        dispatchDriverTel: null,\r\n        dispatchTruckNo: null,\r\n        dispatchTruckRemark: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.importClearanceBasicInfo = {\r\n        importClearanceId: null,\r\n        rctId: null,\r\n        exportCustomsTypeId: null,\r\n        importCustomsTypeId: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.grossWeight = 0\r\n      this.goodsValue = 0\r\n      this.carrierId = null\r\n      this.relationClientIds = []\r\n      this.verifyPsaId = null\r\n      this.salesId = null\r\n      this.salesAssistantId = null\r\n      this.salesObserverId = null\r\n      this.opId = null\r\n      this.bookingOpId = null\r\n      this.docOpId = null\r\n      this.opObserverId = null\r\n      this.carrierIds = []\r\n      this.preCarriage = false\r\n      this.importClearance = false\r\n      this.exportDeclaration = false\r\n      this.logisticsProcess = []\r\n      this.logisticsNoInfo = []\r\n      this.openLogisticsNoInfo = false\r\n      this.logisticsOpHistory = []\r\n      this.logisticsReceivablePayableList = []\r\n      this.preCarriageNoInfo = []\r\n      this.openPreCarriageNoInfo = false\r\n      this.preCarriageOpHistory = []\r\n      this.preCarriageReceivablePayableList = []\r\n      this.openExportDeclarationNoInfo = false\r\n      this.exportDeclarationNoInfo = []\r\n      this.exportDeclarationOpHistory = []\r\n      this.exportDeclarationReceivablePayableList = []\r\n      this.openImportPassNoInfo = false\r\n      this.importClearanceNoInfo = []\r\n      this.importClearanceOpHistory = []\r\n      this.importClearanceReceivablePayableList = []\r\n      this.chargeList = []\r\n      this.resetForm('form')\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    /**\r\n     * 生成Rct号\r\n     * @param v\r\n     */\r\n    generateRct(v) {\r\n      if (v) {\r\n        getRctMon().then(v => {\r\n          let num = v.data\r\n          if (num.toString().length < 4) {\r\n            const j = 4 - (num.toString().length)\r\n            for (let i = 0; i < j; i++) {\r\n              num = '0' + num\r\n            }\r\n          }\r\n          let date = new Date()\r\n          let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n          let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n          this.rct.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? '0' + month : month) + num.toString()\r\n        })\r\n      } else {\r\n        this.openGenerateRct = true\r\n      }\r\n    },\r\n    confirmRct() {\r\n      this.form.rctNo = this.rct.rctNo\r\n      this.openGenerateRct = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\r\n.spc {\r\n  ::v-deep.el-form-item__content {\r\n    padding-left: 0;\r\n  }\r\n}\r\n\r\n.booking {\r\n  ::v-deep.el-input__inner,\r\n  ::v-deep.el-textarea__inner,\r\n  ::v-deep.vue-treeselect__control,\r\n  ::v-deep.el-input__count,\r\n  ::v-deep.el-range-input {\r\n    background-color: gainsboro !important;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n@keyframes slide-down {\r\n  0% {\r\n    transform: scale(1, 0);\r\n  }\r\n  100% {\r\n    transform: scale(1, 1);\r\n  }\r\n}\r\n\r\n.active {\r\n  max-height: 500px;\r\n  transition: max-height .3s ease-in;\r\n  transform-origin: 50% 0;\r\n  animation: slide-down 0.3s ease-in;\r\n  -webkit-animation: slide-down 0.3s ease-in;\r\n}\r\n\r\n.inactive {\r\n  max-height: 0;\r\n  overflow: auto;\r\n  transition: max-height .3s ease-out;\r\n}\r\n\r\n.show {\r\n  display: contents;\r\n\r\n  &.invisible {\r\n    display: none;\r\n  }\r\n\r\n  &.visible {\r\n\r\n  }\r\n\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AA8uCA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,cAAA,GAAAF,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAK,gBAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,kBAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,UAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,kBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,MAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AAQA,IAAAW,QAAA,GAAAX,OAAA;AAUA,IAAAY,KAAA,GAAAZ,OAAA;AACA,IAAAa,MAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,IAAA,GAAAd,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAe,IAAA;EACAC,KAAA;EACAC,KAAA;EACAC,UAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;MACAC,YAAA;MACAC,UAAA;MACAC,WAAA;MACAC,eAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,IAAA,MAAAC,GAAA;MACAC,aAAA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACAI,KAAA;MACAC,aAAA;MACAC,SAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,IAAA;MACAC,WAAA;MACAC,OAAA;MACAC,YAAA;MACA;MACAC,eAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,MAAA,OAAAC,IAAA;MACArC,SAAA,OAAAqC,IAAA;MACApC,iBAAA;MACAC,KAAA,OAAAmC,IAAA;MACAC,IAAA;MACAC,OAAA;MACAC,WAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,kBAAA;MACAC,8BAAA;MACAC,iBAAA;MACAC,qBAAA;MACAC,qBAAA;MACAC,oBAAA;MACAC,gCAAA;MACAC,2BAAA;MACAC,uBAAA;MACAC,2BAAA;MACAC,0BAAA;MACAC,sCAAA;MACAC,oBAAA;MACAC,qBAAA;MACAC,yBAAA;MACAC,wBAAA;MACAC,oCAAA;MACA;MACAC,WAAA;MACAC,OAAA;MACAC,IAAA;MACAC,kBAAA;MACAC,oBAAA;MACAC,0BAAA;MACAC,wBAAA;MACAC,GAAA;QACAC,gBAAA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACA;MACAC,KAAA;MACAC,UAAA;IACA;EAEA;EACAC,KAAA;IACA,iCAAAC,oBAAAC,CAAA;MAAA,IAAAC,KAAA;MACA,SAAApE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiF,eAAA,CAAAC,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAC,WAAA;QACAC,cAAA,CAAAC,QAAA,uBAAAC,IAAA;UACAP,KAAA,CAAAQ,OAAA,CAAAT,CAAA;QACA;MACA;QACA,KAAAS,OAAA,CAAAT,CAAA;MACA;IACA;EACA;EACAU,WAAA,WAAAA,YAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,KAAA;IACA;IACA,SAAA1D,IAAA;MACA;MACA,KAAA2D,cAAA,GAAAL,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAA5D,IAAA;MACA;MACA,KAAA6D,UAAA,GAAAP,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAC,EAAA,SAAAhE,IAAA;MACA,KAAAiE,YAAA,MAAAH,MAAA,CAAAC,KAAA,CAAAC,EAAA,EAAAV,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAG,GAAA;MACA,KAAAC,gBAAA,MAAAL,MAAA,CAAAC,KAAA,CAAAG,GAAA,EAAAZ,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAK,GAAA;MACA,KAAAC,YAAA,MAAAP,MAAA,CAAAC,KAAA,CAAAK,GAAA,EAAAd,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAnE,SAAA;MACA,KAAAA,SAAA;IACA;EACA;EACA0E,OAAA;IACA;IACAL,YAAA,WAAAA,aAAAD,EAAA;MAAA,IAAAO,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAAb,KAAA;cAAAqB,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAhB,uBAAA,EAAAD,EAAA,EAAAV,IAAA,WAAA4B,QAAA;gBACAX,MAAA,CAAA1C,IAAA,CAAAsD,eAAA,GAAAD,QAAA,CAAAnH,IAAA,CAAAoH,eAAA;gBACAZ,MAAA,CAAA1C,IAAA,CAAAzC,OAAA,GAAA8F,QAAA,CAAAnH,IAAA,CAAAqH,OAAA;gBACAb,MAAA,CAAA1C,IAAA,CAAAwD,QAAA,GAAAH,QAAA,CAAAnH,IAAA,CAAAuH,SAAA;gBACAf,MAAA,CAAA1C,IAAA,CAAA0D,YAAA,GAAAL,QAAA,CAAAnH,IAAA,CAAAyH,aAAA;gBACAjB,MAAA,CAAA1C,IAAA,CAAA4D,eAAA,GAAAP,QAAA,CAAAnH,IAAA,CAAA2H,YAAA;gBACAnB,MAAA,CAAA1C,IAAA,CAAA8D,kBAAA,GAAAT,QAAA,CAAAnH,IAAA,CAAA6H,gBAAA;gBACArB,MAAA,CAAA1C,IAAA,CAAAgE,oBAAA,GAAAX,QAAA,CAAAnH,IAAA,CAAA+H,uBAAA;gBACAvB,MAAA,CAAA1C,IAAA,CAAAkE,WAAA,GAAAb,QAAA,CAAAnH,IAAA,CAAAiI,MAAA;gBACAzB,MAAA,CAAA1C,IAAA,CAAAoE,aAAA,OAAAC,IAAA;gBACA3B,MAAA,CAAA1C,IAAA,CAAAsE,YAAA,GAAAjB,QAAA,CAAAnH,IAAA,CAAAqI,QAAA;gBACA7B,MAAA,CAAA1C,IAAA,CAAAwE,gBAAA,GAAAnB,QAAA,CAAAnH,IAAA,CAAAuI,SAAA;gBACA/B,MAAA,CAAA1C,IAAA,CAAAxD,UAAA,GAAA6G,QAAA,CAAAnH,IAAA,CAAAwI,UAAA;gBACAhC,MAAA,CAAA1C,IAAA,CAAA2E,eAAA,GAAAtB,QAAA,CAAAnH,IAAA,CAAA0I,eAAA;gBACAlC,MAAA,CAAA1C,IAAA,CAAAvD,WAAA,GAAA4G,QAAA,CAAAnH,IAAA,CAAAO,WAAA;gBACAiG,MAAA,CAAA1C,IAAA,CAAA6E,YAAA,GAAAxB,QAAA,CAAAnH,IAAA,CAAA4I,WAAA;gBACApC,MAAA,CAAA1C,IAAA,CAAA+E,KAAA,GAAA1B,QAAA,CAAAnH,IAAA,CAAA8I,WAAA;gBACAtC,MAAA,CAAA1C,IAAA,CAAAiF,iBAAA,GAAA5B,QAAA,CAAAnH,IAAA,CAAAgJ,aAAA;gBACAxC,MAAA,CAAA1C,IAAA,CAAAmF,aAAA,GAAA9B,QAAA,CAAAnH,IAAA,CAAAkJ,qBAAA;gBACA1C,MAAA,CAAA1C,IAAA,CAAAqF,WAAA,GAAAhC,QAAA,CAAAnH,IAAA,CAAAmJ,WAAA;gBACA3C,MAAA,CAAA1C,IAAA,CAAAsF,gBAAA,GAAAjC,QAAA,CAAAnH,IAAA,CAAAqJ,MAAA;gBACA7C,MAAA,CAAA1C,IAAA,CAAAwF,SAAA,GAAAnC,QAAA,CAAAnH,IAAA,CAAAiI,MAAA;gBACA,IAAAzB,MAAA,CAAArG,UAAA,IAAAoJ,SAAA;kBAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAA/C,OAAA,EACAF,MAAA,CAAArG,UAAA;oBAAAuJ,KAAA;kBAAA;oBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAzE,CAAA,IAAA6E,IAAA;sBAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;sBACA,IAAAD,CAAA,CAAAE,QAAA,IAAAR,SAAA;wBAAA,IAAAS,UAAA,OAAAP,2BAAA,CAAA/C,OAAA,EACAmD,CAAA,CAAAE,QAAA;0BAAAE,MAAA;wBAAA;0BAAA,KAAAD,UAAA,CAAAL,CAAA,MAAAM,MAAA,GAAAD,UAAA,CAAAjF,CAAA,IAAA6E,IAAA;4BAAA,IAAAM,CAAA,GAAAD,MAAA,CAAAH,KAAA;4BACA,IAAAI,CAAA,CAAAH,QAAA,IAAAR,SAAA;8BAAA,IAAAY,UAAA,OAAAV,2BAAA,CAAA/C,OAAA,EACAwD,CAAA,CAAAH,QAAA;gCAAAK,MAAA;8BAAA;gCAAA,KAAAD,UAAA,CAAAR,CAAA,MAAAS,MAAA,GAAAD,UAAA,CAAApF,CAAA,IAAA6E,IAAA;kCAAA,IAAAS,CAAA,GAAAD,MAAA,CAAAN,KAAA;kCACA,IAAAO,CAAA,CAAAhD,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAqH,OAAA;oCACAb,MAAA,CAAAnF,OAAA,GAAAgJ,CAAA,CAAAC,MAAA;kCACA;gCACA;8BAAA,SAAAC,GAAA;gCAAAJ,UAAA,CAAAK,CAAA,CAAAD,GAAA;8BAAA;gCAAAJ,UAAA,CAAAM,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAAP,UAAA,CAAAQ,CAAA,CAAAD,GAAA;wBAAA;0BAAAP,UAAA,CAAAS,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAf,SAAA,CAAAgB,CAAA,CAAAD,GAAA;kBAAA;oBAAAf,SAAA,CAAAiB,CAAA;kBAAA;gBACA;gBACA,IAAAC,IAAA,OAAAjK,GAAA;gBAAA,IAAAkK,UAAA,OAAAlB,2BAAA,CAAA/C,OAAA,EACAF,MAAA,CAAApG,WAAA;kBAAAwK,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAAhB,CAAA,MAAAiB,MAAA,GAAAD,UAAA,CAAA5F,CAAA,IAAA6E,IAAA;oBAAA,IAAAiB,CAAA,GAAAD,MAAA,CAAAd,KAAA;oBACA,IAAAe,CAAA,CAAAd,QAAA,IAAAR,SAAA,IAAAsB,CAAA,CAAAd,QAAA,CAAA7E,MAAA;sBAAA,IAAA4F,UAAA,OAAArB,2BAAA,CAAA/C,OAAA,EACAmE,CAAA,CAAAd,QAAA;wBAAAgB,MAAA;sBAAA;wBAAA,KAAAD,UAAA,CAAAnB,CAAA,MAAAoB,MAAA,GAAAD,UAAA,CAAA/F,CAAA,IAAA6E,IAAA;0BAAA,IAAAC,EAAA,GAAAkB,MAAA,CAAAjB,KAAA;0BACA,IAAAD,EAAA,CAAAmB,OAAA,YAAAnB,EAAA,CAAAmB,OAAA,CAAA/J,SAAA,YAAA4I,EAAA,CAAAmB,OAAA,CAAA/J,SAAA,IAAAsI,SAAA,IAAApC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,YAAAiG,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAAgE,MAAA;4BACA,IAAAiC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAA+J,QAAA,CAAApB,EAAA,CAAAmB,OAAA,CAAA/J,SAAA;8BACAyJ,IAAA,CAAAQ,GAAA,CAAArB,EAAA,CAAAsB,aAAA;4BACA;0BACA;0BACA,IAAAtB,EAAA,CAAAE,QAAA,IAAAR,SAAA,IAAAM,EAAA,CAAAE,QAAA,CAAA7E,MAAA;4BAAA,IAAAkG,UAAA,OAAA3B,2BAAA,CAAA/C,OAAA,EACAmD,EAAA,CAAAE,QAAA;8BAAAsB,MAAA;4BAAA;8BAAA,KAAAD,UAAA,CAAAzB,CAAA,MAAA0B,MAAA,GAAAD,UAAA,CAAArG,CAAA,IAAA6E,IAAA;gCAAA,IAAAM,EAAA,GAAAmB,MAAA,CAAAvB,KAAA;gCACA,IAAAI,EAAA,CAAAc,OAAA,YAAAd,EAAA,CAAAc,OAAA,CAAA/J,SAAA,YAAAiJ,EAAA,CAAAc,OAAA,CAAA/J,SAAA,IAAAsI,SAAA,IAAApC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,YAAAiG,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAAgE,MAAA;kCACA,IAAAiC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAA+J,QAAA,CAAAf,EAAA,CAAAc,OAAA,CAAA/J,SAAA;oCACAyJ,IAAA,CAAAQ,GAAA,CAAAhB,EAAA,CAAAiB,aAAA;kCACA;gCACA;8BACA;4BAAA,SAAAZ,GAAA;8BAAAa,UAAA,CAAAZ,CAAA,CAAAD,GAAA;4BAAA;8BAAAa,UAAA,CAAAX,CAAA;4BAAA;0BACA;wBACA;sBAAA,SAAAF,GAAA;wBAAAO,UAAA,CAAAN,CAAA,CAAAD,GAAA;sBAAA;wBAAAO,UAAA,CAAAL,CAAA;sBAAA;oBACA;kBACA;gBAAA,SAAAF,GAAA;kBAAAI,UAAA,CAAAH,CAAA,CAAAD,GAAA;gBAAA;kBAAAI,UAAA,CAAAF,CAAA;gBAAA;gBACA,IAAAC,IAAA,CAAA/J,IAAA;kBACA+J,IAAA,CAAAY,OAAA,WAAAjB,CAAA;oBACA7D,MAAA,CAAAtF,UAAA,CAAAqK,IAAA,CAAAlB,CAAA;kBACA;gBACA;gBACA,IAAAmB,OAAA;gBAAA,IAAAC,UAAA,OAAAhC,2BAAA,CAAA/C,OAAA,EACAS,QAAA,CAAAuE,gBAAA;kBAAAC,MAAA;gBAAA;kBAAA,KAAAF,UAAA,CAAA9B,CAAA,MAAAgC,MAAA,GAAAF,UAAA,CAAA1G,CAAA,IAAA6E,IAAA;oBAAA,IAAAgC,EAAA,GAAAD,MAAA,CAAA7B,KAAA;oBACA0B,OAAA,KAAAI,EAAA,CAAAC,MAAA,WAAAD,EAAA,CAAAC,MAAA,iBAAAD,EAAA,CAAAE,iBAAA,WAAAF,EAAA,CAAAE,iBAAA,CAAAC,WAAA,WAAAC,MAAA,CAAAJ,EAAA,CAAAK,aAAA,KAAAL,EAAA,CAAAM,IAAA,iBAAAN,EAAA,CAAAM,IAAA;oBACAN,EAAA,CAAAO,UAAA;oBACAP,EAAA,CAAAQ,YAAA;oBACAR,EAAA,CAAAS,mBAAA;oBACAT,EAAA,CAAAU,cAAA;oBACAV,EAAA,CAAAW,qBAAA;oBACAX,EAAA,CAAAY,gBAAA;oBACAZ,EAAA,CAAAa,iBAAA;oBACAb,EAAA,CAAAc,YAAA;oBACAd,EAAA,CAAAe,mBAAA,GAAAf,EAAA,CAAAgB,UAAA;oBACAhB,EAAA,CAAAiB,iBAAA,GAAAjB,EAAA,CAAAkB,QAAA;oBACAlB,EAAA,CAAAmB,eAAA,GAAAnB,EAAA,CAAAC,MAAA;oBACAD,EAAA,CAAAoB,YAAA,GAAApB,EAAA,CAAAkB,QAAA;oBACAlB,EAAA,CAAAqB,eAAA,GAAArB,EAAA,CAAAkB,QAAA;oBACAlB,EAAA,CAAAsB,UAAA,GAAAtB,EAAA,CAAAC,MAAA;oBACAD,EAAA,CAAAuB,aAAA,GAAAvB,EAAA,CAAAC,MAAA;oBACAD,EAAA,CAAAwB,eAAA,GAAAxB,EAAA,CAAAyB,MAAA;oBAEAzB,EAAA,CAAA0B,iBAAA,GAAA1B,EAAA,CAAA2B,QAAA;oBACA3B,EAAA,CAAA4B,aAAA,GAAA5B,EAAA,CAAAM,IAAA;oBACAN,EAAA,CAAA6B,UAAA,GAAA7B,EAAA,CAAAyB,MAAA;oBAEAzB,EAAA,CAAA8B,eAAA,GAAA9B,EAAA,CAAAM,IAAA;oBACAN,EAAA,CAAA+B,WAAA,GAAA/B,EAAA,CAAA+B,WAAA;oBACA/B,EAAA,CAAAgC,QAAA,GAAAhC,EAAA,CAAAM,IAAA;oBACAN,EAAA,CAAAiC,mBAAA,GAAAjC,EAAA,CAAAkC,YAAA;oBACAlC,EAAA,CAAAmC,mBAAA,GAAAnC,EAAA,CAAAoC,YAAA;oBACApC,EAAA,CAAAqC,qBAAA,GAAArC,EAAA,CAAAkC,YAAA;oBACAlC,EAAA,CAAAsC,iBAAA,GAAAtC,EAAA,CAAAgB,UAAA;oBACAhB,EAAA,CAAAuC,gBAAA,GAAAvC,EAAA,CAAAwC,OAAA;oBACAxC,EAAA,CAAAyC,cAAA,GAAAzC,EAAA,CAAAwC,OAAA;oBACAxC,EAAA,CAAAtE,QAAA,GAAAH,QAAA,CAAAnH,IAAA,CAAAuH,SAAA;oBACAqE,EAAA,CAAA0C,MAAA,GAAAnH,QAAA,CAAAnH,IAAA,CAAAuO,OAAA;oBACA/H,MAAA,CAAA1C,IAAA,CAAA0K,eAAA,IAAA5C,EAAA,CAAA6C,UAAA;oBACA7C,EAAA,CAAA6C,UAAA,GAAA7C,EAAA,CAAArE,SAAA;oBACAqE,EAAA,CAAA8C,QAAA,GAAA9C,EAAA,CAAA2C,OAAA;oBACA3C,EAAA,CAAA+C,cAAA,GAAA3C,MAAA,CAAAJ,EAAA,CAAAK,aAAA,IAAAD,MAAA,CAAAJ,EAAA,CAAAgD,eAAA,IAAA5C,MAAA,CAAAJ,EAAA,CAAAqC,qBAAA,SAAAjC,MAAA,CAAAJ,EAAA,CAAAuC,gBAAA;oBACAvC,EAAA,CAAAiD,SAAA,GAAA7C,MAAA,CAAAJ,EAAA,CAAA+B,WAAA,IAAA3B,MAAA,CAAAJ,EAAA,CAAAkD,aAAA,IAAA9C,MAAA,CAAAJ,EAAA,CAAAiC,mBAAA,SAAA7B,MAAA,CAAAJ,EAAA,CAAAyC,cAAA;oBACA,IAAAzC,EAAA,CAAAmD,MAAA,WAAAnD,EAAA,CAAAmD,MAAA,WAAAnD,EAAA,CAAAmD,MAAA;sBACA;sBACAvI,MAAA,CAAA5D,8BAAA,CAAA2I,IAAA,CAAAK,EAAA;oBACA;oBACA,IAAAA,EAAA,CAAAmD,MAAA;sBACA;sBACAvI,MAAA,CAAAvD,gCAAA,CAAAsI,IAAA,CAAAK,EAAA;oBACA;oBACA,IAAAA,EAAA,CAAAmD,MAAA;sBACA;sBACAvI,MAAA,CAAAlD,sCAAA,CAAAiI,IAAA,CAAAK,EAAA;oBACA;kBACA;gBAAA,SAAArB,GAAA;kBAAAkB,UAAA,CAAAjB,CAAA,CAAAD,GAAA;gBAAA;kBAAAkB,UAAA,CAAAhB,CAAA;gBAAA;gBACAjE,MAAA,CAAA1C,IAAA,CAAAkL,gBAAA,GAAAxD,OAAA;gBACA;gBACA,IAAAyD,eAAA;gBACA,IAAA9H,QAAA,CAAA8H,eAAA;kBAAA,IAAAC,UAAA,OAAAzF,2BAAA,CAAA/C,OAAA,EACAS,QAAA,CAAA8H,eAAA;oBAAAE,MAAA;kBAAA;oBAAA,KAAAD,UAAA,CAAAvF,CAAA,MAAAwF,MAAA,GAAAD,UAAA,CAAAnK,CAAA,IAAA6E,IAAA;sBAAA,IAAAS,EAAA,GAAA8E,MAAA,CAAArF,KAAA;sBACAmF,eAAA,KAAA5E,EAAA,CAAAjF,WAAA,WAAAiF,EAAA,CAAAjF,WAAA,UACAiF,EAAA,CAAA+E,SAAA,WAAA/E,EAAA,CAAA+E,SAAA,UACA/E,EAAA,CAAAkE,OAAA,WAAAlE,EAAA,CAAAkE,OAAA,UACAlE,EAAA,CAAAgF,iBAAA,WAAAhF,EAAA,CAAAgF,iBAAA,UACAhF,EAAA,CAAAiF,mBAAA,WAAAjF,EAAA,CAAAiF,mBAAA,UACAjF,EAAA,CAAAkF,IAAA,WAAAlF,EAAA,CAAAkF,IAAA,UACAlF,EAAA,CAAAmF,eAAA,WAAAnF,EAAA,CAAAmF,eAAA;oBACA;kBAAA,SAAAjF,GAAA;oBAAA2E,UAAA,CAAA1E,CAAA,CAAAD,GAAA;kBAAA;oBAAA2E,UAAA,CAAAzE,CAAA;kBAAA;gBACA;gBACAjE,MAAA,CAAA1C,IAAA,CAAA2L,aAAA,GAAAR,eAAA;gBACAzI,MAAA,CAAA1C,IAAA,CAAA4L,cAAA,GAAAvI,QAAA,CAAAuI,cAAA;gBACAlJ,MAAA,CAAA1C,IAAA,CAAA6L,YAAA,GAAAxI,QAAA,CAAAwI,YAAA;gBACAnJ,MAAA,CAAAnG,eAAA,GAAA8G,QAAA,CAAA9G,eAAA;gBACAmG,MAAA,CAAA1C,IAAA,CAAA8L,oBAAA,GAAAzI,QAAA,CAAA0I,kBAAA;gBACArJ,MAAA,CAAA1C,IAAA,CAAA0D,YAAA,GAAAL,QAAA,CAAA2I,OAAA;cACA;YAAA;YAAA;cAAA,OAAA9I,QAAA,CAAA+I,IAAA;UAAA;QAAA,GAAAlJ,OAAA;MAAA;IACA;IACAjB,cAAA,WAAAA,eAAA;MAAA,IAAAoK,MAAA;MAAA,WAAAvJ,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqJ,SAAA;QAAA,WAAAtJ,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlJ,IAAA,GAAAkJ,SAAA,CAAAjJ,IAAA;YAAA;cACA8I,MAAA,CAAA7N,OAAA;cAAAgO,SAAA,CAAAjJ,IAAA;cAAA,OACA,IAAAkJ,oBAAA;gBAAA5L,OAAA,EAAAwL,MAAA,CAAAxL,OAAA;gBAAAC,QAAA,EAAAuL,MAAA,CAAAvL;cAAA,GAAAc,IAAA,WAAA4B,QAAA;gBACA,IAAAA,QAAA;kBACA6I,MAAA,CAAApM,WAAA,GAAAuD,QAAA,CAAAkJ,IAAA;kBACAL,MAAA,CAAAtL,KAAA,GAAAyC,QAAA,CAAAzC,KAAA;gBACA;gBACAsL,MAAA,CAAA7N,OAAA;cACA;YAAA;YAAA;cAAA,OAAAgO,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACA7J,gBAAA,WAAAA,iBAAAH,EAAA;MAAA,IAAAqK,MAAA;MAAA,WAAA7J,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA2J,SAAA;QAAA,WAAA5J,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxJ,IAAA,GAAAwJ,SAAA,CAAAvJ,IAAA;YAAA;cACAoJ,MAAA,CAAA3K,KAAA;cAAA8K,SAAA,CAAAvJ,IAAA;cAAA,OACA,IAAAwJ,mBAAA,EAAAzK,EAAA,EAAAV,IAAA,WAAA4B,QAAA;gBACA,IAAAwJ,EAAA;gBACA,IAAAxJ,QAAA,CAAAnH,IAAA,CAAAmB,iBAAA;kBACAgG,QAAA,CAAAnH,IAAA,CAAAmB,iBAAA,CAAAyP,KAAA,MAAAtF,OAAA,WAAAT,CAAA;oBACA8F,EAAA,CAAApF,IAAA,CAAAS,MAAA,CAAAnB,CAAA;kBACA;gBACA;gBACAyF,MAAA,CAAAnP,iBAAA,GAAAwP,EAAA;gBACAL,MAAA,CAAA/P,WAAA,GAAA4G,QAAA,CAAAnH,IAAA,CAAAO,WAAA;gBACA+P,MAAA,CAAAhQ,UAAA,GAAA6G,QAAA,CAAAnH,IAAA,CAAAM,UAAA;gBACAgQ,MAAA,CAAAxM,IAAA,GAAAqD,QAAA,CAAAnH,IAAA;gBACAsQ,MAAA,CAAAxM,IAAA,CAAA3C,iBAAA,GAAAwP,EAAA;gBACA,IAAAjG,IAAA,OAAAjK,GAAA;gBACA,IAAA0G,QAAA,CAAAnH,IAAA,CAAAkB,UAAA;kBAAA,IAAA2P,UAAA,OAAApH,2BAAA,CAAA/C,OAAA,EACA4J,MAAA,CAAAlQ,WAAA;oBAAA0Q,MAAA;kBAAA;oBAAA,KAAAD,UAAA,CAAAlH,CAAA,MAAAmH,MAAA,GAAAD,UAAA,CAAA9L,CAAA,IAAA6E,IAAA;sBAAA,IAAAiB,CAAA,GAAAiG,MAAA,CAAAhH,KAAA;sBACA,IAAAe,CAAA,CAAAd,QAAA,IAAAR,SAAA,IAAAsB,CAAA,CAAAd,QAAA,CAAA7E,MAAA;wBAAA,IAAA6L,WAAA,OAAAtH,2BAAA,CAAA/C,OAAA,EACAmE,CAAA,CAAAd,QAAA;0BAAAiH,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAApH,CAAA,MAAAqH,OAAA,GAAAD,WAAA,CAAAhM,CAAA,IAAA6E,IAAA;4BAAA,IAAAC,CAAA,GAAAmH,OAAA,CAAAlH,KAAA;4BACA,IAAAD,CAAA,CAAAmB,OAAA,YAAAnB,CAAA,CAAAmB,OAAA,CAAA/J,SAAA,YAAA4I,CAAA,CAAAmB,OAAA,CAAA/J,SAAA,IAAAsI,SAAA,IAAApC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,YAAAiG,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAAgE,MAAA;8BACA,IAAAiC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAA+J,QAAA,CAAApB,CAAA,CAAAmB,OAAA,CAAA/J,SAAA;gCACAyJ,IAAA,CAAAQ,GAAA,CAAArB,CAAA,CAAAsB,aAAA;8BACA;4BACA;4BACA,IAAAtB,CAAA,CAAAE,QAAA,IAAAR,SAAA,IAAAM,CAAA,CAAAE,QAAA,CAAA7E,MAAA;8BAAA,IAAA+L,WAAA,OAAAxH,2BAAA,CAAA/C,OAAA,EACAmD,CAAA,CAAAE,QAAA;gCAAAmH,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAAtH,CAAA,MAAAuH,OAAA,GAAAD,WAAA,CAAAlM,CAAA,IAAA6E,IAAA;kCAAA,IAAAM,CAAA,GAAAgH,OAAA,CAAApH,KAAA;kCACA,IAAAI,CAAA,CAAAc,OAAA,YAAAd,CAAA,CAAAc,OAAA,CAAA/J,SAAA,YAAAiJ,CAAA,CAAAc,OAAA,CAAA/J,SAAA,IAAAsI,SAAA,IAAApC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,YAAAiG,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAAgE,MAAA;oCACA,IAAAiC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAA+J,QAAA,CAAAf,CAAA,CAAAc,OAAA,CAAA/J,SAAA;sCACAyJ,IAAA,CAAAQ,GAAA,CAAAhB,CAAA,CAAAiB,aAAA;oCACA;kCACA;gCACA;8BAAA,SAAAZ,GAAA;gCAAA0G,WAAA,CAAAzG,CAAA,CAAAD,GAAA;8BAAA;gCAAA0G,WAAA,CAAAxG,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAAwG,WAAA,CAAAvG,CAAA,CAAAD,GAAA;wBAAA;0BAAAwG,WAAA,CAAAtG,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAsG,UAAA,CAAArG,CAAA,CAAAD,GAAA;kBAAA;oBAAAsG,UAAA,CAAApG,CAAA;kBAAA;gBACA;gBACA,IAAAC,IAAA,CAAA/J,IAAA;kBACA+J,IAAA,CAAAY,OAAA,WAAAjB,CAAA;oBACAiG,MAAA,CAAApP,UAAA,CAAAqK,IAAA,CAAAlB,CAAA;kBACA;gBACA;gBACA,IAAAiG,MAAA,CAAAnQ,UAAA,IAAAoJ,SAAA;kBAAA,IAAA4H,WAAA,OAAA1H,2BAAA,CAAA/C,OAAA,EACA4J,MAAA,CAAAnQ,UAAA;oBAAAiR,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAxH,CAAA,MAAAyH,OAAA,GAAAD,WAAA,CAAApM,CAAA,IAAA6E,IAAA;sBAAA,IAAAC,GAAA,GAAAuH,OAAA,CAAAtH,KAAA;sBACA,IAAAD,GAAA,CAAAE,QAAA,IAAAR,SAAA;wBAAA,IAAA8H,WAAA,OAAA5H,2BAAA,CAAA/C,OAAA,EACAmD,GAAA,CAAAE,QAAA;0BAAAuH,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAA1H,CAAA,MAAA2H,OAAA,GAAAD,WAAA,CAAAtM,CAAA,IAAA6E,IAAA;4BAAA,IAAAM,GAAA,GAAAoH,OAAA,CAAAxH,KAAA;4BACA,IAAAI,GAAA,CAAAH,QAAA,IAAAR,SAAA;8BAAA,IAAAgI,WAAA,OAAA9H,2BAAA,CAAA/C,OAAA,EACAwD,GAAA,CAAAH,QAAA;gCAAAyH,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAA5H,CAAA,MAAA6H,OAAA,GAAAD,WAAA,CAAAxM,CAAA,IAAA6E,IAAA;kCAAA,IAAAS,CAAA,GAAAmH,OAAA,CAAA1H,KAAA;kCACA,IAAAO,CAAA,CAAAhD,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAqB,OAAA;oCACAiP,MAAA,CAAAjP,OAAA,GAAAgJ,CAAA,CAAAC,MAAA;kCACA;kCACA,IAAAD,CAAA,CAAAhD,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAsB,gBAAA;oCACAgP,MAAA,CAAAhP,gBAAA,GAAA+I,CAAA,CAAAC,MAAA;kCACA;kCACA,IAAAD,CAAA,CAAAhD,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAuB,eAAA;oCACA+O,MAAA,CAAA/O,eAAA,GAAA8I,CAAA,CAAAC,MAAA;kCACA;gCACA;8BAAA,SAAAC,GAAA;gCAAAgH,WAAA,CAAA/G,CAAA,CAAAD,GAAA;8BAAA;gCAAAgH,WAAA,CAAA9G,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAA8G,WAAA,CAAA7G,CAAA,CAAAD,GAAA;wBAAA;0BAAA8G,WAAA,CAAA5G,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA4G,WAAA,CAAA3G,CAAA,CAAAD,GAAA;kBAAA;oBAAA4G,WAAA,CAAA1G,CAAA;kBAAA;gBACA;gBACA,IAAA6F,MAAA,CAAArQ,MAAA,IAAAsJ,SAAA;kBAAA,IAAAkI,WAAA,OAAAhI,2BAAA,CAAA/C,OAAA,EACA4J,MAAA,CAAArQ,MAAA;oBAAAyR,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAA9H,CAAA,MAAA+H,OAAA,GAAAD,WAAA,CAAA1M,CAAA,IAAA6E,IAAA;sBAAA,IAAAC,GAAA,GAAA6H,OAAA,CAAA5H,KAAA;sBACA,IAAAD,GAAA,CAAAE,QAAA,IAAAR,SAAA;wBAAA,IAAAoI,WAAA,OAAAlI,2BAAA,CAAA/C,OAAA,EACAmD,GAAA,CAAAE,QAAA;0BAAA6H,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAhI,CAAA,MAAAiI,OAAA,GAAAD,WAAA,CAAA5M,CAAA,IAAA6E,IAAA;4BAAA,IAAAM,GAAA,GAAA0H,OAAA,CAAA9H,KAAA;4BACA,IAAAD,GAAA,CAAAgI,IAAA,CAAAC,aAAA,aAAA5H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAwB,IAAA;8BACA8O,MAAA,CAAA9O,IAAA,GAAA0I,GAAA,CAAA6H,MAAA;4BACA;4BACA,IAAAlI,GAAA,CAAAgI,IAAA,CAAAC,aAAA,aAAA5H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAyB,WAAA;8BACA6O,MAAA,CAAA7O,WAAA,GAAAyI,GAAA,CAAA6H,MAAA;4BACA;4BACA,IAAAlI,GAAA,CAAAgI,IAAA,CAAAC,aAAA,aAAA5H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAA0B,OAAA;8BACA4O,MAAA,CAAA5O,OAAA,GAAAwI,GAAA,CAAA6H,MAAA;4BACA;4BACA,IAAA7H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAA2B,YAAA;8BACA2O,MAAA,CAAA3O,YAAA,GAAAuI,GAAA,CAAA6H,MAAA;4BACA;0BACA;wBAAA,SAAAxH,GAAA;0BAAAoH,WAAA,CAAAnH,CAAA,CAAAD,GAAA;wBAAA;0BAAAoH,WAAA,CAAAlH,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAkH,WAAA,CAAAjH,CAAA,CAAAD,GAAA;kBAAA;oBAAAkH,WAAA,CAAAhH,CAAA;kBAAA;gBACA;gBACA,IAAA6F,MAAA,CAAApQ,YAAA,IAAAqJ,SAAA;kBAAA,IAAAyI,WAAA,OAAAvI,2BAAA,CAAA/C,OAAA,EACA4J,MAAA,CAAApQ,YAAA;oBAAA+R,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAArI,CAAA,MAAAsI,OAAA,GAAAD,WAAA,CAAAjN,CAAA,IAAA6E,IAAA;sBAAA,IAAAC,GAAA,GAAAoI,OAAA,CAAAnI,KAAA;sBACA,IAAAD,GAAA,CAAAE,QAAA,IAAAR,SAAA;wBAAA,IAAA2I,WAAA,OAAAzI,2BAAA,CAAA/C,OAAA,EACAmD,GAAA,CAAAE,QAAA;0BAAAoI,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAvI,CAAA,MAAAwI,OAAA,GAAAD,WAAA,CAAAnN,CAAA,IAAA6E,IAAA;4BAAA,IAAAM,GAAA,GAAAiI,OAAA,CAAArI,KAAA;4BACA,IAAAI,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAoB,WAAA;8BACAkP,MAAA,CAAAlP,WAAA,GAAA8I,GAAA,CAAA6H,MAAA;4BACA;0BACA;wBAAA,SAAAxH,GAAA;0BAAA2H,WAAA,CAAA1H,CAAA,CAAAD,GAAA;wBAAA;0BAAA2H,WAAA,CAAAzH,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAyH,WAAA,CAAAxH,CAAA,CAAAD,GAAA;kBAAA;oBAAAyH,WAAA,CAAAvH,CAAA;kBAAA;gBACA;gBACA;gBACA,IAAAtD,QAAA,CAAAnH,IAAA,CAAAoS,+BAAA;kBACA9B,MAAA,CAAAvM,kBAAA,GAAAoD,QAAA,CAAAnH,IAAA,CAAAoS,+BAAA;kBACA9B,MAAA,CAAA1N,8BAAA,GAAAuE,QAAA,CAAAnH,IAAA,CAAAoS,+BAAA,CAAAC,8BAAA;kBACA;AACA;AACA;gBACA;gBACA;gBACA,IAAAlL,QAAA,CAAAnH,IAAA,CAAAsS,6BAAA;kBACAhC,MAAA,CAAAtM,oBAAA,GAAAmD,QAAA,CAAAnH,IAAA,CAAAsS,6BAAA;kBACAhC,MAAA,CAAArN,gCAAA,GAAAkE,QAAA,CAAAnH,IAAA,CAAAsS,6BAAA,CAAAD,8BAAA;gBACA;gBACA;gBACA,IAAAlL,QAAA,CAAAnH,IAAA,CAAAuS,mCAAA;kBACAjC,MAAA,CAAArM,0BAAA,GAAAkD,QAAA,CAAAnH,IAAA,CAAAuS,mCAAA;kBACAjC,MAAA,CAAAhN,sCAAA,GAAA6D,QAAA,CAAAnH,IAAA,CAAAuS,mCAAA,CAAAF,8BAAA;gBACA;gBACA;gBACA,IAAAlL,QAAA,CAAAnH,IAAA,CAAAwS,iCAAA;kBACAlC,MAAA,CAAApM,wBAAA,GAAAiD,QAAA,CAAAnH,IAAA,CAAAwS,iCAAA;kBACAlC,MAAA,CAAA3M,oCAAA,GAAAwD,QAAA,CAAAnH,IAAA,CAAAwS,iCAAA,CAAAH,8BAAA;gBACA;gBACA/B,MAAA,CAAAjQ,eAAA,GAAA8G,QAAA,CAAA9G,eAAA;cACA;YAAA;YAAA;cAAA,OAAAoQ,SAAA,CAAAV,IAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IACA;IACAzK,UAAA,WAAAA,WAAA;MAAA,IAAA2M,MAAA;MAAA,WAAAhM,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA8L,SAAA;QAAA,WAAA/L,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA6L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3L,IAAA,GAAA2L,SAAA,CAAA1L,IAAA;YAAA;cACAuL,MAAA,CAAAtQ,OAAA;cAAAyQ,SAAA,CAAA1L,IAAA;cAAA,OACA,IAAA2L,eAAA;gBAAArO,OAAA,EAAAiO,MAAA,CAAAjO,OAAA;gBAAAC,QAAA,EAAAgO,MAAA,CAAAhO;cAAA,GAAAc,IAAA,WAAA4B,QAAA;gBACA,IAAAA,QAAA;kBACAsL,MAAA,CAAA5O,OAAA,GAAAsD,QAAA,CAAAkJ,IAAA;kBACAoC,MAAA,CAAA/N,KAAA,GAAAyC,QAAA,CAAAzC,KAAA;gBACA;gBACA+N,MAAA,CAAAtQ,OAAA;cACA;YAAA;YAAA;cAAA,OAAAyQ,SAAA,CAAA7C,IAAA;UAAA;QAAA,GAAA2C,QAAA;MAAA;IACA;IACApM,YAAA,WAAAA,aAAAL,EAAA;MAAA,IAAA6M,MAAA;MAAA,WAAArM,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAmM,SAAA;QAAA,WAAApM,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAkM,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhM,IAAA,GAAAgM,SAAA,CAAA/L,IAAA;YAAA;cACA4L,MAAA,CAAAnN,KAAA;cAAAsN,SAAA,CAAA/L,IAAA;cAAA,OACA,IAAAgM,cAAA,EAAAjN,EAAA,EAAAV,IAAA,WAAA4B,QAAA;gBACA2L,MAAA,CAAAvS,WAAA,GAAA4G,QAAA,CAAAnH,IAAA,CAAAO,WAAA;gBACAuS,MAAA,CAAAxS,UAAA,GAAA6G,QAAA,CAAAnH,IAAA,CAAAM,UAAA;gBACA,IAAAoK,IAAA,OAAAjK,GAAA;gBACA;gBACA,IAAA0G,QAAA,CAAAnH,IAAA,CAAAkB,UAAA;kBAAA,IAAAiS,WAAA,OAAA1J,2BAAA,CAAA/C,OAAA,EACAoM,MAAA,CAAA1S,WAAA;oBAAAgT,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAxJ,CAAA,MAAAyJ,OAAA,GAAAD,WAAA,CAAApO,CAAA,IAAA6E,IAAA;sBAAA,IAAAiB,CAAA,GAAAuI,OAAA,CAAAtJ,KAAA;sBACA,IAAAe,CAAA,CAAAd,QAAA,IAAAR,SAAA,IAAAsB,CAAA,CAAAd,QAAA,CAAA7E,MAAA;wBAAA,IAAAmO,WAAA,OAAA5J,2BAAA,CAAA/C,OAAA,EACAmE,CAAA,CAAAd,QAAA;0BAAAuJ,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAA1J,CAAA,MAAA2J,OAAA,GAAAD,WAAA,CAAAtO,CAAA,IAAA6E,IAAA;4BAAA,IAAAC,CAAA,GAAAyJ,OAAA,CAAAxJ,KAAA;4BACA,IAAAD,CAAA,CAAAmB,OAAA,YAAAnB,CAAA,CAAAmB,OAAA,CAAA/J,SAAA,YAAA4I,CAAA,CAAAmB,OAAA,CAAA/J,SAAA,IAAAsI,SAAA,IAAApC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,YAAAiG,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAAgE,MAAA;8BACA,IAAAiC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAA+J,QAAA,CAAApB,CAAA,CAAAmB,OAAA,CAAA/J,SAAA;gCACAyJ,IAAA,CAAAQ,GAAA,CAAArB,CAAA,CAAAsB,aAAA;8BACA;4BACA;4BACA,IAAAtB,CAAA,CAAAE,QAAA,IAAAR,SAAA,IAAAM,CAAA,CAAAE,QAAA,CAAA7E,MAAA;8BAAA,IAAAqO,WAAA,OAAA9J,2BAAA,CAAA/C,OAAA,EACAmD,CAAA,CAAAE,QAAA;gCAAAyJ,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAA5J,CAAA,MAAA6J,OAAA,GAAAD,WAAA,CAAAxO,CAAA,IAAA6E,IAAA;kCAAA,IAAAM,CAAA,GAAAsJ,OAAA,CAAA1J,KAAA;kCACA,IAAAI,CAAA,CAAAc,OAAA,YAAAd,CAAA,CAAAc,OAAA,CAAA/J,SAAA,YAAAiJ,CAAA,CAAAc,OAAA,CAAA/J,SAAA,IAAAsI,SAAA,IAAApC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,YAAAiG,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAAgE,MAAA;oCACA,IAAAiC,QAAA,CAAAnH,IAAA,CAAAkB,UAAA,CAAA+J,QAAA,CAAAf,CAAA,CAAAc,OAAA,CAAA/J,SAAA;sCACAyJ,IAAA,CAAAQ,GAAA,CAAAhB,CAAA,CAAAiB,aAAA;oCACA;kCACA;gCACA;8BAAA,SAAAZ,GAAA;gCAAAgJ,WAAA,CAAA/I,CAAA,CAAAD,GAAA;8BAAA;gCAAAgJ,WAAA,CAAA9I,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAA8I,WAAA,CAAA7I,CAAA,CAAAD,GAAA;wBAAA;0BAAA8I,WAAA,CAAA5I,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA4I,WAAA,CAAA3I,CAAA,CAAAD,GAAA;kBAAA;oBAAA4I,WAAA,CAAA1I,CAAA;kBAAA;gBACA;gBACA,IAAAC,IAAA,CAAA/J,IAAA;kBACA+J,IAAA,CAAAY,OAAA,WAAAjB,CAAA;oBACAyI,MAAA,CAAA5R,UAAA,CAAAqK,IAAA,CAAAlB,CAAA;kBACA;gBACA;gBACA,IAAAsG,EAAA;gBACA;gBACA,IAAAxJ,QAAA,CAAAnH,IAAA,CAAAmB,iBAAA;kBACAgG,QAAA,CAAAnH,IAAA,CAAAmB,iBAAA,CAAAyP,KAAA,MAAAtF,OAAA,WAAAT,CAAA;oBACA8F,EAAA,CAAApF,IAAA,CAAAS,MAAA,CAAAnB,CAAA;kBACA;gBACA;gBACAiI,MAAA,CAAA3R,iBAAA,GAAAwP,EAAA;gBACAmC,MAAA,CAAAhP,IAAA,GAAAqD,QAAA,CAAAnH,IAAA;gBACA8S,MAAA,CAAAhP,IAAA,CAAA3C,iBAAA,GAAAwP,EAAA;gBACA,IAAAmC,MAAA,CAAA3S,UAAA,IAAAoJ,SAAA;kBAAA,IAAAkK,WAAA,OAAAhK,2BAAA,CAAA/C,OAAA,EACAoM,MAAA,CAAA3S,UAAA;oBAAAuT,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAA9J,CAAA,MAAA+J,OAAA,GAAAD,WAAA,CAAA1O,CAAA,IAAA6E,IAAA;sBAAA,IAAAC,GAAA,GAAA6J,OAAA,CAAA5J,KAAA;sBACA,IAAAD,GAAA,CAAAE,QAAA,IAAAR,SAAA;wBAAA,IAAAoK,WAAA,OAAAlK,2BAAA,CAAA/C,OAAA,EACAmD,GAAA,CAAAE,QAAA;0BAAA6J,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAhK,CAAA,MAAAiK,OAAA,GAAAD,WAAA,CAAA5O,CAAA,IAAA6E,IAAA;4BAAA,IAAAM,GAAA,GAAA0J,OAAA,CAAA9J,KAAA;4BACA,IAAAI,GAAA,CAAAH,QAAA,IAAAR,SAAA;8BAAA,IAAAsK,WAAA,OAAApK,2BAAA,CAAA/C,OAAA,EACAwD,GAAA,CAAAH,QAAA;gCAAA+J,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAAlK,CAAA,MAAAmK,OAAA,GAAAD,WAAA,CAAA9O,CAAA,IAAA6E,IAAA;kCAAA,IAAAS,CAAA,GAAAyJ,OAAA,CAAAhK,KAAA;kCACA,IAAAO,CAAA,CAAAhD,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAqB,OAAA;oCACAyR,MAAA,CAAAzR,OAAA,GAAAgJ,CAAA,CAAAC,MAAA;kCACA;kCACA,IAAAD,CAAA,CAAAhD,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAsB,gBAAA;oCACAwR,MAAA,CAAAxR,gBAAA,GAAA+I,CAAA,CAAAC,MAAA;kCACA;kCACA,IAAAD,CAAA,CAAAhD,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAuB,eAAA;oCACAuR,MAAA,CAAAvR,eAAA,GAAA8I,CAAA,CAAAC,MAAA;kCACA;gCACA;8BAAA,SAAAC,GAAA;gCAAAsJ,WAAA,CAAArJ,CAAA,CAAAD,GAAA;8BAAA;gCAAAsJ,WAAA,CAAApJ,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAAoJ,WAAA,CAAAnJ,CAAA,CAAAD,GAAA;wBAAA;0BAAAoJ,WAAA,CAAAlJ,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAkJ,WAAA,CAAAjJ,CAAA,CAAAD,GAAA;kBAAA;oBAAAkJ,WAAA,CAAAhJ,CAAA;kBAAA;gBACA;gBACA,IAAAqI,MAAA,CAAA7S,MAAA,IAAAsJ,SAAA;kBAAA,IAAAwK,WAAA,OAAAtK,2BAAA,CAAA/C,OAAA,EACAoM,MAAA,CAAA7S,MAAA;oBAAA+T,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAApK,CAAA,MAAAqK,OAAA,GAAAD,WAAA,CAAAhP,CAAA,IAAA6E,IAAA;sBAAA,IAAAC,GAAA,GAAAmK,OAAA,CAAAlK,KAAA;sBACA,IAAAD,GAAA,CAAAE,QAAA,IAAAR,SAAA;wBAAA,IAAA0K,WAAA,OAAAxK,2BAAA,CAAA/C,OAAA,EACAmD,GAAA,CAAAE,QAAA;0BAAAmK,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAtK,CAAA,MAAAuK,OAAA,GAAAD,WAAA,CAAAlP,CAAA,IAAA6E,IAAA;4BAAA,IAAAM,GAAA,GAAAgK,OAAA,CAAApK,KAAA;4BACA,IAAAD,GAAA,CAAAgI,IAAA,CAAAC,aAAA,aAAA5H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAwB,IAAA;8BACAsR,MAAA,CAAAtR,IAAA,GAAA0I,GAAA,CAAA6H,MAAA;4BACA;4BACA,IAAAlI,GAAA,CAAAgI,IAAA,CAAAC,aAAA,aAAA5H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAyB,WAAA;8BACAqR,MAAA,CAAArR,WAAA,GAAAyI,GAAA,CAAA6H,MAAA;4BACA;4BACA,IAAAlI,GAAA,CAAAgI,IAAA,CAAAC,aAAA,aAAA5H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAA0B,OAAA;8BACAoR,MAAA,CAAApR,OAAA,GAAAwI,GAAA,CAAA6H,MAAA;4BACA;4BACA,IAAA7H,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAA2B,YAAA;8BACAmR,MAAA,CAAAnR,YAAA,GAAAuI,GAAA,CAAA6H,MAAA;4BACA;0BACA;wBAAA,SAAAxH,GAAA;0BAAA0J,WAAA,CAAAzJ,CAAA,CAAAD,GAAA;wBAAA;0BAAA0J,WAAA,CAAAxJ,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAwJ,WAAA,CAAAvJ,CAAA,CAAAD,GAAA;kBAAA;oBAAAwJ,WAAA,CAAAtJ,CAAA;kBAAA;gBACA;gBACA,IAAAqI,MAAA,CAAA5S,YAAA,IAAAqJ,SAAA;kBAAA,IAAA4K,WAAA,OAAA1K,2BAAA,CAAA/C,OAAA,EACAoM,MAAA,CAAA5S,YAAA;oBAAAkU,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAxK,CAAA,MAAAyK,OAAA,GAAAD,WAAA,CAAApP,CAAA,IAAA6E,IAAA;sBAAA,IAAAC,GAAA,GAAAuK,OAAA,CAAAtK,KAAA;sBACA,IAAAD,GAAA,CAAAE,QAAA,IAAAR,SAAA;wBAAA,IAAA8K,WAAA,OAAA5K,2BAAA,CAAA/C,OAAA,EACAmD,GAAA,CAAAE,QAAA;0BAAAuK,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAA1K,CAAA,MAAA2K,OAAA,GAAAD,WAAA,CAAAtP,CAAA,IAAA6E,IAAA;4BAAA,IAAAM,GAAA,GAAAoK,OAAA,CAAAxK,KAAA;4BACA,IAAAI,GAAA,CAAA7C,OAAA,IAAAF,QAAA,CAAAnH,IAAA,CAAAoB,WAAA;8BACA0R,MAAA,CAAA1R,WAAA,GAAA8I,GAAA,CAAA6H,MAAA;4BACA;0BACA;wBAAA,SAAAxH,GAAA;0BAAA8J,WAAA,CAAA7J,CAAA,CAAAD,GAAA;wBAAA;0BAAA8J,WAAA,CAAA5J,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA4J,WAAA,CAAA3J,CAAA,CAAAD,GAAA;kBAAA;oBAAA4J,WAAA,CAAA1J,CAAA;kBAAA;gBACA;gBACA,IAAAtD,QAAA,CAAAnH,IAAA,CAAAuU,2BAAA;kBACAzB,MAAA,CAAA/O,kBAAA,GAAAoD,QAAA,CAAAnH,IAAA,CAAAuU,2BAAA;kBACAzB,MAAA,CAAAlQ,8BAAA,GAAAuE,QAAA,CAAAnH,IAAA,CAAAuU,2BAAA,CAAAC,0BAAA;kBACA1B,MAAA,CAAAnQ,kBAAA,GAAAwE,QAAA,CAAAnH,IAAA,CAAAuU,2BAAA,CAAAE,wBAAA;gBACA;gBACA,IAAAtN,QAAA,CAAAnH,IAAA,CAAA0U,yBAAA;kBACA5B,MAAA,CAAA9O,oBAAA,GAAAmD,QAAA,CAAAnH,IAAA,CAAA0U,yBAAA;kBACA5B,MAAA,CAAA7P,gCAAA,GAAAkE,QAAA,CAAAnH,IAAA,CAAA0U,yBAAA,CAAAF,0BAAA;kBACA1B,MAAA,CAAA9P,oBAAA,GAAAmE,QAAA,CAAAnH,IAAA,CAAA0U,yBAAA,CAAAD,wBAAA;gBACA;gBACA,IAAAtN,QAAA,CAAAnH,IAAA,CAAA2U,+BAAA;kBACA7B,MAAA,CAAA7O,0BAAA,GAAAkD,QAAA,CAAAnH,IAAA,CAAA2U,+BAAA;kBACA7B,MAAA,CAAAxP,sCAAA,GAAA6D,QAAA,CAAAnH,IAAA,CAAA2U,+BAAA,CAAAH,0BAAA;kBACA1B,MAAA,CAAAzP,0BAAA,GAAA8D,QAAA,CAAAnH,IAAA,CAAA2U,+BAAA,CAAAF,wBAAA;gBACA;gBACA,IAAAtN,QAAA,CAAAnH,IAAA,CAAA4U,6BAAA;kBACA9B,MAAA,CAAA5O,wBAAA,GAAAiD,QAAA,CAAAnH,IAAA,CAAA4U,6BAAA;kBACA9B,MAAA,CAAAnP,oCAAA,GAAAwD,QAAA,CAAAnH,IAAA,CAAA4U,6BAAA,CAAAJ,0BAAA;kBACA1B,MAAA,CAAApP,wBAAA,GAAAyD,QAAA,CAAAnH,IAAA,CAAA4U,6BAAA,CAAAH,wBAAA;gBACA;gBACA3B,MAAA,CAAAzS,eAAA,GAAA8G,QAAA,CAAA9G,eAAA;cACA;YAAA;YAAA;cAAA,OAAA4S,SAAA,CAAAlD,IAAA;UAAA;QAAA,GAAAgD,QAAA;MAAA;IACA;IACA8B,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtO,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoO,SAAA;QAAA,IAAAC,WAAA,EAAAC,OAAA,EAAAvL,CAAA,EAAAwL,WAAA,EAAAC,OAAA,EAAAC,CAAA;QAAA,WAAA1O,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAwO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtO,IAAA,GAAAsO,SAAA,CAAArO,IAAA;YAAA;cAAA,MACA6N,MAAA,CAAAnU,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiF,eAAA,CAAAC,MAAA;gBAAAqQ,SAAA,CAAArO,IAAA;gBAAA;cAAA;cAAAqO,SAAA,CAAArO,IAAA;cAAA,OACA6N,MAAA,CAAAnU,MAAA,CAAA0E,QAAA;YAAA;cAEAyP,MAAA,CAAAvU,IAAA,CAAAgV,KAAA;cACAT,MAAA,CAAAjR,IAAA,CAAA4L,cAAA,GAAAoF,GAAA;cAAAG,WAAA,OAAAxL,2BAAA,CAAA/C,OAAA,EACAqO,MAAA,CAAAnU,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiF,eAAA,IAAA8E,QAAA;cAAA;gBAAA,KAAAkL,WAAA,CAAAtL,CAAA,MAAAuL,OAAA,GAAAD,WAAA,CAAAlQ,CAAA,IAAA6E,IAAA;kBAAAD,CAAA,GAAAuL,OAAA,CAAApL,KAAA;kBACA,IAAAgL,GAAA,CAAA7J,QAAA,CAAAtB,CAAA,CAAAwB,aAAA;oBACA,IAAAxB,CAAA,CAAAoF,MAAA;sBACAgG,MAAA,CAAAvU,IAAA,CAAA0K,GAAA,CAAAvB,CAAA,CAAAoF,MAAA;oBACA;kBACA;kBACA,IAAApF,CAAA,CAAAI,QAAA;oBAAAoL,WAAA,OAAA1L,2BAAA,CAAA/C,OAAA,EACAiD,CAAA,CAAAI,QAAA;oBAAA;sBAAA,KAAAoL,WAAA,CAAAxL,CAAA,MAAAyL,OAAA,GAAAD,WAAA,CAAApQ,CAAA,IAAA6E,IAAA;wBAAAyL,CAAA,GAAAD,OAAA,CAAAtL,KAAA;wBACA,IAAAgL,GAAA,CAAA7J,QAAA,CAAAoK,CAAA,CAAAlK,aAAA;0BACA,IAAAxB,CAAA,CAAAoF,MAAA;4BACAgG,MAAA,CAAAvU,IAAA,CAAA0K,GAAA,CAAAvB,CAAA,CAAAoF,MAAA;0BACA;0BACA,IAAAsG,CAAA,CAAAtG,MAAA;4BACAgG,MAAA,CAAAvU,IAAA,CAAA0K,GAAA,CAAAmK,CAAA,CAAAtG,MAAA;0BACA;wBACA;sBACA;oBAAA,SAAAxE,GAAA;sBAAA4K,WAAA,CAAA3K,CAAA,CAAAD,GAAA;oBAAA;sBAAA4K,WAAA,CAAA1K,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAA0K,WAAA,CAAAzK,CAAA,CAAAD,GAAA;cAAA;gBAAA0K,WAAA,CAAAxK,CAAA;cAAA;cACA,IAAAqK,GAAA,CAAA7J,QAAA;gBACA8J,MAAA,CAAAvU,IAAA,CAAA0K,GAAA;cACA;cACA6J,MAAA,CAAAU,YAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAxF,IAAA;UAAA;QAAA,GAAAiF,QAAA;MAAA;IACA;IACA;AACA;AACA;AACA;IACAxP,OAAA,WAAAA,QAAAT,CAAA;MAAA,IAAA2Q,WAAA,OAAAjM,2BAAA,CAAA/C,OAAA,EACA,KAAA9F,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiF,eAAA,IAAA8E,QAAA;QAAA4L,OAAA;MAAA;QAAA,KAAAD,WAAA,CAAA/L,CAAA,MAAAgM,OAAA,GAAAD,WAAA,CAAA3Q,CAAA,IAAA6E,IAAA;UAAA,IAAAD,CAAA,GAAAgM,OAAA,CAAA7L,KAAA;UACA,IAAAH,CAAA,CAAAwB,aAAA,IAAApG,CAAA;YACA,KAAA/D,aAAA,GAAA2I,CAAA,CAAAoF,MAAA;UACA;UACA,IAAApF,CAAA,CAAAI,QAAA;YAAA,IAAA6L,WAAA,OAAAnM,2BAAA,CAAA/C,OAAA,EACAiD,CAAA,CAAAI,QAAA;cAAA8L,OAAA;YAAA;cAAA,KAAAD,WAAA,CAAAjM,CAAA,MAAAkM,OAAA,GAAAD,WAAA,CAAA7Q,CAAA,IAAA6E,IAAA;gBAAA,IAAAyL,CAAA,GAAAQ,OAAA,CAAA/L,KAAA;gBACA,IAAAuL,CAAA,CAAAlK,aAAA,IAAApG,CAAA;kBACA,KAAA/D,aAAA,GAAA2I,CAAA,CAAAoF,MAAA;gBACA;cACA;YAAA,SAAAxE,GAAA;cAAAqL,WAAA,CAAApL,CAAA,CAAAD,GAAA;YAAA;cAAAqL,WAAA,CAAAnL,CAAA;YAAA;UACA;QACA;MAAA,SAAAF,GAAA;QAAAmL,WAAA,CAAAlL,CAAA,CAAAD,GAAA;MAAA;QAAAmL,WAAA,CAAAjL,CAAA;MAAA;IACA;IACAqL,oBAAA,WAAAA,qBAAAhB,GAAA;MACA,KAAAhR,IAAA,CAAA3C,iBAAA,GAAA2T,GAAA;MACA,KAAA3T,iBAAA,GAAA2T,GAAA;IACA;IACA;IACAiB,cAAA,WAAAA,eAAAjB,GAAA;MACA,IAAAkB,EAAA;MACA,IAAAC,GAAA;MACA,IAAAnB,GAAA;QACA,IAAAmB,GAAA,CAAAC,IAAA,MAAA3V,WAAA;UACA,KAAAA,WAAA,QAAAA,WAAA,CAAA4V,OAAA;UACA,KAAArS,IAAA,CAAAvD,WAAA,QAAAA,WAAA;UACA,IAAA6V,GAAA,QAAA7V,WAAA,CAAAqQ,KAAA;UACA,IAAAyF,EAAA,GAAAD,GAAA,IAAAD,OAAA,CAAAH,EAAA;UACA,KAAAzV,WAAA,GAAA6V,GAAA,CAAAlR,MAAA,QAAAkR,GAAA,SAAAE,MAAA,CAAAD,EAAA,OAAAC,MAAA,CAAAF,GAAA,UAAAE,MAAA,CAAAD,EAAA;QACA;UACA,KAAAE,QAAA,CAAAC,OAAA;QACA;MACA;MACA,IAAA1B,GAAA;QACA,IAAAmB,GAAA,CAAAC,IAAA,MAAA5V,UAAA;UACA,KAAAA,UAAA,QAAAA,UAAA,CAAA6V,OAAA;UACA,KAAArS,IAAA,CAAAxD,UAAA,QAAAA,UAAA;UACA,IAAA8V,IAAA,QAAA9V,UAAA,CAAAsQ,KAAA;UACA,IAAAyF,EAAA,GAAAD,IAAA,IAAAD,OAAA,CAAAH,EAAA;UACA,KAAA1V,UAAA,GAAA8V,IAAA,CAAAlR,MAAA,QAAAkR,IAAA,SAAAE,MAAA,CAAAD,EAAA,OAAAC,MAAA,CAAAF,IAAA,UAAAE,MAAA,CAAAD,EAAA;QACA;UACA,KAAAE,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAAxU,IAAA,EAAA6S,GAAA;MACA,IAAAtU,IAAA;MACA,IAAAyB,IAAA;QACA,KAAAO,eAAA,GAAAsS,GAAA;QACAtU,IAAA,QAAAiC,mBAAA,IACA;UAAAR,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAAyU,WAAA;UAAAC,OAAA;QAAA;MAEA;MACA,IAAA1U,IAAA;QACA,KAAAY,iBAAA,GAAAiS,GAAA;QACAtU,IAAA,QAAAsC,qBAAA,IACA;UAAAb,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA,GACA;UAAA1U,IAAA;UAAA2U,aAAA;UAAAD,OAAA;QAAA;MACA;MAAA,IAAAE,WAAA,OAAApN,2BAAA,CAAA/C,OAAA,EACAlG,IAAA;QAAAsW,OAAA;MAAA;QAAA,IAAAC,KAAA,YAAAA,MAAA;UAAA,IAAAC,OAAA,GAAAF,OAAA,CAAAhN,KAAA;UACAgL,GAAA,CAAAxJ,OAAA,WAAAT,CAAA;YACA,SAAAoM,IAAA,IAAApM,CAAA;cACA,IAAAoM,IAAA,IAAAD,OAAA,CAAA/U,IAAA;gBACA+U,OAAA,CAAAL,OAAA,GAAAK,OAAA,CAAAL,OAAA,SAAA9L,CAAA,CAAAoM,IAAA,YAAApM,CAAA,CAAAoM,IAAA,SAAAD,OAAA,CAAAL,OAAA,IAAA9L,CAAA,CAAAoM,IAAA,kBAAApM,CAAA,CAAAoM,IAAA;cACA;YACA;UACA;QACA;QARA,KAAAJ,WAAA,CAAAlN,CAAA,MAAAmN,OAAA,GAAAD,WAAA,CAAA9R,CAAA,IAAA6E,IAAA;UAAAmN,KAAA;QAAA;MAQA,SAAAxM,GAAA;QAAAsM,WAAA,CAAArM,CAAA,CAAAD,GAAA;MAAA;QAAAsM,WAAA,CAAApM,CAAA;MAAA;IACA;IACA;IACA;IACAyM,UAAA,WAAAA,WAAArM,CAAA;MAAA,IAAAsM,MAAA;MACA,KAAArT,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA+D,MAAA,YAAApB,IAAA,CAAA3C,iBAAA,CAAAiW,QAAA;MACA,SAAAvV,SAAA;QACA,KAAAiC,IAAA,CAAAuT,aAAA;QACA,KAAAvT,IAAA,CAAAwT,eAAA;QACA,KAAAxT,IAAA,CAAAyT,aAAA,OAAAC,eAAA,MAAArP,IAAA;MACA;MACA,KAAAsP,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA;QACA;QACA,IAAAR,MAAA,CAAAlV,IAAA,YAAAkV,MAAA,CAAAlV,IAAA,iBAAAkV,MAAA,CAAAtV,SAAA;UACA;UACA,IAAAgJ,CAAA;YACAsM,MAAA,CAAArT,IAAA,CAAA8T,KAAA;UACA;UACA,IAAAD,KAAA;YACA;YACA,IAAA3X,IAAA;YACAA,IAAA,CAAAsH,QAAA,GAAA6P,MAAA,CAAArT,IAAA,CAAAwD,QAAA;YACAtH,IAAA,CAAAwH,YAAA,GAAA2P,MAAA,CAAArT,IAAA,CAAA0D,YAAA;YACAxH,IAAA,CAAA0H,eAAA,GAAAyP,MAAA,CAAArT,IAAA,CAAA4D,eAAA;YACA1H,IAAA,CAAA4H,kBAAA,GAAAuP,MAAA,CAAArT,IAAA,CAAA8D,kBAAA;YACA5H,IAAA,CAAA8H,oBAAA,GAAAqP,MAAA,CAAArT,IAAA,CAAAgE,oBAAA;YACA9H,IAAA,CAAA6X,cAAA,GAAAV,MAAA,CAAArT,IAAA,CAAAgU,aAAA;YACA9X,IAAA,CAAA+X,eAAA;YACA/X,IAAA,CAAAgY,WAAA,GAAAb,MAAA,CAAArT,IAAA,CAAAmU,aAAA;YACAjY,IAAA,CAAAkY,gBAAA,GAAAf,MAAA,CAAArT,IAAA,CAAAoU,gBAAA;YACAlY,IAAA,CAAAmY,UAAA,GAAAhB,MAAA,CAAArT,IAAA,CAAAsE,YAAA;YACApI,IAAA,CAAAoY,YAAA,GAAAjB,MAAA,CAAArT,IAAA,CAAAuU,cAAA;YACArY,IAAA,CAAAsY,eAAA,GAAAnB,MAAA,CAAArT,IAAA,CAAAwU,eAAA;YACAtY,IAAA,CAAAuY,qBAAA,GAAApB,MAAA,CAAArT,IAAA,CAAAyU,qBAAA;YACAvY,IAAA,CAAAwY,gBAAA,GAAArB,MAAA,CAAArT,IAAA,CAAA0U,gBAAA;YACAxY,IAAA,CAAAyY,eAAA,GAAAtB,MAAA,CAAArT,IAAA,CAAA2U,eAAA;YACAzY,IAAA,CAAA0Y,cAAA,GAAAvB,MAAA,CAAArT,IAAA,CAAA6L,YAAA,CAAAyH,QAAA;YACApX,IAAA,CAAAsI,gBAAA,GAAA6O,MAAA,CAAArT,IAAA,CAAAwE,gBAAA;YACAtI,IAAA,CAAA2Y,eAAA,GAAAxB,MAAA,CAAArT,IAAA,CAAA6U,eAAA;YACA3Y,IAAA,CAAA4Y,WAAA,GAAAzB,MAAA,CAAArT,IAAA,CAAA+U,MAAA;YACA7Y,IAAA,CAAAO,WAAA,GAAA4W,MAAA,CAAA5W,WAAA;YACAP,IAAA,CAAA8Y,cAAA,GAAA3B,MAAA,CAAArT,IAAA,CAAA6E,YAAA;YACA3I,IAAA,CAAA+Y,iBAAA,GAAA5B,MAAA,CAAArT,IAAA,CAAA2E,eAAA;YACAzI,IAAA,CAAAM,UAAA,GAAA6W,MAAA,CAAArT,IAAA,CAAAxD,UAAA;YACAN,IAAA,CAAAoH,eAAA,GAAA+P,MAAA,CAAArT,IAAA,CAAAsD,eAAA;YACApH,IAAA,CAAAgZ,UAAA,GAAA7B,MAAA,CAAArT,IAAA,CAAAqF,WAAA;YACAnJ,IAAA,CAAA6I,KAAA,GAAAsO,MAAA,CAAArT,IAAA,CAAA+E,KAAA;YACA7I,IAAA,CAAAiZ,gBAAA,GAAA9B,MAAA,CAAArT,IAAA,CAAAmV,gBAAA;YACAjZ,IAAA,CAAAiJ,aAAA,GAAAkO,MAAA,CAAArT,IAAA,CAAAmF,aAAA;YACAjJ,IAAA,CAAAkZ,KAAA,GAAA/B,MAAA,CAAArT,IAAA,CAAAoV,KAAA;YACAlZ,IAAA,CAAA+I,iBAAA,GAAAoO,MAAA,CAAArT,IAAA,CAAAiF,iBAAA;YACA/I,IAAA,CAAAmZ,aAAA,GAAAhC,MAAA,CAAArT,IAAA,CAAAqV,aAAA;YACAnZ,IAAA,CAAAoZ,aAAA,GAAAjC,MAAA,CAAArT,IAAA,CAAAsV,aAAA;YACApZ,IAAA,CAAAqZ,WAAA,GAAAlC,MAAA,CAAArT,IAAA,CAAAuV,WAAA;YACArZ,IAAA,CAAAsZ,WAAA,GAAAnC,MAAA,CAAArT,IAAA,CAAAwV,WAAA;YACAtZ,IAAA,CAAAuZ,eAAA,GAAApC,MAAA,CAAArT,IAAA,CAAAyV,eAAA;YACAvZ,IAAA,CAAAwZ,kBAAA,GAAArC,MAAA,CAAArT,IAAA,CAAA0V,kBAAA;YACAxZ,IAAA,CAAAyZ,QAAA,GAAAtC,MAAA,CAAArT,IAAA,CAAA2V,QAAA;YACAzZ,IAAA,CAAA0Z,WAAA,GAAAvC,MAAA,CAAArT,IAAA,CAAA4V,WAAA;YACA1Z,IAAA,CAAA2Z,WAAA,GAAAxC,MAAA,CAAArT,IAAA,CAAA6V,WAAA;YACA3Z,IAAA,CAAA4Z,oBAAA,GAAAzC,MAAA,CAAArT,IAAA,CAAA8V,oBAAA;YACA5Z,IAAA,CAAA6Z,QAAA,GAAA1C,MAAA,CAAArT,IAAA,CAAA+V,QAAA;YACA7Z,IAAA,CAAA8Z,MAAA,GAAA3C,MAAA,CAAArT,IAAA,CAAAgW,MAAA;YACA9Z,IAAA,CAAA+Z,kBAAA,GAAA5C,MAAA,CAAArT,IAAA,CAAAiW,kBAAA;YACA;YACA/Z,IAAA,CAAAga,sBAAA,GAAA7C,MAAA,CAAArT,IAAA,CAAAkW,sBAAA;YACAha,IAAA,CAAAia,eAAA,GAAA9C,MAAA,CAAArT,IAAA,CAAAmW,eAAA;YACAja,IAAA,CAAAka,cAAA,GAAA/C,MAAA,CAAArT,IAAA,CAAAoW,cAAA;YACAla,IAAA,CAAAma,iBAAA,GAAAhD,MAAA,CAAArT,IAAA,CAAAqW,iBAAA;YACAna,IAAA,CAAAoa,UAAA,GAAAjD,MAAA,CAAArT,IAAA,CAAAsW,UAAA;YACApa,IAAA,CAAAqa,WAAA,GAAAlD,MAAA,CAAArT,IAAA,CAAAuW,WAAA;YACAra,IAAA,CAAAsa,qBAAA,GAAAnD,MAAA,CAAArT,IAAA,CAAAwW,qBAAA;YACAta,IAAA,CAAAua,YAAA,GAAApD,MAAA,CAAArT,IAAA,CAAAyW,YAAA;YACAva,IAAA,CAAAwa,oBAAA,GAAArD,MAAA,CAAArT,IAAA,CAAA0W,oBAAA;YACAxa,IAAA,CAAAya,cAAA,GAAAtD,MAAA,CAAArT,IAAA,CAAA2W,cAAA;YACAza,IAAA,CAAA0a,uBAAA,GAAAvD,MAAA,CAAArT,IAAA,CAAA4W,uBAAA;YACA1a,IAAA,CAAA2a,sBAAA,GAAAxD,MAAA,CAAArT,IAAA,CAAA6W,sBAAA;YACA3a,IAAA,CAAA4a,gBAAA,GAAAzD,MAAA,CAAArT,IAAA,CAAA8W,gBAAA;YACA5a,IAAA,CAAA6a,6BAAA,GAAA1D,MAAA,CAAArT,IAAA,CAAA+W,6BAAA;YACA7a,IAAA,CAAA8a,0BAAA,GAAA3D,MAAA,CAAArT,IAAA,CAAAgX,0BAAA;YACA9a,IAAA,CAAA+a,eAAA,GAAA5D,MAAA,CAAArT,IAAA,CAAAiX,eAAA;YACA/a,IAAA,CAAAgb,SAAA,GAAA7D,MAAA,CAAArT,IAAA,CAAAkX,SAAA;YACAhb,IAAA,CAAAib,mBAAA,GAAA9D,MAAA,CAAArT,IAAA,CAAAmX,mBAAA;YACAjb,IAAA,CAAAkb,YAAA,GAAA/D,MAAA,CAAArT,IAAA,CAAAoX,YAAA;YACAlb,IAAA,CAAAmb,SAAA,GAAAhE,MAAA,CAAArT,IAAA,CAAAqX,SAAA;YACAnb,IAAA,CAAAob,eAAA,GAAAjE,MAAA,CAAArT,IAAA,CAAAsX,eAAA;YACApb,IAAA,CAAAqb,iBAAA,GAAAlE,MAAA,CAAArT,IAAA,CAAAuX,iBAAA;YACArb,IAAA,CAAAsb,aAAA,GAAAnE,MAAA,CAAArT,IAAA,CAAAwX,aAAA;YACAtb,IAAA,CAAAub,WAAA,GAAApE,MAAA,CAAArT,IAAA,CAAAyX,WAAA;YACAvb,IAAA,CAAAwb,eAAA,GAAArE,MAAA,CAAArT,IAAA,CAAA0X,eAAA;YACAxb,IAAA,CAAAqB,OAAA,GAAA8V,MAAA,CAAArT,IAAA,CAAAzC,OAAA;YACArB,IAAA,CAAAyb,aAAA,GAAAtE,MAAA,CAAArT,IAAA,CAAA2X,aAAA;YACAzb,IAAA,CAAA0b,YAAA,GAAAvE,MAAA,CAAArT,IAAA,CAAA4X,YAAA;YACA1b,IAAA,CAAAoJ,gBAAA,GAAA+N,MAAA,CAAArT,IAAA,CAAAsF,gBAAA;YACApJ,IAAA,CAAAsB,gBAAA,GAAA6V,MAAA,CAAArT,IAAA,CAAAxC,gBAAA;YACAtB,IAAA,CAAAuB,eAAA,GAAA4V,MAAA,CAAArT,IAAA,CAAAvC,eAAA;YACAvB,IAAA,CAAA2b,cAAA,GAAAxE,MAAA,CAAArT,IAAA,CAAA6X,cAAA;YACA3b,IAAA,CAAA4b,gBAAA,GAAAzE,MAAA,CAAArT,IAAA,CAAA8X,gBAAA;YACA5b,IAAA,CAAA6b,qBAAA,GAAA1E,MAAA,CAAArT,IAAA,CAAA+X,qBAAA;YACA7b,IAAA,CAAAoB,WAAA,GAAA+V,MAAA,CAAArT,IAAA,CAAA1C,WAAA;YACApB,IAAA,CAAAuX,aAAA,GAAAJ,MAAA,CAAArT,IAAA,CAAAyT,aAAA;YACAvX,IAAA,CAAA8b,cAAA,GAAA3E,MAAA,CAAArT,IAAA,CAAAgY,cAAA;YACA9b,IAAA,CAAA+b,aAAA,GAAA5E,MAAA,CAAArT,IAAA,CAAAiY,aAAA;YACA/b,IAAA,CAAAwB,IAAA,GAAA2V,MAAA,CAAArT,IAAA,CAAAtC,IAAA;YACAxB,IAAA,CAAAyB,WAAA,GAAA0V,MAAA,CAAArT,IAAA,CAAArC,WAAA;YACAzB,IAAA,CAAA0B,OAAA,GAAAyV,MAAA,CAAArT,IAAA,CAAApC,OAAA;YACA1B,IAAA,CAAA2B,YAAA,GAAAwV,MAAA,CAAArT,IAAA,CAAAnC,YAAA;;YAEA;YACA,IAAAwV,MAAA,CAAArT,IAAA,CAAA8T,KAAA;cACAT,MAAA,CAAArT,IAAA,CAAAwT,eAAA;cACA;cACA,IAAA0E,cAAA,EAAAhc,IAAA,EAAAuF,IAAA,WAAA4B,QAAA;gBACAgQ,MAAA,CAAA8E,OAAA,CAAA9E,MAAA,CAAArT,IAAA,CAAA8T,KAAA;gBACAT,MAAA,CAAA+E,MAAA,CAAAC,UAAA;gBACAhF,MAAA,CAAAjV,IAAA;gBACAiV,MAAA,CAAArR,UAAA;cACA;cACA;YACA;cACAqR,MAAA,CAAArT,IAAA,CAAAwT,eAAA;cACA;cACA,IAAA8E,WAAA,EAAApc,IAAA,EAAAuF,IAAA,WAAA4B,QAAA;gBACAgQ,MAAA,CAAArT,IAAA,CAAA8T,KAAA,GAAAzQ,QAAA,CAAAnH,IAAA;gBACAmX,MAAA,CAAA8E,OAAA,CAAA9U,QAAA,CAAAnH,IAAA;gBACAmX,MAAA,CAAA+E,MAAA,CAAAC,UAAA;gBACAhF,MAAA,CAAAjV,IAAA;gBACAiV,MAAA,CAAArR,UAAA;cACA;YACA;UACA;QACA;QACA;QACA,IAAAqR,MAAA,CAAAlV,IAAA;UACA,IAAA4I,CAAA;YACAsM,MAAA,CAAArT,IAAA,CAAAuY,SAAA;UACA;UACA,IAAA1E,KAAA;YACA,IAAAR,MAAA,CAAArT,IAAA,CAAAuY,SAAA;cACAlF,MAAA,CAAArT,IAAA,CAAAwT,eAAA;cACA,IAAAgF,sBAAA,EAAAnF,MAAA,CAAArT,IAAA,EAAAyB,IAAA,WAAA4B,QAAA;gBACA,KAAAgQ,MAAA,CAAAtV,SAAA;kBACAsV,MAAA,CAAA8E,OAAA,CAAA9E,MAAA,CAAArT,IAAA,CAAAuY,SAAA;kBACAlF,MAAA,CAAA+E,MAAA,CAAAC,UAAA;kBACAhF,MAAA,CAAAjV,IAAA;kBACAiV,MAAA,CAAAvR,cAAA;gBACA;cACA;YACA;cACAuR,MAAA,CAAArT,IAAA,CAAAwT,eAAA;cACA,IAAAiF,mBAAA,EAAApF,MAAA,CAAArT,IAAA,EAAAyB,IAAA,WAAA4B,QAAA;gBACAgQ,MAAA,CAAArT,IAAA,CAAAuY,SAAA,GAAAlV,QAAA,CAAAnH,IAAA;gBACAmX,MAAA,CAAA8E,OAAA,CAAA9U,QAAA,CAAAnH,IAAA;gBACAmX,MAAA,CAAA+E,MAAA,CAAAC,UAAA;gBACAhF,MAAA,CAAAjV,IAAA;gBACAiV,MAAA,CAAAvR,cAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACA4W,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACA3a,IAAA;QACA4a,WAAA;MACA,GAAAtX,IAAA,WAAAuX,GAAA;QACA,IAAAA,GAAA;UACAL,OAAA,CAAA3Y,IAAA,CAAA3C,iBAAA,GAAAsb,OAAA,CAAA3Y,IAAA,CAAA3C,iBAAA,YAAAsb,OAAA,CAAA3Y,IAAA,CAAA3C,iBAAA,CAAA+D,MAAA,OAAAuX,OAAA,CAAA3Y,IAAA,CAAA3C,iBAAA,CAAAiW,QAAA;UACAqF,OAAA,CAAAhF,KAAA,SAAAC,QAAA,WAAAC,KAAA;YACA8E,OAAA,CAAA3Y,IAAA,CAAAuT,aAAA;YACAoF,OAAA,CAAA3Y,IAAA,CAAAwT,eAAA;YACA,IAAAK,KAAA;cACA,IAAA2E,sBAAA,EAAAG,OAAA,CAAA3Y,IAAA,EAAAyB,IAAA,WAAA4B,QAAA;gBACAsV,OAAA,CAAAP,MAAA,CAAAC,UAAA;gBACAM,OAAA,CAAAva,IAAA;gBACAua,OAAA,CAAA7W,cAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAqW,OAAA,WAAAA,QAAAhW,EAAA;MACA,KAAA8W,aAAA,CAAA9W,EAAA;MACA,KAAA+W,eAAA,CAAA/W,EAAA;MACA,KAAAgX,qBAAA,CAAAhX,EAAA;MACA,KAAAiX,mBAAA,CAAAjX,EAAA;IACA;IACA;IACA8W,aAAA,WAAAA,cAAA9W,EAAA;MAAA,IAAAkX,OAAA;MACA,SAAArZ,IAAA,CAAAuY,SAAA,iBAAAvY,IAAA,CAAA8T,KAAA;QACA,KAAArB,QAAA,CAAA6G,KAAA;MACA;QACA,KAAAtZ,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA+D,MAAA,YAAApB,IAAA,CAAA3C,iBAAA,CAAAiW,QAAA;QACA,SAAAnV,IAAA,uBAAAJ,SAAA;UACA,SAAAe,8BAAA,CAAAsC,MAAA;YACA,KAAAnB,kBAAA,CAAAsO,8BAAA,QAAAzP,8BAAA;UACA;UACA,KAAAmB,kBAAA,CAAAsY,SAAA,UAAApW,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAAuY,SAAA;UACA,KAAAtY,kBAAA,CAAAgL,MAAA;UACA,KAAAjL,IAAA,CAAAsO,+BAAA,QAAArO,kBAAA;UACA,IAAAsZ,6BAAA,OAAAvZ,IAAA,EAAAyB,IAAA;YACA,WAAAU,EAAA;cACAkX,OAAA,CAAA5G,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;QACA,SAAArb,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAAe,8BAAA,CAAAsC,MAAA;YACA,KAAAnB,kBAAA,CAAAyQ,0BAAA,QAAA5R,8BAAA;UACA;UACA,SAAAJ,eAAA,CAAA0C,MAAA;YACA,KAAAnB,kBAAA,CAAAwZ,qBAAA,QAAA/a,eAAA;UACA;UACA,SAAAG,kBAAA,CAAAuC,MAAA;YACA,KAAAnB,kBAAA,CAAA0Q,wBAAA,QAAA9R,kBAAA;UACA;UACA,KAAAoB,kBAAA,CAAA6T,KAAA,UAAA3R,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAA8T,KAAA;UACA,KAAA7T,kBAAA,CAAAgL,MAAA;UACA,KAAAjL,IAAA,CAAAyQ,2BAAA,QAAAxQ,kBAAA;UACA,KAAAD,IAAA,CAAA0Z,gBAAA,QAAAzZ,kBAAA;UACA;UACA,IAAA0Z,kBAAA;UACAA,kBAAA,CAAAtS,aAAA,QAAApH,kBAAA,CAAAgL,MAAA;UACA0O,kBAAA,CAAA7F,KAAA,GAAA3R,EAAA;UACAwX,kBAAA,CAAAlZ,KAAA,QAAAT,IAAA,CAAAS,KAAA;UACAkZ,kBAAA,CAAAjP,eAAA,QAAA1K,IAAA,CAAA0K,eAAA;UACA;UACAiP,kBAAA,CAAAC,yBAAA,QAAA5Z,IAAA,CAAA6Z,UAAA;UACAF,kBAAA,CAAAnU,SAAA,QAAAxF,IAAA,CAAAwF,SAAA;UACA;UACA;UACAmU,kBAAA,CAAAG,SAAA,QAAA9Z,IAAA,CAAAvD,WAAA;UACAkd,kBAAA,CAAAI,eAAA;UACA,KAAA/Z,IAAA,CAAA2Z,kBAAA,GAAAA,kBAAA;;UAEA;UACA,IAAAK,sBAAA,OAAAha,IAAA,EAAAyB,IAAA,WAAA4B,QAAA;YACA,WAAAlB,EAAA;cACAkX,OAAA,CAAA5G,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAN,eAAA,WAAAA,gBAAA/W,EAAA;MAAA,IAAA8X,OAAA;MACA,SAAAja,IAAA,CAAAuY,SAAA,iBAAAvY,IAAA,CAAA8T,KAAA;QACA,KAAArB,QAAA,CAAA6G,KAAA;MACA;QACA,KAAAtZ,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA+D,MAAA,YAAApB,IAAA,CAAA3C,iBAAA,CAAAiW,QAAA;QACA,SAAAnV,IAAA,uBAAAJ,SAAA;UACA,SAAAoB,gCAAA,CAAAiC,MAAA;YACA,KAAAlB,oBAAA,CAAAqO,8BAAA,QAAApP,gCAAA;UACA;UACA,KAAAe,oBAAA,CAAAqY,SAAA,UAAApW,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAAuY,SAAA;UACA,KAAArY,oBAAA,CAAA+K,MAAA;UACA,KAAAjL,IAAA,CAAAwO,6BAAA,QAAAtO,oBAAA;UACA,IAAAga,+BAAA,OAAAla,IAAA,EAAAyB,IAAA;YACA,WAAAU,EAAA;cACA8X,OAAA,CAAAxH,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;QACA,SAAArb,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAAoB,gCAAA,CAAAiC,MAAA;YACA,KAAAlB,oBAAA,CAAAwQ,0BAAA,QAAAvR,gCAAA;UACA;UACA,SAAAJ,iBAAA,CAAAqC,MAAA;YACA,KAAAlB,oBAAA,CAAAia,uBAAA,QAAApb,iBAAA;UACA;UACA,SAAAG,oBAAA,CAAAkC,MAAA;YACA,KAAAlB,oBAAA,CAAAyQ,wBAAA,QAAAzR,oBAAA;UACA;UACA,KAAAgB,oBAAA,CAAA4T,KAAA,UAAA3R,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAA8T,KAAA;UACA,KAAA5T,oBAAA,CAAA+K,MAAA;UACA,KAAAjL,IAAA,CAAA4Q,yBAAA,QAAA1Q,oBAAA;UACA;UACA,IAAAka,0BAAA,OAAApa,IAAA,EAAAyB,IAAA;YACA,WAAAU,EAAA;cACA8X,OAAA,CAAAxH,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAL,qBAAA,WAAAA,sBAAAhX,EAAA;MAAA,IAAAkY,OAAA;MACA,SAAAra,IAAA,CAAAuY,SAAA,iBAAAvY,IAAA,CAAA8T,KAAA;QACA,KAAArB,QAAA,CAAA6G,KAAA;MACA;QACA,KAAAtZ,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA+D,MAAA,YAAApB,IAAA,CAAA3C,iBAAA,CAAAiW,QAAA;QACA,SAAAnV,IAAA,uBAAAJ,SAAA;UACA,SAAAyB,sCAAA,CAAA4B,MAAA;YACA,KAAAjB,0BAAA,CAAAoO,8BAAA,QAAA/O,sCAAA;UACA;UACA,KAAAW,0BAAA,CAAAoY,SAAA,UAAApW,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAAuY,SAAA;UACA,KAAApY,0BAAA,CAAA8K,MAAA;UACA,KAAAjL,IAAA,CAAAyO,mCAAA,QAAAtO,0BAAA;UACA,IAAAma,qCAAA,OAAAta,IAAA,EAAAyB,IAAA;YACA,WAAAU,EAAA;cACAkY,OAAA,CAAA5H,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;QACA,SAAArb,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAAyB,sCAAA,CAAA4B,MAAA;YACA,KAAAjB,0BAAA,CAAAuQ,0BAAA,QAAAlR,sCAAA;UACA;UACA,SAAAD,0BAAA,CAAA6B,MAAA;YACA,KAAAjB,0BAAA,CAAAwQ,wBAAA,QAAApR,0BAAA;UACA;UACA,KAAAY,0BAAA,CAAA2T,KAAA,UAAA3R,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAA8T,KAAA;UACA,KAAA3T,0BAAA,CAAA8K,MAAA;UACA,KAAAjL,IAAA,CAAA6Q,+BAAA,QAAA1Q,0BAAA;UACA,IAAAoa,gCAAA,OAAAva,IAAA,EAAAyB,IAAA;YACA,WAAAU,EAAA;cACAkY,OAAA,CAAA5H,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAJ,mBAAA,WAAAA,oBAAAjX,EAAA;MAAA,IAAAqY,OAAA;MACA,SAAAxa,IAAA,CAAAuY,SAAA,iBAAAvY,IAAA,CAAA8T,KAAA;QACA,KAAArB,QAAA,CAAA6G,KAAA;MACA;QACA,KAAAtZ,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA+D,MAAA,YAAApB,IAAA,CAAA3C,iBAAA,CAAAiW,QAAA;QACA,SAAAnV,IAAA,uBAAAJ,SAAA;UACA,SAAA8B,oCAAA,CAAAuB,MAAA;YACA,KAAAhB,wBAAA,CAAAmO,8BAAA,QAAA1O,oCAAA;UACA;UACA,KAAAO,wBAAA,CAAAmY,SAAA,UAAApW,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAAuY,SAAA;UACA,KAAAnY,wBAAA,CAAA6K,MAAA;UACA,KAAAjL,IAAA,CAAA0O,iCAAA,QAAAtO,wBAAA;UACA,IAAAqa,mCAAA,OAAAza,IAAA,EAAAyB,IAAA;YACA,WAAAU,EAAA;cACAqY,OAAA,CAAA/H,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;QACA,SAAArb,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAA8B,oCAAA,CAAAuB,MAAA;YACA,KAAAhB,wBAAA,CAAAsQ,0BAAA,QAAA7Q,oCAAA;UACA;UACA,SAAAD,wBAAA,CAAAwB,MAAA;YACA,KAAAhB,wBAAA,CAAAuQ,wBAAA,QAAA/Q,wBAAA;UACA;UACA,KAAAQ,wBAAA,CAAA0T,KAAA,UAAA3R,EAAA,eAAAA,EAAA,QAAAnC,IAAA,CAAA8T,KAAA;UACA,KAAA1T,wBAAA,CAAA6K,MAAA;UACA,KAAAjL,IAAA,CAAA8Q,6BAAA,QAAA1Q,wBAAA;UACA,IAAAsa,8BAAA,OAAA1a,IAAA,EAAAyB,IAAA;YACA,WAAAU,EAAA;cACAqY,OAAA,CAAA/H,QAAA,CAAA+G,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAmB,iBAAA,WAAAA,kBAAAC,SAAA,EAAAC,qBAAA;MAAA,IAAAC,OAAA;MACA,KAAA7a,kBAAA,CAAAyQ,0BAAA,CAAAqK,GAAA,WAAAC,GAAA;QACA,IAAAC,OAAA;QACAA,OAAA,CAAAL,SAAA,GAAAvX,QAAA,CAAAnH,IAAA;QACA+e,OAAA,CAAAC,gBAAA,GAAAF,GAAA,CAAA/P,MAAA;QACAgQ,OAAA,CAAAE,QAAA,GAAAL,OAAA,CAAA9a,IAAA,CAAAS,KAAA;QACAwa,OAAA,CAAAG,mBAAA;QACAH,OAAA,CAAAI,iBAAA,GAAAL,GAAA,CAAAxX,QAAA;QACAyX,OAAA,CAAAK,sBAAA;QACAL,OAAA,CAAApS,mBAAA,GAAAmS,GAAA,CAAAnS,mBAAA;QACAoS,OAAA,CAAAM,cAAA,GAAAP,GAAA,CAAAjS,iBAAA;QACAkS,OAAA,CAAAO,cAAA,GAAAR,GAAA,CAAAS,qBAAA;QACAR,OAAA,CAAAS,UAAA,GAAAV,GAAA,CAAA7S,aAAA;QACA8S,OAAA,CAAAU,UAAA,GAAAX,GAAA,CAAAxR,iBAAA;QACAyR,OAAA,CAAAW,QAAA,GAAAZ,GAAA,CAAAlQ,eAAA;QACAmQ,OAAA,CAAAY,iBAAA,GAAAb,GAAA,CAAA7Q,qBAAA;QACA8Q,OAAA,CAAAa,QAAA,GAAAd,GAAA,CAAA3Q,gBAAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACAyQ,OAAA,CAAAha,UAAA,CAAA2G,IAAA,CAAAwT,OAAA;QACA,IAAAc,OAAA;QACAA,OAAA,CAAAnB,SAAA,GAAAvX,QAAA,CAAAnH,IAAA;QACA6f,OAAA,CAAAb,gBAAA,GAAAF,GAAA,CAAA/P,MAAA;QACA8Q,OAAA,CAAAZ,QAAA,GAAAL,OAAA,CAAA9a,IAAA,CAAAS,KAAA;QACAsb,OAAA,CAAAX,mBAAA;QACAW,OAAA,CAAAV,iBAAA,GAAAL,GAAA,CAAArQ,UAAA;QACAoR,OAAA,CAAAT,sBAAA;QACAS,OAAA,CAAAlT,mBAAA,GAAAmS,GAAA,CAAA5Q,iBAAA;QACA2R,OAAA,CAAAR,cAAA,GAAAP,GAAA,CAAA7R,eAAA;QACA4S,OAAA,CAAAP,cAAA,GAAAR,GAAA,CAAA/Q,mBAAA;QACA8R,OAAA,CAAAL,UAAA,GAAAV,GAAA,CAAAnR,WAAA;QACAkS,OAAA,CAAAJ,UAAA,GAAAX,GAAA,CAAApR,eAAA;QACAmS,OAAA,CAAAH,QAAA,GAAAZ,GAAA,CAAAhQ,aAAA;QACA+Q,OAAA,CAAAF,iBAAA,GAAAb,GAAA,CAAAjR,mBAAA;QACAgS,OAAA,CAAAD,QAAA,GAAAd,GAAA,CAAAzQ,cAAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACAuQ,OAAA,CAAAha,UAAA,CAAA2G,IAAA,CAAAsU,OAAA;MACA;IACA;IACA;IACAha,aAAA,WAAAA,cAAA;MACA;MACA,SAAAjF,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiF,eAAA,CAAAC,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAC,WAAA;QACA,KAAAxE,MAAA,CAAA0E,QAAA;MACA;MACA;MACA,SAAA1E,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA8f,UAAA,CAAA5a,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAmJ,MAAA;QACA,KAAA1N,MAAA,CAAA0E,QAAA;MACA;MACA;MACA,SAAA1E,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA+f,YAAA,CAAA7a,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAuJ,QAAA;QACA,KAAA9N,MAAA,CAAA0E,QAAA;MACA;MACA,KAAA0a,MAAA;MACA,KAAAC,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,cAAA;IACA;IACA;IACAH,MAAA,WAAAA,OAAA;MAAA,IAAAI,OAAA;MACA,SAAAxf,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAC,MAAA,CAAAiF,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAlF,MAAA;QACAoF,cAAA,CAAAC,QAAA,cAAAC,IAAA;UACA6a,OAAA,CAAAngB,MAAA,GAAAmgB,OAAA,CAAAxf,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAC,MAAA;QACA;MACA;QACA,KAAAA,MAAA,QAAAW,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAC,MAAA;MACA;IACA;IACA;IACAigB,SAAA,WAAAA,UAAA;MAAA,IAAAG,OAAA;MACA,SAAAzf,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAsgB,SAAA,CAAApb,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAmb,SAAA;QACAjb,cAAA,CAAAC,QAAA,iBAAAC,IAAA;UACA8a,OAAA,CAAAlgB,UAAA,GAAAkgB,OAAA,CAAAzf,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAsgB,SAAA;QACA;MACA;QACA,KAAAngB,UAAA,QAAAS,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAsgB,SAAA;MACA;IACA;IACA;IACAL,WAAA,WAAAA,YAAA;MAAA,IAAAM,OAAA;MACA,SAAA3f,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAwgB,mBAAA,CAAAtb,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAqb,mBAAA;QACAnb,cAAA,CAAAC,QAAA,+BAAAC,IAAA;UACAgb,OAAA,CAAAngB,WAAA,GAAAmgB,OAAA,CAAA3f,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAwgB,mBAAA;QACA;MACA;QACA,KAAApgB,WAAA,QAAAQ,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAwgB,mBAAA;MACA;IACA;IACA;IACAL,cAAA,WAAAA,eAAA;MAAA,IAAAM,OAAA;MACA,SAAA7f,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA0gB,cAAA,CAAAxb,MAAA,cAAAtE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmF,SAAA,CAAAub,cAAA;QACArb,cAAA,CAAAC,QAAA,sBAAAC,IAAA;UACAkb,OAAA,CAAAvgB,YAAA,GAAAugB,OAAA,CAAA7f,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA0gB,cAAA;QACA;MACA;QACA,KAAAxgB,YAAA,QAAAU,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA0gB,cAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAA7W,QAAA,KAAA6W,IAAA,CAAA7W,QAAA,CAAA7E,MAAA;QACA,OAAA0b,IAAA,CAAA7W,QAAA;MACA;MACA,IAAA8W,CAAA;MACA,IAAAD,IAAA,CAAAE,KAAA;QACA,IAAAF,IAAA,CAAAE,KAAA,CAAAC,oBAAA,YAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;UACA,IAAAJ,IAAA,CAAA/O,IAAA,CAAAC,aAAA;YACA+O,CAAA,GAAAD,IAAA,CAAA/O,IAAA,CAAAC,aAAA,SAAAmP,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAA/O,IAAA,CAAAC,aAAA;UACA;YACA+O,CAAA,GAAAD,IAAA,CAAAO,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAAO,IAAA,CAAAC,aAAA;UACA;QACA;UACAP,CAAA,GAAAD,IAAA,CAAAE,KAAA,CAAAO,SAAA,SAAAT,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA,SAAAJ,IAAA,CAAAE,KAAA,CAAAQ,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAJ,IAAA,CAAA7O,MAAA;QACA;UACA9L,EAAA,EAAA2a,IAAA,CAAA7O,MAAA;UACAwP,KAAA,EAAAV,CAAA;UACA9W,QAAA,EAAA6W,IAAA,CAAA7W,QAAA;UACAyX,UAAA,EAAAZ,IAAA,CAAAvZ,OAAA,YAAAuZ,IAAA,CAAA7W,QAAA,IAAAR;QACA;MACA;QACA;UACAtD,EAAA,EAAA2a,IAAA,CAAAtW,MAAA;UACAiX,KAAA,EAAAV,CAAA;UACA9W,QAAA,EAAA6W,IAAA,CAAA7W,QAAA;UACAyX,UAAA,EAAAZ,IAAA,CAAAvZ,OAAA,YAAAuZ,IAAA,CAAA7W,QAAA,IAAAR;QACA;MACA;IACA;IACAkY,iBAAA,WAAAA,kBAAAb,IAAA;MACA,IAAAA,IAAA,CAAA7W,QAAA,KAAA6W,IAAA,CAAA7W,QAAA,CAAA7E,MAAA;QACA,OAAA0b,IAAA,CAAA7W,QAAA;MACA;MACA,IAAA8W,CAAA;MACA,KAAAD,IAAA,CAAA5V,OAAA,IAAA4V,IAAA,CAAA5V,OAAA,CAAA0W,gBAAA,YAAAd,IAAA,CAAA5V,OAAA,CAAA2W,aAAA;QACAd,CAAA,GAAAD,IAAA,CAAAgB,gBAAA,SAAAhB,IAAA,CAAAiB,aAAA,SAAAZ,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAAgB,gBAAA,IAAArY,SAAA,GAAAqX,IAAA,CAAAgB,gBAAA;MACA;QACAf,CAAA,IAAAD,IAAA,CAAA5V,OAAA,CAAA8W,eAAA,WAAAlB,IAAA,CAAA5V,OAAA,CAAA8W,eAAA,gBAAAlB,IAAA,CAAA5V,OAAA,CAAA2W,aAAA,WAAAf,IAAA,CAAA5V,OAAA,CAAA2W,aAAA,gBAAAf,IAAA,CAAA5V,OAAA,CAAA0W,gBAAA,WAAAd,IAAA,CAAA5V,OAAA,CAAA0W,gBAAA,eAAAT,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAA5V,OAAA,CAAA0W,gBAAA,WAAAd,IAAA,CAAA5V,OAAA,CAAA0W,gBAAA;MACA;MACA;QACAzb,EAAA,EAAA2a,IAAA,CAAAzV,aAAA;QACAoW,KAAA,EAAAV,CAAA;QACA9W,QAAA,EAAA6W,IAAA,CAAA7W;MACA;IACA;IACAgY,MAAA,WAAAA,OAAA;MACA,KAAApc,KAAA;MACA,KAAAxD,OAAA;MACA,KAAAD,IAAA;MACA,KAAAN,eAAA;IACA;IACA+D,KAAA,WAAAA,MAAA;MACA,KAAA7B,IAAA;QACA8T,KAAA;QACArT,KAAA;QACAyd,SAAA;QACAxgB,IAAA;QACAC,WAAA;QACAC,OAAA;QACAC,YAAA;QACA+Z,YAAA;QACAC,cAAA;QACA3T,WAAA;QACAE,aAAA;QACA7G,OAAA;QACAC,gBAAA;QACAC,eAAA;QACAH,WAAA;QACAmW,aAAA;QACAO,aAAA;QACAmK,aAAA;QACAhK,aAAA;QACAX,eAAA;QACAhQ,QAAA;QACAE,YAAA;QACAE,eAAA;QACAE,kBAAA;QACAE,oBAAA;QACA3G,iBAAA;QACAiH,YAAA;QACA8Z,uBAAA;QACA7J,cAAA;QACA8J,gBAAA;QACA3J,gBAAA;QACAC,eAAA;QACAnQ,gBAAA;QACAqQ,eAAA;QACApY,WAAA;QACAoI,YAAA;QACAkQ,MAAA;QACAuJ,YAAA;QACAzS,YAAA;QACArP,UAAA;QACAmI,eAAA;QACAmV,SAAA;QACAzU,WAAA;QACA/B,eAAA;QACAyB,KAAA;QACAE,iBAAA;QACA7H,UAAA;QACAmhB,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,KAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;QACAC,eAAA;QACAC,WAAA;QACAC,SAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;QACAC,eAAA;QACA9T,cAAA;QACAV,gBAAA;QACA5F,gBAAA;QACAqG,aAAA;QACAgU,kBAAA;QACAC,cAAA;QACA3H,aAAA;QACA4H,eAAA;QACAC,WAAA;QACAC,QAAA;QACAlG,UAAA;QACAmG,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,MAAA;QACAC,aAAA;QACAC,eAAA;QACArL,aAAA;QACAsL,eAAA;QACAC,GAAA;QACAtL,aAAA;QACAuL,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,WAAA;QACAC,MAAA;QACA7b,MAAA;QACAmF,eAAA;MACA;MACA,KAAAzK,kBAAA;QACAohB,mBAAA;QACAvN,KAAA;QACAxQ,eAAA;QACAnG,SAAA;QACA4H,KAAA;QACAuc,kBAAA;QACA7L,eAAA;QACAC,kBAAA;QACAC,QAAA;QACAJ,WAAA;QACAC,WAAA;QACAL,gBAAA;QACAoM,gBAAA;QACAzL,oBAAA;QACAC,QAAA;QACAH,WAAA;QACAC,WAAA;QACA1Q,aAAA;QACAiQ,KAAA;QACAY,MAAA;QACA/Q,iBAAA;QACAgR,kBAAA;QACAuL,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAAtiB,oBAAA;QACAuiB,iBAAA;QACA3O,KAAA;QACAxQ,eAAA;QACAof,mBAAA;QACAC,kBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAvB,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAAriB,0BAAA;QACA6iB,mBAAA;QACAlP,KAAA;QACAxQ,eAAA;QACA2f,gBAAA;QACAC,eAAA;QACAC,YAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,mBAAA;QACAlC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAApiB,wBAAA;QACAujB,iBAAA;QACA7P,KAAA;QACA8P,mBAAA;QACAC,mBAAA;QACArC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAA/lB,WAAA;MACA,KAAAD,UAAA;MACA,KAAAW,SAAA;MACA,KAAAE,iBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,eAAA;MACA,KAAAC,IAAA;MACA,KAAAC,WAAA;MACA,KAAAC,OAAA;MACA,KAAAC,YAAA;MACA,KAAAT,UAAA;MACA,KAAAkB,WAAA;MACA,KAAAC,eAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,eAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,8BAAA;MACA,KAAAC,iBAAA;MACA,KAAAE,qBAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,gCAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,uBAAA;MACA,KAAAE,0BAAA;MACA,KAAAC,sCAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,qBAAA;MACA,KAAAE,wBAAA;MACA,KAAAC,oCAAA;MACA,KAAAiB,UAAA;MACA,KAAAgjB,SAAA;IACA;IACAC,sBAAA,WAAAA,uBAAAjH,IAAA;MACA,KAAA9c,IAAA,CAAA5C,UAAA,CAAAqK,IAAA,CAAAqV,IAAA,CAAA5V,OAAA,CAAA/J,SAAA;IACA;IACA6mB,wBAAA,WAAAA,yBAAAlH,IAAA;MACA,KAAA9c,IAAA,CAAA5C,UAAA,QAAA4C,IAAA,CAAA5C,UAAA,CAAA6mB,MAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,IAAApH,IAAA,CAAA5V,OAAA,CAAA/J,SAAA;MACA;IACA;IACA;AACA;AACA;AACA;IACAgnB,WAAA,WAAAA,YAAApd,CAAA;MAAA,IAAAqd,OAAA;MACA,IAAArd,CAAA;QACA,IAAAsd,iBAAA,IAAA5iB,IAAA,WAAAsF,CAAA;UACA,IAAAoL,GAAA,GAAApL,CAAA,CAAA7K,IAAA;UACA,IAAAiW,GAAA,CAAAmB,QAAA,GAAAlS,MAAA;YACA,IAAAkjB,CAAA,OAAAnS,GAAA,CAAAmB,QAAA,GAAAlS,MAAA;YACA,SAAAmjB,CAAA,MAAAA,CAAA,GAAAD,CAAA,EAAAC,CAAA;cACApS,GAAA,SAAAA,GAAA;YACA;UACA;UACA,IAAAqS,IAAA,OAAAngB,IAAA;UACA,IAAA9D,KAAA,IAAAikB,IAAA,CAAAC,QAAA,KAAAvc,MAAA,CAAAkc,OAAA,CAAA/jB,GAAA,CAAAE,KAAA,GAAA+S,QAAA;UACA,IAAAoR,IAAA,IAAAF,IAAA,CAAAG,WAAA,MAAApkB,KAAA,oBAAA+S,QAAA,GAAAsR,SAAA;UACAR,OAAA,CAAA/jB,GAAA,CAAAI,KAAA,GAAA2jB,OAAA,CAAA/jB,GAAA,CAAAC,gBAAA,GAAAokB,IAAA,IAAAnkB,KAAA,CAAAa,MAAA,cAAAb,KAAA,GAAAA,KAAA,IAAA4R,GAAA,CAAAmB,QAAA;QACA;MACA;QACA,KAAAxV,eAAA;MACA;IACA;IACA+mB,UAAA,WAAAA,WAAA;MACA,KAAA7kB,IAAA,CAAAS,KAAA,QAAAJ,GAAA,CAAAI,KAAA;MACA,KAAA3C,eAAA;IACA;EACA;AACA;AAAAgnB,OAAA,CAAAliB,OAAA,GAAAmiB,QAAA"}]}