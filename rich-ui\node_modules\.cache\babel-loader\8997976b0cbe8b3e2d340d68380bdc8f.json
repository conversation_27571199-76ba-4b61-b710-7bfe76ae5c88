{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\DynamicSearch\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\DynamicSearch\\index.vue", "mtime": 1745402694757}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "_vueTreeselect", "_store", "_js<PERSON><PERSON>yin", "_LocationSelect", "_aggregator", "_default", "name", "components", "Treeselect", "LocationSelect", "props", "searchFields", "type", "Object", "required", "configType", "String", "data", "searchConditions", "remoteData", "treeData", "belongList", "businessList", "opList", "config<PERSON><PERSON>", "configDialogVisible", "configLoading", "savedConfigs", "searchDialogVisible", "clickTimer", "computed", "availableFields", "usedFields", "Set", "map", "c", "field", "entries", "_ref", "_ref2", "_slicedToArray2", "default", "key", "config", "label", "disabled", "has", "filter", "methods", "handleTreeSelect", "value", "index", "handleTreeSelectClose", "handleCompanySelect", "handleLocationSelect", "getFieldConfig", "addCondition", "push", "options", "loading", "treeValue", "treeOptions", "removeCondition", "splice", "handleFieldChange", "_this", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "condition", "fieldConfig", "wrap", "_callee$", "_context", "prev", "next", "remote", "fetchRemoteOptions", "stop", "handleTreeselectOpen", "_this2", "loadMethod", "result", "then", "dataSource", "$store", "state", "length", "handleTreeselectSelect", "node", "valueField", "undefined", "staffId", "id", "handleTreeselectInput", "renderValueSlot", "valueR<PERSON><PERSON>", "raw", "staff", "staffFamilyLocalName", "staffGivingLocalName", "staffGivingEnName", "renderOptionSlot", "option<PERSON><PERSON><PERSON>", "indexOf", "substring", "handleRemoteSearch", "query", "_this3", "_callee2", "_callee2$", "_context2", "find", "abrupt", "finish", "_arguments", "arguments", "_this4", "_callee3", "url", "response", "_callee3$", "_context3", "remoteMethodsMap", "request", "method", "params", "sent", "code", "t0", "console", "error", "$message", "handleSearch", "searchParams", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "err", "e", "f", "$emit", "handleReset", "staffNormalizer", "children", "l", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "roleId", "isDisabled", "deptId", "loadSales", "_this5", "salesList", "redisList", "store", "dispatch", "loadBusinesses", "_this6", "businessesList", "loadOp", "_this7", "getNormalizer", "normalizer", "saveConfig", "_this8", "_callee4", "configToSave", "_callee4$", "_context4", "warning", "saveAggregatorConfig", "success", "message", "loadConfigs", "_this9", "_callee5", "_err$response", "_callee5$", "_context5", "loadAggregatorConfigs", "rows", "handleConfigSelect", "row", "_this10", "_callee6", "_iterator2", "_step2", "newCondition", "_callee6$", "_context6", "Error", "JSON", "parse", "Array", "isArray", "t1", "deleteConfig", "_this11", "_callee7", "_callee7$", "_context7", "$confirm", "deleteAggregatorConfig", "handleButtonClick", "event", "_this12", "clearTimeout", "setTimeout", "openSearchDialog", "handleSearchAndClose", "exports"], "sources": ["src/components/DynamicSearch/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!-- 触发按钮 -->\r\n    <el-button icon=\"el-icon-search\" type=\"primary\" @click=\"handleButtonClick\">{{ configName ? configName : '筛选' }}\r\n    </el-button>\r\n    <el-button icon=\"el-icon-edit\" type=\"primary\" @click=\"searchDialogVisible = true\">{{ '修改' }}</el-button>\r\n\r\n    <!-- 最外层弹出对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"searchDialogVisible\"\r\n      append-to-body\r\n      title=\"高级搜索\"\r\n      width=\"400px\"\r\n    >\r\n      <div class=\"dynamic-search\">\r\n        <el-form>\r\n          <el-row>\r\n            <!-- 搜索条件区域 -->\r\n            <el-col>\r\n              <el-form-item>\r\n                <el-row :gutter=\"10\" align=\"middle\" type=\"flex\">\r\n                  <el-col :span=\"18\">\r\n                    <el-input v-model=\"configName\" placeholder=\"请输入速查名称\"/>\r\n                  </el-col>\r\n                  <el-col :span=\"3\">\r\n                    <el-button size=\"small\" type=\"text\" @click=\"saveConfig\">[↗]</el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"3\">\r\n                    <el-button size=\"small\" type=\"text\" @click=\"loadConfigs\">[...]</el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n\r\n            <!--字段选择-->\r\n            <el-col>\r\n              <div v-for=\"(condition, index) in searchConditions\"\r\n                   :key=\"index\"\r\n              >\r\n                <el-row>\r\n                  <!-- 未选择字段时显示字段选择器 -->\r\n                  <template v-if=\"!condition.field\">\r\n                    <el-select\r\n                      v-model=\"condition.field\"\r\n                      filterable\r\n                      class=\"field-select\"\r\n                      placeholder=\"请选择搜索字段\"\r\n                      @change=\"handleFieldChange(index)\"\r\n                    >\r\n                      <el-option\r\n                        v-for=\"field in availableFields\"\r\n                        :key=\"field.key\"\r\n                        :label=\"field.label\"\r\n                        :value=\"field.key\"\r\n                      />\r\n                    </el-select>\r\n                  </template>\r\n\r\n                  <!-- 选择字段后显示表单项 -->\r\n                  <template v-else>\r\n                    <el-form-item\r\n                      :label=\"getFieldConfig(condition.field).label\"\r\n                    >\r\n                      <el-row>\r\n                        <el-col :span=\"16\">\r\n                          <!-- 文本输入 -->\r\n                          <el-input\r\n                            v-if=\"getFieldConfig(condition.field).type === 'input'\"\r\n                            v-model=\"condition.value\"\r\n                            :placeholder=\"getFieldConfig(condition.field).placeholder\"\r\n                            class=\"value-input\"\r\n                          />\r\n\r\n                          <!-- 日期选择 -->\r\n                          <el-date-picker\r\n                            v-else-if=\"getFieldConfig(condition.field).type === 'date'\"\r\n                            v-model=\"condition.value\"\r\n                            class=\"value-input\"\r\n                            end-placeholder=\"结束\"\r\n                            range-separator=\"至\"\r\n                            start-placeholder=\"开始\"\r\n                            style=\"width: 100%\"\r\n                            type=\"daterange\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                          />\r\n\r\n                          <!-- 下拉选择 -->\r\n                          <el-select\r\n                            v-else-if=\"getFieldConfig(condition.field).type === 'select'\"\r\n                            v-model=\"condition.value\"\r\n                            :loading=\"condition.loading\"\r\n                            :placeholder=\"getFieldConfig(condition.field).placeholder\"\r\n                            :remote=\"getFieldConfig(condition.field).remote\"\r\n                            :remote-method=\"(query) => handleRemoteSearch(query, condition.field)\"\r\n                            class=\"value-input\"\r\n                            clearable\r\n                            filterable\r\n                          >\r\n                            <el-option\r\n                              v-for=\"option in condition.options\"\r\n                              :key=\"option.value\"\r\n                              :label=\"option.label\"\r\n                              :value=\"option.value\"\r\n                            />\r\n                          </el-select>\r\n\r\n                          <!-- 树形选择器 -->\r\n                          <treeselect\r\n                            v-else-if=\"getFieldConfig(condition.field).type === 'treeselect'\"\r\n                            v-model=\"condition.treeValue\"\r\n                            :disable-branch-nodes=\"getFieldConfig(condition.field).disableBranchNodes || true\"\r\n                            :disabled-fuzzy-matching=\"getFieldConfig(condition.field).disabledFuzzyMatching || true\"\r\n                            :flatten-search-results=\"getFieldConfig(condition.field).flattenSearchResults || true\"\r\n                            :normalizer=\"getNormalizer(condition.field)\"\r\n                            :options=\"condition.treeOptions || []\"\r\n                            :placeholder=\"getFieldConfig(condition.field).placeholder\"\r\n                            :show-count=\"getFieldConfig(condition.field).showCount || true\"\r\n                            class=\"value-input\"\r\n                            @input=\"handleTreeselectInput(index, $event)\"\r\n                            @open=\"handleTreeselectOpen(index, condition.field)\"\r\n                            @select=\"handleTreeselectSelect(index, $event)\"\r\n                          >\r\n                            <template v-if=\"getFieldConfig(condition.field).valueSlot\" #value-label=\"{node}\">\r\n                              {{ renderValueSlot(condition.field, node) }}\r\n                            </template>\r\n                            <template v-if=\"getFieldConfig(condition.field).optionSlot\"\r\n                                      #option-label=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                            >\r\n                                <span :class=\"labelClassName\">\r\n                                  {{ renderOptionSlot(condition.field, node) }}\r\n                                  <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                                </span>\r\n                            </template>\r\n                          </treeselect>\r\n\r\n                          <!-- 位置选择器 -->\r\n                          <location-select\r\n                            v-else-if=\"getFieldConfig(condition.field).type === 'location'\"\r\n                            v-model=\"condition.value\"\r\n                            :en=\"getFieldConfig(condition.field).en\"\r\n                            :multiple=\"getFieldConfig(condition.field).multiple\"\r\n                            :pass=\"condition.value\"\r\n                            :placeholder=\"getFieldConfig(condition.field).placeholder\"\r\n                            @return=\"handleLocationSelect($event, index)\"\r\n                          />\r\n\r\n                          <!-- 公司选择器 -->\r\n                          <company-select\r\n                            v-else-if=\"getFieldConfig(condition.field).type === 'company'\"\r\n                            :multiple=\"getFieldConfig(condition.field).multiple\"\r\n                            :no-parent=\"getFieldConfig(condition.field).noParent\"\r\n                            :pass=\"condition.value\"\r\n                            :placeholder=\"getFieldConfig(condition.field).placeholder\"\r\n                            :role-client=\"getFieldConfig(condition.field).roleClient\"\r\n                            :roleTypeId=\"getFieldConfig(condition.field).roleTypeId\"\r\n                            @return=\"handleCompanySelect($event, index)\"\r\n                          />\r\n\r\n                          <!-- 自定义树形选择器 -->\r\n                          <tree-select\r\n                            v-else-if=\"getFieldConfig(condition.field).type === 'tree-select'\"\r\n                            v-model=\"condition.value\"\r\n                            :d-load=\"getFieldConfig(condition.field).dLoad\"\r\n                            :flat=\"getFieldConfig(condition.field).flat || false\"\r\n                            :multiple=\"getFieldConfig(condition.field).multiple || false\"\r\n                            :pass=\"condition.value\"\r\n                            :placeholder=\"getFieldConfig(condition.field).placeholder\"\r\n                            :type=\"getFieldConfig(condition.field).treeType\"\r\n                            :type-id=\"getFieldConfig(condition.field).typeId\"\r\n                            @close=\"handleTreeSelectClose\"\r\n                            @return=\"handleTreeSelect($event, index)\"\r\n                          />\r\n                        </el-col>\r\n                        <el-col :span=\"1\">\r\n                          <!-- 删除按钮 -->\r\n                          <el-button\r\n                            class=\"remove-btn\"\r\n                            icon=\"el-icon-delete\"\r\n                            style=\"color: red\"\r\n                            type=\"text\"\r\n                            @click=\"removeCondition(index)\"\r\n                          />\r\n                        </el-col>\r\n                      </el-row>\r\n                    </el-form-item>\r\n                  </template>\r\n                </el-row>\r\n              </div>\r\n              <el-button type=\"text\" @click=\"addCondition\">[+]</el-button>\r\n\r\n              <!-- 操作按钮 -->\r\n              <el-row>\r\n                <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n                <el-button @click=\"handleReset\">重置</el-button>\r\n              </el-row>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form>\r\n\r\n\r\n      </div>\r\n\r\n      <!--      <div slot=\"footer\" class=\"dialog-footer\">\r\n              <el-button @click=\"searchDialogVisible = false\">取消</el-button>\r\n              <el-button type=\"primary\" @click=\"handleSearchAndClose\">确定</el-button>\r\n            </div>-->\r\n    </el-dialog>\r\n    <!-- 添加加载配置对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"configDialogVisible\"\r\n      append-to-body\r\n      title=\"加载配置\"\r\n      width=\"500px\"\r\n    >\r\n      <el-table\r\n        v-loading=\"configLoading\"\r\n        :data=\"savedConfigs\"\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleConfigSelect\"\r\n      >\r\n        <el-table-column label=\"配置名称\" prop=\"name\"/>\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\"/>\r\n        <el-table-column width=\"120\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" @click.stop=\"deleteConfig(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport request from \"@/utils/request\"\r\nimport Treeselect from \"@riophae/vue-treeselect\" // 导入树形选择器组件\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\" // 导入样式\r\nimport store from \"@/store\"\r\nimport pinyin from \"js-pinyin\" // 导入状态管理\r\nimport LocationSelect from \"@/components/LocationSelect\" // 导入位置选择器组件\r\nimport {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from \"@/api/system/aggregator\"\r\n\r\nexport default {\r\n  name: \"DynamicSearch\",\r\n  components: {\r\n    Treeselect, // 注册树形选择器组件\r\n    LocationSelect // 注册位置选择器组件\r\n  },\r\n  props: {\r\n    // 从父组件接收搜索字段配置\r\n    searchFields: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    configType: {\r\n      type: String,\r\n      required: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      searchConditions: [], // 搜索条件列表\r\n      remoteData: {}, // 缓存远程获取的选项数据\r\n      treeData: {}, // 缓存树形数据\r\n      belongList: [],\r\n      businessList: [],\r\n      opList: [],\r\n      configName: \"\",\r\n      configDialogVisible: false,\r\n      configLoading: false,\r\n      savedConfigs: [],\r\n      searchDialogVisible: false,\r\n      clickTimer: null\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 可用的搜索字段\r\n    availableFields() {\r\n      const usedFields = new Set(this.searchConditions.map(c => c.field))\r\n      return Object.entries(this.searchFields).map(([key, config]) => ({\r\n        key,\r\n        label: config.label,\r\n        disabled: usedFields.has(key)\r\n      })).filter(field => !field.disabled)\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    // 处理自定义树形选择器选择\r\n    handleTreeSelect(value, index) {\r\n      this.searchConditions[index].value = value\r\n    },\r\n\r\n// 处理自定义树形选择器关闭\r\n    handleTreeSelectClose() {\r\n      // 可以添加关闭时的处理逻辑\r\n    },\r\n    // 处理公司选择\r\n    handleCompanySelect(value, index) {\r\n      this.searchConditions[index].value = value\r\n    },\r\n    // 处理位置选择\r\n    handleLocationSelect(value, index) {\r\n      this.searchConditions[index].value = value\r\n    },\r\n    // 获取字段配置\r\n    getFieldConfig(field) {\r\n      return this.searchFields[field] || {}\r\n    },\r\n\r\n    // 添加搜索条件\r\n    addCondition() {\r\n      this.searchConditions.push({\r\n        field: \"\",\r\n        value: \"\",\r\n        options: [],\r\n        loading: false,\r\n        treeValue: null,\r\n        treeOptions: []\r\n      })\r\n    },\r\n\r\n    // 移除搜索条件\r\n    removeCondition(index) {\r\n      this.searchConditions.splice(index, 1)\r\n    },\r\n\r\n    // 处理字段变更\r\n    async handleFieldChange(index) {\r\n      const condition = this.searchConditions[index]\r\n      const fieldConfig = this.getFieldConfig(condition.field)\r\n\r\n      // 根据字段类型初始化值\r\n      if (fieldConfig.type === \"date\") {\r\n        condition.value = [] // 初始化为空数组，用于日期范围\r\n      } else if (fieldConfig.type === \"treeselect\") {\r\n        condition.treeValue = null // 初始化树形选择器的值\r\n        condition.treeOptions = [] // 初始化树形选择器的选项\r\n        condition.value = null // 实际存储的值\r\n      } else if (fieldConfig.type === \"treeselect\" || fieldConfig.type === \"tree-select\") {\r\n        condition.treeValue = null\r\n        condition.treeOptions = []\r\n        condition.value = null\r\n      } else {\r\n        condition.value = \"\"\r\n      }\r\n\r\n      condition.options = []\r\n\r\n      if (fieldConfig.type === \"select\") {\r\n        if (fieldConfig.remote) {\r\n          // 如果是远程选项且没有缓存，则获取选项数据\r\n          if (!this.remoteData[condition.field]) {\r\n            await this.fetchRemoteOptions(condition.field)\r\n          }\r\n          condition.options = this.remoteData[condition.field] || []\r\n        } else {\r\n          // 使用静态选项\r\n          condition.options = fieldConfig.options || []\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理树形选择器打开事件\r\n    handleTreeselectOpen(index, field) {\r\n      const fieldConfig = this.getFieldConfig(field)\r\n      const condition = this.searchConditions[index]\r\n\r\n      if (fieldConfig.loadMethod) {\r\n        // 直接使用组件内定义的方法\r\n        if (typeof fieldConfig.loadMethod === \"string\" && typeof this[fieldConfig.loadMethod] === \"function\") {\r\n          // 调用组件内的加载方法\r\n          const result = this[fieldConfig.loadMethod]()\r\n\r\n          if (result && typeof result.then === \"function\") {\r\n            // 如果返回Promise\r\n            result.then(data => {\r\n              if (data) {\r\n                // 直接使用返回的数据\r\n                condition.treeOptions = data\r\n              } else if (fieldConfig.dataSource && this.$store.state.data[fieldConfig.dataSource]) {\r\n                // 或从store获取数据\r\n                condition.treeOptions = this.$store.state.data[fieldConfig.dataSource]\r\n              }\r\n            })\r\n          } else {\r\n            // 直接使用返回的数据\r\n            condition.treeOptions = result\r\n\r\n            // 如果直接返回数据为空，但dataSource有值，尝试从store获取\r\n            if ((!result || result.length === 0) && fieldConfig.dataSource && this.$store.state.data[fieldConfig.dataSource]) {\r\n              condition.treeOptions = this.$store.state.data[fieldConfig.dataSource]\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理树形选择器选择事件\r\n    handleTreeselectSelect(index, node) {\r\n      const condition = this.searchConditions[index]\r\n      const fieldConfig = this.getFieldConfig(condition.field)\r\n\r\n      if (fieldConfig.valueField && node[fieldConfig.valueField] !== undefined) {\r\n        condition.value = node[fieldConfig.valueField]\r\n      } else if (node.staffId !== undefined) {\r\n        condition.value = node.staffId\r\n      } else {\r\n        condition.value = node.id\r\n      }\r\n    },\r\n\r\n    // 处理树形选择器输入事件\r\n    handleTreeselectInput(index, value) {\r\n      const condition = this.searchConditions[index]\r\n\r\n      if (value === undefined || value === null) {\r\n        condition.value = null\r\n      }\r\n    },\r\n\r\n    // 渲染值槽\r\n    renderValueSlot(field, node) {\r\n      const fieldConfig = this.getFieldConfig(field)\r\n\r\n      if (fieldConfig.valueRenderer && typeof fieldConfig.valueRenderer === \"function\") {\r\n        return fieldConfig.valueRenderer(node)\r\n      }\r\n\r\n      // 默认值渲染\r\n      if (node.raw && node.raw.staff) {\r\n        return node.raw.staff.staffFamilyLocalName +\r\n          node.raw.staff.staffGivingLocalName +\r\n          \" \" +\r\n          node.raw.staff.staffGivingEnName\r\n      }\r\n\r\n      return node.label\r\n    },\r\n\r\n    // 渲染选项槽\r\n    renderOptionSlot(field, node) {\r\n      const fieldConfig = this.getFieldConfig(field)\r\n\r\n      if (fieldConfig.optionRenderer && typeof fieldConfig.optionRenderer === \"function\") {\r\n        return fieldConfig.optionRenderer(node)\r\n      }\r\n\r\n      // 默认选项渲染\r\n      if (node.label && node.label.indexOf(\",\") !== -1) {\r\n        return node.label.substring(0, node.label.indexOf(\",\"))\r\n      }\r\n\r\n      return node.label\r\n    },\r\n\r\n    // 处理远程搜索\r\n    async handleRemoteSearch(query, field) {\r\n      const condition = this.searchConditions.find(c => c.field === field)\r\n      if (!condition) return\r\n\r\n      condition.loading = true\r\n      try {\r\n        await this.fetchRemoteOptions(field, query)\r\n        condition.options = this.remoteData[field] || []\r\n      } finally {\r\n        condition.loading = false\r\n      }\r\n    },\r\n\r\n    // 获取远程选项数据\r\n    async fetchRemoteOptions(field, query = \"\") {\r\n      const fieldConfig = this.getFieldConfig(field)\r\n      if (!fieldConfig.remote || !fieldConfig.options) return\r\n\r\n      const url = remoteMethodsMap[fieldConfig.options]\r\n      if (!url) return\r\n\r\n      try {\r\n        const response = await request({\r\n          url,\r\n          method: \"get\",\r\n          params: {query}\r\n        })\r\n\r\n        if (response.code === 200) {\r\n          this.remoteData[field] = response.data\r\n        }\r\n      } catch (error) {\r\n        console.error(\"获取远程选项失败:\", error)\r\n        this.$message.error(\"获取选项数据失败\")\r\n      }\r\n    },\r\n\r\n    // 处理搜索\r\n    handleSearch() {\r\n      const searchParams = {}\r\n      for (const condition of this.searchConditions) {\r\n        if (condition.field && condition.value !== \"\" && condition.value !== null) {\r\n          const fieldConfig = this.getFieldConfig(condition.field)\r\n\r\n          // 处理日期范围\r\n          /* if (fieldConfig.type === \"date\" && Array.isArray(condition.value) && condition.value.length === 2) {\r\n            searchParams[condition.field + \"Start\"] = condition.value[0]\r\n            searchParams[condition.field + \"End\"] = condition.value[1]\r\n          } else {\r\n            searchParams[condition.field] = condition.value\r\n          } */\r\n          searchParams[condition.field] = condition.value\r\n        }\r\n      }\r\n\r\n      this.$emit(\"search\", searchParams)\r\n    },\r\n\r\n    // 处理重置\r\n    handleReset() {\r\n      this.searchConditions = []\r\n      this.configName = \"\"\r\n      this.$emit(\"reset\")\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        return store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n          return this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n        return this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        return store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n          return this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n        return this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        return store.dispatch(\"getOpList\").then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n          return this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n        return this.$store.state.data.opList\r\n      }\r\n    },\r\n    getNormalizer(field) {\r\n      const fieldConfig = this.getFieldConfig(field)\r\n      if (fieldConfig.normalizer && typeof fieldConfig.normalizer === \"string\") {\r\n        return this[fieldConfig.normalizer]\r\n      } else if (fieldConfig.normalizer && typeof fieldConfig.normalizer === \"function\") {\r\n        return fieldConfig.normalizer\r\n      } else {\r\n        return this.staffNormalizer\r\n      }\r\n    },\r\n    // 保存配置\r\n    async saveConfig() {\r\n      try {\r\n        // 验证配置名称\r\n        if (!this.configName) {\r\n          this.$message.warning(\"请输入速查名称\")\r\n          return\r\n        }\r\n\r\n        // 验证是否有搜索条件\r\n        if (!this.searchConditions.length) {\r\n          this.$message.warning(\"请添加至少一个搜索条件\")\r\n          return\r\n        }\r\n\r\n        // 构造保存的配置数据\r\n        const configToSave = {\r\n          name: this.configName,\r\n          type: this.configType,\r\n          config: {\r\n            searchConditions: this.searchConditions.map(condition => ({\r\n              field: condition.field,\r\n              value: condition.value,\r\n              type: this.getFieldConfig(condition.field).type\r\n            }))\r\n          }\r\n        }\r\n\r\n        // 发送保存请求\r\n        await saveAggregatorConfig(configToSave)\r\n        this.$message.success(\"配置保存成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"保存配置失败：\" + (err.message || \"未知错误\"))\r\n        }\r\n      }\r\n    },\r\n    // 加载配置列表\r\n    async loadConfigs() {\r\n      this.configLoading = true\r\n      this.configDialogVisible = true\r\n      try {\r\n        const result = await loadAggregatorConfigs({configType: this.configType})\r\n        this.savedConfigs = result.rows\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\r\n          err.response?.data?.message ||\r\n          err.message ||\r\n          \"加载配置列表失败，请稍后重试\"\r\n        )\r\n      } finally {\r\n        this.configLoading = false\r\n      }\r\n    },\r\n    // 选择配置\r\n    async handleConfigSelect(row) {\r\n      try {\r\n        // 检查 row.config 是否为字符串\r\n        if (typeof row.config !== 'string') {\r\n          throw new Error('配置格式错误');\r\n        }\r\n\r\n        const config = JSON.parse(row.config);\r\n        this.configName = row.name;\r\n\r\n        // 清空现有搜索条件\r\n        this.searchConditions = [];\r\n\r\n        // 检查配置格式是否正确\r\n        if (!config || !config.searchConditions || !Array.isArray(config.searchConditions)) {\r\n          throw new Error('配置数据格式不正确');\r\n        }\r\n\r\n        // 恢复保存的搜索条件\r\n        for (const condition of config.searchConditions) {\r\n          // 检查条件数据是否完整\r\n          if (!condition || !condition.field) {\r\n            continue; // 跳过无效的条件\r\n          }\r\n\r\n          const newCondition = {\r\n            field: condition.field,\r\n            value: condition.value || '',\r\n            options: [],\r\n            loading: false,\r\n            treeValue: null,\r\n            treeOptions: []\r\n          };\r\n\r\n          this.searchConditions.push(newCondition);\r\n          // 触发字段变更以加载必要的选项数据\r\n          // await this.handleFieldChange(this.searchConditions.length - 1);\r\n        }\r\n\r\n        this.configDialogVisible = false;\r\n        this.$message.success('配置加载成功');\r\n\r\n        this.handleSearch()\r\n      } catch (err) {\r\n        console.error('加载配置失败:', err);\r\n        this.$message.error('加载配置失败：' + (err.message || '配置数据格式错误'));\r\n      }\r\n    },\r\n    // 删除配置\r\n    async deleteConfig(row) {\r\n      try {\r\n        await this.$confirm(\"确认删除该配置？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n\r\n        await deleteAggregatorConfig(row.id)\r\n        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)\r\n        this.$message.success(\"配置删除成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"删除配置失败：\" + err.message)\r\n        }\r\n      }\r\n    },\r\n    handleButtonClick(event) {\r\n      if (this.clickTimer) {\r\n        // 检测到双击\r\n        clearTimeout(this.clickTimer);\r\n        this.clickTimer = null;\r\n        this.handleReset();\r\n      } else {\r\n        // 设置单击定时器\r\n        this.clickTimer = setTimeout(() => {\r\n          this.clickTimer = null;\r\n          // this.openSearchDialog();\r\n          this.loadConfigs()\r\n        }, 300); // 300毫秒的检测窗口\r\n      }\r\n    },\r\n    openSearchDialog() {\r\n      this.searchDialogVisible = true\r\n    },\r\n    handleSearchAndClose() {\r\n      this.handleSearch()\r\n      this.searchDialogVisible = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AA0OA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,cAAA,GAAAF,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,eAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,WAAA,GAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AALA;AACA;AAEA;AACA;AAAA,IAAAM,QAAA,GAGA;EACAC,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,sBAAA;IAAA;IACAC,cAAA,EAAAA,uBAAA;EACA;;EACAC,KAAA;IACA;IACAC,YAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,UAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,QAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,gBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,QAAA;MAAA;MACAC,UAAA;MACAC,YAAA;MACAC,MAAA;MACAC,UAAA;MACAC,mBAAA;MACAC,aAAA;MACAC,YAAA;MACAC,mBAAA;MACAC,UAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,IAAAC,UAAA,OAAAC,GAAA,MAAAf,gBAAA,CAAAgB,GAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,KAAA;MAAA;MACA,OAAAvB,MAAA,CAAAwB,OAAA,MAAA1B,YAAA,EAAAuB,GAAA,WAAAI,IAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAC,OAAA,EAAAH,IAAA;UAAAI,GAAA,GAAAH,KAAA;UAAAI,MAAA,GAAAJ,KAAA;QAAA;UACAG,GAAA,EAAAA,GAAA;UACAE,KAAA,EAAAD,MAAA,CAAAC,KAAA;UACAC,QAAA,EAAAb,UAAA,CAAAc,GAAA,CAAAJ,GAAA;QACA;MAAA,GAAAK,MAAA,WAAAX,KAAA;QAAA,QAAAA,KAAA,CAAAS,QAAA;MAAA;IACA;EACA;EAEAG,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA,EAAAC,KAAA;MACA,KAAAjC,gBAAA,CAAAiC,KAAA,EAAAD,KAAA,GAAAA,KAAA;IACA;IAEA;IACAE,qBAAA,WAAAA,sBAAA;MACA;IAAA,CACA;IACA;IACAC,mBAAA,WAAAA,oBAAAH,KAAA,EAAAC,KAAA;MACA,KAAAjC,gBAAA,CAAAiC,KAAA,EAAAD,KAAA,GAAAA,KAAA;IACA;IACA;IACAI,oBAAA,WAAAA,qBAAAJ,KAAA,EAAAC,KAAA;MACA,KAAAjC,gBAAA,CAAAiC,KAAA,EAAAD,KAAA,GAAAA,KAAA;IACA;IACA;IACAK,cAAA,WAAAA,eAAAnB,KAAA;MACA,YAAAzB,YAAA,CAAAyB,KAAA;IACA;IAEA;IACAoB,YAAA,WAAAA,aAAA;MACA,KAAAtC,gBAAA,CAAAuC,IAAA;QACArB,KAAA;QACAc,KAAA;QACAQ,OAAA;QACAC,OAAA;QACAC,SAAA;QACAC,WAAA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAX,KAAA;MACA,KAAAjC,gBAAA,CAAA6C,MAAA,CAAAZ,KAAA;IACA;IAEA;IACAa,iBAAA,WAAAA,kBAAAb,KAAA;MAAA,IAAAc,KAAA;MAAA,WAAAC,kBAAA,CAAAzB,OAAA,oBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAC,QAAA;QAAA,IAAAC,SAAA,EAAAC,WAAA;QAAA,WAAAJ,oBAAA,CAAA1B,OAAA,IAAA+B,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAN,SAAA,GAAAL,KAAA,CAAA/C,gBAAA,CAAAiC,KAAA;cACAoB,WAAA,GAAAN,KAAA,CAAAV,cAAA,CAAAe,SAAA,CAAAlC,KAAA,GAEA;cACA,IAAAmC,WAAA,CAAA3D,IAAA;gBACA0D,SAAA,CAAApB,KAAA;cACA,WAAAqB,WAAA,CAAA3D,IAAA;gBACA0D,SAAA,CAAAV,SAAA;gBACAU,SAAA,CAAAT,WAAA;gBACAS,SAAA,CAAApB,KAAA;cACA,WAAAqB,WAAA,CAAA3D,IAAA,qBAAA2D,WAAA,CAAA3D,IAAA;gBACA0D,SAAA,CAAAV,SAAA;gBACAU,SAAA,CAAAT,WAAA;gBACAS,SAAA,CAAApB,KAAA;cACA;gBACAoB,SAAA,CAAApB,KAAA;cACA;cAEAoB,SAAA,CAAAZ,OAAA;cAAA,MAEAa,WAAA,CAAA3D,IAAA;gBAAA8D,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,KACAL,WAAA,CAAAM,MAAA;gBAAAH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,IAEAX,KAAA,CAAA9C,UAAA,CAAAmD,SAAA,CAAAlC,KAAA;gBAAAsC,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OACAX,KAAA,CAAAa,kBAAA,CAAAR,SAAA,CAAAlC,KAAA;YAAA;cAEAkC,SAAA,CAAAZ,OAAA,GAAAO,KAAA,CAAA9C,UAAA,CAAAmD,SAAA,CAAAlC,KAAA;cAAAsC,QAAA,CAAAE,IAAA;cAAA;YAAA;cAEA;cACAN,SAAA,CAAAZ,OAAA,GAAAa,WAAA,CAAAb,OAAA;YAAA;YAAA;cAAA,OAAAgB,QAAA,CAAAK,IAAA;UAAA;QAAA,GAAAV,OAAA;MAAA;IAGA;IAEA;IACAW,oBAAA,WAAAA,qBAAA7B,KAAA,EAAAf,KAAA;MAAA,IAAA6C,MAAA;MACA,IAAAV,WAAA,QAAAhB,cAAA,CAAAnB,KAAA;MACA,IAAAkC,SAAA,QAAApD,gBAAA,CAAAiC,KAAA;MAEA,IAAAoB,WAAA,CAAAW,UAAA;QACA;QACA,WAAAX,WAAA,CAAAW,UAAA,6BAAAX,WAAA,CAAAW,UAAA;UACA;UACA,IAAAC,MAAA,QAAAZ,WAAA,CAAAW,UAAA;UAEA,IAAAC,MAAA,WAAAA,MAAA,CAAAC,IAAA;YACA;YACAD,MAAA,CAAAC,IAAA,WAAAnE,IAAA;cACA,IAAAA,IAAA;gBACA;gBACAqD,SAAA,CAAAT,WAAA,GAAA5C,IAAA;cACA,WAAAsD,WAAA,CAAAc,UAAA,IAAAJ,MAAA,CAAAK,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAsD,WAAA,CAAAc,UAAA;gBACA;gBACAf,SAAA,CAAAT,WAAA,GAAAoB,MAAA,CAAAK,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAsD,WAAA,CAAAc,UAAA;cACA;YACA;UACA;YACA;YACAf,SAAA,CAAAT,WAAA,GAAAsB,MAAA;;YAEA;YACA,MAAAA,MAAA,IAAAA,MAAA,CAAAK,MAAA,WAAAjB,WAAA,CAAAc,UAAA,SAAAC,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAsD,WAAA,CAAAc,UAAA;cACAf,SAAA,CAAAT,WAAA,QAAAyB,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAsD,WAAA,CAAAc,UAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAI,sBAAA,WAAAA,uBAAAtC,KAAA,EAAAuC,IAAA;MACA,IAAApB,SAAA,QAAApD,gBAAA,CAAAiC,KAAA;MACA,IAAAoB,WAAA,QAAAhB,cAAA,CAAAe,SAAA,CAAAlC,KAAA;MAEA,IAAAmC,WAAA,CAAAoB,UAAA,IAAAD,IAAA,CAAAnB,WAAA,CAAAoB,UAAA,MAAAC,SAAA;QACAtB,SAAA,CAAApB,KAAA,GAAAwC,IAAA,CAAAnB,WAAA,CAAAoB,UAAA;MACA,WAAAD,IAAA,CAAAG,OAAA,KAAAD,SAAA;QACAtB,SAAA,CAAApB,KAAA,GAAAwC,IAAA,CAAAG,OAAA;MACA;QACAvB,SAAA,CAAApB,KAAA,GAAAwC,IAAA,CAAAI,EAAA;MACA;IACA;IAEA;IACAC,qBAAA,WAAAA,sBAAA5C,KAAA,EAAAD,KAAA;MACA,IAAAoB,SAAA,QAAApD,gBAAA,CAAAiC,KAAA;MAEA,IAAAD,KAAA,KAAA0C,SAAA,IAAA1C,KAAA;QACAoB,SAAA,CAAApB,KAAA;MACA;IACA;IAEA;IACA8C,eAAA,WAAAA,gBAAA5D,KAAA,EAAAsD,IAAA;MACA,IAAAnB,WAAA,QAAAhB,cAAA,CAAAnB,KAAA;MAEA,IAAAmC,WAAA,CAAA0B,aAAA,WAAA1B,WAAA,CAAA0B,aAAA;QACA,OAAA1B,WAAA,CAAA0B,aAAA,CAAAP,IAAA;MACA;;MAEA;MACA,IAAAA,IAAA,CAAAQ,GAAA,IAAAR,IAAA,CAAAQ,GAAA,CAAAC,KAAA;QACA,OAAAT,IAAA,CAAAQ,GAAA,CAAAC,KAAA,CAAAC,oBAAA,GACAV,IAAA,CAAAQ,GAAA,CAAAC,KAAA,CAAAE,oBAAA,GACA,MACAX,IAAA,CAAAQ,GAAA,CAAAC,KAAA,CAAAG,iBAAA;MACA;MAEA,OAAAZ,IAAA,CAAA9C,KAAA;IACA;IAEA;IACA2D,gBAAA,WAAAA,iBAAAnE,KAAA,EAAAsD,IAAA;MACA,IAAAnB,WAAA,QAAAhB,cAAA,CAAAnB,KAAA;MAEA,IAAAmC,WAAA,CAAAiC,cAAA,WAAAjC,WAAA,CAAAiC,cAAA;QACA,OAAAjC,WAAA,CAAAiC,cAAA,CAAAd,IAAA;MACA;;MAEA;MACA,IAAAA,IAAA,CAAA9C,KAAA,IAAA8C,IAAA,CAAA9C,KAAA,CAAA6D,OAAA;QACA,OAAAf,IAAA,CAAA9C,KAAA,CAAA8D,SAAA,IAAAhB,IAAA,CAAA9C,KAAA,CAAA6D,OAAA;MACA;MAEA,OAAAf,IAAA,CAAA9C,KAAA;IACA;IAEA;IACA+D,kBAAA,WAAAA,mBAAAC,KAAA,EAAAxE,KAAA;MAAA,IAAAyE,MAAA;MAAA,WAAA3C,kBAAA,CAAAzB,OAAA,oBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA0C,SAAA;QAAA,IAAAxC,SAAA;QAAA,WAAAH,oBAAA,CAAA1B,OAAA,IAAA+B,IAAA,UAAAuC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAArC,IAAA,GAAAqC,SAAA,CAAApC,IAAA;YAAA;cACAN,SAAA,GAAAuC,MAAA,CAAA3F,gBAAA,CAAA+F,IAAA,WAAA9E,CAAA;gBAAA,OAAAA,CAAA,CAAAC,KAAA,KAAAA,KAAA;cAAA;cAAA,IACAkC,SAAA;gBAAA0C,SAAA,CAAApC,IAAA;gBAAA;cAAA;cAAA,OAAAoC,SAAA,CAAAE,MAAA;YAAA;cAEA5C,SAAA,CAAAX,OAAA;cAAAqD,SAAA,CAAArC,IAAA;cAAAqC,SAAA,CAAApC,IAAA;cAAA,OAEAiC,MAAA,CAAA/B,kBAAA,CAAA1C,KAAA,EAAAwE,KAAA;YAAA;cACAtC,SAAA,CAAAZ,OAAA,GAAAmD,MAAA,CAAA1F,UAAA,CAAAiB,KAAA;YAAA;cAAA4E,SAAA,CAAArC,IAAA;cAEAL,SAAA,CAAAX,OAAA;cAAA,OAAAqD,SAAA,CAAAG,MAAA;YAAA;YAAA;cAAA,OAAAH,SAAA,CAAAjC,IAAA;UAAA;QAAA,GAAA+B,QAAA;MAAA;IAEA;IAEA;IACAhC,kBAAA,WAAAA,mBAAA1C,KAAA;MAAA,IAAAgF,UAAA,GAAAC,SAAA;QAAAC,MAAA;MAAA,WAAApD,kBAAA,CAAAzB,OAAA,oBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAmD,SAAA;QAAA,IAAAX,KAAA,EAAArC,WAAA,EAAAiD,GAAA,EAAAC,QAAA;QAAA,WAAAtD,oBAAA,CAAA1B,OAAA,IAAA+B,IAAA,UAAAkD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAhD,IAAA,GAAAgD,SAAA,CAAA/C,IAAA;YAAA;cAAAgC,KAAA,GAAAQ,UAAA,CAAA5B,MAAA,QAAA4B,UAAA,QAAAxB,SAAA,GAAAwB,UAAA;cACA7C,WAAA,GAAA+C,MAAA,CAAA/D,cAAA,CAAAnB,KAAA;cAAA,MACA,CAAAmC,WAAA,CAAAM,MAAA,KAAAN,WAAA,CAAAb,OAAA;gBAAAiE,SAAA,CAAA/C,IAAA;gBAAA;cAAA;cAAA,OAAA+C,SAAA,CAAAT,MAAA;YAAA;cAEAM,GAAA,GAAAI,gBAAA,CAAArD,WAAA,CAAAb,OAAA;cAAA,IACA8D,GAAA;gBAAAG,SAAA,CAAA/C,IAAA;gBAAA;cAAA;cAAA,OAAA+C,SAAA,CAAAT,MAAA;YAAA;cAAAS,SAAA,CAAAhD,IAAA;cAAAgD,SAAA,CAAA/C,IAAA;cAAA,OAGA,IAAAiD,gBAAA;gBACAL,GAAA,EAAAA,GAAA;gBACAM,MAAA;gBACAC,MAAA;kBAAAnB,KAAA,EAAAA;gBAAA;cACA;YAAA;cAJAa,QAAA,GAAAE,SAAA,CAAAK,IAAA;cAMA,IAAAP,QAAA,CAAAQ,IAAA;gBACAX,MAAA,CAAAnG,UAAA,CAAAiB,KAAA,IAAAqF,QAAA,CAAAxG,IAAA;cACA;cAAA0G,SAAA,CAAA/C,IAAA;cAAA;YAAA;cAAA+C,SAAA,CAAAhD,IAAA;cAAAgD,SAAA,CAAAO,EAAA,GAAAP,SAAA;cAEAQ,OAAA,CAAAC,KAAA,cAAAT,SAAA,CAAAO,EAAA;cACAZ,MAAA,CAAAe,QAAA,CAAAD,KAAA;YAAA;YAAA;cAAA,OAAAT,SAAA,CAAA5C,IAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IAEA;IAEA;IACAe,YAAA,WAAAA,aAAA;MACA,IAAAC,YAAA;MAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAhG,OAAA,EACA,KAAAvB,gBAAA;QAAAwH,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAAvE,SAAA,GAAAoE,KAAA,CAAAxF,KAAA;UACA,IAAAoB,SAAA,CAAAlC,KAAA,IAAAkC,SAAA,CAAApB,KAAA,WAAAoB,SAAA,CAAApB,KAAA;YACA,IAAAqB,WAAA,QAAAhB,cAAA,CAAAe,SAAA,CAAAlC,KAAA;;YAEA;YACA;AACA;AACA;AACA;AACA;AACA;YACAmG,YAAA,CAAAjE,SAAA,CAAAlC,KAAA,IAAAkC,SAAA,CAAApB,KAAA;UACA;QACA;MAAA,SAAA4F,GAAA;QAAAN,SAAA,CAAAO,CAAA,CAAAD,GAAA;MAAA;QAAAN,SAAA,CAAAQ,CAAA;MAAA;MAEA,KAAAC,KAAA,WAAAV,YAAA;IACA;IAEA;IACAW,WAAA,WAAAA,YAAA;MACA,KAAAhI,gBAAA;MACA,KAAAM,UAAA;MACA,KAAAyH,KAAA;IACA;IACAE,eAAA,WAAAA,gBAAAzD,IAAA;MACA,IAAAA,IAAA,CAAA0D,QAAA,KAAA1D,IAAA,CAAA0D,QAAA,CAAA5D,MAAA;QACA,OAAAE,IAAA,CAAA0D,QAAA;MACA;MACA,IAAAC,CAAA;MACA,IAAA3D,IAAA,CAAAS,KAAA;QACA,IAAAT,IAAA,CAAAS,KAAA,CAAAC,oBAAA,YAAAV,IAAA,CAAAS,KAAA,CAAAE,oBAAA;UACA,IAAAX,IAAA,CAAA4D,IAAA,CAAAC,aAAA;YACAF,CAAA,GAAA3D,IAAA,CAAA4D,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAA/D,IAAA,CAAA4D,IAAA,CAAAC,aAAA;UACA;YACAF,CAAA,GAAA3D,IAAA,CAAAgE,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAA/D,IAAA,CAAAgE,IAAA,CAAAC,aAAA;UACA;QACA;UACAN,CAAA,GAAA3D,IAAA,CAAAS,KAAA,CAAAyD,SAAA,SAAAlE,IAAA,CAAAS,KAAA,CAAAC,oBAAA,GAAAV,IAAA,CAAAS,KAAA,CAAAE,oBAAA,SAAAX,IAAA,CAAAS,KAAA,CAAAG,iBAAA,SAAAkD,iBAAA,CAAAC,YAAA,CAAA/D,IAAA,CAAAS,KAAA,CAAAC,oBAAA,GAAAV,IAAA,CAAAS,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAX,IAAA,CAAAmE,MAAA;QACA;UACA/D,EAAA,EAAAJ,IAAA,CAAAmE,MAAA;UACAjH,KAAA,EAAAyG,CAAA;UACAD,QAAA,EAAA1D,IAAA,CAAA0D,QAAA;UACAU,UAAA,EAAApE,IAAA,CAAAG,OAAA,YAAAH,IAAA,CAAA0D,QAAA,IAAAxD;QACA;MACA;QACA;UACAE,EAAA,EAAAJ,IAAA,CAAAqE,MAAA;UACAnH,KAAA,EAAAyG,CAAA;UACAD,QAAA,EAAA1D,IAAA,CAAA0D,QAAA;UACAU,UAAA,EAAApE,IAAA,CAAAG,OAAA,YAAAH,IAAA,CAAA0D,QAAA,IAAAxD;QACA;MACA;IACA;IACAoE,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,SAAA3E,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAiJ,SAAA,CAAA1E,MAAA,cAAAF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAkJ,SAAA,CAAAD,SAAA;QACA,OAAAE,cAAA,CAAAC,QAAA,iBAAAjF,IAAA;UACA6E,MAAA,CAAA5I,UAAA,GAAA4I,MAAA,CAAA3E,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAiJ,SAAA;UACA,OAAAD,MAAA,CAAA3E,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAiJ,SAAA;QACA;MACA;QACA,KAAA7I,UAAA,QAAAiE,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAiJ,SAAA;QACA,YAAA5E,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAiJ,SAAA;MACA;IACA;IACAI,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,SAAAjF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAuJ,cAAA,CAAAhF,MAAA,cAAAF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAkJ,SAAA,CAAAK,cAAA;QACA,OAAAJ,cAAA,CAAAC,QAAA,sBAAAjF,IAAA;UACAmF,MAAA,CAAAjJ,YAAA,GAAAiJ,MAAA,CAAAjF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAuJ,cAAA;UACA,OAAAD,MAAA,CAAAjF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAuJ,cAAA;QACA;MACA;QACA,KAAAlJ,YAAA,QAAAgE,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAuJ,cAAA;QACA,YAAAlF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAuJ,cAAA;MACA;IACA;IACAC,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,SAAApF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAM,MAAA,CAAAiE,MAAA,cAAAF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAkJ,SAAA,CAAA5I,MAAA;QACA,OAAA6I,cAAA,CAAAC,QAAA,cAAAjF,IAAA;UACAsF,MAAA,CAAAnJ,MAAA,GAAAmJ,MAAA,CAAApF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAM,MAAA;UACA,OAAAmJ,MAAA,CAAApF,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAM,MAAA;QACA;MACA;QACA,KAAAA,MAAA,QAAA+D,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAM,MAAA;QACA,YAAA+D,MAAA,CAAAC,KAAA,CAAAtE,IAAA,CAAAM,MAAA;MACA;IACA;IACAoJ,aAAA,WAAAA,cAAAvI,KAAA;MACA,IAAAmC,WAAA,QAAAhB,cAAA,CAAAnB,KAAA;MACA,IAAAmC,WAAA,CAAAqG,UAAA,WAAArG,WAAA,CAAAqG,UAAA;QACA,YAAArG,WAAA,CAAAqG,UAAA;MACA,WAAArG,WAAA,CAAAqG,UAAA,WAAArG,WAAA,CAAAqG,UAAA;QACA,OAAArG,WAAA,CAAAqG,UAAA;MACA;QACA,YAAAzB,eAAA;MACA;IACA;IACA;IACA0B,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAA5G,kBAAA,CAAAzB,OAAA,oBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA2G,SAAA;QAAA,IAAAC,YAAA;QAAA,WAAA7G,oBAAA,CAAA1B,OAAA,IAAA+B,IAAA,UAAAyG,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvG,IAAA,GAAAuG,SAAA,CAAAtG,IAAA;YAAA;cAAAsG,SAAA,CAAAvG,IAAA;cAAA,IAGAmG,MAAA,CAAAtJ,UAAA;gBAAA0J,SAAA,CAAAtG,IAAA;gBAAA;cAAA;cACAkG,MAAA,CAAAzC,QAAA,CAAA8C,OAAA;cAAA,OAAAD,SAAA,CAAAhE,MAAA;YAAA;cAAA,IAKA4D,MAAA,CAAA5J,gBAAA,CAAAsE,MAAA;gBAAA0F,SAAA,CAAAtG,IAAA;gBAAA;cAAA;cACAkG,MAAA,CAAAzC,QAAA,CAAA8C,OAAA;cAAA,OAAAD,SAAA,CAAAhE,MAAA;YAAA;cAIA;cACA8D,YAAA;gBACA1K,IAAA,EAAAwK,MAAA,CAAAtJ,UAAA;gBACAZ,IAAA,EAAAkK,MAAA,CAAA/J,UAAA;gBACA4B,MAAA;kBACAzB,gBAAA,EAAA4J,MAAA,CAAA5J,gBAAA,CAAAgB,GAAA,WAAAoC,SAAA;oBAAA;sBACAlC,KAAA,EAAAkC,SAAA,CAAAlC,KAAA;sBACAc,KAAA,EAAAoB,SAAA,CAAApB,KAAA;sBACAtC,IAAA,EAAAkK,MAAA,CAAAvH,cAAA,CAAAe,SAAA,CAAAlC,KAAA,EAAAxB;oBACA;kBAAA;gBACA;cACA,GAEA;cAAAsK,SAAA,CAAAtG,IAAA;cAAA,OACA,IAAAwG,gCAAA,EAAAJ,YAAA;YAAA;cACAF,MAAA,CAAAzC,QAAA,CAAAgD,OAAA;cAAAH,SAAA,CAAAtG,IAAA;cAAA;YAAA;cAAAsG,SAAA,CAAAvG,IAAA;cAAAuG,SAAA,CAAAhD,EAAA,GAAAgD,SAAA;cAEA,IAAAA,SAAA,CAAAhD,EAAA;gBACA4C,MAAA,CAAAzC,QAAA,CAAAD,KAAA,cAAA8C,SAAA,CAAAhD,EAAA,CAAAoD,OAAA;cACA;YAAA;YAAA;cAAA,OAAAJ,SAAA,CAAAnG,IAAA;UAAA;QAAA,GAAAgG,QAAA;MAAA;IAEA;IACA;IACAQ,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAtH,kBAAA,CAAAzB,OAAA,oBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAAqH,SAAA;QAAA,IAAAtG,MAAA,EAAAuG,aAAA;QAAA,WAAAvH,oBAAA,CAAA1B,OAAA,IAAA+B,IAAA,UAAAmH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjH,IAAA,GAAAiH,SAAA,CAAAhH,IAAA;YAAA;cACA4G,MAAA,CAAA9J,aAAA;cACA8J,MAAA,CAAA/J,mBAAA;cAAAmK,SAAA,CAAAjH,IAAA;cAAAiH,SAAA,CAAAhH,IAAA;cAAA,OAEA,IAAAiH,iCAAA;gBAAA9K,UAAA,EAAAyK,MAAA,CAAAzK;cAAA;YAAA;cAAAoE,MAAA,GAAAyG,SAAA,CAAA5D,IAAA;cACAwD,MAAA,CAAA7J,YAAA,GAAAwD,MAAA,CAAA2G,IAAA;cAAAF,SAAA,CAAAhH,IAAA;cAAA;YAAA;cAAAgH,SAAA,CAAAjH,IAAA;cAAAiH,SAAA,CAAA1D,EAAA,GAAA0D,SAAA;cAEAzD,OAAA,CAAAC,KAAA,YAAAwD,SAAA,CAAA1D,EAAA;cACAsD,MAAA,CAAAnD,QAAA,CAAAD,KAAA,CACA,EAAAsD,aAAA,GAAAE,SAAA,CAAA1D,EAAA,CAAAT,QAAA,cAAAiE,aAAA,gBAAAA,aAAA,GAAAA,aAAA,CAAAzK,IAAA,cAAAyK,aAAA,uBAAAA,aAAA,CAAAJ,OAAA,KACAM,SAAA,CAAA1D,EAAA,CAAAoD,OAAA,IACA,gBACA;YAAA;cAAAM,SAAA,CAAAjH,IAAA;cAEA6G,MAAA,CAAA9J,aAAA;cAAA,OAAAkK,SAAA,CAAAzE,MAAA;YAAA;YAAA;cAAA,OAAAyE,SAAA,CAAA7G,IAAA;UAAA;QAAA,GAAA0G,QAAA;MAAA;IAEA;IACA;IACAM,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/H,kBAAA,CAAAzB,OAAA,oBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA8H,SAAA;QAAA,IAAAvJ,MAAA,EAAAwJ,UAAA,EAAAC,MAAA,EAAA9H,SAAA,EAAA+H,YAAA;QAAA,WAAAlI,oBAAA,CAAA1B,OAAA,IAAA+B,IAAA,UAAA8H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5H,IAAA,GAAA4H,SAAA,CAAA3H,IAAA;YAAA;cAAA2H,SAAA,CAAA5H,IAAA;cAAA,MAGA,OAAAqH,GAAA,CAAArJ,MAAA;gBAAA4J,SAAA,CAAA3H,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4H,KAAA;YAAA;cAGA7J,MAAA,GAAA8J,IAAA,CAAAC,KAAA,CAAAV,GAAA,CAAArJ,MAAA;cACAsJ,OAAA,CAAAzK,UAAA,GAAAwK,GAAA,CAAA1L,IAAA;;cAEA;cACA2L,OAAA,CAAA/K,gBAAA;;cAEA;cAAA,MACA,CAAAyB,MAAA,KAAAA,MAAA,CAAAzB,gBAAA,KAAAyL,KAAA,CAAAC,OAAA,CAAAjK,MAAA,CAAAzB,gBAAA;gBAAAqL,SAAA,CAAA3H,IAAA;gBAAA;cAAA;cAAA,MACA,IAAA4H,KAAA;YAAA;cAGA;cAAAL,UAAA,OAAA1D,2BAAA,CAAAhG,OAAA,EACAE,MAAA,CAAAzB,gBAAA;cAAAqL,SAAA,CAAA5H,IAAA;cAAAwH,UAAA,CAAAxD,CAAA;YAAA;cAAA,KAAAyD,MAAA,GAAAD,UAAA,CAAAvD,CAAA,IAAAC,IAAA;gBAAA0D,SAAA,CAAA3H,IAAA;gBAAA;cAAA;cAAAN,SAAA,GAAA8H,MAAA,CAAAlJ,KAAA;cAAA,MAEA,CAAAoB,SAAA,KAAAA,SAAA,CAAAlC,KAAA;gBAAAmK,SAAA,CAAA3H,IAAA;gBAAA;cAAA;cAAA,OAAA2H,SAAA,CAAArF,MAAA;YAAA;cAIAmF,YAAA;gBACAjK,KAAA,EAAAkC,SAAA,CAAAlC,KAAA;gBACAc,KAAA,EAAAoB,SAAA,CAAApB,KAAA;gBACAQ,OAAA;gBACAC,OAAA;gBACAC,SAAA;gBACAC,WAAA;cACA;cAEAoI,OAAA,CAAA/K,gBAAA,CAAAuC,IAAA,CAAA4I,YAAA;cACA;cACA;YAAA;cAAAE,SAAA,CAAA3H,IAAA;cAAA;YAAA;cAAA2H,SAAA,CAAA3H,IAAA;cAAA;YAAA;cAAA2H,SAAA,CAAA5H,IAAA;cAAA4H,SAAA,CAAArE,EAAA,GAAAqE,SAAA;cAAAJ,UAAA,CAAApD,CAAA,CAAAwD,SAAA,CAAArE,EAAA;YAAA;cAAAqE,SAAA,CAAA5H,IAAA;cAAAwH,UAAA,CAAAnD,CAAA;cAAA,OAAAuD,SAAA,CAAApF,MAAA;YAAA;cAGA8E,OAAA,CAAAxK,mBAAA;cACAwK,OAAA,CAAA5D,QAAA,CAAAgD,OAAA;cAEAY,OAAA,CAAA3D,YAAA;cAAAiE,SAAA,CAAA3H,IAAA;cAAA;YAAA;cAAA2H,SAAA,CAAA5H,IAAA;cAAA4H,SAAA,CAAAM,EAAA,GAAAN,SAAA;cAEApE,OAAA,CAAAC,KAAA,YAAAmE,SAAA,CAAAM,EAAA;cACAZ,OAAA,CAAA5D,QAAA,CAAAD,KAAA,cAAAmE,SAAA,CAAAM,EAAA,CAAAvB,OAAA;YAAA;YAAA;cAAA,OAAAiB,SAAA,CAAAxH,IAAA;UAAA;QAAA,GAAAmH,QAAA;MAAA;IAEA;IACA;IACAY,YAAA,WAAAA,aAAAd,GAAA;MAAA,IAAAe,OAAA;MAAA,WAAA7I,kBAAA,CAAAzB,OAAA,oBAAA0B,oBAAA,CAAA1B,OAAA,IAAA2B,IAAA,UAAA4I,SAAA;QAAA,WAAA7I,oBAAA,CAAA1B,OAAA,IAAA+B,IAAA,UAAAyI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvI,IAAA,GAAAuI,SAAA,CAAAtI,IAAA;YAAA;cAAAsI,SAAA,CAAAvI,IAAA;cAAAuI,SAAA,CAAAtI,IAAA;cAAA,OAEAmI,OAAA,CAAAI,QAAA;gBACAvM,IAAA;cACA;YAAA;cAAAsM,SAAA,CAAAtI,IAAA;cAAA,OAEA,IAAAwI,kCAAA,EAAApB,GAAA,CAAAlG,EAAA;YAAA;cACAiH,OAAA,CAAApL,YAAA,GAAAoL,OAAA,CAAApL,YAAA,CAAAoB,MAAA,WAAAJ,MAAA;gBAAA,OAAAA,MAAA,CAAAmD,EAAA,KAAAkG,GAAA,CAAAlG,EAAA;cAAA;cACAiH,OAAA,CAAA1E,QAAA,CAAAgD,OAAA;cAAA6B,SAAA,CAAAtI,IAAA;cAAA;YAAA;cAAAsI,SAAA,CAAAvI,IAAA;cAAAuI,SAAA,CAAAhF,EAAA,GAAAgF,SAAA;cAEA,IAAAA,SAAA,CAAAhF,EAAA;gBACA6E,OAAA,CAAA1E,QAAA,CAAAD,KAAA,aAAA8E,SAAA,CAAAhF,EAAA,CAAAoD,OAAA;cACA;YAAA;YAAA;cAAA,OAAA4B,SAAA,CAAAnI,IAAA;UAAA;QAAA,GAAAiI,QAAA;MAAA;IAEA;IACAK,iBAAA,WAAAA,kBAAAC,KAAA;MAAA,IAAAC,OAAA;MACA,SAAA1L,UAAA;QACA;QACA2L,YAAA,MAAA3L,UAAA;QACA,KAAAA,UAAA;QACA,KAAAqH,WAAA;MACA;QACA;QACA,KAAArH,UAAA,GAAA4L,UAAA;UACAF,OAAA,CAAA1L,UAAA;UACA;UACA0L,OAAA,CAAAhC,WAAA;QACA;MACA;IACA;IACAmC,gBAAA,WAAAA,iBAAA;MACA,KAAA9L,mBAAA;IACA;IACA+L,oBAAA,WAAAA,qBAAA;MACA,KAAArF,YAAA;MACA,KAAA1G,mBAAA;IACA;EACA;AACA;AAAAgM,OAAA,CAAAnL,OAAA,GAAApC,QAAA"}]}