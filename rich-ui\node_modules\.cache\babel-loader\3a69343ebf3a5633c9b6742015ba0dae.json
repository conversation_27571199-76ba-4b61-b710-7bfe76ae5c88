{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\mailrules.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\mailrules.js", "mtime": 1686884406000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listM<PERSON><PERSON>les", "query", "request", "url", "method", "params", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mailRulesId", "add<PERSON><PERSON><PERSON><PERSON>", "data", "updateMailrules", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/mailrules.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询邮件规则列表\r\nexport function listMailrules(query) {\r\n  return request({\r\n    url: '/system/mailrules/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询邮件规则详细\r\nexport function getMailrules(mailRulesId) {\r\n  return request({\r\n    url: '/system/mailrules/' + mailRulesId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增邮件规则\r\nexport function addMailrules(data) {\r\n  return request({\r\n    url: '/system/mailrules',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改邮件规则\r\nexport function updateMailrules(data) {\r\n  return request({\r\n    url: '/system/mailrules',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除邮件规则\r\nexport function delMailrules(mailRulesId) {\r\n  return request({\r\n    url: '/system/mailrules/' + mailRulesId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(mailRulesId, status) {\r\n  const data = {\r\n      mailRulesId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/mailrules/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAACC,WAAW,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,WAAW;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACJ,WAAW,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,WAAW;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,WAAW,EAAEM,MAAM,EAAE;EAChD,IAAMJ,IAAI,GAAG;IACTF,WAAW,EAAXA,WAAW;IACbM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}