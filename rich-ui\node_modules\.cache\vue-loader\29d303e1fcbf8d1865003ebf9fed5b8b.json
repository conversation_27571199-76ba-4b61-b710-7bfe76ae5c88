{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue", "mtime": 1754876882572}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudCINCmltcG9ydCBjdXJyZW5jeSBmcm9tICJjdXJyZW5jeS5qcyINCmltcG9ydCB7c2F2ZUFnZ3JlZ2F0b3JDb25maWcsIGxvYWRBZ2dyZWdhdG9yQ29uZmlncywgZGVsZXRlQWdncmVnYXRvckNvbmZpZ30gZnJvbSAiQC9hcGkvc3lzdGVtL2FnZ3JlZ2F0b3IiDQppbXBvcnQgaHRtbDJwZGYgZnJvbSAiaHRtbDJwZGYuanMiDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkRhdGFBZ2dyZWdhdG9yIiwNCiAgcHJvcHM6IHsNCiAgICBkYXRhU291cmNlOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICBmaWVsZExhYmVsTWFwOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGNvbmZpZ05hbWU6ICIiLA0KICAgICAgY29uZmlnOiB7DQogICAgICAgIG5hbWU6ICIiLA0KICAgICAgICBwcmltYXJ5RmllbGQ6ICIiLA0KICAgICAgICBtYXRjaE9wdGlvbnM6IHsNCiAgICAgICAgICBleGFjdDogdHJ1ZSwNCiAgICAgICAgICBjYXNlU2Vuc2l0aXZlOiBmYWxzZQ0KICAgICAgICB9LA0KICAgICAgICBkYXRlRmllbGQ6ICIiLA0KICAgICAgICBkYXRlT3B0aW9uczogew0KICAgICAgICAgIGNvbnZlcnRUb051bWJlcjogZmFsc2UsDQogICAgICAgICAgZm9ybWF0VHlwZTogImRheSINCiAgICAgICAgfSwNCiAgICAgICAgc2hvd0RldGFpbHM6IGZhbHNlLA0KICAgICAgICBmaWVsZHM6IFtdLA0KICAgICAgICBzcGxpdEJ5Q3VycmVuY3k6IGZhbHNlLA0KICAgICAgfSwNCiAgICAgIGRhdGVPcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogIuaMieW5tCIsIHZhbHVlOiAieWVhciJ9LA0KICAgICAgICB7bGFiZWw6ICLmjInmnIgiLCB2YWx1ZTogIm1vbnRoIn0sDQogICAgICAgIHtsYWJlbDogIuaMieWRqCIsIHZhbHVlOiAid2VlayJ9LA0KICAgICAgICB7bGFiZWw6ICLmjInml6UiLCB2YWx1ZTogImRheSJ9LA0KICAgICAgICB7bGFiZWw6ICLmjInml7YiLCB2YWx1ZTogImhvdXIifSwNCiAgICAgICAge2xhYmVsOiAi5oyJ5YiGIiwgdmFsdWU6ICJtaW51dGUifQ0KICAgICAgXSwNCiAgICAgIGFnZ3JlZ2F0aW9uT3B0aW9uczogWw0KICAgICAgICB7bGFiZWw6ICLorqHmlbAiLCB2YWx1ZTogImNvdW50In0sDQogICAgICAgIHtsYWJlbDogIuaxguWSjCIsIHZhbHVlOiAic3VtIn0sDQogICAgICAgIHtsYWJlbDogIuW5s+Wdh+WAvCIsIHZhbHVlOiAiYXZnIn0sDQogICAgICAgIHtsYWJlbDogIuaWueW3riIsIHZhbHVlOiAidmFyaWFuY2UifSwNCiAgICAgICAge2xhYmVsOiAi5pyA5aSn5YC8IiwgdmFsdWU6ICJtYXgifSwNCiAgICAgICAge2xhYmVsOiAi5pyA5bCP5YC8IiwgdmFsdWU6ICJtaW4ifQ0KICAgICAgXSwNCiAgICAgIGxvYWRpbmc6IGZhbHNlLA0KICAgICAgY29uZmlnRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBzYXZlZENvbmZpZ3M6IFtdLA0KICAgICAgY29uZmlnTG9hZGluZzogZmFsc2UsDQogICAgICBpc0xhbmRzY2FwZTogZmFsc2UsDQogICAgICBzaG93UmVzdWx0OiBmYWxzZSwNCiAgICAgIHByb2Nlc3NlZERhdGE6IFtdDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIC8vIOWPr+eUqOWtl+auteWIl+ihqA0KICAgIGF2YWlsYWJsZUZpZWxkcygpIHsNCiAgICAgIGlmICh0aGlzLmRhdGFTb3VyY2UubGVuZ3RoID09PSAwKSByZXR1cm4gW10NCiAgICAgIC8vIOWPqui/lOWbnuWcqCBmaWVsZExhYmVsTWFwIOS4reWumuS5ieeahOWtl+autQ0KICAgICAgcmV0dXJuIE9iamVjdC5rZXlzKHRoaXMuZGF0YVNvdXJjZVswXSkuZmlsdGVyKGZpZWxkID0+IGZpZWxkIGluIHRoaXMuZmllbGRMYWJlbE1hcCkNCiAgICB9LA0KDQogICAgLy8g5pWw5YC85Z6L5a2X5q615YiX6KGoDQogICAgbnVtZXJpY0ZpZWxkcygpIHsNCiAgICAgIC8vIOi/h+a7pOWHuuaVsOWAvOexu+Wei+eahOWtl+aute+8jOWQjOaXtuehruS/neWug+S7rOWcqCBmaWVsZExhYmVsTWFwIOS4reWtmOWcqA0KICAgICAgcmV0dXJuIHRoaXMuYXZhaWxhYmxlRmllbGRzLmZpbHRlcihmaWVsZCA9PiB7DQogICAgICAgIHJldHVybiB0eXBlb2YgdGhpcy5kYXRhU291cmNlWzBdW2ZpZWxkXSA9PT0gIm51bWJlciINCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOW9k+WJjeWtl+auteexu+Weiw0KICAgIGN1cnJlbnRGaWVsZFR5cGUoKSB7DQogICAgICBpZiAoIXRoaXMuY29uZmlnLnByaW1hcnlGaWVsZCB8fCAhdGhpcy5kYXRhU291cmNlLmxlbmd0aCkgcmV0dXJuIG51bGwNCiAgICAgIGNvbnN0IHNhbXBsZVZhbHVlID0gdGhpcy5kYXRhU291cmNlWzBdW3RoaXMuY29uZmlnLnByaW1hcnlGaWVsZF0NCiAgICAgIGlmIChtb21lbnQoc2FtcGxlVmFsdWUsIG1vbWVudC5JU09fODYwMSwgdHJ1ZSkuaXNWYWxpZCgpKSByZXR1cm4gImRhdGUiDQogICAgICByZXR1cm4gdHlwZW9mIHNhbXBsZVZhbHVlDQogICAgfSwNCg0KICAgIC8vIOWIhue7hOWtl+auteWQjeensA0KICAgIGdyb3VwRmllbGROYW1lKCkgew0KICAgICAgaWYgKHRoaXMuY29uZmlnLnByaW1hcnlGaWVsZCAmJiB0aGlzLmNvbmZpZy5kYXRlRmllbGQpIHsNCiAgICAgICAgcmV0dXJuIGAke3RoaXMuZ2V0RmllbGRMYWJlbCh0aGlzLmNvbmZpZy5kYXRlRmllbGQpfSske3RoaXMuZ2V0RmllbGRMYWJlbCh0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGQpfWANCiAgICAgIH0NCiAgICAgIHJldHVybiB0aGlzLmdldEZpZWxkTGFiZWwodGhpcy5jb25maWcucHJpbWFyeUZpZWxkKQ0KICAgIH0sDQoNCiAgICBkYXRlRmllbGRzKCkgew0KICAgICAgcmV0dXJuIHRoaXMuYXZhaWxhYmxlRmllbGRzLmZpbHRlcihmaWVsZCA9PiB7DQogICAgICAgIC8vIOmmluWFiOajgOafpSBmaWVsZExhYmVsTWFwIOS4reeahCBkaXNwbGF5IOWxnuaApw0KICAgICAgICBpZiAodGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkXSAmJiB0aGlzLmZpZWxkTGFiZWxNYXBbZmllbGRdLmRpc3BsYXkgPT09ICJkYXRlIikgew0KICAgICAgICAgIHJldHVybiB0cnVlDQogICAgICAgIH0NCg0KICAgICAgICAvKiAvLyDlpoLmnpzmsqHmnInmmI7noa7moIforrDkuLrml6XmnJ/vvIzliJnlsJ3or5Xmo4Dmn6XlgLzmmK/lkKbkuLrml6XmnJ/moLzlvI8NCiAgICAgICAgY29uc3QgdmFsdWUgPSB0aGlzLmRhdGFTb3VyY2VbMF0/LltmaWVsZF07DQogICAgICAgIHJldHVybiB2YWx1ZSAmJiBtb21lbnQodmFsdWUsIG1vbWVudC5JU09fODYwMSwgdHJ1ZSkuaXNWYWxpZCgpOyAqLw0KICAgICAgfSkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyAuLi4gZXhpc3RpbmcgY29kZSAuLi4NCg0KICAgIC8qKg0KICAgICAqIOiuoeeul+ihqOagvOWQiOiuoeihjA0KICAgICAqIEBwYXJhbSB7T2JqZWN0fSBwYXJhbTAgLSDljIXlkKvliJfkv6Hmga/lkozmlbDmja7nmoTlr7nosaENCiAgICAgKiBAcmV0dXJucyB7QXJyYXl9IOWQiOiuoeihjOaVsOaNrg0KICAgICAqLw0KICAgIGdldFN1bW1hcnkoe2NvbHVtbnMsIGRhdGF9KSB7DQogICAgICBjb25zdCBzdW1zID0gW10NCg0KICAgICAgY29sdW1ucy5mb3JFYWNoKChjb2x1bW4sIGluZGV4KSA9PiB7DQogICAgICAgIC8vIOesrOS4gOWIl+aYvuekuiLlkIjorqEi5paH5pysDQogICAgICAgIGlmIChpbmRleCA9PT0gMCkgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuWQiOiuoSINCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOiOt+WPluW9k+WJjeWIl+WvueW6lOeahOWtl+autemFjee9rg0KICAgICAgICBjb25zdCBmaWVsZCA9IHRoaXMuY29uZmlnLmZpZWxkc1tpbmRleCAtIDFdDQogICAgICAgIGlmICghZmllbGQgfHwgIWZpZWxkLmZpZWxkS2V5KSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAiIg0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5qOA5p+l5a2X5q615piv5ZCm6YWN572u5LqG5rGH5oC75pa55byPDQogICAgICAgIGlmICghZmllbGQuYWdncmVnYXRpb24gfHwgZmllbGQuYWdncmVnYXRpb24gPT09ICJub25lIikgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiINCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOiOt+WPluWtl+autemFjee9rg0KICAgICAgICBjb25zdCBmaWVsZENvbmZpZyA9IHRoaXMuZmllbGRMYWJlbE1hcFtmaWVsZC5maWVsZEtleV0NCiAgICAgICAgaWYgKCFmaWVsZENvbmZpZyB8fCBmaWVsZENvbmZpZy5kaXNwbGF5ICE9PSAibnVtYmVyIikgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiINCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOiOt+WPluWIl+aVsOaNruW5tui9rOaNouS4uuaVsOWtlw0KICAgICAgICBjb25zdCB2YWx1ZXMgPSBkYXRhLm1hcChpdGVtID0+IHsNCiAgICAgICAgICBjb25zdCBwcm9wID0gdGhpcy5nZXRSZXN1bHRQcm9wKGZpZWxkKQ0KICAgICAgICAgIHJldHVybiBOdW1iZXIoaXRlbVtwcm9wXSkNCiAgICAgICAgfSkuZmlsdGVyKHZhbCA9PiAhaXNOYU4odmFsKSkNCg0KICAgICAgICBpZiAodmFsdWVzLmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiINCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagueaNruaxh+aAu+aWueW8j+iuoeeul+e7k+aenA0KICAgICAgICBsZXQgc3VtID0gMA0KICAgICAgICBzd2l0Y2ggKGZpZWxkLmFnZ3JlZ2F0aW9uKSB7DQogICAgICAgICAgY2FzZSAic3VtIjoNCiAgICAgICAgICAgIHN1bSA9IHZhbHVlcy5yZWR1Y2UoKGEsIGIpID0+IGEgKyBiLCAwKQ0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJhdmciOg0KICAgICAgICAgICAgc3VtID0gdmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gdmFsdWVzLmxlbmd0aA0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJtYXgiOg0KICAgICAgICAgICAgc3VtID0gTWF0aC5tYXgoLi4udmFsdWVzKQ0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJtaW4iOg0KICAgICAgICAgICAgc3VtID0gTWF0aC5taW4oLi4udmFsdWVzKQ0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJ2YXJpYW5jZSI6DQogICAgICAgICAgICBjb25zdCBtZWFuID0gdmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIGIsIDApIC8gdmFsdWVzLmxlbmd0aA0KICAgICAgICAgICAgc3VtID0gdmFsdWVzLnJlZHVjZSgoYSwgYikgPT4gYSArIE1hdGgucG93KGIgLSBtZWFuLCAyKSwgMCkgLyB2YWx1ZXMubGVuZ3RoDQogICAgICAgICAgICBicmVhaw0KICAgICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgICBzdW0gPSB2YWx1ZXMucmVkdWNlKChhLCBiKSA9PiBhICsgYiwgMCkNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagueaNruWtl+auteagvOW8j+WMluiuvue9ruagvOW8j+WMlue7k+aenA0KICAgICAgICBpZiAoZmllbGQuZm9ybWF0ID09PSAiZGVjaW1hbCIpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9IHN1bS50b0ZpeGVkKDIpDQogICAgICAgIH0gZWxzZSBpZiAoZmllbGQuZm9ybWF0ID09PSAicGVyY2VudCIpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9IChzdW0gKiAxMDApLnRvRml4ZWQoMikgKyAiJSINCiAgICAgICAgfSBlbHNlIGlmIChmaWVsZC5mb3JtYXQgPT09ICJjdXJyZW5jeSIpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICLCpSIgKyBzdW0udG9GaXhlZCgyKQ0KICAgICAgICB9IGVsc2UgaWYgKGZpZWxkLmZvcm1hdCA9PT0gInVzZCIpIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICIkIiArIHN1bS50b0ZpeGVkKDIpDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSBzdW0NCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgcmV0dXJuIHN1bXMNCiAgICB9LA0KICAgIGdldE5hbWUoaWQpIHsNCiAgICAgIGlmIChpZCAhPT0gbnVsbCkgew0KICAgICAgICBsZXQgc3RhZmYgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSBpZClbMF0NCiAgICAgICAgaWYgKHN0YWZmICYmIHN0YWZmICE9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgICByZXR1cm4gc3RhZmYuc3RhZmZTaG9ydE5hbWUgKyBzdGFmZi5zdGFmZkZhbWlseUVuTmFtZQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gIiINCiAgICB9LA0KICAgIC8qKg0KICAgICAqIOiOt+WPluWtl+auteagh+etvg0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBmaWVsZCAtIOWtl+auteagh+ivhg0KICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IOWtl+auteagh+etvg0KICAgICAqLw0KICAgIGdldEZpZWxkTGFiZWwoZmllbGQpIHsNCiAgICAgIHJldHVybiB0aGlzLmZpZWxkTGFiZWxNYXBbZmllbGRdPy5uYW1lIHx8IGZpZWxkDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOWIhue7hOaVsOaNrg0KICAgICAqIEByZXR1cm5zIHtPYmplY3R9IOWIhue7hOWQjueahOaVsOaNrg0KICAgICAqLw0KICAgIGdyb3VwRGF0YSgpIHsNCiAgICAgIGNvbnN0IGdyb3VwcyA9IHt9DQoNCiAgICAgIHRoaXMuZGF0YVNvdXJjZS5mb3JFYWNoKGl0ZW0gPT4gew0KLy8g5aaC5p6c6K6+572u5LqG5YiG57uE5pel5pyf77yM5L2G6K6w5b2V5Lit6K+l5a2X5q615Li656m677yM5YiZ6Lez6L+H6K+l6K6w5b2VDQogICAgICAgIGlmICh0aGlzLmNvbmZpZy5kYXRlRmllbGQgJiYgIWl0ZW1bdGhpcy5jb25maWcuZGF0ZUZpZWxkXSkgew0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5Yid5aeL5YyW5YiG57uE6ZSu5Li65Li75YiG57uE5a2X5q6155qE5YC8DQogICAgICAgIGxldCBwcmltYXJ5S2V5VmFsdWUgPSBpdGVtW3RoaXMuY29uZmlnLnByaW1hcnlGaWVsZF0NCg0KICAgICAgICAvLyDlpITnkIbmlofmnKzljLnphY0NCiAgICAgICAgaWYgKHR5cGVvZiBwcmltYXJ5S2V5VmFsdWUgPT09ICJzdHJpbmciKSB7DQogICAgICAgICAgaWYgKCF0aGlzLmNvbmZpZy5tYXRjaE9wdGlvbnMuY2FzZVNlbnNpdGl2ZSkgew0KICAgICAgICAgICAgcHJpbWFyeUtleVZhbHVlID0gcHJpbWFyeUtleVZhbHVlLnRvTG93ZXJDYXNlKCkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHRoaXMuY29uZmlnLm1hdGNoT3B0aW9ucy5leGFjdCkgew0KICAgICAgICAgICAgcHJpbWFyeUtleVZhbHVlID0gcHJpbWFyeUtleVZhbHVlLnRyaW0oKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOacgOe7iOeahOWIhue7hOmUrg0KICAgICAgICBsZXQgZ3JvdXBLZXkgPSBwcmltYXJ5S2V5VmFsdWUNCg0KICAgICAgICAvLyDlpoLmnpzlkIzml7borr7nva7kuobml6XmnJ/lrZfmrrXvvIzliJnnu4TlkIjkuKTkuKrlrZfmrrXkvZzkuLrliIbnu4TplK4NCiAgICAgICAgaWYgKHRoaXMuY29uZmlnLmRhdGVGaWVsZCAmJiBpdGVtW3RoaXMuY29uZmlnLmRhdGVGaWVsZF0pIHsNCiAgICAgICAgICBjb25zdCBkYXRlID0gbW9tZW50KGl0ZW1bdGhpcy5jb25maWcuZGF0ZUZpZWxkXSkNCiAgICAgICAgICBsZXQgZGF0ZVZhbHVlDQoNCiAgICAgICAgICAvLyDmoLnmja7ml6XmnJ/moLzlvI/ljJbpgInpobnlpITnkIbml6XmnJ8NCiAgICAgICAgICBpZiAodGhpcy5jb25maWcuZGF0ZU9wdGlvbnMuZm9ybWF0VHlwZSkgew0KICAgICAgICAgICAgZGF0ZVZhbHVlID0gZGF0ZS5mb3JtYXQodGhpcy5nZXREYXRlRm9ybWF0KCkpDQogICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmNvbmZpZy5kYXRlT3B0aW9ucy5jb252ZXJ0VG9OdW1iZXIpIHsNCiAgICAgICAgICAgIGRhdGVWYWx1ZSA9IGRhdGUudmFsdWVPZigpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGRhdGVWYWx1ZSA9IGRhdGUuZm9ybWF0KCJZWVlZLU1NLUREIikNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDosIPmlbTkuLrml6XmnJ/kvZzkuLrkuLvopoHliIbnu4TplK7vvIzkuLvliIbnu4TlrZfmrrXkvZzkuLrmrKHopoHliIbnu4TplK4NCiAgICAgICAgICBncm91cEtleSA9IHsNCiAgICAgICAgICAgIHByaW1hcnk6IHByaW1hcnlLZXlWYWx1ZSwNCiAgICAgICAgICAgIGRhdGU6IGRhdGVWYWx1ZSwNCiAgICAgICAgICAgIC8vIOeUqOS6jk1hcOmUrueahOWtl+espuS4suihqOekuu+8jOWwhuaXpeacn+aUvuWcqOWJjemdog0KICAgICAgICAgICAgdG9TdHJpbmc6IGZ1bmN0aW9uICgpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIGAke3RoaXMuZGF0ZX1fJHt0aGlzLnByaW1hcnl9YA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOWIm+W7uuWIhue7hOaIlua3u+WKoOWIsOeOsOacieWIhue7hA0KICAgICAgICBjb25zdCBrZXkgPSBncm91cEtleS50b1N0cmluZyA/IGdyb3VwS2V5LnRvU3RyaW5nKCkgOiBncm91cEtleQ0KICAgICAgICBpZiAoIWdyb3Vwc1trZXldKSB7DQogICAgICAgICAgZ3JvdXBzW2tleV0gPSB7DQogICAgICAgICAgICBpdGVtczogW10sDQogICAgICAgICAgICBncm91cEtleTogZ3JvdXBLZXkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgZ3JvdXBzW2tleV0uaXRlbXMucHVzaChpdGVtKQ0KICAgICAgfSkNCg0KICAgICAgcmV0dXJuIGdyb3Vwcw0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDorqHnrpfmsYfmgLvlgLwNCiAgICAgKiBAcGFyYW0ge09iamVjdH0gZ3JvdXBzIC0g5YiG57uE5ZCO55qE5pWw5o2uDQogICAgICogQHJldHVybnMge0FycmF5fSDmsYfmgLvnu5PmnpwNCiAgICAgKi8NCiAgICBjYWxjdWxhdGVBZ2dyZWdhdGlvbnMoZ3JvdXBzKSB7DQogICAgICByZXR1cm4gT2JqZWN0LnZhbHVlcyhncm91cHMpLm1hcChncm91cCA9PiB7DQogICAgICAgIC8vIOehruS/nSBncm91cCDmmK/mraPnoa7nmoTnu5PmnoQNCiAgICAgICAgY29uc3QgaXRlbXMgPSBncm91cC5pdGVtcyB8fCBbXQ0KICAgICAgICBjb25zdCBncm91cEtleSA9IGdyb3VwLmdyb3VwS2V5DQoNCiAgICAgICAgY29uc3QgcmVzdWx0ID0ge2dyb3VwS2V5fQ0KDQogICAgICAgIHRoaXMuY29uZmlnLmZpZWxkcy5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgICBpZiAoIWZpZWxkLmZpZWxkS2V5KSByZXR1cm4NCg0KICAgICAgICAgIGNvbnN0IGZpZWxkQ29uZmlnID0gdGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkLmZpZWxkS2V5XQ0KICAgICAgICAgIGlmICghZmllbGRDb25maWcpIHJldHVybg0KDQogICAgICAgICAgLy8g6I635Y+W5Y6f5aeL5YC8DQogICAgICAgICAgY29uc3QgdmFsdWVzID0gaXRlbXMubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgcmV0dXJuIGl0ZW1bZmllbGQuZmllbGRLZXldDQogICAgICAgICAgfSkNCiAgICAgICAgICBjb25zdCBwcm9wID0gdGhpcy5nZXRSZXN1bHRQcm9wKGZpZWxkKQ0KDQogICAgICAgICAgLy8g5a+55LqO6Ieq5a6a5LmJIGRpc3BsYXkg5pa55rOV55qE5a2X5q6177yM5oiR5Lus6ZyA6KaB54m55q6K5aSE55CGDQogICAgICAgICAgY29uc3QgaXNDdXN0b21EaXNwbGF5ID0gZmllbGRDb25maWcuZGlzcGxheSAmJiB0eXBlb2YgdGhpc1tmaWVsZENvbmZpZy5kaXNwbGF5XSA9PT0gImZ1bmN0aW9uIg0KDQogICAgICAgICAgaWYgKGlzQ3VzdG9tRGlzcGxheSkgew0KICAgICAgICAgICAgLy8g5a+55LqO6Ieq5a6a5LmJ5pa55rOV77yM5oiR5Lus5Y+q5Y+W56ys5LiA5Liq5YC877yM5LiN6L+b6KGM5rGH5oC7DQogICAgICAgICAgICByZXN1bHRbcHJvcF0gPSB2YWx1ZXNbMF0NCiAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgIH0NCg0KICAgICAgICAgIHN3aXRjaCAoZmllbGRDb25maWcuZGlzcGxheSkgew0KICAgICAgICAgICAgY2FzZSAibnVtYmVyIjoNCiAgICAgICAgICAgICAgLy8g6L+H5ruk5bm26L2s5o2i5Li65pWw5a2XDQogICAgICAgICAgICAgIGNvbnN0IG51bWVyaWNWYWx1ZXMgPSB2YWx1ZXMNCiAgICAgICAgICAgICAgICAuZmlsdGVyKHYgPT4gdiAhPSBudWxsKQ0KICAgICAgICAgICAgICAgIC5tYXAodiA9PiBOdW1iZXIodikpDQogICAgICAgICAgICAgICAgLmZpbHRlcih2ID0+ICFpc05hTih2KSkNCg0KICAgICAgICAgICAgICBpZiAobnVtZXJpY1ZhbHVlcy5sZW5ndGggPT09IDApIHsNCiAgICAgICAgICAgICAgICByZXN1bHRbcHJvcF0gPSBudWxsDQogICAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBzd2l0Y2ggKGZpZWxkLmFnZ3JlZ2F0aW9uKSB7DQogICAgICAgICAgICAgICAgY2FzZSAic3VtIjoNCiAgICAgICAgICAgICAgICAgIHJlc3VsdFtwcm9wXSA9IG51bWVyaWNWYWx1ZXMucmVkdWNlKChzdW0sIHZhbCkgPT4gew0KICAgICAgICAgICAgICAgICAgICByZXR1cm4gTnVtYmVyKChzdW0gKyB2YWwpLnRvRml4ZWQoMikpDQogICAgICAgICAgICAgICAgICB9LCAwKQ0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgICBjYXNlICJhdmciOg0KICAgICAgICAgICAgICAgICAgcmVzdWx0W3Byb3BdID0gTnVtYmVyKChudW1lcmljVmFsdWVzLnJlZHVjZSgoc3VtLCB2YWwpID0+IHN1bSArIHZhbCwgMCkgLyBudW1lcmljVmFsdWVzLmxlbmd0aCkudG9GaXhlZCgyKSkNCiAgICAgICAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgICAgICAgY2FzZSAibWF4IjoNCiAgICAgICAgICAgICAgICAgIHJlc3VsdFtwcm9wXSA9IE1hdGgubWF4KC4uLm51bWVyaWNWYWx1ZXMpDQogICAgICAgICAgICAgICAgICBicmVhaw0KICAgICAgICAgICAgICAgIGNhc2UgIm1pbiI6DQogICAgICAgICAgICAgICAgICByZXN1bHRbcHJvcF0gPSBNYXRoLm1pbiguLi5udW1lcmljVmFsdWVzKQ0KICAgICAgICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICAgICAgICBjYXNlICJ2YXJpYW5jZSI6DQogICAgICAgICAgICAgICAgICBpZiAobnVtZXJpY1ZhbHVlcy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IG1lYW4gPSBudW1lcmljVmFsdWVzLnJlZHVjZSgoc3VtLCB2YWwpID0+IHN1bSArIHZhbCwgMCkgLyBudW1lcmljVmFsdWVzLmxlbmd0aA0KICAgICAgICAgICAgICAgICAgICByZXN1bHRbcHJvcF0gPSBOdW1iZXIoKG51bWVyaWNWYWx1ZXMucmVkdWNlKChzdW0sIHZhbCkgPT4NCiAgICAgICAgICAgICAgICAgICAgICBzdW0gKyBNYXRoLnBvdyh2YWwgLSBtZWFuLCAyKSwgMCkgLyBudW1lcmljVmFsdWVzLmxlbmd0aCkudG9GaXhlZCgyKSkNCiAgICAgICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgICAgIHJlc3VsdFtwcm9wXSA9IDANCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgICAgICAgY2FzZSAibm9uZSI6DQogICAgICAgICAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICAgICAgICAgIHJlc3VsdFtwcm9wXSA9IHZhbHVlc1swXQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGJyZWFrDQogICAgICAgICAgICBjYXNlICJkYXRlIjoNCiAgICAgICAgICAgIGNhc2UgInRleHQiOg0KICAgICAgICAgICAgY2FzZSAiYm9vbGVhbiI6DQogICAgICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgICAgICByZXN1bHRbcHJvcF0gPSB2YWx1ZXNbMF0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQoNCiAgICAgICAgcmV0dXJuIHJlc3VsdA0KICAgICAgfSkNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5aSE55CG5YiG57G75rGH5oC75oyJ6ZKu54K55Ye75LqL5Lu2DQogICAgICog6aqM6K+B6YWN572u5bm25omn6KGM5pWw5o2u5rGH5oC7DQogICAgICovDQogICAgaGFuZGxlQWdncmVnYXRlKCkgew0KICAgICAgaWYgKCF0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fpgInmi6nliIbnu4Tkvp3mja7lrZfmrrUiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgaWYgKCF0aGlzLmNvbmZpZy5maWVsZHMubGVuZ3RoKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35re75Yqg6KaB5rGH5oC755qE5a2X5q61IikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRyeSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgICAgLy8g5aSE55CG5pWw5o2uDQogICAgICAgIGNvbnN0IGdyb3VwcyA9IHRoaXMuZ3JvdXBEYXRhKCkNCiAgICAgICAgdGhpcy5wcm9jZXNzZWREYXRhID0gdGhpcy5jYWxjdWxhdGVBZ2dyZWdhdGlvbnMoZ3JvdXBzKQ0KDQogICAgICAgIC8vIOW6lOeUqOaOkuW6jw0KICAgICAgICB0aGlzLmFwcGx5U29ydGluZygpDQoNCiAgICAgICAgdGhpcy5zaG93UmVzdWx0ID0gdHJ1ZQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5rGH5oC75aSE55CG5aSx6LSl77yaIiArIGVycm9yLm1lc3NhZ2UpDQogICAgICB9IGZpbmFsbHkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDlupTnlKjmjpLluo/op4TliJnliLDlpITnkIblkI7nmoTmlbDmja4NCiAgICAgKi8NCiAgICBhcHBseVNvcnRpbmcoKSB7DQogICAgICAvLyDmn6Xmib7nrKzkuIDkuKrorr7nva7kuobmjpLluo/nmoTlrZfmrrUNCiAgICAgIGNvbnN0IHNvcnRGaWVsZCA9IHRoaXMuY29uZmlnLmZpZWxkcy5maW5kKGZpZWxkID0+IGZpZWxkLnNvcnQgIT09ICJub25lIikNCg0KICAgICAgaWYgKCFzb3J0RmllbGQpIHJldHVybiAvLyDlpoLmnpzmsqHmnInorr7nva7mjpLluo/vvIznm7TmjqXov5Tlm54NCg0KICAgICAgY29uc3QgcHJvcCA9IHRoaXMuZ2V0UmVzdWx0UHJvcChzb3J0RmllbGQpDQogICAgICBjb25zdCBpc0FzYyA9IHNvcnRGaWVsZC5zb3J0ID09PSAiYXNjIg0KDQogICAgICAvLyDmoLnmja7lrZfmrrXnsbvlnovlkozmjpLluo/mlrnlkJHov5vooYzmjpLluo8NCiAgICAgIGNvbnN0IGZpZWxkQ29uZmlnID0gdGhpcy5maWVsZExhYmVsTWFwW3NvcnRGaWVsZC5maWVsZEtleV0NCiAgICAgIGlmICghZmllbGRDb25maWcpIHJldHVybg0KDQogICAgICB0aGlzLnByb2Nlc3NlZERhdGEuc29ydCgoYSwgYikgPT4gew0KICAgICAgICBsZXQgdmFsdWVBID0gYVtwcm9wXQ0KICAgICAgICBsZXQgdmFsdWVCID0gYltwcm9wXQ0KDQogICAgICAgIC8vIOagueaNruWtl+auteexu+Wei+i/m+ihjOavlOi+gw0KICAgICAgICBzd2l0Y2ggKGZpZWxkQ29uZmlnLmRpc3BsYXkpIHsNCiAgICAgICAgICBjYXNlICJudW1iZXIiOg0KICAgICAgICAgICAgdmFsdWVBID0gTnVtYmVyKHZhbHVlQSkgfHwgMA0KICAgICAgICAgICAgdmFsdWVCID0gTnVtYmVyKHZhbHVlQikgfHwgMA0KICAgICAgICAgICAgYnJlYWsNCiAgICAgICAgICBjYXNlICJkYXRlIjoNCiAgICAgICAgICAgIHZhbHVlQSA9IHZhbHVlQSA/IG5ldyBEYXRlKHZhbHVlQSkuZ2V0VGltZSgpIDogMA0KICAgICAgICAgICAgdmFsdWVCID0gdmFsdWVCID8gbmV3IERhdGUodmFsdWVCKS5nZXRUaW1lKCkgOiAwDQogICAgICAgICAgICBicmVhaw0KICAgICAgICAgIGNhc2UgImJvb2xlYW4iOg0KICAgICAgICAgICAgdmFsdWVBID0gdmFsdWVBID8gMSA6IDANCiAgICAgICAgICAgIHZhbHVlQiA9IHZhbHVlQiA/IDEgOiAwDQogICAgICAgICAgICBicmVhaw0KICAgICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgICB2YWx1ZUEgPSBTdHJpbmcodmFsdWVBIHx8ICIiKQ0KICAgICAgICAgICAgdmFsdWVCID0gU3RyaW5nKHZhbHVlQiB8fCAiIikNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOagueaNruaOkuW6j+aWueWQkei/lOWbnuavlOi+g+e7k+aenA0KICAgICAgICBpZiAoaXNBc2MpIHsNCiAgICAgICAgICByZXR1cm4gdmFsdWVBID4gdmFsdWVCID8gMSA6IHZhbHVlQSA8IHZhbHVlQiA/IC0xIDogMA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHJldHVybiB2YWx1ZUEgPCB2YWx1ZUIgPyAxIDogdmFsdWVBID4gdmFsdWVCID8gLTEgOiAwDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIGhhbmRsZUZpZWxkQ2hhbmdlKCkgew0KICAgICAgLy8g5a2X5q615Y+Y5YyW5pe26YeN572u55u45YWz6YWN572uDQogICAgICB0aGlzLmNvbmZpZy5zZWxlY3RlZEZpZWxkcyA9IFtdDQogICAgICB0aGlzLmNvbmZpZy5kaXNwbGF5RmllbGRzID0gW10NCiAgICB9LA0KDQogICAgYXN5bmMgc2F2ZUNvbmZpZygpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIC8vIOmqjOivgemFjee9ruWQjeensA0KICAgICAgICBpZiAoIXRoaXMuY29uZmlnLm5hbWUpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+i+k+WFpemAn+afpeWQjeensCIpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDpqozor4Hlv4XopoHnmoTphY3nva7pobkNCiAgICAgICAgaWYgKCF0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGQpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeWIhue7hOS+neaNruWtl+autSIpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICBpZiAoIXRoaXMuY29uZmlnLmZpZWxkcy5sZW5ndGgpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+a3u+WKoOiHs+WwkeS4gOS4quWtl+autSIpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCg0KICAgICAgICAvLyDpqozor4HlrZfmrrXphY3nva7mmK/lkKblrozmlbQNCiAgICAgICAgY29uc3QgaW5jb21wbGV0ZUZpZWxkID0gdGhpcy5jb25maWcuZmllbGRzLmZpbmQoZmllbGQgPT4gIWZpZWxkLmZpZWxkS2V5KQ0KICAgICAgICBpZiAoaW5jb21wbGV0ZUZpZWxkKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flrozmiJDmiYDmnInlrZfmrrXnmoTphY3nva4iKQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5p6E6YCg56ym5ZCIIEFnZ3JlZ2F0b3JDb25maWdEVE8g55qE5pWw5o2u57uT5p6EDQogICAgICAgIGNvbnN0IGNvbmZpZ1RvU2F2ZSA9IHsNCiAgICAgICAgICBuYW1lOiB0aGlzLmNvbmZpZy5uYW1lLA0KICAgICAgICAgIHR5cGU6ICdBZ2dyZWdhdG9yJywNCiAgICAgICAgICBjb25maWc6IHsNCiAgICAgICAgICAgIHByaW1hcnlGaWVsZDogdGhpcy5jb25maWcucHJpbWFyeUZpZWxkLA0KICAgICAgICAgICAgbWF0Y2hPcHRpb25zOiB0aGlzLmNvbmZpZy5tYXRjaE9wdGlvbnMsDQogICAgICAgICAgICBkYXRlRmllbGQ6IHRoaXMuY29uZmlnLmRhdGVGaWVsZCwNCiAgICAgICAgICAgIGRhdGVPcHRpb25zOiB0aGlzLmNvbmZpZy5kYXRlT3B0aW9ucywNCiAgICAgICAgICAgIHNob3dEZXRhaWxzOiB0aGlzLmNvbmZpZy5zaG93RGV0YWlscywNCiAgICAgICAgICAgIGZpZWxkczogdGhpcy5jb25maWcuZmllbGRzDQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5Y+R6YCB6K+35rGCDQogICAgICAgIGF3YWl0IHNhdmVBZ2dyZWdhdG9yQ29uZmlnKGNvbmZpZ1RvU2F2ZSkNCg0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIumFjee9ruS/neWtmOaIkOWKnyIpDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgaWYgKGVyciAhPT0gImNhbmNlbCIpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLkv53lrZjphY3nva7lpLHotKXvvJoiICsgKGVyci5tZXNzYWdlIHx8ICLmnKrnn6XplJnor68iKSkNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0sDQoNCiAgICBhc3luYyBsb2FkQ29uZmlncygpIHsNCiAgICAgIHRoaXMuY29uZmlnTG9hZGluZyA9IHRydWUNCiAgICAgIHRoaXMuY29uZmlnRGlhbG9nVmlzaWJsZSA9IHRydWUNCiAgICAgIHRyeSB7DQogICAgICAgIGxldCByZXN1bHQgPSBhd2FpdCBsb2FkQWdncmVnYXRvckNvbmZpZ3Moe2NvbmZpZ1R5cGU6ICdBZ2dyZWdhdG9yJ30pDQogICAgICAgIGNvbnN0IGNvbmZpZ3MgPSByZXN1bHQucm93cw0KDQogICAgICAgIC8vIOmqjOivgei/lOWbnueahOaVsOaNruagvOW8jw0KICAgICAgICBpZiAoIUFycmF5LmlzQXJyYXkoY29uZmlncykpIHsNCiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoIui/lOWbnuaVsOaNruagvOW8j+mUmeivryIpDQogICAgICAgIH0NCg0KICAgICAgICAvLyDkv53nlZnljp/lp4vphY3nva7mlbDmja7vvIzlj6rlnKjlv4XopoHml7bmj5Dkvpvpu5jorqTlgLwNCiAgICAgICAgdGhpcy5zYXZlZENvbmZpZ3MgPSBjb25maWdzLm1hcChjb25maWcgPT4gKHsNCiAgICAgICAgICBpZDogY29uZmlnLmlkLA0KICAgICAgICAgIG5hbWU6IGNvbmZpZy5uYW1lLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6IGNvbmZpZy5jcmVhdGVUaW1lLA0KICAgICAgICAgIGNvbmZpZzogY29uZmlnLmNvbmZpZyB8fCB7DQogICAgICAgICAgICBwcmltYXJ5RmllbGQ6ICIiLA0KICAgICAgICAgICAgc2Vjb25kYXJ5RmllbGQ6ICIiLA0KICAgICAgICAgICAgdGV4dE1hdGNoTW9kZTogImV4YWN0IiwNCiAgICAgICAgICAgIGNhc2VTZW5zaXRpdmU6IGZhbHNlLA0KICAgICAgICAgICAgZGF0ZUdyYW51bGFyaXR5OiAiZGF5IiwNCiAgICAgICAgICAgIGFnZ3JlZ2F0aW9uTWV0aG9kczogWyJjb3VudCIsICJzdW0iXSwNCiAgICAgICAgICAgIHNob3dEZXRhaWxzOiBmYWxzZSwNCiAgICAgICAgICAgIHNlbGVjdGVkRmllbGRzOiBbXSwNCiAgICAgICAgICAgIGRpc3BsYXlGaWVsZHM6IFtdDQogICAgICAgICAgfQ0KICAgICAgICB9KSkNCg0KICAgICAgICB0aGlzLmNvbmZpZ0RpYWxvZ1Zpc2libGUgPSB0cnVlDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L296YWN572u5aSx6LSlOiIsIGVycikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigNCiAgICAgICAgICBlcnIucmVzcG9uc2U/LmRhdGE/Lm1lc3NhZ2UgfHwNCiAgICAgICAgICBlcnIubWVzc2FnZSB8fA0KICAgICAgICAgICLliqDovb3phY3nva7liJfooajlpLHotKXvvIzor7fnqI3lkI7ph43or5UiDQogICAgICAgICkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMuY29uZmlnTG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBoYW5kbGVDb25maWdTZWxlY3Qocm93KSB7DQogICAgICB0cnkgew0KICAgICAgICAvLyDnoa7kv53phY3nva7lr7nosaHljIXlkKvmiYDmnInlv4XopoHnmoTlrZfmrrUNCiAgICAgICAgY29uc3QgZGVmYXVsdENvbmZpZyA9IHsNCiAgICAgICAgICBwcmltYXJ5RmllbGQ6ICIiLA0KICAgICAgICAgIHNlY29uZGFyeUZpZWxkOiAiIiwNCiAgICAgICAgICB0ZXh0TWF0Y2hNb2RlOiAiZXhhY3QiLA0KICAgICAgICAgIGNhc2VTZW5zaXRpdmU6IGZhbHNlLA0KICAgICAgICAgIGRhdGVHcmFudWxhcml0eTogImRheSIsDQogICAgICAgICAgYWdncmVnYXRpb25NZXRob2RzOiBbImNvdW50IiwgInN1bSJdLA0KICAgICAgICAgIHNob3dEZXRhaWxzOiBmYWxzZSwNCiAgICAgICAgICBzZWxlY3RlZEZpZWxkczogW10sDQogICAgICAgICAgZGlzcGxheUZpZWxkczogW10NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOa3seaLt+i0nemFjee9ruWvueixoe+8jOmBv+WFjeW8leeUqOmXrumimA0KICAgICAgICB0aGlzLmNvbmZpZyA9IHsNCiAgICAgICAgICAuLi5kZWZhdWx0Q29uZmlnLA0KICAgICAgICAgIC4uLkpTT04ucGFyc2Uocm93LmNvbmZpZyksDQogICAgICAgICAgbmFtZTogcm93Lm5hbWUNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuY29uZmlnRGlhbG9nVmlzaWJsZSA9IGZhbHNlDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi6YWN572u5Yqg6L295oiQ5YqfIikNCg0KICAgICAgICAvLyDop6blj5HooajljZXph43mlrDmuLLmn5MNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIC8vIOWmguaenOmcgOimge+8jOWPr+S7peWcqOi/memHjOa3u+WKoOmineWklueahOWkhOeQhumAu+i+kQ0KICAgICAgICAgIGNvbnNvbGUubG9nKCLphY3nva7lt7LliqDovb06IiwgdGhpcy5jb25maWcpDQogICAgICAgIH0pDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L296YWN572u5aSx6LSlOiIsIGVycikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi5Yqg6L296YWN572u5aSx6LSl77yaIiArIGVyci5tZXNzYWdlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgZGVsZXRlQ29uZmlnKHJvdykgew0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgdGhpcy4kY29uZmlybSgi56Gu6K6k5Yig6Zmk6K+l6YWN572u77yfIiwgIuaPkOekuiIsIHsNCiAgICAgICAgICB0eXBlOiAid2FybmluZyINCiAgICAgICAgfSkNCg0KICAgICAgICBhd2FpdCBkZWxldGVBZ2dyZWdhdG9yQ29uZmlnKHJvdy5pZCkNCiAgICAgICAgdGhpcy5zYXZlZENvbmZpZ3MgPSB0aGlzLnNhdmVkQ29uZmlncy5maWx0ZXIoY29uZmlnID0+IGNvbmZpZy5pZCAhPT0gcm93LmlkKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIumFjee9ruWIoOmZpOaIkOWKnyIpDQogICAgICB9IGNhdGNoIChlcnIpIHsNCiAgICAgICAgaWYgKGVyciAhPT0gImNhbmNlbCIpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliKDpmaTphY3nva7lpLHotKXvvJoiICsgZXJyLm1lc3NhZ2UpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgcHJpbnRUYWJsZSgpIHsNCiAgICAgIGNvbnN0IHByaW50V2luZG93ID0gd2luZG93Lm9wZW4oIiIsICJfYmxhbmsiKQ0KICAgICAgY29uc3QgdGFibGUgPSB0aGlzLiRyZWZzLnJlc3VsdFRhYmxlLiRlbC5jbG9uZU5vZGUodHJ1ZSkNCiAgICAgIGNvbnN0IHRpdGxlID0gIiINCiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZygpDQoNCiAgICAgIC8vIOWFrOWPuOagh+W/l+WSjOagh+mimOeahEhUTUzmqKHmnb8NCiAgICAgIGNvbnN0IGhlYWRlclRlbXBsYXRlID0gYA0KICAgICAgICA8ZGl2IGNsYXNzPSJjb21wYW55LWhlYWRlciI+DQogICAgICAgICAgPGRpdiBjbGFzcz0iY29tcGFueS1sb2dvIj4NCiAgICAgICAgICAgIDxpbWcgc3JjPSIvbG9nby5wbmciIGFsdD0iUmljaCBTaGlwcGluZyBMb2dvIiAvPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29tcGFueS1uYW1lIj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29tcGFueS1uYW1lLWNuIj7lub/lt57nkZ7ml5flm73pmYXotKfov5Dku6PnkIbmnInpmZDlhazlj7g8L2Rpdj4NCiAgICAgICAgICAgICAgPGRpdiBjbGFzcz0iY29tcGFueS1uYW1lLWVuIj5HVUFOR1pIT1UgUklDSCBTSElQUElORyBJTlQnTCBDTy4sTFRELjwvZGl2Pg0KICAgICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPC9kaXY+DQogICAgICAgICAgPGRpdiBjbGFzcz0iZG9jdW1lbnQtdGl0bGUiPg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0idGl0bGUtY24iPuWvuei0puWNleaxh+aAuzwvZGl2Pg0KICAgICAgICAgICAgPGRpdiBjbGFzcz0idGl0bGUtZW4iPltERUJJVCBOT1RFXTwvZGl2Pg0KICAgICAgICAgIDwvZGl2Pg0KICAgICAgICA8L2Rpdj4NCiAgICAgIGANCg0KICAgICAgcHJpbnRXaW5kb3cuZG9jdW1lbnQud3JpdGUoYA0KICAgICAgICA8aHRtbCBsYW5nPSIiPg0KICAgICAgICAgIDxoZWFkPg0KICAgICAgICAgICAgPHRpdGxlPiR7dGl0bGV9PC90aXRsZT4NCiAgICAgICAgICAgIDxzdHlsZT4NCiAgICAgICAgICAvKiDln7rnoYDmoLflvI8gKi8NCiAgICAgICAgICBib2R5IHsNCiAgICAgICAgICAgIG1hcmdpbjogMDsNCiAgICAgICAgICAgIHBhZGRpbmc6IDA7DQogICAgICAgICAgICBmb250LWZhbWlseTogQXJpYWwsIHNhbnMtc2VyaWY7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLyog5omT5Y2w5qC35byPIC0g5b+F6aG75pS+5Zyo6L+Z6YeM5omN6IO955Sf5pWIICovDQogICAgICAgICAgQG1lZGlhIHByaW50IHsNCiAgICAgICAgICAgIEBwYWdlIHsNCiAgICAgICAgICAgICAgc2l6ZTogJHt0aGlzLmlzTGFuZHNjYXBlID8gImxhbmRzY2FwZSIgOiAicG9ydHJhaXQifTsNCiAgICAgICAgICAgICAgbWFyZ2luOiAxLjVjbSAxY20gMWNtIDFjbTsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLyog6YeN6KaB77ya5L2/55So6YeN5aSN6KGo5aS05oqA5pyvICovDQogICAgICAgICAgICB0aGVhZCB7DQogICAgICAgICAgICAgIGRpc3BsYXk6IHRhYmxlLWhlYWRlci1ncm91cDsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLyog6aG155yJ5L2c5Li66KGo5qC855qE5LiA6YOo5YiG77yM5pS+5ZyodGhlYWTkuK0gKi8NCiAgICAgICAgICAgIC5wYWdlLWhlYWRlciB7DQogICAgICAgICAgICAgIGRpc3BsYXk6IHRhYmxlLWhlYWRlci1ncm91cDsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLyog5YaF5a656YOo5YiGICovDQogICAgICAgICAgICAucGFnZS1jb250ZW50IHsNCiAgICAgICAgICAgICAgZGlzcGxheTogdGFibGUtcm93LWdyb3VwOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvKiDpobXohJogKi8NCiAgICAgICAgICAgIHRmb290IHsNCiAgICAgICAgICAgICAgZGlzcGxheTogdGFibGUtZm9vdGVyLWdyb3VwOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvKiDpgb/lhY3lhYPntKDlhoXpg6jliIbpobUgKi8NCiAgICAgICAgICAgIC5jb21wYW55LWhlYWRlciwgLmhlYWRlci1jb250ZW50IHsNCiAgICAgICAgICAgICAgcGFnZS1icmVhay1pbnNpZGU6IGF2b2lkOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvKiDooajmoLzmoLflvI8gKi8NCiAgICAgICAgICAgIHRhYmxlLm1haW4tdGFibGUgew0KICAgICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgICAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsNCiAgICAgICAgICAgICAgYm9yZGVyOiBub25lOw0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvKiDnoa7kv53ooajlpLTlnKjmr4/pobXpg73mmL7npLogKi8NCiAgICAgICAgICAgIHRhYmxlLmRhdGEtdGFibGUgdGhlYWQgew0KICAgICAgICAgICAgICBkaXNwbGF5OiB0YWJsZS1oZWFkZXItZ3JvdXA7DQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIC8qIOmBv+WFjeihjOWGheWIhumhtSAqLw0KICAgICAgICAgICAgdGFibGUuZGF0YS10YWJsZSB0ciB7DQogICAgICAgICAgICAgIHBhZ2UtYnJlYWstaW5zaWRlOiBhdm9pZDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvKiDooajmoLzmoLflvI8gKi8NCiAgICAgICAgICB0YWJsZS5kYXRhLXRhYmxlIHsNCiAgICAgICAgICAgIGJvcmRlci1jb2xsYXBzZTogY29sbGFwc2U7DQogICAgICAgICAgICB3aWR0aDogMTAwJTsNCiAgICAgICAgICAgIG1hcmdpbi10b3A6IDIwcHg7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGFibGUuZGF0YS10YWJsZSB0aCwgdGFibGUuZGF0YS10YWJsZSB0ZCB7DQogICAgICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjZGRkOw0KICAgICAgICAgICAgcGFkZGluZzogOHB4Ow0KICAgICAgICAgICAgdGV4dC1hbGlnbjogbGVmdDsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0YWJsZS5kYXRhLXRhYmxlIHRoIHsNCiAgICAgICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmMmYyZjI7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLyogRWxlbWVudCBVSSDooajmoLzmoLflvI/mqKHmi58gKi8NCiAgICAgICAgICAuZWwtdGFibGUgew0KICAgICAgICAgICAgYm9yZGVyLWNvbGxhcHNlOiBjb2xsYXBzZTsNCiAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5lbC10YWJsZSB0aCwgLmVsLXRhYmxlIHRkIHsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7DQogICAgICAgICAgICBwYWRkaW5nOiA4cHg7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiBsZWZ0Ow0KICAgICAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5lbC10YWJsZSB0aCB7DQogICAgICAgICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjJmMmYyOw0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLmVsLXRhYmxlX19mb290ZXIgew0KICAgICAgICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjhmOTsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5lbC10YWJsZV9fZm9vdGVyIHRkIHsNCiAgICAgICAgICAgIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7DQogICAgICAgICAgICBwYWRkaW5nOiA4cHg7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLyog5YWs5Y+45qCH6aKY5ZKM5qCH5b+X5qC35byPICovDQogICAgICAgICAgLmNvbXBhbnktaGVhZGVyIHsNCiAgICAgICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgICAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47DQogICAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAgICAgICAgYm9yZGVyLWJvdHRvbTogMnB4IHNvbGlkICMwMDA7DQogICAgICAgICAgICBwYWRkaW5nLWJvdHRvbTogMTBweDsNCiAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5jb21wYW55LWxvZ28gew0KICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLmNvbXBhbnktbG9nbyBpbWcgew0KICAgICAgICAgICAgaGVpZ2h0OiA1MHB4Ow0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5jb21wYW55LW5hbWUgew0KICAgICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLmNvbXBhbnktbmFtZS1jbiB7DQogICAgICAgICAgICBmb250LXNpemU6IDE4cHg7DQogICAgICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgICAgIGNvbG9yOiAjZmYwMDAwOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5jb21wYW55LW5hbWUtZW4gew0KICAgICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC5kb2N1bWVudC10aXRsZSB7DQogICAgICAgICAgICB0ZXh0LWFsaWduOiByaWdodDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAudGl0bGUtY24gew0KICAgICAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICAgICAgfQ0KDQogICAgICAgICAgLnRpdGxlLWVuIHsNCiAgICAgICAgICAgIGZvbnQtc2l6ZTogMTZweDsNCiAgICAgICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8qIOa4hemZpOihqOagvOi+ueahhiAqLw0KICAgICAgICAgIHRhYmxlLm1haW4tdGFibGUsIHRhYmxlLm1haW4tdGFibGUgdGQgew0KICAgICAgICAgICAgYm9yZGVyOiBub25lOw0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8qIOmhteecieWuueWZqCAqLw0KICAgICAgICAgIC5oZWFkZXItY29udGFpbmVyIHsNCiAgICAgICAgICAgIHdpZHRoOiAxMDAlOw0KICAgICAgICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvKiDml6XmnJ/kv6Hmga8gKi8NCiAgICAgICAgICAuZGF0ZS1pbmZvIHsNCiAgICAgICAgICAgIHRleHQtYWxpZ246IHJpZ2h0Ow0KICAgICAgICAgICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICAgICAgICAgIG1hcmdpbi1ib3R0b206IDIwcHg7DQogICAgICAgICAgfQ0KICAgICAgICA8L3N0eWxlPg0KICAgICAgICAgIDwvaGVhZD4NCiAgICAgICAgICA8Ym9keT4NCiAgICAgICAgICAgIDwhLS0g5L2/55So6KGo5qC85biD5bGA56Gu5L+d6aG155yJ5Zyo5q+P6aG16YeN5aSNIC0tPg0KICAgICAgICAgICAgPHRhYmxlIGNsYXNzPSJtYWluLXRhYmxlIj4NCiAgICAgICAgICAgICAgPHRoZWFkIGNsYXNzPSJwYWdlLWhlYWRlciI+DQogICAgICAgICAgICAgICAgPHRyPg0KICAgICAgICAgICAgICAgICAgPHRkPg0KICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzPSJoZWFkZXItY29udGFpbmVyIj4NCiAgICAgICAgICAgICAgICAgICAgICAke2hlYWRlclRlbXBsYXRlfQ0KICAgICAgICAgICAgICAgICAgICA8L2Rpdj4NCiAgICAgICAgICAgICAgICAgIDwvdGQ+DQogICAgICAgICAgICAgICAgPC90cj4NCiAgICAgICAgICAgICAgPC90aGVhZD4NCiAgICAgICAgICAgICAgPHRib2R5IGNsYXNzPSJwYWdlLWNvbnRlbnQiPg0KICAgICAgICAgICAgICAgIDx0cj4NCiAgICAgICAgICAgICAgICAgIDx0ZD4NCiAgICAgICAgICAgICAgICAgICAgPCEtLSDkv53nlZnljp/lp4vooajmoLznmoTnsbvlkI3lubbmt7vliqBkYXRhLXRhYmxl57G7IC0tPg0KICAgICAgICAgICAgICAgICAgICAke3RhYmxlLm91dGVySFRNTC5yZXBsYWNlKCc8dGFibGUnLCAnPHRhYmxlIGNsYXNzPSJlbC10YWJsZSBkYXRhLXRhYmxlIicpfQ0KICAgICAgICAgICAgICAgICAgPC90ZD4NCiAgICAgICAgICAgICAgICA8L3RyPg0KICAgICAgICAgICAgICA8L3Rib2R5Pg0KICAgICAgICAgICAgICA8dGZvb3Q+DQogICAgICAgICAgICAgICAgPHRyPg0KICAgICAgICAgICAgICAgICAgPHRkPjwvdGQ+DQogICAgICAgICAgICAgICAgPC90cj4NCiAgICAgICAgICAgICAgPC90Zm9vdD4NCiAgICAgICAgICAgIDwvdGFibGU+DQogICAgICAgICAgPC9ib2R5Pg0KICAgICAgICA8L2h0bWw+DQogICAgICBgKQ0KDQogICAgICBwcmludFdpbmRvdy5kb2N1bWVudC5jbG9zZSgpDQoNCiAgICAgIHNldFRpbWVvdXQoKCkgPT4gew0KICAgICAgICB0cnkgew0KICAgICAgICAgIHByaW50V2luZG93LmZvY3VzKCk7DQogICAgICAgICAgcHJpbnRXaW5kb3cucHJpbnQoKTsNCiAgICAgICAgfSBjYXRjaCAoZSkgew0KICAgICAgICAgIGNvbnNvbGUuZXJyb3IoIuaJk+WNsOi/h+eoi+S4reWPkeeUn+mUmeivrzoiLCBlKTsNCiAgICAgICAgfQ0KICAgICAgfSwgMTAwMCkNCiAgICB9LA0KDQogICAgYXN5bmMgZXhwb3J0VG9QREYoKSB7DQogICAgICB0cnkgew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICAgIGNvbnN0IGVsZW1lbnQgPSB0aGlzLiRyZWZzLnJlc3VsdFRhYmxlLiRlbA0KICAgICAgICBjb25zdCBvcHQgPSB7DQogICAgICAgICAgbWFyZ2luOiBbMC44LCAwLjgsIDAuOCwgMC44XSwgLy8g5LiK5Y+z5LiL5bem6L656Led77yI6Iux5a+477yJDQogICAgICAgICAgZmlsZW5hbWU6ICLmsYfmgLvmlbDmja4ucGRmIiwNCiAgICAgICAgICBpbWFnZToge3R5cGU6ICJqcGVnIiwgcXVhbGl0eTogMC45OH0sDQogICAgICAgICAgaHRtbDJjYW52YXM6IHtzY2FsZTogMn0sDQogICAgICAgICAganNQREY6IHsNCiAgICAgICAgICAgIHVuaXQ6ICJpbiIsDQogICAgICAgICAgICBmb3JtYXQ6ICJhMyIsDQogICAgICAgICAgICBvcmllbnRhdGlvbjogdGhpcy5pc0xhbmRzY2FwZSA/ICJsYW5kc2NhcGUiIDogInBvcnRyYWl0Ig0KICAgICAgICAgIH0sDQogICAgICAgICAgcGFnZWJyZWFrOiB7bW9kZTogWyJhdm9pZC1hbGwiLCAiY3NzIiwgImxlZ2FjeSJdfSwgLy8g5re75Yqg5YiG6aG15o6n5Yi2DQogICAgICAgICAgaGVhZGVyOiBbDQogICAgICAgICAgICB7dGV4dDogIuaxh+aAu+aVsOaNriIsIHN0eWxlOiAiaGVhZGVyU3R5bGUifSwNCiAgICAgICAgICAgIHt0ZXh0OiBuZXcgRGF0ZSgpLnRvTG9jYWxlRGF0ZVN0cmluZygpLCBzdHlsZTogImhlYWRlclN0eWxlIiwgYWxpZ25tZW50OiAicmlnaHQifQ0KICAgICAgICAgIF0sDQogICAgICAgICAgZm9vdGVyOiB7DQogICAgICAgICAgICBoZWlnaHQ6ICIyMHB4IiwNCiAgICAgICAgICAgIGNvbnRlbnRzOiB7DQogICAgICAgICAgICAgIGRlZmF1bHQ6ICI8c3BhbiBzdHlsZT1cImZsb2F0OnJpZ2h0XCI+e3twYWdlfX0ve3twYWdlc319PC9zcGFuPiIgLy8g5re75Yqg6aG156CBDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgYXdhaXQgaHRtbDJwZGYoKS5zZXQob3B0KS5mcm9tKGVsZW1lbnQpLnNhdmUoKQ0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIlBERuWvvOWHuuaIkOWKnyIpDQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCJQREblr7zlh7rlpLHotKXvvJoiICsgZXJyb3IubWVzc2FnZSkNCiAgICAgIH0gZmluYWxseSB7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOa3u+WKoOaWsOeahOWtl+autemFjee9ruihjA0KICAgICAqIOWIneWni+WMluS4gOS4quaWsOeahOWtl+autemFjee9ruWvueixoe+8jOWMheWQq+m7mOiupOWAvA0KICAgICAqLw0KICAgIGFkZEZpZWxkKCkgew0KICAgICAgdGhpcy5jb25maWcuZmllbGRzLnB1c2goew0KICAgICAgICBmaWVsZEtleTogIiIsICAgICAgICAvLyDlrZfmrrXmoIfor4YNCiAgICAgICAgYWdncmVnYXRpb246ICJub25lIiwgLy8g5rGH5oC75pa55byPDQogICAgICAgIGZvcm1hdDogIm5vbmUiLCAgICAgIC8vIOaYvuekuuagvOW8jw0KICAgICAgICBzb3J0OiAibm9uZSIgICAgICAgICAvLyDmjpLluo/mlrnlvI8NCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOWIoOmZpOaMh+Wumue0ouW8leeahOWtl+autemFjee9ruihjA0KICAgICAqIEBwYXJhbSB7bnVtYmVyfSBpbmRleCAtIOimgeWIoOmZpOeahOWtl+autee0ouW8lQ0KICAgICAqLw0KICAgIHJlbW92ZUZpZWxkKGluZGV4KSB7DQogICAgICB0aGlzLmNvbmZpZy5maWVsZHMuc3BsaWNlKGluZGV4LCAxKQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDnp7vliqjlrZfmrrXphY3nva7ooYznmoTkvY3nva4NCiAgICAgKiBAcGFyYW0ge251bWJlcn0gaW5kZXggLSDlvZPliY3lrZfmrrXnmoTntKLlvJUNCiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZGlyZWN0aW9uIC0g56e75Yqo5pa55ZCR77yMJ3VwJyDmiJYgJ2Rvd24nDQogICAgICovDQogICAgbW92ZUZpZWxkKGluZGV4LCBkaXJlY3Rpb24pIHsNCiAgICAgIGNvbnN0IGZpZWxkcyA9IFsuLi50aGlzLmNvbmZpZy5maWVsZHNdIC8vIOWIm+W7uuaVsOe7hOWJr+acrA0KDQogICAgICBpZiAoZGlyZWN0aW9uID09PSAidXAiICYmIGluZGV4ID4gMCkgew0KICAgICAgICAvLyDlkJHkuIrnp7vliqjvvIzkuI7kuIrkuIDkuKrlhYPntKDkuqTmjaLkvY3nva4NCiAgICAgICAgW2ZpZWxkc1tpbmRleF0sIGZpZWxkc1tpbmRleCAtIDFdXSA9IFtmaWVsZHNbaW5kZXggLSAxXSwgZmllbGRzW2luZGV4XV0NCiAgICAgIH0gZWxzZSBpZiAoZGlyZWN0aW9uID09PSAiZG93biIgJiYgaW5kZXggPCBmaWVsZHMubGVuZ3RoIC0gMSkgew0KICAgICAgICAvLyDlkJHkuIvnp7vliqjvvIzkuI7kuIvkuIDkuKrlhYPntKDkuqTmjaLkvY3nva4NCiAgICAgICAgW2ZpZWxkc1tpbmRleF0sIGZpZWxkc1tpbmRleCArIDFdXSA9IFtmaWVsZHNbaW5kZXggKyAxXSwgZmllbGRzW2luZGV4XV0NCiAgICAgIH0NCg0KICAgICAgLy8g5L2/55So5pW05Liq5paw5pWw57uE5pu/5o2i77yM56Gu5L+d5ZON5bqU5byP5pu05pawDQogICAgICB0aGlzLiRzZXQodGhpcy5jb25maWcsICJmaWVsZHMiLCBmaWVsZHMpDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOWkhOeQhuWtl+autemAieaLqeWPmOabtOS6i+S7tg0KICAgICAqIOagueaNrumAieaLqeeahOWtl+auteiHquWKqOiuvue9ruebuOWFs+mFjee9rg0KICAgICAqIEBwYXJhbSB7bnVtYmVyfSBpbmRleCAtIOWPmOabtOeahOWtl+autee0ouW8lQ0KICAgICAqLw0KICAgIGhhbmRsZUZpZWxkU2VsZWN0KGluZGV4KSB7DQogICAgICBjb25zdCBmaWVsZCA9IHRoaXMuY29uZmlnLmZpZWxkc1tpbmRleF0NCiAgICAgIGNvbnN0IGZpZWxkQ29uZmlnID0gdGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkLmZpZWxkS2V5XQ0KICAgICAgaWYgKGZpZWxkQ29uZmlnKSB7DQogICAgICAgIC8vIOagueaNruWtl+autemFjee9ruiuvue9rum7mOiupOWAvA0KICAgICAgICBmaWVsZC5mb3JtYXQgPSB0aGlzLmdldERlZmF1bHRGb3JtYXQoZmllbGRDb25maWcuZGlzcGxheSkNCiAgICAgICAgZmllbGQuYWdncmVnYXRpb24gPSBmaWVsZENvbmZpZy5hZ2dyZWdhdGVkID8gInN1bSIgOiAibm9uZSINCiAgICAgICAgZmllbGQuc29ydCA9ICJub25lIiAvLyDpu5jorqTkuI3mjpLluo8NCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W5a2X5q6155qE5pi+56S657G75Z6LDQogICAgICogQHBhcmFtIHtzdHJpbmd9IGZpZWxkS2V5IC0g5a2X5q615qCH6K+GDQogICAgICogQHJldHVybnMge3N0cmluZ30g5a2X5q615pi+56S657G75Z6L77yIdGV4dC9udW1iZXIvZGF0ZS9ib29sZWFuL2N1c3Rvbe+8iQ0KICAgICAqLw0KICAgIGdldEZpZWxkRGlzcGxheShmaWVsZEtleSkgew0KICAgICAgY29uc3QgZmllbGRDb25maWcgPSB0aGlzLmZpZWxkTGFiZWxNYXBbZmllbGRLZXldDQogICAgICBpZiAoIWZpZWxkQ29uZmlnKSByZXR1cm4gInRleHQiDQoNCiAgICAgIC8vIOajgOafpeaYr+WQpuaYr+iHquWumuS5ieaWueazlQ0KICAgICAgaWYgKGZpZWxkQ29uZmlnLmRpc3BsYXkgJiYgdHlwZW9mIHRoaXNbZmllbGRDb25maWcuZGlzcGxheV0gPT09ICJmdW5jdGlvbiIpIHsNCiAgICAgICAgcmV0dXJuICJjdXN0b20iDQogICAgICB9DQoNCiAgICAgIHJldHVybiBmaWVsZENvbmZpZy5kaXNwbGF5IHx8ICJ0ZXh0Ig0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDliKTmlq3lrZfmrrXmmK/lkKblj6/ku6Xov5vooYzmsYfmgLvorqHnrpcNCiAgICAgKiBAcGFyYW0ge3N0cmluZ30gZmllbGRLZXkgLSDlrZfmrrXmoIfor4YNCiAgICAgKiBAcmV0dXJucyB7Ym9vbGVhbn0g5piv5ZCm5Y+v5rGH5oC7DQogICAgICovDQogICAgaXNBZ2dyZWdhdGFibGUoZmllbGRLZXkpIHsNCiAgICAgIGNvbnN0IGZpZWxkQ29uZmlnID0gdGhpcy5maWVsZExhYmVsTWFwW2ZpZWxkS2V5XQ0KICAgICAgcmV0dXJuIGZpZWxkQ29uZmlnPy5hZ2dyZWdhdGVkIHx8IGZhbHNlDQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOagueaNruaYvuekuuexu+Wei+iOt+WPlum7mOiupOeahOagvOW8j+WMluaWueW8jw0KICAgICAqIEBwYXJhbSB7c3RyaW5nfSBkaXNwbGF5VHlwZSAtIOaYvuekuuexu+Weiw0KICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IOm7mOiupOagvOW8jw0KICAgICAqLw0KICAgIGdldERlZmF1bHRGb3JtYXQoZGlzcGxheVR5cGUpIHsNCiAgICAgIHN3aXRjaCAoZGlzcGxheVR5cGUpIHsNCiAgICAgICAgY2FzZSAiZGF0ZSI6DQogICAgICAgICAgcmV0dXJuICJZWVlZLU1NLUREIg0KICAgICAgICBjYXNlICJudW1iZXIiOg0KICAgICAgICAgIHJldHVybiAiZGVjaW1hbCINCiAgICAgICAgZGVmYXVsdDoNCiAgICAgICAgICByZXR1cm4gIm5vbmUiDQogICAgICB9DQogICAgfSwNCg0KICAgIC8qKg0KICAgICAqIOmHjee9rumFjee9ruWIsOWIneWni+eKtuaAgQ0KICAgICAqLw0KICAgIHJlc2V0Q29uZmlnKCkgew0KICAgICAgdGhpcy5jb25maWcgPSB7DQogICAgICAgIG5hbWU6ICIiLA0KICAgICAgICBwcmltYXJ5RmllbGQ6ICIiLA0KICAgICAgICBtYXRjaE9wdGlvbnM6IHsNCiAgICAgICAgICBleGFjdDogdHJ1ZSwNCiAgICAgICAgICBjYXNlU2Vuc2l0aXZlOiBmYWxzZQ0KICAgICAgICB9LA0KICAgICAgICBkYXRlRmllbGQ6ICIiLA0KICAgICAgICBkYXRlT3B0aW9uczogew0KICAgICAgICAgIGNvbnZlcnRUb051bWJlcjogZmFsc2UsDQogICAgICAgICAgZm9ybWF0VHlwZTogImRheSINCiAgICAgICAgfSwNCiAgICAgICAgc2hvd0RldGFpbHM6IGZhbHNlLA0KICAgICAgICBmaWVsZHM6IFtdDQogICAgICB9DQogICAgICB0aGlzLnNob3dSZXN1bHQgPSBmYWxzZQ0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDojrflj5bnu5PmnpzmlbDmja7nmoTlsZ7mgKflkI0NCiAgICAgKiBAcGFyYW0ge09iamVjdH0gZmllbGQgLSDlrZfmrrXphY3nva4NCiAgICAgKiBAcmV0dXJucyB7c3RyaW5nfSDlsZ7mgKflkI0NCiAgICAgKi8NCiAgICBnZXRSZXN1bHRQcm9wKGZpZWxkKSB7DQogICAgICAvLyDlpoLmnpzmnInmsYfmgLvmlrnlvI/vvIzlsZ7mgKflkI3kuLogZmllbGRLZXlfYWdncmVnYXRpb24NCiAgICAgIGlmIChmaWVsZC5hZ2dyZWdhdGlvbiAmJiBmaWVsZC5hZ2dyZWdhdGlvbiAhPT0gIm5vbmUiKSB7DQogICAgICAgIHJldHVybiBgJHtmaWVsZC5maWVsZEtleX1fJHtmaWVsZC5hZ2dyZWdhdGlvbn1gDQogICAgICB9DQogICAgICByZXR1cm4gZmllbGQuZmllbGRLZXkNCiAgICB9LA0KDQogICAgLyoqDQogICAgICog6I635Y+W57uT5p6c6KGo5qC855qE5YiX5qCH6aKYDQogICAgICogQHBhcmFtIHtPYmplY3R9IGZpZWxkIC0g5a2X5q616YWN572uDQogICAgICogQHJldHVybnMge3N0cmluZ30g5YiX5qCH6aKYDQogICAgICovDQogICAgZ2V0UmVzdWx0TGFiZWwoZmllbGQpIHsNCiAgICAgIGNvbnN0IGJhc2VMYWJlbCA9IHRoaXMuZ2V0RmllbGRMYWJlbChmaWVsZC5maWVsZEtleSkNCg0KICAgICAgLy8g5aaC5p6c5pyJ5rGH5oC75pa55byP77yM5Zyo5qCH562+5Lit5re75Yqg5rGH5oC75pa55byP5L+h5oGvDQogICAgICBpZiAoZmllbGQuYWdncmVnYXRpb24gJiYgZmllbGQuYWdncmVnYXRpb24gIT09ICJub25lIikgew0KICAgICAgICAvLyDojrflj5bmsYfmgLvmlrnlvI/nmoTkuK3mloflkI3np7ANCiAgICAgICAgY29uc3QgYWdncmVnYXRpb25MYWJlbCA9IHRoaXMuYWdncmVnYXRpb25PcHRpb25zLmZpbmQob3B0ID0+IG9wdC52YWx1ZSA9PT0gZmllbGQuYWdncmVnYXRpb24pPy5sYWJlbCB8fCBmaWVsZC5hZ2dyZWdhdGlvbg0KICAgICAgICByZXR1cm4gYCR7YmFzZUxhYmVsfSgke2FnZ3JlZ2F0aW9uTGFiZWx9KWANCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIGJhc2VMYWJlbA0KICAgIH0sDQoNCiAgICAvKioNCiAgICAgKiDmoLzlvI/ljJbljZXlhYPmoLznmoTlgLwNCiAgICAgKiBAcGFyYW0geyp9IHZhbHVlIC0g5Y6f5aeL5YC8DQogICAgICogQHBhcmFtIHtPYmplY3R9IGZpZWxkIC0g5a2X5q616YWN572uDQogICAgICogQHJldHVybnMge3N0cmluZ30g5qC85byP5YyW5ZCO55qE5YC8DQogICAgICovDQogICAgZm9ybWF0Q2VsbFZhbHVlKHZhbHVlLCBmaWVsZCkgew0KICAgICAgaWYgKHZhbHVlID09IG51bGwpIHJldHVybiAiLSINCg0KICAgICAgY29uc3QgZmllbGRDb25maWcgPSB0aGlzLmZpZWxkTGFiZWxNYXBbZmllbGQuZmllbGRLZXldDQogICAgICBpZiAoIWZpZWxkQ29uZmlnKSByZXR1cm4gdmFsdWUNCg0KICAgICAgLy8g5aSE55CG6Ieq5a6a5LmJIGRpc3BsYXkg5pa55rOVDQogICAgICBpZiAoZmllbGRDb25maWcuZGlzcGxheSAmJiB0eXBlb2YgdGhpc1tmaWVsZENvbmZpZy5kaXNwbGF5XSA9PT0gImZ1bmN0aW9uIikgew0KICAgICAgICAvLyDosIPnlKjnu4Tku7bkuK3lrprkuYnnmoTmlrnms5UNCiAgICAgICAgcmV0dXJuIHRoaXNbZmllbGRDb25maWcuZGlzcGxheV0odmFsdWUpDQogICAgICB9DQoNCiAgICAgIC8vIOagueaNruWtl+auteexu+Wei+i/m+ihjOagvOW8j+WMlg0KICAgICAgc3dpdGNoIChmaWVsZENvbmZpZy5kaXNwbGF5KSB7DQogICAgICAgIGNhc2UgIm51bWJlciI6DQogICAgICAgICAgY29uc3QgbnVtVmFsdWUgPSBOdW1iZXIodmFsdWUpDQogICAgICAgICAgaWYgKGlzTmFOKG51bVZhbHVlKSkgcmV0dXJuICItIg0KDQogICAgICAgICAgLy8g5aaC5p6c5piv5rGH5oC75a2X5q6177yM5YWI5bqU55So5rGH5oC75qC85byP5YyWDQogICAgICAgICAgaWYgKGZpZWxkLmFnZ3JlZ2F0aW9uICYmIGZpZWxkLmFnZ3JlZ2F0aW9uICE9PSAibm9uZSIpIHsNCiAgICAgICAgICAgIC8vIOWvueS6juW5s+Wdh+WAvOWSjOaWueW3ru+8jOS/neeVmeabtOWkmuWwj+aVsOS9jQ0KICAgICAgICAgICAgaWYgKGZpZWxkLmFnZ3JlZ2F0aW9uID09PSAiYXZnIiB8fCBmaWVsZC5hZ2dyZWdhdGlvbiA9PT0gInZhcmlhbmNlIikgew0KICAgICAgICAgICAgICBpZiAoZmllbGQuZm9ybWF0ID09PSAicGVyY2VudCIpIHsNCiAgICAgICAgICAgICAgICByZXR1cm4gKG51bVZhbHVlICogMTAwKS50b0ZpeGVkKDIpICsgIiUiDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgcmV0dXJuIG51bVZhbHVlLnRvRml4ZWQoMikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQoNCiAgICAgICAgICBzd2l0Y2ggKGZpZWxkLmZvcm1hdCkgew0KICAgICAgICAgICAgY2FzZSAiZGVjaW1hbCI6DQogICAgICAgICAgICAgIHJldHVybiBudW1WYWx1ZS50b0ZpeGVkKDIpDQogICAgICAgICAgICBjYXNlICJwZXJjZW50IjoNCiAgICAgICAgICAgICAgcmV0dXJuIChudW1WYWx1ZSAqIDEwMCkudG9GaXhlZCgyKSArICIlIg0KICAgICAgICAgICAgY2FzZSAiY3VycmVuY3kiOg0KICAgICAgICAgICAgICByZXR1cm4gIsKlIiArIG51bVZhbHVlLnRvRml4ZWQoMikNCiAgICAgICAgICAgIGNhc2UgInVzZCI6DQogICAgICAgICAgICAgIHJldHVybiAiJCIgKyBudW1WYWx1ZS50b0ZpeGVkKDIpDQogICAgICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgICAgICByZXR1cm4gbnVtVmFsdWUudG9GaXhlZCgyKQ0KICAgICAgICAgIH0NCg0KICAgICAgICBjYXNlICJkYXRlIjoNCiAgICAgICAgICByZXR1cm4gbW9tZW50KHZhbHVlKS5mb3JtYXQoZmllbGQuZm9ybWF0IHx8ICJZWVlZLU1NLUREIikNCg0KICAgICAgICBjYXNlICJib29sZWFuIjoNCiAgICAgICAgICBpZiAoZmllbGQuYWdncmVnYXRpb24gPT09ICJhdmciKSB7DQogICAgICAgICAgICByZXR1cm4gKE51bWJlcih2YWx1ZSkgKiAxMDApLnRvRml4ZWQoMikgKyAiJSINCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIHZhbHVlID8gIuaYryIgOiAi5ZCmIg0KDQogICAgICAgIGRlZmF1bHQ6DQogICAgICAgICAgcmV0dXJuIHZhbHVlDQogICAgICB9DQogICAgfSwNCg0KICAgIGdldERhdGVGb3JtYXQoKSB7DQogICAgICBzd2l0Y2ggKHRoaXMuY29uZmlnLmRhdGVPcHRpb25zLmZvcm1hdFR5cGUpIHsNCiAgICAgICAgY2FzZSAieWVhciI6DQogICAgICAgICAgcmV0dXJuICJZWVlZIg0KICAgICAgICBjYXNlICJtb250aCI6DQogICAgICAgICAgcmV0dXJuICJZWVlZLU1NIg0KICAgICAgICBjYXNlICJkYXkiOg0KICAgICAgICBkZWZhdWx0Og0KICAgICAgICAgIHJldHVybiAiWVlZWS1NTS1ERCINCiAgICAgIH0NCiAgICB9LA0KDQogICAgLyoqDQogICAgICog5qC85byP5YyW5YiG57uE6ZSuDQogICAgICogQHBhcmFtIHtPYmplY3R8c3RyaW5nfSBncm91cEtleSAtIOWIhue7hOmUrg0KICAgICAqIEByZXR1cm5zIHtzdHJpbmd9IOagvOW8j+WMluWQjueahOWIhue7hOmUrg0KICAgICAqLw0KICAgIGZvcm1hdEdyb3VwS2V5KGdyb3VwS2V5KSB7DQogICAgICBpZiAodHlwZW9mIGdyb3VwS2V5ID09PSAib2JqZWN0IiAmJiBncm91cEtleSAhPT0gbnVsbCkgew0KICAgICAgICBpZiAoZ3JvdXBLZXkucHJpbWFyeSAhPT0gdW5kZWZpbmVkICYmIGdyb3VwS2V5LmRhdGUgIT09IHVuZGVmaW5lZCkgew0KICAgICAgICAgIC8vIOiOt+WPluS4u+WIhue7hOWtl+auteeahOmFjee9rg0KICAgICAgICAgIGNvbnN0IHByaW1hcnlGaWVsZENvbmZpZyA9IHRoaXMuZmllbGRMYWJlbE1hcFt0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGRdDQogICAgICAgICAgbGV0IHByaW1hcnlWYWx1ZSA9IGdyb3VwS2V5LnByaW1hcnkNCg0KICAgICAgICAgIC8vIOWmguaenOS4u+WIhue7hOWtl+auteacieiHquWumuS5iSBkaXNwbGF5IOaWueazle+8jOW6lOeUqOWugw0KICAgICAgICAgIGlmIChwcmltYXJ5RmllbGRDb25maWcgJiYgcHJpbWFyeUZpZWxkQ29uZmlnLmRpc3BsYXkgJiYNCiAgICAgICAgICAgIHR5cGVvZiB0aGlzW3ByaW1hcnlGaWVsZENvbmZpZy5kaXNwbGF5XSA9PT0gImZ1bmN0aW9uIikgew0KICAgICAgICAgICAgcHJpbWFyeVZhbHVlID0gdGhpc1twcmltYXJ5RmllbGRDb25maWcuZGlzcGxheV0ocHJpbWFyeVZhbHVlKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOaXpeacn+WAvOWcqOWJje+8jOS4u+WAvOWcqOWQju+8jOS4jea3u+WKoOWIhumalOespg0KICAgICAgICAgIHJldHVybiBgJHtncm91cEtleS5kYXRlfSR7cHJpbWFyeVZhbHVlfWANCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmmK/nroDljZXlgLzvvIzmo4Dmn6XmmK/lkKbpnIDopoHlupTnlKjoh6rlrprkuYkgZGlzcGxheSDmlrnms5UNCiAgICAgIGlmICh0aGlzLmNvbmZpZy5wcmltYXJ5RmllbGQpIHsNCiAgICAgICAgY29uc3QgZmllbGRDb25maWcgPSB0aGlzLmZpZWxkTGFiZWxNYXBbdGhpcy5jb25maWcucHJpbWFyeUZpZWxkXQ0KICAgICAgICBpZiAoZmllbGRDb25maWcgJiYgZmllbGRDb25maWcuZGlzcGxheSAmJg0KICAgICAgICAgIHR5cGVvZiB0aGlzW2ZpZWxkQ29uZmlnLmRpc3BsYXldID09PSAiZnVuY3Rpb24iKSB7DQogICAgICAgICAgcmV0dXJuIHRoaXNbZmllbGRDb25maWcuZGlzcGxheV0oZ3JvdXBLZXkpDQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIFN0cmluZyhncm91cEtleSB8fCAiIikNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2RA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/DataAggregator", "sourcesContent": ["<template>\r\n  <div class=\"data-aggregator\">\r\n    <el-row :gutter=\"20\">\r\n      <!-- 配置区域 - 左侧 -->\r\n      <el-col :span=\"showResult ? 10 : 10\">\r\n        <el-card class=\"config-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总配置</span>\r\n            </div>\r\n          </template>\r\n          <el-form class=\"edit\" label-width=\"80px\">\r\n            <!-- 速查名称 -->\r\n            <el-form-item label=\"速查名称\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"18\">\r\n                  <el-input v-model=\"config.name\" placeholder=\"请输入速查名称\"/>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-button size=\"small\" type=\"text\" @click=\"saveConfig\">[↗]</el-button>\r\n                  <el-button size=\"small\" type=\"text\" @click=\"loadConfigs\">[...]</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组依据 -->\r\n            <el-form-item label=\"分组依据\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.primaryField\" clearable filterable placeholder=\"操作单号\">\r\n                    <el-option\r\n                      v-for=\"field in availableFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.matchOptions.exact\">精确匹配</el-checkbox>\r\n                  <el-checkbox v-model=\"config.matchOptions.caseSensitive\">区分大小写</el-checkbox>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组日期 -->\r\n            <el-form-item label=\"分组日期\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.dateField\" clearable filterable placeholder=\"分组日期\">\r\n                    <el-option\r\n                      v-for=\"field in dateFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.dateOptions.convertToNumber\">转换为数字</el-checkbox>\r\n                  <el-radio-group v-model=\"config.dateOptions.formatType\" style=\"display: flex;line-height: 26px\">\r\n                    <el-radio label=\"year\">按年</el-radio>\r\n                    <el-radio label=\"month\">按月</el-radio>\r\n                    <el-radio label=\"day\">按天</el-radio>\r\n                  </el-radio-group>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 显示方式 -->\r\n            <el-form-item label=\"显示方式\">\r\n              <el-checkbox v-model=\"config.showDetails\" style=\"padding-left: 5px;\">含明细</el-checkbox>\r\n              <el-switch\r\n                v-model=\"config.splitByCurrency\"\r\n                active-text=\"区分币种\">\r\n              </el-switch>\r\n            </el-form-item>\r\n\r\n            <!-- 动态字段配置 -->\r\n            <el-table\r\n              :data=\"config.fields\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"60\"\r\n              />\r\n\r\n              <el-table-column label=\"表头名称\" min-width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.fieldKey\"\r\n                    filterable\r\n                    placeholder=\"选择字段\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleFieldSelect(scope.$index)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(config, key) in fieldLabelMap\"\r\n                      :key=\"key\"\r\n                      :label=\"config.name\"\r\n                      :value=\"key\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"排序\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.sort\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"∧\" value=\"asc\"/>\r\n                    <el-option label=\"∨ \" value=\"desc\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"汇总方式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.aggregation\"\r\n                    :disabled=\"!isAggregatable(scope.row.fieldKey)\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"求和\" value=\"sum\"/>\r\n                    <el-option label=\"平均值\" value=\"avg\"/>\r\n                    <el-option label=\"最大值\" value=\"max\"/>\r\n                    <el-option label=\"最小值\" value=\"min\"/>\r\n                    <el-option label=\"方差\" value=\"variance\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"显示格式\" width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.format\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'date'\">\r\n                      <el-option label=\"YYYYMM\" value=\"YYYYMM\"/>\r\n                      <el-option label=\"MM-DD\" value=\"MM-DD\"/>\r\n                      <el-option label=\"YYYY-MM-DD\" value=\"YYYY-MM-DD\"/>\r\n                    </template>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'number'\">\r\n                      <el-option label=\"0.00\" value=\"decimal\"/>\r\n                      <el-option label=\"0.00%\" value=\"percent\"/>\r\n                      <el-option label=\"¥0.00\" value=\"currency\"/>\r\n                      <el-option label=\"$0.00\" value=\"usd\"/>\r\n                    </template>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n\r\n              <el-table-column align=\"center\" label=\"操作\" width=\"120\">\r\n                <template #default=\"scope\">\r\n                  <el-button-group>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === 0\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'up')\"\r\n                    >[∧]\r\n                    </el-button>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === config.fields.length - 1\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'down')\"\r\n                    >[∨]\r\n                    </el-button>\r\n                    <el-button\r\n                      icon=\"el-icon-delete\"\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"removeField(scope.$index)\"\r\n                    >\r\n                    </el-button>\r\n                  </el-button-group>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div style=\"margin-top: 10px;\">\r\n              <el-button plain type=\"text\" @click=\"addField\">[ + ]</el-button>\r\n            </div>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleAggregate\">分类汇总</el-button>\r\n              <el-button @click=\"resetConfig\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 结果展示 - 右侧 -->\r\n      <el-col v-if=\"showResult\" :span=\"14\">\r\n        <el-card class=\"result-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总结果</span>\r\n              <div class=\"operations\">\r\n                <el-switch\r\n                  v-model=\"isLandscape\"\r\n                  active-text=\"横向\"\r\n                  inactive-text=\"纵向\"\r\n                  style=\"margin-right: 15px\"\r\n                />\r\n                <el-button size=\"small\" @click=\"printTable\">打印</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"exportToPDF\">导出PDF</el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <el-table\r\n            ref=\"resultTable\"\r\n            v-loading=\"loading\"\r\n            :data=\"processedData\"\r\n            border\r\n            :summary-method=\"getSummary\"\r\n            show-summary\r\n            style=\"width: 100%\"\r\n          >\r\n            <!-- 分组字段列 -->\r\n            <el-table-column\r\n              :align=\"config.primaryField ? fieldLabelMap[config.primaryField].align : 'left'\"\r\n              :label=\"groupFieldName\"\r\n              :width=\"config.primaryField ? fieldLabelMap[config.primaryField].width : ''\"\r\n            >\r\n              <template #default=\"scope\">\r\n                {{ formatGroupKey(scope.row.groupKey) }}\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <!-- 动态字段列 -->\r\n            <template v-for=\"field in config.fields\">\r\n              <el-table-column\r\n                v-if=\"field.fieldKey\"\r\n                :key=\"field.fieldKey\"\r\n                :align=\"fieldLabelMap[field.fieldKey].align\"\r\n                :label=\"getResultLabel(field)\"\r\n                :width=\"fieldLabelMap[field.fieldKey].width\"\r\n              >\r\n                <template #default=\"scope\">\r\n                  {{ formatCellValue(scope.row[getResultProp(field)], field) }}\r\n                </template>\r\n              </el-table-column>\r\n            </template>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 加载配置对话框 -->\r\n    <el-dialog :visible.sync=\"configDialogVisible\" append-to-body title=\"加载配置\" width=\"500px\">\r\n      <el-table\r\n        v-loading=\"configLoading\"\r\n        :data=\"savedConfigs\"\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleConfigSelect\"\r\n      >\r\n        <el-table-column label=\"配置名称\" prop=\"name\"/>\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\"/>\r\n        <el-table-column width=\"120\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" @click.stop=\"deleteConfig(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from \"@/api/system/aggregator\"\r\nimport html2pdf from \"html2pdf.js\"\r\n\r\nexport default {\r\n  name: \"DataAggregator\",\r\n  props: {\r\n    dataSource: {\r\n      type: Array,\r\n      required: true\r\n    },\r\n    fieldLabelMap: {\r\n      type: Object,\r\n      required: true,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      configName: \"\",\r\n      config: {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        splitByCurrency: false,\r\n      },\r\n      dateOptions: [\r\n        {label: \"按年\", value: \"year\"},\r\n        {label: \"按月\", value: \"month\"},\r\n        {label: \"按周\", value: \"week\"},\r\n        {label: \"按日\", value: \"day\"},\r\n        {label: \"按时\", value: \"hour\"},\r\n        {label: \"按分\", value: \"minute\"}\r\n      ],\r\n      aggregationOptions: [\r\n        {label: \"计数\", value: \"count\"},\r\n        {label: \"求和\", value: \"sum\"},\r\n        {label: \"平均值\", value: \"avg\"},\r\n        {label: \"方差\", value: \"variance\"},\r\n        {label: \"最大值\", value: \"max\"},\r\n        {label: \"最小值\", value: \"min\"}\r\n      ],\r\n      loading: false,\r\n      configDialogVisible: false,\r\n      savedConfigs: [],\r\n      configLoading: false,\r\n      isLandscape: false,\r\n      showResult: false,\r\n      processedData: []\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用字段列表\r\n    availableFields() {\r\n      if (this.dataSource.length === 0) return []\r\n      // 只返回在 fieldLabelMap 中定义的字段\r\n      return Object.keys(this.dataSource[0]).filter(field => field in this.fieldLabelMap)\r\n    },\r\n\r\n    // 数值型字段列表\r\n    numericFields() {\r\n      // 过滤出数值类型的字段，同时确保它们在 fieldLabelMap 中存在\r\n      return this.availableFields.filter(field => {\r\n        return typeof this.dataSource[0][field] === \"number\"\r\n      })\r\n    },\r\n\r\n    // 当前字段类型\r\n    currentFieldType() {\r\n      if (!this.config.primaryField || !this.dataSource.length) return null\r\n      const sampleValue = this.dataSource[0][this.config.primaryField]\r\n      if (moment(sampleValue, moment.ISO_8601, true).isValid()) return \"date\"\r\n      return typeof sampleValue\r\n    },\r\n\r\n    // 分组字段名称\r\n    groupFieldName() {\r\n      if (this.config.primaryField && this.config.dateField) {\r\n        return `${this.getFieldLabel(this.config.dateField)}+${this.getFieldLabel(this.config.primaryField)}`\r\n      }\r\n      return this.getFieldLabel(this.config.primaryField)\r\n    },\r\n\r\n    dateFields() {\r\n      return this.availableFields.filter(field => {\r\n        // 首先检查 fieldLabelMap 中的 display 属性\r\n        if (this.fieldLabelMap[field] && this.fieldLabelMap[field].display === \"date\") {\r\n          return true\r\n        }\r\n\r\n        /* // 如果没有明确标记为日期，则尝试检查值是否为日期格式\r\n        const value = this.dataSource[0]?.[field];\r\n        return value && moment(value, moment.ISO_8601, true).isValid(); */\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    // ... existing code ...\r\n\r\n    /**\r\n     * 计算表格合计行\r\n     * @param {Object} param0 - 包含列信息和数据的对象\r\n     * @returns {Array} 合计行数据\r\n     */\r\n    getSummary({columns, data}) {\r\n      const sums = []\r\n\r\n      columns.forEach((column, index) => {\r\n        // 第一列显示\"合计\"文本\r\n        if (index === 0) {\r\n          sums[index] = \"合计\"\r\n          return\r\n        }\r\n\r\n        // 获取当前列对应的字段配置\r\n        const field = this.config.fields[index - 1]\r\n        if (!field || !field.fieldKey) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 检查字段是否配置了汇总方式\r\n        if (!field.aggregation || field.aggregation === \"none\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取字段配置\r\n        const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n        if (!fieldConfig || fieldConfig.display !== \"number\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取列数据并转换为数字\r\n        const values = data.map(item => {\r\n          const prop = this.getResultProp(field)\r\n          return Number(item[prop])\r\n        }).filter(val => !isNaN(val))\r\n\r\n        if (values.length === 0) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 根据汇总方式计算结果\r\n        let sum = 0\r\n        switch (field.aggregation) {\r\n          case \"sum\":\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n            break\r\n          case \"avg\":\r\n            sum = values.reduce((a, b) => a + b, 0) / values.length\r\n            break\r\n          case \"max\":\r\n            sum = Math.max(...values)\r\n            break\r\n          case \"min\":\r\n            sum = Math.min(...values)\r\n            break\r\n          case \"variance\":\r\n            const mean = values.reduce((a, b) => a + b, 0) / values.length\r\n            sum = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length\r\n            break\r\n          default:\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n        }\r\n\r\n        // 根据字段格式化设置格式化结果\r\n        if (field.format === \"decimal\") {\r\n          sums[index] = sum.toFixed(2)\r\n        } else if (field.format === \"percent\") {\r\n          sums[index] = (sum * 100).toFixed(2) + \"%\"\r\n        } else if (field.format === \"currency\") {\r\n          sums[index] = \"¥\" + sum.toFixed(2)\r\n        } else if (field.format === \"usd\") {\r\n          sums[index] = \"$\" + sum.toFixed(2)\r\n        } else {\r\n          sums[index] = sum\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffShortName + staff.staffFamilyEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n    /**\r\n     * 获取字段标签\r\n     * @param {string} field - 字段标识\r\n     * @returns {string} 字段标签\r\n     */\r\n    getFieldLabel(field) {\r\n      return this.fieldLabelMap[field]?.name || field\r\n    },\r\n\r\n    /**\r\n     * 分组数据\r\n     * @returns {Object} 分组后的数据\r\n     */\r\n    groupData() {\r\n      const groups = {}\r\n\r\n      this.dataSource.forEach(item => {\r\n// 如果设置了分组日期，但记录中该字段为空，则跳过该记录\r\n        if (this.config.dateField && !item[this.config.dateField]) {\r\n          return\r\n        }\r\n\r\n        // 初始化分组键为主分组字段的值\r\n        let primaryKeyValue = item[this.config.primaryField]\r\n\r\n        // 处理文本匹配\r\n        if (typeof primaryKeyValue === \"string\") {\r\n          if (!this.config.matchOptions.caseSensitive) {\r\n            primaryKeyValue = primaryKeyValue.toLowerCase()\r\n          }\r\n          if (this.config.matchOptions.exact) {\r\n            primaryKeyValue = primaryKeyValue.trim()\r\n          }\r\n        }\r\n\r\n        // 最终的分组键\r\n        let groupKey = primaryKeyValue\r\n\r\n        // 如果同时设置了日期字段，则组合两个字段作为分组键\r\n        if (this.config.dateField && item[this.config.dateField]) {\r\n          const date = moment(item[this.config.dateField])\r\n          let dateValue\r\n\r\n          // 根据日期格式化选项处理日期\r\n          if (this.config.dateOptions.formatType) {\r\n            dateValue = date.format(this.getDateFormat())\r\n          } else if (this.config.dateOptions.convertToNumber) {\r\n            dateValue = date.valueOf()\r\n          } else {\r\n            dateValue = date.format(\"YYYY-MM-DD\")\r\n          }\r\n\r\n          // 调整为日期作为主要分组键，主分组字段作为次要分组键\r\n          groupKey = {\r\n            primary: primaryKeyValue,\r\n            date: dateValue,\r\n            // 用于Map键的字符串表示，将日期放在前面\r\n            toString: function () {\r\n              return `${this.date}_${this.primary}`\r\n            }\r\n          }\r\n        }\r\n\r\n        // 创建分组或添加到现有分组\r\n        const key = groupKey.toString ? groupKey.toString() : groupKey\r\n        if (!groups[key]) {\r\n          groups[key] = {\r\n            items: [],\r\n            groupKey: groupKey\r\n          }\r\n        }\r\n        groups[key].items.push(item)\r\n      })\r\n\r\n      return groups\r\n    },\r\n\r\n    /**\r\n     * 计算汇总值\r\n     * @param {Object} groups - 分组后的数据\r\n     * @returns {Array} 汇总结果\r\n     */\r\n    calculateAggregations(groups) {\r\n      return Object.values(groups).map(group => {\r\n        // 确保 group 是正确的结构\r\n        const items = group.items || []\r\n        const groupKey = group.groupKey\r\n\r\n        const result = {groupKey}\r\n\r\n        this.config.fields.forEach(field => {\r\n          if (!field.fieldKey) return\r\n\r\n          const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n          if (!fieldConfig) return\r\n\r\n          // 获取原始值\r\n          const values = items.map(item => {\r\n            return item[field.fieldKey]\r\n          })\r\n          const prop = this.getResultProp(field)\r\n\r\n          // 对于自定义 display 方法的字段，我们需要特殊处理\r\n          const isCustomDisplay = fieldConfig.display && typeof this[fieldConfig.display] === \"function\"\r\n\r\n          if (isCustomDisplay) {\r\n            // 对于自定义方法，我们只取第一个值，不进行汇总\r\n            result[prop] = values[0]\r\n            return\r\n          }\r\n\r\n          switch (fieldConfig.display) {\r\n            case \"number\":\r\n              // 过滤并转换为数字\r\n              const numericValues = values\r\n                .filter(v => v != null)\r\n                .map(v => Number(v))\r\n                .filter(v => !isNaN(v))\r\n\r\n              if (numericValues.length === 0) {\r\n                result[prop] = null\r\n                return\r\n              }\r\n\r\n              switch (field.aggregation) {\r\n                case \"sum\":\r\n                  result[prop] = numericValues.reduce((sum, val) => {\r\n                    return Number((sum + val).toFixed(2))\r\n                  }, 0)\r\n                  break\r\n                case \"avg\":\r\n                  result[prop] = Number((numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length).toFixed(2))\r\n                  break\r\n                case \"max\":\r\n                  result[prop] = Math.max(...numericValues)\r\n                  break\r\n                case \"min\":\r\n                  result[prop] = Math.min(...numericValues)\r\n                  break\r\n                case \"variance\":\r\n                  if (numericValues.length > 0) {\r\n                    const mean = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length\r\n                    result[prop] = Number((numericValues.reduce((sum, val) =>\r\n                      sum + Math.pow(val - mean, 2), 0) / numericValues.length).toFixed(2))\r\n                  } else {\r\n                    result[prop] = 0\r\n                  }\r\n                  break\r\n                case \"none\":\r\n                default:\r\n                  result[prop] = values[0]\r\n              }\r\n              break\r\n            case \"date\":\r\n            case \"text\":\r\n            case \"boolean\":\r\n            default:\r\n              result[prop] = values[0]\r\n          }\r\n        })\r\n\r\n        return result\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 处理分类汇总按钮点击事件\r\n     * 验证配置并执行数据汇总\r\n     */\r\n    handleAggregate() {\r\n      if (!this.config.primaryField) {\r\n        this.$message.warning(\"请选择分组依据字段\")\r\n        return\r\n      }\r\n\r\n      if (!this.config.fields.length) {\r\n        this.$message.warning(\"请添加要汇总的字段\")\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loading = true\r\n        // 处理数据\r\n        const groups = this.groupData()\r\n        this.processedData = this.calculateAggregations(groups)\r\n\r\n        // 应用排序\r\n        this.applySorting()\r\n\r\n        this.showResult = true\r\n      } catch (error) {\r\n        this.$message.error(\"汇总处理失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 应用排序规则到处理后的数据\r\n     */\r\n    applySorting() {\r\n      // 查找第一个设置了排序的字段\r\n      const sortField = this.config.fields.find(field => field.sort !== \"none\")\r\n\r\n      if (!sortField) return // 如果没有设置排序，直接返回\r\n\r\n      const prop = this.getResultProp(sortField)\r\n      const isAsc = sortField.sort === \"asc\"\r\n\r\n      // 根据字段类型和排序方向进行排序\r\n      const fieldConfig = this.fieldLabelMap[sortField.fieldKey]\r\n      if (!fieldConfig) return\r\n\r\n      this.processedData.sort((a, b) => {\r\n        let valueA = a[prop]\r\n        let valueB = b[prop]\r\n\r\n        // 根据字段类型进行比较\r\n        switch (fieldConfig.display) {\r\n          case \"number\":\r\n            valueA = Number(valueA) || 0\r\n            valueB = Number(valueB) || 0\r\n            break\r\n          case \"date\":\r\n            valueA = valueA ? new Date(valueA).getTime() : 0\r\n            valueB = valueB ? new Date(valueB).getTime() : 0\r\n            break\r\n          case \"boolean\":\r\n            valueA = valueA ? 1 : 0\r\n            valueB = valueB ? 1 : 0\r\n            break\r\n          default:\r\n            valueA = String(valueA || \"\")\r\n            valueB = String(valueB || \"\")\r\n        }\r\n\r\n        // 根据排序方向返回比较结果\r\n        if (isAsc) {\r\n          return valueA > valueB ? 1 : valueA < valueB ? -1 : 0\r\n        } else {\r\n          return valueA < valueB ? 1 : valueA > valueB ? -1 : 0\r\n        }\r\n      })\r\n    },\r\n\r\n    handleFieldChange() {\r\n      // 字段变化时重置相关配置\r\n      this.config.selectedFields = []\r\n      this.config.displayFields = []\r\n    },\r\n\r\n    async saveConfig() {\r\n      try {\r\n        // 验证配置名称\r\n        if (!this.config.name) {\r\n          this.$message.warning(\"请输入速查名称\")\r\n          return\r\n        }\r\n\r\n        // 验证必要的配置项\r\n        if (!this.config.primaryField) {\r\n          this.$message.warning(\"请选择分组依据字段\")\r\n          return\r\n        }\r\n\r\n        if (!this.config.fields.length) {\r\n          this.$message.warning(\"请添加至少一个字段\")\r\n          return\r\n        }\r\n\r\n        // 验证字段配置是否完整\r\n        const incompleteField = this.config.fields.find(field => !field.fieldKey)\r\n        if (incompleteField) {\r\n          this.$message.warning(\"请完成所有字段的配置\")\r\n          return\r\n        }\r\n\r\n        // 构造符合 AggregatorConfigDTO 的数据结构\r\n        const configToSave = {\r\n          name: this.config.name,\r\n          type: 'Aggregator',\r\n          config: {\r\n            primaryField: this.config.primaryField,\r\n            matchOptions: this.config.matchOptions,\r\n            dateField: this.config.dateField,\r\n            dateOptions: this.config.dateOptions,\r\n            showDetails: this.config.showDetails,\r\n            fields: this.config.fields\r\n          }\r\n        }\r\n\r\n        // 发送请求\r\n        await saveAggregatorConfig(configToSave)\r\n\r\n        this.$message.success(\"配置保存成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"保存配置失败：\" + (err.message || \"未知错误\"))\r\n        }\r\n      }\r\n    },\r\n\r\n    async loadConfigs() {\r\n      this.configLoading = true\r\n      this.configDialogVisible = true\r\n      try {\r\n        let result = await loadAggregatorConfigs({configType: 'Aggregator'})\r\n        const configs = result.rows\r\n\r\n        // 验证返回的数据格式\r\n        if (!Array.isArray(configs)) {\r\n          throw new Error(\"返回数据格式错误\")\r\n        }\r\n\r\n        // 保留原始配置数据，只在必要时提供默认值\r\n        this.savedConfigs = configs.map(config => ({\r\n          id: config.id,\r\n          name: config.name,\r\n          createTime: config.createTime,\r\n          config: config.config || {\r\n            primaryField: \"\",\r\n            secondaryField: \"\",\r\n            textMatchMode: \"exact\",\r\n            caseSensitive: false,\r\n            dateGranularity: \"day\",\r\n            aggregationMethods: [\"count\", \"sum\"],\r\n            showDetails: false,\r\n            selectedFields: [],\r\n            displayFields: []\r\n          }\r\n        }))\r\n\r\n        this.configDialogVisible = true\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\r\n          err.response?.data?.message ||\r\n          err.message ||\r\n          \"加载配置列表失败，请稍后重试\"\r\n        )\r\n      } finally {\r\n        this.configLoading = false\r\n      }\r\n    },\r\n    async handleConfigSelect(row) {\r\n      try {\r\n        // 确保配置对象包含所有必要的字段\r\n        const defaultConfig = {\r\n          primaryField: \"\",\r\n          secondaryField: \"\",\r\n          textMatchMode: \"exact\",\r\n          caseSensitive: false,\r\n          dateGranularity: \"day\",\r\n          aggregationMethods: [\"count\", \"sum\"],\r\n          showDetails: false,\r\n          selectedFields: [],\r\n          displayFields: []\r\n        }\r\n\r\n        // 深拷贝配置对象，避免引用问题\r\n        this.config = {\r\n          ...defaultConfig,\r\n          ...JSON.parse(row.config),\r\n          name: row.name\r\n        }\r\n\r\n        this.configDialogVisible = false\r\n        this.$message.success(\"配置加载成功\")\r\n\r\n        // 触发表单重新渲染\r\n        this.$nextTick(() => {\r\n          // 如果需要，可以在这里添加额外的处理逻辑\r\n          console.log(\"配置已加载:\", this.config)\r\n        })\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\"加载配置失败：\" + err.message)\r\n      }\r\n    },\r\n    async deleteConfig(row) {\r\n      try {\r\n        await this.$confirm(\"确认删除该配置？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n\r\n        await deleteAggregatorConfig(row.id)\r\n        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)\r\n        this.$message.success(\"配置删除成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"删除配置失败：\" + err.message)\r\n        }\r\n      }\r\n    },\r\n\r\n    printTable() {\r\n      const printWindow = window.open(\"\", \"_blank\")\r\n      const table = this.$refs.resultTable.$el.cloneNode(true)\r\n      const title = \"\"\r\n      const date = new Date().toLocaleDateString()\r\n\r\n      // 公司标志和标题的HTML模板\r\n      const headerTemplate = `\r\n        <div class=\"company-header\">\r\n          <div class=\"company-logo\">\r\n            <img src=\"/logo.png\" alt=\"Rich Shipping Logo\" />\r\n            <div class=\"company-name\">\r\n              <div class=\"company-name-cn\">广州瑞旗国际货运代理有限公司</div>\r\n              <div class=\"company-name-en\">GUANGZHOU RICH SHIPPING INT'L CO.,LTD.</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"document-title\">\r\n            <div class=\"title-cn\">对账单汇总</div>\r\n            <div class=\"title-en\">[DEBIT NOTE]</div>\r\n          </div>\r\n        </div>\r\n      `\r\n\r\n      printWindow.document.write(`\r\n        <html lang=\"\">\r\n          <head>\r\n            <title>${title}</title>\r\n            <style>\r\n          /* 基础样式 */\r\n          body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: Arial, sans-serif;\r\n          }\r\n\r\n          /* 打印样式 - 必须放在这里才能生效 */\r\n          @media print {\r\n            @page {\r\n              size: ${this.isLandscape ? \"landscape\" : \"portrait\"};\r\n              margin: 1.5cm 1cm 1cm 1cm;\r\n            }\r\n\r\n            /* 重要：使用重复表头技术 */\r\n            thead {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 页眉作为表格的一部分，放在thead中 */\r\n            .page-header {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 内容部分 */\r\n            .page-content {\r\n              display: table-row-group;\r\n            }\r\n\r\n            /* 页脚 */\r\n            tfoot {\r\n              display: table-footer-group;\r\n            }\r\n\r\n            /* 避免元素内部分页 */\r\n            .company-header, .header-content {\r\n              page-break-inside: avoid;\r\n            }\r\n\r\n            /* 表格样式 */\r\n            table.main-table {\r\n              width: 100%;\r\n              border-collapse: collapse;\r\n              border: none;\r\n            }\r\n\r\n            /* 确保表头在每页都显示 */\r\n            table.data-table thead {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 避免行内分页 */\r\n            table.data-table tr {\r\n              page-break-inside: avoid;\r\n            }\r\n          }\r\n\r\n          /* 表格样式 */\r\n          table.data-table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 20px;\r\n          }\r\n\r\n          table.data-table th, table.data-table td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n            text-align: left;\r\n            font-size: 12px;\r\n          }\r\n\r\n          table.data-table th {\r\n            background-color: #f2f2f2;\r\n          }\r\n\r\n          /* Element UI 表格样式模拟 */\r\n          .el-table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n          }\r\n\r\n          .el-table th, .el-table td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n            text-align: left;\r\n            font-size: 12px;\r\n          }\r\n\r\n          .el-table th {\r\n            background-color: #f2f2f2;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .el-table__footer {\r\n            background-color: #f8f8f9;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .el-table__footer td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n          }\r\n\r\n          /* 公司标题和标志样式 */\r\n          .company-header {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            border-bottom: 2px solid #000;\r\n            padding-bottom: 10px;\r\n            width: 100%;\r\n          }\r\n\r\n          .company-logo {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .company-logo img {\r\n            height: 50px;\r\n            margin-right: 10px;\r\n          }\r\n\r\n          .company-name {\r\n            display: flex;\r\n            flex-direction: column;\r\n          }\r\n\r\n          .company-name-cn {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #ff0000;\r\n          }\r\n\r\n          .company-name-en {\r\n            font-size: 14px;\r\n          }\r\n\r\n          .document-title {\r\n            text-align: right;\r\n          }\r\n\r\n          .title-cn {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .title-en {\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n          }\r\n\r\n          /* 清除表格边框 */\r\n          table.main-table, table.main-table td {\r\n            border: none;\r\n          }\r\n\r\n          /* 页眉容器 */\r\n          .header-container {\r\n            width: 100%;\r\n            margin-bottom: 20px;\r\n          }\r\n\r\n          /* 日期信息 */\r\n          .date-info {\r\n            text-align: right;\r\n            margin-top: 10px;\r\n            margin-bottom: 20px;\r\n          }\r\n        </style>\r\n          </head>\r\n          <body>\r\n            <!-- 使用表格布局确保页眉在每页重复 -->\r\n            <table class=\"main-table\">\r\n              <thead class=\"page-header\">\r\n                <tr>\r\n                  <td>\r\n                    <div class=\"header-container\">\r\n                      ${headerTemplate}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </thead>\r\n              <tbody class=\"page-content\">\r\n                <tr>\r\n                  <td>\r\n                    <!-- 保留原始表格的类名并添加data-table类 -->\r\n                    ${table.outerHTML.replace('<table', '<table class=\"el-table data-table\"')}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n              <tfoot>\r\n                <tr>\r\n                  <td></td>\r\n                </tr>\r\n              </tfoot>\r\n            </table>\r\n          </body>\r\n        </html>\r\n      `)\r\n\r\n      printWindow.document.close()\r\n\r\n      setTimeout(() => {\r\n        try {\r\n          printWindow.focus();\r\n          printWindow.print();\r\n        } catch (e) {\r\n          console.error(\"打印过程中发生错误:\", e);\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    async exportToPDF() {\r\n      try {\r\n        this.loading = true\r\n        const element = this.$refs.resultTable.$el\r\n        const opt = {\r\n          margin: [0.8, 0.8, 0.8, 0.8], // 上右下左边距（英寸）\r\n          filename: \"汇总数据.pdf\",\r\n          image: {type: \"jpeg\", quality: 0.98},\r\n          html2canvas: {scale: 2},\r\n          jsPDF: {\r\n            unit: \"in\",\r\n            format: \"a3\",\r\n            orientation: this.isLandscape ? \"landscape\" : \"portrait\"\r\n          },\r\n          pagebreak: {mode: [\"avoid-all\", \"css\", \"legacy\"]}, // 添加分页控制\r\n          header: [\r\n            {text: \"汇总数据\", style: \"headerStyle\"},\r\n            {text: new Date().toLocaleDateString(), style: \"headerStyle\", alignment: \"right\"}\r\n          ],\r\n          footer: {\r\n            height: \"20px\",\r\n            contents: {\r\n              default: \"<span style=\\\"float:right\\\">{{page}}/{{pages}}</span>\" // 添加页码\r\n            }\r\n          }\r\n        }\r\n\r\n        await html2pdf().set(opt).from(element).save()\r\n        this.$message.success(\"PDF导出成功\")\r\n      } catch (error) {\r\n        this.$message.error(\"PDF导出失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 添加新的字段配置行\r\n     * 初始化一个新的字段配置对象，包含默认值\r\n     */\r\n    addField() {\r\n      this.config.fields.push({\r\n        fieldKey: \"\",        // 字段标识\r\n        aggregation: \"none\", // 汇总方式\r\n        format: \"none\",      // 显示格式\r\n        sort: \"none\"         // 排序方式\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 删除指定索引的字段配置行\r\n     * @param {number} index - 要删除的字段索引\r\n     */\r\n    removeField(index) {\r\n      this.config.fields.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 移动字段配置行的位置\r\n     * @param {number} index - 当前字段的索引\r\n     * @param {string} direction - 移动方向，'up' 或 'down'\r\n     */\r\n    moveField(index, direction) {\r\n      const fields = [...this.config.fields] // 创建数组副本\r\n\r\n      if (direction === \"up\" && index > 0) {\r\n        // 向上移动，与上一个元素交换位置\r\n        [fields[index], fields[index - 1]] = [fields[index - 1], fields[index]]\r\n      } else if (direction === \"down\" && index < fields.length - 1) {\r\n        // 向下移动，与下一个元素交换位置\r\n        [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]\r\n      }\r\n\r\n      // 使用整个新数组替换，确保响应式更新\r\n      this.$set(this.config, \"fields\", fields)\r\n    },\r\n\r\n    /**\r\n     * 处理字段选择变更事件\r\n     * 根据选择的字段自动设置相关配置\r\n     * @param {number} index - 变更的字段索引\r\n     */\r\n    handleFieldSelect(index) {\r\n      const field = this.config.fields[index]\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (fieldConfig) {\r\n        // 根据字段配置设置默认值\r\n        field.format = this.getDefaultFormat(fieldConfig.display)\r\n        field.aggregation = fieldConfig.aggregated ? \"sum\" : \"none\"\r\n        field.sort = \"none\" // 默认不排序\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取字段的显示类型\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {string} 字段显示类型（text/number/date/boolean/custom）\r\n     */\r\n    getFieldDisplay(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      if (!fieldConfig) return \"text\"\r\n\r\n      // 检查是否是自定义方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        return \"custom\"\r\n      }\r\n\r\n      return fieldConfig.display || \"text\"\r\n    },\r\n\r\n    /**\r\n     * 判断字段是否可以进行汇总计算\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {boolean} 是否可汇总\r\n     */\r\n    isAggregatable(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig?.aggregated || false\r\n    },\r\n\r\n    /**\r\n     * 根据显示类型获取默认的格式化方式\r\n     * @param {string} displayType - 显示类型\r\n     * @returns {string} 默认格式\r\n     */\r\n    getDefaultFormat(displayType) {\r\n      switch (displayType) {\r\n        case \"date\":\r\n          return \"YYYY-MM-DD\"\r\n        case \"number\":\r\n          return \"decimal\"\r\n        default:\r\n          return \"none\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 重置配置到初始状态\r\n     */\r\n    resetConfig() {\r\n      this.config = {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: []\r\n      }\r\n      this.showResult = false\r\n    },\r\n\r\n    /**\r\n     * 获取结果数据的属性名\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 属性名\r\n     */\r\n    getResultProp(field) {\r\n      // 如果有汇总方式，属性名为 fieldKey_aggregation\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        return `${field.fieldKey}_${field.aggregation}`\r\n      }\r\n      return field.fieldKey\r\n    },\r\n\r\n    /**\r\n     * 获取结果表格的列标题\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 列标题\r\n     */\r\n    getResultLabel(field) {\r\n      const baseLabel = this.getFieldLabel(field.fieldKey)\r\n\r\n      // 如果有汇总方式，在标签中添加汇总方式信息\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        // 获取汇总方式的中文名称\r\n        const aggregationLabel = this.aggregationOptions.find(opt => opt.value === field.aggregation)?.label || field.aggregation\r\n        return `${baseLabel}(${aggregationLabel})`\r\n      }\r\n\r\n      return baseLabel\r\n    },\r\n\r\n    /**\r\n     * 格式化单元格的值\r\n     * @param {*} value - 原始值\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 格式化后的值\r\n     */\r\n    formatCellValue(value, field) {\r\n      if (value == null) return \"-\"\r\n\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (!fieldConfig) return value\r\n\r\n      // 处理自定义 display 方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        // 调用组件中定义的方法\r\n        return this[fieldConfig.display](value)\r\n      }\r\n\r\n      // 根据字段类型进行格式化\r\n      switch (fieldConfig.display) {\r\n        case \"number\":\r\n          const numValue = Number(value)\r\n          if (isNaN(numValue)) return \"-\"\r\n\r\n          // 如果是汇总字段，先应用汇总格式化\r\n          if (field.aggregation && field.aggregation !== \"none\") {\r\n            // 对于平均值和方差，保留更多小数位\r\n            if (field.aggregation === \"avg\" || field.aggregation === \"variance\") {\r\n              if (field.format === \"percent\") {\r\n                return (numValue * 100).toFixed(2) + \"%\"\r\n              }\r\n              return numValue.toFixed(2)\r\n            }\r\n          }\r\n\r\n          switch (field.format) {\r\n            case \"decimal\":\r\n              return numValue.toFixed(2)\r\n            case \"percent\":\r\n              return (numValue * 100).toFixed(2) + \"%\"\r\n            case \"currency\":\r\n              return \"¥\" + numValue.toFixed(2)\r\n            case \"usd\":\r\n              return \"$\" + numValue.toFixed(2)\r\n            default:\r\n              return numValue.toFixed(2)\r\n          }\r\n\r\n        case \"date\":\r\n          return moment(value).format(field.format || \"YYYY-MM-DD\")\r\n\r\n        case \"boolean\":\r\n          if (field.aggregation === \"avg\") {\r\n            return (Number(value) * 100).toFixed(2) + \"%\"\r\n          }\r\n          return value ? \"是\" : \"否\"\r\n\r\n        default:\r\n          return value\r\n      }\r\n    },\r\n\r\n    getDateFormat() {\r\n      switch (this.config.dateOptions.formatType) {\r\n        case \"year\":\r\n          return \"YYYY\"\r\n        case \"month\":\r\n          return \"YYYY-MM\"\r\n        case \"day\":\r\n        default:\r\n          return \"YYYY-MM-DD\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 格式化分组键\r\n     * @param {Object|string} groupKey - 分组键\r\n     * @returns {string} 格式化后的分组键\r\n     */\r\n    formatGroupKey(groupKey) {\r\n      if (typeof groupKey === \"object\" && groupKey !== null) {\r\n        if (groupKey.primary !== undefined && groupKey.date !== undefined) {\r\n          // 获取主分组字段的配置\r\n          const primaryFieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n          let primaryValue = groupKey.primary\r\n\r\n          // 如果主分组字段有自定义 display 方法，应用它\r\n          if (primaryFieldConfig && primaryFieldConfig.display &&\r\n            typeof this[primaryFieldConfig.display] === \"function\") {\r\n            primaryValue = this[primaryFieldConfig.display](primaryValue)\r\n          }\r\n\r\n          // 日期值在前，主值在后，不添加分隔符\r\n          return `${groupKey.date}${primaryValue}`\r\n        }\r\n      }\r\n\r\n      // 如果是简单值，检查是否需要应用自定义 display 方法\r\n      if (this.config.primaryField) {\r\n        const fieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n        if (fieldConfig && fieldConfig.display &&\r\n          typeof this[fieldConfig.display] === \"function\") {\r\n          return this[fieldConfig.display](groupKey)\r\n        }\r\n      }\r\n\r\n      return String(groupKey || \"\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.data-aggregator {\r\n  padding: 20px;\r\n}\r\n\r\n.config-card, .result-card {\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n.result-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-with-operations {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.operations {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.field-config-table {\r\n  border: 1px solid #EBEEF5;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.table-header,\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px;\r\n  border-bottom: 1px solid #EBEEF5;\r\n}\r\n\r\n.table-header {\r\n  background-color: #F5F7FA;\r\n  font-weight: bold;\r\n}\r\n\r\n.col {\r\n  flex: 1;\r\n  padding: 0 5px;\r\n  min-width: 120px;\r\n}\r\n\r\n.col:first-child {\r\n  flex: 0 0 60px;\r\n  min-width: 60px;\r\n}\r\n\r\n.col-operation {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n"]}]}