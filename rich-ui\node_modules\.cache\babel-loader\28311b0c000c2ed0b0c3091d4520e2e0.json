{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\clientsinfo.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\clientsinfo.js", "mtime": 1718100178721}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listClientsinfo", "query", "request", "url", "method", "params", "getClientsinfo", "clientsInfoId", "addClientsinfo", "data", "updateClientsinfo", "delClientsinfo", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/clientsinfo.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询用来记录客户常用的信息，避免重复劳动、错漏列表\r\nexport function listClientsinfo(query) {\r\n  return request({\r\n    url: '/system/clientsinfo/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询用来记录客户常用的信息，避免重复劳动、错漏详细\r\nexport function getClientsinfo(clientsInfoId) {\r\n  return request({\r\n    url: '/system/clientsinfo/' + clientsInfoId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增用来记录客户常用的信息，避免重复劳动、错漏\r\nexport function addClientsinfo(data) {\r\n  return request({\r\n    url: '/system/clientsinfo',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改用来记录客户常用的信息，避免重复劳动、错漏\r\nexport function updateClientsinfo(data) {\r\n  return request({\r\n    url: '/system/clientsinfo',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除用来记录客户常用的信息，避免重复劳动、错漏\r\nexport function delClientsinfo(clientsInfoId) {\r\n  return request({\r\n    url: '/system/clientsinfo/' + clientsInfoId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(clientsInfoId, status) {\r\n  const data = {\r\n    clientsInfoId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/clientsinfo/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,aAAa;IAC3CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACJ,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,aAAa;IAC3CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,aAAa,EAAEM,MAAM,EAAE;EAClD,IAAMJ,IAAI,GAAG;IACXF,aAAa,EAAbA,aAAa;IACbM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}