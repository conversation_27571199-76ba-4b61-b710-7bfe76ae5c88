{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\RightPanel.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\RightPanel.vue", "mtime": 1754876882603}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_util", "require", "_vuedraggable", "_interopRequireDefault", "_TreeNodeDialog", "_index", "_IconsDialog", "_config", "dateTimeFormat", "date", "week", "month", "year", "datetime", "daterange", "monthrange", "datetimerange", "_default", "components", "draggable", "TreeNodeDialog", "IconsDialog", "props", "data", "currentTab", "currentNode", "dialogVisible", "iconsVisible", "currentIconModel", "dateTypeOptions", "label", "value", "dateRangeTypeOptions", "colorFormatOptions", "justifyOptions", "layoutTreeProps", "node", "componentName", "concat", "vModel", "computed", "documentLink", "activeData", "document", "dateOptions", "type", "undefined", "tag", "tagList", "options", "inputComponents", "selectComponents", "methods", "addReg", "regList", "push", "pattern", "message", "addSelectItem", "addTreeItem", "idGlobal", "renderContent", "h", "_ref", "_this", "store", "click", "append", "remove", "children", "$set", "parent", "index", "findIndex", "d", "id", "splice", "addNode", "setOptionValue", "item", "val", "isNumberStr", "setDefaultValue", "Array", "isArray", "join", "indexOf", "onDefaultValueInput", "str", "defaultValue", "split", "map", "JSON", "parse", "onSwitchValueInput", "name", "setTimeValue", "valueFormat", "spanChange", "formConf", "span", "multipleChange", "dateTypeChange", "rangeChange", "min", "max", "rateTextChange", "rateScoreChange", "colorFormatChange", "<PERSON><PERSON><PERSON>", "Date", "openIconsDialog", "model", "setIcon", "tagChange", "tagIcon", "target", "find", "$emit", "exports", "default"], "sources": ["src/views/tool/build/RightPanel.vue"], "sourcesContent": ["<template>\r\n  <div class=\"right-board\">\r\n    <el-tabs v-model=\"currentTab\" class=\"center-tabs\">\r\n      <el-tab-pane label=\"组件属性\" name=\"field\"/>\r\n      <el-tab-pane label=\"表单属性\" name=\"form\"/>\r\n    </el-tabs>\r\n    <div class=\"field-box\">\r\n      <a :href=\"documentLink\" class=\"document-link\" target=\"_blank\" title=\"查看组件文档\">\r\n        <i class=\"el-icon-link\"/>\r\n      </a>\r\n      <el-scrollbar class=\"right-scrollbar\">\r\n        <!-- 组件属性 -->\r\n        <el-form v-show=\"currentTab=='field' && showField\" label-width=\"90px\" size=\"mini\">\r\n          <el-form-item v-if=\"activeData.changeTag\" label=\"组件类型\">\r\n            <el-select\r\n              v-model=\"activeData.tagIcon\"\r\n              :style=\"{width: '100%'}\"\r\n              placeholder=\"组件类型\"\r\n              @change=\"tagChange\"\r\n            >\r\n              <el-option-group v-for=\"group in tagList\" :key=\"group.label\" :label=\"group.label\">\r\n                <el-option\r\n                  v-for=\"item in group.options\"\r\n                  :key=\"item.label\"\r\n                  :label=\"item.label\"\r\n                  :value=\"item.tagIcon\"\r\n                >\r\n                  <svg-icon :icon-class=\"item.tagIcon\" class=\"node-icon\"/>\r\n                  <span> {{ item.label }}</span>\r\n                </el-option>\r\n              </el-option-group>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.vModel!=undefined\" label=\"字段名\">\r\n            <el-input v-model=\"activeData.vModel\" placeholder=\"字段名（v-model）\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.componentName!=undefined\" label=\"组件名\">\r\n            {{ activeData.componentName }}\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.label!=undefined\" label=\"标题\">\r\n            <el-input v-model=\"activeData.label\" placeholder=\"标题\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.placeholder!=undefined\" label=\"占位提示\">\r\n            <el-input v-model=\"activeData.placeholder\" placeholder=\"占位提示\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['start-placeholder']!=undefined\" label=\"开始占位\">\r\n            <el-input v-model=\"activeData['start-placeholder']\" placeholder=\"占位提示\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['end-placeholder']!=undefined\" label=\"结束占位\">\r\n            <el-input v-model=\"activeData['end-placeholder']\" placeholder=\"占位提示\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.span!=undefined\" label=\"表单栅格\">\r\n            <el-slider v-model=\"activeData.span\" :marks=\"{12:''}\" :max=\"24\" :min=\"1\" @change=\"spanChange\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.layout=='rowFormItem'\" label=\"栅格间隔\">\r\n            <el-input-number v-model=\"activeData.gutter\" :min=\"0\" placeholder=\"栅格间隔\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.layout=='rowFormItem'\" label=\"布局模式\">\r\n            <el-radio-group v-model=\"activeData.type\">\r\n              <el-radio-button label=\"default\"/>\r\n              <el-radio-button label=\"flex\"/>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.justify!=undefined&&activeData.type=='flex'\" label=\"水平排列\">\r\n            <el-select v-model=\"activeData.justify\" :style=\"{width: '100%'}\" clearable filterable\r\n                       placeholder=\"水平排列\">\r\n              <el-option\r\n                v-for=\"(item, index) in justifyOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.align!=undefined&&activeData.type=='flex'\" label=\"垂直排列\">\r\n            <el-radio-group v-model=\"activeData.align\">\r\n              <el-radio-button label=\"top\"/>\r\n              <el-radio-button label=\"middle\"/>\r\n              <el-radio-button label=\"bottom\"/>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.labelWidth!=undefined\" label=\"标签宽度\">\r\n            <el-input v-model.number=\"activeData.labelWidth\" placeholder=\"标签宽度\" type=\"number\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.style&&activeData.style.width!=undefined\" label=\"组件宽度\">\r\n            <el-input v-model=\"activeData.style.width\" clearable placeholder=\"组件宽度\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.vModel!=undefined\" label=\"默认值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData.defaultValue)\"\r\n              placeholder=\"默认值\"\r\n              @input=\"onDefaultValueInput\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag=='el-checkbox-group'\" label=\"至少应选\">\r\n            <el-input-number\r\n              :min=\"0\"\r\n              :value=\"activeData.min\"\r\n              placeholder=\"至少应选\"\r\n              @input=\"$set(activeData, 'min', $event?$event:undefined)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag=='el-checkbox-group'\" label=\"最多可选\">\r\n            <el-input-number\r\n              :min=\"0\"\r\n              :value=\"activeData.max\"\r\n              placeholder=\"最多可选\"\r\n              @input=\"$set(activeData, 'max', $event?$event:undefined)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.prepend!=undefined\" label=\"前缀\">\r\n            <el-input v-model=\"activeData.prepend\" placeholder=\"前缀\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.append!=undefined\" label=\"后缀\">\r\n            <el-input v-model=\"activeData.append\" placeholder=\"后缀\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['prefix-icon']!=undefined\" label=\"前图标\">\r\n            <el-input v-model=\"activeData['prefix-icon']\" placeholder=\"前图标名称\">\r\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('prefix-icon')\">\r\n                选择\r\n              </el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['suffix-icon'] != undefined\" label=\"后图标\">\r\n            <el-input v-model=\"activeData['suffix-icon']\" placeholder=\"后图标名称\">\r\n              <el-button slot=\"append\" icon=\"el-icon-thumb\" @click=\"openIconsDialog('suffix-icon')\">\r\n                选择\r\n              </el-button>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-cascader'\" label=\"选项分隔符\">\r\n            <el-input v-model=\"activeData.separator\" placeholder=\"选项分隔符\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.autosize != undefined\" label=\"最小行数\">\r\n            <el-input-number v-model=\"activeData.autosize.minRows\" :min=\"1\" placeholder=\"最小行数\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.autosize != undefined\" label=\"最大行数\">\r\n            <el-input-number v-model=\"activeData.autosize.maxRows\" :min=\"1\" placeholder=\"最大行数\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.min != undefined\" label=\"最小值\">\r\n            <el-input-number v-model=\"activeData.min\" placeholder=\"最小值\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.max != undefined\" label=\"最大值\">\r\n            <el-input-number v-model=\"activeData.max\" placeholder=\"最大值\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.step != undefined\" label=\"步长\">\r\n            <el-input-number v-model=\"activeData.step\" placeholder=\"步数\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-input-number'\" label=\"精度\">\r\n            <el-input-number v-model=\"activeData.precision\" :min=\"0\" placeholder=\"精度\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-input-number'\" label=\"按钮位置\">\r\n            <el-radio-group v-model=\"activeData['controls-position']\">\r\n              <el-radio-button label=\"\">\r\n                默认\r\n              </el-radio-button>\r\n              <el-radio-button label=\"right\">\r\n                右侧\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.maxlength != undefined\" label=\"最多输入\">\r\n            <el-input v-model=\"activeData.maxlength\" placeholder=\"字符长度\">\r\n              <template slot=\"append\">\r\n                个字符\r\n              </template>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-text'] != undefined\" label=\"开启提示\">\r\n            <el-input v-model=\"activeData['active-text']\" placeholder=\"开启提示\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-text'] != undefined\" label=\"关闭提示\">\r\n            <el-input v-model=\"activeData['inactive-text']\" placeholder=\"关闭提示\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-value'] != undefined\" label=\"开启值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData['active-value'])\"\r\n              placeholder=\"开启值\"\r\n              @input=\"onSwitchValueInput($event, 'active-value')\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-value'] != undefined\" label=\"关闭值\">\r\n            <el-input\r\n              :value=\"setDefaultValue(activeData['inactive-value'])\"\r\n              placeholder=\"关闭值\"\r\n              @input=\"onSwitchValueInput($event, 'inactive-value')\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.type != undefined && 'el-date-picker' == activeData.tag\"\r\n            label=\"时间类型\"\r\n          >\r\n            <el-select\r\n              v-model=\"activeData.type\"\r\n              :style=\"{ width: '100%' }\"\r\n              placeholder=\"时间类型\"\r\n              @change=\"dateTypeChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in dateOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.name != undefined\" label=\"文件字段名\">\r\n            <el-input v-model=\"activeData.name\" placeholder=\"上传文件字段名\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.accept != undefined\" label=\"文件类型\">\r\n            <el-select\r\n              v-model=\"activeData.accept\"\r\n              :style=\"{ width: '100%' }\"\r\n              clearable\r\n              placeholder=\"文件类型\"\r\n            >\r\n              <el-option label=\"图片\" value=\"image/*\"/>\r\n              <el-option label=\"视频\" value=\"video/*\"/>\r\n              <el-option label=\"音频\" value=\"audio/*\"/>\r\n              <el-option label=\"excel\" value=\".xls,.xlsx\"/>\r\n              <el-option label=\"word\" value=\".doc,.docx\"/>\r\n              <el-option label=\"pdf\" value=\".pdf\"/>\r\n              <el-option label=\"txt\" value=\".txt\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.fileSize != undefined\" label=\"文件大小\">\r\n            <el-input v-model.number=\"activeData.fileSize\" placeholder=\"文件大小\">\r\n              <el-select slot=\"append\" v-model=\"activeData.sizeUnit\" :style=\"{ width: '66px' }\">\r\n                <el-option label=\"KB\" value=\"KB\"/>\r\n                <el-option label=\"MB\" value=\"MB\"/>\r\n                <el-option label=\"GB\" value=\"GB\"/>\r\n              </el-select>\r\n            </el-input>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.action != undefined\" label=\"上传地址\">\r\n            <el-input v-model=\"activeData.action\" clearable placeholder=\"上传地址\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['list-type'] != undefined\" label=\"列表类型\">\r\n            <el-radio-group v-model=\"activeData['list-type']\" size=\"mini\">\r\n              <el-radio-button label=\"text\">\r\n                text\r\n              </el-radio-button>\r\n              <el-radio-button label=\"picture\">\r\n                picture\r\n              </el-radio-button>\r\n              <el-radio-button label=\"picture-card\">\r\n                picture-card\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.buttonText != undefined\"\r\n            v-show=\"'picture-card' != activeData['list-type']\"\r\n            label=\"按钮文字\"\r\n          >\r\n            <el-input v-model=\"activeData.buttonText\" placeholder=\"按钮文字\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['range-separator'] != undefined\" label=\"分隔符\">\r\n            <el-input v-model=\"activeData['range-separator']\" placeholder=\"分隔符\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['picker-options'] != undefined\" label=\"时间段\">\r\n            <el-input\r\n              v-model=\"activeData['picker-options'].selectableRange\"\r\n              placeholder=\"时间段\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.format != undefined\" label=\"时间格式\">\r\n            <el-input\r\n              :value=\"activeData.format\"\r\n              placeholder=\"时间格式\"\r\n              @input=\"setTimeValue($event)\"\r\n            />\r\n          </el-form-item>\r\n          <template v-if=\"['el-checkbox-group', 'el-radio-group', 'el-select'].indexOf(activeData.tag) > -1\">\r\n            <el-divider>选项</el-divider>\r\n            <draggable\r\n              :animation=\"340\"\r\n              :list=\"activeData.options\"\r\n              group=\"selectItem\"\r\n              handle=\".option-drag\"\r\n            >\r\n              <div v-for=\"(item, index) in activeData.options\" :key=\"index\" class=\"select-item\">\r\n                <div class=\"select-line-icon option-drag\">\r\n                  <i class=\"el-icon-s-operation\"/>\r\n                </div>\r\n                <el-input v-model=\"item.label\" placeholder=\"选项名\" size=\"mini\"/>\r\n                <el-input\r\n                  :value=\"item.value\"\r\n                  placeholder=\"选项值\"\r\n                  size=\"mini\"\r\n                  @input=\"setOptionValue(item, $event)\"\r\n                />\r\n                <div class=\"close-btn select-line-icon\" @click=\"activeData.options.splice(index, 1)\">\r\n                  <i class=\"el-icon-remove-outline\"/>\r\n                </div>\r\n              </div>\r\n            </draggable>\r\n            <div style=\"margin-left: 20px;\">\r\n              <el-button\r\n                icon=\"el-icon-circle-plus-outline\"\r\n                style=\"padding-bottom: 0\"\r\n                type=\"text\"\r\n                @click=\"addSelectItem\"\r\n              >\r\n                添加选项\r\n              </el-button>\r\n            </div>\r\n            <el-divider/>\r\n          </template>\r\n\r\n          <template v-if=\"['el-cascader'].indexOf(activeData.tag) > -1\">\r\n            <el-divider>选项</el-divider>\r\n            <el-form-item label=\"数据类型\">\r\n              <el-radio-group v-model=\"activeData.dataType\" size=\"mini\">\r\n                <el-radio-button label=\"dynamic\">\r\n                  动态数据\r\n                </el-radio-button>\r\n                <el-radio-button label=\"static\">\r\n                  静态数据\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n\r\n            <template v-if=\"activeData.dataType == 'dynamic'\">\r\n              <el-form-item label=\"标签键名\">\r\n                <el-input v-model=\"activeData.labelKey\" placeholder=\"标签键名\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"值键名\">\r\n                <el-input v-model=\"activeData.valueKey\" placeholder=\"值键名\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"子级键名\">\r\n                <el-input v-model=\"activeData.childrenKey\" placeholder=\"子级键名\"/>\r\n              </el-form-item>\r\n            </template>\r\n\r\n            <el-tree\r\n              v-if=\"activeData.dataType == 'static'\"\r\n              :data=\"activeData.options\"\r\n              :expand-on-click-node=\"false\"\r\n              :render-content=\"renderContent\"\r\n              draggable\r\n              node-key=\"id\"\r\n            />\r\n            <div v-if=\"activeData.dataType == 'static'\" style=\"margin-left: 20px\">\r\n              <el-button\r\n                icon=\"el-icon-circle-plus-outline\"\r\n                style=\"padding-bottom: 0\"\r\n                type=\"text\"\r\n                @click=\"addTreeItem\"\r\n              >\r\n                添加父级\r\n              </el-button>\r\n            </div>\r\n            <el-divider/>\r\n          </template>\r\n\r\n          <el-form-item v-if=\"activeData.optionType != undefined\" label=\"选项样式\">\r\n            <el-radio-group v-model=\"activeData.optionType\">\r\n              <el-radio-button label=\"default\">\r\n                默认\r\n              </el-radio-button>\r\n              <el-radio-button label=\"button\">\r\n                按钮\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['active-color'] != undefined\" label=\"开启颜色\">\r\n            <el-color-picker v-model=\"activeData['active-color']\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['inactive-color'] != undefined\" label=\"关闭颜色\">\r\n            <el-color-picker v-model=\"activeData['inactive-color']\"/>\r\n          </el-form-item>\r\n\r\n          <el-form-item v-if=\"activeData['allow-half'] != undefined\" label=\"允许半选\">\r\n            <el-switch v-model=\"activeData['allow-half']\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-text'] != undefined\" label=\"辅助文字\">\r\n            <el-switch v-model=\"activeData['show-text']\" @change=\"rateTextChange\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-score'] != undefined\" label=\"显示分数\">\r\n            <el-switch v-model=\"activeData['show-score']\" @change=\"rateScoreChange\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-stops'] != undefined\" label=\"显示间断点\">\r\n            <el-switch v-model=\"activeData['show-stops']\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.range != undefined\" label=\"范围选择\">\r\n            <el-switch v-model=\"activeData.range\" @change=\"rangeChange\"/>\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.border != undefined && activeData.optionType == 'default'\"\r\n            label=\"是否带边框\"\r\n          >\r\n            <el-switch v-model=\"activeData.border\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-color-picker'\" label=\"颜色格式\">\r\n            <el-select\r\n              v-model=\"activeData['color-format']\"\r\n              :style=\"{ width: '100%' }\"\r\n              placeholder=\"颜色格式\"\r\n              @change=\"colorFormatChange\"\r\n            >\r\n              <el-option\r\n                v-for=\"(item, index) in colorFormatOptions\"\r\n                :key=\"index\"\r\n                :label=\"item.label\"\r\n                :value=\"item.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item\r\n            v-if=\"activeData.size != undefined &&\r\n              (activeData.optionType == 'button' ||\r\n                activeData.border ||\r\n                activeData.tag == 'el-color-picker')\"\r\n            label=\"选项尺寸\"\r\n          >\r\n            <el-radio-group v-model=\"activeData.size\">\r\n              <el-radio-button label=\"medium\">\r\n                中等\r\n              </el-radio-button>\r\n              <el-radio-button label=\"small\">\r\n                较小\r\n              </el-radio-button>\r\n              <el-radio-button label=\"mini\">\r\n                迷你\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['show-word-limit'] != undefined\" label=\"输入统计\">\r\n            <el-switch v-model=\"activeData['show-word-limit']\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-input-number'\" label=\"严格步数\">\r\n            <el-switch v-model=\"activeData['step-strictly']\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-cascader'\" label=\"是否多选\">\r\n            <el-switch v-model=\"activeData.props.props.multiple\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-cascader'\" label=\"展示全路径\">\r\n            <el-switch v-model=\"activeData['show-all-levels']\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-cascader'\" label=\"可否筛选\">\r\n            <el-switch v-model=\"activeData.filterable\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.clearable != undefined\" label=\"能否清空\">\r\n            <el-switch v-model=\"activeData.clearable\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.showTip != undefined\" label=\"显示提示\">\r\n            <el-switch v-model=\"activeData.showTip\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.multiple != undefined\" label=\"多选文件\">\r\n            <el-switch v-model=\"activeData.multiple\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData['auto-upload'] != undefined\" label=\"自动上传\">\r\n            <el-switch v-model=\"activeData['auto-upload']\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.readonly != undefined\" label=\"是否只读\">\r\n            <el-switch v-model=\"activeData.readonly\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.disabled != undefined\" label=\"是否禁用\">\r\n            <el-switch v-model=\"activeData.disabled\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-select'\" label=\"是否可搜索\">\r\n            <el-switch v-model=\"activeData.filterable\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.tag == 'el-select'\" label=\"是否多选\">\r\n            <el-switch v-model=\"activeData.multiple\" @change=\"multipleChange\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"activeData.required != undefined\" label=\"是否必填\">\r\n            <el-switch v-model=\"activeData.required\"/>\r\n          </el-form-item>\r\n\r\n          <template v-if=\"activeData.layoutTree\">\r\n            <el-divider>布局结构树</el-divider>\r\n            <el-tree\r\n              :data=\"[activeData]\"\r\n              :props=\"layoutTreeProps\"\r\n              default-expand-all\r\n              draggable\r\n              node-key=\"renderKey\"\r\n            >\r\n              <span slot-scope=\"{ node, data }\">\r\n                <span class=\"node-label\">\r\n                  <svg-icon :icon-class=\"data.tagIcon\" class=\"node-icon\"/>\r\n                  {{ node.label }}\r\n                </span>\r\n              </span>\r\n            </el-tree>\r\n          </template>\r\n\r\n          <template v-if=\"activeData.layout == 'colFormItem' && activeData.tag != 'el-button'\">\r\n            <el-divider>正则校验</el-divider>\r\n            <div\r\n              v-for=\"(item, index) in activeData.regList\"\r\n              :key=\"index\"\r\n              class=\"reg-item\"\r\n            >\r\n              <span class=\"close-btn\" @click=\"activeData.regList.splice(index, 1)\">\r\n                <i class=\"el-icon-close\"/>\r\n              </span>\r\n              <el-form-item label=\"表达式\">\r\n                <el-input v-model=\"item.pattern\" placeholder=\"正则\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"错误提示\" style=\"margin-bottom:0\">\r\n                <el-input v-model=\"item.message\" placeholder=\"错误提示\"/>\r\n              </el-form-item>\r\n            </div>\r\n            <div style=\"margin-left: 20px\">\r\n              <el-button icon=\"el-icon-circle-plus-outline\" type=\"text\" @click=\"addReg\">\r\n                添加规则\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-form>\r\n        <!-- 表单属性 -->\r\n        <el-form v-show=\"currentTab == 'form'\" label-width=\"90px\" size=\"mini\">\r\n          <el-form-item label=\"表单名\">\r\n            <el-input v-model=\"formConf.formRef\" placeholder=\"表单名（ref）\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"表单模型\">\r\n            <el-input v-model=\"formConf.formModel\" placeholder=\"数据模型\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"校验模型\">\r\n            <el-input v-model=\"formConf.formRules\" placeholder=\"校验模型\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"表单尺寸\">\r\n            <el-radio-group v-model=\"formConf.size\">\r\n              <el-radio-button label=\"medium\">\r\n                中等\r\n              </el-radio-button>\r\n              <el-radio-button label=\"small\">\r\n                较小\r\n              </el-radio-button>\r\n              <el-radio-button label=\"mini\">\r\n                迷你\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"标签对齐\">\r\n            <el-radio-group v-model=\"formConf.labelPosition\">\r\n              <el-radio-button label=\"left\">\r\n                左对齐\r\n              </el-radio-button>\r\n              <el-radio-button label=\"right\">\r\n                右对齐\r\n              </el-radio-button>\r\n              <el-radio-button label=\"top\">\r\n                顶部对齐\r\n              </el-radio-button>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n          <el-form-item label=\"标签宽度\">\r\n            <el-input-number v-model=\"formConf.labelWidth\" placeholder=\"标签宽度\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"栅格间隔\">\r\n            <el-input-number v-model=\"formConf.gutter\" :min=\"0\" placeholder=\"栅格间隔\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"禁用表单\">\r\n            <el-switch v-model=\"formConf.disabled\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"表单按钮\">\r\n            <el-switch v-model=\"formConf.formBtns\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"显示未选中组件边框\">\r\n            <el-switch v-model=\"formConf.unFocusedComponentBorder\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <treeNode-dialog :visible.sync=\"dialogVisible\" title=\"添加选项\" @commit=\"addNode\"/>\r\n    <icons-dialog :current=\"activeData[currentIconModel]\" :visible.sync=\"iconsVisible\" @select=\"setIcon\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {isArray} from 'util'\r\nimport draggable from 'vuedraggable'\r\nimport TreeNodeDialog from './TreeNodeDialog'\r\nimport {isNumberStr} from '@/utils/index'\r\nimport IconsDialog from './IconsDialog'\r\nimport {inputComponents, selectComponents} from '@/utils/generator/config'\r\n\r\nconst dateTimeFormat = {\r\n  date: 'yyyy-MM-dd',\r\n  week: 'yyyy 第 WW 周',\r\n  month: 'yyyy-MM',\r\n  year: 'yyyy',\r\n  datetime: 'yyyy-MM-dd HH:mm:ss',\r\n  daterange: 'yyyy-MM-dd',\r\n  monthrange: 'yyyy-MM',\r\n  datetimerange: 'yyyy-MM-dd HH:mm:ss'\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    draggable,\r\n    TreeNodeDialog,\r\n    IconsDialog\r\n  },\r\n  props: ['showField', 'activeData', 'formConf'],\r\n  data() {\r\n    return {\r\n      currentTab: 'field',\r\n      currentNode: null,\r\n      dialogVisible: false,\r\n      iconsVisible: false,\r\n      currentIconModel: null,\r\n      dateTypeOptions: [\r\n        {\r\n          label: '日(date)',\r\n          value: 'date'\r\n        },\r\n        {\r\n          label: '周(week)',\r\n          value: 'week'\r\n        },\r\n        {\r\n          label: '月(month)',\r\n          value: 'month'\r\n        },\r\n        {\r\n          label: '年(year)',\r\n          value: 'year'\r\n        },\r\n        {\r\n          label: '日期时间(datetime)',\r\n          value: 'datetime'\r\n        }\r\n      ],\r\n      dateRangeTypeOptions: [\r\n        {\r\n          label: '日期范围(daterange)',\r\n          value: 'daterange'\r\n        },\r\n        {\r\n          label: '月范围(monthrange)',\r\n          value: 'monthrange'\r\n        },\r\n        {\r\n          label: '日期时间范围(datetimerange)',\r\n          value: 'datetimerange'\r\n        }\r\n      ],\r\n      colorFormatOptions: [\r\n        {\r\n          label: 'hex',\r\n          value: 'hex'\r\n        },\r\n        {\r\n          label: 'rgb',\r\n          value: 'rgb'\r\n        },\r\n        {\r\n          label: 'rgba',\r\n          value: 'rgba'\r\n        },\r\n        {\r\n          label: 'hsv',\r\n          value: 'hsv'\r\n        },\r\n        {\r\n          label: 'hsl',\r\n          value: 'hsl'\r\n        }\r\n      ],\r\n      justifyOptions: [\r\n        {\r\n          label: 'start',\r\n          value: 'start'\r\n        },\r\n        {\r\n          label: 'end',\r\n          value: 'end'\r\n        },\r\n        {\r\n          label: 'center',\r\n          value: 'center'\r\n        },\r\n        {\r\n          label: 'space-around',\r\n          value: 'space-around'\r\n        },\r\n        {\r\n          label: 'space-between',\r\n          value: 'space-between'\r\n        }\r\n      ],\r\n      layoutTreeProps: {\r\n        label(data, node) {\r\n          return data.componentName || `${data.label}: ${data.vModel}`\r\n        }\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    documentLink() {\r\n      return (\r\n        this.activeData.document\r\n        || 'https://element.eleme.cn/#/zh-CN/component/installation'\r\n      )\r\n    },\r\n    dateOptions() {\r\n      if (\r\n        this.activeData.type != undefined\r\n        && this.activeData.tag == 'el-date-picker'\r\n      ) {\r\n        if (this.activeData['start-placeholder'] == undefined) {\r\n          return this.dateTypeOptions\r\n        }\r\n        return this.dateRangeTypeOptions\r\n      }\r\n      return []\r\n    },\r\n    tagList() {\r\n      return [\r\n        {\r\n          label: '输入型组件',\r\n          options: inputComponents\r\n        },\r\n        {\r\n          label: '选择型组件',\r\n          options: selectComponents\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    addReg() {\r\n      this.activeData.regList.push({\r\n        pattern: '',\r\n        message: ''\r\n      })\r\n    },\r\n    addSelectItem() {\r\n      this.activeData.options.push({\r\n        label: '',\r\n        value: ''\r\n      })\r\n    },\r\n    addTreeItem() {\r\n      ++this.idGlobal\r\n      this.dialogVisible = true\r\n      this.currentNode = this.activeData.options\r\n    },\r\n    renderContent(h, {node, data, store}) {\r\n      return (\r\n        <div class=\"custom-tree-node\">\r\n          <span>{node.label}</span>\r\n          <span class=\"node-operation\">\r\n            <i on-click={() => this.append(data)}\r\n               class=\"el-icon-plus\"\r\n               title=\"添加\"\r\n            ></i>\r\n            <i on-click={() => this.remove(node, data)}\r\n               class=\"el-icon-delete\"\r\n               title=\"删除\"\r\n            ></i>\r\n          </span>\r\n        </div>\r\n      )\r\n    },\r\n    append(data) {\r\n      if (!data.children) {\r\n        this.$set(data, 'children', [])\r\n      }\r\n      this.dialogVisible = true\r\n      this.currentNode = data.children\r\n    },\r\n    remove(node, data) {\r\n      const {parent} = node\r\n      const children = parent.data.children || parent.data\r\n      const index = children.findIndex(d => d.id == data.id)\r\n      children.splice(index, 1)\r\n    },\r\n    addNode(data) {\r\n      this.currentNode.push(data)\r\n    },\r\n    setOptionValue(item, val) {\r\n      item.value = isNumberStr(val) ? +val : val\r\n    },\r\n    setDefaultValue(val) {\r\n      if (Array.isArray(val)) {\r\n        return val.join(',')\r\n      }\r\n      if (['string', 'number'].indexOf(val) > -1) {\r\n        return val\r\n      }\r\n      if (typeof val == 'boolean') {\r\n        return `${val}`\r\n      }\r\n      return val\r\n    },\r\n    onDefaultValueInput(str) {\r\n      if (isArray(this.activeData.defaultValue)) {\r\n        // 数组\r\n        this.$set(\r\n          this.activeData,\r\n          'defaultValue',\r\n          str.split(',').map(val => (isNumberStr(val) ? +val : val))\r\n        )\r\n      } else if (['true', 'false'].indexOf(str) > -1) {\r\n        // 布尔\r\n        this.$set(this.activeData, 'defaultValue', JSON.parse(str))\r\n      } else {\r\n        // 字符串和数字\r\n        this.$set(\r\n          this.activeData,\r\n          'defaultValue',\r\n          isNumberStr(str) ? +str : str\r\n        )\r\n      }\r\n    },\r\n    onSwitchValueInput(val, name) {\r\n      if (['true', 'false'].indexOf(val) > -1) {\r\n        this.$set(this.activeData, name, JSON.parse(val))\r\n      } else {\r\n        this.$set(this.activeData, name, isNumberStr(val) ? +val : val)\r\n      }\r\n    },\r\n    setTimeValue(val, type) {\r\n      const valueFormat = type == 'week' ? dateTimeFormat.date : val\r\n      this.$set(this.activeData, 'defaultValue', null)\r\n      this.$set(this.activeData, 'value-format', valueFormat)\r\n      this.$set(this.activeData, 'format', val)\r\n    },\r\n    spanChange(val) {\r\n      this.formConf.span = val\r\n    },\r\n    multipleChange(val) {\r\n      this.$set(this.activeData, 'defaultValue', val ? [] : '')\r\n    },\r\n    dateTypeChange(val) {\r\n      this.setTimeValue(dateTimeFormat[val], val)\r\n    },\r\n    rangeChange(val) {\r\n      this.$set(\r\n        this.activeData,\r\n        'defaultValue',\r\n        val ? [this.activeData.min, this.activeData.max] : this.activeData.min\r\n      )\r\n    },\r\n    rateTextChange(val) {\r\n      if (val) this.activeData['show-score'] = false\r\n    },\r\n    rateScoreChange(val) {\r\n      if (val) this.activeData['show-text'] = false\r\n    },\r\n    colorFormatChange(val) {\r\n      this.activeData.defaultValue = null\r\n      this.activeData['show-alpha'] = val.indexOf('a') > -1\r\n      this.activeData.renderKey = +new Date() // 更新renderKey,重新渲染该组件\r\n    },\r\n    openIconsDialog(model) {\r\n      this.iconsVisible = true\r\n      this.currentIconModel = model\r\n    },\r\n    setIcon(val) {\r\n      this.activeData[this.currentIconModel] = val\r\n    },\r\n    tagChange(tagIcon) {\r\n      let target = inputComponents.find(item => item.tagIcon == tagIcon)\r\n      if (!target) target = selectComponents.find(item => item.tagIcon == tagIcon)\r\n      this.$emit('tag-change', target)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.right-board {\r\n  width: 350px;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  padding-top: 3px;\r\n\r\n  .field-box {\r\n    position: relative;\r\n    height: calc(100vh - 42px);\r\n    box-sizing: border-box;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .el-scrollbar {\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n.select-item {\r\n  display: flex;\r\n  border: 1px dashed #fff;\r\n  box-sizing: border-box;\r\n\r\n  & .close-btn {\r\n    cursor: pointer;\r\n    color: #f56c6c;\r\n  }\r\n\r\n  & .el-input + .el-input {\r\n    margin-left: 4px;\r\n  }\r\n}\r\n\r\n.select-item + .select-item {\r\n  margin-top: 4px;\r\n}\r\n\r\n.select-item.sortable-chosen {\r\n  border: 1px dashed #409eff;\r\n}\r\n\r\n.select-line-icon {\r\n  line-height: 32px;\r\n  font-size: 22px;\r\n  padding: 0 4px;\r\n  color: #777;\r\n}\r\n\r\n.option-drag {\r\n  cursor: move;\r\n}\r\n\r\n.time-range {\r\n  .el-date-editor {\r\n    width: 227px;\r\n  }\r\n\r\n  ::v-deep .el-icon-time {\r\n    display: none;\r\n  }\r\n}\r\n\r\n.document-link {\r\n  position: absolute;\r\n  display: block;\r\n  width: 26px;\r\n  height: 26px;\r\n  top: 0;\r\n  left: 0;\r\n  cursor: pointer;\r\n  background: #409eff;\r\n  z-index: 1;\r\n  border-radius: 0 0 6px 0;\r\n  text-align: center;\r\n  line-height: 26px;\r\n  color: #fff;\r\n  font-size: 18px;\r\n}\r\n\r\n.node-label {\r\n  font-size: 14px;\r\n}\r\n\r\n.node-icon {\r\n  color: #bebfc3;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA+jBA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,eAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AACA,IAAAK,YAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,OAAA,GAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAO,cAAA;EACAC,IAAA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA;EACAC,QAAA;EACAC,SAAA;EACAC,UAAA;EACAC,aAAA;AACA;AAAA,IAAAC,QAAA,GAEA;EACAC,UAAA;IACAC,SAAA,EAAAA,qBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,WAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,WAAA;MACAC,aAAA;MACAC,YAAA;MACAC,gBAAA;MACAC,eAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAC,oBAAA,GACA;QACAF,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAE,kBAAA,GACA;QACAH,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAG,cAAA,GACA;QACAJ,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MACAI,eAAA;QACAL,KAAA,WAAAA,MAAAP,IAAA,EAAAa,IAAA;UACA,OAAAb,IAAA,CAAAc,aAAA,OAAAC,MAAA,CAAAf,IAAA,CAAAO,KAAA,QAAAQ,MAAA,CAAAf,IAAA,CAAAgB,MAAA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,OACA,KAAAC,UAAA,CAAAC,QAAA,IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,IACA,KAAAF,UAAA,CAAAG,IAAA,IAAAC,SAAA,IACA,KAAAJ,UAAA,CAAAK,GAAA,sBACA;QACA,SAAAL,UAAA,yBAAAI,SAAA;UACA,YAAAjB,eAAA;QACA;QACA,YAAAG,oBAAA;MACA;MACA;IACA;IACAgB,OAAA,WAAAA,QAAA;MACA,QACA;QACAlB,KAAA;QACAmB,OAAA,EAAAC;MACA,GACA;QACApB,KAAA;QACAmB,OAAA,EAAAE;MACA,EACA;IACA;EACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAX,UAAA,CAAAY,OAAA,CAAAC,IAAA;QACAC,OAAA;QACAC,OAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAAhB,UAAA,CAAAO,OAAA,CAAAM,IAAA;QACAzB,KAAA;QACAC,KAAA;MACA;IACA;IACA4B,WAAA,WAAAA,YAAA;MACA,OAAAC,QAAA;MACA,KAAAlC,aAAA;MACA,KAAAD,WAAA,QAAAiB,UAAA,CAAAO,OAAA;IACA;IACAY,aAAA,WAAAA,cAAAC,CAAA,EAAAC,IAAA;MAAA,IAAAC,KAAA;MAAA,IAAA5B,IAAA,GAAA2B,IAAA,CAAA3B,IAAA;QAAAb,IAAA,GAAAwC,IAAA,CAAAxC,IAAA;QAAA0C,KAAA,GAAAF,IAAA,CAAAE,KAAA;MACA,OAAAH,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA,UACA1B,IAAA,CAAAN,KAAA,IAAAgC,CAAA;QAAA,SACA;MAAA,IAAAA,CAAA;QAAA;UAAA,SACA,SAAAI,MAAA;YAAA,OAAAF,KAAA,CAAAG,MAAA,CAAA5C,IAAA;UAAA;QAAA;QAAA,SACA;QAAA;UAAA,SACA;QAAA;MAAA,IAAAuC,CAAA;QAAA;UAAA,SAEA,SAAAI,MAAA;YAAA,OAAAF,KAAA,CAAAI,MAAA,CAAAhC,IAAA,EAAAb,IAAA;UAAA;QAAA;QAAA,SACA;QAAA;UAAA,SACA;QAAA;MAAA;IAKA;IACA4C,MAAA,WAAAA,OAAA5C,IAAA;MACA,KAAAA,IAAA,CAAA8C,QAAA;QACA,KAAAC,IAAA,CAAA/C,IAAA;MACA;MACA,KAAAG,aAAA;MACA,KAAAD,WAAA,GAAAF,IAAA,CAAA8C,QAAA;IACA;IACAD,MAAA,WAAAA,OAAAhC,IAAA,EAAAb,IAAA;MACA,IAAAgD,MAAA,GAAAnC,IAAA,CAAAmC,MAAA;MACA,IAAAF,QAAA,GAAAE,MAAA,CAAAhD,IAAA,CAAA8C,QAAA,IAAAE,MAAA,CAAAhD,IAAA;MACA,IAAAiD,KAAA,GAAAH,QAAA,CAAAI,SAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,EAAA,IAAApD,IAAA,CAAAoD,EAAA;MAAA;MACAN,QAAA,CAAAO,MAAA,CAAAJ,KAAA;IACA;IACAK,OAAA,WAAAA,QAAAtD,IAAA;MACA,KAAAE,WAAA,CAAA8B,IAAA,CAAAhC,IAAA;IACA;IACAuD,cAAA,WAAAA,eAAAC,IAAA,EAAAC,GAAA;MACAD,IAAA,CAAAhD,KAAA,OAAAkD,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;IACA;IACAE,eAAA,WAAAA,gBAAAF,GAAA;MACA,IAAAG,KAAA,CAAAC,OAAA,CAAAJ,GAAA;QACA,OAAAA,GAAA,CAAAK,IAAA;MACA;MACA,yBAAAC,OAAA,CAAAN,GAAA;QACA,OAAAA,GAAA;MACA;MACA,WAAAA,GAAA;QACA,UAAA1C,MAAA,CAAA0C,GAAA;MACA;MACA,OAAAA,GAAA;IACA;IACAO,mBAAA,WAAAA,oBAAAC,GAAA;MACA,QAAAJ,aAAA,OAAA1C,UAAA,CAAA+C,YAAA;QACA;QACA,KAAAnB,IAAA,CACA,KAAA5B,UAAA,EACA,gBACA8C,GAAA,CAAAE,KAAA,MAAAC,GAAA,WAAAX,GAAA;UAAA,WAAAC,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;QAAA,EACA;MACA,6BAAAM,OAAA,CAAAE,GAAA;QACA;QACA,KAAAlB,IAAA,MAAA5B,UAAA,kBAAAkD,IAAA,CAAAC,KAAA,CAAAL,GAAA;MACA;QACA;QACA,KAAAlB,IAAA,CACA,KAAA5B,UAAA,EACA,gBACA,IAAAuC,kBAAA,EAAAO,GAAA,KAAAA,GAAA,GAAAA,GACA;MACA;IACA;IACAM,kBAAA,WAAAA,mBAAAd,GAAA,EAAAe,IAAA;MACA,sBAAAT,OAAA,CAAAN,GAAA;QACA,KAAAV,IAAA,MAAA5B,UAAA,EAAAqD,IAAA,EAAAH,IAAA,CAAAC,KAAA,CAAAb,GAAA;MACA;QACA,KAAAV,IAAA,MAAA5B,UAAA,EAAAqD,IAAA,MAAAd,kBAAA,EAAAD,GAAA,KAAAA,GAAA,GAAAA,GAAA;MACA;IACA;IACAgB,YAAA,WAAAA,aAAAhB,GAAA,EAAAnC,IAAA;MACA,IAAAoD,WAAA,GAAApD,IAAA,aAAArC,cAAA,CAAAC,IAAA,GAAAuE,GAAA;MACA,KAAAV,IAAA,MAAA5B,UAAA;MACA,KAAA4B,IAAA,MAAA5B,UAAA,kBAAAuD,WAAA;MACA,KAAA3B,IAAA,MAAA5B,UAAA,YAAAsC,GAAA;IACA;IACAkB,UAAA,WAAAA,WAAAlB,GAAA;MACA,KAAAmB,QAAA,CAAAC,IAAA,GAAApB,GAAA;IACA;IACAqB,cAAA,WAAAA,eAAArB,GAAA;MACA,KAAAV,IAAA,MAAA5B,UAAA,kBAAAsC,GAAA;IACA;IACAsB,cAAA,WAAAA,eAAAtB,GAAA;MACA,KAAAgB,YAAA,CAAAxF,cAAA,CAAAwE,GAAA,GAAAA,GAAA;IACA;IACAuB,WAAA,WAAAA,YAAAvB,GAAA;MACA,KAAAV,IAAA,CACA,KAAA5B,UAAA,EACA,gBACAsC,GAAA,SAAAtC,UAAA,CAAA8D,GAAA,OAAA9D,UAAA,CAAA+D,GAAA,SAAA/D,UAAA,CAAA8D,GACA;IACA;IACAE,cAAA,WAAAA,eAAA1B,GAAA;MACA,IAAAA,GAAA,OAAAtC,UAAA;IACA;IACAiE,eAAA,WAAAA,gBAAA3B,GAAA;MACA,IAAAA,GAAA,OAAAtC,UAAA;IACA;IACAkE,iBAAA,WAAAA,kBAAA5B,GAAA;MACA,KAAAtC,UAAA,CAAA+C,YAAA;MACA,KAAA/C,UAAA,iBAAAsC,GAAA,CAAAM,OAAA;MACA,KAAA5C,UAAA,CAAAmE,SAAA,QAAAC,IAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA;MACA,KAAArF,YAAA;MACA,KAAAC,gBAAA,GAAAoF,KAAA;IACA;IACAC,OAAA,WAAAA,QAAAjC,GAAA;MACA,KAAAtC,UAAA,MAAAd,gBAAA,IAAAoD,GAAA;IACA;IACAkC,SAAA,WAAAA,UAAAC,OAAA;MACA,IAAAC,MAAA,GAAAlE,uBAAA,CAAAmE,IAAA,WAAAtC,IAAA;QAAA,OAAAA,IAAA,CAAAoC,OAAA,IAAAA,OAAA;MAAA;MACA,KAAAC,MAAA,EAAAA,MAAA,GAAAjE,wBAAA,CAAAkE,IAAA,WAAAtC,IAAA;QAAA,OAAAA,IAAA,CAAAoC,OAAA,IAAAA,OAAA;MAAA;MACA,KAAAG,KAAA,eAAAF,MAAA;IACA;EACA;AACA;AAAAG,OAAA,CAAAC,OAAA,GAAAvG,QAAA"}]}