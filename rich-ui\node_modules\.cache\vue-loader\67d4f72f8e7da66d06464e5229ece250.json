{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\hour.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\hour.vue", "mtime": 1754876882525}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHJhZGlvVmFsdWU6IDEsDQogICAgICBjeWNsZTAxOiAwLA0KICAgICAgY3ljbGUwMjogMSwNCiAgICAgIGF2ZXJhZ2UwMTogMCwNCiAgICAgIGF2ZXJhZ2UwMjogMSwNCiAgICAgIGNoZWNrYm94TGlzdDogW10sDQogICAgICBjaGVja051bTogdGhpcy4kb3B0aW9ucy5wcm9wc0RhdGEuY2hlY2sNCiAgICB9DQogIH0sDQogIG5hbWU6ICdjcm9udGFiLWhvdXInLA0KICBwcm9wczogWydjaGVjaycsICdjcm9uJ10sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDljZXpgInmjInpkq7lgLzlj5jljJbml7YNCiAgICByYWRpb0NoYW5nZSgpIHsNCiAgICAgIHN3aXRjaCAodGhpcy5yYWRpb1ZhbHVlKSB7DQogICAgICAgIGNhc2UgMToNCiAgICAgICAgICB0aGlzLiRlbWl0KCd1cGRhdGUnLCAnaG91cicsICcqJykNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAyOg0KICAgICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICdob3VyJywgdGhpcy5jeWNsZVRvdGFsKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAzOg0KICAgICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICdob3VyJywgdGhpcy5hdmVyYWdlVG90YWwpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlIDQ6DQogICAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlJywgJ2hvdXInLCB0aGlzLmNoZWNrYm94U3RyaW5nKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWRqOacn+S4pOS4quWAvOWPmOWMluaXtg0KICAgIGN5Y2xlQ2hhbmdlKCkgew0KICAgICAgaWYgKHRoaXMucmFkaW9WYWx1ZSA9PSAnMicpIHsNCiAgICAgICAgdGhpcy4kZW1pdCgndXBkYXRlJywgJ2hvdXInLCB0aGlzLmN5Y2xlVG90YWwpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5bmz5Z2H5Lik5Liq5YC85Y+Y5YyW5pe2DQogICAgYXZlcmFnZUNoYW5nZSgpIHsNCiAgICAgIGlmICh0aGlzLnJhZGlvVmFsdWUgPT0gJzMnKSB7DQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICdob3VyJywgdGhpcy5hdmVyYWdlVG90YWwpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8gY2hlY2tib3jlgLzlj5jljJbml7YNCiAgICBjaGVja2JveENoYW5nZSgpIHsNCiAgICAgIGlmICh0aGlzLnJhZGlvVmFsdWUgPT0gJzQnKSB7DQogICAgICAgIHRoaXMuJGVtaXQoJ3VwZGF0ZScsICdob3VyJywgdGhpcy5jaGVja2JveFN0cmluZyk7DQogICAgICB9DQogICAgfQ0KICB9LA0KICB3YXRjaDogew0KICAgICdyYWRpb1ZhbHVlJzogJ3JhZGlvQ2hhbmdlJywNCiAgICAnY3ljbGVUb3RhbCc6ICdjeWNsZUNoYW5nZScsDQogICAgJ2F2ZXJhZ2VUb3RhbCc6ICdhdmVyYWdlQ2hhbmdlJywNCiAgICAnY2hlY2tib3hTdHJpbmcnOiAnY2hlY2tib3hDaGFuZ2UnDQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g6K6h566X5Lik5Liq5ZGo5pyf5YC8DQogICAgY3ljbGVUb3RhbDogZnVuY3Rpb24gKCkgew0KICAgICAgY29uc3QgY3ljbGUwMSA9IHRoaXMuY2hlY2tOdW0odGhpcy5jeWNsZTAxLCAwLCAyMikNCiAgICAgIGNvbnN0IGN5Y2xlMDIgPSB0aGlzLmNoZWNrTnVtKHRoaXMuY3ljbGUwMiwgY3ljbGUwMSA/IGN5Y2xlMDEgKyAxIDogMSwgMjMpDQogICAgICByZXR1cm4gY3ljbGUwMSArICctJyArIGN5Y2xlMDI7DQogICAgfSwNCiAgICAvLyDorqHnrpflubPlnYfnlKjliLDnmoTlgLwNCiAgICBhdmVyYWdlVG90YWw6IGZ1bmN0aW9uICgpIHsNCiAgICAgIGNvbnN0IGF2ZXJhZ2UwMSA9IHRoaXMuY2hlY2tOdW0odGhpcy5hdmVyYWdlMDEsIDAsIDIyKQ0KICAgICAgY29uc3QgYXZlcmFnZTAyID0gdGhpcy5jaGVja051bSh0aGlzLmF2ZXJhZ2UwMiwgMSwgMjMgLSBhdmVyYWdlMDEgfHwgMCkNCiAgICAgIHJldHVybiBhdmVyYWdlMDEgKyAnLycgKyBhdmVyYWdlMDI7DQogICAgfSwNCiAgICAvLyDorqHnrpfli77pgInnmoRjaGVja2JveOWAvOWQiOmbhg0KICAgIGNoZWNrYm94U3RyaW5nOiBmdW5jdGlvbiAoKSB7DQogICAgICBsZXQgc3RyID0gdGhpcy5jaGVja2JveExpc3Quam9pbigpOw0KICAgICAgcmV0dXJuIHN0ciA9PSAnJyA/ICcqJyA6IHN0cjsNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["hour.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "hour.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n  <el-form size=\"mini\">\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"1\">\r\n        小时，允许的通配符[, - * /]\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"2\">\r\n        周期从\r\n        <el-input-number v-model='cycle01' :max=\"22\" :min=\"0\"/>\r\n        -\r\n        <el-input-number v-model='cycle02' :max=\"23\" :min=\"cycle01 ? cycle01 + 1 : 1\"/>\r\n        小时\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"3\">\r\n        从\r\n        <el-input-number v-model='average01' :max=\"22\" :min=\"0\"/>\r\n        小时开始，每\r\n        <el-input-number v-model='average02' :max=\"23 - average01 || 0\" :min=\"1\"/>\r\n        小时执行一次\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"4\">\r\n        指定\r\n        <el-select v-model=\"checkboxList\" clearable multiple placeholder=\"可多选\" style=\"width:100%\">\r\n          <el-option v-for=\"item in 24\" :key=\"item\" :value=\"item-1\">{{ item - 1 }}</el-option>\r\n        </el-select>\r\n      </el-radio>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      radioValue: 1,\r\n      cycle01: 0,\r\n      cycle02: 1,\r\n      average01: 0,\r\n      average02: 1,\r\n      checkboxList: [],\r\n      checkNum: this.$options.propsData.check\r\n    }\r\n  },\r\n  name: 'crontab-hour',\r\n  props: ['check', 'cron'],\r\n  methods: {\r\n    // 单选按钮值变化时\r\n    radioChange() {\r\n      switch (this.radioValue) {\r\n        case 1:\r\n          this.$emit('update', 'hour', '*')\r\n          break;\r\n        case 2:\r\n          this.$emit('update', 'hour', this.cycleTotal);\r\n          break;\r\n        case 3:\r\n          this.$emit('update', 'hour', this.averageTotal);\r\n          break;\r\n        case 4:\r\n          this.$emit('update', 'hour', this.checkboxString);\r\n          break;\r\n      }\r\n    },\r\n    // 周期两个值变化时\r\n    cycleChange() {\r\n      if (this.radioValue == '2') {\r\n        this.$emit('update', 'hour', this.cycleTotal);\r\n      }\r\n    },\r\n    // 平均两个值变化时\r\n    averageChange() {\r\n      if (this.radioValue == '3') {\r\n        this.$emit('update', 'hour', this.averageTotal);\r\n      }\r\n    },\r\n    // checkbox值变化时\r\n    checkboxChange() {\r\n      if (this.radioValue == '4') {\r\n        this.$emit('update', 'hour', this.checkboxString);\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    'radioValue': 'radioChange',\r\n    'cycleTotal': 'cycleChange',\r\n    'averageTotal': 'averageChange',\r\n    'checkboxString': 'checkboxChange'\r\n  },\r\n  computed: {\r\n    // 计算两个周期值\r\n    cycleTotal: function () {\r\n      const cycle01 = this.checkNum(this.cycle01, 0, 22)\r\n      const cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 23)\r\n      return cycle01 + '-' + cycle02;\r\n    },\r\n    // 计算平均用到的值\r\n    averageTotal: function () {\r\n      const average01 = this.checkNum(this.average01, 0, 22)\r\n      const average02 = this.checkNum(this.average02, 1, 23 - average01 || 0)\r\n      return average01 + '/' + average02;\r\n    },\r\n    // 计算勾选的checkbox值合集\r\n    checkboxString: function () {\r\n      let str = this.checkboxList.join();\r\n      return str == '' ? '*' : str;\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}