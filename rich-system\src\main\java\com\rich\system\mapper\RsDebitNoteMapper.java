package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsDebitNote;
import org.apache.ibatis.annotations.Mapper;

/**
 * 分账单Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Mapper
public interface RsDebitNoteMapper {
    /**
     * 查询分账单
     *
     * @param debitNoteId 分账单主键
     * @return 分账单
     */
    RsDebitNote selectRsDebitNoteByDebitNoteId(Long debitNoteId);

    /**
     * 查询分账单列表
     *
     * @param rsDebitNote 分账单
     * @return 分账单集合
     */
    List<RsDebitNote> selectRsDebitNoteList(RsDebitNote rsDebitNote);

    /**
     * 新增分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    int insertRsDebitNote(RsDebitNote rsDebitNote);

    /**
     * 修改分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    int updateRsDebitNote(RsDebitNote rsDebitNote);

    /**
     * 删除分账单
     *
     * @param debitNoteId 分账单主键
     * @return 结果
     */
    int deleteRsDebitNoteByDebitNoteId(Long debitNoteId);

    /**
     * 批量删除分账单
     *
     * @param debitNoteIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsDebitNoteByDebitNoteIds(Long[] debitNoteIds);

    List<RsDebitNote> selectRsDebitNoteByRctId(Long rctId);

    void deleteRsDebitNoteByRctId(Long rctId);

    int upsertRsDebitNote(RsDebitNote rsDebitNote);
}
