{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue?vue&type=template&id=7eaaafac&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue", "mtime": 1754876882572}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}