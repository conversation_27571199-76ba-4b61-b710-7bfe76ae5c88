{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opHistory.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opHistory.vue", "mtime": 1754876882584}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge3BhcnNlVGltZX0gZnJvbSAiQC91dGlscy9yaWNoIjsNCmltcG9ydCB7YWRkT3BlcmF0aW9uYWxwcm9jZXNzLCBkZWxPcGVyYXRpb25hbHByb2Nlc3N9IGZyb20gIkAvYXBpL3N5c3RlbS9vcGVyYXRpb25hbHByb2Nlc3MiOw0KaW1wb3J0IHthZGREb2NkZXRhaWwsIGRlbERvY2RldGFpbCwgZ2V0RG9jZGV0YWlsLCB1cGRhdGVEb2NkZXRhaWx9IGZyb20gIkAvYXBpL3N5c3RlbS9kb2NkZXRhaWwiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJvcEhpc3RvcnkiLA0KICBwcm9wczogWydvcEhpc3RvcnknLCAnb3Blbk9wSGlzdG9yeScsICd0eXBlSWQnLCAncmN0SWQnLCAnYmFzaWNJbmZvSWQnXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICBkb2M6IHt9LA0KICAgICAgZExpc3Q6IHt9LA0KICAgICAgcmVjZWl2ZTogZmFsc2UsDQogICAgICBzZW5kOiBmYWxzZQ0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBvcEhpc3Rvcnkobikgew0KICAgICAgdGhpcy4kZW1pdCgncmV0dXJuJywgbikNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVBZGREb2NMaXN0KHJvdykgew0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZQ0KICAgICAgdGhpcy5kb2MgPSB7DQogICAgICAgIG9wZXJhdGlvbmFsUHJvY2Vzc0lkOiByb3cub3BlcmF0aW9uYWxQcm9jZXNzSWQsDQogICAgICAgIGRvY0lkOiBudWxsLA0KICAgICAgICBmbG93Tm86IG51bGwsDQogICAgICAgIGRvY0Zsb3dEaXJlY3Rpb25JZDogbnVsbCwNCiAgICAgICAgaXNzdWVUeXBlSWQ6IG51bGwsDQogICAgICAgIGZpbGVMaXN0OiBudWxsLA0KICAgICAgICBjcmVhdGVCeTogdGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQsDQogICAgICAgIGNyZWF0ZUJ5TmFtZTogdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lLnNwbGl0KCIgIilbMV0sDQogICAgICAgIGNyZWF0ZVRpbWU6IHBhcnNlVGltZShuZXcgRGF0ZSgpKSwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBwYXJzZVRpbWUobmV3IERhdGUoKSksDQogICAgICB9DQogICAgICB0aGlzLmRMaXN0ID0gcm93DQogICAgfSwNCiAgICBjaGVja0RvYyhkb2MsIHJvdykgew0KICAgICAgZ2V0RG9jZGV0YWlsKGRvYy5kb2NEZXRhaWxJZCkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLmRvYyA9IHJlcy5kYXRhDQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIH0pDQogICAgICB0aGlzLmRMaXN0ID0gcm93DQogICAgfSwNCiAgICBnZXRTZW5kZXIodmFsLCByb3cpIHsNCiAgICAgIHRoaXMuc2VuZCA9IHRydWUNCiAgICAgIHRoaXMucmVjZWl2ZSA9IGZhbHNlDQogICAgICBpZiAoIXRoaXMucmVjZWl2ZSkgew0KICAgICAgICByb3cuc2VuZGVySWQgPSB2YWwNCiAgICAgICAgcm93LnJlY2VpdmVySWQgPSAxDQogICAgICB9DQogICAgfSwNCiAgICBnZXRSZWNlaXZlcih2YWwsIHJvdykgew0KICAgICAgdGhpcy5yZWNlaXZlID0gdHJ1ZQ0KICAgICAgdGhpcy5zZW5kID0gZmFsc2UNCiAgICAgIGlmICghdGhpcy5zZW5kKSB7DQogICAgICAgIHJvdy5yZWNlaXZlcklkID0gdmFsDQogICAgICAgIHJvdy5zZW5kZXJJZCA9IDENCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZVNlbmQoKSB7DQoNCiAgICB9LA0KICAgIGFkZE9wSGlzdG9yeSgpIHsNCiAgICAgIGxldCBvcEhpc3RvcnlGb3JtID0gew0KICAgICAgICBkb2NMaXN0OiBbXSwNCiAgICAgICAgdHlwZUlkOiB0aGlzLnR5cGVJZCwNCiAgICAgICAgcmN0SWQ6IHRoaXMucmN0SWQsDQogICAgICAgIGJhc2ljSW5mb0lkOiB0aGlzLmJhc2ljSW5mb0lkLA0KICAgICAgICBwcm9jZXNzSWQ6IG51bGwsDQogICAgICAgIHByb2Nlc3NTdGF0dXNJZDogbnVsbCwNCiAgICAgICAgc2VuZGVySWQ6IG51bGwsDQogICAgICAgIHJlY2VpdmVySWQ6IG51bGwsDQogICAgICAgIHJlbGVhc2VXYXlJZDogbnVsbCwNCiAgICAgICAgcHJvY2Vzc1N0YXR1c1RpbWU6IHBhcnNlVGltZShuZXcgRGF0ZSgpKSwNCiAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICBvcElkOiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZCwNCiAgICAgICAgb3BOYW1lOiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWUuc3BsaXQoIiAiKVsxXSwNCiAgICAgICAgY3JlYXRlVGltZTogcGFyc2VUaW1lKG5ldyBEYXRlKCkpLA0KICAgICAgfQ0KICAgICAgYWRkT3BlcmF0aW9uYWxwcm9jZXNzKG9wSGlzdG9yeUZvcm0pLnRoZW4ocmVzID0+IHsNCiAgICAgICAgdGhpcy5vcEhpc3RvcnkucHVzaChvcEhpc3RvcnlGb3JtKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGRlbE9wSGlzdG9yeShyb3cpIHsNCiAgICAgIGxldCBvcEhpc3RvcnlGb3JtID0gew0KICAgICAgICBvcGVyYXRpb25hbFByb2Nlc3NJZDogcm93Lm9wZXJhdGlvbmFsUHJvY2Vzc0lkLA0KICAgICAgICB0eXBlSWQ6IHJvdy50eXBlSWQsDQogICAgICAgIHJjdElkOiByb3cucmN0SWQsDQogICAgICAgIGJhc2ljSW5mb0lkOiByb3cuYmFzaWNJbmZvSWQsDQogICAgICB9DQogICAgICBkZWxPcGVyYXRpb25hbHByb2Nlc3Mob3BIaXN0b3J5Rm9ybSkudGhlbihyZXMgPT4gew0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzLm1zZykNCiAgICAgICAgdGhpcy5vcEhpc3RvcnkgPSB0aGlzLm9wSGlzdG9yeS5maWx0ZXIoaXRlbSA9PiB7DQogICAgICAgICAgcmV0dXJuIGl0ZW0ub3BlcmF0aW9uYWxQcm9jZXNzSWQgIT0gcm93Lm9wZXJhdGlvbmFsUHJvY2Vzc0lkDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0sDQogICAgc3VibWl0RG9jKCkgew0KICAgICAgaWYgKHRoaXMuZG9jLmRvY0RldGFpbElkID09IG51bGwpIHsNCiAgICAgICAgYWRkRG9jZGV0YWlsKHRoaXMuZG9jKS50aGVuKHJlcyA9PiB7DQogICAgICAgICAgaWYgKHRoaXMuZExpc3QuZG9jTGlzdCA9PSBudWxsKSB7DQogICAgICAgICAgICB0aGlzLmRMaXN0LmRvY0xpc3QgPSBbXQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLmRMaXN0LmRvY0xpc3QucHVzaCh0aGlzLmRvYykNCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmRvYykNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHVwZGF0ZURvY2RldGFpbCh0aGlzLmRvYykudGhlbihyZXMgPT4gew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgICBjb25zb2xlLmxvZyh0aGlzLmRvYykNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgfSwNCiAgICBkZWxEb2MoKSB7DQogICAgICBpZiAodGhpcy5kb2MuZG9jRGV0YWlsSWQgIT0gbnVsbCkgew0KICAgICAgICBkZWxEb2NkZXRhaWwodGhpcy5kb2MuZG9jRGV0YWlsSWQpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpDQogICAgICAgICAgdGhpcy5kTGlzdC5kb2NMaXN0ID0gdGhpcy5kTGlzdC5kb2NMaXN0LmZpbHRlcih2ID0+IHsNCiAgICAgICAgICAgIHJldHVybiB2LmRvY0RldGFpbElkICE9IHRoaXMuZG9jLmRvY0RldGFpbElkDQogICAgICAgICAgfSkNCiAgICAgICAgICB0aGlzLmRvYyA9IHt9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["opHistory.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwJA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "opHistory.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-col :span=\"16\" :style=\"{'display':openOpHistory?'':'none'}\">\r\n    <div class=\"titleStyle\">\r\n      <div class=\"titleText\">操作历史记录（文件管理）</div>\r\n    </div>\r\n    <div :class=\"{'inactive':openOpHistory==false,'active':openOpHistory}\">\r\n      <el-table :data=\"opHistory\" border class=\"pd0\">\r\n        <el-table-column align=\"center\" label=\"操作进度流水\" prop=\"operationalProcessId\" show-tooltip-when-overflow\r\n                         width=\"80px\"/>\r\n        <el-table-column align=\"center\" label=\"进度名称\" prop=\"processId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.processId\"\r\n                         :placeholder=\"'进度状态'\" :type=\"'process'\"\r\n                         @return=\"scope.row.processId=$event\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"进度状态\" prop=\"processStatusId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.processStatusId\"\r\n                         :placeholder=\"'进度状态'\" :type=\"'processStatus'\"\r\n                         @return=\"scope.row.processStatusId=$event\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"发送方\" prop=\"senderId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-select v-model=\"scope.row.senderId\" :placeholder=\"'发送方'\" filterable\r\n                       @change=\"getSender($event,scope.row)\">\r\n              <el-option v-for=\"c in $store.state.data.companyList\" :key=\"c.companyId\" :label=\"c.companyShortName\"\r\n                         :value=\"c.companyId\"/>\r\n            </el-select>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"接收方\" prop=\"receiverId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-select v-model=\"scope.row.receiverId\" filterable placeholder=\"接收方\"\r\n                       @change=\"getReceiver($event,scope.row)\">\r\n              <el-option v-for=\"c in $store.state.data.companyList\" :key=\"c.companyId\" :label=\"c.companyShortName\"\r\n                         :value=\"c.companyId\"/>\r\n            </el-select>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"随附文件列表\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-for=\"doc in scope.row.docList\"\r\n                       :key=\"doc.docDetailId\"\r\n                       size=\"small\"\r\n                       style=\"padding: 0;\"\r\n                       type=\"text\"\r\n                       @click=\"checkDoc(doc,scope.row)\">\r\n              {{ '[' + doc.flowNo + ']' }}\r\n            </el-button>\r\n            <el-button size=\"mini\" style=\"padding: 0\" type=\"text\"\r\n                       @click=\"handleAddDocList(scope.row)\">\r\n              [＋]\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"交互方式\" prop=\"releaseWayId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"scope.row.releaseWayId\" :placeholder=\"'交互方式'\"\r\n                         :type=\"'docReleaseWay'\" @return=\"scope.row.releaseWayId=$event\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"发生时间\" prop=\"processStatusTime\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-date-picker v-model=\"scope.row.processStatusTime\"\r\n                            clearable\r\n                            placeholder=\"进度发生时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\">\r\n            </el-date-picker>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"备注\" prop=\"remark\">\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.remark\" placeholder=\"备注\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"发送\" width=\"50px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button icon=\"el-icon-s-promotion\" size=\"mini\" style=\"padding: 0\" type=\"primary\"\r\n                       @click=\"handleSend\">Send\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"操作员\" prop=\"opName\" width=\"50px\"></el-table-column>\r\n        <el-table-column align=\"center\" label=\"录入时间\" prop=\"createTime\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.createTime, '{y}/{m}/{d}') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button icon=\"el-icon-delete\" size=\"mini\" type=\"danger\"\r\n                       @click=\"delOpHistory(scope.row)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-button style=\"padding: 0\" type=\"text\"\r\n                 @click=\"addOpHistory\">[＋]\r\n      </el-button>\r\n      <el-dialog v-dialogDrag v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n                 :visible.sync=\"open\" append-to-body title=\"新增操作历史记录\" width=\"500px\">\r\n        <el-form ref=\"docList\" :model=\"doc\" border label-width=\"68px\">\r\n          <el-form-item label=\"流向编号\" prop=\"flowNo\">\r\n            <el-input v-model=\"doc.flowNo\" placeholder=\"文件名/流向编号\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件类型\" prop=\"docId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"doc.docId\" :placeholder=\"'文件类型'\"\r\n                         :type=\"'doc'\" @return=\"doc.docId=$event\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件流向\" prop=\"docFlowDirectionId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"doc.docFlowDirectionId\" :placeholder=\"'文件流向'\"\r\n                         :type=\"'docFlowDirection'\" @return=\"doc.docFlowDirectionId=$event\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件形式\" prop=\"issueTypeId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"doc.issueTypeId\"\r\n                         :placeholder=\"'文件形式'\"\r\n                         :type=\"'docIssueType'\" @return=\"doc.issueTypeId=$event\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\" prop=\"createTime\">\r\n            {{ parseTime(doc.createTime, '{y}/{m}/{d}') }}\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"remark\">\r\n            <el-input v-model=\"doc.remark\" :autosize=\"{ minRows: 1}\" maxlength=\"300\"\r\n                      placeholder=\"备注\" show-word-limit type=\"textarea\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建人\" prop=\"createByName\">\r\n            {{ doc.createByName }}\r\n          </el-form-item>\r\n          <el-form-item label=\"录入时间\" prop=\"updateTime\">\r\n            {{ parseTime(doc.updateTime, '{y}/{m}/{d}') }}\r\n          </el-form-item>\r\n          <file-upload :file-type=\"['xlsx','xls','docx','doc','pdf']\" :limit=\"3\" :value=\"doc.fileList\"\r\n                       @input=\"doc.fileList=$event\"/>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitDoc\">确 定</el-button>\r\n          <el-button type=\"danger\" @click=\"delDoc\">删 除</el-button>\r\n          <el-button @click=\"cancel\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport {parseTime} from \"@/utils/rich\";\r\nimport {addOperationalprocess, delOperationalprocess} from \"@/api/system/operationalprocess\";\r\nimport {addDocdetail, delDocdetail, getDocdetail, updateDocdetail} from \"@/api/system/docdetail\";\r\n\r\nexport default {\r\n  name: \"opHistory\",\r\n  props: ['opHistory', 'openOpHistory', 'typeId', 'rctId', 'basicInfoId'],\r\n  data() {\r\n    return {\r\n      open: false,\r\n      doc: {},\r\n      dList: {},\r\n      receive: false,\r\n      send: false\r\n    }\r\n  },\r\n  watch: {\r\n    opHistory(n) {\r\n      this.$emit('return', n)\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDocList(row) {\r\n      this.open = true\r\n      this.doc = {\r\n        operationalProcessId: row.operationalProcessId,\r\n        docId: null,\r\n        flowNo: null,\r\n        docFlowDirectionId: null,\r\n        issueTypeId: null,\r\n        fileList: null,\r\n        createBy: this.$store.state.user.sid,\r\n        createByName: this.$store.state.user.name.split(\" \")[1],\r\n        createTime: parseTime(new Date()),\r\n        remark: null,\r\n        updateTime: parseTime(new Date()),\r\n      }\r\n      this.dList = row\r\n    },\r\n    checkDoc(doc, row) {\r\n      getDocdetail(doc.docDetailId).then(res => {\r\n        this.doc = res.data\r\n        this.open = true\r\n      })\r\n      this.dList = row\r\n    },\r\n    getSender(val, row) {\r\n      this.send = true\r\n      this.receive = false\r\n      if (!this.receive) {\r\n        row.senderId = val\r\n        row.receiverId = 1\r\n      }\r\n    },\r\n    getReceiver(val, row) {\r\n      this.receive = true\r\n      this.send = false\r\n      if (!this.send) {\r\n        row.receiverId = val\r\n        row.senderId = 1\r\n      }\r\n    },\r\n    handleSend() {\r\n\r\n    },\r\n    addOpHistory() {\r\n      let opHistoryForm = {\r\n        docList: [],\r\n        typeId: this.typeId,\r\n        rctId: this.rctId,\r\n        basicInfoId: this.basicInfoId,\r\n        processId: null,\r\n        processStatusId: null,\r\n        senderId: null,\r\n        receiverId: null,\r\n        releaseWayId: null,\r\n        processStatusTime: parseTime(new Date()),\r\n        remark: null,\r\n        opId: this.$store.state.user.sid,\r\n        opName: this.$store.state.user.name.split(\" \")[1],\r\n        createTime: parseTime(new Date()),\r\n      }\r\n      addOperationalprocess(opHistoryForm).then(res => {\r\n        this.opHistory.push(opHistoryForm)\r\n      })\r\n    },\r\n    delOpHistory(row) {\r\n      let opHistoryForm = {\r\n        operationalProcessId: row.operationalProcessId,\r\n        typeId: row.typeId,\r\n        rctId: row.rctId,\r\n        basicInfoId: row.basicInfoId,\r\n      }\r\n      delOperationalprocess(opHistoryForm).then(res => {\r\n        this.$message.success(res.msg)\r\n        this.opHistory = this.opHistory.filter(item => {\r\n          return item.operationalProcessId != row.operationalProcessId\r\n        })\r\n      })\r\n    },\r\n    submitDoc() {\r\n      if (this.doc.docDetailId == null) {\r\n        addDocdetail(this.doc).then(res => {\r\n          if (this.dList.docList == null) {\r\n            this.dList.docList = []\r\n          }\r\n          this.dList.docList.push(this.doc)\r\n          console.log(this.doc)\r\n        })\r\n      } else {\r\n        updateDocdetail(this.doc).then(res => {\r\n          this.$message.success(\"修改成功\")\r\n          console.log(this.doc)\r\n        })\r\n      }\r\n      this.open = false\r\n    },\r\n    delDoc() {\r\n      if (this.doc.docDetailId != null) {\r\n        delDocdetail(this.doc.docDetailId).then(res => {\r\n          this.$message.success(\"删除成功\")\r\n          this.dList.docList = this.dList.docList.filter(v => {\r\n            return v.docDetailId != this.doc.docDetailId\r\n          })\r\n          this.doc = {}\r\n        })\r\n      }\r\n    },\r\n    cancel() {\r\n      this.open = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}