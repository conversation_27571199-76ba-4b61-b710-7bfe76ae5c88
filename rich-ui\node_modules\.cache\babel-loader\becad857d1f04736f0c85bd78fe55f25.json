{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\docList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\docList.vue", "mtime": 1722505520357}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_doc", "require", "_rich", "_index", "_interopRequireDefault", "name", "components", "CompanySelect", "props", "watch", "logisticsNoInfo", "$emit", "openDocList", "n", "oopen", "showSearch", "showRight", "showLeft", "created", "loading", "data", "openInsert", "ids", "single", "multiple", "total", "title", "open", "queryParams", "pageNum", "pageSize", "serviceId", "sqdServiceTypeId", "sqdRctNo", "docSourceId", "docTypeId", "docTrackingNo", "shipper", "consignee", "notify<PERSON><PERSON><PERSON>", "shippingMark", "goodsDescription", "containersSealsList", "blIssueDate", "blIssueLocation", "isSwitchBl", "isDividedBl", "isAgreementShowed", "isCustomsIntransit", "docIssueType", "docGettingWay", "docDeliveryWay", "form", "rules", "methods", "parseTime", "cancel", "reset", "docId", "resetForm", "handleQuery", "getList", "reset<PERSON><PERSON>y", "handleStatusChange", "row", "_this", "text", "status", "$confirm", "customClass", "then", "changeStatus", "$modal", "msgSuccess", "catch", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "_this2", "getDoc", "response", "submitForm", "_this3", "$refs", "validate", "valid", "updateDoc", "addDoc", "handleDelete", "_this4", "docIds", "delDoc", "_this5", "listDoc", "docList", "rows", "exports", "default", "_default"], "sources": ["src/views/system/document/docList.vue"], "sourcesContent": ["<template>\r\n  <el-dialog v-dialogDrag v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n             :visible.sync=\"oopen\" append-to-body width=\"1500px\">\r\n    <div class=\"app-container\">\r\n      <el-row :gutter=\"20\">\r\n        <el-col :span=\"showLeft\">\r\n          <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" label-width=\"68px\"\r\n                   size=\"mini\">\r\n            <el-form-item label=\"所属服务类型id\" prop=\"sqdServiceTypeId\">\r\n              <el-input\r\n                v-model=\"queryParams.sqdServiceTypeId\"\r\n                clearable\r\n                placeholder=\"所属服务类型id\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"所属操作单号\" prop=\"sqdRctNo\">\r\n              <el-input\r\n                v-model=\"queryParams.sqdRctNo\"\r\n                clearable\r\n                placeholder=\"所属操作单号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"文件来源\" prop=\"docSourceId\">\r\n              <el-input\r\n                v-model=\"queryParams.docSourceId\"\r\n                clearable\r\n                placeholder=\"文件来源公司id\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"文件类型\" prop=\"docTypeId\">\r\n              <el-input\r\n                v-model=\"queryParams.docTypeId\"\r\n                clearable\r\n                placeholder=\"文件类型HBL,MBL,AWBL,SWBL,CO,BSC,COC\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"文件编号\" prop=\"docTrackingNo\">\r\n              <el-input\r\n                v-model=\"queryParams.docTrackingNo\"\r\n                clearable\r\n                placeholder=\"文件编号提单号等\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"发货人\" prop=\"shipper\">\r\n              <el-input\r\n                v-model=\"queryParams.shipper\"\r\n                clearable\r\n                placeholder=\"发货人\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"收货人\" prop=\"consignee\">\r\n              <el-input\r\n                v-model=\"queryParams.consignee\"\r\n                clearable\r\n                placeholder=\"收货人\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"通知人\" prop=\"notifyParty\">\r\n              <el-input\r\n                v-model=\"queryParams.notifyParty\"\r\n                clearable\r\n                placeholder=\"通知人\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"唛头\" prop=\"shippingMark\">\r\n              <el-input\r\n                v-model=\"queryParams.shippingMark\"\r\n                clearable\r\n                placeholder=\"唛头\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"货描\" prop=\"goodsDescription\">\r\n              <el-input\r\n                v-model=\"queryParams.goodsDescription\"\r\n                clearable\r\n                placeholder=\"货描\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"资料\" prop=\"containersSealsList\">\r\n              <el-input\r\n                v-model=\"queryParams.containersSealsList\"\r\n                clearable\r\n                placeholder=\"柜号和封条和分柜资料(list)\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"签单日期\" prop=\"blIssueDate\">\r\n              <el-date-picker v-model=\"queryParams.blIssueDate\"\r\n                              clearable\r\n                              placeholder=\"签单日期\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n            <el-form-item label=\"签单地点\" prop=\"blIssueLocation\">\r\n              <el-input\r\n                v-model=\"queryParams.blIssueLocation\"\r\n                clearable\r\n                placeholder=\"签单地点\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"是否转单\" prop=\"isSwitchBl\">\r\n              <el-input\r\n                v-model=\"queryParams.isSwitchBl\"\r\n                clearable\r\n                placeholder=\"是否转单\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"是否拆单\" prop=\"isDividedBl\">\r\n              <el-input\r\n                v-model=\"queryParams.isDividedBl\"\r\n                clearable\r\n                placeholder=\"是否拆单\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"显示套约\" prop=\"isAgreementShowed\">\r\n              <el-input\r\n                v-model=\"queryParams.isAgreementShowed\"\r\n                clearable\r\n                placeholder=\"显示套约\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"清关中转\" prop=\"isCustomsIntransit\">\r\n              <el-input\r\n                v-model=\"queryParams.isCustomsIntransit\"\r\n                clearable\r\n                placeholder=\"清关中转\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"取单方式\" prop=\"docGettingWay\">\r\n              <el-input\r\n                v-model=\"queryParams.docGettingWay\"\r\n                clearable\r\n                placeholder=\"取单方式\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item label=\"交单方式\" prop=\"docDeliveryWay\">\r\n              <el-input\r\n                v-model=\"queryParams.docDeliveryWay\"\r\n                clearable\r\n                placeholder=\"交单方式\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n              />\r\n            </el-form-item>\r\n            <el-form-item>\r\n              <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-col>\r\n        <el-col :span=\"showRight\">\r\n          <el-row :gutter=\"10\" class=\"mb8\">\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                v-hasPermi=\"['system:doc:add']\"\r\n                icon=\"el-icon-plus\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd\"\r\n              >新增\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                v-hasPermi=\"['system:doc:edit']\"\r\n                :disabled=\"single\"\r\n                icon=\"el-icon-edit\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate\"\r\n              >修改\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button\r\n                v-hasPermi=\"['system:doc:remove']\"\r\n                :disabled=\"multiple\"\r\n                icon=\"el-icon-delete\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete\"\r\n              >删除\r\n              </el-button>\r\n            </el-col>\r\n            <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n          </el-row>\r\n\r\n          <el-table v-loading=\"loading\" :data=\"docList\" @selection-change=\"handleSelectionChange\">\r\n            <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n            <el-table-column align=\"center\" label=\"操作单号\" prop=\"sqdRctNo\"/>\r\n            <el-table-column align=\"center\" label=\"文件来源公司\" prop=\"docSourceId\"/>\r\n            <el-table-column align=\"center\" label=\"文件类型\" prop=\"docTypeId\"/>\r\n            <el-table-column align=\"center\" label=\"文件编号\" prop=\"docTrackingNo\"/>\r\n            <el-table-column align=\"center\" label=\"发货人\" prop=\"shipper\"/>\r\n            <el-table-column align=\"center\" label=\"收货人\" prop=\"consignee\"/>\r\n            <el-table-column align=\"center\" label=\"通知人\" prop=\"notifyParty\"/>\r\n            <el-table-column align=\"center\" label=\"唛头\" prop=\"shippingMark\"/>\r\n            <el-table-column align=\"center\" label=\"货描\" prop=\"goodsDescription\"/>\r\n            <el-table-column align=\"center\" label=\"资料\" prop=\"containersSealsList\"/>\r\n            <el-table-column align=\"center\" label=\"签单日期\" prop=\"blIssueDate\" width=\"180\">\r\n              <template slot-scope=\"scope\">\r\n                <span>{{ parseTime(scope.row.blIssueDate, '{y}-{m}-{d}') }}</span>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"签单地点\" prop=\"blIssueLocation\"/>\r\n            <el-table-column align=\"center\" label=\"是否转单\" prop=\"isSwitchBl\"/>\r\n            <el-table-column align=\"center\" label=\"是否拆单\" prop=\"isDividedBl\"/>\r\n            <el-table-column align=\"center\" label=\"显示套约\" prop=\"isAgreementShowed\"/>\r\n            <el-table-column align=\"center\" label=\"清关中转\" prop=\"isCustomsIntransit\"/>\r\n            <el-table-column align=\"center\" label=\"出单方式\" prop=\"docIssueType\"/>\r\n            <el-table-column align=\"center\" label=\"取单方式\" prop=\"docGettingWay\"/>\r\n            <el-table-column align=\"center\" label=\"交单方式\" prop=\"docDeliveryWay\"/>\r\n            <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  v-hasPermi=\"['system:doc:edit']\"\r\n                  icon=\"el-icon-edit\"\r\n                  size=\"mini\"\r\n                  style=\"margin-right: -8px\"\r\n                  :disabled=\"disabled\"\r\n                  type=\"success\"\r\n                  @click=\"handleUpdate(scope.row)\"\r\n                >修改\r\n                </el-button>\r\n                <el-button\r\n                  v-hasPermi=\"['system:doc:remove']\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  style=\"margin-right: -8px\"\r\n                  :disabled=\"disabled\"\r\n                  type=\"danger\"\r\n                  @click=\"handleDelete(scope.row)\"\r\n                >删除\r\n                </el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n          <pagination\r\n            v-show=\"total>0\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :total=\"total\"\r\n            @pagination=\"getList\"\r\n          />\r\n        </el-col>\r\n      </el-row>\r\n      <!-- 添加或修改文件信息对话框 -->\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"title\" :visible.sync=\"open\" append-to-body\r\n        width=\"500px\">\r\n        <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n          <el-form-item label=\"文件来源\" prop=\"docSourceId\">\r\n            <company-select :load-options=\"form.docSourceId\" :multiple=\"false\"\r\n                            :no-parent=\"true\" :pass=\"form.docSourceId\"\r\n                            :placeholder=\"'委托单位'\" :roleTypeId=\"1\" @return=\"form.docSourceId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"文件类型\" prop=\"docTypeId\">\r\n            <el-input v-model=\"form.docTypeId\" placeholder=\"文件类型HBL,MBL,AWBL,SWBL,CO,BSC,COC\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件编号\" prop=\"docTrackingNo\">\r\n            <el-input v-model=\"form.docTrackingNo\" placeholder=\"文件编号提单号等\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"发货人\" prop=\"shipper\">\r\n            <el-input v-model=\"form.shipper\" placeholder=\"发货人\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"收货人\" prop=\"consignee\">\r\n            <el-input v-model=\"form.consignee\" placeholder=\"收货人\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"通知人\" prop=\"notifyParty\">\r\n            <el-input v-model=\"form.notifyParty\" placeholder=\"通知人\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"唛头\" prop=\"shippingMark\">\r\n            <el-input v-model=\"form.shippingMark\" placeholder=\"唛头\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"货描\" prop=\"goodsDescription\">\r\n            <el-input v-model=\"form.goodsDescription\" placeholder=\"货描\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"资料\" prop=\"containersSealsList\">\r\n            <el-input v-model=\"form.containersSealsList\" placeholder=\"柜号和封条和分柜资料(list)\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"签单日期\" prop=\"blIssueDate\">\r\n            <el-date-picker v-model=\"form.blIssueDate\"\r\n                            clearable\r\n                            placeholder=\"签单日期\"\r\n                            style=\"width: 100%\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"签单地点\" prop=\"blIssueLocation\">\r\n            <el-input v-model=\"form.blIssueLocation\" placeholder=\"签单地点\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"是否转单\" prop=\"isSwitchBl\">\r\n            <el-input v-model=\"form.isSwitchBl\" placeholder=\"是否转单\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"是否拆单\" prop=\"isDividedBl\">\r\n            <el-input v-model=\"form.isDividedBl\" placeholder=\"是否拆单\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"显示套约\" prop=\"isAgreementShowed\">\r\n            <el-input v-model=\"form.isAgreementShowed\" placeholder=\"显示套约\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"清关中转\" prop=\"isCustomsIntransit\">\r\n            <el-input v-model=\"form.isCustomsIntransit\" placeholder=\"清关中转\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"取单方式\" prop=\"docGettingWay\">\r\n            <el-input v-model=\"form.docGettingWay\" placeholder=\"取单方式\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"交单方式\" prop=\"docDeliveryWay\">\r\n            <el-input v-model=\"form.docDeliveryWay\" placeholder=\"交单方式\"/>\r\n          </el-form-item>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n          <el-button @click=\"cancel\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\n\r\nimport {addDoc, getDoc, updateDoc} from '@/api/system/doc'\r\nimport {parseTime} from '@/utils/rich'\r\nimport CompanySelect from '@/components/CompanySelect/index.vue'\r\n\r\nexport default {\r\n  name: 'DocList',\r\n  components: {CompanySelect},\r\n  props: ['openDocList', 'docList','disabled'],\r\n  watch: {\r\n    logisticsNoInfo() {\r\n      this.$emit('return', this.logisticsNoInfo)\r\n    },\r\n    openDocList(n) {\r\n      this.oopen = n\r\n    },\r\n    oopen(n) {\r\n      if (n == false) {\r\n        this.$emit('close')\r\n      }\r\n    },\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.loading = false\r\n  },\r\n  data() {\r\n    return {\r\n      openInsert: false,\r\n      oopen: false,\r\n      logisticsNoInfo: [],\r\n      // form: {},\r\n\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 文件信息表格数据\r\n      // docList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        serviceId: null,\r\n        sqdServiceTypeId: null,\r\n        sqdRctNo: null,\r\n        docSourceId: null,\r\n        docTypeId: null,\r\n        docTrackingNo: null,\r\n        shipper: null,\r\n        consignee: null,\r\n        notifyParty: null,\r\n        shippingMark: null,\r\n        goodsDescription: null,\r\n        containersSealsList: null,\r\n        blIssueDate: null,\r\n        blIssueLocation: null,\r\n        isSwitchBl: null,\r\n        isDividedBl: null,\r\n        isAgreementShowed: null,\r\n        isCustomsIntransit: null,\r\n        docIssueType: null,\r\n        docGettingWay: null,\r\n        docDeliveryWay: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n  },\r\n\r\n  methods: {\r\n    parseTime,\r\n    /*  /!** 序号 *!/\r\n     rowIndex({row, rowIndex}) {\r\n       row.id = rowIndex + 1;\r\n     },\r\n     handleUpdate(row) {\r\n       this.form = row\r\n       this.open = true\r\n     },\r\n     handleDelete(row) {\r\n       this.logisticsNoInfo = this.logisticsNoInfo.filter(item => {\r\n         return item.id != row.id\r\n       })\r\n     },\r\n     submitForm() {\r\n       if (this.form.id != null) {\r\n         this.reset()\r\n         this.open = false\r\n       } else {\r\n         this.logisticsNoInfo.push(this.form)\r\n         this.reset()\r\n         this.open = false\r\n       }\r\n     },\r\n     reset() {\r\n       this.form = {\r\n         id: null,\r\n         soNo: null,\r\n         mblNo: null,\r\n         hblNo: null,\r\n         containersInfo: null,\r\n         shipper: null,\r\n         consignee: null,\r\n         notifyParty: null,\r\n         polBookingAgent: null,\r\n         podHandleAgent: null,\r\n         shippingMark: null,\r\n         goodsDescription: null,\r\n         blIssueDate: null,\r\n         blIssueLocation: null,\r\n       }\r\n       this.resetForm(\"form\");\r\n     },\r\n     cancel() {\r\n       this.open = false\r\n     }, */\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        docId: null,\r\n        serviceId: null,\r\n        sqdServiceTypeId: null,\r\n        sqdRctNo: null,\r\n        docSourceId: null,\r\n        docTypeId: null,\r\n        docTrackingNo: null,\r\n        shipper: null,\r\n        consignee: null,\r\n        notifyParty: null,\r\n        shippingMark: null,\r\n        goodsDescription: null,\r\n        containersSealsList: null,\r\n        blIssueDate: null,\r\n        blIssueLocation: null,\r\n        isSwitchBl: null,\r\n        isDividedBl: null,\r\n        isAgreementShowed: null,\r\n        isCustomsIntransit: null,\r\n        docIssueType: null,\r\n        docGettingWay: null,\r\n        docDeliveryWay: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.docId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.docId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加文件信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const docId = row.docId || this.ids\r\n      getDoc(docId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改文件信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.docId != null) {\r\n            updateDoc(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDoc(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const docIds = row.docId || this.ids;\r\n      this.$confirm('是否确认删除文件信息编号为\"' + docIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delDoc(docIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 查询文件信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDoc(this.queryParams).then(response => {\r\n        this.docList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;AAsVA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAI,IAAA;EACAC,UAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,KAAA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,gBAAAD,eAAA;IACA;IACAE,WAAA,WAAAA,YAAAC,CAAA;MACA,KAAAC,KAAA,GAAAD,CAAA;IACA;IACAC,KAAA,WAAAA,MAAAD,CAAA;MACA,IAAAA,CAAA;QACA,KAAAF,KAAA;MACA;IACA;IACAI,UAAA,WAAAA,WAAAF,CAAA;MACA,IAAAA,CAAA;QACA,KAAAG,SAAA;QACA,KAAAC,QAAA;MACA;QACA,KAAAD,SAAA;QACA,KAAAC,QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAP,KAAA;MACAJ,eAAA;MACA;;MAEAO,QAAA;MACAD,SAAA;MACA;MACAG,OAAA;MACA;MACAG,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAT,UAAA;MACA;MACAU,KAAA;MACA;MACA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,aAAA;QACAC,OAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,WAAA;QACAC,eAAA;QACAC,UAAA;QACAC,WAAA;QACAC,iBAAA;QACAC,kBAAA;QACAC,YAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EAEAC,OAAA;IACAC,SAAA,EAAAA,eAAA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAEA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA7B,IAAA;MACA,KAAA8B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAL,IAAA;QACAM,KAAA;QACA3B,SAAA;QACAC,gBAAA;QACAC,QAAA;QACAC,WAAA;QACAC,SAAA;QACAC,aAAA;QACAC,OAAA;QACAC,SAAA;QACAC,WAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,WAAA;QACAC,eAAA;QACAC,UAAA;QACAC,WAAA;QACAC,iBAAA;QACAC,kBAAA;QACAC,YAAA;QACAC,aAAA;QACAC,cAAA;MACA;MACA,KAAAQ,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAhC,WAAA,CAAAC,OAAA;MACA,KAAAgC,OAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAAH,SAAA;MACA,KAAAC,WAAA;IACA;IACAG,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,MAAA;MACA,KAAAC,QAAA,UAAAF,IAAA;QAAAG,WAAA;MAAA,GAAAC,IAAA;QACA,OAAAC,YAAA,CAAAP,GAAA,CAAAN,KAAA,EAAAM,GAAA,CAAAG,MAAA;MACA,GAAAG,IAAA;QACAL,KAAA,CAAAO,MAAA,CAAAC,UAAA,CAAAP,IAAA;MACA,GAAAQ,KAAA;QACAV,GAAA,CAAAG,MAAA,GAAAH,GAAA,CAAAG,MAAA;MACA;IACA;IACA;IACAQ,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAtD,GAAA,GAAAsD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAApB,KAAA;MAAA;MACA,KAAAnC,MAAA,GAAAqD,SAAA,CAAAG,MAAA;MACA,KAAAvD,QAAA,IAAAoD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAvB,KAAA;MACA,KAAA9B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAuD,YAAA,WAAAA,aAAAjB,GAAA;MAAA,IAAAkB,MAAA;MACA,KAAAzB,KAAA;MACA,IAAAC,KAAA,GAAAM,GAAA,CAAAN,KAAA,SAAApC,GAAA;MACA,IAAA6D,WAAA,EAAAzB,KAAA,EAAAY,IAAA,WAAAc,QAAA;QACAF,MAAA,CAAA9B,IAAA,GAAAgC,QAAA,CAAAhE,IAAA;QACA8D,MAAA,CAAAvD,IAAA;QACAuD,MAAA,CAAAxD,KAAA;MACA;IACA;IACA,WACA2D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAlC,IAAA,CAAAM,KAAA;YACA,IAAAgC,cAAA,EAAAJ,MAAA,CAAAlC,IAAA,EAAAkB,IAAA,WAAAc,QAAA;cACAE,MAAA,CAAAd,MAAA,CAAAC,UAAA;cACAa,MAAA,CAAA3D,IAAA;cACA2D,MAAA,CAAAzB,OAAA;YACA;UACA;YACA,IAAA8B,WAAA,EAAAL,MAAA,CAAAlC,IAAA,EAAAkB,IAAA,WAAAc,QAAA;cACAE,MAAA,CAAAd,MAAA,CAAAC,UAAA;cACAa,MAAA,CAAA3D,IAAA;cACA2D,MAAA,CAAAzB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+B,YAAA,WAAAA,aAAA5B,GAAA;MAAA,IAAA6B,MAAA;MACA,IAAAC,MAAA,GAAA9B,GAAA,CAAAN,KAAA,SAAApC,GAAA;MACA,KAAA8C,QAAA,oBAAA0B,MAAA;QAAAzB,WAAA;MAAA,GAAAC,IAAA;QACA,OAAAyB,MAAA,CAAAD,MAAA;MACA,GAAAxB,IAAA;QACAuB,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAArB,MAAA,CAAAC,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,eACAb,OAAA,WAAAA,QAAA;MAAA,IAAAmC,MAAA;MACA,KAAA7E,OAAA;MACA8E,OAAA,MAAArE,WAAA,EAAA0C,IAAA,WAAAc,QAAA;QACAY,MAAA,CAAAE,OAAA,GAAAd,QAAA,CAAAe,IAAA;QACAH,MAAA,CAAAvE,KAAA,GAAA2D,QAAA,CAAA3D,KAAA;QACAuE,MAAA,CAAA7E,OAAA;MACA;IACA;EACA;AACA;AAAAiF,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}