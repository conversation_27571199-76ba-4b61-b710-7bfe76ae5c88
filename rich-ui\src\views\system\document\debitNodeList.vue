<template>
  <div class="debit-note-list">
    <el-table
      ref="debitNoteTable"
      :data="debitNoteList"
      border
      style="width: 100%"
      @expand-change="handleExpandChange"
      @selection-change="handleSelectionChange"
    >
      <!-- 可展开列 -->
      <el-table-column type="expand" width="50">
        <template slot-scope="scope">
          <!-- 嵌套 chargeList 组件 -->
          <debit-note-charge-list
            :charge-data="scope.row.rsChargeList"
            :company-list="companyList"
            :debit-note="scope.row"
            :disabled="debitNoteDisabled(scope.row)"
            :hidden-supplier="hiddenSupplier"
            :is-receivable="isReceivable"
            :open-charge-list="true"
            @copyFreight="handleCopyFreight"
            @deleteAll="handleDeleteAll"
            @deleteItem="scope.row.rsChargeList = scope.row.rsChargeList.filter(charge => charge !== $event)"
            @return="handleChargeDataChange(scope.row, $event)"
            @selectRow="handleChargeSelection(scope.row, $event)"
          />
        </template>
      </el-table-column>

      <!-- 勾选列 -->
      <el-table-column align="center" type="selection"></el-table-column>

      <!-- 分账单基本信息列 -->
      <el-table-column label="所属公司" prop="sqdRctNo" width="60">
        <template slot-scope="scope">
          <tree-select :flat="false" :multiple="false" :pass="scope.row.companyBelongsTo" :placeholder="'收付路径'"
                       :type="'rsPaymentTitle'" @return="scope.row.companyBelongsTo=$event"
                       :class="debitNoteDisabled(scope.row)?'disable-form':''" :disabled="debitNoteDisabled(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="我司账户" prop="companyName" width="100">
        <template slot-scope="scope">
          <tree-select :flat="false" :multiple="false"
                       :pass="scope.row.bankAccountCode" :placeholder="'我司账户'"
                       :class="debitNoteDisabled(scope.row)?'disable-form':''" :disabled="debitNoteDisabled(scope.row)"
                       :type="'companyAccount'"
                       @return="scope.row.bankAccountCode=$event" @returnData="selectBankAccount(scope.row, $event)"
          />
        </template>
      </el-table-column>

      <el-table-column align="center" label="收付标志" prop="isRecievingOrPaying" width="50">
        <template slot-scope="scope">
          <el-tag
            :type="getBillStatusType(scope.row.billStatus)"
            size="mini"
          >
            {{ scope.row.isRecievingOrPaying == 0 ? "收" : "付" }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="结算单位" prop="dnCurrencyCode" width="100">
        <template slot-scope="scope">
          <tree-select :custom-options="companyList" :flat="false" :multiple="false"
                       :pass="scope.row.clearingCompanyId" :placeholder="'客户'"
                       :type="'clientCustom'" @return="scope.row.clearingCompanyId=$event"
                       :class="debitNoteDisabled(scope.row)?'disable-form':''" :disabled="debitNoteDisabled(scope.row)"
                       @returnData="handleSelectCompany(scope.row, $event)"
          />
        </template>
      </el-table-column>

      <el-table-column label="对方账户" prop="billReceivable" width="120">
        <template slot-scope="scope">
          <el-input v-model="scope.row.clearingCompanyBankAccount"
                    :class="debitNoteDisabled(scope.row)?'disable-form':''"
                    :disabled="debitNoteDisabled(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="结算币种" prop="billPayable" width="120">
        <template slot-scope="scope">
          <tree-select :class="debitNoteDisabled(scope.row)?'disable-form':''" :disabled="debitNoteDisabled(scope.row)"
                       :pass="scope.row.dnCurrencyCode"
                       :type="'currency'"
                       @return="changeCurrency(scope.row,$event)"
          />
        </template>
      </el-table-column>

      <el-table-column align="right" label="账单应收" v-if="isReceivable" prop="billReceivable" width="80"/>
      <el-table-column align="right" label="账单应付" v-if="!isReceivable" prop="billPayable" width="80"/>

      <el-table-column align="center" label="账单状态" prop="billStatus" width="60">
        <template slot-scope="scope">
          <el-tag
            :type="getBillStatusType(scope.row.billStatus)"
            size="mini"
            :class="debitNoteDisabled(scope.row)?'disable-form':''" :disabled="debitNoteDisabled(scope.row)"
          >
            {{ getBillStatusText(scope.row.billStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column align="center" label="发票状态" prop="invoiceStatus" width="60">
        <template slot-scope="scope">
          <el-tag
            :type="getInvoiceStatusType(scope.row.invoiceStatus)"
            :class="debitNoteDisabled(scope.row)?'disable-form':''" :disabled="debitNoteDisabled(scope.row)"
            size="mini" @click="handleInvoiceStatusClick(scope.row)"
          >
            {{ getInvoiceStatusText(scope.row.invoiceStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="申请支付" prop="invoiceStatus" width="80">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.requestPaymentDate"
            :class="debitNoteDisabled(scope.row)?'disable-form':''"
            :disabled="debitNoteDisabled(scope.row)"
            placeholder="选择日期" type="date"
          />
        </template>
      </el-table-column>
      <el-table-column label="预计支付" prop="invoiceStatus" width="80">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.expectedPaymentDate"
            :class="debitNoteDisabled(scope.row)?'disable-form':''"
            :disabled="debitNoteDisabled(scope.row)"
            placeholder="选择日期" type="date"
          />
        </template>
      </el-table-column>
      <el-table-column label="实际支付" prop="invoiceStatus" width="80">
        <template slot-scope="scope">
          <el-date-picker
            v-model="scope.row.actualPaymentDate"
            :class="debitNoteDisabled(scope.row)?'disable-form':''"
            :disabled="debitNoteDisabled(scope.row)"
            placeholder="选择日期" type="date"
          />
        </template>
      </el-table-column>

      <el-table-column align="center" label="销账状态" prop="writeoffStatus" width="60">
        <template slot-scope="scope">
          <el-tag
            :type="getWriteoffStatusType(scope.row.writeoffStatus)"
            size="mini"
            :class="debitNoteDisabled(scope.row)?'disable-form':''" :disabled="debitNoteDisabled(scope.row)"
          >
            {{ getWriteoffStatusText(scope.row.writeoffStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column class-name="small-padding fixed-width" fixed="right" label="操作" width="130">
        <template slot-scope="scope">
          <div style="display: flex; gap: 4px;">
            <el-button
              v-if="scope.row.billStatus==='confirmed'"
              icon="el-icon-unlock"
              size="mini"
              type="success"
              @click="applyUnlock(scope.row)"
            >
              申请解锁
            </el-button>
            <el-button
              v-if="scope.row.billStatus==='draft'"
              icon="el-icon-check"
              size="mini"
              type="success"
              @click="setComplete(scope.row)"
            >
              设置完成
            </el-button>
            <el-button
              :disabled="scope.row.rsChargeList.length>0"
              icon="el-icon-delete"
              size="mini"
              type="danger"
              @click="deleteDebitNote(scope.row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-button :disabled="disabled" style="padding: 0"
               type="text"
               @click="addDebitNote"
    >[＋]
    </el-button>

    <!-- 发票对话框 -->
    <vatinvoice-dialog
      :company-list="companyList"
      :form="invoiceForm"
      :invoice-items="invoiceItems"
      :title="'增值税发票管理'"
      :visible.sync="invoiceDialogVisible"
      @cancel="handleInvoiceCancel"
      @submit="handleInvoiceSubmit"
    />
  </div>
</template>

<script>
import currency from "currency.js"
import debitNoteChargeList from "@/views/system/document/debitNoteChargeList.vue"
import VatinvoiceDialog from "@/views/system/vatinvoice/components/VatinvoiceDialog.vue"
import {
  addVatinvoice,
  updateVatinvoice,
  getVatinvoice,
  countVatinvoiceByRctId,
  generateInvoiceCode
} from "@/api/system/vatInvoice"
import {updateDebitNote} from "@/api/system/debitnote"

export default {
  name: "debitNoteList",
  components: {debitNoteChargeList, VatinvoiceDialog},
  props: [
    "companyList",
    "disabled",
    "hiddenSupplier",
    "rctId",
    "debitNoteList",
    "isReceivable"
  ],
  data() {
    return {
      loading: false,
      expandedRows: [],
      localDebitNoteList: [],
      selectedDebitNotes: [],
      invoiceDialogVisible: false,
      currentDebitNote: null,
      invoiceForm: {},
      invoiceItems: []
    }
  },
  watch: {
    debitNoteList: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.$emit("update:debitNoteList", newVal)
        this.$emit("return", newVal)
      }
    }
  },
  computed: {},
  methods: {
    // 处理表格选择变化
    handleSelectionChange(selection) {
      this.selectedDebitNotes = selection
      this.$emit("selection-change", selection)
    },
    debitNoteDisabled(row) {
      return row.billStatus === "confirmed" || this.disabled
    },
    async handleInvoiceStatusClick(row) {
      // 申请开票
      this.currentDebitNote = row

      // 检查是否有invoiceId
      if (row.invoiceId) {
        try {
          // 根据invoiceId查找现有发票信息
          const response = await getVatinvoice(row.invoiceId)
          if (response.code === 200 && response.data) {
            // 使用现有发票信息准备数据
            this.prepareInvoiceDataWithExisting(row, response.data)
          } else {
            // 查找失败，使用默认数据
            await this.prepareInvoiceData(row)
          }
        } catch (error) {
          console.error("获取发票信息失败:", error)
          // 出错时使用默认数据
          await this.prepareInvoiceData(row)
        }
      } else {
        // 没有invoiceId，创建新的发票
        await this.prepareInvoiceData(row)
      }

      this.invoiceDialogVisible = true
    },

    // 生成发票流水号
    async generateInvoiceCodeNo(rctId, cooperatorId) {
      try {
        // 调用API生成发票编码
        const response = await generateInvoiceCode(rctId, cooperatorId)
        if (response.code === 200) {
          return response.msg
        }
      } catch (error) {
        console.error("生成发票编码失败:", error)
      }
      // 如果API调用失败，返回默认格式
      return ""
    },

    // 准备发票对话框数据（新建发票）
    async prepareInvoiceData(debitNote) {
      // 生成发票流水号
      let invoiceCodeNo
      if (this.rctId && debitNote.clearingCompanyId) {
        let invoiceCode = await this.generateInvoiceCodeNo(this.rctId, debitNote.clearingCompanyId)
        invoiceCodeNo = debitNote.sqdRctNo + "-" + invoiceCode
      }

      // 设置发票表单数据
      this.invoiceForm = {
        // 基本发票信息
        invoiceId: debitNote.invoiceId || null, // 发票ID
        invoiceCodeNo: invoiceCodeNo,
        saleBuy: debitNote.isRecievingOrPaying == 0 ? "sale" : "buy", // 根据收付标志设置进销项
        taxClass: "",
        invoiceType: "增值税发票",
        mergeInvoice: debitNote.mergeInvoice || "0",
        invoiceOfficalNo: debitNote.invoiceOfficalNo || "",

        // 公司和账户信息
        invoiceBelongsTo: debitNote.companyBelongsTo || "",
        richBankCode: debitNote.bankAccountCode || "",
        cooperatorId: debitNote.clearingCompanyId || "",
        cooperatorBankCode: debitNote.clearingCompanyBankAccount || "",
        richCompanyTitle: debitNote.bankAccountName || "",
        cooperatorCompanyTitle: debitNote.clearingCompanyName || "",

        // 项目和订单信息
        officalChargeNameSummary: "",
        relatedOrderNo: "",

        // 税号和银行信息
        richVatSerialNo: "",
        cooperatorVatSerialNo: "",
        richBankFullname: "",
        cooperatorBankFullname: "",
        richBankAccount: "",
        cooperatorBankAccount: debitNote.clearingCompanyBankAccount || "",

        // 备注和日期信息
        invoiceRemark: "",
        expectedPayDate: debitNote.expectedPaymentDate || "",
        approvedPayDate: "",
        actualPayDate: debitNote.actualPaymentDate || "",

        // 发票金额信息
        invoiceExchangeRate: "1",
        invoiceCurrencyCode: debitNote.dnCurrencyCode || "RMB",
        invoiceNetAmount: debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable,
        invoiceStatus: debitNote.invoiceStatus === "issued" ? "1" : "0",
        belongsToMonth: this.formatCurrentMonth(),

        // RCT关联信息
        rctId: this.rctId || null
      }

      // 准备发票明细项
      this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {
        return {
          billNo: debitNote.billNo || "",
          rctNo: "",  // 这里可能需要从父组件获取
          serviceType: charge.chargeName || "",
          chargeName: charge.chargeName || "",
          remark: charge.remark || "",
          paymentFlag: debitNote.isRecievingOrPaying == 0 ? "收" : "付",
          quoteCurrency: charge.currencyCode || "",
          unitPrice: charge.unitPrice || 0,
          quantity: charge.quantity || 0,
          unit: charge.unit || "",
          settlementRate: charge.exchangeRate || 1,
          settlementCurrency: debitNote.dnCurrencyCode || "",
          taxRate: charge.taxRate || "",
          taxIncludedTotal: charge.subtotal || 0,
          invoiceItemName: charge.chargeName || "",
          taxCode: ""
        }
      }) : []
    },

    // 准备发票对话框数据（使用现有发票信息）
    prepareInvoiceDataWithExisting(debitNote, existingInvoice) {
      // 使用现有发票信息设置表单数据
      this.invoiceForm = {
        // 基本发票信息
        invoiceId: existingInvoice.invoiceId || debitNote.invoiceId || null,
        invoiceCodeNo: existingInvoice.invoiceCodeNo || debitNote.invoiceCodeNo || "",
        saleBuy: existingInvoice.saleBuy || (debitNote.isRecievingOrPaying == 0 ? "sale" : "buy"),
        taxClass: existingInvoice.taxClass || "",
        invoiceType: existingInvoice.invoiceType || "增值税发票",
        mergeInvoice: existingInvoice.mergeInvoice || debitNote.mergeInvoice || "0",
        invoiceOfficalNo: existingInvoice.invoiceOfficalNo || debitNote.invoiceOfficalNo || "",

        // 公司和账户信息
        invoiceBelongsTo: existingInvoice.invoiceBelongsTo || debitNote.companyBelongsTo || "",
        richBankCode: existingInvoice.richBankCode || debitNote.bankAccountCode || "",
        cooperatorId: existingInvoice.cooperatorId || debitNote.clearingCompanyId || "",
        cooperatorBankCode: existingInvoice.cooperatorBankCode || debitNote.clearingCompanyBankAccount || "",
        richCompanyTitle: existingInvoice.richCompanyTitle || debitNote.bankAccountName || "",
        cooperatorCompanyTitle: existingInvoice.cooperatorCompanyTitle || debitNote.clearingCompanyName || "",

        // 项目和订单信息
        officalChargeNameSummary: existingInvoice.officalChargeNameSummary || "",
        relatedOrderNo: existingInvoice.relatedOrderNo || "",

        // 税号和银行信息
        richVatSerialNo: existingInvoice.richVatSerialNo || "",
        cooperatorVatSerialNo: existingInvoice.cooperatorVatSerialNo || "",
        richBankFullname: existingInvoice.richBankFullname || "",
        cooperatorBankFullname: existingInvoice.cooperatorBankFullname || "",
        richBankAccount: existingInvoice.richBankAccount || "",
        cooperatorBankAccount: existingInvoice.cooperatorBankAccount || debitNote.clearingCompanyBankAccount || "",

        // 备注和日期信息
        invoiceRemark: existingInvoice.invoiceRemark || "",
        expectedPayDate: existingInvoice.expectedPayDate || debitNote.expectedPaymentDate || "",
        approvedPayDate: existingInvoice.approvedPayDate || "",
        actualPayDate: existingInvoice.actualPayDate || debitNote.actualPaymentDate || "",

        // 发票金额信息
        invoiceExchangeRate: existingInvoice.invoiceExchangeRate || "1",
        invoiceCurrencyCode: existingInvoice.invoiceCurrencyCode || debitNote.dnCurrencyCode || "RMB",
        invoiceNetAmount: existingInvoice.invoiceNetAmount || (debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable),
        invoiceStatus: existingInvoice.invoiceStatus || (debitNote.invoiceStatus === "issued" ? "1" : "0"),
        belongsToMonth: existingInvoice.belongsToMonth || this.formatCurrentMonth(),

        // RCT关联信息
        rctId: existingInvoice.rctId || this.rctId || null
      }

      // 使用现有发票明细项，如果没有则使用debitNote的费用明细
      if (existingInvoice.invoiceItems && existingInvoice.invoiceItems.length > 0) {
        this.invoiceItems = existingInvoice.invoiceItems
      } else {
        // 准备发票明细项
        this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {
          return {
            billNo: debitNote.billNo || "",
            rctNo: "",  // 这里可能需要从父组件获取
            serviceType: charge.chargeName || "",
            chargeName: charge.chargeName || "",
            remark: charge.remark || "",
            paymentFlag: debitNote.isRecievingOrPaying == 0 ? "收" : "付",
            quoteCurrency: charge.currencyCode || "",
            unitPrice: charge.unitPrice || 0,
            quantity: charge.quantity || 0,
            unit: charge.unit || "",
            settlementRate: charge.exchangeRate || 1,
            settlementCurrency: debitNote.dnCurrencyCode || "",
            taxRate: charge.taxRate || "",
            taxIncludedTotal: charge.subtotal || 0,
            invoiceItemName: charge.chargeName || "",
            taxCode: ""
          }
        }) : []
      }
    },

    // 格式化当前月份为 yyyyMM 格式（如 202503）
    formatCurrentMonth() {
      const date = new Date()
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, "0")
      return `${year}${month}`
    },

    // 处理发票对话框提交
    async handleInvoiceSubmit(formData) {
      try {
        let response

        // 根据是否有invoiceId决定是新增还是修改
        if (formData.invoiceId) {
          // 修改发票
          response = await updateVatinvoice(formData)
        } else {
          // 新增发票
          response = await addVatinvoice(formData)
        }

        if (response.code === 200) {
          // 更新发票状态
          if (this.currentDebitNote) {
            this.currentDebitNote.invoiceStatus = formData.invoiceStatus === "1" ? "issued" : "unissued"

            // 将发票ID写入到debitNote中
            let invoiceId = null
            if (response.data && response.data.invoiceId) {
              invoiceId = response.data.invoiceId
              this.currentDebitNote.invoiceId = invoiceId
            } else if (formData.invoiceId) {
              invoiceId = formData.invoiceId
              this.currentDebitNote.invoiceId = invoiceId
            }

            // 更新发票相关字段
            this.currentDebitNote.invoiceCodeNo = formData.invoiceCodeNo || ""
            this.currentDebitNote.invoiceOfficalNo = formData.invoiceOfficalNo || ""
            this.currentDebitNote.invoiceType = formData.invoiceType || ""
            this.currentDebitNote.mergeInvoice = formData.mergeInvoice || "0"
            // 更新分账单的发票状态为已申请
            this.currentDebitNote.invoiceStatus = "applied"

            // 调用接口更新debitNote中的invoiceId
            if (invoiceId && this.currentDebitNote.debitNoteId) {
              try {
                const updateData = {
                  debitNoteId: this.currentDebitNote.debitNoteId,
                  invoiceId: invoiceId,
                  invoiceCodeNo: formData.invoiceCodeNo || "",
                  invoiceOfficalNo: formData.invoiceOfficalNo || "",
                  invoiceType: formData.invoiceType || "",
                  mergeInvoice: formData.mergeInvoice || "0",
                  invoiceStatus: "applied"
                }

                // 更新分账单
                const updateResponse = await updateDebitNote(updateData)
                if (updateResponse.code === 200) {
                  this.$message.success("发票信息已更新到分账单")
                } else {
                  console.warn("更新分账单发票信息失败:", updateResponse.msg)
                }
              } catch (updateError) {
                console.error("更新分账单发票信息失败:", updateError)
              }
            }

            // 通知父组件数据变化
            this.$emit("return", this.debitNoteList)
          }

          this.$message.success("发票保存成功")
        } else {
          this.$message.error(response.msg || "发票保存失败")
        }
      } catch (error) {
        console.error("发票保存失败:", error)
        this.$message.error("发票保存失败，请重试")
      }

      this.invoiceDialogVisible = false
    },

    // 处理发票对话框取消
    handleInvoiceCancel() {
      this.invoiceDialogVisible = false
    },
    applyUnlock(row) {
      // 查看发票状态,已开票的不能申请解锁
      if (row.invoiceStatus === "issued") {
        this.$message.error("已开票的分账单不能申请解锁")
        return
      }

      this.$emit("applyUnlock", row)
    },
    setComplete(row) {
      try {
        this.$confirm("确定要将该分账单设置为已确认状态吗？此操作将禁用整条数据及其费用明细。", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          // 修改状态为已确认
          row.billStatus = "confirmed"

          // 设置所有费用明细的isAccountConfirmed为'1'，使其被禁用
          if (row.rsChargeList && row.rsChargeList.length > 0) {
            row.rsChargeList.forEach(charge => {
              charge.isAccountConfirmed = "1"
            })
          }

          // 通知父组件状态变更
          this.$emit("setComplete", row)

          // 提示用户
          this.$message({
            type: "success",
            message: "分账单已设置为已确认状态"
          })
        }).catch(() => {
          // 用户取消操作
        })
      } catch (error) {
        console.error("设置分账单状态失败:", error)
        this.$message.error("设置分账单状态失败")
      }
    },
    changeCurrency(row, currency) {
      row.dnCurrencyCode = currency
    },
    selectBankAccount(row, bankAccount) {
      row.bankAccountCode = bankAccount.bankAccountCode
      row.bankAccountName = bankAccount.bankAccountName
    },
    handleSelectCompany(row, company) {
      row.clearingCompanyId = company.companyId
      row.clearingCompanyName = company.companyShortName
    },
    addDebitNote() {
      this.$emit("addDebitNote")
    },
    currency,

    // 展开/收起行
    handleExpandChange(row, expandedRows) {
      this.expandedRows = expandedRows
    },

    // 创建分账单
    async createDebitNote(row) {
      try {

      } catch (error) {
        console.error("创建分账单失败:", error)
        this.$message.error("创建分账单失败")
      }
    },
    // 删除分账单
    async deleteDebitNote(row) {
      try {
        await this.$confirm("确定要删除该分账单吗？", "提示", {
          type: "warning"
        })
        this.$emit("deleteItem", row)
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除分账单失败:", error)
          this.$message.error("删除分账单失败")
        }
      }
    },

    // 处理费用数据变化
    handleChargeDataChange(row, chargeData) {
      let billReceivable = 0
      let billPayable = 0

      // 统计chargeData的费用
      if (this.isReceivable) {
        // 应收
        chargeData.forEach(item => {
          // 使用currency.js计算
          billReceivable = currency(billReceivable).add(item.subtotal).toString()
        })
      } else {
        // 应付
        chargeData.forEach(item => {
          billPayable = currency(billPayable).add(item.subtotal).toString()
        })
      }
      row.billReceivable = billReceivable
      row.billPayable = billPayable

      row.rsChargeList = chargeData
      // 通知父组件数据变化
      this.$emit("return", this.debitNoteList)
    },

    // 处理费用选择
    handleChargeSelection(row, selectedCharges) {
      const index = this.localDebitNoteList.findIndex(item => item === row)
      if (index !== -1) {
        this.localDebitNoteList[index].selectedCharges = selectedCharges
        // 通知父组件数据变化
        this.$emit("update:debitNoteList", this.localDebitNoteList)
      }
    },

    // 复制费用
    handleCopyFreight(charge) {
      this.$emit("copyFreight", charge)
    },

    // 删除费用项
    handleDeleteItem(charge) {
      this.$emit("deleteItem", charge)
    },

    // 删除所有费用
    handleDeleteAll() {
      this.$emit("deleteAll")
    },

    // 获取账单状态类型
    getBillStatusType(status) {
      const statusMap = {
        "draft": "info",
        "confirmed": "success",
        "closed": "danger"
      }
      return statusMap[status] || "info"
    },

    // 获取账单状态文本
    getBillStatusText(status) {
      const statusMap = {
        "draft": "草稿",
        "confirmed": "已确认",
        "closed": "已关闭"
      }
      return statusMap[status] || "未知"
    },

    // 获取发票状态类型
    getInvoiceStatusType(status) {
      const statusMap = {
        "unissued": "info",
        "issued": "success",
        "applied": "warning",
        "canceled": "danger"
      }
      return statusMap[status] || "info"
    },

    // 获取发票状态文本
    getInvoiceStatusText(status) {
      const statusMap = {
        "unissued": "未开票",
        "issued": "已开票",
        "applied": "已申请",
        "canceled": "已作废"
      }
      return statusMap[status] || "未知"
    },

    // 获取销账状态类型
    getWriteoffStatusType(status) {
      const statusMap = {
        "unwritten": "info",
        "partial": "warning",
        "written": "success"
      }
      return statusMap[status] || "info"
    },

    // 获取销账状态文本
    getWriteoffStatusText(status) {
      const statusMap = {
        "unwritten": "未销账",
        "partial": "部分销账",
        "written": "已销账"
      }
      return statusMap[status] || "未知"
    }
  }
}
</script>

<style lang="scss" scoped>
.expand-content {
  padding: 20px;
  background-color: #f9f9f9;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin: 10px 0;
}

.charge-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e4e7ed;

  span {
    font-weight: bold;
    font-size: 16px;
    color: #303133;
  }
}

input:focus {
  outline: none;
}

.unHighlight-text {
  color: #b7bbc2;
  margin: 0;
}

// 覆盖 Element UI 表格样式
:deep(.el-table) {
  .el-table__expanded-cell {
    padding: 0;

    .expand-content {
      margin: 0;
      border: none;
      background-color: transparent;
    }
  }
}
</style>
