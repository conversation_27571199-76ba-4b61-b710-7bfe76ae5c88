{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\HeaderSearch\\index.vue?vue&type=style&index=0&id=032bd1f0&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\HeaderSearch\\index.vue", "mtime": 1754876882530}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5oZWFkZXItc2VhcmNoIHsNCiAgZm9udC1zaXplOiAwICFpbXBvcnRhbnQ7DQoNCiAgLnNlYXJjaC1pY29uIHsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgZm9udC1zaXplOiAxOHB4Ow0KICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQogIH0NCg0KICAuaGVhZGVyLXNlYXJjaC1zZWxlY3Qgew0KICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICB0cmFuc2l0aW9uOiB3aWR0aCAwLjJzOw0KICAgIHdpZHRoOiAwOw0KICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQ7DQogICAgYm9yZGVyLXJhZGl1czogMDsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgdmVydGljYWwtYWxpZ246IG1pZGRsZTsNCg0KICAgIDo6di1kZWVwIC5lbC1pbnB1dF9faW5uZXIgew0KICAgICAgYm9yZGVyLXJhZGl1czogMDsNCiAgICAgIGJvcmRlcjogMDsNCiAgICAgIHBhZGRpbmctbGVmdDogMDsNCiAgICAgIHBhZGRpbmctcmlnaHQ6IDA7DQogICAgICBib3gtc2hhZG93OiBub25lICFpbXBvcnRhbnQ7DQogICAgICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2Q5ZDlkOTsNCiAgICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7DQogICAgfQ0KICB9DQoNCiAgJi5zaG93IHsNCiAgICAuaGVhZGVyLXNlYXJjaC1zZWxlY3Qgew0KICAgICAgd2lkdGg6IDIxMHB4Ow0KICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyJA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/HeaderSearch", "sourcesContent": ["<template>\r\n  <div :class=\"{'show':show}\" class=\"header-search\">\r\n    <svg-icon class-name=\"search-icon\" icon-class=\"search\" @click.stop=\"click\"/>\r\n    <el-select\r\n      ref=\"headerSearchSelect\"\r\n      v-model=\"search\"\r\n      :remote-method=\"querySearch\"\r\n      class=\"header-search-select\"\r\n      default-first-option\r\n      filterable\r\n      placeholder=\"Search\"\r\n      remote\r\n      @change=\"change\">\r\n      <el-option v-for=\"option in options\" :key=\"option.item.path\" :label=\"option.item.title.join(' > ')\"\r\n                 :value=\"option.item\"/>\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n// fuse is a lightweight fuzzy-search module\r\n// make search results more in line with expectations\r\nimport Fuse from 'fuse.js/dist/fuse.min.js'\r\nimport path from 'path'\r\n\r\nexport default {\r\n  name: 'HeaderSearch',\r\n  data() {\r\n    return {\r\n      search: '',\r\n      options: [],\r\n      searchPool: [],\r\n      show: false,\r\n      fuse: undefined\r\n    }\r\n  },\r\n  computed: {\r\n    routes() {\r\n      return this.$store.getters.permission_routes\r\n    }\r\n  },\r\n  watch: {\r\n    routes() {\r\n      this.searchPool = this.generateRoutes(this.routes)\r\n    },\r\n    searchPool(list) {\r\n      this.initFuse(list)\r\n    },\r\n    show(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.close)\r\n      } else {\r\n        document.body.removeEventListener('click', this.close)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.searchPool = this.generateRoutes(this.routes)\r\n  },\r\n  methods: {\r\n    click() {\r\n      this.show = !this.show\r\n      if (this.show) {\r\n        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus()\r\n      }\r\n    },\r\n    close() {\r\n      this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.blur()\r\n      this.options = []\r\n      this.show = false\r\n    },\r\n    change(val) {\r\n      const path = val.path;\r\n      if (this.ishttp(val.path)) {\r\n        // http(s):// 路径新窗口打开\r\n        const pindex = path.indexOf(\"http\");\r\n        window.open(path.substr(pindex, path.length), \"_blank\");\r\n      } else {\r\n        this.$router.push(val.path)\r\n      }\r\n      this.search = ''\r\n      this.options = []\r\n      this.$nextTick(() => {\r\n        this.show = false\r\n      })\r\n    },\r\n    initFuse(list) {\r\n      this.fuse = new Fuse(list, {\r\n        shouldSort: true,\r\n        threshold: 0.4,\r\n        location: 0,\r\n        distance: 100,\r\n        minMatchCharLength: 1,\r\n        keys: [{\r\n          name: 'title',\r\n          weight: 0.7\r\n        }, {\r\n          name: 'path',\r\n          weight: 0.3\r\n        }]\r\n      })\r\n    },\r\n    // Filter out the routes that can be displayed in the sidebar\r\n    // And generate the internationalized title\r\n    generateRoutes(routes, basePath = '/', prefixTitle = []) {\r\n      let res = []\r\n\r\n      for (const router of routes) {\r\n        // skip hidden router\r\n        if (router.hidden) {\r\n          continue\r\n        }\r\n\r\n        const data = {\r\n          path: !this.ishttp(router.path) ? path.resolve(basePath, router.path) : router.path,\r\n          title: [...prefixTitle]\r\n        }\r\n\r\n        if (router.meta && router.meta.title) {\r\n          data.title = [...data.title, router.meta.title]\r\n\r\n          if (router.redirect != 'noRedirect') {\r\n            // only push the routes with title\r\n            // special case: need to exclude parent router without redirect\r\n            res.push(data)\r\n          }\r\n        }\r\n\r\n        // recursive child routes\r\n        if (router.children) {\r\n          const tempRoutes = this.generateRoutes(router.children, data.path, data.title)\r\n          if (tempRoutes.length >= 1) {\r\n            res = [...res, ...tempRoutes]\r\n          }\r\n        }\r\n      }\r\n      return res\r\n    },\r\n    querySearch(query) {\r\n      if (query != '') {\r\n        this.options = this.fuse.search(query)\r\n      } else {\r\n        this.options = []\r\n      }\r\n    },\r\n    ishttp(url) {\r\n      return url.indexOf('http://') != -1 || url.indexOf('https://') != -1\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.header-search {\r\n  font-size: 0 !important;\r\n\r\n  .search-icon {\r\n    cursor: pointer;\r\n    font-size: 18px;\r\n    vertical-align: middle;\r\n  }\r\n\r\n  .header-search-select {\r\n    font-size: 18px;\r\n    transition: width 0.2s;\r\n    width: 0;\r\n    overflow: hidden;\r\n    background: transparent;\r\n    border-radius: 0;\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    ::v-deep .el-input__inner {\r\n      border-radius: 0;\r\n      border: 0;\r\n      padding-left: 0;\r\n      padding-right: 0;\r\n      box-shadow: none !important;\r\n      border-bottom: 1px solid #d9d9d9;\r\n      vertical-align: middle;\r\n    }\r\n  }\r\n\r\n  &.show {\r\n    .header-search-select {\r\n      width: 210px;\r\n      margin-left: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}