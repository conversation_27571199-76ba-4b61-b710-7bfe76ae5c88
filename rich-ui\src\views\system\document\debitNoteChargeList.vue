<template>
  <el-col :style="{'display':openChargeList?'':'none'}" style="margin: 0;padding: 0;">
    <div :class="{'inactive':openChargeList==false,'active':openChargeList}">
      <el-table
        ref="chargeTable"
        :data="localChargeData"
        border
        class="pd0"
        row-key="getItemKey"
        @selection-change="handleSelectionChange"
        :row-class-name="setRowData"
      >
        <el-table-column
          align="center"
          type="selection"
        >
        </el-table-column>
        <el-table-column align="center" label="费用" prop="quotationChargeId" width="80px">
          <template slot-scope="scope">
            <div v-if="!scope.row.showQuotationCharge"
                 @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCharge = true"
            >
              {{ scope.row.chargeName }}
            </div>
            <tree-select v-show="scope.row.showQuotationCharge" :dbn="true" :disabled="disabled|| scope.row.isAccountConfirmed == '1'" :flat="false"
                         :multiple="false" :pass="scope.row.dnChargeNameId" :placeholder="'运费'"
                         :type="'charge'"
                         @return="scope.row.dnChargeNameId = $event"
                         @returnData="handleChargeSelect(scope.row,$event)"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="货币" prop="quotationCurrencyId" width="70">
          <template slot-scope="scope">
            <div v-if="!scope.row.showQuotationCurrency" style="width: 69px ;height: 23px"
                 @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationCurrency = true"
            >
              {{ scope.row.dnCurrencyCode }}
            </div>
            <tree-select v-show="scope.row.showQuotationCurrency" :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                         :pass="scope.row.dnCurrencyCode" :type="'currency'"
                         @return="changeCurrency(scope.row,$event)"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="单价" prop="quotationRate" width="80px">
          <template slot-scope="scope">
            <div v-if="!scope.row.showUnitRate"
                 @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showUnitRate = true"
            >
              {{
                scope.row.dnCurrencyCode === "RMB" ? currency(scope.row.dnUnitRate, {
                  separator: ",",
                  precision: 2,
                  symbol: "¥"
                }).format() : (scope.row.dnCurrencyCode === "USD" ? currency(scope.row.dnUnitRate, {
                  separator: ",",
                  precision: 2,
                  symbol: "$"
                }).format() : scope.row.dnUnitRate)
              }}
            </div>
            <el-input-number v-show="scope.row.showUnitRate" v-model="scope.row.dnUnitRate" :controls="false"
                             :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                             :min="0.0001"
                             :precision="4" style="display:flex;width: 100%"
                             @blur="scope.row.showUnitRate=false"
                             @change="countProfit(scope.row,'unitRate')"
                             @input="countProfit(scope.row,'unitRate')"
                             @focusout.native="scope.row.showUnitRate=false"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" prop="quotationUnitId" width="50">
          <template slot-scope="scope">
            <div v-if="!scope.row.showQuotationUnit"
                 @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showQuotationUnit = true"
            >
              {{ scope.row.dnUnitCode }}
            </div>
            <tree-select v-show="scope.row.showQuotationUnit"
                         :disabled="disabled|| scope.row.isAccountConfirmed == '1'" :pass="scope.row.dnUnitCode"
                         :type="'unit'" @return="changeUnit(scope.row,$event)"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="数量" prop="quotationAmount" width="48px">
          <template slot-scope="scope">
            <div v-if="!scope.row.showAmount"
                 @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showAmount = true"
            >
              {{ scope.row.dnAmount }}
            </div>
            <el-input-number v-if="scope.row.showAmount" v-model="scope.row.dnAmount" :controls="false"
                             :disabled="disabled|| scope.row.isAccountConfirmed == '1'"
                             :min="0.00" placeholder="数量"
                             style="display:flex;width: 100%" @blur="scope.row.showAmount=false"
                             @change="countProfit(scope.row,'amount')"
                             @input="countProfit(scope.row,'amount')"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="汇率" prop="quotationExchangeRate" width="60">
          <template slot-scope="scope" style="display:flex;">
            <div v-if="!scope.row.showCurrencyRate"
                 @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showCurrencyRate = true"
            >
              {{ currency(scope.row.basicCurrencyRate, {precision: 4}).value }}
            </div>
            <el-input-number v-show="scope.row.showCurrencyRate" v-model="scope.row.basicCurrencyRate"
                             :controls="false"
                             :disabled="disabled|| scope.row.isAccountConfirmed == '1'"
                             :min="0.0001" :precision="4" :step="0.0001"
                             style="width: 100%" @blur="scope.row.showCurrencyRate=false"
                             @change="countProfit(scope.row,'currencyRate')"
                             @input="countProfit(scope.row,'currencyRate')"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="税率" prop="quotationTaxRate" width="50px">
          <template slot-scope="scope">
            <div style="display: flex;justify-content: center">
              <div v-if="!scope.row.showDutyRate"
                   @click="(disabled|| scope.row.isAccountConfirmed == '1')?null:scope.row.showDutyRate = true"
              >
                {{ scope.row.dutyRate }}
              </div>
              <el-input-number v-if="scope.row.showDutyRate" v-model="scope.row.dutyRate" :controls="false"
                               :disabled="disabled || scope.row.isAccountConfirmed == '1'"
                               :min="0" style="width: 75%"
                               @blur="scope.row.showDutyRate=false"
                               @change="countProfit(scope.row,'dutyRate')"
                               @input="countProfit(scope.row,'dutyRate')"
              />
              <div>%</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="小计" prop="subtotal" width="75">
          <template slot-scope="scope">
            <div>
              {{
                currency(scope.row.subtotal, {
                  separator: ",",
                  precision: 2,
                  symbol: (scope.row.dnCurrencyCode === "RMB" ? "¥" : "$")
                }).format()
              }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="费用备注">
          <template slot-scope="scope">
            <input v-model="scope.row.chargeRemark"
                   :disabled="disabled|| scope.row.isAccountConfirmed == '1'"
                   style="border: none;width: 100%;height: 100%;"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="审核状态">
          <template slot-scope="scope">
            {{ auditStatus(scope.row.isAccountConfirmed) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="已收金额">
          <template slot-scope="scope">
            {{
              scope.row.sqdDnCurrencyPaid
            }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="未收余额">
          <template slot-scope="scope">
            {{ scope.row.sqdDnCurrencyBalance }}
          </template>
        </el-table-column>
        <el-table-column label="所属服务" width="80">
          <template slot-scope="scope">
            <div>
              {{ scope.row.serviceName ? scope.row.serviceName : getServiceName(scope.row.sqdServiceTypeId) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="50">
          <template slot="header" slot-scope="scope">
            <el-button
              :disabled="disabled || hasConfirmRow"
              size="mini"
              style="color: red"
              type="text"
              @click="deleteAllItem()"
            >全部删除
            </el-button>
          </template>
          <template slot-scope="scope">
            <el-button
              :disabled="disabled || scope.row.isAccountConfirmed == '1'"
              icon="el-icon-delete"
              size="mini"
              type="danger"
              @click="deleteItem(scope.row)"
            >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-button :disabled="disabled" style="padding: 0"
               type="text"
               @click="addReceivablePayable"
    >[＋]
    </el-button>
  </el-col>
</template>

<script>
import currency from "currency.js"
import Treeselect from "@riophae/vue-treeselect"
import pinyin from "js-pinyin"
import CompanySelect from "@/components/CompanySelect/index.vue"
import {parseTime} from "@/utils/rich"
import Sortable from "sortablejs"

export default {
  name: "debitNoteChargeList",
  components: {CompanySelect, Treeselect},
  props: ["chargeData", "companyList", "openChargeList", "isReceivable", "disabled",
    "hiddenSupplier", "rsClientMessageReceivableTaxUSD",
    "rsClientMessageReceivableTaxRMB", "rsClientMessagePayableTaxUSD", "rsClientMessagePayableTaxRMB",
    "rsClientMessageReceivableRMB", "rsClientMessageReceivableUSD", "rsClientMessagePayableRMB",
    "rsClientMessagePayableUSD", "rsClientMessageProfit", "rsClientMessageProfitNoTax", "payDetailRMB",
    "payDetailUSD", "payDetailRMBTax", "payDetailUSDTax", "rsClientMessageProfitUSD", "rsClientMessageProfitRMB",
    "rsClientMessageProfitTaxRMB", "rsClientMessageProfitTaxUSD", "debitNote", "dragGroupName"],
  computed: {
    localChargeData: {
      get() {
        return this.chargeData || []
      },
      set(value) {
        this.$emit("return", value)
      }
    },
    hasConfirmRow() {
      let result = false;
      (this.chargeData && this.chargeData.length > 0) ? this.chargeData.map(item => {
        if (item.isAccountConfirmed === "1") {
          result = true
        }
      }) : null
      return result
    }
  },
  watch: {
    chargeData: {
      handler: function (newVal, oldVal) {
        if (!oldVal) {
          this.$emit("return", newVal)
          return
        }

        // 遍历费用列表，检查币种变化
        newVal ? newVal.forEach((item, index) => {
          const oldItem = oldVal[index]

          // 检查币种变化并计算小计
          if (item.currency && item.amount) {
            // 如果从 RMB 换成 USD，使用 1/汇率 计算
            if (oldItem && oldItem.currency === "RMB" && item.currency === "USD") {
              if (item.exchangeRate && item.exchangeRate !== 0) {
                try {
                  // 计算 1/汇率，保留4位小数
                  const inverseRate = currency(1, {precision: 4}).divide(item.exchangeRate).value

                  // 计算小计: 单价 * 数量 * 汇率 * (1 + 税率/100)
                  item.subtotal = currency(item.dnUnitRate || 0, {precision: 4})
                    .multiply(item.amount)
                    .multiply(inverseRate)
                    .multiply(currency(1).add(currency(item.dutyRate || 0).divide(100)))
                    .value
                } catch (error) {
                  console.error("计算小计出错:", error)
                  item.subtotal = 0
                }
              }
            }
          }
        }) : null

        this.$emit("return", newVal ? newVal : [])

        // 数据变化后重新初始化拖拽，使用更安全的方式
        this.reinitializeSortable()
      },
      deep: true,
      immediate: true
    },
    disabled: {
      handler: function (newVal) {
        // 禁用状态改变时重新初始化拖拽
        this.reinitializeSortable()
      }
    }
  },
  mounted() {
    this.initSortable()
  },
  beforeDestroy() {
    // 清理定时器
    if (this.reinitTimer) {
      clearTimeout(this.reinitTimer)
      this.reinitTimer = null
    }
    // 销毁拖拽实例
    this.destroySortable()
  },
  data() {
    return {
      payTotalRMB: 0,
      payTotalUSD: 0,
      showClientName: null,
      sortable: null,
      reinitTimer: null,
      dragStartColumn: -1, // 记录拖拽开始的列索引
      services: [{
        value: 1,
        label: "海运"
      }, {
        value: 10,
        label: "空运"
      }, {
        value: 20,
        label: "铁路"
      }, {
        value: 40,
        label: "快递"
      }, {
        value: 50,
        label: "拖车"
      }, {
        value: 60,
        label: "报关"
      }, {
        value: 70,
        label: "清关派送"
      }, {
        value: 80,
        label: "码头仓储"
      }, {
        value: 90,
        label: "检验证书"
      }, {
        value: 100,
        label: "保险"
      }, {
        value: 101,
        label: "扩展服务"
      }],
      service: [{
        value: 1,
        label: "基础服务"
      }, {
        value: 4,
        label: "前程运输"
      }, {
        value: 5,
        label: "出口报关"
      }, {
        value: 6,
        label: "进口清关"
      }, {value: 2, label: "海运"}
        , {value: 3, label: "陆运"}
        , {value: 4, label: "铁路"}
        , {value: 5, label: "空运"}
        , {value: 6, label: "快递"}
        , {value: 21, label: "整柜海运"}
        , {value: 22, label: "拼柜海运"}
        , {value: 23, label: "散杂船"}
        , {value: 24, label: "滚装船"}
        , {value: 41, label: "整柜铁路"}
        , {value: 42, label: "拼柜铁路"}
        , {value: 43, label: "铁路车皮"}
        , {value: 51, label: "空运普舱"}
        , {value: 52, label: "空运包板"}
        , {value: 53, label: "空运包机"}
        , {value: 54, label: "空运行李"}
        , {value: 961, label: "前程运输"}
        , {value: 964, label: "进口清关"}
        , {value: 7, label: "出口报关"}
      ],
      chargeRemark: null
    }
  },
  methods: {
    destroySortable() {
      if (this.sortable) {
        try {
          // 检查 sortable 实例是否还有效
          if (this.sortable.el && this.sortable.el.parentNode) {
            this.sortable.destroy()
          }
        } catch (error) {
          console.warn('Error destroying sortable:', error)
        } finally {
          this.sortable = null
        }
      }
    },
    reinitializeSortable() {
      // 使用防抖延迟重新初始化，避免频繁的创建销毁
      if (this.reinitTimer) {
        clearTimeout(this.reinitTimer)
      }

      this.reinitTimer = setTimeout(() => {
        this.$nextTick(() => {
          this.destroySortable()
          // 确保 DOM 已更新后再初始化
          this.$nextTick(() => {
            this.initSortable()
          })
        })
      }, 100)
    },
    initSortable() {
      this.$nextTick(() => {
        if (this.$refs.chargeTable && this.$refs.chargeTable.$el) {
          const tbody = this.$refs.chargeTable.$el.querySelector('tbody')
          if (tbody) {
            try {
              this.sortable = Sortable.create(tbody, {
                group: {
                  name: this.dragGroupName || 'debitNoteChargeGroup',
                  pull: (to, from, dragEl, evt) => {
                    // 根据拖拽列决定是否允许拖出（剪切或复制）
                    if (this.dragStartColumn === 1) {
                      return true; // 从第一列拖动时允许拖出（剪切）
                    } else {
                      return 'clone'; // 从其他列拖动时复制
                    }
                  },
                  put: !this.disabled
                },
                animation: 150,
                disabled: this.disabled,
                ghostClass: 'sortable-ghost',
                dragClass: 'sortable-drag',
                filter: '.disabled',
                onStart: (evt) => {
                  // 拖拽开始
                  const draggedItem = this.localChargeData[evt.oldIndex]

                  // 获取拖拽开始的列索引
                  this.dragStartColumn = -1 // 默认设置为-1
                  try {
                    // 获取拖拽事件的起始坐标
                    const mouseX = evt.originalEvent.clientX
                    const mouseY = evt.originalEvent.clientY

                    // 尝试更准确地确定拖拽开始的列
                    const cells = evt.item.querySelectorAll('td')
                    if (cells && cells.length > 0) {
                      // 计算每个单元格的位置，找到包含鼠标位置的单元格
                      for (let i = 0; i < cells.length; i++) {
                        const rect = cells[i].getBoundingClientRect()
                        if (mouseX >= rect.left && mouseX <= rect.right &&
                          mouseY >= rect.top && mouseY <= rect.bottom) {
                          this.dragStartColumn = i
                          break
                        }
                      }
                    }

                    // 备选方法：如果上面的方法没找到，使用表头定位
                    if (this.dragStartColumn === -1) {
                      const headerCells = this.$refs.chargeTable.$el.querySelectorAll('thead th')
                      if (headerCells && headerCells.length > 0) {
                        for (let i = 0; i < headerCells.length; i++) {
                          const rect = headerCells[i].getBoundingClientRect()
                          if (mouseX >= rect.left && mouseX <= rect.right) {
                            this.dragStartColumn = i
                            break
                          }
                        }
                      }
                    }

                    if (this.dragStartColumn === -1) {
                      // 回退方案：如果通过坐标无法确定，则默认为非第一列
                      this.dragStartColumn = 2 // 设置为非第一列，默认为复制模式
                    }

                  } catch (error) {
                    console.error('确定拖拽开始列时出错:', error)
                    this.dragStartColumn = 1 // 出错时默认为第一列
                  }

                  // 设置被拖拽元素的数据
                  try {
                    evt.item.setAttribute('data-drag-item', JSON.stringify(draggedItem))
                    // 额外添加拖拽起始列信息
                    evt.item.setAttribute('data-drag-column', this.dragStartColumn)
                  } catch (error) {
                    console.error('Failed to stringify drag item:', error)
                    evt.item.setAttribute('data-drag-item', '{}')
                    evt.item.setAttribute('data-drag-column', '-1')
                  }

                  this.$emit('dragStart', {
                    item: draggedItem,
                    index: evt.oldIndex,
                    from: this,
                    column: this.dragStartColumn
                  })
                },
                onAdd: (evt) => {
                  // 接收到新元素
                  const item = evt.item
                  let draggedItem = {}
                  let dragStartColumn = -1

                  try {
                    const dragData = item.getAttribute('data-drag-item')
                    if (dragData && dragData !== 'undefined') {
                      draggedItem = JSON.parse(dragData)
                    }

                    // 获取拖拽起始列
                    const columnData = item.getAttribute('data-drag-column')
                    if (columnData && columnData !== 'undefined') {
                      dragStartColumn = parseInt(columnData, 10)
                    }
                  } catch (error) {
                    console.error('Failed to parse drag item data:', error)
                  }

                  // 处理新增元素到表格
                  const newChargeData = [...this.localChargeData]

                  // 无论是复制还是剪切，都需要添加项到目标位置
                  // 但要给新项生成一个新的ID，表示这是一个全新的项
                  newChargeData.splice(evt.newIndex, 0, {
                    ...draggedItem,
                    tempId: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    chargeId: null // 清除原有ID，作为新增项
                  })

                  console.log(newChargeData)
                  this.localChargeData = newChargeData

                  this.$emit('return', newChargeData)
                  this.$emit('dragAdd', {
                    item: draggedItem,
                    newIndex: evt.newIndex,
                    to: this,
                    column: dragStartColumn
                  })
                },
                onRemove: (evt) => {
                  // 获取拖拽信息
                  const item = evt.item
                  let dragStartColumn = -1
                  try {
                    const columnData = item.getAttribute('data-drag-column')
                    if (columnData && columnData !== 'undefined') {
                      dragStartColumn = parseInt(columnData, 10)
                    }
                  } catch (error) {
                    console.error('Failed to parse drag column data:', error)
                  }

                  const newChargeData = [...this.localChargeData]
                  const removedItem = newChargeData[evt.oldIndex]

                  // 只有在从第一列开始拖拽时才执行剪切操作
                  // Sortable的clone选项已经控制了复制行为，这里我们只需处理剪切的情况
                  if (dragStartColumn === 1) {
                    newChargeData.splice(evt.oldIndex, 1)
                    this.$emit('return', newChargeData)
                  }

                  this.$emit('dragRemove', {
                    item: removedItem,
                    oldIndex: evt.oldIndex,
                    from: this,
                    column: dragStartColumn,
                    isCut: dragStartColumn === 1
                  })
                },
                onUpdate: (evt) => {
                  // 同一表格内排序
                  const newChargeData = [...this.localChargeData]
                  const item = newChargeData.splice(evt.oldIndex, 1)[0]
                  newChargeData.splice(evt.newIndex, 0, item)

                  this.$emit('return', newChargeData)
                  this.$emit('dragUpdate', {
                    item: item,
                    oldIndex: evt.oldIndex,
                    newIndex: evt.newIndex,
                    column: this.dragStartColumn
                  })
                },
                onEnd: (evt) => {
                  // 拖拽结束
                  this.$emit('dragEnd', {
                    ...evt,
                    dragColumn: this.dragStartColumn
                  })
                  // 重置拖拽列
                  this.dragStartColumn = -1
                }
              })
            } catch (error) {
              console.error('Error creating sortable:', error)
            }
          }
        }
      })
    },
    setRowData({row, rowIndex}) {
      // 为每行设置数据属性，用于拖拽传递数据
      this.$nextTick(() => {
        const tableRows = this.$refs.chargeTable.$el.querySelectorAll('tbody tr')
        if (tableRows[rowIndex]) {
          try {
            tableRows[rowIndex].setAttribute('data-drag-item', JSON.stringify(row))
          } catch (error) {
            console.error('Failed to stringify row data:', error)
            tableRows[rowIndex].setAttribute('data-drag-item', '{}')
          }
        }
      })
      return ''
    },
    auditStatus(status) {
      return status == 1 ? "已审核" : "未审核"
    },
    selectCharge(target, row) {
      row.dnChargeNameId = target.chargeId
      row.chargeName = target.chargeLocalName
    },
    handleSelectionChange(val) {
      // 处理选择变化
      this.$emit("selectRow", val)

      this.payTotalUSD = 0
      this.payTotalRMB = 0
      val ? val.map(item => {
        if (item.isRecievingOrPaying == 1) {
          if (item.dnCurrencyCode === "USD") {
            this.payTotalUSD = currency(this.payTotalUSD).add(item.subtotal)
          } else {
            this.payTotalRMB = currency(this.payTotalRMB).add(item.subtotal)
          }

        }
      }) : null

    },
    currency,
    // 获取项目的唯一键
    getItemKey(item) {
      return item.tempId || item.chargeId || item.id || `item_${Math.random().toString(36).substr(2, 9)}`
    },
    getServiceName(id) {
      let serviceName = ""
      this.services.map(obj => {
        obj.value === id ? serviceName = obj.label : null
      })
      return serviceName
    },
    copyFreight(row) {
      if (this.companyList.length > 0) {
        row.payClearingCompanyId = this.companyList[0].companyId
        row.payCompanyName = this.companyList[0].companyShortName
      }
      row.isAccountConfirmed = 0
      // 报价列表跳转订舱时没有公司列表,复制到应收没有客户信息
      let data = this._.cloneDeep(row)

      this.$emit("copyFreight", {...data, chargeId: null})
    },
    copyAllFreight() {
      if (!this.companyList.length > 0) {
        this.$modal.alertWarning("请先选择委托单位或关联单位")
        return
      }

      this.chargeData.map(charge => {
        charge.payClearingCompanyId = this.companyList[0].companyId
        charge.payCompanyName = this.companyList[0].companyShortName
        charge.isRecievingOrPaying = 0
        charge.isAccountConfirmed = 0
        charge.chargeId = null
        this.$emit("copyFreight", this._.cloneDeep(charge))
      })
    },
    changeUnitCost(row, unit) {
      row.dnUnitCode = unit
      this.$nextTick(() => {
        row.showCostUnit = false
      })
    },
    changeUnit(row, unit) {
      row.dnUnitCode = unit
      this.$nextTick(() => {
        row.showQuotationUnit = false
      })
    },
    handleChargeSelect(row, data) {
      if (row.chargeLocalName === data.chargeName) {
        row.chargeName = data.chargeLocalName
        row.showQuotationCharge = false
      }
      if (row.currencyCode == null && data.currencyCode) {
        row.dnCurrencyCode = data.currencyCode
      }
    },
    changeCurrency(row, currencyCode) {
      row.dnCurrencyCode = currencyCode
      /* let exchangeRate
      if (currencyCode === "USD") {
        for (const a of this.$store.state.data.exchangeRateList) {
          if (a.localCurrency === "RMB"
            && row.dnCurrencyCode == a.overseaCurrency
          ) {
            exchangeRate = currency(a.settleRate).divide(a.base).value
          }
        }
      } */

      this.$nextTick(() => {
        // row.basicCurrencyRate = exchangeRate ? exchangeRate : 1
        row.showQuotationCurrency = false
      })
    },
    /** 序号 */
    rowIndex({row, rowIndex}) {
      row.id = rowIndex + 1
    },
    addReceivablePayable() {
      let obj = {
        showClient: true,
        showSupplier: true,
        showQuotationCharge: true,
        showCostCharge: true,
        showQuotationCurrency: true,
        showCostCurrency: true,
        showQuotationUnit: true,
        showCostUnit: true,
        showStrategy: true,
        showUnitRate: true,
        showAmount: true,
        showCurrencyRate: true,
        showDutyRate: true,
        basicCurrencyRate: 1,
        dutyRate: 0,
        dnAmount: 1,
        // 应收还是应付
        isRecievingOrPaying: this.isReceivable ? 0 : 1,
        clearingCompanyId: this.chargeData.length > 0 ? this.chargeData[this.chargeData.length - 1].clearingCompanyId : null
      }
      if (this.serviceTypeId === 1) obj.sqdServiceTypeId = 1
      if (this.serviceTypeId === 10) obj.sqdServiceTypeId = 10
      if (this.serviceTypeId === 20) obj.sqdServiceTypeId = 20
      if (this.serviceTypeId === 40) obj.sqdServiceTypeId = 40
      if (this.serviceTypeId === 50) obj.sqdServiceTypeId = 50
      if (this.serviceTypeId === 60) obj.sqdServiceTypeId = 60
      if (this.serviceTypeId === 70) obj.sqdServiceTypeId = 70
      if (this.serviceTypeId === 80) obj.sqdServiceTypeId = 80
      if (this.serviceTypeId === 90) obj.sqdServiceTypeId = 90
      if (this.serviceTypeId === 100) obj.sqdServiceTypeId = 100
      if (this.serviceTypeId === 101) obj.sqdServiceTypeId = 101
      this.chargeData.push(obj)
    },
    countProfit(row, category) {
      // 确保所有必要的值都存在且有效
      if (!row) return

      // 使用currency.js来处理数值,避免精度损失
      const unitRate = row.dnUnitRate || 0
      const amount = row.dnAmount || 0
      const currencyRate = currency(row.basicCurrencyRate || 1, {precision: 4}).value
      const dutyRate = currency(row.dutyRate || 0).value

      try {
        // 计算小计
        const subtotal = currency(unitRate, {precision: 4})
          .multiply(amount)
          .multiply(currencyRate)
          .multiply(currency(1).add(currency(dutyRate).divide(100)))
          .value

        // 更新行数据
        row.subtotal = currency(subtotal, {precision: 2}).value
        row.sqdDnCurrencyBalance = row.isAccountConfirmed === "0" ? currency(subtotal, {precision: 2}).value : row.sqdDnCurrencyBalance

        // 根据不同的输入类型关闭对应的编辑状态
        switch (category) {
          case "strategy":
            row.showStrategy = false
            break
          case "unitRate":
            // 不在这里关闭编辑状态,改用@blur事件
            break
          case "amount":
            // 不在这里关闭编辑状态,改用@blur事件
            break
          case "currencyRate":
            // 不在这里关闭编辑状态,改用@blur事件
            break
          case "dutyRate":
            // 不在这里关闭编辑状态,改用@blur事件
            break
        }

        // 触发数据更新
        this.$emit("return", this.chargeData)

      } catch (error) {
        console.error("计算小计时出错:", error)
        this.$message.error("计算小计时出错,请检查输入值是否正确")
      }
    },
    deleteItem(row) {
      this.$emit("deleteItem", row)
    },
    deleteAllItem(row) {
      this.$emit("deleteAll")
    },
    companyNormalizer(node) {
      return {
        id: node.companyId,
        label: (node.companyShortName != null ? node.companyShortName : "") + " " + (node.companyLocalName != null ? node.companyLocalName : "") + "," + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : "") + " " + (node.companyLocalName != null ? node.companyLocalName : ""))
      }
    }
  }
}
</script>
<style lang="scss" scoped>
input:focus {
  outline: none;
}

.unHighlight-text {
  color: #b7bbc2;
  margin: 0;
}

// 拖拽样式
::v-deep .sortable-ghost {
  opacity: 0.5;
  background-color: #f5f7fa;
}

::v-deep .sortable-drag {
  opacity: 0.8;
  background-color: #ecf5ff;
  border: 1px dashed #409eff;
}

// 拖拽时的表格行样式
::v-deep tbody tr {
  cursor: move;
  transition: all 0.3s ease;
}

::v-deep tbody tr:hover {
  background-color: #f5f7fa;
}

// 禁用状态下不显示拖拽光标
::v-deep tbody tr.disabled {
  cursor: default;
}
</style>
