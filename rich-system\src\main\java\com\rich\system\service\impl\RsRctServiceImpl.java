package com.rich.system.service.impl;

import com.rich.common.config.NotificationWebSocket;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.domain.entity.*;
import com.rich.common.core.domain.model.LoginUser;
import com.rich.common.core.text.Convert;
import com.rich.common.utils.*;
import com.rich.system.domain.MidCargoType;
import com.rich.system.domain.MidCarrier;
import com.rich.system.domain.MidServiceType;
import com.rich.system.mapper.*;
import com.rich.system.service.BasPositionService;
import com.rich.system.service.RsRctService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.rich.common.utils.PageUtils.startPage;

/**
 * 操作单Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-20
 */
@Service
public class RsRctServiceImpl implements RsRctService {
    private static final Logger log = LoggerFactory.getLogger(RsRctServiceImpl.class);

    @Autowired
    private RsRctMapper rsRctMapper;

    @Resource
    private RsServiceInstancesMapper rsServiceInstancesMapper;

    @Resource
    private RsBasicLogisticsMapper rsBasicLogisticsMapper;

    @Resource
    private RsChargeMapper rsChargeMapper;

    @Resource
    private RsOpLogMapper rsOpLogMapper;

    @Autowired
    private MidServiceTypeMapper midServiceTypeMapper;

    @Autowired
    private MidCargoTypeMapper midCargoTypeMapper;

    @Autowired
    private MidCarrierMapper midCarrierMapper;

    @Autowired
    private RedisCacheImpl RedisCache;

    @Autowired
    private RsDocMapper rsDocMapper;

    @Autowired
    private RsPrecarriageMapper rsPrecarriageMapper;

    @Autowired
    private RsImportCustomsMapper rsImportCustomsMapper;

    @Autowired
    private RsExportCustomsMapper rsExportCustomsMapper;

    @Autowired
    private com.rich.common.core.redis.RedisCache redisCache;

    @Autowired
    private ExtCompanyMapper extCompanyMapper;

    @Resource
    private RsOpSeaFclMapper rsOpSeaFclMapper;
    @Resource
    private RsOpSeaLclMapper rsOpSeaLclMapper;
    @Resource
    private RsOpBulkShipMapper rsOpBulkShipMapper;
    @Resource
    private RsOpRoroShipMapper rsOpRoroShipMapper;
    @Resource
    private RsOpAirMapper rsOpAirMapper;
    @Resource
    private RsOpRailMapper rsOpRailMapper;
    @Resource
    private RsOpExpressMapper rsOpExpressMapper;
    @Resource
    private RsOpPortServiceMapper rsOpPortServiceMapper;
    @Resource
    private RsOpTruckMapper rsOpTruckMapper;
    @Resource
    private RsOpExportCustomsClearanceMapper rsOpExportCustomsClearanceMapper;
    @Resource
    private RsOpImportCustomsClearanceMapper rsOpImportCustomsClearanceMapper;
    @Resource
    private RsOpImportDispatchTruckMapper rsOpImportDispatchTruckMapper;
    @Resource
    private RsOpWarehouseMapper rsOpWarehouseMapper;
    @Resource
    private RsOpInspectionAndCertificateMapper rsOpInspectionAndCertificateMapper;
    @Resource
    private RsOpLandMapper rsOpLandMapper;
    @Resource
    private RsOpInsuranceMapper rsOpInsuranceMapper;
    @Resource
    private RsOpExpandServiceMapper rsOpExpandServiceMapper;

    @Resource
    private RsBookingMessageMapper rsBookingMessageMapper;

    @Autowired
    private DataSourceTransactionManager transactionManager;

    @Resource
    private RsOpTruckListMapper rsOpTruckListMapper;

    @Resource
    private RedisIdGeneratorService redisIdGeneratorService;

    @Resource
    private BasPositionService basPositionService;

    @Resource
    private RsDebitNoteMapper rsDebitNoteMapper;

    /**
     * 查询操作单
     *
     * @param rctId 操作单主键
     * @return 操作单
     */
    @Override
    public RsRct selectRsRctByRctId(Long rctId) {
        // 1. 查询基础数据
        RsRct rsRct = rsRctMapper.selectRsRctByRctId(rctId);
        if (rsRct == null) {
            return null;
        }

        // 2. 一次性查询所有关联数据
        // 预加载提单列表
        RsBookingMessage rsBookingMessage = new RsBookingMessage();
        rsBookingMessage.setRctId(rctId);
        rsRct.setBookingMessagesList(rsBookingMessageMapper.selectRsBookingMessageList(rsBookingMessage));

        // 预加载IDs
        rsRct.setCarrierIds(midCarrierMapper.selectMidCarrierById(rctId, "rct").toArray(new Long[0]));
        rsRct.setCargoTypeIds(midCargoTypeMapper.selectMidCargoTypeById(rctId, "rct").toArray(new Long[0]));
        rsRct.setServiceTypeIds(midServiceTypeMapper.selectMidServiceTypeById(rctId, "rct").toArray(new Long[0]));

        // 3. 一次性查询所有费用和日志 - 避免每个服务实例单独查询
        List<RsCharge> allCharges = rsChargeMapper.selectRsChargeListByRctId(rctId);
        List<RsOpLog> allOpLogs = rsOpLogMapper.selectRsOpLogByRctId(rctId);
        List<RsDebitNote> allDebitNotes = rsDebitNoteMapper.selectRsDebitNoteByRctId(rctId);

        // 按serviceId预先分组费用和日志 - 避免后续重复遍历
        Map<Long, List<RsCharge>> chargeMap = new HashMap<>();
        Map<Long, List<RsOpLog>> logMap = new HashMap<>();
        Map<Long, List<RsDebitNote>> debitNodeMap = new HashMap<>();
        // 按debitNodeId分组费用
        Map<Long, List<RsCharge>> debitNoteChargeMap = new HashMap<>();

        for (RsCharge charge : allCharges) {
            if (charge.getServiceId() != null && charge.getDebitNoteId() == null) {
                chargeMap.computeIfAbsent(charge.getServiceId(), k -> new ArrayList<>()).add(charge);
            }

            // 按debitNodeId分组费用
            if (charge.getDebitNoteId() != null) {
                debitNoteChargeMap.computeIfAbsent(charge.getDebitNoteId(), k -> new ArrayList<>()).add(charge);
            }
        }

        for (RsOpLog log : allOpLogs) {
            if (log.getServiceId() != null) {
                logMap.computeIfAbsent(log.getServiceId(), k -> new ArrayList<>()).add(log);
            }
        }

        for (RsDebitNote rsDebitNote : allDebitNotes) {
            if (rsDebitNote.getServiceId() != null) {
                debitNodeMap.computeIfAbsent(rsDebitNote.getServiceId(), k -> new ArrayList<>()).add(rsDebitNote);

                // 将对应的费用放入到debit note中
                if (rsDebitNote.getDebitNoteId() != null) {
                    List<RsCharge> debitNoteCharges = debitNoteChargeMap.get(rsDebitNote.getDebitNoteId());
                    if (debitNoteCharges != null) {
                        rsDebitNote.setRsChargeList(debitNoteCharges);
                    } else {
                        rsDebitNote.setRsChargeList(new ArrayList<>());
                    }
                }
            }
        }

        // 4. 处理客户端消息
        processClientMessage(rsRct, allOpLogs, chargeMap, logMap, debitNodeMap);

        // 5. 初始化供应商ID集合
        rsRct.setSupplierIds(new LinkedHashSet<>());

        // 6. 预先准备服务类型映射 - 避免大量if判断
        Map<Long, ServiceProcessor> serviceProcessors = createServiceProcessorMap(rsRct, chargeMap, logMap, debitNodeMap);

        // 7. 批量处理所有服务类型
        Long[] serviceTypeIds = rsRct.getServiceTypeIds();
        for (Long serviceTypeId : serviceTypeIds) {
            ServiceProcessor processor = serviceProcessors.get(serviceTypeId);
            if (processor != null) {
                processor.process();
            }
        }

        return rsRct;
    }

    /**
     * 处理客户端消息
     */
    private void processClientMessage(RsRct rsRct, List<RsOpLog> allOpLogs,
                                      Map<Long, List<RsCharge>> chargeMap,
                                      Map<Long, List<RsOpLog>> logMap,
                                      Map<Long, List<RsDebitNote>> debitNodeMap) {
        RsClientMessage rsClientMessage = new RsClientMessage();
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "client");
        
        if (rsServiceInstances != null) {
            rsClientMessage.setRsServiceInstances(rsServiceInstances);
            Long serviceId = rsServiceInstances.getServiceId();

            // 使用预先分组的数据，确保返回的列表是可修改的
            List<RsCharge> charges = chargeMap.get(serviceId);
            rsClientMessage.setRsChargeList(charges != null ? new ArrayList<>(charges) : new ArrayList<>());

            List<RsOpLog> logs = logMap.get(serviceId);
            rsClientMessage.setRsOpLogList(logs != null ? new ArrayList<>(logs) : new ArrayList<>());

            List<RsDebitNote> debitNotes = debitNodeMap.get(serviceId);
            rsClientMessage.setRsDebitNoteList(debitNotes != null ? new ArrayList<>(debitNotes) : new ArrayList<>());
        } else {
            rsClientMessage.setRsServiceInstances(new RsServiceInstances());
            rsClientMessage.setRsChargeList(new ArrayList<>());
            rsClientMessage.setRsOpLogList(new ArrayList<>());
            rsClientMessage.setRsDebitNoteList(new ArrayList<>());
        }

        rsRct.setRsClientMessage(rsClientMessage);
    }

    /**
     * 创建服务处理器映射
     */
    private Map<Long, ServiceProcessor> createServiceProcessorMap(RsRct rsRct,
                                                                  Map<Long, List<RsCharge>> chargeMap,
                                                                  Map<Long, List<RsOpLog>> logMap,
                                                                  Map<Long, List<RsDebitNote>> debitNodeMap) {
        Map<Long, ServiceProcessor> processors = new HashMap<>();
        Long rctId = rsRct.getRctId();

        // 整柜海运 - 1L
        processors.put(1L, () -> {
            List<RsOpSeaFcl> rsOpSeaFclList = rsOpSeaFclMapper.selectRsOpSeaFclByRctId(rctId, 1L);
            processServiceList(rsOpSeaFclList, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            rsRct.setRsOpSeaFclList(rsOpSeaFclList);
        });

        // 拼柜海运 - 2L
        processors.put(2L, () -> {
            List<RsOpSeaLcl> rsOpSeaLclList = rsOpSeaLclMapper.selectRsOpSeaLclByRctId(rctId, 2L);
            processServiceList(rsOpSeaLclList, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            rsRct.setRsOpSeaLclList(rsOpSeaLclList);
        });

        // 空运 - 10L
        processors.put(10L, () -> {
            List<RsOpAir> rsOpAirList = rsOpAirMapper.selectRsOpAirByRctId(rctId, 10L);
            processServiceList(rsOpAirList, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            rsRct.setRsOpAirList(rsOpAirList);
        });

        // 整柜铁路 - 20L
        processors.put(20L, () -> {
            RsOpRailFCL rsOpRailFCL = rsOpRailMapper.selectRsOpRailFclByRctId(rctId, 20L);
            if (ObjectUtils.isNotEmpty(rsOpRailFCL)) {
                processService(rsOpRailFCL, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpRailFCL(rsOpRailFCL);
        });

        // 拼柜铁路 - 21L
        processors.put(21L, () -> {
            RsOpRailLCL rsOpRailLCL = rsOpRailMapper.selectRsOpRailLclByRctId(rctId, 21L);
            if (ObjectUtils.isNotEmpty(rsOpRailLCL)) {
                processService(rsOpRailLCL, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpRailLCL(rsOpRailLCL);
        });

        // 快递 - 40L
        processors.put(40L, () -> {
            RsOpExpress rsOpExpress = rsOpExpressMapper.selectRsOpExpressByRctId(rctId, 40L);
            if (ObjectUtils.isNotEmpty(rsOpExpress)) {
                processService(rsOpExpress, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpExpress(rsOpExpress);
        });

        // 整柜拖车 - 50L
        processors.put(50L, () -> {
            List<RsOpCtnrTruck> rsOpCtnrTruckList = rsOpTruckMapper.selectRsOpCtnrTruckByRctId(rctId, 50L);
            if (!rsOpCtnrTruckList.isEmpty()) {
                for (RsOpCtnrTruck truck : rsOpCtnrTruckList) {
                    Long serviceId = truck.getRsServiceInstances().getServiceId();
                    truck.setRsOpTruckList(rsOpTruckListMapper.selectRsOpTruckListByServiceId(serviceId));
                }
            }
            processServiceList(rsOpCtnrTruckList, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            rsRct.setRsOpCtnrTruckList(rsOpCtnrTruckList);
        });

        // 散货拖车 - 51L
        processors.put(51L, () -> {
            List<RsOpBulkTruck> rsOpBulkTruckList = rsOpTruckMapper.selectRsOpBulkTruckByRctId(rctId, 51L);
            if (!rsOpBulkTruckList.isEmpty()) {
                for (RsOpBulkTruck truck : rsOpBulkTruckList) {
                    Long serviceId = truck.getRsServiceInstances().getServiceId();
                    truck.setRsOpTruckList(rsOpTruckListMapper.selectRsOpTruckListByServiceId(serviceId));
                }
            }
            processServiceList(rsOpBulkTruckList, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            rsRct.setRsOpBulkTruckList(rsOpBulkTruckList);
        });

        // 单证报关 - 60L
        processors.put(60L, () -> {
            List<RsOpDocDeclare> rsOpDocDeclareList = rsOpExportCustomsClearanceMapper.selectRsOpDocDeclareByRctId(rctId, 60L);
            processServiceList(rsOpDocDeclareList, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            rsRct.setRsOpDocDeclareList(rsOpDocDeclareList);
        });

        // 全包报关 - 61L
        processors.put(61L, () -> {
            List<RsOpFreeDeclare> rsOpFreeDeclareList = rsOpExportCustomsClearanceMapper.selectRsOpFreeDeclareByRctId(rctId, 61L);
            processServiceList(rsOpFreeDeclareList, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            rsRct.setRsOpFreeDeclareList(rsOpFreeDeclareList);
        });

        // 代理放单 - 70L
        processors.put(70L, () -> {
            RsOpDOAgent rsOpDOAgent = rsOpImportCustomsClearanceMapper.selectRsOpDOAgentByRctId(rctId, 70L);
            if (ObjectUtils.isNotEmpty(rsOpDOAgent)) {
                processService(rsOpDOAgent, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpDOAgent(rsOpDOAgent);
        });

        // 代理清关 - 71L
        processors.put(71L, () -> {
            RsOpClearAgent rsOpClearAgent = rsOpImportCustomsClearanceMapper.selectRsOpClearAgentByRctId(rctId, 71L);
            if (ObjectUtils.isNotEmpty(rsOpClearAgent)) {
                processService(rsOpClearAgent, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpClearAgent(rsOpClearAgent);
        });

        // 码头与仓储 - 80L
        processors.put(80L, () -> {
            RsOpWHS rsOpWHS = rsOpWarehouseMapper.selectRsOpWHSByRctId(rctId, 80L);
            if (ObjectUtils.isNotEmpty(rsOpWHS)) {
                processService(rsOpWHS, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpWHS(rsOpWHS);
        });

        // 扩展服务 - 90L-104L
        addExpandServiceProcessors(processors, rsRct, chargeMap, logMap, debitNodeMap);

        return processors;
    }

    /**
     * 添加扩展服务处理器
     */
    private void addExpandServiceProcessors(Map<Long, ServiceProcessor> processors, RsRct rsRct,
                                            Map<Long, List<RsCharge>> chargeMap,
                                            Map<Long, List<RsOpLog>> logMap,
                                            Map<Long, List<RsDebitNote>> debitNodeMap) {
        Long rctId = rsRct.getRctId();

        // 第三方认证 - 90L
        processors.put(90L, () -> {
            RsOp3rdCert rsOp3rdCert = rsOpExpandServiceMapper.selectRsOp3rdCertByRctId(rctId, 90L);
            if (ObjectUtils.isNotEmpty(rsOp3rdCert)) {
                processService(rsOp3rdCert, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOp3rdCert(rsOp3rdCert);
        });

        // 保险 - 100L
        processors.put(100L, () -> {
            RsOpINS rsOpINS = rsOpExpandServiceMapper.selectRsOpINSByRctId(rctId, 100L);
            if (ObjectUtils.isNotEmpty(rsOpINS)) {
                processService(rsOpINS, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpINS(rsOpINS);
        });

        // 贸易 - 101L
        processors.put(101L, () -> {
            RsOpTrading rsOpTrading = rsOpExpandServiceMapper.selectRsOpTradingByRctId(rctId, 101L);
            if (ObjectUtils.isNotEmpty(rsOpTrading)) {
                processService(rsOpTrading, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpTrading(rsOpTrading);
        });

        // 熏蒸 - 102L
        processors.put(102L, () -> {
            RsOpFumigation rsOpFumigation = rsOpExpandServiceMapper.selectRsOpFumigationByRctId(rctId, 102L);
            if (ObjectUtils.isNotEmpty(rsOpFumigation)) {
                processService(rsOpFumigation, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpFumigation(rsOpFumigation);
        });

        // 原产地证 - 103L
        processors.put(103L, () -> {
            RsOpCO rsOpCO = rsOpExpandServiceMapper.selectRsOpCOByRctId(rctId, 103L);
            if (ObjectUtils.isNotEmpty(rsOpCO)) {
                processService(rsOpCO, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpCO(rsOpCO);
        });

        // 其他 - 104L
        processors.put(104L, () -> {
            RsOpOther rsOpOther = rsOpExpandServiceMapper.selectRsOpOtherByRctId(rctId, 104L);
            if (ObjectUtils.isNotEmpty(rsOpOther)) {
                processService(rsOpOther, rsRct.getSupplierIds(), chargeMap, logMap, debitNodeMap);
            }
            rsRct.setRsOpOther(rsOpOther);
        });
    }

    /**
     * 处理单个服务实例
     */
    private void processService(Object service, Set<Long> supplierIds,
                                Map<Long, List<RsCharge>> chargeMap,
                                Map<Long, List<RsOpLog>> logMap,
                                Map<Long, List<RsDebitNote>> debitNodeMap) {
        try {
            // 使用反射获取服务实例对象属性和方法
            Method getRsServiceInstancesMethod = service.getClass().getMethod("getRsServiceInstances");
            Method setRsChargeListMethod = service.getClass().getMethod("setRsChargeList", List.class);
            Method setRsOpLogListMethod = service.getClass().getMethod("setRsOpLogList", List.class);
            Method setRsDebitNoteListMethod = service.getClass().getMethod("setRsDebitNoteList", List.class);

            RsServiceInstances serviceInstances = (RsServiceInstances) getRsServiceInstancesMethod.invoke(service);
            if (serviceInstances == null) {
                return;
            }

            // 初始化列表
            setRsChargeListMethod.invoke(service, new ArrayList<>());
            setRsOpLogListMethod.invoke(service, new ArrayList<>());
            setRsDebitNoteListMethod.invoke(service, new ArrayList<>());

            Long serviceId = serviceInstances.getServiceId();

            // 使用预先分组的数据，避免重复遍历
            // 注意：使用 new ArrayList<>() 而不是 Collections.emptyList()，确保返回的列表是可修改的
            List<RsCharge> charges = chargeMap.get(serviceId);
            setRsChargeListMethod.invoke(service, charges != null ? new ArrayList<>(charges) : new ArrayList<>());

            List<RsOpLog> logs = logMap.get(serviceId);
            setRsOpLogListMethod.invoke(service, logs != null ? new ArrayList<>(logs) : new ArrayList<>());

            List<RsDebitNote> debitNotes = debitNodeMap.get(serviceId);
            setRsDebitNoteListMethod.invoke(service, debitNotes != null ? new ArrayList<>(debitNotes) : new ArrayList<>());


            // 收集供应商ID
            Long supplierId = serviceInstances.getSupplierId();
            if (supplierId != null) {
                supplierIds.add(supplierId);
            }
        } catch (Exception e) {
            // 反射调用失败时记录日志，但不中断程序
            e.printStackTrace();
        }
    }

    /**
     * 处理服务实例列表
     */
    private void processServiceList(List<?> serviceList, Set<Long> supplierIds,
                                    Map<Long, List<RsCharge>> chargeMap,
                                    Map<Long, List<RsOpLog>> logMap,
                                    Map<Long, List<RsDebitNote>> debitNodeMap) {
        if (serviceList == null || serviceList.isEmpty()) {
            return;
        }

        for (Object service : serviceList) {
            processService(service, supplierIds, chargeMap, logMap, debitNodeMap);
        }
    }

    /**
     * 处理列表类型的服务
     */
    private void processListServices(RsRct rsRct) {
        // 海运整箱
        if (rsRct.getRsOpSeaFclList() != null && !rsRct.getRsOpSeaFclList().isEmpty()) {
            saveSeaFcl(rsRct);
        }

        // 海运拼箱
        if (rsRct.getRsOpSeaLclList() != null && !rsRct.getRsOpSeaLclList().isEmpty()) {
            saveSeaLcl(rsRct);
        }

        // 空运
        if (rsRct.getRsOpAirList() != null && !rsRct.getRsOpAirList().isEmpty()) {
            saveAir(rsRct);
        }

        // 集装箱卡车
        if (rsRct.getRsOpCtnrTruckList() != null && !rsRct.getRsOpCtnrTruckList().isEmpty()) {
            saveCtnrTruck(rsRct);
        }

        // 散货拖车
        if (rsRct.getRsOpBulkTruckList() != null && !rsRct.getRsOpBulkTruckList().isEmpty()) {
            saveBulkTruck(rsRct);
        }

        // 自由报关
        if (rsRct.getRsOpFreeDeclareList() != null && !rsRct.getRsOpFreeDeclareList().isEmpty()) {
            saveFreeDeclare(rsRct);
        }

        // 单证报关
        if (rsRct.getRsOpDocDeclareList() != null && !rsRct.getRsOpDocDeclareList().isEmpty()) {
            saveDocDeclare(rsRct);
        }
    }

    /**
     * 服务处理器接口
     */
    @FunctionalInterface
    private interface ServiceProcessor {
        void process();
    }

    /**
     * 查询未审核的订舱单
     *
     * @param rsRct 操作单
     * @return 操作单
     */
    @Override
    public List<RsRct> selectUnVerifyRsRctList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        rsRct.setUserId(SecurityUtils.getUserId());
        startPage();
        return rsRctMapper.selectUnVerifyRsRctList(rsRct);
    }

    @Override
    public List<RsRct> selectUnVerifyExportRsRctList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        rsRct.setUserId(SecurityUtils.getUserId());
        return rsRctMapper.selectUnVerifyRsRctList(rsRct);
    }

    @Override
    public int opNotification(Long opId) {
        return rsRctMapper.opNotification(opId);
    }

    @Override
    public int psaNotification() {
        return rsRctMapper.psaNotification();
    }


    @Override
    public List<RsRct> selectRsRctList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        startPage();
        return rsRctMapper.selectRsRctList(rsRct);
    }

    /**
     * 新增操作单
     *
     * @param rsRct 操作单
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertRsRct(RsRct rsRct) {
        rsRct.setCreateTime(DateUtils.getNowDate());
        rsRct.setCreateBy(SecurityUtils.getUserId());

        rsRct.setServiceTypeIdList(StringUtils.join(rsRct.getServiceTypeIds(), ","));

        // 委托单位概要
        if (rsRct.getClientId() != null) {
            ExtCompany extCompany = extCompanyMapper.selectExtCompanyByCompanyId(rsRct.getClientId());
            rsRct.setClientSummary(extCompany.getCompanyTaxCode() + '/' + ((extCompany.getCompanyShortName() != null && !extCompany.getCompanyShortName().isEmpty()) ? extCompany.getCompanyShortName() : extCompany.getCompanyEnShortName()) + '/' + extCompany.getCompanyLocalName());
        }

        rsRctMapper.insertRsRct(rsRct);
        // 插入货物类型（普货、食品等）到中间表mid_cargo_type
        if (rsRct.getCargoTypeIds() != null && rsRct.getCargoTypeIds().length > 0) {
            insertCargoType(rsRct);
        }
        // 插入承运人（CMA、MSC等）到mid_carriers
        if (rsRct.getCarrierIds() != null && rsRct.getCarrierIds().length > 0) {
            insertCarriers(rsRct);
        }
        // 插入服务类型(海运、空运等)到mid_service_type
        if (rsRct.getServiceTypeIds() != null && rsRct.getServiceTypeIds().length > 0) {
            insertServiceType(rsRct);
        }

        // 插入提单信息列表
        List<RsBookingMessage> bookingMessagesList = rsRct.getBookingMessagesList();
        if (bookingMessagesList != null && !bookingMessagesList.isEmpty()) {
            for (RsBookingMessage rsBookingMessage : bookingMessagesList) {
                rsBookingMessageMapper.insertRsBookingMessage(rsBookingMessage);
            }
        }

        return rsRct.getRctId();
    }

    /**
     * 修改操作单
     *
     * @param rsRct 操作单
     * @return 结果
     */
    @Override
    public int updateRsRct(RsRct rsRct) {
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setPropagationBehaviorName("PROPAGATION_REQUIRED");
        TransactionStatus transaction = transactionManager.getTransaction(definition);

        if (rsRct.getRctId() == null) {
            throw new RuntimeException("更新异常");
        }
        rsRct.setUpdateTime(DateUtils.getNowDate());
        rsRct.setUpdateBy(SecurityUtils.getUserId());

        rsRct.setServiceTypeIdList(StringUtils.join(rsRct.getServiceTypeIds(), ","));

        // 委托单位概要
        if (rsRct.getClientId() != null) {
            ExtCompany extCompany = extCompanyMapper.selectExtCompanyByCompanyId(rsRct.getClientId());
            rsRct.setClientSummary(extCompany.getCompanyTaxCode() + '/' + ((extCompany.getCompanyShortName() != null && !extCompany.getCompanyShortName().isEmpty()) ? extCompany.getCompanyShortName() : extCompany.getCompanyEnShortName()) + '/' + extCompany.getCompanyLocalName());
        }

/*        // 计算费用
        if (rsRct.getQuotationRmbSumVat() != null && rsRct.getInquiryCnRmbSumVat() != null) {

        }*/

        try {
            midServiceTypeMapper.deleteMidServiceTypeById(rsRct.getRctId(), "rct");
            if (rsRct.getServiceTypeIds() != null && rsRct.getServiceTypeIds().length > 0) {
                insertServiceType(rsRct);
            }
            midCarrierMapper.deleteMidCarrierById(rsRct.getRctId(), "rct");
            midCargoTypeMapper.deleteMidCargoTypeById(rsRct.getRctId(), "rct");
            if (rsRct.getCargoTypeIds() != null && rsRct.getCargoTypeIds().length > 0) {
                insertCargoType(rsRct);
            }
            if (rsRct.getCarrierIds() != null && rsRct.getCarrierIds().length > 0) {
                insertCarriers(rsRct);
            }

            rsBookingMessageMapper.deleteRsBookingMessageByRctId(rsRct.getRctId());
            List<RsBookingMessage> bookingMessagesList = rsRct.getBookingMessagesList();
            if (bookingMessagesList != null && !bookingMessagesList.isEmpty()) {
                for (RsBookingMessage rsBookingMessage : bookingMessagesList) {
                    rsBookingMessageMapper.insertRsBookingMessage(rsBookingMessage);
                }
            }

            if (rsRct.getOpAskingBlGetTime() != null) {
                rsRct.setDocStatusA("期望赎单:" + rsRct.getOpAskingBlGetTime());
            }
            if (rsRct.getOpAskingBlReleaseTime() != null) {
                rsRct.setDocStatusA("期望放单:" + rsRct.getOpAskingBlReleaseTime());
            }
            if (rsRct.getAccPromissBlGetTime() != null) {
                rsRct.setDocStatusA("预计赎单:" + rsRct.getAccPromissBlGetTime());
            }
            if (rsRct.getActualBlGotTime() != null) {
                rsRct.setDocStatusA("提单赎回:" + rsRct.getActualBlGotTime());
            }
            if (rsRct.getAccPromissBlReleaseTime() != null) {
                rsRct.setDocStatusA("预计放单:" + rsRct.getAccPromissBlReleaseTime());
            }
            if (rsRct.getActualBlReleaseTime() != null) {
                rsRct.setDocStatusA("已放提单:" + rsRct.getActualBlReleaseTime());
            }

            if (rsRct.getAgentNoticeTime() != null) {
                rsRct.setDocStatusB("已发代理:" + rsRct.getAgentNoticeTime());
            }

            transactionManager.commit(transaction);
        } catch (Exception ex) {
            transactionManager.rollback(transaction);
            throw ex;
        }

        return rsRctMapper.updateRsRct(rsRct);
    }


    /**
     * 修改操作单状态
     *
     * @param rsRct 操作单
     * @return 操作单
     */
    @Override
    public int changeStatus(RsRct rsRct) {
        return rsRctMapper.updateRsRct(rsRct);
    }

    /**
     * 批量删除操作单
     *
     * @param rctIds 需要删除的操作单主键
     * @return 结果
     */
    @Override
    public int deleteRsRctByRctIds(Long[] rctIds) {
        return rsRctMapper.deleteRsRctByRctIds(rctIds);
    }

    /**
     * 删除操作单信息
     *
     * @param rctId 操作单主键
     * @return 结果
     */
    @Override
    public int deleteRsRctByRctId(Long rctId) {
        return rsRctMapper.deleteRsRctByRctId(rctId);
    }

    private static void resetCharge(List<RsCharge> rsChargeList) {
        for (RsCharge rsCharge : rsChargeList) {
            rsCharge.setServiceId(null);
            rsCharge.setSqdRctId(null);
            rsCharge.setSqdServiceTypeId(null);
            rsCharge.setChargeId(null);
            rsCharge.setIsAccountConfirmed("0");
            rsCharge.setAccountConfirmTime(null);
            rsCharge.setChargeRemark(null);
            rsCharge.setBankRecordId(null);
            rsCharge.setDnCurrencyPaid(BigDecimal.ZERO);
            rsCharge.setDnCurrencyBalance(rsCharge.getSubtotal());
            rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
            rsCharge.setSqdDnCurrencyPaid(BigDecimal.ZERO);
            rsCharge.setDnCurrencyReceived(BigDecimal.ZERO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveBasicLogistics(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "logistics");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsBasicLogistics().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsBasicLogistics().getRsServiceInstances());
            rsRct.getRsBasicLogistics().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 基础物流信息
        Long serviceInstanceId = rsRct.getRsBasicLogistics().getRsServiceInstances().getServiceId();
        RsBasicLogistics rsBasicLogistics = rsBasicLogisticsMapper.selectRsBasicLogistics(serviceInstanceId);
        rsRct.getRsBasicLogistics().setServiceId(serviceInstanceId);
        rsRct.getRsBasicLogistics().setSqdRctNo(rsRct.getRctNo());
        if (rsBasicLogistics != null) {
            out += rsBasicLogisticsMapper.updateRsBasicLogistics(rsRct.getRsBasicLogistics());
        } else {
            out += rsBasicLogisticsMapper.insertRsBasicLogistics(rsRct.getRsBasicLogistics());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsBasicLogistics().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int savePreCarriage(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "pre-carriage");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsPrecarriage().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsPrecarriage().getRsServiceInstances());
            rsRct.getRsPrecarriage().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 前程运输信息
        Long serviceInstanceId = rsRct.getRsPrecarriage().getRsServiceInstances().getServiceId();
        RsPrecarriage rsPrecarriage = rsPrecarriageMapper.selectRsPrecarriage(serviceInstanceId);
        rsRct.getRsPrecarriage().setServiceId(serviceInstanceId);
        rsRct.getRsPrecarriage().setSqdRctNo(rsRct.getRctNo());
        if (rsPrecarriage != null) {
            out += rsPrecarriageMapper.updateRsPrecarriage(rsRct.getRsPrecarriage());
        } else {
            out += rsPrecarriageMapper.insertRsPrecarriage(rsRct.getRsPrecarriage());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsPrecarriage().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveExportDeclaration(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "export-declaration");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsExportCustoms().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsExportCustoms().getRsServiceInstances());
            rsRct.getRsExportCustoms().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 出口报关信息
        Long serviceInstanceId = rsRct.getRsExportCustoms().getRsServiceInstances().getServiceId();
        RsExportCustoms rsExportCustoms = rsExportCustomsMapper.selectRsExportCustoms(serviceInstanceId);
        rsRct.getRsExportCustoms().setServiceId(serviceInstanceId);
        rsRct.getRsExportCustoms().setSqdRctNo(rsRct.getRctNo());
        if (rsExportCustoms != null) {
            out += rsExportCustomsMapper.updateRsExportCustoms(rsRct.getRsExportCustoms());
        } else {
            out += rsExportCustomsMapper.insertRsExportCustoms(rsRct.getRsExportCustoms());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsExportCustoms().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveImportClearance(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "import-clearance");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsImportCustoms().getRsServiceInstances());
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsImportCustoms().getRsServiceInstances());
            rsRct.getRsImportCustoms().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }


        // 基础物流信息
        Long serviceInstanceId = rsRct.getRsImportCustoms().getRsServiceInstances().getServiceId();
        RsImportCustoms rsImportCustoms = rsImportCustomsMapper.selectRsImportCustoms(serviceInstanceId);
        rsRct.getRsImportCustoms().setServiceId(serviceInstanceId);
        rsRct.getRsImportCustoms().setSqdRctNo(rsRct.getRctNo());
        if (rsImportCustoms != null) {
            out += rsImportCustomsMapper.updateRsImportCustoms(rsRct.getRsImportCustoms());
        } else {
            out += rsImportCustomsMapper.insertRsImportCustoms(rsRct.getRsImportCustoms());
        }

        // 文件信息

        // 物流进度

        // 费用信息
        out += rsChargeMapper.deleteRsCharge(serviceInstanceId);
        List<RsCharge> rsCharges = rsRct.getRsImportCustoms().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            out += rsChargeMapper.insertRsCharge(rsCharge);
        }
        return out;
    }

    @Override
    public int getMon() {
        return rsRctMapper.getMon();
    }

    private Map<String, List<?>> queryRctList(RsRct rsRct) {
        List<List<Long>> lists = new ArrayList<>();
        List<BasDistLocation> basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        if (basDistLocations == null) {
            RedisCache.location();
            basDistLocations = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "location");
        }
        List<BasDistLine> basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        if (basDistLines == null) {
            RedisCache.line();
            basDistLines = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "line");
        }
        Set<Long> lineDestination = new HashSet<>();
        if (rsRct.getLineIds() != null) {
            for (BasDistLine line : basDistLines) {
                String[] lineAncestors = line.getAncestors().split(",");
                if (ArrayUtils.contains(rsRct.getLineIds(), line.getLineId())) {
                    for (String a : lineAncestors) {
                        lineDestination.add(Convert.toLong(a));
                    }
                }
                if (SearchUtils.existSame(lineAncestors, rsRct.getLineIds())) {
                    lineDestination.add(line.getLineId());
                }
            }
        }
        // 启运目的区域id集合
        Set<Long> locationDeparture = new HashSet<>();
        // 目的区域id集合
        Set<Long> locationDestination = new HashSet<>();
        //启运港或目的港
        if (rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null) {
            // 遍历所有的区域(3885)
            for (BasDistLocation location : basDistLocations) {
                // 当前地名的父级
                String[] ancestors = location.getAncestors().split(",");
                // 启运区域id没有包含-1(亚洲、欧洲、南美洲、北美洲、非洲、大洋洲)
                if (rsRct.getPolIds() != null && !ArrayUtils.contains(rsRct.getPolIds(), -1L)) {
                    // 当前区域的id在搜索的id集合中
                    if (ArrayUtils.contains(rsRct.getPolIds(), location.getLocationId())) {
                        locationDeparture.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDeparture.add(Convert.toLong(a));
                        }
                    }
                    // 是否存在重复
                    if (SearchUtils.existSame(ancestors, rsRct.getPolIds())) {
                        locationDeparture.add(location.getLocationId());
                    }
                }
                // 目的港不为空且不为顶级区域(亚洲、欧洲、南美洲、北美洲、非洲、大洋洲)
                if (rsRct.getDestinationPortIds() != null && !ArrayUtils.contains(rsRct.getDestinationPortIds(), -1L)) {
                    if (ArrayUtils.contains(rsRct.getDestinationPortIds(), location.getLocationId())) {
                        locationDestination.add(location.getLocationId());
                        for (String a : ancestors) {
                            locationDestination.add(Convert.toLong(a));
                        }
                    }
                    if (SearchUtils.existSame(ancestors, rsRct.getDestinationPortIds())) {
                        locationDestination.add(location.getLocationId());
                    }
                    if (lineDestination.contains(location.getLineId())) {
                        locationDestination.add(location.getLocationId());
                    }
                }
            }
        }
        HashMap<String, List<?>> out = new HashMap<>();
        out.put("list", lists);
        out.put("locationDeparture", Arrays.asList(locationDeparture.toArray()));
        out.put("locationDestination", Arrays.asList(locationDestination.toArray()));
        return out;
    }

    public void insertCarriers(RsRct rsRct) {
        Long[] roles = rsRct.getCarrierIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCarrier> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCarrier midCarrier = new MidCarrier();
                midCarrier.setBelongId(rsRct.getRctId());
                midCarrier.setCarrierId(r);
                midCarrier.setBelongTo("rct");
                list.add(midCarrier);
            }
            midCarrierMapper.batchCarrier(list);
        }
        RedisCache.midCarrier("rct", "rctCarriers");
    }

    public void insertCargoType(RsRct rsRct) {
        Long[] roles = rsRct.getCargoTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidCargoType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidCargoType MidCargoType = new MidCargoType();
                MidCargoType.setBelongId(rsRct.getRctId());
                MidCargoType.setBelongTo("rct");
                MidCargoType.setCargoTypeId(r);
                list.add(MidCargoType);
            }
            midCargoTypeMapper.batchCargoType(list);
        }
        RedisCache.midCargoType("rct", "rctCargoType");
    }

    public void insertServiceType(RsRct rsRct) {
        Long[] roles = rsRct.getServiceTypeIds();
        if (StringUtils.isNotEmpty(roles)) {
            List<MidServiceType> list = new ArrayList<>(roles.length);
            for (Long r : roles) {
                MidServiceType MidServiceType = new MidServiceType();
                MidServiceType.setBelongId(rsRct.getRctId());
                MidServiceType.setServiceTypeId(r);
                MidServiceType.setBelongTo("rct");
                list.add(MidServiceType);
            }
            midServiceTypeMapper.batchServiceType(list);
        }
        RedisCache.midServiceType("rct", "rctServiceType");
    }

    @Override
    public List<Long> getCarrierIds(Long rctId) {
        return midCarrierMapper.selectMidCarrierById(rctId, "rct");
    }

    @Override
    public List<Long> getCargoTypeIds(Long rctId) {
        return midCargoTypeMapper.selectMidCargoTypeById(rctId, "rct");
    }

    @Override
    public List<Long> getServiceTypeIds(Long rctId) {
        return midServiceTypeMapper.selectMidServiceTypeById(rctId, "rct");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, timeout = 300)
    public RsRct saveAllServices(RsRct rsRct) {
        try {
            // 关联服务实例
            List<RsServiceInstances> rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstancesByRctId(rsRct.getRctId());
            List<String> serviceNames = rsServiceInstances.stream().map(RsServiceInstances::getServiceBelongTo).collect(Collectors.toList());
            List<Long> serviceTypeIds = rsServiceInstances.stream().map(RsServiceInstances::getServiceTypeId).collect(Collectors.toList());

            // 删除现有费用记录
            rsChargeMapper.deleteRsChargeByRctId(rsRct.getRctId());
            rsDebitNoteMapper.deleteRsDebitNoteByRctId(rsRct.getRctId());

            // 保存客户信息
            saveClientMessage(rsRct);

            // 处理列表类型的服务
            processListServices(rsRct);

            // 处理单个服务
            processSingleServices(rsRct, serviceNames, serviceTypeIds);

            // 处理拓展服务
            processExpandServices(rsRct, serviceNames, serviceTypeIds);

            log.info("成功保存操作单所有服务，操作单号: {}", rsRct.getRctNo());
            return rsRct;
        } catch (Exception e) {
            log.error("保存操作单服务失败，操作单号: {}，错误信息: {}", rsRct.getRctNo(), e.getMessage(), e);
            throw new RuntimeException("保存操作单服务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 基础服务接口，用于统一处理不同服务类型
     */
    private interface BaseService {
        RsServiceInstances getRsServiceInstances();

        List<RsCharge> getRsChargeList();

        void setRsChargeList(List<RsCharge> chargeList);

        List<RsOpLog> getRsOpLogList();

        void setRsOpLogList(List<RsOpLog> opLogList);
    }

    /**
     * 处理单个服务对象
     */
    private void processSingleServices(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        // 铁路整箱
        if (rsRct.getRsOpRailFCL() != null) {
            saveRailFCL(rsRct, serviceNames, serviceTypeIds);
        }

        // 铁路拼箱
        if (rsRct.getRsOpRailLCL() != null) {
            saveRailLCL(rsRct, serviceNames, serviceTypeIds);
        }

        // 快递
        if (rsRct.getRsOpExpress() != null) {
            saveExpress(rsRct, serviceNames, serviceTypeIds);
        }

        // DO代理
        if (rsRct.getRsOpDOAgent() != null) {
            saveDOAgent(rsRct, serviceNames, serviceTypeIds);
        }

        // 清关代理
        if (rsRct.getRsOpClearAgent() != null) {
            saveClearAgent(rsRct, serviceNames, serviceTypeIds);
        }

        // 仓库服务
        if (rsRct.getRsOpWHS() != null) {
            saveWHS(rsRct, serviceNames, serviceTypeIds);
        }
    }

    /**
     * 处理拓展服务
     */
    private void processExpandServices(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        // 第三方证书
        if (rsRct.getRsOp3rdCert() != null) {
            save3rdCert(rsRct, serviceNames, serviceTypeIds);
        }

        // 保险
        if (rsRct.getRsOpINS() != null) {
            saveINS(rsRct, serviceNames, serviceTypeIds);
        }

        // 贸易
        if (rsRct.getRsOpTrading() != null) {
            saveTrading(rsRct, serviceNames, serviceTypeIds);
        }

        // 熏蒸
        if (rsRct.getRsOpFumigation() != null) {
            saveFumigation(rsRct, serviceNames, serviceTypeIds);
        }

        // CO证书
        if (rsRct.getRsOpCO() != null) {
            saveCO(rsRct, serviceNames, serviceTypeIds);
        }

        // 其他服务
        if (rsRct.getRsOpOther() != null) {
            saveOther(rsRct, serviceNames, serviceTypeIds);
        }
    }

    public String getProcessStatus(Long processStatusId) {
        if (processStatusId.equals(1L)) {
            return "通过";
        }
        if (processStatusId.equals(2L)) {
            return "进行";
        }
        if (processStatusId.equals(4L)) {
            return "异常";
        }
        if (processStatusId.equals(6L)) {
            return "确认";
        }
        if (processStatusId.equals(7L)) {
            return "完成";
        }
        if (processStatusId.equals(8L)) {
            return "取消";
        }
        if (processStatusId.equals(9L)) {
            return "驳回";
        }
        return "";
    }

    @Override
    public List<RsRct> selectRsRctByCompanyId(Long companyId) {
        return rsRctMapper.selectRsRctByCompanyId(companyId);
    }

    @Override
    public int getCFMon() {
        return rsRctMapper.getCFMon();
    }

    @Override
    public Long getRctIdByRctNo(String rctNo) {
        return rsRctMapper.selectRctIdByRctNo(rctNo);
    }

    @Override
    public List<StatisticsOpDTO> statisticsOp() {
        return rsRctMapper.statisticsOp();
    }

    @Override
    public List<RsRct> selectRsRctListToExport(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        return rsRctMapper.selectRsRctList(rsRct);
    }

    @Override
    public int getRSWHMon() {
        return rsRctMapper.getRSWHMon();
    }

    /**
     * 第三方证书
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void save3rdCert(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOp3rdCert().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOp3rdCert().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("Cert") && serviceTypeIds.contains(90L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOp3rdCert().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOp3rdCert().setSqdServiceTypeId(90L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOp3rdCert());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOp3rdCert().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOp3rdCert().getRsChargeList(), rctId, 90L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOp3rdCert().getRsOpLogList(), rctId, 90L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("Cert");
            rsServiceInstance.setServiceTypeId(90L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOp3rdCert().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOp3rdCert().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOp3rdCert().setSqdServiceTypeId(90L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOp3rdCert());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOp3rdCert().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOp3rdCert().getRsChargeList(), rctId, 90L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOp3rdCert().getRsOpLogList(), rctId, 90L);
        }
    }

    /**
     * 保险
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveINS(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpINS().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpINS().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("INS") && serviceTypeIds.contains(100L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpINS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpINS().setSqdServiceTypeId(100L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpINS());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpINS().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpINS().getRsChargeList(), rctId, 100L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpINS().getRsOpLogList(), rctId, 100L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("INS");
            rsServiceInstance.setServiceTypeId(100L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpINS().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpINS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpINS().setSqdServiceTypeId(100L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpINS());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpINS().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpINS().getRsChargeList(), rctId, 100L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpINS().getRsOpLogList(), rctId, 100L);
        }
    }

    /**
     * 保存检验与证书服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveTrading(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpTrading().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpTrading().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("Trading") && serviceTypeIds.contains(101L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpTrading().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpTrading().setSqdServiceTypeId(101L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpTrading());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpTrading().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpTrading().getRsChargeList(), rctId, 101L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpTrading().getRsOpLogList(), rctId, 101L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("Trading");
            rsServiceInstance.setServiceTypeId(101L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpTrading().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpTrading().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpTrading().setSqdServiceTypeId(101L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpTrading());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpTrading().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpTrading().getRsChargeList(), rctId, 101L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpTrading().getRsOpLogList(), rctId, 101L);
        }
    }

    /**
     * 保存检验与证书服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveFumigation(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpFumigation().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpFumigation().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("Fumigation") && serviceTypeIds.contains(102L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpFumigation().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpFumigation().setSqdServiceTypeId(102L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpFumigation());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpFumigation().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpFumigation().getRsChargeList(), rctId, 102L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpFumigation().getRsOpLogList(), rctId, 102L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("Fumigation");
            rsServiceInstance.setServiceTypeId(102L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpFumigation().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpFumigation().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpFumigation().setSqdServiceTypeId(102L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpFumigation());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpFumigation().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpFumigation().getRsChargeList(), rctId, 102L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpFumigation().getRsOpLogList(), rctId, 102L);
        }
    }

    /**
     * 保存检验与证书服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveCO(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpCO().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpCO().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("CO") && serviceTypeIds.contains(103L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpCO().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpCO().setSqdServiceTypeId(103L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpCO());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpCO().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpCO().getRsChargeList(), rctId, 103L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpCO().getRsOpLogList(), rctId, 103L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("CO");
            rsServiceInstance.setServiceTypeId(103L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpCO().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpCO().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpCO().setSqdServiceTypeId(103L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpCO());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpCO().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpCO().getRsChargeList(), rctId, 103L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpCO().getRsOpLogList(), rctId, 103L);
        }
    }


    /**
     * 保存其他服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveOther(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpOther().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpOther().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("报销") && serviceTypeIds.contains(104L)) {
            // 更新服务实例
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpOther().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpOther().setSqdServiceTypeId(104L);
            rsOpExpandServiceMapper.updateRsOpExpandService(rsRct.getRsOpOther());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpOther().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpOther().getRsChargeList(), rctId, 104L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpOther().getRsOpLogList(), rctId, 104L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("报销");
            rsServiceInstance.setServiceTypeId(104L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpOther().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpOther().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpOther().setSqdServiceTypeId(104L);
            rsOpExpandServiceMapper.insertRsOpExpandService(rsRct.getRsOpOther());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpOther().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpOther().getRsChargeList(), rctId, 104L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpOther().getRsOpLogList(), rctId, 104L);
        }
    }


    /**
     * 保存清关服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveDOAgent(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpDOAgent().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpDOAgent().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("DOAgent") && serviceTypeIds.contains(70L)) {
            // 更新服务实例
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpDOAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpDOAgent().setSqdServiceTypeId(70L);
            rsOpImportCustomsClearanceMapper.updateRsOpImportCustomsClearance(rsRct.getRsOpDOAgent());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpDOAgent().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpDOAgent().getRsChargeList(), rctId, 70L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpDOAgent().getRsOpLogList(), rctId, 70L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("DOAgent");
            rsServiceInstance.setServiceTypeId(70L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.insertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpDOAgent().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpDOAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpDOAgent().setSqdServiceTypeId(70L);
            rsOpImportCustomsClearanceMapper.insertRsOpImportCustomsClearance(rsRct.getRsOpDOAgent());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpDOAgent().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpDOAgent().getRsChargeList(), rctId, 70L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpDOAgent().getRsOpLogList(), rctId, 70L);
        }
    }

    /**
     * 保存清关服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveClearAgent(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpClearAgent().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpClearAgent().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("ClearAgent") && serviceTypeIds.contains(71L)) {
            // 更新服务实例
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpClearAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpClearAgent().setSqdServiceTypeId(71L);
            rsOpImportCustomsClearanceMapper.updateRsOpImportCustomsClearance(rsRct.getRsOpClearAgent());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpClearAgent().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpClearAgent().getRsChargeList(), rctId, 71L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpClearAgent().getRsOpLogList(), rctId, 71L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("ClearAgent");
            rsServiceInstance.setServiceTypeId(71L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpClearAgent().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpClearAgent().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpClearAgent().setSqdServiceTypeId(71L);
            rsOpImportCustomsClearanceMapper.insertRsOpImportCustomsClearance(rsRct.getRsOpClearAgent());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpClearAgent().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpClearAgent().getRsChargeList(), rctId, 71L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpClearAgent().getRsOpLogList(), rctId, 71L);
        }
    }

    /**
     * 报关服务信息
     *
     * @param rsRct
     */
    public void saveDocDeclare(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpDocDeclare> existingList = rsOpExportCustomsClearanceMapper.selectRsOpDocDeclareByRctId(rctId, 60L);
        List<RsOpDocDeclare> newList = rsRct.getRsOpDocDeclareList();

        List<RsOpDocDeclare> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getExportCustomsClearanceId() != null &&
                                newItem.getExportCustomsClearanceId().equals(existing.getExportCustomsClearanceId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpDocDeclare rsOpDocDeclare : toDeleteServiceObject) {
                rsOpExportCustomsClearanceMapper.deleteRsOpExportCustomsClearanceByExportCustomsClearanceId(rsOpDocDeclare.getExportCustomsClearanceId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpDocDeclare.getRsServiceInstances().getServiceId());
                if (rsOpDocDeclare.getRsChargeList() != null && !rsOpDocDeclare.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpDocDeclare.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpDocDeclare rsOpDocDeclare : rsRct.getRsOpDocDeclareList()) {
            RsServiceInstances rsServiceInstance = rsOpDocDeclare.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("DocDeclare");
            rsServiceInstance.setServiceTypeId(60L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpDocDeclare.setServiceId(rsServiceInstance.getServiceId());
            rsOpDocDeclare.setSqdRctNo(rsRct.getRctNo());
            rsOpDocDeclare.setSqdServiceTypeId(60L);
            rsOpExportCustomsClearanceMapper.upsertRsOpExportCustomsClearance(rsOpDocDeclare);

            updateRsDebitNoteList(rsServiceInstance, rsOpDocDeclare.getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsOpDocDeclare.getRsChargeList(), rctId, 60L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpDocDeclare.getRsOpLogList(), rctId, 60L);

        }
    }

    /**
     * 报关服务信息
     *
     * @param rsRct
     */
    public void saveFreeDeclare(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpFreeDeclare> existingList = rsOpExportCustomsClearanceMapper.selectRsOpFreeDeclareByRctId(rctId, 61L);
        List<RsOpFreeDeclare> newList = rsRct.getRsOpFreeDeclareList();

        List<RsOpFreeDeclare> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getExportCustomsClearanceId() != null &&
                                newItem.getExportCustomsClearanceId().equals(existing.getExportCustomsClearanceId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpFreeDeclare rsOpFreeDeclare : toDeleteServiceObject) {
                rsOpExportCustomsClearanceMapper.deleteRsOpExportCustomsClearanceByExportCustomsClearanceId(rsOpFreeDeclare.getExportCustomsClearanceId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpFreeDeclare.getRsServiceInstances().getServiceId());
                if (rsOpFreeDeclare.getRsChargeList() != null && !rsOpFreeDeclare.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpFreeDeclare.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpFreeDeclare rsOpFreeDeclare : rsRct.getRsOpFreeDeclareList()) {
            RsServiceInstances rsServiceInstance = rsOpFreeDeclare.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("FreeDeclare");
            rsServiceInstance.setServiceTypeId(61L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpFreeDeclare.setServiceId(rsServiceInstance.getServiceId());
            rsOpFreeDeclare.setSqdRctNo(rsRct.getRctNo());
            rsOpFreeDeclare.setSqdServiceTypeId(61L);
            rsOpExportCustomsClearanceMapper.upsertRsOpExportCustomsClearance(rsOpFreeDeclare);

            updateRsDebitNoteList(rsServiceInstance, rsOpFreeDeclare.getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsOpFreeDeclare.getRsChargeList(), rctId, 61L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpFreeDeclare.getRsOpLogList(), rctId, 61L);

        }

    }

    /**
     * 保存拖车服务信息
     *
     * @param rsRct
     */
    public void saveCtnrTruck(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpCtnrTruck> existingList = rsOpTruckMapper.selectRsOpCtnrTruckByRctId(rctId, 50L);
        List<RsOpCtnrTruck> newList = rsRct.getRsOpCtnrTruckList();

        List<RsOpCtnrTruck> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getTruckId() != null &&
                                newItem.getTruckId().equals(existing.getTruckId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpCtnrTruck rsOpCtnrTruck : toDeleteServiceObject) {
                rsOpTruckMapper.deleteRsOpTruckByTruckId(rsOpCtnrTruck.getTruckId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpCtnrTruck.getRsServiceInstances().getServiceId());
                if (rsOpCtnrTruck.getRsChargeList() != null && !rsOpCtnrTruck.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpCtnrTruck.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }
        rsOpTruckListMapper.deleteRsOpTruckListByRctNo(rsRct.getRctNo());

        for (RsOpCtnrTruck rsOpCtnrTruck : rsRct.getRsOpCtnrTruckList()) {
            RsServiceInstances rsServiceInstance = rsOpCtnrTruck.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("CtnrTruck");
            rsServiceInstance.setServiceTypeId(50L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpCtnrTruck.setServiceId(rsServiceInstance.getServiceId());
            rsOpCtnrTruck.setSqdRctNo(rsRct.getRctNo());
            rsOpCtnrTruck.setRctId(rctId);
            rsOpCtnrTruck.setSqdServiceTypeId(50L);
            rsOpTruckMapper.upsertRsOpCtnrTruck(rsOpCtnrTruck);

            List<RsOpTruckList> rsOpTruckList = rsOpCtnrTruck.getRsOpTruckList();
            for (RsOpTruckList opTruckListItem : rsOpTruckList) {
                opTruckListItem.setServiceId(rsServiceInstance.getServiceId());
                opTruckListItem.setSqdRctNo(rsRct.getRctNo());
                opTruckListItem.setSqdServiceTypeId(50L);
                rsOpTruckListMapper.insertRsOpTruckList(opTruckListItem);
            }

            updateRsDebitNoteList(rsServiceInstance, rsOpCtnrTruck.getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsOpCtnrTruck.getRsChargeList(), rctId, 50L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpCtnrTruck.getRsOpLogList(), rctId, 50L);

        }

    }

    /**
     * 保存拖车服务信息
     *
     * @param rsRct
     */
    public void saveBulkTruck(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpBulkTruck> existingList = rsOpTruckMapper.selectRsOpBulkTruckByRctId(rctId, 51L);
        List<RsOpBulkTruck> newList = rsRct.getRsOpBulkTruckList();

        List<RsOpBulkTruck> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getTruckId() != null &&
                                newItem.getTruckId().equals(existing.getTruckId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpBulkTruck rsOpBulkTruck : toDeleteServiceObject) {
                rsOpTruckMapper.deleteRsOpTruckByTruckId(rsOpBulkTruck.getTruckId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpBulkTruck.getRsServiceInstances().getServiceId());
                if (rsOpBulkTruck.getRsChargeList() != null && !rsOpBulkTruck.getRsChargeList().isEmpty())
                    for (RsCharge rsCharge : rsOpBulkTruck.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
            }
        }
        rsOpTruckListMapper.deleteRsOpTruckListByRctNo(rsRct.getRctNo());

        for (RsOpBulkTruck rsOpBulkTruck : rsRct.getRsOpBulkTruckList()) {
            RsServiceInstances rsServiceInstance = rsOpBulkTruck.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("BulkTruck");
            rsServiceInstance.setServiceTypeId(51L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpBulkTruck.setServiceId(rsServiceInstance.getServiceId());
            rsOpBulkTruck.setSqdRctNo(rsRct.getRctNo());
            rsOpBulkTruck.setSqdServiceTypeId(51L);
            rsOpBulkTruck.setRctId(rctId);
            rsOpTruckMapper.upsertRsOpBulkTruck(rsOpBulkTruck);

            List<RsOpTruckList> rsOpTruckLists = rsOpBulkTruck.getRsOpTruckList();
            for (RsOpTruckList rsOpTruckListItem : rsOpTruckLists) {
                rsOpTruckListItem.setServiceId(rsServiceInstance.getServiceId());
                rsOpTruckListItem.setSqdRctNo(rsRct.getRctNo());
                rsOpTruckListItem.setSqdServiceTypeId(51L);
                rsOpTruckListMapper.insertRsOpTruckList(rsOpTruckListItem);
            }

            updateRsDebitNoteList(rsServiceInstance, rsOpBulkTruck.getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsOpBulkTruck.getRsChargeList(), rctId, 51L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpBulkTruck.getRsOpLogList(), rctId, 51L);
        }
    }

    /**
     * 保存码头与仓储服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveWHS(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpWHS().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpWHS().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("WHS") && serviceTypeIds.contains(80L)) {
            // 更新服务实例
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpWHS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpWHS().setSqdServiceTypeId(80L);
            rsOpWarehouseMapper.updateRsOpWarehouse(rsRct.getRsOpWHS());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpWHS().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpWHS().getRsChargeList(), rctId, 80L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpWHS().getRsOpLogList(), rctId, 80L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("WHS");
            rsServiceInstance.setServiceTypeId(80L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpWHS().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpWHS().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpWHS().setSqdServiceTypeId(80L);
            rsOpWarehouseMapper.insertRsOpWarehouse(rsRct.getRsOpWHS());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpWHS().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpWHS().getRsChargeList(), rctId, 80L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpWHS().getRsOpLogList(), rctId, 80L);
        }
    }

    /**
     * 保存快递服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveExpress(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpExpress().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpExpress().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("EXPRESS") && serviceTypeIds.contains(40L)) {
            // 更新服务实例
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpExpress().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpExpress().setSqdServiceTypeId(40L);
            rsOpExpressMapper.updateRsOpExpress(rsRct.getRsOpExpress());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpExpress().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpExpress().getRsChargeList(), rctId, 40L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpExpress().getRsOpLogList(), rctId, 40L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("EXPRESS");
            rsServiceInstance.setServiceTypeId(40L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpExpress().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpExpress().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpExpress().setSqdServiceTypeId(40L);
            rsOpExpressMapper.insertRsOpExpress(rsRct.getRsOpExpress());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpExpress().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpExpress().getRsChargeList(), rctId, 40L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpExpress().getRsOpLogList(), rctId, 40L);
        }
    }

    /**
     * 保存铁路服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveRailFCL(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpRailFCL().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpRailFCL().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("RailFCL") && serviceTypeIds.contains(20L)) {
            // 更新服务实例
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpRailFCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailFCL().setSqdServiceTypeId(20L);
            rsOpRailMapper.updateRsOpRail(rsRct.getRsOpRailFCL());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpRailFCL().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailFCL().getRsChargeList(), rctId, 20L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailFCL().getRsOpLogList(), rctId, 20L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("RailFCL");
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setServiceTypeId(20L);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpRailFCL().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpRailFCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailFCL().setSqdServiceTypeId(20L);
            rsOpRailMapper.insertRsOpRail(rsRct.getRsOpRailFCL());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpRailFCL().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailFCL().getRsChargeList(), rctId, 20L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailFCL().getRsOpLogList(), rctId, 20L);
        }
    }

    /**
     * 保存铁路服务信息
     *
     * @param rsRct
     * @param serviceNames
     * @param serviceTypeIds
     */
    public void saveRailLCL(RsRct rsRct, List<String> serviceNames, List<Long> serviceTypeIds) {
        RsServiceInstances rsServiceInstance;
        if (ObjectUtils.isNotEmpty(rsRct.getRsOpRailLCL().getRsServiceInstances())) {
            rsServiceInstance = rsRct.getRsOpRailLCL().getRsServiceInstances();
        } else {
            rsServiceInstance = new RsServiceInstances();
        }
        Long rctId = rsRct.getRctId();

        if (serviceNames.contains("RailLCL") && serviceTypeIds.contains(21L)) {
            // 更新服务实例
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.updateRsServiceInstances(rsServiceInstance);

            // 更新服务
            rsRct.getRsOpRailLCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailLCL().setSqdServiceTypeId(21L);
            rsOpRailMapper.updateRsOpRail(rsRct.getRsOpRailLCL());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpRailLCL().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 更新费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailLCL().getRsChargeList(), rctId, 21L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailLCL().getRsOpLogList(), rctId, 21L);
        } else {
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("RailLCL");
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setServiceTypeId(21L);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsRct.getRsOpRailLCL().setServiceId(rsServiceInstance.getServiceId());
            rsRct.getRsOpRailLCL().setSqdRctNo(rsRct.getRctNo());
            rsRct.getRsOpRailLCL().setSqdServiceTypeId(21L);
            rsOpRailMapper.insertRsOpRail(rsRct.getRsOpRailLCL());

            updateRsDebitNoteList(rsServiceInstance, rsRct.getRsOpRailLCL().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsRct.getRsOpRailLCL().getRsChargeList(), rctId, 21L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsRct.getRsOpRailLCL().getRsOpLogList(), rctId, 21L);
        }
    }

    /**
     * 保存空运服务信息
     *
     * @param rsRct
     */
    public void saveAir(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpAir> existingList = rsOpAirMapper.selectRsOpAirByRctId(rctId, 10L);

        List<RsOpAir> newList = rsRct.getRsOpAirList();

        // 找出需要删除的记录
        List<RsOpAir> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getAirId() != null &&
                                newItem.getAirId().equals(existing.getAirId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpAir rsOpAir : toDeleteServiceObject) {
//                rsOpSeaFclMapper.deleteRsOpSeaFclBySeaFclId(rsOpAir.getAirId());
                rsOpAirMapper.deleteRsOpAirByAirId(rsOpAir.getAirId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpAir.getRsServiceInstances().getServiceId());
                if (rsOpAir.getRsChargeList() != null && !rsOpAir.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpAir.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpAir rsOpAir : rsRct.getRsOpAirList()) {
            RsServiceInstances rsServiceInstance = rsOpAir.getRsServiceInstances();
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("AIR");
            rsServiceInstance.setServiceTypeId(10L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpAir.setServiceId(rsServiceInstance.getServiceId());
            rsOpAir.setSqdRctNo(rsRct.getRctNo());
            rsOpAir.setSqdServiceTypeId(10L);
            rsOpAirMapper.upsertRsOpAir(rsOpAir);

            updateRsDebitNoteList(rsServiceInstance, rsOpAir.getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsOpAir.getRsChargeList(), rctId, 10L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpAir.getRsOpLogList(), rctId, 10L);

        }
    }

    /**
     * 保存整柜海运服务信息
     *
     * @param rsRct
     */
    public void saveSeaFcl(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpSeaFcl> existingList = rsOpSeaFclMapper.selectRsOpSeaFclByRctId(rctId, 1L);

        List<RsOpSeaFcl> newList = rsRct.getRsOpSeaFclList();

        // 找出需要删除的记录
        List<RsOpSeaFcl> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getSeaId() != null &&
                                newItem.getSeaId().equals(existing.getSeaId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpSeaFcl rsOpSeaFcl : toDeleteServiceObject) {
                rsOpSeaFclMapper.deleteRsOpSeaFclBySeaFclId(rsOpSeaFcl.getSeaId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpSeaFcl.getRsServiceInstances().getServiceId());
                if (rsOpSeaFcl.getRsChargeList() != null && !rsOpSeaFcl.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpSeaFcl.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpSeaFcl rsOpSeaFcl : newList) {
            RsServiceInstances rsServiceInstance = rsOpSeaFcl.getRsServiceInstances();

            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("SeaFCL");
            rsServiceInstance.setServiceTypeId(1L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpSeaFcl.setServiceId(rsServiceInstance.getServiceId());
            rsOpSeaFcl.setSqdRctNo(rsRct.getRctNo());
            rsOpSeaFcl.setSqdServiceTypeId(1L);
            rsOpSeaFcl.setRctId(rctId);
            if (rsOpSeaFcl.getSeaId() == null) {
                String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
                String date = DateUtils.dateTime();
                rsOpSeaFcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
            }
            rsOpSeaFclMapper.upsertRsOpSeaFcl(rsOpSeaFcl);

            updateRsDebitNoteList(rsServiceInstance, rsOpSeaFcl.getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsOpSeaFcl.getRsChargeList(), rctId, 1L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpSeaFcl.getRsOpLogList(), rctId, 1L);
        }
    }

    /**
     * 保存拼柜海运服务信息
     *
     * @param rsRct
     */
    public void saveSeaLcl(RsRct rsRct) {
        Long rctId = rsRct.getRctId();

        List<RsOpSeaLcl> existingList = rsOpSeaLclMapper.selectRsOpSeaLclByRctId(rctId, 2L);

        List<RsOpSeaLcl> newList = rsRct.getRsOpSeaLclList();

        // 找出需要删除的记录
        List<RsOpSeaLcl> toDeleteServiceObject = existingList.stream()
                .filter(existing -> newList.stream()
                        .noneMatch(newItem -> newItem.getSeaId() != null &&
                                newItem.getSeaId().equals(existing.getSeaId())))
                .collect(Collectors.toList());

        if (!toDeleteServiceObject.isEmpty()) {
            for (RsOpSeaLcl rsOpSeaLcl : toDeleteServiceObject) {
                rsOpSeaLclMapper.deleteRsOpSeaLclBySeaLclId(rsOpSeaLcl.getSeaId());
                rsServiceInstancesMapper.deleteRsServiceInstancesByServiceId(rsOpSeaLcl.getRsServiceInstances().getServiceId());
                if (rsOpSeaLcl.getRsChargeList() != null && !rsOpSeaLcl.getRsChargeList().isEmpty()) {
                    for (RsCharge rsCharge : rsOpSeaLcl.getRsChargeList()) {
                        rsChargeMapper.deleteRsChargeByChargeId(rsCharge.getChargeId());
                    }
                }
            }
        }

        for (RsOpSeaLcl rsOpSeaLcl : rsRct.getRsOpSeaLclList()) {
            RsServiceInstances rsServiceInstance = rsOpSeaLcl.getRsServiceInstances();
            // 插入服务实例
            rsServiceInstance.setServiceBelongTo("SeaLCL");
            rsServiceInstance.setServiceTypeId(2L);
            rsServiceInstance.setRctId(rctId);
            rsServiceInstance.setRctNo(rsRct.getRctNo());
            rsServiceInstancesMapper.upsertRsServiceInstances(rsServiceInstance);

            // 插入服务
            rsOpSeaLcl.setServiceId(rsServiceInstance.getServiceId());
            rsOpSeaLcl.setSqdRctNo(rsRct.getRctNo());
            rsOpSeaLcl.setSqdServiceTypeId(2L);
            if (rsOpSeaLcl.getSeaId() == null) {
                String psaNo = redisIdGeneratorService.generateUniqueId("psa_no");
                String date = DateUtils.dateTime();
                rsOpSeaLcl.setSqdPsaNo("PSA" + date.substring(2, 6) + psaNo);
            }
            rsOpSeaLclMapper.upsertRsOpSeaLcl(rsOpSeaLcl);

            updateRsDebitNoteList(rsServiceInstance, rsOpSeaLcl.getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

            // 插入费用
            updateFreight(rsServiceInstance, rsOpSeaLcl.getRsChargeList(), rctId, 2L, rsRct.getRctNo(), rsRct.getRctCreateTime(), null);

            // 更新操作记录
            updateOpLog(rsServiceInstance.getServiceId(), rsOpSeaLcl.getRsOpLogList(), rctId, 2L);
        }
    }

    private void updateOpLog(Long serviceId, List<RsOpLog> rsOpLogs, Long rctId, Long sqdServiceTypeId) {
        if (rsOpLogs == null || rsOpLogs.isEmpty()) {
            return;
        }
        rsOpLogMapper.deleteRsOpLogByServiceId(serviceId);
        for (RsOpLog rsOpLog : rsOpLogs) {
            rsOpLog.setServiceId(serviceId);
            rsOpLog.setSqdRctId(rctId);
            rsOpLog.setSqdServiceTypeId(sqdServiceTypeId);
            rsOpLog.setServerSystemTime(new Date());
            rsOpLogMapper.insertRsOpLog(rsOpLog);
        }
    }

    public void updateFreight(RsServiceInstances rsServiceInstances, List<RsCharge> rsCharges, Long rctId, long sqdServiceTypeId, String rctNo, Date rctCreateTime, RsDebitNote rsDebitNote) {
        if (rsCharges == null || rsCharges.isEmpty()) {
            return;
        }

        if (rsServiceInstances.getServiceId() != null && rsDebitNote.getDebitNoteId() != null) {
            for (RsCharge rsCharge : rsCharges) {
                rsCharge.setServiceId(rsServiceInstances.getServiceId());
                rsCharge.setSqdRctId(rctId);
                rsCharge.setSqdServiceTypeId(sqdServiceTypeId);
                rsCharge.setPaymentTitleCode(rsServiceInstances.getPaymentTitleCode());
                rsCharge.setIsRecievingOrPaying(1L);
                rsCharge.setSqdRctNo(rctNo);
                if (rsCharge.getIsAccountConfirmed() == null) {
                    rsCharge.setIsAccountConfirmed("0");
                }
                rsCharge.setDebitNoteId(rsDebitNote.getDebitNoteId() != null ? rsDebitNote.getDebitNoteId() : null);
                rsCharge.setClearingCompanyId(rsDebitNote.getClearingCompanyId());
                rsCharge.setCurrencyRateCalculateDate(rctCreateTime);
                if (rsCharge.getChargeId() == null || rsCharge.getIsAccountConfirmed().equals("0")) {
                    rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
                    rsCharge.setDnCurrencyBalance(rsCharge.getSubtotal());
                }
                rsChargeMapper.upsertRsCharge(rsCharge);
            }
        }
    }

    @Override
    public void notification() {
        try {
            // 获取通知数量
            // 获取用户ID
            Long userId = SecurityUtils.getUserId();
            // 获取用户职位
            Long position = basPositionService.selectPostByUserId(userId);
            // 获取登录用户信息
            LoginUser loginUser = SecurityUtils.getLoginUser();

            // 检查是否满足特定条件
            boolean isOperator = loginUser.getUser().getRoles().stream()
                    .anyMatch(role -> "Operator".equals(role.getRoleKey()));
            int deptCreateListNum = loginUser.getUser().getDept().getCreateListNum();

            // 根据条件返回不同的通知数量
            int opNotificationCount;
            if (isOperator && position < 14 && deptCreateListNum != 4) {
                opNotificationCount = rsRctMapper.opNotification(SecurityUtils.getUserId());
            } else {
                opNotificationCount = rsRctMapper.opNotification(null);
            }

            int psaNotificationCount = rsRctMapper.psaNotification();

            // 存储到Redis中
            // 使用具有业务意义的键名
            redisCache.setCacheObject("notification:psa:count", psaNotificationCount);

            // 通过WebSocket广播通知 - 使用静态方法

            NotificationWebSocket.broadcastNotification(opNotificationCount, psaNotificationCount);
        } catch (Exception e) {
            log.error("发送通知失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int saveClientMessage(RsRct rsRct) {
        int out;
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "client");
        if (rsServiceInstances == null) {
            out = rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsServiceInstances = rsRct.getRsClientMessage().getRsServiceInstances();
        } else {
            out = rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsRct.getRsClientMessage().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }

        if (rsServiceInstances.getRctNo() == null) {
            rsServiceInstances.setRctNo(rsRct.getRctNo());
        }
        Long serviceInstanceId = rsRct.getRsClientMessage().getRsServiceInstances().getServiceId();

        assert rsServiceInstances != null;
        updateOpLog(rsServiceInstances.getServiceId(), rsRct.getRsClientMessage().getRsOpLogList(), rsRct.getRctId(), serviceInstanceId);

        updateRsDebitNoteList(rsServiceInstances, rsRct.getRsClientMessage().getRsDebitNoteList(), rsRct.getRctId(), rsRct.getRctCreateTime());

        // 费用信息
        List<RsCharge> rsCharges = rsRct.getRsClientMessage().getRsChargeList();
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            rsCharge.setSqdRctId(rsRct.getRctId());
//            rsCharge.setSqdServiceTypeId();
            rsCharge.setIsRecievingOrPaying(0L);
            rsCharge.setPaymentTitleCode(rsServiceInstances.getPaymentTitleCode());
            rsCharge.setSqdRctNo(rsRct.getRctNo());
            rsCharge.setCurrencyRateCalculateDate(rsRct.getRctCreateTime());
            if (rsCharge.getChargeId() == null) {
                rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
            }
            out += rsChargeMapper.upsertRsCharge(rsCharge);
        }
        return out;
    }

    public void updateRsDebitNoteList(RsServiceInstances rsServiceInstances, List<RsDebitNote> rsDebitNoteList, Long rctId, Date rctCreateTime) {
        if (rsDebitNoteList != null && !rsDebitNoteList.isEmpty()) {
            for (RsDebitNote rsDebitNote : rsDebitNoteList) {
                rsDebitNote.setRctId(rctId);
                rsDebitNote.setServiceId(rsServiceInstances.getServiceId());
                rsDebitNoteMapper.upsertRsDebitNote(rsDebitNote);
                if (rsServiceInstances.getServiceTypeId() == null && rsServiceInstances.getServiceBelongTo().equals("client")) {
                    rsServiceInstances.setServiceTypeId(0L);
                }
                updateFreight(rsServiceInstances, rsDebitNote.getRsChargeList(), rctId, rsServiceInstances.getServiceTypeId(), rsServiceInstances.getRctNo(), rctCreateTime, rsDebitNote);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, timeout = 300)
    public int saveAsAllServices(RsRct rsRct) {
        try {
            int out;
            // 关联服务实例
            List<String> serviceNames = Collections.emptyList();
            List<Long> serviceTypeIds = Collections.emptyList();

        List<RsOpLog> rsOpLogList = new ArrayList<>();

        saveAsClientMessage(rsRct);
        if (rsRct.getRsOpSeaFclList() != null && !rsRct.getRsOpSeaFclList().isEmpty()) {
            for (RsOpSeaFcl rsOpSeaFcl : rsRct.getRsOpSeaFclList()) {
                List<RsOpLog> rsOpLogs = rsOpSeaFcl.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }

                rsOpSeaFcl.setSeaId(null);
                rsOpSeaFcl.setPsaNo(null);
                rsOpSeaFcl.setPsaId(null);
                rsOpSeaFcl.setSqdPsaNo(null);
                rsOpSeaFcl.setSqdRctNo(null);
                rsOpSeaFcl.setSqdPsaNo(null);
                rsOpSeaFcl.getRsServiceInstances().setServiceId(null);

                resetCharge(rsOpSeaFcl.getRsChargeList());
            }
            saveSeaFcl(rsRct);
        }

        if (rsRct.getRsOpSeaLclList() != null && !rsRct.getRsOpSeaLclList().isEmpty()) {
            for (RsOpSeaLcl rsOpSeaLcl : rsRct.getRsOpSeaLclList()) {
                List<RsOpLog> rsOpLogs = rsOpSeaLcl.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }

                rsOpSeaLcl.setSeaId(null);
                rsOpSeaLcl.setPsaNo(null);
                rsOpSeaLcl.setPsaId(null);
                rsOpSeaLcl.setSqdPsaNo(null);
                rsOpSeaLcl.setSqdRctNo(null);
                rsOpSeaLcl.setSqdPsaNo(null);
                rsOpSeaLcl.getRsServiceInstances().setServiceId(null);

                resetCharge(rsOpSeaLcl.getRsChargeList());
            }
            saveSeaLcl(rsRct);
        }

        if (rsRct.getRsOpAirList() != null && !rsRct.getRsOpAirList().isEmpty()) {
            for (RsOpAir rsOpAir : rsRct.getRsOpAirList()) {
                List<RsOpLog> rsOpLogs = rsOpAir.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpAir.setAirId(null);
                rsOpAir.getRsServiceInstances().setServiceId(null);

                resetCharge(rsOpAir.getRsChargeList());
                saveAir(rsRct);
            }
        }
        if (rsRct.getRsOpRailFCL() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpRailFCL().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpRailFCL().setRailId(null);
            rsRct.getRsOpRailFCL().getRsServiceInstances().setServiceId(null);

            resetCharge(rsRct.getRsOpRailFCL().getRsChargeList());
            saveRailFCL(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpRailLCL() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpRailLCL().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpRailLCL().setRailId(null);
            rsRct.getRsOpRailLCL().getRsServiceInstances().setServiceId(null);

            resetCharge(rsRct.getRsOpRailLCL().getRsChargeList());
            saveRailLCL(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpExpress() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpExpress().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpExpress().setExpressId(null);
            rsRct.getRsOpExpress().getRsServiceInstances().setServiceId(null);

            resetCharge(rsRct.getRsOpExpress().getRsChargeList());
            saveExpress(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpCtnrTruckList() != null && !rsRct.getRsOpCtnrTruckList().isEmpty()) {
            for (RsOpCtnrTruck rsOpCtnrTruck : rsRct.getRsOpCtnrTruckList()) {
                List<RsOpLog> rsOpLogs = rsOpCtnrTruck.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpCtnrTruck.setTruckId(null);
                rsOpCtnrTruck.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpCtnrTruck.getRsChargeList());
                saveCtnrTruck(rsRct);
            }
        }
        if (rsRct.getRsOpBulkTruckList() != null && !rsRct.getRsOpBulkTruckList().isEmpty()) {
            for (RsOpBulkTruck rsOpBulkTruck : rsRct.getRsOpBulkTruckList()) {
                List<RsOpLog> rsOpLogs = rsOpBulkTruck.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpBulkTruck.setTruckId(null);
                rsOpBulkTruck.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpBulkTruck.getRsChargeList());
                saveBulkTruck(rsRct);
            }
        }
        if (rsRct.getRsOpFreeDeclareList() != null && !rsRct.getRsOpFreeDeclareList().isEmpty()) {
            for (RsOpFreeDeclare rsOpFreeDeclare : rsRct.getRsOpFreeDeclareList()) {
                List<RsOpLog> rsOpLogs = rsOpFreeDeclare.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpFreeDeclare.setExportCustomsClearanceId(null);
                rsOpFreeDeclare.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpFreeDeclare.getRsChargeList());
                saveFreeDeclare(rsRct);
            }
        }
        if (rsRct.getRsOpDocDeclareList() != null && !rsRct.getRsOpDocDeclareList().isEmpty()) {
            for (RsOpDocDeclare rsOpDocDeclare : rsRct.getRsOpDocDeclareList()) {
                List<RsOpLog> rsOpLogs = rsOpDocDeclare.getRsOpLogList();
                if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                    rsOpLogList.addAll(rsOpLogs);
                }
                rsOpDocDeclare.setExportCustomsClearanceId(null);
                rsOpDocDeclare.getRsServiceInstances().setServiceId(null);
                resetCharge(rsOpDocDeclare.getRsChargeList());
                saveDocDeclare(rsRct);
            }
        }
        if (rsRct.getRsOpDOAgent() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpDOAgent().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpDOAgent().setImportCustomsClearanceId(null);
            rsRct.getRsOpDOAgent().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpDOAgent().getRsChargeList());
            saveDOAgent(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpClearAgent() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpClearAgent().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpClearAgent().setImportCustomsClearanceId(null);
            rsRct.getRsOpClearAgent().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpClearAgent().getRsChargeList());
            saveClearAgent(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpWHS() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpWHS().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpWHS().setWarehouseId(null);
            rsRct.getRsOpWHS().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpWHS().getRsChargeList());
            saveWHS(rsRct, serviceNames, serviceTypeIds);
        }

        // 拓展服务
        if (rsRct.getRsOp3rdCert() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOp3rdCert().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOp3rdCert().setExpandServiceId(null);
            rsRct.getRsOp3rdCert().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOp3rdCert().getRsChargeList());
            save3rdCert(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpINS() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpINS().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpINS().setExpandServiceId(null);
            rsRct.getRsOpINS().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpINS().getRsChargeList());
            saveINS(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpTrading() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpTrading().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpTrading().setExpandServiceId(null);
            rsRct.getRsOpTrading().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpTrading().getRsChargeList());
            saveTrading(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpFumigation() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpFumigation().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpFumigation().setExpandServiceId(null);
            rsRct.getRsOpFumigation().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpFumigation().getRsChargeList());
            saveFumigation(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpCO() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpCO().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpCO().setExpandServiceId(null);
            rsRct.getRsOpCO().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpCO().getRsChargeList());
            saveCO(rsRct, serviceNames, serviceTypeIds);
        }
        if (rsRct.getRsOpOther() != null) {
            List<RsOpLog> rsOpLogs = rsRct.getRsOpOther().getRsOpLogList();
            if (rsOpLogs != null && !rsOpLogs.isEmpty()) {
                rsOpLogList.addAll(rsOpLogs);
            }
            rsRct.getRsOpOther().setExpandServiceId(null);
            rsRct.getRsOpOther().getRsServiceInstances().setServiceId(null);
            resetCharge(rsRct.getRsOpOther().getRsChargeList());
            saveOther(rsRct, serviceNames, serviceTypeIds);
        }

            log.info("成功另存为操作单所有服务，操作单号: {}", rsRct.getRctNo());
            return 0;
        } catch (Exception e) {
            log.error("另存为操作单服务失败，操作单号: {}，错误信息: {}", rsRct.getRctNo(), e.getMessage(), e);
            throw new RuntimeException("另存为操作单服务失败: " + e.getMessage(), e);
        }
    }

    private void saveAsClientMessage(RsRct rsRct) {
        // 关联服务实例
        RsServiceInstances rsServiceInstances = rsServiceInstancesMapper.selectRsServiceInstances(rsRct.getRctId(), "client");
        if (rsServiceInstances == null) {
            rsServiceInstancesMapper.insertRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsServiceInstances = rsRct.getRsClientMessage().getRsServiceInstances();
        } else {
            rsServiceInstancesMapper.updateRsServiceInstances(rsRct.getRsClientMessage().getRsServiceInstances());
            rsRct.getRsClientMessage().getRsServiceInstances().setServiceId(rsServiceInstances.getServiceId());
        }

        Long serviceInstanceId = rsRct.getRsClientMessage().getRsServiceInstances().getServiceId();

        assert rsServiceInstances != null;
        updateOpLog(rsServiceInstances.getServiceId(), rsRct.getRsClientMessage().getRsOpLogList(), rsRct.getRctId(), serviceInstanceId);

        // 费用信息
        List<RsCharge> rsCharges = rsRct.getRsClientMessage().getRsChargeList();
        resetCharge(rsCharges);
        for (RsCharge rsCharge : rsCharges) {
            rsCharge.setServiceId(serviceInstanceId);
            rsCharge.setSqdRctId(rsRct.getRctId());
//            rsCharge.setSqdServiceTypeId();
            rsCharge.setIsRecievingOrPaying(0L);
            rsCharge.setPaymentTitleCode(rsServiceInstances.getPaymentTitleCode());
            rsCharge.setSqdRctNo(rsRct.getRctNo());
            rsCharge.setCurrencyRateCalculateDate(rsRct.getRctCreateTime());
            if (rsCharge.getChargeId() == null) {
                rsCharge.setSqdDnCurrencyBalance(rsCharge.getSubtotal());
            }
            rsChargeMapper.upsertRsCharge(rsCharge);
        }
    }

    @Override
    public void writeoff(List<String> rctNoList) {
        rsRctMapper.updateByWriteoff(rctNoList);
    }

    @Override
    public List<RsRct> selectUnVerifyRsRctAggregatorList(RsRct rsRct) {
        boolean search = rsRct.getPolIds() != null || rsRct.getDestinationPortIds() != null || rsRct.getLineIds() != null;
        if (search) {
            Map<String, List<?>> query = queryRctList(rsRct);
            List<List<Long>> queryList = (List<List<Long>>) query.get("list");
            List<Long> queryPolIds = (List<Long>) query.get("locationDeparture");
            List<Long> queryDestinationIds = (List<Long>) query.get("locationDestination");
            if (!queryList.isEmpty()) {
                rsRct.setRctIds(SearchUtils.getLongs(queryList));
            }
            if (!queryPolIds.isEmpty()) {
                rsRct.setPolIds(queryPolIds.toArray(new Long[0]));
            } else {
                rsRct.setPolIds(null);
            }
            if (!queryDestinationIds.isEmpty()) {
                rsRct.setDestinationPortIds(queryDestinationIds.toArray(new Long[0]));
            } else {
                rsRct.setDestinationPortIds(null);
            }
        }
        rsRct.setUserId(SecurityUtils.getUserId());
        return rsRctMapper.selectUnVerifyRsRctList(rsRct);
    }
}
