{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\preCarriageNoInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\preCarriageNoInfo.vue", "mtime": 1754876882585}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["preCarriageNoInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "preCarriageNoInfo.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n             :visible.sync=\"oopen\" append-to-body width=\"1200px\">\r\n    <div style=\"display: flex\">\r\n      <h2 style=\"font-weight: bold ;margin:10px;\">新增编号信息</h2>\r\n      <div style=\"vertical-align: middle;line-height: 41px\">\r\n        <el-button type=\"primary\" @click=\"open=true\">新增</el-button>\r\n      </div>\r\n    </div>\r\n    <el-table border :data=\"preCarriageNoInfo\">\r\n      <el-table-column label=\"SO号码\" prop=\"soNo\"></el-table-column>\r\n      <el-table-column label=\"司机姓名\" prop=\"preCarriageDriverName\"></el-table-column>\r\n      <el-table-column label=\"司机电话\" prop=\"preCarriageDriverTel\"></el-table-column>\r\n      <el-table-column label=\"司机车牌\" prop=\"preCarriageTruckNo\"></el-table-column>\r\n      <el-table-column label=\"司机备注\" prop=\"preCarriageTruckRemark\"></el-table-column>\r\n      <el-table-column label=\"装柜地址\" prop=\"preCarriageAddress\"></el-table-column>\r\n      <el-table-column label=\"到场时间\" prop=\"preCarriageTime\"></el-table-column>\r\n      <el-table-column label=\"柜号\" prop=\"containerNo\"></el-table-column>\r\n      <el-table-column label=\"柜型\" prop=\"containerType\"></el-table-column>\r\n      <el-table-column label=\"封条\" prop=\"sealNo\"></el-table-column>\r\n      <el-table-column label=\"磅单\" prop=\"weightPaper\"></el-table-column>\r\n      <el-table-column header-align=\"center\" align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\"\r\n                       width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              style=\"margin-right: -8px\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n          >修改\r\n          </el-button>\r\n          <el-button\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              style=\"margin-right: -8px\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete(scope.row)\"\r\n          >删除\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n               :visible.sync=\"open\" append-to-body width=\"500px\" title=\"新增编号信息\">\r\n      <el-form border :data=\"form\" label-width=\"105px\">\r\n        <el-form-item label=\"SO号码\" prop=\"soNo\">\r\n          <el-input v-model=\"form.soNo\" placeholder=\"SO号码\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机姓名\" prop=\"preCarriageDriverName\">\r\n          <el-input v-model=\"form.preCarriageDriverName\" placeholder=\"司机姓名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机电话\" prop=\"preCarriageDriverTel\">\r\n          <el-input v-model=\"form.preCarriageDriverTel\" placeholder=\"司机电话\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机车牌\" prop=\"preCarriageTruckNo\">\r\n          <el-input v-model=\"form.preCarriageTruckNo\" placeholder=\"司机车牌\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机备注\" prop=\"preCarriageTruckRemark\">\r\n          <el-input v-model=\"form.preCarriageTruckRemark\" placeholder=\"司机备注\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"装柜地址\" prop=\"preCarriageAddress\">\r\n          <el-input v-model=\"form.preCarriageAddress\" placeholder=\"装柜地址\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"到场时间\" prop=\"preCarriageTime\">\r\n          <el-input v-model=\"form.preCarriageTime\" placeholder=\"到场时间\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n          <el-input v-model=\"form.containerNo\" placeholder=\"柜号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"柜型\" prop=\"containerType\">\r\n          <el-input v-model=\"form.containerType\" placeholder=\"柜型\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"封条\" prop=\"sealNo\">\r\n          <el-input v-model=\"form.sealNo\" placeholder=\"封条\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"磅单\" prop=\"weightPaper\">\r\n          <el-input v-model=\"form.weightPaper\" placeholder=\"磅单\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'PreCarriageNoInfo',\r\n  props: ['openPreCarriageNoInfo'],\r\n  watch: {\r\n    preCarriageNoInfo() {\r\n      this.$emit('return', this.preCarriageNoInfo)\r\n    },\r\n    openPreCarriageNoInfo(n) {\r\n      this.oopen = n\r\n    },\r\n    oopen(n) {\r\n      if (n == false) {\r\n        this.$emit('close')\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      open: false,\r\n      oopen: false,\r\n      preCarriageNoInfo: [],\r\n      form: {},\r\n    }\r\n  },\r\n  methods: {\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1;\r\n    },\r\n    handleUpdate(row) {\r\n      this.form = row\r\n      this.open = true\r\n    },\r\n    handleDelete(row) {\r\n      this.preCarriageNoInfo = this.preCarriageNoInfo.filter(item => {\r\n        return item.id != row.id\r\n      })\r\n    },\r\n    submitForm() {\r\n      if (this.form.id != null) {\r\n        this.reset()\r\n        this.open = false\r\n      } else {\r\n        this.preCarriageNoInfo.push(this.form)\r\n        this.reset()\r\n        this.open = false\r\n      }\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        soNo: null,\r\n        preCarriageDriverName: null,\r\n        preCarriageDriverTel: null,\r\n        preCarriageTruckNo: null,\r\n        preCarriageTruckRemark: null,\r\n        preCarriageAddress: null,\r\n        preCarriageTime: null,\r\n        containerNo: null,\r\n        containerType: null,\r\n        sealNo: null,\r\n        weightPaper: null,\r\n      }\r\n      this.resetForm(\"form\");\r\n    },\r\n    cancel() {\r\n      this.open = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}