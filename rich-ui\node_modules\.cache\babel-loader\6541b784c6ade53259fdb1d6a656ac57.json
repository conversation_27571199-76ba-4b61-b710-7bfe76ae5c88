{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\vatInvoice.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\vatInvoice.js", "mtime": 1754881964205}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listVatinvoice", "query", "request", "url", "method", "params", "getVatinvoice", "invoiceId", "addVatinvoice", "data", "updateVatinvoice", "delVatinvoice", "changeStatus", "status", "countVatinvoiceByRctId", "rctId", "generateInvoiceCode", "cooperatorId", "concat", "generateInvoiceExcel", "invoiceIds", "responseType"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/vatInvoice.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询发票登记列表\r\nexport function listVatinvoice(query) {\r\n  return request({\r\n    url: '/system/vatinvoice/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询发票登记详细\r\nexport function getVatinvoice(invoiceId) {\r\n  return request({\r\n    url: '/system/vatinvoice/' + invoiceId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增发票登记\r\nexport function addVatinvoice(data) {\r\n  return request({\r\n    url: '/system/vatinvoice',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改发票登记\r\nexport function updateVatinvoice(data) {\r\n  return request({\r\n    url: '/system/vatinvoice',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除发票登记\r\nexport function delVatinvoice(invoiceId) {\r\n  return request({\r\n    url: '/system/vatinvoice/' + invoiceId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(invoiceId, status) {\r\n  const data = {\r\n    invoiceId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/vatinvoice/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 根据rctId查询发票数量\r\nexport function countVatinvoiceByRctId(rctId) {\r\n  return request({\r\n    url: '/system/vatinvoice/count/' + rctId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 根据rctId和cooperatorId生成发票编码\r\nexport function generateInvoiceCode(rctId, cooperatorId) {\r\n  return request({\r\n    url: `/system/vatinvoice/generateCode/${rctId}/${cooperatorId}`,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 生成发票Excel文件\r\nexport function generateInvoiceExcel(invoiceIds) {\r\n  return request({\r\n    url: '/system/vatinvoice/generateExcel',\r\n    method: 'put',\r\n    data: invoiceIds,\r\n    responseType: 'arraybuffer'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,SAAS,EAAE;EACvC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,SAAS;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,SAAS,EAAE;EACvC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,SAAS;IACtCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,SAAS,EAAEM,MAAM,EAAE;EAC9C,IAAMJ,IAAI,GAAG;IACXF,SAAS,EAATA,SAAS;IACTM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,sBAAsBA,CAACC,KAAK,EAAE;EAC5C,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGY,KAAK;IACxCX,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASY,mBAAmBA,CAACD,KAAK,EAAEE,YAAY,EAAE;EACvD,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,qCAAAe,MAAA,CAAqCH,KAAK,OAAAG,MAAA,CAAID,YAAY,CAAE;IAC/Db,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASe,oBAAoBA,CAACC,UAAU,EAAE;EAC/C,OAAO,IAAAlB,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEW,UAAU;IAChBC,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ"}]}