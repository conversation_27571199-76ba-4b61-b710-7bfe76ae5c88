{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\freeStorage.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\freeStorage.vue", "mtime": 1754876882588}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJmcmVlU3RvcmFnZSIsDQogIHByb3BzOiBbJ3Njb3BlJ10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNpemU6IHRoaXMuJHN0b3JlLnN0YXRlLmFwcC5zaXplIHx8ICdtaW5pJywNCiAgICB9DQogIH0sDQp9DQo="}, {"version": 3, "sources": ["freeStorage.vue"], "names": [], "mappings": ";;;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "freeStorage.vue", "sourceRoot": "src/views/system/freight", "sourcesContent": ["<template>\r\n  <div>\r\n    <h6 style=\"margin: 0\">{{ scope.row.freeStorage }}</h6>\r\n    <h6 style=\"margin: 0\">{{ scope.row.storageUnit }}</h6>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"freeStorage\",\r\n  props: ['scope'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini',\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}