{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\basicBankRecord.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\basicBankRecord.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_bankrecord", "require", "_index", "_interopRequireDefault", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_store", "_currency", "_rsCharge", "_exchangerate", "_rich", "_log", "_moment", "_request", "_auth", "name", "components", "Treeselect", "CompanySelect", "data", "writeOffList", "salesId", "belongList", "staffList", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "bankrecordList", "title", "open", "queryParams", "pageNum", "pageSize", "isRecievingOrPaying", "sqdPaymentTitleCode", "bankAccountCode", "clearingCompanyId", "sqdClearingCompanyShortname", "chargeTypeId", "chargeDescription", "bankCurrencyCode", "actualBankRecievedAmount", "actualBankPaidAmount", "bankRecievedHandlingFee", "bankPaidHandlingFee", "bankRecievedExchangeLost", "bankPaidExchangeLost", "sqdBillRecievedAmount", "sqdBillPaidAmount", "billRecievedWriteoffAmount", "billPaidWriteoffAmount", "sqdBillRecievedWriteoffBalance", "sqdBillPaidWriteoffBalance", "writeoffStatus", "bankRecordTime", "paymentTypeCode", "voucherNo", "bankRecordRemark", "bankRecordByStaffId", "bankRecordUpdateTime", "isBankRecordLocked", "isWriteoffLocked", "sqdChargeIdList", "sqdRaletiveRctList", "sqdRaletiveInvoiceList", "sqdRsStaffId", "writeoffRemark", "writeoffStaffId", "writeoffTime", "form", "upload", "isUploading", "updateSupport", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "rules", "required", "message", "trigger", "add", "showDetail", "selected<PERSON><PERSON>ges", "totalAmount", "selectedAmount", "selectedBalanceAmount", "loadingCharge", "staffId", "alreadyWriteoffList", "turnBackWriteoffList", "showCompany", "companyList", "bankSlipPreview", "imageFile", "beforeMount", "loadStaff", "watch", "n", "formActualBankRecievedAmount", "currency", "value", "formBankRecievedHandlingFee", "formBankRecievedExchangeLost", "subtract", "formActualBankPaidAmount", "formBankPaidHandlingFee", "formBankPaidExchangeLost", "formSqdBillRecievedAmount", "formSqdBillPaidAmount", "formBillRecievedWriteoffAmount", "formBillPaidWriteoffAmount", "handler", "newVal", "oldVal", "_this", "map", "item", "writeoffFromDnBalance", "writeoffFromBankBalance", "divide", "exchangeRate", "sqdDnCurrencyBalance", "dnUnitRate", "multiply", "dnAmount", "deep", "created", "getList", "loadSales", "getExchangeRate", "val", "console", "log", "computed", "receiveRate", "paidRate", "isLocked", "isBankSlipConfirmed", "slipConfirmed", "methods", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "response", "$refs", "clearFiles", "$message", "info", "msg", "download", "submitFileForm", "submit", "handleImport", "handleSearch", "type", "parseTime", "updateSlipSatus", "_this2", "updateBankrecord", "then", "success", "writeOffConfirm", "_this3", "clearReceiveOrPay", "rctSet", "Set", "sqdRctNo", "rctArr", "for<PERSON>ach", "push", "toString", "moment", "format", "$modal", "msgSuccess", "turnBackWriteoff", "rsChargeList", "midChargeBankWriteoffs", "clearingCurrencyCode", "midChargeBankWriteoff", "chargeId", "bankRecordId", "exchangeRateShowing", "exchangeRateShow", "dnBasicRate", "chargeWriteOff", "midChargeBankWriteoffList", "invertSelection", "_this4", "row", "writeOffTable", "toggleRowSelection", "autoSelection", "addHedging", "projectRemove", "print", "getCompanyCharges", "_this5", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "_iterator", "_step", "result", "billExchangeRate", "wrap", "_callee$", "_context", "prev", "next", "selectListCharge", "sent", "length", "_createForOfIteratorHelper2", "s", "done", "dnCurrencyCode", "error", "abrupt", "getBillDataExchangeRate", "currencyRateCalculateDate", "t0", "e", "f", "finish", "setTimeout", "midChargeBankId", "sort", "a", "b", "isAccountConfirmed", "stop", "verify", "_this6", "verifyId", "verifyTime", "$store", "state", "user", "sid", "_this7", "listBankrecord", "rows", "cancel", "reset", "invoiceNo", "chargeType", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this8", "text", "status", "$confirm", "changeStatus", "catch", "handleSelectionChange", "selection", "_this9", "indexOf", "sqdDnCurrencyBalanceShow", "handleAdd", "handleUpdate", "_this10", "getBankrecord", "submitForm", "_this11", "validate", "_ref", "_callee2", "valid", "_callee2$", "_context2", "addBankrecord", "_x", "apply", "arguments", "uploadImage", "_this12", "Promise", "resolve", "reject", "customHttpRequest", "onSuccess", "handleSuccess", "onError", "handleError", "handleDelete", "_this13", "bankRecordIds", "delBankrecord", "handleExport", "_objectSpread2", "concat", "Date", "getTime", "staffNormalizer", "node", "children", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "id", "label", "isDisabled", "undefined", "deptId", "_this14", "salesList", "redisList", "store", "dispatch", "_this15", "allRsStaffList", "handledbClick", "selectCompany", "company", "companyId", "companyShortName", "overseaCurrency", "localCurrency", "valueDate", "_callee3", "re", "_iterator2", "_step2", "_callee3$", "_context3", "selectListExchangerate", "validFrom", "validTo", "buyRate", "base", "sellRate", "err", "_callee4", "_iterator3", "_step3", "_callee4$", "_context4", "chargeCurrencyCode", "_this16", "_callee5", "_callee5$", "_context5", "getName", "filter", "rsStaff", "staffShortName", "selectStaff", "checkSelectable", "handleDialogOpened", "_this17", "$nextTick", "treeSelectInput", "treeSelect", "getInputElement", "focus", "isRecievingOrPayingNormalizer", "selectBankAccount", "sqdBelongToCompanyCode", "options", "_this18", "formData", "FormData", "append", "request", "method", "slipFile", "handleChange", "extension", "substring", "lastIndexOf", "newFileName", "bankRecordNo", "File", "raw", "exports", "_default"], "sources": ["src/views/system/bankrecord/basicBankRecord.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n            <el-input\r\n              v-model=\"queryParams.isRecievingOrPaying\"\r\n              clearable\r\n              placeholder=\"收支标志\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属公司\" prop=\"sqdPaymentTitleCode\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdPaymentTitleCode\"\r\n              clearable\r\n              placeholder=\"所属公司\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankAccountCode\"\r\n              clearable\r\n              placeholder=\"银行账户\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算公司\" prop=\"sqdClearingCompanyShortname\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdClearingCompanyShortname\"\r\n              clearable\r\n              placeholder=\"结算公司简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"费用描述\" prop=\"chargeDescription\">\r\n            <el-input\r\n              v-model=\"queryParams.chargeDescription\"\r\n              clearable\r\n              placeholder=\"费用描述\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行币种\" prop=\"bankCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankCurrencyCode\"\r\n              clearable\r\n              placeholder=\"银行币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"结算方式\" prop=\"paymentTypeCode\">\r\n            <el-input\r\n              v-model=\"queryParams.paymentTypeCode\"\r\n              clearable\r\n              placeholder=\"结算方式\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"凭证号\" prop=\"voucherNo\">\r\n            <el-input\r\n              v-model=\"queryParams.voucherNo\"\r\n              clearable\r\n              placeholder=\"凭证号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"流水备注\" prop=\"bankRecordRemark\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordRemark\"\r\n              clearable\r\n              placeholder=\"银行流水备注\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"录入人\" prop=\"bankRecordByStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordByStaffId\"\r\n              clearable\r\n              placeholder=\"银行流水录入人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"录入时间 ,\" prop=\"bankRecordUpdateTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordUpdateTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水录入时间 ,\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属员工\" prop=\"sqdRsStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdRsStaffId\"\r\n              clearable\r\n              placeholder=\"所属员工\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.writeoffStaffId\"\r\n              clearable\r\n              placeholder=\"销账人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账时间\" prop=\"writeoffTime\">\r\n            <el-date-picker v-model=\"queryParams.writeoffTime\"\r\n                            clearable\r\n                            placeholder=\"销账时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:edit']\"\r\n              :disabled=\"single\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleImport\"\r\n            >导入\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"bankrecordList\" highlight-current-row\r\n                  stripe @selection-change=\"handleSelectionChange\" @row-dblclick=\"handledbClick\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"银行流水\" prop=\"bankRecordNo\"/>\r\n          <el-table-column align=\"center\" label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.isRecievingOrPaying == 1 ? \"付\" : \"收\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"sqdPaymentTitleCode\"/>\r\n          <el-table-column align=\"center\" label=\"结算公司\" prop=\"sqdClearingCompanyShortname\"/>\r\n          <el-table-column align=\"center\" label=\"银行时间\" prop=\"bankRecordTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.bankRecordTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"银行账户\" prop=\"bankAccountCode\"/>\r\n          <el-table-column align=\"center\" label=\"水单金额\" prop=\"slipAmount\"/>\r\n          <el-table-column align=\"center\" label=\"实收金额\" prop=\"actualBankRecievedAmount\"/>\r\n          <el-table-column align=\"center\" label=\"收款手续费\" prop=\"bankRecievedHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"实付金额\" prop=\"actualBankPaidAmount\"/>\r\n          <el-table-column align=\"center\" label=\"付款手续费\" prop=\"bankPaidHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"销账状态 \" prop=\"writeoffStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.writeoffStatus == 1 ? \"=\" : (scope.row.writeoffStatus == 0 ? \"√\" : \"-\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"相关操作单号\" prop=\"sqdRaletiveRctList\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <template slot-scope=\"scope\">\r\n              {{ getName(scope.row.writeoffStaffId) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"销账备注\" prop=\"bankRecordRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账时间\" prop=\"writeoffTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.writeoffTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改记录公司账户出入账明细对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      append-to-body\r\n      height=\"60%\"\r\n      width=\"60%\"\r\n      @close=\"showDetail=false\"\r\n    >\r\n      <el-form v-if=\"open\" ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"12\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"银行流水\" prop=\"voucherNo\">\r\n                <el-input :value=\"form.bankRecordNo\" class=\"disable-form\" disabled placeholder=\"银行流水\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n                <treeselect ref=\"treeSelect\" v-model=\"form.isRecievingOrPaying\"\r\n                            :auto-focus=\"true\"\r\n                            :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\" :disable-branch-nodes=\"true\"\r\n                            :disable-fuzzy-matching=\"true\"\r\n                            :disabled=\"isLocked||isBankSlipConfirmed\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"isRecievingOrPayingNormalizer\"\r\n                            :options=\"[{label:'实收',value:'0'},{label:'实付',value:'1'}]\" :show-count=\"true\"\r\n                            placeholder=\"选择收付信息\" @select=\"form.isRecievingOrPaying=$event.value\"\r\n                >\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n                <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                             :disabled=\"isLocked||isBankSlipConfirmed\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.bankAccountCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\"\r\n                             @return=\"form.bankAccountCode=$event\" @returnData=\"selectBankAccount\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"所属员工\">\r\n                <el-select v-model=\"form.sqdRsStaffId\" :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                           :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                           filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"staff in staffList\"\r\n                    :key=\"staff.staffId\"\r\n                    :label=\"staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                    :value=\"staff.staffId\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"费用描述\" prop=\"voucherNo\">\r\n                <el-row>\r\n                  <el-col :span=\"8\">\r\n                    <tree-select :charge-name-type=\"5\" :pass=\"form.chargeTypeId\" :placeholder=\"'费用类型'\"\r\n                                 :type=\"'chargeNameType'\" style=\"width: 100%\" @return=\"form.chargeTypeId=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.chargeDescription\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                              placeholder=\"费用描述\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.bankRecordTime\"\r\n                  :class=\"isLocked?'disable-form':''\"\r\n                  :disabled=\"isLocked\"\r\n                  clearable\r\n                  default-time=\"12:00:00\"\r\n                  placeholder=\"银行时间\"\r\n                  style=\"width: 100%\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"实收金额\" prop=\"actualBankRecievedAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                                 :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                                 :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankRecievedAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实收金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"手续费\" prop=\"bankRecievedExchangeLost\">\r\n                <el-input v-model=\"form.bankRecievedHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收款记账\" prop=\"sqdBillRecievedAmount\">\r\n                <el-input v-model=\"form.sqdBillRecievedAmount\" :class=\"'disable-form'\" disabled\r\n                          placeholder=\"收款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收账已销\" prop=\"billRecievedWriteoffAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.billRecievedWriteoffAmount\" :class=\"'disable-form'\"\r\n                              disabled placeholder=\"收账已销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-input v-model=\"form.bankRecievedExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                              :disabled=\"!isLocked\" placeholder=\"收款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" :disabled=\"isLocked\" label=\"收账未销\"\r\n                            prop=\"sqdBillRecievedWriteoffBalance\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillRecievedWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"收账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillRecievedWriteoffBalance===form.sqdBillRecievedAmount?'-':form.sqdBillRecievedWriteoffBalance===0?'√':'='\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"实付金额\" prop=\"actualBankPaidAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankPaidAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实付金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"手续费\" prop=\"bankPaidExchangeLost\">\r\n                <el-input v-model=\"form.bankPaidHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付款记账\" prop=\"sqdBillPaidAmount\">\r\n                <el-input v-model=\"form.sqdBillPaidAmount\" class=\"disable-form\" disabled\r\n                          placeholder=\"付款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账已销\" prop=\"billPaidWriteoffAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.billPaidWriteoffAmount\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账已销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-input v-model=\"form.bankPaidExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                              :disabled=\"!isLocked\" placeholder=\"付款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账未销\" prop=\"sqdBillPaidWriteoffBalance\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillPaidWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillPaidAmount===form.sqdBillPaidWriteoffBalance?'-':form.sqdBillPaidWriteoffBalance===0?'√':'='\"\r\n                      placeholder=\"收款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行备注\" prop=\"bankRecordRemark\">\r\n                <el-input v-model=\"form.bankRecordRemark\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"银行备注\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col v-if=\"!this.form.bankRecordId\" :span=\"1.5\">\r\n              <el-button :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" style=\"float: right\" type=\"primary\"\r\n                         @click=\"submitForm\"\r\n              >{{ this.form.bankRecordId === null ? \"新增\" : \"保存\" }}\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <div style=\"display: flex\">\r\n                <el-button :icon=\"form.isBankRecordLocked==1?'el-icon-check':''\" type=\"primary\"\r\n                           v-if=\"form.bankRecordId\" @click=\"verify\"\r\n                >\r\n                  {{ form.isBankRecordLocked == 1 ? \"已审核\" : \"出账审核\" }}\r\n                </el-button>\r\n                <div v-if=\"form.isBankRecordLocked == 1 \">\r\n                  <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                    {{ (form.verifyId ? getName(form.verifyId) : \"\") }}\r\n                  </div>\r\n                  <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                    {{ parseTime(form.verifyTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-button :disabled=\"!isLocked\" :loading=\"loadingCharge\" style=\"float: right\" type=\"primary\"\r\n                         @click=\"getCompanyCharges\"\r\n              >\r\n                调取相关费用明细\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table\r\n        v-if=\"showDetail\"\r\n        ref=\"writeOffTable\"\r\n        :data=\"writeOffList\"\r\n        border\r\n        max-height=\"315px\"\r\n        size=\"mini\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column type=\"index\" width=\"20\"/>\r\n        <el-table-column\r\n          :selectable=\"checkSelectable\"\r\n          type=\"selection\"\r\n          width=\"35\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"审核\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"30\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.isAccountConfirmed === \"1\" ? \"√\" : \"-\" }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"Rct号\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"150\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"费用名称\"\r\n          prop=\"chargeName\"\r\n          width=\"80\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"备注\"\r\n          prop=\"address\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"收付标志\"\r\n          prop=\"dnCurrencyCode\"\r\n          width=\"100\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.isRecievingOrPaying == 1 ? (\"应付\" + scope.row.dnCurrencyCode) : (\"应收\" + scope.row.dnCurrencyCode)\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"应收/付金额\"\r\n          prop=\"dnUnitRate\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ currency(scope.row.dnUnitRate).multiply(scope.row.dnAmount).value }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"销账余额\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"本次拟销账金额\"\r\n          prop=\"sqdDnCurrencyBalance\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.writeoffFromDnBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"汇率展示\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.exchangeRateShow\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"折算记账金额\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-input-number v-model=\"scope.row.writeoffFromBankBalance\" :controls=\"false\" autocomplete=\"off\"\r\n                             style=\"width: 100%; \"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账人\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ getName(scope.row.midChargeBankWriteoff.writeoffStaffId) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账时间\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.midChargeBankWriteoff.writeoffTime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"销账状态\"\r\n          width=\"50\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.writeoffStatus === \"0\" ? \"√\" : scope.row.writeoffStatus === \"1\" ? \"=\" : \"-\"\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <!--总计-->\r\n      <div v-if=\"showDetail\" class=\"total\">\r\n        <div style=\"width: 30%;\">全部总计: {{ totalAmount }}</div>\r\n        <div style=\"width: 30%;\">已选总计:\r\n          {{\r\n            this.form.isRecievingOrPaying == 0 ? this.form.billRecievedWriteoffAmount : this.form.billPaidWriteoffAmount\r\n          }}\r\n        </div>\r\n        <div style=\"width: 30%;\">已选余额总计: {{ form.bankCurrencyCode }} {{ selectedBalanceAmount }}</div>\r\n      </div>\r\n\r\n      <!--按钮区-->\r\n      <div v-if=\"showDetail\" class=\"table-btn-group\">\r\n        <div class=\"table-btn-left\">\r\n          <el-button type=\"primary\" @click=\"invertSelection\">反选</el-button>\r\n          <el-button type=\"primary\" @click=\"autoSelection\">智选</el-button>\r\n          <el-button type=\"primary\" @click=\"addHedging\">增加对冲</el-button>\r\n          <el-button type=\"primary\" @click=\"projectRemove\">项目去除</el-button>\r\n          <el-button type=\"primary\" @click=\"print\">打印</el-button>\r\n        </div>\r\n        <div class=\"table-btn-right\">\r\n          <el-button style=\"float: right\" type=\"primary\" @click=\"writeOffConfirm\">确定销账</el-button>\r\n        </div>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"bankSlipPreview\"\r\n        append-to-body destroy-on-close\r\n        height=\"50%\"\r\n        width=\"50%\"\r\n      >\r\n        <el-image :src=\"form.slipFile\" style=\"margin-top: 20px;\"/>\r\n      </el-dialog>\r\n    </el-dialog>\r\n\r\n    <!--    // excel 上传导入组件-->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"upload.title\" :visible.sync=\"upload.open\"\r\n      append-to-body width=\"400px\"\r\n    >\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\r\n        :auto-upload=\"false\"\r\n        :disabled=\"upload.isUploading\"\r\n        :headers=\"upload.headers\"\r\n        :limit=\"1\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        accept=\".xlsx, .xls\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div slot=\"tip\" class=\"el-upload__tip text-center\">\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <!--<el-link :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" type=\"primary\"\r\n                   @click=\"importTemplate\"\r\n          >下载模板\r\n          </el-link>-->\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addBankrecord,\r\n  changeStatus,\r\n  delBankrecord,\r\n  getBankrecord,\r\n  listBankrecord,\r\n  updateBankrecord\r\n} from \"@/api/system/bankrecord\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport currency from \"currency.js\"\r\nimport {chargeWriteOff, selectListCharge, turnBackWriteoff} from \"@/api/system/rsCharge\"\r\nimport {selectListExchangerate} from \"@/api/system/exchangerate\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport log from \"@/views/monitor/job/log.vue\"\r\nimport moment from \"moment\"\r\nimport request from \"@/utils/request\"\r\nimport {getToken} from \"@/utils/auth\"\r\n\r\nexport default {\r\n  name: \"reimbursementBankRecord\",\r\n  components: {Treeselect, CompanySelect},\r\n  data() {\r\n    return {\r\n      writeOffList: [],\r\n      salesId: null,\r\n      belongList: [],\r\n      staffList: [],\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 记录公司账户出入账明细表格数据\r\n      bankrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: null,\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: false,\r\n        // 设置上传的请求头部\r\n        headers: {Authorization: \"Bearer \" + getToken()},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/system/bankrecord/importData\"\r\n      },\r\n      // 表单校验\r\n      rules: {\r\n        actualBankRecievedAmount: [\r\n          {required: true, message: \"请输入实收信息\", trigger: \"blur\"}\r\n        ],\r\n        bankRecordTime: [\r\n          {required: true, message: \"请输入银行时间\", trigger: \"blur\"}\r\n        ],\r\n        actualBankPaidAmount: [\r\n          {required: true, message: \"请输入实付信息\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      add: false,\r\n      showDetail: false,\r\n      selectedCharges: [],\r\n      totalAmount: null,\r\n      selectedAmount: null,\r\n      selectedBalanceAmount: null,\r\n      loadingCharge: false,\r\n      staffId: null,\r\n      alreadyWriteoffList: [],\r\n      turnBackWriteoffList: [],\r\n      showCompany: false,\r\n      companyList: [],\r\n      bankSlipPreview: false,\r\n      imageFile: null\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    // 实收金额\r\n    \"form.actualBankRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款手续费\r\n    \"form.bankRecievedHandlingFee\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款损益\r\n    \"form.bankRecievedExchangeLost\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 实付金额\r\n    \"form.actualBankPaidAmount\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款手续费\r\n    \"form.bankPaidHandlingFee\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款损益\r\n    \"form.bankPaidExchangeLost\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收款记账\r\n    \"form.sqdBillRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付款记账\r\n    \"form.sqdBillPaidAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收账已销\r\n    \"form.billRecievedWriteoffAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付账已销\r\n    \"form.billPaidWriteoffAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n    /*      // 收账未销\r\n         'form.sqdBillRecievedWriteoffBalance'(n) {\r\n           this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n         },\r\n         // 付账未销\r\n         'form.sqdBillPaidWriteoffBalance'(n) {\r\n           this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n         }, */\r\n    selectedCharges: {\r\n      handler: function (newVal, oldVal) {\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = 0\r\n\r\n        // 已选总计\r\n        this.selectedBalanceAmount = 0\r\n\r\n        let billRecievedWriteoffAmount = 0\r\n        let billPaidWriteoffAmount = 0\r\n\r\n        let selectedBalanceAmount = 0\r\n        newVal.map(item => {\r\n          // 本次拟销账金额\r\n          item.writeoffFromDnBalance = currency(item.writeoffFromBankBalance).divide(item.exchangeRate).value\r\n\r\n          // 收账已销\r\n          billRecievedWriteoffAmount += currency(item.writeoffFromBankBalance).value\r\n          // 付账已销\r\n          billPaidWriteoffAmount += currency(item.writeoffFromBankBalance).value\r\n\r\n          // 已选余额总计\r\n          selectedBalanceAmount = currency(item.sqdDnCurrencyBalance).add(selectedBalanceAmount).value\r\n\r\n          // 已选总计\r\n          this.selectedAmount = null\r\n\r\n          currency(item.dnUnitRate).multiply(item.dnAmount).value === item.writeoffFromDnBalance ? item.writeoffStatus = \"0\" : item.writeoffFromDnBalance > 0 ? item.writeoffStatus = \"1\" : item.writeoffStatus = \"-1\"\r\n        })\r\n\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = billRecievedWriteoffAmount\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = billPaidWriteoffAmount\r\n\r\n        this.selectedBalanceAmount = selectedBalanceAmount\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSales()\r\n    this.loadStaff()\r\n    this.getExchangeRate(\"USD\", \"RMB\", null, val => {\r\n      console.log(val)\r\n    })\r\n  },\r\n  computed: {\r\n    receiveRate() {\r\n      return this.form.actualBankRecievedAmount + this.form.bankRecievedHandlingFee + this.form.bankRecievedExchangeLost\r\n    },\r\n    paidRate() {\r\n      return this.form.actualBankPaidAmount + this.form.bankPaidHandlingFee + this.form.bankPaidExchangeLost\r\n    },\r\n    isLocked() {\r\n      return this.form.isBankRecordLocked == 1\r\n    },\r\n    isBankSlipConfirmed() {\r\n      return this.form.slipConfirmed == 1\r\n    }\r\n  },\r\n  methods: {\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false\r\n      this.upload.isUploading = false\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info(response.msg)\r\n      if (response.msg != \"全部上传成功\") {\r\n        this.download(\"system/freight/failList\", {}, `上传失败列表.xlsx`)\r\n      }\r\n      this.getList()\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit()\r\n    },\r\n    handleImport() {\r\n      this.upload.title = \"用户导入\"\r\n      this.upload.open = true\r\n    },\r\n    handleSearch(type) {\r\n      switch (type) {\r\n        case \"common\":\r\n          this.getList({})\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    },\r\n    parseTime,\r\n    updateSlipSatus() {\r\n      if (this.form.slipConfirmed == 1) {\r\n        this.form.slipConfirmed = \"0\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单取消确认\")\r\n        })\r\n      } else {\r\n        this.form.slipConfirmed = \"1\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单确认\")\r\n        })\r\n      }\r\n\r\n    },\r\n    writeOffConfirm() {\r\n      this.clearReceiveOrPay()\r\n      // 更新银行流水\r\n      let rctSet = new Set()\r\n      this.selectedCharges.map(item => rctSet.add(item.sqdRctNo))\r\n      let rctArr = []\r\n      rctSet.forEach(item => rctArr.push(item))\r\n      this.form.sqdRaletiveRctList = rctArr.toString()\r\n      this.form.writeoffTime = moment().format(\"yyyy-MM-DD\")\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n\r\n      // 取消销账的费用,进行回退\r\n      turnBackWriteoff({rsChargeList: this.turnBackWriteoffList})\r\n\r\n      // 更新费用中的销账余额(更新费用时插入中间表)\r\n      let midChargeBankWriteoffs = []\r\n      this.selectedCharges.map(item => {\r\n        // 每次销账的时候再计算余额写回费用明细中\r\n        item.sqdDnCurrencyBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).subtract(item.writeoffFromDnBalance).value\r\n        item.clearingCurrencyCode = this.form.bankCurrencyCode\r\n        item.writeoffStatus = currency(item.sqdDnCurrencyBalance).value === currency(item.dnUnitRate).multiply(item.dnAmount).value ? \"-1\" : currency(item.sqdDnCurrencyBalance).value === 0 ? \"0\" : \"1\"\r\n        let midChargeBankWriteoff = {}\r\n        midChargeBankWriteoff.chargeId = item.chargeId\r\n        midChargeBankWriteoff.bankRecordId = this.form.bankRecordId\r\n        midChargeBankWriteoff.writeoffFromDnBalance = item.writeoffFromDnBalance\r\n        midChargeBankWriteoff.exchangeRateShowing = item.exchangeRateShow\r\n        midChargeBankWriteoff.writeoffFromBankBalance = item.writeoffFromBankBalance\r\n        midChargeBankWriteoff.dnBasicRate = item.exchangeRate\r\n\r\n        midChargeBankWriteoffs.push(midChargeBankWriteoff)\r\n      })\r\n\r\n      chargeWriteOff({\r\n        rsChargeList: this.selectedCharges,\r\n        midChargeBankWriteoffList: midChargeBankWriteoffs\r\n      }).then(response => {\r\n\r\n      })\r\n\r\n    },\r\n    invertSelection() {\r\n      this.writeOffList.forEach(row => {\r\n        // if (this.selectedCharges.indexOf(row) !== -1) {\r\n        this.$refs.writeOffTable.toggleRowSelection(row)\r\n        // }\r\n      })\r\n    },\r\n    autoSelection() {\r\n    },\r\n    addHedging() {\r\n    },\r\n    projectRemove() {\r\n    },\r\n    print() {\r\n    },\r\n    currency,\r\n    async getCompanyCharges() {\r\n      this.writeOffList = []\r\n      this.loadingCharge = true\r\n      // 销完的流水只展示销账记录\r\n      let writeoffStatus\r\n      if (this.form.isRecievingOrPaying === \"0\" && this.form.sqdBillRecievedWriteoffBalance === 0) {\r\n        writeoffStatus = \"ALL\"\r\n      } else if (this.form.isRecievingOrPaying === \"1\" && this.form.sqdBillPaidWriteoffBalance === 0) {\r\n        writeoffStatus = \"ALL\"\r\n      } else if (this.form.billRecievedWriteoffAmount !== null || this.form.billPaidWriteoffAmount !== null) {\r\n        // 不是已销完的流水,费用过滤掉已经销账完成的费用\r\n        writeoffStatus = \"Part\"\r\n      }\r\n\r\n      let response = await selectListCharge({\r\n        clearingCompanyId: this.form.clearingCompanyId,\r\n        isRecievingOrPaying: this.form.isRecievingOrPaying,\r\n        bankRecordId: this.form.bankRecordId,\r\n        writeoffStatus: writeoffStatus\r\n      })\r\n      if (response && response.length > 0) {\r\n        for (let item of response) {\r\n          if (this.form.bankCurrencyCode === item.dnCurrencyCode) {\r\n            item.exchangeRateShow = 1\r\n            item.exchangeRate = 1\r\n          } else {\r\n            // 当天汇率\r\n            let result = await this.getExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, \"\")\r\n            if (!result) {\r\n              // 没有找到对应的汇率\r\n              this.$message.error(\"系统中没有对应的汇率\")\r\n              this.loadingCharge = false\r\n              return\r\n            }\r\n            // 账单日期汇率\r\n            let billExchangeRate = await this.getBillDataExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, item.currencyRateCalculateDate)\r\n            item.exchangeRate = result[0] === 1 ? result[1] : currency(1).divide(result[1]).value\r\n            item.exchangeRateShow = result[0] === 1 ? result[1] : \"1/\" + result[1]\r\n          }\r\n        }\r\n        this.writeOffList = response\r\n        setTimeout(() => {\r\n          this.writeOffList.map(item => {\r\n            // 有销账记录的要勾选并显示上次销账信息\r\n            if (item.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              // 填入信息\r\n              // 本次拟销账金额\r\n              // 销账余额\r\n              item.writeoffFromDnBalance = item.midChargeBankWriteoff.writeoffFromDnBalance\r\n              item.sqdDnCurrencyBalance = item.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n              // 折算记账金额\r\n              item.writeoffFromBankBalance = item.midChargeBankWriteoff.writeoffFromBankBalance\r\n\r\n              this.selectedCharges.push(item)\r\n              this.alreadyWriteoffList.push(item)\r\n              this.$refs.writeOffTable.toggleRowSelection(item, true)\r\n            }\r\n          })\r\n\r\n          //排序\r\n          this.writeOffList.sort((a, b) => {\r\n            // 未审核排最后\r\n            if (a.isAccountConfirmed === \"0\" && b.isAccountConfirmed === \"1\") {\r\n              return 1\r\n            }\r\n            if (a.isAccountConfirmed === \"1\" && b.isAccountConfirmed === \"0\") {\r\n              return -1\r\n            }\r\n\r\n            // 如果a对象有 midChargeBankWriteoff.midChargeBankId，而b没有，则a排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId === null && b.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              return 1\r\n            }\r\n            // 如果b对象有 midChargeBankWriteoff.midChargeBankId，而a没有，则b排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId !== null && b.midChargeBankWriteoff.midChargeBankId === null) {\r\n              return -1\r\n            }\r\n            // 检查是否都有 midChargeBankWriteoff.midChargeBankId 属性\r\n            if (a.midChargeBankWriteoff.midChargeBankId && b.midChargeBankWriteoff.midChargeBankId) {\r\n              // 如果两个对象都有 midChargeBankWriteoff.midChargeBankId，则按 writeoffFromDnBalance 进行排序\r\n              return a.midChargeBankWriteoff.sqdDnCurrencyBalance - b.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n            } else {\r\n              // 如果有一个或两个对象缺少 midChargeBankWriteoff.midChargeBankId 属性，则保持原顺序\r\n              return 0\r\n            }\r\n          })\r\n        })\r\n        // this.$nextTick()\r\n      }\r\n      this.loadingCharge = false\r\n      this.showDetail = true\r\n    },\r\n    verify() {\r\n      if (this.form.isBankRecordLocked == 1) {\r\n        this.form.isBankRecordLocked = 0\r\n        this.form.verifyId = null\r\n        this.form.verifyTime = null\r\n      } else {\r\n        this.form.isBankRecordLocked = 1\r\n        this.form.verifyId = this.$store.state.user.sid\r\n        this.form.verifyTime = moment().format(\"yyyy-MM-DD\")\r\n      }\r\n\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$message.success(\"修改成功\")\r\n      })\r\n    },\r\n    /** 查询记录公司账户出入账明细列表 */\r\n    getList() {\r\n      this.loading = true\r\n      this.queryParams.chargeTypeId = 5\r\n      listBankrecord(this.queryParams).then(response => {\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n// 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    }\r\n    ,\r\n// 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankRecordId: null,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: \"0\",\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        invoiceNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        chargeType: \"订单\"\r\n      }\r\n      this.staffId = null\r\n      this.selectedBalanceAmount = 0\r\n      this.resetForm(\"form\")\r\n    }\r\n    ,\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    }\r\n    ,\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    }\r\n    ,\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.bankRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    }\r\n    ,\r\n// 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      // this.ids = selection.map(item => item.bankRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n\r\n      // 勾选时计算销账金额\r\n      this.selectedAmount = 0\r\n      this.turnBackWriteoffList = []\r\n      selection.map(item => {\r\n        // 上次勾选的不受影响\r\n        if (this.selectedCharges.indexOf(item) === -1) {\r\n          // 自动填入拟销账金额\r\n          if (item.sqdDnCurrencyBalance !== null && item.sqdDnCurrencyBalance > 0) {\r\n            // 本次拟销账金额\r\n            item.writeoffFromDnBalance = currency(item.sqdDnCurrencyBalance).value\r\n            // 折算记账金额\r\n            item.writeoffFromBankBalance = currency(item.sqdDnCurrencyBalance).multiply(item.exchangeRate).value\r\n          } else {\r\n            item.writeoffFromDnBalance = currency(item.dnUnitRate).multiply(item.dnAmount).value\r\n            item.writeoffFromBankBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).multiply(item.exchangeRate).value\r\n          }\r\n        }\r\n\r\n      })\r\n      // 取消勾选的费用本次拟核销金额置为0\r\n      this.selectedCharges.map(item => {\r\n        if (selection.indexOf(item) === -1) {\r\n          item.writeoffFromDnBalance = 0\r\n          item.writeoffFromBankBalance = 0\r\n          item.sqdDnCurrencyBalanceShow = currency(item.sqdDnCurrencyBalance).value\r\n\r\n          // 找出取消销账的费用\r\n          if (this.alreadyWriteoffList.indexOf(item) !== -1) {\r\n            this.turnBackWriteoffList.push(item)\r\n          }\r\n        }\r\n      })\r\n\r\n      this.selectedCharges = selection\r\n    }\r\n    ,\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.showDetail = false\r\n      this.open = true\r\n      this.title = \"新建银行流水\"\r\n      this.add = true\r\n      this.form.isRecievingOrPaying = \"1\"\r\n      this.form.chargeType = \"\"\r\n      this.form.chargeTypeId = 4\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.add = false\r\n      this.reset()\r\n      const bankRecordId = row.bankRecordId || this.ids\r\n      getBankrecord(bankRecordId).then(response => {\r\n        this.form = response.data\r\n        this.form.chargeType = \"订单\"\r\n        this.open = true\r\n        this.title = \"查看银行流水-销账明细\"\r\n        this.companyList = [response.companyList]\r\n\r\n        if (this.form.isBankRecordLocked === \"1\" && ((response.data.isRecievingOrPaying === \"0\" && currency(response.data.sqdBillRecievedWriteoffBalance).value !== 0) || (response.data.isRecievingOrPaying === \"1\" && currency(response.data.sqdBillPaidWriteoffBalance).value !== 0))) {\r\n          this.getCompanyCharges()\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(async valid => {\r\n        if (valid) {\r\n          // 收款时将付款信息清空,付款时将收款信息清空\r\n          this.clearReceiveOrPay()\r\n          if (this.form.bankRecordId != null) {\r\n            updateBankrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addBankrecord(this.form).then(response => {\r\n              this.form = response.data\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    uploadImage() {\r\n      return new Promise((resolve, reject) => {\r\n        console.log(this.imageFile)\r\n        this.customHttpRequest({\r\n          file: this.imageFile,\r\n          onSuccess: (response) => {\r\n            this.handleSuccess(response)\r\n            resolve(response)\r\n          },\r\n          onError: (error) => {\r\n            this.handleError(error)\r\n            reject(error)\r\n          }\r\n        })\r\n      })\r\n    },\r\n    clearReceiveOrPay() {\r\n      if (this.form.isRecievingOrPaying === \"0\") {\r\n        // 收款\r\n        this.form.actualBankPaidAmount = 0\r\n        this.form.bankPaidHandlingFee = 0\r\n        this.form.sqdBillPaidAmount = 0\r\n        this.form.billPaidWriteoffAmount = 0\r\n        this.form.bankPaidExchangeLost = 0\r\n        this.form.sqdBillPaidWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillRecievedWriteoffBalance === this.form.sqdBillRecievedAmount ? this.form.writeoffStatus = -1 : this.form.sqdBillRecievedWriteoffBalance === 0 ? this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      } else {\r\n        // 付款\r\n        this.form.actualBankRecievedAmount = 0\r\n        this.form.bankRecievedHandlingFee = 0\r\n        this.form.sqdBillRecievedAmount = 0\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        this.form.bankRecievedExchangeLost = 0\r\n        this.form.sqdBillRecievedWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillPaidAmount === this.form.sqdBillPaidWriteoffBalance ? this.form.writeoffStatus = -1 : this.form.sqdBillPaidWriteoffBalance === 0 ? this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const bankRecordIds = row.bankRecordId || this.ids\r\n      this.$confirm(\"是否确认删除记录公司账户出入账明细编号为\\\"\" + bankRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delBankrecord(bankRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    }\r\n    ,\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/bankrecord/export\", {\r\n        ...this.queryParams\r\n      }, `bankrecord_${new Date().getTime()}.xlsx`)\r\n    }\r\n    ,\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n    ,\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    handledbClick(row) {\r\n      this.handleUpdate(row)\r\n    }\r\n    ,\r\n    selectCompany(company) {\r\n      this.form.clearingCompanyId = company.companyId\r\n      this.form.sqdClearingCompanyShortname = company.companyShortName\r\n      this.showCompany = false\r\n    }\r\n    ,\r\n    async getExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency === localCurrency || a.currency === localCurrency) && (a.currency === overseaCurrency || a.localCurrency === overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(new Date()) && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    },\r\n    async getBillDataExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re = [0, 1]\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency == localCurrency || a.currency == localCurrency)\r\n            && (a.currency == overseaCurrency || a.localCurrency == overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(valueDate)\r\n            && parseTime(valueDate) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    }\r\n    ,\r\n    async exchangeRateShow(bankCurrencyCode, chargeCurrencyCode) {\r\n      if (bankCurrencyCode === chargeCurrencyCode) {\r\n        return 1\r\n      }\r\n      let result = await this.getExchangeRate(bankCurrencyCode, chargeCurrencyCode, \"\")\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        if (id) {\r\n          let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n          if (staff) {\r\n            return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n          } else {\r\n            return \"\"\r\n          }\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    selectStaff(row) {\r\n      this.form.sqdRsStaffId = row.staff.staffId\r\n    },\r\n    checkSelectable(row) {\r\n      return row.isAccountConfirmed === \"1\"\r\n    },\r\n    handleDialogOpened() {\r\n      // 在弹出层打开时，自动聚焦姓名输入框\r\n      this.$nextTick(() => {\r\n        const treeSelectInput = this.$refs.treeSelect.getInputElement()\r\n        if (treeSelectInput) {\r\n          treeSelectInput.focus()\r\n        }\r\n      })\r\n    },\r\n    isRecievingOrPayingNormalizer(node) {\r\n      return {\r\n        id: node.value,\r\n        label: node.label\r\n      }\r\n    },\r\n    selectBankAccount(row) {\r\n      this.form.sqdPaymentTitleCode = row.sqdBelongToCompanyCode\r\n    },\r\n    customHttpRequest(options) {\r\n      const formData = new FormData()\r\n      formData.append(\"file\", options.file)\r\n\r\n      request({\r\n        url: \"/system/bankrecord/uploadImg\",\r\n        method: \"post\",\r\n        data: formData\r\n      }).then(response => {\r\n        options.onSuccess(response, options.file)\r\n        console.log(response.url)\r\n        this.form.slipFile = response.url\r\n      }).catch(error => {\r\n        options.onError(error)\r\n      })\r\n    },\r\n    handleChange(file, fileList) {\r\n      const extension = file.name.substring(file.name.lastIndexOf(\".\"))\r\n      const newFileName = `${this.form.bankRecordNo}${extension}`\r\n      this.imageFile = new File([file.raw], newFileName, {type: file.type})\r\n    },\r\n    handleSuccess(response, file, fileList) {\r\n      this.form.slipFile = response.url\r\n    },\r\n    handleError(err, file, fileList) {\r\n      this.$message.error(\"Upload failed:\", err)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.table-btn-group {\r\n  display: flex;\r\n\r\n  .table-btn-left {\r\n    display: flex;\r\n    width: 100%;\r\n  }\r\n\r\n  .table-btn-right {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.total {\r\n  display: flex;\r\n  width: 60%;\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n::v-deep .el-input-number.is-without-controls .el-input__inner {\r\n  background-color: rgb(255, 242, 204) !important;\r\n}\r\n\r\n::v-deep .vue-treeselect__value-container {\r\n  display: block;\r\n}\r\n\r\n//固定el-table中行高度,不会被内容撑高\r\n::v-deep .cell {\r\n  overflow: hidden; /* 隐藏超出部分 */\r\n  white-space: nowrap; /* 禁止内容换行 */\r\n  text-overflow: ellipsis; /* 使用省略号表示超出的内容 */\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAouBA,IAAAA,WAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,SAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,IAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,OAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,QAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,KAAA,GAAAb,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAc,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,OAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;MACA;MACA;MACAC,IAAA;MACAC,MAAA;QACA;QACAzC,IAAA;QACA;QACAD,KAAA;QACA;QACA2C,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,KAAA;QACAvC,wBAAA,GACA;UAAAwC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA7B,cAAA,GACA;UAAA2B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAzC,oBAAA,GACA;UAAAuC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,GAAA;MACAC,UAAA;MACAC,eAAA;MACAC,WAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,aAAA;MACAC,OAAA;MACAC,mBAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,SAAA;IACA;EACA;EACAC,WAAA,WAAAA,YAAA;IACA,KAAAC,SAAA;EACA;EACAC,KAAA;IACA3E,UAAA,WAAAA,WAAA4E,CAAA;MACA,IAAAA,CAAA;QACA,KAAAjF,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACA;IACA,0CAAAmF,6BAAAD,CAAA;MACA,KAAAhC,IAAA,CAAAtB,qBAAA,OAAAwD,iBAAA,OAAAlC,IAAA,CAAA5B,wBAAA,EAAA2C,GAAA,MAAAf,IAAA,CAAA1B,uBAAA,EAAA6D,KAAA;IACA;IACA;IACA,yCAAAC,4BAAAJ,CAAA;MACA,KAAAhC,IAAA,CAAAtB,qBAAA,OAAAwD,iBAAA,OAAAlC,IAAA,CAAA5B,wBAAA,EAAA2C,GAAA,MAAAf,IAAA,CAAA1B,uBAAA,EAAA6D,KAAA;IACA;IACA;IACA,0CAAAE,6BAAAL,CAAA;MACA,KAAAhC,IAAA,CAAAlB,8BAAA,OAAAoD,iBAAA,OAAAlC,IAAA,CAAAtB,qBAAA,EAAA4D,QAAA,MAAAtC,IAAA,CAAApB,0BAAA,EAAA0D,QAAA,MAAAtC,IAAA,CAAAxB,wBAAA,EAAA2D,KAAA;IACA;IACA;IACA,sCAAAI,yBAAAP,CAAA;MACA,KAAAhC,IAAA,CAAArB,iBAAA,OAAAuD,iBAAA,OAAAlC,IAAA,CAAA3B,oBAAA,EAAAiE,QAAA,MAAAtC,IAAA,CAAAzB,mBAAA,EAAA4D,KAAA;IACA;IACA;IACA,qCAAAK,wBAAAR,CAAA;MACA,KAAAhC,IAAA,CAAArB,iBAAA,OAAAuD,iBAAA,OAAAlC,IAAA,CAAA3B,oBAAA,EAAAiE,QAAA,MAAAtC,IAAA,CAAAzB,mBAAA,EAAA4D,KAAA;IACA;IACA;IACA,sCAAAM,yBAAAT,CAAA;MACA,KAAAhC,IAAA,CAAAjB,0BAAA,OAAAmD,iBAAA,OAAAlC,IAAA,CAAArB,iBAAA,EAAA2D,QAAA,MAAAtC,IAAA,CAAAnB,sBAAA,EAAAyD,QAAA,MAAAtC,IAAA,CAAAvB,oBAAA,EAAA0D,KAAA;IACA;IAEA;IACA,uCAAAO,0BAAAV,CAAA;MACA,KAAAhC,IAAA,CAAAlB,8BAAA,OAAAoD,iBAAA,OAAAlC,IAAA,CAAAtB,qBAAA,EAAA4D,QAAA,MAAAtC,IAAA,CAAApB,0BAAA,EAAA0D,QAAA,MAAAtC,IAAA,CAAAxB,wBAAA,EAAA2D,KAAA;IACA;IACA;IACA,mCAAAQ,sBAAAX,CAAA;MACA,KAAAhC,IAAA,CAAAjB,0BAAA,OAAAmD,iBAAA,OAAAlC,IAAA,CAAArB,iBAAA,EAAA2D,QAAA,MAAAtC,IAAA,CAAAnB,sBAAA,EAAAyD,QAAA,MAAAtC,IAAA,CAAAvB,oBAAA,EAAA0D,KAAA;IACA;IAEA;IACA,4CAAAS,+BAAAZ,CAAA;MACA,KAAAhC,IAAA,CAAAlB,8BAAA,OAAAoD,iBAAA,OAAAlC,IAAA,CAAAtB,qBAAA,EAAA4D,QAAA,MAAAtC,IAAA,CAAApB,0BAAA,EAAA0D,QAAA,MAAAtC,IAAA,CAAAxB,wBAAA,EAAA2D,KAAA;IACA;IACA;IACA,wCAAAU,2BAAAb,CAAA;MACA,KAAAhC,IAAA,CAAAjB,0BAAA,OAAAmD,iBAAA,OAAAlC,IAAA,CAAArB,iBAAA,EAAA2D,QAAA,MAAAtC,IAAA,CAAAnB,sBAAA,EAAAyD,QAAA,MAAAtC,IAAA,CAAAvB,oBAAA,EAAA0D,KAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAlB,eAAA;MACA6B,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAC,KAAA;QACA;QACA,KAAAjD,IAAA,CAAApB,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA;;QAEA;QACA,KAAAuC,qBAAA;QAEA,IAAAxC,0BAAA;QACA,IAAAC,sBAAA;QAEA,IAAAuC,qBAAA;QACA2B,MAAA,CAAAG,GAAA,WAAAC,IAAA;UACA;UACAA,IAAA,CAAAC,qBAAA,OAAAlB,iBAAA,EAAAiB,IAAA,CAAAE,uBAAA,EAAAC,MAAA,CAAAH,IAAA,CAAAI,YAAA,EAAApB,KAAA;;UAEA;UACAvD,0BAAA,QAAAsD,iBAAA,EAAAiB,IAAA,CAAAE,uBAAA,EAAAlB,KAAA;UACA;UACAtD,sBAAA,QAAAqD,iBAAA,EAAAiB,IAAA,CAAAE,uBAAA,EAAAlB,KAAA;;UAEA;UACAf,qBAAA,OAAAc,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAAzC,GAAA,CAAAK,qBAAA,EAAAe,KAAA;;UAEA;UACAc,KAAA,CAAA9B,cAAA;UAEA,IAAAe,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,EAAAxB,KAAA,KAAAgB,IAAA,CAAAC,qBAAA,GAAAD,IAAA,CAAAnE,cAAA,SAAAmE,IAAA,CAAAC,qBAAA,OAAAD,IAAA,CAAAnE,cAAA,SAAAmE,IAAA,CAAAnE,cAAA;QACA;;QAEA;QACA,KAAAgB,IAAA,CAAApB,0BAAA,GAAAA,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA,GAAAA,sBAAA;QAEA,KAAAuC,qBAAA,GAAAA,qBAAA;MAEA;MACAwC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;IACA,KAAAjC,SAAA;IACA,KAAAkC,eAAA,+BAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;IACA;EACA;EACAG,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAArE,IAAA,CAAA5B,wBAAA,QAAA4B,IAAA,CAAA1B,uBAAA,QAAA0B,IAAA,CAAAxB,wBAAA;IACA;IACA8F,QAAA,WAAAA,SAAA;MACA,YAAAtE,IAAA,CAAA3B,oBAAA,QAAA2B,IAAA,CAAAzB,mBAAA,QAAAyB,IAAA,CAAAvB,oBAAA;IACA;IACA8F,QAAA,WAAAA,SAAA;MACA,YAAAvE,IAAA,CAAAT,kBAAA;IACA;IACAiF,mBAAA,WAAAA,oBAAA;MACA,YAAAxE,IAAA,CAAAyE,aAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAA7E,MAAA,CAAAC,WAAA;IACA;IACA;IACA6E,iBAAA,WAAAA,kBAAAC,QAAA,EAAAH,IAAA,EAAAC,QAAA;MACA,KAAA7E,MAAA,CAAAzC,IAAA;MACA,KAAAyC,MAAA,CAAAC,WAAA;MACA,KAAA+E,KAAA,CAAAhF,MAAA,CAAAiF,UAAA;MACA,KAAAC,QAAA,CAAAC,IAAA,CAAAJ,QAAA,CAAAK,GAAA;MACA,IAAAL,QAAA,CAAAK,GAAA;QACA,KAAAC,QAAA;MACA;MACA,KAAAxB,OAAA;IACA;IACA;IACAyB,cAAA,WAAAA,eAAA;MACA,KAAAN,KAAA,CAAAhF,MAAA,CAAAuF,MAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAxF,MAAA,CAAA1C,KAAA;MACA,KAAA0C,MAAA,CAAAzC,IAAA;IACA;IACAkI,YAAA,WAAAA,aAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAA7B,OAAA;UACA;QACA;UACA;MACA;IACA;IACA8B,SAAA,EAAAA,eAAA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAA9F,IAAA,CAAAyE,aAAA;QACA,KAAAzE,IAAA,CAAAyE,aAAA;QACA,IAAAsB,4BAAA,OAAA/F,IAAA,EAAAgG,IAAA,WAAAhB,QAAA;UACAc,MAAA,CAAAX,QAAA,CAAAc,OAAA;QACA;MACA;QACA,KAAAjG,IAAA,CAAAyE,aAAA;QACA,IAAAsB,4BAAA,OAAA/F,IAAA,EAAAgG,IAAA,WAAAhB,QAAA;UACAc,MAAA,CAAAX,QAAA,CAAAc,OAAA;QACA;MACA;IAEA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,iBAAA;MACA;MACA,IAAAC,MAAA,OAAAC,GAAA;MACA,KAAArF,eAAA,CAAAiC,GAAA,WAAAC,IAAA;QAAA,OAAAkD,MAAA,CAAAtF,GAAA,CAAAoC,IAAA,CAAAoD,QAAA;MAAA;MACA,IAAAC,MAAA;MACAH,MAAA,CAAAI,OAAA,WAAAtD,IAAA;QAAA,OAAAqD,MAAA,CAAAE,IAAA,CAAAvD,IAAA;MAAA;MACA,KAAAnD,IAAA,CAAAN,kBAAA,GAAA8G,MAAA,CAAAG,QAAA;MACA,KAAA3G,IAAA,CAAAD,YAAA,OAAA6G,eAAA,IAAAC,MAAA;MACA,IAAAd,4BAAA,OAAA/F,IAAA,EAAAgG,IAAA,WAAAhB,QAAA;QACAmB,MAAA,CAAAW,MAAA,CAAAC,UAAA;QACAZ,MAAA,CAAA3I,IAAA;QACA2I,MAAA,CAAArC,OAAA;MACA;;MAEA;MACA,IAAAkD,0BAAA;QAAAC,YAAA,OAAAzF;MAAA;;MAEA;MACA,IAAA0F,sBAAA;MACA,KAAAjG,eAAA,CAAAiC,GAAA,WAAAC,IAAA;QACA;QACAA,IAAA,CAAAK,oBAAA,OAAAtB,iBAAA,MAAAA,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,GAAArB,QAAA,CAAAa,IAAA,CAAAC,qBAAA,EAAAjB,KAAA;QACAgB,IAAA,CAAAgE,oBAAA,GAAAhB,MAAA,CAAAnG,IAAA,CAAA7B,gBAAA;QACAgF,IAAA,CAAAnE,cAAA,OAAAkD,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA,SAAAD,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,EAAAxB,KAAA,cAAAD,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA;QACA,IAAAiF,qBAAA;QACAA,qBAAA,CAAAC,QAAA,GAAAlE,IAAA,CAAAkE,QAAA;QACAD,qBAAA,CAAAE,YAAA,GAAAnB,MAAA,CAAAnG,IAAA,CAAAsH,YAAA;QACAF,qBAAA,CAAAhE,qBAAA,GAAAD,IAAA,CAAAC,qBAAA;QACAgE,qBAAA,CAAAG,mBAAA,GAAApE,IAAA,CAAAqE,gBAAA;QACAJ,qBAAA,CAAA/D,uBAAA,GAAAF,IAAA,CAAAE,uBAAA;QACA+D,qBAAA,CAAAK,WAAA,GAAAtE,IAAA,CAAAI,YAAA;QAEA2D,sBAAA,CAAAR,IAAA,CAAAU,qBAAA;MACA;MAEA,IAAAM,wBAAA;QACAT,YAAA,OAAAhG,eAAA;QACA0G,yBAAA,EAAAT;MACA,GAAAlB,IAAA,WAAAhB,QAAA,GAEA;IAEA;IACA4C,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAnL,YAAA,CAAA+J,OAAA,WAAAqB,GAAA;QACA;QACAD,MAAA,CAAA5C,KAAA,CAAA8C,aAAA,CAAAC,kBAAA,CAAAF,GAAA;QACA;MACA;IACA;IACAG,aAAA,WAAAA,cAAA,GACA;IACAC,UAAA,WAAAA,WAAA,GACA;IACAC,aAAA,WAAAA,cAAA,GACA;IACAC,KAAA,WAAAA,MAAA,GACA;IACAlG,QAAA,EAAAA,iBAAA;IACAmG,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,IAAA3J,cAAA,EAAAgG,QAAA,EAAA4D,SAAA,EAAAC,KAAA,EAAA1F,IAAA,EAAA2F,MAAA,EAAAC,gBAAA;QAAA,WAAAN,oBAAA,CAAAD,OAAA,IAAAQ,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAd,MAAA,CAAA5L,YAAA;cACA4L,MAAA,CAAAjH,aAAA;cACA;;cAEA,IAAAiH,MAAA,CAAAtI,IAAA,CAAApC,mBAAA,YAAA0K,MAAA,CAAAtI,IAAA,CAAAlB,8BAAA;gBACAE,cAAA;cACA,WAAAsJ,MAAA,CAAAtI,IAAA,CAAApC,mBAAA,YAAA0K,MAAA,CAAAtI,IAAA,CAAAjB,0BAAA;gBACAC,cAAA;cACA,WAAAsJ,MAAA,CAAAtI,IAAA,CAAApB,0BAAA,aAAA0J,MAAA,CAAAtI,IAAA,CAAAnB,sBAAA;gBACA;gBACAG,cAAA;cACA;cAAAkK,QAAA,CAAAE,IAAA;cAAA,OAEA,IAAAC,0BAAA;gBACAtL,iBAAA,EAAAuK,MAAA,CAAAtI,IAAA,CAAAjC,iBAAA;gBACAH,mBAAA,EAAA0K,MAAA,CAAAtI,IAAA,CAAApC,mBAAA;gBACA0J,YAAA,EAAAgB,MAAA,CAAAtI,IAAA,CAAAsH,YAAA;gBACAtI,cAAA,EAAAA;cACA;YAAA;cALAgG,QAAA,GAAAkE,QAAA,CAAAI,IAAA;cAAA,MAMAtE,QAAA,IAAAA,QAAA,CAAAuE,MAAA;gBAAAL,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAR,SAAA,OAAAY,2BAAA,CAAAhB,OAAA,EACAxD,QAAA;cAAAkE,QAAA,CAAAC,IAAA;cAAAP,SAAA,CAAAa,CAAA;YAAA;cAAA,KAAAZ,KAAA,GAAAD,SAAA,CAAA5G,CAAA,IAAA0H,IAAA;gBAAAR,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAjG,IAAA,GAAA0F,KAAA,CAAA1G,KAAA;cAAA,MACAmG,MAAA,CAAAtI,IAAA,CAAA7B,gBAAA,KAAAgF,IAAA,CAAAwG,cAAA;gBAAAT,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAjG,IAAA,CAAAqE,gBAAA;cACArE,IAAA,CAAAI,YAAA;cAAA2F,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAGAd,MAAA,CAAAtE,eAAA,CAAAsE,MAAA,CAAAtI,IAAA,CAAA7B,gBAAA,EAAAgF,IAAA,CAAAwG,cAAA;YAAA;cAAAb,MAAA,GAAAI,QAAA,CAAAI,IAAA;cAAA,IACAR,MAAA;gBAAAI,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA;cACAd,MAAA,CAAAnD,QAAA,CAAAyE,KAAA;cACAtB,MAAA,CAAAjH,aAAA;cAAA,OAAA6H,QAAA,CAAAW,MAAA;YAAA;cAAAX,QAAA,CAAAE,IAAA;cAAA,OAIAd,MAAA,CAAAwB,uBAAA,CAAAxB,MAAA,CAAAtI,IAAA,CAAA7B,gBAAA,EAAAgF,IAAA,CAAAwG,cAAA,EAAAxG,IAAA,CAAA4G,yBAAA;YAAA;cAAAhB,gBAAA,GAAAG,QAAA,CAAAI,IAAA;cACAnG,IAAA,CAAAI,YAAA,GAAAuF,MAAA,YAAAA,MAAA,UAAA5G,iBAAA,KAAAoB,MAAA,CAAAwF,MAAA,KAAA3G,KAAA;cACAgB,IAAA,CAAAqE,gBAAA,GAAAsB,MAAA,YAAAA,MAAA,aAAAA,MAAA;YAAA;cAAAI,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAc,EAAA,GAAAd,QAAA;cAAAN,SAAA,CAAAqB,CAAA,CAAAf,QAAA,CAAAc,EAAA;YAAA;cAAAd,QAAA,CAAAC,IAAA;cAAAP,SAAA,CAAAsB,CAAA;cAAA,OAAAhB,QAAA,CAAAiB,MAAA;YAAA;cAGA7B,MAAA,CAAA5L,YAAA,GAAAsI,QAAA;cACAoF,UAAA;gBACA9B,MAAA,CAAA5L,YAAA,CAAAwG,GAAA,WAAAC,IAAA;kBACA;kBACA,IAAAA,IAAA,CAAAiE,qBAAA,CAAAiD,eAAA;oBACA;oBACA;oBACA;oBACAlH,IAAA,CAAAC,qBAAA,GAAAD,IAAA,CAAAiE,qBAAA,CAAAhE,qBAAA;oBACAD,IAAA,CAAAK,oBAAA,GAAAL,IAAA,CAAAiE,qBAAA,CAAA5D,oBAAA;oBACA;oBACAL,IAAA,CAAAE,uBAAA,GAAAF,IAAA,CAAAiE,qBAAA,CAAA/D,uBAAA;oBAEAiF,MAAA,CAAArH,eAAA,CAAAyF,IAAA,CAAAvD,IAAA;oBACAmF,MAAA,CAAA/G,mBAAA,CAAAmF,IAAA,CAAAvD,IAAA;oBACAmF,MAAA,CAAArD,KAAA,CAAA8C,aAAA,CAAAC,kBAAA,CAAA7E,IAAA;kBACA;gBACA;;gBAEA;gBACAmF,MAAA,CAAA5L,YAAA,CAAA4N,IAAA,WAAAC,CAAA,EAAAC,CAAA;kBACA;kBACA,IAAAD,CAAA,CAAAE,kBAAA,YAAAD,CAAA,CAAAC,kBAAA;oBACA;kBACA;kBACA,IAAAF,CAAA,CAAAE,kBAAA,YAAAD,CAAA,CAAAC,kBAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAF,CAAA,CAAAnD,qBAAA,CAAAiD,eAAA,aAAAG,CAAA,CAAApD,qBAAA,CAAAiD,eAAA;oBACA;kBACA;kBACA;kBACA,IAAAE,CAAA,CAAAnD,qBAAA,CAAAiD,eAAA,aAAAG,CAAA,CAAApD,qBAAA,CAAAiD,eAAA;oBACA;kBACA;kBACA;kBACA,IAAAE,CAAA,CAAAnD,qBAAA,CAAAiD,eAAA,IAAAG,CAAA,CAAApD,qBAAA,CAAAiD,eAAA;oBACA;oBACA,OAAAE,CAAA,CAAAnD,qBAAA,CAAA5D,oBAAA,GAAAgH,CAAA,CAAApD,qBAAA,CAAA5D,oBAAA;kBACA;oBACA;oBACA;kBACA;gBACA;cACA;cACA;YAAA;cAEA8E,MAAA,CAAAjH,aAAA;cACAiH,MAAA,CAAAtH,UAAA;YAAA;YAAA;cAAA,OAAAkI,QAAA,CAAAwB,IAAA;UAAA;QAAA,GAAA/B,OAAA;MAAA;IACA;IACAgC,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,SAAA5K,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAA6K,QAAA;QACA,KAAA7K,IAAA,CAAA8K,UAAA;MACA;QACA,KAAA9K,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAA6K,QAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACA,KAAAlL,IAAA,CAAA8K,UAAA,OAAAlE,eAAA,IAAAC,MAAA;MACA;MAEA,IAAAd,4BAAA,OAAA/F,IAAA,EAAAgG,IAAA,WAAAhB,QAAA;QACA4F,MAAA,CAAAzF,QAAA,CAAAc,OAAA;MACA;IACA;IACA,sBACAnC,OAAA,WAAAA,QAAA;MAAA,IAAAqH,MAAA;MACA,KAAAnO,OAAA;MACA,KAAAS,WAAA,CAAAQ,YAAA;MACA,IAAAmN,0BAAA,OAAA3N,WAAA,EAAAuI,IAAA,WAAAhB,QAAA;QACAmG,MAAA,CAAA7N,cAAA,GAAA0H,QAAA,CAAAqG,IAAA;QACAF,MAAA,CAAA9N,KAAA,GAAA2H,QAAA,CAAA3H,KAAA;QACA8N,MAAA,CAAAnO,OAAA;MACA;IACA;IACA;IACAsO,MAAA,WAAAA,OAAA;MACA,KAAA9N,IAAA;MACA,KAAA+N,KAAA;IACA;IAEA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAvL,IAAA;QACAsH,YAAA;QACA1J,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAqM,SAAA;QACApM,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACA0L,UAAA;MACA;MACA,KAAAnK,OAAA;MACA,KAAAF,qBAAA;MACA,KAAAsK,SAAA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAlO,WAAA,CAAAC,OAAA;MACA,KAAAoG,OAAA;IACA;IAEA,aACA8H,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IAEAE,kBAAA,WAAAA,mBAAA/D,GAAA;MAAA,IAAAgE,MAAA;MACA,IAAAC,IAAA,GAAAjE,GAAA,CAAAkE,MAAA;MACA,KAAAC,QAAA,WAAAF,IAAA,SAAA/F,IAAA;QACA,WAAAkG,wBAAA,EAAApE,GAAA,CAAAR,YAAA,EAAAQ,GAAA,CAAAkE,MAAA;MACA,GAAAhG,IAAA;QACA8F,MAAA,CAAAhF,MAAA,CAAAC,UAAA,CAAAgF,IAAA;MACA,GAAAI,KAAA;QACArE,GAAA,CAAAkE,MAAA,GAAAlE,GAAA,CAAAkE,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAApP,MAAA,GAAAmP,SAAA,CAAA9C,MAAA;MACA,KAAApM,QAAA,IAAAkP,SAAA,CAAA9C,MAAA;;MAEA;MACA,KAAApI,cAAA;MACA,KAAAK,oBAAA;MACA6K,SAAA,CAAAnJ,GAAA,WAAAC,IAAA;QACA;QACA,IAAAmJ,MAAA,CAAArL,eAAA,CAAAsL,OAAA,CAAApJ,IAAA;UACA;UACA,IAAAA,IAAA,CAAAK,oBAAA,aAAAL,IAAA,CAAAK,oBAAA;YACA;YACAL,IAAA,CAAAC,qBAAA,OAAAlB,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA;YACA;YACAgB,IAAA,CAAAE,uBAAA,OAAAnB,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAAE,QAAA,CAAAP,IAAA,CAAAI,YAAA,EAAApB,KAAA;UACA;YACAgB,IAAA,CAAAC,qBAAA,OAAAlB,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,EAAAxB,KAAA;YACAgB,IAAA,CAAAE,uBAAA,OAAAnB,iBAAA,MAAAA,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,GAAAD,QAAA,CAAAP,IAAA,CAAAI,YAAA,EAAApB,KAAA;UACA;QACA;MAEA;MACA;MACA,KAAAlB,eAAA,CAAAiC,GAAA,WAAAC,IAAA;QACA,IAAAkJ,SAAA,CAAAE,OAAA,CAAApJ,IAAA;UACAA,IAAA,CAAAC,qBAAA;UACAD,IAAA,CAAAE,uBAAA;UACAF,IAAA,CAAAqJ,wBAAA,OAAAtK,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA;;UAEA;UACA,IAAAmK,MAAA,CAAA/K,mBAAA,CAAAgL,OAAA,CAAApJ,IAAA;YACAmJ,MAAA,CAAA9K,oBAAA,CAAAkF,IAAA,CAAAvD,IAAA;UACA;QACA;MACA;MAEA,KAAAlC,eAAA,GAAAoL,SAAA;IACA;IAEA,aACAI,SAAA,WAAAA,UAAA;MACA,KAAAlB,KAAA;MACA,KAAAvK,UAAA;MACA,KAAAxD,IAAA;MACA,KAAAD,KAAA;MACA,KAAAwD,GAAA;MACA,KAAAf,IAAA,CAAApC,mBAAA;MACA,KAAAoC,IAAA,CAAAyL,UAAA;MACA,KAAAzL,IAAA,CAAA/B,YAAA;IACA;IACA,aACAyO,YAAA,WAAAA,aAAA5E,GAAA;MAAA,IAAA6E,OAAA;MACA,KAAA5L,GAAA;MACA,KAAAwK,KAAA;MACA,IAAAjE,YAAA,GAAAQ,GAAA,CAAAR,YAAA,SAAArK,GAAA;MACA,IAAA2P,yBAAA,EAAAtF,YAAA,EAAAtB,IAAA,WAAAhB,QAAA;QACA2H,OAAA,CAAA3M,IAAA,GAAAgF,QAAA,CAAAvI,IAAA;QACAkQ,OAAA,CAAA3M,IAAA,CAAAyL,UAAA;QACAkB,OAAA,CAAAnP,IAAA;QACAmP,OAAA,CAAApP,KAAA;QACAoP,OAAA,CAAAjL,WAAA,IAAAsD,QAAA,CAAAtD,WAAA;QAEA,IAAAiL,OAAA,CAAA3M,IAAA,CAAAT,kBAAA,aAAAyF,QAAA,CAAAvI,IAAA,CAAAmB,mBAAA,gBAAAsE,iBAAA,EAAA8C,QAAA,CAAAvI,IAAA,CAAAqC,8BAAA,EAAAqD,KAAA,UAAA6C,QAAA,CAAAvI,IAAA,CAAAmB,mBAAA,gBAAAsE,iBAAA,EAAA8C,QAAA,CAAAvI,IAAA,CAAAsC,0BAAA,EAAAoD,KAAA;UACAwK,OAAA,CAAAtE,iBAAA;QACA;MACA;IACA;IAEA,WACAwE,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAA7H,KAAA,SAAA8H,QAAA;QAAA,IAAAC,IAAA,OAAAzE,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuE,SAAAC,KAAA;UAAA,WAAAzE,oBAAA,CAAAD,OAAA,IAAAQ,IAAA,UAAAmE,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAjE,IAAA,GAAAiE,SAAA,CAAAhE,IAAA;cAAA;gBACA,IAAA8D,KAAA;kBACA;kBACAJ,OAAA,CAAA1G,iBAAA;kBACA,IAAA0G,OAAA,CAAA9M,IAAA,CAAAsH,YAAA;oBACA,IAAAvB,4BAAA,EAAA+G,OAAA,CAAA9M,IAAA,EAAAgG,IAAA,WAAAhB,QAAA;sBACA8H,OAAA,CAAAhG,MAAA,CAAAC,UAAA;sBACA;sBACA+F,OAAA,CAAAhJ,OAAA;oBACA;kBACA;oBACA,IAAAuJ,yBAAA,EAAAP,OAAA,CAAA9M,IAAA,EAAAgG,IAAA,WAAAhB,QAAA;sBACA8H,OAAA,CAAA9M,IAAA,GAAAgF,QAAA,CAAAvI,IAAA;sBACAqQ,OAAA,CAAAhG,MAAA,CAAAC,UAAA;sBACA;sBACA+F,OAAA,CAAAhJ,OAAA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAAsJ,SAAA,CAAA1C,IAAA;YAAA;UAAA,GAAAuC,QAAA;QAAA,CACA;QAAA,iBAAAK,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACA3J,OAAA,CAAAC,GAAA,CAAAuJ,OAAA,CAAA9L,SAAA;QACA8L,OAAA,CAAAI,iBAAA;UACAjJ,IAAA,EAAA6I,OAAA,CAAA9L,SAAA;UACAmM,SAAA,WAAAA,UAAA/I,QAAA;YACA0I,OAAA,CAAAM,aAAA,CAAAhJ,QAAA;YACA4I,OAAA,CAAA5I,QAAA;UACA;UACAiJ,OAAA,WAAAA,QAAArE,KAAA;YACA8D,OAAA,CAAAQ,WAAA,CAAAtE,KAAA;YACAiE,MAAA,CAAAjE,KAAA;UACA;QACA;MACA;IACA;IACAxD,iBAAA,WAAAA,kBAAA;MACA,SAAApG,IAAA,CAAApC,mBAAA;QACA;QACA,KAAAoC,IAAA,CAAA3B,oBAAA;QACA,KAAA2B,IAAA,CAAAzB,mBAAA;QACA,KAAAyB,IAAA,CAAArB,iBAAA;QACA,KAAAqB,IAAA,CAAAnB,sBAAA;QACA,KAAAmB,IAAA,CAAAvB,oBAAA;QACA,KAAAuB,IAAA,CAAAjB,0BAAA;QACA;QACA,KAAAiB,IAAA,CAAAlB,8BAAA,UAAAkB,IAAA,CAAAtB,qBAAA,QAAAsB,IAAA,CAAAhB,cAAA,aAAAgB,IAAA,CAAAlB,8BAAA,cAAAkB,IAAA,CAAAhB,cAAA,YAAAgB,IAAA,CAAAhB,cAAA;MACA;QACA;QACA,KAAAgB,IAAA,CAAA5B,wBAAA;QACA,KAAA4B,IAAA,CAAA1B,uBAAA;QACA,KAAA0B,IAAA,CAAAtB,qBAAA;QACA,KAAAsB,IAAA,CAAApB,0BAAA;QACA,KAAAoB,IAAA,CAAAxB,wBAAA;QACA,KAAAwB,IAAA,CAAAlB,8BAAA;QACA;QACA,KAAAkB,IAAA,CAAArB,iBAAA,UAAAqB,IAAA,CAAAjB,0BAAA,QAAAiB,IAAA,CAAAhB,cAAA,aAAAgB,IAAA,CAAAjB,0BAAA,cAAAiB,IAAA,CAAAhB,cAAA,YAAAgB,IAAA,CAAAhB,cAAA;MACA;IACA;IACA,aACAmP,YAAA,WAAAA,aAAArG,GAAA;MAAA,IAAAsG,OAAA;MACA,IAAAC,aAAA,GAAAvG,GAAA,CAAAR,YAAA,SAAArK,GAAA;MACA,KAAAgP,QAAA,4BAAAoC,aAAA,cAAArI,IAAA;QACA,WAAAsI,yBAAA,EAAAD,aAAA;MACA,GAAArI,IAAA;QACAoI,OAAA,CAAAtK,OAAA;QACAsK,OAAA,CAAAtH,MAAA,CAAAC,UAAA;MACA,GAAAoF,KAAA,cACA;IACA;IAEA,aACAoC,YAAA,WAAAA,aAAA;MACA,KAAAjJ,QAAA,iCAAAkJ,cAAA,CAAAhG,OAAA,MACA,KAAA/K,WAAA,iBAAAgR,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAvF,MAAA;QACA,OAAAsF,IAAA,CAAAC,QAAA;MACA;MACA,IAAAC,CAAA;MACA,IAAAF,IAAA,CAAAG,KAAA;QACA,IAAAH,IAAA,CAAAG,KAAA,CAAAC,oBAAA,YAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;UACA,IAAAL,IAAA,CAAAM,IAAA,CAAAC,aAAA;YACAL,CAAA,GAAAF,IAAA,CAAAM,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAM,IAAA,CAAAC,aAAA;UACA;YACAL,CAAA,GAAAF,IAAA,CAAAU,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAU,IAAA,CAAAC,aAAA;UACA;QACA;UACAT,CAAA,GAAAF,IAAA,CAAAG,KAAA,CAAAS,SAAA,SAAAZ,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA,SAAAL,IAAA,CAAAG,KAAA,CAAAU,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAL,IAAA,CAAAc,MAAA;QACA;UACAC,EAAA,EAAAf,IAAA,CAAAc,MAAA;UACAE,KAAA,EAAAd,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAgB,UAAA,EAAAjB,IAAA,CAAAvN,OAAA,YAAAuN,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;QACA;UACAH,EAAA,EAAAf,IAAA,CAAAmB,MAAA;UACAH,KAAA,EAAAd,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAgB,UAAA,EAAAjB,IAAA,CAAAvN,OAAA,YAAAuN,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;IACA;IAEAhM,SAAA,WAAAA,UAAA;MAAA,IAAAkM,OAAA;MACA,SAAAlF,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAAyT,SAAA,CAAA3G,MAAA,cAAAwB,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAA0T,SAAA,CAAAD,SAAA;QACAE,cAAA,CAAAC,QAAA,iBAAArK,IAAA;UACAiK,OAAA,CAAArT,UAAA,GAAAqT,OAAA,CAAAlF,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAAyT,SAAA;QACA;MACA;QACA,KAAAtT,UAAA,QAAAmO,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAAyT,SAAA;MACA;IACA;IACApO,SAAA,WAAAA,UAAA;MAAA,IAAAwO,OAAA;MACA,SAAAvF,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAA8T,cAAA,CAAAhH,MAAA,cAAAwB,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAA0T,SAAA,CAAAI,cAAA;QACAH,cAAA,CAAAC,QAAA,sBAAArK,IAAA;UACAsK,OAAA,CAAAzT,SAAA,GAAAyT,OAAA,CAAAvF,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAA8T,cAAA;QACA;MACA;QACA,KAAA1T,SAAA,QAAAkO,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAA8T,cAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA1I,GAAA;MACA,KAAA4E,YAAA,CAAA5E,GAAA;IACA;IAEA2I,aAAA,WAAAA,cAAAC,OAAA;MACA,KAAA1Q,IAAA,CAAAjC,iBAAA,GAAA2S,OAAA,CAAAC,SAAA;MACA,KAAA3Q,IAAA,CAAAhC,2BAAA,GAAA0S,OAAA,CAAAE,gBAAA;MACA,KAAAnP,WAAA;IACA;IAEAuC,eAAA,WAAAA,gBAAA6M,eAAA,EAAAC,aAAA,EAAAC,SAAA;MAAA,WAAAxI,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAsI,SAAA;QAAA,IAAAC,EAAA,EAAAjM,QAAA,EAAAkM,UAAA,EAAAC,MAAA,EAAA5G,CAAA;QAAA,WAAA9B,oBAAA,CAAAD,OAAA,IAAAQ,IAAA,UAAAoI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlI,IAAA,GAAAkI,SAAA,CAAAjI,IAAA;YAAA;cAAAiI,SAAA,CAAAjI,IAAA;cAAA,OAEA,IAAAkI,oCAAA;YAAA;cAAAtM,QAAA,GAAAqM,SAAA,CAAA/H,IAAA;cAAA,MACAuH,eAAA,aAAAC,aAAA;gBAAAO,SAAA,CAAAjI,IAAA;gBAAA;cAAA;cAAA8H,UAAA,OAAA1H,2BAAA,CAAAhB,OAAA,EACAxD,QAAA,CAAAvI,IAAA;cAAA;gBAAA,KAAAyU,UAAA,CAAAzH,CAAA,MAAA0H,MAAA,GAAAD,UAAA,CAAAlP,CAAA,IAAA0H,IAAA;kBAAAa,CAAA,GAAA4G,MAAA,CAAAhP,KAAA;kBACA,KAAAoI,CAAA,CAAAuG,aAAA,KAAAA,aAAA,IAAAvG,CAAA,CAAArI,QAAA,KAAA4O,aAAA,MAAAvG,CAAA,CAAArI,QAAA,KAAA2O,eAAA,IAAAtG,CAAA,CAAAuG,aAAA,KAAAD,eAAA,KACA,IAAAjL,eAAA,EAAA2E,CAAA,CAAAgH,SAAA,SAAA3L,eAAA,MAAA8I,IAAA,WAAA9I,eAAA,MAAA8I,IAAA,WAAA9I,eAAA,EAAA2E,CAAA,CAAAiH,OAAA;oBACA;oBACA,IAAAX,eAAA,KAAAtG,CAAA,CAAAsG,eAAA,IAAAC,aAAA,KAAAvG,CAAA,CAAAuG,aAAA;sBACAG,EAAA,WAAA/O,iBAAA,EAAAqI,CAAA,CAAAkH,OAAA,EAAAnO,MAAA,CAAAiH,CAAA,CAAAmH,IAAA,EAAAvP,KAAA;oBACA;sBACA8O,EAAA,WAAA/O,iBAAA,EAAAqI,CAAA,CAAAoH,QAAA,EAAArO,MAAA,CAAAiH,CAAA,CAAAmH,IAAA,EAAAvP,KAAA;oBACA;kBACA;gBACA;cAAA,SAAAyP,GAAA;gBAAAV,UAAA,CAAAjH,CAAA,CAAA2H,GAAA;cAAA;gBAAAV,UAAA,CAAAhH,CAAA;cAAA;cAAA,OAAAmH,SAAA,CAAAxH,MAAA,WACAoH,EAAA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAA3G,IAAA;UAAA;QAAA,GAAAsG,QAAA;MAAA;IAEA;IACAlH,uBAAA,WAAAA,wBAAA+G,eAAA,EAAAC,aAAA,EAAAC,SAAA;MAAA,WAAAxI,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAmJ,SAAA;QAAA,IAAAZ,EAAA,EAAAjM,QAAA,EAAA8M,UAAA,EAAAC,MAAA,EAAAxH,CAAA;QAAA,WAAA9B,oBAAA,CAAAD,OAAA,IAAAQ,IAAA,UAAAgJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9I,IAAA,GAAA8I,SAAA,CAAA7I,IAAA;YAAA;cACA6H,EAAA;cAAAgB,SAAA,CAAA7I,IAAA;cAAA,OACA,IAAAkI,oCAAA;YAAA;cAAAtM,QAAA,GAAAiN,SAAA,CAAA3I,IAAA;cAAA,MACAuH,eAAA,aAAAC,aAAA;gBAAAmB,SAAA,CAAA7I,IAAA;gBAAA;cAAA;cAAA0I,UAAA,OAAAtI,2BAAA,CAAAhB,OAAA,EACAxD,QAAA,CAAAvI,IAAA;cAAA;gBAAA,KAAAqV,UAAA,CAAArI,CAAA,MAAAsI,MAAA,GAAAD,UAAA,CAAA9P,CAAA,IAAA0H,IAAA;kBAAAa,CAAA,GAAAwH,MAAA,CAAA5P,KAAA;kBACA,KAAAoI,CAAA,CAAAuG,aAAA,IAAAA,aAAA,IAAAvG,CAAA,CAAArI,QAAA,IAAA4O,aAAA,MACAvG,CAAA,CAAArI,QAAA,IAAA2O,eAAA,IAAAtG,CAAA,CAAAuG,aAAA,IAAAD,eAAA,KACA,IAAAjL,eAAA,EAAA2E,CAAA,CAAAgH,SAAA,SAAA3L,eAAA,EAAAmL,SAAA,KACA,IAAAnL,eAAA,EAAAmL,SAAA,SAAAnL,eAAA,EAAA2E,CAAA,CAAAiH,OAAA;oBACA;oBACA,IAAAX,eAAA,KAAAtG,CAAA,CAAAsG,eAAA,IAAAC,aAAA,KAAAvG,CAAA,CAAAuG,aAAA;sBACAG,EAAA,WAAA/O,iBAAA,EAAAqI,CAAA,CAAAkH,OAAA,EAAAnO,MAAA,CAAAiH,CAAA,CAAAmH,IAAA,EAAAvP,KAAA;oBACA;sBACA8O,EAAA,WAAA/O,iBAAA,EAAAqI,CAAA,CAAAoH,QAAA,EAAArO,MAAA,CAAAiH,CAAA,CAAAmH,IAAA,EAAAvP,KAAA;oBACA;kBACA;gBACA;cAAA,SAAAyP,GAAA;gBAAAE,UAAA,CAAA7H,CAAA,CAAA2H,GAAA;cAAA;gBAAAE,UAAA,CAAA5H,CAAA;cAAA;cAAA,OAAA+H,SAAA,CAAApI,MAAA,WACAoH,EAAA;YAAA;YAAA;cAAA,OAAAgB,SAAA,CAAAvH,IAAA;UAAA;QAAA,GAAAmH,QAAA;MAAA;IAEA;IAEArK,gBAAA,WAAAA,iBAAArJ,gBAAA,EAAA+T,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5J,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0J,SAAA;QAAA,IAAAtJ,MAAA;QAAA,WAAAL,oBAAA,CAAAD,OAAA,IAAAQ,IAAA,UAAAqJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAnJ,IAAA,GAAAmJ,SAAA,CAAAlJ,IAAA;YAAA;cAAA,MACAjL,gBAAA,KAAA+T,kBAAA;gBAAAI,SAAA,CAAAlJ,IAAA;gBAAA;cAAA;cAAA,OAAAkJ,SAAA,CAAAzI,MAAA,WACA;YAAA;cAAAyI,SAAA,CAAAlJ,IAAA;cAAA,OAEA+I,OAAA,CAAAnO,eAAA,CAAA7F,gBAAA,EAAA+T,kBAAA;YAAA;cAAApJ,MAAA,GAAAwJ,SAAA,CAAAhJ,IAAA;YAAA;YAAA;cAAA,OAAAgJ,SAAA,CAAA5H,IAAA;UAAA;QAAA,GAAA0H,QAAA;MAAA;IACA;IAEAG,OAAA,WAAAA,QAAA3C,EAAA;MACA,IAAAA,EAAA;QACA,IAAAA,EAAA;UACA,IAAAZ,KAAA,QAAAjE,MAAA,CAAAC,KAAA,CAAAvO,IAAA,CAAA8T,cAAA,CAAAiC,MAAA,WAAAC,OAAA;YAAA,OAAAA,OAAA,CAAAnR,OAAA,IAAAsO,EAAA;UAAA;UACA,IAAAZ,KAAA;YACA,OAAAA,KAAA,CAAAC,oBAAA,GAAAD,KAAA,CAAAE,oBAAA,GAAAF,KAAA,CAAA0D,cAAA;UACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA7K,GAAA;MACA,KAAA9H,IAAA,CAAAJ,YAAA,GAAAkI,GAAA,CAAAkH,KAAA,CAAA1N,OAAA;IACA;IACAsR,eAAA,WAAAA,gBAAA9K,GAAA;MACA,OAAAA,GAAA,CAAA2C,kBAAA;IACA;IACAoI,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAAC,SAAA;QACA,IAAAC,eAAA,GAAAF,OAAA,CAAA7N,KAAA,CAAAgO,UAAA,CAAAC,eAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,KAAA;QACA;MACA;IACA;IACAC,6BAAA,WAAAA,8BAAAvE,IAAA;MACA;QACAe,EAAA,EAAAf,IAAA,CAAA1M,KAAA;QACA0N,KAAA,EAAAhB,IAAA,CAAAgB;MACA;IACA;IACAwD,iBAAA,WAAAA,kBAAAvL,GAAA;MACA,KAAA9H,IAAA,CAAAnC,mBAAA,GAAAiK,GAAA,CAAAwL,sBAAA;IACA;IACAxF,iBAAA,WAAAA,kBAAAyF,OAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAAJ,OAAA,CAAA1O,IAAA;MAEA,IAAA+O,gBAAA;QACArT,GAAA;QACAsT,MAAA;QACApX,IAAA,EAAAgX;MACA,GAAAzN,IAAA,WAAAhB,QAAA;QACAuO,OAAA,CAAAxF,SAAA,CAAA/I,QAAA,EAAAuO,OAAA,CAAA1O,IAAA;QACAX,OAAA,CAAAC,GAAA,CAAAa,QAAA,CAAAzE,GAAA;QACAiT,OAAA,CAAAxT,IAAA,CAAA8T,QAAA,GAAA9O,QAAA,CAAAzE,GAAA;MACA,GAAA4L,KAAA,WAAAvC,KAAA;QACA2J,OAAA,CAAAtF,OAAA,CAAArE,KAAA;MACA;IACA;IACAmK,YAAA,WAAAA,aAAAlP,IAAA,EAAAC,QAAA;MACA,IAAAkP,SAAA,GAAAnP,IAAA,CAAAxI,IAAA,CAAA4X,SAAA,CAAApP,IAAA,CAAAxI,IAAA,CAAA6X,WAAA;MACA,IAAAC,WAAA,MAAA1F,MAAA,MAAAzO,IAAA,CAAAoU,YAAA,EAAA3F,MAAA,CAAAuF,SAAA;MACA,KAAApS,SAAA,OAAAyS,IAAA,EAAAxP,IAAA,CAAAyP,GAAA,GAAAH,WAAA;QAAAxO,IAAA,EAAAd,IAAA,CAAAc;MAAA;IACA;IACAqI,aAAA,WAAAA,cAAAhJ,QAAA,EAAAH,IAAA,EAAAC,QAAA;MACA,KAAA9E,IAAA,CAAA8T,QAAA,GAAA9O,QAAA,CAAAzE,GAAA;IACA;IACA2N,WAAA,WAAAA,YAAA0D,GAAA,EAAA/M,IAAA,EAAAC,QAAA;MACA,KAAAK,QAAA,CAAAyE,KAAA,mBAAAgI,GAAA;IACA;EACA;AACA;AAAA2C,OAAA,CAAA/L,OAAA,GAAAgM,QAAA"}]}