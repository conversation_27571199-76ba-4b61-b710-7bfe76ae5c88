{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Screenfull\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Screenfull\\index.vue", "mtime": 1678688095309}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9zY3JlZW5mdWxsID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJzY3JlZW5mdWxsIikpOwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogJ1NjcmVlbmZ1bGwnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBpc0Z1bGxzY3JlZW46IGZhbHNlCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdCgpOwogIH0sCiAgYmVmb3JlRGVzdHJveTogZnVuY3Rpb24gYmVmb3JlRGVzdHJveSgpIHsKICAgIHRoaXMuZGVzdHJveSgpOwogIH0sCiAgbWV0aG9kczogewogICAgY2xpY2s6IGZ1bmN0aW9uIGNsaWNrKCkgewogICAgICBpZiAoIV9zY3JlZW5mdWxsLmRlZmF1bHQuaXNFbmFibGVkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZSh7CiAgICAgICAgICBtZXNzYWdlOiAn5L2g55qE5rWP6KeI5Zmo5LiN5pSv5oyB5YWo5bGPJywKICAgICAgICAgIHR5cGU6ICd3YXJuaW5nJwogICAgICAgIH0pOwogICAgICAgIHJldHVybiBmYWxzZTsKICAgICAgfQogICAgICBfc2NyZWVuZnVsbC5kZWZhdWx0LnRvZ2dsZSgpOwogICAgfSwKICAgIGNoYW5nZTogZnVuY3Rpb24gY2hhbmdlKCkgewogICAgICB0aGlzLmlzRnVsbHNjcmVlbiA9IF9zY3JlZW5mdWxsLmRlZmF1bHQuaXNGdWxsc2NyZWVuOwogICAgfSwKICAgIGluaXQ6IGZ1bmN0aW9uIGluaXQoKSB7CiAgICAgIGlmIChfc2NyZWVuZnVsbC5kZWZhdWx0LmlzRW5hYmxlZCkgewogICAgICAgIF9zY3JlZW5mdWxsLmRlZmF1bHQub24oJ2NoYW5nZScsIHRoaXMuY2hhbmdlKTsKICAgICAgfQogICAgfSwKICAgIGRlc3Ryb3k6IGZ1bmN0aW9uIGRlc3Ryb3koKSB7CiAgICAgIGlmIChfc2NyZWVuZnVsbC5kZWZhdWx0LmlzRW5hYmxlZCkgewogICAgICAgIF9zY3JlZW5mdWxsLmRlZmF1bHQub2ZmKCdjaGFuZ2UnLCB0aGlzLmNoYW5nZSk7CiAgICAgIH0KICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "names": ["_screenfull", "_interopRequireDefault", "require", "name", "data", "isFullscreen", "mounted", "init", "<PERSON><PERSON><PERSON><PERSON>", "destroy", "methods", "click", "screenfull", "isEnabled", "$message", "message", "type", "toggle", "change", "on", "off", "exports", "default", "_default"], "sources": ["src/components/Screenfull/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <svg-icon :icon-class=\"isFullscreen?'exit-fullscreen':'fullscreen'\" @click=\"click\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport screenfull from 'screenfull'\r\n\r\nexport default {\r\n  name: 'Screenfull',\r\n  data() {\r\n    return {\r\n      isFullscreen: false\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  beforeDestroy() {\r\n    this.destroy()\r\n  },\r\n  methods: {\r\n    click() {\r\n      if (!screenfull.isEnabled) {\r\n        this.$message({message: '你的浏览器不支持全屏', type: 'warning'})\r\n        return false\r\n      }\r\n      screenfull.toggle()\r\n    },\r\n    change() {\r\n      this.isFullscreen = screenfull.isFullscreen\r\n    },\r\n    init() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.on('change', this.change)\r\n      }\r\n    },\r\n    destroy() {\r\n      if (screenfull.isEnabled) {\r\n        screenfull.off('change', this.change)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.screenfull-svg {\r\n  display: inline-block;\r\n  cursor: pointer;\r\n  fill: #5a5e66;;\r\n  width: 20px;\r\n  height: 20px;\r\n  vertical-align: 10px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAOA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;eAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,mBAAA,CAAAC,SAAA;QACA,KAAAC,QAAA;UAAAC,OAAA;UAAAC,IAAA;QAAA;QACA;MACA;MACAJ,mBAAA,CAAAK,MAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAb,YAAA,GAAAO,mBAAA,CAAAP,YAAA;IACA;IACAE,IAAA,WAAAA,KAAA;MACA,IAAAK,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAO,EAAA,gBAAAD,MAAA;MACA;IACA;IACAT,OAAA,WAAAA,QAAA;MACA,IAAAG,mBAAA,CAAAC,SAAA;QACAD,mBAAA,CAAAQ,GAAA,gBAAAF,MAAA;MACA;IACA;EACA;AACA;AAAAG,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}