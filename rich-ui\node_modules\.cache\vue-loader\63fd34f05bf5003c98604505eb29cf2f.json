{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\staffrole\\index.vue?vue&type=template&id=1f252713&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\staffrole\\index.vue", "mtime": 1754876882598}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}