{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\design\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\design\\index.vue", "mtime": 1740984174843}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "require", "_panel", "_interopRequireDefault", "_printData", "_preview", "_j<PERSON><PERSON><PERSON>w", "_fontSize", "_scale", "hiprintTemplate", "_default", "name", "components", "printPreview", "jsonView", "data", "template", "curPaper", "type", "width", "height", "paperTypes", "paperPopVisible", "paperWidth", "paperHeight", "scaleValue", "scaleMax", "scaleMin", "jsonIn", "jsonOut", "computed", "curPaperType", "types", "key", "item", "_this$curPaper", "mounted", "init", "methods", "<PERSON><PERSON><PERSON>", "providers", "defaultElementTypeProvider", "setConfig", "optionItems", "fontSize", "scale", "t", "prototype", "css", "e", "length", "createTarget", "target", "$", "getValue", "find", "val", "parseInt", "toString", "setValue", "destroy", "remove", "movingDistance", "text", "tabs", "options", "after", "hidden", "supportOptions", "image", "replace", "PrintElementTypeManager", "buildByHtml", "empty", "that", "PrintTemplate", "panel", "onImageChooseClick", "setTimeout", "refresh", "real", "fontList", "title", "value", "dataMode", "history", "onDataChanged", "json", "console", "log", "onUpdateError", "<PERSON><PERSON><PERSON><PERSON>", "paginationContainer", "design", "grid", "editingPanel", "setPaper", "Object", "keys", "includes", "error", "$message", "concat", "otherPaper", "changeScale", "big", "zoom", "rotatePaper", "preView", "updateElementType", "refreshPrinterList", "list", "get<PERSON><PERSON><PERSON>", "$refs", "show", "printData", "only<PERSON><PERSON><PERSON>", "$print", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "onlyPrint2", "window", "hiwebSocket", "opened", "$print2", "printer", "on", "$notification", "success", "placement", "message", "description", "print", "printerList", "getPrinterList", "print2", "clearPaper", "clear", "ippPrintAttr", "p", "url", "ippPrint", "opt", "action", "res", "ippPrintTest", "encoding", "ippRequestTest", "ippRequest", "ippRequestPrint", "str", "array", "Uint8Array", "i", "charCodeAt", "testData", "buffer", "updateJson", "update", "JSON", "parse", "exportJson", "stringify", "get<PERSON>son", "setElsAlign", "setElsSpace", "h", "getSelectEls", "els", "updateFontSize", "updateOption", "updateFontWeight", "exports", "default"], "sources": ["src/views/print/demo/design/index.vue"], "sourcesContent": ["<template>\r\n  <el-card>\r\n    <div style=\"display: flex;flex-direction: column\">\r\n      <el-row :gutter=\"10\" style=\"margin-bottom: 10px\">\r\n        <el-button-group>\r\n          <template v-for=\"(value,type) in paperTypes\">\r\n            <el-button :key=\"type\" :type=\"curPaperType === type ? 'primary' : 'info'\" @click=\"setPaper(type,value)\">\r\n              {{ type }}\r\n            </el-button>\r\n          </template>\r\n          <el-popover v-model=\"paperPopVisible\" title=\"设置纸张宽高(mm)\" trigger=\"click\">\r\n            <div slot=\"content\">\r\n              <el-input v-model=\"paperWidth\" placeholder=\"宽(mm)\" style=\" width: 100px; text-align: center\"\r\n                        type=\"number\"\r\n              />\r\n              <el-input disabled\r\n                        placeholder=\"~\" style=\" width: 30px; border-left: 0; pointer-events: none; backgroundColor: #fff\"\r\n              />\r\n              <el-input v-model=\"paperHeight\" placeholder=\"高(mm)\" style=\"width: 100px; text-align: center; border-left: 0\"\r\n                        type=\"number\"\r\n              />\r\n              <el-button style=\"width: 100%\" type=\"primary\" @click=\"otherPaper\">确定</el-button>\r\n            </div>\r\n            <el-button :type=\"'other'==curPaperType?'primary':''\">自定义纸张</el-button>\r\n          </el-popover>\r\n        </el-button-group>\r\n        <el-button icon=\"zoom-out\" type=\"text\" @click=\"changeScale(false)\"></el-button>\r\n        <el-input-number\r\n          :formatter=\"value => `${(value * 100).toFixed(0)}%`\"\r\n          :max=\"scaleMax\"\r\n          :min=\"scaleMin\"\r\n          :parser=\"value => value.replace('%', '')\"\r\n          :step=\"0.1\"\r\n          :value=\"scaleValue\"\r\n          disabled\r\n          style=\"width: 70px;\"\r\n        />\r\n        <el-button icon=\"zoom-in\" type=\"text\" @click=\"changeScale(true)\"></el-button>\r\n        <el-button icon=\"redo\" type=\"primary\" @click=\"rotatePaper()\">旋转</el-button>\r\n        <el-button icon=\"eye\" type=\"primary\" @click=\"preView\">\r\n          预览\r\n        </el-button>\r\n        <el-button icon=\"printer\" type=\"primary\" @click=\"print\">\r\n          直接打印\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"onlyPrint\">\r\n          Api单独打印\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"onlyPrint2\">\r\n          Api单独直接打印\r\n        </el-button>\r\n        <el-popconfirm\r\n          okText=\"确定清空\"\r\n          okType=\"danger\"\r\n          title=\"是否确认清空?\"\r\n          @confirm=\"clearPaper\"\r\n        >\r\n          <i slot=\"icon\" style=\"color: red\" type=\"question-circle-o\"/>\r\n          <el-button type=\"danger\">\r\n            清空\r\n            <i type=\"close\"/>\r\n          </el-button>\r\n        </el-popconfirm>\r\n        <json-view :template=\"template\"/>\r\n        <el-button type=\"primary\" @click=\"ippPrintAttr\">\r\n          ipp获取 打印机 参数情况\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"ippPrintTest\">\r\n          ipp打印测试\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"ippRequestTest\">\r\n          ipp请求 获取 打印机 参数情况\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"ippRequestPrint\">\r\n          ipp请求 打印测试\r\n        </el-button>\r\n      </el-row>\r\n      <el-row :gutter=\"10\" style=\"margin-bottom: 10px\">\r\n        <el-input v-model:value=\"jsonIn\" allow-clear placeholder=\"复制json模板到此后 点击右侧更新\" style=\"width:30vw\"\r\n                  type=\"textarea\"\r\n                  @pressEnter=\"updateJson\"\r\n        />\r\n        <el-button type=\"primary\" @click=\"updateJson\">\r\n          更新json模板\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"exportJson\">\r\n          导出json模板到 textArea\r\n        </el-button>\r\n        <el-input v-model:value=\"jsonOut\" allow-clear placeholder=\"点击左侧导出json\" style=\"width:30vw\"\r\n                  type=\"textarea\"\r\n        />\r\n      </el-row>\r\n      <el-row :gutter=\"10\" style=\"margin-bottom: 10px\">\r\n        <el-button type=\"primary\" @click=\"getSelectEls\">\r\n          获取选中元素\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"updateFontSize\">\r\n          选中元素字体12pt\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"updateFontWeight\">\r\n          选中元素字体Bolder\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"setElsSpace(true)\"> 水平间距10\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"setElsSpace(false)\"> 垂直间距10\r\n        </el-button>\r\n        <el-radio-group>\r\n          <el-radio-button label=\"左对齐\" @click=\"setElsAlign('left')\">\r\n            <span class=\"glyphicon glyphicon-object-align-left\"></span>\r\n          </el-radio-button>\r\n          <el-radio-button label=\"居中\" @click=\"setElsAlign('vertical')\">\r\n            <span class=\"glyphicon glyphicon-object-align-vertical\"></span>\r\n          </el-radio-button>\r\n          <el-radio-button label=\"右对齐\" @click=\"setElsAlign('right')\">\r\n            <span class=\"glyphicon glyphicon-object-align-right\"></span>\r\n          </el-radio-button>\r\n          <el-radio-button label=\"顶部对齐\" @click=\"setElsAlign('top')\">\r\n            <span class=\"glyphicon glyphicon-object-align-top\"></span>\r\n          </el-radio-button>\r\n          <el-radio-button label=\"垂直居中\" @click=\"setElsAlign('horizontal')\">\r\n            <span class=\"glyphicon glyphicon-object-align-horizontal\"></span>\r\n          </el-radio-button>\r\n          <el-radio-button label=\"底部对齐\" @click=\"setElsAlign('bottom')\">\r\n            <span class=\"glyphicon glyphicon-object-align-bottom\"></span>\r\n          </el-radio-button>\r\n          <el-radio-button label=\"横向分散\" @click=\"setElsAlign('distributeHor')\">\r\n            <span class=\"glyphicon glyphicon-resize-horizontal\"></span>\r\n          </el-radio-button>\r\n          <el-radio-button label=\"纵向分散\" @click=\"setElsAlign('distributeVer')\">\r\n            <span class=\"glyphicon glyphicon-resize-vertical\"></span>\r\n          </el-radio-button>\r\n        </el-radio-group>\r\n      </el-row>\r\n    </div>\r\n    <el-row :gutter=\"10\">\r\n      <el-col :span=\"4\">\r\n        <el-card style=\"height: 100vh\">\r\n          <el-row>\r\n            <el-col :span=\"24\" class=\"rect-printElement-types hiprintEpContainer\">\r\n              <el-row class=\"drag_item_title\">拖拽组件列表</el-row>\r\n              <el-row style=\"height: 100px;\">\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" style tid=\"defaultModule.text\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-text-width\"></span>\r\n                      <p class=\"glyphicon-class\">文本</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" style tid=\"defaultModule.image\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-picture\"></span>\r\n                      <p class=\"glyphicon-class\">图片</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row style=\"height: 100px;\">\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" tid=\"defaultModule.longText\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-subscript\"></span>\r\n                      <p class=\"glyphicon-class\">长文</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" style tid=\"defaultModule.table\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-th\"></span>\r\n                      <p class=\"glyphicon-class\">表格</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row style=\"height: 100px;\">\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" style=\"\" tid=\"defaultModule.html\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-header\"></span>\r\n                      <p class=\"glyphicon-class\">html</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" style tid=\"defaultModule.customText\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-text-width\"></span>\r\n                      <p class=\"glyphicon-class\">自定义</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row class=\"drag_item_title\">辅助</el-row>\r\n              <el-row style=\"height: 100px;\">\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" style tid=\"defaultModule.hline\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-resize-horizontal\"></span>\r\n                      <p class=\"glyphicon-class\">横线</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" style tid=\"defaultModule.vline\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-resize-vertical\"></span>\r\n                      <p class=\"glyphicon-class\">竖线</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row style=\"height: 100px;\">\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" tid=\"defaultModule.rect\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-unchecked\"></span>\r\n                      <p class=\"glyphicon-class\">矩形</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n                <el-col :span=\"12\" class=\"drag_item_box\">\r\n                  <div>\r\n                    <a class=\"ep-draggable-item\" tid=\"defaultModule.oval\">\r\n                      <span aria-hidden=\"true\" class=\"glyphicon glyphicon-record\"></span>\r\n                      <p class=\"glyphicon-class\">椭圆</p>\r\n                    </a>\r\n                  </div>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"15\">\r\n        <el-card class=\"card-design\">\r\n          <div id=\"hiprint-printTemplate\" class=\"hiprint-printTemplate\"></div>\r\n        </el-card>\r\n      </el-col>\r\n      <el-col :span=\"5\" class=\"params_setting_container\">\r\n        <el-card>\r\n          <el-row class=\"hinnn-layout-sider\">\r\n            <div id=\"PrintElementOptionSetting\"></div>\r\n          </el-row>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </el-card>\r\n</template>\r\n\r\n<script>\r\nimport {defaultElementTypeProvider, hiprint} from '../../../../index'\r\nimport panel from './panel'\r\nimport printData from './print-data'\r\nimport printPreview from './preview'\r\nimport jsonView from '../json-view.vue'\r\nimport fontSize from './font-size.js'\r\nimport scale from './scale.js'\r\n// disAutoConnect();\r\n\r\nlet hiprintTemplate\r\n\r\nexport default {\r\n  name: 'printDesign',\r\n  components: {printPreview, jsonView},\r\n  data() {\r\n    return {\r\n      template: null,\r\n      curPaper: {\r\n        type: 'A4',\r\n        width: 210,\r\n        height: 296.6\r\n      },\r\n      paperTypes: {\r\n        'A3': {\r\n          width: 420,\r\n          height: 296.6\r\n        },\r\n        'A4': {\r\n          width: 210,\r\n          height: 296.6\r\n        },\r\n        'A5': {\r\n          width: 210,\r\n          height: 147.6\r\n        },\r\n        'B3': {\r\n          width: 500,\r\n          height: 352.6\r\n        },\r\n        'B4': {\r\n          width: 250,\r\n          height: 352.6\r\n        },\r\n        'B5': {\r\n          width: 250,\r\n          height: 175.6\r\n        }\r\n      },\r\n      // 自定义纸张\r\n      paperPopVisible: false,\r\n      paperWidth: '220',\r\n      paperHeight: '80',\r\n      // 缩放\r\n      scaleValue: 1,\r\n      scaleMax: 5,\r\n      scaleMin: 0.5,\r\n      // 导入导出json\r\n      jsonIn: '',\r\n      jsonOut: ''\r\n    }\r\n  },\r\n  computed: {\r\n    curPaperType() {\r\n      let type = 'other'\r\n      let types = this.paperTypes\r\n      for (const key in types) {\r\n        let item = types[key]\r\n        let {width, height} = this.curPaper\r\n        if (item.width === width && item.height === height) {\r\n          type = key\r\n        }\r\n      }\r\n      return type\r\n    }\r\n  },\r\n  mounted() {\r\n    this.init()\r\n  },\r\n  methods: {\r\n    init() {\r\n      hiprint.init({\r\n        providers: [new defaultElementTypeProvider()]\r\n      })\r\n      // 还原配置\r\n      hiprint.setConfig()\r\n      // 替换配置\r\n      hiprint.setConfig({\r\n        optionItems: [\r\n          fontSize,\r\n          scale,\r\n          function () {\r\n            function t() {\r\n              this.name = 'zIndex'\r\n            }\r\n\r\n            return t.prototype.css = function (t, e) {\r\n              if (t && t.length) {\r\n                if (e) return t.css('z-index', e)\r\n              }\r\n              return null\r\n            }, t.prototype.createTarget = function () {\r\n              return this.target = $('<div class=\"hiprint-option-item\">\\n        <div class=\"hiprint-option-item-label\">\\n        元素层级2\\n        </div>\\n        <div class=\"hiprint-option-item-field\">\\n        <input type=\"number\" class=\"auto-submit\"/>\\n        </div>\\n    </div>'), this.target\r\n            }, t.prototype.getValue = function () {\r\n              var t = this.target.find('input').val()\r\n              if (t) return parseInt(t.toString())\r\n            }, t.prototype.setValue = function (t) {\r\n              this.target.find('input').val(t)\r\n            }, t.prototype.destroy = function () {\r\n              this.target.remove()\r\n            }, t\r\n          }()\r\n        ],\r\n        movingDistance: 2.5,\r\n        text: {\r\n          tabs: [\r\n            // 隐藏部分\r\n            {\r\n              // name: '测试', // tab名称 可忽略\r\n              options: [] // 必须包含 options\r\n            },// 当修改第二个 tabs 时,必须把他之前的 tabs 都列举出来.\r\n            {\r\n              name: '样式', options: [\r\n                {\r\n                  name: 'scale',\r\n                  after: 'transform', // 自定义参数，插入在 transform 之后\r\n                  hidden: false\r\n                }\r\n              ]\r\n            }\r\n          ],\r\n          supportOptions: [\r\n            {\r\n              name: 'styler',\r\n              hidden: true\r\n            },\r\n            {\r\n              name: 'scale', // 自定义参数，supportOptions 必须得添加\r\n              after: 'transform', // 自定义参数，插入在 transform 之后\r\n              hidden: false\r\n            },\r\n            {\r\n              name: 'formatter',\r\n              hidden: true\r\n            }\r\n          ]\r\n        },\r\n        image: {\r\n          tabs: [\r\n            {\r\n              // 整体替换\r\n              replace: true,\r\n              name: '基本', options: [\r\n                {\r\n                  name: 'field',\r\n                  hidden: false\r\n                },\r\n                {\r\n                  name: 'src',\r\n                  hidden: false\r\n                },\r\n                {\r\n                  name: 'fit',\r\n                  hidden: false\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        }\r\n      })\r\n      // eslint-disable-next-line no-undef\r\n      hiprint.PrintElementTypeManager.buildByHtml($('.ep-draggable-item'))\r\n      $('#hiprint-printTemplate').empty()\r\n      let that = this\r\n      this.template = hiprintTemplate = new hiprint.PrintTemplate({\r\n        template: panel,\r\n        // 图片选择功能\r\n        onImageChooseClick: (target) => {\r\n          // 测试 3秒后修改图片地址值\r\n          setTimeout(() => {\r\n            // target.refresh(url,options,callback)\r\n            // callback(el, width, height) // 原元素,宽,高\r\n            // target.refresh(url,false,(el,width,height)=>{\r\n            //   el.options.width = width;\r\n            //   el.designTarget.css('width', width + \"pt\");\r\n            //   el.designTarget.children('.resize-panel').trigger($.Event('click'));\r\n            // })\r\n            target.refresh('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAtAAAAIIAQMAAAB99EudAAAABlBMVEUmf8vG2O41LStnAAABD0lEQVR42u3XQQqCQBSAYcWFS4/QUTpaHa2jdISWLUJjjMpclJoPGvq+1WsYfiJCZ4oCAAAAAAAAAAAAAAAAAHin6pL9c6H/fOzHbRrP0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0u/SY9LS0tLS0tLS0tLS0n+edm+UlpaWlpaWlpaWlpaW/tl0Ndyzbno7/+tPTJdd1wal69dNa6abx+Lq6TSeYtK7BX/Diek0XULSZZrakPRtV0i6Hu/KIt30q4fM0pvBqvR9mvsQkZaW9gyJT+f5lsnzjR54xAk8mAUeJyMPwYFH98ALx5Jr0kRLLndT7b64UX9QR/0eAAAAAAAAAAAAAAAAAAD/4gpryzr/bja4QgAAAABJRU5ErkJggg==', {\r\n              // auto: true, // 根据图片宽高自动等比(宽>高?width:height)\r\n              // width: true, // 按宽调整高\r\n              // height: true, // 按高调整宽\r\n              real: true // 根据图片实际尺寸调整(转pt)\r\n            })\r\n          }, 3000)\r\n          // target.getValue()\r\n          // target.refresh(url)\r\n        },\r\n        // 自定义可选字体\r\n        // 或者使用 hiprintTemplate.setFontList([])\r\n        // 或元素中 options.fontList: []\r\n        fontList: [\r\n          {title: '微软雅黑', value: 'Microsoft YaHei'},\r\n          {title: '黑体', value: 'STHeitiSC-Light'},\r\n          {title: '思源黑体', value: 'SourceHanSansCN-Normal'},\r\n          {title: '王羲之书法体', value: '王羲之书法体'},\r\n          {title: '宋体', value: 'SimSun'},\r\n          {title: '华为楷体', value: 'STKaiti'},\r\n          {title: 'cursive', value: 'cursive'}\r\n        ],\r\n        dataMode: 1, // 1:getJson 其他：getJsonTid 默认1\r\n        history: true, // 是否需要 撤销重做功能\r\n        onDataChanged: (type, json) => {\r\n          console.log(type) // 新增、移动、删除、修改(参数调整)、大小、旋转\r\n          console.log(json) // 返回 template\r\n        },\r\n        onUpdateError: (e) => {\r\n          console.log(e)\r\n        },\r\n        settingContainer: '#PrintElementOptionSetting',\r\n        paginationContainer: '.hiprint-printPagination'\r\n      })\r\n      hiprintTemplate.design('#hiprint-printTemplate', {grid: true})\r\n      console.log(hiprintTemplate)\r\n      // 获取当前放大比例, 当zoom时传true 才会有\r\n      this.scaleValue = hiprintTemplate.editingPanel.scale || 1\r\n    },\r\n    /**\r\n     * 设置纸张大小\r\n     * @param type [A3, A4, A5, B3, B4, B5, other]\r\n     * @param value {width,height} mm\r\n     */\r\n    setPaper(type, value) {\r\n      try {\r\n        if (Object.keys(this.paperTypes).includes(type)) {\r\n          this.curPaper = {type: type, width: value.width, height: value.height}\r\n          hiprintTemplate.setPaper(value.width, value.height)\r\n        } else {\r\n          this.curPaper = {type: 'other', width: value.width, height: value.height}\r\n          hiprintTemplate.setPaper(value.width, value.height)\r\n        }\r\n      } catch (error) {\r\n        this.$message.error(`打印清除 操作失败: ${error}`)\r\n      }\r\n    },\r\n    otherPaper() {\r\n      let value = {}\r\n      value.width = this.paperWidth\r\n      value.height = this.paperHeight\r\n      this.paperPopVisible = false\r\n      this.setPaper('other', value)\r\n    },\r\n    changeScale(big) {\r\n      let scaleValue = this.scaleValue\r\n      if (big) {\r\n        scaleValue += 0.1\r\n        if (scaleValue > this.scaleMax) scaleValue = 5\r\n      } else {\r\n        scaleValue -= 0.1\r\n        if (scaleValue < this.scaleMin) scaleValue = 0.5\r\n      }\r\n      if (hiprintTemplate) {\r\n        // scaleValue: 放大缩小值, false: 不保存(不传也一样), 如果传 true, 打印时也会放大\r\n        hiprintTemplate.zoom(scaleValue)\r\n        this.scaleValue = scaleValue\r\n      }\r\n    },\r\n    rotatePaper() {\r\n      if (hiprintTemplate) {\r\n        hiprintTemplate.rotatePaper()\r\n      }\r\n    },\r\n    preView() {\r\n      // 测试, 点预览更新拖拽元素\r\n      hiprint.updateElementType('defaultModule.text', (type) => {\r\n        type.title = '这是更新后的元素'\r\n        return type\r\n      })\r\n      // 测试, 通过socket刷新打印机列表； 默认只有连接的时候才会获取到最新的打印机列表\r\n      hiprint.refreshPrinterList((list) => {\r\n        console.log('refreshPrinterList')\r\n        console.log(list)\r\n      })\r\n      // 测试, 获取IP、IPV6、MAC地址、DNS\r\n      // 参数格式：\r\n      // 1. 类型（ip、ipv6、mac、dns、all、interface、vboxnet）\r\n      // 2. 回调 data => {addr, e}  addr: 返回的数据 e:错误信息\r\n      // 3. 其他参数 ...args\r\n      hiprint.getAddress('ip', (data) => {\r\n        console.log('ip')\r\n        console.log(data)\r\n      })\r\n      hiprint.getAddress('ipv6', (data) => {\r\n        console.log('ipv6')\r\n        console.log(data)\r\n      })\r\n      hiprint.getAddress('mac', (data) => {\r\n        console.log('mac')\r\n        console.log(data)\r\n      })\r\n      hiprint.getAddress('dns', (data) => {\r\n        console.log('dns')\r\n        console.log(data)\r\n      })\r\n      hiprint.getAddress('all', (data) => {\r\n        console.log('all')\r\n        console.log(data)\r\n      })\r\n      // 各个平台不一样, 用法见: https://www.npmjs.com/package/address\r\n      hiprint.getAddress('interface', (data) => {\r\n        console.log('interface')\r\n        console.log(data)\r\n      }, 'IPv4', 'eth1')\r\n\r\n      // 打开预览组件\r\n      this.$refs.preView.show(hiprintTemplate, printData)\r\n    },\r\n    onlyPrint() {\r\n      let hiprintTemplate = this.$print(undefined, panel, printData, {}, {\r\n        styleHandler: () => {\r\n          let css = '<link href=\"http://hiprint.io/Content/hiprint/css/print-lock.css\" media=\"print\" rel=\"stylesheet\">'\r\n          return css\r\n        }\r\n      })\r\n      console.log(hiprintTemplate)\r\n    },\r\n    onlyPrint2() {\r\n      let that = this\r\n      if (window.hiwebSocket.opened) {\r\n        let hiprintTemplate = this.$print2(undefined, panel, printData, {\r\n          printer: '', title: 'Api单独打印',\r\n          styleHandler: () => {\r\n            // let css = '<link href=\"http://hiprint.io/Content/hiprint/css/print-lock.css\" media=\"print\" rel=\"stylesheet\">';\r\n            let css = '<style>.hiprint-printElement-text{color:red !important;}</style>'\r\n            return css\r\n          }\r\n        })\r\n        let key = 'Api单独直接打印'\r\n        hiprintTemplate.on('printSuccess', function () {\r\n          that.$notification.success({\r\n            key: key,\r\n            placement: 'topRight',\r\n            message: key + ' 打印成功',\r\n            description: 'Api单独直接打印回调'\r\n          })\r\n        })\r\n        return\r\n      }\r\n      this.$message.error('客户端未连接,无法直接打印')\r\n    },\r\n    print() {\r\n      if (window.hiwebSocket.opened) {\r\n        const printerList = hiprintTemplate.getPrinterList()\r\n        console.log(printerList)\r\n        hiprintTemplate.print2(printData, {printer: '', title: 'hiprint测试打印'})\r\n        return\r\n      }\r\n      this.$message.error('客户端未连接,无法直接打印')\r\n    },\r\n    clearPaper() {\r\n      try {\r\n        hiprintTemplate.clear()\r\n      } catch (error) {\r\n        this.$message.error(`打印清除 操作失败: ${error}`)\r\n      }\r\n    },\r\n    ippPrintAttr() {\r\n      // 不知道打印机 ipp 情况， 可通过 '客户端' 获取一下\r\n      const printerList = hiprintTemplate.getPrinterList()\r\n      console.log(printerList)\r\n      if (!printerList.length) return\r\n      let p = printerList[0]\r\n      console.log(p)\r\n      // 系统不同， 参数可能不同\r\n      let url = p.options['printer-uri-supported']\r\n      // 测试 获取 ipp打印 支持参数\r\n      hiprint.ippPrint({\r\n        url: url,\r\n        // 打印机参数： {version,uri,charset,language}\r\n        opt: {},\r\n        action: 'Get-Printer-Attributes', // 获取打印机支持参数\r\n        // ipp参数\r\n        message: null\r\n      }, (res) => {\r\n        // 执行的ipp 任务回调 / 错误回调\r\n        console.log(res)\r\n      }, (printer) => {\r\n        // ipp连接成功 回调 打印机信息\r\n        console.log(printer)\r\n      })\r\n    },\r\n    ippPrintTest() {\r\n      // 不知道打印机 ipp 情况， 可通过 '客户端' 获取一下\r\n      const printerList = hiprintTemplate.getPrinterList()\r\n      console.log(printerList)\r\n      if (!printerList.length) return\r\n      let p = printerList[0]\r\n      console.log(p)\r\n      // 系统不同， 参数可能不同\r\n      let url = p.options['printer-uri-supported']\r\n      // 测试 打印文本\r\n      hiprint.ippPrint({\r\n        url: url,\r\n        // 打印机参数： {version,uri,charset,language}\r\n        opt: {},\r\n        action: 'Print-Job',\r\n        // ipp参数\r\n        message: {\r\n          'operation-attributes-tag': {\r\n            'requesting-user-name': 'hiPrint', // 用户名\r\n            'job-name': 'ipp Test Job', // 任务名\r\n            'document-format': 'text/plain' // 文档类型\r\n          },\r\n          // data 需为 Buffer (客户端简单处理了string 转 Buffer), 支持设置 encoding\r\n          // data 需为 Buffer (客户端简单处理了string 转 Buffer), 支持设置 encoding\r\n          // data 需为 Buffer (客户端简单处理了string 转 Buffer), 支持设置 encoding\r\n          // 其他 Uint8Array/ArrayBuffer   默认仅 使用 Buffer.from(data)\r\n          // 其他 Uint8Array/ArrayBuffer   默认仅 使用 Buffer.from(data)\r\n          // 其他 Uint8Array/ArrayBuffer   默认仅 使用 Buffer.from(data)\r\n          // 其他 Uint8Array/ArrayBuffer   默认仅 使用 Buffer.from(data)\r\n          data: 'test test test test test test test',\r\n          encoding: 'utf-8' // 默认可不传\r\n        }\r\n      }, (res) => {\r\n        // 执行的ipp 任务回调 / 错误回调\r\n        console.log(res)\r\n      }, (printer) => {\r\n        // ipp连接成功 回调 打印机信息\r\n        console.log(printer)\r\n      })\r\n    },\r\n    // 自定义 ipp 请求\r\n    ippRequestTest() {\r\n      const printerList = hiprintTemplate.getPrinterList()\r\n      console.log(printerList)\r\n      if (!printerList.length) return\r\n      let p = printerList[0]\r\n      console.log(p)\r\n      // 系统不同， 参数可能不同\r\n      let url = p.options['printer-uri-supported']\r\n      // 详见： https://www.npmjs.com/package/ipp\r\n      hiprint.ippRequest({\r\n        url: url,\r\n        // 传入的数据 ipp.serialize 后 未做任何处理  打印内容 需要 Buffer\r\n        // 传入的数据 ipp.serialize 后 未做任何处理  打印内容 需要 Buffer\r\n        // 传入的数据 ipp.serialize 后 未做任何处理  打印内容 需要 Buffer\r\n        data: {\r\n          'operation': 'Get-Printer-Attributes',\r\n          'operation-attributes-tag': {\r\n            // 测试发现 Request下列3个必须要有\r\n            'attributes-charset': 'utf-8',\r\n            'attributes-natural-language': 'zh-cn',\r\n            'printer-uri': url\r\n          }\r\n        }\r\n      }, (res) => {\r\n        // 执行的ipp 任务回调 / 错误回调\r\n        console.log(res)\r\n      })\r\n    },\r\n    ippRequestPrint() {\r\n      const printerList = hiprintTemplate.getPrinterList()\r\n      console.log(printerList)\r\n      if (!printerList.length) return\r\n      let p = printerList[0]\r\n      console.log(p)\r\n      // 系统不同， 参数可能不同\r\n      let url = p.options['printer-uri-supported']\r\n      let str = 'ippRequestPrint ippRequestPrint ippRequestPrint'\r\n      let array = new Uint8Array(str.length)\r\n      for (var i = 0; i < str.length; i++) {\r\n        array[i] = str.charCodeAt(i)\r\n      }\r\n      let testData = array.buffer\r\n      // 详见： https://www.npmjs.com/package/ipp\r\n      hiprint.ippRequest({\r\n        url: url,\r\n        // 传入的数据 ipp.serialize 后 未做任何处理  打印内容 需要 Buffer\r\n        // 传入的数据 ipp.serialize 后 未做任何处理  打印内容 需要 Buffer\r\n        // 传入的数据 ipp.serialize 后 未做任何处理  打印内容 需要 Buffer\r\n        data: {\r\n          'operation': 'Print-Job',\r\n          'operation-attributes-tag': {\r\n            // 测试发现 Request下列3个必须要有\r\n            'attributes-charset': 'utf-8',\r\n            'attributes-natural-language': 'zh-cn',\r\n            'printer-uri': url,\r\n            'requesting-user-name': 'hiPrint', // 用户名\r\n            'job-name': 'ipp Request Job', // 任务名\r\n            'document-format': 'text/plain' // 文档类型\r\n          },\r\n          data: testData\r\n        }\r\n      }, (res) => {\r\n        // 执行的ipp 任务回调 / 错误回调\r\n        console.log(res)\r\n      })\r\n    },\r\n    updateJson() {\r\n      if (hiprintTemplate) {\r\n        try {\r\n          hiprintTemplate.update(JSON.parse(this.jsonIn))\r\n        } catch (e) {\r\n          this.$message.error(`更新失败: ${e}`)\r\n        }\r\n      }\r\n    },\r\n    exportJson() {\r\n      if (hiprintTemplate) {\r\n        this.jsonOut = JSON.stringify(hiprintTemplate.getJson() || {})\r\n      }\r\n    },\r\n    setElsAlign(e) {\r\n      hiprintTemplate.setElsAlign(e)\r\n    },\r\n    setElsSpace(h) {\r\n      hiprintTemplate.setElsSpace(10, h)\r\n    },\r\n    getSelectEls() {\r\n      let els = hiprintTemplate.getSelectEls()\r\n      console.log(els)\r\n    },\r\n    updateFontSize() {\r\n      hiprintTemplate.updateOption('fontSize', 12)\r\n    },\r\n    updateFontWeight() {\r\n      hiprintTemplate.updateOption('fontWeight', 'bolder')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n// 拖拽\r\n.drag_item_box {\r\n  height: 100%;\r\n  padding: 6px;\r\n}\r\n\r\n.drag_item_box > div {\r\n  height: 100%;\r\n  width: 100%;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.drag_item_box > div > a {\r\n  text-align: center;\r\n  text-decoration-line: none;\r\n}\r\n\r\n.drag_item_box > div > a > span {\r\n  font-size: 28px;\r\n}\r\n\r\n.drag_item_box > div > a > p {\r\n  margin: 0;\r\n}\r\n\r\n.drag_item_title {\r\n  font-size: 16px;\r\n  padding: 12px 6px 0 6px;\r\n  font-weight: bold;\r\n}\r\n\r\n// 默认图片\r\n.hiprint-printElement-image-content {\r\n  img {\r\n    content: url(\"../../../../assets/images/signet5.png\");\r\n  }\r\n}\r\n\r\n// 辅助线样式\r\n::v-deep .toplineOfPosition {\r\n  border: 0;\r\n  border-top: 1px dashed purple;\r\n}\r\n\r\n::v-deep .bottomlineOfPosition {\r\n  border: 0;\r\n  border-top: 1px dashed purple;\r\n}\r\n\r\n::v-deep .leftlineOfPosition {\r\n  border: 0;\r\n  border-left: 1px dashed purple;\r\n}\r\n\r\n::v-deep .rightlineOfPosition {\r\n  border: 0;\r\n  border-left: 1px dashed purple;\r\n}\r\n\r\n// 设计容器\r\n.card-design {\r\n  overflow: hidden;\r\n  overflow-x: auto;\r\n  overflow-y: auto;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8PA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,UAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,QAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,SAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,MAAA,GAAAL,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA,IAAAQ,eAAA;AAAA,IAAAC,QAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IAAAC,YAAA,EAAAA,gBAAA;IAAAC,QAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,QAAA;QACAC,IAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACAC,UAAA;QACA;UACAF,KAAA;UACAC,MAAA;QACA;QACA;UACAD,KAAA;UACAC,MAAA;QACA;QACA;UACAD,KAAA;UACAC,MAAA;QACA;QACA;UACAD,KAAA;UACAC,MAAA;QACA;QACA;UACAD,KAAA;UACAC,MAAA;QACA;QACA;UACAD,KAAA;UACAC,MAAA;QACA;MACA;MACA;MACAE,eAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,UAAA;MACAC,QAAA;MACAC,QAAA;MACA;MACAC,MAAA;MACAC,OAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAb,IAAA;MACA,IAAAc,KAAA,QAAAX,UAAA;MACA,SAAAY,GAAA,IAAAD,KAAA;QACA,IAAAE,IAAA,GAAAF,KAAA,CAAAC,GAAA;QACA,IAAAE,cAAA,QAAAlB,QAAA;UAAAE,KAAA,GAAAgB,cAAA,CAAAhB,KAAA;UAAAC,MAAA,GAAAe,cAAA,CAAAf,MAAA;QACA,IAAAc,IAAA,CAAAf,KAAA,KAAAA,KAAA,IAAAe,IAAA,CAAAd,MAAA,KAAAA,MAAA;UACAF,IAAA,GAAAe,GAAA;QACA;MACA;MACA,OAAAf,IAAA;IACA;EACA;EACAkB,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;EACA;EACAC,OAAA;IACAD,IAAA,WAAAA,KAAA;MACAE,cAAA,CAAAF,IAAA;QACAG,SAAA,OAAAC,iCAAA;MACA;MACA;MACAF,cAAA,CAAAG,SAAA;MACA;MACAH,cAAA,CAAAG,SAAA;QACAC,WAAA,GACAC,iBAAA,EACAC,cAAA,EACA;UACA,SAAAC,EAAA;YACA,KAAAnC,IAAA;UACA;UAEA,OAAAmC,CAAA,CAAAC,SAAA,CAAAC,GAAA,aAAAF,CAAA,EAAAG,CAAA;YACA,IAAAH,CAAA,IAAAA,CAAA,CAAAI,MAAA;cACA,IAAAD,CAAA,SAAAH,CAAA,CAAAE,GAAA,YAAAC,CAAA;YACA;YACA;UACA,GAAAH,CAAA,CAAAC,SAAA,CAAAI,YAAA;YACA,YAAAC,MAAA,GAAAC,CAAA,6PAAAD,MAAA;UACA,GAAAN,CAAA,CAAAC,SAAA,CAAAO,QAAA;YACA,IAAAR,CAAA,QAAAM,MAAA,CAAAG,IAAA,UAAAC,GAAA;YACA,IAAAV,CAAA,SAAAW,QAAA,CAAAX,CAAA,CAAAY,QAAA;UACA,GAAAZ,CAAA,CAAAC,SAAA,CAAAY,QAAA,aAAAb,CAAA;YACA,KAAAM,MAAA,CAAAG,IAAA,UAAAC,GAAA,CAAAV,CAAA;UACA,GAAAA,CAAA,CAAAC,SAAA,CAAAa,OAAA;YACA,KAAAR,MAAA,CAAAS,MAAA;UACA,GAAAf,CAAA;QACA,IACA;QACAgB,cAAA;QACAC,IAAA;UACAC,IAAA;UACA;UACA;YACA;YACAC,OAAA;UACA;UAAA;UACA;YACAtD,IAAA;YAAAsD,OAAA,GACA;cACAtD,IAAA;cACAuD,KAAA;cAAA;cACAC,MAAA;YACA;UAEA,EACA;UACAC,cAAA,GACA;YACAzD,IAAA;YACAwD,MAAA;UACA,GACA;YACAxD,IAAA;YAAA;YACAuD,KAAA;YAAA;YACAC,MAAA;UACA,GACA;YACAxD,IAAA;YACAwD,MAAA;UACA;QAEA;QACAE,KAAA;UACAL,IAAA,GACA;YACA;YACAM,OAAA;YACA3D,IAAA;YAAAsD,OAAA,GACA;cACAtD,IAAA;cACAwD,MAAA;YACA,GACA;cACAxD,IAAA;cACAwD,MAAA;YACA,GACA;cACAxD,IAAA;cACAwD,MAAA;YACA;UAEA;QAEA;MACA;MACA;MACA5B,cAAA,CAAAgC,uBAAA,CAAAC,WAAA,CAAAnB,CAAA;MACAA,CAAA,2BAAAoB,KAAA;MACA,IAAAC,IAAA;MACA,KAAA1D,QAAA,GAAAP,eAAA,OAAA8B,cAAA,CAAAoC,aAAA;QACA3D,QAAA,EAAA4D,cAAA;QACA;QACAC,kBAAA,WAAAA,mBAAAzB,MAAA;UACA;UACA0B,UAAA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA1B,MAAA,CAAA2B,OAAA;cACA;cACA;cACA;cACAC,IAAA;YACA;UACA;UACA;UACA;QACA;;QACA;QACA;QACA;QACAC,QAAA,GACA;UAAAC,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAC,QAAA;QAAA;QACAC,OAAA;QAAA;QACAC,aAAA,WAAAA,cAAApE,IAAA,EAAAqE,IAAA;UACAC,OAAA,CAAAC,GAAA,CAAAvE,IAAA;UACAsE,OAAA,CAAAC,GAAA,CAAAF,IAAA;QACA;;QACAG,aAAA,WAAAA,cAAAzC,CAAA;UACAuC,OAAA,CAAAC,GAAA,CAAAxC,CAAA;QACA;QACA0C,gBAAA;QACAC,mBAAA;MACA;MACAnF,eAAA,CAAAoF,MAAA;QAAAC,IAAA;MAAA;MACAN,OAAA,CAAAC,GAAA,CAAAhF,eAAA;MACA;MACA,KAAAgB,UAAA,GAAAhB,eAAA,CAAAsF,YAAA,CAAAlD,KAAA;IACA;IACA;AACA;AACA;AACA;AACA;IACAmD,QAAA,WAAAA,SAAA9E,IAAA,EAAAiE,KAAA;MACA;QACA,IAAAc,MAAA,CAAAC,IAAA,MAAA7E,UAAA,EAAA8E,QAAA,CAAAjF,IAAA;UACA,KAAAD,QAAA;YAAAC,IAAA,EAAAA,IAAA;YAAAC,KAAA,EAAAgE,KAAA,CAAAhE,KAAA;YAAAC,MAAA,EAAA+D,KAAA,CAAA/D;UAAA;UACAX,eAAA,CAAAuF,QAAA,CAAAb,KAAA,CAAAhE,KAAA,EAAAgE,KAAA,CAAA/D,MAAA;QACA;UACA,KAAAH,QAAA;YAAAC,IAAA;YAAAC,KAAA,EAAAgE,KAAA,CAAAhE,KAAA;YAAAC,MAAA,EAAA+D,KAAA,CAAA/D;UAAA;UACAX,eAAA,CAAAuF,QAAA,CAAAb,KAAA,CAAAhE,KAAA,EAAAgE,KAAA,CAAA/D,MAAA;QACA;MACA,SAAAgF,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA,uDAAAE,MAAA,CAAAF,KAAA;MACA;IACA;IACAG,UAAA,WAAAA,WAAA;MACA,IAAApB,KAAA;MACAA,KAAA,CAAAhE,KAAA,QAAAI,UAAA;MACA4D,KAAA,CAAA/D,MAAA,QAAAI,WAAA;MACA,KAAAF,eAAA;MACA,KAAA0E,QAAA,UAAAb,KAAA;IACA;IACAqB,WAAA,WAAAA,YAAAC,GAAA;MACA,IAAAhF,UAAA,QAAAA,UAAA;MACA,IAAAgF,GAAA;QACAhF,UAAA;QACA,IAAAA,UAAA,QAAAC,QAAA,EAAAD,UAAA;MACA;QACAA,UAAA;QACA,IAAAA,UAAA,QAAAE,QAAA,EAAAF,UAAA;MACA;MACA,IAAAhB,eAAA;QACA;QACAA,eAAA,CAAAiG,IAAA,CAAAjF,UAAA;QACA,KAAAA,UAAA,GAAAA,UAAA;MACA;IACA;IACAkF,WAAA,WAAAA,YAAA;MACA,IAAAlG,eAAA;QACAA,eAAA,CAAAkG,WAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA;MACArE,cAAA,CAAAsE,iBAAA,iCAAA3F,IAAA;QACAA,IAAA,CAAAgE,KAAA;QACA,OAAAhE,IAAA;MACA;MACA;MACAqB,cAAA,CAAAuE,kBAAA,WAAAC,IAAA;QACAvB,OAAA,CAAAC,GAAA;QACAD,OAAA,CAAAC,GAAA,CAAAsB,IAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACAxE,cAAA,CAAAyE,UAAA,iBAAAjG,IAAA;QACAyE,OAAA,CAAAC,GAAA;QACAD,OAAA,CAAAC,GAAA,CAAA1E,IAAA;MACA;MACAwB,cAAA,CAAAyE,UAAA,mBAAAjG,IAAA;QACAyE,OAAA,CAAAC,GAAA;QACAD,OAAA,CAAAC,GAAA,CAAA1E,IAAA;MACA;MACAwB,cAAA,CAAAyE,UAAA,kBAAAjG,IAAA;QACAyE,OAAA,CAAAC,GAAA;QACAD,OAAA,CAAAC,GAAA,CAAA1E,IAAA;MACA;MACAwB,cAAA,CAAAyE,UAAA,kBAAAjG,IAAA;QACAyE,OAAA,CAAAC,GAAA;QACAD,OAAA,CAAAC,GAAA,CAAA1E,IAAA;MACA;MACAwB,cAAA,CAAAyE,UAAA,kBAAAjG,IAAA;QACAyE,OAAA,CAAAC,GAAA;QACAD,OAAA,CAAAC,GAAA,CAAA1E,IAAA;MACA;MACA;MACAwB,cAAA,CAAAyE,UAAA,wBAAAjG,IAAA;QACAyE,OAAA,CAAAC,GAAA;QACAD,OAAA,CAAAC,GAAA,CAAA1E,IAAA;MACA;;MAEA;MACA,KAAAkG,KAAA,CAAAL,OAAA,CAAAM,IAAA,CAAAzG,eAAA,EAAA0G,kBAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,IAAA3G,eAAA,QAAA4G,MAAA,CAAAC,SAAA,EAAA1C,cAAA,EAAAuC,kBAAA;QACAI,YAAA,WAAAA,aAAA;UACA,IAAAvE,GAAA;UACA,OAAAA,GAAA;QACA;MACA;MACAwC,OAAA,CAAAC,GAAA,CAAAhF,eAAA;IACA;IACA+G,UAAA,WAAAA,WAAA;MACA,IAAA9C,IAAA;MACA,IAAA+C,MAAA,CAAAC,WAAA,CAAAC,MAAA;QACA,IAAAlH,gBAAA,QAAAmH,OAAA,CAAAN,SAAA,EAAA1C,cAAA,EAAAuC,kBAAA;UACAU,OAAA;UAAA3C,KAAA;UACAqC,YAAA,WAAAA,aAAA;YACA;YACA,IAAAvE,GAAA;YACA,OAAAA,GAAA;UACA;QACA;QACA,IAAAf,GAAA;QACAxB,gBAAA,CAAAqH,EAAA;UACApD,IAAA,CAAAqD,aAAA,CAAAC,OAAA;YACA/F,GAAA,EAAAA,GAAA;YACAgG,SAAA;YACAC,OAAA,EAAAjG,GAAA;YACAkG,WAAA;UACA;QACA;QACA;MACA;MACA,KAAA9B,QAAA,CAAAD,KAAA;IACA;IACAgC,KAAA,WAAAA,MAAA;MACA,IAAAX,MAAA,CAAAC,WAAA,CAAAC,MAAA;QACA,IAAAU,WAAA,GAAA5H,eAAA,CAAA6H,cAAA;QACA9C,OAAA,CAAAC,GAAA,CAAA4C,WAAA;QACA5H,eAAA,CAAA8H,MAAA,CAAApB,kBAAA;UAAAU,OAAA;UAAA3C,KAAA;QAAA;QACA;MACA;MACA,KAAAmB,QAAA,CAAAD,KAAA;IACA;IACAoC,UAAA,WAAAA,WAAA;MACA;QACA/H,eAAA,CAAAgI,KAAA;MACA,SAAArC,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA,uDAAAE,MAAA,CAAAF,KAAA;MACA;IACA;IACAsC,YAAA,WAAAA,aAAA;MACA;MACA,IAAAL,WAAA,GAAA5H,eAAA,CAAA6H,cAAA;MACA9C,OAAA,CAAAC,GAAA,CAAA4C,WAAA;MACA,KAAAA,WAAA,CAAAnF,MAAA;MACA,IAAAyF,CAAA,GAAAN,WAAA;MACA7C,OAAA,CAAAC,GAAA,CAAAkD,CAAA;MACA;MACA,IAAAC,GAAA,GAAAD,CAAA,CAAA1E,OAAA;MACA;MACA1B,cAAA,CAAAsG,QAAA;QACAD,GAAA,EAAAA,GAAA;QACA;QACAE,GAAA;QACAC,MAAA;QAAA;QACA;QACAb,OAAA;MACA,aAAAc,GAAA;QACA;QACAxD,OAAA,CAAAC,GAAA,CAAAuD,GAAA;MACA,aAAAnB,OAAA;QACA;QACArC,OAAA,CAAAC,GAAA,CAAAoC,OAAA;MACA;IACA;IACAoB,YAAA,WAAAA,aAAA;MACA;MACA,IAAAZ,WAAA,GAAA5H,eAAA,CAAA6H,cAAA;MACA9C,OAAA,CAAAC,GAAA,CAAA4C,WAAA;MACA,KAAAA,WAAA,CAAAnF,MAAA;MACA,IAAAyF,CAAA,GAAAN,WAAA;MACA7C,OAAA,CAAAC,GAAA,CAAAkD,CAAA;MACA;MACA,IAAAC,GAAA,GAAAD,CAAA,CAAA1E,OAAA;MACA;MACA1B,cAAA,CAAAsG,QAAA;QACAD,GAAA,EAAAA,GAAA;QACA;QACAE,GAAA;QACAC,MAAA;QACA;QACAb,OAAA;UACA;YACA;YAAA;YACA;YAAA;YACA;UACA;;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACAnH,IAAA;UACAmI,QAAA;QACA;MACA,aAAAF,GAAA;QACA;QACAxD,OAAA,CAAAC,GAAA,CAAAuD,GAAA;MACA,aAAAnB,OAAA;QACA;QACArC,OAAA,CAAAC,GAAA,CAAAoC,OAAA;MACA;IACA;IACA;IACAsB,cAAA,WAAAA,eAAA;MACA,IAAAd,WAAA,GAAA5H,eAAA,CAAA6H,cAAA;MACA9C,OAAA,CAAAC,GAAA,CAAA4C,WAAA;MACA,KAAAA,WAAA,CAAAnF,MAAA;MACA,IAAAyF,CAAA,GAAAN,WAAA;MACA7C,OAAA,CAAAC,GAAA,CAAAkD,CAAA;MACA;MACA,IAAAC,GAAA,GAAAD,CAAA,CAAA1E,OAAA;MACA;MACA1B,cAAA,CAAA6G,UAAA;QACAR,GAAA,EAAAA,GAAA;QACA;QACA;QACA;QACA7H,IAAA;UACA;UACA;YACA;YACA;YACA;YACA,eAAA6H;UACA;QACA;MACA,aAAAI,GAAA;QACA;QACAxD,OAAA,CAAAC,GAAA,CAAAuD,GAAA;MACA;IACA;IACAK,eAAA,WAAAA,gBAAA;MACA,IAAAhB,WAAA,GAAA5H,eAAA,CAAA6H,cAAA;MACA9C,OAAA,CAAAC,GAAA,CAAA4C,WAAA;MACA,KAAAA,WAAA,CAAAnF,MAAA;MACA,IAAAyF,CAAA,GAAAN,WAAA;MACA7C,OAAA,CAAAC,GAAA,CAAAkD,CAAA;MACA;MACA,IAAAC,GAAA,GAAAD,CAAA,CAAA1E,OAAA;MACA,IAAAqF,GAAA;MACA,IAAAC,KAAA,OAAAC,UAAA,CAAAF,GAAA,CAAApG,MAAA;MACA,SAAAuG,CAAA,MAAAA,CAAA,GAAAH,GAAA,CAAApG,MAAA,EAAAuG,CAAA;QACAF,KAAA,CAAAE,CAAA,IAAAH,GAAA,CAAAI,UAAA,CAAAD,CAAA;MACA;MACA,IAAAE,QAAA,GAAAJ,KAAA,CAAAK,MAAA;MACA;MACArH,cAAA,CAAA6G,UAAA;QACAR,GAAA,EAAAA,GAAA;QACA;QACA;QACA;QACA7H,IAAA;UACA;UACA;YACA;YACA;YACA;YACA,eAAA6H,GAAA;YACA;YAAA;YACA;YAAA;YACA;UACA;;UACA7H,IAAA,EAAA4I;QACA;MACA,aAAAX,GAAA;QACA;QACAxD,OAAA,CAAAC,GAAA,CAAAuD,GAAA;MACA;IACA;IACAa,UAAA,WAAAA,WAAA;MACA,IAAApJ,eAAA;QACA;UACAA,eAAA,CAAAqJ,MAAA,CAAAC,IAAA,CAAAC,KAAA,MAAApI,MAAA;QACA,SAAAqB,CAAA;UACA,KAAAoD,QAAA,CAAAD,KAAA,8BAAAE,MAAA,CAAArD,CAAA;QACA;MACA;IACA;IACAgH,UAAA,WAAAA,WAAA;MACA,IAAAxJ,eAAA;QACA,KAAAoB,OAAA,GAAAkI,IAAA,CAAAG,SAAA,CAAAzJ,eAAA,CAAA0J,OAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAAnH,CAAA;MACAxC,eAAA,CAAA2J,WAAA,CAAAnH,CAAA;IACA;IACAoH,WAAA,WAAAA,YAAAC,CAAA;MACA7J,eAAA,CAAA4J,WAAA,KAAAC,CAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,IAAAC,GAAA,GAAA/J,eAAA,CAAA8J,YAAA;MACA/E,OAAA,CAAAC,GAAA,CAAA+E,GAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACAhK,eAAA,CAAAiK,YAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACAlK,eAAA,CAAAiK,YAAA;IACA;EACA;AACA;AAAAE,OAAA,CAAAC,OAAA,GAAAnK,QAAA"}]}