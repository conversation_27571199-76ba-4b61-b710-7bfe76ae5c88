{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BulkTruckComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BulkTruckComponent.vue", "mtime": 1754881964228}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "_index", "_index2", "_debitNodeList", "name", "components", "DebitNoteList", "Audit", "LogisticsProgress", "ChargeList", "locationSelect", "TreeSelect", "props", "bulkTruckList", "type", "Array", "default", "_default", "form", "Object", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "companyList", "locationOptions", "data", "dispatchBillConfig", "file", "templateList", "computed", "isDisabled", "methods", "handleAddDebitNote", "serviceObject", "row", "sqdRctNo", "rctNo", "rctId", "isRecievingOrPaying", "rsChargeList", "$emit", "isFieldDisabled", "item", "getServiceInstanceDisable", "rsServiceInstances", "getSupplierEmail", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "serviceInstance", "agreementTypeCode", "agreementNo", "getDispatchingBill", "addTuck", "rsOpTruckList", "push", "deleteTruckItem", "truckRow", "filter", "truck", "deleteLogItem", "logItem", "rsOpLogList", "log", "deleteChargeItem", "chargeItem", "charge", "changeServiceFold", "addBulkTruck", "deleteRsOpBulkTruck", "openChargeSelect", "auditCharge", "event", "generateFreight", "type1", "type2", "handleAddCommon", "openDispatchCommon", "copyFreight", "calculateCharge", "serviceType", "getPayable", "$parent", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/BulkTruckComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"bulk-truck-component\">\r\n    <!--散货拖车-->\r\n    <div v-for=\"(item, index) in bulkTruckList\" :key=\"`bulk-truck-${index}`\" class=\"bulk-truck-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                拖车-BulkTruck\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addBulkTruck\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpBulkTruck(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(item.serviceTypeId, item)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item,$event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"dispatch-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in dispatchBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"getDispatchingBill(item)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"dispatch-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(5, 51, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"派车单号\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageSupplierNo\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"拖车公司单号\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"装运时间\">\r\n                    <el-date-picker\r\n                      v-model=\"item.precarriageTime\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"装运时间\"\r\n                      style=\"width:100%\"\r\n                      type=\"datetime\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"装运区域\">\r\n                    <location-select\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      :load-options=\"locationOptions\"\r\n                      :multiple=\"false\"\r\n                      :pass=\"item.precarriageRegionId\"\r\n                      :placeholder=\"'装运区域'\"\r\n                      @return=\"item.precarriageRegionId=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"装运详址\">\r\n                    <el-row :gutter=\"5\">\r\n                      <el-col :span=\"19\">\r\n                        <el-input\r\n                          v-model=\"item.precarriageAddress\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"装运详址\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"2\">\r\n                        <el-button\r\n                          :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          style=\"color: blue\"\r\n                          type=\"text\"\r\n                          @click=\"handleAddCommon('dispatch')\"\r\n                        >[↗]\r\n                        </el-button>\r\n                      </el-col>\r\n                      <el-col :span=\"1\">\r\n                        <el-button\r\n                          :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          style=\"color: blue\"\r\n                          type=\"text\"\r\n                          @click=\"openDispatchCommon\"\r\n                        >[...]\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"联系人\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageContact\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"装运联系人\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"装运电话\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageTel\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"装运电话\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"14\">\r\n                  <el-form-item label=\"装运备注\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageRemark\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"装运备注\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col>\r\n                  <el-table\r\n                    :data=\"item.rsOpTruckList\"\r\n                    border\r\n                    class=\"pd0\"\r\n                  >\r\n                    <el-table-column\r\n                      label=\"序号\"\r\n                      type=\"index\"\r\n                      width=\"50\"\r\n                    />\r\n                    <el-table-column align=\"center\" label=\"提货司机姓名\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageDriverName\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机姓名\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货司机电话\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageDriverTel\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机电话\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货司机车牌\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageTruckNo\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机车牌\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"司机备注\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageTruckRemark\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机备注\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"柜号\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.containerNo\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"柜号\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"柜型\">\r\n                      <template slot-scope=\"scope\">\r\n                        <tree-select\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          :pass=\"scope.row.containerTypeCode\"\r\n                          :type=\"'unit'\"\r\n                          placeholder=\"选择柜型\"\r\n                          @returnData=\"scope.row.containerTypeCode = $event.unitCode\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"封条\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.sealNo\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"封条\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"磅单\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.weightPaper\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"磅单\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货须知\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageNote\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货须知\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货备注\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageRemark\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货备注\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button\r\n                          v-if=\"!disabled && !getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"deleteTruckItem(item, scope.row)\"\r\n                        >删除\r\n                        </el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                  <el-button\r\n                    :disabled=\"isFieldDisabled(item)\"\r\n                    style=\"padding: 0\"\r\n                    type=\"text\"\r\n                    @click=\"addTuck(item.rsOpTruckList)\"\r\n                  >[＋]\r\n                  </el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <div>\r\n                <logistics-progress\r\n                  :disabled=\"isFieldDisabled(item)\"\r\n                  :logistics-progress-data=\"item.rsOpLogList\"\r\n                  :open-logistics-progress-list=\"true\"\r\n                  :process-type=\"4\"\r\n                  :service-type=\"51\"\r\n                  @deleteItem=\"deleteLogItem(item, $event)\"\r\n                  @return=\"item.rsOpLogList=$event\"\r\n                />\r\n              </div>\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :service-type-id=\"51\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList=[]\"\r\n              @deleteItem=\"deleteChargeItem(item, $event)\"\r\n              @return=\"calculateCharge(51, $event, item)\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"item.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(item)\"\r\n              @deleteItem=\"item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(51,$event,item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport locationSelect from \"@/components/LocationSelect/index.vue\"\r\nimport TreeSelect from \"@/components/TreeSelect/index.vue\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\n\r\nexport default {\r\n  name: \"BulkTruckComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    locationSelect,\r\n    TreeSelect\r\n  },\r\n  props: {\r\n    // 散货拖车数据列表\r\n    bulkTruckList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    locationOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dispatchBillConfig: [{\r\n        file: \"派车单\",\r\n        templateList: [\"Dispatch Bill\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.psaVerify || this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理派车单生成\r\n    getDispatchingBill(item) {\r\n      this.$emit(\"getDispatchingBill\", item)\r\n    },\r\n    // 添加拖车记录\r\n    addTuck(rsOpTruckList) {\r\n      if (!rsOpTruckList) return\r\n      rsOpTruckList.push({})\r\n    },\r\n    // 删除拖车记录\r\n    deleteTruckItem(item, truckRow) {\r\n      item.rsOpTruckList = item.rsOpTruckList.filter(truck => truck !== truckRow)\r\n    },\r\n    // 删除物流进度\r\n    deleteLogItem(item, logItem) {\r\n      item.rsOpLogList = item.rsOpLogList.filter(log => log !== logItem)\r\n    },\r\n    // 删除费用项\r\n    deleteChargeItem(item, chargeItem) {\r\n      item.rsChargeList = item.rsChargeList.filter(charge => charge !== chargeItem)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addBulkTruck() {\r\n      this.$emit(\"addBulkTruck\")\r\n    },\r\n    deleteRsOpBulkTruck(item) {\r\n      this.$emit(\"deleteRsOpBulkTruck\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    handleAddCommon(type) {\r\n      this.$emit(\"handleAddCommon\", type)\r\n    },\r\n    openDispatchCommon() {\r\n      this.$emit(\"openDispatchCommon\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(serviceType, item) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(serviceType, item) : null\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// BulkTruck组件特定样式\r\n.bulk-truck-component {\r\n  width: 100%;\r\n\r\n  .bulk-truck-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .dispatch-bill-container {\r\n      margin-left: auto;\r\n\r\n      .dispatch-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        margin-left: 5px;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n\r\n  // 司机信息表格样式\r\n  .pd0 {\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;AAyaA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,OAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,cAAA,GAAAN,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAGA;EACAM,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,cAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,aAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,IAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,UAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,aAAA;MACAR,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAQ,SAAA;MACAV,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAX,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAZ,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAW,SAAA;MACAb,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAY,YAAA;MACAd,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAY,WAAA;MACAf,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAa,eAAA;MACAhB,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;EACA;EACAc,IAAA,WAAAA,KAAA;IACA;MACAC,kBAAA;QACAC,IAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAX,QAAA,SAAAE,SAAA;IACA;EACA;EACAU,OAAA;IACAC,kBAAA,WAAAA,mBAAAC,aAAA;MACA,IAAAC,GAAA;MACAA,GAAA,CAAAC,QAAA,QAAAvB,IAAA,CAAAwB,KAAA;MACAF,GAAA,CAAAG,KAAA,QAAAzB,IAAA,CAAAyB,KAAA;MACAH,GAAA,CAAAI,mBAAA;MACAJ,GAAA,CAAAK,YAAA;MACA,KAAAC,KAAA,iBAAAN,GAAA,EAAAD,aAAA;IACA;IACA;IACAQ,eAAA,WAAAA,gBAAAC,IAAA;MACA,YAAArB,SAAA,SAAAF,QAAA,SAAAwB,yBAAA,CAAAD,IAAA,CAAAE,kBAAA;IACA;IACA;IACAC,gBAAA,WAAAA,iBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAzB,YAAA,CAAA0B,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAJ,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,eAAA;MACA,OAAAA,eAAA,CAAAC,iBAAA,GAAAD,eAAA,CAAAE,WAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAd,IAAA;MACA,KAAAF,KAAA,uBAAAE,IAAA;IACA;IACA;IACAe,OAAA,WAAAA,QAAAC,aAAA;MACA,KAAAA,aAAA;MACAA,aAAA,CAAAC,IAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAAlB,IAAA,EAAAmB,QAAA;MACAnB,IAAA,CAAAgB,aAAA,GAAAhB,IAAA,CAAAgB,aAAA,CAAAI,MAAA,WAAAC,KAAA;QAAA,OAAAA,KAAA,KAAAF,QAAA;MAAA;IACA;IACA;IACAG,aAAA,WAAAA,cAAAtB,IAAA,EAAAuB,OAAA;MACAvB,IAAA,CAAAwB,WAAA,GAAAxB,IAAA,CAAAwB,WAAA,CAAAJ,MAAA,WAAAK,GAAA;QAAA,OAAAA,GAAA,KAAAF,OAAA;MAAA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAA1B,IAAA,EAAA2B,UAAA;MACA3B,IAAA,CAAAH,YAAA,GAAAG,IAAA,CAAAH,YAAA,CAAAuB,MAAA,WAAAQ,MAAA;QAAA,OAAAA,MAAA,KAAAD,UAAA;MAAA;IACA;IACA;IACAE,iBAAA,WAAAA,kBAAAlB,eAAA;MACA,KAAAb,KAAA,sBAAAa,eAAA;IACA;IACAmB,YAAA,WAAAA,aAAA;MACA,KAAAhC,KAAA;IACA;IACAiC,mBAAA,WAAAA,oBAAA/B,IAAA;MACA,KAAAF,KAAA,wBAAAE,IAAA;IACA;IACAgC,gBAAA,WAAAA,iBAAAhC,IAAA;MACA,KAAAF,KAAA,qBAAAE,IAAA;IACA;IACAiC,WAAA,WAAAA,YAAAjC,IAAA,EAAAkC,KAAA;MACA,KAAApC,KAAA,gBAAAE,IAAA,EAAAkC,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAArC,IAAA;MACA,KAAAF,KAAA,oBAAAsC,KAAA,EAAAC,KAAA,EAAArC,IAAA;IACA;IACAsC,eAAA,WAAAA,gBAAAxE,IAAA;MACA,KAAAgC,KAAA,oBAAAhC,IAAA;IACA;IACAyE,kBAAA,WAAAA,mBAAA;MACA,KAAAzC,KAAA;IACA;IACA0C,WAAA,WAAAA,YAAAN,KAAA;MACA,KAAApC,KAAA,gBAAAoC,KAAA;IACA;IACAO,eAAA,WAAAA,gBAAAC,WAAA,EAAAR,KAAA,EAAAlC,IAAA;MACA,KAAAF,KAAA,oBAAA4C,WAAA,EAAAR,KAAA,EAAAlC,IAAA;IACA;IACA2C,UAAA,WAAAA,WAAAD,WAAA,EAAA1C,IAAA;MACA,YAAA4C,OAAA,CAAAD,UAAA,QAAAC,OAAA,CAAAD,UAAA,CAAAD,WAAA,EAAA1C,IAAA;IACA;IACAC,yBAAA,WAAAA,0BAAAU,eAAA;MACA,YAAAiC,OAAA,CAAA3C,yBAAA,QAAA2C,OAAA,CAAA3C,yBAAA,CAAAU,eAAA;IACA;EACA;AACA;AAAAkC,OAAA,CAAA7E,OAAA,GAAA8E,SAAA"}]}