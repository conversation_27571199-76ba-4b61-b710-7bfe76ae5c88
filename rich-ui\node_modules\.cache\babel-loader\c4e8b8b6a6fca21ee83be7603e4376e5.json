{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\distribute.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\distribute.js", "mtime": 1754876882461}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDistribute", "query", "request", "url", "method", "params", "getDistribute", "distributeId", "addDistribute", "data", "flashDistribute", "updateDistribute", "delDistribute", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/distribute.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询权限分配列表\r\nexport function listDistribute(query) {\r\n  return request({\r\n    url: '/system/distribute/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询权限分配详细\r\nexport function getDistribute(distributeId) {\r\n  return request({\r\n    url: '/system/distribute/' + distributeId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增权限分配\r\nexport function addDistribute(data) {\r\n  return request({\r\n    url: '/system/distribute',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 新增权限分配\r\nexport function flashDistribute() {\r\n  return request({\r\n    url: '/system/distribute/flash',\r\n    method: 'post',\r\n  })\r\n}\r\n\r\n// 修改权限分配\r\nexport function updateDistribute(data) {\r\n  return request({\r\n    url: '/system/distribute',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除权限分配\r\nexport function delDistribute(distributeId) {\r\n  return request({\r\n    url: '/system/distribute/' + distributeId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(distributeId, status) {\r\n  const data = {\r\n    distributeId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/distribute/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,YAAY,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,YAAY;IACzCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAAA,EAAG;EAChC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,gBAAgBA,CAACF,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASG,aAAaA,CAACL,YAAY,EAAE;EAC1C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,YAAY;IACzCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACN,YAAY,EAAEO,MAAM,EAAE;EACjD,IAAML,IAAI,GAAG;IACXF,YAAY,EAAZA,YAAY;IACZO,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}