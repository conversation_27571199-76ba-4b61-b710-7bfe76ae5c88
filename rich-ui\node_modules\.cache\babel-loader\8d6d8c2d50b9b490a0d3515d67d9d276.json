{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\doctype.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\doctype.js", "mtime": 1718100178726}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDoctype", "query", "request", "url", "method", "params", "getDoctype", "docTypeCode", "addDoctype", "data", "updateDoctype", "delDoctype", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/doctype.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询【请填写功能名称】列表\r\nexport function listDoctype(query) {\r\n  return request({\r\n    url: '/system/doctype/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询【请填写功能名称】详细\r\nexport function getDoctype(docTypeCode) {\r\n  return request({\r\n    url: '/system/doctype/' + docTypeCode,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增【请填写功能名称】\r\nexport function addDoctype(data) {\r\n  return request({\r\n    url: '/system/doctype',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改【请填写功能名称】\r\nexport function updateDoctype(data) {\r\n  return request({\r\n    url: '/system/doctype',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除【请填写功能名称】\r\nexport function delDoctype(docTypeCode) {\r\n  return request({\r\n    url: '/system/doctype/' + docTypeCode,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(docTypeCode, status) {\r\n  const data = {\r\n    docTypeCode,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/doctype/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,WAAW,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,WAAW;IACrCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAACD,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,UAAUA,CAACJ,WAAW,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,WAAW;IACrCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,WAAW,EAAEM,MAAM,EAAE;EAChD,IAAMJ,IAAI,GAAG;IACXF,WAAW,EAAXA,WAAW;IACXM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}