{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue?vue&type=template&id=1a95385c&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNoteChargeList.vue", "mtime": 1754899468843}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}