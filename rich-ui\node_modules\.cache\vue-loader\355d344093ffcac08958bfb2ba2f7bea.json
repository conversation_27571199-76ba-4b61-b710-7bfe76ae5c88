{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\logisticsNoInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\logisticsNoInfo.vue", "mtime": 1754876882583}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["logisticsNoInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "logisticsNoInfo.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n             :visible.sync=\"oopen\" append-to-body width=\"1500px\">\r\n    <div style=\"display: flex\">\r\n      <h2 style=\"font-weight: bold ;margin:10px;\">新增编号信息</h2>\r\n      <div style=\"vertical-align: middle;line-height: 41px\">\r\n        <el-button type=\"primary\" @click=\"open=true\">新增</el-button>\r\n      </div>\r\n    </div>\r\n    <el-table border :data=\"logisticsNoInfo\" :row-class-name=\"rowIndex\">\r\n      <el-table-column header-align=\"center\" label=\"SO号码\" prop=\"soNo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"主提单号\" prop=\"mblNo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"货代单号\" prop=\"hblNo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"柜号信息\" prop=\"containersInfo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"发货人\" prop=\"shipper\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"收货人\" prop=\"consignee\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"通知人\" prop=\"notifyParty\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"启运港放舱代理\" prop=\"polBookingAgent\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"目的港换单代理\" prop=\"podHandleAgent\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"唛头\" prop=\"shippingMark\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"货描\" prop=\"goodsDescription\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"签单日期\" prop=\"blIssueDate\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"签单地点\" prop=\"blIssueLocation\"></el-table-column>\r\n      <el-table-column header-align=\"center\" align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\"\r\n                       width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            icon=\"el-icon-edit\"\r\n            size=\"mini\"\r\n            style=\"margin-right: -8px\"\r\n            type=\"success\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-delete\"\r\n            size=\"mini\"\r\n            style=\"margin-right: -8px\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n               :visible.sync=\"open\" append-to-body width=\"500px\" title=\"新增编号信息\">\r\n      <el-form border :data=\"form\" label-width=\"105px\">\r\n        <el-form-item label=\"SO号码\" prop=\"soNo\">\r\n          <el-input v-model=\"form.soNo\" placeholder=\"SO号码\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"主提单号\" prop=\"mblNo\">\r\n          <el-input v-model=\"form.mblNo\" placeholder=\"主提单号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"货代单号\" prop=\"hblNo\">\r\n          <el-input v-model=\"form.hblNo\" placeholder=\"货代单号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"柜号信息\" prop=\"containersInfo\">\r\n          <el-input v-model=\"form.containersInfo\" placeholder=\"柜号信息\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"发货人\" prop=\"shipper\">\r\n          <el-input v-model=\"form.shipper\" placeholder=\"发货人\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货人\" prop=\"consignee\">\r\n          <el-input v-model=\"form.consignee\" placeholder=\"收货人\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"通知人\" prop=\"notifyParty\">\r\n          <el-input v-model=\"form.notifyParty\" placeholder=\"通知人\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"启运港放舱代理\" prop=\"polBookingAgent\">\r\n          <el-input v-model=\"form.polBookingAgent\" placeholder=\"启运港放舱代理\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"目的港换单代理\" prop=\"podHandleAgent\">\r\n          <el-input v-model=\"form.podHandleAgent\" placeholder=\"目的港换单代理\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"唛头\" prop=\"shippingMark\">\r\n          <el-input v-model=\"form.shippingMark\" placeholder=\"唛头\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"货描\" prop=\"goodsDescription\">\r\n          <el-input v-model=\"form.goodsDescription\" placeholder=\"货描\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"签单日期\" prop=\"blIssueDate\">\r\n          <el-input v-model=\"form.blIssueDate\" placeholder=\"签单日期\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"签单地点\" prop=\"blIssueLocation\">\r\n          <el-input v-model=\"form.blIssueLocation\" placeholder=\"签单地点\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\n\r\nexport default {\r\n  name: 'logisticsNoInfo',\r\n  props: ['openLogisticsNoInfo'],\r\n  watch: {\r\n    logisticsNoInfo() {\r\n      this.$emit('return', this.logisticsNoInfo)\r\n    },\r\n    openLogisticsNoInfo(n) {\r\n      this.oopen = n\r\n    },\r\n    oopen(n) {\r\n      if (n == false) {\r\n        this.$emit('close')\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      open: false,\r\n      oopen: false,\r\n      logisticsNoInfo: [],\r\n      form: {},\r\n    }\r\n  },\r\n  methods: {\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1;\r\n    },\r\n    handleUpdate(row) {\r\n      this.form = row\r\n      this.open = true\r\n    },\r\n    handleDelete(row) {\r\n      this.logisticsNoInfo = this.logisticsNoInfo.filter(item => {\r\n        return item.id != row.id\r\n      })\r\n    },\r\n    submitForm() {\r\n      if (this.form.id != null) {\r\n        this.reset()\r\n        this.open = false\r\n      } else {\r\n        this.logisticsNoInfo.push(this.form)\r\n        this.reset()\r\n        this.open = false\r\n      }\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        soNo: null,\r\n        mblNo: null,\r\n        hblNo: null,\r\n        containersInfo: null,\r\n        shipper: null,\r\n        consignee: null,\r\n        notifyParty: null,\r\n        polBookingAgent: null,\r\n        podHandleAgent: null,\r\n        shippingMark: null,\r\n        goodsDescription: null,\r\n        blIssueDate: null,\r\n        blIssueLocation: null,\r\n      }\r\n      this.resetForm(\"form\");\r\n    },\r\n    cancel() {\r\n      this.open = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}