{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\chargetype\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\chargetype\\index.vue", "mtime": 1754876882576}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_chargeType", "require", "name", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "typeList", "title", "open", "queryParams", "pageNum", "pageSize", "chargeTypeQuery", "form", "rules", "watch", "n", "created", "getList", "methods", "_this", "listChargeType", "then", "response", "handleTree", "cancel", "reset", "chargeTypeId", "chargeTypeShortName", "chargeTypeEnName", "chargeTypeLocalName", "orderNum", "remark", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "row", "undefined", "parentId", "handleUpdate", "_this2", "getChargeType", "checkDeptIds", "check", "enterDeptIds", "enter", "submitForm", "_this3", "$refs", "validate", "valid", "updateChargeType", "$modal", "msgSuccess", "addChargeType", "handleDelete", "_this4", "chargeTypeIds", "$confirm", "customClass", "delChargeType", "catch", "handleStatusChange", "_this5", "text", "status", "changeStatus", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "ParentId", "val", "getCheckDeptIds", "includes", "$message", "warning", "filter", "v", "getEnterDeptIds", "exports", "_default"], "sources": ["src/views/system/chargetype/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"查询\" prop=\"chargeTypeQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.chargeTypeQuery\"\r\n              clearable\r\n              placeholder=\"中英文，简称\"\r\n              style=\"width: 100%\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <!--          <el-col :span=\"1.5\">-->\r\n          <!--            <el-button-->\r\n          <!--              type=\"primary\"-->\r\n          <!--              plain-->\r\n          <!--              icon=\"el-icon-plus\"-->\r\n          <!--              size=\"mini\"-->\r\n          <!--              @click=\"handleAdd\"-->\r\n          <!--              v-hasPermi=\"['system:chargetype:add']\"-->\r\n          <!--            >新增-->\r\n          <!--            </el-button>-->\r\n          <!--          </el-col>-->\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:chargetype:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"typeList\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n                  row-key=\"chargeTypeId\"\r\n                  @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column key=\"chargeTypeName\" align=\"left\" label=\"费用类型名称\" width=\"350\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.chargeTypeShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.chargeTypeLocalName }}</a>\r\n              {{ scope.row.chargeTypeEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column key=\"enterDepts\" align=\"left\" label=\"录入部门\" prop=\"enterDepts\" show-tooltip-when-overflow\r\n                           width=\"170\"/>\r\n          <el-table-column key=\"checkDepts\" align=\"left\" label=\"可显部门\" prop=\"checkDepts\" show-tooltip-when-overflow\r\n                           width=\"170\"/>\r\n          <el-table-column key=\"remark\" align=\"left\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow/>\r\n          <el-table-column key=\"orderNum\" align=\"center\" label=\"优先级\" prop=\"orderNum\" width=\"58\"/>\r\n          <el-table-column key=\"status\" align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:chargetype:add']\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:chargetype:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.parentId != 0\"\r\n                v-hasPermi=\"['system:chargetype:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改费用类型对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\" :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item v-if=\"form.parentId!=0\" label=\"上级类型\" prop=\"parentId\">\r\n          <tree-select :multiple=\"false\" :pass=\"form.parentId\" :placeholder=\"'上级类名'\" :type=\"'chargeType'\"\r\n                       :dbn=\"false\" @return=\"ParentId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"简称\" prop=\"chargeTypeShortName\">\r\n          <el-input v-model=\"form.chargeTypeShortName\" placeholder=\"简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"chargeTypeEnName\">\r\n          <el-input v-model=\"form.chargeTypeEnName\" placeholder=\"英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"chargeTypeLocalName\">\r\n          <el-input v-model=\"form.chargeTypeLocalName\" placeholder=\"中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"录入部门\" prop=\"enterDeptIds\">\r\n          <tree-select :multiple=\"true\" :pass=\"form.enterDeptIds\" :placeholder=\"'适用的部门'\"\r\n                       :type=\"'dept'\" @return=\"getEnterDeptIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"可显部门\" prop=\"checkDeptIds\">\r\n          <tree-select :multiple=\"true\" :pass=\"form.checkDeptIds\" :placeholder=\"'适用的部门'\"\r\n                       :type=\"'dept'\" @return=\"getCheckDeptIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" style=\"width: 100%\" v-model=\"form.orderNum\" placeholder=\"排序\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 5, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addChargeType,\r\n  changeStatus,\r\n  delChargeType,\r\n  getChargeType,\r\n  listChargeType,\r\n  updateChargeType\r\n} from \"@/api/system/chargeType\";\r\n\r\nexport default {\r\n  name: \"Type\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 费用类型表格数据\r\n      typeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        chargeTypeQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {}\r\n      ,\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n      ;\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询费用类型列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listChargeType(this.queryParams).then(response => {\r\n        this.typeList = this.handleTree(response.data, \"chargeTypeId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        chargeTypeId: null,\r\n        chargeTypeShortName: null,\r\n        chargeTypeEnName: null,\r\n        chargeTypeLocalName: null,\r\n        orderNum: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.chargeTypeId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.open = true;\r\n      if (row != undefined) {\r\n        this.form.parentId = row.chargeTypeId;\r\n      }\r\n      this.title = \"添加费用类型\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const chargeTypeId = row.chargeTypeId || this.ids\r\n      getChargeType(chargeTypeId).then(response => {\r\n        this.form = response.data;\r\n        this.form.checkDeptIds = response.check\r\n        this.form.enterDeptIds = response.enter\r\n        this.open = true;\r\n        this.title = \"修改费用类型\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.chargeTypeId != null) {\r\n            updateChargeType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addChargeType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const chargeTypeIds = row.chargeTypeId || this.ids;\r\n      this.$confirm('是否确认删除费用类型编号为\"' + chargeTypeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delChargeType(chargeTypeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.chargeTypeLocalName + '\"吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.chargeTypeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/type/export', {\r\n        ...this.queryParams\r\n      }, `type_${new Date().getTime()}.xlsx`)\r\n    },\r\n    ParentId(val) {\r\n      this.form.parentId = val\r\n    },\r\n    getCheckDeptIds(val) {\r\n      if (this.form.enterDeptIds.includes(val[val.length - 1])) {\r\n        this.$message.warning(\"录入部门已存在此部门，已可显\")\r\n        this.form.checkDeptIds = val.filter(v => {\r\n          return v != val[val.length - 1]\r\n        })\r\n      } else {\r\n        this.form.checkDeptIds = val\r\n      }\r\n    },\r\n    getEnterDeptIds(val) {\r\n      this.form.enterDeptIds = val\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AAkKA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eASA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,eAAA;MACA;MACA;MACAC,IAAA;MAEA;MACAC,KAAA;IACA;EAEA;EACAC,KAAA;IACAX,UAAA,WAAAA,WAAAY,CAAA;MACA,IAAAA,CAAA;QACA,KAAAjB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAmB,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApB,OAAA;MACA,IAAAqB,0BAAA,OAAAZ,WAAA,EAAAa,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAd,QAAA,GAAAc,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA1B,IAAA;QACAuB,KAAA,CAAApB,OAAA;MACA;IACA;IACA;IACAyB,MAAA,WAAAA,OAAA;MACA,KAAAjB,IAAA;MACA,KAAAkB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAb,IAAA;QACAc,YAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAzB,WAAA,CAAAC,OAAA;MACA,KAAAQ,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApC,GAAA,GAAAoC,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAZ,YAAA;MAAA;MACA,KAAAzB,MAAA,GAAAmC,SAAA,CAAAG,MAAA;MACA,KAAArC,QAAA,IAAAkC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAhB,KAAA;MACA,KAAAlB,IAAA;MACA,IAAAkC,GAAA,IAAAC,SAAA;QACA,KAAA9B,IAAA,CAAA+B,QAAA,GAAAF,GAAA,CAAAf,YAAA;MACA;MACA,KAAApB,KAAA;IACA;IACA,aACAsC,YAAA,WAAAA,aAAAH,GAAA;MAAA,IAAAI,MAAA;MACA,KAAApB,KAAA;MACA,IAAAC,YAAA,GAAAe,GAAA,CAAAf,YAAA,SAAA1B,GAAA;MACA,IAAA8C,yBAAA,EAAApB,YAAA,EAAAL,IAAA,WAAAC,QAAA;QACAuB,MAAA,CAAAjC,IAAA,GAAAU,QAAA,CAAA1B,IAAA;QACAiD,MAAA,CAAAjC,IAAA,CAAAmC,YAAA,GAAAzB,QAAA,CAAA0B,KAAA;QACAH,MAAA,CAAAjC,IAAA,CAAAqC,YAAA,GAAA3B,QAAA,CAAA4B,KAAA;QACAL,MAAA,CAAAtC,IAAA;QACAsC,MAAA,CAAAvC,KAAA;MACA;IACA;IACA,WACA6C,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAxC,IAAA,CAAAc,YAAA;YACA,IAAA8B,4BAAA,EAAAJ,MAAA,CAAAxC,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACA8B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA7C,IAAA;cACA6C,MAAA,CAAAnC,OAAA;YACA;UACA;YACA,IAAA0C,yBAAA,EAAAP,MAAA,CAAAxC,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACA8B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA7C,IAAA;cACA6C,MAAA,CAAAnC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAnB,GAAA;MAAA,IAAAoB,MAAA;MACA,IAAAC,aAAA,GAAArB,GAAA,CAAAf,YAAA,SAAA1B,GAAA;MACA,KAAA+D,QAAA,oBAAAD,aAAA;QAAAE,WAAA;MAAA,GAAA3C,IAAA;QACA,WAAA4C,yBAAA,EAAAH,aAAA;MACA,GAAAzC,IAAA;QACAwC,MAAA,CAAA5C,OAAA;QACA4C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACAC,kBAAA,WAAAA,mBAAA1B,GAAA;MAAA,IAAA2B,MAAA;MACA,IAAAC,IAAA,GAAA5B,GAAA,CAAA6B,MAAA;MACA,KAAAP,QAAA,UAAAM,IAAA,UAAA5B,GAAA,CAAAZ,mBAAA;QAAAmC,WAAA;MAAA,GAAA3C,IAAA;QACA,WAAAkD,wBAAA,EAAA9B,GAAA,CAAAf,YAAA,EAAAe,GAAA,CAAA6B,MAAA;MACA,GAAAjD,IAAA;QACA+C,MAAA,CAAAX,MAAA,CAAAC,UAAA,CAAAW,IAAA;MACA,GAAAH,KAAA;QACAzB,GAAA,CAAA6B,MAAA,GAAA7B,GAAA,CAAA6B,MAAA;MACA;IACA;IACA,aACAE,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnE,WAAA,WAAAoE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAApE,IAAA,CAAA+B,QAAA,GAAAqC,GAAA;IACA;IACAC,eAAA,WAAAA,gBAAAD,GAAA;MACA,SAAApE,IAAA,CAAAqC,YAAA,CAAAiC,QAAA,CAAAF,GAAA,CAAAA,GAAA,CAAAzC,MAAA;QACA,KAAA4C,QAAA,CAAAC,OAAA;QACA,KAAAxE,IAAA,CAAAmC,YAAA,GAAAiC,GAAA,CAAAK,MAAA,WAAAC,CAAA;UACA,OAAAA,CAAA,IAAAN,GAAA,CAAAA,GAAA,CAAAzC,MAAA;QACA;MACA;QACA,KAAA3B,IAAA,CAAAmC,YAAA,GAAAiC,GAAA;MACA;IACA;IACAO,eAAA,WAAAA,gBAAAP,GAAA;MACA,KAAApE,IAAA,CAAAqC,YAAA,GAAA+B,GAAA;IACA;EACA;AACA;AAAAQ,OAAA,CAAAb,OAAA,GAAAc,QAAA"}]}