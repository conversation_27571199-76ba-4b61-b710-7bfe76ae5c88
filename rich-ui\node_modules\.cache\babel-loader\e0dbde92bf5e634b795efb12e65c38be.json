{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\quotationstrategy.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\quotationstrategy.js", "mtime": 1718100178751}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQuotationstrategy", "query", "request", "url", "method", "params", "getQuotationstrategy", "strategyCode", "addQuotationstrategy", "data", "updateQuotationstrategy", "delQuotationstrategy", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/quotationstrategy.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询【请填写功能名称】列表\r\nexport function listQuotationstrategy(query) {\r\n  return request({\r\n    url: '/system/quotationstrategy/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询【请填写功能名称】详细\r\nexport function getQuotationstrategy(strategyCode) {\r\n  return request({\r\n    url: '/system/quotationstrategy/' + strategyCode,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增【请填写功能名称】\r\nexport function addQuotationstrategy(data) {\r\n  return request({\r\n    url: '/system/quotationstrategy',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改【请填写功能名称】\r\nexport function updateQuotationstrategy(data) {\r\n  return request({\r\n    url: '/system/quotationstrategy',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除【请填写功能名称】\r\nexport function delQuotationstrategy(strategyCode) {\r\n  return request({\r\n    url: '/system/quotationstrategy/' + strategyCode,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(strategyCode, status) {\r\n  const data = {\r\n    strategyCode,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/quotationstrategy/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,oBAAoBA,CAACC,YAAY,EAAE;EACjD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGI,YAAY;IAChDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,uBAAuBA,CAACD,IAAI,EAAE;EAC5C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,oBAAoBA,CAACJ,YAAY,EAAE;EACjD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B,GAAGI,YAAY;IAChDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,YAAY,EAAEM,MAAM,EAAE;EACjD,IAAMJ,IAAI,GAAG;IACXF,YAAY,EAAZA,YAAY;IACZM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}