{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue?vue&type=style&index=0&id=7eaaafac&scoped=true&lang=css&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue", "mtime": 1754876882572}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi5kYXRhLWFnZ3JlZ2F0b3Igew0KICBwYWRkaW5nOiAyMHB4Ow0KfQ0KDQouY29uZmlnLWNhcmQsIC5yZXN1bHQtY2FyZCB7DQogIGhlaWdodDogMTAwJTsNCiAgb3ZlcmZsb3c6IGF1dG87DQp9DQoNCi5yZXN1bHQtY2FyZCB7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5oZWFkZXItd2l0aC1vcGVyYXRpb25zIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KfQ0KDQoub3BlcmF0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGdhcDogMTBweDsNCn0NCg0KLmZpZWxkLWNvbmZpZy10YWJsZSB7DQogIGJvcmRlcjogMXB4IHNvbGlkICNFQkVFRjU7DQogIG1hcmdpbi1ib3R0b206IDEwcHg7DQp9DQoNCi50YWJsZS1oZWFkZXIsDQoudGFibGUtcm93IHsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgcGFkZGluZzogOHB4Ow0KICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI0VCRUVGNTsNCn0NCg0KLnRhYmxlLWhlYWRlciB7DQogIGJhY2tncm91bmQtY29sb3I6ICNGNUY3RkE7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KfQ0KDQouY29sIHsNCiAgZmxleDogMTsNCiAgcGFkZGluZzogMCA1cHg7DQogIG1pbi13aWR0aDogMTIwcHg7DQp9DQoNCi5jb2w6Zmlyc3QtY2hpbGQgew0KICBmbGV4OiAwIDAgNjBweDsNCiAgbWluLXdpZHRoOiA2MHB4Ow0KfQ0KDQouY29sLW9wZXJhdGlvbiB7DQogIGZsZXg6IDAgMCAxMjBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KfQ0KDQouZWwtc2VsZWN0IHsNCiAgd2lkdGg6IDEwMCU7DQp9DQoNCi5lbC1pbnB1dC1udW1iZXIgew0KICB3aWR0aDogMTAwJTsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi5CA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/DataAggregator", "sourcesContent": ["<template>\r\n  <div class=\"data-aggregator\">\r\n    <el-row :gutter=\"20\">\r\n      <!-- 配置区域 - 左侧 -->\r\n      <el-col :span=\"showResult ? 10 : 10\">\r\n        <el-card class=\"config-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总配置</span>\r\n            </div>\r\n          </template>\r\n          <el-form class=\"edit\" label-width=\"80px\">\r\n            <!-- 速查名称 -->\r\n            <el-form-item label=\"速查名称\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"18\">\r\n                  <el-input v-model=\"config.name\" placeholder=\"请输入速查名称\"/>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-button size=\"small\" type=\"text\" @click=\"saveConfig\">[↗]</el-button>\r\n                  <el-button size=\"small\" type=\"text\" @click=\"loadConfigs\">[...]</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组依据 -->\r\n            <el-form-item label=\"分组依据\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.primaryField\" clearable filterable placeholder=\"操作单号\">\r\n                    <el-option\r\n                      v-for=\"field in availableFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.matchOptions.exact\">精确匹配</el-checkbox>\r\n                  <el-checkbox v-model=\"config.matchOptions.caseSensitive\">区分大小写</el-checkbox>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组日期 -->\r\n            <el-form-item label=\"分组日期\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.dateField\" clearable filterable placeholder=\"分组日期\">\r\n                    <el-option\r\n                      v-for=\"field in dateFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.dateOptions.convertToNumber\">转换为数字</el-checkbox>\r\n                  <el-radio-group v-model=\"config.dateOptions.formatType\" style=\"display: flex;line-height: 26px\">\r\n                    <el-radio label=\"year\">按年</el-radio>\r\n                    <el-radio label=\"month\">按月</el-radio>\r\n                    <el-radio label=\"day\">按天</el-radio>\r\n                  </el-radio-group>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 显示方式 -->\r\n            <el-form-item label=\"显示方式\">\r\n              <el-checkbox v-model=\"config.showDetails\" style=\"padding-left: 5px;\">含明细</el-checkbox>\r\n              <el-switch\r\n                v-model=\"config.splitByCurrency\"\r\n                active-text=\"区分币种\">\r\n              </el-switch>\r\n            </el-form-item>\r\n\r\n            <!-- 动态字段配置 -->\r\n            <el-table\r\n              :data=\"config.fields\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"60\"\r\n              />\r\n\r\n              <el-table-column label=\"表头名称\" min-width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.fieldKey\"\r\n                    filterable\r\n                    placeholder=\"选择字段\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleFieldSelect(scope.$index)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(config, key) in fieldLabelMap\"\r\n                      :key=\"key\"\r\n                      :label=\"config.name\"\r\n                      :value=\"key\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"排序\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.sort\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"∧\" value=\"asc\"/>\r\n                    <el-option label=\"∨ \" value=\"desc\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"汇总方式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.aggregation\"\r\n                    :disabled=\"!isAggregatable(scope.row.fieldKey)\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"求和\" value=\"sum\"/>\r\n                    <el-option label=\"平均值\" value=\"avg\"/>\r\n                    <el-option label=\"最大值\" value=\"max\"/>\r\n                    <el-option label=\"最小值\" value=\"min\"/>\r\n                    <el-option label=\"方差\" value=\"variance\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"显示格式\" width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.format\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'date'\">\r\n                      <el-option label=\"YYYYMM\" value=\"YYYYMM\"/>\r\n                      <el-option label=\"MM-DD\" value=\"MM-DD\"/>\r\n                      <el-option label=\"YYYY-MM-DD\" value=\"YYYY-MM-DD\"/>\r\n                    </template>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'number'\">\r\n                      <el-option label=\"0.00\" value=\"decimal\"/>\r\n                      <el-option label=\"0.00%\" value=\"percent\"/>\r\n                      <el-option label=\"¥0.00\" value=\"currency\"/>\r\n                      <el-option label=\"$0.00\" value=\"usd\"/>\r\n                    </template>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n\r\n              <el-table-column align=\"center\" label=\"操作\" width=\"120\">\r\n                <template #default=\"scope\">\r\n                  <el-button-group>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === 0\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'up')\"\r\n                    >[∧]\r\n                    </el-button>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === config.fields.length - 1\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'down')\"\r\n                    >[∨]\r\n                    </el-button>\r\n                    <el-button\r\n                      icon=\"el-icon-delete\"\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"removeField(scope.$index)\"\r\n                    >\r\n                    </el-button>\r\n                  </el-button-group>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div style=\"margin-top: 10px;\">\r\n              <el-button plain type=\"text\" @click=\"addField\">[ + ]</el-button>\r\n            </div>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleAggregate\">分类汇总</el-button>\r\n              <el-button @click=\"resetConfig\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 结果展示 - 右侧 -->\r\n      <el-col v-if=\"showResult\" :span=\"14\">\r\n        <el-card class=\"result-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总结果</span>\r\n              <div class=\"operations\">\r\n                <el-switch\r\n                  v-model=\"isLandscape\"\r\n                  active-text=\"横向\"\r\n                  inactive-text=\"纵向\"\r\n                  style=\"margin-right: 15px\"\r\n                />\r\n                <el-button size=\"small\" @click=\"printTable\">打印</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"exportToPDF\">导出PDF</el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <el-table\r\n            ref=\"resultTable\"\r\n            v-loading=\"loading\"\r\n            :data=\"processedData\"\r\n            border\r\n            :summary-method=\"getSummary\"\r\n            show-summary\r\n            style=\"width: 100%\"\r\n          >\r\n            <!-- 分组字段列 -->\r\n            <el-table-column\r\n              :align=\"config.primaryField ? fieldLabelMap[config.primaryField].align : 'left'\"\r\n              :label=\"groupFieldName\"\r\n              :width=\"config.primaryField ? fieldLabelMap[config.primaryField].width : ''\"\r\n            >\r\n              <template #default=\"scope\">\r\n                {{ formatGroupKey(scope.row.groupKey) }}\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <!-- 动态字段列 -->\r\n            <template v-for=\"field in config.fields\">\r\n              <el-table-column\r\n                v-if=\"field.fieldKey\"\r\n                :key=\"field.fieldKey\"\r\n                :align=\"fieldLabelMap[field.fieldKey].align\"\r\n                :label=\"getResultLabel(field)\"\r\n                :width=\"fieldLabelMap[field.fieldKey].width\"\r\n              >\r\n                <template #default=\"scope\">\r\n                  {{ formatCellValue(scope.row[getResultProp(field)], field) }}\r\n                </template>\r\n              </el-table-column>\r\n            </template>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 加载配置对话框 -->\r\n    <el-dialog :visible.sync=\"configDialogVisible\" append-to-body title=\"加载配置\" width=\"500px\">\r\n      <el-table\r\n        v-loading=\"configLoading\"\r\n        :data=\"savedConfigs\"\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleConfigSelect\"\r\n      >\r\n        <el-table-column label=\"配置名称\" prop=\"name\"/>\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\"/>\r\n        <el-table-column width=\"120\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" @click.stop=\"deleteConfig(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from \"@/api/system/aggregator\"\r\nimport html2pdf from \"html2pdf.js\"\r\n\r\nexport default {\r\n  name: \"DataAggregator\",\r\n  props: {\r\n    dataSource: {\r\n      type: Array,\r\n      required: true\r\n    },\r\n    fieldLabelMap: {\r\n      type: Object,\r\n      required: true,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      configName: \"\",\r\n      config: {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        splitByCurrency: false,\r\n      },\r\n      dateOptions: [\r\n        {label: \"按年\", value: \"year\"},\r\n        {label: \"按月\", value: \"month\"},\r\n        {label: \"按周\", value: \"week\"},\r\n        {label: \"按日\", value: \"day\"},\r\n        {label: \"按时\", value: \"hour\"},\r\n        {label: \"按分\", value: \"minute\"}\r\n      ],\r\n      aggregationOptions: [\r\n        {label: \"计数\", value: \"count\"},\r\n        {label: \"求和\", value: \"sum\"},\r\n        {label: \"平均值\", value: \"avg\"},\r\n        {label: \"方差\", value: \"variance\"},\r\n        {label: \"最大值\", value: \"max\"},\r\n        {label: \"最小值\", value: \"min\"}\r\n      ],\r\n      loading: false,\r\n      configDialogVisible: false,\r\n      savedConfigs: [],\r\n      configLoading: false,\r\n      isLandscape: false,\r\n      showResult: false,\r\n      processedData: []\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用字段列表\r\n    availableFields() {\r\n      if (this.dataSource.length === 0) return []\r\n      // 只返回在 fieldLabelMap 中定义的字段\r\n      return Object.keys(this.dataSource[0]).filter(field => field in this.fieldLabelMap)\r\n    },\r\n\r\n    // 数值型字段列表\r\n    numericFields() {\r\n      // 过滤出数值类型的字段，同时确保它们在 fieldLabelMap 中存在\r\n      return this.availableFields.filter(field => {\r\n        return typeof this.dataSource[0][field] === \"number\"\r\n      })\r\n    },\r\n\r\n    // 当前字段类型\r\n    currentFieldType() {\r\n      if (!this.config.primaryField || !this.dataSource.length) return null\r\n      const sampleValue = this.dataSource[0][this.config.primaryField]\r\n      if (moment(sampleValue, moment.ISO_8601, true).isValid()) return \"date\"\r\n      return typeof sampleValue\r\n    },\r\n\r\n    // 分组字段名称\r\n    groupFieldName() {\r\n      if (this.config.primaryField && this.config.dateField) {\r\n        return `${this.getFieldLabel(this.config.dateField)}+${this.getFieldLabel(this.config.primaryField)}`\r\n      }\r\n      return this.getFieldLabel(this.config.primaryField)\r\n    },\r\n\r\n    dateFields() {\r\n      return this.availableFields.filter(field => {\r\n        // 首先检查 fieldLabelMap 中的 display 属性\r\n        if (this.fieldLabelMap[field] && this.fieldLabelMap[field].display === \"date\") {\r\n          return true\r\n        }\r\n\r\n        /* // 如果没有明确标记为日期，则尝试检查值是否为日期格式\r\n        const value = this.dataSource[0]?.[field];\r\n        return value && moment(value, moment.ISO_8601, true).isValid(); */\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    // ... existing code ...\r\n\r\n    /**\r\n     * 计算表格合计行\r\n     * @param {Object} param0 - 包含列信息和数据的对象\r\n     * @returns {Array} 合计行数据\r\n     */\r\n    getSummary({columns, data}) {\r\n      const sums = []\r\n\r\n      columns.forEach((column, index) => {\r\n        // 第一列显示\"合计\"文本\r\n        if (index === 0) {\r\n          sums[index] = \"合计\"\r\n          return\r\n        }\r\n\r\n        // 获取当前列对应的字段配置\r\n        const field = this.config.fields[index - 1]\r\n        if (!field || !field.fieldKey) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 检查字段是否配置了汇总方式\r\n        if (!field.aggregation || field.aggregation === \"none\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取字段配置\r\n        const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n        if (!fieldConfig || fieldConfig.display !== \"number\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取列数据并转换为数字\r\n        const values = data.map(item => {\r\n          const prop = this.getResultProp(field)\r\n          return Number(item[prop])\r\n        }).filter(val => !isNaN(val))\r\n\r\n        if (values.length === 0) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 根据汇总方式计算结果\r\n        let sum = 0\r\n        switch (field.aggregation) {\r\n          case \"sum\":\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n            break\r\n          case \"avg\":\r\n            sum = values.reduce((a, b) => a + b, 0) / values.length\r\n            break\r\n          case \"max\":\r\n            sum = Math.max(...values)\r\n            break\r\n          case \"min\":\r\n            sum = Math.min(...values)\r\n            break\r\n          case \"variance\":\r\n            const mean = values.reduce((a, b) => a + b, 0) / values.length\r\n            sum = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length\r\n            break\r\n          default:\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n        }\r\n\r\n        // 根据字段格式化设置格式化结果\r\n        if (field.format === \"decimal\") {\r\n          sums[index] = sum.toFixed(2)\r\n        } else if (field.format === \"percent\") {\r\n          sums[index] = (sum * 100).toFixed(2) + \"%\"\r\n        } else if (field.format === \"currency\") {\r\n          sums[index] = \"¥\" + sum.toFixed(2)\r\n        } else if (field.format === \"usd\") {\r\n          sums[index] = \"$\" + sum.toFixed(2)\r\n        } else {\r\n          sums[index] = sum\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffShortName + staff.staffFamilyEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n    /**\r\n     * 获取字段标签\r\n     * @param {string} field - 字段标识\r\n     * @returns {string} 字段标签\r\n     */\r\n    getFieldLabel(field) {\r\n      return this.fieldLabelMap[field]?.name || field\r\n    },\r\n\r\n    /**\r\n     * 分组数据\r\n     * @returns {Object} 分组后的数据\r\n     */\r\n    groupData() {\r\n      const groups = {}\r\n\r\n      this.dataSource.forEach(item => {\r\n// 如果设置了分组日期，但记录中该字段为空，则跳过该记录\r\n        if (this.config.dateField && !item[this.config.dateField]) {\r\n          return\r\n        }\r\n\r\n        // 初始化分组键为主分组字段的值\r\n        let primaryKeyValue = item[this.config.primaryField]\r\n\r\n        // 处理文本匹配\r\n        if (typeof primaryKeyValue === \"string\") {\r\n          if (!this.config.matchOptions.caseSensitive) {\r\n            primaryKeyValue = primaryKeyValue.toLowerCase()\r\n          }\r\n          if (this.config.matchOptions.exact) {\r\n            primaryKeyValue = primaryKeyValue.trim()\r\n          }\r\n        }\r\n\r\n        // 最终的分组键\r\n        let groupKey = primaryKeyValue\r\n\r\n        // 如果同时设置了日期字段，则组合两个字段作为分组键\r\n        if (this.config.dateField && item[this.config.dateField]) {\r\n          const date = moment(item[this.config.dateField])\r\n          let dateValue\r\n\r\n          // 根据日期格式化选项处理日期\r\n          if (this.config.dateOptions.formatType) {\r\n            dateValue = date.format(this.getDateFormat())\r\n          } else if (this.config.dateOptions.convertToNumber) {\r\n            dateValue = date.valueOf()\r\n          } else {\r\n            dateValue = date.format(\"YYYY-MM-DD\")\r\n          }\r\n\r\n          // 调整为日期作为主要分组键，主分组字段作为次要分组键\r\n          groupKey = {\r\n            primary: primaryKeyValue,\r\n            date: dateValue,\r\n            // 用于Map键的字符串表示，将日期放在前面\r\n            toString: function () {\r\n              return `${this.date}_${this.primary}`\r\n            }\r\n          }\r\n        }\r\n\r\n        // 创建分组或添加到现有分组\r\n        const key = groupKey.toString ? groupKey.toString() : groupKey\r\n        if (!groups[key]) {\r\n          groups[key] = {\r\n            items: [],\r\n            groupKey: groupKey\r\n          }\r\n        }\r\n        groups[key].items.push(item)\r\n      })\r\n\r\n      return groups\r\n    },\r\n\r\n    /**\r\n     * 计算汇总值\r\n     * @param {Object} groups - 分组后的数据\r\n     * @returns {Array} 汇总结果\r\n     */\r\n    calculateAggregations(groups) {\r\n      return Object.values(groups).map(group => {\r\n        // 确保 group 是正确的结构\r\n        const items = group.items || []\r\n        const groupKey = group.groupKey\r\n\r\n        const result = {groupKey}\r\n\r\n        this.config.fields.forEach(field => {\r\n          if (!field.fieldKey) return\r\n\r\n          const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n          if (!fieldConfig) return\r\n\r\n          // 获取原始值\r\n          const values = items.map(item => {\r\n            return item[field.fieldKey]\r\n          })\r\n          const prop = this.getResultProp(field)\r\n\r\n          // 对于自定义 display 方法的字段，我们需要特殊处理\r\n          const isCustomDisplay = fieldConfig.display && typeof this[fieldConfig.display] === \"function\"\r\n\r\n          if (isCustomDisplay) {\r\n            // 对于自定义方法，我们只取第一个值，不进行汇总\r\n            result[prop] = values[0]\r\n            return\r\n          }\r\n\r\n          switch (fieldConfig.display) {\r\n            case \"number\":\r\n              // 过滤并转换为数字\r\n              const numericValues = values\r\n                .filter(v => v != null)\r\n                .map(v => Number(v))\r\n                .filter(v => !isNaN(v))\r\n\r\n              if (numericValues.length === 0) {\r\n                result[prop] = null\r\n                return\r\n              }\r\n\r\n              switch (field.aggregation) {\r\n                case \"sum\":\r\n                  result[prop] = numericValues.reduce((sum, val) => {\r\n                    return Number((sum + val).toFixed(2))\r\n                  }, 0)\r\n                  break\r\n                case \"avg\":\r\n                  result[prop] = Number((numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length).toFixed(2))\r\n                  break\r\n                case \"max\":\r\n                  result[prop] = Math.max(...numericValues)\r\n                  break\r\n                case \"min\":\r\n                  result[prop] = Math.min(...numericValues)\r\n                  break\r\n                case \"variance\":\r\n                  if (numericValues.length > 0) {\r\n                    const mean = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length\r\n                    result[prop] = Number((numericValues.reduce((sum, val) =>\r\n                      sum + Math.pow(val - mean, 2), 0) / numericValues.length).toFixed(2))\r\n                  } else {\r\n                    result[prop] = 0\r\n                  }\r\n                  break\r\n                case \"none\":\r\n                default:\r\n                  result[prop] = values[0]\r\n              }\r\n              break\r\n            case \"date\":\r\n            case \"text\":\r\n            case \"boolean\":\r\n            default:\r\n              result[prop] = values[0]\r\n          }\r\n        })\r\n\r\n        return result\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 处理分类汇总按钮点击事件\r\n     * 验证配置并执行数据汇总\r\n     */\r\n    handleAggregate() {\r\n      if (!this.config.primaryField) {\r\n        this.$message.warning(\"请选择分组依据字段\")\r\n        return\r\n      }\r\n\r\n      if (!this.config.fields.length) {\r\n        this.$message.warning(\"请添加要汇总的字段\")\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loading = true\r\n        // 处理数据\r\n        const groups = this.groupData()\r\n        this.processedData = this.calculateAggregations(groups)\r\n\r\n        // 应用排序\r\n        this.applySorting()\r\n\r\n        this.showResult = true\r\n      } catch (error) {\r\n        this.$message.error(\"汇总处理失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 应用排序规则到处理后的数据\r\n     */\r\n    applySorting() {\r\n      // 查找第一个设置了排序的字段\r\n      const sortField = this.config.fields.find(field => field.sort !== \"none\")\r\n\r\n      if (!sortField) return // 如果没有设置排序，直接返回\r\n\r\n      const prop = this.getResultProp(sortField)\r\n      const isAsc = sortField.sort === \"asc\"\r\n\r\n      // 根据字段类型和排序方向进行排序\r\n      const fieldConfig = this.fieldLabelMap[sortField.fieldKey]\r\n      if (!fieldConfig) return\r\n\r\n      this.processedData.sort((a, b) => {\r\n        let valueA = a[prop]\r\n        let valueB = b[prop]\r\n\r\n        // 根据字段类型进行比较\r\n        switch (fieldConfig.display) {\r\n          case \"number\":\r\n            valueA = Number(valueA) || 0\r\n            valueB = Number(valueB) || 0\r\n            break\r\n          case \"date\":\r\n            valueA = valueA ? new Date(valueA).getTime() : 0\r\n            valueB = valueB ? new Date(valueB).getTime() : 0\r\n            break\r\n          case \"boolean\":\r\n            valueA = valueA ? 1 : 0\r\n            valueB = valueB ? 1 : 0\r\n            break\r\n          default:\r\n            valueA = String(valueA || \"\")\r\n            valueB = String(valueB || \"\")\r\n        }\r\n\r\n        // 根据排序方向返回比较结果\r\n        if (isAsc) {\r\n          return valueA > valueB ? 1 : valueA < valueB ? -1 : 0\r\n        } else {\r\n          return valueA < valueB ? 1 : valueA > valueB ? -1 : 0\r\n        }\r\n      })\r\n    },\r\n\r\n    handleFieldChange() {\r\n      // 字段变化时重置相关配置\r\n      this.config.selectedFields = []\r\n      this.config.displayFields = []\r\n    },\r\n\r\n    async saveConfig() {\r\n      try {\r\n        // 验证配置名称\r\n        if (!this.config.name) {\r\n          this.$message.warning(\"请输入速查名称\")\r\n          return\r\n        }\r\n\r\n        // 验证必要的配置项\r\n        if (!this.config.primaryField) {\r\n          this.$message.warning(\"请选择分组依据字段\")\r\n          return\r\n        }\r\n\r\n        if (!this.config.fields.length) {\r\n          this.$message.warning(\"请添加至少一个字段\")\r\n          return\r\n        }\r\n\r\n        // 验证字段配置是否完整\r\n        const incompleteField = this.config.fields.find(field => !field.fieldKey)\r\n        if (incompleteField) {\r\n          this.$message.warning(\"请完成所有字段的配置\")\r\n          return\r\n        }\r\n\r\n        // 构造符合 AggregatorConfigDTO 的数据结构\r\n        const configToSave = {\r\n          name: this.config.name,\r\n          type: 'Aggregator',\r\n          config: {\r\n            primaryField: this.config.primaryField,\r\n            matchOptions: this.config.matchOptions,\r\n            dateField: this.config.dateField,\r\n            dateOptions: this.config.dateOptions,\r\n            showDetails: this.config.showDetails,\r\n            fields: this.config.fields\r\n          }\r\n        }\r\n\r\n        // 发送请求\r\n        await saveAggregatorConfig(configToSave)\r\n\r\n        this.$message.success(\"配置保存成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"保存配置失败：\" + (err.message || \"未知错误\"))\r\n        }\r\n      }\r\n    },\r\n\r\n    async loadConfigs() {\r\n      this.configLoading = true\r\n      this.configDialogVisible = true\r\n      try {\r\n        let result = await loadAggregatorConfigs({configType: 'Aggregator'})\r\n        const configs = result.rows\r\n\r\n        // 验证返回的数据格式\r\n        if (!Array.isArray(configs)) {\r\n          throw new Error(\"返回数据格式错误\")\r\n        }\r\n\r\n        // 保留原始配置数据，只在必要时提供默认值\r\n        this.savedConfigs = configs.map(config => ({\r\n          id: config.id,\r\n          name: config.name,\r\n          createTime: config.createTime,\r\n          config: config.config || {\r\n            primaryField: \"\",\r\n            secondaryField: \"\",\r\n            textMatchMode: \"exact\",\r\n            caseSensitive: false,\r\n            dateGranularity: \"day\",\r\n            aggregationMethods: [\"count\", \"sum\"],\r\n            showDetails: false,\r\n            selectedFields: [],\r\n            displayFields: []\r\n          }\r\n        }))\r\n\r\n        this.configDialogVisible = true\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\r\n          err.response?.data?.message ||\r\n          err.message ||\r\n          \"加载配置列表失败，请稍后重试\"\r\n        )\r\n      } finally {\r\n        this.configLoading = false\r\n      }\r\n    },\r\n    async handleConfigSelect(row) {\r\n      try {\r\n        // 确保配置对象包含所有必要的字段\r\n        const defaultConfig = {\r\n          primaryField: \"\",\r\n          secondaryField: \"\",\r\n          textMatchMode: \"exact\",\r\n          caseSensitive: false,\r\n          dateGranularity: \"day\",\r\n          aggregationMethods: [\"count\", \"sum\"],\r\n          showDetails: false,\r\n          selectedFields: [],\r\n          displayFields: []\r\n        }\r\n\r\n        // 深拷贝配置对象，避免引用问题\r\n        this.config = {\r\n          ...defaultConfig,\r\n          ...JSON.parse(row.config),\r\n          name: row.name\r\n        }\r\n\r\n        this.configDialogVisible = false\r\n        this.$message.success(\"配置加载成功\")\r\n\r\n        // 触发表单重新渲染\r\n        this.$nextTick(() => {\r\n          // 如果需要，可以在这里添加额外的处理逻辑\r\n          console.log(\"配置已加载:\", this.config)\r\n        })\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\"加载配置失败：\" + err.message)\r\n      }\r\n    },\r\n    async deleteConfig(row) {\r\n      try {\r\n        await this.$confirm(\"确认删除该配置？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n\r\n        await deleteAggregatorConfig(row.id)\r\n        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)\r\n        this.$message.success(\"配置删除成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"删除配置失败：\" + err.message)\r\n        }\r\n      }\r\n    },\r\n\r\n    printTable() {\r\n      const printWindow = window.open(\"\", \"_blank\")\r\n      const table = this.$refs.resultTable.$el.cloneNode(true)\r\n      const title = \"\"\r\n      const date = new Date().toLocaleDateString()\r\n\r\n      // 公司标志和标题的HTML模板\r\n      const headerTemplate = `\r\n        <div class=\"company-header\">\r\n          <div class=\"company-logo\">\r\n            <img src=\"/logo.png\" alt=\"Rich Shipping Logo\" />\r\n            <div class=\"company-name\">\r\n              <div class=\"company-name-cn\">广州瑞旗国际货运代理有限公司</div>\r\n              <div class=\"company-name-en\">GUANGZHOU RICH SHIPPING INT'L CO.,LTD.</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"document-title\">\r\n            <div class=\"title-cn\">对账单汇总</div>\r\n            <div class=\"title-en\">[DEBIT NOTE]</div>\r\n          </div>\r\n        </div>\r\n      `\r\n\r\n      printWindow.document.write(`\r\n        <html lang=\"\">\r\n          <head>\r\n            <title>${title}</title>\r\n            <style>\r\n          /* 基础样式 */\r\n          body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: Arial, sans-serif;\r\n          }\r\n\r\n          /* 打印样式 - 必须放在这里才能生效 */\r\n          @media print {\r\n            @page {\r\n              size: ${this.isLandscape ? \"landscape\" : \"portrait\"};\r\n              margin: 1.5cm 1cm 1cm 1cm;\r\n            }\r\n\r\n            /* 重要：使用重复表头技术 */\r\n            thead {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 页眉作为表格的一部分，放在thead中 */\r\n            .page-header {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 内容部分 */\r\n            .page-content {\r\n              display: table-row-group;\r\n            }\r\n\r\n            /* 页脚 */\r\n            tfoot {\r\n              display: table-footer-group;\r\n            }\r\n\r\n            /* 避免元素内部分页 */\r\n            .company-header, .header-content {\r\n              page-break-inside: avoid;\r\n            }\r\n\r\n            /* 表格样式 */\r\n            table.main-table {\r\n              width: 100%;\r\n              border-collapse: collapse;\r\n              border: none;\r\n            }\r\n\r\n            /* 确保表头在每页都显示 */\r\n            table.data-table thead {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 避免行内分页 */\r\n            table.data-table tr {\r\n              page-break-inside: avoid;\r\n            }\r\n          }\r\n\r\n          /* 表格样式 */\r\n          table.data-table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 20px;\r\n          }\r\n\r\n          table.data-table th, table.data-table td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n            text-align: left;\r\n            font-size: 12px;\r\n          }\r\n\r\n          table.data-table th {\r\n            background-color: #f2f2f2;\r\n          }\r\n\r\n          /* Element UI 表格样式模拟 */\r\n          .el-table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n          }\r\n\r\n          .el-table th, .el-table td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n            text-align: left;\r\n            font-size: 12px;\r\n          }\r\n\r\n          .el-table th {\r\n            background-color: #f2f2f2;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .el-table__footer {\r\n            background-color: #f8f8f9;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .el-table__footer td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n          }\r\n\r\n          /* 公司标题和标志样式 */\r\n          .company-header {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            border-bottom: 2px solid #000;\r\n            padding-bottom: 10px;\r\n            width: 100%;\r\n          }\r\n\r\n          .company-logo {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .company-logo img {\r\n            height: 50px;\r\n            margin-right: 10px;\r\n          }\r\n\r\n          .company-name {\r\n            display: flex;\r\n            flex-direction: column;\r\n          }\r\n\r\n          .company-name-cn {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #ff0000;\r\n          }\r\n\r\n          .company-name-en {\r\n            font-size: 14px;\r\n          }\r\n\r\n          .document-title {\r\n            text-align: right;\r\n          }\r\n\r\n          .title-cn {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .title-en {\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n          }\r\n\r\n          /* 清除表格边框 */\r\n          table.main-table, table.main-table td {\r\n            border: none;\r\n          }\r\n\r\n          /* 页眉容器 */\r\n          .header-container {\r\n            width: 100%;\r\n            margin-bottom: 20px;\r\n          }\r\n\r\n          /* 日期信息 */\r\n          .date-info {\r\n            text-align: right;\r\n            margin-top: 10px;\r\n            margin-bottom: 20px;\r\n          }\r\n        </style>\r\n          </head>\r\n          <body>\r\n            <!-- 使用表格布局确保页眉在每页重复 -->\r\n            <table class=\"main-table\">\r\n              <thead class=\"page-header\">\r\n                <tr>\r\n                  <td>\r\n                    <div class=\"header-container\">\r\n                      ${headerTemplate}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </thead>\r\n              <tbody class=\"page-content\">\r\n                <tr>\r\n                  <td>\r\n                    <!-- 保留原始表格的类名并添加data-table类 -->\r\n                    ${table.outerHTML.replace('<table', '<table class=\"el-table data-table\"')}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n              <tfoot>\r\n                <tr>\r\n                  <td></td>\r\n                </tr>\r\n              </tfoot>\r\n            </table>\r\n          </body>\r\n        </html>\r\n      `)\r\n\r\n      printWindow.document.close()\r\n\r\n      setTimeout(() => {\r\n        try {\r\n          printWindow.focus();\r\n          printWindow.print();\r\n        } catch (e) {\r\n          console.error(\"打印过程中发生错误:\", e);\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    async exportToPDF() {\r\n      try {\r\n        this.loading = true\r\n        const element = this.$refs.resultTable.$el\r\n        const opt = {\r\n          margin: [0.8, 0.8, 0.8, 0.8], // 上右下左边距（英寸）\r\n          filename: \"汇总数据.pdf\",\r\n          image: {type: \"jpeg\", quality: 0.98},\r\n          html2canvas: {scale: 2},\r\n          jsPDF: {\r\n            unit: \"in\",\r\n            format: \"a3\",\r\n            orientation: this.isLandscape ? \"landscape\" : \"portrait\"\r\n          },\r\n          pagebreak: {mode: [\"avoid-all\", \"css\", \"legacy\"]}, // 添加分页控制\r\n          header: [\r\n            {text: \"汇总数据\", style: \"headerStyle\"},\r\n            {text: new Date().toLocaleDateString(), style: \"headerStyle\", alignment: \"right\"}\r\n          ],\r\n          footer: {\r\n            height: \"20px\",\r\n            contents: {\r\n              default: \"<span style=\\\"float:right\\\">{{page}}/{{pages}}</span>\" // 添加页码\r\n            }\r\n          }\r\n        }\r\n\r\n        await html2pdf().set(opt).from(element).save()\r\n        this.$message.success(\"PDF导出成功\")\r\n      } catch (error) {\r\n        this.$message.error(\"PDF导出失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 添加新的字段配置行\r\n     * 初始化一个新的字段配置对象，包含默认值\r\n     */\r\n    addField() {\r\n      this.config.fields.push({\r\n        fieldKey: \"\",        // 字段标识\r\n        aggregation: \"none\", // 汇总方式\r\n        format: \"none\",      // 显示格式\r\n        sort: \"none\"         // 排序方式\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 删除指定索引的字段配置行\r\n     * @param {number} index - 要删除的字段索引\r\n     */\r\n    removeField(index) {\r\n      this.config.fields.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 移动字段配置行的位置\r\n     * @param {number} index - 当前字段的索引\r\n     * @param {string} direction - 移动方向，'up' 或 'down'\r\n     */\r\n    moveField(index, direction) {\r\n      const fields = [...this.config.fields] // 创建数组副本\r\n\r\n      if (direction === \"up\" && index > 0) {\r\n        // 向上移动，与上一个元素交换位置\r\n        [fields[index], fields[index - 1]] = [fields[index - 1], fields[index]]\r\n      } else if (direction === \"down\" && index < fields.length - 1) {\r\n        // 向下移动，与下一个元素交换位置\r\n        [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]\r\n      }\r\n\r\n      // 使用整个新数组替换，确保响应式更新\r\n      this.$set(this.config, \"fields\", fields)\r\n    },\r\n\r\n    /**\r\n     * 处理字段选择变更事件\r\n     * 根据选择的字段自动设置相关配置\r\n     * @param {number} index - 变更的字段索引\r\n     */\r\n    handleFieldSelect(index) {\r\n      const field = this.config.fields[index]\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (fieldConfig) {\r\n        // 根据字段配置设置默认值\r\n        field.format = this.getDefaultFormat(fieldConfig.display)\r\n        field.aggregation = fieldConfig.aggregated ? \"sum\" : \"none\"\r\n        field.sort = \"none\" // 默认不排序\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取字段的显示类型\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {string} 字段显示类型（text/number/date/boolean/custom）\r\n     */\r\n    getFieldDisplay(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      if (!fieldConfig) return \"text\"\r\n\r\n      // 检查是否是自定义方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        return \"custom\"\r\n      }\r\n\r\n      return fieldConfig.display || \"text\"\r\n    },\r\n\r\n    /**\r\n     * 判断字段是否可以进行汇总计算\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {boolean} 是否可汇总\r\n     */\r\n    isAggregatable(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig?.aggregated || false\r\n    },\r\n\r\n    /**\r\n     * 根据显示类型获取默认的格式化方式\r\n     * @param {string} displayType - 显示类型\r\n     * @returns {string} 默认格式\r\n     */\r\n    getDefaultFormat(displayType) {\r\n      switch (displayType) {\r\n        case \"date\":\r\n          return \"YYYY-MM-DD\"\r\n        case \"number\":\r\n          return \"decimal\"\r\n        default:\r\n          return \"none\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 重置配置到初始状态\r\n     */\r\n    resetConfig() {\r\n      this.config = {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: []\r\n      }\r\n      this.showResult = false\r\n    },\r\n\r\n    /**\r\n     * 获取结果数据的属性名\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 属性名\r\n     */\r\n    getResultProp(field) {\r\n      // 如果有汇总方式，属性名为 fieldKey_aggregation\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        return `${field.fieldKey}_${field.aggregation}`\r\n      }\r\n      return field.fieldKey\r\n    },\r\n\r\n    /**\r\n     * 获取结果表格的列标题\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 列标题\r\n     */\r\n    getResultLabel(field) {\r\n      const baseLabel = this.getFieldLabel(field.fieldKey)\r\n\r\n      // 如果有汇总方式，在标签中添加汇总方式信息\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        // 获取汇总方式的中文名称\r\n        const aggregationLabel = this.aggregationOptions.find(opt => opt.value === field.aggregation)?.label || field.aggregation\r\n        return `${baseLabel}(${aggregationLabel})`\r\n      }\r\n\r\n      return baseLabel\r\n    },\r\n\r\n    /**\r\n     * 格式化单元格的值\r\n     * @param {*} value - 原始值\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 格式化后的值\r\n     */\r\n    formatCellValue(value, field) {\r\n      if (value == null) return \"-\"\r\n\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (!fieldConfig) return value\r\n\r\n      // 处理自定义 display 方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        // 调用组件中定义的方法\r\n        return this[fieldConfig.display](value)\r\n      }\r\n\r\n      // 根据字段类型进行格式化\r\n      switch (fieldConfig.display) {\r\n        case \"number\":\r\n          const numValue = Number(value)\r\n          if (isNaN(numValue)) return \"-\"\r\n\r\n          // 如果是汇总字段，先应用汇总格式化\r\n          if (field.aggregation && field.aggregation !== \"none\") {\r\n            // 对于平均值和方差，保留更多小数位\r\n            if (field.aggregation === \"avg\" || field.aggregation === \"variance\") {\r\n              if (field.format === \"percent\") {\r\n                return (numValue * 100).toFixed(2) + \"%\"\r\n              }\r\n              return numValue.toFixed(2)\r\n            }\r\n          }\r\n\r\n          switch (field.format) {\r\n            case \"decimal\":\r\n              return numValue.toFixed(2)\r\n            case \"percent\":\r\n              return (numValue * 100).toFixed(2) + \"%\"\r\n            case \"currency\":\r\n              return \"¥\" + numValue.toFixed(2)\r\n            case \"usd\":\r\n              return \"$\" + numValue.toFixed(2)\r\n            default:\r\n              return numValue.toFixed(2)\r\n          }\r\n\r\n        case \"date\":\r\n          return moment(value).format(field.format || \"YYYY-MM-DD\")\r\n\r\n        case \"boolean\":\r\n          if (field.aggregation === \"avg\") {\r\n            return (Number(value) * 100).toFixed(2) + \"%\"\r\n          }\r\n          return value ? \"是\" : \"否\"\r\n\r\n        default:\r\n          return value\r\n      }\r\n    },\r\n\r\n    getDateFormat() {\r\n      switch (this.config.dateOptions.formatType) {\r\n        case \"year\":\r\n          return \"YYYY\"\r\n        case \"month\":\r\n          return \"YYYY-MM\"\r\n        case \"day\":\r\n        default:\r\n          return \"YYYY-MM-DD\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 格式化分组键\r\n     * @param {Object|string} groupKey - 分组键\r\n     * @returns {string} 格式化后的分组键\r\n     */\r\n    formatGroupKey(groupKey) {\r\n      if (typeof groupKey === \"object\" && groupKey !== null) {\r\n        if (groupKey.primary !== undefined && groupKey.date !== undefined) {\r\n          // 获取主分组字段的配置\r\n          const primaryFieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n          let primaryValue = groupKey.primary\r\n\r\n          // 如果主分组字段有自定义 display 方法，应用它\r\n          if (primaryFieldConfig && primaryFieldConfig.display &&\r\n            typeof this[primaryFieldConfig.display] === \"function\") {\r\n            primaryValue = this[primaryFieldConfig.display](primaryValue)\r\n          }\r\n\r\n          // 日期值在前，主值在后，不添加分隔符\r\n          return `${groupKey.date}${primaryValue}`\r\n        }\r\n      }\r\n\r\n      // 如果是简单值，检查是否需要应用自定义 display 方法\r\n      if (this.config.primaryField) {\r\n        const fieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n        if (fieldConfig && fieldConfig.display &&\r\n          typeof this[fieldConfig.display] === \"function\") {\r\n          return this[fieldConfig.display](groupKey)\r\n        }\r\n      }\r\n\r\n      return String(groupKey || \"\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.data-aggregator {\r\n  padding: 20px;\r\n}\r\n\r\n.config-card, .result-card {\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n.result-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-with-operations {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.operations {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.field-config-table {\r\n  border: 1px solid #EBEEF5;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.table-header,\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px;\r\n  border-bottom: 1px solid #EBEEF5;\r\n}\r\n\r\n.table-header {\r\n  background-color: #F5F7FA;\r\n  font-weight: bold;\r\n}\r\n\r\n.col {\r\n  flex: 1;\r\n  padding: 0 5px;\r\n  min-width: 120px;\r\n}\r\n\r\n.col:first-child {\r\n  flex: 0 0 60px;\r\n  min-width: 60px;\r\n}\r\n\r\n.col-operation {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n"]}]}