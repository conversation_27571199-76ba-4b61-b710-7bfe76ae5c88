{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\staffInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\staffInfo.vue", "mtime": 1737429728534}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_extStaff", "require", "name", "dicts", "props", "data", "size", "$store", "state", "app", "single", "multiple", "openContent", "loading", "oopen", "title", "extStaffList", "form", "queryParams", "sqdCompanyId", "staffShortName", "staffLocalName", "staffEnName", "staffTelNum", "staffEmailEnterprise", "staffPhoneNum", "staffBirthday", "staff<PERSON>ender", "staffWechat", "staffQq", "staffCountry", "staffNation", "staffReligion", "locationId", "staff<PERSON><PERSON><PERSON>", "staffLanguage", "deptName", "deptPositionName", "staffJobStatus", "rules", "watch", "loadOptions", "open", "val", "$emit", "methods", "getList", "_this", "company", "companyId", "listExtStaff", "then", "response", "cancel", "reset", "staffId", "credentialTypeId", "is<PERSON><PERSON>", "resetForm", "handleAdd", "handleUpdate", "row", "_this2", "ids", "getExtStaff", "$refs", "location", "remoteMethod", "locationLocalName", "submitForm", "_this3", "validate", "valid", "updateExtStaff", "$modal", "msgSuccess", "addExtStaff", "handleDelete", "_this4", "staffIds", "$confirm", "customClass", "delExtStaff", "catch", "handleStatusChange", "_this5", "text", "changeStaffStatus", "getLocationId", "handleClose", "exports", "default", "_default"], "sources": ["src/views/system/company/staffInfo.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag\r\n      v-dialogDragWidth\r\n      :modal-append-to-body=\"false\"\r\n      :append-to-body=\"true\"\r\n      :visible.sync=\"openContent\"\r\n      class=\"staffInfo\"\r\n      title=\"员工信息\"\r\n      width=\"1800px\"\r\n      @close=\"handleClose\"\r\n    >\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:extstaff:add']\"\r\n            :size=\"size\"\r\n            icon=\"el-icon-plus\"\r\n            plain\r\n            type=\"primary\"\r\n            @click=\"handleAdd\"\r\n          >新增\r\n          </el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:extstaff:edit']\"\r\n            :disabled=\"single\"\r\n            :size=\"size\"\r\n            icon=\"el-icon-edit\"\r\n            plain\r\n            type=\"success\"\r\n            @click=\"handleUpdate\"\r\n          >修改\r\n          </el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:extstaff:remove']\"\r\n            :disabled=\"multiple\"\r\n            :size=\"size\"\r\n            icon=\"el-icon-delete\"\r\n            plain\r\n            type=\"danger\"\r\n            @click=\"handleDelete\"\r\n          >删除\r\n          </el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <el-table v-loading=\"loading\" :data=\"extStaffList\" border stripe>\r\n        <el-table-column key=\"staffShortName\" align=\"left\" label=\"员工\" prop=\"staffShortName\" show-tooltip-when-overflow\r\n                         width=\"180\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-row>\r\n              <el-col :span=\"5\">\r\n                <el-tag :size=\"size\" v-if=\"scope.row.isMain == 'Y'\" type=\"primary\">主</el-tag>\r\n              </el-col>\r\n              <el-col :span=\"19\">\r\n                <h6 style=\"margin: 5px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap\">\r\n                  {{ company.companyShortName != null ? company.companyShortName : '' }}\r\n                  {{ scope.row.staffLocalName != null ? scope.row.staffLocalName : '' }}\r\n                  {{ scope.row.staffEnName != null ? scope.row.staffEnName : '' }}\r\n                  {{ scope.row.staffShortName != null ? scope.row.staffShortName : '' }}\r\n                  {{ scope.row.deptName != null ? scope.row.deptName : '' }}\r\n                  {{ scope.row.deptPositionName != null ? scope.row.deptPositionName : '' }}\r\n                </h6>\r\n              </el-col>\r\n            </el-row>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"职位\" show-tooltip-when-overflow width=\"60px\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 style=\"margin: 5px;overflow:hidden;text-overflow: ellipsis;white-space: nowrap\">\r\n              {{ scope.row.deptName != null ? scope.row.deptName + '/' : '' }}{{ scope.row.deptPositionName }}\r\n            </h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"微信\" prop=\"staffWechat\" show-tooltip-when-overflow width=\"100px\"/>\r\n        <el-table-column align=\"center\" label=\"QQ\" prop=\"staffQq\" show-tooltip-when-overflow width=\"80px\"/>\r\n        <el-table-column align=\"center\" label=\"WhatsApp\" prop=\"staffWhatsapp\" show-tooltip-when-overflow width=\"80px\"/>\r\n        <el-table-column align=\"center\" label=\"其他联系人方式\" prop=\"staffOtherContact\" show-tooltip-when-overflow\r\n                         width=\"80px\"\r\n        />\r\n        <el-table-column key=\"staffTelNum\" align=\"center\" label=\"个人邮箱\" prop=\"staffTelNum\"\r\n                         show-tooltip-when-overflow width=\"150px\"\r\n        />\r\n        <el-table-column key=\"staffEmailEnterprise\" align=\"center\" label=\"企业邮箱\" prop=\"staffEmailEnterprise\"\r\n                         show-tooltip-when-overflow width=\"150px\"\r\n        />\r\n        <el-table-column key=\"staffGender\" align=\"center\" label=\"性别\" prop=\"staffGender\" width=\"38px\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_user_sex\" :value=\"scope.row.staffGender\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column key=\"staffPhoneNum\" align=\"center\" label=\"电话号码\" prop=\"staffPhoneNum\"\r\n                         show-tooltip-when-overflow width=\"100\"\r\n        />\r\n        <el-table-column key=\"staffAddress\" align=\"center\" label=\"详细住址\" prop=\"staffAddress\"\r\n                         show-tooltip-when-overflow\r\n                         width=\"100px\"\r\n        />\r\n        <el-table-column key=\"staffBirthday\" align=\"center\" label=\"生日\" prop=\"staffBirthday\" width=\"78px\"/>\r\n        <el-table-column key=\"staffLanguage\" align=\"center\" label=\"母语\" prop=\"staffLanguage\" width=\"48px\"/>\r\n        <el-table-column key=\"staffCountry\" align=\"center\" label=\"国籍\" prop=\"staffCountry\" width=\"48px\"/>\r\n        <el-table-column key=\"staffNation\" align=\"center\" label=\"民族\" prop=\"staffNation\" width=\"48px\"/>\r\n        <el-table-column key=\"staffReligion\" align=\"center\" label=\"宗教\" prop=\"staffReligion\" width=\"48px\"/>\r\n        <el-table-column key=\"remark\" align=\"center\" label=\"员工评价\" prop=\"remark\"/>\r\n        <el-table-column key=\"staffJobStatus\" align=\"center\" label=\"状态\" width=\"58px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-switch\r\n              v-model=\"scope.row.staffJobStatus\"\r\n              active-value=\"0\"\r\n              inactive-value=\"1\"\r\n              @change=\"handleStatusChange(scope.row)\"\r\n            ></el-switch>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-hasPermi=\"['system:extstaff:edit']\" :size=\"size\" icon=\"el-icon-edit\" style=\"margin: 2px\"\r\n                       type=\"success\" @click=\"handleUpdate(scope.row)\"\r\n            >修改\r\n            </el-button>\r\n            <el-button v-hasPermi=\"['system:extstaff:remove']\" :size=\"size\" icon=\"el-icon-delete\" style=\"margin: 2px\"\r\n                       type=\"danger\" @click=\"handleDelete(scope.row)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag v-dialogDragWidth :append-to-body=\"true\" :modal-append-to-body=\"false\" :title=\"title\"\r\n      :visible.sync=\"oopen\"\r\n      width=\"800px\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"88px\">\r\n        <el-form-item label=\"所属公司：\">\r\n          <h4 style=\"margin: 0;font-weight:bold;\">{{ company.companyTaxCode }}\r\n            <span style=\"font-size: large\">{{ company.companyShortName }}</span>\r\n          </h4>\r\n          {{ company.companyLocalName }}\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名称谓：\">\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffShortName\" placeholder=\"正式称谓\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffLocalName\" placeholder=\"中文姓名\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffEnName\" placeholder=\"英文姓名\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门职位：\">\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.deptName\" placeholder=\"部门名称，手动输入\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.deptPositionName\" placeholder=\"职位名称，手动输入\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-select v-model=\"form.isMain\" placeholder=\"是否主要联系人\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_yes_no\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                ></el-option>\r\n              </el-select>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form-item>\r\n        <el-form-item label=\"联系方式：\">\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffPhoneNum\" placeholder=\"手机号码\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffWechat\" placeholder=\"微信\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffQq\" placeholder=\"QQ\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffWhatsapp\" placeholder=\"Whatsapp\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffOtherContact\" placeholder=\"其他联系方式\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffTelNum\" placeholder=\"固定电话\"/>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-input v-model=\"form.staffEmailEnterprise\" placeholder=\"企业邮箱\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form-item>\r\n        <el-form-item label=\"个人信息：\">\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-select v-model=\"form.staffGender\" style=\"width: 100%\">\r\n                <el-option label=\"0\">男</el-option>\r\n                <el-option label=\"1\">女</el-option>\r\n              </el-select>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-date-picker v-model=\"form.staffBirthday\"\r\n                              clearable\r\n                              placeholder=\"生日\"\r\n                              type=\"date\"\r\n                              style=\"width: 100%\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n              >\r\n              </el-date-picker>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffLanguage\" placeholder=\"擅长语种\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffCountry\" placeholder=\"国籍\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffNation\" placeholder=\"民族\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-input v-model=\"form.staffReligion\" placeholder=\"宗教信仰\"/>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <location-select ref=\"location\" :pass=\"form.locationId\" :type=\"'location'\" @return=\"getLocationId\"/>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-input v-model=\"form.staffAddress\" placeholder=\"详细住址\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注信息：\" prop=\"remark\">\r\n          <el-input\r\n            v-model=\"form.remark\"\r\n            maxlength=\"200\"\r\n            placeholder=\"内容\"\r\n            show-word-limit\r\n            type=\"textarea\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"员工状态：\" prop=\"staffJobStatus\">\r\n          <el-select v-model=\"form.staffJobStatus\">\r\n            <el-option v-for=\"dict in dict.type.sys_normal_disable\"\r\n                       :key=\"dict.value\"\r\n                       :label=\"dict.label\"\r\n                       :value=\"dict.value\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addExtStaff,\r\n  changeStaffStatus,\r\n  delExtStaff,\r\n  getExtStaff,\r\n  listExtStaff,\r\n  updateExtStaff\r\n} from '@/api/system/extStaff'\r\n\r\nexport default {\r\n  name: 'staffInfo',\r\n  dicts: ['sys_user_sex', 'sys_normal_disable', 'sys_yes_no'],\r\n  props: ['loadOptions', 'open', 'company'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini',\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      openContent: false,\r\n      loading: true,\r\n      oopen: false,\r\n      title: null,\r\n      extStaffList: [],\r\n      form: {},\r\n      queryParams: {\r\n        sqdCompanyId: null,\r\n        staffShortName: null,\r\n        staffLocalName: null,\r\n        staffEnName: null,\r\n        staffTelNum: null,\r\n        staffEmailEnterprise: null,\r\n        staffPhoneNum: null,\r\n        staffBirthday: null,\r\n        staffGender: null,\r\n        staffWechat: null,\r\n        staffQq: null,\r\n        staffCountry: null,\r\n        staffNation: null,\r\n        staffReligion: null,\r\n        locationId: null,\r\n        staffAddress: null,\r\n        staffLanguage: null,\r\n        deptName: null,\r\n        deptPositionName: null,\r\n        staffJobStatus: null\r\n      },\r\n      rules: {}\r\n    }\r\n  },\r\n  watch: {\r\n    loadOptions: function () {\r\n      this.extStaffList = this.loadOptions\r\n      this.loading = false\r\n    },\r\n    open: function (val) {\r\n      this.openContent = val\r\n    },\r\n    openContent(val) {\r\n      if (val == false) {\r\n        this.$emit('openStaffs')\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询外部员工列表 */\r\n    getList() {\r\n      this.loading = true\r\n      this.queryParams.sqdCompanyId = this.company.companyId\r\n      listExtStaff(this.queryParams).then(response => {\r\n        this.extStaffList = response.data\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.oopen = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        staffId: null,\r\n        sqdCompanyId: null,\r\n        staffShortName: null,\r\n        staffLocalName: null,\r\n        staffEnName: null,\r\n        staffTelNum: null,\r\n        staffEmailEnterprise: null,\r\n        credentialTypeId: null,\r\n        staffPhoneNum: null,\r\n        staffBirthday: null,\r\n        staffGender: null,\r\n        locationId: null,\r\n        staffAddress: null,\r\n        staffLanguage: null,\r\n        deptName: null,\r\n        deptPositionName: null,\r\n        staffJobStatus: 0,\r\n        isMain: 'N'\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.oopen = true\r\n      this.title = '添加外部员工'\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const staffId = row.staffId || this.ids\r\n      getExtStaff(staffId).then(response => {\r\n        this.form = response.data\r\n        this.oopen = true\r\n        this.title = '修改外部员工'\r\n        this.$refs.location.remoteMethod(row.locationLocalName)\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          this.form.sqdCompanyId = this.company.companyId\r\n          if (this.form.staffId != null) {\r\n            updateExtStaff(this.form).then(response => {\r\n              this.$modal.msgSuccess('修改成功')\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addExtStaff(this.form).then(response => {\r\n              this.$modal.msgSuccess('新增成功')\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const staffIds = row.staffId || this.ids\r\n      this.$confirm('是否确认删除外部员工编号为\"' + staffIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delExtStaff(staffIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.staffJobStatus == '0' ? '启用' : '停用'\r\n      let name = (row.staffShortName != null ? row.staffShortName : '') + (row.staffLocalName != null ? row.staffLocalName : '') + (row.staffEnName != null ? row.staffEnName : '')\r\n      this.$confirm('确认要\"' + text + '\"\"' + name + '\"用户吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStaffStatus(row.staffId, row.staffJobStatus)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + '成功')\r\n      }).catch(function () {\r\n        row.staffJobStatus = row.staffJobStatus == '0' ? '1' : '0'\r\n      })\r\n    },\r\n    getLocationId(val) {\r\n      this.form.locationId = val\r\n    },\r\n    handleClose() {\r\n      // this.$emit('refresh')\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n"], "mappings": ";;;;;;AAgRA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eASA;EACAC,IAAA;EACAC,KAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACA;MACAI,MAAA;MACA;MACAC,QAAA;MACAC,WAAA;MACAC,OAAA;MACAC,KAAA;MACAC,KAAA;MACAC,YAAA;MACAC,IAAA;MACAC,WAAA;QACAC,YAAA;QACAC,cAAA;QACAC,cAAA;QACAC,WAAA;QACAC,WAAA;QACAC,oBAAA;QACAC,aAAA;QACAC,aAAA;QACAC,WAAA;QACAC,WAAA;QACAC,OAAA;QACAC,YAAA;QACAC,WAAA;QACAC,aAAA;QACAC,UAAA;QACAC,YAAA;QACAC,aAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,cAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAzB,YAAA,QAAAyB,WAAA;MACA,KAAA5B,OAAA;IACA;IACA6B,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAA/B,WAAA,GAAA+B,GAAA;IACA;IACA/B,WAAA,WAAAA,YAAA+B,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,KAAA;MACA;IACA;EACA;EACAC,OAAA;IACA,eACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,KAAAlC,OAAA;MACA,KAAAK,WAAA,CAAAC,YAAA,QAAA6B,OAAA,CAAAC,SAAA;MACA,IAAAC,sBAAA,OAAAhC,WAAA,EAAAiC,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAA/B,YAAA,GAAAoC,QAAA,CAAA/C,IAAA;QACA0C,KAAA,CAAAlC,OAAA;MACA;IACA;IACA;IACAwC,MAAA,WAAAA,OAAA;MACA,KAAAvC,KAAA;MACA,KAAAwC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArC,IAAA;QACAsC,OAAA;QACApC,YAAA;QACAC,cAAA;QACAC,cAAA;QACAC,WAAA;QACAC,WAAA;QACAC,oBAAA;QACAgC,gBAAA;QACA/B,aAAA;QACAC,aAAA;QACAC,WAAA;QACAM,UAAA;QACAC,YAAA;QACAC,aAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,cAAA;QACAmB,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAL,KAAA;MACA,KAAAxC,KAAA;MACA,KAAAC,KAAA;IACA;IACA,aACA6C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAR,KAAA;MACA,IAAAC,OAAA,GAAAM,GAAA,CAAAN,OAAA,SAAAQ,GAAA;MACA,IAAAC,qBAAA,EAAAT,OAAA,EAAAJ,IAAA,WAAAC,QAAA;QACAU,MAAA,CAAA7C,IAAA,GAAAmC,QAAA,CAAA/C,IAAA;QACAyD,MAAA,CAAAhD,KAAA;QACAgD,MAAA,CAAA/C,KAAA;QACA+C,MAAA,CAAAG,KAAA,CAAAC,QAAA,CAAAC,YAAA,CAAAN,GAAA,CAAAO,iBAAA;MACA;IACA;IACA,WACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAL,KAAA,SAAAM,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAArD,IAAA,CAAAE,YAAA,GAAAmD,MAAA,CAAAtB,OAAA,CAAAC,SAAA;UACA,IAAAqB,MAAA,CAAArD,IAAA,CAAAsC,OAAA;YACA,IAAAkB,wBAAA,EAAAH,MAAA,CAAArD,IAAA,EAAAkC,IAAA,WAAAC,QAAA;cACAkB,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAAxD,KAAA;cACAwD,MAAA,CAAAxB,OAAA;YACA;UACA;YACA,IAAA8B,qBAAA,EAAAN,MAAA,CAAArD,IAAA,EAAAkC,IAAA,WAAAC,QAAA;cACAkB,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAAxD,KAAA;cACAwD,MAAA,CAAAxB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+B,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,IAAAC,QAAA,GAAAlB,GAAA,CAAAN,OAAA,SAAAQ,GAAA;MACA,KAAAiB,QAAA,oBAAAD,QAAA;QAAAE,WAAA;MAAA,GAAA9B,IAAA;QACA,WAAA+B,qBAAA,EAAAH,QAAA;MACA,GAAA5B,IAAA;QACA2B,MAAA,CAAAhC,OAAA;QACAgC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,IAAA,GAAAzB,GAAA,CAAAvB,cAAA;MACA,IAAApC,IAAA,IAAA2D,GAAA,CAAAzC,cAAA,WAAAyC,GAAA,CAAAzC,cAAA,UAAAyC,GAAA,CAAAxC,cAAA,WAAAwC,GAAA,CAAAxC,cAAA,UAAAwC,GAAA,CAAAvC,WAAA,WAAAuC,GAAA,CAAAvC,WAAA;MACA,KAAA0D,QAAA,UAAAM,IAAA,UAAApF,IAAA;QAAA+E,WAAA;MAAA,GAAA9B,IAAA;QACA,WAAAoC,2BAAA,EAAA1B,GAAA,CAAAN,OAAA,EAAAM,GAAA,CAAAvB,cAAA;MACA,GAAAa,IAAA;QACAkC,MAAA,CAAAX,MAAA,CAAAC,UAAA,CAAAW,IAAA;MACA,GAAAH,KAAA;QACAtB,GAAA,CAAAvB,cAAA,GAAAuB,GAAA,CAAAvB,cAAA;MACA;IACA;IACAkD,aAAA,WAAAA,cAAA7C,GAAA;MACA,KAAA1B,IAAA,CAAAgB,UAAA,GAAAU,GAAA;IACA;IACA8C,WAAA,WAAAA,YAAA;MACA;IAAA;EAEA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}