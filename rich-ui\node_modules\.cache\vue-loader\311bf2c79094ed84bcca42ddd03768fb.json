{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BulkTruckComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BulkTruckComponent.vue", "mtime": 1754881964228}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQXVkaXQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvYXVkaXQudnVlIg0KaW1wb3J0IExvZ2lzdGljc1Byb2dyZXNzIGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2xvZ2lzdGljc1Byb2dyZXNzLnZ1ZSINCmltcG9ydCBDaGFyZ2VMaXN0IGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2NoYXJnZUxpc3QudnVlIg0KaW1wb3J0IGxvY2F0aW9uU2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9Mb2NhdGlvblNlbGVjdC9pbmRleC52dWUiDQppbXBvcnQgVHJlZVNlbGVjdCBmcm9tICJAL2NvbXBvbmVudHMvVHJlZVNlbGVjdC9pbmRleC52dWUiDQppbXBvcnQgRGViaXROb3RlTGlzdCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9kb2N1bWVudC9kZWJpdE5vZGVMaXN0LnZ1ZSINCg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJCdWxrVHJ1Y2tDb21wb25lbnQiLA0KICBjb21wb25lbnRzOiB7DQogICAgRGViaXROb3RlTGlzdCwNCiAgICBBdWRpdCwNCiAgICBMb2dpc3RpY3NQcm9ncmVzcywNCiAgICBDaGFyZ2VMaXN0LA0KICAgIGxvY2F0aW9uU2VsZWN0LA0KICAgIFRyZWVTZWxlY3QNCiAgfSwNCiAgcHJvcHM6IHsNCiAgICAvLyDmlaPotKfmi5bovabmlbDmja7liJfooagNCiAgICBidWxrVHJ1Y2tMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICAvLyDooajljZXmlbDmja4NCiAgICBmb3JtOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfSwNCiAgICAvLyDmmL7npLrmjqfliLYNCiAgICBicmFuY2hJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgbG9naXN0aWNzSW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGNoYXJnZUluZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICBhdWRpdEluZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgLy8g54q25oCB5o6n5Yi2DQogICAgZGlzYWJsZWQ6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgYm9va2luZzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBwc2FWZXJpZnk6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgLy8g5pWw5o2u5YiX6KGoDQogICAgc3VwcGxpZXJMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICBjb21wYW55TGlzdDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgbG9jYXRpb25PcHRpb25zOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBkaXNwYXRjaEJpbGxDb25maWc6IFt7DQogICAgICAgIGZpbGU6ICLmtL7ovabljZUiLA0KICAgICAgICB0ZW1wbGF0ZUxpc3Q6IFsiRGlzcGF0Y2ggQmlsbCJdDQogICAgICB9XQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDliKTmlq3mmK/lkKbnpoHnlKjnirbmgIENCiAgICBpc0Rpc2FibGVkKCkgew0KICAgICAgcmV0dXJuIHRoaXMuZGlzYWJsZWQgfHwgdGhpcy5wc2FWZXJpZnkNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBoYW5kbGVBZGREZWJpdE5vdGUoc2VydmljZU9iamVjdCkgew0KICAgICAgbGV0IHJvdyA9IHt9DQogICAgICByb3cuc3FkUmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIHJvdy5yY3RJZCA9IHRoaXMuZm9ybS5yY3RJZA0KICAgICAgcm93LmlzUmVjaWV2aW5nT3JQYXlpbmcgPSAxDQogICAgICByb3cucnNDaGFyZ2VMaXN0ID0gW10NCiAgICAgIHRoaXMuJGVtaXQoJ2FkZERlYml0Tm90ZScsIHJvdywgc2VydmljZU9iamVjdCkNCiAgICB9LA0KICAgIC8vIOWIpOaWreWtl+auteaYr+WQpuemgeeUqA0KICAgIGlzRmllbGREaXNhYmxlZChpdGVtKSB7DQogICAgICByZXR1cm4gdGhpcy5wc2FWZXJpZnkgfHwgdGhpcy5kaXNhYmxlZCB8fCB0aGlzLmdldFNlcnZpY2VJbnN0YW5jZURpc2FibGUoaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMpDQogICAgfSwNCiAgICAvLyDojrflj5bkvpvlupTllYbpgq7nrrENCiAgICBnZXRTdXBwbGllckVtYWlsKHN1cHBsaWVySWQpIHsNCiAgICAgIGNvbnN0IHN1cHBsaWVyID0gdGhpcy5zdXBwbGllckxpc3QuZmluZCh2ID0+IHYuY29tcGFueUlkID09PSBzdXBwbGllcklkKQ0KICAgICAgcmV0dXJuIHN1cHBsaWVyID8gc3VwcGxpZXIuc3RhZmZFbWFpbCA6ICcnDQogICAgfSwNCiAgICAvLyDojrflj5blkIjnuqbmmL7npLrmlofmnKwNCiAgICBnZXRBZ3JlZW1lbnREaXNwbGF5KHNlcnZpY2VJbnN0YW5jZSkgew0KICAgICAgcmV0dXJuIHNlcnZpY2VJbnN0YW5jZS5hZ3JlZW1lbnRUeXBlQ29kZSArIHNlcnZpY2VJbnN0YW5jZS5hZ3JlZW1lbnRObw0KICAgIH0sDQogICAgLy8g5aSE55CG5rS+6L2m5Y2V55Sf5oiQDQogICAgZ2V0RGlzcGF0Y2hpbmdCaWxsKGl0ZW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoImdldERpc3BhdGNoaW5nQmlsbCIsIGl0ZW0pDQogICAgfSwNCiAgICAvLyDmt7vliqDmi5bovaborrDlvZUNCiAgICBhZGRUdWNrKHJzT3BUcnVja0xpc3QpIHsNCiAgICAgIGlmICghcnNPcFRydWNrTGlzdCkgcmV0dXJuDQogICAgICByc09wVHJ1Y2tMaXN0LnB1c2goe30pDQogICAgfSwNCiAgICAvLyDliKDpmaTmi5bovaborrDlvZUNCiAgICBkZWxldGVUcnVja0l0ZW0oaXRlbSwgdHJ1Y2tSb3cpIHsNCiAgICAgIGl0ZW0ucnNPcFRydWNrTGlzdCA9IGl0ZW0ucnNPcFRydWNrTGlzdC5maWx0ZXIodHJ1Y2sgPT4gdHJ1Y2sgIT09IHRydWNrUm93KQ0KICAgIH0sDQogICAgLy8g5Yig6Zmk54mp5rWB6L+b5bqmDQogICAgZGVsZXRlTG9nSXRlbShpdGVtLCBsb2dJdGVtKSB7DQogICAgICBpdGVtLnJzT3BMb2dMaXN0ID0gaXRlbS5yc09wTG9nTGlzdC5maWx0ZXIobG9nID0+IGxvZyAhPT0gbG9nSXRlbSkNCiAgICB9LA0KICAgIC8vIOWIoOmZpOi0ueeUqOmhuQ0KICAgIGRlbGV0ZUNoYXJnZUl0ZW0oaXRlbSwgY2hhcmdlSXRlbSkgew0KICAgICAgaXRlbS5yc0NoYXJnZUxpc3QgPSBpdGVtLnJzQ2hhcmdlTGlzdC5maWx0ZXIoY2hhcmdlID0+IGNoYXJnZSAhPT0gY2hhcmdlSXRlbSkNCiAgICB9LA0KICAgIC8vIOS6i+S7tui9rOWPkee7meeItue7hOS7tg0KICAgIGNoYW5nZVNlcnZpY2VGb2xkKHNlcnZpY2VJbnN0YW5jZSkgew0KICAgICAgdGhpcy4kZW1pdCgiY2hhbmdlU2VydmljZUZvbGQiLCBzZXJ2aWNlSW5zdGFuY2UpDQogICAgfSwNCiAgICBhZGRCdWxrVHJ1Y2soKSB7DQogICAgICB0aGlzLiRlbWl0KCJhZGRCdWxrVHJ1Y2siKQ0KICAgIH0sDQogICAgZGVsZXRlUnNPcEJ1bGtUcnVjayhpdGVtKSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVSc09wQnVsa1RydWNrIiwgaXRlbSkNCiAgICB9LA0KICAgIG9wZW5DaGFyZ2VTZWxlY3QoaXRlbSkgew0KICAgICAgdGhpcy4kZW1pdCgib3BlbkNoYXJnZVNlbGVjdCIsIGl0ZW0pDQogICAgfSwNCiAgICBhdWRpdENoYXJnZShpdGVtLCBldmVudCkgew0KICAgICAgdGhpcy4kZW1pdCgiYXVkaXRDaGFyZ2UiLCBpdGVtLCBldmVudCkNCiAgICB9LA0KICAgIGdlbmVyYXRlRnJlaWdodCh0eXBlMSwgdHlwZTIsIGl0ZW0pIHsNCiAgICAgIHRoaXMuJGVtaXQoImdlbmVyYXRlRnJlaWdodCIsIHR5cGUxLCB0eXBlMiwgaXRlbSkNCiAgICB9LA0KICAgIGhhbmRsZUFkZENvbW1vbih0eXBlKSB7DQogICAgICB0aGlzLiRlbWl0KCJoYW5kbGVBZGRDb21tb24iLCB0eXBlKQ0KICAgIH0sDQogICAgb3BlbkRpc3BhdGNoQ29tbW9uKCkgew0KICAgICAgdGhpcy4kZW1pdCgib3BlbkRpc3BhdGNoQ29tbW9uIikNCiAgICB9LA0KICAgIGNvcHlGcmVpZ2h0KGV2ZW50KSB7DQogICAgICB0aGlzLiRlbWl0KCJjb3B5RnJlaWdodCIsIGV2ZW50KQ0KICAgIH0sDQogICAgY2FsY3VsYXRlQ2hhcmdlKHNlcnZpY2VUeXBlLCBldmVudCwgaXRlbSkgew0KICAgICAgdGhpcy4kZW1pdCgiY2FsY3VsYXRlQ2hhcmdlIiwgc2VydmljZVR5cGUsIGV2ZW50LCBpdGVtKQ0KICAgIH0sDQogICAgZ2V0UGF5YWJsZShzZXJ2aWNlVHlwZSwgaXRlbSkgew0KICAgICAgcmV0dXJuIHRoaXMuJHBhcmVudC5nZXRQYXlhYmxlID8gdGhpcy4kcGFyZW50LmdldFBheWFibGUoc2VydmljZVR5cGUsIGl0ZW0pIDogbnVsbA0KICAgIH0sDQogICAgZ2V0U2VydmljZUluc3RhbmNlRGlzYWJsZShzZXJ2aWNlSW5zdGFuY2UpIHsNCiAgICAgIHJldHVybiB0aGlzLiRwYXJlbnQuZ2V0U2VydmljZUluc3RhbmNlRGlzYWJsZSA/IHRoaXMuJHBhcmVudC5nZXRTZXJ2aWNlSW5zdGFuY2VEaXNhYmxlKHNlcnZpY2VJbnN0YW5jZSkgOiBmYWxzZQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["BulkTruckComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyaA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BulkTruckComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"bulk-truck-component\">\r\n    <!--散货拖车-->\r\n    <div v-for=\"(item, index) in bulkTruckList\" :key=\"`bulk-truck-${index}`\" class=\"bulk-truck-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                拖车-BulkTruck\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addBulkTruck\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpBulkTruck(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(item.serviceTypeId, item)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item,$event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"dispatch-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in dispatchBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"getDispatchingBill(item)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"dispatch-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(5, 51, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"派车单号\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageSupplierNo\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"拖车公司单号\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"装运时间\">\r\n                    <el-date-picker\r\n                      v-model=\"item.precarriageTime\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"装运时间\"\r\n                      style=\"width:100%\"\r\n                      type=\"datetime\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"装运区域\">\r\n                    <location-select\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      :load-options=\"locationOptions\"\r\n                      :multiple=\"false\"\r\n                      :pass=\"item.precarriageRegionId\"\r\n                      :placeholder=\"'装运区域'\"\r\n                      @return=\"item.precarriageRegionId=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"装运详址\">\r\n                    <el-row :gutter=\"5\">\r\n                      <el-col :span=\"19\">\r\n                        <el-input\r\n                          v-model=\"item.precarriageAddress\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"装运详址\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"2\">\r\n                        <el-button\r\n                          :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          style=\"color: blue\"\r\n                          type=\"text\"\r\n                          @click=\"handleAddCommon('dispatch')\"\r\n                        >[↗]\r\n                        </el-button>\r\n                      </el-col>\r\n                      <el-col :span=\"1\">\r\n                        <el-button\r\n                          :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          style=\"color: blue\"\r\n                          type=\"text\"\r\n                          @click=\"openDispatchCommon\"\r\n                        >[...]\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"联系人\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageContact\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"装运联系人\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"装运电话\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageTel\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"装运电话\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"14\">\r\n                  <el-form-item label=\"装运备注\">\r\n                    <el-input\r\n                      v-model=\"item.precarriageRemark\"\r\n                      :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"装运备注\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col>\r\n                  <el-table\r\n                    :data=\"item.rsOpTruckList\"\r\n                    border\r\n                    class=\"pd0\"\r\n                  >\r\n                    <el-table-column\r\n                      label=\"序号\"\r\n                      type=\"index\"\r\n                      width=\"50\"\r\n                    />\r\n                    <el-table-column align=\"center\" label=\"提货司机姓名\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageDriverName\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机姓名\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货司机电话\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageDriverTel\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机电话\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货司机车牌\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageTruckNo\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机车牌\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"司机备注\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageTruckRemark\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货司机备注\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"柜号\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.containerNo\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"柜号\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"柜型\">\r\n                      <template slot-scope=\"scope\">\r\n                        <tree-select\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          :pass=\"scope.row.containerTypeCode\"\r\n                          :type=\"'unit'\"\r\n                          placeholder=\"选择柜型\"\r\n                          @returnData=\"scope.row.containerTypeCode = $event.unitCode\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"封条\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.sealNo\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"封条\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"磅单\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.weightPaper\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"磅单\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货须知\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageNote\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货须知\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"提货备注\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-input\r\n                          v-model=\"scope.row.precarriageRemark\"\r\n                          :class=\"isFieldDisabled(item) ? 'disable-form' : ''\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"提货备注\"\r\n                        />\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column align=\"center\" label=\"操作\">\r\n                      <template slot-scope=\"scope\">\r\n                        <el-button\r\n                          v-if=\"!disabled && !getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          style=\"color: red\"\r\n                          type=\"text\"\r\n                          @click=\"deleteTruckItem(item, scope.row)\"\r\n                        >删除\r\n                        </el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n                  <el-button\r\n                    :disabled=\"isFieldDisabled(item)\"\r\n                    style=\"padding: 0\"\r\n                    type=\"text\"\r\n                    @click=\"addTuck(item.rsOpTruckList)\"\r\n                  >[＋]\r\n                  </el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <div>\r\n                <logistics-progress\r\n                  :disabled=\"isFieldDisabled(item)\"\r\n                  :logistics-progress-data=\"item.rsOpLogList\"\r\n                  :open-logistics-progress-list=\"true\"\r\n                  :process-type=\"4\"\r\n                  :service-type=\"51\"\r\n                  @deleteItem=\"deleteLogItem(item, $event)\"\r\n                  @return=\"item.rsOpLogList=$event\"\r\n                />\r\n              </div>\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :service-type-id=\"51\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList=[]\"\r\n              @deleteItem=\"deleteChargeItem(item, $event)\"\r\n              @return=\"calculateCharge(51, $event, item)\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"item.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(item)\"\r\n              @deleteItem=\"item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(51,$event,item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport locationSelect from \"@/components/LocationSelect/index.vue\"\r\nimport TreeSelect from \"@/components/TreeSelect/index.vue\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\n\r\nexport default {\r\n  name: \"BulkTruckComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    locationSelect,\r\n    TreeSelect\r\n  },\r\n  props: {\r\n    // 散货拖车数据列表\r\n    bulkTruckList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    locationOptions: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      dispatchBillConfig: [{\r\n        file: \"派车单\",\r\n        templateList: [\"Dispatch Bill\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.psaVerify || this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理派车单生成\r\n    getDispatchingBill(item) {\r\n      this.$emit(\"getDispatchingBill\", item)\r\n    },\r\n    // 添加拖车记录\r\n    addTuck(rsOpTruckList) {\r\n      if (!rsOpTruckList) return\r\n      rsOpTruckList.push({})\r\n    },\r\n    // 删除拖车记录\r\n    deleteTruckItem(item, truckRow) {\r\n      item.rsOpTruckList = item.rsOpTruckList.filter(truck => truck !== truckRow)\r\n    },\r\n    // 删除物流进度\r\n    deleteLogItem(item, logItem) {\r\n      item.rsOpLogList = item.rsOpLogList.filter(log => log !== logItem)\r\n    },\r\n    // 删除费用项\r\n    deleteChargeItem(item, chargeItem) {\r\n      item.rsChargeList = item.rsChargeList.filter(charge => charge !== chargeItem)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addBulkTruck() {\r\n      this.$emit(\"addBulkTruck\")\r\n    },\r\n    deleteRsOpBulkTruck(item) {\r\n      this.$emit(\"deleteRsOpBulkTruck\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    handleAddCommon(type) {\r\n      this.$emit(\"handleAddCommon\", type)\r\n    },\r\n    openDispatchCommon() {\r\n      this.$emit(\"openDispatchCommon\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(serviceType, item) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(serviceType, item) : null\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// BulkTruck组件特定样式\r\n.bulk-truck-component {\r\n  width: 100%;\r\n\r\n  .bulk-truck-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .dispatch-bill-container {\r\n      margin-left: auto;\r\n\r\n      .dispatch-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        margin-left: 5px;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n\r\n  // 司机信息表格样式\r\n  .pd0 {\r\n    padding: 0;\r\n  }\r\n}\r\n</style>\r\n"]}]}