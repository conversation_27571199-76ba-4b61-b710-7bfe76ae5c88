{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\outboundrecord.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\outboundrecord.js", "mtime": 1739357164399}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listOutboundrecord", "query", "request", "url", "method", "params", "getOutboundrecord", "outboundRecordId", "addOutboundrecord", "data", "updateOutboundrecord", "delOutboundrecord", "changeStatus", "status", "listOutboundrecords", "listRental", "getOutboundrecords", "getRentals", "downloadOutboundBill", "responseType"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/outboundrecord.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询出仓记录列表\r\nexport function listOutboundrecord(query) {\r\n  return request({\r\n    url: '/system/outboundrecord/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询出仓记录详细\r\nexport function getOutboundrecord(outboundRecordId) {\r\n  return request({\r\n    url: '/system/outboundrecord/' + outboundRecordId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增出仓记录\r\nexport function addOutboundrecord(data) {\r\n  return request({\r\n    url: '/system/outboundrecord',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改出仓记录\r\nexport function updateOutboundrecord(data) {\r\n  return request({\r\n    url: '/system/outboundrecord',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除出仓记录\r\nexport function delOutboundrecord(outboundRecordId) {\r\n  return request({\r\n    url: '/system/outboundrecord/' + outboundRecordId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(outboundRecordId, status) {\r\n  const data = {\r\n    outboundRecordId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/outboundrecord/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 查询已出库记录\r\nexport function listOutboundrecords(query) {\r\n  return request({\r\n    url: '/system/outboundrecord/outboundrecords',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询已结算仓租记录\r\nexport function listRental(query) {\r\n  return request({\r\n    url: '/system/outboundrecord/listRental',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function getOutboundrecords(outboundRecordId) {\r\n  return request({\r\n    url: '/system/outboundrecord/outboundrecords/' + outboundRecordId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function getRentals(outboundRecordId) {\r\n  return request({\r\n    url: '/system/outboundrecord/rentals/' + outboundRecordId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function downloadOutboundBill(data) {\r\n  return request({\r\n    url: '/system/outboundrecord/outboundBill',\r\n    method: 'put',\r\n    data: data,\r\n    responseType: 'arraybuffer',\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACxC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACC,gBAAgB,EAAE;EAClD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,gBAAgB;IACjDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,iBAAiBA,CAACC,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,oBAAoBA,CAACD,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,iBAAiBA,CAACJ,gBAAgB,EAAE;EAClD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGI,gBAAgB;IACjDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,gBAAgB,EAAEM,MAAM,EAAE;EACrD,IAAMJ,IAAI,GAAG;IACXF,gBAAgB,EAAhBA,gBAAgB;IAChBM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,mBAAmBA,CAACb,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASc,UAAUA,CAACd,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mCAAmC;IACxCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASe,kBAAkBA,CAACT,gBAAgB,EAAE;EACnD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,yCAAyC,GAAGI,gBAAgB;IACjEH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASa,UAAUA,CAACV,gBAAgB,EAAE;EAC3C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC,GAAGI,gBAAgB;IACzDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASc,oBAAoBA,CAACT,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qCAAqC;IAC1CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA,IAAI;IACVU,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ"}]}