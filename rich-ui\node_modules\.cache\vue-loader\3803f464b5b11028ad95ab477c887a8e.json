{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\index.vue?vue&type=template&id=39cfdb14&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\index.vue", "mtime": 1754876882605}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}