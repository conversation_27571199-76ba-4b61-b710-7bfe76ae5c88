{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\charges\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\charges\\index.vue", "mtime": 1750148148758}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_rsCharge", "require", "_currency", "_interopRequireDefault", "_rsCharge<PERSON><PERSON><PERSON><PERSON>ields", "_index", "_rsChargeFieldLabelMap", "_rct", "_index2", "_rich", "_index3", "name", "components", "DataAggregatorBackGround", "DynamicSearch", "DataAggregator", "data", "rsC<PERSON>ge<PERSON><PERSON><PERSON><PERSON><PERSON>s", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "chargeList", "title", "open", "openAggregator", "aggregatorList", "rsChargeFieldLabelMap", "queryParams", "pageNum", "pageSize", "sqdRctId", "serviceId", "sqdServiceTypeId", "sqdRctNo", "relatedFreightId", "isRecievingOrPaying", "clearingCompanyId", "clearingCompanySummary", "quotationStrategyId", "dnChargeNameId", "dnCurrencyCode", "dnUnitRate", "dnUnitCode", "dnAmount", "basicCurrencyRate", "dutyRate", "subtotal", "chargeRemark", "clearingCurrencyCode", "dnCurrencyReceived", "dnCurrencyPaid", "dnCurrencyBalance", "accountReceivedIdList", "accountPaidIdList", "logisticsInvoiceIdList", "sqdServiceDetailsCode", "paymentTitleCode", "logisticsPaymentTermsCode", "isAccountConfirmed", "sqdDnCurrencyPaid", "sqdDnCurrencyBalance", "sqdWriteoffNoList", "sqdInvoiceIssued", "sqdInvoiceBalance", "currencyRateCalculateDate", "writeoffStatus", "form", "rules", "selectedRows", "totalBalance", "watch", "n", "created", "getList", "computed", "formattedTotalBalance", "currency", "format", "toFixed", "methods", "getAggregator", "params", "config", "JSON", "stringify", "rsChargeList", "aggregator", "parseTime", "_this", "listCharge", "then", "response", "rows", "cancel", "reset", "chargeId", "resetForm", "handleQuery", "_objectSpread2", "default", "console", "log", "reset<PERSON><PERSON>y", "handleStatusChange", "row", "_this2", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "catch", "handleSelectionChange", "selection", "calculateTotalBalance", "length", "reduce", "sum", "balance", "parseFloat", "handleAdd", "handleUpdate", "_this3", "getCharge", "submitForm", "_this4", "$refs", "validate", "valid", "updateCharge", "addCharge", "handleDelete", "_this5", "chargeIds", "delCharge", "handleExport", "download", "concat", "Date", "getTime", "exports", "_default"], "sources": ["src/views/system/charges/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"\" prop=\"sqdRctId\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdRctId\"\r\n              clearable\r\n              placeholder=\"\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属服务实例id ,\" prop=\"serviceId\">\r\n            <el-input\r\n              v-model=\"queryParams.serviceId\"\r\n              clearable\r\n              placeholder=\"所属服务实例id ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属服务类型id ,\" prop=\"sqdServiceTypeId\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdServiceTypeId\"\r\n              clearable\r\n              placeholder=\"所属服务类型id ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属操作单号 ,\" prop=\"sqdRctNo\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdRctNo\"\r\n              clearable\r\n              placeholder=\"所属操作单号 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"关联收费条目 ,与此条收入相关的成本，针对报价\" prop=\"relatedFreightId\">\r\n            <el-input\r\n              v-model=\"queryParams.relatedFreightId\"\r\n              clearable\r\n              placeholder=\"关联收费条目 ,与此条收入相关的成本，针对报价\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可\"\r\n                        prop=\"isRecievingOrPaying\"\r\n          >\r\n            <el-input\r\n              v-model=\"queryParams.isRecievingOrPaying\"\r\n              clearable\r\n              placeholder=\"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算公司 ,(应收/应付)\" prop=\"clearingCompanyId\">\r\n            <el-input\r\n              v-model=\"queryParams.clearingCompanyId\"\r\n              clearable\r\n              placeholder=\"结算公司 ,(应收/应付)\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算公司概要 ,(应收/应付)\" prop=\"clearingCompanySummary\">\r\n            <el-input\r\n              v-model=\"queryParams.clearingCompanySummary\"\r\n              clearable\r\n              placeholder=\"结算公司概要 ,(应收/应付)\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报价策略 ,\" prop=\"quotationStrategyId\">\r\n            <el-input\r\n              v-model=\"queryParams.quotationStrategyId\"\r\n              clearable\r\n              placeholder=\"报价策略 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"费用名称ID ,dn=debit note账单\" prop=\"dnChargeNameId\">\r\n            <el-input\r\n              v-model=\"queryParams.dnChargeNameId\"\r\n              clearable\r\n              placeholder=\"费用名称ID ,dn=debit note账单\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单币种 ,\" prop=\"dnCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.dnCurrencyCode\"\r\n              clearable\r\n              placeholder=\"账单币种 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"计费单价 ,\" prop=\"dnUnitRate\">\r\n            <el-input\r\n              v-model=\"queryParams.dnUnitRate\"\r\n              clearable\r\n              placeholder=\"计费单价 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"计费单位 ,\" prop=\"dnUnitCode\">\r\n            <el-input\r\n              v-model=\"queryParams.dnUnitCode\"\r\n              clearable\r\n              placeholder=\"计费单位 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"计费数量 ,\" prop=\"dnAmount\">\r\n            <el-input\r\n              v-model=\"queryParams.dnAmount\"\r\n              clearable\r\n              placeholder=\"计费数量 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"本位币汇率 ,\" prop=\"basicCurrencyRate\">\r\n            <el-input\r\n              v-model=\"queryParams.basicCurrencyRate\"\r\n              clearable\r\n              placeholder=\"本位币汇率 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"税率 ,\" prop=\"dutyRate\">\r\n            <el-input\r\n              v-model=\"queryParams.dutyRate\"\r\n              clearable\r\n              placeholder=\"税率 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"金额小计 \" prop=\"subtotal\">\r\n            <el-input\r\n              v-model=\"queryParams.subtotal\"\r\n              clearable\r\n              placeholder=\"金额小计 \"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"费用备注 ,\" prop=\"chargeRemark\">\r\n            <el-input\r\n              v-model=\"queryParams.chargeRemark\"\r\n              clearable\r\n              placeholder=\"费用备注 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算币种ID ,\" prop=\"clearingCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clearingCurrencyCode\"\r\n              clearable\r\n              placeholder=\"结算币种ID ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"折合账单币种已收 ,\" prop=\"dnCurrencyReceived\">\r\n            <el-input\r\n              v-model=\"queryParams.dnCurrencyReceived\"\r\n              clearable\r\n              placeholder=\"折合账单币种已收 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"折合账单币种已付 ,\" prop=\"dnCurrencyPaid\">\r\n            <el-input\r\n              v-model=\"queryParams.dnCurrencyPaid\"\r\n              clearable\r\n              placeholder=\"折合账单币种已付 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"本条目账单币种余额 ,\" prop=\"dnCurrencyBalance\">\r\n            <el-input\r\n              v-model=\"queryParams.dnCurrencyBalance\"\r\n              clearable\r\n              placeholder=\"本条目账单币种余额 ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"财务已收销账流水号List ,\" prop=\"accountReceivedIdList\">\r\n            <el-input\r\n              v-model=\"queryParams.accountReceivedIdList\"\r\n              clearable\r\n              placeholder=\"财务已收销账流水号List ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"财务已付销账流水号List ,\" prop=\"accountPaidIdList\">\r\n            <el-input\r\n              v-model=\"queryParams.accountPaidIdList\"\r\n              clearable\r\n              placeholder=\"财务已付销账流水号List ,\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票查询编号 ,发票查询编号\" prop=\"logisticsInvoiceIdList\">\r\n            <el-input\r\n              v-model=\"queryParams.logisticsInvoiceIdList\"\r\n              clearable\r\n              placeholder=\"发票查询编号 ,发票查询编号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"服务细目code \" prop=\"sqdServiceDetailsCode\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdServiceDetailsCode\"\r\n              clearable\r\n              placeholder=\"服务细目code \"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"付款抬头 \" prop=\"paymentTitleCode\">\r\n            <el-input\r\n              v-model=\"queryParams.paymentTitleCode\"\r\n              clearable\r\n              placeholder=\"付款抬头 \"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结款方式\" prop=\"logisticsPaymentTermsCode\">\r\n            <el-input\r\n              v-model=\"queryParams.logisticsPaymentTermsCode\"\r\n              clearable\r\n              placeholder=\"结款方式\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"财务审核标记\" prop=\"isAccountConfirmed\">\r\n            <el-input\r\n              v-model=\"queryParams.isAccountConfirmed\"\r\n              clearable\r\n              placeholder=\"财务审核标记\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"已销账金额\" prop=\"sqdDnCurrencyPaid\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdDnCurrencyPaid\"\r\n              clearable\r\n              placeholder=\"已销账金额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"未销账余额\" prop=\"sqdDnCurrencyBalance\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdDnCurrencyBalance\"\r\n              clearable\r\n              placeholder=\"未销账余额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账流水号List\" prop=\"sqdWriteoffNoList\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdWriteoffNoList\"\r\n              clearable\r\n              placeholder=\"销账流水号List\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"已开票金额\" prop=\"sqdInvoiceIssued\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdInvoiceIssued\"\r\n              clearable\r\n              placeholder=\"已开票金额\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"未开票余额 \" prop=\"sqdInvoiceBalance\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdInvoiceBalance\"\r\n              clearable\r\n              placeholder=\"未开票余额 \"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"\" prop=\"currencyRateCalculateDate\">\r\n            <el-date-picker v-model=\"queryParams.currencyRateCalculateDate\"\r\n                            clearable\r\n                            placeholder=\"\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:charge:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button @click=\"openAggregator=true\">数据汇总</el-button>\r\n            <el-dialog v-dialogDrag v-dialogDragWidth :visible.sync=\"openAggregator\"\r\n                       append-to-body width=\"80%\"\r\n            >\r\n              <data-aggregator-back-ground :aggregate-function=\"getAggregator\"\r\n                                           :data-source=\"aggregatorList\"\r\n                                           :data-source-type=\"'charges'\"\r\n                                           :config-type=\"'charges-agg'\"\r\n                                           :field-label-map=\"rsChargeFieldLabelMap\"\r\n              />\r\n            </el-dialog>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <dynamic-search\r\n              :search-fields=\"rsChargeSearchFields\"\r\n              :config-type=\"'charges-search'\"\r\n              @reset=\"resetQuery\"\r\n              @search=\"handleQuery($event)\"\r\n            />\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n          :data=\"chargeList\"\r\n          border\r\n          stripe\r\n          style=\"width: 100%\"\r\n          @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column\r\n            label=\"序号\"\r\n            type=\"index\"\r\n            width=\"30\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            type=\"selection\"\r\n            width=\"35\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"center\"\r\n            label=\"财务审核\"\r\n            prop=\"sqdRctNo\"\r\n            width=\"50\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.isAccountConfirmed === \"1\" ? \"√\" : \"-\" }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"center\"\r\n            label=\"销账状态\"\r\n            prop=\"sqdRctNo\"\r\n            width=\"50\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.writeoffStatus == \"1\" ? \"√\" : \"-\" }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"操作单号\"\r\n            prop=\"sqdRctNo\"\r\n            width=\"100\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"计费货量\"\r\n            prop=\"revenueTon\"\r\n            width=\"100\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"ETA\"\r\n            prop=\"eta\"\r\n            width=\"100\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ parseTime(scope.row.eta, \"{y}-{m}-{d}\") }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"ETD\"\r\n            prop=\"etd\"\r\n            width=\"100\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ parseTime(scope.row.etd, \"{y}-{m}-{d}\") }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"收付\"\r\n            prop=\"etd\"\r\n            width=\"50\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.isRecievingOrPaying == 0 ? \"收\" : \"付\" }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"结算单位\"\r\n            prop=\"companyName\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"委托单位\"\r\n            prop=\"clientSummary\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"订单所属\"\r\n            prop=\"orderBelongsTo\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"费用名称\"\r\n            prop=\"chargeName\"\r\n            width=\"80\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"单价\"\r\n            prop=\"dnUnitRate\"\r\n            width=\"80\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"数量\"\r\n            prop=\"dnAmount\"\r\n            width=\"80\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"币种\"\r\n            prop=\"dnCurrencyCode\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            label=\"汇率\"\r\n            prop=\"basicCurrencyRate\"\r\n          >\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"启运港\"\r\n            prop=\"dnCurrencyCode\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.pol }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"目的港\"\r\n            prop=\"dnCurrencyCode\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.destinationPort }}\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"提单号\"\r\n            prop=\"dnCurrencyCode\"\r\n            show-overflow-tooltip\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.blNo }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"柜号\"\r\n            prop=\"dnCurrencyCode\"\r\n            show-overflow-tooltip\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.sqdContainersSealsSum }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"税率\"\r\n            prop=\"dnCurrencyCode\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.dutyRate }}%\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column\r\n            align=\"right\"\r\n            label=\"小计\"\r\n            prop=\"dnUnitRate\"\r\n          >\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.subtotal }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"已收小计\" prop=\"sqdDnCurrencyPaid\"/>\r\n          <el-table-column align=\"right\" label=\"已付小计\" prop=\"sqdDnCurrencyPaid\"/>\r\n          <el-table-column align=\"right\" label=\"应收RMB\" prop=\"receivableRmb\"/>\r\n          <el-table-column align=\"right\" label=\"应收USD\" prop=\"receivableUsd\"/>\r\n          <el-table-column align=\"right\" label=\"未收RMB\" prop=\"uncollectedRmb\"/>\r\n          <el-table-column align=\"right\" label=\"未收USD\" prop=\"uncollectedUsd\"/>\r\n          <el-table-column align=\"right\" label=\"应付RMB\" prop=\"payableRmb\"/>\r\n          <el-table-column align=\"right\" label=\"应付USD\" prop=\"payableUsd\"/>\r\n          <el-table-column align=\"right\" label=\"未付RMB\" prop=\"unpaidRmb\"/>\r\n          <el-table-column align=\"right\" label=\"未付USD\" prop=\"unpaidUsd\"/>\r\n          <el-table-column align=\"right\" label=\"已销账金额\" prop=\"sqdDnCurrencyPaid\"/>\r\n          <el-table-column align=\"right\" label=\"未销账余额\" prop=\"sqdDnCurrencyBalance\"/>\r\n          <el-table-column align=\"right\" label=\"备注\" prop=\"chargeRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"right\" label=\"付款抬头\" prop=\"paymentTitleCode\"/>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n        <div v-if=\"selectedRows.length > 0\" class=\"statistics-area\">\r\n          <el-alert\r\n            :closable=\"false\"\r\n            show-icon\r\n            title=\"已选择记录统计\"\r\n            type=\"info\"\r\n          >\r\n            <div class=\"statistics-content\">\r\n              <span class=\"statistics-item\">\r\n                <span class=\"label\">已选择记录数:</span>\r\n                <span class=\"value\">{{ selectedRows.length }}</span>\r\n              </span>\r\n              <span class=\"statistics-item\">\r\n                <span class=\"label\">未销账余额合计:</span>\r\n                <span class=\"value\">{{ formattedTotalBalance }}</span>\r\n              </span>\r\n            </div>\r\n          </el-alert>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改费用明细对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"title\" :visible.sync=\"open\"\r\n      append-to-body\r\n      width=\"500px\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"\" prop=\"sqdRctId\">\r\n          <el-input v-model=\"form.sqdRctId\" placeholder=\"\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属服务实例id ,\" prop=\"serviceId\">\r\n          <el-input v-model=\"form.serviceId\" placeholder=\"所属服务实例id ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属服务类型id ,\" prop=\"sqdServiceTypeId\">\r\n          <el-input v-model=\"form.sqdServiceTypeId\" placeholder=\"所属服务类型id ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属操作单号 ,\" prop=\"sqdRctNo\">\r\n          <el-input v-model=\"form.sqdRctNo\" placeholder=\"所属操作单号 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"关联收费条目 ,与此条收入相关的成本，针对报价\" prop=\"relatedFreightId\">\r\n          <el-input v-model=\"form.relatedFreightId\" placeholder=\"关联收费条目 ,与此条收入相关的成本，针对报价\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可\"\r\n                      prop=\"isRecievingOrPaying\"\r\n        >\r\n          <el-input v-model=\"form.isRecievingOrPaying\"\r\n                    placeholder=\"收支标志 (0应收，1应付)，客户信息中的查询在表中只需要找到全部对应的应收即可\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"结算公司 ,(应收/应付)\" prop=\"clearingCompanyId\">\r\n          <el-input v-model=\"form.clearingCompanyId\" placeholder=\"结算公司 ,(应收/应付)\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"结算公司概要 ,(应收/应付)\" prop=\"clearingCompanySummary\">\r\n          <el-input v-model=\"form.clearingCompanySummary\" placeholder=\"结算公司概要 ,(应收/应付)\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"报价策略 ,\" prop=\"quotationStrategyId\">\r\n          <el-input v-model=\"form.quotationStrategyId\" placeholder=\"报价策略 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用名称ID ,dn=debit note账单\" prop=\"dnChargeNameId\">\r\n          <el-input v-model=\"form.dnChargeNameId\" placeholder=\"费用名称ID ,dn=debit note账单\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"账单币种 ,\" prop=\"dnCurrencyCode\">\r\n          <el-input v-model=\"form.dnCurrencyCode\" placeholder=\"账单币种 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"计费单价 ,\" prop=\"dnUnitRate\">\r\n          <el-input v-model=\"form.dnUnitRate\" placeholder=\"计费单价 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"计费单位 ,\" prop=\"dnUnitCode\">\r\n          <el-input v-model=\"form.dnUnitCode\" placeholder=\"计费单位 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"计费数量 ,\" prop=\"dnAmount\">\r\n          <el-input v-model=\"form.dnAmount\" placeholder=\"计费数量 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"本位币汇率 ,\" prop=\"basicCurrencyRate\">\r\n          <el-input v-model=\"form.basicCurrencyRate\" placeholder=\"本位币汇率 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"税率 ,\" prop=\"dutyRate\">\r\n          <el-input v-model=\"form.dutyRate\" placeholder=\"税率 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"金额小计 \" prop=\"subtotal\">\r\n          <el-input v-model=\"form.subtotal\" placeholder=\"金额小计 \"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"费用备注 ,\" prop=\"chargeRemark\">\r\n          <el-input v-model=\"form.chargeRemark\" placeholder=\"费用备注 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"结算币种ID ,\" prop=\"clearingCurrencyCode\">\r\n          <el-input v-model=\"form.clearingCurrencyCode\" placeholder=\"结算币种ID ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"折合账单币种已收 ,\" prop=\"dnCurrencyReceived\">\r\n          <el-input v-model=\"form.dnCurrencyReceived\" placeholder=\"折合账单币种已收 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"折合账单币种已付 ,\" prop=\"dnCurrencyPaid\">\r\n          <el-input v-model=\"form.dnCurrencyPaid\" placeholder=\"折合账单币种已付 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"本条目账单币种余额 ,\" prop=\"dnCurrencyBalance\">\r\n          <el-input v-model=\"form.dnCurrencyBalance\" placeholder=\"本条目账单币种余额 ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"财务已收销账流水号List ,\" prop=\"accountReceivedIdList\">\r\n          <el-input v-model=\"form.accountReceivedIdList\" placeholder=\"财务已收销账流水号List ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"财务已付销账流水号List ,\" prop=\"accountPaidIdList\">\r\n          <el-input v-model=\"form.accountPaidIdList\" placeholder=\"财务已付销账流水号List ,\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"发票查询编号 ,发票查询编号\" prop=\"logisticsInvoiceIdList\">\r\n          <el-input v-model=\"form.logisticsInvoiceIdList\" placeholder=\"发票查询编号 ,发票查询编号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务细目code \" prop=\"sqdServiceDetailsCode\">\r\n          <el-input v-model=\"form.sqdServiceDetailsCode\" placeholder=\"服务细目code \"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"付款抬头 \" prop=\"paymentTitleCode\">\r\n          <el-input v-model=\"form.paymentTitleCode\" placeholder=\"付款抬头 \"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"结款方式\" prop=\"logisticsPaymentTermsCode\">\r\n          <el-input v-model=\"form.logisticsPaymentTermsCode\" placeholder=\"结款方式\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"财务审核标记\" prop=\"isAccountConfirmed\">\r\n          <el-input v-model=\"form.isAccountConfirmed\" placeholder=\"财务审核标记\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"已销账金额\" prop=\"sqdDnCurrencyPaid\">\r\n          <el-input v-model=\"form.sqdDnCurrencyPaid\" placeholder=\"已销账金额\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"未销账余额\" prop=\"sqdDnCurrencyBalance\">\r\n          <el-input v-model=\"form.sqdDnCurrencyBalance\" placeholder=\"未销账余额\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"销账流水号List\" prop=\"sqdWriteoffNoList\">\r\n          <el-input v-model=\"form.sqdWriteoffNoList\" placeholder=\"销账流水号List\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"已开票金额\" prop=\"sqdInvoiceIssued\">\r\n          <el-input v-model=\"form.sqdInvoiceIssued\" placeholder=\"已开票金额\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"未开票余额 \" prop=\"sqdInvoiceBalance\">\r\n          <el-input v-model=\"form.sqdInvoiceBalance\" placeholder=\"未开票余额 \"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"\" prop=\"currencyRateCalculateDate\">\r\n          <el-date-picker v-model=\"form.currencyRateCalculateDate\"\r\n                          clearable\r\n                          placeholder=\"\"\r\n                          type=\"date\"\r\n                          value-format=\"yyyy-MM-dd\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCharge,\r\n  aggregator,\r\n  changeStatus,\r\n  delCharge,\r\n  getCharge,\r\n  listCharge,\r\n  updateCharge\r\n} from \"@/api/system/rsCharge\"\r\nimport currency from \"currency.js\"\r\nimport {rsChargeSearchFields} from \"@/config/rsChargeSearchFields\"\r\nimport DataAggregator from \"@/views/system/DataAggregator/index.vue\"\r\nimport {rsChargeFieldLabelMap} from \"@/config/rsChargeFieldLabelMap\"\r\nimport {listAggregatorRct, listVerifyAggregatorList} from \"@/api/system/rct\"\r\nimport DynamicSearch from \"@/components/DynamicSearch/index.vue\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\r\n\r\nexport default {\r\n  name: \"Charges\",\r\n  components: {DataAggregatorBackGround, DynamicSearch, DataAggregator},\r\n  data() {\r\n    return {\r\n      rsChargeSearchFields,\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 费用明细表格数据\r\n      chargeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      openAggregator: false,\r\n      aggregatorList: [],\r\n      rsChargeFieldLabelMap,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        sqdRctId: null,\r\n        serviceId: null,\r\n        sqdServiceTypeId: null,\r\n        sqdRctNo: null,\r\n        relatedFreightId: null,\r\n        isRecievingOrPaying: null,\r\n        clearingCompanyId: null,\r\n        clearingCompanySummary: null,\r\n        quotationStrategyId: null,\r\n        dnChargeNameId: null,\r\n        dnCurrencyCode: null,\r\n        dnUnitRate: null,\r\n        dnUnitCode: null,\r\n        dnAmount: null,\r\n        basicCurrencyRate: null,\r\n        dutyRate: null,\r\n        subtotal: null,\r\n        chargeRemark: null,\r\n        clearingCurrencyCode: null,\r\n        dnCurrencyReceived: null,\r\n        dnCurrencyPaid: null,\r\n        dnCurrencyBalance: null,\r\n        accountReceivedIdList: null,\r\n        accountPaidIdList: null,\r\n        logisticsInvoiceIdList: null,\r\n        sqdServiceDetailsCode: null,\r\n        paymentTitleCode: null,\r\n        logisticsPaymentTermsCode: null,\r\n        isAccountConfirmed: null,\r\n        sqdDnCurrencyPaid: null,\r\n        sqdDnCurrencyBalance: null,\r\n        sqdWriteoffNoList: null,\r\n        sqdInvoiceIssued: null,\r\n        sqdInvoiceBalance: null,\r\n        currencyRateCalculateDate: null,\r\n        writeoffStatus: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      selectedRows: [], // 用于存储选中的行\r\n      totalBalance: 0 // 未销账余额合计\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  computed: {\r\n    // 格式化后的总金额\r\n    formattedTotalBalance() {\r\n      // 如果使用了currency库，可以使用currency格式化\r\n      if (this.currency) {\r\n        return this.currency(this.totalBalance).format()\r\n      }\r\n      // 否则使用简单的数字格式化\r\n      return this.totalBalance.toFixed(2)\r\n    }\r\n  },\r\n  methods: {\r\n    getAggregator(params) {\r\n      params.config = JSON.stringify(params.config)\r\n      this.queryParams.params = params\r\n      this.queryParams.rsChargeList = this.aggregatorList\r\n      return aggregator(this.queryParams)\r\n    },\r\n    parseTime,\r\n    currency,\r\n    /** 查询费用明细列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listCharge(this.queryParams).then(response => {\r\n        this.chargeList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        chargeId: null,\r\n        sqdRctId: null,\r\n        serviceId: null,\r\n        sqdServiceTypeId: null,\r\n        sqdRctNo: null,\r\n        relatedFreightId: null,\r\n        isRecievingOrPaying: null,\r\n        clearingCompanyId: null,\r\n        clearingCompanySummary: null,\r\n        quotationStrategyId: null,\r\n        dnChargeNameId: null,\r\n        dnCurrencyCode: null,\r\n        dnUnitRate: null,\r\n        dnUnitCode: null,\r\n        dnAmount: null,\r\n        basicCurrencyRate: null,\r\n        dutyRate: null,\r\n        subtotal: null,\r\n        chargeRemark: null,\r\n        clearingCurrencyCode: null,\r\n        dnCurrencyReceived: null,\r\n        dnCurrencyPaid: null,\r\n        dnCurrencyBalance: null,\r\n        accountReceivedIdList: null,\r\n        accountPaidIdList: null,\r\n        logisticsInvoiceIdList: null,\r\n        sqdServiceDetailsCode: null,\r\n        paymentTitleCode: null,\r\n        logisticsPaymentTermsCode: null,\r\n        isAccountConfirmed: null,\r\n        sqdDnCurrencyPaid: null,\r\n        sqdDnCurrencyBalance: null,\r\n        sqdWriteoffNoList: null,\r\n        sqdInvoiceIssued: null,\r\n        sqdInvoiceBalance: null,\r\n        currencyRateCalculateDate: null,\r\n        writeoffStatus: \"0\"\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery(queryParams) {\r\n      this.queryParams = {\r\n        ...this.queryParams,\r\n        ...queryParams,\r\n        pageNum: 1  // 直接在合并时设置页码为1\r\n      }\r\n      this.queryParams.pageNum = 1\r\n      console.log(queryParams)\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.chargeId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.selectedRows = selection\r\n      this.aggregatorList = selection\r\n      this.calculateTotalBalance()\r\n    },\r\n    // 计算选中行的未销账余额合计\r\n    calculateTotalBalance() {\r\n      if (!this.selectedRows || this.selectedRows.length === 0) {\r\n        this.totalBalance = 0\r\n        return\r\n      }\r\n\r\n      // 计算未销账余额的合计\r\n      this.totalBalance = this.selectedRows.reduce((sum, row) => {\r\n        // 转换为数字并相加\r\n        const balance = parseFloat(row.sqdDnCurrencyBalance) || 0\r\n        return sum + balance\r\n      }, 0)\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加费用明细\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const chargeId = row.chargeId || this.ids\r\n      getCharge(chargeId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改费用明细\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.chargeId != null) {\r\n            updateCharge(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addCharge(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const chargeIds = row.chargeId || this.ids\r\n      this.$modal.confirm(\"是否确认删除费用明细编号为\\\"\" + chargeIds + \"\\\"的数据项？\").then(function () {\r\n        return delCharge(chargeIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/charge/export\", {\r\n        ...this.queryParams\r\n      }, `charge_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.statistics-area {\r\n  margin-top: 15px;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.statistics-content {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n}\r\n\r\n.statistics-item {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.statistics-item .label {\r\n  font-weight: bold;\r\n  margin-right: 5px;\r\n}\r\n\r\n.statistics-item .value {\r\n  color: #409EFF;\r\n  font-weight: bold;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AAirBA,IAAAA,SAAA,GAAAC,OAAA;AASA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,qBAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,sBAAA,GAAAL,OAAA;AACA,IAAAM,IAAA,GAAAN,OAAA;AACA,IAAAO,OAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,OAAA,GAAAP,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAU,IAAA;EACAC,UAAA;IAAAC,wBAAA,EAAAA,eAAA;IAAAC,aAAA,EAAAA,eAAA;IAAAC,cAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,oBAAA,EAAAA,0CAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACAC,cAAA;MACAC,cAAA;MACAC,qBAAA,EAAAA,4CAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,sBAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,cAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,iBAAA;QACAC,QAAA;QACAC,QAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,sBAAA;QACAC,qBAAA;QACAC,gBAAA;QACAC,yBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,oBAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,yBAAA;QACAC,cAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACAC,YAAA;MAAA;MACAC,YAAA;IACA;EACA;;EACAC,KAAA;IACAnD,UAAA,WAAAA,WAAAoD,CAAA;MACA,IAAAA,CAAA;QACA,KAAAzD,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACA2D,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,QAAA;IACA;IACAC,qBAAA,WAAAA,sBAAA;MACA;MACA,SAAAC,QAAA;QACA,YAAAA,QAAA,MAAAP,YAAA,EAAAQ,MAAA;MACA;MACA;MACA,YAAAR,YAAA,CAAAS,OAAA;IACA;EACA;EACAC,OAAA;IACAC,aAAA,WAAAA,cAAAC,MAAA;MACAA,MAAA,CAAAC,MAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAH,MAAA,CAAAC,MAAA;MACA,KAAAvD,WAAA,CAAAsD,MAAA,GAAAA,MAAA;MACA,KAAAtD,WAAA,CAAA0D,YAAA,QAAA5D,cAAA;MACA,WAAA6D,oBAAA,OAAA3D,WAAA;IACA;IACA4D,SAAA,EAAAA,eAAA;IACAX,QAAA,EAAAA,iBAAA;IACA,eACAH,OAAA,WAAAA,QAAA;MAAA,IAAAe,KAAA;MACA,KAAAzE,OAAA;MACA,IAAA0E,oBAAA,OAAA9D,WAAA,EAAA+D,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAnE,UAAA,GAAAsE,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAApE,KAAA,GAAAuE,QAAA,CAAAvE,KAAA;QACAoE,KAAA,CAAAzE,OAAA;MACA;IACA;IACA;IACA8E,MAAA,WAAAA,OAAA;MACA,KAAAtE,IAAA;MACA,KAAAuE,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA5B,IAAA;QACA6B,QAAA;QACAjE,QAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,iBAAA;QACAC,sBAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,cAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,iBAAA;QACAC,QAAA;QACAC,QAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,sBAAA;QACAC,qBAAA;QACAC,gBAAA;QACAC,yBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,oBAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,yBAAA;QACAC,cAAA;MACA;MACA,KAAA+B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAAtE,WAAA;MACA,KAAAA,WAAA,OAAAuE,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,KAAAxE,WAAA,GACAA,WAAA;QACAC,OAAA;MAAA,EACA;;MACA,KAAAD,WAAA,CAAAC,OAAA;MACAwE,OAAA,CAAAC,GAAA,CAAA1E,WAAA;MACA,KAAA8C,OAAA;IACA;IACA,aACA6B,UAAA,WAAAA,WAAA;MACA,KAAAN,SAAA;MACA,KAAAC,WAAA;IACA;IACAM,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAAhB,IAAA;QACA,WAAAoB,sBAAA,EAAAN,GAAA,CAAAT,QAAA,EAAAS,GAAA,CAAAG,MAAA;MACA,GAAAjB,IAAA;QACAe,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAAM,KAAA;QACAR,GAAA,CAAAG,MAAA,GAAAH,GAAA,CAAAG,MAAA;MACA;IACA;IACA;IACAM,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9C,YAAA,GAAA8C,SAAA;MACA,KAAAzF,cAAA,GAAAyF,SAAA;MACA,KAAAC,qBAAA;IACA;IACA;IACAA,qBAAA,WAAAA,sBAAA;MACA,UAAA/C,YAAA,SAAAA,YAAA,CAAAgD,MAAA;QACA,KAAA/C,YAAA;QACA;MACA;;MAEA;MACA,KAAAA,YAAA,QAAAD,YAAA,CAAAiD,MAAA,WAAAC,GAAA,EAAAd,GAAA;QACA;QACA,IAAAe,OAAA,GAAAC,UAAA,CAAAhB,GAAA,CAAA5C,oBAAA;QACA,OAAA0D,GAAA,GAAAC,OAAA;MACA;IACA;IACA,aACAE,SAAA,WAAAA,UAAA;MACA,KAAA3B,KAAA;MACA,KAAAvE,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAoG,YAAA,WAAAA,aAAAlB,GAAA;MAAA,IAAAmB,MAAA;MACA,KAAA7B,KAAA;MACA,IAAAC,QAAA,GAAAS,GAAA,CAAAT,QAAA,SAAA/E,GAAA;MACA,IAAA4G,mBAAA,EAAA7B,QAAA,EAAAL,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAAzD,IAAA,GAAAyB,QAAA,CAAAhF,IAAA;QACAgH,MAAA,CAAApG,IAAA;QACAoG,MAAA,CAAArG,KAAA;MACA;IACA;IACA,WACAuG,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA5D,IAAA,CAAA6B,QAAA;YACA,IAAAmC,sBAAA,EAAAJ,MAAA,CAAA5D,IAAA,EAAAwB,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAlB,MAAA,CAAAG,UAAA;cACAe,MAAA,CAAAvG,IAAA;cACAuG,MAAA,CAAArD,OAAA;YACA;UACA;YACA,IAAA0D,mBAAA,EAAAL,MAAA,CAAA5D,IAAA,EAAAwB,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAlB,MAAA,CAAAG,UAAA;cACAe,MAAA,CAAAvG,IAAA;cACAuG,MAAA,CAAArD,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA2D,YAAA,WAAAA,aAAA5B,GAAA;MAAA,IAAA6B,MAAA;MACA,IAAAC,SAAA,GAAA9B,GAAA,CAAAT,QAAA,SAAA/E,GAAA;MACA,KAAA4F,MAAA,CAAAC,OAAA,qBAAAyB,SAAA,cAAA5C,IAAA;QACA,WAAA6C,mBAAA,EAAAD,SAAA;MACA,GAAA5C,IAAA;QACA2C,MAAA,CAAA5D,OAAA;QACA4D,MAAA,CAAAzB,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACAwB,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,6BAAAvC,cAAA,CAAAC,OAAA,MACA,KAAAxE,WAAA,aAAA+G,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAA1C,OAAA,GAAA2C,QAAA"}]}