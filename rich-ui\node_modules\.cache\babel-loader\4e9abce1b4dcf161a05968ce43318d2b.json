{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\json-view.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\json-view.vue", "mtime": 1737429728488}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5qc29uLnN0cmluZ2lmeS5qcyIpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwp2YXIgX2RlZmF1bHQgPSB7CiAgbmFtZTogIkpTT05WaWV3IiwKICBwcm9wczogewogICAgdGVtcGxhdGU6IHsKICAgICAgdHlwZTogT2JqZWN0CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdmlzaWJsZTogZmFsc2UsCiAgICAgIHNwaW5uaW5nOiB0cnVlLAogICAgICBqc29uT3V0OiAiIiwKICAgICAgdGlkTW9kZTogZmFsc2UsCiAgICAgIGJlYXV0aWZ5OiBmYWxzZQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7fSwKICB3YXRjaDoge30sCiAgY3JlYXRlZDogZnVuY3Rpb24gY3JlYXRlZCgpIHt9LAogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICBoaWRlTW9kYWw6IGZ1bmN0aW9uIGhpZGVNb2RhbCgpIHsKICAgICAgdGhpcy52aXNpYmxlID0gZmFsc2U7CiAgICB9LAogICAgc2hvdzogZnVuY3Rpb24gc2hvdygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgdGhpcy52aXNpYmxlID0gdHJ1ZTsKICAgICAgdGhpcy5zcGlubmluZyA9IHRydWU7CiAgICAgIHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgewogICAgICAgIHZhciBqc29uID0gX3RoaXMudGlkTW9kZSA/IF90aGlzLnRlbXBsYXRlLmdldEpzb25UaWQoKSA6IF90aGlzLnRlbXBsYXRlLmdldEpzb24oKTsKICAgICAgICB2YXIgYmVhdXRpZnkgPSBfdGhpcy5iZWF1dGlmeSA/IDIgOiAwOwogICAgICAgIF90aGlzLmpzb25PdXQgPSBKU09OLnN0cmluZ2lmeShqc29uLCBudWxsLCBiZWF1dGlmeSk7CiAgICAgICAgX3RoaXMuc3Bpbm5pbmcgPSBmYWxzZTsKICAgICAgfSwgNTAwKTsKICAgIH0sCiAgICBvbk1vZGVDaGFuZ2U6IGZ1bmN0aW9uIG9uTW9kZUNoYW5nZSgpIHsKICAgICAgdGhpcy5zaG93KCk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["name", "props", "template", "type", "Object", "data", "visible", "spinning", "jsonOut", "tidMode", "beautify", "computed", "watch", "created", "mounted", "methods", "hideModal", "show", "_this", "setTimeout", "json", "getJsonTid", "get<PERSON>son", "JSON", "stringify", "onModeChange", "exports", "default", "_default"], "sources": ["src/views/print/demo/json-view.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-button type=\"primary\" @click=\"show\">\r\n      查看模板json\r\n    </el-button>\r\n    <el-dialog :maskClosable=\"false\" :visible=\"visible\"\r\n               @cancel=\"hideModal\">\r\n      <div v-loading=\"spinning\" style=\"min-height: 100px\">\r\n        <el-input v-model:value=\"jsonOut\" style=\"width:100%;height:100%\" type=\"textarea\"/>\r\n      </div>\r\n      <template slot=\"title\">\r\n        <el-row :gutter=\"0\">\r\n          <div style=\"margin-right: 20px\">JSON</div>\r\n          <el-col :span=\"12\">\r\n            <el-switch v-model:checked=\"tidMode\" active-text=\"tid模式\" inactive-text=\"默认\"\r\n                       @change=\"onModeChange\"/>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-switch v-model:checked=\"beautify\" active-text=\"美化\" inactive-textn=\"压缩\"\r\n                       @change=\"onModeChange\"/>\r\n          </el-col>\r\n        </el-row>\r\n      </template>\r\n      <template slot=\"footer\">\r\n        <el-button key=\"close\" type=\"info\" @click=\"hideModal\">\r\n          关闭\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"JSONView\",\r\n  props: {\r\n    template: {\r\n      type: Object,\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      spinning: true,\r\n      jsonOut: \"\",\r\n      tidMode: false,\r\n      beautify: false,\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  created() {\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    hideModal() {\r\n      this.visible = false\r\n    },\r\n    show() {\r\n      this.visible = true\r\n      this.spinning = true\r\n      setTimeout(() => {\r\n        let json = this.tidMode ? this.template.getJsonTid() : this.template.getJson();\r\n        let beautify = this.beautify ? 2 : 0;\r\n        this.jsonOut = JSON.stringify(json, null, beautify);\r\n        this.spinning = false\r\n      }, 500)\r\n    },\r\n    onModeChange() {\r\n      this.show();\r\n    }\r\n  }\r\n}\r\n\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .ant-modal-body {\r\n  padding: 0px;\r\n}\r\n\r\n::v-deep .ant-modal-content {\r\n  margin-bottom: 24px;\r\n}\r\n\r\n::v-deep .el-textarea__inner {\r\n  height: 40vh;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAiCA;EACAA,IAAA;EACAC,KAAA;IACAC,QAAA;MACAC,IAAA,EAAAC;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;MACAC,OAAA;MACAC,QAAA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAV,OAAA;IACA;IACAW,IAAA,WAAAA,KAAA;MAAA,IAAAC,KAAA;MACA,KAAAZ,OAAA;MACA,KAAAC,QAAA;MACAY,UAAA;QACA,IAAAC,IAAA,GAAAF,KAAA,CAAAT,OAAA,GAAAS,KAAA,CAAAhB,QAAA,CAAAmB,UAAA,KAAAH,KAAA,CAAAhB,QAAA,CAAAoB,OAAA;QACA,IAAAZ,QAAA,GAAAQ,KAAA,CAAAR,QAAA;QACAQ,KAAA,CAAAV,OAAA,GAAAe,IAAA,CAAAC,SAAA,CAAAJ,IAAA,QAAAV,QAAA;QACAQ,KAAA,CAAAX,QAAA;MACA;IACA;IACAkB,YAAA,WAAAA,aAAA;MACA,KAAAR,IAAA;IACA;EACA;AACA;AAAAS,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}