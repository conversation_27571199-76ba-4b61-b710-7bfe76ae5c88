{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\logisticsProgress.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\logisticsProgress.vue", "mtime": 1718360821897}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "_interopRequireDefault", "require", "_index2", "_moment", "name", "components", "ProgressName", "ProgressStatus", "props", "watch", "logisticsProgressData", "n", "$emit", "methods", "rowIndex", "_ref", "row", "id", "addLogisticsProgress", "obj", "showProgress", "showStatus", "processStatusId", "processStatusTime", "moment", "format", "opId", "$store", "state", "user", "sid", "push", "deleteLogisticsProgress", "exports", "default", "_default"], "sources": ["src/views/system/document/logisticsProgress.vue"], "sourcesContent": ["<template>\r\n  <div id=\"app-container\">\r\n    <el-col :style=\"{'display':openLogisticsProgressList?'':'none'}\" style=\"margin: 0;padding: 0;\">\r\n      <div :class=\"{'inactive':openLogisticsProgressList==false,'active':openLogisticsProgressList}\">\r\n        <el-table :data=\"logisticsProgressData\" :row-class-name=\"rowIndex\" border class=\"pd0\">\r\n          <el-table-column label=\"进度\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showProgress\" style=\"width: 50px;height: 20px;\"\r\n                   @click=\"scope.row.showProgress = true\"\r\n              >\r\n                {{ scope.row.basProcess.processShortName }}\r\n              </div>\r\n              <progress-name v-if=\"scope.row.showProgress\" :disabled=\"disabled\" :pass=\"scope.row.processId\"\r\n                             :placeholder=\"'进度'\" @returnData=\"scope.row.sqdProcessEnName=$event.processEnName\"\r\n                             :process-type=\"processType\" :service-type=\"serviceType\"\r\n                             @progressName=\"scope.row.processId=$event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"quotationChargeId\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showStatus\" @click=\"scope.row.showStatus = true\">\r\n                {{ scope.row.basProcessStatus.processStatusShortName }}\r\n                {{ scope.row.processStatusTime }}\r\n              </div>\r\n              <div v-if=\"scope.row.showStatus\" style=\"display: flex\">\r\n                <progress-status :pass=\"scope.row.processStatusId\" :placeholder=\"'物流进度'\"\r\n                                 style=\"flex: 1\" @progressStatus=\"scope.row.processStatusId=$event\"\r\n                />\r\n                <el-date-picker\r\n                  v-if=\"scope.row.showStatus\"\r\n                  v-model=\"scope.row.processStatusTime\"\r\n                  placeholder=\"选择日期时间\"\r\n                  style=\"flex: 2\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\">\r\n                </el-date-picker>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                :disabled=\"disabled\"\r\n                @click=\"deleteLogisticsProgress(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n      <el-button style=\"padding: 0\" type=\"text\"\r\n                 @click=\"addLogisticsProgress\"\r\n                 :disabled=\"disabled\"\r\n      >[＋]\r\n      </el-button>\r\n    </el-col>\r\n  </div>\r\n</template>\r\n<script>\r\nimport ProgressStatus from '@/components/ProgressStatus/index.vue'\r\nimport ProgressName from '@/components/ProgressName/index.vue'\r\nimport moment from 'moment'\r\n\r\nexport default {\r\n  name: 'logisticsProgress',\r\n  components: { ProgressName, ProgressStatus },\r\n  props: ['logisticsProgressData', 'openLogisticsProgressList', 'disabled', 'serviceType', 'processType'],\r\n  watch: {\r\n    logisticsProgressData(n) {\r\n      this.$emit('return', n)\r\n    }\r\n  },\r\n  methods: {\r\n    /** 序号 */\r\n    rowIndex({ row, rowIndex }) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addLogisticsProgress() {\r\n      let obj = {\r\n        showProgress: true,\r\n        showStatus: true,\r\n        processStatusId: 7,\r\n        processStatusTime: moment().format('yyyy-MM-DD HH:mm:ss'),\r\n        opId: this.$store.state.user.sid,\r\n      }\r\n      this.logisticsProgressData.push(obj)\r\n    },\r\n    deleteLogisticsProgress(row) {\r\n      this.$emit('deleteItem', row)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;AAgEA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,OAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAG,IAAA;EACAC,UAAA;IAAAC,YAAA,EAAAA,eAAA;IAAAC,cAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,KAAA;IACAC,qBAAA,WAAAA,sBAAAC,CAAA;MACA,KAAAC,KAAA,WAAAD,CAAA;IACA;EACA;EACAE,OAAA;IACA,SACAC,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAF,QAAA,GAAAC,IAAA,CAAAD,QAAA;MACAE,GAAA,CAAAC,EAAA,GAAAH,QAAA;IACA;IACAI,oBAAA,WAAAA,qBAAA;MACA,IAAAC,GAAA;QACAC,YAAA;QACAC,UAAA;QACAC,eAAA;QACAC,iBAAA,MAAAC,eAAA,IAAAC,MAAA;QACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC;MACA;MACA,KAAApB,qBAAA,CAAAqB,IAAA,CAAAZ,GAAA;IACA;IACAa,uBAAA,WAAAA,wBAAAhB,GAAA;MACA,KAAAJ,KAAA,eAAAI,GAAA;IACA;EACA;AACA;AAAAiB,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}