{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\CodeTypeDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\CodeTypeDialog.vue", "mtime": 1754876882602}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["inheritAttrs", "props", "data", "formData", "fileName", "undefined", "type", "rules", "required", "message", "trigger", "typeOptions", "label", "value", "computed", "watch", "mounted", "methods", "onOpen", "showFileName", "concat", "Date", "onClose", "close", "e", "$emit", "handleConfirm", "_this", "$refs", "elForm", "validate", "valid", "_objectSpread2", "default", "exports", "_default"], "sources": ["src/views/tool/build/CodeTypeDialog.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-bind=\"$attrs\"\r\n      width=\"500px\"\r\n      @close=\"onClose\"\r\n      @open=\"onOpen\"\r\n      v-on=\"$listeners\"\r\n    >\r\n      <el-row :gutter=\"15\">\r\n        <el-form\r\n          ref=\"elForm\"\r\n          :model=\"formData\"\r\n          :rules=\"rules\"\r\n          label-width=\"100px\"\r\n          size=\"medium\"\r\n        >\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"生成类型\" prop=\"type\">\r\n              <el-radio-group v-model=\"formData.type\">\r\n                <el-radio-button\r\n                  v-for=\"(item, index) in typeOptions\"\r\n                  :key=\"index\"\r\n                  :disabled=\"item.disabled\"\r\n                  :label=\"item.value\"\r\n                >\r\n                  {{ item.label }}\r\n                </el-radio-button>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n            <el-form-item v-if=\"showFileName\" label=\"文件名\" prop=\"fileName\">\r\n              <el-input v-model=\"formData.fileName\" clearable placeholder=\"文件名\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"close\">\r\n          取消\r\n        </el-button>\r\n        <el-button type=\"primary\" @click=\"handleConfirm\">\r\n          确定\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  inheritAttrs: false,\r\n  props: ['showFileName'],\r\n  data() {\r\n    return {\r\n      formData: {\r\n        fileName: undefined,\r\n        type: 'file'\r\n      },\r\n      rules: {\r\n        fileName: [{\r\n          required: true,\r\n          message: '文件名',\r\n          trigger: 'blur'\r\n        }],\r\n        type: [{\r\n          required: true,\r\n          message: '生成类型不能为空',\r\n          trigger: 'change'\r\n        }]\r\n      },\r\n      typeOptions: [{\r\n        label: '页面',\r\n        value: 'file'\r\n      }, {\r\n        label: '弹窗',\r\n        value: 'dialog'\r\n      }]\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {},\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    onOpen() {\r\n      if (this.showFileName) {\r\n        this.formData.fileName = `${+new Date()}.vue`\r\n      }\r\n    },\r\n    onClose() {\r\n    },\r\n    close(e) {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    handleConfirm() {\r\n      this.$refs.elForm.validate(valid => {\r\n        if (!valid) return\r\n        this.$emit('confirm', {...this.formData})\r\n        this.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAmDA;EACAA,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;QACAC,QAAA,EAAAC,SAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAH,QAAA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;QACAJ,IAAA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MACA;MACAC,WAAA;QACAC,KAAA;QACAC,KAAA;MACA;QACAD,KAAA;QACAC,KAAA;MACA;IACA;EACA;EACAC,QAAA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,SAAAC,YAAA;QACA,KAAAhB,QAAA,CAAAC,QAAA,MAAAgB,MAAA,MAAAC,IAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAA,GACA;IACAC,KAAA,WAAAA,MAAAC,CAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACAJ,KAAA,CAAAF,KAAA,gBAAAO,cAAA,CAAAC,OAAA,MAAAN,KAAA,CAAAxB,QAAA;QACAwB,KAAA,CAAAJ,KAAA;MACA;IACA;EACA;AACA;AAAAW,OAAA,CAAAD,OAAA,GAAAE,QAAA"}]}