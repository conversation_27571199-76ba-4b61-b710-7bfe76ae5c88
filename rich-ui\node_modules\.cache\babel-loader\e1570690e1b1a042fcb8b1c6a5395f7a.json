{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\AirComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\AirComponent.vue", "mtime": 1754881964225}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "_vueTreeselect", "_debitNodeList", "name", "components", "DebitNoteList", "Audit", "LogisticsProgress", "ChargeList", "Treeselect", "props", "airList", "type", "Array", "default", "_default", "form", "Object", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "carrierList", "companyList", "carrierNormalizer", "Function", "data", "bookingBillConfig", "file", "templateList", "computed", "isDisabled", "methods", "handleAddDebitNote", "serviceObject", "row", "sqdRctNo", "rctNo", "rctId", "isRecievingOrPaying", "rsChargeList", "$emit", "getSupplierEmail", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "serviceInstance", "agreementTypeCode", "agreementNo", "handleBookingBill", "item", "template", "changeServiceFold", "addAir", "deleteRsOpAir", "openChargeSelect", "auditCharge", "event", "getBookingBill", "generateFreight", "type1", "type2", "selectPsaBookingOpen", "selectCarrier", "addProgress", "logList", "psaBookingCancel", "copyFreight", "calculateCharge", "serviceType", "getPayable", "$parent", "getBookingStatus", "status", "getServiceInstanceDisable", "getServiceObject", "serviceTypeId", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/AirComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"air-component\">\r\n    <!--空运-->\r\n    <div v-for=\"(item, index) in airList\" :key=\"`air-${index}`\" class=\"air-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                空运-AIR\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addAir\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpAir(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)\"\r\n              :payable=\"getPayable(10)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item,$event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"handleBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  :class=\"{ 'disable-form': form.sqdPsaNo }\"\r\n                  :disabled=\"!!form.sqdPsaNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(2, 10, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"订舱状态\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input\r\n                      :value=\"getBookingStatus(item.bookingStatus)\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                      placeholder=\"订舱状态\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                      class=\"cancel-btn\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel(item)\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\" prop=\"supplierId\">\r\n                    <el-input :class=\"item.sqdPsaNo?'disable-form':''\" :disabled=\"item.sqdPsaNo?true:false\"\r\n                              :value=\"item.sqdPsaNo\"\r\n                              @focus=\"selectPsaBookingOpen(item)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"BKG号码\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.soNo\"\r\n                              :class=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"BKG号码\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"提单号码\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.blNo\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"提单号码\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"航空公司\" prop=\"carrierIds\">\r\n                    <treeselect v-model=\"item.carrierId\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disable-branch-nodes=\"true\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                :disabled-fuzzy-matching=\"true\" :flat=\"false\"\r\n                                :flatten-search-results=\"true\" :multiple=\"false\"\r\n                                :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                                placeholder=\"选择承运人\" @select=\"selectCarrier(item,$event)\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          (node.raw.carrierIntlCode != null) ? node.raw.carrierIntlCode : \" \"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程航班\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.firstVessel\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"头程航班\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"二程航班\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.basicVessel\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"二程航班\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"航班时效\" style=\"padding-right: 0;\">\r\n                    <el-input v-model=\"item.inquiryScheduleSummary\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"航班时效\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程截重\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.firstCyClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              clearable placeholder=\"头程截重\"\r\n                              style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截关时间\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.cvClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"截关时间\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD1\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.etd\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ETD1\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD2\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.basicFinalGateinTime\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ETD2\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ETA\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.eta\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ETA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截补料\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.siClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"截补料\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"AMS/ENS\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.sqdAmsEnsPostStatus\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"AMS/ENS\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ATD1\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"item.podEta\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATD1\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    @change=\"addProgress(getServiceObject(10).rsOpLogList,15)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ATD2\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"item.basicEtd\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATD2\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ATA\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"item.destinationPortEta\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    @change=\"addProgress(getServiceObject(10).rsOpLogList,18)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"19\">\r\n                  <el-form-item label=\"订舱备注\" prop=\"revenueTons\">\r\n                    <div style=\"display: flex\">\r\n                      <el-input v-model=\"item.bookingChargeRemark\"\r\n                                :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                placeholder=\"订舱费用备注\" type=\"textarea\"\r\n                      />\r\n                      <el-input v-model=\"item.bookingAgentRemark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                placeholder=\"订舱备注\"\r\n                                type=\"textarea\"\r\n                      />\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getServiceInstanceDisable(item.rsServiceInstances) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\" :process-type=\"4\" :service-type=\"10\"\r\n                @deleteItem=\"item.rsOpLogList=item.rsOpLogList.filter(item=>{return item!=$event})\"\r\n                @return=\"item.rsOpLogList=$event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list :a-t-d=\"form.podEta\"\r\n                         :charge-data=\"item.rsChargeList\"\r\n                         :company-list=\"companyList\"\r\n                         :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)|| disabled\" :hiddenSupplier=\"booking\"\r\n                         :is-receivable=\"false\" :open-charge-list=\"true\"\r\n                         :pay-detail-r-m-b=\"item.payableRMB\"\r\n                         :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n                         :pay-detail-u-s-d=\"item.payableUSD\"\r\n                         :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n                         :service-type-id=\"10\" @copyFreight=\"copyFreight($event)\"\r\n                         @deleteAll=\"item.rsChargeList=[]\"\r\n                         @deleteItem=\"item.rsChargeList=item.rsChargeList.filter(item=>{return item!=$event})\"\r\n                         @return=\"calculateCharge(10,$event,item)\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"item.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(item)\"\r\n              @deleteItem=\"item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(10,$event,item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"AirComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 空运数据列表\r\n    airList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    carrierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 方法函数\r\n    carrierNormalizer: {\r\n      type: Function,\r\n      default: () => {\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理订舱单生成\r\n    handleBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addAir() {\r\n      this.$emit(\"addAir\")\r\n    },\r\n    deleteRsOpAir(item) {\r\n      this.$emit(\"deleteRsOpAir\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    getBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    selectCarrier(item, event) {\r\n      this.$emit(\"selectCarrier\", item, event)\r\n    },\r\n    addProgress(logList, type) {\r\n      this.$emit(\"addProgress\", logList, type)\r\n    },\r\n    psaBookingCancel(item) {\r\n      this.$emit(\"psaBookingCancel\", item)\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(type) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(type) : null\r\n    },\r\n    getBookingStatus(status) {\r\n      return this.$parent.getBookingStatus ? this.$parent.getBookingStatus(status) : \"\"\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.$parent.getServiceObject ? this.$parent.getServiceObject(serviceTypeId) : {}\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Air组件特定样式\r\n.air-component {\r\n  width: 100%;\r\n\r\n  .air-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .cancel-btn {\r\n        color: red;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAyaA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACA,IAAAI,cAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAK,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,OAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,IAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,UAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,aAAA;MACAR,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAQ,SAAA;MACAV,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAX,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAZ,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAW,SAAA;MACAb,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAY,YAAA;MACAd,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAY,WAAA;MACAf,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAa,WAAA;MACAhB,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAc,iBAAA;MACAjB,IAAA,EAAAkB,QAAA;MACAhB,OAAA,WAAAC,SAAA,GACA;IACA;EACA;EACAgB,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;QACAC,IAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAb,QAAA,SAAAE,SAAA;IACA;EACA;EACAY,OAAA;IACAC,kBAAA,WAAAA,mBAAAC,aAAA;MACA,IAAAC,GAAA;MACAA,GAAA,CAAAC,QAAA,QAAAzB,IAAA,CAAA0B,KAAA;MACAF,GAAA,CAAAG,KAAA,QAAA3B,IAAA,CAAA2B,KAAA;MACAH,GAAA,CAAAI,mBAAA;MACAJ,GAAA,CAAAK,YAAA;MACA,KAAAC,KAAA,iBAAAN,GAAA,EAAAD,aAAA;IACA;IACA;IACAQ,gBAAA,WAAAA,iBAAAC,UAAA;MACA,IAAAC,QAAA,QAAAvB,YAAA,CAAAwB,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAJ,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,eAAA;MACA,OAAAA,eAAA,CAAAC,iBAAA,GAAAD,eAAA,CAAAE,WAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA,EAAAC,QAAA;MACA,KAAAd,KAAA,mBAAAa,IAAA,EAAAC,QAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAN,eAAA;MACA,KAAAT,KAAA,sBAAAS,eAAA;IACA;IACAO,MAAA,WAAAA,OAAA;MACA,KAAAhB,KAAA;IACA;IACAiB,aAAA,WAAAA,cAAAJ,IAAA;MACA,KAAAb,KAAA,kBAAAa,IAAA;IACA;IACAK,gBAAA,WAAAA,iBAAAL,IAAA;MACA,KAAAb,KAAA,qBAAAa,IAAA;IACA;IACAM,WAAA,WAAAA,YAAAN,IAAA,EAAAO,KAAA;MACA,KAAApB,KAAA,gBAAAa,IAAA,EAAAO,KAAA;IACA;IACAC,cAAA,WAAAA,eAAAR,IAAA,EAAAC,QAAA;MACA,KAAAd,KAAA,mBAAAa,IAAA,EAAAC,QAAA;IACA;IACAQ,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAX,IAAA;MACA,KAAAb,KAAA,oBAAAuB,KAAA,EAAAC,KAAA,EAAAX,IAAA;IACA;IACAY,oBAAA,WAAAA,qBAAAZ,IAAA;MACA,KAAAb,KAAA,yBAAAa,IAAA;IACA;IACAa,aAAA,WAAAA,cAAAb,IAAA,EAAAO,KAAA;MACA,KAAApB,KAAA,kBAAAa,IAAA,EAAAO,KAAA;IACA;IACAO,WAAA,WAAAA,YAAAC,OAAA,EAAA9D,IAAA;MACA,KAAAkC,KAAA,gBAAA4B,OAAA,EAAA9D,IAAA;IACA;IACA+D,gBAAA,WAAAA,iBAAAhB,IAAA;MACA,KAAAb,KAAA,qBAAAa,IAAA;IACA;IACAiB,WAAA,WAAAA,YAAAV,KAAA;MACA,KAAApB,KAAA,gBAAAoB,KAAA;IACA;IACAW,eAAA,WAAAA,gBAAAC,WAAA,EAAAZ,KAAA,EAAAP,IAAA;MACA,KAAAb,KAAA,oBAAAgC,WAAA,EAAAZ,KAAA,EAAAP,IAAA;IACA;IACAoB,UAAA,WAAAA,WAAAnE,IAAA;MACA,YAAAoE,OAAA,CAAAD,UAAA,QAAAC,OAAA,CAAAD,UAAA,CAAAnE,IAAA;IACA;IACAqE,gBAAA,WAAAA,iBAAAC,MAAA;MACA,YAAAF,OAAA,CAAAC,gBAAA,QAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA;IACA;IACAC,yBAAA,WAAAA,0BAAA5B,eAAA;MACA,YAAAyB,OAAA,CAAAG,yBAAA,QAAAH,OAAA,CAAAG,yBAAA,CAAA5B,eAAA;IACA;IACA6B,gBAAA,WAAAA,iBAAAC,aAAA;MACA,YAAAL,OAAA,CAAAI,gBAAA,QAAAJ,OAAA,CAAAI,gBAAA,CAAAC,aAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAxE,OAAA,GAAAyE,SAAA"}]}