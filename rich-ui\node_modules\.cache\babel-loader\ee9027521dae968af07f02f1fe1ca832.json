{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opHistory.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opHistory.vue", "mtime": 1754876882584}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_rich", "require", "_operationalprocess", "_docdetail", "name", "props", "data", "open", "doc", "dList", "receive", "send", "watch", "opHistory", "n", "$emit", "methods", "handleAddDocList", "row", "operationalProcessId", "docId", "flowNo", "docFlowDirectionId", "issueTypeId", "fileList", "createBy", "$store", "state", "user", "sid", "createByName", "split", "createTime", "parseTime", "Date", "remark", "updateTime", "checkDoc", "_this", "getDocdetail", "docDetailId", "then", "res", "getSender", "val", "senderId", "receiverId", "getReceiver", "handleSend", "addOpHistory", "_this2", "opHistoryForm", "docList", "typeId", "rctId", "basicInfoId", "processId", "processStatusId", "releaseWayId", "processStatusTime", "opId", "opName", "addOperationalprocess", "push", "delOpHistory", "_this3", "delOperationalprocess", "$message", "success", "msg", "filter", "item", "submitDoc", "_this4", "addDocdetail", "console", "log", "updateDocdetail", "delDoc", "_this5", "delDocdetail", "v", "cancel", "exports", "default", "_default"], "sources": ["src/views/system/document/opHistory.vue"], "sourcesContent": ["<template>\r\n  <el-col :span=\"16\" :style=\"{'display':openOpHistory?'':'none'}\">\r\n    <div class=\"titleStyle\">\r\n      <div class=\"titleText\">操作历史记录（文件管理）</div>\r\n    </div>\r\n    <div :class=\"{'inactive':openOpHistory==false,'active':openOpHistory}\">\r\n      <el-table :data=\"opHistory\" border class=\"pd0\">\r\n        <el-table-column align=\"center\" label=\"操作进度流水\" prop=\"operationalProcessId\" show-tooltip-when-overflow\r\n                         width=\"80px\"/>\r\n        <el-table-column align=\"center\" label=\"进度名称\" prop=\"processId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.processId\"\r\n                         :placeholder=\"'进度状态'\" :type=\"'process'\"\r\n                         @return=\"scope.row.processId=$event\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"进度状态\" prop=\"processStatusId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.processStatusId\"\r\n                         :placeholder=\"'进度状态'\" :type=\"'processStatus'\"\r\n                         @return=\"scope.row.processStatusId=$event\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"发送方\" prop=\"senderId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-select v-model=\"scope.row.senderId\" :placeholder=\"'发送方'\" filterable\r\n                       @change=\"getSender($event,scope.row)\">\r\n              <el-option v-for=\"c in $store.state.data.companyList\" :key=\"c.companyId\" :label=\"c.companyShortName\"\r\n                         :value=\"c.companyId\"/>\r\n            </el-select>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"接收方\" prop=\"receiverId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-select v-model=\"scope.row.receiverId\" filterable placeholder=\"接收方\"\r\n                       @change=\"getReceiver($event,scope.row)\">\r\n              <el-option v-for=\"c in $store.state.data.companyList\" :key=\"c.companyId\" :label=\"c.companyShortName\"\r\n                         :value=\"c.companyId\"/>\r\n            </el-select>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"随附文件列表\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-for=\"doc in scope.row.docList\"\r\n                       :key=\"doc.docDetailId\"\r\n                       size=\"small\"\r\n                       style=\"padding: 0;\"\r\n                       type=\"text\"\r\n                       @click=\"checkDoc(doc,scope.row)\">\r\n              {{ '[' + doc.flowNo + ']' }}\r\n            </el-button>\r\n            <el-button size=\"mini\" style=\"padding: 0\" type=\"text\"\r\n                       @click=\"handleAddDocList(scope.row)\">\r\n              [＋]\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"交互方式\" prop=\"releaseWayId\" width=\"68px\">\r\n          <template slot-scope=\"scope\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"scope.row.releaseWayId\" :placeholder=\"'交互方式'\"\r\n                         :type=\"'docReleaseWay'\" @return=\"scope.row.releaseWayId=$event\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"发生时间\" prop=\"processStatusTime\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <el-date-picker v-model=\"scope.row.processStatusTime\"\r\n                            clearable\r\n                            placeholder=\"进度发生时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\">\r\n            </el-date-picker>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"备注\" prop=\"remark\">\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.remark\" placeholder=\"备注\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"发送\" width=\"50px\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button icon=\"el-icon-s-promotion\" size=\"mini\" style=\"padding: 0\" type=\"primary\"\r\n                       @click=\"handleSend\">Send\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"操作员\" prop=\"opName\" width=\"50px\"></el-table-column>\r\n        <el-table-column align=\"center\" label=\"录入时间\" prop=\"createTime\" width=\"80\">\r\n          <template slot-scope=\"scope\">\r\n            {{ parseTime(scope.row.createTime, '{y}/{m}/{d}') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button icon=\"el-icon-delete\" size=\"mini\" type=\"danger\"\r\n                       @click=\"delOpHistory(scope.row)\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-button style=\"padding: 0\" type=\"text\"\r\n                 @click=\"addOpHistory\">[＋]\r\n      </el-button>\r\n      <el-dialog v-dialogDrag v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n                 :visible.sync=\"open\" append-to-body title=\"新增操作历史记录\" width=\"500px\">\r\n        <el-form ref=\"docList\" :model=\"doc\" border label-width=\"68px\">\r\n          <el-form-item label=\"流向编号\" prop=\"flowNo\">\r\n            <el-input v-model=\"doc.flowNo\" placeholder=\"文件名/流向编号\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件类型\" prop=\"docId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"doc.docId\" :placeholder=\"'文件类型'\"\r\n                         :type=\"'doc'\" @return=\"doc.docId=$event\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件流向\" prop=\"docFlowDirectionId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"doc.docFlowDirectionId\" :placeholder=\"'文件流向'\"\r\n                         :type=\"'docFlowDirection'\" @return=\"doc.docFlowDirectionId=$event\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"文件形式\" prop=\"issueTypeId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"doc.issueTypeId\"\r\n                         :placeholder=\"'文件形式'\"\r\n                         :type=\"'docIssueType'\" @return=\"doc.issueTypeId=$event\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建时间\" prop=\"createTime\">\r\n            {{ parseTime(doc.createTime, '{y}/{m}/{d}') }}\r\n          </el-form-item>\r\n          <el-form-item label=\"备注\" prop=\"remark\">\r\n            <el-input v-model=\"doc.remark\" :autosize=\"{ minRows: 1}\" maxlength=\"300\"\r\n                      placeholder=\"备注\" show-word-limit type=\"textarea\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"创建人\" prop=\"createByName\">\r\n            {{ doc.createByName }}\r\n          </el-form-item>\r\n          <el-form-item label=\"录入时间\" prop=\"updateTime\">\r\n            {{ parseTime(doc.updateTime, '{y}/{m}/{d}') }}\r\n          </el-form-item>\r\n          <file-upload :file-type=\"['xlsx','xls','docx','doc','pdf']\" :limit=\"3\" :value=\"doc.fileList\"\r\n                       @input=\"doc.fileList=$event\"/>\r\n        </el-form>\r\n        <div slot=\"footer\" class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"submitDoc\">确 定</el-button>\r\n          <el-button type=\"danger\" @click=\"delDoc\">删 除</el-button>\r\n          <el-button @click=\"cancel\">取 消</el-button>\r\n        </div>\r\n      </el-dialog>\r\n    </div>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nimport {parseTime} from \"@/utils/rich\";\r\nimport {addOperationalprocess, delOperationalprocess} from \"@/api/system/operationalprocess\";\r\nimport {addDocdetail, delDocdetail, getDocdetail, updateDocdetail} from \"@/api/system/docdetail\";\r\n\r\nexport default {\r\n  name: \"opHistory\",\r\n  props: ['opHistory', 'openOpHistory', 'typeId', 'rctId', 'basicInfoId'],\r\n  data() {\r\n    return {\r\n      open: false,\r\n      doc: {},\r\n      dList: {},\r\n      receive: false,\r\n      send: false\r\n    }\r\n  },\r\n  watch: {\r\n    opHistory(n) {\r\n      this.$emit('return', n)\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDocList(row) {\r\n      this.open = true\r\n      this.doc = {\r\n        operationalProcessId: row.operationalProcessId,\r\n        docId: null,\r\n        flowNo: null,\r\n        docFlowDirectionId: null,\r\n        issueTypeId: null,\r\n        fileList: null,\r\n        createBy: this.$store.state.user.sid,\r\n        createByName: this.$store.state.user.name.split(\" \")[1],\r\n        createTime: parseTime(new Date()),\r\n        remark: null,\r\n        updateTime: parseTime(new Date()),\r\n      }\r\n      this.dList = row\r\n    },\r\n    checkDoc(doc, row) {\r\n      getDocdetail(doc.docDetailId).then(res => {\r\n        this.doc = res.data\r\n        this.open = true\r\n      })\r\n      this.dList = row\r\n    },\r\n    getSender(val, row) {\r\n      this.send = true\r\n      this.receive = false\r\n      if (!this.receive) {\r\n        row.senderId = val\r\n        row.receiverId = 1\r\n      }\r\n    },\r\n    getReceiver(val, row) {\r\n      this.receive = true\r\n      this.send = false\r\n      if (!this.send) {\r\n        row.receiverId = val\r\n        row.senderId = 1\r\n      }\r\n    },\r\n    handleSend() {\r\n\r\n    },\r\n    addOpHistory() {\r\n      let opHistoryForm = {\r\n        docList: [],\r\n        typeId: this.typeId,\r\n        rctId: this.rctId,\r\n        basicInfoId: this.basicInfoId,\r\n        processId: null,\r\n        processStatusId: null,\r\n        senderId: null,\r\n        receiverId: null,\r\n        releaseWayId: null,\r\n        processStatusTime: parseTime(new Date()),\r\n        remark: null,\r\n        opId: this.$store.state.user.sid,\r\n        opName: this.$store.state.user.name.split(\" \")[1],\r\n        createTime: parseTime(new Date()),\r\n      }\r\n      addOperationalprocess(opHistoryForm).then(res => {\r\n        this.opHistory.push(opHistoryForm)\r\n      })\r\n    },\r\n    delOpHistory(row) {\r\n      let opHistoryForm = {\r\n        operationalProcessId: row.operationalProcessId,\r\n        typeId: row.typeId,\r\n        rctId: row.rctId,\r\n        basicInfoId: row.basicInfoId,\r\n      }\r\n      delOperationalprocess(opHistoryForm).then(res => {\r\n        this.$message.success(res.msg)\r\n        this.opHistory = this.opHistory.filter(item => {\r\n          return item.operationalProcessId != row.operationalProcessId\r\n        })\r\n      })\r\n    },\r\n    submitDoc() {\r\n      if (this.doc.docDetailId == null) {\r\n        addDocdetail(this.doc).then(res => {\r\n          if (this.dList.docList == null) {\r\n            this.dList.docList = []\r\n          }\r\n          this.dList.docList.push(this.doc)\r\n          console.log(this.doc)\r\n        })\r\n      } else {\r\n        updateDocdetail(this.doc).then(res => {\r\n          this.$message.success(\"修改成功\")\r\n          console.log(this.doc)\r\n        })\r\n      }\r\n      this.open = false\r\n    },\r\n    delDoc() {\r\n      if (this.doc.docDetailId != null) {\r\n        delDocdetail(this.doc.docDetailId).then(res => {\r\n          this.$message.success(\"删除成功\")\r\n          this.dList.docList = this.dList.docList.filter(v => {\r\n            return v.docDetailId != this.doc.docDetailId\r\n          })\r\n          this.doc = {}\r\n        })\r\n      }\r\n    },\r\n    cancel() {\r\n      this.open = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAwJA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,mBAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAG,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAC,GAAA;MACAC,KAAA;MACAC,OAAA;MACAC,IAAA;IACA;EACA;EACAC,KAAA;IACAC,SAAA,WAAAA,UAAAC,CAAA;MACA,KAAAC,KAAA,WAAAD,CAAA;IACA;EACA;EACAE,OAAA;IACAC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAX,IAAA;MACA,KAAAC,GAAA;QACAW,oBAAA,EAAAD,GAAA,CAAAC,oBAAA;QACAC,KAAA;QACAC,MAAA;QACAC,kBAAA;QACAC,WAAA;QACAC,QAAA;QACAC,QAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACAC,YAAA,OAAAJ,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAxB,IAAA,CAAA2B,KAAA;QACAC,UAAA,MAAAC,eAAA,MAAAC,IAAA;QACAC,MAAA;QACAC,UAAA,MAAAH,eAAA,MAAAC,IAAA;MACA;MACA,KAAAzB,KAAA,GAAAS,GAAA;IACA;IACAmB,QAAA,WAAAA,SAAA7B,GAAA,EAAAU,GAAA;MAAA,IAAAoB,KAAA;MACA,IAAAC,uBAAA,EAAA/B,GAAA,CAAAgC,WAAA,EAAAC,IAAA,WAAAC,GAAA;QACAJ,KAAA,CAAA9B,GAAA,GAAAkC,GAAA,CAAApC,IAAA;QACAgC,KAAA,CAAA/B,IAAA;MACA;MACA,KAAAE,KAAA,GAAAS,GAAA;IACA;IACAyB,SAAA,WAAAA,UAAAC,GAAA,EAAA1B,GAAA;MACA,KAAAP,IAAA;MACA,KAAAD,OAAA;MACA,UAAAA,OAAA;QACAQ,GAAA,CAAA2B,QAAA,GAAAD,GAAA;QACA1B,GAAA,CAAA4B,UAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAAH,GAAA,EAAA1B,GAAA;MACA,KAAAR,OAAA;MACA,KAAAC,IAAA;MACA,UAAAA,IAAA;QACAO,GAAA,CAAA4B,UAAA,GAAAF,GAAA;QACA1B,GAAA,CAAA2B,QAAA;MACA;IACA;IACAG,UAAA,WAAAA,WAAA,GAEA;IACAC,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,aAAA;QACAC,OAAA;QACAC,MAAA,OAAAA,MAAA;QACAC,KAAA,OAAAA,KAAA;QACAC,WAAA,OAAAA,WAAA;QACAC,SAAA;QACAC,eAAA;QACAZ,QAAA;QACAC,UAAA;QACAY,YAAA;QACAC,iBAAA,MAAA1B,eAAA,MAAAC,IAAA;QACAC,MAAA;QACAyB,IAAA,OAAAlC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACAgC,MAAA,OAAAnC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAxB,IAAA,CAAA2B,KAAA;QACAC,UAAA,MAAAC,eAAA,MAAAC,IAAA;MACA;MACA,IAAA4B,yCAAA,EAAAX,aAAA,EAAAV,IAAA,WAAAC,GAAA;QACAQ,MAAA,CAAArC,SAAA,CAAAkD,IAAA,CAAAZ,aAAA;MACA;IACA;IACAa,YAAA,WAAAA,aAAA9C,GAAA;MAAA,IAAA+C,MAAA;MACA,IAAAd,aAAA;QACAhC,oBAAA,EAAAD,GAAA,CAAAC,oBAAA;QACAkC,MAAA,EAAAnC,GAAA,CAAAmC,MAAA;QACAC,KAAA,EAAApC,GAAA,CAAAoC,KAAA;QACAC,WAAA,EAAArC,GAAA,CAAAqC;MACA;MACA,IAAAW,yCAAA,EAAAf,aAAA,EAAAV,IAAA,WAAAC,GAAA;QACAuB,MAAA,CAAAE,QAAA,CAAAC,OAAA,CAAA1B,GAAA,CAAA2B,GAAA;QACAJ,MAAA,CAAApD,SAAA,GAAAoD,MAAA,CAAApD,SAAA,CAAAyD,MAAA,WAAAC,IAAA;UACA,OAAAA,IAAA,CAAApD,oBAAA,IAAAD,GAAA,CAAAC,oBAAA;QACA;MACA;IACA;IACAqD,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,SAAAjE,GAAA,CAAAgC,WAAA;QACA,IAAAkC,uBAAA,OAAAlE,GAAA,EAAAiC,IAAA,WAAAC,GAAA;UACA,IAAA+B,MAAA,CAAAhE,KAAA,CAAA2C,OAAA;YACAqB,MAAA,CAAAhE,KAAA,CAAA2C,OAAA;UACA;UACAqB,MAAA,CAAAhE,KAAA,CAAA2C,OAAA,CAAAW,IAAA,CAAAU,MAAA,CAAAjE,GAAA;UACAmE,OAAA,CAAAC,GAAA,CAAAH,MAAA,CAAAjE,GAAA;QACA;MACA;QACA,IAAAqE,0BAAA,OAAArE,GAAA,EAAAiC,IAAA,WAAAC,GAAA;UACA+B,MAAA,CAAAN,QAAA,CAAAC,OAAA;UACAO,OAAA,CAAAC,GAAA,CAAAH,MAAA,CAAAjE,GAAA;QACA;MACA;MACA,KAAAD,IAAA;IACA;IACAuE,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,SAAAvE,GAAA,CAAAgC,WAAA;QACA,IAAAwC,uBAAA,OAAAxE,GAAA,CAAAgC,WAAA,EAAAC,IAAA,WAAAC,GAAA;UACAqC,MAAA,CAAAZ,QAAA,CAAAC,OAAA;UACAW,MAAA,CAAAtE,KAAA,CAAA2C,OAAA,GAAA2B,MAAA,CAAAtE,KAAA,CAAA2C,OAAA,CAAAkB,MAAA,WAAAW,CAAA;YACA,OAAAA,CAAA,CAAAzC,WAAA,IAAAuC,MAAA,CAAAvE,GAAA,CAAAgC,WAAA;UACA;UACAuC,MAAA,CAAAvE,GAAA;QACA;MACA;IACA;IACA0E,MAAA,WAAAA,OAAA;MACA,KAAA3E,IAAA;IACA;EACA;AACA;AAAA4E,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}