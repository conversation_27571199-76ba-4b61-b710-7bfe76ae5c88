{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\cache\\list.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\cache\\list.vue", "mtime": 1754876882564}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBjbGVhckNhY2hlQWxsLA0KICBjbGVhckNhY2hlS2V5LA0KICBjbGVhckNhY2hlTmFtZSwNCiAgZ2V0Q2FjaGVWYWx1ZSwNCiAgbGlzdENhY2hlS2V5LA0KICBsaXN0Q2FjaGVOYW1lDQp9IGZyb20gIkAvYXBpL21vbml0b3IvY2FjaGUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJDYWNoZUxpc3QiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBjYWNoZU5hbWVzOiBbXSwNCiAgICAgIGNhY2hlS2V5czogW10sDQogICAgICBjYWNoZUZvcm06IHt9LA0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIHN1YkxvYWRpbmc6IGZhbHNlLA0KICAgICAgbm93Q2FjaGVOYW1lOiAiIiwNCiAgICAgIHRhYmxlSGVpZ2h0OiB3aW5kb3cuaW5uZXJIZWlnaHQgLSAyMDANCiAgICB9Ow0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0Q2FjaGVOYW1lcygpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoue8k+WtmOWQjeensOWIl+ihqCAqLw0KICAgIGdldENhY2hlTmFtZXMoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdENhY2hlTmFtZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNhY2hlTmFtZXMgPSByZXNwb25zZS5kYXRhOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIt+aWsOe8k+WtmOWQjeensOWIl+ihqCAqLw0KICAgIHJlZnJlc2hDYWNoZU5hbWVzKCkgew0KICAgICAgdGhpcy5nZXRDYWNoZU5hbWVzKCk7DQogICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliLfmlrDnvJPlrZjliJfooajmiJDlip8iKTsNCiAgICB9LA0KICAgIC8qKiDmuIXnkIbmjIflrprlkI3np7DnvJPlrZggKi8NCiAgICBoYW5kbGVDbGVhckNhY2hlTmFtZShyb3cpIHsNCiAgICAgIGNsZWFyQ2FjaGVOYW1lKHJvdy5jYWNoZU5hbWUpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmuIXnkIbnvJPlrZjlkI3np7BbIiArIHJvdy5jYWNoZU5hbWUgKyAiXeaIkOWKnyIpOw0KICAgICAgICB0aGlzLmdldENhY2hlS2V5cygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5p+l6K+i57yT5a2Y6ZSu5ZCN5YiX6KGoICovDQogICAgZ2V0Q2FjaGVLZXlzKHJvdykgew0KICAgICAgY29uc3QgY2FjaGVOYW1lID0gcm93ICE9IHVuZGVmaW5lZCA/IHJvdy5jYWNoZU5hbWUgOiB0aGlzLm5vd0NhY2hlTmFtZTsNCiAgICAgIGlmIChjYWNoZU5hbWUgPT0gIiIpIHsNCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KICAgICAgdGhpcy5zdWJMb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RDYWNoZUtleShjYWNoZU5hbWUpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNhY2hlS2V5cyA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuc3ViTG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB0aGlzLm5vd0NhY2hlTmFtZSA9IGNhY2hlTmFtZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIt+aWsOe8k+WtmOmUruWQjeWIl+ihqCAqLw0KICAgIHJlZnJlc2hDYWNoZUtleXMoKSB7DQogICAgICB0aGlzLmdldENhY2hlS2V5cygpOw0KICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yi35paw6ZSu5ZCN5YiX6KGo5oiQ5YqfIik7DQogICAgfSwNCiAgICAvKiog5riF55CG5oyH5a6a6ZSu5ZCN57yT5a2YICovDQogICAgaGFuZGxlQ2xlYXJDYWNoZUtleShjYWNoZUtleSkgew0KICAgICAgY2xlYXJDYWNoZUtleShjYWNoZUtleSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIua4heeQhue8k+WtmOmUruWQjVsiICsgY2FjaGVLZXkgKyAiXeaIkOWKnyIpOw0KICAgICAgICB0aGlzLmdldENhY2hlS2V5cygpOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5YiX6KGo5YmN57yA5Y676ZmkICovDQogICAgbmFtZUZvcm1hdHRlcihyb3cpIHsNCiAgICAgIHJldHVybiByb3cuY2FjaGVOYW1lLnJlcGxhY2UoIjoiLCAiIik7DQogICAgfSwNCiAgICAvKiog6ZSu5ZCN5YmN57yA5Y676ZmkICovDQogICAga2V5Rm9ybWF0dGVyKGNhY2hlS2V5KSB7DQogICAgICByZXR1cm4gY2FjaGVLZXkucmVwbGFjZSh0aGlzLm5vd0NhY2hlTmFtZSwgIiIpOw0KICAgIH0sDQogICAgLyoqIOafpeivoue8k+WtmOWGheWuueivpue7hiAqLw0KICAgIGhhbmRsZUNhY2hlVmFsdWUoY2FjaGVLZXkpIHsNCiAgICAgIGdldENhY2hlVmFsdWUodGhpcy5ub3dDYWNoZU5hbWUsIGNhY2hlS2V5KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5jYWNoZUZvcm0gPSByZXNwb25zZS5kYXRhOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5riF55CG5YWo6YOo57yT5a2YICovDQogICAgaGFuZGxlQ2xlYXJDYWNoZUFsbCgpIHsNCiAgICAgIGNsZWFyQ2FjaGVBbGwoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5riF55CG5YWo6YOo57yT5a2Y5oiQ5YqfIik7DQogICAgICB9KTsNCiAgICB9DQogIH0sDQp9Ow0K"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"10\">\r\n      <el-col :span=\"8\">\r\n        <el-card style=\"height: calc(100vh - 125px)\">\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-collection\"></i>缓存列表</span>\r\n            <el-button\r\n              icon=\"el-icon-refresh-right\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"refreshCacheNames()\"\r\n            ></el-button>\r\n          </div>\r\n          <el-table\r\n            v-loading=\"loading\"\r\n            :data=\"cacheNames\"\r\n            :height=\"tableHeight\"\r\n            highlight-current-row\r\n            style=\"width: 100%\"\r\n            @row-click=\"getCacheKeys\"\r\n          >\r\n            <el-table-column\r\n              label=\"序号\"\r\n              type=\"index\"\r\n              width=\"60\"\r\n            ></el-table-column>\r\n\r\n            <el-table-column\r\n              :formatter=\"nameFormatter\"\r\n              :show-tooltip-when-overflow=\"true\"\r\n              align=\"center\"\r\n              label=\"缓存名称\"\r\n              prop=\"cacheName\"\r\n            ></el-table-column>\r\n\r\n            <el-table-column\r\n              :show-tooltip-when-overflow=\"true\"\r\n              align=\"center\"\r\n              label=\"备注\"\r\n              prop=\"remark\"\r\n            />\r\n            <el-table-column\r\n              align=\"center\"\r\n              class-name=\"small-padding fixed-width\"\r\n              label=\"操作\"\r\n              width=\"60\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  @click=\"handleClearCacheName(scope.row)\"\r\n                ></el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"8\">\r\n        <el-card style=\"height: calc(100vh - 125px)\">\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-key\"></i>键名列表</span>\r\n            <el-button\r\n              icon=\"el-icon-refresh-right\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"refreshCacheKeys()\"\r\n            ></el-button>\r\n          </div>\r\n          <el-table\r\n            v-loading=\"subLoading\"\r\n            :data=\"cacheKeys\"\r\n            :height=\"tableHeight\"\r\n            highlight-current-row\r\n            style=\"width: 100%\"\r\n            @row-click=\"handleCacheValue\"\r\n          >\r\n            <el-table-column\r\n              label=\"序号\"\r\n              type=\"index\"\r\n              width=\"60\"\r\n            ></el-table-column>\r\n            <el-table-column\r\n              :formatter=\"keyFormatter\"\r\n              :show-tooltip-when-overflow=\"true\"\r\n              align=\"center\"\r\n              label=\"缓存键名\"\r\n            >\r\n            </el-table-column>\r\n            <el-table-column\r\n              align=\"center\"\r\n              class-name=\"small-padding fixed-width\"\r\n              label=\"操作\"\r\n              width=\"60\"\r\n            >\r\n              <template slot-scope=\"scope\">\r\n                <el-button\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  type=\"text\"\r\n                  @click=\"handleClearCacheKey(scope.row)\"\r\n                ></el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"8\">\r\n        <el-card :bordered=\"false\" style=\"height: calc(100vh - 125px)\">\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-document\"></i> 缓存内容</span>\r\n            <el-button\r\n              icon=\"el-icon-refresh-right\"\r\n              style=\"float: right; padding: 3px 0\"\r\n              type=\"text\"\r\n              @click=\"handleClearCacheAll()\"\r\n            >清理全部\r\n            </el-button\r\n            >\r\n          </div>\r\n          <el-form :model=\"cacheForm\">\r\n            <el-row :gutter=\"32\">\r\n              <el-col :offset=\"1\" :span=\"22\">\r\n                <el-form-item label=\"缓存名称:\" prop=\"cacheName\">\r\n                  <el-input v-model=\"cacheForm.cacheName\" :readOnly=\"true\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :offset=\"1\" :span=\"22\">\r\n                <el-form-item label=\"缓存键名:\" prop=\"cacheKey\">\r\n                  <el-input v-model=\"cacheForm.cacheKey\" :readOnly=\"true\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :offset=\"1\" :span=\"22\">\r\n                <el-form-item label=\"缓存内容:\" prop=\"cacheValue\">\r\n                  <el-input\r\n                    v-model=\"cacheForm.cacheValue\"\r\n                    :autosize=\"{ minRows: 10, maxRows: 20}\"\r\n                    :readOnly=\"true\"\r\n                    :rows=\"8\"\r\n                    type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  clearCacheAll,\r\n  clearCacheKey,\r\n  clearCacheName,\r\n  getCacheValue,\r\n  listCacheKey,\r\n  listCacheName\r\n} from \"@/api/monitor/cache\";\r\n\r\nexport default {\r\n  name: \"CacheList\",\r\n  data() {\r\n    return {\r\n      cacheNames: [],\r\n      cacheKeys: [],\r\n      cacheForm: {},\r\n      loading: true,\r\n      subLoading: false,\r\n      nowCacheName: \"\",\r\n      tableHeight: window.innerHeight - 200\r\n    };\r\n  },\r\n  created() {\r\n    this.getCacheNames();\r\n  },\r\n  methods: {\r\n    /** 查询缓存名称列表 */\r\n    getCacheNames() {\r\n      this.loading = true;\r\n      listCacheName().then(response => {\r\n        this.cacheNames = response.data;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 刷新缓存名称列表 */\r\n    refreshCacheNames() {\r\n      this.getCacheNames();\r\n      this.$modal.msgSuccess(\"刷新缓存列表成功\");\r\n    },\r\n    /** 清理指定名称缓存 */\r\n    handleClearCacheName(row) {\r\n      clearCacheName(row.cacheName).then(response => {\r\n        this.$modal.msgSuccess(\"清理缓存名称[\" + row.cacheName + \"]成功\");\r\n        this.getCacheKeys();\r\n      });\r\n    },\r\n    /** 查询缓存键名列表 */\r\n    getCacheKeys(row) {\r\n      const cacheName = row != undefined ? row.cacheName : this.nowCacheName;\r\n      if (cacheName == \"\") {\r\n        return;\r\n      }\r\n      this.subLoading = true;\r\n      listCacheKey(cacheName).then(response => {\r\n        this.cacheKeys = response.data;\r\n        this.subLoading = false;\r\n        this.nowCacheName = cacheName;\r\n      });\r\n    },\r\n    /** 刷新缓存键名列表 */\r\n    refreshCacheKeys() {\r\n      this.getCacheKeys();\r\n      this.$modal.msgSuccess(\"刷新键名列表成功\");\r\n    },\r\n    /** 清理指定键名缓存 */\r\n    handleClearCacheKey(cacheKey) {\r\n      clearCacheKey(cacheKey).then(response => {\r\n        this.$modal.msgSuccess(\"清理缓存键名[\" + cacheKey + \"]成功\");\r\n        this.getCacheKeys();\r\n      });\r\n    },\r\n    /** 列表前缀去除 */\r\n    nameFormatter(row) {\r\n      return row.cacheName.replace(\":\", \"\");\r\n    },\r\n    /** 键名前缀去除 */\r\n    keyFormatter(cacheKey) {\r\n      return cacheKey.replace(this.nowCacheName, \"\");\r\n    },\r\n    /** 查询缓存内容详细 */\r\n    handleCacheValue(cacheKey) {\r\n      getCacheValue(this.nowCacheName, cacheKey).then(response => {\r\n        this.cacheForm = response.data;\r\n      });\r\n    },\r\n    /** 清理全部缓存 */\r\n    handleClearCacheAll() {\r\n      clearCacheAll().then(response => {\r\n        this.$modal.msgSuccess(\"清理全部缓存成功\");\r\n      });\r\n    }\r\n  },\r\n};\r\n</script>\r\n"]}]}