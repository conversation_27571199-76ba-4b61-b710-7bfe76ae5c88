<template>
  <div class="sea-lcl-component">
    <!--拼柜海运-->
    <div v-for="(item, index) in seaLclList" :key="`sea-lcl-${index}`" class="sea-lcl-item">
      <!--标题栏-->
      <el-row>
        <el-col :span="18">
          <div class="service-bar">
            <a :class="[
                'service-toggle-icon',
                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
              ]"
            />
            <div class="service-title-group">
              <h3 class="service-title" @click="changeServiceFold(item.rsServiceInstances)">
                海运-LCL
              </h3>
              <el-button
                class="service-action-btn"
                type="text"
                @click="addSeaLCL"
              >[+]
              </el-button>
              <el-button
                class="service-action-btn"
                type="text"
                @click="deleteRsOpLclSea(item)"
              >[-]
              </el-button>
              <el-button
                class="service-action-btn"
                type="text"
                @click="openChargeSelect(item)"
              >[CN...]
              </el-button>
            </div>
            <!--审核信息-->
            <audit
              v-if="auditInfo"
              :audit="true"
              :basic-info="item.rsServiceInstances"
              :disabled="disabled"
              :payable="getPayable(2)"
              :rs-charge-list="item.rsChargeList"
              @return="item = $event"
            />
            <div class="booking-bill-container">
              <el-popover
                v-for="(billConfig, billIndex) in bookingBillConfig"
                :key="`bill-${billIndex}`"
                placement="top"
                trigger="click"
                width="100"
              >
                <el-button
                  v-for="(template, templateIndex) in billConfig.templateList"
                  :key="`template-${templateIndex}`"
                  @click="handleBookingBill(item, template)"
                >
                  {{ template }}
                </el-button>
                <a
                  slot="reference"
                  class="booking-bill-link"
                  target="_blank"
                >
                  [{{ billConfig.file }}]
                </a>
              </el-popover>
            </div>
          </div>
        </el-col>
      </el-row>
      <!--内容区域-->
      <transition name="fade">
        <el-row
          v-if="item.rsServiceInstances.serviceFold"
          :gutter="10"
          class="service-content-area"
        >
          <!--服务信息栏-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="3" class="service-info-col">
              <el-form-item label="询价单号">
                <el-input
                  v-model="item.rsServiceInstances.inquiryNo"
                  :class="{ 'disable-form': form.sqdPsaNo }"
                  :disabled="!!form.sqdPsaNo"
                  placeholder="询价单号"
                  @focus="generateFreight(1, 2, item)"
                />
              </el-form-item>

              <el-form-item v-if="!booking && branchInfo" label="供应商">
                <el-popover
                  :content="getSupplierEmail(item.rsServiceInstances.supplierId)"
                  placement="bottom"
                  trigger="hover"
                  width="200"
                >
                  <el-input
                    slot="reference"
                    :value="item.rsServiceInstances.supplierName"
                    class="disable-form"
                    disabled
                  />
                </el-popover>
              </el-form-item>

              <el-form-item label="合约类型">
                <el-input
                  :value="getAgreementDisplay(item.rsServiceInstances)"
                  class="disable-form"
                  disabled
                  placeholder="合约类型"
                />
              </el-form-item>

              <el-form-item label="业务须知">
                <el-input
                  v-model="item.rsServiceInstances.inquiryNotice"
                  class="disable-form"
                  disabled
                  placeholder="业务须知"
                />
              </el-form-item>

              <el-form-item label="订舱状态">
                <el-row>
                  <el-col :span="20">
                    <el-input
                      :value="getBookingStatus(item.bookingStatus)"
                      class="disable-form"
                      disabled
                      placeholder="订舱状态"
                    />
                  </el-col>
                  <el-col :span="4">
                    <el-button
                      :disabled="getServiceInstanceDisable(item.rsServiceInstances)"
                      class="cancel-btn"
                      size="mini"
                      type="text"
                      @click="psaBookingCancel(item)"
                    >
                      取消
                    </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </transition>
          <!--分支信息-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="15" class="branch-info-col">
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-form-item label="商务单号">
                    <el-row>
                      <el-col :span="20">
                        <el-input
                          :class="{ 'disable-form': item.sqdPsaNo }"
                          :disabled="!!item.sqdPsaNo"
                          :value="item.sqdPsaNo"
                          @focus="selectPsaBookingOpen(item)"
                        />
                      </el-col>
                      <el-col :span="4">
                        <el-button
                          :disabled="getServiceInstanceDisable(item.rsServiceInstances)"
                          class="cancel-btn"
                          size="mini"
                          type="text"
                          @click="psaBookingCancel(item)"
                        >
                          取消
                        </el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="SO号码">
                    <el-input
                      v-model="item.soNo"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="SO号码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="提单号码">
                    <el-input
                      v-model="item.blNo"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="提单号码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="柜号概览">
                    <el-input
                      v-model="item.sqdContainersSealsSum"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="柜号概览"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-form-item label="船公司">
                    <treeselect
                      v-model="item.carrierId"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disable-branch-nodes="true"
                      :disabled="isFieldDisabled(item)"
                      :disabled-fuzzy-matching="true"
                      :flat="false"
                      :flatten-search-results="true"
                      :multiple="false"
                      :normalizer="carrierNormalizer"
                      :options="carrierList"
                      :show-count="true"
                      placeholder="选择承运人"
                      @select="selectCarrier(item, $event)"
                    >
                      <div slot="value-label" slot-scope="{ node }">
                        {{ node.raw.carrierIntlCode || " " }}
                      </div>
                      <label
                        slot="option-label"
                        slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                        :class="labelClassName"
                      >
                        {{ formatCarrierLabel(node.label) }}
                        <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                      </label>
                    </treeselect>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="头程船名">
                    <el-input
                      v-model="item.firstVessel"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="头程船名船次"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="二程船名">
                    <el-input
                      v-model="item.secondVessel"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="二程船名船次"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="船期">
                    <el-input
                      v-model="item.inquiryScheduleSummary"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="航班时效"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-form-item label="头程开船">
                    <el-input
                      v-model="item.firstCyOpenTime"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="头程开船"
                      @change="addProgress(getServiceObject(item.serviceTypeId).rsOpLogList, 15)"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="头程截重">
                    <el-input
                      v-model="item.firstCyClosingTime"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      clearable
                      placeholder="头程截重"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="截关时间">
                    <el-input
                      v-model="item.cvClosingTime"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="截关时间"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="ETD">
                    <el-date-picker
                      v-model="item.etd"
                      :class="{ 'disable-form': isDateFieldDisabled(item) }"
                      :disabled="isDateFieldDisabled(item)"
                      clearable
                      placeholder="ETD"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item label="ETA">
                    <el-date-picker
                      v-model="item.eta"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      clearable
                      placeholder="ETA"
                      type="date"
                      value-format="yyyy-MM-dd"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-form-item label="截补料">
                    <el-input
                      v-model="item.siClosingTime"
                      :class="{ 'disable-form': isDateFieldDisabled(item) }"
                      :disabled="isDateFieldDisabled(item)"
                      placeholder="截补料"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="截VGM">
                    <el-input
                      v-model="item.sqdVgmStatus"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="截VGM"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="AMS/ENS">
                    <el-input
                      v-model="item.sqdAmsEnsPostStatus"
                      :class="{ 'disable-form': isFieldDisabled(item) }"
                      :disabled="isFieldDisabled(item)"
                      placeholder="AMS/ENS"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-form-item label="结算价">
                    <el-row>
                      <el-col :span="20">
                        <el-input
                          v-model="item.settledRate"
                          :class="{ 'disable-form': isFieldDisabled(item) }"
                          :disabled="isFieldDisabled(item)"
                          placeholder="price1/price2/price3"
                        />
                      </el-col>
                      <el-col :span="4">
                        <el-button type="text" @click="handleSettledRate(item)">确定</el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
                <el-col :span="19">
                  <el-form-item label="订舱备注">
                    <div class="booking-remark-group">
                      <el-input
                        v-model="item.bookingChargeRemark"
                        :autosize="{ minRows: 2.5, maxRows: 5}"
                        :class="{ 'disable-form': isFieldDisabled(item) }"
                        :disabled="isFieldDisabled(item)"
                        placeholder="订舱费用备注"
                        type="textarea"
                      />
                      <el-input
                        v-model="item.bookingAgentRemark"
                        :autosize="{ minRows: 2.5, maxRows: 5}"
                        :class="{ 'disable-form': isFieldDisabled(item) }"
                        :disabled="isFieldDisabled(item)"
                        placeholder="订舱备注"
                        type="textarea"
                      />
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-col>
          </transition>
          <!--物流进度-->
          <transition name="fade">
            <el-col v-if="logisticsInfo" :span="4">
              <logistics-progress
                :disabled="getServiceInstanceDisable(item.rsServiceInstances) || disabled || psaVerify"
                :logistics-progress-data="item.rsOpLogList"
                :open-logistics-progress-list="true"
                :process-type="4"
                :service-type="1"
                @deleteItem="item.rsOpLogList = item.rsOpLogList.filter(log => log !== $event)"
                @return="item.rsOpLogList = $event"
              />
            </el-col>
          </transition>
          <!--费用列表-->
          <el-col v-if="chargeInfo" :span="10.3">
            <!--<charge-list
              :a-t-d="form.podEta"
              :charge-data="item.rsChargeList"
              :company-list="companyList"
              :disabled="getFormDisable(item.serviceTypeId) || disabled"
              :hiddenSupplier="booking"
              :is-receivable="false"
              :open-charge-list="true"
              :pay-detail-r-m-b="item.payableRMB"
              :pay-detail-r-m-b-tax="item.payableRMBTax"
              :pay-detail-u-s-d="item.payableUSD"
              :pay-detail-u-s-d-tax="item.payableUSDTax"
              :service-type-id="1"
              @copyFreight="copyFreight($event)"
              @deleteAll="item.rsChargeList = []"
              @deleteItem="item.rsChargeList = item.rsChargeList.filter(charge => charge !== $event)"
              @return="calculateCharge(2, $event, item)"
            />-->
            <debit-note-list
              :company-list="companyList"
              :debit-note-list="item.rsDebitNoteList"
              :disabled="false"
              :hidden-supplier="false"
              :is-receivable="0"
              :rct-id="form.rctId"
              @addDebitNote="handleAddDebitNote(item)"
              @deleteItem="item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)"
              @return="calculateCharge(2,$event,item)"
            />
          </el-col>
        </el-row>
      </transition>
    </div>
  </div>
</template>

<script>
import Audit from "../audit.vue"
import LogisticsProgress from "../logisticsProgress.vue"
import ChargeList from "../chargeList.vue"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.css"
import DebitNoteList from "@/views/system/document/debitNodeList.vue"

export default {
  name: "SeaLclComponent",
  components: {
    DebitNoteList,
    Audit,
    LogisticsProgress,
    ChargeList,
    Treeselect
  },
  props: {
    // 拼柜海运数据列表
    seaLclList: {
      type: Array,
      default: () => []
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({})
    },
    // 显示控制
    branchInfo: {
      type: Boolean,
      default: true
    },
    logisticsInfo: {
      type: Boolean,
      default: true
    },
    chargeInfo: {
      type: Boolean,
      default: true
    },
    auditInfo: {
      type: Boolean,
      default: false
    },
    // 状态控制
    disabled: {
      type: Boolean,
      default: false
    },
    booking: {
      type: Boolean,
      default: false
    },
    psaVerify: {
      type: Boolean,
      default: false
    },
    // 数据列表
    supplierList: {
      type: Array,
      default: () => []
    },
    carrierList: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    },
    // 方法函数
    carrierNormalizer: {
      type: Function,
      default: () => {
      }
    }
  },
  data() {
    return {
      bookingBillConfig: [{
        file: "订舱单",
        templateList: ["bookingOrder1"]
      }]
    }
  },
  computed: {
    // 判断是否禁用状态
    isDisabled() {
      return this.disabled || this.psaVerify
    }
  },
  methods: {
    handleAddDebitNote(serviceObject) {
      let row = {}
      row.sqdRctNo = this.form.rctNo
      row.rctId = this.form.rctId
      row.isRecievingOrPaying = 1
      row.rsChargeList = []
      this.$emit('addDebitNote', row, serviceObject)
    },
    // 判断字段是否禁用
    isFieldDisabled(item) {
      return this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)
    },
    // 判断日期字段是否禁用（注意有些地方使用了getFormDisable）
    isDateFieldDisabled(item) {
      return this.disabled || this.getFormDisable(item.rsServiceInstances)
    },
    // 获取供应商邮箱
    getSupplierEmail(supplierId) {
      const supplier = this.supplierList.find(v => v.companyId === supplierId)
      return supplier ? supplier.staffEmail : ''
    },
    // 获取合约显示文本
    getAgreementDisplay(serviceInstance) {
      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo
    },
    // 格式化承运人标签
    formatCarrierLabel(label) {
      const commaIndex = label.indexOf(",")
      return commaIndex !== -1 ? label.substring(0, commaIndex) : label
    },
    // 处理订舱单生成
    handleBookingBill(item, template) {
      this.$emit("getBookingBill", item, template)
    },
    // 事件转发给父组件
    changeServiceFold(serviceInstance) {
      this.$emit("changeServiceFold", serviceInstance)
    },
    addSeaLCL() {
      this.$emit("addSeaLCL")
    },
    deleteRsOpLclSea(item) {
      this.$emit("deleteRsOpLclSea", item)
    },
    openChargeSelect(item) {
      this.$emit("openChargeSelect", item)
    },
    auditCharge(item, event) {
      this.$emit("auditCharge", item, event)
    },
    generateFreight(type1, type2, item) {
      this.$emit("generateFreight", type1, type2, item)
    },
    selectPsaBookingOpen(item) {
      this.$emit("selectPsaBookingOpen", item)
    },
    selectCarrier(item, event) {
      this.$emit("selectCarrier", item, event)
    },
    addProgress(logList, type) {
      this.$emit("addProgress", logList, type)
    },
    handleSettledRate(item) {
      this.$emit("handleSettledRate", item)
    },
    psaBookingCancel(item) {
      this.$emit("psaBookingCancel", item)
    },
    copyFreight(event) {
      this.$emit("copyFreight", event)
    },
    calculateCharge(serviceType, event, item) {
      this.$emit("calculateCharge", serviceType, event, item)
    },
    getPayable(type) {
      return this.$parent.getPayable ? this.$parent.getPayable(type) : null
    },
    getBookingStatus(status) {
      return this.$parent.getBookingStatus ? this.$parent.getBookingStatus(status) : ''
    },
    getServiceInstanceDisable(serviceInstance) {
      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false
    },
    getServiceObject(serviceTypeId) {
      return this.$parent.getServiceObject ? this.$parent.getServiceObject(serviceTypeId) : {}
    },
    getFormDisable(serviceTypeId) {
      return this.$parent.getFormDisable ? this.$parent.getFormDisable(serviceTypeId) : false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/op-document';

// SeaLcl组件特定样式
.sea-lcl-component {
  width: 100%;

  .sea-lcl-item {
    margin-bottom: 10px;
  }

  .service-bar {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;

    .service-toggle-icon {
      cursor: pointer;
      margin-right: 5px;
    }

    .service-title-group {
      display: flex;
      align-items: center;
      width: 250px;

      .service-title {
        margin: 0;
        cursor: pointer;
      }

      .service-action-btn {
        margin-left: 10px;
      }
    }

    .booking-bill-container {
      margin-left: auto;

      .booking-bill-link {
        color: blue;
        padding: 0;
        text-decoration: none;
        cursor: pointer;
      }
    }
  }

  .service-content-area {
    margin-bottom: 15px;
    display: -webkit-box;

    .service-info-col {
      .el-form-item {
        margin-bottom: 10px;
      }

      .cancel-btn {
        color: red;
      }
    }

    .branch-info-col {
      .booking-remark-group {
        display: flex;
        gap: 10px;

        .el-textarea {
          flex: 1;
        }
      }
    }
  }

  // 优化表单输入框样式
  .el-input {
    width: 100%;
  }

  // 优化日期选择器样式
  .el-date-picker {
    width: 100%;
  }
}
</style>
