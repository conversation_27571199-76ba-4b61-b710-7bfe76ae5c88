{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\generator\\html.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\generator\\html.js", "mtime": 1754876882558}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_config", "require", "confGlobal", "someSpanIsNot24", "dialogWrapper", "str", "concat", "vueTemplate", "vueScript", "cssStyle", "cssStr", "buildFormTemplate", "conf", "child", "type", "labelPosition", "disabled", "formRef", "formModel", "formRules", "size", "labelWidth", "buildFromBtns", "gutter", "formBtns", "colWrapper", "element", "span", "layouts", "colFormItem", "required", "trigger", "tag", "tagDom", "tags", "label", "vModel", "rowFormItem", "justify", "align", "children", "map", "el", "layout", "join", "elButton", "_attrBuilder", "attrBuilder", "icon", "buildElButtonChild", "elInput", "_attrBuilder2", "clearable", "placeholder", "width", "maxlength", "showWordLimit", "readonly", "prefixIcon", "suffixIcon", "showPassword", "autosize", "minRows", "maxRows", "buildElInputChild", "elInputNumber", "_attrBuilder3", "controlsPosition", "min", "max", "step", "stepStrictly", "precision", "elSelect", "_attrBuilder4", "filterable", "multiple", "buildElSelectChild", "elRadioGroup", "_attrBuilder5", "buildElRadioGroupChild", "elCheckboxGroup", "_attrBuilder6", "buildElCheckboxGroupChild", "elSwitch", "_attrBuilder7", "activeText", "inactiveText", "activeColor", "inactiveColor", "activeValue", "JSON", "stringify", "inactiveValue", "elCascader", "_attrBuilder8", "options", "props", "showAllLevels", "separator", "<PERSON><PERSON><PERSON><PERSON>", "_attrBuilder9", "range", "showStops", "elTimePicker", "_attrBuilder10", "startPlaceholder", "endPlaceholder", "rangeSeparator", "isRange", "format", "valueFormat", "pickerOptions", "elDatePicker", "_attrBuilder11", "elRate", "_attrBuilder12", "allowHalf", "showText", "showScore", "elColorPicker", "_attrBuilder13", "showAlpha", "colorFormat", "elUpload", "action", "listType", "accept", "name", "autoUpload", "beforeUpload", "fileList", "ref", "buildElUploadChild", "style", "default", "push", "prepend", "append", "length", "optionType", "border", "list", "buttonText", "showTip", "fileSize", "sizeUnit", "makeUpHtml", "htmlList", "fields", "some", "item", "for<PERSON>ach", "htmlStr", "temp"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/utils/generator/html.js"], "sourcesContent": ["/* eslint-disable max-len */\r\nimport {trigger} from './config'\r\n\r\nlet confGlobal\r\nlet someSpanIsNot24\r\n\r\nexport function dialogWrapper(str) {\r\n  return `<el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag v-dialogDragWidth\r\n      :modal-append-to-body=\"false\" v-bind=\"$attrs\" v-on=\"$listeners\" @open=\"onOpen\" @close=\"onClose\" title=\"Dialog Title\">\r\n    ${str}\r\n    <div slot=\"footer\">\r\n      <el-button @click=\"close\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleConfirm\">确定</el-button>\r\n    </div>\r\n  </el-dialog>`\r\n}\r\n\r\nexport function vueTemplate(str) {\r\n  return `<template>\r\n    <div>\r\n      ${str}\r\n    </div>\r\n  </template>`\r\n}\r\n\r\nexport function vueScript(str) {\r\n  return `<script>\r\n    ${str}\r\n  </script>`\r\n}\r\n\r\nexport function cssStyle(cssStr) {\r\n  return `<style>\r\n    ${cssStr}\r\n  </style>`\r\n}\r\n\r\nfunction buildFormTemplate(conf, child, type) {\r\n  let labelPosition = ''\r\n  if (conf.labelPosition != 'right') {\r\n    labelPosition = `label-position=\"${conf.labelPosition}\"`\r\n  }\r\n  const disabled = conf.disabled ? `:disabled=\"${conf.disabled}\"` : ''\r\n  let str = `<el-form ref=\"${conf.formRef}\" :model=\"${conf.formModel}\" :rules=\"${conf.formRules}\" size=\"${conf.size}\" ${disabled} label-width=\"${conf.labelWidth}px\" ${labelPosition}>\r\n      ${child}\r\n      ${buildFromBtns(conf, type)}\r\n    </el-form>`\r\n  if (someSpanIsNot24) {\r\n    str = `<el-row :gutter=\"${conf.gutter}\">\r\n        ${str}\r\n      </el-row>`\r\n  }\r\n  return str\r\n}\r\n\r\nfunction buildFromBtns(conf, type) {\r\n  let str = ''\r\n  if (conf.formBtns && type == 'file') {\r\n    str = `<el-form-item size=\"large\">\r\n          <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>`\r\n    if (someSpanIsNot24) {\r\n      str = `<el-col :span=\"24\">\r\n          ${str}\r\n        </el-col>`\r\n    }\r\n  }\r\n  return str\r\n}\r\n\r\n// span不为24的用el-col包裹\r\nfunction colWrapper(element, str) {\r\n  if (someSpanIsNot24 || element.span != 24) {\r\n    return `<el-col :span=\"${element.span}\">\r\n      ${str}\r\n    </el-col>`\r\n  }\r\n  return str\r\n}\r\n\r\nconst layouts = {\r\n  colFormItem(element) {\r\n    let labelWidth = ''\r\n    if (element.labelWidth && element.labelWidth != confGlobal.labelWidth) {\r\n      labelWidth = `label-width=\"${element.labelWidth}px\"`\r\n    }\r\n    const required = !trigger[element.tag] && element.required ? 'required' : ''\r\n    const tagDom = tags[element.tag] ? tags[element.tag](element) : null\r\n    let str = `<el-form-item ${labelWidth} label=\"${element.label}\" prop=\"${element.vModel}\" ${required}>\r\n        ${tagDom}\r\n      </el-form-item>`\r\n    str = colWrapper(element, str)\r\n    return str\r\n  },\r\n  rowFormItem(element) {\r\n    const type = element.type == 'default' ? '' : `type=\"${element.type}\"`\r\n    const justify = element.type == 'default' ? '' : `justify=\"${element.justify}\"`\r\n    const align = element.type == 'default' ? '' : `align=\"${element.align}\"`\r\n    const gutter = element.gutter ? `gutter=\"${element.gutter}\"` : ''\r\n    const children = element.children.map(el => layouts[el.layout](el))\r\n    let str = `<el-row ${type} ${justify} ${align} ${gutter}>\r\n      ${children.join('\\n')}\r\n    </el-row>`\r\n    str = colWrapper(element, str)\r\n    return str\r\n  }\r\n}\r\n\r\nconst tags = {\r\n  'el-button': el => {\r\n    const {\r\n      tag, disabled\r\n    } = attrBuilder(el)\r\n    const type = el.type ? `type=\"${el.type}\"` : ''\r\n    const icon = el.icon ? `icon=\"${el.icon}\"` : ''\r\n    const size = el.size ? `size=\"${el.size}\"` : ''\r\n    let child = buildElButtonChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${type} ${icon} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-input': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const maxlength = el.maxlength ? `:maxlength=\"${el.maxlength}\"` : ''\r\n    const showWordLimit = el['show-word-limit'] ? 'show-word-limit' : ''\r\n    const readonly = el.readonly ? 'readonly' : ''\r\n    const prefixIcon = el['prefix-icon'] ? `prefix-icon='${el['prefix-icon']}'` : ''\r\n    const suffixIcon = el['suffix-icon'] ? `suffix-icon='${el['suffix-icon']}'` : ''\r\n    const showPassword = el['show-password'] ? 'show-password' : ''\r\n    const type = el.type ? `type=\"${el.type}\"` : ''\r\n    const autosize = el.autosize && el.autosize.minRows\r\n      ? `:autosize=\"{minRows: ${el.autosize.minRows}, maxRows: ${el.autosize.maxRows}}\"`\r\n      : ''\r\n    let child = buildElInputChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${type} ${placeholder} ${maxlength} ${showWordLimit} ${readonly} ${disabled} ${clearable} ${prefixIcon} ${suffixIcon} ${showPassword} ${autosize} ${width}>${child}</${el.tag}>`\r\n  },\r\n  'el-input-number': el => {\r\n    const {disabled, vModel, placeholder} = attrBuilder(el)\r\n    const controlsPosition = el['controls-position'] ? `controls-position=${el['controls-position']}` : ''\r\n    const min = el.min ? `:min='${el.min}'` : ''\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const step = el.step ? `:step='${el.step}'` : ''\r\n    const stepStrictly = el['step-strictly'] ? 'step-strictly' : ''\r\n    const precision = el.precision ? `:precision='${el.precision}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${placeholder} ${step} ${stepStrictly} ${precision} ${controlsPosition} ${min} ${max} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-select': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const filterable = el.filterable ? 'filterable' : ''\r\n    const multiple = el.multiple ? 'multiple' : ''\r\n    let child = buildElSelectChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${placeholder} ${disabled} ${multiple} ${filterable} ${clearable} ${width}>${child}</${el.tag}>`\r\n  },\r\n  'el-radio-group': el => {\r\n    const {disabled, vModel} = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    let child = buildElRadioGroupChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-checkbox-group': el => {\r\n    const {disabled, vModel} = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    const min = el.min ? `:min=\"${el.min}\"` : ''\r\n    const max = el.max ? `:max=\"${el.max}\"` : ''\r\n    let child = buildElCheckboxGroupChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${vModel} ${min} ${max} ${size} ${disabled}>${child}</${el.tag}>`\r\n  },\r\n  'el-switch': el => {\r\n    const {disabled, vModel} = attrBuilder(el)\r\n    const activeText = el['active-text'] ? `active-text=\"${el['active-text']}\"` : ''\r\n    const inactiveText = el['inactive-text'] ? `inactive-text=\"${el['inactive-text']}\"` : ''\r\n    const activeColor = el['active-color'] ? `active-color=\"${el['active-color']}\"` : ''\r\n    const inactiveColor = el['inactive-color'] ? `inactive-color=\"${el['inactive-color']}\"` : ''\r\n    const activeValue = el['active-value'] != true ? `:active-value='${JSON.stringify(el['active-value'])}'` : ''\r\n    const inactiveValue = el['inactive-value'] != false ? `:inactive-value='${JSON.stringify(el['inactive-value'])}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${activeText} ${inactiveText} ${activeColor} ${inactiveColor} ${activeValue} ${inactiveValue} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-cascader': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const options = el.options ? `:options=\"${el.vModel}Options\"` : ''\r\n    const props = el.props ? `:props=\"${el.vModel}Props\"` : ''\r\n    const showAllLevels = el['show-all-levels'] ? '' : ':show-all-levels=\"false\"'\r\n    const filterable = el.filterable ? 'filterable' : ''\r\n    const separator = el.separator == '/' ? '' : `separator=\"${el.separator}\"`\r\n\r\n    return `<${el.tag} ${vModel} ${options} ${props} ${width} ${showAllLevels} ${placeholder} ${separator} ${filterable} ${clearable} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-slider': el => {\r\n    const {disabled, vModel} = attrBuilder(el)\r\n    const min = el.min ? `:min='${el.min}'` : ''\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const step = el.step ? `:step='${el.step}'` : ''\r\n    const range = el.range ? 'range' : ''\r\n    const showStops = el['show-stops'] ? `:show-stops=\"${el['show-stops']}\"` : ''\r\n\r\n    return `<${el.tag} ${min} ${max} ${step} ${vModel} ${range} ${showStops} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-time-picker': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\r\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\r\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\r\n    const isRange = el['is-range'] ? 'is-range' : ''\r\n    const format = el.format ? `format=\"${el.format}\"` : ''\r\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\r\n    const pickerOptions = el['picker-options'] ? `:picker-options='${JSON.stringify(el['picker-options'])}'` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${isRange} ${format} ${valueFormat} ${pickerOptions} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-date-picker': el => {\r\n    const {\r\n      disabled, vModel, clearable, placeholder, width\r\n    } = attrBuilder(el)\r\n    const startPlaceholder = el['start-placeholder'] ? `start-placeholder=\"${el['start-placeholder']}\"` : ''\r\n    const endPlaceholder = el['end-placeholder'] ? `end-placeholder=\"${el['end-placeholder']}\"` : ''\r\n    const rangeSeparator = el['range-separator'] ? `range-separator=\"${el['range-separator']}\"` : ''\r\n    const format = el.format ? `format=\"${el.format}\"` : ''\r\n    const valueFormat = el['value-format'] ? `value-format=\"${el['value-format']}\"` : ''\r\n    const type = el.type == 'date' ? '' : `type=\"${el.type}\"`\r\n    const readonly = el.readonly ? 'readonly' : ''\r\n\r\n    return `<${el.tag} ${type} ${vModel} ${format} ${valueFormat} ${width} ${placeholder} ${startPlaceholder} ${endPlaceholder} ${rangeSeparator} ${clearable} ${readonly} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-rate': el => {\r\n    const {disabled, vModel} = attrBuilder(el)\r\n    const max = el.max ? `:max='${el.max}'` : ''\r\n    const allowHalf = el['allow-half'] ? 'allow-half' : ''\r\n    const showText = el['show-text'] ? 'show-text' : ''\r\n    const showScore = el['show-score'] ? 'show-score' : ''\r\n\r\n    return `<${el.tag} ${vModel} ${allowHalf} ${showText} ${showScore} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-color-picker': el => {\r\n    const {disabled, vModel} = attrBuilder(el)\r\n    const size = `size=\"${el.size}\"`\r\n    const showAlpha = el['show-alpha'] ? 'show-alpha' : ''\r\n    const colorFormat = el['color-format'] ? `color-format=\"${el['color-format']}\"` : ''\r\n\r\n    return `<${el.tag} ${vModel} ${size} ${showAlpha} ${colorFormat} ${disabled}></${el.tag}>`\r\n  },\r\n  'el-upload': el => {\r\n    const disabled = el.disabled ? ':disabled=\\'true\\'' : ''\r\n    const action = el.action ? `:action=\"${el.vModel}Action\"` : ''\r\n    const multiple = el.multiple ? 'multiple' : ''\r\n    const listType = el['list-type'] != 'text' ? `list-type=\"${el['list-type']}\"` : ''\r\n    const accept = el.accept ? `accept=\"${el.accept}\"` : ''\r\n    const name = el.name != 'file' ? `name=\"${el.name}\"` : ''\r\n    const autoUpload = el['auto-upload'] == false ? ':auto-upload=\"false\"' : ''\r\n    const beforeUpload = `:before-upload=\"${el.vModel}BeforeUpload\"`\r\n    const fileList = `:file-list=\"${el.vModel}fileList\"`\r\n    const ref = `ref=\"${el.vModel}\"`\r\n    let child = buildElUploadChild(el)\r\n\r\n    if (child) child = `\\n${child}\\n` // 换行\r\n    return `<${el.tag} ${ref} ${fileList} ${action} ${autoUpload} ${multiple} ${beforeUpload} ${listType} ${accept} ${name} ${disabled}>${child}</${el.tag}>`\r\n  }\r\n}\r\n\r\nfunction attrBuilder(el) {\r\n  return {\r\n    vModel: `v-model=\"${confGlobal.formModel}.${el.vModel}\"`,\r\n    clearable: el.clearable ? 'clearable' : '',\r\n    placeholder: el.placeholder ? `placeholder=\"${el.placeholder}\"` : '',\r\n    width: el.style && el.style.width ? ':style=\"{width: \\'100%\\'}\"' : '',\r\n    disabled: el.disabled ? ':disabled=\\'true\\'' : ''\r\n  }\r\n}\r\n\r\n// el-buttin 子级\r\nfunction buildElButtonChild(conf) {\r\n  const children = []\r\n  if (conf.default) {\r\n    children.push(conf.default)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\n// el-input innerHTML\r\nfunction buildElInputChild(conf) {\r\n  const children = []\r\n  if (conf.prepend) {\r\n    children.push(`<template slot=\"prepend\">${conf.prepend}</template>`)\r\n  }\r\n  if (conf.append) {\r\n    children.push(`<template slot=\"append\">${conf.append}</template>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElSelectChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    children.push(`<el-option v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.label\" :value=\"item.value\" :disabled=\"item.disabled\"></el-option>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElRadioGroupChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    const tag = conf.optionType == 'button' ? 'el-radio-button' : 'el-radio'\r\n    const border = conf.border ? 'border' : ''\r\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElCheckboxGroupChild(conf) {\r\n  const children = []\r\n  if (conf.options && conf.options.length) {\r\n    const tag = conf.optionType == 'button' ? 'el-checkbox-button' : 'el-checkbox'\r\n    const border = conf.border ? 'border' : ''\r\n    children.push(`<${tag} v-for=\"(item, index) in ${conf.vModel}Options\" :key=\"index\" :label=\"item.value\" :disabled=\"item.disabled\" ${border}>{{item.label}}</${tag}>`)\r\n  }\r\n  return children.join('\\n')\r\n}\r\n\r\nfunction buildElUploadChild(conf) {\r\n  const list = []\r\n  if (conf['list-type'] == 'picture-card') list.push('<i class=\"el-icon-plus\"></i>')\r\n  else list.push(`<el-button size=\"mini\" type=\"primary\" icon=\"el-icon-upload\">${conf.buttonText}</el-button>`)\r\n  if (conf.showTip) list.push(`<div slot=\"tip\" class=\"el-upload__tip\">只能上传不超过 ${conf.fileSize}${conf.sizeUnit} 的${conf.accept}文件</div>`)\r\n  return list.join('\\n')\r\n}\r\n\r\nexport function makeUpHtml(conf, type) {\r\n  const htmlList = []\r\n  confGlobal = conf\r\n  someSpanIsNot24 = conf.fields.some(item => item.span != 24)\r\n  conf.fields.forEach(el => {\r\n    htmlList.push(layouts[el.layout](el))\r\n  })\r\n  const htmlStr = htmlList.join('\\n')\r\n\r\n  let temp = buildFormTemplate(conf, htmlStr, type)\r\n  if (type == 'dialog') {\r\n    temp = dialogWrapper(temp)\r\n  }\r\n  confGlobal = null\r\n  return temp\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AADA;;AAGA,IAAIC,UAAU;AACd,IAAIC,eAAe;AAEZ,SAASC,aAAaA,CAACC,GAAG,EAAE;EACjC,gPAAAC,MAAA,CAIID,GAAG;AAMT;AAEO,SAASE,WAAWA,CAACF,GAAG,EAAE;EAC/B,uCAAAC,MAAA,CAEMD,GAAG;AAGX;AAEO,SAASG,SAASA,CAACH,GAAG,EAAE;EAC7B,wBAAAC,MAAA,CACID,GAAG;AAET;AAEO,SAASI,QAAQA,CAACC,MAAM,EAAE;EAC/B,uBAAAJ,MAAA,CACII,MAAM;AAEZ;AAEA,SAASC,iBAAiBA,CAACC,IAAI,EAAEC,KAAK,EAAEC,IAAI,EAAE;EAC5C,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIH,IAAI,CAACG,aAAa,IAAI,OAAO,EAAE;IACjCA,aAAa,uBAAAT,MAAA,CAAsBM,IAAI,CAACG,aAAa,OAAG;EAC1D;EACA,IAAMC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,kBAAAV,MAAA,CAAiBM,IAAI,CAACI,QAAQ,UAAM,EAAE;EACpE,IAAIX,GAAG,qBAAAC,MAAA,CAAoBM,IAAI,CAACK,OAAO,kBAAAX,MAAA,CAAaM,IAAI,CAACM,SAAS,kBAAAZ,MAAA,CAAaM,IAAI,CAACO,SAAS,gBAAAb,MAAA,CAAWM,IAAI,CAACQ,IAAI,SAAAd,MAAA,CAAKU,QAAQ,qBAAAV,MAAA,CAAiBM,IAAI,CAACS,UAAU,WAAAf,MAAA,CAAOS,aAAa,eAAAT,MAAA,CAC5KO,KAAK,cAAAP,MAAA,CACLgB,aAAa,CAACV,IAAI,EAAEE,IAAI,CAAC,qBAClB;EACb,IAAIX,eAAe,EAAE;IACnBE,GAAG,wBAAAC,MAAA,CAAuBM,IAAI,CAACW,MAAM,mBAAAjB,MAAA,CAC/BD,GAAG,sBACG;EACd;EACA,OAAOA,GAAG;AACZ;AAEA,SAASiB,aAAaA,CAACV,IAAI,EAAEE,IAAI,EAAE;EACjC,IAAIT,GAAG,GAAG,EAAE;EACZ,IAAIO,IAAI,CAACY,QAAQ,IAAIV,IAAI,IAAI,MAAM,EAAE;IACnCT,GAAG,qNAGiB;IACpB,IAAIF,eAAe,EAAE;MACnBE,GAAG,uCAAAC,MAAA,CACGD,GAAG,wBACG;IACd;EACF;EACA,OAAOA,GAAG;AACZ;;AAEA;AACA,SAASoB,UAAUA,CAACC,OAAO,EAAErB,GAAG,EAAE;EAChC,IAAIF,eAAe,IAAIuB,OAAO,CAACC,IAAI,IAAI,EAAE,EAAE;IACzC,0BAAArB,MAAA,CAAyBoB,OAAO,CAACC,IAAI,iBAAArB,MAAA,CACjCD,GAAG;EAET;EACA,OAAOA,GAAG;AACZ;AAEA,IAAMuB,OAAO,GAAG;EACdC,WAAW,WAAAA,YAACH,OAAO,EAAE;IACnB,IAAIL,UAAU,GAAG,EAAE;IACnB,IAAIK,OAAO,CAACL,UAAU,IAAIK,OAAO,CAACL,UAAU,IAAInB,UAAU,CAACmB,UAAU,EAAE;MACrEA,UAAU,oBAAAf,MAAA,CAAmBoB,OAAO,CAACL,UAAU,SAAK;IACtD;IACA,IAAMS,QAAQ,GAAG,CAACC,eAAO,CAACL,OAAO,CAACM,GAAG,CAAC,IAAIN,OAAO,CAACI,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC5E,IAAMG,MAAM,GAAGC,IAAI,CAACR,OAAO,CAACM,GAAG,CAAC,GAAGE,IAAI,CAACR,OAAO,CAACM,GAAG,CAAC,CAACN,OAAO,CAAC,GAAG,IAAI;IACpE,IAAIrB,GAAG,oBAAAC,MAAA,CAAoBe,UAAU,eAAAf,MAAA,CAAWoB,OAAO,CAACS,KAAK,gBAAA7B,MAAA,CAAWoB,OAAO,CAACU,MAAM,SAAA9B,MAAA,CAAKwB,QAAQ,iBAAAxB,MAAA,CAC7F2B,MAAM,4BACM;IAClB5B,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ,CAAC;EACDgC,WAAW,WAAAA,YAACX,OAAO,EAAE;IACnB,IAAMZ,IAAI,GAAGY,OAAO,CAACZ,IAAI,IAAI,SAAS,GAAG,EAAE,aAAAR,MAAA,CAAYoB,OAAO,CAACZ,IAAI,OAAG;IACtE,IAAMwB,OAAO,GAAGZ,OAAO,CAACZ,IAAI,IAAI,SAAS,GAAG,EAAE,gBAAAR,MAAA,CAAeoB,OAAO,CAACY,OAAO,OAAG;IAC/E,IAAMC,KAAK,GAAGb,OAAO,CAACZ,IAAI,IAAI,SAAS,GAAG,EAAE,cAAAR,MAAA,CAAaoB,OAAO,CAACa,KAAK,OAAG;IACzE,IAAMhB,MAAM,GAAGG,OAAO,CAACH,MAAM,eAAAjB,MAAA,CAAcoB,OAAO,CAACH,MAAM,UAAM,EAAE;IACjE,IAAMiB,QAAQ,GAAGd,OAAO,CAACc,QAAQ,CAACC,GAAG,CAAC,UAAAC,EAAE;MAAA,OAAId,OAAO,CAACc,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC;IAAA,EAAC;IACnE,IAAIrC,GAAG,cAAAC,MAAA,CAAcQ,IAAI,OAAAR,MAAA,CAAIgC,OAAO,OAAAhC,MAAA,CAAIiC,KAAK,OAAAjC,MAAA,CAAIiB,MAAM,eAAAjB,MAAA,CACnDkC,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC,oBACb;IACVvC,GAAG,GAAGoB,UAAU,CAACC,OAAO,EAAErB,GAAG,CAAC;IAC9B,OAAOA,GAAG;EACZ;AACF,CAAC;AAED,IAAM6B,IAAI,GAAG;EACX,WAAW,EAAE,SAAAW,SAAAH,EAAE,EAAI;IACjB,IAAAI,YAAA,GAEIC,WAAW,CAACL,EAAE,CAAC;MADjBV,GAAG,GAAAc,YAAA,CAAHd,GAAG;MAAEhB,QAAQ,GAAA8B,YAAA,CAAR9B,QAAQ;IAEf,IAAMF,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,UAAM,EAAE;IAC/C,IAAMkC,IAAI,GAAGN,EAAE,CAACM,IAAI,aAAA1C,MAAA,CAAYoC,EAAE,CAACM,IAAI,UAAM,EAAE;IAC/C,IAAM5B,IAAI,GAAGsB,EAAE,CAACtB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,UAAM,EAAE;IAC/C,IAAIP,KAAK,GAAGoC,kBAAkB,CAACP,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI0C,IAAI,OAAA1C,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC3E,CAAC;EACD,UAAU,EAAE,SAAAkB,QAAAR,EAAE,EAAI;IAChB,IAAAS,aAAA,GAEIJ,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAmC,aAAA,CAARnC,QAAQ;MAAEoB,MAAM,GAAAe,aAAA,CAANf,MAAM;MAAEgB,SAAS,GAAAD,aAAA,CAATC,SAAS;MAAEC,WAAW,GAAAF,aAAA,CAAXE,WAAW;MAAEC,KAAK,GAAAH,aAAA,CAALG,KAAK;IAEjD,IAAMC,SAAS,GAAGb,EAAE,CAACa,SAAS,mBAAAjD,MAAA,CAAkBoC,EAAE,CAACa,SAAS,UAAM,EAAE;IACpE,IAAMC,aAAa,GAAGd,EAAE,CAAC,iBAAiB,CAAC,GAAG,iBAAiB,GAAG,EAAE;IACpE,IAAMe,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMC,UAAU,GAAGhB,EAAE,CAAC,aAAa,CAAC,mBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMiB,UAAU,GAAGjB,EAAE,CAAC,aAAa,CAAC,mBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,SAAM,EAAE;IAChF,IAAMkB,YAAY,GAAGlB,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM5B,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,UAAM,EAAE;IAC/C,IAAM+C,QAAQ,GAAGnB,EAAE,CAACmB,QAAQ,IAAInB,EAAE,CAACmB,QAAQ,CAACC,OAAO,4BAAAxD,MAAA,CACvBoC,EAAE,CAACmB,QAAQ,CAACC,OAAO,iBAAAxD,MAAA,CAAcoC,EAAE,CAACmB,QAAQ,CAACE,OAAO,WAC5E,EAAE;IACN,IAAIlD,KAAK,GAAGmD,iBAAiB,CAACtB,EAAE,CAAC;IAEjC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIiD,SAAS,OAAAjD,MAAA,CAAIkD,aAAa,OAAAlD,MAAA,CAAImD,QAAQ,OAAAnD,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIoD,UAAU,OAAApD,MAAA,CAAIqD,UAAU,OAAArD,MAAA,CAAIsD,YAAY,OAAAtD,MAAA,CAAIuD,QAAQ,OAAAvD,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC5M,CAAC;EACD,iBAAiB,EAAE,SAAAiC,cAAAvB,EAAE,EAAI;IACvB,IAAAwB,aAAA,GAAwCnB,WAAW,CAACL,EAAE,CAAC;MAAhD1B,QAAQ,GAAAkD,aAAA,CAARlD,QAAQ;MAAEoB,MAAM,GAAA8B,aAAA,CAAN9B,MAAM;MAAEiB,WAAW,GAAAa,aAAA,CAAXb,WAAW;IACpC,IAAMc,gBAAgB,GAAGzB,EAAE,CAAC,mBAAmB,CAAC,wBAAApC,MAAA,CAAwBoC,EAAE,CAAC,mBAAmB,CAAC,IAAK,EAAE;IACtG,IAAM0B,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAAhE,MAAA,CAAaoC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMC,YAAY,GAAG7B,EAAE,CAAC,eAAe,CAAC,GAAG,eAAe,GAAG,EAAE;IAC/D,IAAM8B,SAAS,GAAG9B,EAAE,CAAC8B,SAAS,kBAAAlE,MAAA,CAAkBoC,EAAE,CAAC8B,SAAS,SAAM,EAAE;IAEpE,WAAAlE,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIgE,IAAI,OAAAhE,MAAA,CAAIiE,YAAY,OAAAjE,MAAA,CAAIkE,SAAS,OAAAlE,MAAA,CAAI6D,gBAAgB,OAAA7D,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC3I,CAAC;EACD,WAAW,EAAE,SAAAyC,SAAA/B,EAAE,EAAI;IACjB,IAAAgC,aAAA,GAEI3B,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAA0D,aAAA,CAAR1D,QAAQ;MAAEoB,MAAM,GAAAsC,aAAA,CAANtC,MAAM;MAAEgB,SAAS,GAAAsB,aAAA,CAATtB,SAAS;MAAEC,WAAW,GAAAqB,aAAA,CAAXrB,WAAW;MAAEC,KAAK,GAAAoB,aAAA,CAALpB,KAAK;IAEjD,IAAMqB,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAI/D,KAAK,GAAGgE,kBAAkB,CAACnC,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIsE,QAAQ,OAAAtE,MAAA,CAAIqE,UAAU,OAAArE,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EAC5H,CAAC;EACD,gBAAgB,EAAE,SAAA8C,aAAApC,EAAE,EAAI;IACtB,IAAAqC,aAAA,GAA2BhC,WAAW,CAACL,EAAE,CAAC;MAAnC1B,QAAQ,GAAA+D,aAAA,CAAR/D,QAAQ;MAAEoB,MAAM,GAAA2C,aAAA,CAAN3C,MAAM;IACvB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAIP,KAAK,GAAGmE,sBAAsB,CAACtC,EAAE,CAAC;IAEtC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACrE,CAAC;EACD,mBAAmB,EAAE,SAAAiD,gBAAAvC,EAAE,EAAI;IACzB,IAAAwC,aAAA,GAA2BnC,WAAW,CAACL,EAAE,CAAC;MAAnC1B,QAAQ,GAAAkE,aAAA,CAARlE,QAAQ;MAAEoB,MAAM,GAAA8C,aAAA,CAAN9C,MAAM;IACvB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAMgD,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,aAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,UAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,aAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,UAAM,EAAE;IAC5C,IAAIxD,KAAK,GAAGsE,yBAAyB,CAACzC,EAAE,CAAC;IAEzC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACnF,CAAC;EACD,WAAW,EAAE,SAAAoD,SAAA1C,EAAE,EAAI;IACjB,IAAA2C,aAAA,GAA2BtC,WAAW,CAACL,EAAE,CAAC;MAAnC1B,QAAQ,GAAAqE,aAAA,CAARrE,QAAQ;MAAEoB,MAAM,GAAAiD,aAAA,CAANjD,MAAM;IACvB,IAAMkD,UAAU,GAAG5C,EAAE,CAAC,aAAa,CAAC,oBAAApC,MAAA,CAAmBoC,EAAE,CAAC,aAAa,CAAC,UAAM,EAAE;IAChF,IAAM6C,YAAY,GAAG7C,EAAE,CAAC,eAAe,CAAC,sBAAApC,MAAA,CAAqBoC,EAAE,CAAC,eAAe,CAAC,UAAM,EAAE;IACxF,IAAM8C,WAAW,GAAG9C,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM+C,aAAa,GAAG/C,EAAE,CAAC,gBAAgB,CAAC,uBAAApC,MAAA,CAAsBoC,EAAE,CAAC,gBAAgB,CAAC,UAAM,EAAE;IAC5F,IAAMgD,WAAW,GAAGhD,EAAE,CAAC,cAAc,CAAC,IAAI,IAAI,qBAAApC,MAAA,CAAqBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,cAAc,CAAC,CAAC,SAAM,EAAE;IAC7G,IAAMmD,aAAa,GAAGnD,EAAE,CAAC,gBAAgB,CAAC,IAAI,KAAK,uBAAApC,MAAA,CAAuBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAEtH,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIgF,UAAU,OAAAhF,MAAA,CAAIiF,YAAY,OAAAjF,MAAA,CAAIkF,WAAW,OAAAlF,MAAA,CAAImF,aAAa,OAAAnF,MAAA,CAAIoF,WAAW,OAAApF,MAAA,CAAIuF,aAAa,OAAAvF,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACnJ,CAAC;EACD,aAAa,EAAE,SAAA8D,WAAApD,EAAE,EAAI;IACnB,IAAAqD,aAAA,GAEIhD,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAA+E,aAAA,CAAR/E,QAAQ;MAAEoB,MAAM,GAAA2D,aAAA,CAAN3D,MAAM;MAAEgB,SAAS,GAAA2C,aAAA,CAAT3C,SAAS;MAAEC,WAAW,GAAA0C,aAAA,CAAX1C,WAAW;MAAEC,KAAK,GAAAyC,aAAA,CAALzC,KAAK;IAEjD,IAAM0C,OAAO,GAAGtD,EAAE,CAACsD,OAAO,iBAAA1F,MAAA,CAAgBoC,EAAE,CAACN,MAAM,iBAAa,EAAE;IAClE,IAAM6D,KAAK,GAAGvD,EAAE,CAACuD,KAAK,eAAA3F,MAAA,CAAcoC,EAAE,CAACN,MAAM,eAAW,EAAE;IAC1D,IAAM8D,aAAa,GAAGxD,EAAE,CAAC,iBAAiB,CAAC,GAAG,EAAE,GAAG,0BAA0B;IAC7E,IAAMiC,UAAU,GAAGjC,EAAE,CAACiC,UAAU,GAAG,YAAY,GAAG,EAAE;IACpD,IAAMwB,SAAS,GAAGzD,EAAE,CAACyD,SAAS,IAAI,GAAG,GAAG,EAAE,kBAAA7F,MAAA,CAAiBoC,EAAE,CAACyD,SAAS,OAAG;IAE1E,WAAA7F,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI0F,OAAO,OAAA1F,MAAA,CAAI2F,KAAK,OAAA3F,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI4F,aAAa,OAAA5F,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAI6F,SAAS,OAAA7F,MAAA,CAAIqE,UAAU,OAAArE,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC1J,CAAC;EACD,WAAW,EAAE,SAAAoE,SAAA1D,EAAE,EAAI;IACjB,IAAA2D,aAAA,GAA2BtD,WAAW,CAACL,EAAE,CAAC;MAAnC1B,QAAQ,GAAAqF,aAAA,CAARrF,QAAQ;MAAEoB,MAAM,GAAAiE,aAAA,CAANjE,MAAM;IACvB,IAAMgC,GAAG,GAAG1B,EAAE,CAAC0B,GAAG,YAAA9D,MAAA,CAAYoC,EAAE,CAAC0B,GAAG,SAAM,EAAE;IAC5C,IAAMC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMC,IAAI,GAAG5B,EAAE,CAAC4B,IAAI,aAAAhE,MAAA,CAAaoC,EAAE,CAAC4B,IAAI,SAAM,EAAE;IAChD,IAAMgC,KAAK,GAAG5D,EAAE,CAAC4D,KAAK,GAAG,OAAO,GAAG,EAAE;IACrC,IAAMC,SAAS,GAAG7D,EAAE,CAAC,YAAY,CAAC,oBAAApC,MAAA,CAAmBoC,EAAE,CAAC,YAAY,CAAC,UAAM,EAAE;IAE7E,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8D,GAAG,OAAA9D,MAAA,CAAI+D,GAAG,OAAA/D,MAAA,CAAIgE,IAAI,OAAAhE,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIgG,KAAK,OAAAhG,MAAA,CAAIiG,SAAS,OAAAjG,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACjG,CAAC;EACD,gBAAgB,EAAE,SAAAwE,aAAA9D,EAAE,EAAI;IACtB,IAAA+D,cAAA,GAEI1D,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAyF,cAAA,CAARzF,QAAQ;MAAEoB,MAAM,GAAAqE,cAAA,CAANrE,MAAM;MAAEgB,SAAS,GAAAqD,cAAA,CAATrD,SAAS;MAAEC,WAAW,GAAAoD,cAAA,CAAXpD,WAAW;MAAEC,KAAK,GAAAmD,cAAA,CAALnD,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAApC,MAAA,CAAyBoC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMmE,OAAO,GAAGnE,EAAE,CAAC,UAAU,CAAC,GAAG,UAAU,GAAG,EAAE;IAChD,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAxG,MAAA,CAAcoC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAMsE,aAAa,GAAGtE,EAAE,CAAC,gBAAgB,CAAC,uBAAApC,MAAA,CAAuBqF,IAAI,CAACC,SAAS,CAAClD,EAAE,CAAC,gBAAgB,CAAC,CAAC,SAAM,EAAE;IAE7G,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIuG,OAAO,OAAAvG,MAAA,CAAIwG,MAAM,OAAAxG,MAAA,CAAIyG,WAAW,OAAAzG,MAAA,CAAI0G,aAAa,OAAA1G,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIoG,gBAAgB,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAIsG,cAAc,OAAAtG,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACvM,CAAC;EACD,gBAAgB,EAAE,SAAAiF,aAAAvE,EAAE,EAAI;IACtB,IAAAwE,cAAA,GAEInE,WAAW,CAACL,EAAE,CAAC;MADjB1B,QAAQ,GAAAkG,cAAA,CAARlG,QAAQ;MAAEoB,MAAM,GAAA8E,cAAA,CAAN9E,MAAM;MAAEgB,SAAS,GAAA8D,cAAA,CAAT9D,SAAS;MAAEC,WAAW,GAAA6D,cAAA,CAAX7D,WAAW;MAAEC,KAAK,GAAA4D,cAAA,CAAL5D,KAAK;IAEjD,IAAMoD,gBAAgB,GAAGhE,EAAE,CAAC,mBAAmB,CAAC,0BAAApC,MAAA,CAAyBoC,EAAE,CAAC,mBAAmB,CAAC,UAAM,EAAE;IACxG,IAAMiE,cAAc,GAAGjE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMkE,cAAc,GAAGlE,EAAE,CAAC,iBAAiB,CAAC,wBAAApC,MAAA,CAAuBoC,EAAE,CAAC,iBAAiB,CAAC,UAAM,EAAE;IAChG,IAAMoE,MAAM,GAAGpE,EAAE,CAACoE,MAAM,eAAAxG,MAAA,CAAcoC,EAAE,CAACoE,MAAM,UAAM,EAAE;IACvD,IAAMC,WAAW,GAAGrE,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IACpF,IAAM5B,IAAI,GAAG4B,EAAE,CAAC5B,IAAI,IAAI,MAAM,GAAG,EAAE,aAAAR,MAAA,CAAYoC,EAAE,CAAC5B,IAAI,OAAG;IACzD,IAAM2C,QAAQ,GAAGf,EAAE,CAACe,QAAQ,GAAG,UAAU,GAAG,EAAE;IAE9C,WAAAnD,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAIQ,IAAI,OAAAR,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIwG,MAAM,OAAAxG,MAAA,CAAIyG,WAAW,OAAAzG,MAAA,CAAIgD,KAAK,OAAAhD,MAAA,CAAI+C,WAAW,OAAA/C,MAAA,CAAIoG,gBAAgB,OAAApG,MAAA,CAAIqG,cAAc,OAAArG,MAAA,CAAIsG,cAAc,OAAAtG,MAAA,CAAI8C,SAAS,OAAA9C,MAAA,CAAImD,QAAQ,OAAAnD,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC/L,CAAC;EACD,SAAS,EAAE,SAAAmF,OAAAzE,EAAE,EAAI;IACf,IAAA0E,cAAA,GAA2BrE,WAAW,CAACL,EAAE,CAAC;MAAnC1B,QAAQ,GAAAoG,cAAA,CAARpG,QAAQ;MAAEoB,MAAM,GAAAgF,cAAA,CAANhF,MAAM;IACvB,IAAMiC,GAAG,GAAG3B,EAAE,CAAC2B,GAAG,YAAA/D,MAAA,CAAYoC,EAAE,CAAC2B,GAAG,SAAM,EAAE;IAC5C,IAAMgD,SAAS,GAAG3E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAM4E,QAAQ,GAAG5E,EAAE,CAAC,WAAW,CAAC,GAAG,WAAW,GAAG,EAAE;IACnD,IAAM6E,SAAS,GAAG7E,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IAEtD,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAI+G,SAAS,OAAA/G,MAAA,CAAIgH,QAAQ,OAAAhH,MAAA,CAAIiH,SAAS,OAAAjH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EAC3F,CAAC;EACD,iBAAiB,EAAE,SAAAwF,cAAA9E,EAAE,EAAI;IACvB,IAAA+E,cAAA,GAA2B1E,WAAW,CAACL,EAAE,CAAC;MAAnC1B,QAAQ,GAAAyG,cAAA,CAARzG,QAAQ;MAAEoB,MAAM,GAAAqF,cAAA,CAANrF,MAAM;IACvB,IAAMhB,IAAI,aAAAd,MAAA,CAAYoC,EAAE,CAACtB,IAAI,OAAG;IAChC,IAAMsG,SAAS,GAAGhF,EAAE,CAAC,YAAY,CAAC,GAAG,YAAY,GAAG,EAAE;IACtD,IAAMiF,WAAW,GAAGjF,EAAE,CAAC,cAAc,CAAC,qBAAApC,MAAA,CAAoBoC,EAAE,CAAC,cAAc,CAAC,UAAM,EAAE;IAEpF,WAAApC,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8B,MAAM,OAAA9B,MAAA,CAAIc,IAAI,OAAAd,MAAA,CAAIoH,SAAS,OAAApH,MAAA,CAAIqH,WAAW,OAAArH,MAAA,CAAIU,QAAQ,SAAAV,MAAA,CAAMoC,EAAE,CAACV,GAAG;EACzF,CAAC;EACD,WAAW,EAAE,SAAA4F,SAAAlF,EAAE,EAAI;IACjB,IAAM1B,QAAQ,GAAG0B,EAAE,CAAC1B,QAAQ,GAAG,oBAAoB,GAAG,EAAE;IACxD,IAAM6G,MAAM,GAAGnF,EAAE,CAACmF,MAAM,gBAAAvH,MAAA,CAAeoC,EAAE,CAACN,MAAM,gBAAY,EAAE;IAC9D,IAAMwC,QAAQ,GAAGlC,EAAE,CAACkC,QAAQ,GAAG,UAAU,GAAG,EAAE;IAC9C,IAAMkD,QAAQ,GAAGpF,EAAE,CAAC,WAAW,CAAC,IAAI,MAAM,kBAAApC,MAAA,CAAiBoC,EAAE,CAAC,WAAW,CAAC,UAAM,EAAE;IAClF,IAAMqF,MAAM,GAAGrF,EAAE,CAACqF,MAAM,eAAAzH,MAAA,CAAcoC,EAAE,CAACqF,MAAM,UAAM,EAAE;IACvD,IAAMC,IAAI,GAAGtF,EAAE,CAACsF,IAAI,IAAI,MAAM,aAAA1H,MAAA,CAAYoC,EAAE,CAACsF,IAAI,UAAM,EAAE;IACzD,IAAMC,UAAU,GAAGvF,EAAE,CAAC,aAAa,CAAC,IAAI,KAAK,GAAG,sBAAsB,GAAG,EAAE;IAC3E,IAAMwF,YAAY,uBAAA5H,MAAA,CAAsBoC,EAAE,CAACN,MAAM,mBAAe;IAChE,IAAM+F,QAAQ,mBAAA7H,MAAA,CAAkBoC,EAAE,CAACN,MAAM,eAAW;IACpD,IAAMgG,GAAG,YAAA9H,MAAA,CAAWoC,EAAE,CAACN,MAAM,OAAG;IAChC,IAAIvB,KAAK,GAAGwH,kBAAkB,CAAC3F,EAAE,CAAC;IAElC,IAAI7B,KAAK,EAAEA,KAAK,QAAAP,MAAA,CAAQO,KAAK,OAAI,EAAC;IAClC,WAAAP,MAAA,CAAWoC,EAAE,CAACV,GAAG,OAAA1B,MAAA,CAAI8H,GAAG,OAAA9H,MAAA,CAAI6H,QAAQ,OAAA7H,MAAA,CAAIuH,MAAM,OAAAvH,MAAA,CAAI2H,UAAU,OAAA3H,MAAA,CAAIsE,QAAQ,OAAAtE,MAAA,CAAI4H,YAAY,OAAA5H,MAAA,CAAIwH,QAAQ,OAAAxH,MAAA,CAAIyH,MAAM,OAAAzH,MAAA,CAAI0H,IAAI,OAAA1H,MAAA,CAAIU,QAAQ,OAAAV,MAAA,CAAIO,KAAK,QAAAP,MAAA,CAAKoC,EAAE,CAACV,GAAG;EACxJ;AACF,CAAC;AAED,SAASe,WAAWA,CAACL,EAAE,EAAE;EACvB,OAAO;IACLN,MAAM,eAAA9B,MAAA,CAAcJ,UAAU,CAACgB,SAAS,OAAAZ,MAAA,CAAIoC,EAAE,CAACN,MAAM,OAAG;IACxDgB,SAAS,EAAEV,EAAE,CAACU,SAAS,GAAG,WAAW,GAAG,EAAE;IAC1CC,WAAW,EAAEX,EAAE,CAACW,WAAW,oBAAA/C,MAAA,CAAmBoC,EAAE,CAACW,WAAW,UAAM,EAAE;IACpEC,KAAK,EAAEZ,EAAE,CAAC4F,KAAK,IAAI5F,EAAE,CAAC4F,KAAK,CAAChF,KAAK,GAAG,4BAA4B,GAAG,EAAE;IACrEtC,QAAQ,EAAE0B,EAAE,CAAC1B,QAAQ,GAAG,oBAAoB,GAAG;EACjD,CAAC;AACH;;AAEA;AACA,SAASiC,kBAAkBA,CAACrC,IAAI,EAAE;EAChC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAAC2H,OAAO,EAAE;IAChB/F,QAAQ,CAACgG,IAAI,CAAC5H,IAAI,CAAC2H,OAAO,CAAC;EAC7B;EACA,OAAO/F,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;;AAEA;AACA,SAASoB,iBAAiBA,CAACpD,IAAI,EAAE;EAC/B,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAAC6H,OAAO,EAAE;IAChBjG,QAAQ,CAACgG,IAAI,+BAAAlI,MAAA,CAA6BM,IAAI,CAAC6H,OAAO,gBAAa,CAAC;EACtE;EACA,IAAI7H,IAAI,CAAC8H,MAAM,EAAE;IACflG,QAAQ,CAACgG,IAAI,8BAAAlI,MAAA,CAA4BM,IAAI,CAAC8H,MAAM,gBAAa,CAAC;EACpE;EACA,OAAOlG,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASiC,kBAAkBA,CAACjE,IAAI,EAAE;EAChC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvCnG,QAAQ,CAACgG,IAAI,wCAAAlI,MAAA,CAAuCM,IAAI,CAACwB,MAAM,kHAAsG,CAAC;EACxK;EACA,OAAOI,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASoC,sBAAsBA,CAACpE,IAAI,EAAE;EACpC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGpB,IAAI,CAACgI,UAAU,IAAI,QAAQ,GAAG,iBAAiB,GAAG,UAAU;IACxE,IAAMC,MAAM,GAAGjI,IAAI,CAACiI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAlI,MAAA,CAAK0B,GAAG,gCAAA1B,MAAA,CAA4BM,IAAI,CAACwB,MAAM,iFAAA9B,MAAA,CAAuEuI,MAAM,uBAAAvI,MAAA,CAAoB0B,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASuC,yBAAyBA,CAACvE,IAAI,EAAE;EACvC,IAAM4B,QAAQ,GAAG,EAAE;EACnB,IAAI5B,IAAI,CAACoF,OAAO,IAAIpF,IAAI,CAACoF,OAAO,CAAC2C,MAAM,EAAE;IACvC,IAAM3G,GAAG,GAAGpB,IAAI,CAACgI,UAAU,IAAI,QAAQ,GAAG,oBAAoB,GAAG,aAAa;IAC9E,IAAMC,MAAM,GAAGjI,IAAI,CAACiI,MAAM,GAAG,QAAQ,GAAG,EAAE;IAC1CrG,QAAQ,CAACgG,IAAI,KAAAlI,MAAA,CAAK0B,GAAG,gCAAA1B,MAAA,CAA4BM,IAAI,CAACwB,MAAM,iFAAA9B,MAAA,CAAuEuI,MAAM,uBAAAvI,MAAA,CAAoB0B,GAAG,MAAG,CAAC;EACtK;EACA,OAAOQ,QAAQ,CAACI,IAAI,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASyF,kBAAkBA,CAACzH,IAAI,EAAE;EAChC,IAAMkI,IAAI,GAAG,EAAE;EACf,IAAIlI,IAAI,CAAC,WAAW,CAAC,IAAI,cAAc,EAAEkI,IAAI,CAACN,IAAI,CAAC,8BAA8B,CAAC,MAC7EM,IAAI,CAACN,IAAI,sEAAAlI,MAAA,CAAgEM,IAAI,CAACmI,UAAU,iBAAc,CAAC;EAC5G,IAAInI,IAAI,CAACoI,OAAO,EAAEF,IAAI,CAACN,IAAI,0FAAAlI,MAAA,CAAmDM,IAAI,CAACqI,QAAQ,EAAA3I,MAAA,CAAGM,IAAI,CAACsI,QAAQ,aAAA5I,MAAA,CAAKM,IAAI,CAACmH,MAAM,uBAAU,CAAC;EACtI,OAAOe,IAAI,CAAClG,IAAI,CAAC,IAAI,CAAC;AACxB;AAEO,SAASuG,UAAUA,CAACvI,IAAI,EAAEE,IAAI,EAAE;EACrC,IAAMsI,QAAQ,GAAG,EAAE;EACnBlJ,UAAU,GAAGU,IAAI;EACjBT,eAAe,GAAGS,IAAI,CAACyI,MAAM,CAACC,IAAI,CAAC,UAAAC,IAAI;IAAA,OAAIA,IAAI,CAAC5H,IAAI,IAAI,EAAE;EAAA,EAAC;EAC3Df,IAAI,CAACyI,MAAM,CAACG,OAAO,CAAC,UAAA9G,EAAE,EAAI;IACxB0G,QAAQ,CAACZ,IAAI,CAAC5G,OAAO,CAACc,EAAE,CAACC,MAAM,CAAC,CAACD,EAAE,CAAC,CAAC;EACvC,CAAC,CAAC;EACF,IAAM+G,OAAO,GAAGL,QAAQ,CAACxG,IAAI,CAAC,IAAI,CAAC;EAEnC,IAAI8G,IAAI,GAAG/I,iBAAiB,CAACC,IAAI,EAAE6I,OAAO,EAAE3I,IAAI,CAAC;EACjD,IAAIA,IAAI,IAAI,QAAQ,EAAE;IACpB4I,IAAI,GAAGtJ,aAAa,CAACsJ,IAAI,CAAC;EAC5B;EACAxJ,UAAU,GAAG,IAAI;EACjB,OAAOwJ,IAAI;AACb"}]}