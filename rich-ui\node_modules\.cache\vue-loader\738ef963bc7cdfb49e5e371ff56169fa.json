{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Message\\index.vue?vue&type=style&index=0&id=a9dfc86c&lang=scss&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Message\\index.vue", "mtime": 1754876882532}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZWwtYmFkZ2VfX2NvbnRlbnQuaXMtZml4ZWQgew0KICB0b3A6IDE1cHg7DQp9DQoNCi5pbmZpbml0ZS1saXN0LWl0ZW0gew0KICAmLmhvdmVyLWVmZmVjdCB7DQogICAgY3Vyc29yOiBwb2ludGVyOw0KICAgIHRyYW5zaXRpb246IGJhY2tncm91bmQgLjNzOw0KDQogICAgJjpob3ZlciB7DQogICAgICBiYWNrZ3JvdW5kOiByZ2JhKDAsIDAsIDAsIC4xMjUpDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6JA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Message", "sourcesContent": ["<template>\r\n  <div class=\"header-search\">\r\n    <el-popover\r\n      placement=\"bottom\"\r\n      trigger=\"hover\"\r\n      width=\"400\"\r\n      @show=\"showMessage\">\r\n      <el-badge slot=\"reference\" :hidden=\"length==0\" :value=\"length>99?'···':length\">\r\n        <i :class=\"length==0?'el-icon-chat-round':'el-icon-chat-dot-round'\"/>\r\n      </el-badge>\r\n      <el-row>\r\n        <div style=\"display:flex;float: right\">\r\n          <h5 style=\"margin: 0;display: contents\">打开提示：</h5>\r\n          <el-switch\r\n            v-model=\"open\"\r\n            active-value=\"true\"\r\n            inactive-value=\"false\"\r\n            @change=\"setMessageNotice\"/>\r\n        </div>\r\n      </el-row>\r\n      <el-row v-infinite-scroll=\"load\" class=\"infinite-list\" style=\"overflow:auto;height:500px\">\r\n        <div\r\n          v-for=\"data of dataList\"\r\n          id=\"id\"\r\n          class=\"infinite-list-item hover-effect\"\r\n          style=\"cursor: pointer;\"\r\n          @click=\"toPath(data.messageFrom)\">\r\n          <el-row>\r\n            <div style=\"font-weight:bold;\">{{ data.messageTitle }}</div>\r\n          </el-row>\r\n          <el-row>\r\n            <div style=\"margin: 5px;margin-left: 10px\">{{ data.messageContent }}</div>\r\n          </el-row>\r\n          <el-row>\r\n            <div style=\"float: left;font-size: smaller;display: contents\">{{ data.messageTypeName }}</div>\r\n            <div style=\"float: right;color: lightgrey\">{{ formatDate(data.createTime) }}</div>\r\n          </el-row>\r\n        </div>\r\n        <el-button v-if=\"showHistory\" style=\"color: lightgrey;width: 100%\" @click=\"resolvePath\">\r\n          查看全部历史消息\r\n        </el-button>\r\n      </el-row>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {createSocket} from \"@/utils/webSocket\";\r\nimport {countNewMessage, listMessage} from \"@/api/system/message\";\r\nimport {formatDate, formatTime} from \"@/utils\";\r\n\r\nexport default {\r\n  name: \"Message\",\r\n  data() {\r\n    return {\r\n      dataList: [],\r\n      getSocketData: null,\r\n      length: 0,\r\n      showHistory: false,\r\n      open: false,\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        messageOwner: this.$store.state.user.sid,\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.initWebSocket();\r\n    this.countNewMessage();\r\n  },\r\n  destroyed() {\r\n    //移除监听器\r\n    window.removeEventListener('onmessageWS', this.getSocketData);\r\n  },\r\n  methods: {\r\n    formatDate(val) {\r\n      return formatTime(new Date(val));\r\n    },\r\n    resolvePath() {\r\n      this.$tab.openPage(\"查看全部历史\", '/internal/message', {pageNum: 1});\r\n    },\r\n    toPath(val) {\r\n      this.$tab.openPage(\"修改海运费\", '/enter/freight', {data: val, pageNum: 1});\r\n    },\r\n    showMessage() {\r\n      this.showHistory = false\r\n      localStorage.setItem(\"lastTimeTap\", formatDate(new Date()))\r\n      if (this.dataList.length == 0) {\r\n        this.dataList = []\r\n        listMessage(this.queryParams).then(response => {\r\n          for (const r of response.rows) {\r\n            this.dataList.push(r)\r\n          }\r\n          if (response.total <= 10)\r\n            this.showHistory = true\r\n          this.length = 0;\r\n        })\r\n      } else {\r\n        this.length = 0\r\n      }\r\n    },\r\n    load() {\r\n      if (this.queryParams.pageNum == 3 || this.dataList.length > 30 || this.isFull) {\r\n        this.showHistory = true\r\n      } else {\r\n        this.showHistory = false\r\n        this.queryParams.pageNum += 1\r\n        listMessage(this.queryParams).then(response => {\r\n          if (response.rows.length > 0) {\r\n            for (const r of response.rows) {\r\n              this.dataList.push(r)\r\n            }\r\n          }\r\n          const totalPage = response.total\r\n          if ((totalPage < 10 && this.queryParams.pageNum == 1) || (10 < totalPage <= 20 && this.queryParams.pageNum == 2) || (20 < totalPage <= 30 && this.queryParams.pageNum == 3)) {\r\n            this.showHistory = true\r\n            this.isFull = true\r\n          }\r\n        })\r\n      }\r\n    },\r\n    countNewMessage() {\r\n      const time = localStorage.getItem('lastTimeTap')\r\n      this.open = localStorage.getItem(\"messageNotice\")\r\n      let data = {\r\n        messageOwner: this.$store.state.user.sid,\r\n        createTime: time\r\n      }\r\n      countNewMessage(data).then(response => {\r\n        this.length = response.data\r\n      })\r\n    },\r\n    setMessageNotice() {\r\n      localStorage.setItem(\"messageNotice\", this.open)\r\n    },\r\n    initWebSocket() {\r\n      this.uid = this.$store.state.user.sid + this.$store.state.user.name.substring(0, 8);\r\n      createSocket((window.location.protocol === 'https:' ? 'wss://sys.richgz.com/' : 'ws://************:8088/') + 'websocket/' + this.uid)\r\n      // createSocket('wss://**********:8088/websocket/' + this.uid)\r\n      this.getSocketData = e => {\r\n        if (this.length == null) {\r\n          this.length = 1\r\n        } else {\r\n          ++this.length\r\n        }\r\n        if (this.open) {\r\n          this.$message.info(e.detail.data)\r\n        }\r\n      }\r\n      window.addEventListener('onmessageWS', this.getSocketData)\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.el-badge__content.is-fixed {\r\n  top: 15px;\r\n}\r\n\r\n.infinite-list-item {\r\n  &.hover-effect {\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .125)\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}