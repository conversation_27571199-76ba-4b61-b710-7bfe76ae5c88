<template>
  <div class="app-container">
    <el-row>
      <el-col :span="showLeft">
        <el-form v-if="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="query" :size="size"
                 label-width="35px"
        >
          <el-form-item v-if="roleClient==='1'" label="所属" prop="queryStaffId">
            <treeselect v-model="queryBFStaffId" :disable-branch-nodes="true" :disable-fuzzy-matching="true"
                        :flatten-search-results="true" :normalizer="staffNormalizer"
                        :options="belongList" :show-count="true" placeholder="所属人" style="width: 100%"
                        @input="cleanBFStaffId" @open="loadSales" @select="handleSelectBFStaffId"
            >
              <div slot="value-label" slot-scope="{node}">
                {{
                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                }}
              </div>
              <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                     :class="labelClassName"
              >
                {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
              </label>
            </treeselect>
          </el-form-item>
          <el-form-item v-if="roleTypeId==='2'" label="所属" prop="queryStaffId">
            <treeselect v-model="queryBStaffId" :disable-branch-nodes="true" :disable-fuzzy-matching="true"
                        :flatten-search-results="true" :normalizer="staffNormalizer" :options="businessList"
                        :show-count="true" placeholder="所属人" style="width: 100%"
                        @input="cleanBStaffId" @open="loadBusinesses" @select="handleSelectBStaffId"
            >
              <div slot="value-label" slot-scope="{node}">
                {{
                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                }}
              </div>
              <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                     :class="labelClassName"
              >
                {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
              </label>
            </treeselect>
          </el-form-item>
          <!--          <el-form-item v-if="roleTypeId==='1'" label="闲置">
                      <el-select v-model="queryParams.idleStatus" placeholder="是否闲置" style="width: 100%"
                                 @change="handleQuery"
                      >
                        <el-option
                          v-for="dict in dict.type.sys_is_idle"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>-->
          <el-form-item label="客户" prop="companyQuery">
            <el-input v-model="queryParams.companyQuery" clearable placeholder="客户名称/代码" style="width: 100%"
                      @change="handleQuery"
            />
          </el-form-item>
          <!--行政区域-->
          <el-form-item label="地址" prop="locationId">
            <location-select :multiple="false" :pass="queryParams.locationId"
                             :load-options="locationOptions" style="width: 100%"
                             @return="queryLocationId"
            />
          </el-form-item>
          <el-form-item label="信用" prop="creditLevel">
            <tree-select :multiple="false" :pass="queryParams.creditLevel"
                         :type="'creditLevel'" style="width: 100%"
                         @return="handleQuery"
            />
          </el-form-item>
          <!--供应商提供哪些服务如：海运、铁路等-->
          <el-form-item label="服务" prop="serviceTypeIds">
            <tree-select :flat="false" :multiple="true" :pass="queryParams.serviceTypeIds"
                         :placeholder="'服务类型'" :type="'serviceType'" style="width: 100%"
                         :d-load="true" @return="queryServiceTypeIds"
            />
          </el-form-item>
          <el-form-item label="启运" prop="locationDepartureIds">
            <location-select :multiple="true" :pass="queryParams.locationDepartureIds"
                             :load-options="locationOptions" style="width: 100%"
                             @return="queryLocationDepartureIds"
            />
          </el-form-item>
          <!--目的区域-->
          <el-form-item label="目的" prop="locationDestinationIds">
            <location-select :multiple="true" :pass="queryParams.locationDestinationIds"
                             :en="true" :load-options="locationOptions" style="width: 100%"
                             @return="queryLocationDestinationIds"
            />
          </el-form-item>
          <!--公司角色-->
          <el-form-item label="角色" prop="locationDestinationIds">
            <tree-select :flat="false" :multiple="true" :pass="queryParams.roleIds" :type="'companyRole'"
                         @return="queryCompanyRoleIds"
            />
          </el-form-item>
          <!--目的航线-->
          <el-form-item label="航线" prop="lineDestinationIds">
            <tree-select :flat="false" :multiple="true" :pass="queryParams.lineDestinationIds"
                         :placeholder="'目的航线'" :type="'line'" style="width: 100%"
                         :d-load="true" @return="queryLineDestinationIds"
            />
          </el-form-item>
          <el-form-item label="货物" prop="cargoTypeIds">
            <tree-select :flat="false" :multiple="true" :pass="queryParams.cargoTypeIds" :placeholder="'货物特征'"
                         :d-load="true" :type="'cargoType'" style="width: 100%" @return="queryCargoTypeIds"
            />
          </el-form-item>
          <el-form-item label="承运" prop="carrierIds">
            <treeselect v-model="queryCarrierIds" :disable-fuzzy-matching="true" :disable-branch-nodes="true"
                        :flat="true" :flatten-search-results="true" :multiple="true"
                        :normalizer="carrierNormalizer" :options="carrierList" :show-count="true" @open="loadCarrier"
                        placeholder="优选承运人" style="width: 100%" @deselect="handleDeselectQueryCarrierIds"
                        @select="handleSelectQueryCarrierIds"
            >
              <div slot="value-label" slot-scope="{node}">
                {{ node.raw.carrier.carrierIntlCode }}
                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : "" }}
              </div>
              <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                     :class="labelClassName"
              >
                {{ node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label }}
                <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
              </label>
            </treeselect>
          </el-form-item>
          <el-form-item label="评级" prop="creditLevel">
            <el-input v-model="queryParams.creditLevel" placeholder="A~E" style="width: 100%"
                      @change="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button :size="size" icon="el-icon-search" type="primary" @click="handleQuery">搜索</el-button>
            <el-button :size="size" icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="showRight">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:company:add']"
              :size="size"
              icon="el-icon-plus"
              plain
              type="primary"
              @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:company:export']"
              :size="size"
              icon="el-icon-download"
              plain
              type="warning"
              @click="handleExport"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              :disabled="single"
              :size="size"
              icon="el-icon-user-solid"
              type="info"
              @click="handleBlackList"
            >
              拉黑
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              :disabled="selectTwo"
              :size="size"
              icon="el-icon-connection"
              @click="handleMergeCompany"
            >
              合并
            </el-button>
          </el-col>
          <right-toolbar :columns="columns" :showSearch.sync="showSearch"
                         :types="this.roleTypeId=='2'?'supplier':this.roleTypeId=='1'?'client':''"
                         @queryTable="getList" @refreshColumns="refreshColumns"
          />
        </el-row>
        <!--合并公司-->
        <el-dialog
          :close-on-click-modal="false"
          :modal-append-to-body="false"
          v-dialogDrag v-dialogDragWidth
          title="选择保留公司"
          :visible.sync="merge"
          width="800px"
        >
          <el-row v-if="mergeList.length>0" :gutter="5">
            <el-col :span="12">
              <el-descriptions title="公司1" direction="vertical" border>
                <el-descriptions-item label="简称">{{ mergeList[0].companyShortName }}</el-descriptions-item>
                <el-descriptions-item label="中文名">{{ mergeList[0].companyLocalName }}</el-descriptions-item>
                <el-descriptions-item label="英文名">{{ mergeList[0].companyEnName }}</el-descriptions-item>
                <el-descriptions-item label="选择">
                  <el-button @click="handleMerge(mergeList[0].companyId,mergeList[1].companyId)">
                    留下{{ mergeList[0].companyShortName }}
                  </el-button>
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
            <el-col :span="12">
              <el-descriptions title="公司2" direction="vertical" border>
                <el-descriptions-item label="简称">{{ mergeList[1].companyShortName }}</el-descriptions-item>
                <el-descriptions-item label="中文名">{{ mergeList[1].companyLocalName }}</el-descriptions-item>
                <el-descriptions-item label="英文名">{{ mergeList[1].companyEnName }}</el-descriptions-item>
                <el-descriptions-item label="选择">
                  <el-button @click="handleMerge(mergeList[1].companyId,mergeList[0].companyId)">
                    留下{{ mergeList[1].companyShortName }}
                  </el-button>
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
        </el-dialog>
        <!--公司列表-->
        <el-table v-if="refreshTable" v-loading="loading" :data="companyList" border
                  @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="28" align="center"/>
          <el-table-column
            v-for="data in columns"
            v-if="data.visible"
            :key="data.key"
            :align="data.align"
            :label="data.label"
            :width="data.width"
            :show-overflow-tooltip="data.tooltip"
          >
            <template v-slot="scope">
              <!--内置组件，根据:is动态加载组件-->
              <!--渲染组件根据组件内部的return方法返回指针渲染内容-->
              <component :is="data.prop" :scope="scope" @return="getReturn"/>
            </template>
          </el-table-column>
          <el-table-column align="center" label="结款方式" width="70">
            <template slot-scope="scope">
              <h6 style="margin: 0;">{{ }}</h6>
            </template>
          </el-table-column>
          <el-table-column align="center" label="额度" width="70">
            <template slot-scope="scope">
              <!--币种-->
              <span style="margin: 0;">{{
                  (roleClient == 1 || roleRich == 1) ? scope.row.receiveCurrencyCode : scope.row.payCurrencyCode
                }}</span>
              <!--额度-->
              <span style="margin: 0;">{{
                  formatDisplayCreditLimit(roleClient == 1 || roleRich == 1 ? scope.row.receiveCreditLimit : scope.row.payCreditLimit)
                }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="录入人" show-overflow-tooltip width="70">
            <template slot-scope="scope">
              <div>
                <h6 class="text-display" style="margin: 0;">{{ scope.row.updateByName }}</h6>
                <h6 class="text-display" style="margin: 0;">{{ parseTime(scope.row.updateTime, "{y}.{m}.{d}") }}</h6>
              </div>

            </template>
          </el-table-column>
          <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="90px">
            <template slot-scope="scope">
              <el-button v-hasPermi="['system:company:edit']"
                         :size="size" icon="el-icon-edit" type="success" style="margin: 0 3px"
                         @click="handleUpdate(scope.row)"
              >修改
              </el-button>
              <el-button v-hasPermi="['system:company:remove']"
                         :size="size" icon="el-icon-delete" type="danger" style="margin: 0"
                         @click="handleDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <pagination
          v-show="total>0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNum"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <!-- 添加或修改公司对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      append-to-body
      v-dialogDrag v-dialogDragWidth
      :title="title"
      :visible.sync="openCompany"
      width="55%"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="68px" class="edit">
        <el-row :gutter="5">
          <el-col :span="12">
            <!--从属信息-->
            <el-row v-if="roleClient==='1'" :gutter="5">
              <el-divider content-position="left">从属信息</el-divider>
              <el-row :gutter="5">
                <el-col :span="8">
                  <el-form-item label="业务员">
                    <treeselect v-model="belongTo" :disable-branch-nodes="true" :disable-fuzzy-matching="true"
                                :class="isLock ?'disable-form':''" :disabled=" isLock"
                                :flatten-search-results="true"
                                :normalizer="staffNormalizer" :options="belongList"
                                :show-count="true" class="sss" placeholder="选择所属人" @input="handleDeselectBelongTo"
                                @open="loadSales" @select="handleSelectBelongTo"
                    >
                      <div slot="value-label" slot-scope="{node}">
                        {{
                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                        }}
                      </div>
                      <label slot="option-label"
                             slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                             :class="labelClassName"
                      >
                        {{
                          node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label
                        }}
                        <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                      </label>
                    </treeselect>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="业务助理">
                    <treeselect v-model="followUp" :disable-branch-nodes="true" :disable-fuzzy-matching="true"
                                :flatten-search-results="true" :normalizer="staffNormalizer"
                                :options="belongList" :show-count="true" class="sss"
                                :class="isLock ?'disable-form':''" :disabled="isLock"
                                placeholder="业务员自己跟进的情况无须填写"
                                @input="handleDeselectFollowUp" @open="loadSales" @select="handleSelectFollowUp"
                    >
                      <div slot="value-label" slot-scope="{node}">
                        {{
                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + " " + node.raw.staff.staffGivingEnName : ""
                        }}
                      </div>
                      <label slot="option-label"
                             slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                             :class="labelClassName"
                      >
                        {{
                          node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label
                        }}
                        <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                      </label>
                    </treeselect>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所属公司">
                    <tree-select
                      :pass="form.companyBelongTo" :placeholder="'收付路径'"
                      :class="isLock ?'disable-form':''" :disabled="isLock" :type="'rsPaymentTitle'"
                      @return="form.companyBelongTo=$event"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-row>
            <!--基本信息-->
            <el-row>
              <el-divider content-position="left">基础资料</el-divider>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="公司代码" prop="companyTaxCode">
                    <el-input v-model="form.companyTaxCode" class=" disable-form" disabled
                              :class="'sss'" placeholder="国际通用简称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="税号" prop="companyTaxCode">
                    <el-input v-model="form.taxNo" :class="basicLock || commonLock ?'disable-form':''"
                              :disabled="basicLock || commonLock"
                              placeholder="公司税号/统一社会信用代码"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item
                    :rules="[{ required: true, message: '简称不能为空', trigger: 'blur'},{validator: validateCompanyShortName, trigger: 'blur'}]"
                    label="公司简称"
                    prop="companyShortName"
                  >
                    <el-input v-model="form.companyShortName" :class="basicLock || commonLock ?'disable-form':''"
                              :disabled="basicLock || commonLock"
                              class="sss" placeholder="公司简称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item :rules="[{ required: true, message: '抬头不能为空', trigger: 'blur'}]" label="发票抬头"
                                prop="companyLocalName">
                    <el-input v-model="form.companyLocalName" :class="basicLock || commonLock ?'disable-form':''"
                              :disabled="basicLock || commonLock" placeholder="公司发票抬头"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="行政区域" prop="locationId">
                    <location-select :load-options="locationOptions" :multiple="false" :pass="form.locationId"
                                     class="sss" @return="getLocationId"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="详细地址" prop="locationDetail">
                    <el-input v-model="form.locationDetail"
                              placeholder="详细地址信息"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="主联系人" prop="mainStaffOfficialName">
                    <el-input v-model="form.mainStaffOfficialName" class="sss"
                              placeholder="联系人名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="微信" prop="staffWechat">
                    <el-input v-model="form.staffWechat" class="sss"
                              placeholder="联系人微信"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="QQ" prop="staffQq">
                    <el-input v-model="form.staffQq"
                              placeholder="联系人QQ"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="手机" prop="staffMobile">
                    <el-input v-model="form.staffMobile" class="sss"
                              placeholder="手机"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="邮箱" prop="staffEmail">
                    <el-input v-model="form.staffEmail"
                              placeholder="联系人邮箱"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="Whatsapp" prop="staffWhatsapp">
                    <el-input v-model="form.staffWhatsapp" class="sss"
                              placeholder="联系人Whatsapp"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="联系方式" prop="staffOtherContact">
                    <el-input v-model="form.staffOtherContact"
                              placeholder="员工其他联系方式"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-row>
            <!--协议信息-->
            <el-row>
              <el-divider content-position="left">协议信息</el-divider>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="协议号" prop="agreementNumber">
                    <el-input v-model="form.agreementNumber" :class="agreementLock || commonLock ?'disable-form':''"
                              :disabled="agreementLock || commonLock"
                              class="sss" placeholder="协议号"

                    />
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="起讫日期" prop="agreementStartDate">
                    <el-date-picker
                      v-model="form.agreementDateRange"
                      :class="agreementLock || commonLock ?'disable-form':''"
                      :disabled="agreementLock || commonLock" class="sss"
                      end-placeholder="结束日期"
                      range-separator="至"
                      start-placeholder="开始日期"
                      style="width: 100%;"
                      :default-time="['00:00:00', '23:59:59']"
                      type="daterange"
                      @input="changeDate"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="信用等级" prop="creditLevel">
                    <tree-select :class="agreementLock || commonLock ?'disable-form':''"
                                 :disabled="agreementLock || commonLock" :pass="form.creditLevel"
                                 :type="'creditLevel'" class="sss"
                                 @return="getcreditLevel"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    :label="roleClient==1|| roleRich==1?'收款方式':roleSupplier==1|| roleSupport==1?'付款方式':''"
                    prop="creditLevel"
                  >
                    <tree-select :class="agreementLock || commonLock ?'disable-form':''"
                                 :disabled="agreementLock || commonLock"
                                 v-if="roleClient==1|| roleRich==1" :multiple="false"
                                 :pass="form.receiveWay" :placeholder="'收款方式'"
                                 :flat="false" :type="'releaseTypeCode'"
                                 class="sss"
                                 @returnData="form.receiveWay=$event.releaseTypeShortName"
                    />
                    <tree-select v-if="roleSupplier==1|| roleSupport==1"
                                 :class="agreementLock || commonLock ?'disable-form':''"
                                 :flat="false" :multiple="false"
                                 :disabled="agreementLock || commonLock" :pass="form.payWay"
                                 :placeholder="'付款方式'" :type="'releaseTypeCode'" class="sss"
                                 @returnData="form.payWay=$event.releaseTypeShortName"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    :label="roleClient==1|| roleRich==1?'收款信用':roleSupplier==1|| roleSupport==1?'付款信用':''"
                    prop="creditLevel"
                  >
                    <el-col v-if="roleClient==1|| roleRich==1">
                      <el-col :span="9">
                        <tree-select :class="agreementLock || commonLock ?'disable-form':''"
                                     :disabled="agreementLock || commonLock"
                                     :pass="form.receiveCurrencyCode"
                                     class=" currency" @return="form.receiveCurrencyCode=$event"
                                     :type="'currency'"
                        />
                      </el-col>
                      <el-col :span="15">
                        <el-input v-model="form.receiveCreditLimit"
                                  :class="agreementLock || commonLock ?'disable-form':''"
                                  :disabled="agreementLock || commonLock"
                                  placeholder="信用额度(填入整数)" @change="formatCreditLimit"
                                  class=" limit"
                        />
                      </el-col>
                    </el-col>
                    <el-col v-if="roleSupplier==1|| roleSupport==1">
                      <el-col :span="9">
                        <tree-select :class="agreementLock || commonLock ?'disable-form':''"
                                     :disabled="agreementLock || commonLock"
                                     :pass="form.payCurrencyCode"
                                     class=" currency" @return="form.payCurrencyCode=$event"
                                     :type="'currency'"
                        />
                      </el-col>
                      <el-col :span="15">
                        <el-input v-model="form.payCreditLimit" :class="agreementLock || commonLock ?'disable-form':''"
                                  :disabled="agreementLock || commonLock"
                                  placeholder="信用额度(填入整数)" @change="formatCreditLimit"
                                  class=" limit"
                        />
                      </el-col>
                    </el-col>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="账单基准" prop="creditLevel">
                    <tree-select v-if="roleClient==1 || roleRich==1"
                                 :class="agreementLock || commonLock ?'disable-form':''"
                                 :disabled="agreementLock || commonLock"
                                 :flat="false" :multiple="false"
                                 :pass="form.receiveStandard" :placeholder="'收款账单基准'"
                                 :type="'commonInfoOfTime'" class="sss"
                                 @return="form.receiveStandard=$event"
                                 @returnData="localStandard=$event.infoLocalName"
                    />
                    <tree-select v-if="roleSupplier==1 || roleSupport==1"
                                 :class="agreementLock || commonLock ?'disable-form':''"
                                 :disabled="agreementLock || commonLock" :flat="false"
                                 :multiple="false"
                                 :pass="form.payStandard" :placeholder="'付款账单基准'"
                                 :type="'commonInfoOfTime'" class="sss"
                                 @returnData="form.payStandard=$event.infoShortName"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    :label="roleClient==1|| roleRich==1?'收款期限':roleSupplier==1|| roleSupport==1?'付款期限':''"
                    :class="agreementLock || commonLock ?'disable-form':''" :disabled="agreementLock || commonLock"
                    prop="agreementNumber"
                  >
                    <el-input v-if="roleClient==1|| roleRich==1" v-model="form.receiveTerm"
                              :class="agreementLock || commonLock ?'disable-form':''"
                              :disabled="agreementLock || commonLock" class="sss"
                              placeholder="收款期限(填写±n)"
                              @change="handleTermChange"
                    />
                    <el-input v-if="roleSupplier==1|| roleSupport==1" v-model="form.payTerm"
                              :class="agreementLock || commonLock ?'disable-form':''"
                              :disabled="agreementLock || commonLock" class="sss"
                              placeholder="付款期限(填写±n)"
                              @change="handleTermChange"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-input :value="description" class="disable-form" disabled/>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label="协议备注" prop="agreementRemark">
                  <el-input v-model="form.agreementRemark" :class="agreementLock || commonLock ?'disable-form':''"
                            :disabled="agreementLock || commonLock"
                            placeholder="协议备注"
                  />
                </el-form-item>
              </el-row>
            </el-row>
            <!--审核意见-->
            <el-row v-if="showConfirm">
              <el-divider content-position="left">
                审核意见
              </el-divider>
              <el-row>
                <el-col v-if="this.roleClient==1" :span="4">
                  <confirmed :id="'companyId'" :confirmed="this.form.salesConfirmed==0" :row="form" :type="'sales'"
                             @lockMethod="updateCompany"
                  />
                </el-col>
                <el-col v-if="this.roleSupplier==1" :span="4">
                  <!--商务锁定-->
                  <confirmed :id="'companyId'" :confirmed="this.form.psaConfirmed==0" :row="form" :type="'psa'"
                             @lockMethod="updateCompany"
                  />
                </el-col>
                <el-col :span="4">
                  <!--操作锁定-->
                  <confirmed :id="'companyId'" :confirmed="this.form.opConfirmed==0" :row="form" :type="'op'"
                             @lockMethod="updateCompany"
                  />
                </el-col>
                <el-col :span="4">
                  <!--财务锁定-->
                  <confirmed :id="'companyId'" :confirmed="this.form.accConfirmed==0" :row="form" :type="'acc'"
                             @lockMethod="updateCompany"
                  />
                </el-col>
              </el-row>
            </el-row>
          </el-col>
          <el-col :span="12">
            <!--分类信息-->
            <el-row v-if="roleClient==1">
              <el-divider content-position="left">
                开发分类
              </el-divider>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="客户来源">
                    <tree-select :pass="form.sourceId" :type="'source'" class="sss"
                                 @returnData="getSourceId"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="开发权重">
                    <el-input v-model="form.developmentWeight" class="sss"
                              placeholder="重要程度"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="业务标记" prop="salesRemark">
                    <el-input v-model="form.salesRemark" class="sss"
                              placeholder="重要程度"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-row>
            <!--物流分类-->
            <el-row>
              <el-divider content-position="left">
                物流分类
              </el-divider>
              <el-row>
                <el-row>
                  <el-col :span="12">
                    <!--供应商公司的角色-->
                    <el-form-item label="公司角色" prop="roleIds">
                      <!--<tree-select :d-load="true" :flat="false" :multiple="true"
                                   :pass="queryParams.roleIds" :placeholder="'公司角色'" :type="'companyRole'"
                                   class="sss" style="width: 100%" @return="queryCompanyRoleIds"
                      />-->
                      <tree-select :flat="false" :multiple="true" :pass="form.roleIds" :type="'companyRole'"
                                   class="sss" @return="getCompanyRoleIds"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="所属组织" prop="organizationIds">
                      <tree-select :multiple="true" :pass="form.organizationIds" :type="'organization'"
                                   class="sss" @return="getOrganizationIds"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="服务类型" prop="serviceTypeIds">
                      <tree-select :flat="false" :multiple="true" :pass="form.serviceTypeIds" :type="'serviceType'"
                                   class="sss" @return="getServiceTypeIds"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="货物类型" prop="cargoTypeIds">
                      <tree-select :flat="false" :multiple="true" :pass="form.cargoTypeIds" :type="'cargoType'"
                                   class="sss" @return="getCargoTypeIds"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="启运区域" prop="locationDepartureIds">
                      <location-select :load-options="locationOptions" :multiple="true"
                                       :pass="form.locationDepartureIds" class="sss"
                                       @return="getLocationDepartureIds"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="优选承运" prop="carrierIds">
                      <treeselect v-model="carrierIds" :disable-branch-nodes="true" :disable-fuzzy-matching="true"
                                  :flat="true" :flatten-search-results="true" :multiple="true"
                                  :normalizer="carrierNormalizer"
                                  :options="temCarrierList" :show-count="true" class="sss"
                                  placeholder="选择承运人" @deselect="handleDeselectCarrierIds" @open="loadCarrier"
                                  @select="handleSelectCarrierIds"
                      >
                        <div slot="value-label" slot-scope="{node}">
                          {{ node.raw.carrier.carrierIntlCode }}
                          {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : "" }}
                        </div>
                        <label slot="option-label"
                               slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                               :class="labelClassName"
                        >
                          {{
                            node.label.indexOf(",") != -1 ? node.label.substring(0, node.label.indexOf(",")) : node.label
                          }}
                          <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
                        </label>
                      </treeselect>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="目的区域" prop="locationDestinationIds">
                      <location-select :en="true" :load-options="locationOptions"
                                       :multiple="true" :pass="form.locationDestinationIds"
                                       class="sss"
                                       @return="getLocationDestinationIds"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="目的航线" prop="lineDestinationIds">
                      <tree-select :flat="false" :multiple="true" :pass="form.lineDestinationIds" :type="'line'"
                                   class="sss" @return="getLineDestinationIds"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-col :span="24">
                  <el-form-item label="备注信息" prop="remark">
                    <el-input v-model="form.remark" class="sss"
                              placeholder="备注信息"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-row>
            <!--客户习惯-->
            <el-row>
              <el-divider content-position="left">
                客户习惯
              </el-divider>
              <el-col :span="24">
                <el-input v-model="form.partnerHabit" :autosize="{ minRows: 15, maxRows: 10}" maxlength="150"
                          placeholder="备注" show-word-limit type="textarea"
                />
              </el-col>
            </el-row>
          </el-col>
        </el-row>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button v-if="!edit" :size="size" type="primary" @click="querySame">查重</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <div>
      <staff-info :company="companyInfo" :load-options="staffList" :open="openStaff" @openStaffs="cancel"/>
      <!--    <account-info :company="companyInfo" :load-options="accountList" :open="openAccount" :type="'supplier'"
                        @openAccounts="cancel"
          />-->
      <account-info :company="companyInfo" :is-lock="isLock" :load-options="accountList" :open="openAccount"
                    :type="'company'"
                    @openAccounts="accountCancel"
      />
      <communications :company="companyInfo" :load-options="communicationList" :open="openCommunication" :totle="ctotle"
                      @openCommunications="cancel"
      />
      <agreement-record :company="companyInfo" :load-options="agreementList" :open="openAgreement" :totle="atotle"
                        @openCommunications="cancel"
      />
      <BlackList :company="companyInfo" :open="openBlackList" @openBlackList="cancel"/>
    </div>

  </div>
</template>

<script>
import {
  delCompany,
  getBank,
  getCompany,
  getConnect,
  listCompany,
  mergeCompany,
  querySame,
  updateCompany
} from "@/api/system/company"
import {getInfoByStaffId} from "@/api/system/role"
import {addMessage} from "@/api/system/message"
import {listCommunication} from "@/api/system/communication"
import {listAgreementrecord} from "@/api/system/agreementrecord"

import pinyin from "js-pinyin"
import store from "@/store"
import Treeselect from "@riophae/vue-treeselect"
import "@riophae/vue-treeselect/dist/vue-treeselect.min.css"

import BlackList from "@/components/BlackList"
import communications from "@/views/system/communication"
import agreementRecord from "@/views/system/agreementRecord"
import staffInfo from "@/views/system/company/staffInfo"

import company from "@/views/system/company/company"
import contactor from "@/views/system/company/contactor"
import location from "@/views/system/company/location"
import role from "@/views/system/company/role"
import serviceType from "@/views/system/company/serviceType"
import departure from "@/views/system/company/departure"
import destination from "@/views/system/company/destination"
import cargoType from "@/views/system/company/cargoType"
import carrier from "@/views/system/company/carrier"
import account from "@/views/system/company/account"
import agreement from "@/views/system/company/agreement"
import communication from "@/views/system/company/communication"
import grade from "@/views/system/company/grade"
import achievement from "@/views/system/company/achievement"
import remark from "@/views/system/company/remark"
import belong from "@/views/system/company/belong"
import auth from "@/plugins/auth"
import {parseTime} from "@/utils/rich"
import {Message} from "element-ui"
import Confirmed from "@/components/Confirmed/index.vue"
import rsPaymentTitle from "@/views/system/company/rsPaymentTitle.vue"
import {checkRole} from "@/utils/permission"
import AccountInfo from "@/views/system/company/accountInfo.vue"

export default {
  name: "Company",
  dicts: ["sys_is_idle"],
  components: {
    AccountInfo,
    Confirmed,
    Treeselect,
    communication,
    communications,
    BlackList,
    belong,
    company,
    contactor,
    staffInfo,
    location,
    role,
    serviceType,
    departure,
    destination,
    cargoType,
    carrier,
    account,
    agreement,
    agreementRecord,
    grade,
    achievement,
    remark,
    rsPaymentTitle
  },
  props: ["roleTypeId", "roleRich", "roleClient", "roleSupplier", "roleSupport"],
  data() {
    return {
      // 遮罩层
      loading: true,
      showLeft: 3,
      showRight: 21,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 选中数组
      ids: [],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      add: false,
      selectTwo: true,
      size: this.$store.state.app.size || "mini",
      // 公司表格数据
      mergeList: [],
      companyList: [],
      staffList: [],
      accountList: [],
      communicationList: [],
      agreementList: [],
      belongList: [],
      carrierList: [],
      businessList: [],
      temCarrierList: [],
      locationOptions: [],
      carrierIds: [],
      companyInfo: {},
      queryCarrierIds: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      merge: false,
      openCompany: false,
      openStaff: false,
      openAccount: false,
      openCommunication: false,
      openAgreement: false,
      openBlackList: false,
      edit: false,
      belongTo: null,
      followUp: null,
      queryBFStaffId: null,
      queryBStaffId: null,
      refreshTable: true,
      ctotle: null,
      atotle: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        // roleTypeId: this.roleTypeId,
        roleRich: this.roleRich ? this.roleRich : null,
        roleClient: this.roleClient ? this.roleClient : null,
        roleSupplier: this.roleSupplier ? this.roleSupplier : null,
        roleSupport: this.roleSupport ? this.roleSupport : null,
        companyQuery: null,
        locationId: null,
        idleStatus: null,
        queryStaffId: null,
        showPriority: null,
        serviceTypeIds: [],
        cargoTypeIds: [],
        locationDepartureIds: [],
        lineDepartureIds: [],
        locationDestinationIds: [],
        lineDestinationIds: [],
        roleIds: [],
        carrierIds: []
      },
      // 表单参数
      form: {
        agreementStartDate: null,
        agreementEndDate: null,
        settlementDate: null
      },
      // 表单校验
      rules: {},
      // 当前点击修改时选中的记录
      companyRow: null,
      isLock: true,
      showConfirm: false,
      localStandard: null,
      description: null
    }
  },
  computed: {
    columns: {
      get() {
        if (this.roleTypeId == "2") {
          return this.$store.state.listSettings.supplierSetting
        }
        if (this.roleTypeId == "1") {
          return this.$store.state.listSettings.clientSetting
        }
        if (this.roleClient) {
          return this.$store.state.listSettings.clientSetting
        }
        if (this.roleSupplier) {
          return this.$store.state.listSettings.supplierSetting
        }
        if (this.roleRich) {
          return this.$store.state.listSettings.supplierSetting
        }
        if (this.roleSupport) {
          return this.$store.state.listSettings.supplierSetting
        }
      }
    },
    commonLock() {
      return (this.form.psaConfirmed == 1 || this.form.salesConfirmed == 1) ? true : false
    },
    basicLock() {
      return this.form.opConfirmed == 1 ? true : false
    },
    agreementLock() {
      return this.form.accConfirmed == 1 ? true : false
    }
  },
  watch: {
    showSearch(n) {
      if (n == true) {
        this.showRight = 21
        this.showLeft = 3
      } else {
        this.showRight = 24
        this.showLeft = 0
      }
    },
    queryStaffId() {
      this.queryParams.queryStaffId = this.queryStaffId
    },
    "form.belongTo"() {
      if (this.form.belongTo == this.form.followUp) {
        this.form.followUp = 0
        this.followUp = null
      }
    },
    "form.serviceTypeIds"(n) {
      this.loadCarrier()
      let list = []
      if (this.carrierList != undefined && n != null && n.includes(-1)) {
        this.temCarrierList = this.carrierList
        for (const v of this.carrierList) {
          if (v.children != undefined && v.children.length > 0) {
            for (const a of v.children) {
              if (a.children != undefined && a.children.length > 0) {
                for (const b of a.children) {
                  if (this.form.carrierIds != null && this.form.carrierIds.includes(b.carrier.carrierId) && !this.carrierIds.includes(b.serviceTypeId)) {
                    this.carrierIds.push(b.serviceTypeId)
                  }
                }
              }
            }
          }
        }
      }
      if (this.carrierList != undefined && n != null && !n.includes(-1)) {
        for (const c of this.carrierList) {
          if (n != null && n != undefined) {
            for (const s of n) {
              if (c.serviceTypeId == s) {
                list.push(c)
              }
              if (c.children != undefined && c.children.length > 0) {
                for (const ch of c.children) {
                  if (ch.serviceTypeId == s) {
                    list.push(ch)
                  }
                }
              }
            }
          }
        }
        this.temCarrierList = list
        if (this.temCarrierList.length > 0) {
          for (const v of this.temCarrierList) {
            if (v.children != undefined && v.children.length > 0) {
              for (const a of v.children) {
                if (this.form.carrierIds != null && this.form.carrierIds.includes(a.carrier.carrierId) && !this.carrierIds.includes(a.serviceTypeId)) {
                  this.carrierIds.push(a.serviceTypeId)
                }
              }
            }
          }
        }
      }
    },
    form() {
      if (this.form.salesConfirmed == 1 || this.form.accConfirmed == 1 || this.form.psaConfirmed == 1 || this.form.opConfirmed == 1) {
        this.isLock = true
      } else {
        this.isLock = false
      }
    },
    localStandard(v) {
      this.handleTermChange()
    },
    "form.receiveTerm"(v) {
      this.handleTermChange()
    },
    "form.payTerm"(v) {
      this.handleTermChange()
    },
    "form.receiveWay"(v) {
      this.handleTermChange()
    },
    "form.payWay"(v) {
      this.handleTermChange()
    }
  },
  created() {
    this.getList().then(() => {
      this.loadBusinesses()
      this.loadCarrier()
      this.loadSales()
    })
  },
  methods: {
    parseTime,
    handleTermChange(v) {
      if (this.form.receiveWay === "月结" || this.form.payWay === "月结") {
        if (this.roleClient == 1 || this.roleRich == 1) {
          this.description = (this.form.receiveStandard ? this.form.receiveStandard : "") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === "-" ? ("的下个月" + this.form.receiveTerm.substring(1, 3) + "号之后") : ("的下个月" + this.form.receiveTerm.substring(1, 3) + "号之前")) : "前")
        } else {
          this.description = (this.form.payStandard ? this.form.payStandard : "") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === "-" ? ("的下个月" + this.form.payTerm.substring(1, 3) + "号之后") : ("的下个月" + this.form.payTerm.substring(1, 3) + "号之前")) : "前")
        }
      } else {
        if (this.roleClient == 1 || this.roleRich == 1) {
          this.description = (this.form.receiveStandard ? this.form.receiveStandard : "") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === "-" ? this.form.receiveTerm.substring(1, 3) + "天前" : "后" + this.form.receiveTerm.substring(1, 3) + "天内") : "前")
        } else {
          this.description = (this.form.payStandard ? this.form.payStandard : "") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === "-" ? this.form.payTerm.substring(1, 3) + "天前" : "后" + this.form.payTerm.substring(1, 3) + "天内") : "前")
        }
      }
    },
    loadSales() {
      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {
        if (checkRole(["Operator"])) {
          store.dispatch("getSalesList").then(() => {
            this.belongList = this.$store.state.data.salesList
          })
        } else {
          store.dispatch("getSalesListC").then(() => {
            this.belongList = this.$store.state.data.salesList
          })
        }
      } else {
        this.belongList = this.$store.state.data.salesList
      }
    },
    loadBusinesses() {
      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {
        store.dispatch("getBusinessesList").then(() => {
          this.businessList = this.$store.state.data.businessesList
        })
      } else {
        this.businessList = this.$store.state.data.businessesList
      }
    },
    loadCarrier() {
      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {
        store.dispatch("getServiceTypeCarriersList").then(() => {
          this.carrierList = this.$store.state.data.serviceTypeCarriers
        })
      } else {
        this.carrierList = this.$store.state.data.serviceTypeCarriers
      }
    },
    // 查重
    querySame() {
      // 初始化请求数据,特别注意deleteStatus设置未1,后端会对这个值的数据做重复校验
      let data = {
        cargoTypeIds: [],
        locationDepartureIds: [],
        locationDestinationIds: [],
        lineDepartureIds: [],
        lineDestinationIds: [],
        companyShortName: this.form.companyShortName,
        companyLocalName: this.form.companyLocalName,
        serviceTypeIds: this.form.serviceTypeIds,
        roleTypeId: this.roleTypeId,
        roleRich: this.roleRich ? this.roleRich : null,
        roleClient: this.roleClient ? this.roleClient : null,
        roleSupplier: this.roleSupplier ? this.roleSupplier : null,
        roleSupport: this.roleSupport ? this.roleSupport : null,
        belongTo: this.form.belongTo,
        followUp: this.form.followUp,
        deleteStatus: 1
      }
      getInfoByStaffId(this.$store.state.user.sid).then(response => {
        data.cargoTypeIds = response.cargoTypeIds
        data.locationDepartureIds = response.locationDepartureIds
        data.locationDestinationIds = response.locationDestinationIds
        data.lineDepartureIds = response.lineDepartureIds
        data.lineDestinationIds = response.lineDestinationIds
      })
      // 公司所属
      if (data.belongTo == null) {
        if (this.belongList != undefined) {
          for (const a of this.belongList) {
            if (a.children != undefined) {
              for (const b of a.children) {
                if (b.children != undefined) {
                  for (const c of b.children) {
                    if (c.staffId == this.$store.state.user.sid) {
                      data.belongTo = this.$store.state.user.sid
                    }
                  }
                }
              }
            }
          }
        }
      }
      this.$refs["form"].validate(valid => {
        if (valid) {
          querySame(data).then(response => {
            let res = response.data
            if (res != undefined) {
              let newRoleType = this.roleRich ? "瑞旗分支" : this.roleClient ? "客户" : this.roleSupplier ? "供应商" : this.roleSupport ? "支持" : ""
              let oldRoleType = res.roleRich == 1 ? "瑞旗分支" : res.roleClient == 1 ? "客户" : res.roleSupplier == 1 ? "供应商" : res.roleSupport == 1 ? "支持" : ""
              this.$confirm(res.deleteStatus == 0 ? "此公司已存在，角色为" + oldRoleType + "，新增角色为" + newRoleType + ",是否确认新增" : "存在重复数据，但已删除，是否重新读取", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                customClass: "modal-confirm"
              }).then(() => {
                res.deleteStatus = 0
                this.form = res
                this.form.roleTypeId = this.roleTypeId
                if (this.belongList != undefined) {
                  for (const a of this.belongList) {
                    if (a.children != undefined) {
                      for (const b of a.children) {
                        if (b.children != undefined) {
                          for (const c of b.children) {
                            if (c.staffId == response.data.belongTo) {
                              this.belongTo = c.deptId
                            }
                            if (c.staffId == response.data.followUp) {
                              this.followUp = c.deptId
                            }
                          }
                        }
                      }
                    }
                  }
                }
                this.form.roleIds = response.roleIds
                this.form.serviceTypeIds = response.serviceTypeIds
                this.form.cargoTypeIds = response.cargoTypeIds
                this.form.lineDepartureIds = response.lineDepartureIds
                this.form.locationDepartureIds = response.locationDepartureIds
                this.form.lineDestinationIds = response.lineDestinationIds
                this.form.locationDestinationIds = response.locationDestinationIds
                this.form.carrierIds = response.carrierIds
                // this.form.organizationIds = response.companyOrganizationIds
                this.form.organizationIds = response.organizationIds
                this.locationOptions = response.locationOptions
                this.openCompany = true
                this.title = "修改公司信息"
                this.loading = false
              })
            }

            // 错误处理,弹出提示后点击确定发送请求更新公司信息
            if (response.msg.toString().indexOf("Error") > -1) {
              this.$confirm(response.msg, "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                customClass: "modal-confirm"
              }).then(() => {
                let data = {
                  messageOwner: 1,
                  messageType: 3,
                  messageFrom: null,
                  messageTitle: this.$store.state.user.name.split(" ")[1] + "请求更新公司",
                  messageContent: response.msg
                }
                addMessage(data).then(response => {
                  this.$message({
                    type: "success",
                    message: "已发送请求!"
                  })
                })
              })
            }

            // 新增验证通过(通过将请求数据中的deleteStatus设置未0)
            if (response.msg.toString().indexOf("Success") > -1) {
              this.$confirm("不存在重复的公司简称，是否确定新增客户？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
                customClass: "modal-confirm"
              }).then(() => {
                data.deleteStatus = 0
                // 真正开始新增
                querySame(data).then(response => {
                  if (response.data) {
                    this.$message.success("添加成功")
                    this.form = response.data
                    this.form.roleTypeId = this.roleTypeId
                    if (this.belongList != undefined) {
                      for (const a of this.belongList) {
                        if (a.children != undefined) {
                          for (const b of a.children) {
                            if (b.children != undefined) {
                              for (const c of b.children) {
                                if (c.staffId == response.data.belongTo) {
                                  this.belongTo = c.deptId
                                }
                                if (c.staffId == response.data.followUp) {
                                  this.followUp = c.deptId
                                }
                              }
                            }
                          }
                        }
                      }
                    }
                    this.form.roleIds = response.roleIds
                    this.form.serviceTypeIds = response.serviceTypeIds
                    this.form.cargoTypeIds = response.cargoTypeIds
                    this.form.lineDepartureIds = response.lineDepartureIds
                    this.form.locationDepartureIds = response.locationDepartureIds
                    this.form.lineDestinationIds = response.lineDestinationIds
                    this.form.locationDestinationIds = response.locationDestinationIds
                    this.form.carrierIds = response.carrierIds
                    // this.form.organizationIds = response.companyOrganizationIds
                    this.form.organizationIds = response.organizationIds
                    this.locationOptions = response.locationOptions
                    this.openCompany = true
                    this.title = "修改公司信息"
                    this.loading = false
                  }
                })
              })
            }
          })
        }
      })
    },
    /** 查询公司列表 */
    async getList() {
      this.loading = true
      await listCompany({
        ...this.queryParams,
        permissionLevel: this.$store.state.user.permissionLevelList.C
      }).then(response => {
        this.companyList = response.rows
        if (!isNaN(response.total)) {
          this.total = response.total
        }
        this.loading = false
      })
    },
    staffNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      let l
      if (node.staff) {
        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {
          if (node.role.roleLocalName != null) {
            l = node.role.roleLocalName + "," + pinyin.getFullChars(node.role.roleLocalName)
          } else {
            l = node.dept.deptLocalName + "," + pinyin.getFullChars(node.dept.deptLocalName)
          }
        } else {
          l = node.staff.staffCode + " " + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + " " + node.staff.staffGivingEnName + "," + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)
        }
      }
      if (node.roleId) {
        return {
          id: node.roleId,
          label: l,
          children: node.children,
          isDisabled: node.staffId == null && node.children == undefined
        }
      } else {
        return {
          id: node.deptId,
          label: l,
          children: node.children,
          isDisabled: node.staffId == null && node.children == undefined
        }
      }
    },
    validateCompanyShortName(rule, value, callback) {

      // 检查 value 是否为空或非字符串
      if (value || typeof value === "string") {
        // 检查是否包含多个中横线
        const hyphenCount = value.split("-").length - 1; // 通过 split 分割来统计中横线数量
        if (hyphenCount > 1) {
          return callback(new Error("只能包含一个中横线"));
        }
      }
      // 验证通过
      callback();
    },
    carrierNormalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      let l
      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {
        l = node.serviceLocalName + " " + node.serviceEnName + "," + pinyin.getFullChars(node.serviceLocalName)
      } else {
        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : "") + " " + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : "") + " " + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : "") + "," + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : ""))
      }
      return {
        id: node.serviceTypeId,
        label: l,
        children: node.children
      }
    },
    // 取消按钮
    cancel() {
      this.openCompany = false
      this.openAccount = false
      this.openStaff = false
      this.openCommunication = false
      this.openAgreement = false
      this.openBlackList = false
      this.add = false
      this.merge = false
      this.edit = false
      this.reset()
      this.belongTo = null
      this.followUp = null
      // this.getList()
    },
    accountCancel() {
      this.openAccount = false
    },
    // 表单重置
    reset() {
      this.belongTo = null
      this.followUp = null
      this.carrierIds = []
      this.form = {
        belongTo: null,
        followUp: null,
        carrierIds: null,
        locationDetail: null,
        companyId: null,
        companyIntlCode: null,
        companyShortName: null,
        companyEnShortName: null,
        companyLocalName: null,
        companyEnName: null,
        companyTaxCode: null,
        roleIds: null,
        serviceTypeIds: null,
        cargoTypeIds: null,
        locationDepartureIds: null,
        lineDepartureIds: null,
        locationDestinationIds: null,
        lineDestinationIds: null,
        organizationIds: null,
        companyPortIds: null,
        roleTypeId: this.roleTypeId,
        roleRich: this.roleRich ? this.roleRich : null,
        roleClient: this.roleClient ? this.roleClient : null,
        roleSupplier: this.roleSupplier ? this.roleSupplier : null,
        roleSupport: this.roleSupport ? this.roleSupport : null,
        locationId: null,
        salesConfirmed: 0,
        salesConfirmedId: null,
        salesConfirmedName: null,
        salesConfirmedDate: null,
        psaConfirmed: 0,
        psaConfirmedId: null,
        psaConfirmedName: null,
        psaConfirmedDate: null,
        accConfirmed: 0,
        accConfirmedId: null,
        accConfirmedName: null,
        accConfirmedDate: null,
        opConfirmed: 0,
        opConfirmedId: null,
        opConfirmedName: null,
        opConfirmedDate: null,
        remark: null,
        rsPaymentTitles: []
      }
      this.carrierIds = []
      this.companyRow = null
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.queryBStaffId = null
      this.queryParams.locationId = null
      this.queryParams.serviceTypeIds = null
      this.queryParams.cargoTypeIds = null
      this.queryParams.locationDepartureIds = null
      this.queryParams.lineDepartureIds = null
      this.queryParams.locationDestinationIds = null
      this.queryParams.lineDestinationIds = null
      this.queryParams.organizationIds = null
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.companyId)
      this.single = selection.length != 1
      this.multiple = !selection.length
      this.selectTwo = selection.length != 2
      if (selection.length == 1) {
        this.setCompanyInfo(selection[0])
      }
      if (selection.length == 2) {
        this.mergeList = selection
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.edit = false
      this.form.belongTo = null
      this.openCompany = true
      this.title = "新增公司信息"
      this.form.serviceTypeIds = []
      this.temCarrierList = this.carrierList
      if (this.temCarrierList != undefined && this.form.serviceTypeIds != null) {
        for (const a of this.temCarrierList) {
          // this.form.serviceTypeIds.push(a.serviceTypeId)
        }
      }
      this.add = true
      if (this.belongList != undefined) {
        for (const a of this.belongList) {
          if (a.children != undefined) {
            for (const b of a.children) {
              if (b.children != undefined) {
                for (const c of b.children) {
                  if (c.staffId == this.$store.state.user.sid) {
                    this.belongTo = c.deptId
                  }
                }
              }
            }
          }
        }
      }
      // 币种默认为人民币RMB
      this.form.agreementCurrencyCode = "RMB"
      this.showConfirm = false
    },
    getReturn(row) {
      if (row.key == "contactor") {
        this.setCompanyInfo(row.value)
        getConnect(row.value.companyId).then(response => {
          this.staffList = response.staffList
          this.openStaff = true
        })
      }
      if (row.key == "communication") {
        this.setCompanyInfo(row.value)
        listCommunication({sqdCompanyId: row.value.companyId}).then(response => {
          this.communicationList = response.rows
          this.ctotle = response.totle
          this.openCommunication = true
        })
      }
      if (row.key == "agreement") {
        this.setCompanyInfo(row.value)
        listAgreementrecord({sqdCompanyId: row.value.companyId}).then(response => {
          this.agreementList = response.rows
          this.atotle = response.totle
          this.openAgreement = true
        })
      }
      if (row.key == "account") {
        this.setCompanyInfo(row.value)
        getBank(row.value.companyId).then(response => {
          this.accountList = response.accountList
          this.openAccount = true
        })
      }
    },
    // 设置客户信息
    setCompanyInfo(row) {
      this.companyInfo = {
        companyId: row.companyId != null ? row.companyId : "",
        companyTaxCode: row.companyTaxCode != null ? row.companyTaxCode : "",
        companyShortName: row.companyShortName != null ? row.companyShortName : "",
        companyEnShortName: row.companyEnShortName != null ? row.companyEnShortName : "",
        companyLocalName: row.companyLocalName != null ? row.companyLocalName : "",
        companyEnName: row.companyEnName != null ? row.companyEnName : "",
        companyLocation: row.locationId != null ? row.locationId : "",
        companyIntlCode: row.companyIntlCode != null ? row.companyIntlCode : "",
        mainStaffId: row.staff != null ? row.staff.staffId : ""
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.loading = true
      this.edit = true
      this.companyRow = row
      this.add = auth.hasPermi("system:client:distribute")
      const companyId = row.companyId || this.ids
      this.showConfirm = true
      getCompany(companyId).then(response => {
        this.form = response.data
        if (this.belongList != undefined) {
          for (const a of this.belongList) {
            if (a.children != undefined) {
              for (const b of a.children) {
                if (b.children != undefined) {
                  for (const c of b.children) {
                    if (c.staffId == response.data.belongTo) {
                      this.belongTo = c.deptId
                    }
                    if (c.staffId == response.data.followUp) {
                      this.followUp = c.deptId
                    }
                  }
                }
              }
            }
          }
        }
        this.form.roleIds = response.roleIds
        this.form.serviceTypeIds = response.serviceTypeIds
        this.form.cargoTypeIds = response.cargoTypeIds
        this.form.lineDepartureIds = response.lineDepartureIds
        this.form.locationDepartureIds = response.locationDepartureIds
        this.form.lineDestinationIds = response.lineDestinationIds
        this.form.locationDestinationIds = response.locationDestinationIds
        this.form.carrierIds = response.carrierIds
        // this.form.organizationIds = response.companyOrganizationIds
        this.form.organizationIds = response.organizationIds
        this.locationOptions = response.locationOptions
        this.openCompany = true
        this.title = "修改公司信息"
        // this.form.rsPaymentTitles = response.data.rsPaymentTitle ? response.data.rsPaymentTitle.split(',') : []
        this.loading = false

        if (response.data.agreementStartDate !== null && response.data.agreementEndDate !== null) {
          this.form.agreementDateRange = []
          this.form.agreementDateRange.push(response.data.agreementStartDate)
          this.form.agreementDateRange.push(response.data.agreementEndDate)
        }

        const formatter = new Intl.NumberFormat("en-US", {
          style: "decimal",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        if ((this.roleClient == 1 || this.roleRich == 1) && response.data.receiveCreditLimit !== null) {
          this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString("en-US")
          this.form.receiveCreditLimit = this.form.receiveCreditLimit.replace(/,/g, "")
          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)
        }
        if ((this.roleSupplier == 1 || this.roleSupport == 1) && response.data.payCreditLimit !== null) {
          this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString("en-US")
          this.form.payCreditLimit = this.form.payCreditLimit.replace(/,/g, "")
          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)
        }

      })

    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        // 收付款描述
        if (this.roleClient == 1 || this.roleRich == 1) {
          this.form.sqdReceiveTermsSummary = this.description
        }
        if (this.roleClient == 1 || this.roleRich == 1) {
          this.form.sqdPayTermsSummary = this.description
        }
        // 公司类型
        this.form.roleRich = this.roleRich ? this.roleRich : null
        this.form.roleClient = this.roleClient ? this.roleClient : null
        this.form.roleSupplier = this.roleSupplier ? this.roleSupplier : null
        this.form.roleSupport = this.roleSupport ? this.roleSupport : null
        // 转换额度显示
        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? String(this.form.receiveCreditLimit).replace(/,/g, "") : 0
        this.form.payCreditLimit = this.form.payCreditLimit ? String(this.form.payCreditLimit).replace(/,/g, "") : 0
        if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {
          this.form.agreementStartDate = this.form.agreementDateRange[0]
          this.form.agreementEndDate = this.form.agreementDateRange[1]
        }
        // 判断日期开始时间是否大于结束时间
        let startDate = new Date(this.form.agreementStartDate)
        let endDate = new Date(this.form.agreementEndDate)
        if (startDate > endDate) {
          Message({
            message: "协议开始时间不能大于结束时间",
            type: "error"
          })
          return
        }
        // 信用周期要为整数
        if (this.form.creditCycleMonth != null && this.form.creditCycleMonth % 1 != 0) {
          Message({
            message: "信用周期必须为整数",
            type: "error"
          })
          return
        }
        if (valid) {
          if (this.form.companyId != null) {
            updateCompany(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.openCompany = false
              this.getList()
            })
            this.reset()
          } else {
            this.$message.info("未查重，先对简称查重吧")
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      row.roleRich = this.roleRich ? this.roleRich : null
      row.roleClient = this.roleClient ? this.roleClient : null
      row.roleSupplier = this.roleSupplier ? this.roleSupplier : null
      row.roleSupport = this.roleSupport ? this.roleSupport : null

      const companyIds = row.companyId || this.ids
      this.$confirm("是否确认删除公司编号为\"" + companyIds + "\"的数据项？", "提示", {customClass: "modal-confirm"}).then(function () {
        return delCompany(row)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    handleBlackList() {
      this.openBlackList = true
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download("system/company/export", {
        ...this.queryParams
      }, `company_${new Date().getTime()}.xlsx`)
    },
    queryLocationId(val) {
      this.queryParams.locationId = val
      this.handleQuery()
    },
    getLocationId(val) {
      this.form.locationId = val
    },
    getSourceId(val) {
      this.form.sourceId = val.sourceShortName
    },
    getOrganizationIds(val) {
      this.form.organizationIds = val
    },
    getServiceTypeIds(val) {
      this.form.serviceTypeIds = val
      if (val == undefined) {
        this.carrierIds = null
        this.form.carrierIds = null
      }
    },
    queryServiceTypeIds(val) {
      this.queryParams.serviceTypeIds = val
      this.handleQuery()
    },
    getCargoTypeIds(val) {
      this.form.cargoTypeIds = val
    },
    queryCargoTypeIds(val) {
      this.queryParams.cargoTypeIds = val
      this.handleQuery()
    },
    getCompanyRoleIds(val) {
      this.form.roleIds = val
    },
    queryCompanyRoleIds(val) {
      this.queryParams.roleIds = val
      this.handleQuery()
    },
    queryLocationDepartureIds(val) {
      this.queryParams.locationDepartureIds = val
      this.handleQuery()
    },
    getLineDepartureIds(val) {
      this.form.lineDepartureIds = val
    },
    getLocationDestinationIds(val) {
      this.form.locationDestinationIds = val
    },
    queryLineDepartureIds(val) {
      this.queryParams.lineDepartureIds = val
      this.handleQuery()
    },
    getLocationDepartureIds(val) {
      this.form.locationDepartureIds = val
    },
    queryLocationDestinationIds(val) {
      this.queryParams.locationDestinationIds = val
      this.handleQuery()
    },
    getLineDestinationIds(val) {
      this.form.lineDestinationIds = val
    },
    queryLineDestinationIds(val) {
      this.queryParams.lineDestinationIds = val
      this.handleQuery()
    },
    handleSelectBelongTo(node) {
      this.form.belongTo = node.staffId
    },
    handleDeselectBelongTo(v) {
      if (v == undefined) {
        this.form.belongTo = 0
        this.belongTo = null
      }
    },
    handleSelectFollowUp(node) {
      this.form.followUp = node.staffId
    },
    handleDeselectFollowUp(value) {
      if (value == undefined) {
        this.form.followUp = 0
        this.followUp = null
      }
    },
    handleSelectBFStaffId(node) {
      this.queryParams.queryBFStaffId = node.staffId
      this.handleQuery()
    },
    cleanBFStaffId(val) {
      if (val == undefined) {
        this.queryParams.queryBFStaffId = null
        this.handleQuery()
      }
    },
    cleanBStaffId(val) {
      if (val == undefined) {
        this.queryParams.queryBStaffId = null
        this.handleQuery()
      }
    },
    handleSelectBStaffId(node) {
      this.queryParams.queryBStaffId = node.staffId
      getInfoByStaffId(node.staffId).then(response => {
        this.queryParams.cargoTypeIds = response.cargoTypeIds
        this.queryParams.serviceTypeIds = response.serviceTypeIds
        this.queryParams.locationDepartureIds = response.locationDepartureIds
        this.queryParams.lineDepartureIds = response.lineDepartureIds
        this.queryParams.locationDestinationIds = response.locationDestinationIds
        this.queryParams.lineDestinationIds = response.lineDestinationIds
        this.locationOptions = response.locationOptions
        this.handleQuery()
      })
    },
    handleSelectCarrierIds(node) {
      this.form.carrierIds.push(node.carrier.carrierId)
    },
    handleSelectQueryCarrierIds(node) {
      this.queryParams.carrierIds.push(node.carrier.carrierId)
      this.handleQuery()
    },
    handleDeselectCarrierIds(node) {
      this.form.carrierIds = this.form.carrierIds.filter((item) => {
        return item != node.carrier.carrierId
      })
    },
    handleDeselectQueryCarrierIds(node) {
      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {
        return item != node.carrier.carrierId
      })
      this.handleQuery()
    },
    refreshColumns() {
      this.refreshTable = false
      this.$nextTick(() => {
        this.refreshTable = true
      })
    },
    handleMergeCompany() {
      this.merge = true
    },
    handleMerge(save, del) {
      mergeCompany(save, del).then(response => {
        this.$message.success(response.msg)
        this.merge = false
        this.getList()
      })
    },
    deptLock() {
      if (this.form.companyId != null) {
        this.form.deptConfirmed = this.form.deptConfirmed == 0 ? 1 : 0
        this.form.deptConfirmedId = this.$store.state.user.sid
        this.form.deptConfirmedName = this.$store.state.user.name.split(" ")[1]
        this.form.deptConfirmedDate = parseTime(new Date())
        updateCompany(this.form).then(response => {
          this.$modal.msgSuccess("修改成功")
        })
      } else {
        this.$modal.msgError("错误操作")
      }
    },
    financeLock() {
      if (this.form.companyId != null) {
        this.form.financeConfirmed = this.form.financeConfirmed == 0 ? 1 : 0
        this.form.financeConfirmedId = this.$store.state.user.sid
        this.form.financeConfirmedName = this.$store.state.user.name.split(" ")[1]
        this.form.financeConfirmedDate = parseTime(new Date())
        updateCompany(this.form).then(response => {
          this.$modal.msgSuccess("修改成功")
        })
      } else {
        this.$modal.msgError("错误操作")
      }
    },
    psaLock() {
      if (this.form.companyId != null) {
        this.form.psaConfirmed = this.form.psaConfirmed == 0 ? 1 : 0
        this.form.psaConfirmedId = this.$store.state.user.sid
        this.form.psaConfirmedName = this.$store.state.user.name.split(" ")[1]
        this.form.psaConfirmedDate = parseTime(new Date())
        updateCompany(this.form).then(response => {
          this.$modal.msgSuccess("修改成功")
        })
      } else {
        this.$modal.msgError("错误操作")
      }
    },
    opLock() {
      if (this.form.companyId != null) {
        this.form.opConfirmed = this.form.opConfirmed === 0 ? 1 : 0
        this.form.opConfirmedId = this.$store.state.user.sid
        this.form.opConfirmedName = this.$store.state.user.name.split(" ")[1]
        this.form.opConfirmedDate = parseTime(new Date())
        updateCompany(this.form).then(response => {
          this.$modal.msgSuccess("修改成功")
        })
      } else {
        this.$modal.msgError("错误操作")
      }
    },
    getCurrencyCode(val) {
      this.form.agreementCurrencyCode = val
    },
    getcreditLevel(val) {
      this.form.creditLevel = val
    },
    getRsPaymentTitle(val) {
      this.form.rsPaymentTitle = val
    },
    async updateCompany(form) {
      // TODO 只更新有修改的字段
      // console.log(Object.assign(this.form, form))

      // this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')

      if (this.roleClient == 1 || this.roleRich == 1) {
        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? this.form.receiveCreditLimit.replace(/,/g, "") : 0
      }
      if (this.roleSupplier == 1 || this.roleSupport == 1) {
        this.form.payCreditLimit = this.form.payCreditLimit ? this.form.payCreditLimit.replace(/,/g, "") : 0
      }

      if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {
        this.form.agreementStartDate = this.form.agreementDateRange[0]
        this.form.agreementEndDate = this.form.agreementDateRange[1]
      }

      await updateCompany(form)
      this.$modal.msgSuccess("修改成功")
      let response = await getCompany(form.companyId)
      // 更新信息
      // this.$nextTick(() => {
      this.form = response.data
      if (this.belongList != undefined) {
        for (const a of this.belongList) {
          if (a.children != undefined) {
            for (const b of a.children) {
              if (b.children != undefined) {
                for (const c of b.children) {
                  if (c.staffId == response.data.belongTo) {
                    this.belongTo = c.deptId
                  }
                  if (c.staffId == response.data.followUp) {
                    this.followUp = c.deptId
                  }
                }
              }
            }
          }
        }
      }
      this.form.roleIds = response.roleIds
      this.form.serviceTypeIds = response.serviceTypeIds
      this.form.cargoTypeIds = response.cargoTypeIds
      this.form.lineDepartureIds = response.lineDepartureIds
      this.form.locationDepartureIds = response.locationDepartureIds
      this.form.lineDestinationIds = response.lineDestinationIds
      this.form.locationDestinationIds = response.locationDestinationIds
      this.form.carrierIds = response.carrierIds
      // this.form.organizationIds = response.companyOrganizationIds
      this.form.organizationIds = response.organizationIds
      this.locationOptions = response.locationOptions
      this.openCompany = true
      this.title = "修改公司信息"
      this.loading = false

      const formatter = new Intl.NumberFormat("en-US", {
        style: "decimal",
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
      /* this.form.creditLimit = response.data.creditLimit.toLocaleString('en-US')
      this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')
      this.form.creditLimit = formatter.format(this.form.creditLimit) */

      if (this.roleClient == 1 || this.roleRich == 1) {
        this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString("en-US")
        this.form.receiveCreditLimit = this.form.receiveCreditLimit.replace(/,/g, "")
        this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)
      }
      if (this.roleSupplier == 1 || this.roleSupport == 1) {
        this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString("en-US")
        this.form.payCreditLimit = this.form.payCreditLimit.replace(/,/g, "")
        this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)
      }

      // 更新日期
      this.form.agreementDateRange = (response.data.agreementStartDate != null && response.data.agreementEndDate != null) ? [response.data.agreementStartDate, response.data.agreementEndDate] : []
      /* if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {
        this.form.agreementStartDate = this.form.agreementDateRange[0]
        this.form.agreementEndDate = this.form.agreementDateRange[1]
      } */

      // })
      this.form.salesConfirmed = response.data.salesConfirmed
      this.form.psaConfirmed = response.data.psaConfirmed
      this.form.opConfirmed = response.data.opConfirmed
      this.form.accConfirmed = response.data.accConfirmed
    },
    formatCreditLimit() {
      if (this.form.receiveCreditLimit != null || this.form.payCreditLimit != null) {
        const formatter = new Intl.NumberFormat("en-US", {
          style: "decimal",
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })
        if (this.roleClient == 1) {
          this.form.receiveCreditLimit = this.form.receiveCreditLimit.replace(/,/g, "")
          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)
        }
        if (this.roleSupplier == 1) {
          this.form.payCreditLimit = this.form.payCreditLimit.replace(/,/g, "")
          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)
        }

      }
    },
    formatDisplayCreditLimit(value) {
      const formatter = new Intl.NumberFormat("en-US", {
        notation: "compact"
      })
      return formatter.format(value)
    },
    changeDate() {
      this.$forceUpdate()
    }
  }
}
</script>


<style scoped lang="scss">
.sss {
  //padding-left: 5px !important;
  padding-right: 15px !important;
  width: 100%;
}

.creditLimit {
  display: flex;
  flex-direction: row;
}

.limit {
  flex: 3
}

.currency {
  flex: 1
}

.confirm {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.box-card {
  width: 20%;
  flex-wrap: wrap;
}
</style>
