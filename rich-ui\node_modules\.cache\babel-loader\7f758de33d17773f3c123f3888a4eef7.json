{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\characteristics\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\characteristics\\index.vue", "mtime": 1754876882575}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_characteristics", "require", "_vueTreeselect", "_interopRequireDefault", "_js<PERSON><PERSON>yin", "_store", "_rich", "_user", "name", "dicts", "components", "Treeselect", "data", "showLeft", "showRight", "validTime", "carrierList", "carrierIds", "queryCarrierIds", "temCarrierList", "locationOptions", "userList", "loading", "ids", "single", "multiple", "showSearch", "total", "characteristicsList", "title", "open", "queryParams", "accurate", "pageNum", "pageSize", "infoId", "showMode", "serviceTypeId", "cargoTypeIds", "locationDepartureIds", "lineDepartureIds", "locationDestinationIds", "lineDestinationIds", "companyId", "essentialDetail", "<PERSON><PERSON><PERSON><PERSON>", "updateBy", "updateTime", "form", "rules", "required", "trigger", "watch", "n", "formServiceTypeId", "loadCarrier", "list", "undefined", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "done", "c", "value", "push", "children", "length", "_iterator2", "_step2", "ch", "err", "e", "f", "created", "_this", "getList", "selectListUser", "then", "response", "methods", "changeTime", "val", "validFrom", "validTo", "_this2", "$store", "state", "serviceTypeCarriers", "redisList", "store", "dispatch", "carrierNormalizer", "node", "l", "carrier", "carrierLocalName", "carrierEnName", "serviceLocalName", "serviceEnName", "pinyin", "getFullChars", "carrierIntlCode", "id", "label", "tableRowClassName", "_ref", "row", "date", "parseTime", "Date", "_this3", "listCharacteristics", "rows", "cancel", "reset", "characteristicsId", "status", "remark", "createBy", "createTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "_this4", "setTimeout", "$refs", "location", "remoteMethod", "handleUpdate", "_this5", "getCharacteristics", "_iterator3", "_step3", "v", "_iterator4", "_step4", "a", "includes", "carrierId", "_iterator5", "_step5", "_iterator8", "_step8", "_iterator6", "_step6", "_iterator7", "_step7", "submitForm", "_this6", "validate", "valid", "updateCharacteristics", "$modal", "msgSuccess", "addCharacteristics", "handleDelete", "_this7", "characteristicsIds", "$confirm", "customClass", "delCharacteristics", "catch", "handleExport", "download", "_objectSpread2", "concat", "getTime", "queryServiceTypeId", "queryCargoTypeIds", "queryLocationDepartureIds", "queryLineDepartureIds", "queryLocationDestinationIds", "queryLineDestinationIds", "queryInfoId", "getServiceTypeId", "getCurrencyId", "currencyId", "getUnitId", "unitId", "getCargoTypeIds", "getCompanyId", "getLocationDepartureIds", "getLineDepartureIds", "getLocationDestinationIds", "getLineDestinationIds", "getInfoId", "handleSelectCarrierIds", "handleSelectQueryCarrierIds", "handleDeselectCarrierIds", "filter", "handleDeselectQueryCarrierIds", "deselectAllCarrrierIds", "deselectAllQueryCarrierIds", "queryCompanyId", "exports", "_default"], "sources": ["src/views/system/characteristics/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-if=\"showSearch\">\r\n          <el-form-item label=\"筛选\">\r\n            <el-select v-model=\"queryParams.accurate\" style=\"width: 100%\" @change=\"handleQuery\">\r\n              <el-option :value=\"1\" label=\"相关所有\">相关所有</el-option>\r\n              <el-option :value=\"2\" label=\"向上所属\">向上所属</el-option>\r\n              <el-option :value=\"3\" label=\"向下包含\">向下包含</el-option>\r\n              <el-option :value=\"4\" label=\"精确匹配\">精确匹配</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"服务\" prop=\"serviceTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"queryParams.serviceTypeId\"\r\n                         :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                         style=\"width: 100%\" @return=\"queryServiceTypeId\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"货物\" prop=\"cargoTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.cargoTypeIds\" :placeholder=\"'货物特征'\"\r\n                         :type=\"'cargoType'\" style=\"width: 100%\" @return=\"queryCargoTypeIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"承运\" prop=\"carrierIds\">\r\n            <treeselect v-model=\"queryCarrierIds\" :disable-fuzzy-matching=\"true\" :disable-branch-nodes=\"true\"\r\n                        :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                        :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                        placeholder=\"承运人\" style=\"width: 100%\" @input=\"deselectAllQueryCarrierIds\"\r\n                         @open=\"loadCarrier\"\r\n                        @deselect=\"handleDeselectQueryCarrierIds\" @select=\"handleSelectQueryCarrierIds\">\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{ node.raw.carrier.carrierIntlCode }}\r\n                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : '' }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\">\r\n                {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"订舱\" prop=\"companyId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"queryParams.companyId\" style=\"width: 100%\"\r\n                         :placeholder=\"'订舱口'\" :type=\"'supplier'\" @return=\"queryCompanyId\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"locationDepartureIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDepartureIds\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDepartureIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"目的\" prop=\"locationDestinationIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDestinationIds\"\r\n                             :en=\"true\" :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDestinationIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.lineDestinationIds\"\r\n                         :placeholder=\"'目的航线'\"\r\n                         :type=\"'line'\" style=\"width: 100%\" @return=\"queryLineDestinationIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"要素\" prop=\"infoId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"queryParams.infoId\" :type=\"'commonInfo'\"\r\n                         @return=\"queryInfoId\" style=\"width: 100%\" :placeholder=\"'物流要素'\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"详细\" prop=\"essentialDetail\">\r\n            <el-input v-model=\"queryParams.essentialDetail\" placeholder=\"要素详细\" style=\"width: 100%\"\r\n                      @focusout.native=\"handleQuery\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"有效\" prop=\"isValid\">\r\n            <el-select v-model=\"queryParams.isValid\" placeholder=\"是否有效\" style=\"width: 100%\" @change=\"handleQuery\">\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_yes_no\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"模式\" prop=\"showMode\">\r\n            <el-select v-model=\"queryParams.showMode\" clearable placeholder=\"显示模式\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\">\r\n              <el-option value=\"0\" label=\"小白模式\"/>\r\n              <el-option value=\"1\" label=\"专业模式\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"员工\" prop=\"updateBy\">\r\n            <el-select v-model=\"queryParams.updateBy\" filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\" clearable>\r\n              <el-option\r\n                v-for=\"staff in userList\"\r\n                :key=\"staff.staffId\"\r\n                :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                :value=\"staff.staffId\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"时间\" prop=\"updateTime\">\r\n            <el-date-picker v-model=\"queryParams.updateTime\"\r\n                            clearable\r\n                            placeholder=\"录入起始时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"date\"\r\n                            @change=\"handleQuery\"\r\n                            value-format=\"yyyy-MM-dd\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:characteristics:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['system:characteristics:edit']\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:characteristics:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:characteristics:export']\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"characteristicsList\" @selection-change=\"handleSelectionChange\"\r\n                  :row-class-name=\"tableRowClassName\" border>\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"center\" label=\"内部序号\" prop=\"richNo\" show-tooltip-when-overflow width=\"80\"/>\r\n          <el-table-column align=\"left\" label=\"服务类型\" prop=\"serviceType\" width=\"58px\"/>\r\n          <el-table-column key=\"cargoType\" align=\"left\" label=\"货物特征\" prop=\"cargoType\" width=\"68px\"\r\n                           show-tooltip-when-overflow>\r\n          </el-table-column>\r\n          <el-table-column key=\"carrier\" align=\"left\" label=\"承运人\" width=\"58px\"\r\n                           show-tooltip-when-overflow>\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{ scope.row.carrier == null ? '全部' : scope.row.carrier }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"订舱口\" prop=\"company\" show-tooltip-when-overflow width=\"58px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{ scope.row.companyId == null ? '全部' : scope.row.company }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"启运区域\" show-tooltip-when-overflow width=\"69px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{\r\n                  (scope.row.locationDeparture != null ? scope.row.locationDeparture : '') + (scope.row.locationDeparture != null && scope.row.lineDeparture != null ? ',' : '') + (scope.row.lineDeparture != null ? scope.row.lineDeparture : '')\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"目的区域\" show-tooltip-when-overflow width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{\r\n                  (scope.row.locationDestinationEn != null ? scope.row.locationDestinationEn : '') + (scope.row.lineDestination != null && scope.row.locationDestinationEn != null ? ',' : '') + (scope.row.lineDestination != null ? scope.row.lineDestination : '')\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"物流要素\" prop=\"info\" width=\"68\"/>\r\n          <el-table-column align=\"left\" label=\"要素详细\" prop=\"essentialDetail\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip v-if=\"scope.row.essentialDetail!=null&&scope.row.essentialDetail.length>13\" placement=\"top\">\r\n                <div slot=\"content\">\r\n                  <div v-for=\"data in scope.row.essentialDetail.split('\\n')\">\r\n                    <h6 style=\"margin: 0;\">\r\n                      {{ data }}\r\n                    </h6>\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <h6 style=\"margin: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                    {{ scope.row.essentialDetail }}\r\n                  </h6>\r\n                </div>\r\n              </el-tooltip>\r\n              <div v-else>\r\n                <h6 style=\"margin: 0;\">\r\n                  {{ scope.row.essentialDetail }}\r\n                </h6>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"有效时间\" prop=\"validTime\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.validFrom, '{y}.{m}.{d}') }}-{{\r\n                  parseTime(scope.row.validTo, '{y}.{m}.{d}')\r\n                }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"有效\" prop=\"isValid\" width=\"38\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.isValid\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"显示模式\" align=\"center\" prop=\"showMode\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag v-if=\"scope.row.showMode == '0'\" type=\"primary\">小白模式</el-tag>\r\n              <el-tag v-if=\"scope.row.showMode == '1'\" type=\"primary\">专业模式</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <!--          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"58\">-->\r\n          <!--            <template slot-scope=\"scope\">-->\r\n          <!--              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>-->\r\n          <!--            </template>-->\r\n          <!--          </el-table-column>-->\r\n          <el-table-column align=\"left\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"center\" label=\"重要\" prop=\"orderNum\" width=\"33\"/>\r\n          <el-table-column align=\"center\" label=\"录入时间\" prop=\"updateTime\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0;\">{{ scope.row.updateByName }}</h6>\r\n              <h6 style=\"margin: 0;\">{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:characteristics:edit']\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:characteristics:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改物流注意事项对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-dialogDrag v-dialogDragWidth\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"500px\"\r\n      append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"78px\" class=\"edit\">\r\n        <el-form-item label=\"服务类型\" prop=\"serviceTypeId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.serviceTypeId\" :type=\"'serviceType'\"\r\n                       @return=\"getServiceTypeId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"货物特征\" prop=\"cargoTypeIds\">\r\n          <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.cargoTypeIds\" :type=\"'cargoType'\"\r\n                       @return=\"getCargoTypeIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"承运人\" prop=\"carrierIds\">\r\n          <treeselect v-model=\"carrierIds\" :disable-fuzzy-matching=\"true\"\r\n                      :flat=\"true\" :flatten-search-results=\"true\" :disable-branch-nodes=\"true\"\r\n                      :multiple=\"true\" disable-branch-nodes @open=\"loadCarrier\"\r\n                      :normalizer=\"carrierNormalizer\" placeholder=\"选择承运人\" :options=\"temCarrierList\"\r\n                      :show-count=\"true\" @input=\"deselectAllCarrrierIds\"\r\n                      @deselect=\"handleDeselectCarrierIds\" @select=\"handleSelectCarrierIds\">\r\n            <div slot=\"value-label\" slot-scope=\"{node}\">\r\n              {{ node.raw.carrier.carrierIntlCode }}\r\n              {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : '' }}\r\n            </div>\r\n            <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                   :class=\"labelClassName\">\r\n              {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n              <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n            </label>\r\n          </treeselect>\r\n        </el-form-item>\r\n        <el-form-item label=\"订舱口\" prop=\"companyId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.companyId\"\r\n                       :placeholder=\"'订舱口'\" :type=\"'supplier'\" @return=\"getCompanyId\"/>\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-form-item label=\"启运区域\" prop=\"locationDepartureIds\">\r\n            <location-select :multiple=\"true\" :pass=\"form.locationDepartureIds\"\r\n                             ref=\"location\" :load-options=\"locationOptions\"\r\n                             @return=\"getLocationDepartureIds\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"目的区域\" prop=\"locationDestinationIds\">\r\n            <location-select :multiple=\"true\" :pass=\"form.locationDestinationIds\"\r\n                             :load-options=\"locationOptions\"\r\n                             :en=\"true\" @return=\"getLocationDestinationIds\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"目的航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.lineDestinationIds\" :type=\"'line'\"\r\n                         @return=\"getLineDestinationIds\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-form-item label=\"物流要素\" prop=\"infoId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.infoId\" :type=\"'commonInfo'\" @return=\"getInfoId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"要素详细\" prop=\"essentialDetail\">\r\n          <el-input v-model=\"form.essentialDetail\" type=\"textarea\" :autosize=\"{ minRows: 5, maxRows: 20}\"\r\n                    maxlength=\"300\" placeholder=\"内容\" show-word-limit/>\r\n        </el-form-item>\r\n        <el-row :gutter=\"5\">\r\n          <el-col :span=\"15\">\r\n            <el-form-item label=\"有效时间\" prop=\"validTime\">\r\n              <el-date-picker v-model=\"validTime\"\r\n                              clearable\r\n                              placeholder=\"有效时间\"\r\n                              style=\"width: 100%\"\r\n                              default-time=\"['00:00:00', '23:59:59']\"\r\n                              @change=\"changeTime\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              type=\"daterange\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"9\">\r\n            <el-form-item label=\"录入时间\" prop=\"updateTime\">\r\n              <el-date-picker v-model=\"form.updateTime\"\r\n                              clearable\r\n                              placeholder=\"录入时间\"\r\n                              style=\"width: 100%\"\r\n                              disabled\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"5\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否有效\" prop=\"isValid\">\r\n              <el-select v-model=\"form.isValid\" placeholder=\"是否有效\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_yes_no\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-select v-model=\"form.status\" placeholder=\"状态\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"显示模式\" prop=\"showMode\">\r\n          <el-select v-model=\"form.showMode\" placeholder=\"显示模式\" style=\"width: 100%\">\r\n            <el-option value=\"0\" label=\"小白模式\"/>\r\n            <el-option value=\"1\" label=\"专业模式\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"重要程度\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" :min=\"0\" placeholder=\"排序\" style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input type=\"textarea\" v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCharacteristics,\r\n  delCharacteristics,\r\n  getCharacteristics,\r\n  listCharacteristics,\r\n  updateCharacteristics\r\n} from \"@/api/system/characteristics\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\";\r\nimport pinyin from \"js-pinyin\";\r\nimport store from \"@/store\";\r\nimport {parseTime} from \"@/utils/rich\";\r\nimport {selectListUser} from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Characteristics\",\r\n  dicts: ['sys_normal_disable', 'sys_yes_no'],\r\n  components: {Treeselect},\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      validTime: [],\r\n      carrierList: [],\r\n      carrierIds: [],\r\n      queryCarrierIds: [],\r\n      temCarrierList: [],\r\n      locationOptions: [],\r\n      userList: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 物流注意事项表格数据\r\n      characteristicsList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        accurate: 1,\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        infoId: null,\r\n        showMode: null,\r\n        serviceTypeId: null,\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        carrierIds: [],\r\n        companyId: null,\r\n        essentialDetail: null,\r\n        isValid: \"Y\",\r\n        updateBy: null,\r\n        updateTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        cargoTypeIds: {required: true, trigger: \"blur\"}\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    'form.serviceTypeId'(n) {\r\n      this.loadCarrier()\r\n      let list = []\r\n      if (this.carrierList != undefined && n != null) {\r\n        this.temCarrierList = this.carrierList\r\n        /* for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (this.form.carrierIds != null && this.form.carrierIds.includes(b.carrier.carrierId) && !this.carrierIds.includes(b.serviceTypeId)) {\r\n                    this.carrierIds.push(b.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } */\r\n      }\r\n      if (this.carrierList != undefined && n != null) {\r\n        for (const c of this.carrierList) {\r\n          if (n != null && n != undefined) {\r\n            if (c.serviceTypeId == n) {\r\n              list.push(c)\r\n            }\r\n            if (c.children != undefined && c.children.length > 0) {\r\n              for (const ch of c.children) {\r\n                if (ch.serviceTypeId == n) {\r\n                  list.push(ch)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.temCarrierList = list\r\n        if (this.temCarrierList.length > 0) {\r\n          /* for (const v of this.temCarrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (this.form.carrierIds != null && this.form.carrierIds.includes(a.carrier.carrierId) && !this.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          } */\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.loadCarrier();\r\n    selectListUser().then(response => {\r\n      this.userList = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    changeTime(val) {\r\n      if (val == undefined) {\r\n        this.form.validFrom = null\r\n        this.form.validTo = null\r\n      }\r\n      this.form.validFrom = val[0]\r\n      this.form.validTo = val[1]\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch('getServiceTypeCarriersList').then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + ' ' + node.serviceEnName + ',' + pinyin.getFullChars(node.serviceLocalName)\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : '') + ' ' + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : '') + ' ' + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : '') + ',' + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : ''))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children,\r\n      }\r\n    },\r\n    tableRowClassName({row}) {\r\n      let date = parseTime(new Date(), \"{y}-{m}-{d}\")\r\n      let validFrom = parseTime(row.validFrom, \"{y}-{m}-{d}\")\r\n      let validTo = parseTime(row.validTo, \"{y}-{m}-{d}\")\r\n      if (validFrom < date < validTo)\r\n        return ''\r\n      if (validTo < date)\r\n        return 'valid-row';\r\n      if (validFrom > date)\r\n        return 'valid-before';\r\n      return ''\r\n    },\r\n    /** 查询物流注意事项列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCharacteristics(this.queryParams).then(response => {\r\n        this.characteristicsList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        characteristicsId: null,\r\n        serviceTypeId: this.form.serviceTypeId != undefined ? this.form.serviceTypeId : 21,\r\n        infoId: null,\r\n        showMode: \"0\",\r\n        essentialDetail: null,\r\n        updateTime: parseTime(new Date()),\r\n        validFrom: null,\r\n        validTo: null,\r\n        isValid: \"Y\",\r\n        status: \"0\",\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\",\r\n        cargoTypeIds: this.form.cargoTypeIds != undefined ? this.form.cargoTypeIds : [-1],\r\n        locationDepartureIds: this.form.locationDepartureIds != undefined ? this.form.locationDepartureIds : [13716],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        carrierIds: [],\r\n      };\r\n      this.validTime = []\r\n      this.carrierIds = []\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.characteristicsId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加物流注意事项\";\r\n      setTimeout(() => {\r\n        this.$refs.location.remoteMethod(\"广东\")\r\n      }, 0)\r\n      // let date = new Date()\r\n      // this.validTime.push(date)\r\n      // this.validTime.push(new Date(new Date(date.getFullYear(), date.getMonth() + 1, 1) - 1000 * 60 * 60 * 24))\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.loading = true\r\n      const characteristicsId = row.characteristicsId || this.ids\r\n      getCharacteristics(characteristicsId).then(response => {\r\n        this.form = response.data;\r\n        this.form.cargoTypeIds = response.cargoTypeIds;\r\n        this.form.lineDepartureIds = response.lineDepartureIds;\r\n        this.form.locationDepartureIds = response.locationDepartureIds;\r\n        this.form.lineDestinationIds = response.lineDestinationIds;\r\n        this.form.locationDestinationIds = response.locationDestinationIds;\r\n        this.form.carrierIds = response.carrierIds\r\n\r\n        this.loadCarrier()\r\n        let list = []\r\n        if (this.carrierList != undefined && this.form.serviceTypeId != null) {\r\n          this.temCarrierList = this.carrierList\r\n          for (const v of this.carrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (response.carrierIds != null && response.carrierIds.includes(a.carrier.carrierId) && !response.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.carrierList != undefined && this.form.serviceTypeId != null) {\r\n          for (const c of this.carrierList) {\r\n            if (this.form.serviceTypeId != null && this.form.serviceTypeId != undefined) {\r\n              if (c.serviceTypeId == this.form.serviceTypeId) {\r\n                list.push(c)\r\n              }\r\n              if (c.children != undefined && c.children.length > 0) {\r\n                for (const ch of c.children) {\r\n                  if (ch.serviceTypeId == this.form.serviceTypeId) {\r\n                    list.push(ch)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.temCarrierList = list\r\n          if (this.form.serviceTypeId == null && this.temCarrierList.length > 0) {\r\n            for (const v of this.temCarrierList) {\r\n              if (v.children != undefined && v.children.length > 0) {\r\n                for (const a of v.children) {\r\n                  if (response.carrierIds != null && response.carrierIds.includes(a.carrier.carrierId) && !response.carrierIds.includes(a.serviceTypeId)) {\r\n                    this.carrierIds.push(a.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (response.data.validFrom)\r\n          this.validTime.push(response.data.validFrom)\r\n        if (response.data.validTo)\r\n          this.validTime.push(response.data.validTo)\r\n        this.locationOptions = response.locationOptions\r\n        this.open = true;\r\n        this.title = \"修改物流注意事项\";\r\n        this.loading = false\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.characteristicsId != null) {\r\n            updateCharacteristics(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCharacteristics(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const characteristicsIds = row.characteristicsId || this.ids;\r\n      this.$confirm('是否确认删除物流注意事项编号为\"' + characteristicsIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCharacteristics(characteristicsIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/characteristics/export', {\r\n        ...this.queryParams\r\n      }, `characteristics_${new Date().getTime()}.xlsx`)\r\n    },\r\n    queryServiceTypeId(val) {\r\n      this.queryParams.serviceTypeId = val\r\n      this.handleQuery()\r\n    },\r\n    queryCargoTypeIds(val) {\r\n      this.queryParams.cargoTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDepartureIds(val) {\r\n      this.queryParams.locationDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLineDepartureIds(val) {\r\n      this.queryParams.lineDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDestinationIds(val) {\r\n      this.queryParams.locationDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLineDestinationIds(val) {\r\n      this.queryParams.lineDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryInfoId(val) {\r\n      this.queryParams.infoId = val\r\n      this.handleQuery()\r\n    },\r\n    getServiceTypeId(val) {\r\n      if (val == undefined) {\r\n        this.form.serviceTypeId = null\r\n        this.carrierIds = null\r\n        this.form.carrierIds = null\r\n      } else {\r\n        this.form.serviceTypeId = val\r\n      }\r\n    },\r\n    getCurrencyId(val) {\r\n      if (val == undefined) {\r\n        this.form.currencyId = null\r\n      } else {\r\n        this.form.currencyId = val\r\n      }\r\n    },\r\n    getUnitId(val) {\r\n      if (val == undefined) {\r\n        this.form.unitId = null\r\n      } else {\r\n        this.form.unitId = val\r\n      }\r\n    },\r\n    getCargoTypeIds(val) {\r\n      this.form.cargoTypeIds = val\r\n    },\r\n    getCompanyId(val) {\r\n      this.form.companyId = val\r\n    },\r\n    getLocationDepartureIds(val) {\r\n      this.form.locationDepartureIds = val\r\n    },\r\n    getLineDepartureIds(val) {\r\n      this.form.lineDepartureIds = val\r\n    },\r\n    getLocationDestinationIds(val) {\r\n      this.form.locationDestinationIds = val\r\n    },\r\n    getLineDestinationIds(val) {\r\n      this.form.lineDestinationIds = val\r\n    },\r\n    getInfoId(val) {\r\n      this.form.infoId = val\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleSelectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds.push(node.carrier.carrierId)\r\n      this.handleQuery()\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    handleDeselectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n      this.handleQuery()\r\n    },\r\n    deselectAllCarrrierIds(value) {\r\n      if (value.length == 0) {\r\n        this.form.carrierIds = []\r\n      }\r\n    },\r\n    deselectAllQueryCarrierIds(value) {\r\n      if (value.length == 0) {\r\n        this.queryParams.carrierIds = []\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    queryCompanyId(val) {\r\n      this.queryParams.companyId = val\r\n      this.handleQuery()\r\n    },\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAgbA,IAAAA,gBAAA,GAAAC,OAAA;AAOA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,MAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,KAAA,GAAAL,OAAA;AACA,IAAAM,KAAA,GAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAO,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,SAAA;MACAC,WAAA;MACAC,UAAA;MACAC,eAAA;MACAC,cAAA;MACAC,eAAA;MACAC,QAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,mBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,aAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,gBAAA;QACAC,sBAAA;QACAC,kBAAA;QACAzB,UAAA;QACA0B,SAAA;QACAC,eAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAX,YAAA;UAAAY,QAAA;UAAAC,OAAA;QAAA;MACA;IACA;EACA;EACAC,KAAA;IACA1B,UAAA,WAAAA,WAAA2B,CAAA;MACA,IAAAA,CAAA;QACA,KAAAvC,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACA,+BAAAyC,kBAAAD,CAAA;MACA,KAAAE,WAAA;MACA,IAAAC,IAAA;MACA,SAAAxC,WAAA,IAAAyC,SAAA,IAAAJ,CAAA;QACA,KAAAlC,cAAA,QAAAH,WAAA;QACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACA;;MACA,SAAAA,WAAA,IAAAyC,SAAA,IAAAJ,CAAA;QAAA,IAAAK,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAA5C,WAAA;UAAA6C,KAAA;QAAA;UAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAL,CAAA,IAAAU,IAAA;YAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;YACA,IAAAZ,CAAA,YAAAA,CAAA,IAAAI,SAAA;cACA,IAAAO,CAAA,CAAA3B,aAAA,IAAAgB,CAAA;gBACAG,IAAA,CAAAU,IAAA,CAAAF,CAAA;cACA;cACA,IAAAA,CAAA,CAAAG,QAAA,IAAAV,SAAA,IAAAO,CAAA,CAAAG,QAAA,CAAAC,MAAA;gBAAA,IAAAC,UAAA,OAAAV,2BAAA,CAAAC,OAAA,EACAI,CAAA,CAAAG,QAAA;kBAAAG,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAAP,CAAA,MAAAQ,MAAA,GAAAD,UAAA,CAAAhB,CAAA,IAAAU,IAAA;oBAAA,IAAAQ,EAAA,GAAAD,MAAA,CAAAL,KAAA;oBACA,IAAAM,EAAA,CAAAlC,aAAA,IAAAgB,CAAA;sBACAG,IAAA,CAAAU,IAAA,CAAAK,EAAA;oBACA;kBACA;gBAAA,SAAAC,GAAA;kBAAAH,UAAA,CAAAI,CAAA,CAAAD,GAAA;gBAAA;kBAAAH,UAAA,CAAAK,CAAA;gBAAA;cACA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAd,SAAA,CAAAe,CAAA,CAAAD,GAAA;QAAA;UAAAd,SAAA,CAAAgB,CAAA;QAAA;QACA,KAAAvD,cAAA,GAAAqC,IAAA;QACA,SAAArC,cAAA,CAAAiD,MAAA;UACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QARA;MAUA;IACA;EACA;EACAO,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAtB,WAAA;IACA,IAAAuB,oBAAA,IAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAvD,QAAA,GAAA2D,QAAA,CAAApE,IAAA;IACA;EACA;EACAqE,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,IAAAA,GAAA,IAAA1B,SAAA;QACA,KAAAT,IAAA,CAAAoC,SAAA;QACA,KAAApC,IAAA,CAAAqC,OAAA;MACA;MACA,KAAArC,IAAA,CAAAoC,SAAA,GAAAD,GAAA;MACA,KAAAnC,IAAA,CAAAqC,OAAA,GAAAF,GAAA;IACA;IACA5B,WAAA,WAAAA,YAAA;MAAA,IAAA+B,MAAA;MACA,SAAAC,MAAA,CAAAC,KAAA,CAAA5E,IAAA,CAAA6E,mBAAA,CAAArB,MAAA,cAAAmB,MAAA,CAAAC,KAAA,CAAA5E,IAAA,CAAA8E,SAAA,CAAAD,mBAAA;QACAE,cAAA,CAAAC,QAAA,+BAAAb,IAAA;UACAO,MAAA,CAAAtE,WAAA,GAAAsE,MAAA,CAAAC,MAAA,CAAAC,KAAA,CAAA5E,IAAA,CAAA6E,mBAAA;QACA;MACA;QACA,KAAAzE,WAAA,QAAAuE,MAAA,CAAAC,KAAA,CAAA5E,IAAA,CAAA6E,mBAAA;MACA;IACA;IACAI,iBAAA,WAAAA,kBAAAC,IAAA;MACA,IAAAA,IAAA,CAAA3B,QAAA,KAAA2B,IAAA,CAAA3B,QAAA,CAAAC,MAAA;QACA,OAAA0B,IAAA,CAAA3B,QAAA;MACA;MACA,IAAA4B,CAAA;MACA,KAAAD,IAAA,CAAAE,OAAA,IAAAF,IAAA,CAAAE,OAAA,CAAAC,gBAAA,YAAAH,IAAA,CAAAE,OAAA,CAAAE,aAAA;QACAH,CAAA,GAAAD,IAAA,CAAAK,gBAAA,SAAAL,IAAA,CAAAM,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAK,gBAAA;MACA;QACAJ,CAAA,IAAAD,IAAA,CAAAE,OAAA,CAAAO,eAAA,WAAAT,IAAA,CAAAE,OAAA,CAAAO,eAAA,gBAAAT,IAAA,CAAAE,OAAA,CAAAE,aAAA,WAAAJ,IAAA,CAAAE,OAAA,CAAAE,aAAA,gBAAAJ,IAAA,CAAAE,OAAA,CAAAC,gBAAA,WAAAH,IAAA,CAAAE,OAAA,CAAAC,gBAAA,eAAAI,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAE,OAAA,CAAAC,gBAAA,WAAAH,IAAA,CAAAE,OAAA,CAAAC,gBAAA;MACA;MACA;QACAO,EAAA,EAAAV,IAAA,CAAAzD,aAAA;QACAoE,KAAA,EAAAV,CAAA;QACA5B,QAAA,EAAA2B,IAAA,CAAA3B;MACA;IACA;IACAuC,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;MACA,IAAAC,IAAA,OAAAC,eAAA,MAAAC,IAAA;MACA,IAAA3B,SAAA,OAAA0B,eAAA,EAAAF,GAAA,CAAAxB,SAAA;MACA,IAAAC,OAAA,OAAAyB,eAAA,EAAAF,GAAA,CAAAvB,OAAA;MACA,IAAAD,SAAA,GAAAyB,IAAA,GAAAxB,OAAA,EACA;MACA,IAAAA,OAAA,GAAAwB,IAAA,EACA;MACA,IAAAzB,SAAA,GAAAyB,IAAA,EACA;MACA;IACA;IACA,iBACAhC,OAAA,WAAAA,QAAA;MAAA,IAAAmC,MAAA;MACA,KAAA1F,OAAA;MACA,IAAA2F,oCAAA,OAAAlF,WAAA,EAAAgD,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAApF,mBAAA,GAAAoD,QAAA,CAAAkC,IAAA;QACAF,MAAA,CAAArF,KAAA,GAAAqD,QAAA,CAAArD,KAAA;QACAqF,MAAA,CAAA1F,OAAA;MACA;IACA;IACA;IACA6F,MAAA,WAAAA,OAAA;MACA,KAAArF,IAAA;MACA,KAAAsF,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAApE,IAAA;QACAqE,iBAAA;QACAhF,aAAA,OAAAW,IAAA,CAAAX,aAAA,IAAAoB,SAAA,QAAAT,IAAA,CAAAX,aAAA;QACAF,MAAA;QACAC,QAAA;QACAQ,eAAA;QACAG,UAAA,MAAA+D,eAAA,MAAAC,IAAA;QACA3B,SAAA;QACAC,OAAA;QACAxC,OAAA;QACAyE,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACA3E,QAAA;QACA4E,QAAA;QACAC,UAAA;QACAC,YAAA;QACAtF,YAAA,OAAAU,IAAA,CAAAV,YAAA,IAAAmB,SAAA,QAAAT,IAAA,CAAAV,YAAA;QACAC,oBAAA,OAAAS,IAAA,CAAAT,oBAAA,IAAAkB,SAAA,QAAAT,IAAA,CAAAT,oBAAA;QACAC,gBAAA;QACAC,sBAAA;QACAC,kBAAA;QACAzB,UAAA;MACA;MACA,KAAAF,SAAA;MACA,KAAAE,UAAA;MACA,KAAA4G,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA/F,WAAA,CAAAE,OAAA;MACA,KAAA4C,OAAA;IACA;IACA,aACAkD,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA1G,GAAA,GAAA0G,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAd,iBAAA;MAAA;MACA,KAAA7F,MAAA,GAAAyG,SAAA,CAAA7D,MAAA;MACA,KAAA3C,QAAA,IAAAwG,SAAA,CAAA7D,MAAA;IACA;IACA,aACAgE,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAjB,KAAA;MACA,KAAAtF,IAAA;MACA,KAAAD,KAAA;MACAyG,UAAA;QACAD,MAAA,CAAAE,KAAA,CAAAC,QAAA,CAAAC,YAAA;MACA;MACA;MACA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA9B,GAAA;MAAA,IAAA+B,MAAA;MACA,KAAAvB,KAAA;MACA,KAAA9F,OAAA;MACA,IAAA+F,iBAAA,GAAAT,GAAA,CAAAS,iBAAA,SAAA9F,GAAA;MACA,IAAAqH,mCAAA,EAAAvB,iBAAA,EAAAtC,IAAA,WAAAC,QAAA;QACA2D,MAAA,CAAA3F,IAAA,GAAAgC,QAAA,CAAApE,IAAA;QACA+H,MAAA,CAAA3F,IAAA,CAAAV,YAAA,GAAA0C,QAAA,CAAA1C,YAAA;QACAqG,MAAA,CAAA3F,IAAA,CAAAR,gBAAA,GAAAwC,QAAA,CAAAxC,gBAAA;QACAmG,MAAA,CAAA3F,IAAA,CAAAT,oBAAA,GAAAyC,QAAA,CAAAzC,oBAAA;QACAoG,MAAA,CAAA3F,IAAA,CAAAN,kBAAA,GAAAsC,QAAA,CAAAtC,kBAAA;QACAiG,MAAA,CAAA3F,IAAA,CAAAP,sBAAA,GAAAuC,QAAA,CAAAvC,sBAAA;QACAkG,MAAA,CAAA3F,IAAA,CAAA/B,UAAA,GAAA+D,QAAA,CAAA/D,UAAA;QAEA0H,MAAA,CAAApF,WAAA;QACA,IAAAC,IAAA;QACA,IAAAmF,MAAA,CAAA3H,WAAA,IAAAyC,SAAA,IAAAkF,MAAA,CAAA3F,IAAA,CAAAX,aAAA;UACAsG,MAAA,CAAAxH,cAAA,GAAAwH,MAAA,CAAA3H,WAAA;UAAA,IAAA6H,UAAA,OAAAlF,2BAAA,CAAAC,OAAA,EACA+E,MAAA,CAAA3H,WAAA;YAAA8H,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAA/E,CAAA,MAAAgF,MAAA,GAAAD,UAAA,CAAAxF,CAAA,IAAAU,IAAA;cAAA,IAAAgF,CAAA,GAAAD,MAAA,CAAA7E,KAAA;cACA,IAAA8E,CAAA,CAAA5E,QAAA,IAAAV,SAAA,IAAAsF,CAAA,CAAA5E,QAAA,CAAAC,MAAA;gBAAA,IAAA4E,UAAA,OAAArF,2BAAA,CAAAC,OAAA,EACAmF,CAAA,CAAA5E,QAAA;kBAAA8E,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAAlF,CAAA,MAAAmF,MAAA,GAAAD,UAAA,CAAA3F,CAAA,IAAAU,IAAA;oBAAA,IAAAmF,CAAA,GAAAD,MAAA,CAAAhF,KAAA;oBACA,IAAAe,QAAA,CAAA/D,UAAA,YAAA+D,QAAA,CAAA/D,UAAA,CAAAkI,QAAA,CAAAD,CAAA,CAAAlD,OAAA,CAAAoD,SAAA,MAAApE,QAAA,CAAA/D,UAAA,CAAAkI,QAAA,CAAAD,CAAA,CAAA7G,aAAA;sBACAsG,MAAA,CAAA1H,UAAA,CAAAiD,IAAA,CAAAgF,CAAA,CAAA7G,aAAA;oBACA;kBACA;gBAAA,SAAAmC,GAAA;kBAAAwE,UAAA,CAAAvE,CAAA,CAAAD,GAAA;gBAAA;kBAAAwE,UAAA,CAAAtE,CAAA;gBAAA;cACA;YACA;UAAA,SAAAF,GAAA;YAAAqE,UAAA,CAAApE,CAAA,CAAAD,GAAA;UAAA;YAAAqE,UAAA,CAAAnE,CAAA;UAAA;QACA;QACA,IAAAiE,MAAA,CAAA3H,WAAA,IAAAyC,SAAA,IAAAkF,MAAA,CAAA3F,IAAA,CAAAX,aAAA;UAAA,IAAAgH,UAAA,OAAA1F,2BAAA,CAAAC,OAAA,EACA+E,MAAA,CAAA3H,WAAA;YAAAsI,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAAvF,CAAA,MAAAwF,MAAA,GAAAD,UAAA,CAAAhG,CAAA,IAAAU,IAAA;cAAA,IAAAC,CAAA,GAAAsF,MAAA,CAAArF,KAAA;cACA,IAAA0E,MAAA,CAAA3F,IAAA,CAAAX,aAAA,YAAAsG,MAAA,CAAA3F,IAAA,CAAAX,aAAA,IAAAoB,SAAA;gBACA,IAAAO,CAAA,CAAA3B,aAAA,IAAAsG,MAAA,CAAA3F,IAAA,CAAAX,aAAA;kBACAmB,IAAA,CAAAU,IAAA,CAAAF,CAAA;gBACA;gBACA,IAAAA,CAAA,CAAAG,QAAA,IAAAV,SAAA,IAAAO,CAAA,CAAAG,QAAA,CAAAC,MAAA;kBAAA,IAAAmF,UAAA,OAAA5F,2BAAA,CAAAC,OAAA,EACAI,CAAA,CAAAG,QAAA;oBAAAqF,MAAA;kBAAA;oBAAA,KAAAD,UAAA,CAAAzF,CAAA,MAAA0F,MAAA,GAAAD,UAAA,CAAAlG,CAAA,IAAAU,IAAA;sBAAA,IAAAQ,EAAA,GAAAiF,MAAA,CAAAvF,KAAA;sBACA,IAAAM,EAAA,CAAAlC,aAAA,IAAAsG,MAAA,CAAA3F,IAAA,CAAAX,aAAA;wBACAmB,IAAA,CAAAU,IAAA,CAAAK,EAAA;sBACA;oBACA;kBAAA,SAAAC,GAAA;oBAAA+E,UAAA,CAAA9E,CAAA,CAAAD,GAAA;kBAAA;oBAAA+E,UAAA,CAAA7E,CAAA;kBAAA;gBACA;cACA;YACA;UAAA,SAAAF,GAAA;YAAA6E,UAAA,CAAA5E,CAAA,CAAAD,GAAA;UAAA;YAAA6E,UAAA,CAAA3E,CAAA;UAAA;UACAiE,MAAA,CAAAxH,cAAA,GAAAqC,IAAA;UACA,IAAAmF,MAAA,CAAA3F,IAAA,CAAAX,aAAA,YAAAsG,MAAA,CAAAxH,cAAA,CAAAiD,MAAA;YAAA,IAAAqF,UAAA,OAAA9F,2BAAA,CAAAC,OAAA,EACA+E,MAAA,CAAAxH,cAAA;cAAAuI,MAAA;YAAA;cAAA,KAAAD,UAAA,CAAA3F,CAAA,MAAA4F,MAAA,GAAAD,UAAA,CAAApG,CAAA,IAAAU,IAAA;gBAAA,IAAAgF,EAAA,GAAAW,MAAA,CAAAzF,KAAA;gBACA,IAAA8E,EAAA,CAAA5E,QAAA,IAAAV,SAAA,IAAAsF,EAAA,CAAA5E,QAAA,CAAAC,MAAA;kBAAA,IAAAuF,UAAA,OAAAhG,2BAAA,CAAAC,OAAA,EACAmF,EAAA,CAAA5E,QAAA;oBAAAyF,MAAA;kBAAA;oBAAA,KAAAD,UAAA,CAAA7F,CAAA,MAAA8F,MAAA,GAAAD,UAAA,CAAAtG,CAAA,IAAAU,IAAA;sBAAA,IAAAmF,EAAA,GAAAU,MAAA,CAAA3F,KAAA;sBACA,IAAAe,QAAA,CAAA/D,UAAA,YAAA+D,QAAA,CAAA/D,UAAA,CAAAkI,QAAA,CAAAD,EAAA,CAAAlD,OAAA,CAAAoD,SAAA,MAAApE,QAAA,CAAA/D,UAAA,CAAAkI,QAAA,CAAAD,EAAA,CAAA7G,aAAA;wBACAsG,MAAA,CAAA1H,UAAA,CAAAiD,IAAA,CAAAgF,EAAA,CAAA7G,aAAA;sBACA;oBACA;kBAAA,SAAAmC,GAAA;oBAAAmF,UAAA,CAAAlF,CAAA,CAAAD,GAAA;kBAAA;oBAAAmF,UAAA,CAAAjF,CAAA;kBAAA;gBACA;cACA;YAAA,SAAAF,GAAA;cAAAiF,UAAA,CAAAhF,CAAA,CAAAD,GAAA;YAAA;cAAAiF,UAAA,CAAA/E,CAAA;YAAA;UACA;QACA;QACA,IAAAM,QAAA,CAAApE,IAAA,CAAAwE,SAAA,EACAuD,MAAA,CAAA5H,SAAA,CAAAmD,IAAA,CAAAc,QAAA,CAAApE,IAAA,CAAAwE,SAAA;QACA,IAAAJ,QAAA,CAAApE,IAAA,CAAAyE,OAAA,EACAsD,MAAA,CAAA5H,SAAA,CAAAmD,IAAA,CAAAc,QAAA,CAAApE,IAAA,CAAAyE,OAAA;QACAsD,MAAA,CAAAvH,eAAA,GAAA4D,QAAA,CAAA5D,eAAA;QACAuH,MAAA,CAAA7G,IAAA;QACA6G,MAAA,CAAA9G,KAAA;QACA8G,MAAA,CAAArH,OAAA;MACA;IACA;IACA,WACAuI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAvB,KAAA,SAAAwB,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAA9G,IAAA,CAAAqE,iBAAA;YACA,IAAA4C,sCAAA,EAAAH,MAAA,CAAA9G,IAAA,EAAA+B,IAAA,WAAAC,QAAA;cACA8E,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAAhI,IAAA;cACAgI,MAAA,CAAAjF,OAAA;YACA;UACA;YACA,IAAAuF,mCAAA,EAAAN,MAAA,CAAA9G,IAAA,EAAA+B,IAAA,WAAAC,QAAA;cACA8E,MAAA,CAAAI,MAAA,CAAAC,UAAA;cACAL,MAAA,CAAAhI,IAAA;cACAgI,MAAA,CAAAjF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAwF,YAAA,WAAAA,aAAAzD,GAAA;MAAA,IAAA0D,MAAA;MACA,IAAAC,kBAAA,GAAA3D,GAAA,CAAAS,iBAAA,SAAA9F,GAAA;MACA,KAAAiJ,QAAA,sBAAAD,kBAAA;QAAAE,WAAA;MAAA,GAAA1F,IAAA;QACA,WAAA2F,mCAAA,EAAAH,kBAAA;MACA,GAAAxF,IAAA;QACAuF,MAAA,CAAAzF,OAAA;QACAyF,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,sCAAAC,cAAA,CAAAlH,OAAA,MACA,KAAA7B,WAAA,sBAAAgJ,MAAA,CACA,IAAAhE,IAAA,GAAAiE,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA9F,GAAA;MACA,KAAApD,WAAA,CAAAM,aAAA,GAAA8C,GAAA;MACA,KAAA2C,WAAA;IACA;IACAoD,iBAAA,WAAAA,kBAAA/F,GAAA;MACA,KAAApD,WAAA,CAAAO,YAAA,GAAA6C,GAAA;MACA,KAAA2C,WAAA;IACA;IACAqD,yBAAA,WAAAA,0BAAAhG,GAAA;MACA,KAAApD,WAAA,CAAAQ,oBAAA,GAAA4C,GAAA;MACA,KAAA2C,WAAA;IACA;IACAsD,qBAAA,WAAAA,sBAAAjG,GAAA;MACA,KAAApD,WAAA,CAAAS,gBAAA,GAAA2C,GAAA;MACA,KAAA2C,WAAA;IACA;IACAuD,2BAAA,WAAAA,4BAAAlG,GAAA;MACA,KAAApD,WAAA,CAAAU,sBAAA,GAAA0C,GAAA;MACA,KAAA2C,WAAA;IACA;IACAwD,uBAAA,WAAAA,wBAAAnG,GAAA;MACA,KAAApD,WAAA,CAAAW,kBAAA,GAAAyC,GAAA;MACA,KAAA2C,WAAA;IACA;IACAyD,WAAA,WAAAA,YAAApG,GAAA;MACA,KAAApD,WAAA,CAAAI,MAAA,GAAAgD,GAAA;MACA,KAAA2C,WAAA;IACA;IACA0D,gBAAA,WAAAA,iBAAArG,GAAA;MACA,IAAAA,GAAA,IAAA1B,SAAA;QACA,KAAAT,IAAA,CAAAX,aAAA;QACA,KAAApB,UAAA;QACA,KAAA+B,IAAA,CAAA/B,UAAA;MACA;QACA,KAAA+B,IAAA,CAAAX,aAAA,GAAA8C,GAAA;MACA;IACA;IACAsG,aAAA,WAAAA,cAAAtG,GAAA;MACA,IAAAA,GAAA,IAAA1B,SAAA;QACA,KAAAT,IAAA,CAAA0I,UAAA;MACA;QACA,KAAA1I,IAAA,CAAA0I,UAAA,GAAAvG,GAAA;MACA;IACA;IACAwG,SAAA,WAAAA,UAAAxG,GAAA;MACA,IAAAA,GAAA,IAAA1B,SAAA;QACA,KAAAT,IAAA,CAAA4I,MAAA;MACA;QACA,KAAA5I,IAAA,CAAA4I,MAAA,GAAAzG,GAAA;MACA;IACA;IACA0G,eAAA,WAAAA,gBAAA1G,GAAA;MACA,KAAAnC,IAAA,CAAAV,YAAA,GAAA6C,GAAA;IACA;IACA2G,YAAA,WAAAA,aAAA3G,GAAA;MACA,KAAAnC,IAAA,CAAAL,SAAA,GAAAwC,GAAA;IACA;IACA4G,uBAAA,WAAAA,wBAAA5G,GAAA;MACA,KAAAnC,IAAA,CAAAT,oBAAA,GAAA4C,GAAA;IACA;IACA6G,mBAAA,WAAAA,oBAAA7G,GAAA;MACA,KAAAnC,IAAA,CAAAR,gBAAA,GAAA2C,GAAA;IACA;IACA8G,yBAAA,WAAAA,0BAAA9G,GAAA;MACA,KAAAnC,IAAA,CAAAP,sBAAA,GAAA0C,GAAA;IACA;IACA+G,qBAAA,WAAAA,sBAAA/G,GAAA;MACA,KAAAnC,IAAA,CAAAN,kBAAA,GAAAyC,GAAA;IACA;IACAgH,SAAA,WAAAA,UAAAhH,GAAA;MACA,KAAAnC,IAAA,CAAAb,MAAA,GAAAgD,GAAA;IACA;IACAiH,sBAAA,WAAAA,uBAAAtG,IAAA;MACA,KAAA9C,IAAA,CAAA/B,UAAA,CAAAiD,IAAA,CAAA4B,IAAA,CAAAE,OAAA,CAAAoD,SAAA;IACA;IACAiD,2BAAA,WAAAA,4BAAAvG,IAAA;MACA,KAAA/D,WAAA,CAAAd,UAAA,CAAAiD,IAAA,CAAA4B,IAAA,CAAAE,OAAA,CAAAoD,SAAA;MACA,KAAAtB,WAAA;IACA;IACAwE,wBAAA,WAAAA,yBAAAxG,IAAA;MACA,KAAA9C,IAAA,CAAA/B,UAAA,QAAA+B,IAAA,CAAA/B,UAAA,CAAAsL,MAAA,WAAApE,IAAA;QACA,OAAAA,IAAA,IAAArC,IAAA,CAAAE,OAAA,CAAAoD,SAAA;MACA;IACA;IACAoD,6BAAA,WAAAA,8BAAA1G,IAAA;MACA,KAAA/D,WAAA,CAAAd,UAAA,QAAAc,WAAA,CAAAd,UAAA,CAAAsL,MAAA,WAAApE,IAAA;QACA,OAAAA,IAAA,IAAArC,IAAA,CAAAE,OAAA,CAAAoD,SAAA;MACA;MACA,KAAAtB,WAAA;IACA;IACA2E,sBAAA,WAAAA,uBAAAxI,KAAA;MACA,IAAAA,KAAA,CAAAG,MAAA;QACA,KAAApB,IAAA,CAAA/B,UAAA;MACA;IACA;IACAyL,0BAAA,WAAAA,2BAAAzI,KAAA;MACA,IAAAA,KAAA,CAAAG,MAAA;QACA,KAAArC,WAAA,CAAAd,UAAA;QACA,KAAA6G,WAAA;MACA;IACA;IACA6E,cAAA,WAAAA,eAAAxH,GAAA;MACA,KAAApD,WAAA,CAAAY,SAAA,GAAAwC,GAAA;MACA,KAAA2C,WAAA;IACA;EACA;AACA;AAAA8E,OAAA,CAAAhJ,OAAA,GAAAiJ,QAAA"}]}