{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregator\\index.vue", "mtime": 1754876882572}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "names": ["_moment", "_interopRequireDefault", "require", "_currency", "_aggregator", "_html2pdf", "name", "props", "dataSource", "type", "Array", "required", "fieldLabelMap", "Object", "default", "_default", "data", "config<PERSON><PERSON>", "config", "primaryField", "matchOptions", "exact", "caseSensitive", "dateField", "dateOptions", "convertToNumber", "formatType", "showDetails", "fields", "splitByCurrency", "label", "value", "aggregationOptions", "loading", "configDialogVisible", "savedConfigs", "configLoading", "isLandscape", "showResult", "processedData", "computed", "availableFields", "_this", "length", "keys", "filter", "field", "numericFields", "_this2", "currentFieldType", "sampleValue", "moment", "ISO_8601", "<PERSON><PERSON><PERSON><PERSON>", "_typeof2", "groupFieldName", "concat", "getFieldLabel", "dateFields", "_this3", "display", "methods", "getSummary", "_ref", "_this4", "columns", "sums", "for<PERSON>ach", "column", "index", "<PERSON><PERSON><PERSON>", "aggregation", "fieldConfig", "values", "map", "item", "prop", "getResultProp", "Number", "val", "isNaN", "sum", "reduce", "a", "b", "Math", "max", "apply", "_toConsumableArray2", "min", "mean", "pow", "format", "toFixed", "getName", "id", "staff", "$store", "state", "allRsStaffList", "rsStaff", "staffId", "undefined", "staffShortName", "staffFamilyEnName", "_this$fieldLabelMap$f", "groupData", "_this5", "groups", "primaryKeyValue", "toLowerCase", "trim", "groupKey", "date", "dateValue", "getDateFormat", "valueOf", "primary", "toString", "key", "items", "push", "calculateAggregations", "_this6", "group", "result", "isCustomDisplay", "numericValues", "v", "handleAggregate", "$message", "warning", "applySorting", "error", "message", "sortField", "find", "sort", "isAsc", "valueA", "valueB", "Date", "getTime", "String", "handleFieldChange", "<PERSON><PERSON><PERSON>s", "displayFields", "saveConfig", "_this7", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "<PERSON><PERSON><PERSON>", "configToSave", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "saveAggregatorConfig", "success", "t0", "stop", "loadConfigs", "_this8", "_callee2", "configs", "_err$response", "_callee2$", "_context2", "loadAggregatorConfigs", "configType", "sent", "rows", "isArray", "Error", "createTime", "secondaryField", "textMatchMode", "dateGranularity", "aggregationMethods", "console", "response", "finish", "handleConfigSelect", "row", "_this9", "_callee3", "defaultConfig", "_callee3$", "_context3", "_objectSpread2", "JSON", "parse", "$nextTick", "log", "err", "deleteConfig", "_this10", "_callee4", "_callee4$", "_context4", "$confirm", "deleteAggregatorConfig", "printTable", "printWindow", "window", "open", "table", "$refs", "resultTable", "$el", "cloneNode", "title", "toLocaleDateString", "headerTemplate", "document", "write", "outerHTML", "replace", "close", "setTimeout", "focus", "print", "e", "exportToPDF", "_this11", "_callee5", "element", "opt", "_callee5$", "_context5", "margin", "filename", "image", "quality", "html2canvas", "scale", "jsPDF", "unit", "orientation", "pagebreak", "mode", "header", "text", "style", "alignment", "footer", "height", "contents", "html2pdf", "set", "from", "save", "addField", "removeField", "splice", "moveField", "direction", "_ref2", "_ref3", "$set", "handleFieldSelect", "getDefaultFormat", "aggregated", "getFieldDisplay", "isAggregatable", "displayType", "resetConfig", "getResultLabel", "baseLabel", "_this$aggregationOpti", "aggregationLabel", "formatCellValue", "numValue", "formatGroupKey", "primaryFieldConfig", "primaryValue", "exports", "_default2"], "sources": ["src/views/system/DataAggregator/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"data-aggregator\">\r\n    <el-row :gutter=\"20\">\r\n      <!-- 配置区域 - 左侧 -->\r\n      <el-col :span=\"showResult ? 10 : 10\">\r\n        <el-card class=\"config-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总配置</span>\r\n            </div>\r\n          </template>\r\n          <el-form class=\"edit\" label-width=\"80px\">\r\n            <!-- 速查名称 -->\r\n            <el-form-item label=\"速查名称\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"18\">\r\n                  <el-input v-model=\"config.name\" placeholder=\"请输入速查名称\"/>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-button size=\"small\" type=\"text\" @click=\"saveConfig\">[↗]</el-button>\r\n                  <el-button size=\"small\" type=\"text\" @click=\"loadConfigs\">[...]</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组依据 -->\r\n            <el-form-item label=\"分组依据\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.primaryField\" clearable filterable placeholder=\"操作单号\">\r\n                    <el-option\r\n                      v-for=\"field in availableFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.matchOptions.exact\">精确匹配</el-checkbox>\r\n                  <el-checkbox v-model=\"config.matchOptions.caseSensitive\">区分大小写</el-checkbox>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组日期 -->\r\n            <el-form-item label=\"分组日期\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.dateField\" clearable filterable placeholder=\"分组日期\">\r\n                    <el-option\r\n                      v-for=\"field in dateFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.dateOptions.convertToNumber\">转换为数字</el-checkbox>\r\n                  <el-radio-group v-model=\"config.dateOptions.formatType\" style=\"display: flex;line-height: 26px\">\r\n                    <el-radio label=\"year\">按年</el-radio>\r\n                    <el-radio label=\"month\">按月</el-radio>\r\n                    <el-radio label=\"day\">按天</el-radio>\r\n                  </el-radio-group>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 显示方式 -->\r\n            <el-form-item label=\"显示方式\">\r\n              <el-checkbox v-model=\"config.showDetails\" style=\"padding-left: 5px;\">含明细</el-checkbox>\r\n              <el-switch\r\n                v-model=\"config.splitByCurrency\"\r\n                active-text=\"区分币种\">\r\n              </el-switch>\r\n            </el-form-item>\r\n\r\n            <!-- 动态字段配置 -->\r\n            <el-table\r\n              :data=\"config.fields\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"60\"\r\n              />\r\n\r\n              <el-table-column label=\"表头名称\" min-width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.fieldKey\"\r\n                    filterable\r\n                    placeholder=\"选择字段\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleFieldSelect(scope.$index)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(config, key) in fieldLabelMap\"\r\n                      :key=\"key\"\r\n                      :label=\"config.name\"\r\n                      :value=\"key\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"排序\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.sort\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"∧\" value=\"asc\"/>\r\n                    <el-option label=\"∨ \" value=\"desc\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"汇总方式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.aggregation\"\r\n                    :disabled=\"!isAggregatable(scope.row.fieldKey)\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"求和\" value=\"sum\"/>\r\n                    <el-option label=\"平均值\" value=\"avg\"/>\r\n                    <el-option label=\"最大值\" value=\"max\"/>\r\n                    <el-option label=\"最小值\" value=\"min\"/>\r\n                    <el-option label=\"方差\" value=\"variance\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"显示格式\" width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.format\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'date'\">\r\n                      <el-option label=\"YYYYMM\" value=\"YYYYMM\"/>\r\n                      <el-option label=\"MM-DD\" value=\"MM-DD\"/>\r\n                      <el-option label=\"YYYY-MM-DD\" value=\"YYYY-MM-DD\"/>\r\n                    </template>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'number'\">\r\n                      <el-option label=\"0.00\" value=\"decimal\"/>\r\n                      <el-option label=\"0.00%\" value=\"percent\"/>\r\n                      <el-option label=\"¥0.00\" value=\"currency\"/>\r\n                      <el-option label=\"$0.00\" value=\"usd\"/>\r\n                    </template>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n\r\n              <el-table-column align=\"center\" label=\"操作\" width=\"120\">\r\n                <template #default=\"scope\">\r\n                  <el-button-group>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === 0\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'up')\"\r\n                    >[∧]\r\n                    </el-button>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === config.fields.length - 1\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'down')\"\r\n                    >[∨]\r\n                    </el-button>\r\n                    <el-button\r\n                      icon=\"el-icon-delete\"\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"removeField(scope.$index)\"\r\n                    >\r\n                    </el-button>\r\n                  </el-button-group>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div style=\"margin-top: 10px;\">\r\n              <el-button plain type=\"text\" @click=\"addField\">[ + ]</el-button>\r\n            </div>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleAggregate\">分类汇总</el-button>\r\n              <el-button @click=\"resetConfig\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 结果展示 - 右侧 -->\r\n      <el-col v-if=\"showResult\" :span=\"14\">\r\n        <el-card class=\"result-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总结果</span>\r\n              <div class=\"operations\">\r\n                <el-switch\r\n                  v-model=\"isLandscape\"\r\n                  active-text=\"横向\"\r\n                  inactive-text=\"纵向\"\r\n                  style=\"margin-right: 15px\"\r\n                />\r\n                <el-button size=\"small\" @click=\"printTable\">打印</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"exportToPDF\">导出PDF</el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <el-table\r\n            ref=\"resultTable\"\r\n            v-loading=\"loading\"\r\n            :data=\"processedData\"\r\n            border\r\n            :summary-method=\"getSummary\"\r\n            show-summary\r\n            style=\"width: 100%\"\r\n          >\r\n            <!-- 分组字段列 -->\r\n            <el-table-column\r\n              :align=\"config.primaryField ? fieldLabelMap[config.primaryField].align : 'left'\"\r\n              :label=\"groupFieldName\"\r\n              :width=\"config.primaryField ? fieldLabelMap[config.primaryField].width : ''\"\r\n            >\r\n              <template #default=\"scope\">\r\n                {{ formatGroupKey(scope.row.groupKey) }}\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <!-- 动态字段列 -->\r\n            <template v-for=\"field in config.fields\">\r\n              <el-table-column\r\n                v-if=\"field.fieldKey\"\r\n                :key=\"field.fieldKey\"\r\n                :align=\"fieldLabelMap[field.fieldKey].align\"\r\n                :label=\"getResultLabel(field)\"\r\n                :width=\"fieldLabelMap[field.fieldKey].width\"\r\n              >\r\n                <template #default=\"scope\">\r\n                  {{ formatCellValue(scope.row[getResultProp(field)], field) }}\r\n                </template>\r\n              </el-table-column>\r\n            </template>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 加载配置对话框 -->\r\n    <el-dialog :visible.sync=\"configDialogVisible\" append-to-body title=\"加载配置\" width=\"500px\">\r\n      <el-table\r\n        v-loading=\"configLoading\"\r\n        :data=\"savedConfigs\"\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleConfigSelect\"\r\n      >\r\n        <el-table-column label=\"配置名称\" prop=\"name\"/>\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\"/>\r\n        <el-table-column width=\"120\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" @click.stop=\"deleteConfig(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from \"@/api/system/aggregator\"\r\nimport html2pdf from \"html2pdf.js\"\r\n\r\nexport default {\r\n  name: \"DataAggregator\",\r\n  props: {\r\n    dataSource: {\r\n      type: Array,\r\n      required: true\r\n    },\r\n    fieldLabelMap: {\r\n      type: Object,\r\n      required: true,\r\n      default: () => ({})\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      configName: \"\",\r\n      config: {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        splitByCurrency: false,\r\n      },\r\n      dateOptions: [\r\n        {label: \"按年\", value: \"year\"},\r\n        {label: \"按月\", value: \"month\"},\r\n        {label: \"按周\", value: \"week\"},\r\n        {label: \"按日\", value: \"day\"},\r\n        {label: \"按时\", value: \"hour\"},\r\n        {label: \"按分\", value: \"minute\"}\r\n      ],\r\n      aggregationOptions: [\r\n        {label: \"计数\", value: \"count\"},\r\n        {label: \"求和\", value: \"sum\"},\r\n        {label: \"平均值\", value: \"avg\"},\r\n        {label: \"方差\", value: \"variance\"},\r\n        {label: \"最大值\", value: \"max\"},\r\n        {label: \"最小值\", value: \"min\"}\r\n      ],\r\n      loading: false,\r\n      configDialogVisible: false,\r\n      savedConfigs: [],\r\n      configLoading: false,\r\n      isLandscape: false,\r\n      showResult: false,\r\n      processedData: []\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用字段列表\r\n    availableFields() {\r\n      if (this.dataSource.length === 0) return []\r\n      // 只返回在 fieldLabelMap 中定义的字段\r\n      return Object.keys(this.dataSource[0]).filter(field => field in this.fieldLabelMap)\r\n    },\r\n\r\n    // 数值型字段列表\r\n    numericFields() {\r\n      // 过滤出数值类型的字段，同时确保它们在 fieldLabelMap 中存在\r\n      return this.availableFields.filter(field => {\r\n        return typeof this.dataSource[0][field] === \"number\"\r\n      })\r\n    },\r\n\r\n    // 当前字段类型\r\n    currentFieldType() {\r\n      if (!this.config.primaryField || !this.dataSource.length) return null\r\n      const sampleValue = this.dataSource[0][this.config.primaryField]\r\n      if (moment(sampleValue, moment.ISO_8601, true).isValid()) return \"date\"\r\n      return typeof sampleValue\r\n    },\r\n\r\n    // 分组字段名称\r\n    groupFieldName() {\r\n      if (this.config.primaryField && this.config.dateField) {\r\n        return `${this.getFieldLabel(this.config.dateField)}+${this.getFieldLabel(this.config.primaryField)}`\r\n      }\r\n      return this.getFieldLabel(this.config.primaryField)\r\n    },\r\n\r\n    dateFields() {\r\n      return this.availableFields.filter(field => {\r\n        // 首先检查 fieldLabelMap 中的 display 属性\r\n        if (this.fieldLabelMap[field] && this.fieldLabelMap[field].display === \"date\") {\r\n          return true\r\n        }\r\n\r\n        /* // 如果没有明确标记为日期，则尝试检查值是否为日期格式\r\n        const value = this.dataSource[0]?.[field];\r\n        return value && moment(value, moment.ISO_8601, true).isValid(); */\r\n      })\r\n    }\r\n  },\r\n  methods: {\r\n    // ... existing code ...\r\n\r\n    /**\r\n     * 计算表格合计行\r\n     * @param {Object} param0 - 包含列信息和数据的对象\r\n     * @returns {Array} 合计行数据\r\n     */\r\n    getSummary({columns, data}) {\r\n      const sums = []\r\n\r\n      columns.forEach((column, index) => {\r\n        // 第一列显示\"合计\"文本\r\n        if (index === 0) {\r\n          sums[index] = \"合计\"\r\n          return\r\n        }\r\n\r\n        // 获取当前列对应的字段配置\r\n        const field = this.config.fields[index - 1]\r\n        if (!field || !field.fieldKey) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 检查字段是否配置了汇总方式\r\n        if (!field.aggregation || field.aggregation === \"none\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取字段配置\r\n        const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n        if (!fieldConfig || fieldConfig.display !== \"number\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取列数据并转换为数字\r\n        const values = data.map(item => {\r\n          const prop = this.getResultProp(field)\r\n          return Number(item[prop])\r\n        }).filter(val => !isNaN(val))\r\n\r\n        if (values.length === 0) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 根据汇总方式计算结果\r\n        let sum = 0\r\n        switch (field.aggregation) {\r\n          case \"sum\":\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n            break\r\n          case \"avg\":\r\n            sum = values.reduce((a, b) => a + b, 0) / values.length\r\n            break\r\n          case \"max\":\r\n            sum = Math.max(...values)\r\n            break\r\n          case \"min\":\r\n            sum = Math.min(...values)\r\n            break\r\n          case \"variance\":\r\n            const mean = values.reduce((a, b) => a + b, 0) / values.length\r\n            sum = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length\r\n            break\r\n          default:\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n        }\r\n\r\n        // 根据字段格式化设置格式化结果\r\n        if (field.format === \"decimal\") {\r\n          sums[index] = sum.toFixed(2)\r\n        } else if (field.format === \"percent\") {\r\n          sums[index] = (sum * 100).toFixed(2) + \"%\"\r\n        } else if (field.format === \"currency\") {\r\n          sums[index] = \"¥\" + sum.toFixed(2)\r\n        } else if (field.format === \"usd\") {\r\n          sums[index] = \"$\" + sum.toFixed(2)\r\n        } else {\r\n          sums[index] = sum\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffShortName + staff.staffFamilyEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n    /**\r\n     * 获取字段标签\r\n     * @param {string} field - 字段标识\r\n     * @returns {string} 字段标签\r\n     */\r\n    getFieldLabel(field) {\r\n      return this.fieldLabelMap[field]?.name || field\r\n    },\r\n\r\n    /**\r\n     * 分组数据\r\n     * @returns {Object} 分组后的数据\r\n     */\r\n    groupData() {\r\n      const groups = {}\r\n\r\n      this.dataSource.forEach(item => {\r\n// 如果设置了分组日期，但记录中该字段为空，则跳过该记录\r\n        if (this.config.dateField && !item[this.config.dateField]) {\r\n          return\r\n        }\r\n\r\n        // 初始化分组键为主分组字段的值\r\n        let primaryKeyValue = item[this.config.primaryField]\r\n\r\n        // 处理文本匹配\r\n        if (typeof primaryKeyValue === \"string\") {\r\n          if (!this.config.matchOptions.caseSensitive) {\r\n            primaryKeyValue = primaryKeyValue.toLowerCase()\r\n          }\r\n          if (this.config.matchOptions.exact) {\r\n            primaryKeyValue = primaryKeyValue.trim()\r\n          }\r\n        }\r\n\r\n        // 最终的分组键\r\n        let groupKey = primaryKeyValue\r\n\r\n        // 如果同时设置了日期字段，则组合两个字段作为分组键\r\n        if (this.config.dateField && item[this.config.dateField]) {\r\n          const date = moment(item[this.config.dateField])\r\n          let dateValue\r\n\r\n          // 根据日期格式化选项处理日期\r\n          if (this.config.dateOptions.formatType) {\r\n            dateValue = date.format(this.getDateFormat())\r\n          } else if (this.config.dateOptions.convertToNumber) {\r\n            dateValue = date.valueOf()\r\n          } else {\r\n            dateValue = date.format(\"YYYY-MM-DD\")\r\n          }\r\n\r\n          // 调整为日期作为主要分组键，主分组字段作为次要分组键\r\n          groupKey = {\r\n            primary: primaryKeyValue,\r\n            date: dateValue,\r\n            // 用于Map键的字符串表示，将日期放在前面\r\n            toString: function () {\r\n              return `${this.date}_${this.primary}`\r\n            }\r\n          }\r\n        }\r\n\r\n        // 创建分组或添加到现有分组\r\n        const key = groupKey.toString ? groupKey.toString() : groupKey\r\n        if (!groups[key]) {\r\n          groups[key] = {\r\n            items: [],\r\n            groupKey: groupKey\r\n          }\r\n        }\r\n        groups[key].items.push(item)\r\n      })\r\n\r\n      return groups\r\n    },\r\n\r\n    /**\r\n     * 计算汇总值\r\n     * @param {Object} groups - 分组后的数据\r\n     * @returns {Array} 汇总结果\r\n     */\r\n    calculateAggregations(groups) {\r\n      return Object.values(groups).map(group => {\r\n        // 确保 group 是正确的结构\r\n        const items = group.items || []\r\n        const groupKey = group.groupKey\r\n\r\n        const result = {groupKey}\r\n\r\n        this.config.fields.forEach(field => {\r\n          if (!field.fieldKey) return\r\n\r\n          const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n          if (!fieldConfig) return\r\n\r\n          // 获取原始值\r\n          const values = items.map(item => {\r\n            return item[field.fieldKey]\r\n          })\r\n          const prop = this.getResultProp(field)\r\n\r\n          // 对于自定义 display 方法的字段，我们需要特殊处理\r\n          const isCustomDisplay = fieldConfig.display && typeof this[fieldConfig.display] === \"function\"\r\n\r\n          if (isCustomDisplay) {\r\n            // 对于自定义方法，我们只取第一个值，不进行汇总\r\n            result[prop] = values[0]\r\n            return\r\n          }\r\n\r\n          switch (fieldConfig.display) {\r\n            case \"number\":\r\n              // 过滤并转换为数字\r\n              const numericValues = values\r\n                .filter(v => v != null)\r\n                .map(v => Number(v))\r\n                .filter(v => !isNaN(v))\r\n\r\n              if (numericValues.length === 0) {\r\n                result[prop] = null\r\n                return\r\n              }\r\n\r\n              switch (field.aggregation) {\r\n                case \"sum\":\r\n                  result[prop] = numericValues.reduce((sum, val) => {\r\n                    return Number((sum + val).toFixed(2))\r\n                  }, 0)\r\n                  break\r\n                case \"avg\":\r\n                  result[prop] = Number((numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length).toFixed(2))\r\n                  break\r\n                case \"max\":\r\n                  result[prop] = Math.max(...numericValues)\r\n                  break\r\n                case \"min\":\r\n                  result[prop] = Math.min(...numericValues)\r\n                  break\r\n                case \"variance\":\r\n                  if (numericValues.length > 0) {\r\n                    const mean = numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length\r\n                    result[prop] = Number((numericValues.reduce((sum, val) =>\r\n                      sum + Math.pow(val - mean, 2), 0) / numericValues.length).toFixed(2))\r\n                  } else {\r\n                    result[prop] = 0\r\n                  }\r\n                  break\r\n                case \"none\":\r\n                default:\r\n                  result[prop] = values[0]\r\n              }\r\n              break\r\n            case \"date\":\r\n            case \"text\":\r\n            case \"boolean\":\r\n            default:\r\n              result[prop] = values[0]\r\n          }\r\n        })\r\n\r\n        return result\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 处理分类汇总按钮点击事件\r\n     * 验证配置并执行数据汇总\r\n     */\r\n    handleAggregate() {\r\n      if (!this.config.primaryField) {\r\n        this.$message.warning(\"请选择分组依据字段\")\r\n        return\r\n      }\r\n\r\n      if (!this.config.fields.length) {\r\n        this.$message.warning(\"请添加要汇总的字段\")\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loading = true\r\n        // 处理数据\r\n        const groups = this.groupData()\r\n        this.processedData = this.calculateAggregations(groups)\r\n\r\n        // 应用排序\r\n        this.applySorting()\r\n\r\n        this.showResult = true\r\n      } catch (error) {\r\n        this.$message.error(\"汇总处理失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 应用排序规则到处理后的数据\r\n     */\r\n    applySorting() {\r\n      // 查找第一个设置了排序的字段\r\n      const sortField = this.config.fields.find(field => field.sort !== \"none\")\r\n\r\n      if (!sortField) return // 如果没有设置排序，直接返回\r\n\r\n      const prop = this.getResultProp(sortField)\r\n      const isAsc = sortField.sort === \"asc\"\r\n\r\n      // 根据字段类型和排序方向进行排序\r\n      const fieldConfig = this.fieldLabelMap[sortField.fieldKey]\r\n      if (!fieldConfig) return\r\n\r\n      this.processedData.sort((a, b) => {\r\n        let valueA = a[prop]\r\n        let valueB = b[prop]\r\n\r\n        // 根据字段类型进行比较\r\n        switch (fieldConfig.display) {\r\n          case \"number\":\r\n            valueA = Number(valueA) || 0\r\n            valueB = Number(valueB) || 0\r\n            break\r\n          case \"date\":\r\n            valueA = valueA ? new Date(valueA).getTime() : 0\r\n            valueB = valueB ? new Date(valueB).getTime() : 0\r\n            break\r\n          case \"boolean\":\r\n            valueA = valueA ? 1 : 0\r\n            valueB = valueB ? 1 : 0\r\n            break\r\n          default:\r\n            valueA = String(valueA || \"\")\r\n            valueB = String(valueB || \"\")\r\n        }\r\n\r\n        // 根据排序方向返回比较结果\r\n        if (isAsc) {\r\n          return valueA > valueB ? 1 : valueA < valueB ? -1 : 0\r\n        } else {\r\n          return valueA < valueB ? 1 : valueA > valueB ? -1 : 0\r\n        }\r\n      })\r\n    },\r\n\r\n    handleFieldChange() {\r\n      // 字段变化时重置相关配置\r\n      this.config.selectedFields = []\r\n      this.config.displayFields = []\r\n    },\r\n\r\n    async saveConfig() {\r\n      try {\r\n        // 验证配置名称\r\n        if (!this.config.name) {\r\n          this.$message.warning(\"请输入速查名称\")\r\n          return\r\n        }\r\n\r\n        // 验证必要的配置项\r\n        if (!this.config.primaryField) {\r\n          this.$message.warning(\"请选择分组依据字段\")\r\n          return\r\n        }\r\n\r\n        if (!this.config.fields.length) {\r\n          this.$message.warning(\"请添加至少一个字段\")\r\n          return\r\n        }\r\n\r\n        // 验证字段配置是否完整\r\n        const incompleteField = this.config.fields.find(field => !field.fieldKey)\r\n        if (incompleteField) {\r\n          this.$message.warning(\"请完成所有字段的配置\")\r\n          return\r\n        }\r\n\r\n        // 构造符合 AggregatorConfigDTO 的数据结构\r\n        const configToSave = {\r\n          name: this.config.name,\r\n          type: 'Aggregator',\r\n          config: {\r\n            primaryField: this.config.primaryField,\r\n            matchOptions: this.config.matchOptions,\r\n            dateField: this.config.dateField,\r\n            dateOptions: this.config.dateOptions,\r\n            showDetails: this.config.showDetails,\r\n            fields: this.config.fields\r\n          }\r\n        }\r\n\r\n        // 发送请求\r\n        await saveAggregatorConfig(configToSave)\r\n\r\n        this.$message.success(\"配置保存成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"保存配置失败：\" + (err.message || \"未知错误\"))\r\n        }\r\n      }\r\n    },\r\n\r\n    async loadConfigs() {\r\n      this.configLoading = true\r\n      this.configDialogVisible = true\r\n      try {\r\n        let result = await loadAggregatorConfigs({configType: 'Aggregator'})\r\n        const configs = result.rows\r\n\r\n        // 验证返回的数据格式\r\n        if (!Array.isArray(configs)) {\r\n          throw new Error(\"返回数据格式错误\")\r\n        }\r\n\r\n        // 保留原始配置数据，只在必要时提供默认值\r\n        this.savedConfigs = configs.map(config => ({\r\n          id: config.id,\r\n          name: config.name,\r\n          createTime: config.createTime,\r\n          config: config.config || {\r\n            primaryField: \"\",\r\n            secondaryField: \"\",\r\n            textMatchMode: \"exact\",\r\n            caseSensitive: false,\r\n            dateGranularity: \"day\",\r\n            aggregationMethods: [\"count\", \"sum\"],\r\n            showDetails: false,\r\n            selectedFields: [],\r\n            displayFields: []\r\n          }\r\n        }))\r\n\r\n        this.configDialogVisible = true\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\r\n          err.response?.data?.message ||\r\n          err.message ||\r\n          \"加载配置列表失败，请稍后重试\"\r\n        )\r\n      } finally {\r\n        this.configLoading = false\r\n      }\r\n    },\r\n    async handleConfigSelect(row) {\r\n      try {\r\n        // 确保配置对象包含所有必要的字段\r\n        const defaultConfig = {\r\n          primaryField: \"\",\r\n          secondaryField: \"\",\r\n          textMatchMode: \"exact\",\r\n          caseSensitive: false,\r\n          dateGranularity: \"day\",\r\n          aggregationMethods: [\"count\", \"sum\"],\r\n          showDetails: false,\r\n          selectedFields: [],\r\n          displayFields: []\r\n        }\r\n\r\n        // 深拷贝配置对象，避免引用问题\r\n        this.config = {\r\n          ...defaultConfig,\r\n          ...JSON.parse(row.config),\r\n          name: row.name\r\n        }\r\n\r\n        this.configDialogVisible = false\r\n        this.$message.success(\"配置加载成功\")\r\n\r\n        // 触发表单重新渲染\r\n        this.$nextTick(() => {\r\n          // 如果需要，可以在这里添加额外的处理逻辑\r\n          console.log(\"配置已加载:\", this.config)\r\n        })\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\"加载配置失败：\" + err.message)\r\n      }\r\n    },\r\n    async deleteConfig(row) {\r\n      try {\r\n        await this.$confirm(\"确认删除该配置？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n\r\n        await deleteAggregatorConfig(row.id)\r\n        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)\r\n        this.$message.success(\"配置删除成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"删除配置失败：\" + err.message)\r\n        }\r\n      }\r\n    },\r\n\r\n    printTable() {\r\n      const printWindow = window.open(\"\", \"_blank\")\r\n      const table = this.$refs.resultTable.$el.cloneNode(true)\r\n      const title = \"\"\r\n      const date = new Date().toLocaleDateString()\r\n\r\n      // 公司标志和标题的HTML模板\r\n      const headerTemplate = `\r\n        <div class=\"company-header\">\r\n          <div class=\"company-logo\">\r\n            <img src=\"/logo.png\" alt=\"Rich Shipping Logo\" />\r\n            <div class=\"company-name\">\r\n              <div class=\"company-name-cn\">广州瑞旗国际货运代理有限公司</div>\r\n              <div class=\"company-name-en\">GUANGZHOU RICH SHIPPING INT'L CO.,LTD.</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"document-title\">\r\n            <div class=\"title-cn\">对账单汇总</div>\r\n            <div class=\"title-en\">[DEBIT NOTE]</div>\r\n          </div>\r\n        </div>\r\n      `\r\n\r\n      printWindow.document.write(`\r\n        <html lang=\"\">\r\n          <head>\r\n            <title>${title}</title>\r\n            <style>\r\n          /* 基础样式 */\r\n          body {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: Arial, sans-serif;\r\n          }\r\n\r\n          /* 打印样式 - 必须放在这里才能生效 */\r\n          @media print {\r\n            @page {\r\n              size: ${this.isLandscape ? \"landscape\" : \"portrait\"};\r\n              margin: 1.5cm 1cm 1cm 1cm;\r\n            }\r\n\r\n            /* 重要：使用重复表头技术 */\r\n            thead {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 页眉作为表格的一部分，放在thead中 */\r\n            .page-header {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 内容部分 */\r\n            .page-content {\r\n              display: table-row-group;\r\n            }\r\n\r\n            /* 页脚 */\r\n            tfoot {\r\n              display: table-footer-group;\r\n            }\r\n\r\n            /* 避免元素内部分页 */\r\n            .company-header, .header-content {\r\n              page-break-inside: avoid;\r\n            }\r\n\r\n            /* 表格样式 */\r\n            table.main-table {\r\n              width: 100%;\r\n              border-collapse: collapse;\r\n              border: none;\r\n            }\r\n\r\n            /* 确保表头在每页都显示 */\r\n            table.data-table thead {\r\n              display: table-header-group;\r\n            }\r\n\r\n            /* 避免行内分页 */\r\n            table.data-table tr {\r\n              page-break-inside: avoid;\r\n            }\r\n          }\r\n\r\n          /* 表格样式 */\r\n          table.data-table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n            margin-top: 20px;\r\n          }\r\n\r\n          table.data-table th, table.data-table td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n            text-align: left;\r\n            font-size: 12px;\r\n          }\r\n\r\n          table.data-table th {\r\n            background-color: #f2f2f2;\r\n          }\r\n\r\n          /* Element UI 表格样式模拟 */\r\n          .el-table {\r\n            border-collapse: collapse;\r\n            width: 100%;\r\n          }\r\n\r\n          .el-table th, .el-table td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n            text-align: left;\r\n            font-size: 12px;\r\n          }\r\n\r\n          .el-table th {\r\n            background-color: #f2f2f2;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .el-table__footer {\r\n            background-color: #f8f8f9;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .el-table__footer td {\r\n            border: 1px solid #ddd;\r\n            padding: 8px;\r\n          }\r\n\r\n          /* 公司标题和标志样式 */\r\n          .company-header {\r\n            display: flex;\r\n            justify-content: space-between;\r\n            align-items: center;\r\n            border-bottom: 2px solid #000;\r\n            padding-bottom: 10px;\r\n            width: 100%;\r\n          }\r\n\r\n          .company-logo {\r\n            display: flex;\r\n            align-items: center;\r\n          }\r\n\r\n          .company-logo img {\r\n            height: 50px;\r\n            margin-right: 10px;\r\n          }\r\n\r\n          .company-name {\r\n            display: flex;\r\n            flex-direction: column;\r\n          }\r\n\r\n          .company-name-cn {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n            color: #ff0000;\r\n          }\r\n\r\n          .company-name-en {\r\n            font-size: 14px;\r\n          }\r\n\r\n          .document-title {\r\n            text-align: right;\r\n          }\r\n\r\n          .title-cn {\r\n            font-size: 18px;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .title-en {\r\n            font-size: 16px;\r\n            font-weight: bold;\r\n          }\r\n\r\n          /* 清除表格边框 */\r\n          table.main-table, table.main-table td {\r\n            border: none;\r\n          }\r\n\r\n          /* 页眉容器 */\r\n          .header-container {\r\n            width: 100%;\r\n            margin-bottom: 20px;\r\n          }\r\n\r\n          /* 日期信息 */\r\n          .date-info {\r\n            text-align: right;\r\n            margin-top: 10px;\r\n            margin-bottom: 20px;\r\n          }\r\n        </style>\r\n          </head>\r\n          <body>\r\n            <!-- 使用表格布局确保页眉在每页重复 -->\r\n            <table class=\"main-table\">\r\n              <thead class=\"page-header\">\r\n                <tr>\r\n                  <td>\r\n                    <div class=\"header-container\">\r\n                      ${headerTemplate}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </thead>\r\n              <tbody class=\"page-content\">\r\n                <tr>\r\n                  <td>\r\n                    <!-- 保留原始表格的类名并添加data-table类 -->\r\n                    ${table.outerHTML.replace('<table', '<table class=\"el-table data-table\"')}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n              <tfoot>\r\n                <tr>\r\n                  <td></td>\r\n                </tr>\r\n              </tfoot>\r\n            </table>\r\n          </body>\r\n        </html>\r\n      `)\r\n\r\n      printWindow.document.close()\r\n\r\n      setTimeout(() => {\r\n        try {\r\n          printWindow.focus();\r\n          printWindow.print();\r\n        } catch (e) {\r\n          console.error(\"打印过程中发生错误:\", e);\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    async exportToPDF() {\r\n      try {\r\n        this.loading = true\r\n        const element = this.$refs.resultTable.$el\r\n        const opt = {\r\n          margin: [0.8, 0.8, 0.8, 0.8], // 上右下左边距（英寸）\r\n          filename: \"汇总数据.pdf\",\r\n          image: {type: \"jpeg\", quality: 0.98},\r\n          html2canvas: {scale: 2},\r\n          jsPDF: {\r\n            unit: \"in\",\r\n            format: \"a3\",\r\n            orientation: this.isLandscape ? \"landscape\" : \"portrait\"\r\n          },\r\n          pagebreak: {mode: [\"avoid-all\", \"css\", \"legacy\"]}, // 添加分页控制\r\n          header: [\r\n            {text: \"汇总数据\", style: \"headerStyle\"},\r\n            {text: new Date().toLocaleDateString(), style: \"headerStyle\", alignment: \"right\"}\r\n          ],\r\n          footer: {\r\n            height: \"20px\",\r\n            contents: {\r\n              default: \"<span style=\\\"float:right\\\">{{page}}/{{pages}}</span>\" // 添加页码\r\n            }\r\n          }\r\n        }\r\n\r\n        await html2pdf().set(opt).from(element).save()\r\n        this.$message.success(\"PDF导出成功\")\r\n      } catch (error) {\r\n        this.$message.error(\"PDF导出失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 添加新的字段配置行\r\n     * 初始化一个新的字段配置对象，包含默认值\r\n     */\r\n    addField() {\r\n      this.config.fields.push({\r\n        fieldKey: \"\",        // 字段标识\r\n        aggregation: \"none\", // 汇总方式\r\n        format: \"none\",      // 显示格式\r\n        sort: \"none\"         // 排序方式\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 删除指定索引的字段配置行\r\n     * @param {number} index - 要删除的字段索引\r\n     */\r\n    removeField(index) {\r\n      this.config.fields.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 移动字段配置行的位置\r\n     * @param {number} index - 当前字段的索引\r\n     * @param {string} direction - 移动方向，'up' 或 'down'\r\n     */\r\n    moveField(index, direction) {\r\n      const fields = [...this.config.fields] // 创建数组副本\r\n\r\n      if (direction === \"up\" && index > 0) {\r\n        // 向上移动，与上一个元素交换位置\r\n        [fields[index], fields[index - 1]] = [fields[index - 1], fields[index]]\r\n      } else if (direction === \"down\" && index < fields.length - 1) {\r\n        // 向下移动，与下一个元素交换位置\r\n        [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]\r\n      }\r\n\r\n      // 使用整个新数组替换，确保响应式更新\r\n      this.$set(this.config, \"fields\", fields)\r\n    },\r\n\r\n    /**\r\n     * 处理字段选择变更事件\r\n     * 根据选择的字段自动设置相关配置\r\n     * @param {number} index - 变更的字段索引\r\n     */\r\n    handleFieldSelect(index) {\r\n      const field = this.config.fields[index]\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (fieldConfig) {\r\n        // 根据字段配置设置默认值\r\n        field.format = this.getDefaultFormat(fieldConfig.display)\r\n        field.aggregation = fieldConfig.aggregated ? \"sum\" : \"none\"\r\n        field.sort = \"none\" // 默认不排序\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取字段的显示类型\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {string} 字段显示类型（text/number/date/boolean/custom）\r\n     */\r\n    getFieldDisplay(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      if (!fieldConfig) return \"text\"\r\n\r\n      // 检查是否是自定义方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        return \"custom\"\r\n      }\r\n\r\n      return fieldConfig.display || \"text\"\r\n    },\r\n\r\n    /**\r\n     * 判断字段是否可以进行汇总计算\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {boolean} 是否可汇总\r\n     */\r\n    isAggregatable(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig?.aggregated || false\r\n    },\r\n\r\n    /**\r\n     * 根据显示类型获取默认的格式化方式\r\n     * @param {string} displayType - 显示类型\r\n     * @returns {string} 默认格式\r\n     */\r\n    getDefaultFormat(displayType) {\r\n      switch (displayType) {\r\n        case \"date\":\r\n          return \"YYYY-MM-DD\"\r\n        case \"number\":\r\n          return \"decimal\"\r\n        default:\r\n          return \"none\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 重置配置到初始状态\r\n     */\r\n    resetConfig() {\r\n      this.config = {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: []\r\n      }\r\n      this.showResult = false\r\n    },\r\n\r\n    /**\r\n     * 获取结果数据的属性名\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 属性名\r\n     */\r\n    getResultProp(field) {\r\n      // 如果有汇总方式，属性名为 fieldKey_aggregation\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        return `${field.fieldKey}_${field.aggregation}`\r\n      }\r\n      return field.fieldKey\r\n    },\r\n\r\n    /**\r\n     * 获取结果表格的列标题\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 列标题\r\n     */\r\n    getResultLabel(field) {\r\n      const baseLabel = this.getFieldLabel(field.fieldKey)\r\n\r\n      // 如果有汇总方式，在标签中添加汇总方式信息\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        // 获取汇总方式的中文名称\r\n        const aggregationLabel = this.aggregationOptions.find(opt => opt.value === field.aggregation)?.label || field.aggregation\r\n        return `${baseLabel}(${aggregationLabel})`\r\n      }\r\n\r\n      return baseLabel\r\n    },\r\n\r\n    /**\r\n     * 格式化单元格的值\r\n     * @param {*} value - 原始值\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 格式化后的值\r\n     */\r\n    formatCellValue(value, field) {\r\n      if (value == null) return \"-\"\r\n\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (!fieldConfig) return value\r\n\r\n      // 处理自定义 display 方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        // 调用组件中定义的方法\r\n        return this[fieldConfig.display](value)\r\n      }\r\n\r\n      // 根据字段类型进行格式化\r\n      switch (fieldConfig.display) {\r\n        case \"number\":\r\n          const numValue = Number(value)\r\n          if (isNaN(numValue)) return \"-\"\r\n\r\n          // 如果是汇总字段，先应用汇总格式化\r\n          if (field.aggregation && field.aggregation !== \"none\") {\r\n            // 对于平均值和方差，保留更多小数位\r\n            if (field.aggregation === \"avg\" || field.aggregation === \"variance\") {\r\n              if (field.format === \"percent\") {\r\n                return (numValue * 100).toFixed(2) + \"%\"\r\n              }\r\n              return numValue.toFixed(2)\r\n            }\r\n          }\r\n\r\n          switch (field.format) {\r\n            case \"decimal\":\r\n              return numValue.toFixed(2)\r\n            case \"percent\":\r\n              return (numValue * 100).toFixed(2) + \"%\"\r\n            case \"currency\":\r\n              return \"¥\" + numValue.toFixed(2)\r\n            case \"usd\":\r\n              return \"$\" + numValue.toFixed(2)\r\n            default:\r\n              return numValue.toFixed(2)\r\n          }\r\n\r\n        case \"date\":\r\n          return moment(value).format(field.format || \"YYYY-MM-DD\")\r\n\r\n        case \"boolean\":\r\n          if (field.aggregation === \"avg\") {\r\n            return (Number(value) * 100).toFixed(2) + \"%\"\r\n          }\r\n          return value ? \"是\" : \"否\"\r\n\r\n        default:\r\n          return value\r\n      }\r\n    },\r\n\r\n    getDateFormat() {\r\n      switch (this.config.dateOptions.formatType) {\r\n        case \"year\":\r\n          return \"YYYY\"\r\n        case \"month\":\r\n          return \"YYYY-MM\"\r\n        case \"day\":\r\n        default:\r\n          return \"YYYY-MM-DD\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 格式化分组键\r\n     * @param {Object|string} groupKey - 分组键\r\n     * @returns {string} 格式化后的分组键\r\n     */\r\n    formatGroupKey(groupKey) {\r\n      if (typeof groupKey === \"object\" && groupKey !== null) {\r\n        if (groupKey.primary !== undefined && groupKey.date !== undefined) {\r\n          // 获取主分组字段的配置\r\n          const primaryFieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n          let primaryValue = groupKey.primary\r\n\r\n          // 如果主分组字段有自定义 display 方法，应用它\r\n          if (primaryFieldConfig && primaryFieldConfig.display &&\r\n            typeof this[primaryFieldConfig.display] === \"function\") {\r\n            primaryValue = this[primaryFieldConfig.display](primaryValue)\r\n          }\r\n\r\n          // 日期值在前，主值在后，不添加分隔符\r\n          return `${groupKey.date}${primaryValue}`\r\n        }\r\n      }\r\n\r\n      // 如果是简单值，检查是否需要应用自定义 display 方法\r\n      if (this.config.primaryField) {\r\n        const fieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n        if (fieldConfig && fieldConfig.display &&\r\n          typeof this[fieldConfig.display] === \"function\") {\r\n          return this[fieldConfig.display](groupKey)\r\n        }\r\n      }\r\n\r\n      return String(groupKey || \"\")\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.data-aggregator {\r\n  padding: 20px;\r\n}\r\n\r\n.config-card, .result-card {\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n.result-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-with-operations {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.operations {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.field-config-table {\r\n  border: 1px solid #EBEEF5;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.table-header,\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px;\r\n  border-bottom: 1px solid #EBEEF5;\r\n}\r\n\r\n.table-header {\r\n  background-color: #F5F7FA;\r\n  font-weight: bold;\r\n}\r\n\r\n.col {\r\n  flex: 1;\r\n  padding: 0 5px;\r\n  min-width: 120px;\r\n}\r\n\r\n.col:first-child {\r\n  flex: 0 0 60px;\r\n  min-width: 60px;\r\n}\r\n\r\n.col-operation {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2RA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAI,IAAA;EACAC,KAAA;IACAC,UAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,QAAA;IACA;IACAC,aAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,QAAA;MACAG,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,UAAA;MACAC,MAAA;QACAZ,IAAA;QACAa,YAAA;QACAC,YAAA;UACAC,KAAA;UACAC,aAAA;QACA;QACAC,SAAA;QACAC,WAAA;UACAC,eAAA;UACAC,UAAA;QACA;QACAC,WAAA;QACAC,MAAA;QACAC,eAAA;MACA;MACAL,WAAA,GACA;QAAAM,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,kBAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAE,OAAA;MACAC,mBAAA;MACAC,YAAA;MACAC,aAAA;MACAC,WAAA;MACAC,UAAA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,KAAA;MACA,SAAAlC,UAAA,CAAAmC,MAAA;MACA;MACA,OAAA9B,MAAA,CAAA+B,IAAA,MAAApC,UAAA,KAAAqC,MAAA,WAAAC,KAAA;QAAA,OAAAA,KAAA,IAAAJ,KAAA,CAAA9B,aAAA;MAAA;IACA;IAEA;IACAmC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA;MACA,YAAAP,eAAA,CAAAI,MAAA,WAAAC,KAAA;QACA,cAAAE,MAAA,CAAAxC,UAAA,IAAAsC,KAAA;MACA;IACA;IAEA;IACAG,gBAAA,WAAAA,iBAAA;MACA,UAAA/B,MAAA,CAAAC,YAAA,UAAAX,UAAA,CAAAmC,MAAA;MACA,IAAAO,WAAA,QAAA1C,UAAA,SAAAU,MAAA,CAAAC,YAAA;MACA,QAAAgC,eAAA,EAAAD,WAAA,EAAAC,eAAA,CAAAC,QAAA,QAAAC,OAAA;MACA,WAAAC,QAAA,CAAAxC,OAAA,EAAAoC,WAAA;IACA;IAEA;IACAK,cAAA,WAAAA,eAAA;MACA,SAAArC,MAAA,CAAAC,YAAA,SAAAD,MAAA,CAAAK,SAAA;QACA,UAAAiC,MAAA,MAAAC,aAAA,MAAAvC,MAAA,CAAAK,SAAA,QAAAiC,MAAA,MAAAC,aAAA,MAAAvC,MAAA,CAAAC,YAAA;MACA;MACA,YAAAsC,aAAA,MAAAvC,MAAA,CAAAC,YAAA;IACA;IAEAuC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,YAAAlB,eAAA,CAAAI,MAAA,WAAAC,KAAA;QACA;QACA,IAAAa,MAAA,CAAA/C,aAAA,CAAAkC,KAAA,KAAAa,MAAA,CAAA/C,aAAA,CAAAkC,KAAA,EAAAc,OAAA;UACA;QACA;;QAEA;AACA;AACA;MACA;IACA;EACA;;EACAC,OAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC,UAAA,WAAAA,WAAAC,IAAA;MAAA,IAAAC,MAAA;MAAA,IAAAC,OAAA,GAAAF,IAAA,CAAAE,OAAA;QAAAjD,IAAA,GAAA+C,IAAA,CAAA/C,IAAA;MACA,IAAAkD,IAAA;MAEAD,OAAA,CAAAE,OAAA,WAAAC,MAAA,EAAAC,KAAA;QACA;QACA,IAAAA,KAAA;UACAH,IAAA,CAAAG,KAAA;UACA;QACA;;QAEA;QACA,IAAAvB,KAAA,GAAAkB,MAAA,CAAA9C,MAAA,CAAAU,MAAA,CAAAyC,KAAA;QACA,KAAAvB,KAAA,KAAAA,KAAA,CAAAwB,QAAA;UACAJ,IAAA,CAAAG,KAAA;UACA;QACA;;QAEA;QACA,KAAAvB,KAAA,CAAAyB,WAAA,IAAAzB,KAAA,CAAAyB,WAAA;UACAL,IAAA,CAAAG,KAAA;UACA;QACA;;QAEA;QACA,IAAAG,WAAA,GAAAR,MAAA,CAAApD,aAAA,CAAAkC,KAAA,CAAAwB,QAAA;QACA,KAAAE,WAAA,IAAAA,WAAA,CAAAZ,OAAA;UACAM,IAAA,CAAAG,KAAA;UACA;QACA;;QAEA;QACA,IAAAI,MAAA,GAAAzD,IAAA,CAAA0D,GAAA,WAAAC,IAAA;UACA,IAAAC,IAAA,GAAAZ,MAAA,CAAAa,aAAA,CAAA/B,KAAA;UACA,OAAAgC,MAAA,CAAAH,IAAA,CAAAC,IAAA;QACA,GAAA/B,MAAA,WAAAkC,GAAA;UAAA,QAAAC,KAAA,CAAAD,GAAA;QAAA;QAEA,IAAAN,MAAA,CAAA9B,MAAA;UACAuB,IAAA,CAAAG,KAAA;UACA;QACA;;QAEA;QACA,IAAAY,GAAA;QACA,QAAAnC,KAAA,CAAAyB,WAAA;UACA;YACAU,GAAA,GAAAR,MAAA,CAAAS,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA;YACA;UACA;YACAH,GAAA,GAAAR,MAAA,CAAAS,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA,QAAAX,MAAA,CAAA9B,MAAA;YACA;UACA;YACAsC,GAAA,GAAAI,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAA1E,OAAA,EAAA2D,MAAA;YACA;UACA;YACAQ,GAAA,GAAAI,IAAA,CAAAI,GAAA,CAAAF,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAA1E,OAAA,EAAA2D,MAAA;YACA;UACA;YACA,IAAAiB,IAAA,GAAAjB,MAAA,CAAAS,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA,QAAAX,MAAA,CAAA9B,MAAA;YACAsC,GAAA,GAAAR,MAAA,CAAAS,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAE,IAAA,CAAAM,GAAA,CAAAP,CAAA,GAAAM,IAAA;YAAA,QAAAjB,MAAA,CAAA9B,MAAA;YACA;UACA;YACAsC,GAAA,GAAAR,MAAA,CAAAS,MAAA,WAAAC,CAAA,EAAAC,CAAA;cAAA,OAAAD,CAAA,GAAAC,CAAA;YAAA;QACA;;QAEA;QACA,IAAAtC,KAAA,CAAA8C,MAAA;UACA1B,IAAA,CAAAG,KAAA,IAAAY,GAAA,CAAAY,OAAA;QACA,WAAA/C,KAAA,CAAA8C,MAAA;UACA1B,IAAA,CAAAG,KAAA,KAAAY,GAAA,QAAAY,OAAA;QACA,WAAA/C,KAAA,CAAA8C,MAAA;UACA1B,IAAA,CAAAG,KAAA,UAAAY,GAAA,CAAAY,OAAA;QACA,WAAA/C,KAAA,CAAA8C,MAAA;UACA1B,IAAA,CAAAG,KAAA,UAAAY,GAAA,CAAAY,OAAA;QACA;UACA3B,IAAA,CAAAG,KAAA,IAAAY,GAAA;QACA;MACA;MAEA,OAAAf,IAAA;IACA;IACA4B,OAAA,WAAAA,QAAAC,EAAA;MACA,IAAAA,EAAA;QACA,IAAAC,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAlF,IAAA,CAAAmF,cAAA,CAAAtD,MAAA,WAAAuD,OAAA;UAAA,OAAAA,OAAA,CAAAC,OAAA,IAAAN,EAAA;QAAA;QACA,IAAAC,KAAA,IAAAA,KAAA,KAAAM,SAAA;UACA,OAAAN,KAAA,CAAAO,cAAA,GAAAP,KAAA,CAAAQ,iBAAA;QACA;MACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACA/C,aAAA,WAAAA,cAAAX,KAAA;MAAA,IAAA2D,qBAAA;MACA,SAAAA,qBAAA,QAAA7F,aAAA,CAAAkC,KAAA,eAAA2D,qBAAA,uBAAAA,qBAAA,CAAAnG,IAAA,KAAAwC,KAAA;IACA;IAEA;AACA;AACA;AACA;IACA4D,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,MAAA;MAEA,KAAApG,UAAA,CAAA2D,OAAA,WAAAQ,IAAA;QACA;QACA,IAAAgC,MAAA,CAAAzF,MAAA,CAAAK,SAAA,KAAAoD,IAAA,CAAAgC,MAAA,CAAAzF,MAAA,CAAAK,SAAA;UACA;QACA;;QAEA;QACA,IAAAsF,eAAA,GAAAlC,IAAA,CAAAgC,MAAA,CAAAzF,MAAA,CAAAC,YAAA;;QAEA;QACA,WAAA0F,eAAA;UACA,KAAAF,MAAA,CAAAzF,MAAA,CAAAE,YAAA,CAAAE,aAAA;YACAuF,eAAA,GAAAA,eAAA,CAAAC,WAAA;UACA;UACA,IAAAH,MAAA,CAAAzF,MAAA,CAAAE,YAAA,CAAAC,KAAA;YACAwF,eAAA,GAAAA,eAAA,CAAAE,IAAA;UACA;QACA;;QAEA;QACA,IAAAC,QAAA,GAAAH,eAAA;;QAEA;QACA,IAAAF,MAAA,CAAAzF,MAAA,CAAAK,SAAA,IAAAoD,IAAA,CAAAgC,MAAA,CAAAzF,MAAA,CAAAK,SAAA;UACA,IAAA0F,IAAA,OAAA9D,eAAA,EAAAwB,IAAA,CAAAgC,MAAA,CAAAzF,MAAA,CAAAK,SAAA;UACA,IAAA2F,SAAA;;UAEA;UACA,IAAAP,MAAA,CAAAzF,MAAA,CAAAM,WAAA,CAAAE,UAAA;YACAwF,SAAA,GAAAD,IAAA,CAAArB,MAAA,CAAAe,MAAA,CAAAQ,aAAA;UACA,WAAAR,MAAA,CAAAzF,MAAA,CAAAM,WAAA,CAAAC,eAAA;YACAyF,SAAA,GAAAD,IAAA,CAAAG,OAAA;UACA;YACAF,SAAA,GAAAD,IAAA,CAAArB,MAAA;UACA;;UAEA;UACAoB,QAAA;YACAK,OAAA,EAAAR,eAAA;YACAI,IAAA,EAAAC,SAAA;YACA;YACAI,QAAA,WAAAA,SAAA;cACA,UAAA9D,MAAA,MAAAyD,IAAA,OAAAzD,MAAA,MAAA6D,OAAA;YACA;UACA;QACA;;QAEA;QACA,IAAAE,GAAA,GAAAP,QAAA,CAAAM,QAAA,GAAAN,QAAA,CAAAM,QAAA,KAAAN,QAAA;QACA,KAAAJ,MAAA,CAAAW,GAAA;UACAX,MAAA,CAAAW,GAAA;YACAC,KAAA;YACAR,QAAA,EAAAA;UACA;QACA;QACAJ,MAAA,CAAAW,GAAA,EAAAC,KAAA,CAAAC,IAAA,CAAA9C,IAAA;MACA;MAEA,OAAAiC,MAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAc,qBAAA,WAAAA,sBAAAd,MAAA;MAAA,IAAAe,MAAA;MACA,OAAA9G,MAAA,CAAA4D,MAAA,CAAAmC,MAAA,EAAAlC,GAAA,WAAAkD,KAAA;QACA;QACA,IAAAJ,KAAA,GAAAI,KAAA,CAAAJ,KAAA;QACA,IAAAR,QAAA,GAAAY,KAAA,CAAAZ,QAAA;QAEA,IAAAa,MAAA;UAAAb,QAAA,EAAAA;QAAA;QAEAW,MAAA,CAAAzG,MAAA,CAAAU,MAAA,CAAAuC,OAAA,WAAArB,KAAA;UACA,KAAAA,KAAA,CAAAwB,QAAA;UAEA,IAAAE,WAAA,GAAAmD,MAAA,CAAA/G,aAAA,CAAAkC,KAAA,CAAAwB,QAAA;UACA,KAAAE,WAAA;;UAEA;UACA,IAAAC,MAAA,GAAA+C,KAAA,CAAA9C,GAAA,WAAAC,IAAA;YACA,OAAAA,IAAA,CAAA7B,KAAA,CAAAwB,QAAA;UACA;UACA,IAAAM,IAAA,GAAA+C,MAAA,CAAA9C,aAAA,CAAA/B,KAAA;;UAEA;UACA,IAAAgF,eAAA,GAAAtD,WAAA,CAAAZ,OAAA,WAAA+D,MAAA,CAAAnD,WAAA,CAAAZ,OAAA;UAEA,IAAAkE,eAAA;YACA;YACAD,MAAA,CAAAjD,IAAA,IAAAH,MAAA;YACA;UACA;UAEA,QAAAD,WAAA,CAAAZ,OAAA;YACA;cACA;cACA,IAAAmE,aAAA,GAAAtD,MAAA,CACA5B,MAAA,WAAAmF,CAAA;gBAAA,OAAAA,CAAA;cAAA,GACAtD,GAAA,WAAAsD,CAAA;gBAAA,OAAAlD,MAAA,CAAAkD,CAAA;cAAA,GACAnF,MAAA,WAAAmF,CAAA;gBAAA,QAAAhD,KAAA,CAAAgD,CAAA;cAAA;cAEA,IAAAD,aAAA,CAAApF,MAAA;gBACAkF,MAAA,CAAAjD,IAAA;gBACA;cACA;cAEA,QAAA9B,KAAA,CAAAyB,WAAA;gBACA;kBACAsD,MAAA,CAAAjD,IAAA,IAAAmD,aAAA,CAAA7C,MAAA,WAAAD,GAAA,EAAAF,GAAA;oBACA,OAAAD,MAAA,EAAAG,GAAA,GAAAF,GAAA,EAAAc,OAAA;kBACA;kBACA;gBACA;kBACAgC,MAAA,CAAAjD,IAAA,IAAAE,MAAA,EAAAiD,aAAA,CAAA7C,MAAA,WAAAD,GAAA,EAAAF,GAAA;oBAAA,OAAAE,GAAA,GAAAF,GAAA;kBAAA,QAAAgD,aAAA,CAAApF,MAAA,EAAAkD,OAAA;kBACA;gBACA;kBACAgC,MAAA,CAAAjD,IAAA,IAAAS,IAAA,CAAAC,GAAA,CAAAC,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAA1E,OAAA,EAAAiH,aAAA;kBACA;gBACA;kBACAF,MAAA,CAAAjD,IAAA,IAAAS,IAAA,CAAAI,GAAA,CAAAF,KAAA,CAAAF,IAAA,MAAAG,mBAAA,CAAA1E,OAAA,EAAAiH,aAAA;kBACA;gBACA;kBACA,IAAAA,aAAA,CAAApF,MAAA;oBACA,IAAA+C,IAAA,GAAAqC,aAAA,CAAA7C,MAAA,WAAAD,GAAA,EAAAF,GAAA;sBAAA,OAAAE,GAAA,GAAAF,GAAA;oBAAA,QAAAgD,aAAA,CAAApF,MAAA;oBACAkF,MAAA,CAAAjD,IAAA,IAAAE,MAAA,EAAAiD,aAAA,CAAA7C,MAAA,WAAAD,GAAA,EAAAF,GAAA;sBAAA,OACAE,GAAA,GAAAI,IAAA,CAAAM,GAAA,CAAAZ,GAAA,GAAAW,IAAA;oBAAA,QAAAqC,aAAA,CAAApF,MAAA,EAAAkD,OAAA;kBACA;oBACAgC,MAAA,CAAAjD,IAAA;kBACA;kBACA;gBACA;gBACA;kBACAiD,MAAA,CAAAjD,IAAA,IAAAH,MAAA;cACA;cACA;YACA;YACA;YACA;YACA;cACAoD,MAAA,CAAAjD,IAAA,IAAAH,MAAA;UACA;QACA;QAEA,OAAAoD,MAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAI,eAAA,WAAAA,gBAAA;MACA,UAAA/G,MAAA,CAAAC,YAAA;QACA,KAAA+G,QAAA,CAAAC,OAAA;QACA;MACA;MAEA,UAAAjH,MAAA,CAAAU,MAAA,CAAAe,MAAA;QACA,KAAAuF,QAAA,CAAAC,OAAA;QACA;MACA;MAEA;QACA,KAAAlG,OAAA;QACA;QACA,IAAA2E,MAAA,QAAAF,SAAA;QACA,KAAAnE,aAAA,QAAAmF,qBAAA,CAAAd,MAAA;;QAEA;QACA,KAAAwB,YAAA;QAEA,KAAA9F,UAAA;MACA,SAAA+F,KAAA;QACA,KAAAH,QAAA,CAAAG,KAAA,aAAAA,KAAA,CAAAC,OAAA;MACA;QACA,KAAArG,OAAA;MACA;IACA;IAEA;AACA;AACA;IACAmG,YAAA,WAAAA,aAAA;MACA;MACA,IAAAG,SAAA,QAAArH,MAAA,CAAAU,MAAA,CAAA4G,IAAA,WAAA1F,KAAA;QAAA,OAAAA,KAAA,CAAA2F,IAAA;MAAA;MAEA,KAAAF,SAAA;;MAEA,IAAA3D,IAAA,QAAAC,aAAA,CAAA0D,SAAA;MACA,IAAAG,KAAA,GAAAH,SAAA,CAAAE,IAAA;;MAEA;MACA,IAAAjE,WAAA,QAAA5D,aAAA,CAAA2H,SAAA,CAAAjE,QAAA;MACA,KAAAE,WAAA;MAEA,KAAAjC,aAAA,CAAAkG,IAAA,WAAAtD,CAAA,EAAAC,CAAA;QACA,IAAAuD,MAAA,GAAAxD,CAAA,CAAAP,IAAA;QACA,IAAAgE,MAAA,GAAAxD,CAAA,CAAAR,IAAA;;QAEA;QACA,QAAAJ,WAAA,CAAAZ,OAAA;UACA;YACA+E,MAAA,GAAA7D,MAAA,CAAA6D,MAAA;YACAC,MAAA,GAAA9D,MAAA,CAAA8D,MAAA;YACA;UACA;YACAD,MAAA,GAAAA,MAAA,OAAAE,IAAA,CAAAF,MAAA,EAAAG,OAAA;YACAF,MAAA,GAAAA,MAAA,OAAAC,IAAA,CAAAD,MAAA,EAAAE,OAAA;YACA;UACA;YACAH,MAAA,GAAAA,MAAA;YACAC,MAAA,GAAAA,MAAA;YACA;UACA;YACAD,MAAA,GAAAI,MAAA,CAAAJ,MAAA;YACAC,MAAA,GAAAG,MAAA,CAAAH,MAAA;QACA;;QAEA;QACA,IAAAF,KAAA;UACA,OAAAC,MAAA,GAAAC,MAAA,OAAAD,MAAA,GAAAC,MAAA;QACA;UACA,OAAAD,MAAA,GAAAC,MAAA,OAAAD,MAAA,GAAAC,MAAA;QACA;MACA;IACA;IAEAI,iBAAA,WAAAA,kBAAA;MACA;MACA,KAAA9H,MAAA,CAAA+H,cAAA;MACA,KAAA/H,MAAA,CAAAgI,aAAA;IACA;IAEAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAvI,OAAA,oBAAAwI,oBAAA,CAAAxI,OAAA,IAAAyI,IAAA,UAAAC,QAAA;QAAA,IAAAC,eAAA,EAAAC,YAAA;QAAA,WAAAJ,oBAAA,CAAAxI,OAAA,IAAA6I,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAA,IAGAV,MAAA,CAAAlI,MAAA,CAAAZ,IAAA;gBAAAuJ,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAX,MAAA,CAAAlB,QAAA,CAAAC,OAAA;cAAA,OAAA0B,QAAA,CAAAG,MAAA;YAAA;cAAA,IAKAZ,MAAA,CAAAlI,MAAA,CAAAC,YAAA;gBAAA0I,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAX,MAAA,CAAAlB,QAAA,CAAAC,OAAA;cAAA,OAAA0B,QAAA,CAAAG,MAAA;YAAA;cAAA,IAIAZ,MAAA,CAAAlI,MAAA,CAAAU,MAAA,CAAAe,MAAA;gBAAAkH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAX,MAAA,CAAAlB,QAAA,CAAAC,OAAA;cAAA,OAAA0B,QAAA,CAAAG,MAAA;YAAA;cAIA;cACAP,eAAA,GAAAL,MAAA,CAAAlI,MAAA,CAAAU,MAAA,CAAA4G,IAAA,WAAA1F,KAAA;gBAAA,QAAAA,KAAA,CAAAwB,QAAA;cAAA;cAAA,KACAmF,eAAA;gBAAAI,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACAX,MAAA,CAAAlB,QAAA,CAAAC,OAAA;cAAA,OAAA0B,QAAA,CAAAG,MAAA;YAAA;cAIA;cACAN,YAAA;gBACApJ,IAAA,EAAA8I,MAAA,CAAAlI,MAAA,CAAAZ,IAAA;gBACAG,IAAA;gBACAS,MAAA;kBACAC,YAAA,EAAAiI,MAAA,CAAAlI,MAAA,CAAAC,YAAA;kBACAC,YAAA,EAAAgI,MAAA,CAAAlI,MAAA,CAAAE,YAAA;kBACAG,SAAA,EAAA6H,MAAA,CAAAlI,MAAA,CAAAK,SAAA;kBACAC,WAAA,EAAA4H,MAAA,CAAAlI,MAAA,CAAAM,WAAA;kBACAG,WAAA,EAAAyH,MAAA,CAAAlI,MAAA,CAAAS,WAAA;kBACAC,MAAA,EAAAwH,MAAA,CAAAlI,MAAA,CAAAU;gBACA;cACA,GAEA;cAAAiI,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAE,gCAAA,EAAAP,YAAA;YAAA;cAEAN,MAAA,CAAAlB,QAAA,CAAAgC,OAAA;cAAAL,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAM,EAAA,GAAAN,QAAA;cAEA,IAAAA,QAAA,CAAAM,EAAA;gBACAf,MAAA,CAAAlB,QAAA,CAAAG,KAAA,cAAAwB,QAAA,CAAAM,EAAA,CAAA7B,OAAA;cACA;YAAA;YAAA;cAAA,OAAAuB,QAAA,CAAAO,IAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEAa,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MAAA,WAAAjB,kBAAA,CAAAvI,OAAA,oBAAAwI,oBAAA,CAAAxI,OAAA,IAAAyI,IAAA,UAAAgB,SAAA;QAAA,IAAA1C,MAAA,EAAA2C,OAAA,EAAAC,aAAA;QAAA,WAAAnB,oBAAA,CAAAxI,OAAA,IAAA6I,IAAA,UAAAe,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAb,IAAA,GAAAa,SAAA,CAAAZ,IAAA;YAAA;cACAO,MAAA,CAAAlI,aAAA;cACAkI,MAAA,CAAApI,mBAAA;cAAAyI,SAAA,CAAAb,IAAA;cAAAa,SAAA,CAAAZ,IAAA;cAAA,OAEA,IAAAa,iCAAA;gBAAAC,UAAA;cAAA;YAAA;cAAAhD,MAAA,GAAA8C,SAAA,CAAAG,IAAA;cACAN,OAAA,GAAA3C,MAAA,CAAAkD,IAAA,EAEA;cAAA,IACArK,KAAA,CAAAsK,OAAA,CAAAR,OAAA;gBAAAG,SAAA,CAAAZ,IAAA;gBAAA;cAAA;cAAA,MACA,IAAAkB,KAAA;YAAA;cAGA;cACAX,MAAA,CAAAnI,YAAA,GAAAqI,OAAA,CAAA9F,GAAA,WAAAxD,MAAA;gBAAA;kBACA6E,EAAA,EAAA7E,MAAA,CAAA6E,EAAA;kBACAzF,IAAA,EAAAY,MAAA,CAAAZ,IAAA;kBACA4K,UAAA,EAAAhK,MAAA,CAAAgK,UAAA;kBACAhK,MAAA,EAAAA,MAAA,CAAAA,MAAA;oBACAC,YAAA;oBACAgK,cAAA;oBACAC,aAAA;oBACA9J,aAAA;oBACA+J,eAAA;oBACAC,kBAAA;oBACA3J,WAAA;oBACAsH,cAAA;oBACAC,aAAA;kBACA;gBACA;cAAA;cAEAoB,MAAA,CAAApI,mBAAA;cAAAyI,SAAA,CAAAZ,IAAA;cAAA;YAAA;cAAAY,SAAA,CAAAb,IAAA;cAAAa,SAAA,CAAAR,EAAA,GAAAQ,SAAA;cAEAY,OAAA,CAAAlD,KAAA,YAAAsC,SAAA,CAAAR,EAAA;cACAG,MAAA,CAAApC,QAAA,CAAAG,KAAA,CACA,EAAAoC,aAAA,GAAAE,SAAA,CAAAR,EAAA,CAAAqB,QAAA,cAAAf,aAAA,gBAAAA,aAAA,GAAAA,aAAA,CAAAzJ,IAAA,cAAAyJ,aAAA,uBAAAA,aAAA,CAAAnC,OAAA,KACAqC,SAAA,CAAAR,EAAA,CAAA7B,OAAA,IACA,gBACA;YAAA;cAAAqC,SAAA,CAAAb,IAAA;cAEAQ,MAAA,CAAAlI,aAAA;cAAA,OAAAuI,SAAA,CAAAc,MAAA;YAAA;YAAA;cAAA,OAAAd,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IAEA;IACAmB,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,WAAAvC,kBAAA,CAAAvI,OAAA,oBAAAwI,oBAAA,CAAAxI,OAAA,IAAAyI,IAAA,UAAAsC,SAAA;QAAA,IAAAC,aAAA;QAAA,WAAAxC,oBAAA,CAAAxI,OAAA,IAAA6I,IAAA,UAAAoC,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlC,IAAA,GAAAkC,SAAA,CAAAjC,IAAA;YAAA;cACA;gBACA;gBACA+B,aAAA;kBACA3K,YAAA;kBACAgK,cAAA;kBACAC,aAAA;kBACA9J,aAAA;kBACA+J,eAAA;kBACAC,kBAAA;kBACA3J,WAAA;kBACAsH,cAAA;kBACAC,aAAA;gBACA,GAEA;gBACA0C,MAAA,CAAA1K,MAAA,OAAA+K,cAAA,CAAAnL,OAAA,MAAAmL,cAAA,CAAAnL,OAAA,MAAAmL,cAAA,CAAAnL,OAAA,MACAgL,aAAA,GACAI,IAAA,CAAAC,KAAA,CAAAR,GAAA,CAAAzK,MAAA;kBACAZ,IAAA,EAAAqL,GAAA,CAAArL;gBAAA,EACA;gBAEAsL,MAAA,CAAA1J,mBAAA;gBACA0J,MAAA,CAAA1D,QAAA,CAAAgC,OAAA;;gBAEA;gBACA0B,MAAA,CAAAQ,SAAA;kBACA;kBACAb,OAAA,CAAAc,GAAA,WAAAT,MAAA,CAAA1K,MAAA;gBACA;cACA,SAAAoL,GAAA;gBACAf,OAAA,CAAAlD,KAAA,YAAAiE,GAAA;gBACAV,MAAA,CAAA1D,QAAA,CAAAG,KAAA,aAAAiE,GAAA,CAAAhE,OAAA;cACA;YAAA;YAAA;cAAA,OAAA0D,SAAA,CAAA5B,IAAA;UAAA;QAAA,GAAAyB,QAAA;MAAA;IACA;IACAU,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,OAAA;MAAA,WAAAnD,kBAAA,CAAAvI,OAAA,oBAAAwI,oBAAA,CAAAxI,OAAA,IAAAyI,IAAA,UAAAkD,SAAA;QAAA,WAAAnD,oBAAA,CAAAxI,OAAA,IAAA6I,IAAA,UAAA+C,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAA5C,IAAA;YAAA;cAAA4C,SAAA,CAAA7C,IAAA;cAAA6C,SAAA,CAAA5C,IAAA;cAAA,OAEAyC,OAAA,CAAAI,QAAA;gBACAnM,IAAA;cACA;YAAA;cAAAkM,SAAA,CAAA5C,IAAA;cAAA,OAEA,IAAA8C,kCAAA,EAAAlB,GAAA,CAAA5F,EAAA;YAAA;cACAyG,OAAA,CAAArK,YAAA,GAAAqK,OAAA,CAAArK,YAAA,CAAAU,MAAA,WAAA3B,MAAA;gBAAA,OAAAA,MAAA,CAAA6E,EAAA,KAAA4F,GAAA,CAAA5F,EAAA;cAAA;cACAyG,OAAA,CAAAtE,QAAA,CAAAgC,OAAA;cAAAyC,SAAA,CAAA5C,IAAA;cAAA;YAAA;cAAA4C,SAAA,CAAA7C,IAAA;cAAA6C,SAAA,CAAAxC,EAAA,GAAAwC,SAAA;cAEA,IAAAA,SAAA,CAAAxC,EAAA;gBACAqC,OAAA,CAAAtE,QAAA,CAAAG,KAAA,aAAAsE,SAAA,CAAAxC,EAAA,CAAA7B,OAAA;cACA;YAAA;YAAA;cAAA,OAAAqE,SAAA,CAAAvC,IAAA;UAAA;QAAA,GAAAqC,QAAA;MAAA;IAEA;IAEAK,UAAA,WAAAA,WAAA;MACA,IAAAC,WAAA,GAAAC,MAAA,CAAAC,IAAA;MACA,IAAAC,KAAA,QAAAC,KAAA,CAAAC,WAAA,CAAAC,GAAA,CAAAC,SAAA;MACA,IAAAC,KAAA;MACA,IAAAtG,IAAA,OAAA4B,IAAA,GAAA2E,kBAAA;;MAEA;MACA,IAAAC,cAAA,iqBAcA;MAEAV,WAAA,CAAAW,QAAA,CAAAC,KAAA,qEAAAnK,MAAA,CAGA+J,KAAA,0XAAA/J,MAAA,CAYA,KAAAnB,WAAA,w8IAAAmB,MAAA,CAwKAiK,cAAA,mVAAAjK,MAAA,CASA0J,KAAA,CAAAU,SAAA,CAAAC,OAAA,wTAYA;MAEAd,WAAA,CAAAW,QAAA,CAAAI,KAAA;MAEAC,UAAA;QACA;UACAhB,WAAA,CAAAiB,KAAA;UACAjB,WAAA,CAAAkB,KAAA;QACA,SAAAC,CAAA;UACA3C,OAAA,CAAAlD,KAAA,eAAA6F,CAAA;QACA;MACA;IACA;IAEAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MAAA,WAAA/E,kBAAA,CAAAvI,OAAA,oBAAAwI,oBAAA,CAAAxI,OAAA,IAAAyI,IAAA,UAAA8E,SAAA;QAAA,IAAAC,OAAA,EAAAC,GAAA;QAAA,WAAAjF,oBAAA,CAAAxI,OAAA,IAAA6I,IAAA,UAAA6E,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3E,IAAA,GAAA2E,SAAA,CAAA1E,IAAA;YAAA;cAAA0E,SAAA,CAAA3E,IAAA;cAEAsE,OAAA,CAAAnM,OAAA;cACAqM,OAAA,GAAAF,OAAA,CAAAjB,KAAA,CAAAC,WAAA,CAAAC,GAAA;cACAkB,GAAA;gBACAG,MAAA;gBAAA;gBACAC,QAAA;gBACAC,KAAA;kBAAAnO,IAAA;kBAAAoO,OAAA;gBAAA;gBACAC,WAAA;kBAAAC,KAAA;gBAAA;gBACAC,KAAA;kBACAC,IAAA;kBACArJ,MAAA;kBACAsJ,WAAA,EAAAd,OAAA,CAAA/L,WAAA;gBACA;gBACA8M,SAAA;kBAAAC,IAAA;gBAAA;gBAAA;gBACAC,MAAA,GACA;kBAAAC,IAAA;kBAAAC,KAAA;gBAAA,GACA;kBAAAD,IAAA,MAAAzG,IAAA,GAAA2E,kBAAA;kBAAA+B,KAAA;kBAAAC,SAAA;gBAAA,EACA;gBACAC,MAAA;kBACAC,MAAA;kBACAC,QAAA;oBACA7O,OAAA;kBACA;gBACA;cACA;cAAA2N,SAAA,CAAA1E,IAAA;cAAA,OAEA,IAAA6F,iBAAA,IAAAC,GAAA,CAAAtB,GAAA,EAAAuB,IAAA,CAAAxB,OAAA,EAAAyB,IAAA;YAAA;cACA3B,OAAA,CAAAlG,QAAA,CAAAgC,OAAA;cAAAuE,SAAA,CAAA1E,IAAA;cAAA;YAAA;cAAA0E,SAAA,CAAA3E,IAAA;cAAA2E,SAAA,CAAAtE,EAAA,GAAAsE,SAAA;cAEAL,OAAA,CAAAlG,QAAA,CAAAG,KAAA,cAAAoG,SAAA,CAAAtE,EAAA,CAAA7B,OAAA;YAAA;cAAAmG,SAAA,CAAA3E,IAAA;cAEAsE,OAAA,CAAAnM,OAAA;cAAA,OAAAwM,SAAA,CAAAhD,MAAA;YAAA;YAAA;cAAA,OAAAgD,SAAA,CAAArE,IAAA;UAAA;QAAA,GAAAiE,QAAA;MAAA;IAEA;IAEA;AACA;AACA;AACA;IACA2B,QAAA,WAAAA,SAAA;MACA,KAAA9O,MAAA,CAAAU,MAAA,CAAA6F,IAAA;QACAnD,QAAA;QAAA;QACAC,WAAA;QAAA;QACAqB,MAAA;QAAA;QACA6C,IAAA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAwH,WAAA,WAAAA,YAAA5L,KAAA;MACA,KAAAnD,MAAA,CAAAU,MAAA,CAAAsO,MAAA,CAAA7L,KAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA8L,SAAA,WAAAA,UAAA9L,KAAA,EAAA+L,SAAA;MACA,IAAAxO,MAAA,OAAA4D,mBAAA,CAAA1E,OAAA,OAAAI,MAAA,CAAAU,MAAA;;MAEA,IAAAwO,SAAA,aAAA/L,KAAA;QACA;QAAA,IAAAgM,KAAA,GACA,CAAAzO,MAAA,CAAAyC,KAAA,OAAAzC,MAAA,CAAAyC,KAAA;QAAAzC,MAAA,CAAAyC,KAAA,IAAAgM,KAAA;QAAAzO,MAAA,CAAAyC,KAAA,QAAAgM,KAAA;MACA,WAAAD,SAAA,eAAA/L,KAAA,GAAAzC,MAAA,CAAAe,MAAA;QACA;QAAA,IAAA2N,KAAA,GACA,CAAA1O,MAAA,CAAAyC,KAAA,OAAAzC,MAAA,CAAAyC,KAAA;QAAAzC,MAAA,CAAAyC,KAAA,IAAAiM,KAAA;QAAA1O,MAAA,CAAAyC,KAAA,QAAAiM,KAAA;MACA;;MAEA;MACA,KAAAC,IAAA,MAAArP,MAAA,YAAAU,MAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA4O,iBAAA,WAAAA,kBAAAnM,KAAA;MACA,IAAAvB,KAAA,QAAA5B,MAAA,CAAAU,MAAA,CAAAyC,KAAA;MACA,IAAAG,WAAA,QAAA5D,aAAA,CAAAkC,KAAA,CAAAwB,QAAA;MACA,IAAAE,WAAA;QACA;QACA1B,KAAA,CAAA8C,MAAA,QAAA6K,gBAAA,CAAAjM,WAAA,CAAAZ,OAAA;QACAd,KAAA,CAAAyB,WAAA,GAAAC,WAAA,CAAAkM,UAAA;QACA5N,KAAA,CAAA2F,IAAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAkI,eAAA,WAAAA,gBAAArM,QAAA;MACA,IAAAE,WAAA,QAAA5D,aAAA,CAAA0D,QAAA;MACA,KAAAE,WAAA;;MAEA;MACA,IAAAA,WAAA,CAAAZ,OAAA,gBAAAY,WAAA,CAAAZ,OAAA;QACA;MACA;MAEA,OAAAY,WAAA,CAAAZ,OAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAgN,cAAA,WAAAA,eAAAtM,QAAA;MACA,IAAAE,WAAA,QAAA5D,aAAA,CAAA0D,QAAA;MACA,QAAAE,WAAA,aAAAA,WAAA,uBAAAA,WAAA,CAAAkM,UAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAD,gBAAA,WAAAA,iBAAAI,WAAA;MACA,QAAAA,WAAA;QACA;UACA;QACA;UACA;QACA;UACA;MACA;IACA;IAEA;AACA;AACA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAA5P,MAAA;QACAZ,IAAA;QACAa,YAAA;QACAC,YAAA;UACAC,KAAA;UACAC,aAAA;QACA;QACAC,SAAA;QACAC,WAAA;UACAC,eAAA;UACAC,UAAA;QACA;QACAC,WAAA;QACAC,MAAA;MACA;MACA,KAAAU,UAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAuC,aAAA,WAAAA,cAAA/B,KAAA;MACA;MACA,IAAAA,KAAA,CAAAyB,WAAA,IAAAzB,KAAA,CAAAyB,WAAA;QACA,UAAAf,MAAA,CAAAV,KAAA,CAAAwB,QAAA,OAAAd,MAAA,CAAAV,KAAA,CAAAyB,WAAA;MACA;MACA,OAAAzB,KAAA,CAAAwB,QAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAyM,cAAA,WAAAA,eAAAjO,KAAA;MACA,IAAAkO,SAAA,QAAAvN,aAAA,CAAAX,KAAA,CAAAwB,QAAA;;MAEA;MACA,IAAAxB,KAAA,CAAAyB,WAAA,IAAAzB,KAAA,CAAAyB,WAAA;QAAA,IAAA0M,qBAAA;QACA;QACA,IAAAC,gBAAA,KAAAD,qBAAA,QAAAjP,kBAAA,CAAAwG,IAAA,WAAA+F,GAAA;UAAA,OAAAA,GAAA,CAAAxM,KAAA,KAAAe,KAAA,CAAAyB,WAAA;QAAA,gBAAA0M,qBAAA,uBAAAA,qBAAA,CAAAnP,KAAA,KAAAgB,KAAA,CAAAyB,WAAA;QACA,UAAAf,MAAA,CAAAwN,SAAA,OAAAxN,MAAA,CAAA0N,gBAAA;MACA;MAEA,OAAAF,SAAA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAG,eAAA,WAAAA,gBAAApP,KAAA,EAAAe,KAAA;MACA,IAAAf,KAAA;MAEA,IAAAyC,WAAA,QAAA5D,aAAA,CAAAkC,KAAA,CAAAwB,QAAA;MACA,KAAAE,WAAA,SAAAzC,KAAA;;MAEA;MACA,IAAAyC,WAAA,CAAAZ,OAAA,gBAAAY,WAAA,CAAAZ,OAAA;QACA;QACA,YAAAY,WAAA,CAAAZ,OAAA,EAAA7B,KAAA;MACA;;MAEA;MACA,QAAAyC,WAAA,CAAAZ,OAAA;QACA;UACA,IAAAwN,QAAA,GAAAtM,MAAA,CAAA/C,KAAA;UACA,IAAAiD,KAAA,CAAAoM,QAAA;;UAEA;UACA,IAAAtO,KAAA,CAAAyB,WAAA,IAAAzB,KAAA,CAAAyB,WAAA;YACA;YACA,IAAAzB,KAAA,CAAAyB,WAAA,cAAAzB,KAAA,CAAAyB,WAAA;cACA,IAAAzB,KAAA,CAAA8C,MAAA;gBACA,QAAAwL,QAAA,QAAAvL,OAAA;cACA;cACA,OAAAuL,QAAA,CAAAvL,OAAA;YACA;UACA;UAEA,QAAA/C,KAAA,CAAA8C,MAAA;YACA;cACA,OAAAwL,QAAA,CAAAvL,OAAA;YACA;cACA,QAAAuL,QAAA,QAAAvL,OAAA;YACA;cACA,aAAAuL,QAAA,CAAAvL,OAAA;YACA;cACA,aAAAuL,QAAA,CAAAvL,OAAA;YACA;cACA,OAAAuL,QAAA,CAAAvL,OAAA;UACA;QAEA;UACA,WAAA1C,eAAA,EAAApB,KAAA,EAAA6D,MAAA,CAAA9C,KAAA,CAAA8C,MAAA;QAEA;UACA,IAAA9C,KAAA,CAAAyB,WAAA;YACA,QAAAO,MAAA,CAAA/C,KAAA,SAAA8D,OAAA;UACA;UACA,OAAA9D,KAAA;QAEA;UACA,OAAAA,KAAA;MACA;IACA;IAEAoF,aAAA,WAAAA,cAAA;MACA,aAAAjG,MAAA,CAAAM,WAAA,CAAAE,UAAA;QACA;UACA;QACA;UACA;QACA;QACA;UACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACA2P,cAAA,WAAAA,eAAArK,QAAA;MACA,QAAA1D,QAAA,CAAAxC,OAAA,EAAAkG,QAAA,kBAAAA,QAAA;QACA,IAAAA,QAAA,CAAAK,OAAA,KAAAf,SAAA,IAAAU,QAAA,CAAAC,IAAA,KAAAX,SAAA;UACA;UACA,IAAAgL,kBAAA,QAAA1Q,aAAA,MAAAM,MAAA,CAAAC,YAAA;UACA,IAAAoQ,YAAA,GAAAvK,QAAA,CAAAK,OAAA;;UAEA;UACA,IAAAiK,kBAAA,IAAAA,kBAAA,CAAA1N,OAAA,IACA,YAAA0N,kBAAA,CAAA1N,OAAA;YACA2N,YAAA,QAAAD,kBAAA,CAAA1N,OAAA,EAAA2N,YAAA;UACA;;UAEA;UACA,UAAA/N,MAAA,CAAAwD,QAAA,CAAAC,IAAA,EAAAzD,MAAA,CAAA+N,YAAA;QACA;MACA;;MAEA;MACA,SAAArQ,MAAA,CAAAC,YAAA;QACA,IAAAqD,WAAA,QAAA5D,aAAA,MAAAM,MAAA,CAAAC,YAAA;QACA,IAAAqD,WAAA,IAAAA,WAAA,CAAAZ,OAAA,IACA,YAAAY,WAAA,CAAAZ,OAAA;UACA,YAAAY,WAAA,CAAAZ,OAAA,EAAAoD,QAAA;QACA;MACA;MAEA,OAAA+B,MAAA,CAAA/B,QAAA;IACA;EACA;AACA;AAAAwK,OAAA,CAAA1Q,OAAA,GAAA2Q,SAAA"}]}