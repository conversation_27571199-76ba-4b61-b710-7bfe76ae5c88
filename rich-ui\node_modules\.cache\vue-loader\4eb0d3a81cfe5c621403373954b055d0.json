{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\CtnrTruckComponent.vue?vue&type=template&id=6aa1f7e5&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\CtnrTruckComponent.vue", "mtime": 1754881964230}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}