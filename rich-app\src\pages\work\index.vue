<template>
  <view class="work-container">
    <uni-section title="Clients Managerment" type="line"></uni-section>
    <view v-if="checkRole(['client','warehouse'])">
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToAddConsignee">
              <image mode="widthFix" src="/static/icons/person-add.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">新客直录</text>
              <text class="text">New Client</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToScan">
              <uni-icons size="30" type="scan"></uni-icons>
              <text class="text">客户扫码</text>
              <text class="text">Scan Client</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToConsignee">
              <uni-icons size="30" type="person-filled"></uni-icons>
              <text class="text">客户列表</text>
              <text class="text">Clients List</text>
            </view>
          </uni-grid-item>
        </uni-grid>
      </view>
    </view>
    <uni-section title="Cargo Entry Information" type="line"></uni-section>
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <uni-grid-item>
            <view class="grid-item-box" @click="handleAddInventory">
              <image mode="widthFix" src="/static/icons/add.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">货物直录</text>
              <text class="text">New Cargo</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view v-if="checkRole(['warehouse'])" class="grid-item-box" @click="handleToScan">
              <uni-icons size="30" type="scan"></uni-icons>
              <text class="text">货物扫码</text>
              <text class="text">Scan Cargo</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToDeliveryList">
              <uni-icons size="30" type="search"></uni-icons>
              <text class="text">货物查询</text>
              <text class="text">Track Cargo</text>
            </view>
          </uni-grid-item>
        </uni-grid>
      </view>

    <!-- 客户服务 -->
    <view v-if="checkRole(['client','warehouse'])">
      <uni-section title="Cargo Managerment Short Cut" type="line"></uni-section>
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToConsignee">
              <uni-icons size="30" type="person-filled"></uni-icons>
              <text class="text">Consignee Management</text>
            </view>
          </uni-grid-item> -->

          <!-- 在途 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToInTransit">
              <image mode="widthFix" src="/static/icons/delivery.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">在途</text>
              <!-- <text class="text">Floating Cargo({{ preEntryCount }})</text> -->
              <text class="text">Floating Cargo</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="preEntryCount" :type="'error' "/>
              </view>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToClaim">
              <image mode="widthFix" src="/static/icons/person-q.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">未知归属</text>
              <text class="text">Unknown</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="unknownCount" :type="'error' "/>
              </view>
            </view>
          </uni-grid-item>


          <!-- 未完成 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToUncompleted">
              <image mode="widthFix" src="/static/icons/unconfirmed.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">信息未完善</text>
              <!-- <text class="text">Uncompleted({{ uncompletedCount }})</text> -->
              <text class="text">Uncompleted</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="uncompletedCount" :type="'error' "/>
              </view>
            </view>
          </uni-grid-item>
          <!-- 未确认 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToUnconfirmed">
              <image mode="widthFix" src="/static/icons/uncompleted.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">仓管未确认</text>
              <!-- <text class="text">Unconfirmed({{ unconfirmedCount }})</text> -->
              <text class="text">Unconfirmed</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="unconfirmedCount" :type=" 'error' "/>
              </view>
            </view>
          </uni-grid-item>
          <!-- 已确认 -->
          <uni-grid-item>
            <view class="grid-item-box" @click="handleToConfirmed">
              <image mode="widthFix" src="/static/icons/confirmed.svg" style="width: 30px; height: 30px;"></image>
              <text class="text">已入仓</text>
              <!-- <text class="text">Printed In({{ confirmedCount }})</text> -->
              <text class="text">Printed In</text>
              <text class="text">({{ totalVolume }} CBM)</text>
              <view class="grid-dot">
                <uni-badge :max-num="9999" :text="confirmedCount" :type="'primary'"/>
              </view>
            </view>
          </uni-grid-item>
        </uni-grid>
      </view>
    </view>

    <!-- 仓管服务 -->
    <view v-if="checkRole(['warehouse'])">
      <uni-section title="系统管理" type="line"></uni-section>
      <view class="grid-body">
        <uni-grid :column="3" :showBorder="false">
          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToInventory">
              <uni-icons size="30" type="email"></uni-icons>
              <text class="text">送货单填写</text>
            </view>
          </uni-grid-item> -->

          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToScan">
              <uni-icons size="30" type="scan"></uni-icons>
              <text class="text">扫一扫</text>
            </view>
          </uni-grid-item>

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToDeliveryList">
              <uni-icons size="30" type="search"></uni-icons>
              <text class="text">货物查询</text>
            </view>
          </uni-grid-item> -->

          <uni-grid-item>
            <view class="grid-item-box" @click="handleToPrint">
              <uni-icons size="30" type="loop"></uni-icons>
              <text class="text">连接打印机</text>
            </view>
          </uni-grid-item>

          <!-- <uni-grid-item>
            <view class="grid-item-box" @click="handleToDeliveryList">
              <uni-icons size="30" type="list"></uni-icons>
              <text class="text">Goods List</text>
            </view>
          </uni-grid-item> -->
        </uni-grid>
      </view>
    </view>

    <!-- 临时调试按钮 -->
    <view v-if="false" style="padding: 20px; border-top: 1px solid #eee; margin-top: 20px;">
      <button size="mini" type="primary" @click="testScanCode">测试扫码功能</button>
    </view>

  </view>
</template>

<script>
import uniSection from '@/componetsPackage/uni-section/uni-section.vue'
import uniGrid from '@/componetsPackage/uni-grid/uni-grid.vue'
import uniGridItem from '@/componetsPackage/uni-grid-item/uni-grid-item.vue'
import uniIcons from '@/componetsPackage/uni-icons/uni-icons.vue'
import uniBadge from '@/componetsPackage/uni-badge/uni-badge.vue'
import {checkRole} from '@/utils/permission'
import {decrypt} from "../../utils/common";
import {
  getInventoryByExpress,
  getStatusNumber,
  checkExpressStatus,
  claimExpress,
  getInventoryList
} from '@/api/system/invenory'


export default {
  components: {
    uniSection,
    uniGrid,
    uniGridItem,
    uniIcons,
    uniBadge
  },
  data() {
    return {
      current: 0,
      swiperDotIndex: 0,
      expressNo: null,
      data: [{
        image: '/static/images/banner/banner01.jpg'
      },
        {
          image: '/static/images/banner/banner02.jpg'
        },
        {
          image: '/static/images/banner/banner03.jpg'
        }
      ],
      inTransitCount: 0,
      preEntryCount: 0,
      uncompletedCount: 0,
      unconfirmedCount: 0,
      confirmedCount: 0,
      unknownCount: 0,
      totalVolume: 0
    }
  },
  onLoad() {
    // this.loadStatusNumber()
  },
  onShow() {
    this.loadStatusNumber()
  },
  methods: {
    handleToAddConsignee() {
      uni.navigateTo({
        url: '/packageA/consignee/add'
      })
    },
    handleAddInventory() {
      uni.navigateTo({
        url: '/packageA/inventory/edit?add=true'
      });
    },
    inputDialogToggle() {
      this.$refs.inputDialog.open()
    },
    dialogInputConfirm(val) {
      uni.showLoading({
        title: '3秒后会关闭'
      })

      setTimeout(() => {
        uni.hideLoading()
        this.value = val
        // 关闭窗口后，恢复默认内容
        this.$refs.inputDialog.close()
      }, 3000)
    },
    handleToClaim() {
      this.$tab.navigateTo('/packageA/inventoryList/index?claim=true')
    },
    handleToPrint() {
      this.$tab.navigateTo('/print/index/index')
    },
    handleToDeliveryList() {
      this.$tab.navigateTo('/packageA/inventoryList/index')
    },
    checkRole,
    handleToConsignee() {
      this.$tab.navigateTo('/packageA/consignee/index')
    },
    handleToInventory() {
      this.$tab.navigateTo('/packageA/inventory/index')
    },
    handleToScan() {
      // this.$tab.navigateTo('/pages/scan/index')
      // 允许从相机和相册扫码
      const that = this;

      // 显示加载提示
      uni.showLoading({
        title: '打开扫码中...',
        mask: true
      });
      
      uni.scanCode({
        // 是否只能从相机扫码，不允许从相册选择图片
        onlyFromCamera: false,
        // 扫码类型，默认为['barCode', 'qrCode']
        scanType: ['barCode', 'qrCode'],
        // 是否启用闪光灯，默认false
        autoDecodeCharset: true,
        success: function (res) {
          console.log('扫码成功:', res);
          uni.hideLoading();
          
          if (res.scanType === 'QR_CODE' && res.result) {
            // 处理URL类型的二维码
            if (res.result.includes('cnshipper.com/scan/inventory/edit')) {
              // 使用正则表达式提取consigneeId参数
              const regExp = /[?&]inventoryId=([^&]*)/;
              const match = regExp.exec(res.result);
              if (match && match[1]) {
                const consigneeIdEncoded = match[1];
                try {
                  // 解密consigneeId
                  const consigneeId = decrypt(consigneeIdEncoded);
                  uni.navigateTo({
                    url: '/packageA/inventory/edit?inventoryId=' + consigneeId
                  });
                  return;
                } catch (error) {
                  console.error('解密失败:', error);
                  uni.showToast({
                    title: '二维码格式错误',
                    icon: 'none'
                  });
                  return;
                }
              }
            } else if (res.result.startsWith('RS.')) {
              // 根据RS.开头的流水号查询库存ID
              const inventoryId = res.result.split('-')[1]
              console.log(inventoryId)
              if (inventoryId) {
                uni.navigateTo({
                  url: '/packageA/inventory/edit?inventoryId=' + decrypt(inventoryId)
                })
              }
              const inventorySerialNo = res.result.split('-')[0]
              console.log(inventorySerialNo)
              if (inventorySerialNo) {
                getInventoryList({inboundSerialNo: inventorySerialNo}).then(res => {
                  console.log(res)
                  if (res.code === 200) {
                    const {rows, total} = res;
                    if (rows.length > 0) {
                      uni.navigateTo({
                        url: '/packageA/inventory/edit?inventoryId=' + rows[0].inventoryId
                      })
                    }
                  }
                })
              }
            }

            try {
              // 原有的解密逻辑保留
              const arr = decrypt(res.result).split('-');
              // 扫码填写该收货人送货单
              if (arr[0] === 'consignee') {
                uni.navigateTo({
                  url: '/packageA/inventory/index?consigneeId=' + arr[1]
                })
              }
              if (arr[0] === 'inventory') {
                uni.navigateTo({
                  url: '/packageA/inventory/index?inventoryId=' + arr[1]
                })
              }
            } catch (error) {
              console.error('二维码解析失败:', error);
              uni.showToast({
                title: '二维码格式不正确',
                icon: 'none'
              });
            }
          }
          // 如果是条形码,说明是快递单号
          else if (res.scanType === 'CODE_128' && res.result) {
            const no = res.result

            // 根据用户角色处理不同逻辑
            if (checkRole(['warehouse'])) {
              // 仓管角色：原有逻辑
              that.handleWarehouseScan(no);
            } else if (checkRole(['client'])) {
              // 客户角色：新的状态检查逻辑
              that.handleClientScan(no);
            } else {
              uni.showToast({
                title: '您没有权限查看此快递',
                icon: 'none'
              });
            }
          } else {
            uni.showToast({
              title: '未识别到有效的二维码或条形码',
              icon: 'none'
            });
          }
        },
        fail: function (err) {
          console.error('扫码失败:', err);
          uni.hideLoading();

          if (err.errMsg) {
            if (err.errMsg.includes('cancel')) {
              uni.showToast({
                title: '取消扫码',
                icon: 'none'
              });
            } else if (err.errMsg.includes('permission')) {
              uni.showModal({
                title: '权限提示',
                content: '扫码功能需要相机权限，请在设置中开启',
                confirmText: '去设置',
                cancelText: '取消',
                success: function (res) {
                  if (res.confirm) {
                    // 打开系统设置
                    plus && plus.runtime.openURL('app-settings:');
                  }
                }
              });
            } else {
              uni.showToast({
                title: '扫码功能暂不可用: ' + (err.errMsg || '未知错误'),
                icon: 'none',
                duration: 3000
              });
            }
          } else {
            uni.showToast({
              title: '扫码功能异常，请重试',
              icon: 'none'
            });
          }
        },
        complete: function () {
          uni.hideLoading();
        }
      });
    },
    handleConfirmExpress() {
      // 处理录入的快递信息
      if (this.expressNo && checkRole(['warehouse'])) {
        uni.navigateTo({
          url: '/packageA/inventory/edit?expressNo=' + this.expressNo
        });
      } else {
        uni.showToast({
          title: '您没有权限',
          icon: 'none'
        });
      }
    },
    changeGrid(e) {
      let {index} = e.detail
      if (index === 0) {
        this.$tab.navigateTo('/packageA/consignee/index')
      }
    },
    loadStatusNumber() {
      const clientCode = this.$store.state.user.mpWarehouseClient?.clientCode
      if (clientCode) {
        getStatusNumber({clientCode: clientCode}).then(res => {
          this.preEntryCount = res.data.preEntryCount
          this.inTransitCount = res.data.inTransitCount
          this.uncompletedCount = res.data.uncompletedCount
          this.unconfirmedCount = res.data.unconfirmedCount
          this.confirmedCount = res.data.confirmedCount
          this.unknownCount = res.data.unknownCount
          this.totalVolume = res.data.totalVolume.toFixed(2)
        })
      } else {
        getStatusNumber({clientCode: null}).then(res => {
          this.preEntryCount = res.data.preEntryCount
          this.inTransitCount = res.data.inTransitCount
          this.uncompletedCount = res.data.uncompletedCount
          this.unconfirmedCount = res.data.unconfirmedCount
          this.confirmedCount = res.data.confirmedCount
          this.unknownCount = res.data.unknownCount
          this.totalVolume = res.data.totalVolume.toFixed(2)
        })
      }
    },
    handleToInTransit() {
      this.$tab.navigateTo('/packageA/inventoryList/index?inTransit=true')
    },
    handleToUncompleted() {
      this.$tab.navigateTo('/packageA/inventoryList/index?uncompleted=true')
    },
    handleToUnconfirmed() {
      this.$tab.navigateTo('/packageA/inventoryList/index?unconfirmed=true')
    },
    handleToConfirmed() {
      this.$tab.navigateTo('/packageA/inventoryList/index?confirmed=true')
    },

    // 仓管扫码处理逻辑
    handleWarehouseScan(expressNo) {
      const that = this;
      // 根据快递单号查询库存
      getInventoryByExpress(expressNo).then(res => {
        if (res.data) {
          uni.navigateTo({
            url: '/packageA/inventory/edit?inventoryId=' + res.data.inventoryId
          })
        } else {
          // 保存快递单号
          that.expressNo = expressNo;
          // 使用uni.showModal替代rich-dialog
          uni.showModal({
            title: '新建送货单',
            content: '快递: ' + expressNo + ' 不存在是否新建',
            cancelText: '取消',
            confirmText: '确定',
            success: function (res) {
              if (res.confirm) {
                that.handleConfirmExpress();
              }
            }
          });
        }
      });
    },

    // 客户扫码处理逻辑
    handleClientScan(expressNo) {
      const that = this;
      // 检查快递状态
      checkExpressStatus(expressNo).then(res => {
        const status = res.data;

        switch (status.type) {
          case 'NOT_EXIST':
            // 1. 不存在：此快递不存在，是否添加？
            uni.showModal({
              title: '快递不存在',
              content: '此快递不存在，是否添加？',
              cancelText: '取消',
              confirmText: '添加',
              success: function (modalRes) {
                if (modalRes.confirm) {
                  // 跳转到添加页面
                  uni.navigateTo({
                    url: '/packageA/inventory/index?add=true&expressNo=' + expressNo
                  });
                }
              }
            });
            break;

          case 'UNKNOWN_OWNER':
            // 2. 存在但属于未知归属：此快递已到仓库但无人认领，是否认领？
            uni.showModal({
              title: '快递无人认领',
              content: '此快递已到仓库但无人认领，是否认领？',
              cancelText: '取消',
              confirmText: '认领',
              success: function (modalRes) {
                if (modalRes.confirm) {
                  that.handleClaimExpress(status.inventoryId);
                }
              }
            });
            break;

          case 'BELONGS_TO_OTHERS':
            // 3. 存在但不属于自己：此快递属于其他人，请您重新核对单号
            uni.showModal({
              title: '快递归属错误',
              content: '此快递属于其他人，请您重新核对单号。',
              showCancel: false,
              confirmText: '确定'
            });
            break;

          case 'INCOMPLETE_INFO':
            // 4. 存在且属于自己但信息未完善：直接打开详情页，可编辑
            uni.navigateTo({
              url: '/packageA/inventory/edit?inventoryId=' + status.inventoryId
            });
            break;

          case 'IN_WAREHOUSE':
            // 5. 存在且属于自己且已入仓：直接打开详情页，仅查看
            uni.navigateTo({
              url: '/packageA/inventory/detail?inventoryId=' + status.inventoryId
            });
            break;

          case 'SHIPPED_OUT':
            // 6. 存在且属于自己但已出仓：货物已出仓，请联系瑞旗公司获取详情
            uni.showModal({
              title: '货物已出仓',
              content: '货物已出仓，请联系瑞旗公司获取详情。',
              showCancel: false,
              confirmText: '确定'
            });
            break;

          default:
            uni.showToast({
              title: '未知状态',
              icon: 'none'
            });
        }
      }).catch(error => {
        console.error('检查快递状态失败:', error);
        uni.showToast({
          title: '查询失败，请重试',
          icon: 'none'
        });
      });
    },

    // 认领快递
    handleClaimExpress(inventoryId) {
      const clientCode = this.$store.state.user.mpWarehouseClient?.clientCode;
      if (!clientCode) {
        uni.showToast({
          title: '客户信息不完整',
          icon: 'none'
        });
        return;
      }

      claimExpress(inventoryId, {clientCode}).then(res => {
        uni.showToast({
          title: '认领成功',
          icon: 'success'
        });
        // 跳转到编辑页面完善信息
        setTimeout(() => {
          uni.navigateTo({
            url: '/packageA/inventory/edit?inventoryId=' + inventoryId
          });
        }, 1500);
      }).catch(error => {
        console.error('认领失败:', error);
        uni.showToast({
          title: '认领失败，请重试',
          icon: 'none'
        });
      });
    }
  }
}
</script>

<style lang="scss" scoped>
/* #ifndef APP-NVUE */
page {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  background-color: #fff;
  min-height: 100%;
  height: auto;
}

view {
  font-size: 14px;
  line-height: inherit;
}

/* #endif */

.text {
  text-align: center;
  font-size: 26rpx;
  margin-top: 10rpx;
}

.grid-item-box {
  flex: 1;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 15px 0;
}

.dialog-content {
  padding: 30rpx;
}

.input-field {
  width: 100%;
  height: 80rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx 30rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  margin: 0 10rpx;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.btn-confirm {
  background-color: #007aff;
  color: #fff;
}

.grid-dot {
  position: absolute;
  top: 0;
  right: 0;
}
</style>
