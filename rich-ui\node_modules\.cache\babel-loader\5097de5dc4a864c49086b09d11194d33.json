{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\staffrole.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\staffrole.js", "mtime": 1754876882467}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listStaffrole", "query", "request", "url", "method", "params", "listStaffroleMenu", "getStaffrole", "staffRoleDeptId", "addStaffrole", "data", "updateStaffrole", "delStaffrole"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/staffrole.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询权限分配列表\r\nexport function listStaffrole(query) {\r\n  return request({\r\n    url: '/system/staffrole/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询权限分配列表\r\nexport function listStaffroleMenu(query) {\r\n  return request({\r\n    url: '/system/staffrole/menuList',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询权限分配详细\r\nexport function getStaffrole(staffRoleDeptId) {\r\n  return request({\r\n    url: '/system/staffrole/' + staffRoleDeptId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增权限分配\r\nexport function addStaffrole(data) {\r\n  return request({\r\n    url: '/system/staffrole',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改权限分配\r\nexport function updateStaffrole(data) {\r\n  return request({\r\n    url: '/system/staffrole',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除权限分配\r\nexport function delStaffrole(staffRoleDeptId) {\r\n  return request({\r\n    url: '/system/staffrole/' + staffRoleDeptId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,iBAAiBA,CAACL,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,YAAYA,CAACC,eAAe,EAAE;EAC5C,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGK,eAAe;IAC3CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACJ,eAAe,EAAE;EAC5C,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGK,eAAe;IAC3CJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}