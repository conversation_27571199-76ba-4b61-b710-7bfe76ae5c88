{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\line\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\line\\index.vue", "mtime": 1737429728558}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_line", "require", "name", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "lineList", "title", "open", "refreshTable", "isExpandAll", "queryParams", "parentId", "lineShortName", "lineLocalName", "lineEnName", "form", "rules", "watch", "n", "created", "getList", "methods", "_this", "listLine", "then", "response", "handleTree", "toggleExpandAll", "_this2", "$nextTick", "cancel", "reset", "lineId", "ancestors", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "pageNum", "reset<PERSON><PERSON>y", "handleAdd", "row", "undefined", "handleStatusChange", "_this3", "text", "status", "$confirm", "customClass", "changeLineStatus", "$modal", "msgSuccess", "catch", "handleUpdate", "_this4", "getLine", "submitForm", "_this5", "$refs", "validate", "valid", "updateLine", "addLine", "handleDelete", "_this6", "lineIds", "delLine", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "ParentId", "val", "exports", "_default"], "sources": ["src/views/system/line/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"lineQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.lineQuery\"\r\n              clearable\r\n              placeholder=\"中英文简称\"\r\n              style=\"width: 158px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item/>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <!--          <el-col :span=\"1.5\">-->\r\n          <!--            <el-button-->\r\n          <!--              type=\"primary\"-->\r\n          <!--              plain-->\r\n          <!--              icon=\"el-icon-plus\"-->\r\n          <!--              size=\"mini\"-->\r\n          <!--              @click=\"handleAdd\"-->\r\n          <!--              v-hasPermi=\"['system:line:add']\"-->\r\n          <!--            >新增-->\r\n          <!--            </el-button>-->\r\n          <!--          </el-col>-->\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:line:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              icon=\"el-icon-sort\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"info\"\r\n              @click=\"toggleExpandAll\"\r\n            >展开/折叠\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"lineList\"\r\n                  :default-expand-all=\"isExpandAll\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n                  row-key=\"lineId\">\r\n          <el-table-column label=\"航线名称\" prop=\"name\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.lineShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.lineLocalName }}</a>\r\n              {{ scope.row.lineEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"备注\" prop=\"remark\" width=\"300\"/>\r\n          <el-table-column label=\"排序\" align=\"center\" prop=\"orderNum\"/>\r\n          <el-table-column key=\"status\" align=\"center\" label=\"状态\" width=\"65\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:line:add']\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:line:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.parentId != 0\"\r\n                v-hasPermi=\"['system:line:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改航线对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item v-if=\"form.parentId!=0\" label=\"上级部门\" prop=\"parentId\">\r\n          <tree-select :multiple=\"false\" :pass=\"form.parentId\" :placeholder=\"'上级类名'\" :type=\"'line'\"\r\n                       @return=\"ParentId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"航线简称\" prop=\"lineShortName\">\r\n          <el-input v-model=\"form.lineShortName\" placeholder=\"航线简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"航线中文名\" prop=\"lineLocalName\">\r\n          <el-input v-model=\"form.lineLocalName\" placeholder=\"航线中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"航线英文名\" prop=\"lineEnName\">\r\n          <el-input v-model=\"form.lineEnName\" placeholder=\"航线英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" placeholder=\"排序\" style=\"width: 100%\" :min=\"0\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addLine, changeLineStatus, delLine, getLine, listLine, updateLine} from \"@/api/system/line\";\r\n\r\nexport default {\r\n  name: \"CourseLine\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 航线表格数据\r\n      lineList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        parentId: null,\r\n        lineShortName:null,\r\n        lineLocalName: null,\r\n        lineEnName: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询航线列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listLine(this.queryParams).then(response => {\r\n        this.lineList = this.handleTree(response.data, \"lineId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        lineId: null,\r\n        parentId: null,\r\n        ancestors: null,\r\n        lineShortName:null,\r\n        lineLocalName: null,\r\n        lineEnName: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: 0\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.open = true;\r\n      if (row != undefined) {\r\n        this.form.parentId = row.lineId;\r\n      }\r\n      this.title = \"添加航线\";\r\n    },\r\n    // 用户状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm(\"确认要\" + text + \"吗？\", '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeLineStatus(row.lineId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const lineId = row.lineId || this.ids\r\n      getLine(lineId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改航线\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.lineId != null) {\r\n            updateLine(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addLine(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const lineIds = row.lineId || this.ids;\r\n      this.$confirm('是否确认删除航线编号为\"' + lineIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delLine(lineIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/line/export', {\r\n        ...this.queryParams\r\n      }, `line_${new Date().getTime()}.xlsx`)\r\n    },\r\n    ParentId(val) {\r\n      this.form.parentId = val\r\n    },\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;AAsJA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;MACA;MACAC,WAAA;QACAC,QAAA;QACAC,aAAA;QACAC,aAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAb,UAAA,WAAAA,WAAAc,CAAA;MACA,IAAAA,CAAA;QACA,KAAAnB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAqB,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAtB,OAAA;MACA,IAAAuB,cAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAjB,QAAA,GAAAiB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAyB,KAAA,CAAAtB,OAAA;MACA;IACA;IACA,cACA2B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAApB,YAAA;MACA,KAAAC,WAAA,SAAAA,WAAA;MACA,KAAAoB,SAAA;QACAD,MAAA,CAAApB,YAAA;MACA;IACA;IACA;IACAsB,MAAA,WAAAA,OAAA;MACA,KAAAvB,IAAA;MACA,KAAAwB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAhB,IAAA;QACAiB,MAAA;QACArB,QAAA;QACAsB,SAAA;QACArB,aAAA;QACAC,aAAA;QACAC,UAAA;QACAoB,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAjC,WAAA,CAAAkC,OAAA;MACA,KAAAxB,OAAA;IACA;IACA,aACAyB,UAAA,WAAAA,WAAA;MACA,KAAAH,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAG,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAhB,KAAA;MACA,KAAAxB,IAAA;MACA,IAAAwC,GAAA,IAAAC,SAAA;QACA,KAAAjC,IAAA,CAAAJ,QAAA,GAAAoC,GAAA,CAAAf,MAAA;MACA;MACA,KAAA1B,KAAA;IACA;IACA;IACA2C,kBAAA,WAAAA,mBAAAF,GAAA;MAAA,IAAAG,MAAA;MACA,IAAAC,IAAA,GAAAJ,GAAA,CAAAK,MAAA;MACA,KAAAC,QAAA,SAAAF,IAAA;QAAAG,WAAA;MAAA,GAAA9B,IAAA;QACA,WAAA+B,sBAAA,EAAAR,GAAA,CAAAf,MAAA,EAAAe,GAAA,CAAAK,MAAA;MACA,GAAA5B,IAAA;QACA0B,MAAA,CAAAM,MAAA,CAAAC,UAAA,CAAAN,IAAA;MACA,GAAAO,KAAA;QACAX,GAAA,CAAAK,MAAA,GAAAL,GAAA,CAAAK,MAAA;MACA;IACA;IACA,aACAO,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,KAAA7B,KAAA;MACA,IAAAC,MAAA,GAAAe,GAAA,CAAAf,MAAA,SAAA/B,GAAA;MACA,IAAA4D,aAAA,EAAA7B,MAAA,EAAAR,IAAA,WAAAC,QAAA;QACAmC,MAAA,CAAA7C,IAAA,GAAAU,QAAA,CAAA5B,IAAA;QACA+D,MAAA,CAAArD,IAAA;QACAqD,MAAA,CAAAtD,KAAA;MACA;IACA;IACA,WACAwD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAhD,IAAA,CAAAiB,MAAA;YACA,IAAAmC,gBAAA,EAAAJ,MAAA,CAAAhD,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACAsC,MAAA,CAAAP,MAAA,CAAAC,UAAA;cACAM,MAAA,CAAAxD,IAAA;cACAwD,MAAA,CAAA3C,OAAA;YACA;UACA;YACA,IAAAgD,aAAA,EAAAL,MAAA,CAAAhD,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACAsC,MAAA,CAAAP,MAAA,CAAAC,UAAA;cACAM,MAAA,CAAAxD,IAAA;cACAwD,MAAA,CAAA3C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiD,YAAA,WAAAA,aAAAtB,GAAA;MAAA,IAAAuB,MAAA;MACA,IAAAC,OAAA,GAAAxB,GAAA,CAAAf,MAAA,SAAA/B,GAAA;MACA,KAAAoD,QAAA,kBAAAkB,OAAA;QAAAjB,WAAA;MAAA,GAAA9B,IAAA;QACA,WAAAgD,aAAA,EAAAD,OAAA;MACA,GAAA/C,IAAA;QACA8C,MAAA,CAAAlD,OAAA;QACAkD,MAAA,CAAAd,MAAA,CAAAC,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACAe,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAAlE,WAAA,WAAAmE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAAC,GAAA;MACA,KAAAlE,IAAA,CAAAJ,QAAA,GAAAsE,GAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAN,OAAA,GAAAO,QAAA"}]}