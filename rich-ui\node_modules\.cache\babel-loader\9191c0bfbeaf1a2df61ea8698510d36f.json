{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\currency.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\currency.vue", "mtime": 1702436066412}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9zdG9yZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC9zdG9yZSIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIG5hbWU6ICdjdXJyZW5jeScsCiAgcHJvcHM6IFsnc2NvcGUnXSwKICBjcmVhdGVkOiBmdW5jdGlvbiBjcmVhdGVkKCkgewogICAgdmFyIF90aGlzID0gdGhpczsKICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmN1cnJlbmN5TGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5jdXJyZW5jeSkgewogICAgICBfc3RvcmUuZGVmYXVsdC5kaXNwYXRjaCgnZ2V0Q3VycmVuY3lMaXN0JykudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXMub3B0aW9ucyA9IF90aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmN1cnJlbmN5TGlzdDsKICAgICAgfSk7CiAgICB9IGVsc2UgewogICAgICB0aGlzLm9wdGlvbnMgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmN1cnJlbmN5TGlzdDsKICAgIH0KICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBvcHRpb25zOiBbXSwKICAgICAgc2l6ZTogdGhpcy4kc3RvcmUuc3RhdGUuYXBwLnNpemUgfHwgJ21pbmknCiAgICB9OwogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "name", "props", "created", "_this", "$store", "state", "data", "currencyList", "length", "redisList", "currency", "store", "dispatch", "then", "options", "size", "app", "exports", "default", "_default"], "sources": ["src/views/system/freight/currency.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <h6 style=\"margin: 0\">{{ scope.row.currencyCode }}</h6>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  name: 'currency',\r\n  props: ['scope'],\r\n  created() {\r\n    if (this.$store.state.data.currencyList.length == 0 || this.$store.state.data.redisList.currency) {\r\n      store.dispatch('getCurrencyList').then(() => {\r\n        this.options = this.$store.state.data.currencyList\r\n      })\r\n    } else {\r\n      this.options = this.$store.state.data.currencyList\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      options: [],\r\n      size: this.$store.state.app.size || 'mini'\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;AAOA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;eAEA;EACAC,IAAA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,YAAA,CAAAC,MAAA,cAAAJ,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAG,SAAA,CAAAC,QAAA;MACAC,cAAA,CAAAC,QAAA,oBAAAC,IAAA;QACAV,KAAA,CAAAW,OAAA,GAAAX,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,YAAA;MACA;IACA;MACA,KAAAO,OAAA,QAAAV,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,YAAA;IACA;EACA;EACAD,IAAA,WAAAA,KAAA;IACA;MACAQ,OAAA;MACAC,IAAA,OAAAX,MAAA,CAAAC,KAAA,CAAAW,GAAA,CAAAD,IAAA;IACA;EACA;AACA;AAAAE,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}