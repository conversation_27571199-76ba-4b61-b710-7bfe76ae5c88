{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\AirComponent.vue?vue&type=style&index=0&id=a3c4bcf6&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\AirComponent.vue", "mtime": 1754881964225}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQpAaW1wb3J0ICdAL2Fzc2V0cy9zdHlsZXMvb3AtZG9jdW1lbnQnOw0KDQovLyBBaXLnu4Tku7bnibnlrprmoLflvI8NCi5haXItY29tcG9uZW50IHsNCiAgd2lkdGg6IDEwMCU7DQoNCiAgLmFpci1pdGVtIHsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICB9DQoNCiAgLnNlcnZpY2UtYmFyIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgbWFyZ2luLXRvcDogMTBweDsNCiAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgIHdpZHRoOiAxMDAlOw0KDQogICAgLnNlcnZpY2UtdG9nZ2xlLWljb24gew0KICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7DQogICAgfQ0KDQogICAgLnNlcnZpY2UtdGl0bGUtZ3JvdXAgew0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICB3aWR0aDogMjUwcHg7DQoNCiAgICAgIC5zZXJ2aWNlLXRpdGxlIHsNCiAgICAgICAgbWFyZ2luOiAwOw0KICAgICAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgICB9DQoNCiAgICAgIC5zZXJ2aWNlLWFjdGlvbi1idG4gew0KICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuYm9va2luZy1iaWxsLWNvbnRhaW5lciB7DQogICAgICBtYXJnaW4tbGVmdDogYXV0bzsNCg0KICAgICAgLmJvb2tpbmctYmlsbC1saW5rIHsNCiAgICAgICAgY29sb3I6IGJsdWU7DQogICAgICAgIHBhZGRpbmc6IDA7DQogICAgICAgIHRleHQtZGVjb3JhdGlvbjogbm9uZTsNCiAgICAgICAgY3Vyc29yOiBwb2ludGVyOw0KICAgICAgfQ0KICAgIH0NCiAgfQ0KDQogIC5zZXJ2aWNlLWNvbnRlbnQtYXJlYSB7DQogICAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgICBkaXNwbGF5OiAtd2Via2l0LWJveDsNCg0KICAgIC5zZXJ2aWNlLWluZm8tY29sIHsNCiAgICAgIC5lbC1mb3JtLWl0ZW0gew0KICAgICAgICBtYXJnaW4tYm90dG9tOiAxMHB4Ow0KICAgICAgfQ0KDQogICAgICAuY2FuY2VsLWJ0biB7DQogICAgICAgIGNvbG9yOiByZWQ7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLy8g5LyY5YyW6KGo5Y2V6L6T5YWl5qGG5qC35byPDQogIC5lbC1pbnB1dCB7DQogICAgd2lkdGg6IDEwMCU7DQogIH0NCg0KICAvLyDkvJjljJbml6XmnJ/pgInmi6nlmajmoLflvI8NCiAgLmVsLWRhdGUtcGlja2VyIHsNCiAgICB3aWR0aDogMTAwJTsNCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["AirComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmlBA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "AirComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"air-component\">\r\n    <!--空运-->\r\n    <div v-for=\"(item, index) in airList\" :key=\"`air-${index}`\" class=\"air-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                空运-AIR\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addAir\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpAir(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)\"\r\n              :payable=\"getPayable(10)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item,$event)\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"handleBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  :class=\"{ 'disable-form': form.sqdPsaNo }\"\r\n                  :disabled=\"!!form.sqdPsaNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(2, 10, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"订舱状态\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input\r\n                      :value=\"getBookingStatus(item.bookingStatus)\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                      placeholder=\"订舱状态\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                      class=\"cancel-btn\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel(item)\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\">\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\" prop=\"supplierId\">\r\n                    <el-input :class=\"item.sqdPsaNo?'disable-form':''\" :disabled=\"item.sqdPsaNo?true:false\"\r\n                              :value=\"item.sqdPsaNo\"\r\n                              @focus=\"selectPsaBookingOpen(item)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"BKG号码\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.soNo\"\r\n                              :class=\"disabled || getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"BKG号码\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"提单号码\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.blNo\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"提单号码\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"航空公司\" prop=\"carrierIds\">\r\n                    <treeselect v-model=\"item.carrierId\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disable-branch-nodes=\"true\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                :disabled-fuzzy-matching=\"true\" :flat=\"false\"\r\n                                :flatten-search-results=\"true\" :multiple=\"false\"\r\n                                :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                                placeholder=\"选择承运人\" @select=\"selectCarrier(item,$event)\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          (node.raw.carrierIntlCode != null) ? node.raw.carrierIntlCode : \" \"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程航班\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.firstVessel\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"头程航班\" style=\"width: 100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"二程航班\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.basicVessel\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"二程航班\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"航班时效\" style=\"padding-right: 0;\">\r\n                    <el-input v-model=\"item.inquiryScheduleSummary\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"航班时效\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程截重\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.firstCyClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              clearable placeholder=\"头程截重\"\r\n                              style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截关时间\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.cvClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"截关时间\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD1\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.etd\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ETD1\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD2\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.basicFinalGateinTime\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ETD2\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ETA\" style=\"padding-right: 0;\">\r\n                    <el-date-picker v-model=\"item.eta\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ETA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截补料\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.siClosingTime\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"截补料\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"AMS/ENS\" prop=\"revenueTons\">\r\n                    <el-input v-model=\"item.sqdAmsEnsPostStatus\"\r\n                              :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                              :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                              placeholder=\"AMS/ENS\" style=\"width:100%\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ATD1\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"item.podEta\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATD1\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    @change=\"addProgress(getServiceObject(10).rsOpLogList,15)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ATD2\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"item.basicEtd\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATD2\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ATA\" prop=\"revenueTons\">\r\n                    <el-date-picker v-model=\"item.destinationPortEta\"\r\n                                    :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                    :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                    clearable\r\n                                    placeholder=\"ATA\" style=\"width:100%\"\r\n                                    type=\"date\"\r\n                                    value-format=\"yyyy-MM-dd\"\r\n                                    @change=\"addProgress(getServiceObject(10).rsOpLogList,18)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"19\">\r\n                  <el-form-item label=\"订舱备注\" prop=\"revenueTons\">\r\n                    <div style=\"display: flex\">\r\n                      <el-input v-model=\"item.bookingChargeRemark\"\r\n                                :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                placeholder=\"订舱费用备注\" type=\"textarea\"\r\n                      />\r\n                      <el-input v-model=\"item.bookingAgentRemark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                                :class=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)?'disable-form':''\"\r\n                                :disabled=\"disabled|| getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                                placeholder=\"订舱备注\"\r\n                                type=\"textarea\"\r\n                      />\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getServiceInstanceDisable(item.rsServiceInstances) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\" :process-type=\"4\" :service-type=\"10\"\r\n                @deleteItem=\"item.rsOpLogList=item.rsOpLogList.filter(item=>{return item!=$event})\"\r\n                @return=\"item.rsOpLogList=$event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list :a-t-d=\"form.podEta\"\r\n                         :charge-data=\"item.rsChargeList\"\r\n                         :company-list=\"companyList\"\r\n                         :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)|| disabled\" :hiddenSupplier=\"booking\"\r\n                         :is-receivable=\"false\" :open-charge-list=\"true\"\r\n                         :pay-detail-r-m-b=\"item.payableRMB\"\r\n                         :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n                         :pay-detail-u-s-d=\"item.payableUSD\"\r\n                         :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n                         :service-type-id=\"10\" @copyFreight=\"copyFreight($event)\"\r\n                         @deleteAll=\"item.rsChargeList=[]\"\r\n                         @deleteItem=\"item.rsChargeList=item.rsChargeList.filter(item=>{return item!=$event})\"\r\n                         @return=\"calculateCharge(10,$event,item)\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"item.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(item)\"\r\n              @deleteItem=\"item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(10,$event,item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"AirComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 空运数据列表\r\n    airList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    carrierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 方法函数\r\n    carrierNormalizer: {\r\n      type: Function,\r\n      default: () => {\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 处理订舱单生成\r\n    handleBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addAir() {\r\n      this.$emit(\"addAir\")\r\n    },\r\n    deleteRsOpAir(item) {\r\n      this.$emit(\"deleteRsOpAir\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    getBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    selectCarrier(item, event) {\r\n      this.$emit(\"selectCarrier\", item, event)\r\n    },\r\n    addProgress(logList, type) {\r\n      this.$emit(\"addProgress\", logList, type)\r\n    },\r\n    psaBookingCancel(item) {\r\n      this.$emit(\"psaBookingCancel\", item)\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(type) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(type) : null\r\n    },\r\n    getBookingStatus(status) {\r\n      return this.$parent.getBookingStatus ? this.$parent.getBookingStatus(status) : \"\"\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.$parent.getServiceObject ? this.$parent.getServiceObject(serviceTypeId) : {}\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// Air组件特定样式\r\n.air-component {\r\n  width: 100%;\r\n\r\n  .air-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .cancel-btn {\r\n        color: red;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}