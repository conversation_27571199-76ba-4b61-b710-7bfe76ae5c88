{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opE.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\opE.vue", "mtime": 1739006109908}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_quotation", "require", "_store", "_interopRequireDefault", "_js<PERSON><PERSON>yin", "_vueTreeselect", "_logisticsNoInfo", "_preCarriageNoInfo", "_opHistory", "_receivablePayable", "_audit", "_rctold", "_booking", "_rich", "name", "dicts", "props", "components", "PreCarriageNoInfo", "LogisticsNoInfo", "opHistory", "receivablePayable", "audit", "Treeselect", "data", "opList", "businessList", "belongList", "carrierList", "locationOptions", "goodsValue", "grossWeight", "list", "Set", "editOpHistory", "size", "$store", "state", "app", "title", "logisticsType", "carrierId", "carrierIds", "relationClientIds", "verifyPsaId", "salesId", "salesAssistantId", "salesObserverId", "opId", "bookingOpId", "docOpId", "opObserverId", "openGenerateRct", "psaVerify", "logistics", "basicInfo", "noInfo", "type", "open", "loading", "preCarriage", "importClearance", "exportDeclaration", "logisticsProcess", "logisticsNoInfo", "showLogisticsNoInfo", "openLogisticsNoInfo", "logisticsOpHistory", "logisticsReceivablePayableList", "preCarriageNoInfo", "showPreCarriageNoInfo", "openPreCarriageNoInfo", "preCarriageOpHistory", "preCarriageReceivablePayableList", "openExportDeclarationNoInfo", "exportDeclarationNoInfo", "showExportDeclarationNoInfo", "exportDeclarationOpHistory", "exportDeclarationReceivablePayableList", "openImportPassNoInfo", "importClearanceNoInfo", "showImportClearanceNoInfo", "importClearanceOpHistory", "importClearanceReceivablePayableList", "bookingList", "rctList", "form", "logisticsBasicInfo", "preCarriageBasicInfo", "exportDeclarationBasicInfo", "importClearanceBasicInfo", "rct", "leadingCharacter", "month", "noNum", "rctNo", "pageNum", "pageSize", "total", "rules", "watch", "formLogisticsTypeId", "n", "_this", "serviceTypeList", "length", "redisList", "serviceType", "store", "dispatch", "then", "getType", "beforeMount", "_this2", "reset", "getBookingList", "loadSelection", "getRctList", "$route", "query", "id", "getQuotation", "bId", "getBookingDetail", "rId", "getRctDetail", "methods", "clientList", "client", "supplierList", "supplier", "loadOp", "loadCarrier", "loadSales", "loadBusinesses", "_this3", "_this4", "salesList", "_this5", "serviceTypeCarriers", "_this6", "businessesList", "staffNormalizer", "node", "children", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "label", "isDisabled", "staffId", "undefined", "deptId", "carrierNormalizer", "carrier", "carrierLocalName", "carrierEnName", "serviceLocalName", "serviceEnName", "carrierIntlCode", "serviceTypeId", "_this7", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "response", "logisticsTypeId", "clientId", "companyId", "clientRoleId", "companyRoleId", "clientContactor", "extStaffName", "clientContactorTel", "extStaffPhoneNum", "clientContactorEmail", "extStaffEmailEnterprise", "quotationNo", "richNo", "quotationDate", "Date", "impExpTypeId", "imExPort", "goodsNameSummary", "cargoName", "cargoPrice", "goodsCurrencyId", "cargoCurrencyId", "weightUnitId", "cargoUnitId", "polId", "departureId", "destinationPortId", "destinationId", "transitPortId", "transportationTermsId", "revenueTons", "newBookingRemark", "remark", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "done", "a", "value", "_iterator2", "_step2", "b", "_iterator3", "_step3", "c", "err", "e", "f", "cIds", "_iterator4", "_step4", "v", "_iterator7", "_step7", "includes", "add", "_iterator8", "_step8", "for<PERSON>ach", "push", "summary", "_iterator5", "quotationFreight", "_step5", "qf", "charge", "quotationCurrency", "toLowerCase", "Number", "quotationPrice", "unit", "showClient", "showSupplier", "showQuotationCharge", "showCostCharge", "showQuotationCurrency", "showCostCurrency", "showQuotationUnit", "showCostUnit", "quotationStrategyId", "strategyId", "costStrategyId", "quotationChargeId", "chargeId", "quotationCharge", "costChargeId", "costCharge", "quotationUnitId", "unitId", "quotationUnit", "costUnitId", "costUnit", "costExchangeRate", "exchangeRate", "quotationExchangeRate", "quotationTaxRate", "taxRate", "costTaxRate", "company", "supplierId", "quotationTotal", "quotationAmount", "costTotal", "costPrice", "costAmount", "typeId", "quotation<PERSON><PERSON><PERSON>y", "characteristics", "_iterator6", "_step6", "cargoType", "locationDeparture", "locationDestination", "info", "essentialDetail", "inquiryNotice", "serviceTypeIds", "cargoTypeIds", "preCarriageRegionIds", "locationLoadingIds", "roleIds", "stop", "_this8", "_callee2", "_callee2$", "_context2", "listBooking", "rows", "_this9", "_callee3", "_callee3$", "_context3", "getBooking", "rr", "split", "_iterator9", "_step9", "_iterator10", "_step10", "_iterator11", "_step11", "_iterator12", "_step12", "_iterator13", "_step13", "_iterator14", "_step14", "_iterator15", "_step15", "_iterator16", "_step16", "_iterator17", "_step17", "_iterator18", "_step18", "rsBookingLogisticsTypeBasicInfo", "rsBookingReceivablePayableList", "rsBookingPreCarriageBasicInfo", "rsBookingExportDeclarationBasicInfo", "rsBookingImportClearanceBasicInfo", "_this10", "_callee4", "_callee4$", "_context4", "listRct", "_this11", "_callee5", "_callee5$", "_context5", "getRct", "_iterator19", "_step19", "_iterator20", "_step20", "_iterator21", "_step21", "_iterator22", "_step22", "_iterator23", "_step23", "_iterator24", "_step24", "_iterator25", "_step25", "_iterator26", "_step26", "_iterator27", "_step27", "_iterator28", "_step28", "rsRctLogisticsTypeBasicInfo", "rsRctReceivablePayableList", "rsOperationalProcessList", "rsRctPreCarriageBasicInfo", "rsRctExportDeclarationBasicInfo", "rsRctImportClearanceBasicInfo", "getServiceTypeList", "val", "_this12", "_callee6", "_iterator29", "_step29", "_iterator30", "_step30", "t", "_callee6$", "_context6", "clear", "$forceUpdate", "_iterator31", "_step31", "_iterator32", "_step32", "getRelationClientIds", "handleSelectCarrierIds", "handleDeselectCarrierIds", "filter", "item", "generateRct", "_this13", "getRctMon", "num", "toString", "j", "i", "date", "getMonth", "year", "getFullYear", "substring", "confirmRct", "autoCompletion", "re", "test", "replace", "str", "n1", "concat", "$message", "warning", "getNoInfo", "logisticsNo", "details", "preCarriageNo", "_iterator33", "_step33", "_loop", "numInfo", "vKey", "cancel", "rctId", "rctOpDate", "newBookingNo", "newBookingTime", "psaVerifyTime", "urgencyDegree", "paymentTypeId", "releaseTypeId", "processStatusId", "tradingPaymentChannelId", "tradingTermsId", "logisticsTermsId", "contractNo", "clientInvoiceNo", "packageQuantity", "volume", "volumeUnitId", "maxWeight", "schedule", "validTimeForm", "isMblNeeded", "mblNo", "isUnderAgreementMbl", "isCustomsIntransitMbl", "isSwitchMbl", "isDividedMbl", "mblIssueTypeId", "mblGetWayId", "mblReleaseWayId", "isHblNeeded", "hblNoList", "isUnderAgreementHbl", "isCustomsIntransitHbl", "isSwitchHbl", "isDividedHbl", "hblIssueTypeId", "hblGetWayId", "hblReleaseWayId", "inquiryInnerRemark", "opLeaderRemark", "opInnerRemark", "agreementTypeId", "agreementNo", "readOnly", "createTime", "updateTime", "deleteTime", "deleteStatus", "deleteBy", "updateBy", "createBy", "soNo", "containerNo", "sealNo", "bookingDetail", "precarriageTime", "cvClosingTime", "cvDeclaringTime", "vgm", "siClosingTime", "trailer", "shipName", "shipTime", "podETA", "telexReleaseType", "isReleasable", "sendToAgent", "boatId", "logisticsTypeInfoId", "firstCvClosingTime", "firstCyOpenTime", "firstCyClosingTime", "firstEtd", "firstVessel", "firstVoyage", "localBasicPortId", "basicClosingTime", "basicFinalGateinTime", "basicEtd", "basicVessel", "basicVoyage", "podId", "podEta", "destinationPortEta", "opConfirmed", "opConfirmedId", "opConfirmedName", "opConfirmedDate", "financeConfirmed", "financeConfirmedId", "financeConfirmedName", "financeConfirmedDate", "salesConfirmed", "salesConfirmedId", "salesConfirmedName", "salesConfirmedDate", "supplierConfirmed", "supplierConfirmedId", "supplierConfirmedName", "supplierConfirmedDate", "invoiceQueryNo", "preCarriageInfoId", "preCarriageRegionId", "preCarriage<PERSON><PERSON><PERSON>", "preCarriageTime", "preCarriageContact", "preCarriageTel", "preCarriageRemark", "exportDeclarationId", "dispatchRegionId", "dispatchAddress", "dispatchTime", "dispatchContact", "dispatchTel", "dispatchRemark", "dispatchDriverName", "dispatchDriverTel", "dispatchTruckNo", "dispatchTruckRemark", "importClearanceId", "exportCustomsTypeId", "importCustomsTypeId", "resetForm", "submitForm", "_this14", "isPsaVerified", "parseTime", "$refs", "validate", "valid", "updateRct", "saveAll", "$modal", "msgSuccess", "addRct", "bookingId", "updateBooking", "addBooking", "rejected", "_this15", "$confirm", "confirmButtonText", "cancelButtonText", "res", "saveLogistics", "savePreCarriage", "saveExportDeclaration", "saveImportClearance", "_this16", "error", "saveBookingLogistics", "success", "rsRctLogisticsNoInfos", "saveRctLogistics", "_this17", "saveBookingPreCarriage", "rsRctPreCarriageNoInfos", "saveRctPreCarriage", "_this18", "saveBookingExportDeclaration", "saveRctExportDeclaration", "_this19", "saveBookingImportClearance", "saveRctImportClearance", "changeMaster", "console", "log", "exports", "_default"], "sources": ["src/views/system/document/opE.vue"], "sourcesContent": ["<template>\r\n  <div style=\"margin: 15px;width: auto\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"63px\">\r\n      <el-row>\r\n        <el-col style=\"width: 75%;margin-right: 10px\">\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :class=\"type=='booking'?'booking':''\" :span=\"4\">\r\n              <div style=\"margin: 0 0 15px;padding:0;font-size: 40px;text-align: center;border: 1px solid #76933C;\">\r\n                {{ \"临时操作单\" }}\r\n              </div>\r\n              <el-form-item label=\"操作单号\" prop=\"rctNo\">\r\n                <el-input v-model=\"form.rctNo\" :disabled=\"type=='booking'||psaVerify\" placeholder=\"操作单号\"\r\n                          @focus=\"generateRct(false)\"\r\n                />\r\n                <!--                生成操作单号弹出层-->\r\n                <el-dialog\r\n                  v-dialogDrag\r\n                  v-dialogDragWidth\r\n                  :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n                  :visible.sync=\"openGenerateRct\" append-to-body\r\n                  title=\"新增操作单号\" width=\"350px\"\r\n                >\r\n                  <el-form ref=\"form\" :model=\"rct\" :rules=\"rules\" class=\"edit\" label-width=\"65px\">\r\n                    <el-form-item label=\"单号规则\">\r\n                      <el-input v-model=\"rct.rules\" disabled placeholder=\"前导字符+2位年份+2位月份+4位序列\"/>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"前导字符\" prop=\"leadingCharacter\">\r\n                      <el-input v-model=\"rct.leadingCharacter\" disabled placeholder=\"前导字符\"/>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"所属月份\">\r\n                      <el-radio-group v-model=\"rct.month\">\r\n                        <el-radio-button label=\"1\">本月单号</el-radio-button>\r\n                        <el-radio-button label=\"2\">显示下月</el-radio-button>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"单号序列\">\r\n                      <el-radio-group v-model=\"rct.noNum\">\r\n                        <el-radio-button label=\"1\">自然序列</el-radio-button>\r\n                        <el-radio-button label=\"2\">手动分配</el-radio-button>\r\n                      </el-radio-group>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"单号预览\">\r\n                      <div style=\"display: flex\">\r\n                        <el-input v-model=\"rct.rctNo\" :disabled=\"rct.noNum=='1'\"/>\r\n                        <el-button size=\"mini\" type=\"success\" @click=\"generateRct(true)\">\r\n                          {{ rct.noNum == '1' ? '生成' : '' }}\r\n                          {{ rct.noNum == '2' ? '校验' : '' }}\r\n                        </el-button>\r\n                      </div>\r\n                    </el-form-item>\r\n                  </el-form>\r\n                  <div slot=\"footer\" class=\"dialog-footer\">\r\n                    <el-button size=\"mini\" type=\"primary\" @click=\"confirmRct\">确 定</el-button>\r\n                    <el-button size=\"mini\" @click=\"cancel\">取 消</el-button>\r\n                  </div>\r\n                </el-dialog>\r\n              </el-form-item>\r\n              <el-form-item label=\"操作日期\" prop=\"rctOpDate\">\r\n                <el-date-picker v-model=\"form.rctOpDate\" :disabled=\"type=='booking'||psaVerify\"\r\n                                clearable\r\n                                placeholder=\"操作日期\"\r\n                                style=\"width:100%\"\r\n                                type=\"date\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item :class=\"psaVerify?'booking':''\" label=\"操作员\" prop=\"opId\">\r\n                <treeselect v-model=\"opId\" :disable-branch-nodes=\"true\" :disabled=\"psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"staffNormalizer\"\r\n                            :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\" :show-count=\"true\" placeholder=\"操作员\"\r\n                            @input=\"$event==undefined?form.opId = null:null\" @open=\"loadOp\"\r\n                            @select=\"form.opId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item :class=\"type=='booking'?'booking':''\" label=\"订舱员\" prop=\"bookingOpId\">\r\n                <treeselect v-model=\"bookingOpId\" :disable-branch-nodes=\"true\" :disabled=\"type=='booking'||psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"staffNormalizer\"\r\n                            :options=\"opList.filter(v => {return v.role.roleLocalName=='订舱员'})\" :show-count=\"true\" placeholder=\"订舱员\"\r\n                            @input=\"$event==undefined?form.bookingOpId = null:null\" @open=\"loadOp\"\r\n                            @select=\"form.bookingOpId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item :class=\"type=='booking'?'booking':''\" label=\"单证员\" prop=\"docOpId\">\r\n                <treeselect v-model=\"docOpId\" :disable-branch-nodes=\"true\" :disabled=\"type=='booking'||psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"staffNormalizer\"\r\n                            :options=\"opList.filter(v => {return v.role.roleLocalName=='单证员'})\" :show-count=\"true\" placeholder=\"单证员\"\r\n                            @input=\"$event==undefined?form.docOpId = null:null\" @open=\"loadOp\"\r\n                            @select=\"form.docOpId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item :class=\"type=='booking'?'booking':''\" label=\"协助操作\" prop=\"opObserverId\">\r\n                <treeselect v-model=\"opObserverId\" :disable-branch-nodes=\"true\" :disabled=\"type=='booking'||psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"staffNormalizer\" :options=\"opList\" :show-count=\"true\"\r\n                            placeholder=\"协助操作\"\r\n                            @input=\"$event==undefined?form.opObserverId = null:null\" @open=\"loadOp\"\r\n                            @select=\"form.opObserverId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"订舱单号\" prop=\"newBookingNo\">\r\n                <el-input v-model=\"form.newBookingNo\" :disabled=\"psaVerify\" placeholder=\"订舱申请单号\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"订舱日期\" prop=\"newBookingTime\">\r\n                <el-date-picker v-model=\"form.newBookingTime\" :disabled=\"psaVerify\"\r\n                                clearable\r\n                                placeholder=\"订舱申请单日期\" style=\"width:100%\"\r\n                                type=\"date\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n              <el-form-item label=\"报价单号\" prop=\"quotationNo\">\r\n                <el-input v-model=\"form.quotationNo\" :disabled=\"psaVerify\" placeholder=\"报价单号\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"报价日期\" prop=\"quotationDate\">\r\n                <el-date-picker v-model=\"form.quotationDate\" :disabled=\"psaVerify\"\r\n                                clearable\r\n                                placeholder=\"报价日期\" style=\"width:100%\"\r\n                                type=\"date\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"业务员\" prop=\"salesId\">\r\n                <treeselect v-model=\"salesId\" :disable-branch-nodes=\"true\" :disabled=\"psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"belongList\" :show-count=\"true\" placeholder=\"业务员\"\r\n                            @input=\"$event==undefined?form.salesId = null:null\" @open=\"loadSales\"\r\n                            @select=\"form.salesId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"业务助理\" prop=\"salesAssistantId\">\r\n                <treeselect v-model=\"salesAssistantId\" :disable-branch-nodes=\"true\" :disabled=\"psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"belongList\" :show-count=\"true\" placeholder=\"业务助理\"\r\n                            @input=\"$event==undefined?form.salesAssistantId=null:null\" @open=\"loadSales\"\r\n                            @select=\"form.salesAssistantId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"协助业务\" prop=\"salesObserverId\">\r\n                <treeselect v-model=\"salesObserverId\" :disable-branch-nodes=\"true\" :disabled=\"psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"belongList\" :show-count=\"true\" placeholder=\"协助业务\"\r\n                            @input=\"$event==undefined?form.salesObserverId=null:null\" @open=\"loadSales\"\r\n                            @select=\"form.salesObserverId=$event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :class=\"type=='booking'&&!psaVerify?'booking':''\" :span=\"4\">\r\n              <el-form-item label=\"商务审核\" prop=\"verifyPsaId\">\r\n                <treeselect v-model=\"verifyPsaId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                            :show-count=\"true\" placeholder=\"商务\"\r\n                            @input=\"$event==undefined?form.verifyPsaId=null:null\" @open=\"loadBusinesses\"\r\n                            @select=\"form.verifyPsaId = $event.staffId\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + ' ' + node.raw.staff.staffGivingEnName : ''\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n              <el-form-item label=\"审核时间\" prop=\"psaVerifyTime\">\r\n                <el-date-picker v-model=\"form.psaVerifyTime\" clearable\r\n                                placeholder=\"商务审核时间\"\r\n                                style=\"width:100%\"\r\n                                type=\"date\"\r\n                                value-format=\"yyyy-MM-dd\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"紧急程度\" prop=\"urgencyDegree\">\r\n                <el-input v-model=\"form.urgencyDegree\" :disabled=\"psaVerify\" placeholder=\"紧急程度\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"收付方式\" prop=\"paymentTypeId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.paymentTypeId\" :placeholder=\"'收付方式'\"\r\n                             :type=\"'paymentType'\" @return=\"form.paymentTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"放货方式\" prop=\"releaseTypeId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.releaseTypeId\" :placeholder=\"'放货方式'\"\r\n                             :type=\"'releaseType'\" @return=\"form.releaseTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"进度状态\" prop=\"processStatusId\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.processStatusId\" :placeholder=\"'进度状态'\"\r\n                             :type=\"'processStatus'\" @return=\"form.processStatusId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"委托单位\" prop=\"clientId\">\r\n                <tree-select v-if=\"$store.state.data.clientList.length>0\" :disabled=\"psaVerify\" :flat=\"false\"\r\n                             :multiple=\"false\" :pass=\"form.clientId\" :placeholder=\"'委托单位'\"\r\n                             :type=\"'client'\" @return=\"form.clientId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"客户角色\" prop=\"clientRoleId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.clientRoleId\" :placeholder=\"'客户角色'\"\r\n                             :type=\"'companyRole'\" @return=\"form.clientRoleId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"联系人\" prop=\"clientContactor\">\r\n                <el-input v-model=\"form.clientContactor\" :disabled=\"psaVerify\" placeholder=\"联系人称谓\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"电话\" prop=\"clientContactorTel\">\r\n                <el-input v-model=\"form.clientContactorTel\" placeholder=\"联系人电话\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"邮箱\" prop=\"clientContactorEmail\">\r\n                <el-input v-model=\"form.clientContactorEmail\" :disabled=\"psaVerify\" placeholder=\"联系人邮箱\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"关联单位\" prop=\"relationClientIds\">\r\n                <tree-select v-if=\"$store.state.data.clientList.length>0\" :disabled=\"psaVerify\" :flat=\"true\"\r\n                             :multiple=\"true\"\r\n                             :pass=\"relationClientIds\" :placeholder=\"'客户'\" :type=\"'client'\"\r\n                             @return=\"getRelationClientIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"进出口\" prop=\"impExpTypeId\">\r\n                <el-select v-model=\"form.impExpTypeId\" :disabled=\"psaVerify\" clearable filterable placeholder=\"进出口\"\r\n                           style=\"width: 100%\"\r\n                >\r\n                  <el-option :value=\"1\" label=\"出口\">出口</el-option>\r\n                  <el-option :value=\"2\" label=\"进口\">进口</el-option>\r\n                </el-select>\r\n              </el-form-item>\r\n              <el-form-item label=\"收汇方式\" prop=\"tradingPaymentChannelId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\"\r\n                             :multiple=\"false\" :pass=\"form.tradingPaymentChannelId\"\r\n                             :placeholder=\"'贸易付款方式'\"\r\n                             :type=\"'paymentChannels'\" @return=\"form.tradingPaymentChannelId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"贸易条款\" prop=\"tradingTermsId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\"\r\n                             :multiple=\"false\" :pass=\"form.tradingTermsId\"\r\n                             :placeholder=\"'贸易条款'\"\r\n                             :type=\"'tradingTerms'\" @return=\"form.tradingTermsId=$event\"\r\n                />\r\n              </el-form-item>\r\n              <el-form-item label=\"运输条款\" prop=\"logisticsTermsId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\"\r\n                             :multiple=\"false\" :pass=\"form.logisticsTermsId\"\r\n                             :placeholder=\"'运输条款'\"\r\n                             :type=\"'transportationTerms'\" @return=\"form.logisticsTermsId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"合同号\" prop=\"contractNo\">\r\n                <el-input v-model=\"form.contractNo\" :disabled=\"psaVerify\" placeholder=\"(委托单位)合同号\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"发票号\" prop=\"clientInvoiceNo\">\r\n                <el-input v-model=\"form.clientInvoiceNo\" :disabled=\"psaVerify\" placeholder=\"(委托单位)发票号\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <!--          2023-10-8新增-->\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"SO号\" prop=\"soNo\">\r\n                <el-input v-model=\"form.soNo\" :disabled=\"psaVerify\" placeholder=\"SO号\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"柜号\" prop=\"soNo\">\r\n                <el-input v-model=\"form.containerNo\" :disabled=\"psaVerify\" placeholder=\"柜号\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                <el-input v-model=\"form.sealNo\" :disabled=\"psaVerify\" placeholder=\"封号\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"订舱概述\" prop=\"bookingDetail\">\r\n                <el-input v-model=\"form.bookingDetail\" placeholder=\"订舱概述\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"装柜时间\" prop=\"precarriageTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.precarriageTime\"\r\n                  :disabled=\"psaVerify\"\r\n                  align=\"left\"\r\n                  placeholder=\"选择装柜时间\"\r\n                  size=\"small\"\r\n                  type=\"datetime\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n              <el-form-item label=\"截关时间\" prop=\"cvClosingTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.cvClosingTime\"\r\n                  :disabled=\"psaVerify\"\r\n                  placeholder=\"选择截关时间\"\r\n                  size=\"small\"\r\n                  type=\"datetime\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"报关\" prop=\"cvDeclaringTime\">\r\n                <el-input v-model=\"form.cvDeclaringTime\" :disabled=\"psaVerify\" placeholder=\"报关\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"截VGM\" prop=\"vgm\">\r\n                <el-input v-model=\"form.vgm\" :disabled=\"psaVerify\" placeholder=\"截VGM\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"截SI\" prop=\"siClosingTime\">\r\n                <el-input v-model=\"form.siClosingTime\" :disabled=\"psaVerify\" placeholder=\"截SI\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"拖车\" prop=\"trailer\">\r\n                <el-input v-model=\"form.trailer\" :disabled=\"psaVerify\" placeholder=\"拖车\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"到港时间\" prop=\"podETA\">\r\n                <el-date-picker\r\n                  v-model=\"form.podETA\"\r\n                  :disabled=\"psaVerify\"\r\n                  placeholder=\"选择到港时间\"\r\n                  size=\"small\"\r\n                  type=\"datetime\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n              <el-form-item label=\"大船时间\" prop=\"shipTime\">\r\n                <el-input v-model=\"form.shipTime\" :disabled=\"psaVerify\" placeholder=\"大船时间\" size=\"small\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"是否放货\" prop=\"isReleasable\">\r\n                <el-input v-model=\"form.isReleasable\" :disabled=\"psaVerify\" placeholder=\"是否放货\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"发资料给代理\" prop=\"sendToAgent\">\r\n                <el-input v-model=\"form.sendToAgent\" placeholder=\"发资料给代理\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"航次\" prop=\"boatId\">\r\n                <el-input v-model=\"form.boatId\" :disabled=\"psaVerify\" placeholder=\"航次\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"备注\" prop=\"remark\">\r\n                <el-input v-model=\"form.remark\" :disabled=\"psaVerify\" placeholder=\"备注\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"船名\" prop=\"shipName\">\r\n                <el-input v-model=\"form.shipName\" :disabled=\"psaVerify\" placeholder=\"船名\"/>\r\n              </el-form-item>\r\n              <el-form-item label=\"电放方式\" prop=\"telexReleaseType\">\r\n                <el-input v-model=\"form.telexReleaseType\" :disabled=\"psaVerify\" placeholder=\"电放方式\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <!--          -->\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"货名概要\" prop=\"goodsNameSummary\">\r\n                <el-input v-model=\"form.goodsNameSummary\" :disabled=\"psaVerify\" placeholder=\"货名概要\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"件数\" prop=\"packageQuantity\">\r\n                <el-input-number v-model=\"form.packageQuantity\" :controls=\"false\" :disabled=\"psaVerify\"\r\n                                 placeholder=\"总件数\" style=\"width: 100%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"毛重\" prop=\"grossWeight\">\r\n                <div style=\"display: flex\">\r\n                  <el-input v-model=\"grossWeight\" :disabled=\"psaVerify\" placeholder=\"总毛重\"\r\n                            style=\"width: 64%\" @change.native=\"autoCompletion('grossWeight')\"\r\n                  />\r\n                  <tree-select :disabled=\"psaVerify\" :pass=\"form.weightUnitId\"\r\n                               :placeholder=\"'重量单位'\" :type=\"'unit'\"\r\n                               style=\"width: 36%\" @return=\"form.weightUnitId = $event\"\r\n                  />\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"总体积\" prop=\"volume\">\r\n                <div style=\"display: flex\">\r\n                  <el-input-number v-model=\"form.volume\" :controls=\"false\" :disabled=\"psaVerify\" :precision=\"2\"\r\n                                   :step=\"0.01\" placeholder=\"总体积\" style=\"width: 64%\"\r\n                  />\r\n                  <tree-select :disabled=\"form.volumeUnitId!=null&&!psaVerify\" :pass=\"form.volumeUnitId\"\r\n                               :placeholder=\"'体积单位'\" :type=\"'unit'\"\r\n                               style=\"width: 36%\" @return=\"form.volumeUnitId=$event\"\r\n                  />\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"货物特征\" prop=\"cargoTypeIds\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\"\r\n                             :multiple=\"true\" :pass=\"form.cargoTypeIds\" :placeholder=\"'货物特征'\"\r\n                             :type=\"'cargoType'\" @return=\"form.cargoTypeIds=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"货值\" prop=\"goodsValue\">\r\n                <div style=\"display: flex\">\r\n                  <el-input v-model=\"goodsValue\" :disabled=\"psaVerify\" placeholder=\"总货值\"\r\n                            style=\"width: 64%\" @change.native=\"autoCompletion('goodsValue')\"\r\n                  />\r\n                  <tree-select :disabled=\"psaVerify\" :pass=\"form.goodsCurrencyId\"\r\n                               :placeholder=\"'货值币种'\" :type=\"'currency'\" style=\"width: 36%\"\r\n                               @return=\"form.goodsCurrencyId=$event\"\r\n                  />\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"货物限重\" prop=\"maxWeight\">\r\n                <el-input-number v-model=\"form.maxWeight\" :controls=\"false\" :disabled=\"psaVerify\" :precision=\"2\"\r\n                                 :step=\"0.01\" placeholder=\"货物限重\" style=\"width: 100%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"计费货量\" prop=\"revenueTons\">\r\n                <el-input v-model=\"form.revenueTons\" :disabled=\"psaVerify\" placeholder=\"计费货量\" style=\"width: 100%\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"物流类型\" prop=\"logisticsTypeId\">\r\n                <tree-select :disabled=\"psaVerify\" :flat=\"false\"\r\n                             :main=\"true\" :multiple=\"false\" :pass=\"form.logisticsTypeId\"\r\n                             :placeholder=\"'物流类型'\" :type=\"'serviceType'\" @return=\"form.logisticsTypeId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"启运港\" prop=\"polId\">\r\n                <location-select :check-port=\"logisticsType\" :disabled=\"psaVerify\" :load-options=\"locationOptions\"\r\n                                 :multiple=\"false\" :no-parent=\"true\"\r\n                                 :pass=\"form.polId\" :placeholder=\"'启运港'\" @return=\"form.polId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"目的港\" prop=\"destinationPortId\">\r\n                <location-select :check-port=\"logisticsType\" :disabled=\"psaVerify\" :en=\"true\"\r\n                                 :load-options=\"locationOptions\" :multiple=\"false\"\r\n                                 :pass=\"form.destinationPortId\" :placeholder=\"'目的港'\"\r\n                                 @return=\"form.destinationPortId=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"承运人\" prop=\"carrierIds\">\r\n                <treeselect v-model=\"carrierIds\" :disable-branch-nodes=\"true\" :disabled=\"psaVerify\"\r\n                            :disabled-fuzzy-matching=\"true\" :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                            :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                            placeholder=\"选择承运人\" @deselect=\"handleDeselectCarrierIds\" @open=\"loadCarrier\"\r\n                            @select=\"handleSelectCarrierIds\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.carrier.carrierIntlCode != null ? node.raw.carrier.carrierIntlCode : node.raw.carrier.carrierShortName\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{\r\n                      node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label\r\n                    }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\" style=\"margin-bottom:15px\">\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"船期\">\r\n                <el-input v-model=\"form.schedule\" :disabled=\"psaVerify\" placeholder=\"内容\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"有效期\">\r\n                <el-input v-model=\"form.validTimeForm\" :disabled=\"psaVerify\" placeholder=\"内容\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n      </el-row>\r\n\r\n      <!-- 业务报价综述,各种备注 -->\r\n      <el-row :gutter=\"10\" class=\"spc\" style=\"margin-bottom:15px;display: -webkit-box\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"业务报价综述\" label-width=\"100\" prop=\"quotationSummary\">\r\n            <el-input v-model=\"form.quotationSummary\" :autosize=\"{ minRows: 10, maxRows: 20}\" :disabled=\"psaVerify\"\r\n                      maxlength=\"150\" placeholder=\"内容\" show-word-limit type=\"textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"业务订舱备注\" label-width=\"100\" prop=\"newBookingRemark\">\r\n            <el-input v-model=\"form.newBookingRemark\" :autosize=\"{ minRows: 10, maxRows: 20}\" :disabled=\"psaVerify\"\r\n                      maxlength=\"150\" placeholder=\"业务订舱备注\" show-word-limit type=\"textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"业务须知\" label-width=\"100\" prop=\"inquiryNotice\">\r\n            <el-input v-model=\"form.inquiryNotice\" :autosize=\"{ minRows: 10, maxRows: 20}\" :disabled=\"psaVerify\"\r\n                      maxlength=\"150\" placeholder=\"内容\" show-word-limit type=\"textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"商务备注\" label-width=\"100\" prop=\"inquiryInnerRemark\">\r\n            <el-input v-model=\"form.inquiryInnerRemark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                      placeholder=\"内容\" show-word-limit type=\"textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"操作主管备注\" label-width=\"100\" prop=\"opLeaderRemark\">\r\n            <el-input v-model=\"form.opLeaderRemark\" :autosize=\"{ minRows: 10, maxRows: 20}\" :disabled=\"psaVerify\"\r\n                      maxlength=\"150\" placeholder=\"内容\" show-word-limit type=\"textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"操作备注\" label-width=\"100\" prop=\"opInnerRemark\">\r\n            <el-input v-model=\"form.opInnerRemark\" :autosize=\"{ minRows: 10, maxRows: 20}\" :disabled=\"psaVerify\"\r\n                      maxlength=\"150\" placeholder=\"内容\"\r\n                      show-word-limit type=\"textarea\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <div>\r\n        <el-form-item label=\"合约类型\" prop=\"agreementTypeId\">\r\n          <el-input v-model=\"form.agreementTypeId\" :disabled=\"psaVerify\" placeholder=\"合约类型\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"合约号\" prop=\"agreementNo\">\r\n          <el-input v-model=\"form.agreementNo\" :disabled=\"psaVerify\" placeholder=\"合约号\"/>\r\n        </el-form-item>\r\n      </div>\r\n\r\n      <!-- 保存按钮 -->\r\n      <el-col style=\"width: 100%\">\r\n        <el-row class=\"button-group\">\r\n          <!-- 订舱的时候显示 -->\r\n          <el-button v-if=\"type=='booking'\" :disabled=\"psaVerify\" size=\"medium\" type=\"primary\"\r\n                     @click=\"submitForm('saveCopy')\">\r\n            另存为\r\n          </el-button>\r\n          <!-- 操作明细的时候 -->\r\n          <el-button size=\"medium\" type=\"primary\" @click=\"submitForm\">{{ psaVerify ? '确认审核' : '保 存' }}</el-button>\r\n          <!-- 审核 -->\r\n          <el-button v-if=\"psaVerify\" size=\"medium\" type=\"warning\" @click=\"rejected\">驳 回</el-button>\r\n          <el-button :disabled=\"psaVerify\" size=\"medium\" @click=\"cancel\">重 置</el-button>\r\n        </el-row>\r\n      </el-col>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getQuotation} from '@/api/system/quotation'\r\nimport store from '@/store'\r\nimport pinyin from 'js-pinyin'\r\nimport Treeselect from '@riophae/vue-treeselect'\r\nimport '@riophae/vue-treeselect/dist/vue-treeselect.min.css'\r\nimport LogisticsNoInfo from '@/views/system/document/logisticsNoInfo'\r\nimport PreCarriageNoInfo from '@/views/system/document/preCarriageNoInfo'\r\nimport opHistory from '@/views/system/document/opHistory'\r\nimport receivablePayable from '@/views/system/document/receivablePayable'\r\nimport audit from '@/views/system/document/audit'\r\nimport {\r\n  addRct,\r\n  getRct, getRctMon,\r\n  listRct,\r\n  saveRctExportDeclaration,\r\n  saveRctImportClearance,\r\n  saveRctLogistics,\r\n  saveRctPreCarriage,\r\n  updateRct\r\n} from '@/api/system/rctold'\r\nimport {\r\n  addBooking,\r\n  getBooking,\r\n  listBooking,\r\n  saveBookingExportDeclaration,\r\n  saveBookingImportClearance,\r\n  saveBookingLogistics,\r\n  saveBookingPreCarriage,\r\n  updateBooking\r\n} from '@/api/system/booking'\r\nimport {parseTime} from '@/utils/rich'\r\n\r\nexport default {\r\n  name: 'Document',\r\n  dicts: ['sys_yes_no'],\r\n  props: ['type'],\r\n  components: {\r\n    PreCarriageNoInfo,\r\n    LogisticsNoInfo,\r\n    opHistory,\r\n    receivablePayable,\r\n    audit,\r\n    Treeselect\r\n  },\r\n  data() {\r\n    return {\r\n      //选择框数据\r\n      opList: [],\r\n      businessList: [],\r\n      belongList: [],\r\n      carrierList: [],\r\n      locationOptions: [],\r\n      goodsValue: null,\r\n      grossWeight: null,\r\n      //过度数据\r\n      list: new Set(),\r\n      editOpHistory: {},\r\n      size: this.$store.state.app.size || 'mini',\r\n      title: '',\r\n      logisticsType: 'SEA',\r\n      carrierId: null,\r\n      carrierIds: [],\r\n      relationClientIds: [],\r\n      verifyPsaId: null,\r\n      salesId: null,\r\n      salesAssistantId: null,\r\n      salesObserverId: null,\r\n      opId: null,\r\n      bookingOpId: null,\r\n      docOpId: null,\r\n      opObserverId: null,\r\n      //逻辑数据\r\n      openGenerateRct: false,\r\n      psaVerify: false,\r\n      logistics: false,\r\n      basicInfo: true,\r\n      noInfo: this.type == 'booking' ? false : true,\r\n      opHistory: this.type == 'booking' ? false : true,\r\n      receivablePayable: true,\r\n      audit: this.type == 'booking' ? false : true,\r\n      open: false,\r\n      loading: false,\r\n      preCarriage: false,\r\n      importClearance: false,\r\n      exportDeclaration: false,\r\n      logisticsProcess: [],\r\n      logisticsNoInfo: [],\r\n      showLogisticsNoInfo: [],\r\n      openLogisticsNoInfo: false,\r\n      logisticsOpHistory: [],\r\n      logisticsReceivablePayableList: [],\r\n      preCarriageNoInfo: [],\r\n      showPreCarriageNoInfo: [],\r\n      openPreCarriageNoInfo: false,\r\n      preCarriageOpHistory: [],\r\n      preCarriageReceivablePayableList: [],\r\n      openExportDeclarationNoInfo: false,\r\n      exportDeclarationNoInfo: [],\r\n      showExportDeclarationNoInfo: [],\r\n      exportDeclarationOpHistory: [],\r\n      exportDeclarationReceivablePayableList: [],\r\n      openImportPassNoInfo: false,\r\n      importClearanceNoInfo: [],\r\n      showImportClearanceNoInfo: [],\r\n      importClearanceOpHistory: [],\r\n      importClearanceReceivablePayableList: [],\r\n      // 表单参数\r\n      bookingList: [],\r\n      rctList: [],\r\n      form: {},\r\n      logisticsBasicInfo: {},\r\n      preCarriageBasicInfo: {},\r\n      exportDeclarationBasicInfo: {},\r\n      importClearanceBasicInfo: {},\r\n      rct: {\r\n        leadingCharacter: 'RCT',\r\n        month: 1,\r\n        noNum: 1,\r\n        rctNo: null\r\n      },\r\n      pageNum: 1,\r\n      pageSize: 20,\r\n      total: 0,\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n\r\n  },\r\n  watch: {\r\n    'form.logisticsTypeId'(n) {\r\n      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {\r\n        store.dispatch('getServiceTypeList').then(() => {\r\n          this.getType(n)\r\n        })\r\n      } else {\r\n        this.getType(n)\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.reset()\r\n    // 通过订舱跳转过来的\r\n    if (this.type == 'booking') {\r\n      this.getBookingList().then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 通过op组件跳转来的\r\n    if (this.type == 'op') {\r\n      this.getRctList().then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 如果是来自订舱申请则\r\n    if (this.$route.query.id && this.type == 'booking') {\r\n      this.getQuotation(this.$route.query.id).then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 如果是来自于申请订舱的修改,则会通过路由传递申请订舱单id\r\n    if (this.$route.query.bId) {\r\n      this.getBookingDetail(this.$route.query.bId).then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    // 如果是来自于操作单修改,则会通过路由传递操作单id\r\n    if (this.$route.query.rId) {\r\n      this.getRctDetail(this.$route.query.rId).then(() => {\r\n        this.loadSelection()\r\n      })\r\n    }\r\n    if (this.$route.query.psaVerify) {\r\n      this.psaVerify = true\r\n    }\r\n  },\r\n  methods: {\r\n    // 加载表单中的多选列表的选项\r\n    loadSelection() {\r\n      // 加载货运类型\r\n      if (this.$store.state.data.serviceTypeList.length == 0 || this.$store.state.data.redisList.serviceType) {\r\n        this.$store.dispatch('getServiceTypeList')\r\n      }\r\n      // 加载公司列表\r\n      if (this.$store.state.data.clientList.length == 0 || this.$store.state.data.redisList.client) {\r\n        this.$store.dispatch('getClientList')\r\n      }\r\n      // 加载供应商列表\r\n      if (this.$store.state.data.supplierList.length == 0 || this.$store.state.data.redisList.supplier) {\r\n        this.$store.dispatch('getSupplierList')\r\n      }\r\n      this.loadOp()\r\n      this.loadCarrier()\r\n      this.loadSales()\r\n      this.loadBusinesses()\r\n    },\r\n    // 查询操作部用户\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        store.dispatch('getOpList').then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n      }\r\n    },\r\n    // 查询业务部用户\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch('getSalesList').then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    // 查询船公司列表\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch('getServiceTypeCarriersList').then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    // 查询商务部用户\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch('getBusinessesList').then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + ',' + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + ',' + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + ' ' + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + ' ' + node.staff.staffGivingEnName + ',' + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + ' ' + node.serviceEnName + ',' + pinyin.getFullChars(node.serviceLocalName != undefined ? node.serviceLocalName : '')\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : '') + ' ' + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : '') + ' ' + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : '') + ',' + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : ''))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children\r\n      }\r\n    },\r\n    async getQuotation(id) {\r\n      this.reset()\r\n      await getQuotation(id).then(response => {\r\n        this.form.logisticsTypeId = response.data.logisticsTypeId\r\n        this.form.salesId = response.data.staffId\r\n        this.form.clientId = response.data.companyId\r\n        this.form.clientRoleId = response.data.companyRoleId\r\n        this.form.clientContactor = response.data.extStaffName\r\n        this.form.clientContactorTel = response.data.extStaffPhoneNum\r\n        this.form.clientContactorEmail = response.data.extStaffEmailEnterprise\r\n        this.form.quotationNo = response.data.richNo\r\n        this.form.quotationDate = new Date()\r\n        this.form.impExpTypeId = response.data.imExPort\r\n        this.form.goodsNameSummary = response.data.cargoName\r\n        this.form.goodsValue = response.data.cargoPrice\r\n        this.form.goodsCurrencyId = response.data.cargoCurrencyId\r\n        this.form.grossWeight = response.data.grossWeight\r\n        this.form.weightUnitId = response.data.cargoUnitId\r\n        this.form.polId = response.data.departureId\r\n        this.form.destinationPortId = response.data.destinationId\r\n        this.form.transitPortId = response.data.transportationTermsId\r\n        this.form.revenueTons = response.data.revenueTons\r\n        this.form.newBookingRemark = response.data.remark\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.staffId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        let cIds = new Set()\r\n        for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                if (response.data.carrierIds.includes(a.carrier.carrierId)) {\r\n                  cIds.add(a.serviceTypeId)\r\n                }\r\n              }\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                    if (response.data.carrierIds.includes(b.carrier.carrierId)) {\r\n                      cIds.add(b.serviceTypeId)\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n        let summary = ''\r\n        for (const qf of response.quotationFreight) {\r\n          summary += (qf.charge != null ? qf.charge + ':' : '') + ((qf.quotationCurrency != null ? qf.quotationCurrency.toLowerCase() : '') + Number(qf.quotationPrice) + (qf.unit != null ? '/' + qf.unit : '')) + '\\n'\r\n          qf.showClient = false\r\n          qf.showSupplier = false\r\n          qf.showQuotationCharge = false\r\n          qf.showCostCharge = false\r\n          qf.showQuotationCurrency = false\r\n          qf.showCostCurrency = false\r\n          qf.showQuotationUnit = false\r\n          qf.showCostUnit = false\r\n          qf.quotationStrategyId = qf.strategyId\r\n          qf.costStrategyId = qf.strategyId\r\n          qf.quotationChargeId = qf.chargeId\r\n          qf.quotationCharge = qf.charge\r\n          qf.costChargeId = qf.chargeId\r\n          qf.costCharge = qf.charge\r\n          qf.quotationUnitId = qf.unitId\r\n          qf.quotationUnit = qf.unit\r\n          qf.costUnitId = qf.unitId\r\n          qf.costUnit = qf.unit\r\n          qf.costExchangeRate = qf.exchangeRate\r\n          qf.quotationExchangeRate = qf.exchangeRate\r\n          qf.quotationTaxRate = qf.taxRate\r\n          qf.costTaxRate = qf.taxRate\r\n          qf.clientId = response.data.companyId\r\n          qf.client = response.data.company\r\n          qf.supplierId = qf.companyId\r\n          qf.supplier = qf.company\r\n          qf.quotationTotal = Number(qf.quotationPrice) * Number(qf.quotationAmount) * Number(qf.quotationExchangeRate) * (1 + Number(qf.quotationTaxRate) / 100)\r\n          qf.costTotal = Number(qf.costPrice) * Number(qf.costAmount) * Number(qf.costExchangeRate) * (1 + Number(qf.costTaxRate) / 100)\r\n          if (qf.typeId == '1' || qf.typeId == '2' || qf.typeId == '3') {\r\n            this.logisticsReceivablePayableList.push(qf)\r\n          }\r\n          if (qf.typeId == '4') {\r\n            this.preCarriageReceivablePayableList.push(qf)\r\n          }\r\n          if (qf.typeId == '5') {\r\n            this.exportDeclarationReceivablePayableList.push(qf)\r\n          }\r\n        }\r\n        this.form.quotationSummary = summary\r\n        let characteristics = ''\r\n        for (const c of response.characteristics) {\r\n          characteristics += (c.serviceType != null ? c.serviceType : '')\r\n            + (c.cargoType != null ? c.cargoType : '')\r\n            + (c.company != null ? c.company : '')\r\n            + (c.locationDeparture != null ? c.locationDeparture : '')\r\n            + (c.locationDestination != null ? c.locationDestination : '')\r\n            + (c.info != null ? c.info : '')\r\n            + (c.essentialDetail != null ? c.essentialDetail : '') + '\\n'\r\n        }\r\n        this.form.inquiryNotice = characteristics\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.form.cargoTypeIds = response.cargoTypeIds\r\n        this.locationOptions = response.locationOptions\r\n        this.form.preCarriageRegionIds = response.locationLoadingIds\r\n        this.form.clientRoleId = response.roleIds[0]\r\n      })\r\n    },\r\n    async getBookingList() {\r\n      this.loading = true\r\n      await listBooking({pageNum: this.pageNum, pageSize: this.pageSize}).then(response => {\r\n        if (response != '') {\r\n          this.bookingList = response.rows\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    async getBookingDetail(id) {\r\n      this.reset()\r\n      await getBooking(id).then(response => {\r\n        let rr = []\r\n        if (response.data.relationClientIds) {\r\n          response.data.relationClientIds.split(',').forEach(v => {\r\n            rr.push(Number(v))\r\n          })\r\n        }\r\n        this.relationClientIds = rr\r\n        this.grossWeight = response.data.grossWeight\r\n        this.goodsValue = response.data.goodsValue\r\n        this.form = response.data\r\n        this.form.relationClientIds = rr\r\n        let cIds = new Set()\r\n        if (response.data.carrierIds) {\r\n          for (const v of this.carrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                  if (response.data.carrierIds.includes(a.carrier.carrierId)) {\r\n                    cIds.add(a.serviceTypeId)\r\n                  }\r\n                }\r\n                if (a.children != undefined && a.children.length > 0) {\r\n                  for (const b of a.children) {\r\n                    if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                      if (response.data.carrierIds.includes(b.carrier.carrierId)) {\r\n                        cIds.add(b.serviceTypeId)\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.salesId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesAssistantId) {\r\n                      this.salesAssistantId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesObserverId) {\r\n                      this.salesObserverId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.opList != undefined) {\r\n          for (const a of this.opList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (a.role.roleLocalName == '操作员' && b.staffId == response.data.opId) {\r\n                  this.opId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '订舱员' && b.staffId == response.data.bookingOpId) {\r\n                  this.bookingOpId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '单证员' && b.staffId == response.data.docOpId) {\r\n                  this.docOpId = b.roleId\r\n                }\r\n                if (b.staffId == response.data.opObserverId) {\r\n                  this.opObserverId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.businessList != undefined) {\r\n          for (const a of this.businessList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.staffId == response.data.verifyPsaId) {\r\n                  this.verifyPsaId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (response.data.rsBookingLogisticsTypeBasicInfo != null) {\r\n          this.logisticsBasicInfo = response.data.rsBookingLogisticsTypeBasicInfo\r\n          this.logisticsReceivablePayableList = response.data.rsBookingLogisticsTypeBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        if (response.data.rsBookingPreCarriageBasicInfo != null) {\r\n          this.preCarriageBasicInfo = response.data.rsBookingPreCarriageBasicInfo\r\n          this.preCarriageReceivablePayableList = response.data.rsBookingPreCarriageBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        if (response.data.rsBookingExportDeclarationBasicInfo != null) {\r\n          this.exportDeclarationBasicInfo = response.data.rsBookingExportDeclarationBasicInfo\r\n          this.exportDeclarationReceivablePayableList = response.data.rsBookingExportDeclarationBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        if (response.data.rsBookingImportClearanceBasicInfo != null) {\r\n          this.importClearanceBasicInfo = response.data.rsBookingImportClearanceBasicInfo\r\n          this.importClearanceReceivablePayableList = response.data.rsBookingImportClearanceBasicInfo.rsBookingReceivablePayableList\r\n        }\r\n        this.locationOptions = response.locationOptions\r\n      })\r\n    },\r\n    async getRctList() {\r\n      this.loading = true\r\n      await listRct({pageNum: this.pageNum, pageSize: this.pageSize}).then(response => {\r\n        if (response != '') {\r\n          this.rctList = response.rows\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    async getRctDetail(id) {\r\n      this.reset()\r\n      await getRct(id).then(response => {\r\n        this.grossWeight = response.data.grossWeight\r\n        this.goodsValue = response.data.goodsValue\r\n        let cIds = new Set()\r\n        if (response.data.carrierIds) {\r\n          for (const v of this.carrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                  if (response.data.carrierIds.includes(a.carrier.carrierId)) {\r\n                    cIds.add(a.serviceTypeId)\r\n                  }\r\n                }\r\n                if (a.children != undefined && a.children.length > 0) {\r\n                  for (const b of a.children) {\r\n                    if (b.carrier != null && b.carrier.carrierId != null && b.carrier.carrierId != undefined && response.data.carrierIds != null && response.data.carrierIds.length > 0) {\r\n                      if (response.data.carrierIds.includes(b.carrier.carrierId)) {\r\n                        cIds.add(b.serviceTypeId)\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (cIds.size > 0) {\r\n          cIds.forEach(c => {\r\n            this.carrierIds.push(c)\r\n          })\r\n        }\r\n        let rr = []\r\n        if (response.data.relationClientIds) {\r\n          response.data.relationClientIds.split(',').forEach(v => {\r\n            rr.push(Number(v))\r\n          })\r\n        }\r\n        this.relationClientIds = rr\r\n        this.form = response.data\r\n        this.form.relationClientIds = rr\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.salesId) {\r\n                      this.salesId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesAssistantId) {\r\n                      this.salesAssistantId = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.salesObserverId) {\r\n                      this.salesObserverId = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.opList != undefined) {\r\n          for (const a of this.opList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (a.role.roleLocalName == '操作员' && b.staffId == response.data.opId) {\r\n                  this.opId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '订舱员' && b.staffId == response.data.bookingOpId) {\r\n                  this.bookingOpId = b.roleId\r\n                }\r\n                if (a.role.roleLocalName == '单证员' && b.staffId == response.data.docOpId) {\r\n                  this.docOpId = b.roleId\r\n                }\r\n                if (b.staffId == response.data.opObserverId) {\r\n                  this.opObserverId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.businessList != undefined) {\r\n          for (const a of this.businessList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.staffId == response.data.verifyPsaId) {\r\n                  this.verifyPsaId = b.roleId\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (response.data.rsRctLogisticsTypeBasicInfo != null) {\r\n          this.logisticsBasicInfo = response.data.rsRctLogisticsTypeBasicInfo\r\n          this.logisticsReceivablePayableList = response.data.rsRctLogisticsTypeBasicInfo.rsRctReceivablePayableList\r\n          this.logisticsOpHistory = response.data.rsRctLogisticsTypeBasicInfo.rsOperationalProcessList\r\n        }\r\n        if (response.data.rsRctPreCarriageBasicInfo != null) {\r\n          this.preCarriageBasicInfo = response.data.rsRctPreCarriageBasicInfo\r\n          this.preCarriageReceivablePayableList = response.data.rsRctPreCarriageBasicInfo.rsRctReceivablePayableList\r\n          this.preCarriageOpHistory = response.data.rsRctPreCarriageBasicInfo.rsOperationalProcessList\r\n        }\r\n        if (response.data.rsRctExportDeclarationBasicInfo != null) {\r\n          this.exportDeclarationBasicInfo = response.data.rsRctExportDeclarationBasicInfo\r\n          this.exportDeclarationReceivablePayableList = response.data.rsRctExportDeclarationBasicInfo.rsRctReceivablePayableList\r\n          this.exportDeclarationOpHistory = response.data.rsRctExportDeclarationBasicInfo.rsOperationalProcessList\r\n        }\r\n        if (response.data.rsRctImportClearanceBasicInfo != null) {\r\n          this.importClearanceBasicInfo = response.data.rsRctImportClearanceBasicInfo\r\n          this.importClearanceReceivablePayableList = response.data.rsRctImportClearanceBasicInfo.rsRctReceivablePayableList\r\n          this.importClearanceOpHistory = response.data.rsRctImportClearanceBasicInfo.rsOperationalProcessList\r\n        }\r\n        this.locationOptions = response.locationOptions\r\n      })\r\n    },\r\n    async getServiceTypeList(val) {\r\n      if (this.$store.state.data.serviceTypeList.length == 0) {\r\n        await this.$store.dispatch('getServiceTypeList')\r\n      }\r\n      this.list.clear()\r\n      this.form.serviceTypeIds = val\r\n      for (const s of this.$store.state.data.serviceTypeList[0].children) {\r\n        if (val.includes(s.serviceTypeId)) {\r\n          if (s.typeId != null) {\r\n            this.list.add(s.typeId)\r\n          }\r\n        }\r\n        if (s.children) {\r\n          for (const t of s.children) {\r\n            if (val.includes(t.serviceTypeId)) {\r\n              if (s.typeId != null) {\r\n                this.list.add(s.typeId)\r\n              }\r\n              if (t.typeId != null) {\r\n                this.list.add(t.typeId)\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if (val.includes(-1)) {\r\n        this.list.add('-1')\r\n      }\r\n      this.$forceUpdate()\r\n    },\r\n    getType(n) {\r\n      for (const s of this.$store.state.data.serviceTypeList[0].children) {\r\n        if (s.serviceTypeId == n) {\r\n          this.logisticsType = s.typeId\r\n        }\r\n        if (s.children) {\r\n          for (const t of s.children) {\r\n            if (t.serviceTypeId == n) {\r\n              this.logisticsType = s.typeId\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    getRelationClientIds(val) {\r\n      this.form.relationClientIds = val\r\n      this.relationClientIds = val\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    generateRct(v) {\r\n      if (v) {\r\n        getRctMon().then(v => {\r\n          let num = v.data\r\n          if (num.toString().length < 4) {\r\n            const j = 4 - (num.toString().length)\r\n            for (let i = 0; i < j; i++) {\r\n              num = '0' + num\r\n            }\r\n          }\r\n          let date = new Date()\r\n          let month = (date.getMonth() + Number(this.rct.month)).toString()\r\n          let year = (date.getFullYear() + (month / 12 > 1 ? 1 : 0)).toString().substring(2, 4)\r\n          this.rct.rctNo = this.rct.leadingCharacter + year + (month.length == 1 ? '0' + month : month) + num.toString()\r\n        })\r\n      } else {\r\n        this.openGenerateRct = true\r\n      }\r\n    },\r\n    confirmRct() {\r\n      this.form.rctNo = this.rct.rctNo\r\n      this.openGenerateRct = false\r\n    },\r\n    autoCompletion(val) {\r\n      let re = /\\d{1,3}(?=(\\d{3})+$)/g\r\n      let num = /[0-9]+/g\r\n      if (val == 'grossWeight') {\r\n        if (num.test(this.grossWeight)) {\r\n          this.grossWeight = this.grossWeight.replace(/\\b(0+)/gi, '')\r\n          this.form.grossWeight = this.grossWeight\r\n          let str = this.grossWeight.split('.')\r\n          let n1 = str[0].replace(re, '$&,')\r\n          this.grossWeight = str.length > 1 && str[1] ? `${n1}.${str[1]}` : `${n1}.00`\r\n        } else {\r\n          this.$message.warning('请输入数字')\r\n        }\r\n      }\r\n      if (val == 'goodsValue') {\r\n        if (num.test(this.goodsValue)) {\r\n          this.goodsValue = this.goodsValue.replace(/\\b(0+)/gi, '')\r\n          this.form.goodsValue = this.goodsValue\r\n          let str = this.goodsValue.split('.')\r\n          let n1 = str[0].replace(re, '$&,')\r\n          this.goodsValue = str.length > 1 && str[1] ? `${n1}.${str[1]}` : `${n1}.00`\r\n        } else {\r\n          this.$message.warning('请输入数字')\r\n        }\r\n      }\r\n    },\r\n    getNoInfo(type, val) {\r\n      let list\r\n      if (type == 'logistics') {\r\n        this.logisticsNoInfo = val\r\n        list = this.showLogisticsNoInfo = [\r\n          {type: 'soNo', logisticsNo: 'SO号码', details: ''},\r\n          {type: 'mblNo', logisticsNo: '主提单号', details: ''},\r\n          {type: 'hblNo', logisticsNo: '货代单号', details: ''},\r\n          {type: 'containersInfo', logisticsNo: '柜号信息', details: ''},\r\n          {type: 'shipper', logisticsNo: '发货人', details: ''},\r\n          {type: 'consignee', logisticsNo: '收货人', details: ''},\r\n          {type: 'notifyParty', logisticsNo: '通知人', details: ''},\r\n          {type: 'polBookingAgent', logisticsNo: '启运港放舱代理', details: ''},\r\n          {type: 'podHandleAgent', logisticsNo: '目的港换单代理', details: ''},\r\n          {type: 'shippingMark', logisticsNo: '唛头', details: ''},\r\n          {type: 'goodsDescription', logisticsNo: '货描', details: ''},\r\n          {type: 'blIssueDate', logisticsNo: '签单日期', details: ''},\r\n          {type: 'blIssueLocation', logisticsNo: '签单地点', details: ''}]\r\n\r\n      }\r\n      if (type == 'preCarriage') {\r\n        this.preCarriageNoInfo = val\r\n        list = this.showPreCarriageNoInfo = [\r\n          {type: 'soNo', preCarriageNo: 'SO号码', details: ''},\r\n          {type: 'preCarriageDriverName', preCarriageNo: '司机姓名', details: ''},\r\n          {type: 'preCarriageDriverTel', preCarriageNo: '司机电话', details: ''},\r\n          {type: 'preCarriageTruckNo', preCarriageNo: '司机车牌', details: ''},\r\n          {type: 'preCarriageTruckRemark', preCarriageNo: '司机备注', details: ''},\r\n          {type: 'preCarriageAddress', preCarriageNo: '装柜地址', details: ''},\r\n          {type: 'preCarriageTime', preCarriageNo: '到场时间', details: ''},\r\n          {type: 'containerNo', preCarriageNo: '柜号', details: ''},\r\n          {type: 'containerType', preCarriageNo: '柜型', details: ''},\r\n          {type: 'sealNo', preCarriageNo: '封条', details: ''},\r\n          {type: 'weightPaper', preCarriageNo: '磅单', details: ''}]\r\n      }\r\n      for (let numInfo of list) {\r\n        val.forEach(v => {\r\n          for (const vKey in v) {\r\n            if (vKey == numInfo.type) {\r\n              numInfo.details = numInfo.details == '' ? (v[vKey] != null ? v[vKey] : '') : numInfo.details + (v[vKey] != null ? ',' + v[vKey] : '')\r\n            }\r\n          }\r\n        })\r\n      }\r\n    },\r\n    cancel() {\r\n      this.reset()\r\n      this.loading = false\r\n      this.open = false\r\n      this.openGenerateRct = false\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        rctId: null,\r\n        rctNo: null,\r\n        rctOpDate: null,\r\n        opId: null,\r\n        bookingOpId: null,\r\n        docOpId: null,\r\n        opObserverId: null,\r\n        newBookingNo: null,\r\n        newBookingTime: null,\r\n        quotationNo: null,\r\n        quotationDate: null,\r\n        salesId: null,\r\n        salesAssistantId: null,\r\n        salesObserverId: null,\r\n        verifyPsaId: null,\r\n        psaVerifyTime: null,\r\n        urgencyDegree: null,\r\n        paymentTypeId: null,\r\n        releaseTypeId: null,\r\n        processStatusId: null,\r\n        clientId: null,\r\n        clientRoleId: null,\r\n        clientContactor: null,\r\n        clientContactorTel: null,\r\n        clientContactorEmail: null,\r\n        relationClientIds: [],\r\n        impExpTypeId: null,\r\n        tradingPaymentChannelId: null,\r\n        tradingTermsId: null,\r\n        logisticsTermsId: null,\r\n        contractNo: null,\r\n        clientInvoiceNo: null,\r\n        goodsNameSummary: null,\r\n        packageQuantity: null,\r\n        grossWeight: null,\r\n        weightUnitId: null,\r\n        volume: null,\r\n        volumeUnitId: null,\r\n        cargoTypeIds: [],\r\n        goodsValue: null,\r\n        goodsCurrencyId: null,\r\n        maxWeight: null,\r\n        revenueTons: null,\r\n        logisticsTypeId: null,\r\n        polId: null,\r\n        destinationPortId: null,\r\n        carrierIds: [],\r\n        schedule: null,\r\n        validTimeForm: null,\r\n        isMblNeeded: 0,\r\n        mblNo: null,\r\n        isUnderAgreementMbl: 0,\r\n        isCustomsIntransitMbl: 0,\r\n        isSwitchMbl: 0,\r\n        isDividedMbl: 0,\r\n        mblIssueTypeId: null,\r\n        mblGetWayId: null,\r\n        mblReleaseWayId: null,\r\n        isHblNeeded: 0,\r\n        hblNoList: null,\r\n        isUnderAgreementHbl: 0,\r\n        isCustomsIntransitHbl: 0,\r\n        isSwitchHbl: 0,\r\n        isDividedHbl: 0,\r\n        hblIssueTypeId: null,\r\n        hblGetWayId: null,\r\n        hblReleaseWayId: null,\r\n        serviceTypeIds: [],\r\n        quotationSummary: null,\r\n        newBookingRemark: null,\r\n        inquiryNotice: null,\r\n        inquiryInnerRemark: null,\r\n        opLeaderRemark: null,\r\n        opInnerRemark: null,\r\n        agreementTypeId: null,\r\n        agreementNo: null,\r\n        readOnly: null,\r\n        createTime: null,\r\n        updateTime: null,\r\n        deleteTime: null,\r\n        deleteStatus: '0',\r\n        deleteBy: null,\r\n        updateBy: null,\r\n        createBy: null,\r\n        soNo: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        bookingDetail: null,\r\n        precarriageTime: null,\r\n        cvClosingTime: null,\r\n        cvDeclaringTime: null,\r\n        vgm: null,\r\n        siClosingTime: null,\r\n        trailer: null,\r\n        shipName: null,\r\n        shipTime: null,\r\n        podETA: null,\r\n        telexReleaseType: null,\r\n        isReleasable: null,\r\n        sendToAgent: null,\r\n        boatId: null,\r\n        remark: null\r\n      }\r\n      this.logisticsBasicInfo = {\r\n        logisticsTypeInfoId: null,\r\n        rctId: null,\r\n        logisticsTypeId: null,\r\n        carrierId: null,\r\n        polId: null,\r\n        firstCvClosingTime: null,\r\n        firstCyOpenTime: null,\r\n        firstCyClosingTime: null,\r\n        firstEtd: null,\r\n        firstVessel: null,\r\n        firstVoyage: null,\r\n        localBasicPortId: null,\r\n        basicClosingTime: null,\r\n        basicFinalGateinTime: null,\r\n        basicEtd: null,\r\n        basicVessel: null,\r\n        basicVoyage: null,\r\n        transitPortId: null,\r\n        podId: null,\r\n        podEta: null,\r\n        destinationPortId: null,\r\n        destinationPortEta: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.preCarriageBasicInfo = {\r\n        preCarriageInfoId: null,\r\n        rctId: null,\r\n        logisticsTypeId: null,\r\n        preCarriageRegionId: null,\r\n        preCarriageAddress: null,\r\n        preCarriageTime: null,\r\n        preCarriageContact: null,\r\n        preCarriageTel: null,\r\n        preCarriageRemark: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.exportDeclarationBasicInfo = {\r\n        exportDeclarationId: null,\r\n        rctId: null,\r\n        logisticsTypeId: null,\r\n        dispatchRegionId: null,\r\n        dispatchAddress: null,\r\n        dispatchTime: null,\r\n        dispatchContact: null,\r\n        dispatchTel: null,\r\n        dispatchRemark: null,\r\n        dispatchDriverName: null,\r\n        dispatchDriverTel: null,\r\n        dispatchTruckNo: null,\r\n        dispatchTruckRemark: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.importClearanceBasicInfo = {\r\n        importClearanceId: null,\r\n        rctId: null,\r\n        exportCustomsTypeId: null,\r\n        importCustomsTypeId: null,\r\n        opConfirmed: null,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        financeConfirmed: null,\r\n        financeConfirmedId: null,\r\n        financeConfirmedName: null,\r\n        financeConfirmedDate: null,\r\n        salesConfirmed: null,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        supplierConfirmed: null,\r\n        supplierConfirmedId: null,\r\n        supplierConfirmedName: null,\r\n        supplierConfirmedDate: null,\r\n        invoiceQueryNo: null\r\n      }\r\n      this.grossWeight = 0\r\n      this.goodsValue = 0\r\n      this.carrierId = null\r\n      this.relationClientIds = []\r\n      this.verifyPsaId = null\r\n      this.salesId = null\r\n      this.salesAssistantId = null\r\n      this.salesObserverId = null\r\n      this.opId = null\r\n      this.bookingOpId = null\r\n      this.docOpId = null\r\n      this.opObserverId = null\r\n      this.carrierIds = []\r\n      this.preCarriage = false\r\n      this.importClearance = false\r\n      this.exportDeclaration = false\r\n      this.logisticsProcess = []\r\n      this.logisticsNoInfo = []\r\n      this.openLogisticsNoInfo = false\r\n      this.logisticsOpHistory = []\r\n      this.logisticsReceivablePayableList = []\r\n      this.preCarriageNoInfo = []\r\n      this.openPreCarriageNoInfo = false\r\n      this.preCarriageOpHistory = []\r\n      this.preCarriageReceivablePayableList = []\r\n      this.openExportDeclarationNoInfo = false\r\n      this.exportDeclarationNoInfo = []\r\n      this.exportDeclarationOpHistory = []\r\n      this.exportDeclarationReceivablePayableList = []\r\n      this.openImportPassNoInfo = false\r\n      this.importClearanceNoInfo = []\r\n      this.importClearanceOpHistory = []\r\n      this.importClearanceReceivablePayableList = []\r\n      this.resetForm('form')\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm(v) {\r\n      this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n      if (this.psaVerify) {\r\n        this.form.isPsaVerified = 1\r\n        this.form.processStatusId = 2\r\n        this.form.psaVerifyTime = parseTime(new Date())\r\n      }\r\n      this.$refs['form'].validate(valid => {\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (v == 'saveCopy') {\r\n            this.form.rctId = null\r\n          }\r\n          if (valid) {\r\n            if (this.form.rctId != null) {\r\n              this.form.processStatusId = 3\r\n              updateRct(this.form).then(response => {\r\n                this.saveAll(this.form.rctId)\r\n                this.$modal.msgSuccess('修改成功')\r\n                this.open = false\r\n                this.getRctList()\r\n              })\r\n            } else {\r\n              this.form.processStatusId = 1\r\n              addRct(this.form).then(response => {\r\n                this.form.rctId = response.data\r\n                this.saveAll(response.data)\r\n                this.$modal.msgSuccess('新增成功')\r\n                this.open = false\r\n                this.getRctList()\r\n              })\r\n            }\r\n          }\r\n        }\r\n        if (this.type == 'booking') {\r\n          if (v == 'saveCopy') {\r\n            this.form.bookingId = null\r\n          }\r\n          if (valid) {\r\n            if (this.form.bookingId != null) {\r\n              this.form.processStatusId = 3\r\n              updateBooking(this.form).then(response => {\r\n                if (!this.psaVerify) {\r\n                  this.saveAll(this.form.bookingId)\r\n                  this.$modal.msgSuccess('修改成功')\r\n                  this.open = false\r\n                  this.getBookingList()\r\n                }\r\n              })\r\n            } else {\r\n              this.form.processStatusId = 1\r\n              addBooking(this.form).then(response => {\r\n                this.form.bookingId = response.data\r\n                this.saveAll(response.data)\r\n                this.$modal.msgSuccess('新增成功')\r\n                this.open = false\r\n                this.getBookingList()\r\n              })\r\n            }\r\n          }\r\n        }\r\n      })\r\n    },\r\n    rejected() {\r\n      this.$confirm('确认单据后不可更改，是否确认？', '', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(res => {\r\n        if (res == 'confirm') {\r\n          this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n          this.$refs['form'].validate(valid => {\r\n            this.form.isPsaVerified = 1\r\n            this.form.processStatusId = 9\r\n            if (valid) {\r\n              updateBooking(this.form).then(response => {\r\n                this.$modal.msgSuccess('修改成功')\r\n                this.open = false\r\n                this.getBookingList()\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    saveAll(id) {\r\n      this.saveLogistics(id)\r\n      this.savePreCarriage(id)\r\n      this.saveExportDeclaration(id)\r\n      this.saveImportClearance(id)\r\n    },\r\n    saveLogistics(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.logisticsReceivablePayableList.length > 0) {\r\n            this.logisticsBasicInfo.rsBookingReceivablePayableList = this.logisticsReceivablePayableList\r\n          }\r\n          this.logisticsBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.logisticsBasicInfo.typeId = 1\r\n          this.form.rsBookingLogisticsTypeBasicInfo = this.logisticsBasicInfo\r\n          saveBookingLogistics(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.logisticsReceivablePayableList.length > 0) {\r\n            this.logisticsBasicInfo.rsRctReceivablePayableList = this.logisticsReceivablePayableList\r\n          }\r\n          if (this.logisticsNoInfo.length > 0) {\r\n            this.logisticsBasicInfo.rsRctLogisticsNoInfos = this.logisticsNoInfo\r\n          }\r\n          if (this.logisticsOpHistory.length > 0) {\r\n            this.logisticsBasicInfo.rsOperationalProcessList = this.logisticsOpHistory\r\n          }\r\n          this.logisticsBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.logisticsBasicInfo.typeId = 1\r\n          this.form.rsRctLogisticsTypeBasicInfo = this.logisticsBasicInfo\r\n          saveRctLogistics(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    savePreCarriage(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.preCarriageReceivablePayableList.length > 0) {\r\n            this.preCarriageBasicInfo.rsBookingReceivablePayableList = this.preCarriageReceivablePayableList\r\n          }\r\n          this.preCarriageBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.preCarriageBasicInfo.typeId = 4\r\n          this.form.rsBookingPreCarriageBasicInfo = this.preCarriageBasicInfo\r\n          saveBookingPreCarriage(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.preCarriageReceivablePayableList.length > 0) {\r\n            this.preCarriageBasicInfo.rsRctReceivablePayableList = this.preCarriageReceivablePayableList\r\n          }\r\n          if (this.preCarriageNoInfo.length > 0) {\r\n            this.preCarriageBasicInfo.rsRctPreCarriageNoInfos = this.preCarriageNoInfo\r\n          }\r\n          if (this.preCarriageOpHistory.length > 0) {\r\n            this.preCarriageBasicInfo.rsOperationalProcessList = this.preCarriageOpHistory\r\n          }\r\n          this.preCarriageBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.preCarriageBasicInfo.typeId = 4\r\n          this.form.rsRctPreCarriageBasicInfo = this.preCarriageBasicInfo\r\n          saveRctPreCarriage(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    saveExportDeclaration(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.exportDeclarationReceivablePayableList.length > 0) {\r\n            this.exportDeclarationBasicInfo.rsBookingReceivablePayableList = this.exportDeclarationReceivablePayableList\r\n          }\r\n          this.exportDeclarationBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.exportDeclarationBasicInfo.typeId = 5\r\n          this.form.rsBookingExportDeclarationBasicInfo = this.exportDeclarationBasicInfo\r\n          saveBookingExportDeclaration(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.exportDeclarationReceivablePayableList.length > 0) {\r\n            this.exportDeclarationBasicInfo.rsRctReceivablePayableList = this.exportDeclarationReceivablePayableList\r\n          }\r\n          if (this.exportDeclarationOpHistory.length > 0) {\r\n            this.exportDeclarationBasicInfo.rsOperationalProcessList = this.exportDeclarationOpHistory\r\n          }\r\n          this.exportDeclarationBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.exportDeclarationBasicInfo.typeId = 5\r\n          this.form.rsRctExportDeclarationBasicInfo = this.exportDeclarationBasicInfo\r\n          saveRctExportDeclaration(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    saveImportClearance(id) {\r\n      if (this.form.bookingId == null && this.form.rctId == null) {\r\n        this.$message.error('请先确定单据')\r\n      } else {\r\n        this.form.relationClientIds = this.form.relationClientIds != null && this.form.relationClientIds.length > 0 ? this.form.relationClientIds.toString() : null\r\n        if (this.type == 'booking' && !this.psaVerify) {\r\n          if (this.importClearanceReceivablePayableList.length > 0) {\r\n            this.importClearanceBasicInfo.rsBookingReceivablePayableList = this.importClearanceReceivablePayableList\r\n          }\r\n          this.importClearanceBasicInfo.bookingId = typeof id == 'number' ? id : this.form.bookingId\r\n          this.importClearanceBasicInfo.typeId = 6\r\n          this.form.rsBookingImportClearanceBasicInfo = this.importClearanceBasicInfo\r\n          saveBookingImportClearance(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n        if (this.type == 'op' || (this.type == 'booking' && this.psaVerify)) {\r\n          if (this.importClearanceReceivablePayableList.length > 0) {\r\n            this.importClearanceBasicInfo.rsRctReceivablePayableList = this.importClearanceReceivablePayableList\r\n          }\r\n          if (this.importClearanceOpHistory.length > 0) {\r\n            this.importClearanceBasicInfo.rsOperationalProcessList = this.importClearanceOpHistory\r\n          }\r\n          this.importClearanceBasicInfo.rctId = typeof id == 'number' ? id : this.form.rctId\r\n          this.importClearanceBasicInfo.typeId = 6\r\n          this.form.rsRctImportClearanceBasicInfo = this.importClearanceBasicInfo\r\n          saveRctImportClearance(this.form).then(() => {\r\n            if (typeof id != 'number') {\r\n              this.$message.success('保存成功')\r\n            }\r\n          })\r\n        }\r\n      }\r\n    },\r\n    // 主提单按钮状态改变\r\n    changeMaster() {\r\n      // this.form.isMblNeeded = !this.form.isMblNeeded;\r\n      console.log(this.form.isMblNeeded)\r\n\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n.spc {\r\n  ::v-deep.el-form-item__content {\r\n    padding-left: 0;\r\n  }\r\n}\r\n\r\n.booking {\r\n  ::v-deep.el-input__inner,\r\n  ::v-deep.el-textarea__inner,\r\n  ::v-deep.vue-treeselect__control,\r\n  ::v-deep.el-input__count,\r\n  ::v-deep.el-range-input {\r\n    background-color: gainsboro !important;\r\n    font-weight: bold;\r\n  }\r\n}\r\n\r\n@keyframes slide-down {\r\n  0% {\r\n    transform: scale(1, 0);\r\n  }\r\n  100% {\r\n    transform: scale(1, 1);\r\n  }\r\n}\r\n\r\n.active {\r\n  max-height: 500px;\r\n  transition: max-height .3s ease-in;\r\n  transform-origin: 50% 0;\r\n  animation: slide-down 0.3s ease-in;\r\n  -webkit-animation: slide-down 0.3s ease-in;\r\n}\r\n\r\n.inactive {\r\n  max-height: 0;\r\n  overflow: auto;\r\n  transition: max-height .3s ease-out;\r\n}\r\n\r\n.show {\r\n  display: contents;\r\n\r\n  &.invisible {\r\n    display: none;\r\n  }\r\n\r\n  &.visible {\r\n\r\n  }\r\n}\r\n\r\n.button-group {\r\n  display: flex;\r\n  margin-top: 20px;\r\n  margin-bottom: 20px;\r\n  justify-content: center;\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA4rBA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,cAAA,GAAAF,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAK,gBAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,kBAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,UAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,kBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,MAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,OAAA,GAAAV,OAAA;AAUA,IAAAW,QAAA,GAAAX,OAAA;AAUA,IAAAY,KAAA,GAAAZ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAa,IAAA;EACAC,KAAA;EACAC,KAAA;EACAC,UAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,UAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,MAAA;MACAC,YAAA;MACAC,UAAA;MACAC,WAAA;MACAC,eAAA;MACAC,UAAA;MACAC,WAAA;MACA;MACAC,IAAA,MAAAC,GAAA;MACAC,aAAA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACAI,KAAA;MACAC,aAAA;MACAC,SAAA;MACAC,UAAA;MACAC,iBAAA;MACAC,WAAA;MACAC,OAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,IAAA;MACAC,WAAA;MACAC,OAAA;MACAC,YAAA;MACA;MACAC,eAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACAC,MAAA,OAAAC,IAAA;MACArC,SAAA,OAAAqC,IAAA;MACApC,iBAAA;MACAC,KAAA,OAAAmC,IAAA;MACAC,IAAA;MACAC,OAAA;MACAC,WAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,gBAAA;MACAC,eAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,kBAAA;MACAC,8BAAA;MACAC,iBAAA;MACAC,qBAAA;MACAC,qBAAA;MACAC,oBAAA;MACAC,gCAAA;MACAC,2BAAA;MACAC,uBAAA;MACAC,2BAAA;MACAC,0BAAA;MACAC,sCAAA;MACAC,oBAAA;MACAC,qBAAA;MACAC,yBAAA;MACAC,wBAAA;MACAC,oCAAA;MACA;MACAC,WAAA;MACAC,OAAA;MACAC,IAAA;MACAC,kBAAA;MACAC,oBAAA;MACAC,0BAAA;MACAC,wBAAA;MACAC,GAAA;QACAC,gBAAA;QACAC,KAAA;QACAC,KAAA;QACAC,KAAA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;MACA;MACAC,KAAA;IACA;EAEA;EACAC,KAAA;IACA,iCAAAC,oBAAAC,CAAA;MAAA,IAAAC,KAAA;MACA,SAAAnE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAgF,eAAA,CAAAC,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAAC,WAAA;QACAC,cAAA,CAAAC,QAAA,uBAAAC,IAAA;UACAP,KAAA,CAAAQ,OAAA,CAAAT,CAAA;QACA;MACA;QACA,KAAAS,OAAA,CAAAT,CAAA;MACA;IACA;EACA;EACAU,WAAA,WAAAA,YAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,KAAA;IACA;IACA,SAAAzD,IAAA;MACA,KAAA0D,cAAA,GAAAL,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAA3D,IAAA;MACA,KAAA4D,UAAA,GAAAP,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAC,EAAA,SAAA/D,IAAA;MACA,KAAAgE,YAAA,MAAAH,MAAA,CAAAC,KAAA,CAAAC,EAAA,EAAAV,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAG,GAAA;MACA,KAAAC,gBAAA,MAAAL,MAAA,CAAAC,KAAA,CAAAG,GAAA,EAAAZ,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAK,GAAA;MACA,KAAAC,YAAA,MAAAP,MAAA,CAAAC,KAAA,CAAAK,GAAA,EAAAd,IAAA;QACAG,MAAA,CAAAG,aAAA;MACA;IACA;IACA,SAAAE,MAAA,CAAAC,KAAA,CAAAlE,SAAA;MACA,KAAAA,SAAA;IACA;EACA;EACAyE,OAAA;IACA;IACAV,aAAA,WAAAA,cAAA;MACA;MACA,SAAAhF,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAgF,eAAA,CAAAC,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAAC,WAAA;QACA,KAAAvE,MAAA,CAAAyE,QAAA;MACA;MACA;MACA,SAAAzE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAuG,UAAA,CAAAtB,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAAsB,MAAA;QACA,KAAA5F,MAAA,CAAAyE,QAAA;MACA;MACA;MACA,SAAAzE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAyG,YAAA,CAAAxB,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAAwB,QAAA;QACA,KAAA9F,MAAA,CAAAyE,QAAA;MACA;MACA,KAAAsB,MAAA;MACA,KAAAC,WAAA;MACA,KAAAC,SAAA;MACA,KAAAC,cAAA;IACA;IACA;IACAH,MAAA,WAAAA,OAAA;MAAA,IAAAI,MAAA;MACA,SAAAnG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAC,MAAA,CAAAgF,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAAjF,MAAA;QACAmF,cAAA,CAAAC,QAAA,cAAAC,IAAA;UACAyB,MAAA,CAAA9G,MAAA,GAAA8G,MAAA,CAAAnG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAC,MAAA;QACA;MACA;QACA,KAAAA,MAAA,QAAAW,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAC,MAAA;MACA;IACA;IACA;IACA4G,SAAA,WAAAA,UAAA;MAAA,IAAAG,MAAA;MACA,SAAApG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiH,SAAA,CAAAhC,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAA+B,SAAA;QACA7B,cAAA,CAAAC,QAAA,iBAAAC,IAAA;UACA0B,MAAA,CAAA7G,UAAA,GAAA6G,MAAA,CAAApG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiH,SAAA;QACA;MACA;QACA,KAAA9G,UAAA,QAAAS,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAiH,SAAA;MACA;IACA;IACA;IACAL,WAAA,WAAAA,YAAA;MAAA,IAAAM,MAAA;MACA,SAAAtG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmH,mBAAA,CAAAlC,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAAiC,mBAAA;QACA/B,cAAA,CAAAC,QAAA,+BAAAC,IAAA;UACA4B,MAAA,CAAA9G,WAAA,GAAA8G,MAAA,CAAAtG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmH,mBAAA;QACA;MACA;QACA,KAAA/G,WAAA,QAAAQ,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAmH,mBAAA;MACA;IACA;IACA;IACAL,cAAA,WAAAA,eAAA;MAAA,IAAAM,MAAA;MACA,SAAAxG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAqH,cAAA,CAAApC,MAAA,cAAArE,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAkF,SAAA,CAAAmC,cAAA;QACAjC,cAAA,CAAAC,QAAA,sBAAAC,IAAA;UACA8B,MAAA,CAAAlH,YAAA,GAAAkH,MAAA,CAAAxG,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAqH,cAAA;QACA;MACA;QACA,KAAAnH,YAAA,QAAAU,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAqH,cAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAvC,MAAA;QACA,OAAAsC,IAAA,CAAAC,QAAA;MACA;MACA,IAAAC,CAAA;MACA,IAAAF,IAAA,CAAAG,KAAA;QACA,IAAAH,IAAA,CAAAG,KAAA,CAAAC,oBAAA,YAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;UACA,IAAAL,IAAA,CAAAM,IAAA,CAAAC,aAAA;YACAL,CAAA,GAAAF,IAAA,CAAAM,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAM,IAAA,CAAAC,aAAA;UACA;YACAL,CAAA,GAAAF,IAAA,CAAAU,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAU,IAAA,CAAAC,aAAA;UACA;QACA;UACAT,CAAA,GAAAF,IAAA,CAAAG,KAAA,CAAAS,SAAA,SAAAZ,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA,SAAAL,IAAA,CAAAG,KAAA,CAAAU,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAL,IAAA,CAAAc,MAAA;QACA;UACArC,EAAA,EAAAuB,IAAA,CAAAc,MAAA;UACAC,KAAA,EAAAb,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAe,UAAA,EAAAhB,IAAA,CAAAiB,OAAA,YAAAjB,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;QACA;UACAzC,EAAA,EAAAuB,IAAA,CAAAmB,MAAA;UACAJ,KAAA,EAAAb,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAe,UAAA,EAAAhB,IAAA,CAAAiB,OAAA,YAAAjB,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;IACA;IACAE,iBAAA,WAAAA,kBAAApB,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAvC,MAAA;QACA,OAAAsC,IAAA,CAAAC,QAAA;MACA;MACA,IAAAC,CAAA;MACA,KAAAF,IAAA,CAAAqB,OAAA,IAAArB,IAAA,CAAAqB,OAAA,CAAAC,gBAAA,YAAAtB,IAAA,CAAAqB,OAAA,CAAAE,aAAA;QACArB,CAAA,GAAAF,IAAA,CAAAwB,gBAAA,SAAAxB,IAAA,CAAAyB,aAAA,SAAAjB,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAwB,gBAAA,IAAAN,SAAA,GAAAlB,IAAA,CAAAwB,gBAAA;MACA;QACAtB,CAAA,IAAAF,IAAA,CAAAqB,OAAA,CAAAK,eAAA,WAAA1B,IAAA,CAAAqB,OAAA,CAAAK,eAAA,gBAAA1B,IAAA,CAAAqB,OAAA,CAAAE,aAAA,WAAAvB,IAAA,CAAAqB,OAAA,CAAAE,aAAA,gBAAAvB,IAAA,CAAAqB,OAAA,CAAAC,gBAAA,WAAAtB,IAAA,CAAAqB,OAAA,CAAAC,gBAAA,eAAAd,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAqB,OAAA,CAAAC,gBAAA,WAAAtB,IAAA,CAAAqB,OAAA,CAAAC,gBAAA;MACA;MACA;QACA7C,EAAA,EAAAuB,IAAA,CAAA2B,aAAA;QACAZ,KAAA,EAAAb,CAAA;QACAD,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACAvB,YAAA,WAAAA,aAAAD,EAAA;MAAA,IAAAmD,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAAzD,KAAA;cAAAiE,QAAA,CAAAE,IAAA;cAAA,OACA,IAAA5D,uBAAA,EAAAD,EAAA,EAAAV,IAAA,WAAAwE,QAAA;gBACAX,MAAA,CAAArF,IAAA,CAAAiG,eAAA,GAAAD,QAAA,CAAA9J,IAAA,CAAA+J,eAAA;gBACAZ,MAAA,CAAArF,IAAA,CAAAzC,OAAA,GAAAyI,QAAA,CAAA9J,IAAA,CAAAwI,OAAA;gBACAW,MAAA,CAAArF,IAAA,CAAAkG,QAAA,GAAAF,QAAA,CAAA9J,IAAA,CAAAiK,SAAA;gBACAd,MAAA,CAAArF,IAAA,CAAAoG,YAAA,GAAAJ,QAAA,CAAA9J,IAAA,CAAAmK,aAAA;gBACAhB,MAAA,CAAArF,IAAA,CAAAsG,eAAA,GAAAN,QAAA,CAAA9J,IAAA,CAAAqK,YAAA;gBACAlB,MAAA,CAAArF,IAAA,CAAAwG,kBAAA,GAAAR,QAAA,CAAA9J,IAAA,CAAAuK,gBAAA;gBACApB,MAAA,CAAArF,IAAA,CAAA0G,oBAAA,GAAAV,QAAA,CAAA9J,IAAA,CAAAyK,uBAAA;gBACAtB,MAAA,CAAArF,IAAA,CAAA4G,WAAA,GAAAZ,QAAA,CAAA9J,IAAA,CAAA2K,MAAA;gBACAxB,MAAA,CAAArF,IAAA,CAAA8G,aAAA,OAAAC,IAAA;gBACA1B,MAAA,CAAArF,IAAA,CAAAgH,YAAA,GAAAhB,QAAA,CAAA9J,IAAA,CAAA+K,QAAA;gBACA5B,MAAA,CAAArF,IAAA,CAAAkH,gBAAA,GAAAlB,QAAA,CAAA9J,IAAA,CAAAiL,SAAA;gBACA9B,MAAA,CAAArF,IAAA,CAAAxD,UAAA,GAAAwJ,QAAA,CAAA9J,IAAA,CAAAkL,UAAA;gBACA/B,MAAA,CAAArF,IAAA,CAAAqH,eAAA,GAAArB,QAAA,CAAA9J,IAAA,CAAAoL,eAAA;gBACAjC,MAAA,CAAArF,IAAA,CAAAvD,WAAA,GAAAuJ,QAAA,CAAA9J,IAAA,CAAAO,WAAA;gBACA4I,MAAA,CAAArF,IAAA,CAAAuH,YAAA,GAAAvB,QAAA,CAAA9J,IAAA,CAAAsL,WAAA;gBACAnC,MAAA,CAAArF,IAAA,CAAAyH,KAAA,GAAAzB,QAAA,CAAA9J,IAAA,CAAAwL,WAAA;gBACArC,MAAA,CAAArF,IAAA,CAAA2H,iBAAA,GAAA3B,QAAA,CAAA9J,IAAA,CAAA0L,aAAA;gBACAvC,MAAA,CAAArF,IAAA,CAAA6H,aAAA,GAAA7B,QAAA,CAAA9J,IAAA,CAAA4L,qBAAA;gBACAzC,MAAA,CAAArF,IAAA,CAAA+H,WAAA,GAAA/B,QAAA,CAAA9J,IAAA,CAAA6L,WAAA;gBACA1C,MAAA,CAAArF,IAAA,CAAAgI,gBAAA,GAAAhC,QAAA,CAAA9J,IAAA,CAAA+L,MAAA;gBACA,IAAA5C,MAAA,CAAAhJ,UAAA,IAAAsI,SAAA;kBAAA,IAAAuD,SAAA,OAAAC,2BAAA,CAAA5C,OAAA,EACAF,MAAA,CAAAhJ,UAAA;oBAAA+L,KAAA;kBAAA;oBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAlH,CAAA,IAAAsH,IAAA;sBAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;sBACA,IAAAD,CAAA,CAAA7E,QAAA,IAAAiB,SAAA;wBAAA,IAAA8D,UAAA,OAAAN,2BAAA,CAAA5C,OAAA,EACAgD,CAAA,CAAA7E,QAAA;0BAAAgF,MAAA;wBAAA;0BAAA,KAAAD,UAAA,CAAAJ,CAAA,MAAAK,MAAA,GAAAD,UAAA,CAAAzH,CAAA,IAAAsH,IAAA;4BAAA,IAAAK,CAAA,GAAAD,MAAA,CAAAF,KAAA;4BACA,IAAAG,CAAA,CAAAjF,QAAA,IAAAiB,SAAA;8BAAA,IAAAiE,UAAA,OAAAT,2BAAA,CAAA5C,OAAA,EACAoD,CAAA,CAAAjF,QAAA;gCAAAmF,MAAA;8BAAA;gCAAA,KAAAD,UAAA,CAAAP,CAAA,MAAAQ,MAAA,GAAAD,UAAA,CAAA5H,CAAA,IAAAsH,IAAA;kCAAA,IAAAQ,CAAA,GAAAD,MAAA,CAAAL,KAAA;kCACA,IAAAM,CAAA,CAAApE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAwI,OAAA;oCACAW,MAAA,CAAA9H,OAAA,GAAAuL,CAAA,CAAAlE,MAAA;kCACA;gCACA;8BAAA,SAAAmE,GAAA;gCAAAH,UAAA,CAAAI,CAAA,CAAAD,GAAA;8BAAA;gCAAAH,UAAA,CAAAK,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAAN,UAAA,CAAAO,CAAA,CAAAD,GAAA;wBAAA;0BAAAN,UAAA,CAAAQ,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAb,SAAA,CAAAc,CAAA,CAAAD,GAAA;kBAAA;oBAAAb,SAAA,CAAAe,CAAA;kBAAA;gBACA;gBACA,IAAAC,IAAA,OAAAvM,GAAA;gBAAA,IAAAwM,UAAA,OAAAhB,2BAAA,CAAA5C,OAAA,EACAF,MAAA,CAAA/I,WAAA;kBAAA8M,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAAd,CAAA,MAAAe,MAAA,GAAAD,UAAA,CAAAnI,CAAA,IAAAsH,IAAA;oBAAA,IAAAe,CAAA,GAAAD,MAAA,CAAAZ,KAAA;oBACA,IAAAa,CAAA,CAAA3F,QAAA,IAAAiB,SAAA,IAAA0E,CAAA,CAAA3F,QAAA,CAAAvC,MAAA;sBAAA,IAAAmI,UAAA,OAAAnB,2BAAA,CAAA5C,OAAA,EACA8D,CAAA,CAAA3F,QAAA;wBAAA6F,MAAA;sBAAA;wBAAA,KAAAD,UAAA,CAAAjB,CAAA,MAAAkB,MAAA,GAAAD,UAAA,CAAAtI,CAAA,IAAAsH,IAAA;0BAAA,IAAAC,EAAA,GAAAgB,MAAA,CAAAf,KAAA;0BACA,IAAAD,EAAA,CAAAzD,OAAA,YAAAyD,EAAA,CAAAzD,OAAA,CAAA3H,SAAA,YAAAoL,EAAA,CAAAzD,OAAA,CAAA3H,SAAA,IAAAwH,SAAA,IAAAqB,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,YAAA4I,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAA+D,MAAA;4BACA,IAAA6E,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAAoM,QAAA,CAAAjB,EAAA,CAAAzD,OAAA,CAAA3H,SAAA;8BACA+L,IAAA,CAAAO,GAAA,CAAAlB,EAAA,CAAAnD,aAAA;4BACA;0BACA;0BACA,IAAAmD,EAAA,CAAA7E,QAAA,IAAAiB,SAAA,IAAA4D,EAAA,CAAA7E,QAAA,CAAAvC,MAAA;4BAAA,IAAAuI,UAAA,OAAAvB,2BAAA,CAAA5C,OAAA,EACAgD,EAAA,CAAA7E,QAAA;8BAAAiG,MAAA;4BAAA;8BAAA,KAAAD,UAAA,CAAArB,CAAA,MAAAsB,MAAA,GAAAD,UAAA,CAAA1I,CAAA,IAAAsH,IAAA;gCAAA,IAAAK,EAAA,GAAAgB,MAAA,CAAAnB,KAAA;gCACA,IAAAG,EAAA,CAAA7D,OAAA,YAAA6D,EAAA,CAAA7D,OAAA,CAAA3H,SAAA,YAAAwL,EAAA,CAAA7D,OAAA,CAAA3H,SAAA,IAAAwH,SAAA,IAAAqB,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,YAAA4I,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAA+D,MAAA;kCACA,IAAA6E,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAAoM,QAAA,CAAAb,EAAA,CAAA7D,OAAA,CAAA3H,SAAA;oCACA+L,IAAA,CAAAO,GAAA,CAAAd,EAAA,CAAAvD,aAAA;kCACA;gCACA;8BACA;4BAAA,SAAA2D,GAAA;8BAAAW,UAAA,CAAAV,CAAA,CAAAD,GAAA;4BAAA;8BAAAW,UAAA,CAAAT,CAAA;4BAAA;0BACA;wBACA;sBAAA,SAAAF,GAAA;wBAAAO,UAAA,CAAAN,CAAA,CAAAD,GAAA;sBAAA;wBAAAO,UAAA,CAAAL,CAAA;sBAAA;oBACA;kBACA;gBAAA,SAAAF,GAAA;kBAAAI,UAAA,CAAAH,CAAA,CAAAD,GAAA;gBAAA;kBAAAI,UAAA,CAAAF,CAAA;gBAAA;gBACA,IAAAC,IAAA,CAAArM,IAAA;kBACAqM,IAAA,CAAAU,OAAA,WAAAd,CAAA;oBACAzD,MAAA,CAAAjI,UAAA,CAAAyM,IAAA,CAAAf,CAAA;kBACA;gBACA;gBACA,IAAAgB,OAAA;gBAAA,IAAAC,UAAA,OAAA5B,2BAAA,CAAA5C,OAAA,EACAS,QAAA,CAAAgE,gBAAA;kBAAAC,MAAA;gBAAA;kBAAA,KAAAF,UAAA,CAAA1B,CAAA,MAAA4B,MAAA,GAAAF,UAAA,CAAA/I,CAAA,IAAAsH,IAAA;oBAAA,IAAA4B,EAAA,GAAAD,MAAA,CAAAzB,KAAA;oBACAsB,OAAA,KAAAI,EAAA,CAAAC,MAAA,WAAAD,EAAA,CAAAC,MAAA,iBAAAD,EAAA,CAAAE,iBAAA,WAAAF,EAAA,CAAAE,iBAAA,CAAAC,WAAA,WAAAC,MAAA,CAAAJ,EAAA,CAAAK,cAAA,KAAAL,EAAA,CAAAM,IAAA,iBAAAN,EAAA,CAAAM,IAAA;oBACAN,EAAA,CAAAO,UAAA;oBACAP,EAAA,CAAAQ,YAAA;oBACAR,EAAA,CAAAS,mBAAA;oBACAT,EAAA,CAAAU,cAAA;oBACAV,EAAA,CAAAW,qBAAA;oBACAX,EAAA,CAAAY,gBAAA;oBACAZ,EAAA,CAAAa,iBAAA;oBACAb,EAAA,CAAAc,YAAA;oBACAd,EAAA,CAAAe,mBAAA,GAAAf,EAAA,CAAAgB,UAAA;oBACAhB,EAAA,CAAAiB,cAAA,GAAAjB,EAAA,CAAAgB,UAAA;oBACAhB,EAAA,CAAAkB,iBAAA,GAAAlB,EAAA,CAAAmB,QAAA;oBACAnB,EAAA,CAAAoB,eAAA,GAAApB,EAAA,CAAAC,MAAA;oBACAD,EAAA,CAAAqB,YAAA,GAAArB,EAAA,CAAAmB,QAAA;oBACAnB,EAAA,CAAAsB,UAAA,GAAAtB,EAAA,CAAAC,MAAA;oBACAD,EAAA,CAAAuB,eAAA,GAAAvB,EAAA,CAAAwB,MAAA;oBACAxB,EAAA,CAAAyB,aAAA,GAAAzB,EAAA,CAAAM,IAAA;oBACAN,EAAA,CAAA0B,UAAA,GAAA1B,EAAA,CAAAwB,MAAA;oBACAxB,EAAA,CAAA2B,QAAA,GAAA3B,EAAA,CAAAM,IAAA;oBACAN,EAAA,CAAA4B,gBAAA,GAAA5B,EAAA,CAAA6B,YAAA;oBACA7B,EAAA,CAAA8B,qBAAA,GAAA9B,EAAA,CAAA6B,YAAA;oBACA7B,EAAA,CAAA+B,gBAAA,GAAA/B,EAAA,CAAAgC,OAAA;oBACAhC,EAAA,CAAAiC,WAAA,GAAAjC,EAAA,CAAAgC,OAAA;oBACAhC,EAAA,CAAAhE,QAAA,GAAAF,QAAA,CAAA9J,IAAA,CAAAiK,SAAA;oBACA+D,EAAA,CAAAxH,MAAA,GAAAsD,QAAA,CAAA9J,IAAA,CAAAkQ,OAAA;oBACAlC,EAAA,CAAAmC,UAAA,GAAAnC,EAAA,CAAA/D,SAAA;oBACA+D,EAAA,CAAAtH,QAAA,GAAAsH,EAAA,CAAAkC,OAAA;oBACAlC,EAAA,CAAAoC,cAAA,GAAAhC,MAAA,CAAAJ,EAAA,CAAAK,cAAA,IAAAD,MAAA,CAAAJ,EAAA,CAAAqC,eAAA,IAAAjC,MAAA,CAAAJ,EAAA,CAAA8B,qBAAA,SAAA1B,MAAA,CAAAJ,EAAA,CAAA+B,gBAAA;oBACA/B,EAAA,CAAAsC,SAAA,GAAAlC,MAAA,CAAAJ,EAAA,CAAAuC,SAAA,IAAAnC,MAAA,CAAAJ,EAAA,CAAAwC,UAAA,IAAApC,MAAA,CAAAJ,EAAA,CAAA4B,gBAAA,SAAAxB,MAAA,CAAAJ,EAAA,CAAAiC,WAAA;oBACA,IAAAjC,EAAA,CAAAyC,MAAA,WAAAzC,EAAA,CAAAyC,MAAA,WAAAzC,EAAA,CAAAyC,MAAA;sBACAtH,MAAA,CAAAvG,8BAAA,CAAA+K,IAAA,CAAAK,EAAA;oBACA;oBACA,IAAAA,EAAA,CAAAyC,MAAA;sBACAtH,MAAA,CAAAlG,gCAAA,CAAA0K,IAAA,CAAAK,EAAA;oBACA;oBACA,IAAAA,EAAA,CAAAyC,MAAA;sBACAtH,MAAA,CAAA7F,sCAAA,CAAAqK,IAAA,CAAAK,EAAA;oBACA;kBACA;gBAAA,SAAAnB,GAAA;kBAAAgB,UAAA,CAAAf,CAAA,CAAAD,GAAA;gBAAA;kBAAAgB,UAAA,CAAAd,CAAA;gBAAA;gBACA5D,MAAA,CAAArF,IAAA,CAAA4M,gBAAA,GAAA9C,OAAA;gBACA,IAAA+C,eAAA;gBAAA,IAAAC,UAAA,OAAA3E,2BAAA,CAAA5C,OAAA,EACAS,QAAA,CAAA6G,eAAA;kBAAAE,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAAzE,CAAA,MAAA0E,MAAA,GAAAD,UAAA,CAAA9L,CAAA,IAAAsH,IAAA;oBAAA,IAAAQ,EAAA,GAAAiE,MAAA,CAAAvE,KAAA;oBACAqE,eAAA,KAAA/D,EAAA,CAAAzH,WAAA,WAAAyH,EAAA,CAAAzH,WAAA,UACAyH,EAAA,CAAAkE,SAAA,WAAAlE,EAAA,CAAAkE,SAAA,UACAlE,EAAA,CAAAsD,OAAA,WAAAtD,EAAA,CAAAsD,OAAA,UACAtD,EAAA,CAAAmE,iBAAA,WAAAnE,EAAA,CAAAmE,iBAAA,UACAnE,EAAA,CAAAoE,mBAAA,WAAApE,EAAA,CAAAoE,mBAAA,UACApE,EAAA,CAAAqE,IAAA,WAAArE,EAAA,CAAAqE,IAAA,UACArE,EAAA,CAAAsE,eAAA,WAAAtE,EAAA,CAAAsE,eAAA;kBACA;gBAAA,SAAArE,GAAA;kBAAA+D,UAAA,CAAA9D,CAAA,CAAAD,GAAA;gBAAA;kBAAA+D,UAAA,CAAA7D,CAAA;gBAAA;gBACA5D,MAAA,CAAArF,IAAA,CAAAqN,aAAA,GAAAR,eAAA;gBACAxH,MAAA,CAAArF,IAAA,CAAAsN,cAAA,GAAAtH,QAAA,CAAAsH,cAAA;gBACAjI,MAAA,CAAArF,IAAA,CAAAuN,YAAA,GAAAvH,QAAA,CAAAuH,YAAA;gBACAlI,MAAA,CAAA9I,eAAA,GAAAyJ,QAAA,CAAAzJ,eAAA;gBACA8I,MAAA,CAAArF,IAAA,CAAAwN,oBAAA,GAAAxH,QAAA,CAAAyH,kBAAA;gBACApI,MAAA,CAAArF,IAAA,CAAAoG,YAAA,GAAAJ,QAAA,CAAA0H,OAAA;cACA;YAAA;YAAA;cAAA,OAAA7H,QAAA,CAAA8H,IAAA;UAAA;QAAA,GAAAjI,OAAA;MAAA;IACA;IACA7D,cAAA,WAAAA,eAAA;MAAA,IAAA+L,MAAA;MAAA,WAAAtI,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoI,SAAA;QAAA,WAAArI,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAmI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjI,IAAA,GAAAiI,SAAA,CAAAhI,IAAA;YAAA;cACA6H,MAAA,CAAAvP,OAAA;cAAA0P,SAAA,CAAAhI,IAAA;cAAA,OACA,IAAAiI,oBAAA;gBAAAtN,OAAA,EAAAkN,MAAA,CAAAlN,OAAA;gBAAAC,QAAA,EAAAiN,MAAA,CAAAjN;cAAA,GAAAa,IAAA,WAAAwE,QAAA;gBACA,IAAAA,QAAA;kBACA4H,MAAA,CAAA9N,WAAA,GAAAkG,QAAA,CAAAiI,IAAA;kBACAL,MAAA,CAAAhN,KAAA,GAAAoF,QAAA,CAAApF,KAAA;gBACA;gBACAgN,MAAA,CAAAvP,OAAA;cACA;YAAA;YAAA;cAAA,OAAA0P,SAAA,CAAAJ,IAAA;UAAA;QAAA,GAAAE,QAAA;MAAA;IACA;IACAxL,gBAAA,WAAAA,iBAAAH,EAAA;MAAA,IAAAgM,MAAA;MAAA,WAAA5I,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0I,SAAA;QAAA,WAAA3I,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAyI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvI,IAAA,GAAAuI,SAAA,CAAAtI,IAAA;YAAA;cACAmI,MAAA,CAAAtM,KAAA;cAAAyM,SAAA,CAAAtI,IAAA;cAAA,OACA,IAAAuI,mBAAA,EAAApM,EAAA,EAAAV,IAAA,WAAAwE,QAAA;gBACA,IAAAuI,EAAA;gBACA,IAAAvI,QAAA,CAAA9J,IAAA,CAAAmB,iBAAA;kBACA2I,QAAA,CAAA9J,IAAA,CAAAmB,iBAAA,CAAAmR,KAAA,MAAA5E,OAAA,WAAAP,CAAA;oBACAkF,EAAA,CAAA1E,IAAA,CAAAS,MAAA,CAAAjB,CAAA;kBACA;gBACA;gBACA6E,MAAA,CAAA7Q,iBAAA,GAAAkR,EAAA;gBACAL,MAAA,CAAAzR,WAAA,GAAAuJ,QAAA,CAAA9J,IAAA,CAAAO,WAAA;gBACAyR,MAAA,CAAA1R,UAAA,GAAAwJ,QAAA,CAAA9J,IAAA,CAAAM,UAAA;gBACA0R,MAAA,CAAAlO,IAAA,GAAAgG,QAAA,CAAA9J,IAAA;gBACAgS,MAAA,CAAAlO,IAAA,CAAA3C,iBAAA,GAAAkR,EAAA;gBACA,IAAArF,IAAA,OAAAvM,GAAA;gBACA,IAAAqJ,QAAA,CAAA9J,IAAA,CAAAkB,UAAA;kBAAA,IAAAqR,UAAA,OAAAtG,2BAAA,CAAA5C,OAAA,EACA2I,MAAA,CAAA5R,WAAA;oBAAAoS,MAAA;kBAAA;oBAAA,KAAAD,UAAA,CAAApG,CAAA,MAAAqG,MAAA,GAAAD,UAAA,CAAAzN,CAAA,IAAAsH,IAAA;sBAAA,IAAAe,CAAA,GAAAqF,MAAA,CAAAlG,KAAA;sBACA,IAAAa,CAAA,CAAA3F,QAAA,IAAAiB,SAAA,IAAA0E,CAAA,CAAA3F,QAAA,CAAAvC,MAAA;wBAAA,IAAAwN,WAAA,OAAAxG,2BAAA,CAAA5C,OAAA,EACA8D,CAAA,CAAA3F,QAAA;0BAAAkL,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAtG,CAAA,MAAAuG,OAAA,GAAAD,WAAA,CAAA3N,CAAA,IAAAsH,IAAA;4BAAA,IAAAC,CAAA,GAAAqG,OAAA,CAAApG,KAAA;4BACA,IAAAD,CAAA,CAAAzD,OAAA,YAAAyD,CAAA,CAAAzD,OAAA,CAAA3H,SAAA,YAAAoL,CAAA,CAAAzD,OAAA,CAAA3H,SAAA,IAAAwH,SAAA,IAAAqB,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,YAAA4I,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAA+D,MAAA;8BACA,IAAA6E,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAAoM,QAAA,CAAAjB,CAAA,CAAAzD,OAAA,CAAA3H,SAAA;gCACA+L,IAAA,CAAAO,GAAA,CAAAlB,CAAA,CAAAnD,aAAA;8BACA;4BACA;4BACA,IAAAmD,CAAA,CAAA7E,QAAA,IAAAiB,SAAA,IAAA4D,CAAA,CAAA7E,QAAA,CAAAvC,MAAA;8BAAA,IAAA0N,WAAA,OAAA1G,2BAAA,CAAA5C,OAAA,EACAgD,CAAA,CAAA7E,QAAA;gCAAAoL,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAAxG,CAAA,MAAAyG,OAAA,GAAAD,WAAA,CAAA7N,CAAA,IAAAsH,IAAA;kCAAA,IAAAK,CAAA,GAAAmG,OAAA,CAAAtG,KAAA;kCACA,IAAAG,CAAA,CAAA7D,OAAA,YAAA6D,CAAA,CAAA7D,OAAA,CAAA3H,SAAA,YAAAwL,CAAA,CAAA7D,OAAA,CAAA3H,SAAA,IAAAwH,SAAA,IAAAqB,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,YAAA4I,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAA+D,MAAA;oCACA,IAAA6E,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAAoM,QAAA,CAAAb,CAAA,CAAA7D,OAAA,CAAA3H,SAAA;sCACA+L,IAAA,CAAAO,GAAA,CAAAd,CAAA,CAAAvD,aAAA;oCACA;kCACA;gCACA;8BAAA,SAAA2D,GAAA;gCAAA8F,WAAA,CAAA7F,CAAA,CAAAD,GAAA;8BAAA;gCAAA8F,WAAA,CAAA5F,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAA4F,WAAA,CAAA3F,CAAA,CAAAD,GAAA;wBAAA;0BAAA4F,WAAA,CAAA1F,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA0F,UAAA,CAAAzF,CAAA,CAAAD,GAAA;kBAAA;oBAAA0F,UAAA,CAAAxF,CAAA;kBAAA;gBACA;gBACA,IAAAC,IAAA,CAAArM,IAAA;kBACAqM,IAAA,CAAAU,OAAA,WAAAd,CAAA;oBACAoF,MAAA,CAAA9Q,UAAA,CAAAyM,IAAA,CAAAf,CAAA;kBACA;gBACA;gBACA,IAAAoF,MAAA,CAAA7R,UAAA,IAAAsI,SAAA;kBAAA,IAAAoK,WAAA,OAAA5G,2BAAA,CAAA5C,OAAA,EACA2I,MAAA,CAAA7R,UAAA;oBAAA2S,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAA1G,CAAA,MAAA2G,OAAA,GAAAD,WAAA,CAAA/N,CAAA,IAAAsH,IAAA;sBAAA,IAAAC,GAAA,GAAAyG,OAAA,CAAAxG,KAAA;sBACA,IAAAD,GAAA,CAAA7E,QAAA,IAAAiB,SAAA;wBAAA,IAAAsK,WAAA,OAAA9G,2BAAA,CAAA5C,OAAA,EACAgD,GAAA,CAAA7E,QAAA;0BAAAwL,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAA5G,CAAA,MAAA6G,OAAA,GAAAD,WAAA,CAAAjO,CAAA,IAAAsH,IAAA;4BAAA,IAAAK,GAAA,GAAAuG,OAAA,CAAA1G,KAAA;4BACA,IAAAG,GAAA,CAAAjF,QAAA,IAAAiB,SAAA;8BAAA,IAAAwK,WAAA,OAAAhH,2BAAA,CAAA5C,OAAA,EACAoD,GAAA,CAAAjF,QAAA;gCAAA0L,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAA9G,CAAA,MAAA+G,OAAA,GAAAD,WAAA,CAAAnO,CAAA,IAAAsH,IAAA;kCAAA,IAAAQ,CAAA,GAAAsG,OAAA,CAAA5G,KAAA;kCACA,IAAAM,CAAA,CAAApE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAqB,OAAA;oCACA2Q,MAAA,CAAA3Q,OAAA,GAAAuL,CAAA,CAAAlE,MAAA;kCACA;kCACA,IAAAkE,CAAA,CAAApE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAsB,gBAAA;oCACA0Q,MAAA,CAAA1Q,gBAAA,GAAAsL,CAAA,CAAAlE,MAAA;kCACA;kCACA,IAAAkE,CAAA,CAAApE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAuB,eAAA;oCACAyQ,MAAA,CAAAzQ,eAAA,GAAAqL,CAAA,CAAAlE,MAAA;kCACA;gCACA;8BAAA,SAAAmE,GAAA;gCAAAoG,WAAA,CAAAnG,CAAA,CAAAD,GAAA;8BAAA;gCAAAoG,WAAA,CAAAlG,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAAkG,WAAA,CAAAjG,CAAA,CAAAD,GAAA;wBAAA;0BAAAkG,WAAA,CAAAhG,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAgG,WAAA,CAAA/F,CAAA,CAAAD,GAAA;kBAAA;oBAAAgG,WAAA,CAAA9F,CAAA;kBAAA;gBACA;gBACA,IAAAiF,MAAA,CAAA/R,MAAA,IAAAwI,SAAA;kBAAA,IAAA0K,WAAA,OAAAlH,2BAAA,CAAA5C,OAAA,EACA2I,MAAA,CAAA/R,MAAA;oBAAAmT,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAhH,CAAA,MAAAiH,OAAA,GAAAD,WAAA,CAAArO,CAAA,IAAAsH,IAAA;sBAAA,IAAAC,GAAA,GAAA+G,OAAA,CAAA9G,KAAA;sBACA,IAAAD,GAAA,CAAA7E,QAAA,IAAAiB,SAAA;wBAAA,IAAA4K,WAAA,OAAApH,2BAAA,CAAA5C,OAAA,EACAgD,GAAA,CAAA7E,QAAA;0BAAA8L,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAlH,CAAA,MAAAmH,OAAA,GAAAD,WAAA,CAAAvO,CAAA,IAAAsH,IAAA;4BAAA,IAAAK,GAAA,GAAA6G,OAAA,CAAAhH,KAAA;4BACA,IAAAD,GAAA,CAAAxE,IAAA,CAAAC,aAAA,aAAA2E,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAwB,IAAA;8BACAwQ,MAAA,CAAAxQ,IAAA,GAAAiL,GAAA,CAAApE,MAAA;4BACA;4BACA,IAAAgE,GAAA,CAAAxE,IAAA,CAAAC,aAAA,aAAA2E,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAyB,WAAA;8BACAuQ,MAAA,CAAAvQ,WAAA,GAAAgL,GAAA,CAAApE,MAAA;4BACA;4BACA,IAAAgE,GAAA,CAAAxE,IAAA,CAAAC,aAAA,aAAA2E,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAA0B,OAAA;8BACAsQ,MAAA,CAAAtQ,OAAA,GAAA+K,GAAA,CAAApE,MAAA;4BACA;4BACA,IAAAoE,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAA2B,YAAA;8BACAqQ,MAAA,CAAArQ,YAAA,GAAA8K,GAAA,CAAApE,MAAA;4BACA;0BACA;wBAAA,SAAAwE,GAAA;0BAAAwG,WAAA,CAAAvG,CAAA,CAAAD,GAAA;wBAAA;0BAAAwG,WAAA,CAAAtG,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAsG,WAAA,CAAArG,CAAA,CAAAD,GAAA;kBAAA;oBAAAsG,WAAA,CAAApG,CAAA;kBAAA;gBACA;gBACA,IAAAiF,MAAA,CAAA9R,YAAA,IAAAuI,SAAA;kBAAA,IAAA8K,WAAA,OAAAtH,2BAAA,CAAA5C,OAAA,EACA2I,MAAA,CAAA9R,YAAA;oBAAAsT,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAApH,CAAA,MAAAqH,OAAA,GAAAD,WAAA,CAAAzO,CAAA,IAAAsH,IAAA;sBAAA,IAAAC,GAAA,GAAAmH,OAAA,CAAAlH,KAAA;sBACA,IAAAD,GAAA,CAAA7E,QAAA,IAAAiB,SAAA;wBAAA,IAAAgL,WAAA,OAAAxH,2BAAA,CAAA5C,OAAA,EACAgD,GAAA,CAAA7E,QAAA;0BAAAkM,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAtH,CAAA,MAAAuH,OAAA,GAAAD,WAAA,CAAA3O,CAAA,IAAAsH,IAAA;4BAAA,IAAAK,GAAA,GAAAiH,OAAA,CAAApH,KAAA;4BACA,IAAAG,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAoB,WAAA;8BACA4Q,MAAA,CAAA5Q,WAAA,GAAAqL,GAAA,CAAApE,MAAA;4BACA;0BACA;wBAAA,SAAAwE,GAAA;0BAAA4G,WAAA,CAAA3G,CAAA,CAAAD,GAAA;wBAAA;0BAAA4G,WAAA,CAAA1G,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA0G,WAAA,CAAAzG,CAAA,CAAAD,GAAA;kBAAA;oBAAA0G,WAAA,CAAAxG,CAAA;kBAAA;gBACA;gBACA,IAAAjD,QAAA,CAAA9J,IAAA,CAAA2T,+BAAA;kBACA3B,MAAA,CAAAjO,kBAAA,GAAA+F,QAAA,CAAA9J,IAAA,CAAA2T,+BAAA;kBACA3B,MAAA,CAAApP,8BAAA,GAAAkH,QAAA,CAAA9J,IAAA,CAAA2T,+BAAA,CAAAC,8BAAA;gBACA;gBACA,IAAA9J,QAAA,CAAA9J,IAAA,CAAA6T,6BAAA;kBACA7B,MAAA,CAAAhO,oBAAA,GAAA8F,QAAA,CAAA9J,IAAA,CAAA6T,6BAAA;kBACA7B,MAAA,CAAA/O,gCAAA,GAAA6G,QAAA,CAAA9J,IAAA,CAAA6T,6BAAA,CAAAD,8BAAA;gBACA;gBACA,IAAA9J,QAAA,CAAA9J,IAAA,CAAA8T,mCAAA;kBACA9B,MAAA,CAAA/N,0BAAA,GAAA6F,QAAA,CAAA9J,IAAA,CAAA8T,mCAAA;kBACA9B,MAAA,CAAA1O,sCAAA,GAAAwG,QAAA,CAAA9J,IAAA,CAAA8T,mCAAA,CAAAF,8BAAA;gBACA;gBACA,IAAA9J,QAAA,CAAA9J,IAAA,CAAA+T,iCAAA;kBACA/B,MAAA,CAAA9N,wBAAA,GAAA4F,QAAA,CAAA9J,IAAA,CAAA+T,iCAAA;kBACA/B,MAAA,CAAArO,oCAAA,GAAAmG,QAAA,CAAA9J,IAAA,CAAA+T,iCAAA,CAAAH,8BAAA;gBACA;gBACA5B,MAAA,CAAA3R,eAAA,GAAAyJ,QAAA,CAAAzJ,eAAA;cACA;YAAA;YAAA;cAAA,OAAA8R,SAAA,CAAAV,IAAA;UAAA;QAAA,GAAAQ,QAAA;MAAA;IACA;IACApM,UAAA,WAAAA,WAAA;MAAA,IAAAmO,OAAA;MAAA,WAAA5K,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0K,SAAA;QAAA,WAAA3K,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAyK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvK,IAAA,GAAAuK,SAAA,CAAAtK,IAAA;YAAA;cACAmK,OAAA,CAAA7R,OAAA;cAAAgS,SAAA,CAAAtK,IAAA;cAAA,OACA,IAAAuK,eAAA;gBAAA5P,OAAA,EAAAwP,OAAA,CAAAxP,OAAA;gBAAAC,QAAA,EAAAuP,OAAA,CAAAvP;cAAA,GAAAa,IAAA,WAAAwE,QAAA;gBACA,IAAAA,QAAA;kBACAkK,OAAA,CAAAnQ,OAAA,GAAAiG,QAAA,CAAAiI,IAAA;kBACAiC,OAAA,CAAAtP,KAAA,GAAAoF,QAAA,CAAApF,KAAA;gBACA;gBACAsP,OAAA,CAAA7R,OAAA;cACA;YAAA;YAAA;cAAA,OAAAgS,SAAA,CAAA1C,IAAA;UAAA;QAAA,GAAAwC,QAAA;MAAA;IACA;IACA5N,YAAA,WAAAA,aAAAL,EAAA;MAAA,IAAAqO,OAAA;MAAA,WAAAjL,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+K,SAAA;QAAA,WAAAhL,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5K,IAAA,GAAA4K,SAAA,CAAA3K,IAAA;YAAA;cACAwK,OAAA,CAAA3O,KAAA;cAAA8O,SAAA,CAAA3K,IAAA;cAAA,OACA,IAAA4K,cAAA,EAAAzO,EAAA,EAAAV,IAAA,WAAAwE,QAAA;gBACAuK,OAAA,CAAA9T,WAAA,GAAAuJ,QAAA,CAAA9J,IAAA,CAAAO,WAAA;gBACA8T,OAAA,CAAA/T,UAAA,GAAAwJ,QAAA,CAAA9J,IAAA,CAAAM,UAAA;gBACA,IAAA0M,IAAA,OAAAvM,GAAA;gBACA,IAAAqJ,QAAA,CAAA9J,IAAA,CAAAkB,UAAA;kBAAA,IAAAwT,WAAA,OAAAzI,2BAAA,CAAA5C,OAAA,EACAgL,OAAA,CAAAjU,WAAA;oBAAAuU,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAvI,CAAA,MAAAwI,OAAA,GAAAD,WAAA,CAAA5P,CAAA,IAAAsH,IAAA;sBAAA,IAAAe,CAAA,GAAAwH,OAAA,CAAArI,KAAA;sBACA,IAAAa,CAAA,CAAA3F,QAAA,IAAAiB,SAAA,IAAA0E,CAAA,CAAA3F,QAAA,CAAAvC,MAAA;wBAAA,IAAA2P,WAAA,OAAA3I,2BAAA,CAAA5C,OAAA,EACA8D,CAAA,CAAA3F,QAAA;0BAAAqN,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAzI,CAAA,MAAA0I,OAAA,GAAAD,WAAA,CAAA9P,CAAA,IAAAsH,IAAA;4BAAA,IAAAC,CAAA,GAAAwI,OAAA,CAAAvI,KAAA;4BACA,IAAAD,CAAA,CAAAzD,OAAA,YAAAyD,CAAA,CAAAzD,OAAA,CAAA3H,SAAA,YAAAoL,CAAA,CAAAzD,OAAA,CAAA3H,SAAA,IAAAwH,SAAA,IAAAqB,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,YAAA4I,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAA+D,MAAA;8BACA,IAAA6E,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAAoM,QAAA,CAAAjB,CAAA,CAAAzD,OAAA,CAAA3H,SAAA;gCACA+L,IAAA,CAAAO,GAAA,CAAAlB,CAAA,CAAAnD,aAAA;8BACA;4BACA;4BACA,IAAAmD,CAAA,CAAA7E,QAAA,IAAAiB,SAAA,IAAA4D,CAAA,CAAA7E,QAAA,CAAAvC,MAAA;8BAAA,IAAA6P,WAAA,OAAA7I,2BAAA,CAAA5C,OAAA,EACAgD,CAAA,CAAA7E,QAAA;gCAAAuN,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAA3I,CAAA,MAAA4I,OAAA,GAAAD,WAAA,CAAAhQ,CAAA,IAAAsH,IAAA;kCAAA,IAAAK,CAAA,GAAAsI,OAAA,CAAAzI,KAAA;kCACA,IAAAG,CAAA,CAAA7D,OAAA,YAAA6D,CAAA,CAAA7D,OAAA,CAAA3H,SAAA,YAAAwL,CAAA,CAAA7D,OAAA,CAAA3H,SAAA,IAAAwH,SAAA,IAAAqB,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,YAAA4I,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAA+D,MAAA;oCACA,IAAA6E,QAAA,CAAA9J,IAAA,CAAAkB,UAAA,CAAAoM,QAAA,CAAAb,CAAA,CAAA7D,OAAA,CAAA3H,SAAA;sCACA+L,IAAA,CAAAO,GAAA,CAAAd,CAAA,CAAAvD,aAAA;oCACA;kCACA;gCACA;8BAAA,SAAA2D,GAAA;gCAAAiI,WAAA,CAAAhI,CAAA,CAAAD,GAAA;8BAAA;gCAAAiI,WAAA,CAAA/H,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAA+H,WAAA,CAAA9H,CAAA,CAAAD,GAAA;wBAAA;0BAAA+H,WAAA,CAAA7H,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA6H,WAAA,CAAA5H,CAAA,CAAAD,GAAA;kBAAA;oBAAA6H,WAAA,CAAA3H,CAAA;kBAAA;gBACA;gBACA,IAAAC,IAAA,CAAArM,IAAA;kBACAqM,IAAA,CAAAU,OAAA,WAAAd,CAAA;oBACAyH,OAAA,CAAAnT,UAAA,CAAAyM,IAAA,CAAAf,CAAA;kBACA;gBACA;gBACA,IAAAyF,EAAA;gBACA,IAAAvI,QAAA,CAAA9J,IAAA,CAAAmB,iBAAA;kBACA2I,QAAA,CAAA9J,IAAA,CAAAmB,iBAAA,CAAAmR,KAAA,MAAA5E,OAAA,WAAAP,CAAA;oBACAkF,EAAA,CAAA1E,IAAA,CAAAS,MAAA,CAAAjB,CAAA;kBACA;gBACA;gBACAkH,OAAA,CAAAlT,iBAAA,GAAAkR,EAAA;gBACAgC,OAAA,CAAAvQ,IAAA,GAAAgG,QAAA,CAAA9J,IAAA;gBACAqU,OAAA,CAAAvQ,IAAA,CAAA3C,iBAAA,GAAAkR,EAAA;gBACA,IAAAgC,OAAA,CAAAlU,UAAA,IAAAsI,SAAA;kBAAA,IAAAuM,WAAA,OAAA/I,2BAAA,CAAA5C,OAAA,EACAgL,OAAA,CAAAlU,UAAA;oBAAA8U,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAA7I,CAAA,MAAA8I,OAAA,GAAAD,WAAA,CAAAlQ,CAAA,IAAAsH,IAAA;sBAAA,IAAAC,GAAA,GAAA4I,OAAA,CAAA3I,KAAA;sBACA,IAAAD,GAAA,CAAA7E,QAAA,IAAAiB,SAAA;wBAAA,IAAAyM,WAAA,OAAAjJ,2BAAA,CAAA5C,OAAA,EACAgD,GAAA,CAAA7E,QAAA;0BAAA2N,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAA/I,CAAA,MAAAgJ,OAAA,GAAAD,WAAA,CAAApQ,CAAA,IAAAsH,IAAA;4BAAA,IAAAK,GAAA,GAAA0I,OAAA,CAAA7I,KAAA;4BACA,IAAAG,GAAA,CAAAjF,QAAA,IAAAiB,SAAA;8BAAA,IAAA2M,WAAA,OAAAnJ,2BAAA,CAAA5C,OAAA,EACAoD,GAAA,CAAAjF,QAAA;gCAAA6N,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAAjJ,CAAA,MAAAkJ,OAAA,GAAAD,WAAA,CAAAtQ,CAAA,IAAAsH,IAAA;kCAAA,IAAAQ,CAAA,GAAAyI,OAAA,CAAA/I,KAAA;kCACA,IAAAM,CAAA,CAAApE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAqB,OAAA;oCACAgT,OAAA,CAAAhT,OAAA,GAAAuL,CAAA,CAAAlE,MAAA;kCACA;kCACA,IAAAkE,CAAA,CAAApE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAsB,gBAAA;oCACA+S,OAAA,CAAA/S,gBAAA,GAAAsL,CAAA,CAAAlE,MAAA;kCACA;kCACA,IAAAkE,CAAA,CAAApE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAuB,eAAA;oCACA8S,OAAA,CAAA9S,eAAA,GAAAqL,CAAA,CAAAlE,MAAA;kCACA;gCACA;8BAAA,SAAAmE,GAAA;gCAAAuI,WAAA,CAAAtI,CAAA,CAAAD,GAAA;8BAAA;gCAAAuI,WAAA,CAAArI,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAAqI,WAAA,CAAApI,CAAA,CAAAD,GAAA;wBAAA;0BAAAqI,WAAA,CAAAnI,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAmI,WAAA,CAAAlI,CAAA,CAAAD,GAAA;kBAAA;oBAAAmI,WAAA,CAAAjI,CAAA;kBAAA;gBACA;gBACA,IAAAsH,OAAA,CAAApU,MAAA,IAAAwI,SAAA;kBAAA,IAAA6M,WAAA,OAAArJ,2BAAA,CAAA5C,OAAA,EACAgL,OAAA,CAAApU,MAAA;oBAAAsV,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAnJ,CAAA,MAAAoJ,OAAA,GAAAD,WAAA,CAAAxQ,CAAA,IAAAsH,IAAA;sBAAA,IAAAC,GAAA,GAAAkJ,OAAA,CAAAjJ,KAAA;sBACA,IAAAD,GAAA,CAAA7E,QAAA,IAAAiB,SAAA;wBAAA,IAAA+M,WAAA,OAAAvJ,2BAAA,CAAA5C,OAAA,EACAgD,GAAA,CAAA7E,QAAA;0BAAAiO,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAArJ,CAAA,MAAAsJ,OAAA,GAAAD,WAAA,CAAA1Q,CAAA,IAAAsH,IAAA;4BAAA,IAAAK,GAAA,GAAAgJ,OAAA,CAAAnJ,KAAA;4BACA,IAAAD,GAAA,CAAAxE,IAAA,CAAAC,aAAA,aAAA2E,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAwB,IAAA;8BACA6S,OAAA,CAAA7S,IAAA,GAAAiL,GAAA,CAAApE,MAAA;4BACA;4BACA,IAAAgE,GAAA,CAAAxE,IAAA,CAAAC,aAAA,aAAA2E,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAyB,WAAA;8BACA4S,OAAA,CAAA5S,WAAA,GAAAgL,GAAA,CAAApE,MAAA;4BACA;4BACA,IAAAgE,GAAA,CAAAxE,IAAA,CAAAC,aAAA,aAAA2E,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAA0B,OAAA;8BACA2S,OAAA,CAAA3S,OAAA,GAAA+K,GAAA,CAAApE,MAAA;4BACA;4BACA,IAAAoE,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAA2B,YAAA;8BACA0S,OAAA,CAAA1S,YAAA,GAAA8K,GAAA,CAAApE,MAAA;4BACA;0BACA;wBAAA,SAAAwE,GAAA;0BAAA2I,WAAA,CAAA1I,CAAA,CAAAD,GAAA;wBAAA;0BAAA2I,WAAA,CAAAzI,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAyI,WAAA,CAAAxI,CAAA,CAAAD,GAAA;kBAAA;oBAAAyI,WAAA,CAAAvI,CAAA;kBAAA;gBACA;gBACA,IAAAsH,OAAA,CAAAnU,YAAA,IAAAuI,SAAA;kBAAA,IAAAiN,WAAA,OAAAzJ,2BAAA,CAAA5C,OAAA,EACAgL,OAAA,CAAAnU,YAAA;oBAAAyV,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAvJ,CAAA,MAAAwJ,OAAA,GAAAD,WAAA,CAAA5Q,CAAA,IAAAsH,IAAA;sBAAA,IAAAC,GAAA,GAAAsJ,OAAA,CAAArJ,KAAA;sBACA,IAAAD,GAAA,CAAA7E,QAAA,IAAAiB,SAAA;wBAAA,IAAAmN,WAAA,OAAA3J,2BAAA,CAAA5C,OAAA,EACAgD,GAAA,CAAA7E,QAAA;0BAAAqO,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAAzJ,CAAA,MAAA0J,OAAA,GAAAD,WAAA,CAAA9Q,CAAA,IAAAsH,IAAA;4BAAA,IAAAK,GAAA,GAAAoJ,OAAA,CAAAvJ,KAAA;4BACA,IAAAG,GAAA,CAAAjE,OAAA,IAAAsB,QAAA,CAAA9J,IAAA,CAAAoB,WAAA;8BACAiT,OAAA,CAAAjT,WAAA,GAAAqL,GAAA,CAAApE,MAAA;4BACA;0BACA;wBAAA,SAAAwE,GAAA;0BAAA+I,WAAA,CAAA9I,CAAA,CAAAD,GAAA;wBAAA;0BAAA+I,WAAA,CAAA7I,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA6I,WAAA,CAAA5I,CAAA,CAAAD,GAAA;kBAAA;oBAAA6I,WAAA,CAAA3I,CAAA;kBAAA;gBACA;gBACA,IAAAjD,QAAA,CAAA9J,IAAA,CAAA8V,2BAAA;kBACAzB,OAAA,CAAAtQ,kBAAA,GAAA+F,QAAA,CAAA9J,IAAA,CAAA8V,2BAAA;kBACAzB,OAAA,CAAAzR,8BAAA,GAAAkH,QAAA,CAAA9J,IAAA,CAAA8V,2BAAA,CAAAC,0BAAA;kBACA1B,OAAA,CAAA1R,kBAAA,GAAAmH,QAAA,CAAA9J,IAAA,CAAA8V,2BAAA,CAAAE,wBAAA;gBACA;gBACA,IAAAlM,QAAA,CAAA9J,IAAA,CAAAiW,yBAAA;kBACA5B,OAAA,CAAArQ,oBAAA,GAAA8F,QAAA,CAAA9J,IAAA,CAAAiW,yBAAA;kBACA5B,OAAA,CAAApR,gCAAA,GAAA6G,QAAA,CAAA9J,IAAA,CAAAiW,yBAAA,CAAAF,0BAAA;kBACA1B,OAAA,CAAArR,oBAAA,GAAA8G,QAAA,CAAA9J,IAAA,CAAAiW,yBAAA,CAAAD,wBAAA;gBACA;gBACA,IAAAlM,QAAA,CAAA9J,IAAA,CAAAkW,+BAAA;kBACA7B,OAAA,CAAApQ,0BAAA,GAAA6F,QAAA,CAAA9J,IAAA,CAAAkW,+BAAA;kBACA7B,OAAA,CAAA/Q,sCAAA,GAAAwG,QAAA,CAAA9J,IAAA,CAAAkW,+BAAA,CAAAH,0BAAA;kBACA1B,OAAA,CAAAhR,0BAAA,GAAAyG,QAAA,CAAA9J,IAAA,CAAAkW,+BAAA,CAAAF,wBAAA;gBACA;gBACA,IAAAlM,QAAA,CAAA9J,IAAA,CAAAmW,6BAAA;kBACA9B,OAAA,CAAAnQ,wBAAA,GAAA4F,QAAA,CAAA9J,IAAA,CAAAmW,6BAAA;kBACA9B,OAAA,CAAA1Q,oCAAA,GAAAmG,QAAA,CAAA9J,IAAA,CAAAmW,6BAAA,CAAAJ,0BAAA;kBACA1B,OAAA,CAAA3Q,wBAAA,GAAAoG,QAAA,CAAA9J,IAAA,CAAAmW,6BAAA,CAAAH,wBAAA;gBACA;gBACA3B,OAAA,CAAAhU,eAAA,GAAAyJ,QAAA,CAAAzJ,eAAA;cACA;YAAA;YAAA;cAAA,OAAAmU,SAAA,CAAA/C,IAAA;UAAA;QAAA,GAAA6C,QAAA;MAAA;IACA;IACA8B,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,OAAA;MAAA,WAAAlN,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAgN,SAAA;QAAA,IAAAC,WAAA,EAAAC,OAAA,EAAAtK,CAAA,EAAAuK,WAAA,EAAAC,OAAA,EAAAC,CAAA;QAAA,WAAAtN,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlN,IAAA,GAAAkN,SAAA,CAAAjN,IAAA;YAAA;cAAA,MACAyM,OAAA,CAAA1V,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAgF,eAAA,CAAAC,MAAA;gBAAA6R,SAAA,CAAAjN,IAAA;gBAAA;cAAA;cAAAiN,SAAA,CAAAjN,IAAA;cAAA,OACAyM,OAAA,CAAA1V,MAAA,CAAAyE,QAAA;YAAA;cAEAiR,OAAA,CAAA9V,IAAA,CAAAuW,KAAA;cACAT,OAAA,CAAAxS,IAAA,CAAAsN,cAAA,GAAAiF,GAAA;cAAAG,WAAA,OAAAvK,2BAAA,CAAA5C,OAAA,EACAiN,OAAA,CAAA1V,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAgF,eAAA,IAAAwC,QAAA;cAAA;gBAAA,KAAAgP,WAAA,CAAArK,CAAA,MAAAsK,OAAA,GAAAD,WAAA,CAAA1R,CAAA,IAAAsH,IAAA;kBAAAD,CAAA,GAAAsK,OAAA,CAAAnK,KAAA;kBACA,IAAA+J,GAAA,CAAA/I,QAAA,CAAAnB,CAAA,CAAAjD,aAAA;oBACA,IAAAiD,CAAA,CAAAsE,MAAA;sBACA6F,OAAA,CAAA9V,IAAA,CAAA+M,GAAA,CAAApB,CAAA,CAAAsE,MAAA;oBACA;kBACA;kBACA,IAAAtE,CAAA,CAAA3E,QAAA;oBAAAkP,WAAA,OAAAzK,2BAAA,CAAA5C,OAAA,EACA8C,CAAA,CAAA3E,QAAA;oBAAA;sBAAA,KAAAkP,WAAA,CAAAvK,CAAA,MAAAwK,OAAA,GAAAD,WAAA,CAAA5R,CAAA,IAAAsH,IAAA;wBAAAwK,CAAA,GAAAD,OAAA,CAAArK,KAAA;wBACA,IAAA+J,GAAA,CAAA/I,QAAA,CAAAsJ,CAAA,CAAA1N,aAAA;0BACA,IAAAiD,CAAA,CAAAsE,MAAA;4BACA6F,OAAA,CAAA9V,IAAA,CAAA+M,GAAA,CAAApB,CAAA,CAAAsE,MAAA;0BACA;0BACA,IAAAmG,CAAA,CAAAnG,MAAA;4BACA6F,OAAA,CAAA9V,IAAA,CAAA+M,GAAA,CAAAqJ,CAAA,CAAAnG,MAAA;0BACA;wBACA;sBACA;oBAAA,SAAA5D,GAAA;sBAAA6J,WAAA,CAAA5J,CAAA,CAAAD,GAAA;oBAAA;sBAAA6J,WAAA,CAAA3J,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAA2J,WAAA,CAAA1J,CAAA,CAAAD,GAAA;cAAA;gBAAA2J,WAAA,CAAAzJ,CAAA;cAAA;cACA,IAAAsJ,GAAA,CAAA/I,QAAA;gBACAgJ,OAAA,CAAA9V,IAAA,CAAA+M,GAAA;cACA;cACA+I,OAAA,CAAAU,YAAA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAArF,IAAA;UAAA;QAAA,GAAA8E,QAAA;MAAA;IACA;IACAhR,OAAA,WAAAA,QAAAT,CAAA;MAAA,IAAAmS,WAAA,OAAAhL,2BAAA,CAAA5C,OAAA,EACA,KAAAzI,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAgF,eAAA,IAAAwC,QAAA;QAAA0P,OAAA;MAAA;QAAA,KAAAD,WAAA,CAAA9K,CAAA,MAAA+K,OAAA,GAAAD,WAAA,CAAAnS,CAAA,IAAAsH,IAAA;UAAA,IAAAD,CAAA,GAAA+K,OAAA,CAAA5K,KAAA;UACA,IAAAH,CAAA,CAAAjD,aAAA,IAAApE,CAAA;YACA,KAAA9D,aAAA,GAAAmL,CAAA,CAAAsE,MAAA;UACA;UACA,IAAAtE,CAAA,CAAA3E,QAAA;YAAA,IAAA2P,WAAA,OAAAlL,2BAAA,CAAA5C,OAAA,EACA8C,CAAA,CAAA3E,QAAA;cAAA4P,OAAA;YAAA;cAAA,KAAAD,WAAA,CAAAhL,CAAA,MAAAiL,OAAA,GAAAD,WAAA,CAAArS,CAAA,IAAAsH,IAAA;gBAAA,IAAAwK,CAAA,GAAAQ,OAAA,CAAA9K,KAAA;gBACA,IAAAsK,CAAA,CAAA1N,aAAA,IAAApE,CAAA;kBACA,KAAA9D,aAAA,GAAAmL,CAAA,CAAAsE,MAAA;gBACA;cACA;YAAA,SAAA5D,GAAA;cAAAsK,WAAA,CAAArK,CAAA,CAAAD,GAAA;YAAA;cAAAsK,WAAA,CAAApK,CAAA;YAAA;UACA;QACA;MAAA,SAAAF,GAAA;QAAAoK,WAAA,CAAAnK,CAAA,CAAAD,GAAA;MAAA;QAAAoK,WAAA,CAAAlK,CAAA;MAAA;IACA;IACAsK,oBAAA,WAAAA,qBAAAhB,GAAA;MACA,KAAAvS,IAAA,CAAA3C,iBAAA,GAAAkV,GAAA;MACA,KAAAlV,iBAAA,GAAAkV,GAAA;IACA;IACAiB,sBAAA,WAAAA,uBAAA/P,IAAA;MACA,KAAAzD,IAAA,CAAA5C,UAAA,CAAAyM,IAAA,CAAApG,IAAA,CAAAqB,OAAA,CAAA3H,SAAA;IACA;IACAsW,wBAAA,WAAAA,yBAAAhQ,IAAA;MACA,KAAAzD,IAAA,CAAA5C,UAAA,QAAA4C,IAAA,CAAA5C,UAAA,CAAAsW,MAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,IAAAlQ,IAAA,CAAAqB,OAAA,CAAA3H,SAAA;MACA;IACA;IACAyW,WAAA,WAAAA,YAAAvK,CAAA;MAAA,IAAAwK,OAAA;MACA,IAAAxK,CAAA;QACA,IAAAyK,iBAAA,IAAAtS,IAAA,WAAA6H,CAAA;UACA,IAAA0K,GAAA,GAAA1K,CAAA,CAAAnN,IAAA;UACA,IAAA6X,GAAA,CAAAC,QAAA,GAAA7S,MAAA;YACA,IAAA8S,CAAA,OAAAF,GAAA,CAAAC,QAAA,GAAA7S,MAAA;YACA,SAAA+S,CAAA,MAAAA,CAAA,GAAAD,CAAA,EAAAC,CAAA;cACAH,GAAA,SAAAA,GAAA;YACA;UACA;UACA,IAAAI,IAAA,OAAApN,IAAA;UACA,IAAAxG,KAAA,IAAA4T,IAAA,CAAAC,QAAA,KAAA9J,MAAA,CAAAuJ,OAAA,CAAAxT,GAAA,CAAAE,KAAA,GAAAyT,QAAA;UACA,IAAAK,IAAA,IAAAF,IAAA,CAAAG,WAAA,MAAA/T,KAAA,oBAAAyT,QAAA,GAAAO,SAAA;UACAV,OAAA,CAAAxT,GAAA,CAAAI,KAAA,GAAAoT,OAAA,CAAAxT,GAAA,CAAAC,gBAAA,GAAA+T,IAAA,IAAA9T,KAAA,CAAAY,MAAA,cAAAZ,KAAA,GAAAA,KAAA,IAAAwT,GAAA,CAAAC,QAAA;QACA;MACA;QACA,KAAAlW,eAAA;MACA;IACA;IACA0W,UAAA,WAAAA,WAAA;MACA,KAAAxU,IAAA,CAAAS,KAAA,QAAAJ,GAAA,CAAAI,KAAA;MACA,KAAA3C,eAAA;IACA;IACA2W,cAAA,WAAAA,eAAAlC,GAAA;MACA,IAAAmC,EAAA;MACA,IAAAX,GAAA;MACA,IAAAxB,GAAA;QACA,IAAAwB,GAAA,CAAAY,IAAA,MAAAlY,WAAA;UACA,KAAAA,WAAA,QAAAA,WAAA,CAAAmY,OAAA;UACA,KAAA5U,IAAA,CAAAvD,WAAA,QAAAA,WAAA;UACA,IAAAoY,GAAA,QAAApY,WAAA,CAAA+R,KAAA;UACA,IAAAsG,EAAA,GAAAD,GAAA,IAAAD,OAAA,CAAAF,EAAA;UACA,KAAAjY,WAAA,GAAAoY,GAAA,CAAA1T,MAAA,QAAA0T,GAAA,SAAAE,MAAA,CAAAD,EAAA,OAAAC,MAAA,CAAAF,GAAA,UAAAE,MAAA,CAAAD,EAAA;QACA;UACA,KAAAE,QAAA,CAAAC,OAAA;QACA;MACA;MACA,IAAA1C,GAAA;QACA,IAAAwB,GAAA,CAAAY,IAAA,MAAAnY,UAAA;UACA,KAAAA,UAAA,QAAAA,UAAA,CAAAoY,OAAA;UACA,KAAA5U,IAAA,CAAAxD,UAAA,QAAAA,UAAA;UACA,IAAAqY,IAAA,QAAArY,UAAA,CAAAgS,KAAA;UACA,IAAAsG,EAAA,GAAAD,IAAA,IAAAD,OAAA,CAAAF,EAAA;UACA,KAAAlY,UAAA,GAAAqY,IAAA,CAAA1T,MAAA,QAAA0T,IAAA,SAAAE,MAAA,CAAAD,EAAA,OAAAC,MAAA,CAAAF,IAAA,UAAAE,MAAA,CAAAD,EAAA;QACA;UACA,KAAAE,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAC,SAAA,WAAAA,UAAA/W,IAAA,EAAAoU,GAAA;MACA,IAAA7V,IAAA;MACA,IAAAyB,IAAA;QACA,KAAAO,eAAA,GAAA6T,GAAA;QACA7V,IAAA,QAAAiC,mBAAA,IACA;UAAAR,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAgX,WAAA;UAAAC,OAAA;QAAA;MAEA;MACA,IAAAjX,IAAA;QACA,KAAAY,iBAAA,GAAAwT,GAAA;QACA7V,IAAA,QAAAsC,qBAAA,IACA;UAAAb,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA,GACA;UAAAjX,IAAA;UAAAkX,aAAA;UAAAD,OAAA;QAAA;MACA;MAAA,IAAAE,WAAA,OAAAnN,2BAAA,CAAA5C,OAAA,EACA7I,IAAA;QAAA6Y,OAAA;MAAA;QAAA,IAAAC,KAAA,YAAAA,MAAA;UAAA,IAAAC,OAAA,GAAAF,OAAA,CAAA/M,KAAA;UACA+J,GAAA,CAAA3I,OAAA,WAAAP,CAAA;YACA,SAAAqM,IAAA,IAAArM,CAAA;cACA,IAAAqM,IAAA,IAAAD,OAAA,CAAAtX,IAAA;gBACAsX,OAAA,CAAAL,OAAA,GAAAK,OAAA,CAAAL,OAAA,SAAA/L,CAAA,CAAAqM,IAAA,YAAArM,CAAA,CAAAqM,IAAA,SAAAD,OAAA,CAAAL,OAAA,IAAA/L,CAAA,CAAAqM,IAAA,kBAAArM,CAAA,CAAAqM,IAAA;cACA;YACA;UACA;QACA;QARA,KAAAJ,WAAA,CAAAjN,CAAA,MAAAkN,OAAA,GAAAD,WAAA,CAAAtU,CAAA,IAAAsH,IAAA;UAAAkN,KAAA;QAAA;MAQA,SAAAzM,GAAA;QAAAuM,WAAA,CAAAtM,CAAA,CAAAD,GAAA;MAAA;QAAAuM,WAAA,CAAArM,CAAA;MAAA;IACA;IACA0M,MAAA,WAAAA,OAAA;MACA,KAAA/T,KAAA;MACA,KAAAvD,OAAA;MACA,KAAAD,IAAA;MACA,KAAAN,eAAA;IACA;IACA8D,KAAA,WAAAA,MAAA;MACA,KAAA5B,IAAA;QACA4V,KAAA;QACAnV,KAAA;QACAoV,SAAA;QACAnY,IAAA;QACAC,WAAA;QACAC,OAAA;QACAC,YAAA;QACAiY,YAAA;QACAC,cAAA;QACAnP,WAAA;QACAE,aAAA;QACAvJ,OAAA;QACAC,gBAAA;QACAC,eAAA;QACAH,WAAA;QACA0Y,aAAA;QACAC,aAAA;QACAC,aAAA;QACAC,aAAA;QACAC,eAAA;QACAlQ,QAAA;QACAE,YAAA;QACAE,eAAA;QACAE,kBAAA;QACAE,oBAAA;QACArJ,iBAAA;QACA2J,YAAA;QACAqP,uBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,eAAA;QACAvP,gBAAA;QACAwP,eAAA;QACAja,WAAA;QACA8K,YAAA;QACAoP,MAAA;QACAC,YAAA;QACArJ,YAAA;QACA/Q,UAAA;QACA6K,eAAA;QACAwP,SAAA;QACA9O,WAAA;QACA9B,eAAA;QACAwB,KAAA;QACAE,iBAAA;QACAvK,UAAA;QACA0Z,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,KAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;QACAC,eAAA;QACAC,WAAA;QACAC,SAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,WAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;QACAC,eAAA;QACA3K,cAAA;QACAV,gBAAA;QACA5E,gBAAA;QACAqF,aAAA;QACA6K,kBAAA;QACAC,cAAA;QACAC,aAAA;QACAC,eAAA;QACAC,WAAA;QACAC,QAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,WAAA;QACAC,MAAA;QACAC,aAAA;QACAC,eAAA;QACAC,aAAA;QACAC,eAAA;QACAC,GAAA;QACAC,aAAA;QACAC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,MAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,WAAA;QACAC,MAAA;QACA9R,MAAA;MACA;MACA,KAAAhI,kBAAA;QACA+Z,mBAAA;QACApE,KAAA;QACA3P,eAAA;QACA9I,SAAA;QACAsK,KAAA;QACAwS,kBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,QAAA;QACAC,WAAA;QACAC,WAAA;QACA/S,aAAA;QACAgT,KAAA;QACAC,MAAA;QACAnT,iBAAA;QACAoT,kBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAA9b,oBAAA;QACA+b,iBAAA;QACArG,KAAA;QACA3P,eAAA;QACAiW,mBAAA;QACAC,kBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,cAAA;QACAC,iBAAA;QACAvB,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAA7b,0BAAA;QACAqc,mBAAA;QACA5G,KAAA;QACA3P,eAAA;QACAwW,gBAAA;QACAC,eAAA;QACAC,YAAA;QACAC,eAAA;QACAC,WAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,mBAAA;QACAlC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAA5b,wBAAA;QACA+c,iBAAA;QACAvH,KAAA;QACAwH,mBAAA;QACAC,mBAAA;QACArC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,oBAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,cAAA;MACA;MACA,KAAAvf,WAAA;MACA,KAAAD,UAAA;MACA,KAAAW,SAAA;MACA,KAAAE,iBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,eAAA;MACA,KAAAC,IAAA;MACA,KAAAC,WAAA;MACA,KAAAC,OAAA;MACA,KAAAC,YAAA;MACA,KAAAT,UAAA;MACA,KAAAkB,WAAA;MACA,KAAAC,eAAA;MACA,KAAAC,iBAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,eAAA;MACA,KAAAE,mBAAA;MACA,KAAAC,kBAAA;MACA,KAAAC,8BAAA;MACA,KAAAC,iBAAA;MACA,KAAAE,qBAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,gCAAA;MACA,KAAAC,2BAAA;MACA,KAAAC,uBAAA;MACA,KAAAE,0BAAA;MACA,KAAAC,sCAAA;MACA,KAAAC,oBAAA;MACA,KAAAC,qBAAA;MACA,KAAAE,wBAAA;MACA,KAAAC,oCAAA;MACA,KAAAyd,SAAA;IACA;IACA,WACAC,UAAA,WAAAA,WAAAlU,CAAA;MAAA,IAAAmU,OAAA;MACA,KAAAxd,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA8D,MAAA,YAAAnB,IAAA,CAAA3C,iBAAA,CAAA2W,QAAA;MACA,SAAAjW,SAAA;QACA,KAAAiC,IAAA,CAAAyd,aAAA;QACA,KAAAzd,IAAA,CAAAoW,eAAA;QACA,KAAApW,IAAA,CAAAgW,aAAA,OAAA0H,eAAA,MAAA3W,IAAA;MACA;MACA,KAAA4W,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAL,OAAA,CAAArf,IAAA,YAAAqf,OAAA,CAAArf,IAAA,iBAAAqf,OAAA,CAAAzf,SAAA;UACA,IAAAsL,CAAA;YACAmU,OAAA,CAAAxd,IAAA,CAAA4V,KAAA;UACA;UACA,IAAAiI,KAAA;YACA,IAAAL,OAAA,CAAAxd,IAAA,CAAA4V,KAAA;cACA4H,OAAA,CAAAxd,IAAA,CAAAoW,eAAA;cACA,IAAA0H,iBAAA,EAAAN,OAAA,CAAAxd,IAAA,EAAAwB,IAAA,WAAAwE,QAAA;gBACAwX,OAAA,CAAAO,OAAA,CAAAP,OAAA,CAAAxd,IAAA,CAAA4V,KAAA;gBACA4H,OAAA,CAAAQ,MAAA,CAAAC,UAAA;gBACAT,OAAA,CAAApf,IAAA;gBACAof,OAAA,CAAAzb,UAAA;cACA;YACA;cACAyb,OAAA,CAAAxd,IAAA,CAAAoW,eAAA;cACA,IAAA8H,cAAA,EAAAV,OAAA,CAAAxd,IAAA,EAAAwB,IAAA,WAAAwE,QAAA;gBACAwX,OAAA,CAAAxd,IAAA,CAAA4V,KAAA,GAAA5P,QAAA,CAAA9J,IAAA;gBACAshB,OAAA,CAAAO,OAAA,CAAA/X,QAAA,CAAA9J,IAAA;gBACAshB,OAAA,CAAAQ,MAAA,CAAAC,UAAA;gBACAT,OAAA,CAAApf,IAAA;gBACAof,OAAA,CAAAzb,UAAA;cACA;YACA;UACA;QACA;QACA,IAAAyb,OAAA,CAAArf,IAAA;UACA,IAAAkL,CAAA;YACAmU,OAAA,CAAAxd,IAAA,CAAAme,SAAA;UACA;UACA,IAAAN,KAAA;YACA,IAAAL,OAAA,CAAAxd,IAAA,CAAAme,SAAA;cACAX,OAAA,CAAAxd,IAAA,CAAAoW,eAAA;cACA,IAAAgI,sBAAA,EAAAZ,OAAA,CAAAxd,IAAA,EAAAwB,IAAA,WAAAwE,QAAA;gBACA,KAAAwX,OAAA,CAAAzf,SAAA;kBACAyf,OAAA,CAAAO,OAAA,CAAAP,OAAA,CAAAxd,IAAA,CAAAme,SAAA;kBACAX,OAAA,CAAAQ,MAAA,CAAAC,UAAA;kBACAT,OAAA,CAAApf,IAAA;kBACAof,OAAA,CAAA3b,cAAA;gBACA;cACA;YACA;cACA2b,OAAA,CAAAxd,IAAA,CAAAoW,eAAA;cACA,IAAAiI,mBAAA,EAAAb,OAAA,CAAAxd,IAAA,EAAAwB,IAAA,WAAAwE,QAAA;gBACAwX,OAAA,CAAAxd,IAAA,CAAAme,SAAA,GAAAnY,QAAA,CAAA9J,IAAA;gBACAshB,OAAA,CAAAO,OAAA,CAAA/X,QAAA,CAAA9J,IAAA;gBACAshB,OAAA,CAAAQ,MAAA,CAAAC,UAAA;gBACAT,OAAA,CAAApf,IAAA;gBACAof,OAAA,CAAA3b,cAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACAyc,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,KAAAC,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAvgB,IAAA;MACA,GAAAqD,IAAA,WAAAmd,GAAA;QACA,IAAAA,GAAA;UACAJ,OAAA,CAAAve,IAAA,CAAA3C,iBAAA,GAAAkhB,OAAA,CAAAve,IAAA,CAAA3C,iBAAA,YAAAkhB,OAAA,CAAAve,IAAA,CAAA3C,iBAAA,CAAA8D,MAAA,OAAAod,OAAA,CAAAve,IAAA,CAAA3C,iBAAA,CAAA2W,QAAA;UACAuK,OAAA,CAAAZ,KAAA,SAAAC,QAAA,WAAAC,KAAA;YACAU,OAAA,CAAAve,IAAA,CAAAyd,aAAA;YACAc,OAAA,CAAAve,IAAA,CAAAoW,eAAA;YACA,IAAAyH,KAAA;cACA,IAAAO,sBAAA,EAAAG,OAAA,CAAAve,IAAA,EAAAwB,IAAA,WAAAwE,QAAA;gBACAuY,OAAA,CAAAP,MAAA,CAAAC,UAAA;gBACAM,OAAA,CAAAngB,IAAA;gBACAmgB,OAAA,CAAA1c,cAAA;cACA;YACA;UACA;QACA;MACA;IACA;IACAkc,OAAA,WAAAA,QAAA7b,EAAA;MACA,KAAA0c,aAAA,CAAA1c,EAAA;MACA,KAAA2c,eAAA,CAAA3c,EAAA;MACA,KAAA4c,qBAAA,CAAA5c,EAAA;MACA,KAAA6c,mBAAA,CAAA7c,EAAA;IACA;IACA0c,aAAA,WAAAA,cAAA1c,EAAA;MAAA,IAAA8c,OAAA;MACA,SAAAhf,IAAA,CAAAme,SAAA,iBAAAne,IAAA,CAAA4V,KAAA;QACA,KAAAZ,QAAA,CAAAiK,KAAA;MACA;QACA,KAAAjf,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA8D,MAAA,YAAAnB,IAAA,CAAA3C,iBAAA,CAAA2W,QAAA;QACA,SAAA7V,IAAA,uBAAAJ,SAAA;UACA,SAAAe,8BAAA,CAAAqC,MAAA;YACA,KAAAlB,kBAAA,CAAA6P,8BAAA,QAAAhR,8BAAA;UACA;UACA,KAAAmB,kBAAA,CAAAke,SAAA,UAAAjc,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAAme,SAAA;UACA,KAAAle,kBAAA,CAAA0M,MAAA;UACA,KAAA3M,IAAA,CAAA6P,+BAAA,QAAA5P,kBAAA;UACA,IAAAif,6BAAA,OAAAlf,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACA8c,OAAA,CAAAhK,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;QACA,SAAAhhB,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAAe,8BAAA,CAAAqC,MAAA;YACA,KAAAlB,kBAAA,CAAAgS,0BAAA,QAAAnT,8BAAA;UACA;UACA,SAAAJ,eAAA,CAAAyC,MAAA;YACA,KAAAlB,kBAAA,CAAAmf,qBAAA,QAAA1gB,eAAA;UACA;UACA,SAAAG,kBAAA,CAAAsC,MAAA;YACA,KAAAlB,kBAAA,CAAAiS,wBAAA,QAAArT,kBAAA;UACA;UACA,KAAAoB,kBAAA,CAAA2V,KAAA,UAAA1T,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAA4V,KAAA;UACA,KAAA3V,kBAAA,CAAA0M,MAAA;UACA,KAAA3M,IAAA,CAAAgS,2BAAA,QAAA/R,kBAAA;UACA,IAAAof,wBAAA,OAAArf,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACA8c,OAAA,CAAAhK,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;MACA;IACA;IACAN,eAAA,WAAAA,gBAAA3c,EAAA;MAAA,IAAAod,OAAA;MACA,SAAAtf,IAAA,CAAAme,SAAA,iBAAAne,IAAA,CAAA4V,KAAA;QACA,KAAAZ,QAAA,CAAAiK,KAAA;MACA;QACA,KAAAjf,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA8D,MAAA,YAAAnB,IAAA,CAAA3C,iBAAA,CAAA2W,QAAA;QACA,SAAA7V,IAAA,uBAAAJ,SAAA;UACA,SAAAoB,gCAAA,CAAAgC,MAAA;YACA,KAAAjB,oBAAA,CAAA4P,8BAAA,QAAA3Q,gCAAA;UACA;UACA,KAAAe,oBAAA,CAAAie,SAAA,UAAAjc,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAAme,SAAA;UACA,KAAAje,oBAAA,CAAAyM,MAAA;UACA,KAAA3M,IAAA,CAAA+P,6BAAA,QAAA7P,oBAAA;UACA,IAAAqf,+BAAA,OAAAvf,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACAod,OAAA,CAAAtK,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;QACA,SAAAhhB,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAAoB,gCAAA,CAAAgC,MAAA;YACA,KAAAjB,oBAAA,CAAA+R,0BAAA,QAAA9S,gCAAA;UACA;UACA,SAAAJ,iBAAA,CAAAoC,MAAA;YACA,KAAAjB,oBAAA,CAAAsf,uBAAA,QAAAzgB,iBAAA;UACA;UACA,SAAAG,oBAAA,CAAAiC,MAAA;YACA,KAAAjB,oBAAA,CAAAgS,wBAAA,QAAAhT,oBAAA;UACA;UACA,KAAAgB,oBAAA,CAAA0V,KAAA,UAAA1T,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAA4V,KAAA;UACA,KAAA1V,oBAAA,CAAAyM,MAAA;UACA,KAAA3M,IAAA,CAAAmS,yBAAA,QAAAjS,oBAAA;UACA,IAAAuf,0BAAA,OAAAzf,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACAod,OAAA,CAAAtK,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;MACA;IACA;IACAL,qBAAA,WAAAA,sBAAA5c,EAAA;MAAA,IAAAwd,OAAA;MACA,SAAA1f,IAAA,CAAAme,SAAA,iBAAAne,IAAA,CAAA4V,KAAA;QACA,KAAAZ,QAAA,CAAAiK,KAAA;MACA;QACA,KAAAjf,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA8D,MAAA,YAAAnB,IAAA,CAAA3C,iBAAA,CAAA2W,QAAA;QACA,SAAA7V,IAAA,uBAAAJ,SAAA;UACA,SAAAyB,sCAAA,CAAA2B,MAAA;YACA,KAAAhB,0BAAA,CAAA2P,8BAAA,QAAAtQ,sCAAA;UACA;UACA,KAAAW,0BAAA,CAAAge,SAAA,UAAAjc,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAAme,SAAA;UACA,KAAAhe,0BAAA,CAAAwM,MAAA;UACA,KAAA3M,IAAA,CAAAgQ,mCAAA,QAAA7P,0BAAA;UACA,IAAAwf,qCAAA,OAAA3f,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACAwd,OAAA,CAAA1K,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;QACA,SAAAhhB,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAAyB,sCAAA,CAAA2B,MAAA;YACA,KAAAhB,0BAAA,CAAA8R,0BAAA,QAAAzS,sCAAA;UACA;UACA,SAAAD,0BAAA,CAAA4B,MAAA;YACA,KAAAhB,0BAAA,CAAA+R,wBAAA,QAAA3S,0BAAA;UACA;UACA,KAAAY,0BAAA,CAAAyV,KAAA,UAAA1T,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAA4V,KAAA;UACA,KAAAzV,0BAAA,CAAAwM,MAAA;UACA,KAAA3M,IAAA,CAAAoS,+BAAA,QAAAjS,0BAAA;UACA,IAAAyf,gCAAA,OAAA5f,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACAwd,OAAA,CAAA1K,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;MACA;IACA;IACAJ,mBAAA,WAAAA,oBAAA7c,EAAA;MAAA,IAAA2d,OAAA;MACA,SAAA7f,IAAA,CAAAme,SAAA,iBAAAne,IAAA,CAAA4V,KAAA;QACA,KAAAZ,QAAA,CAAAiK,KAAA;MACA;QACA,KAAAjf,IAAA,CAAA3C,iBAAA,QAAA2C,IAAA,CAAA3C,iBAAA,iBAAA2C,IAAA,CAAA3C,iBAAA,CAAA8D,MAAA,YAAAnB,IAAA,CAAA3C,iBAAA,CAAA2W,QAAA;QACA,SAAA7V,IAAA,uBAAAJ,SAAA;UACA,SAAA8B,oCAAA,CAAAsB,MAAA;YACA,KAAAf,wBAAA,CAAA0P,8BAAA,QAAAjQ,oCAAA;UACA;UACA,KAAAO,wBAAA,CAAA+d,SAAA,UAAAjc,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAAme,SAAA;UACA,KAAA/d,wBAAA,CAAAuM,MAAA;UACA,KAAA3M,IAAA,CAAAiQ,iCAAA,QAAA7P,wBAAA;UACA,IAAA0f,mCAAA,OAAA9f,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACA2d,OAAA,CAAA7K,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;QACA,SAAAhhB,IAAA,iBAAAA,IAAA,sBAAAJ,SAAA;UACA,SAAA8B,oCAAA,CAAAsB,MAAA;YACA,KAAAf,wBAAA,CAAA6R,0BAAA,QAAApS,oCAAA;UACA;UACA,SAAAD,wBAAA,CAAAuB,MAAA;YACA,KAAAf,wBAAA,CAAA8R,wBAAA,QAAAtS,wBAAA;UACA;UACA,KAAAQ,wBAAA,CAAAwV,KAAA,UAAA1T,EAAA,eAAAA,EAAA,QAAAlC,IAAA,CAAA4V,KAAA;UACA,KAAAxV,wBAAA,CAAAuM,MAAA;UACA,KAAA3M,IAAA,CAAAqS,6BAAA,QAAAjS,wBAAA;UACA,IAAA2f,8BAAA,OAAA/f,IAAA,EAAAwB,IAAA;YACA,WAAAU,EAAA;cACA2d,OAAA,CAAA7K,QAAA,CAAAmK,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAa,YAAA,WAAAA,aAAA;MACA;MACAC,OAAA,CAAAC,GAAA,MAAAlgB,IAAA,CAAAgX,WAAA;IAEA;EACA;AACA;AAAAmJ,OAAA,CAAA5a,OAAA,GAAA6a,QAAA"}]}