{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\hiprint\\plugins\\qrcode.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\hiprint\\plugins\\qrcode.js", "mtime": 1737429728507}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["window", "QRCode", "QR8bitByte", "data", "mode", "QRMode", "MODE_8BIT_BYTE", "parsedData", "i", "l", "length", "byteArray", "code", "charCodeAt", "push", "Array", "prototype", "concat", "apply", "unshift", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "write", "put", "QRCodeModel", "typeNumber", "errorCorrectLevel", "modules", "moduleCount", "dataCache", "dataList", "addData", "newData", "isDark", "row", "col", "Error", "getModuleCount", "make", "makeImpl", "getBestMaskPattern", "test", "maskPattern", "setupPositionProbePattern", "setupPositionAdjustPattern", "setupTimingPattern", "setupTypeInfo", "setupTypeNumber", "createData", "mapData", "r", "c", "minLostPoint", "pattern", "lostPoint", "QRUtil", "getLostPoint", "createMovieClip", "target_mc", "instance_name", "depth", "qr_mc", "createEmptyMovieClip", "cs", "y", "x", "dark", "beginFill", "moveTo", "lineTo", "endFill", "pos", "getPatternPosition", "j", "bits", "getBCHTypeNumber", "mod", "Math", "floor", "getBCHTypeInfo", "inc", "bitIndex", "byteIndex", "mask", "getMask", "PAD0", "PAD1", "rsBlocks", "QRRSBlock", "getRSBlocks", "QRBitBuffer", "getLengthInBits", "totalDataCount", "dataCount", "putBit", "createBytes", "offset", "maxDcCount", "maxEcCount", "dcdata", "ecdata", "dcCount", "ecCount", "totalCount", "max", "rsPoly", "getErrorCorrectPolynomial", "rawPoly", "QRPolynomial", "modPoly", "modIndex", "get", "totalCodeCount", "index", "MODE_NUMBER", "MODE_ALPHA_NUM", "MODE_KANJI", "QRErrorCorrectLevel", "L", "M", "Q", "H", "QRMaskPattern", "PATTERN000", "PATTERN001", "PATTERN010", "PATTERN011", "PATTERN100", "PATTERN101", "PATTERN110", "PATTERN111", "PATTERN_POSITION_TABLE", "G15", "G18", "G15_MASK", "d", "getBCHDigit", "digit", "errorCorrectLength", "a", "multiply", "QRMath", "gexp", "type", "qrCode", "sameCount", "count", "darkCount", "ratio", "abs", "glog", "n", "LOG_TABLE", "EXP_TABLE", "num", "shift", "undefined", "e", "RS_BLOCK_TABLE", "rsBlock", "getRsBlockTable", "list", "bufIndex", "bit", "QRCodeLimitLength", "_isSupportCanvas", "CanvasRenderingContext2D", "_getAndroid", "android", "sAgent", "navigator", "userAgent", "aMat", "toString", "match", "parseFloat", "svgDrawer", "Drawing", "el", "htOption", "_el", "_htOption", "draw", "oQRCode", "nCount", "nWidth", "width", "nHeight", "height", "clear", "makeSVG", "tag", "attrs", "document", "createElementNS", "k", "hasOwnProperty", "setAttribute", "svg", "String", "colorLight", "setAttributeNS", "append<PERSON><PERSON><PERSON>", "colorDark", "child", "hasChildNodes", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "useSVG", "documentElement", "tagName", "toLowerCase", "aHTML", "innerHTML", "join", "elTable", "childNodes", "nLeftMarginTable", "offsetWidth", "nTopMarginTable", "offsetHeight", "style", "margin", "_onMakeImage", "_elImage", "src", "_elCanvas", "toDataURL", "display", "_android", "factor", "devicePixelRatio", "drawImage", "image", "sx", "sy", "sw", "sh", "dx", "dy", "dw", "dh", "nodeName", "arguments", "_safeSetDataURI", "fSuccess", "fFail", "self", "_fFail", "_fSuccess", "_bSupportDataURI", "createElement", "fOnError", "call", "fOnSuccess", "<PERSON>ab<PERSON>", "onerror", "onload", "_bIsPainted", "_oContext", "getContext", "alt", "nRoundedWidth", "round", "nRoundedHeight", "bIsDark", "nLeft", "nTop", "strokeStyle", "lineWidth", "fillStyle", "fillRect", "strokeRect", "ceil", "makeImage", "isPainted", "clearRect", "nNumber", "_getTypeNumber", "sText", "nCorrectLevel", "nType", "_getUTF8Length", "len", "nLimit", "replacedText", "encodeURI", "replace", "vOption", "correctLevel", "text", "getElementById", "_oQRCode", "_oDrawing", "makeCode", "title", "CorrectLevel"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/print/hiprint/plugins/qrcode.js"], "sourcesContent": ["/**\r\n * @fileoverview\r\n * - Using the 'QRCode for Javascript library'\r\n * - Fixed dataset of 'QRCode for Javascript library' for support full-spec.\r\n * - this library has no dependencies.\r\n *\r\n * <AUTHOR>\r\n * @see <a href=\"http://www.d-project.com/\" target=\"_blank\">http://www.d-project.com/</a>\r\n * @see <a href=\"http://jeromeetienne.github.com/jquery-qrcode/\" target=\"_blank\">http://jeromeetienne.github.com/jquery-qrcode/</a>\r\n */\r\n\r\nwindow.QRCode;\r\n(function () {\r\n\r\n  //---------------------------------------------------------------------\r\n  // QRCode for JavaScript\r\n  //\r\n  // Copyright (c) 2009 Ka<PERSON>hiko <PERSON>\r\n  //\r\n  // URL: http://www.d-project.com/\r\n  //\r\n  // Licensed under the MIT license:\r\n  //   http://www.opensource.org/licenses/mit-license.php\r\n  //\r\n  // The word \"QR Code\" is registered trademark of\r\n  // DENSO WAVE INCORPORATED\r\n  //   http://www.denso-wave.com/qrcode/faqpatent-e.html\r\n  //\r\n  //---------------------------------------------------------------------\r\n  function QR8bitByte(data) {\r\n    this.mode = QRMode.MODE_8BIT_BYTE;\r\n    this.data = data;\r\n    this.parsedData = [];\r\n\r\n    // Added to support UTF-8 Characters\r\n    for (var i = 0, l = this.data.length; i < l; i++) {\r\n      var byteArray = [];\r\n      var code = this.data.charCodeAt(i);\r\n\r\n      if (code > 0x10000) {\r\n        byteArray[0] = 0xF0 | ((code & 0x1C0000) >>> 18);\r\n        byteArray[1] = 0x80 | ((code & 0x3F000) >>> 12);\r\n        byteArray[2] = 0x80 | ((code & 0xFC0) >>> 6);\r\n        byteArray[3] = 0x80 | (code & 0x3F);\r\n      } else if (code > 0x800) {\r\n        byteArray[0] = 0xE0 | ((code & 0xF000) >>> 12);\r\n        byteArray[1] = 0x80 | ((code & 0xFC0) >>> 6);\r\n        byteArray[2] = 0x80 | (code & 0x3F);\r\n      } else if (code > 0x80) {\r\n        byteArray[0] = 0xC0 | ((code & 0x7C0) >>> 6);\r\n        byteArray[1] = 0x80 | (code & 0x3F);\r\n      } else {\r\n        byteArray[0] = code;\r\n      }\r\n\r\n      this.parsedData.push(byteArray);\r\n    }\r\n\r\n    this.parsedData = Array.prototype.concat.apply([], this.parsedData);\r\n\r\n    if (this.parsedData.length != this.data.length) {\r\n      this.parsedData.unshift(191);\r\n      this.parsedData.unshift(187);\r\n      this.parsedData.unshift(239);\r\n    }\r\n  }\r\n\r\n  QR8bitByte.prototype = {\r\n    getLength: function (buffer) {\r\n      return this.parsedData.length;\r\n    },\r\n    write: function (buffer) {\r\n      for (var i = 0, l = this.parsedData.length; i < l; i++) {\r\n        buffer.put(this.parsedData[i], 8);\r\n      }\r\n    }\r\n  };\r\n\r\n  function QRCodeModel(typeNumber, errorCorrectLevel) {\r\n    this.typeNumber = typeNumber;\r\n    this.errorCorrectLevel = errorCorrectLevel;\r\n    this.modules = null;\r\n    this.moduleCount = 0;\r\n    this.dataCache = null;\r\n    this.dataList = [];\r\n  }\r\n\r\n  QRCodeModel.prototype = {\r\n    addData: function (data) {\r\n      var newData = new QR8bitByte(data);\r\n      this.dataList.push(newData);\r\n      this.dataCache = null;\r\n    }, isDark: function (row, col) {\r\n      if (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {\r\n        throw new Error(row + \",\" + col);\r\n      }\r\n      return this.modules[row][col];\r\n    }, getModuleCount: function () {\r\n      return this.moduleCount;\r\n    }, make: function () {\r\n      this.makeImpl(false, this.getBestMaskPattern());\r\n    }, makeImpl: function (test, maskPattern) {\r\n      this.moduleCount = this.typeNumber * 4 + 17;\r\n      this.modules = new Array(this.moduleCount);\r\n      for (var row = 0; row < this.moduleCount; row++) {\r\n        this.modules[row] = new Array(this.moduleCount);\r\n        for (var col = 0; col < this.moduleCount; col++) {\r\n          this.modules[row][col] = null;\r\n        }\r\n      }\r\n      this.setupPositionProbePattern(0, 0);\r\n      this.setupPositionProbePattern(this.moduleCount - 7, 0);\r\n      this.setupPositionProbePattern(0, this.moduleCount - 7);\r\n      this.setupPositionAdjustPattern();\r\n      this.setupTimingPattern();\r\n      this.setupTypeInfo(test, maskPattern);\r\n      if (this.typeNumber >= 7) {\r\n        this.setupTypeNumber(test);\r\n      }\r\n      if (this.dataCache == null) {\r\n        this.dataCache = QRCodeModel.createData(this.typeNumber, this.errorCorrectLevel, this.dataList);\r\n      }\r\n      this.mapData(this.dataCache, maskPattern);\r\n    }, setupPositionProbePattern: function (row, col) {\r\n      for (var r = -1; r <= 7; r++) {\r\n        if (row + r <= -1 || this.moduleCount <= row + r) continue;\r\n        for (var c = -1; c <= 7; c++) {\r\n          if (col + c <= -1 || this.moduleCount <= col + c) continue;\r\n          if ((0 <= r && r <= 6 && (c == 0 || c == 6)) || (0 <= c && c <= 6 && (r == 0 || r == 6)) || (2 <= r && r <= 4 && 2 <= c && c <= 4)) {\r\n            this.modules[row + r][col + c] = true;\r\n          } else {\r\n            this.modules[row + r][col + c] = false;\r\n          }\r\n        }\r\n      }\r\n    }, getBestMaskPattern: function () {\r\n      var minLostPoint = 0;\r\n      var pattern = 0;\r\n      for (var i = 0; i < 8; i++) {\r\n        this.makeImpl(true, i);\r\n        var lostPoint = QRUtil.getLostPoint(this);\r\n        if (i == 0 || minLostPoint > lostPoint) {\r\n          minLostPoint = lostPoint;\r\n          pattern = i;\r\n        }\r\n      }\r\n      return pattern;\r\n    }, createMovieClip: function (target_mc, instance_name, depth) {\r\n      var qr_mc = target_mc.createEmptyMovieClip(instance_name, depth);\r\n      var cs = 1;\r\n      this.make();\r\n      for (var row = 0; row < this.modules.length; row++) {\r\n        var y = row * cs;\r\n        for (var col = 0; col < this.modules[row].length; col++) {\r\n          var x = col * cs;\r\n          var dark = this.modules[row][col];\r\n          if (dark) {\r\n            qr_mc.beginFill(0, 100);\r\n            qr_mc.moveTo(x, y);\r\n            qr_mc.lineTo(x + cs, y);\r\n            qr_mc.lineTo(x + cs, y + cs);\r\n            qr_mc.lineTo(x, y + cs);\r\n            qr_mc.endFill();\r\n          }\r\n        }\r\n      }\r\n      return qr_mc;\r\n    }, setupTimingPattern: function () {\r\n      for (var r = 8; r < this.moduleCount - 8; r++) {\r\n        if (this.modules[r][6] != null) {\r\n          continue;\r\n        }\r\n        this.modules[r][6] = (r % 2 == 0);\r\n      }\r\n      for (var c = 8; c < this.moduleCount - 8; c++) {\r\n        if (this.modules[6][c] != null) {\r\n          continue;\r\n        }\r\n        this.modules[6][c] = (c % 2 == 0);\r\n      }\r\n    }, setupPositionAdjustPattern: function () {\r\n      var pos = QRUtil.getPatternPosition(this.typeNumber);\r\n      for (var i = 0; i < pos.length; i++) {\r\n        for (var j = 0; j < pos.length; j++) {\r\n          var row = pos[i];\r\n          var col = pos[j];\r\n          if (this.modules[row][col] != null) {\r\n            continue;\r\n          }\r\n          for (var r = -2; r <= 2; r++) {\r\n            for (var c = -2; c <= 2; c++) {\r\n              if (r == -2 || r == 2 || c == -2 || c == 2 || (r == 0 && c == 0)) {\r\n                this.modules[row + r][col + c] = true;\r\n              } else {\r\n                this.modules[row + r][col + c] = false;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }, setupTypeNumber: function (test) {\r\n      var bits = QRUtil.getBCHTypeNumber(this.typeNumber);\r\n      for (var i = 0; i < 18; i++) {\r\n        var mod = (!test && ((bits >> i) & 1) == 1);\r\n        this.modules[Math.floor(i / 3)][i % 3 + this.moduleCount - 8 - 3] = mod;\r\n      }\r\n      for (var i = 0; i < 18; i++) {\r\n        var mod = (!test && ((bits >> i) & 1) == 1);\r\n        this.modules[i % 3 + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\r\n      }\r\n    }, setupTypeInfo: function (test, maskPattern) {\r\n      var data = (this.errorCorrectLevel << 3) | maskPattern;\r\n      var bits = QRUtil.getBCHTypeInfo(data);\r\n      for (var i = 0; i < 15; i++) {\r\n        var mod = (!test && ((bits >> i) & 1) == 1);\r\n        if (i < 6) {\r\n          this.modules[i][8] = mod;\r\n        } else if (i < 8) {\r\n          this.modules[i + 1][8] = mod;\r\n        } else {\r\n          this.modules[this.moduleCount - 15 + i][8] = mod;\r\n        }\r\n      }\r\n      for (var i = 0; i < 15; i++) {\r\n        var mod = (!test && ((bits >> i) & 1) == 1);\r\n        if (i < 8) {\r\n          this.modules[8][this.moduleCount - i - 1] = mod;\r\n        } else if (i < 9) {\r\n          this.modules[8][15 - i - 1 + 1] = mod;\r\n        } else {\r\n          this.modules[8][15 - i - 1] = mod;\r\n        }\r\n      }\r\n      this.modules[this.moduleCount - 8][8] = (!test);\r\n    }, mapData: function (data, maskPattern) {\r\n      var inc = -1;\r\n      var row = this.moduleCount - 1;\r\n      var bitIndex = 7;\r\n      var byteIndex = 0;\r\n      for (var col = this.moduleCount - 1; col > 0; col -= 2) {\r\n        if (col == 6) col--;\r\n        while (true) {\r\n          for (var c = 0; c < 2; c++) {\r\n            if (this.modules[row][col - c] == null) {\r\n              var dark = false;\r\n              if (byteIndex < data.length) {\r\n                dark = (((data[byteIndex] >>> bitIndex) & 1) == 1);\r\n              }\r\n              var mask = QRUtil.getMask(maskPattern, row, col - c);\r\n              if (mask) {\r\n                dark = !dark;\r\n              }\r\n              this.modules[row][col - c] = dark;\r\n              bitIndex--;\r\n              if (bitIndex == -1) {\r\n                byteIndex++;\r\n                bitIndex = 7;\r\n              }\r\n            }\r\n          }\r\n          row += inc;\r\n          if (row < 0 || this.moduleCount <= row) {\r\n            row -= inc;\r\n            inc = -inc;\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  };\r\n  QRCodeModel.PAD0 = 0xEC;\r\n  QRCodeModel.PAD1 = 0x11;\r\n  QRCodeModel.createData = function (typeNumber, errorCorrectLevel, dataList) {\r\n    var rsBlocks = QRRSBlock.getRSBlocks(typeNumber, errorCorrectLevel);\r\n    var buffer = new QRBitBuffer();\r\n    for (var i = 0; i < dataList.length; i++) {\r\n      var data = dataList[i];\r\n      buffer.put(data.mode, 4);\r\n      buffer.put(data.getLength(), QRUtil.getLengthInBits(data.mode, typeNumber));\r\n      data.write(buffer);\r\n    }\r\n    var totalDataCount = 0;\r\n    for (var i = 0; i < rsBlocks.length; i++) {\r\n      totalDataCount += rsBlocks[i].dataCount;\r\n    }\r\n    if (buffer.getLengthInBits() > totalDataCount * 8) {\r\n      throw new Error(\"code length overflow. (\"\r\n        + buffer.getLengthInBits()\r\n        + \">\"\r\n        + totalDataCount * 8\r\n        + \")\");\r\n    }\r\n    if (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\r\n      buffer.put(0, 4);\r\n    }\r\n    while (buffer.getLengthInBits() % 8 != 0) {\r\n      buffer.putBit(false);\r\n    }\r\n    while (true) {\r\n      if (buffer.getLengthInBits() >= totalDataCount * 8) {\r\n        break;\r\n      }\r\n      buffer.put(QRCodeModel.PAD0, 8);\r\n      if (buffer.getLengthInBits() >= totalDataCount * 8) {\r\n        break;\r\n      }\r\n      buffer.put(QRCodeModel.PAD1, 8);\r\n    }\r\n    return QRCodeModel.createBytes(buffer, rsBlocks);\r\n  };\r\n  QRCodeModel.createBytes = function (buffer, rsBlocks) {\r\n    var offset = 0;\r\n    var maxDcCount = 0;\r\n    var maxEcCount = 0;\r\n    var dcdata = new Array(rsBlocks.length);\r\n    var ecdata = new Array(rsBlocks.length);\r\n    for (var r = 0; r < rsBlocks.length; r++) {\r\n      var dcCount = rsBlocks[r].dataCount;\r\n      var ecCount = rsBlocks[r].totalCount - dcCount;\r\n      maxDcCount = Math.max(maxDcCount, dcCount);\r\n      maxEcCount = Math.max(maxEcCount, ecCount);\r\n      dcdata[r] = new Array(dcCount);\r\n      for (var i = 0; i < dcdata[r].length; i++) {\r\n        dcdata[r][i] = 0xff & buffer.buffer[i + offset];\r\n      }\r\n      offset += dcCount;\r\n      var rsPoly = QRUtil.getErrorCorrectPolynomial(ecCount);\r\n      var rawPoly = new QRPolynomial(dcdata[r], rsPoly.getLength() - 1);\r\n      var modPoly = rawPoly.mod(rsPoly);\r\n      ecdata[r] = new Array(rsPoly.getLength() - 1);\r\n      for (var i = 0; i < ecdata[r].length; i++) {\r\n        var modIndex = i + modPoly.getLength() - ecdata[r].length;\r\n        ecdata[r][i] = (modIndex >= 0) ? modPoly.get(modIndex) : 0;\r\n      }\r\n    }\r\n    var totalCodeCount = 0;\r\n    for (var i = 0; i < rsBlocks.length; i++) {\r\n      totalCodeCount += rsBlocks[i].totalCount;\r\n    }\r\n    var data = new Array(totalCodeCount);\r\n    var index = 0;\r\n    for (var i = 0; i < maxDcCount; i++) {\r\n      for (var r = 0; r < rsBlocks.length; r++) {\r\n        if (i < dcdata[r].length) {\r\n          data[index++] = dcdata[r][i];\r\n        }\r\n      }\r\n    }\r\n    for (var i = 0; i < maxEcCount; i++) {\r\n      for (var r = 0; r < rsBlocks.length; r++) {\r\n        if (i < ecdata[r].length) {\r\n          data[index++] = ecdata[r][i];\r\n        }\r\n      }\r\n    }\r\n    return data;\r\n  };\r\n  var QRMode = {MODE_NUMBER: 1 << 0, MODE_ALPHA_NUM: 1 << 1, MODE_8BIT_BYTE: 1 << 2, MODE_KANJI: 1 << 3};\r\n  var QRErrorCorrectLevel = {L: 1, M: 0, Q: 3, H: 2};\r\n  var QRMaskPattern = {\r\n    PATTERN000: 0,\r\n    PATTERN001: 1,\r\n    PATTERN010: 2,\r\n    PATTERN011: 3,\r\n    PATTERN100: 4,\r\n    PATTERN101: 5,\r\n    PATTERN110: 6,\r\n    PATTERN111: 7\r\n  };\r\n  var QRUtil = {\r\n    PATTERN_POSITION_TABLE: [[], [6, 18], [6, 22], [6, 26], [6, 30], [6, 34], [6, 22, 38], [6, 24, 42], [6, 26, 46], [6, 28, 50], [6, 30, 54], [6, 32, 58], [6, 34, 62], [6, 26, 46, 66], [6, 26, 48, 70], [6, 26, 50, 74], [6, 30, 54, 78], [6, 30, 56, 82], [6, 30, 58, 86], [6, 34, 62, 90], [6, 28, 50, 72, 94], [6, 26, 50, 74, 98], [6, 30, 54, 78, 102], [6, 28, 54, 80, 106], [6, 32, 58, 84, 110], [6, 30, 58, 86, 114], [6, 34, 62, 90, 118], [6, 26, 50, 74, 98, 122], [6, 30, 54, 78, 102, 126], [6, 26, 52, 78, 104, 130], [6, 30, 56, 82, 108, 134], [6, 34, 60, 86, 112, 138], [6, 30, 58, 86, 114, 142], [6, 34, 62, 90, 118, 146], [6, 30, 54, 78, 102, 126, 150], [6, 24, 50, 76, 102, 128, 154], [6, 28, 54, 80, 106, 132, 158], [6, 32, 58, 84, 110, 136, 162], [6, 26, 54, 82, 110, 138, 166], [6, 30, 58, 86, 114, 142, 170]],\r\n    G15: (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0),\r\n    G18: (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0),\r\n    G15_MASK: (1 << 14) | (1 << 12) | (1 << 10) | (1 << 4) | (1 << 1),\r\n    getBCHTypeInfo: function (data) {\r\n      var d = data << 10;\r\n      while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) >= 0) {\r\n        d ^= (QRUtil.G15 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15)));\r\n      }\r\n      return ((data << 10) | d) ^ QRUtil.G15_MASK;\r\n    },\r\n    getBCHTypeNumber: function (data) {\r\n      var d = data << 12;\r\n      while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) >= 0) {\r\n        d ^= (QRUtil.G18 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18)));\r\n      }\r\n      return (data << 12) | d;\r\n    },\r\n    getBCHDigit: function (data) {\r\n      var digit = 0;\r\n      while (data != 0) {\r\n        digit++;\r\n        data >>>= 1;\r\n      }\r\n      return digit;\r\n    },\r\n    getPatternPosition: function (typeNumber) {\r\n      return QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1];\r\n    },\r\n    getMask: function (maskPattern, i, j) {\r\n      switch (maskPattern) {\r\n        case QRMaskPattern.PATTERN000:\r\n          return (i + j) % 2 == 0;\r\n        case QRMaskPattern.PATTERN001:\r\n          return i % 2 == 0;\r\n        case QRMaskPattern.PATTERN010:\r\n          return j % 3 == 0;\r\n        case QRMaskPattern.PATTERN011:\r\n          return (i + j) % 3 == 0;\r\n        case QRMaskPattern.PATTERN100:\r\n          return (Math.floor(i / 2) + Math.floor(j / 3)) % 2 == 0;\r\n        case QRMaskPattern.PATTERN101:\r\n          return (i * j) % 2 + (i * j) % 3 == 0;\r\n        case QRMaskPattern.PATTERN110:\r\n          return ((i * j) % 2 + (i * j) % 3) % 2 == 0;\r\n        case QRMaskPattern.PATTERN111:\r\n          return ((i * j) % 3 + (i + j) % 2) % 2 == 0;\r\n        default:\r\n          throw new Error(\"bad maskPattern:\" + maskPattern);\r\n      }\r\n    },\r\n    getErrorCorrectPolynomial: function (errorCorrectLength) {\r\n      var a = new QRPolynomial([1], 0);\r\n      for (var i = 0; i < errorCorrectLength; i++) {\r\n        a = a.multiply(new QRPolynomial([1, QRMath.gexp(i)], 0));\r\n      }\r\n      return a;\r\n    },\r\n    getLengthInBits: function (mode, type) {\r\n      if (1 <= type && type < 10) {\r\n        switch (mode) {\r\n          case QRMode.MODE_NUMBER:\r\n            return 10;\r\n          case QRMode.MODE_ALPHA_NUM:\r\n            return 9;\r\n          case QRMode.MODE_8BIT_BYTE:\r\n            return 8;\r\n          case QRMode.MODE_KANJI:\r\n            return 8;\r\n          default:\r\n            throw new Error(\"mode:\" + mode);\r\n        }\r\n      } else if (type < 27) {\r\n        switch (mode) {\r\n          case QRMode.MODE_NUMBER:\r\n            return 12;\r\n          case QRMode.MODE_ALPHA_NUM:\r\n            return 11;\r\n          case QRMode.MODE_8BIT_BYTE:\r\n            return 16;\r\n          case QRMode.MODE_KANJI:\r\n            return 10;\r\n          default:\r\n            throw new Error(\"mode:\" + mode);\r\n        }\r\n      } else if (type < 41) {\r\n        switch (mode) {\r\n          case QRMode.MODE_NUMBER:\r\n            return 14;\r\n          case QRMode.MODE_ALPHA_NUM:\r\n            return 13;\r\n          case QRMode.MODE_8BIT_BYTE:\r\n            return 16;\r\n          case QRMode.MODE_KANJI:\r\n            return 12;\r\n          default:\r\n            throw new Error(\"mode:\" + mode);\r\n        }\r\n      } else {\r\n        throw new Error(\"type:\" + type);\r\n      }\r\n    },\r\n    getLostPoint: function (qrCode) {\r\n      var moduleCount = qrCode.getModuleCount();\r\n      var lostPoint = 0;\r\n      for (var row = 0; row < moduleCount; row++) {\r\n        for (var col = 0; col < moduleCount; col++) {\r\n          var sameCount = 0;\r\n          var dark = qrCode.isDark(row, col);\r\n          for (var r = -1; r <= 1; r++) {\r\n            if (row + r < 0 || moduleCount <= row + r) {\r\n              continue;\r\n            }\r\n            for (var c = -1; c <= 1; c++) {\r\n              if (col + c < 0 || moduleCount <= col + c) {\r\n                continue;\r\n              }\r\n              if (r == 0 && c == 0) {\r\n                continue;\r\n              }\r\n              if (dark == qrCode.isDark(row + r, col + c)) {\r\n                sameCount++;\r\n              }\r\n            }\r\n          }\r\n          if (sameCount > 5) {\r\n            lostPoint += (3 + sameCount - 5);\r\n          }\r\n        }\r\n      }\r\n      for (var row = 0; row < moduleCount - 1; row++) {\r\n        for (var col = 0; col < moduleCount - 1; col++) {\r\n          var count = 0;\r\n          if (qrCode.isDark(row, col)) count++;\r\n          if (qrCode.isDark(row + 1, col)) count++;\r\n          if (qrCode.isDark(row, col + 1)) count++;\r\n          if (qrCode.isDark(row + 1, col + 1)) count++;\r\n          if (count == 0 || count == 4) {\r\n            lostPoint += 3;\r\n          }\r\n        }\r\n      }\r\n      for (var row = 0; row < moduleCount; row++) {\r\n        for (var col = 0; col < moduleCount - 6; col++) {\r\n          if (qrCode.isDark(row, col) && !qrCode.isDark(row, col + 1) && qrCode.isDark(row, col + 2) && qrCode.isDark(row, col + 3) && qrCode.isDark(row, col + 4) && !qrCode.isDark(row, col + 5) && qrCode.isDark(row, col + 6)) {\r\n            lostPoint += 40;\r\n          }\r\n        }\r\n      }\r\n      for (var col = 0; col < moduleCount; col++) {\r\n        for (var row = 0; row < moduleCount - 6; row++) {\r\n          if (qrCode.isDark(row, col) && !qrCode.isDark(row + 1, col) && qrCode.isDark(row + 2, col) && qrCode.isDark(row + 3, col) && qrCode.isDark(row + 4, col) && !qrCode.isDark(row + 5, col) && qrCode.isDark(row + 6, col)) {\r\n            lostPoint += 40;\r\n          }\r\n        }\r\n      }\r\n      var darkCount = 0;\r\n      for (var col = 0; col < moduleCount; col++) {\r\n        for (var row = 0; row < moduleCount; row++) {\r\n          if (qrCode.isDark(row, col)) {\r\n            darkCount++;\r\n          }\r\n        }\r\n      }\r\n      var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\r\n      lostPoint += ratio * 10;\r\n      return lostPoint;\r\n    }\r\n  };\r\n  var QRMath = {\r\n    glog: function (n) {\r\n      if (n < 1) {\r\n        throw new Error(\"glog(\" + n + \")\");\r\n      }\r\n      return QRMath.LOG_TABLE[n];\r\n    }, gexp: function (n) {\r\n      while (n < 0) {\r\n        n += 255;\r\n      }\r\n      while (n >= 256) {\r\n        n -= 255;\r\n      }\r\n      return QRMath.EXP_TABLE[n];\r\n    }, EXP_TABLE: new Array(256), LOG_TABLE: new Array(256)\r\n  };\r\n  for (var i = 0; i < 8; i++) {\r\n    QRMath.EXP_TABLE[i] = 1 << i;\r\n  }\r\n  for (var i = 8; i < 256; i++) {\r\n    QRMath.EXP_TABLE[i] = QRMath.EXP_TABLE[i - 4] ^ QRMath.EXP_TABLE[i - 5] ^ QRMath.EXP_TABLE[i - 6] ^ QRMath.EXP_TABLE[i - 8];\r\n  }\r\n  for (var i = 0; i < 255; i++) {\r\n    QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]] = i;\r\n  }\r\n\r\n  function QRPolynomial(num, shift) {\r\n    if (num.length == undefined) {\r\n      throw new Error(num.length + \"/\" + shift);\r\n    }\r\n    var offset = 0;\r\n    while (offset < num.length && num[offset] == 0) {\r\n      offset++;\r\n    }\r\n    this.num = new Array(num.length - offset + shift);\r\n    for (var i = 0; i < num.length - offset; i++) {\r\n      this.num[i] = num[i + offset];\r\n    }\r\n  }\r\n\r\n  QRPolynomial.prototype = {\r\n    get: function (index) {\r\n      return this.num[index];\r\n    }, getLength: function () {\r\n      return this.num.length;\r\n    }, multiply: function (e) {\r\n      var num = new Array(this.getLength() + e.getLength() - 1);\r\n      for (var i = 0; i < this.getLength(); i++) {\r\n        for (var j = 0; j < e.getLength(); j++) {\r\n          num[i + j] ^= QRMath.gexp(QRMath.glog(this.get(i)) + QRMath.glog(e.get(j)));\r\n        }\r\n      }\r\n      return new QRPolynomial(num, 0);\r\n    }, mod: function (e) {\r\n      if (this.getLength() - e.getLength() < 0) {\r\n        return this;\r\n      }\r\n      var ratio = QRMath.glog(this.get(0)) - QRMath.glog(e.get(0));\r\n      var num = new Array(this.getLength());\r\n      for (var i = 0; i < this.getLength(); i++) {\r\n        num[i] = this.get(i);\r\n      }\r\n      for (var i = 0; i < e.getLength(); i++) {\r\n        num[i] ^= QRMath.gexp(QRMath.glog(e.get(i)) + ratio);\r\n      }\r\n      return new QRPolynomial(num, 0).mod(e);\r\n    }\r\n  };\r\n\r\n  function QRRSBlock(totalCount, dataCount) {\r\n    this.totalCount = totalCount;\r\n    this.dataCount = dataCount;\r\n  }\r\n\r\n  QRRSBlock.RS_BLOCK_TABLE = [[1, 26, 19], [1, 26, 16], [1, 26, 13], [1, 26, 9], [1, 44, 34], [1, 44, 28], [1, 44, 22], [1, 44, 16], [1, 70, 55], [1, 70, 44], [2, 35, 17], [2, 35, 13], [1, 100, 80], [2, 50, 32], [2, 50, 24], [4, 25, 9], [1, 134, 108], [2, 67, 43], [2, 33, 15, 2, 34, 16], [2, 33, 11, 2, 34, 12], [2, 86, 68], [4, 43, 27], [4, 43, 19], [4, 43, 15], [2, 98, 78], [4, 49, 31], [2, 32, 14, 4, 33, 15], [4, 39, 13, 1, 40, 14], [2, 121, 97], [2, 60, 38, 2, 61, 39], [4, 40, 18, 2, 41, 19], [4, 40, 14, 2, 41, 15], [2, 146, 116], [3, 58, 36, 2, 59, 37], [4, 36, 16, 4, 37, 17], [4, 36, 12, 4, 37, 13], [2, 86, 68, 2, 87, 69], [4, 69, 43, 1, 70, 44], [6, 43, 19, 2, 44, 20], [6, 43, 15, 2, 44, 16], [4, 101, 81], [1, 80, 50, 4, 81, 51], [4, 50, 22, 4, 51, 23], [3, 36, 12, 8, 37, 13], [2, 116, 92, 2, 117, 93], [6, 58, 36, 2, 59, 37], [4, 46, 20, 6, 47, 21], [7, 42, 14, 4, 43, 15], [4, 133, 107], [8, 59, 37, 1, 60, 38], [8, 44, 20, 4, 45, 21], [12, 33, 11, 4, 34, 12], [3, 145, 115, 1, 146, 116], [4, 64, 40, 5, 65, 41], [11, 36, 16, 5, 37, 17], [11, 36, 12, 5, 37, 13], [5, 109, 87, 1, 110, 88], [5, 65, 41, 5, 66, 42], [5, 54, 24, 7, 55, 25], [11, 36, 12], [5, 122, 98, 1, 123, 99], [7, 73, 45, 3, 74, 46], [15, 43, 19, 2, 44, 20], [3, 45, 15, 13, 46, 16], [1, 135, 107, 5, 136, 108], [10, 74, 46, 1, 75, 47], [1, 50, 22, 15, 51, 23], [2, 42, 14, 17, 43, 15], [5, 150, 120, 1, 151, 121], [9, 69, 43, 4, 70, 44], [17, 50, 22, 1, 51, 23], [2, 42, 14, 19, 43, 15], [3, 141, 113, 4, 142, 114], [3, 70, 44, 11, 71, 45], [17, 47, 21, 4, 48, 22], [9, 39, 13, 16, 40, 14], [3, 135, 107, 5, 136, 108], [3, 67, 41, 13, 68, 42], [15, 54, 24, 5, 55, 25], [15, 43, 15, 10, 44, 16], [4, 144, 116, 4, 145, 117], [17, 68, 42], [17, 50, 22, 6, 51, 23], [19, 46, 16, 6, 47, 17], [2, 139, 111, 7, 140, 112], [17, 74, 46], [7, 54, 24, 16, 55, 25], [34, 37, 13], [4, 151, 121, 5, 152, 122], [4, 75, 47, 14, 76, 48], [11, 54, 24, 14, 55, 25], [16, 45, 15, 14, 46, 16], [6, 147, 117, 4, 148, 118], [6, 73, 45, 14, 74, 46], [11, 54, 24, 16, 55, 25], [30, 46, 16, 2, 47, 17], [8, 132, 106, 4, 133, 107], [8, 75, 47, 13, 76, 48], [7, 54, 24, 22, 55, 25], [22, 45, 15, 13, 46, 16], [10, 142, 114, 2, 143, 115], [19, 74, 46, 4, 75, 47], [28, 50, 22, 6, 51, 23], [33, 46, 16, 4, 47, 17], [8, 152, 122, 4, 153, 123], [22, 73, 45, 3, 74, 46], [8, 53, 23, 26, 54, 24], [12, 45, 15, 28, 46, 16], [3, 147, 117, 10, 148, 118], [3, 73, 45, 23, 74, 46], [4, 54, 24, 31, 55, 25], [11, 45, 15, 31, 46, 16], [7, 146, 116, 7, 147, 117], [21, 73, 45, 7, 74, 46], [1, 53, 23, 37, 54, 24], [19, 45, 15, 26, 46, 16], [5, 145, 115, 10, 146, 116], [19, 75, 47, 10, 76, 48], [15, 54, 24, 25, 55, 25], [23, 45, 15, 25, 46, 16], [13, 145, 115, 3, 146, 116], [2, 74, 46, 29, 75, 47], [42, 54, 24, 1, 55, 25], [23, 45, 15, 28, 46, 16], [17, 145, 115], [10, 74, 46, 23, 75, 47], [10, 54, 24, 35, 55, 25], [19, 45, 15, 35, 46, 16], [17, 145, 115, 1, 146, 116], [14, 74, 46, 21, 75, 47], [29, 54, 24, 19, 55, 25], [11, 45, 15, 46, 46, 16], [13, 145, 115, 6, 146, 116], [14, 74, 46, 23, 75, 47], [44, 54, 24, 7, 55, 25], [59, 46, 16, 1, 47, 17], [12, 151, 121, 7, 152, 122], [12, 75, 47, 26, 76, 48], [39, 54, 24, 14, 55, 25], [22, 45, 15, 41, 46, 16], [6, 151, 121, 14, 152, 122], [6, 75, 47, 34, 76, 48], [46, 54, 24, 10, 55, 25], [2, 45, 15, 64, 46, 16], [17, 152, 122, 4, 153, 123], [29, 74, 46, 14, 75, 47], [49, 54, 24, 10, 55, 25], [24, 45, 15, 46, 46, 16], [4, 152, 122, 18, 153, 123], [13, 74, 46, 32, 75, 47], [48, 54, 24, 14, 55, 25], [42, 45, 15, 32, 46, 16], [20, 147, 117, 4, 148, 118], [40, 75, 47, 7, 76, 48], [43, 54, 24, 22, 55, 25], [10, 45, 15, 67, 46, 16], [19, 148, 118, 6, 149, 119], [18, 75, 47, 31, 76, 48], [34, 54, 24, 34, 55, 25], [20, 45, 15, 61, 46, 16]];\r\n  QRRSBlock.getRSBlocks = function (typeNumber, errorCorrectLevel) {\r\n    var rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel);\r\n    if (rsBlock == undefined) {\r\n      throw new Error(\"bad rs block @ typeNumber:\" + typeNumber + \"/errorCorrectLevel:\" + errorCorrectLevel);\r\n    }\r\n    var length = rsBlock.length / 3;\r\n    var list = [];\r\n    for (var i = 0; i < length; i++) {\r\n      var count = rsBlock[i * 3 + 0];\r\n      var totalCount = rsBlock[i * 3 + 1];\r\n      var dataCount = rsBlock[i * 3 + 2];\r\n      for (var j = 0; j < count; j++) {\r\n        list.push(new QRRSBlock(totalCount, dataCount));\r\n      }\r\n    }\r\n    return list;\r\n  };\r\n  QRRSBlock.getRsBlockTable = function (typeNumber, errorCorrectLevel) {\r\n    switch (errorCorrectLevel) {\r\n      case QRErrorCorrectLevel.L:\r\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\r\n      case QRErrorCorrectLevel.M:\r\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\r\n      case QRErrorCorrectLevel.Q:\r\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\r\n      case QRErrorCorrectLevel.H:\r\n        return QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\r\n      default:\r\n        return undefined;\r\n    }\r\n  };\r\n\r\n  function QRBitBuffer() {\r\n    this.buffer = [];\r\n    this.length = 0;\r\n  }\r\n\r\n  QRBitBuffer.prototype = {\r\n    get: function (index) {\r\n      var bufIndex = Math.floor(index / 8);\r\n      return ((this.buffer[bufIndex] >>> (7 - index % 8)) & 1) == 1;\r\n    }, put: function (num, length) {\r\n      for (var i = 0; i < length; i++) {\r\n        this.putBit(((num >>> (length - i - 1)) & 1) == 1);\r\n      }\r\n    }, getLengthInBits: function () {\r\n      return this.length;\r\n    }, putBit: function (bit) {\r\n      var bufIndex = Math.floor(this.length / 8);\r\n      if (this.buffer.length <= bufIndex) {\r\n        this.buffer.push(0);\r\n      }\r\n      if (bit) {\r\n        this.buffer[bufIndex] |= (0x80 >>> (this.length % 8));\r\n      }\r\n      this.length++;\r\n    }\r\n  };\r\n  var QRCodeLimitLength = [[17, 14, 11, 7], [32, 26, 20, 14], [53, 42, 32, 24], [78, 62, 46, 34], [106, 84, 60, 44], [134, 106, 74, 58], [154, 122, 86, 64], [192, 152, 108, 84], [230, 180, 130, 98], [271, 213, 151, 119], [321, 251, 177, 137], [367, 287, 203, 155], [425, 331, 241, 177], [458, 362, 258, 194], [520, 412, 292, 220], [586, 450, 322, 250], [644, 504, 364, 280], [718, 560, 394, 310], [792, 624, 442, 338], [858, 666, 482, 382], [929, 711, 509, 403], [1003, 779, 565, 439], [1091, 857, 611, 461], [1171, 911, 661, 511], [1273, 997, 715, 535], [1367, 1059, 751, 593], [1465, 1125, 805, 625], [1528, 1190, 868, 658], [1628, 1264, 908, 698], [1732, 1370, 982, 742], [1840, 1452, 1030, 790], [1952, 1538, 1112, 842], [2068, 1628, 1168, 898], [2188, 1722, 1228, 958], [2303, 1809, 1283, 983], [2431, 1911, 1351, 1051], [2563, 1989, 1423, 1093], [2699, 2099, 1499, 1139], [2809, 2213, 1579, 1219], [2953, 2331, 1663, 1273]];\r\n\r\n  function _isSupportCanvas() {\r\n    return typeof CanvasRenderingContext2D != \"undefined\";\r\n  }\r\n\r\n  // android 2.x doesn't support Data-URI spec\r\n  function _getAndroid() {\r\n    var android = false;\r\n    var sAgent = navigator.userAgent;\r\n\r\n    if (/android/i.test(sAgent)) { // android\r\n      android = true;\r\n      var aMat = sAgent.toString().match(/android ([0-9]\\.[0-9])/i);\r\n\r\n      if (aMat && aMat[1]) {\r\n        android = parseFloat(aMat[1]);\r\n      }\r\n    }\r\n\r\n    return android;\r\n  }\r\n\r\n  var svgDrawer = (function () {\r\n\r\n    var Drawing = function (el, htOption) {\r\n      this._el = el;\r\n      this._htOption = htOption;\r\n    };\r\n\r\n    Drawing.prototype.draw = function (oQRCode) {\r\n      var _htOption = this._htOption;\r\n      var _el = this._el;\r\n      var nCount = oQRCode.getModuleCount();\r\n      var nWidth = Math.floor(_htOption.width / nCount);\r\n      var nHeight = Math.floor(_htOption.height / nCount);\r\n\r\n      this.clear();\r\n\r\n      function makeSVG(tag, attrs) {\r\n        var el = document.createElementNS('http://www.w3.org/2000/svg', tag);\r\n        for (var k in attrs)\r\n          if (attrs.hasOwnProperty(k)) el.setAttribute(k, attrs[k]);\r\n        return el;\r\n      }\r\n\r\n      var svg = makeSVG(\"svg\", {\r\n        'viewBox': '0 0 ' + String(nCount) + \" \" + String(nCount),\r\n        'width': '100%',\r\n        'height': '100%',\r\n        'fill': _htOption.colorLight\r\n      });\r\n      svg.setAttributeNS(\"http://www.w3.org/2000/xmlns/\", \"xmlns:xlink\", \"http://www.w3.org/1999/xlink\");\r\n      _el.appendChild(svg);\r\n\r\n      svg.appendChild(makeSVG(\"rect\", {\"fill\": _htOption.colorLight, \"width\": \"100%\", \"height\": \"100%\"}));\r\n      svg.appendChild(makeSVG(\"rect\", {\"fill\": _htOption.colorDark, \"width\": \"1\", \"height\": \"1\", \"id\": \"template\"}));\r\n\r\n      for (var row = 0; row < nCount; row++) {\r\n        for (var col = 0; col < nCount; col++) {\r\n          if (oQRCode.isDark(row, col)) {\r\n            var child = makeSVG(\"use\", {\"x\": String(col), \"y\": String(row)});\r\n            child.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\", \"#template\")\r\n            svg.appendChild(child);\r\n          }\r\n        }\r\n      }\r\n    };\r\n    Drawing.prototype.clear = function () {\r\n      while (this._el.hasChildNodes())\r\n        this._el.removeChild(this._el.lastChild);\r\n    };\r\n    return Drawing;\r\n  })();\r\n\r\n  var useSVG = document.documentElement.tagName.toLowerCase() === \"svg\";\r\n\r\n  // Drawing in DOM by using Table tag\r\n  var Drawing = useSVG ? svgDrawer : !_isSupportCanvas() ? (function () {\r\n    var Drawing = function (el, htOption) {\r\n      this._el = el;\r\n      this._htOption = htOption;\r\n    };\r\n\r\n    /**\r\n     * Draw the QRCode\r\n     *\r\n     * @param {QRCode} oQRCode\r\n     */\r\n    Drawing.prototype.draw = function (oQRCode) {\r\n      var _htOption = this._htOption;\r\n      var _el = this._el;\r\n      var nCount = oQRCode.getModuleCount();\r\n      var nWidth = Math.floor(_htOption.width / nCount);\r\n      var nHeight = Math.floor(_htOption.height / nCount);\r\n      var aHTML = ['<table style=\"border:0;border-collapse:collapse;\">'];\r\n\r\n      for (var row = 0; row < nCount; row++) {\r\n        aHTML.push('<tr>');\r\n\r\n        for (var col = 0; col < nCount; col++) {\r\n          aHTML.push('<td style=\"border:0;border-collapse:collapse;padding:0;margin:0;width:' + nWidth + 'px;height:' + nHeight + 'px;background-color:' + (oQRCode.isDark(row, col) ? _htOption.colorDark : _htOption.colorLight) + ';\"></td>');\r\n        }\r\n\r\n        aHTML.push('</tr>');\r\n      }\r\n\r\n      aHTML.push('</table>');\r\n      _el.innerHTML = aHTML.join('');\r\n\r\n      // Fix the margin values as real size.\r\n      var elTable = _el.childNodes[0];\r\n      var nLeftMarginTable = (_htOption.width - elTable.offsetWidth) / 2;\r\n      var nTopMarginTable = (_htOption.height - elTable.offsetHeight) / 2;\r\n\r\n      if (nLeftMarginTable > 0 && nTopMarginTable > 0) {\r\n        elTable.style.margin = nTopMarginTable + \"px \" + nLeftMarginTable + \"px\";\r\n      }\r\n    };\r\n\r\n    /**\r\n     * Clear the QRCode\r\n     */\r\n    Drawing.prototype.clear = function () {\r\n      this._el.innerHTML = '';\r\n    };\r\n\r\n    return Drawing;\r\n  })() : (function () { // Drawing in Canvas\r\n    function _onMakeImage() {\r\n      this._elImage.src = this._elCanvas.toDataURL(\"image/png\");\r\n      this._elImage.style.display = \"block\";\r\n      this._elCanvas.style.display = \"none\";\r\n    }\r\n\r\n    // Android 2.1 bug workaround\r\n    // http://code.google.com/p/android/issues/detail?id=5141\r\n    if (this._android && this._android <= 2.1) {\r\n      var factor = 1 / window.devicePixelRatio;\r\n      var drawImage = CanvasRenderingContext2D.prototype.drawImage;\r\n      CanvasRenderingContext2D.prototype.drawImage = function (image, sx, sy, sw, sh, dx, dy, dw, dh) {\r\n        if ((\"nodeName\" in image) && /img/i.test(image.nodeName)) {\r\n          for (var i = arguments.length - 1; i >= 1; i--) {\r\n            arguments[i] = arguments[i] * factor;\r\n          }\r\n        } else if (typeof dw == \"undefined\") {\r\n          arguments[1] *= factor;\r\n          arguments[2] *= factor;\r\n          arguments[3] *= factor;\r\n          arguments[4] *= factor;\r\n        }\r\n\r\n        drawImage.apply(this, arguments);\r\n      };\r\n    }\r\n\r\n    /**\r\n     * Check whether the user's browser supports Data URI or not\r\n     *\r\n     * @private\r\n     * @param {Function} fSuccess Occurs if it supports Data URI\r\n     * @param {Function} fFail Occurs if it doesn't support Data URI\r\n     */\r\n    function _safeSetDataURI(fSuccess, fFail) {\r\n      var self = this;\r\n      self._fFail = fFail;\r\n      self._fSuccess = fSuccess;\r\n\r\n      // Check it just once\r\n      if (self._bSupportDataURI === null) {\r\n        var el = document.createElement(\"img\");\r\n        var fOnError = function () {\r\n          self._bSupportDataURI = false;\r\n\r\n          if (self._fFail) {\r\n            self._fFail.call(self);\r\n          }\r\n        };\r\n        var fOnSuccess = function () {\r\n          self._bSupportDataURI = true;\r\n\r\n          if (self._fSuccess) {\r\n            self._fSuccess.call(self);\r\n          }\r\n        };\r\n\r\n        el.onabort = fOnError;\r\n        el.onerror = fOnError;\r\n        el.onload = fOnSuccess;\r\n        el.src = \"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==\"; // the Image contains 1px data.\r\n        return;\r\n      } else if (self._bSupportDataURI === true && self._fSuccess) {\r\n        self._fSuccess.call(self);\r\n      } else if (self._bSupportDataURI === false && self._fFail) {\r\n        self._fFail.call(self);\r\n      }\r\n    };\r\n\r\n    /**\r\n     * Drawing QRCode by using canvas\r\n     *\r\n     * @constructor\r\n     * @param {HTMLElement} el\r\n     * @param {Object} htOption QRCode Options\r\n     */\r\n    var Drawing = function (el, htOption) {\r\n      this._bIsPainted = false;\r\n      this._android = _getAndroid();\r\n\r\n      this._htOption = htOption;\r\n      this._elCanvas = document.createElement(\"canvas\");\r\n      this._elCanvas.width = htOption.width;\r\n      this._elCanvas.height = htOption.height;\r\n      el.appendChild(this._elCanvas);\r\n      this._el = el;\r\n      this._oContext = this._elCanvas.getContext(\"2d\");\r\n      this._bIsPainted = false;\r\n      this._elImage = document.createElement(\"img\");\r\n      this._elImage.alt = \"Scan me!\";\r\n      this._elImage.style.display = \"none\";\r\n      this._el.appendChild(this._elImage);\r\n      this._bSupportDataURI = null;\r\n    };\r\n\r\n    /**\r\n     * Draw the QRCode\r\n     *\r\n     * @param {QRCode} oQRCode\r\n     */\r\n    Drawing.prototype.draw = function (oQRCode) {\r\n      var _elImage = this._elImage;\r\n      var _oContext = this._oContext;\r\n      var _htOption = this._htOption;\r\n\r\n      var nCount = oQRCode.getModuleCount();\r\n      var nWidth = _htOption.width / nCount;\r\n      var nHeight = _htOption.height / nCount;\r\n      var nRoundedWidth = Math.round(nWidth);\r\n      var nRoundedHeight = Math.round(nHeight);\r\n\r\n      _elImage.style.display = \"none\";\r\n      this.clear();\r\n\r\n      for (var row = 0; row < nCount; row++) {\r\n        for (var col = 0; col < nCount; col++) {\r\n          var bIsDark = oQRCode.isDark(row, col);\r\n          var nLeft = col * nWidth;\r\n          var nTop = row * nHeight;\r\n          _oContext.strokeStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\r\n          _oContext.lineWidth = 1;\r\n          _oContext.fillStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\r\n          _oContext.fillRect(nLeft, nTop, nWidth, nHeight);\r\n\r\n          // 안티 앨리어싱 방지 처리\r\n          _oContext.strokeRect(\r\n            Math.floor(nLeft) + 0.5,\r\n            Math.floor(nTop) + 0.5,\r\n            nRoundedWidth,\r\n            nRoundedHeight\r\n          );\r\n\r\n          _oContext.strokeRect(\r\n            Math.ceil(nLeft) - 0.5,\r\n            Math.ceil(nTop) - 0.5,\r\n            nRoundedWidth,\r\n            nRoundedHeight\r\n          );\r\n        }\r\n      }\r\n\r\n      this._bIsPainted = true;\r\n    };\r\n\r\n    /**\r\n     * Make the image from Canvas if the browser supports Data URI.\r\n     */\r\n    Drawing.prototype.makeImage = function () {\r\n      if (this._bIsPainted) {\r\n        _safeSetDataURI.call(this, _onMakeImage);\r\n      }\r\n    };\r\n\r\n    /**\r\n     * Return whether the QRCode is painted or not\r\n     *\r\n     * @return {Boolean}\r\n     */\r\n    Drawing.prototype.isPainted = function () {\r\n      return this._bIsPainted;\r\n    };\r\n\r\n    /**\r\n     * Clear the QRCode\r\n     */\r\n    Drawing.prototype.clear = function () {\r\n      this._oContext.clearRect(0, 0, this._elCanvas.width, this._elCanvas.height);\r\n      this._bIsPainted = false;\r\n    };\r\n\r\n    /**\r\n     * @private\r\n     * @param {Number} nNumber\r\n     */\r\n    Drawing.prototype.round = function (nNumber) {\r\n      if (!nNumber) {\r\n        return nNumber;\r\n      }\r\n\r\n      return Math.floor(nNumber * 1000) / 1000;\r\n    };\r\n\r\n    return Drawing;\r\n  })();\r\n\r\n  /**\r\n   * Get the type by string length\r\n   *\r\n   * @private\r\n   * @param {String} sText\r\n   * @param {Number} nCorrectLevel\r\n   * @return {Number} type\r\n   */\r\n  function _getTypeNumber(sText, nCorrectLevel) {\r\n    var nType = 1;\r\n    var length = _getUTF8Length(sText);\r\n\r\n    for (var i = 0, len = QRCodeLimitLength.length; i <= len; i++) {\r\n      var nLimit = 0;\r\n\r\n      switch (nCorrectLevel) {\r\n        case QRErrorCorrectLevel.L :\r\n          nLimit = QRCodeLimitLength[i][0];\r\n          break;\r\n        case QRErrorCorrectLevel.M :\r\n          nLimit = QRCodeLimitLength[i][1];\r\n          break;\r\n        case QRErrorCorrectLevel.Q :\r\n          nLimit = QRCodeLimitLength[i][2];\r\n          break;\r\n        case QRErrorCorrectLevel.H :\r\n          nLimit = QRCodeLimitLength[i][3];\r\n          break;\r\n      }\r\n\r\n      if (length <= nLimit) {\r\n        break;\r\n      } else {\r\n        nType++;\r\n      }\r\n    }\r\n\r\n    if (nType > QRCodeLimitLength.length) {\r\n      throw new Error(\"Too long data\");\r\n    }\r\n\r\n    return nType;\r\n  }\r\n\r\n  function _getUTF8Length(sText) {\r\n    var replacedText = encodeURI(sText).toString().replace(/\\%[0-9a-fA-F]{2}/g, 'a');\r\n    return replacedText.length + (replacedText.length != sText ? 3 : 0);\r\n  }\r\n\r\n  /**\r\n   * @class QRCode\r\n   * @constructor\r\n   * @example\r\n   * new QRCode(document.getElementById(\"test\"), \"http://jindo.dev.naver.com/collie\");\r\n   *\r\n   * @example\r\n   * var oQRCode = new QRCode(\"test\", {\r\n   *    text : \"http://naver.com\",\r\n   *    width : 128,\r\n   *    height : 128\r\n   * });\r\n   *\r\n   * oQRCode.clear(); // Clear the QRCode.\r\n   * oQRCode.makeCode(\"http://map.naver.com\"); // Re-create the QRCode.\r\n   *\r\n   * @param {HTMLElement|String} el target element or 'id' attribute of element.\r\n   * @param {Object|String} vOption\r\n   * @param {String} vOption.text QRCode link data\r\n   * @param {Number} [vOption.width=256]\r\n   * @param {Number} [vOption.height=256]\r\n   * @param {String} [vOption.colorDark=\"#000000\"]\r\n   * @param {String} [vOption.colorLight=\"#ffffff\"]\r\n   * @param {QRCode.CorrectLevel} [vOption.correctLevel=QRCode.CorrectLevel.H] [L|M|Q|H]\r\n   */\r\n  QRCode = function (el, vOption) {\r\n    this._htOption = {\r\n      width: 256,\r\n      height: 256,\r\n      typeNumber: 4,\r\n      colorDark: \"#000000\",\r\n      colorLight: \"#ffffff\",\r\n      correctLevel: QRErrorCorrectLevel.H\r\n    };\r\n\r\n    if (typeof vOption === 'string') {\r\n      vOption = {\r\n        text: vOption\r\n      };\r\n    }\r\n\r\n    // Overwrites options\r\n    if (vOption) {\r\n      for (var i in vOption) {\r\n        this._htOption[i] = vOption[i];\r\n      }\r\n    }\r\n\r\n    if (typeof el == \"string\") {\r\n      el = document.getElementById(el);\r\n    }\r\n\r\n    if (this._htOption.useSVG) {\r\n      Drawing = svgDrawer;\r\n    }\r\n\r\n    this._android = _getAndroid();\r\n    this._el = el;\r\n    this._oQRCode = null;\r\n    this._oDrawing = new Drawing(this._el, this._htOption);\r\n\r\n    if (this._htOption.text) {\r\n      this.makeCode(this._htOption.text);\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Make the QRCode\r\n   *\r\n   * @param {String} sText link data\r\n   */\r\n  QRCode.prototype.makeCode = function (sText) {\r\n    this._oQRCode = new QRCodeModel(_getTypeNumber(sText, this._htOption.correctLevel), this._htOption.correctLevel);\r\n    this._oQRCode.addData(sText);\r\n    this._oQRCode.make();\r\n    this._el.title = sText;\r\n    this._oDrawing.draw(this._oQRCode);\r\n    this.makeImage();\r\n  };\r\n\r\n  /**\r\n   * Make the Image from Canvas element\r\n   * - It occurs automatically\r\n   * - Android below 3 doesn't support Data-URI spec.\r\n   *\r\n   * @private\r\n   */\r\n  QRCode.prototype.makeImage = function () {\r\n    if (typeof this._oDrawing.makeImage == \"function\" && (!this._android || this._android >= 3)) {\r\n      this._oDrawing.makeImage();\r\n    }\r\n  };\r\n\r\n  /**\r\n   * Clear the QRCode\r\n   */\r\n  QRCode.prototype.clear = function () {\r\n    this._oDrawing.clear();\r\n  };\r\n\r\n  /**\r\n   * @name QRCode.CorrectLevel\r\n   */\r\n  QRCode.CorrectLevel = QRErrorCorrectLevel;\r\n})();\r\n"], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAA,MAAM,CAACC,MAAM;AACb,CAAC,YAAY;EAEX;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,SAASC,UAAUA,CAACC,IAAI,EAAE;IACxB,IAAI,CAACC,IAAI,GAAGC,MAAM,CAACC,cAAc;IACjC,IAAI,CAACH,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACI,UAAU,GAAG,EAAE;;IAEpB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACN,IAAI,CAACO,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAChD,IAAIG,SAAS,GAAG,EAAE;MAClB,IAAIC,IAAI,GAAG,IAAI,CAACT,IAAI,CAACU,UAAU,CAACL,CAAC,CAAC;MAElC,IAAII,IAAI,GAAG,OAAO,EAAE;QAClBD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,QAAQ,MAAM,EAAG;QAChDD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,OAAO,MAAM,EAAG;QAC/CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,KAAK,MAAM,CAAE;QAC5CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIC,IAAI,GAAG,IAAK;MACrC,CAAC,MAAM,IAAIA,IAAI,GAAG,KAAK,EAAE;QACvBD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,MAAM,MAAM,EAAG;QAC9CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,KAAK,MAAM,CAAE;QAC5CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIC,IAAI,GAAG,IAAK;MACrC,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,EAAE;QACtBD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,CAACC,IAAI,GAAG,KAAK,MAAM,CAAE;QAC5CD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAIC,IAAI,GAAG,IAAK;MACrC,CAAC,MAAM;QACLD,SAAS,CAAC,CAAC,CAAC,GAAGC,IAAI;MACrB;MAEA,IAAI,CAACL,UAAU,CAACO,IAAI,CAACH,SAAS,CAAC;IACjC;IAEA,IAAI,CAACJ,UAAU,GAAGQ,KAAK,CAACC,SAAS,CAACC,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE,IAAI,CAACX,UAAU,CAAC;IAEnE,IAAI,IAAI,CAACA,UAAU,CAACG,MAAM,IAAI,IAAI,CAACP,IAAI,CAACO,MAAM,EAAE;MAC9C,IAAI,CAACH,UAAU,CAACY,OAAO,CAAC,GAAG,CAAC;MAC5B,IAAI,CAACZ,UAAU,CAACY,OAAO,CAAC,GAAG,CAAC;MAC5B,IAAI,CAACZ,UAAU,CAACY,OAAO,CAAC,GAAG,CAAC;IAC9B;EACF;EAEAjB,UAAU,CAACc,SAAS,GAAG;IACrBI,SAAS,EAAE,SAAAA,UAAUC,MAAM,EAAE;MAC3B,OAAO,IAAI,CAACd,UAAU,CAACG,MAAM;IAC/B,CAAC;IACDY,KAAK,EAAE,SAAAA,MAAUD,MAAM,EAAE;MACvB,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACF,UAAU,CAACG,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QACtDa,MAAM,CAACE,GAAG,CAAC,IAAI,CAAChB,UAAU,CAACC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnC;IACF;EACF,CAAC;EAED,SAASgB,WAAWA,CAACC,UAAU,EAAEC,iBAAiB,EAAE;IAClD,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,QAAQ,GAAG,EAAE;EACpB;EAEAN,WAAW,CAACR,SAAS,GAAG;IACtBe,OAAO,EAAE,SAAAA,QAAU5B,IAAI,EAAE;MACvB,IAAI6B,OAAO,GAAG,IAAI9B,UAAU,CAACC,IAAI,CAAC;MAClC,IAAI,CAAC2B,QAAQ,CAAChB,IAAI,CAACkB,OAAO,CAAC;MAC3B,IAAI,CAACH,SAAS,GAAG,IAAI;IACvB,CAAC;IAAEI,MAAM,EAAE,SAAAA,OAAUC,GAAG,EAAEC,GAAG,EAAE;MAC7B,IAAID,GAAG,GAAG,CAAC,IAAI,IAAI,CAACN,WAAW,IAAIM,GAAG,IAAIC,GAAG,GAAG,CAAC,IAAI,IAAI,CAACP,WAAW,IAAIO,GAAG,EAAE;QAC5E,MAAM,IAAIC,KAAK,CAACF,GAAG,GAAG,GAAG,GAAGC,GAAG,CAAC;MAClC;MACA,OAAO,IAAI,CAACR,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC;IAC/B,CAAC;IAAEE,cAAc,EAAE,SAAAA,eAAA,EAAY;MAC7B,OAAO,IAAI,CAACT,WAAW;IACzB,CAAC;IAAEU,IAAI,EAAE,SAAAA,KAAA,EAAY;MACnB,IAAI,CAACC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAACC,kBAAkB,CAAC,CAAC,CAAC;IACjD,CAAC;IAAED,QAAQ,EAAE,SAAAA,SAAUE,IAAI,EAAEC,WAAW,EAAE;MACxC,IAAI,CAACd,WAAW,GAAG,IAAI,CAACH,UAAU,GAAG,CAAC,GAAG,EAAE;MAC3C,IAAI,CAACE,OAAO,GAAG,IAAIZ,KAAK,CAAC,IAAI,CAACa,WAAW,CAAC;MAC1C,KAAK,IAAIM,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACN,WAAW,EAAEM,GAAG,EAAE,EAAE;QAC/C,IAAI,CAACP,OAAO,CAACO,GAAG,CAAC,GAAG,IAAInB,KAAK,CAAC,IAAI,CAACa,WAAW,CAAC;QAC/C,KAAK,IAAIO,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACP,WAAW,EAAEO,GAAG,EAAE,EAAE;UAC/C,IAAI,CAACR,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC,GAAG,IAAI;QAC/B;MACF;MACA,IAAI,CAACQ,yBAAyB,CAAC,CAAC,EAAE,CAAC,CAAC;MACpC,IAAI,CAACA,yBAAyB,CAAC,IAAI,CAACf,WAAW,GAAG,CAAC,EAAE,CAAC,CAAC;MACvD,IAAI,CAACe,yBAAyB,CAAC,CAAC,EAAE,IAAI,CAACf,WAAW,GAAG,CAAC,CAAC;MACvD,IAAI,CAACgB,0BAA0B,CAAC,CAAC;MACjC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACC,aAAa,CAACL,IAAI,EAAEC,WAAW,CAAC;MACrC,IAAI,IAAI,CAACjB,UAAU,IAAI,CAAC,EAAE;QACxB,IAAI,CAACsB,eAAe,CAACN,IAAI,CAAC;MAC5B;MACA,IAAI,IAAI,CAACZ,SAAS,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACA,SAAS,GAAGL,WAAW,CAACwB,UAAU,CAAC,IAAI,CAACvB,UAAU,EAAE,IAAI,CAACC,iBAAiB,EAAE,IAAI,CAACI,QAAQ,CAAC;MACjG;MACA,IAAI,CAACmB,OAAO,CAAC,IAAI,CAACpB,SAAS,EAAEa,WAAW,CAAC;IAC3C,CAAC;IAAEC,yBAAyB,EAAE,SAAAA,0BAAUT,GAAG,EAAEC,GAAG,EAAE;MAChD,KAAK,IAAIe,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC5B,IAAIhB,GAAG,GAAGgB,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAACtB,WAAW,IAAIM,GAAG,GAAGgB,CAAC,EAAE;QAClD,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UAC5B,IAAIhB,GAAG,GAAGgB,CAAC,IAAI,CAAC,CAAC,IAAI,IAAI,CAACvB,WAAW,IAAIO,GAAG,GAAGgB,CAAC,EAAE;UAClD,IAAK,CAAC,IAAID,CAAC,IAAIA,CAAC,IAAI,CAAC,KAAKC,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAC,IAAM,CAAC,IAAIA,CAAC,IAAIA,CAAC,IAAI,CAAC,KAAKD,CAAC,IAAI,CAAC,IAAIA,CAAC,IAAI,CAAC,CAAE,IAAK,CAAC,IAAIA,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAIA,CAAC,IAAI,CAAE,EAAE;YAClI,IAAI,CAACxB,OAAO,CAACO,GAAG,GAAGgB,CAAC,CAAC,CAACf,GAAG,GAAGgB,CAAC,CAAC,GAAG,IAAI;UACvC,CAAC,MAAM;YACL,IAAI,CAACxB,OAAO,CAACO,GAAG,GAAGgB,CAAC,CAAC,CAACf,GAAG,GAAGgB,CAAC,CAAC,GAAG,KAAK;UACxC;QACF;MACF;IACF,CAAC;IAAEX,kBAAkB,EAAE,SAAAA,mBAAA,EAAY;MACjC,IAAIY,YAAY,GAAG,CAAC;MACpB,IAAIC,OAAO,GAAG,CAAC;MACf,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,IAAI,CAAC+B,QAAQ,CAAC,IAAI,EAAE/B,CAAC,CAAC;QACtB,IAAI8C,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,IAAI,CAAC;QACzC,IAAIhD,CAAC,IAAI,CAAC,IAAI4C,YAAY,GAAGE,SAAS,EAAE;UACtCF,YAAY,GAAGE,SAAS;UACxBD,OAAO,GAAG7C,CAAC;QACb;MACF;MACA,OAAO6C,OAAO;IAChB,CAAC;IAAEI,eAAe,EAAE,SAAAA,gBAAUC,SAAS,EAAEC,aAAa,EAAEC,KAAK,EAAE;MAC7D,IAAIC,KAAK,GAAGH,SAAS,CAACI,oBAAoB,CAACH,aAAa,EAAEC,KAAK,CAAC;MAChE,IAAIG,EAAE,GAAG,CAAC;MACV,IAAI,CAACzB,IAAI,CAAC,CAAC;MACX,KAAK,IAAIJ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACP,OAAO,CAACjB,MAAM,EAAEwB,GAAG,EAAE,EAAE;QAClD,IAAI8B,CAAC,GAAG9B,GAAG,GAAG6B,EAAE;QAChB,KAAK,IAAI5B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,IAAI,CAACR,OAAO,CAACO,GAAG,CAAC,CAACxB,MAAM,EAAEyB,GAAG,EAAE,EAAE;UACvD,IAAI8B,CAAC,GAAG9B,GAAG,GAAG4B,EAAE;UAChB,IAAIG,IAAI,GAAG,IAAI,CAACvC,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC;UACjC,IAAI+B,IAAI,EAAE;YACRL,KAAK,CAACM,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;YACvBN,KAAK,CAACO,MAAM,CAACH,CAAC,EAAED,CAAC,CAAC;YAClBH,KAAK,CAACQ,MAAM,CAACJ,CAAC,GAAGF,EAAE,EAAEC,CAAC,CAAC;YACvBH,KAAK,CAACQ,MAAM,CAACJ,CAAC,GAAGF,EAAE,EAAEC,CAAC,GAAGD,EAAE,CAAC;YAC5BF,KAAK,CAACQ,MAAM,CAACJ,CAAC,EAAED,CAAC,GAAGD,EAAE,CAAC;YACvBF,KAAK,CAACS,OAAO,CAAC,CAAC;UACjB;QACF;MACF;MACA,OAAOT,KAAK;IACd,CAAC;IAAEhB,kBAAkB,EAAE,SAAAA,mBAAA,EAAY;MACjC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,WAAW,GAAG,CAAC,EAAEsB,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAACvB,OAAO,CAACuB,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;UAC9B;QACF;QACA,IAAI,CAACvB,OAAO,CAACuB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAIA,CAAC,GAAG,CAAC,IAAI,CAAE;MACnC;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,WAAW,GAAG,CAAC,EAAEuB,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAACxB,OAAO,CAAC,CAAC,CAAC,CAACwB,CAAC,CAAC,IAAI,IAAI,EAAE;UAC9B;QACF;QACA,IAAI,CAACxB,OAAO,CAAC,CAAC,CAAC,CAACwB,CAAC,CAAC,GAAIA,CAAC,GAAG,CAAC,IAAI,CAAE;MACnC;IACF,CAAC;IAAEP,0BAA0B,EAAE,SAAAA,2BAAA,EAAY;MACzC,IAAI2B,GAAG,GAAGhB,MAAM,CAACiB,kBAAkB,CAAC,IAAI,CAAC/C,UAAU,CAAC;MACpD,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+D,GAAG,CAAC7D,MAAM,EAAEF,CAAC,EAAE,EAAE;QACnC,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAAC7D,MAAM,EAAE+D,CAAC,EAAE,EAAE;UACnC,IAAIvC,GAAG,GAAGqC,GAAG,CAAC/D,CAAC,CAAC;UAChB,IAAI2B,GAAG,GAAGoC,GAAG,CAACE,CAAC,CAAC;UAChB,IAAI,IAAI,CAAC9C,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,CAAC,IAAI,IAAI,EAAE;YAClC;UACF;UACA,KAAK,IAAIe,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;cAC5B,IAAID,CAAC,IAAI,CAAC,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,CAAC,IAAIA,CAAC,IAAI,CAAC,IAAKD,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAE,EAAE;gBAChE,IAAI,CAACxB,OAAO,CAACO,GAAG,GAAGgB,CAAC,CAAC,CAACf,GAAG,GAAGgB,CAAC,CAAC,GAAG,IAAI;cACvC,CAAC,MAAM;gBACL,IAAI,CAACxB,OAAO,CAACO,GAAG,GAAGgB,CAAC,CAAC,CAACf,GAAG,GAAGgB,CAAC,CAAC,GAAG,KAAK;cACxC;YACF;UACF;QACF;MACF;IACF,CAAC;IAAEJ,eAAe,EAAE,SAAAA,gBAAUN,IAAI,EAAE;MAClC,IAAIiC,IAAI,GAAGnB,MAAM,CAACoB,gBAAgB,CAAC,IAAI,CAAClD,UAAU,CAAC;MACnD,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,IAAIoE,GAAG,GAAI,CAACnC,IAAI,IAAI,CAAEiC,IAAI,IAAIlE,CAAC,GAAI,CAAC,KAAK,CAAE;QAC3C,IAAI,CAACmB,OAAO,CAACkD,IAAI,CAACC,KAAK,CAACtE,CAAC,GAAG,CAAC,CAAC,CAAC,CAACA,CAAC,GAAG,CAAC,GAAG,IAAI,CAACoB,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGgD,GAAG;MACzE;MACA,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,IAAIoE,GAAG,GAAI,CAACnC,IAAI,IAAI,CAAEiC,IAAI,IAAIlE,CAAC,GAAI,CAAC,KAAK,CAAE;QAC3C,IAAI,CAACmB,OAAO,CAACnB,CAAC,GAAG,CAAC,GAAG,IAAI,CAACoB,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAACiD,IAAI,CAACC,KAAK,CAACtE,CAAC,GAAG,CAAC,CAAC,CAAC,GAAGoE,GAAG;MACzE;IACF,CAAC;IAAE9B,aAAa,EAAE,SAAAA,cAAUL,IAAI,EAAEC,WAAW,EAAE;MAC7C,IAAIvC,IAAI,GAAI,IAAI,CAACuB,iBAAiB,IAAI,CAAC,GAAIgB,WAAW;MACtD,IAAIgC,IAAI,GAAGnB,MAAM,CAACwB,cAAc,CAAC5E,IAAI,CAAC;MACtC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,IAAIoE,GAAG,GAAI,CAACnC,IAAI,IAAI,CAAEiC,IAAI,IAAIlE,CAAC,GAAI,CAAC,KAAK,CAAE;QAC3C,IAAIA,CAAC,GAAG,CAAC,EAAE;UACT,IAAI,CAACmB,OAAO,CAACnB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoE,GAAG;QAC1B,CAAC,MAAM,IAAIpE,CAAC,GAAG,CAAC,EAAE;UAChB,IAAI,CAACmB,OAAO,CAACnB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoE,GAAG;QAC9B,CAAC,MAAM;UACL,IAAI,CAACjD,OAAO,CAAC,IAAI,CAACC,WAAW,GAAG,EAAE,GAAGpB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoE,GAAG;QAClD;MACF;MACA,KAAK,IAAIpE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAC3B,IAAIoE,GAAG,GAAI,CAACnC,IAAI,IAAI,CAAEiC,IAAI,IAAIlE,CAAC,GAAI,CAAC,KAAK,CAAE;QAC3C,IAAIA,CAAC,GAAG,CAAC,EAAE;UACT,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,CAACC,WAAW,GAAGpB,CAAC,GAAG,CAAC,CAAC,GAAGoE,GAAG;QACjD,CAAC,MAAM,IAAIpE,CAAC,GAAG,CAAC,EAAE;UAChB,IAAI,CAACmB,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAGnB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAGoE,GAAG;QACvC,CAAC,MAAM;UACL,IAAI,CAACjD,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,GAAGnB,CAAC,GAAG,CAAC,CAAC,GAAGoE,GAAG;QACnC;MACF;MACA,IAAI,CAACjD,OAAO,CAAC,IAAI,CAACC,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAI,CAACa,IAAK;IACjD,CAAC;IAAEQ,OAAO,EAAE,SAAAA,QAAU9C,IAAI,EAAEuC,WAAW,EAAE;MACvC,IAAIsC,GAAG,GAAG,CAAC,CAAC;MACZ,IAAI9C,GAAG,GAAG,IAAI,CAACN,WAAW,GAAG,CAAC;MAC9B,IAAIqD,QAAQ,GAAG,CAAC;MAChB,IAAIC,SAAS,GAAG,CAAC;MACjB,KAAK,IAAI/C,GAAG,GAAG,IAAI,CAACP,WAAW,GAAG,CAAC,EAAEO,GAAG,GAAG,CAAC,EAAEA,GAAG,IAAI,CAAC,EAAE;QACtD,IAAIA,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE;QACnB,OAAO,IAAI,EAAE;UACX,KAAK,IAAIgB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC1B,IAAI,IAAI,CAACxB,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,GAAGgB,CAAC,CAAC,IAAI,IAAI,EAAE;cACtC,IAAIe,IAAI,GAAG,KAAK;cAChB,IAAIgB,SAAS,GAAG/E,IAAI,CAACO,MAAM,EAAE;gBAC3BwD,IAAI,GAAI,CAAE/D,IAAI,CAAC+E,SAAS,CAAC,KAAKD,QAAQ,GAAI,CAAC,KAAK,CAAE;cACpD;cACA,IAAIE,IAAI,GAAG5B,MAAM,CAAC6B,OAAO,CAAC1C,WAAW,EAAER,GAAG,EAAEC,GAAG,GAAGgB,CAAC,CAAC;cACpD,IAAIgC,IAAI,EAAE;gBACRjB,IAAI,GAAG,CAACA,IAAI;cACd;cACA,IAAI,CAACvC,OAAO,CAACO,GAAG,CAAC,CAACC,GAAG,GAAGgB,CAAC,CAAC,GAAGe,IAAI;cACjCe,QAAQ,EAAE;cACV,IAAIA,QAAQ,IAAI,CAAC,CAAC,EAAE;gBAClBC,SAAS,EAAE;gBACXD,QAAQ,GAAG,CAAC;cACd;YACF;UACF;UACA/C,GAAG,IAAI8C,GAAG;UACV,IAAI9C,GAAG,GAAG,CAAC,IAAI,IAAI,CAACN,WAAW,IAAIM,GAAG,EAAE;YACtCA,GAAG,IAAI8C,GAAG;YACVA,GAAG,GAAG,CAACA,GAAG;YACV;UACF;QACF;MACF;IACF;EACF,CAAC;EACDxD,WAAW,CAAC6D,IAAI,GAAG,IAAI;EACvB7D,WAAW,CAAC8D,IAAI,GAAG,IAAI;EACvB9D,WAAW,CAACwB,UAAU,GAAG,UAAUvB,UAAU,EAAEC,iBAAiB,EAAEI,QAAQ,EAAE;IAC1E,IAAIyD,QAAQ,GAAGC,SAAS,CAACC,WAAW,CAAChE,UAAU,EAAEC,iBAAiB,CAAC;IACnE,IAAIL,MAAM,GAAG,IAAIqE,WAAW,CAAC,CAAC;IAC9B,KAAK,IAAIlF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,QAAQ,CAACpB,MAAM,EAAEF,CAAC,EAAE,EAAE;MACxC,IAAIL,IAAI,GAAG2B,QAAQ,CAACtB,CAAC,CAAC;MACtBa,MAAM,CAACE,GAAG,CAACpB,IAAI,CAACC,IAAI,EAAE,CAAC,CAAC;MACxBiB,MAAM,CAACE,GAAG,CAACpB,IAAI,CAACiB,SAAS,CAAC,CAAC,EAAEmC,MAAM,CAACoC,eAAe,CAACxF,IAAI,CAACC,IAAI,EAAEqB,UAAU,CAAC,CAAC;MAC3EtB,IAAI,CAACmB,KAAK,CAACD,MAAM,CAAC;IACpB;IACA,IAAIuE,cAAc,GAAG,CAAC;IACtB,KAAK,IAAIpF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+E,QAAQ,CAAC7E,MAAM,EAAEF,CAAC,EAAE,EAAE;MACxCoF,cAAc,IAAIL,QAAQ,CAAC/E,CAAC,CAAC,CAACqF,SAAS;IACzC;IACA,IAAIxE,MAAM,CAACsE,eAAe,CAAC,CAAC,GAAGC,cAAc,GAAG,CAAC,EAAE;MACjD,MAAM,IAAIxD,KAAK,CAAC,yBAAyB,GACrCf,MAAM,CAACsE,eAAe,CAAC,CAAC,GACxB,GAAG,GACHC,cAAc,GAAG,CAAC,GAClB,GAAG,CAAC;IACV;IACA,IAAIvE,MAAM,CAACsE,eAAe,CAAC,CAAC,GAAG,CAAC,IAAIC,cAAc,GAAG,CAAC,EAAE;MACtDvE,MAAM,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAClB;IACA,OAAOF,MAAM,CAACsE,eAAe,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;MACxCtE,MAAM,CAACyE,MAAM,CAAC,KAAK,CAAC;IACtB;IACA,OAAO,IAAI,EAAE;MACX,IAAIzE,MAAM,CAACsE,eAAe,CAAC,CAAC,IAAIC,cAAc,GAAG,CAAC,EAAE;QAClD;MACF;MACAvE,MAAM,CAACE,GAAG,CAACC,WAAW,CAAC6D,IAAI,EAAE,CAAC,CAAC;MAC/B,IAAIhE,MAAM,CAACsE,eAAe,CAAC,CAAC,IAAIC,cAAc,GAAG,CAAC,EAAE;QAClD;MACF;MACAvE,MAAM,CAACE,GAAG,CAACC,WAAW,CAAC8D,IAAI,EAAE,CAAC,CAAC;IACjC;IACA,OAAO9D,WAAW,CAACuE,WAAW,CAAC1E,MAAM,EAAEkE,QAAQ,CAAC;EAClD,CAAC;EACD/D,WAAW,CAACuE,WAAW,GAAG,UAAU1E,MAAM,EAAEkE,QAAQ,EAAE;IACpD,IAAIS,MAAM,GAAG,CAAC;IACd,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,UAAU,GAAG,CAAC;IAClB,IAAIC,MAAM,GAAG,IAAIpF,KAAK,CAACwE,QAAQ,CAAC7E,MAAM,CAAC;IACvC,IAAI0F,MAAM,GAAG,IAAIrF,KAAK,CAACwE,QAAQ,CAAC7E,MAAM,CAAC;IACvC,KAAK,IAAIwC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,QAAQ,CAAC7E,MAAM,EAAEwC,CAAC,EAAE,EAAE;MACxC,IAAImD,OAAO,GAAGd,QAAQ,CAACrC,CAAC,CAAC,CAAC2C,SAAS;MACnC,IAAIS,OAAO,GAAGf,QAAQ,CAACrC,CAAC,CAAC,CAACqD,UAAU,GAAGF,OAAO;MAC9CJ,UAAU,GAAGpB,IAAI,CAAC2B,GAAG,CAACP,UAAU,EAAEI,OAAO,CAAC;MAC1CH,UAAU,GAAGrB,IAAI,CAAC2B,GAAG,CAACN,UAAU,EAAEI,OAAO,CAAC;MAC1CH,MAAM,CAACjD,CAAC,CAAC,GAAG,IAAInC,KAAK,CAACsF,OAAO,CAAC;MAC9B,KAAK,IAAI7F,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2F,MAAM,CAACjD,CAAC,CAAC,CAACxC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzC2F,MAAM,CAACjD,CAAC,CAAC,CAAC1C,CAAC,CAAC,GAAG,IAAI,GAAGa,MAAM,CAACA,MAAM,CAACb,CAAC,GAAGwF,MAAM,CAAC;MACjD;MACAA,MAAM,IAAIK,OAAO;MACjB,IAAII,MAAM,GAAGlD,MAAM,CAACmD,yBAAyB,CAACJ,OAAO,CAAC;MACtD,IAAIK,OAAO,GAAG,IAAIC,YAAY,CAACT,MAAM,CAACjD,CAAC,CAAC,EAAEuD,MAAM,CAACrF,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;MACjE,IAAIyF,OAAO,GAAGF,OAAO,CAAC/B,GAAG,CAAC6B,MAAM,CAAC;MACjCL,MAAM,CAAClD,CAAC,CAAC,GAAG,IAAInC,KAAK,CAAC0F,MAAM,CAACrF,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7C,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4F,MAAM,CAAClD,CAAC,CAAC,CAACxC,MAAM,EAAEF,CAAC,EAAE,EAAE;QACzC,IAAIsG,QAAQ,GAAGtG,CAAC,GAAGqG,OAAO,CAACzF,SAAS,CAAC,CAAC,GAAGgF,MAAM,CAAClD,CAAC,CAAC,CAACxC,MAAM;QACzD0F,MAAM,CAAClD,CAAC,CAAC,CAAC1C,CAAC,CAAC,GAAIsG,QAAQ,IAAI,CAAC,GAAID,OAAO,CAACE,GAAG,CAACD,QAAQ,CAAC,GAAG,CAAC;MAC5D;IACF;IACA,IAAIE,cAAc,GAAG,CAAC;IACtB,KAAK,IAAIxG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+E,QAAQ,CAAC7E,MAAM,EAAEF,CAAC,EAAE,EAAE;MACxCwG,cAAc,IAAIzB,QAAQ,CAAC/E,CAAC,CAAC,CAAC+F,UAAU;IAC1C;IACA,IAAIpG,IAAI,GAAG,IAAIY,KAAK,CAACiG,cAAc,CAAC;IACpC,IAAIC,KAAK,GAAG,CAAC;IACb,KAAK,IAAIzG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyF,UAAU,EAAEzF,CAAC,EAAE,EAAE;MACnC,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,QAAQ,CAAC7E,MAAM,EAAEwC,CAAC,EAAE,EAAE;QACxC,IAAI1C,CAAC,GAAG2F,MAAM,CAACjD,CAAC,CAAC,CAACxC,MAAM,EAAE;UACxBP,IAAI,CAAC8G,KAAK,EAAE,CAAC,GAAGd,MAAM,CAACjD,CAAC,CAAC,CAAC1C,CAAC,CAAC;QAC9B;MACF;IACF;IACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0F,UAAU,EAAE1F,CAAC,EAAE,EAAE;MACnC,KAAK,IAAI0C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqC,QAAQ,CAAC7E,MAAM,EAAEwC,CAAC,EAAE,EAAE;QACxC,IAAI1C,CAAC,GAAG4F,MAAM,CAAClD,CAAC,CAAC,CAACxC,MAAM,EAAE;UACxBP,IAAI,CAAC8G,KAAK,EAAE,CAAC,GAAGb,MAAM,CAAClD,CAAC,CAAC,CAAC1C,CAAC,CAAC;QAC9B;MACF;IACF;IACA,OAAOL,IAAI;EACb,CAAC;EACD,IAAIE,MAAM,GAAG;IAAC6G,WAAW,EAAE,CAAC,IAAI,CAAC;IAAEC,cAAc,EAAE,CAAC,IAAI,CAAC;IAAE7G,cAAc,EAAE,CAAC,IAAI,CAAC;IAAE8G,UAAU,EAAE,CAAC,IAAI;EAAC,CAAC;EACtG,IAAIC,mBAAmB,GAAG;IAACC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAC,CAAC;EAClD,IAAIC,aAAa,GAAG;IAClBC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC;EACD,IAAI3E,MAAM,GAAG;IACX4E,sBAAsB,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAC/yBC,GAAG,EAAG,CAAC,IAAI,EAAE,GAAK,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE;IAChFC,GAAG,EAAG,CAAC,IAAI,EAAE,GAAK,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE;IAC7FC,QAAQ,EAAG,CAAC,IAAI,EAAE,GAAK,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,EAAG,GAAI,CAAC,IAAI,CAAE,GAAI,CAAC,IAAI,CAAE;IACjEvD,cAAc,EAAE,SAAAA,eAAU5E,IAAI,EAAE;MAC9B,IAAIoI,CAAC,GAAGpI,IAAI,IAAI,EAAE;MAClB,OAAOoD,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAGhF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC6E,GAAG,CAAC,IAAI,CAAC,EAAE;QAClEG,CAAC,IAAKhF,MAAM,CAAC6E,GAAG,IAAK7E,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAGhF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC6E,GAAG,CAAG;MAC/E;MACA,OAAO,CAAEjI,IAAI,IAAI,EAAE,GAAIoI,CAAC,IAAIhF,MAAM,CAAC+E,QAAQ;IAC7C,CAAC;IACD3D,gBAAgB,EAAE,SAAAA,iBAAUxE,IAAI,EAAE;MAChC,IAAIoI,CAAC,GAAGpI,IAAI,IAAI,EAAE;MAClB,OAAOoD,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAGhF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC8E,GAAG,CAAC,IAAI,CAAC,EAAE;QAClEE,CAAC,IAAKhF,MAAM,CAAC8E,GAAG,IAAK9E,MAAM,CAACiF,WAAW,CAACD,CAAC,CAAC,GAAGhF,MAAM,CAACiF,WAAW,CAACjF,MAAM,CAAC8E,GAAG,CAAG;MAC/E;MACA,OAAQlI,IAAI,IAAI,EAAE,GAAIoI,CAAC;IACzB,CAAC;IACDC,WAAW,EAAE,SAAAA,YAAUrI,IAAI,EAAE;MAC3B,IAAIsI,KAAK,GAAG,CAAC;MACb,OAAOtI,IAAI,IAAI,CAAC,EAAE;QAChBsI,KAAK,EAAE;QACPtI,IAAI,MAAM,CAAC;MACb;MACA,OAAOsI,KAAK;IACd,CAAC;IACDjE,kBAAkB,EAAE,SAAAA,mBAAU/C,UAAU,EAAE;MACxC,OAAO8B,MAAM,CAAC4E,sBAAsB,CAAC1G,UAAU,GAAG,CAAC,CAAC;IACtD,CAAC;IACD2D,OAAO,EAAE,SAAAA,QAAU1C,WAAW,EAAElC,CAAC,EAAEiE,CAAC,EAAE;MACpC,QAAQ/B,WAAW;QACjB,KAAKgF,aAAa,CAACC,UAAU;UAC3B,OAAO,CAACnH,CAAC,GAAGiE,CAAC,IAAI,CAAC,IAAI,CAAC;QACzB,KAAKiD,aAAa,CAACE,UAAU;UAC3B,OAAOpH,CAAC,GAAG,CAAC,IAAI,CAAC;QACnB,KAAKkH,aAAa,CAACG,UAAU;UAC3B,OAAOpD,CAAC,GAAG,CAAC,IAAI,CAAC;QACnB,KAAKiD,aAAa,CAACI,UAAU;UAC3B,OAAO,CAACtH,CAAC,GAAGiE,CAAC,IAAI,CAAC,IAAI,CAAC;QACzB,KAAKiD,aAAa,CAACK,UAAU;UAC3B,OAAO,CAAClD,IAAI,CAACC,KAAK,CAACtE,CAAC,GAAG,CAAC,CAAC,GAAGqE,IAAI,CAACC,KAAK,CAACL,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QACzD,KAAKiD,aAAa,CAACM,UAAU;UAC3B,OAAQxH,CAAC,GAAGiE,CAAC,GAAI,CAAC,GAAIjE,CAAC,GAAGiE,CAAC,GAAI,CAAC,IAAI,CAAC;QACvC,KAAKiD,aAAa,CAACO,UAAU;UAC3B,OAAO,CAAEzH,CAAC,GAAGiE,CAAC,GAAI,CAAC,GAAIjE,CAAC,GAAGiE,CAAC,GAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7C,KAAKiD,aAAa,CAACQ,UAAU;UAC3B,OAAO,CAAE1H,CAAC,GAAGiE,CAAC,GAAI,CAAC,GAAG,CAACjE,CAAC,GAAGiE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;QAC7C;UACE,MAAM,IAAIrC,KAAK,CAAC,kBAAkB,GAAGM,WAAW,CAAC;MACrD;IACF,CAAC;IACDgE,yBAAyB,EAAE,SAAAA,0BAAUgC,kBAAkB,EAAE;MACvD,IAAIC,CAAC,GAAG,IAAI/B,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MAChC,KAAK,IAAIpG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkI,kBAAkB,EAAElI,CAAC,EAAE,EAAE;QAC3CmI,CAAC,GAAGA,CAAC,CAACC,QAAQ,CAAC,IAAIhC,YAAY,CAAC,CAAC,CAAC,EAAEiC,MAAM,CAACC,IAAI,CAACtI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC1D;MACA,OAAOmI,CAAC;IACV,CAAC;IACDhD,eAAe,EAAE,SAAAA,gBAAUvF,IAAI,EAAE2I,IAAI,EAAE;MACrC,IAAI,CAAC,IAAIA,IAAI,IAAIA,IAAI,GAAG,EAAE,EAAE;QAC1B,QAAQ3I,IAAI;UACV,KAAKC,MAAM,CAAC6G,WAAW;YACrB,OAAO,EAAE;UACX,KAAK7G,MAAM,CAAC8G,cAAc;YACxB,OAAO,CAAC;UACV,KAAK9G,MAAM,CAACC,cAAc;YACxB,OAAO,CAAC;UACV,KAAKD,MAAM,CAAC+G,UAAU;YACpB,OAAO,CAAC;UACV;YACE,MAAM,IAAIhF,KAAK,CAAC,OAAO,GAAGhC,IAAI,CAAC;QACnC;MACF,CAAC,MAAM,IAAI2I,IAAI,GAAG,EAAE,EAAE;QACpB,QAAQ3I,IAAI;UACV,KAAKC,MAAM,CAAC6G,WAAW;YACrB,OAAO,EAAE;UACX,KAAK7G,MAAM,CAAC8G,cAAc;YACxB,OAAO,EAAE;UACX,KAAK9G,MAAM,CAACC,cAAc;YACxB,OAAO,EAAE;UACX,KAAKD,MAAM,CAAC+G,UAAU;YACpB,OAAO,EAAE;UACX;YACE,MAAM,IAAIhF,KAAK,CAAC,OAAO,GAAGhC,IAAI,CAAC;QACnC;MACF,CAAC,MAAM,IAAI2I,IAAI,GAAG,EAAE,EAAE;QACpB,QAAQ3I,IAAI;UACV,KAAKC,MAAM,CAAC6G,WAAW;YACrB,OAAO,EAAE;UACX,KAAK7G,MAAM,CAAC8G,cAAc;YACxB,OAAO,EAAE;UACX,KAAK9G,MAAM,CAACC,cAAc;YACxB,OAAO,EAAE;UACX,KAAKD,MAAM,CAAC+G,UAAU;YACpB,OAAO,EAAE;UACX;YACE,MAAM,IAAIhF,KAAK,CAAC,OAAO,GAAGhC,IAAI,CAAC;QACnC;MACF,CAAC,MAAM;QACL,MAAM,IAAIgC,KAAK,CAAC,OAAO,GAAG2G,IAAI,CAAC;MACjC;IACF,CAAC;IACDvF,YAAY,EAAE,SAAAA,aAAUwF,MAAM,EAAE;MAC9B,IAAIpH,WAAW,GAAGoH,MAAM,CAAC3G,cAAc,CAAC,CAAC;MACzC,IAAIiB,SAAS,GAAG,CAAC;MACjB,KAAK,IAAIpB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,WAAW,EAAEM,GAAG,EAAE,EAAE;QAC1C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,WAAW,EAAEO,GAAG,EAAE,EAAE;UAC1C,IAAI8G,SAAS,GAAG,CAAC;UACjB,IAAI/E,IAAI,GAAG8E,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC;UAClC,KAAK,IAAIe,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC5B,IAAIhB,GAAG,GAAGgB,CAAC,GAAG,CAAC,IAAItB,WAAW,IAAIM,GAAG,GAAGgB,CAAC,EAAE;cACzC;YACF;YACA,KAAK,IAAIC,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;cAC5B,IAAIhB,GAAG,GAAGgB,CAAC,GAAG,CAAC,IAAIvB,WAAW,IAAIO,GAAG,GAAGgB,CAAC,EAAE;gBACzC;cACF;cACA,IAAID,CAAC,IAAI,CAAC,IAAIC,CAAC,IAAI,CAAC,EAAE;gBACpB;cACF;cACA,IAAIe,IAAI,IAAI8E,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAGgB,CAAC,EAAEf,GAAG,GAAGgB,CAAC,CAAC,EAAE;gBAC3C8F,SAAS,EAAE;cACb;YACF;UACF;UACA,IAAIA,SAAS,GAAG,CAAC,EAAE;YACjB3F,SAAS,IAAK,CAAC,GAAG2F,SAAS,GAAG,CAAE;UAClC;QACF;MACF;MACA,KAAK,IAAI/G,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,WAAW,GAAG,CAAC,EAAEM,GAAG,EAAE,EAAE;QAC9C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,WAAW,GAAG,CAAC,EAAEO,GAAG,EAAE,EAAE;UAC9C,IAAI+G,KAAK,GAAG,CAAC;UACb,IAAIF,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE+G,KAAK,EAAE;UACpC,IAAIF,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,EAAE+G,KAAK,EAAE;UACxC,IAAIF,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,EAAE+G,KAAK,EAAE;UACxC,IAAIF,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,CAAC,CAAC,EAAE+G,KAAK,EAAE;UAC5C,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,IAAI,CAAC,EAAE;YAC5B5F,SAAS,IAAI,CAAC;UAChB;QACF;MACF;MACA,KAAK,IAAIpB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,WAAW,EAAEM,GAAG,EAAE,EAAE;QAC1C,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,WAAW,GAAG,CAAC,EAAEO,GAAG,EAAE,EAAE;UAC9C,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,IAAI,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,GAAG,CAAC,CAAC,EAAE;YACvNmB,SAAS,IAAI,EAAE;UACjB;QACF;MACF;MACA,KAAK,IAAInB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,WAAW,EAAEO,GAAG,EAAE,EAAE;QAC1C,KAAK,IAAID,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,WAAW,GAAG,CAAC,EAAEM,GAAG,EAAE,EAAE;UAC9C,IAAI8G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,IAAI,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,IAAI,CAAC6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,IAAI6G,MAAM,CAAC/G,MAAM,CAACC,GAAG,GAAG,CAAC,EAAEC,GAAG,CAAC,EAAE;YACvNmB,SAAS,IAAI,EAAE;UACjB;QACF;MACF;MACA,IAAI6F,SAAS,GAAG,CAAC;MACjB,KAAK,IAAIhH,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGP,WAAW,EAAEO,GAAG,EAAE,EAAE;QAC1C,KAAK,IAAID,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGN,WAAW,EAAEM,GAAG,EAAE,EAAE;UAC1C,IAAI8G,MAAM,CAAC/G,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE;YAC3BgH,SAAS,EAAE;UACb;QACF;MACF;MACA,IAAIC,KAAK,GAAGvE,IAAI,CAACwE,GAAG,CAAC,GAAG,GAAGF,SAAS,GAAGvH,WAAW,GAAGA,WAAW,GAAG,EAAE,CAAC,GAAG,CAAC;MAC1E0B,SAAS,IAAI8F,KAAK,GAAG,EAAE;MACvB,OAAO9F,SAAS;IAClB;EACF,CAAC;EACD,IAAIuF,MAAM,GAAG;IACXS,IAAI,EAAE,SAAAA,KAAUC,CAAC,EAAE;MACjB,IAAIA,CAAC,GAAG,CAAC,EAAE;QACT,MAAM,IAAInH,KAAK,CAAC,OAAO,GAAGmH,CAAC,GAAG,GAAG,CAAC;MACpC;MACA,OAAOV,MAAM,CAACW,SAAS,CAACD,CAAC,CAAC;IAC5B,CAAC;IAAET,IAAI,EAAE,SAAAA,KAAUS,CAAC,EAAE;MACpB,OAAOA,CAAC,GAAG,CAAC,EAAE;QACZA,CAAC,IAAI,GAAG;MACV;MACA,OAAOA,CAAC,IAAI,GAAG,EAAE;QACfA,CAAC,IAAI,GAAG;MACV;MACA,OAAOV,MAAM,CAACY,SAAS,CAACF,CAAC,CAAC;IAC5B,CAAC;IAAEE,SAAS,EAAE,IAAI1I,KAAK,CAAC,GAAG,CAAC;IAAEyI,SAAS,EAAE,IAAIzI,KAAK,CAAC,GAAG;EACxD,CAAC;EACD,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC1BqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,CAAC,GAAG,CAAC,IAAIA,CAAC;EAC9B;EACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5BqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,CAAC,GAAGqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAG,CAAC,CAAC,GAAGqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAG,CAAC,CAAC,GAAGqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAG,CAAC,CAAC,GAAGqI,MAAM,CAACY,SAAS,CAACjJ,CAAC,GAAG,CAAC,CAAC;EAC7H;EACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IAC5BqI,MAAM,CAACW,SAAS,CAACX,MAAM,CAACY,SAAS,CAACjJ,CAAC,CAAC,CAAC,GAAGA,CAAC;EAC3C;EAEA,SAASoG,YAAYA,CAAC8C,GAAG,EAAEC,KAAK,EAAE;IAChC,IAAID,GAAG,CAAChJ,MAAM,IAAIkJ,SAAS,EAAE;MAC3B,MAAM,IAAIxH,KAAK,CAACsH,GAAG,CAAChJ,MAAM,GAAG,GAAG,GAAGiJ,KAAK,CAAC;IAC3C;IACA,IAAI3D,MAAM,GAAG,CAAC;IACd,OAAOA,MAAM,GAAG0D,GAAG,CAAChJ,MAAM,IAAIgJ,GAAG,CAAC1D,MAAM,CAAC,IAAI,CAAC,EAAE;MAC9CA,MAAM,EAAE;IACV;IACA,IAAI,CAAC0D,GAAG,GAAG,IAAI3I,KAAK,CAAC2I,GAAG,CAAChJ,MAAM,GAAGsF,MAAM,GAAG2D,KAAK,CAAC;IACjD,KAAK,IAAInJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkJ,GAAG,CAAChJ,MAAM,GAAGsF,MAAM,EAAExF,CAAC,EAAE,EAAE;MAC5C,IAAI,CAACkJ,GAAG,CAAClJ,CAAC,CAAC,GAAGkJ,GAAG,CAAClJ,CAAC,GAAGwF,MAAM,CAAC;IAC/B;EACF;EAEAY,YAAY,CAAC5F,SAAS,GAAG;IACvB+F,GAAG,EAAE,SAAAA,IAAUE,KAAK,EAAE;MACpB,OAAO,IAAI,CAACyC,GAAG,CAACzC,KAAK,CAAC;IACxB,CAAC;IAAE7F,SAAS,EAAE,SAAAA,UAAA,EAAY;MACxB,OAAO,IAAI,CAACsI,GAAG,CAAChJ,MAAM;IACxB,CAAC;IAAEkI,QAAQ,EAAE,SAAAA,SAAUiB,CAAC,EAAE;MACxB,IAAIH,GAAG,GAAG,IAAI3I,KAAK,CAAC,IAAI,CAACK,SAAS,CAAC,CAAC,GAAGyI,CAAC,CAACzI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;MACzD,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACY,SAAS,CAAC,CAAC,EAAEZ,CAAC,EAAE,EAAE;QACzC,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoF,CAAC,CAACzI,SAAS,CAAC,CAAC,EAAEqD,CAAC,EAAE,EAAE;UACtCiF,GAAG,CAAClJ,CAAC,GAAGiE,CAAC,CAAC,IAAIoE,MAAM,CAACC,IAAI,CAACD,MAAM,CAACS,IAAI,CAAC,IAAI,CAACvC,GAAG,CAACvG,CAAC,CAAC,CAAC,GAAGqI,MAAM,CAACS,IAAI,CAACO,CAAC,CAAC9C,GAAG,CAACtC,CAAC,CAAC,CAAC,CAAC;QAC7E;MACF;MACA,OAAO,IAAImC,YAAY,CAAC8C,GAAG,EAAE,CAAC,CAAC;IACjC,CAAC;IAAE9E,GAAG,EAAE,SAAAA,IAAUiF,CAAC,EAAE;MACnB,IAAI,IAAI,CAACzI,SAAS,CAAC,CAAC,GAAGyI,CAAC,CAACzI,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;QACxC,OAAO,IAAI;MACb;MACA,IAAIgI,KAAK,GAAGP,MAAM,CAACS,IAAI,CAAC,IAAI,CAACvC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG8B,MAAM,CAACS,IAAI,CAACO,CAAC,CAAC9C,GAAG,CAAC,CAAC,CAAC,CAAC;MAC5D,IAAI2C,GAAG,GAAG,IAAI3I,KAAK,CAAC,IAAI,CAACK,SAAS,CAAC,CAAC,CAAC;MACrC,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACY,SAAS,CAAC,CAAC,EAAEZ,CAAC,EAAE,EAAE;QACzCkJ,GAAG,CAAClJ,CAAC,CAAC,GAAG,IAAI,CAACuG,GAAG,CAACvG,CAAC,CAAC;MACtB;MACA,KAAK,IAAIA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqJ,CAAC,CAACzI,SAAS,CAAC,CAAC,EAAEZ,CAAC,EAAE,EAAE;QACtCkJ,GAAG,CAAClJ,CAAC,CAAC,IAAIqI,MAAM,CAACC,IAAI,CAACD,MAAM,CAACS,IAAI,CAACO,CAAC,CAAC9C,GAAG,CAACvG,CAAC,CAAC,CAAC,GAAG4I,KAAK,CAAC;MACtD;MACA,OAAO,IAAIxC,YAAY,CAAC8C,GAAG,EAAE,CAAC,CAAC,CAAC9E,GAAG,CAACiF,CAAC,CAAC;IACxC;EACF,CAAC;EAED,SAASrE,SAASA,CAACe,UAAU,EAAEV,SAAS,EAAE;IACxC,IAAI,CAACU,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACV,SAAS,GAAGA,SAAS;EAC5B;EAEAL,SAAS,CAACsE,cAAc,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EAC3pHtE,SAAS,CAACC,WAAW,GAAG,UAAUhE,UAAU,EAAEC,iBAAiB,EAAE;IAC/D,IAAIqI,OAAO,GAAGvE,SAAS,CAACwE,eAAe,CAACvI,UAAU,EAAEC,iBAAiB,CAAC;IACtE,IAAIqI,OAAO,IAAIH,SAAS,EAAE;MACxB,MAAM,IAAIxH,KAAK,CAAC,4BAA4B,GAAGX,UAAU,GAAG,qBAAqB,GAAGC,iBAAiB,CAAC;IACxG;IACA,IAAIhB,MAAM,GAAGqJ,OAAO,CAACrJ,MAAM,GAAG,CAAC;IAC/B,IAAIuJ,IAAI,GAAG,EAAE;IACb,KAAK,IAAIzJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC/B,IAAI0I,KAAK,GAAGa,OAAO,CAACvJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAC9B,IAAI+F,UAAU,GAAGwD,OAAO,CAACvJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACnC,IAAIqF,SAAS,GAAGkE,OAAO,CAACvJ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MAClC,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyE,KAAK,EAAEzE,CAAC,EAAE,EAAE;QAC9BwF,IAAI,CAACnJ,IAAI,CAAC,IAAI0E,SAAS,CAACe,UAAU,EAAEV,SAAS,CAAC,CAAC;MACjD;IACF;IACA,OAAOoE,IAAI;EACb,CAAC;EACDzE,SAAS,CAACwE,eAAe,GAAG,UAAUvI,UAAU,EAAEC,iBAAiB,EAAE;IACnE,QAAQA,iBAAiB;MACvB,KAAK2F,mBAAmB,CAACC,CAAC;QACxB,OAAO9B,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3D,KAAK4F,mBAAmB,CAACE,CAAC;QACxB,OAAO/B,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3D,KAAK4F,mBAAmB,CAACG,CAAC;QACxB,OAAOhC,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3D,KAAK4F,mBAAmB,CAACI,CAAC;QACxB,OAAOjC,SAAS,CAACsE,cAAc,CAAC,CAACrI,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;MAC3D;QACE,OAAOmI,SAAS;IACpB;EACF,CAAC;EAED,SAASlE,WAAWA,CAAA,EAAG;IACrB,IAAI,CAACrE,MAAM,GAAG,EAAE;IAChB,IAAI,CAACX,MAAM,GAAG,CAAC;EACjB;EAEAgF,WAAW,CAAC1E,SAAS,GAAG;IACtB+F,GAAG,EAAE,SAAAA,IAAUE,KAAK,EAAE;MACpB,IAAIiD,QAAQ,GAAGrF,IAAI,CAACC,KAAK,CAACmC,KAAK,GAAG,CAAC,CAAC;MACpC,OAAO,CAAE,IAAI,CAAC5F,MAAM,CAAC6I,QAAQ,CAAC,KAAM,CAAC,GAAGjD,KAAK,GAAG,CAAE,GAAI,CAAC,KAAK,CAAC;IAC/D,CAAC;IAAE1F,GAAG,EAAE,SAAAA,IAAUmI,GAAG,EAAEhJ,MAAM,EAAE;MAC7B,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGE,MAAM,EAAEF,CAAC,EAAE,EAAE;QAC/B,IAAI,CAACsF,MAAM,CAAC,CAAE4D,GAAG,KAAMhJ,MAAM,GAAGF,CAAC,GAAG,CAAE,GAAI,CAAC,KAAK,CAAC,CAAC;MACpD;IACF,CAAC;IAAEmF,eAAe,EAAE,SAAAA,gBAAA,EAAY;MAC9B,OAAO,IAAI,CAACjF,MAAM;IACpB,CAAC;IAAEoF,MAAM,EAAE,SAAAA,OAAUqE,GAAG,EAAE;MACxB,IAAID,QAAQ,GAAGrF,IAAI,CAACC,KAAK,CAAC,IAAI,CAACpE,MAAM,GAAG,CAAC,CAAC;MAC1C,IAAI,IAAI,CAACW,MAAM,CAACX,MAAM,IAAIwJ,QAAQ,EAAE;QAClC,IAAI,CAAC7I,MAAM,CAACP,IAAI,CAAC,CAAC,CAAC;MACrB;MACA,IAAIqJ,GAAG,EAAE;QACP,IAAI,CAAC9I,MAAM,CAAC6I,QAAQ,CAAC,IAAK,IAAI,KAAM,IAAI,CAACxJ,MAAM,GAAG,CAAG;MACvD;MACA,IAAI,CAACA,MAAM,EAAE;IACf;EACF,CAAC;EACD,IAAI0J,iBAAiB,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EAE/5B,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,OAAO,OAAOC,wBAAwB,IAAI,WAAW;EACvD;;EAEA;EACA,SAASC,WAAWA,CAAA,EAAG;IACrB,IAAIC,OAAO,GAAG,KAAK;IACnB,IAAIC,MAAM,GAAGC,SAAS,CAACC,SAAS;IAEhC,IAAI,UAAU,CAAClI,IAAI,CAACgI,MAAM,CAAC,EAAE;MAAE;MAC7BD,OAAO,GAAG,IAAI;MACd,IAAII,IAAI,GAAGH,MAAM,CAACI,QAAQ,CAAC,CAAC,CAACC,KAAK,CAAC,yBAAyB,CAAC;MAE7D,IAAIF,IAAI,IAAIA,IAAI,CAAC,CAAC,CAAC,EAAE;QACnBJ,OAAO,GAAGO,UAAU,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC;MAC/B;IACF;IAEA,OAAOJ,OAAO;EAChB;EAEA,IAAIQ,SAAS,GAAI,YAAY;IAE3B,IAAIC,OAAO,GAAG,SAAVA,OAAOA,CAAaC,EAAE,EAAEC,QAAQ,EAAE;MACpC,IAAI,CAACC,GAAG,GAAGF,EAAE;MACb,IAAI,CAACG,SAAS,GAAGF,QAAQ;IAC3B,CAAC;IAEDF,OAAO,CAACjK,SAAS,CAACsK,IAAI,GAAG,UAAUC,OAAO,EAAE;MAC1C,IAAIF,SAAS,GAAG,IAAI,CAACA,SAAS;MAC9B,IAAID,GAAG,GAAG,IAAI,CAACA,GAAG;MAClB,IAAII,MAAM,GAAGD,OAAO,CAAClJ,cAAc,CAAC,CAAC;MACrC,IAAIoJ,MAAM,GAAG5G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACK,KAAK,GAAGF,MAAM,CAAC;MACjD,IAAIG,OAAO,GAAG9G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACO,MAAM,GAAGJ,MAAM,CAAC;MAEnD,IAAI,CAACK,KAAK,CAAC,CAAC;MAEZ,SAASC,OAAOA,CAACC,GAAG,EAAEC,KAAK,EAAE;QAC3B,IAAId,EAAE,GAAGe,QAAQ,CAACC,eAAe,CAAC,4BAA4B,EAAEH,GAAG,CAAC;QACpE,KAAK,IAAII,CAAC,IAAIH,KAAK,EACjB,IAAIA,KAAK,CAACI,cAAc,CAACD,CAAC,CAAC,EAAEjB,EAAE,CAACmB,YAAY,CAACF,CAAC,EAAEH,KAAK,CAACG,CAAC,CAAC,CAAC;QAC3D,OAAOjB,EAAE;MACX;MAEA,IAAIoB,GAAG,GAAGR,OAAO,CAAC,KAAK,EAAE;QACvB,SAAS,EAAE,MAAM,GAAGS,MAAM,CAACf,MAAM,CAAC,GAAG,GAAG,GAAGe,MAAM,CAACf,MAAM,CAAC;QACzD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE,MAAM;QAChB,MAAM,EAAEH,SAAS,CAACmB;MACpB,CAAC,CAAC;MACFF,GAAG,CAACG,cAAc,CAAC,+BAA+B,EAAE,aAAa,EAAE,8BAA8B,CAAC;MAClGrB,GAAG,CAACsB,WAAW,CAACJ,GAAG,CAAC;MAEpBA,GAAG,CAACI,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE;QAAC,MAAM,EAAET,SAAS,CAACmB,UAAU;QAAE,OAAO,EAAE,MAAM;QAAE,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC;MACnGF,GAAG,CAACI,WAAW,CAACZ,OAAO,CAAC,MAAM,EAAE;QAAC,MAAM,EAAET,SAAS,CAACsB,SAAS;QAAE,OAAO,EAAE,GAAG;QAAE,QAAQ,EAAE,GAAG;QAAE,IAAI,EAAE;MAAU,CAAC,CAAC,CAAC;MAE9G,KAAK,IAAIzK,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGsJ,MAAM,EAAEtJ,GAAG,EAAE,EAAE;QACrC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGqJ,MAAM,EAAErJ,GAAG,EAAE,EAAE;UACrC,IAAIoJ,OAAO,CAACtJ,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,EAAE;YAC5B,IAAIyK,KAAK,GAAGd,OAAO,CAAC,KAAK,EAAE;cAAC,GAAG,EAAES,MAAM,CAACpK,GAAG,CAAC;cAAE,GAAG,EAAEoK,MAAM,CAACrK,GAAG;YAAC,CAAC,CAAC;YAChE0K,KAAK,CAACH,cAAc,CAAC,8BAA8B,EAAE,MAAM,EAAE,WAAW,CAAC;YACzEH,GAAG,CAACI,WAAW,CAACE,KAAK,CAAC;UACxB;QACF;MACF;IACF,CAAC;IACD3B,OAAO,CAACjK,SAAS,CAAC6K,KAAK,GAAG,YAAY;MACpC,OAAO,IAAI,CAACT,GAAG,CAACyB,aAAa,CAAC,CAAC,EAC7B,IAAI,CAACzB,GAAG,CAAC0B,WAAW,CAAC,IAAI,CAAC1B,GAAG,CAAC2B,SAAS,CAAC;IAC5C,CAAC;IACD,OAAO9B,OAAO;EAChB,CAAC,CAAE,CAAC;EAEJ,IAAI+B,MAAM,GAAGf,QAAQ,CAACgB,eAAe,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK;;EAErE;EACA,IAAIlC,OAAO,GAAG+B,MAAM,GAAGhC,SAAS,GAAG,CAACX,gBAAgB,CAAC,CAAC,GAAI,YAAY;IACpE,IAAIY,OAAO,GAAG,SAAVA,OAAOA,CAAaC,EAAE,EAAEC,QAAQ,EAAE;MACpC,IAAI,CAACC,GAAG,GAAGF,EAAE;MACb,IAAI,CAACG,SAAS,GAAGF,QAAQ;IAC3B,CAAC;;IAED;AACJ;AACA;AACA;AACA;IACIF,OAAO,CAACjK,SAAS,CAACsK,IAAI,GAAG,UAAUC,OAAO,EAAE;MAC1C,IAAIF,SAAS,GAAG,IAAI,CAACA,SAAS;MAC9B,IAAID,GAAG,GAAG,IAAI,CAACA,GAAG;MAClB,IAAII,MAAM,GAAGD,OAAO,CAAClJ,cAAc,CAAC,CAAC;MACrC,IAAIoJ,MAAM,GAAG5G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACK,KAAK,GAAGF,MAAM,CAAC;MACjD,IAAIG,OAAO,GAAG9G,IAAI,CAACC,KAAK,CAACuG,SAAS,CAACO,MAAM,GAAGJ,MAAM,CAAC;MACnD,IAAI4B,KAAK,GAAG,CAAC,oDAAoD,CAAC;MAElE,KAAK,IAAIlL,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGsJ,MAAM,EAAEtJ,GAAG,EAAE,EAAE;QACrCkL,KAAK,CAACtM,IAAI,CAAC,MAAM,CAAC;QAElB,KAAK,IAAIqB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGqJ,MAAM,EAAErJ,GAAG,EAAE,EAAE;UACrCiL,KAAK,CAACtM,IAAI,CAAC,wEAAwE,GAAG2K,MAAM,GAAG,YAAY,GAAGE,OAAO,GAAG,sBAAsB,IAAIJ,OAAO,CAACtJ,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC,GAAGkJ,SAAS,CAACsB,SAAS,GAAGtB,SAAS,CAACmB,UAAU,CAAC,GAAG,UAAU,CAAC;QACxO;QAEAY,KAAK,CAACtM,IAAI,CAAC,OAAO,CAAC;MACrB;MAEAsM,KAAK,CAACtM,IAAI,CAAC,UAAU,CAAC;MACtBsK,GAAG,CAACiC,SAAS,GAAGD,KAAK,CAACE,IAAI,CAAC,EAAE,CAAC;;MAE9B;MACA,IAAIC,OAAO,GAAGnC,GAAG,CAACoC,UAAU,CAAC,CAAC,CAAC;MAC/B,IAAIC,gBAAgB,GAAG,CAACpC,SAAS,CAACK,KAAK,GAAG6B,OAAO,CAACG,WAAW,IAAI,CAAC;MAClE,IAAIC,eAAe,GAAG,CAACtC,SAAS,CAACO,MAAM,GAAG2B,OAAO,CAACK,YAAY,IAAI,CAAC;MAEnE,IAAIH,gBAAgB,GAAG,CAAC,IAAIE,eAAe,GAAG,CAAC,EAAE;QAC/CJ,OAAO,CAACM,KAAK,CAACC,MAAM,GAAGH,eAAe,GAAG,KAAK,GAAGF,gBAAgB,GAAG,IAAI;MAC1E;IACF,CAAC;;IAED;AACJ;AACA;IACIxC,OAAO,CAACjK,SAAS,CAAC6K,KAAK,GAAG,YAAY;MACpC,IAAI,CAACT,GAAG,CAACiC,SAAS,GAAG,EAAE;IACzB,CAAC;IAED,OAAOpC,OAAO;EAChB,CAAC,CAAE,CAAC,GAAI,YAAY;IAAE;IACpB,SAAS8C,YAAYA,CAAA,EAAG;MACtB,IAAI,CAACC,QAAQ,CAACC,GAAG,GAAG,IAAI,CAACC,SAAS,CAACC,SAAS,CAAC,WAAW,CAAC;MACzD,IAAI,CAACH,QAAQ,CAACH,KAAK,CAACO,OAAO,GAAG,OAAO;MACrC,IAAI,CAACF,SAAS,CAACL,KAAK,CAACO,OAAO,GAAG,MAAM;IACvC;;IAEA;IACA;IACA,IAAI,IAAI,CAACC,QAAQ,IAAI,IAAI,CAACA,QAAQ,IAAI,GAAG,EAAE;MACzC,IAAIC,MAAM,GAAG,CAAC,GAAGtO,MAAM,CAACuO,gBAAgB;MACxC,IAAIC,SAAS,GAAGlE,wBAAwB,CAACtJ,SAAS,CAACwN,SAAS;MAC5DlE,wBAAwB,CAACtJ,SAAS,CAACwN,SAAS,GAAG,UAAUC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAE;QAC9F,IAAK,UAAU,IAAIR,KAAK,IAAK,MAAM,CAAChM,IAAI,CAACgM,KAAK,CAACS,QAAQ,CAAC,EAAE;UACxD,KAAK,IAAI1O,CAAC,GAAG2O,SAAS,CAACzO,MAAM,GAAG,CAAC,EAAEF,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;YAC9C2O,SAAS,CAAC3O,CAAC,CAAC,GAAG2O,SAAS,CAAC3O,CAAC,CAAC,GAAG8N,MAAM;UACtC;QACF,CAAC,MAAM,IAAI,OAAOU,EAAE,IAAI,WAAW,EAAE;UACnCG,SAAS,CAAC,CAAC,CAAC,IAAIb,MAAM;UACtBa,SAAS,CAAC,CAAC,CAAC,IAAIb,MAAM;UACtBa,SAAS,CAAC,CAAC,CAAC,IAAIb,MAAM;UACtBa,SAAS,CAAC,CAAC,CAAC,IAAIb,MAAM;QACxB;QAEAE,SAAS,CAACtN,KAAK,CAAC,IAAI,EAAEiO,SAAS,CAAC;MAClC,CAAC;IACH;;IAEA;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,SAASC,eAAeA,CAACC,QAAQ,EAAEC,KAAK,EAAE;MACxC,IAAIC,IAAI,GAAG,IAAI;MACfA,IAAI,CAACC,MAAM,GAAGF,KAAK;MACnBC,IAAI,CAACE,SAAS,GAAGJ,QAAQ;;MAEzB;MACA,IAAIE,IAAI,CAACG,gBAAgB,KAAK,IAAI,EAAE;QAClC,IAAIxE,EAAE,GAAGe,QAAQ,CAAC0D,aAAa,CAAC,KAAK,CAAC;QACtC,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAe;UACzBL,IAAI,CAACG,gBAAgB,GAAG,KAAK;UAE7B,IAAIH,IAAI,CAACC,MAAM,EAAE;YACfD,IAAI,CAACC,MAAM,CAACK,IAAI,CAACN,IAAI,CAAC;UACxB;QACF,CAAC;QACD,IAAIO,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAe;UAC3BP,IAAI,CAACG,gBAAgB,GAAG,IAAI;UAE5B,IAAIH,IAAI,CAACE,SAAS,EAAE;YAClBF,IAAI,CAACE,SAAS,CAACI,IAAI,CAACN,IAAI,CAAC;UAC3B;QACF,CAAC;QAEDrE,EAAE,CAAC6E,OAAO,GAAGH,QAAQ;QACrB1E,EAAE,CAAC8E,OAAO,GAAGJ,QAAQ;QACrB1E,EAAE,CAAC+E,MAAM,GAAGH,UAAU;QACtB5E,EAAE,CAAC+C,GAAG,GAAG,4IAA4I,CAAC,CAAC;QACvJ;MACF,CAAC,MAAM,IAAIsB,IAAI,CAACG,gBAAgB,KAAK,IAAI,IAAIH,IAAI,CAACE,SAAS,EAAE;QAC3DF,IAAI,CAACE,SAAS,CAACI,IAAI,CAACN,IAAI,CAAC;MAC3B,CAAC,MAAM,IAAIA,IAAI,CAACG,gBAAgB,KAAK,KAAK,IAAIH,IAAI,CAACC,MAAM,EAAE;QACzDD,IAAI,CAACC,MAAM,CAACK,IAAI,CAACN,IAAI,CAAC;MACxB;IACF;IAAC;;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;IACI,IAAItE,OAAO,GAAG,SAAVA,OAAOA,CAAaC,EAAE,EAAEC,QAAQ,EAAE;MACpC,IAAI,CAAC+E,WAAW,GAAG,KAAK;MACxB,IAAI,CAAC7B,QAAQ,GAAG9D,WAAW,CAAC,CAAC;MAE7B,IAAI,CAACc,SAAS,GAAGF,QAAQ;MACzB,IAAI,CAAC+C,SAAS,GAAGjC,QAAQ,CAAC0D,aAAa,CAAC,QAAQ,CAAC;MACjD,IAAI,CAACzB,SAAS,CAACxC,KAAK,GAAGP,QAAQ,CAACO,KAAK;MACrC,IAAI,CAACwC,SAAS,CAACtC,MAAM,GAAGT,QAAQ,CAACS,MAAM;MACvCV,EAAE,CAACwB,WAAW,CAAC,IAAI,CAACwB,SAAS,CAAC;MAC9B,IAAI,CAAC9C,GAAG,GAAGF,EAAE;MACb,IAAI,CAACiF,SAAS,GAAG,IAAI,CAACjC,SAAS,CAACkC,UAAU,CAAC,IAAI,CAAC;MAChD,IAAI,CAACF,WAAW,GAAG,KAAK;MACxB,IAAI,CAAClC,QAAQ,GAAG/B,QAAQ,CAAC0D,aAAa,CAAC,KAAK,CAAC;MAC7C,IAAI,CAAC3B,QAAQ,CAACqC,GAAG,GAAG,UAAU;MAC9B,IAAI,CAACrC,QAAQ,CAACH,KAAK,CAACO,OAAO,GAAG,MAAM;MACpC,IAAI,CAAChD,GAAG,CAACsB,WAAW,CAAC,IAAI,CAACsB,QAAQ,CAAC;MACnC,IAAI,CAAC0B,gBAAgB,GAAG,IAAI;IAC9B,CAAC;;IAED;AACJ;AACA;AACA;AACA;IACIzE,OAAO,CAACjK,SAAS,CAACsK,IAAI,GAAG,UAAUC,OAAO,EAAE;MAC1C,IAAIyC,QAAQ,GAAG,IAAI,CAACA,QAAQ;MAC5B,IAAImC,SAAS,GAAG,IAAI,CAACA,SAAS;MAC9B,IAAI9E,SAAS,GAAG,IAAI,CAACA,SAAS;MAE9B,IAAIG,MAAM,GAAGD,OAAO,CAAClJ,cAAc,CAAC,CAAC;MACrC,IAAIoJ,MAAM,GAAGJ,SAAS,CAACK,KAAK,GAAGF,MAAM;MACrC,IAAIG,OAAO,GAAGN,SAAS,CAACO,MAAM,GAAGJ,MAAM;MACvC,IAAI8E,aAAa,GAAGzL,IAAI,CAAC0L,KAAK,CAAC9E,MAAM,CAAC;MACtC,IAAI+E,cAAc,GAAG3L,IAAI,CAAC0L,KAAK,CAAC5E,OAAO,CAAC;MAExCqC,QAAQ,CAACH,KAAK,CAACO,OAAO,GAAG,MAAM;MAC/B,IAAI,CAACvC,KAAK,CAAC,CAAC;MAEZ,KAAK,IAAI3J,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGsJ,MAAM,EAAEtJ,GAAG,EAAE,EAAE;QACrC,KAAK,IAAIC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGqJ,MAAM,EAAErJ,GAAG,EAAE,EAAE;UACrC,IAAIsO,OAAO,GAAGlF,OAAO,CAACtJ,MAAM,CAACC,GAAG,EAAEC,GAAG,CAAC;UACtC,IAAIuO,KAAK,GAAGvO,GAAG,GAAGsJ,MAAM;UACxB,IAAIkF,IAAI,GAAGzO,GAAG,GAAGyJ,OAAO;UACxBwE,SAAS,CAACS,WAAW,GAAGH,OAAO,GAAGpF,SAAS,CAACsB,SAAS,GAAGtB,SAAS,CAACmB,UAAU;UAC5E2D,SAAS,CAACU,SAAS,GAAG,CAAC;UACvBV,SAAS,CAACW,SAAS,GAAGL,OAAO,GAAGpF,SAAS,CAACsB,SAAS,GAAGtB,SAAS,CAACmB,UAAU;UAC1E2D,SAAS,CAACY,QAAQ,CAACL,KAAK,EAAEC,IAAI,EAAElF,MAAM,EAAEE,OAAO,CAAC;;UAEhD;UACAwE,SAAS,CAACa,UAAU,CAClBnM,IAAI,CAACC,KAAK,CAAC4L,KAAK,CAAC,GAAG,GAAG,EACvB7L,IAAI,CAACC,KAAK,CAAC6L,IAAI,CAAC,GAAG,GAAG,EACtBL,aAAa,EACbE,cACF,CAAC;UAEDL,SAAS,CAACa,UAAU,CAClBnM,IAAI,CAACoM,IAAI,CAACP,KAAK,CAAC,GAAG,GAAG,EACtB7L,IAAI,CAACoM,IAAI,CAACN,IAAI,CAAC,GAAG,GAAG,EACrBL,aAAa,EACbE,cACF,CAAC;QACH;MACF;MAEA,IAAI,CAACN,WAAW,GAAG,IAAI;IACzB,CAAC;;IAED;AACJ;AACA;IACIjF,OAAO,CAACjK,SAAS,CAACkQ,SAAS,GAAG,YAAY;MACxC,IAAI,IAAI,CAAChB,WAAW,EAAE;QACpBd,eAAe,CAACS,IAAI,CAAC,IAAI,EAAE9B,YAAY,CAAC;MAC1C;IACF,CAAC;;IAED;AACJ;AACA;AACA;AACA;IACI9C,OAAO,CAACjK,SAAS,CAACmQ,SAAS,GAAG,YAAY;MACxC,OAAO,IAAI,CAACjB,WAAW;IACzB,CAAC;;IAED;AACJ;AACA;IACIjF,OAAO,CAACjK,SAAS,CAAC6K,KAAK,GAAG,YAAY;MACpC,IAAI,CAACsE,SAAS,CAACiB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAClD,SAAS,CAACxC,KAAK,EAAE,IAAI,CAACwC,SAAS,CAACtC,MAAM,CAAC;MAC3E,IAAI,CAACsE,WAAW,GAAG,KAAK;IAC1B,CAAC;;IAED;AACJ;AACA;AACA;IACIjF,OAAO,CAACjK,SAAS,CAACuP,KAAK,GAAG,UAAUc,OAAO,EAAE;MAC3C,IAAI,CAACA,OAAO,EAAE;QACZ,OAAOA,OAAO;MAChB;MAEA,OAAOxM,IAAI,CAACC,KAAK,CAACuM,OAAO,GAAG,IAAI,CAAC,GAAG,IAAI;IAC1C,CAAC;IAED,OAAOpG,OAAO;EAChB,CAAC,CAAE,CAAC;;EAEJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASqG,cAAcA,CAACC,KAAK,EAAEC,aAAa,EAAE;IAC5C,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI/Q,MAAM,GAAGgR,cAAc,CAACH,KAAK,CAAC;IAElC,KAAK,IAAI/Q,CAAC,GAAG,CAAC,EAAEmR,GAAG,GAAGvH,iBAAiB,CAAC1J,MAAM,EAAEF,CAAC,IAAImR,GAAG,EAAEnR,CAAC,EAAE,EAAE;MAC7D,IAAIoR,MAAM,GAAG,CAAC;MAEd,QAAQJ,aAAa;QACnB,KAAKnK,mBAAmB,CAACC,CAAC;UACxBsK,MAAM,GAAGxH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF,KAAK6G,mBAAmB,CAACE,CAAC;UACxBqK,MAAM,GAAGxH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF,KAAK6G,mBAAmB,CAACG,CAAC;UACxBoK,MAAM,GAAGxH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;QACF,KAAK6G,mBAAmB,CAACI,CAAC;UACxBmK,MAAM,GAAGxH,iBAAiB,CAAC5J,CAAC,CAAC,CAAC,CAAC,CAAC;UAChC;MACJ;MAEA,IAAIE,MAAM,IAAIkR,MAAM,EAAE;QACpB;MACF,CAAC,MAAM;QACLH,KAAK,EAAE;MACT;IACF;IAEA,IAAIA,KAAK,GAAGrH,iBAAiB,CAAC1J,MAAM,EAAE;MACpC,MAAM,IAAI0B,KAAK,CAAC,eAAe,CAAC;IAClC;IAEA,OAAOqP,KAAK;EACd;EAEA,SAASC,cAAcA,CAACH,KAAK,EAAE;IAC7B,IAAIM,YAAY,GAAGC,SAAS,CAACP,KAAK,CAAC,CAAC1G,QAAQ,CAAC,CAAC,CAACkH,OAAO,CAAC,mBAAmB,EAAE,GAAG,CAAC;IAChF,OAAOF,YAAY,CAACnR,MAAM,IAAImR,YAAY,CAACnR,MAAM,IAAI6Q,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;EACrE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtR,MAAM,GAAG,SAAAA,OAAUiL,EAAE,EAAE8G,OAAO,EAAE;IAC9B,IAAI,CAAC3G,SAAS,GAAG;MACfK,KAAK,EAAE,GAAG;MACVE,MAAM,EAAE,GAAG;MACXnK,UAAU,EAAE,CAAC;MACbkL,SAAS,EAAE,SAAS;MACpBH,UAAU,EAAE,SAAS;MACrByF,YAAY,EAAE5K,mBAAmB,CAACI;IACpC,CAAC;IAED,IAAI,OAAOuK,OAAO,KAAK,QAAQ,EAAE;MAC/BA,OAAO,GAAG;QACRE,IAAI,EAAEF;MACR,CAAC;IACH;;IAEA;IACA,IAAIA,OAAO,EAAE;MACX,KAAK,IAAIxR,CAAC,IAAIwR,OAAO,EAAE;QACrB,IAAI,CAAC3G,SAAS,CAAC7K,CAAC,CAAC,GAAGwR,OAAO,CAACxR,CAAC,CAAC;MAChC;IACF;IAEA,IAAI,OAAO0K,EAAE,IAAI,QAAQ,EAAE;MACzBA,EAAE,GAAGe,QAAQ,CAACkG,cAAc,CAACjH,EAAE,CAAC;IAClC;IAEA,IAAI,IAAI,CAACG,SAAS,CAAC2B,MAAM,EAAE;MACzB/B,OAAO,GAAGD,SAAS;IACrB;IAEA,IAAI,CAACqD,QAAQ,GAAG9D,WAAW,CAAC,CAAC;IAC7B,IAAI,CAACa,GAAG,GAAGF,EAAE;IACb,IAAI,CAACkH,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,SAAS,GAAG,IAAIpH,OAAO,CAAC,IAAI,CAACG,GAAG,EAAE,IAAI,CAACC,SAAS,CAAC;IAEtD,IAAI,IAAI,CAACA,SAAS,CAAC6G,IAAI,EAAE;MACvB,IAAI,CAACI,QAAQ,CAAC,IAAI,CAACjH,SAAS,CAAC6G,IAAI,CAAC;IACpC;EACF,CAAC;;EAED;AACF;AACA;AACA;AACA;EACEjS,MAAM,CAACe,SAAS,CAACsR,QAAQ,GAAG,UAAUf,KAAK,EAAE;IAC3C,IAAI,CAACa,QAAQ,GAAG,IAAI5Q,WAAW,CAAC8P,cAAc,CAACC,KAAK,EAAE,IAAI,CAAClG,SAAS,CAAC4G,YAAY,CAAC,EAAE,IAAI,CAAC5G,SAAS,CAAC4G,YAAY,CAAC;IAChH,IAAI,CAACG,QAAQ,CAACrQ,OAAO,CAACwP,KAAK,CAAC;IAC5B,IAAI,CAACa,QAAQ,CAAC9P,IAAI,CAAC,CAAC;IACpB,IAAI,CAAC8I,GAAG,CAACmH,KAAK,GAAGhB,KAAK;IACtB,IAAI,CAACc,SAAS,CAAC/G,IAAI,CAAC,IAAI,CAAC8G,QAAQ,CAAC;IAClC,IAAI,CAAClB,SAAS,CAAC,CAAC;EAClB,CAAC;;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEjR,MAAM,CAACe,SAAS,CAACkQ,SAAS,GAAG,YAAY;IACvC,IAAI,OAAO,IAAI,CAACmB,SAAS,CAACnB,SAAS,IAAI,UAAU,KAAK,CAAC,IAAI,CAAC7C,QAAQ,IAAI,IAAI,CAACA,QAAQ,IAAI,CAAC,CAAC,EAAE;MAC3F,IAAI,CAACgE,SAAS,CAACnB,SAAS,CAAC,CAAC;IAC5B;EACF,CAAC;;EAED;AACF;AACA;EACEjR,MAAM,CAACe,SAAS,CAAC6K,KAAK,GAAG,YAAY;IACnC,IAAI,CAACwG,SAAS,CAACxG,KAAK,CAAC,CAAC;EACxB,CAAC;;EAED;AACF;AACA;EACE5L,MAAM,CAACuS,YAAY,GAAGnL,mBAAmB;AAC3C,CAAC,EAAE,CAAC"}]}