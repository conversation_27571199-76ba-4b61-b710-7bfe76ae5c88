{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\difficultylevel.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\difficultylevel.js", "mtime": 1718877098273}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDifficultylevel", "query", "request", "url", "method", "params", "getDifficultylevel", "difficultyLevelId", "addDifficultylevel", "data", "updateDifficultylevel", "delDifficultylevel", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/difficultylevel.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询订单难度列表\r\nexport function listDifficultylevel(query) {\r\n  return request({\r\n    url: '/system/difficultylevel/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询订单难度详细\r\nexport function getDifficultylevel(difficultyLevelId) {\r\n  return request({\r\n    url: '/system/difficultylevel/' + difficultyLevelId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增订单难度\r\nexport function addDifficultylevel(data) {\r\n  return request({\r\n    url: '/system/difficultylevel',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改订单难度\r\nexport function updateDifficultylevel(data) {\r\n  return request({\r\n    url: '/system/difficultylevel',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除订单难度\r\nexport function delDifficultylevel(difficultyLevelId) {\r\n  return request({\r\n    url: '/system/difficultylevel/' + difficultyLevelId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(difficultyLevelId, status) {\r\n  const data = {\r\n    difficultyLevelId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/difficultylevel/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACC,iBAAiB,EAAE;EACpD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,iBAAiB;IACnDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,qBAAqBA,CAACD,IAAI,EAAE;EAC1C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,kBAAkBA,CAACJ,iBAAiB,EAAE;EACpD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,iBAAiB;IACnDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,iBAAiB,EAAEM,MAAM,EAAE;EACtD,IAAMJ,IAAI,GAAG;IACXF,iBAAiB,EAAjBA,iBAAiB;IACjBM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}