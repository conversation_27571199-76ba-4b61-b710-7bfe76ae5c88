{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\SizeSelect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\SizeSelect\\index.vue", "mtime": 1754876882534}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNpemVPcHRpb25zOiBbDQogICAgICAgIHtsYWJlbDogJ0RlZmF1bHQnLCB2YWx1ZTogJ2RlZmF1bHQnfSwNCiAgICAgICAge2xhYmVsOiAnTWVkaXVtJywgdmFsdWU6ICdtZWRpdW0nfSwNCiAgICAgICAge2xhYmVsOiAnU21hbGwnLCB2YWx1ZTogJ3NtYWxsJ30sDQogICAgICAgIHtsYWJlbDogJ01pbmknLCB2YWx1ZTogJ21pbmknfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBzaXplKCkgew0KICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMuc2l6ZQ0KICAgIH0NCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZVNldFNpemUoc2l6ZSkgew0KICAgICAgdGhpcy4kRUxFTUVOVC5zaXplID0gc2l6ZQ0KICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ2FwcC9zZXRTaXplJywgc2l6ZSkNCiAgICAgIHRoaXMucmVmcmVzaFZpZXcoKQ0KICAgICAgdGhpcy4kbWVzc2FnZSh7DQogICAgICAgIG1lc3NhZ2U6ICdTd2l0Y2ggU2l6ZSBTdWNjZXNzJywNCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnDQogICAgICB9KQ0KICAgIH0sDQogICAgcmVmcmVzaFZpZXcoKSB7DQogICAgICAvLyBJbiBvcmRlciB0byBtYWtlIHRoZSBjYWNoZWQgcGFnZSByZS1yZW5kZXJlZA0KICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2RlbEFsbENhY2hlZFZpZXdzJywgdGhpcy4kcm91dGUpDQoNCiAgICAgIGNvbnN0IHtmdWxsUGF0aH0gPSB0aGlzLiRyb3V0ZQ0KDQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJvdXRlci5yZXBsYWNlKHsNCiAgICAgICAgICBwYXRoOiAnL3JlZGlyZWN0JyArIGZ1bGxQYXRoDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgIH0NCiAgfQ0KDQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAeA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "index.vue", "sourceRoot": "src/components/SizeSelect", "sourcesContent": ["<template>\r\n  <el-dropdown trigger=\"click\" @command=\"handleSetSize\">\r\n    <div>\r\n      <svg-icon class-name=\"size-icon\" icon-class=\"size\"/>\r\n    </div>\r\n    <el-dropdown-menu slot=\"dropdown\">\r\n      <el-dropdown-item v-for=\"item of sizeOptions\" :key=\"item.value\" :command=\"item.value\"\r\n                        :disabled=\"size==item.value\">\r\n        {{ item.label }}\r\n      </el-dropdown-item>\r\n    </el-dropdown-menu>\r\n  </el-dropdown>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      sizeOptions: [\r\n        {label: 'Default', value: 'default'},\r\n        {label: 'Medium', value: 'medium'},\r\n        {label: 'Small', value: 'small'},\r\n        {label: 'Mini', value: 'mini'}\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    size() {\r\n      return this.$store.getters.size\r\n    }\r\n  },\r\n  methods: {\r\n    handleSetSize(size) {\r\n      this.$ELEMENT.size = size\r\n      this.$store.dispatch('app/setSize', size)\r\n      this.refreshView()\r\n      this.$message({\r\n        message: 'Switch Size Success',\r\n        type: 'success'\r\n      })\r\n    },\r\n    refreshView() {\r\n      // In order to make the cached page re-rendered\r\n      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)\r\n\r\n      const {fullPath} = this.$route\r\n\r\n      this.$nextTick(() => {\r\n        this.$router.replace({\r\n          path: '/redirect' + fullPath\r\n        })\r\n      })\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n"]}]}