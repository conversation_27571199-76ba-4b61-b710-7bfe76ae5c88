{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\mixin\\ResizeHandler.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\mixin\\ResizeHandler.js", "mtime": 1754876882546}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "_document", "document", "body", "WIDTH", "_default", "watch", "$route", "route", "device", "sidebar", "opened", "store", "dispatch", "withoutAnimation", "beforeMount", "window", "addEventListener", "$_resizeHandler", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "mounted", "isMobile", "$_isMobile", "methods", "rect", "getBoundingClientRect", "width", "hidden", "exports", "default"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/layout/mixin/ResizeHandler.js"], "sourcesContent": ["import store from '@/store'\r\n\r\nconst {body} = document\r\nconst WIDTH = 992 // refer to Bootstrap's responsive design\r\n\r\nexport default {\r\n  watch: {\r\n    $route(route) {\r\n      if (this.device == 'mobile' && this.sidebar.opened) {\r\n        store.dispatch('app/closeSideBar', {withoutAnimation: false})\r\n      }\r\n    }\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.$_resizeHandler)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.$_resizeHandler)\r\n  },\r\n  mounted() {\r\n    const isMobile = this.$_isMobile()\r\n    if (isMobile) {\r\n      store.dispatch('app/toggleDevice', 'mobile')\r\n      store.dispatch('app/closeSideBar', {withoutAnimation: true})\r\n    }\r\n  },\r\n  methods: {\r\n    // use $_ for mixins properties\r\n    // https://vuejs.org/v2/style-guide/index.html#Private-property-names-essential\r\n    $_isMobile() {\r\n      const rect = body.getBoundingClientRect()\r\n      return rect.width - 1 < WIDTH\r\n    },\r\n    $_resizeHandler() {\r\n      if (!document.hidden) {\r\n        const isMobile = this.$_isMobile()\r\n        store.dispatch('app/toggleDevice', isMobile ? 'mobile' : 'desktop')\r\n\r\n        if (isMobile) {\r\n          store.dispatch('app/closeSideBar', {withoutAnimation: true})\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,SAAA,GAAeC,QAAQ;EAAhBC,IAAI,GAAAF,SAAA,CAAJE,IAAI;AACX,IAAMC,KAAK,GAAG,GAAG,EAAC;AAAA,IAAAC,QAAA,GAEH;EACbC,KAAK,EAAE;IACLC,MAAM,WAAAA,OAACC,KAAK,EAAE;MACZ,IAAI,IAAI,CAACC,MAAM,IAAI,QAAQ,IAAI,IAAI,CAACC,OAAO,CAACC,MAAM,EAAE;QAClDC,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;UAACC,gBAAgB,EAAE;QAAK,CAAC,CAAC;MAC/D;IACF;EACF,CAAC;EACDC,WAAW,WAAAA,YAAA,EAAG;IACZC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,eAAe,CAAC;EACzD,CAAC;EACDC,aAAa,WAAAA,cAAA,EAAG;IACdH,MAAM,CAACI,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAACF,eAAe,CAAC;EAC5D,CAAC;EACDG,OAAO,WAAAA,QAAA,EAAG;IACR,IAAMC,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;IAClC,IAAID,QAAQ,EAAE;MACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE,QAAQ,CAAC;MAC5CD,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;QAACC,gBAAgB,EAAE;MAAI,CAAC,CAAC;IAC9D;EACF,CAAC;EACDU,OAAO,EAAE;IACP;IACA;IACAD,UAAU,WAAAA,WAAA,EAAG;MACX,IAAME,IAAI,GAAGtB,IAAI,CAACuB,qBAAqB,CAAC,CAAC;MACzC,OAAOD,IAAI,CAACE,KAAK,GAAG,CAAC,GAAGvB,KAAK;IAC/B,CAAC;IACDc,eAAe,WAAAA,gBAAA,EAAG;MAChB,IAAI,CAAChB,QAAQ,CAAC0B,MAAM,EAAE;QACpB,IAAMN,QAAQ,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;QAClCX,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAES,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC;QAEnE,IAAIA,QAAQ,EAAE;UACZV,cAAK,CAACC,QAAQ,CAAC,kBAAkB,EAAE;YAACC,gBAAgB,EAAE;UAAI,CAAC,CAAC;QAC9D;MACF;IACF;EACF;AACF,CAAC;AAAAe,OAAA,CAAAC,OAAA,GAAAzB,QAAA"}]}