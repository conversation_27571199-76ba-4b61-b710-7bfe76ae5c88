{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\staffrole\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\staffrole\\index.vue", "mtime": 1754876882598}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_staff<PERSON>e", "require", "_vueTreeselect", "_interopRequireDefault", "_user", "name", "dicts", "components", "Treeselect", "data", "showLeft", "showRight", "total", "userList", "defaultProps", "children", "label", "loading", "ids", "single", "multiple", "showSearch", "staffroleList", "title", "open", "queryParams", "pageNum", "pageSize", "roleId", "deptId", "is<PERSON><PERSON>", "staffId", "form", "rules", "required", "trigger", "message", "watch", "n", "created", "_this", "getList", "selectListUser", "then", "response", "methods", "_this2", "listStaffrole", "rows", "loadMenu", "val", "menuList", "query", "listStaffroleMenu", "cancel", "reset", "staffRoleDeptId", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this3", "getStaffrole", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "done", "a", "value", "undefined", "_iterator2", "_step2", "b", "_iterator3", "_step3", "c", "_iterator4", "_step4", "d", "_iterator5", "_step5", "e", "err", "f", "submitForm", "_this4", "$refs", "validate", "valid", "updateStaffrole", "$modal", "msgSuccess", "addStaffrole", "handleDelete", "_this5", "staffRoleDeptIds", "$confirm", "customClass", "delStaffrole", "catch", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "getDeptId", "getRoleId", "queryDeptId", "queryRoleId", "exports", "_default"], "sources": ["src/views/system/staffrole/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" label-width=\"35px\"\r\n                 size=\"mini\">\r\n          <el-form-item label=\"员工\" prop=\"staffId\">\r\n            <el-select v-model=\"queryParams.staffId\" filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\">\r\n              <el-option\r\n                v-for=\"staff in userList\"\r\n                :key=\"staff.staffId\"\r\n                :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                :value=\"staff.staffId\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"角色\" prop=\"roleId\">\r\n            <tree-select :pass=\"queryParams.roleId\" :placeholder=\"'选择权限'\" :type=\"'role'\"\r\n                         style=\"width: 100%\" @return=\"queryRoleId\" :dbn=\"true\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <tree-select :multiple=\"false\" :pass=\"queryParams.deptId\"\r\n                         :placeholder=\"'选择部门'\" :type=\"'dept'\" style=\"width: 100%\" @return=\"queryDeptId\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"主职\" prop=\"isMain\">\r\n            <el-select v-model=\"queryParams.isMain\" placeholder=\"是否主职\" style=\"width: 100%\" @change=\"handleQuery\">\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_yes_no\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:role:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:role:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:role:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"staffroleList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"left\" label=\"员工\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.staff.staffCode }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{\r\n                  scope.row.staff.staffFamilyLocalName + scope.row.staff.staffGivingLocalName\r\n                }}</a>\r\n              {{ scope.row.staff.staffGivingEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"角色名称\">\r\n            <template slot-scope=\"scope\">\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.role.roleLocalName }}</a>\r\n              {{ scope.row.role.roleEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column key=\"role\" align=\"center\" label=\"权限概览\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"right\"\r\n                trigger=\"hover\"\r\n                width=\"400\">\r\n                <el-tree\r\n                  :ref=\"scope.row.roleId\"\r\n                  :check-strictly=\"!scope.row.role.menuCheckStrictly\"\r\n                  :data=\"scope.row.menuList\"\r\n                  :props=\"defaultProps\"\r\n                  class=\"tree-border\"\r\n                  empty-text=\"不存在权限\"\r\n                  node-key=\"id\"\r\n                ></el-tree>\r\n                <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\" @mouseenter.native=\"loadMenu(scope.row)\">查看\r\n                </el-button>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属部门\" prop=\"dept.deptLocalName\" width=\"100\"/>\r\n          <el-table-column align=\"center\" label=\"是否主职\" prop=\"isMain\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.isMain\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:role:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:role:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改权限分配对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-form-item label=\"员工\" prop=\"staffId\">\r\n          <el-select v-model=\"form.staffId\" filterable placeholder=\"选择员工\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"staff in userList\"\r\n              :key=\"staff.staffId\"\r\n              :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n              :value=\"staff.staffId\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"角色名称\" prop=\"roleId\">\r\n          <tree-select :pass=\"form.roleId\" :type=\"'role'\" :dbn=\"true\" @return=\"getRoleId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属部门\" prop=\"deptId\">\r\n          <tree-select :multiple=\"false\" :pass=\"form.deptId\" :type=\"'dept'\" @return=\"getDeptId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否主职\" prop=\"isMain\">\r\n          <el-select v-model=\"form.isMain\" placeholder=\"是否主职\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dict.type.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addStaffrole,\r\n  delStaffrole,\r\n  getStaffrole,\r\n  listStaffrole,\r\n  listStaffroleMenu,\r\n  updateStaffrole\r\n} from \"@/api/system/staffrole\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\";\r\nimport {selectListUser} from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Staffrole\",\r\n  dicts: ['sys_yes_no'],\r\n  components: {Treeselect},\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      total: 0,\r\n      userList: [],\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 权限分配表格数据\r\n      staffroleList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        roleId: null,\r\n        deptId: null,\r\n        isMain: null,\r\n        staffId: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        staffId: [\r\n          {\r\n            required: true,\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        roleId: [\r\n          {\r\n            required: true,\r\n            message: \"权限不能为空\", trigger: \"blur\"\r\n          }\r\n        ],\r\n        isMain: [\r\n          {\r\n            required: true,\r\n            message: \"是否主要权限不能为空\", trigger: \"blur\"\r\n          }\r\n        ],\r\n        deptId: [\r\n          {\r\n            required: true,\r\n            message: \"部门ID不能为空\", trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    selectListUser().then(response => {\r\n      this.userList = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询权限分配列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listStaffrole(this.queryParams).then(response => {\r\n        this.staffroleList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    loadMenu(val) {\r\n      if (val.menuList == null) {\r\n        let query = {\r\n          staffId: val.staffId\r\n        }\r\n        listStaffroleMenu(query).then(response => {\r\n          val.menuList = response.data\r\n        })\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        staffRoleDeptId: null,\r\n        staffId: null,\r\n        roleId: null,\r\n        isMain: \"N\",\r\n        deptId: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.staffRoleDeptId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加权限分配\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const staffRoleDeptId = row.staffRoleDeptId || this.ids\r\n      getStaffrole(staffRoleDeptId).then(response => {\r\n        for (const a of this.userList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.children != undefined) {\r\n                    for (const d of c.children) {\r\n                      if (d.staffId == response.data.staffId) {\r\n                        this.staffId = d.deptId\r\n                      }\r\n                      if (d.children != undefined) {\r\n                        for (const e of d.children) {\r\n                          if (e.staffId == response.data.staffId) {\r\n                            this.staffId = e.deptId\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改权限分配\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.staffRoleDeptId != null) {\r\n            updateStaffrole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addStaffrole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const staffRoleDeptIds = row.staffRoleDeptId || this.ids;\r\n      this.$confirm('是否确认删除权限分配编号为\"' + staffRoleDeptIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delStaffrole\r\n        (staffRoleDeptIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/staffrole/export', {\r\n        ...this.queryParams\r\n      }, `staffrole_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getDeptId(val) {\r\n      this.form.deptId = val\r\n    },\r\n    getRoleId(val) {\r\n      this.form.roleId = val\r\n    },\r\n    queryDeptId(val) {\r\n      this.queryParams.deptId = val\r\n    },\r\n    queryRoleId(val) {\r\n      this.queryParams.roleId = val\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAiMA,IAAAA,UAAA,GAAAC,OAAA;AAQA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAI,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,KAAA;MACAC,QAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,aAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAF,OAAA,GACA;UACAG,QAAA;UACAC,OAAA;QACA,EACA;QACAP,MAAA,GACA;UACAM,QAAA;UACAE,OAAA;UAAAD,OAAA;QACA,EACA;QACAL,MAAA,GACA;UACAI,QAAA;UACAE,OAAA;UAAAD,OAAA;QACA,EACA;QACAN,MAAA,GACA;UACAK,QAAA;UACAE,OAAA;UAAAD,OAAA;QACA;MAEA;IACA;EACA;EACAE,KAAA;IACAhB,UAAA,WAAAA,WAAAiB,CAAA;MACA,IAAAA,CAAA;QACA,KAAA3B,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACA6B,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,IAAAC,oBAAA,IAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAA3B,QAAA,GAAA+B,QAAA,CAAAnC,IAAA;IACA;EACA;EACAoC,OAAA;IACA,eACAJ,OAAA,WAAAA,QAAA;MAAA,IAAAK,MAAA;MACA,KAAA7B,OAAA;MACA,IAAA8B,wBAAA,OAAAtB,WAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAE,MAAA,CAAAxB,aAAA,GAAAsB,QAAA,CAAAI,IAAA;QACAF,MAAA,CAAAlC,KAAA,GAAAgC,QAAA,CAAAhC,KAAA;QACAkC,MAAA,CAAA7B,OAAA;MACA;IACA;IACAgC,QAAA,WAAAA,SAAAC,GAAA;MACA,IAAAA,GAAA,CAAAC,QAAA;QACA,IAAAC,KAAA;UACArB,OAAA,EAAAmB,GAAA,CAAAnB;QACA;QACA,IAAAsB,4BAAA,EAAAD,KAAA,EAAAT,IAAA,WAAAC,QAAA;UACAM,GAAA,CAAAC,QAAA,GAAAP,QAAA,CAAAnC,IAAA;QACA;MACA;IACA;IACA;IACA6C,MAAA,WAAAA,OAAA;MACA,KAAA9B,IAAA;MACA,KAAA+B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAvB,IAAA;QACAwB,eAAA;QACAzB,OAAA;QACAH,MAAA;QACAE,MAAA;QACAD,MAAA;MACA;MACA,KAAA4B,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAjC,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACAkB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3C,GAAA,GAAA2C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAP,eAAA;MAAA;MACA,KAAArC,MAAA,GAAA0C,SAAA,CAAAG,MAAA;MACA,KAAA5C,QAAA,IAAAyC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAV,KAAA;MACA,KAAA/B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAb,KAAA;MACA,IAAAC,eAAA,GAAAW,GAAA,CAAAX,eAAA,SAAAtC,GAAA;MACA,IAAAmD,uBAAA,EAAAb,eAAA,EAAAb,IAAA,WAAAC,QAAA;QAAA,IAAA0B,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACAJ,MAAA,CAAAvD,QAAA;UAAA4D,KAAA;QAAA;UAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAhC,CAAA,IAAAqC,IAAA;YAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;YACA,IAAAD,CAAA,CAAA7D,QAAA,IAAA+D,SAAA;cAAA,IAAAC,UAAA,OAAAR,2BAAA,CAAAC,OAAA,EACAI,CAAA,CAAA7D,QAAA;gBAAAiE,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAAL,CAAA,MAAAM,MAAA,GAAAD,UAAA,CAAAzC,CAAA,IAAAqC,IAAA;kBAAA,IAAAM,CAAA,GAAAD,MAAA,CAAAH,KAAA;kBACA,IAAAI,CAAA,CAAAlE,QAAA,IAAA+D,SAAA;oBAAA,IAAAI,UAAA,OAAAX,2BAAA,CAAAC,OAAA,EACAS,CAAA,CAAAlE,QAAA;sBAAAoE,MAAA;oBAAA;sBAAA,KAAAD,UAAA,CAAAR,CAAA,MAAAS,MAAA,GAAAD,UAAA,CAAA5C,CAAA,IAAAqC,IAAA;wBAAA,IAAAS,CAAA,GAAAD,MAAA,CAAAN,KAAA;wBACA,IAAAO,CAAA,CAAArE,QAAA,IAAA+D,SAAA;0BAAA,IAAAO,UAAA,OAAAd,2BAAA,CAAAC,OAAA,EACAY,CAAA,CAAArE,QAAA;4BAAAuE,MAAA;0BAAA;4BAAA,KAAAD,UAAA,CAAAX,CAAA,MAAAY,MAAA,GAAAD,UAAA,CAAA/C,CAAA,IAAAqC,IAAA;8BAAA,IAAAY,CAAA,GAAAD,MAAA,CAAAT,KAAA;8BACA,IAAAU,CAAA,CAAAxD,OAAA,IAAAa,QAAA,CAAAnC,IAAA,CAAAsB,OAAA;gCACAqC,MAAA,CAAArC,OAAA,GAAAwD,CAAA,CAAA1D,MAAA;8BACA;8BACA,IAAA0D,CAAA,CAAAxE,QAAA,IAAA+D,SAAA;gCAAA,IAAAU,UAAA,OAAAjB,2BAAA,CAAAC,OAAA,EACAe,CAAA,CAAAxE,QAAA;kCAAA0E,MAAA;gCAAA;kCAAA,KAAAD,UAAA,CAAAd,CAAA,MAAAe,MAAA,GAAAD,UAAA,CAAAlD,CAAA,IAAAqC,IAAA;oCAAA,IAAAe,CAAA,GAAAD,MAAA,CAAAZ,KAAA;oCACA,IAAAa,CAAA,CAAA3D,OAAA,IAAAa,QAAA,CAAAnC,IAAA,CAAAsB,OAAA;sCACAqC,MAAA,CAAArC,OAAA,GAAA2D,CAAA,CAAA7D,MAAA;oCACA;kCACA;gCAAA,SAAA8D,GAAA;kCAAAH,UAAA,CAAAE,CAAA,CAAAC,GAAA;gCAAA;kCAAAH,UAAA,CAAAI,CAAA;gCAAA;8BACA;4BACA;0BAAA,SAAAD,GAAA;4BAAAN,UAAA,CAAAK,CAAA,CAAAC,GAAA;0BAAA;4BAAAN,UAAA,CAAAO,CAAA;0BAAA;wBACA;sBACA;oBAAA,SAAAD,GAAA;sBAAAT,UAAA,CAAAQ,CAAA,CAAAC,GAAA;oBAAA;sBAAAT,UAAA,CAAAU,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAD,GAAA;gBAAAZ,UAAA,CAAAW,CAAA,CAAAC,GAAA;cAAA;gBAAAZ,UAAA,CAAAa,CAAA;cAAA;YACA;UACA;QAAA,SAAAD,GAAA;UAAArB,SAAA,CAAAoB,CAAA,CAAAC,GAAA;QAAA;UAAArB,SAAA,CAAAsB,CAAA;QAAA;QACAxB,MAAA,CAAApC,IAAA,GAAAY,QAAA,CAAAnC,IAAA;QACA2D,MAAA,CAAA5C,IAAA;QACA4C,MAAA,CAAA7C,KAAA;MACA;IACA;IACA,WACAsE,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA9D,IAAA,CAAAwB,eAAA;YACA,IAAA0C,0BAAA,EAAAJ,MAAA,CAAA9D,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAkD,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtE,IAAA;cACAsE,MAAA,CAAArD,OAAA;YACA;UACA;YACA,IAAA4D,uBAAA,EAAAP,MAAA,CAAA9D,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAkD,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAtE,IAAA;cACAsE,MAAA,CAAArD,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA6D,YAAA,WAAAA,aAAAnC,GAAA;MAAA,IAAAoC,MAAA;MACA,IAAAC,gBAAA,GAAArC,GAAA,CAAAX,eAAA,SAAAtC,GAAA;MACA,KAAAuF,QAAA,oBAAAD,gBAAA;QAAAE,WAAA;MAAA,GAAA/D,IAAA;QACA,WAAAgE,uBAAA,EACAH,gBAAA;MACA,GAAA7D,IAAA;QACA4D,MAAA,CAAA9D,OAAA;QACA8D,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,gCAAAC,cAAA,CAAAvC,OAAA,MACA,KAAA/C,WAAA,gBAAAuF,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,SAAA,WAAAA,UAAAjE,GAAA;MACA,KAAAlB,IAAA,CAAAH,MAAA,GAAAqB,GAAA;IACA;IACAkE,SAAA,WAAAA,UAAAlE,GAAA;MACA,KAAAlB,IAAA,CAAAJ,MAAA,GAAAsB,GAAA;IACA;IACAmE,WAAA,WAAAA,YAAAnE,GAAA;MACA,KAAAzB,WAAA,CAAAI,MAAA,GAAAqB,GAAA;IACA;IACAoE,WAAA,WAAAA,YAAApE,GAAA;MACA,KAAAzB,WAAA,CAAAG,MAAA,GAAAsB,GAAA;IACA;EACA;AACA;AAAAqE,OAAA,CAAA/C,OAAA,GAAAgD,QAAA"}]}