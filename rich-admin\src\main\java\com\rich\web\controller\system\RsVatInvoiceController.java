package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasAccount;
import com.rich.common.core.domain.entity.ExtCompany;
import com.rich.common.core.domain.entity.RsVatInvoice;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasAccountService;
import com.rich.system.service.ExtCompanyService;
import com.rich.system.service.RsVatInvoiceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;

import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

/**
 * 发票登记Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/system/vatinvoice")
public class RsVatInvoiceController extends BaseController {
    @Autowired
    private RsVatInvoiceService rsVatInvoiceService;

    @Autowired
    private ExtCompanyService extCompanyService;

    @Autowired
    private BasAccountService basAccountService;

    /**
     * 查询发票登记列表
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsVatInvoice rsVatInvoice) {
        startPage();
        List<RsVatInvoice> list = rsVatInvoiceService.selectRsVatInvoiceList(rsVatInvoice);
        return getDataTable(list);
    }

    /**
     * 导出发票登记列表
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:export')")
    @Log(title = "发票登记", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsVatInvoice rsVatInvoice) {
        List<RsVatInvoice> list = rsVatInvoiceService.selectRsVatInvoiceList(rsVatInvoice);
        ExcelUtil<RsVatInvoice> util = new ExcelUtil<RsVatInvoice>(RsVatInvoice.class);
        util.exportExcel(response, list, "发票登记数据");
    }

    /**
     * 获取发票登记详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:query')")
    @GetMapping(value = "/{invoiceId}")
    public AjaxResult getInfo(@PathVariable("invoiceId") Long invoiceId) {
        RsVatInvoice data = rsVatInvoiceService.selectRsVatInvoiceByInvoiceId(invoiceId);
        AjaxResult ajaxResult = AjaxResult.success();
        ajaxResult.put(AjaxResult.DATA_TAG, data);
        if (data.getCooperatorId() != null) {
            List<ExtCompany> extCompanyList = extCompanyService.selectExtCompanyByCompanyIds(Collections.singleton(data.getCooperatorId()));
            ajaxResult.put("extCompanyList", extCompanyList);
            BasAccount basAccount = new BasAccount();
            basAccount.setBelongToCompany(data.getCooperatorId());
            List<BasAccount> list = basAccountService.selectBasAccountList(basAccount);
            ajaxResult.put("basAccountList", list);
        }
        return ajaxResult;
    }

    /**
     * 新增发票登记
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:add')")
    @Log(title = "发票登记", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsVatInvoice rsVatInvoice) {
        RsVatInvoice result = rsVatInvoiceService.insertRsVatInvoice(rsVatInvoice);
        return AjaxResult.success(result);
    }

    /**
     * 修改发票登记
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:edit')")
    @Log(title = "发票登记", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsVatInvoice rsVatInvoice) {
        return toAjax(rsVatInvoiceService.updateRsVatInvoice(rsVatInvoice));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsVatInvoice rsVatInvoice) {
        rsVatInvoice.setUpdateBy(getUserId());
        return toAjax(rsVatInvoiceService.changeStatus(rsVatInvoice));
    }

    /**
     * 删除发票登记
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:remove')")
    @Log(title = "发票登记", businessType = BusinessType.DELETE)
    @DeleteMapping("/{invoiceIds}")
    public AjaxResult remove(@PathVariable Long[] invoiceIds) {
        return toAjax(rsVatInvoiceService.deleteRsVatInvoiceByInvoiceIds(invoiceIds));
    }

    /**
     * 根据rctId查询发票数量
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:query')")
    @GetMapping("/count/{rctId}")
    public AjaxResult countByRctId(@PathVariable("rctId") Long rctId) {
        int count = rsVatInvoiceService.countRsVatInvoiceByRctId(rctId);
        return AjaxResult.success(count);
    }

    /**
     * 根据rctId和cooperatorId生成发票编码
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:query')")
    @GetMapping("/generateCode/{rctId}/{cooperatorId}")
    public AjaxResult generateInvoiceCode(@PathVariable("rctId") Long rctId,
                                          @PathVariable("cooperatorId") Long cooperatorId) {
        String code = rsVatInvoiceService.generateInvoiceCode(rctId, cooperatorId);
        return AjaxResult.success(code);
    }

    /**
     * 生成发票Excel文件
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoice:query')")
    @Log(title = "发票Excel生成", businessType = BusinessType.EXPORT)
    @PutMapping("/generateExcel")
    public ResponseEntity<Object> generateInvoiceExcel(@RequestBody List<Long> invoiceIds) throws IOException {
        if (invoiceIds == null || invoiceIds.isEmpty()) {
            return ResponseEntity.badRequest().body("发票ID列表不能为空");
        }

        // 从 resources 目录加载模板文件
        InputStream templateStream =
                RsVatInvoiceController.class.getResourceAsStream("/template/invoice.xlsx");
        if (templateStream == null) {
            throw new FileNotFoundException("未找到模板文件");
        }
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 使用正确的 Workbook 类型加载模板
        Workbook workbook = WorkbookFactory.create(templateStream);

        rsVatInvoiceService.writeInvoiceData(invoiceIds, workbook);

        // 将工作簿写入输出流
        workbook.write(outputStream);

        // 关闭工作簿
        workbook.close();

        // 返回 Excel 文件作为响应
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=Invoices_" + DateUtils.dateTimeNow() + ".xlsx")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(outputStream.toByteArray());
    }
}
