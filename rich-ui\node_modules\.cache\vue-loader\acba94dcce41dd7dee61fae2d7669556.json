{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\bankSlip.vue?vue&type=style&index=0&id=b0d50104&scoped=true&lang=css&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\bankSlip.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCjo6di1kZWVwIC52dWUtdHJlZXNlbGVjdF9fdmFsdWUtY29udGFpbmVyIHsNCiAgZGlzcGxheTogYmxvY2s7DQp9DQo="}, {"version": 3, "sources": ["bankSlip.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2fA;AACA;AACA", "file": "bankSlip.vue", "sourceRoot": "src/views/system/rct", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-button v-hasPermi=\"['system:company:list']\"\r\n               size=\"mini\"\r\n               type=\"text\"\r\n               :disabled=\"scope.row.opAccept==0\"\r\n               @click=\"openBankSlip\"\r\n    >\r\n      {{\r\n        \"[\" +\r\n        currency(type === \"pay\" ? scope.row.cnInRmb : scope.row.dnInRmb, {\r\n          separator: \",\",\r\n          symbol: \"¥\",\r\n          precision: 2\r\n        }).format()\r\n        + (type === \"pay\" ? (\" \" + (scope.row.sqdDnPaySlipStatus ? scope.row.sqdDnPaySlipStatus : \"-\")) : (\" \" + (scope.row.sqdDnReceiveSlipStatus ? scope.row.sqdDnReceiveSlipStatus : \"-\"))) + \"]\"\r\n      }}\r\n    </el-button>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n      :title=\"type==='pay'?'付款水单':'收款水单'\" :visible.sync=\"bankSlipOpen\"\r\n      append-to-body destroy-on-close\r\n      height=\"60%\" @open=\"loadCompanyOptions\"\r\n      width=\"60%\"\r\n      @close=\"showDetail=false\"\r\n    >\r\n      <el-table v-loading=\"loading\" :data=\"bankrecordList\" highlight-current-row\r\n                stripe style=\"margin-top: 20px;\"\r\n      >\r\n        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n        <el-table-column align=\"center\" label=\"银行流水\" prop=\"bankRecordNo\"/>\r\n        <el-table-column align=\"center\" label=\"收支\" prop=\"isRecievingOrPaying\" width=\"30\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.isRecievingOrPaying == 1 ? \"付\" : \"收\" }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"所属公司\" prop=\"sqdPaymentTitleCode\" width=\"50\"/>\r\n        <el-table-column align=\"center\" label=\"银行账户\" prop=\"bankAccountCode\" width=\"120\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ scope.row.bankAccountCode }}\r\n            </div>\r\n            <tree-select v-else :class=\"isLocked?'disable-form':''\"\r\n                         :disabled=\"isLocked || isBankSlipConfirmed\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"scope.row.bankAccountCode\" :placeholder=\"'银行账户'\"\r\n                         :type=\"'companyAccount'\" class=\"edit\"\r\n                         @return=\"scope.row.bankAccountCode=$event\"\r\n                         @returnData=\"scope.row.sqdPaymentTitleCode=$event.sqdBelongToCompanyCode\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"结算公司\" prop=\"sqdClearingCompanyShortname\">\r\n          <template slot-scope=\"scope\">\r\n            <el-select v-model=\"scope.row.clearingCompanyId\"\r\n                       :class=\"(scope.row.isBankRecordLocked == 1 && scope.row.slipConfirmed == 1)?'':'edit'\"\r\n                       :disabled=\"scope.row.isBankRecordLocked == 1 && scope.row.slipConfirmed == 1\"\r\n                       placeholder=\"请选择\"\r\n            >\r\n              <el-option\r\n                v-for=\"item in clients\"\r\n                :key=\"item.companyId\"\r\n                :label=\"item.companyShortName\"\r\n                :value=\"item.companyId\"\r\n              >\r\n              </el-option>\r\n            </el-select>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"银行币种\" prop=\"bankCurrencyCode\" width=\"60\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ scope.row.bankCurrencyCode }}\r\n            </div>\r\n            <tree-select v-else\r\n                         :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                         :disabled=\"isBankSlipConfirmed\"\r\n                         :pass=\"scope.row.bankCurrencyCode\" :placeholder=\"'币种'\"\r\n                         :type=\"'currency'\" class=\"edit\"\r\n                         style=\"width: 100%\" @return=\"scope.row.bankCurrencyCode=$event\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"水单金额\" prop=\"slipAmount\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ scope.row.slipAmount }}\r\n            </div>\r\n            <el-input v-else v-model=\"scope.row.slipAmount\"\r\n                      :class=\"isBankSlipConfirmed?'disable-form':''\" :disabled=\"isBankSlipConfirmed\"\r\n                      class=\"edit\" placeholder=\"水单金额\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"水单\">\r\n          <template slot-scope=\"scope\">\r\n            <div>\r\n              <div style=\"display: flex\">\r\n                <el-upload\r\n                  ref=\"bankSlipUpload\"\r\n                  :action=\"uploadUrl\"\r\n                  :auto-upload=\"false\"\r\n                  :disabled=\"!scope.row.bankRecordNo\"\r\n                  :http-request=\"customHttpRequest\"\r\n                  :on-change=\"handleChange\"\r\n                  :on-error=\"handleError\"\r\n                  v-if=\"scope.row.slipConfirmed==0\"\r\n                  :on-success=\"handleSuccess\"\r\n                  :show-file-list=\"false\"\r\n                  action=\"xxx\"\r\n                  class=\"upload-demo\" style=\"flex: 1\"\r\n                >\r\n                  <el-button icon=\"el-icon-top-right\" style=\"color: rgb(103, 194, 58)\" type=\"text\"></el-button>\r\n                </el-upload>\r\n                <img-preview v-if=\"scope.row.slipFile || (scope.row.slipFile && scope.row.slipConfirmed)\" :scope=\"scope\"\r\n                             style=\"flex: 1\"\r\n                />\r\n                <el-button v-if=\"scope.row.slipFile && scope.row.slipConfirmed==0\" icon=\"el-icon-delete\"\r\n                           style=\"flex: 1;color: red\"\r\n                           type=\"text\"\r\n                           @click=\"deleteBankSlip(scope.row)\"\r\n                ></el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"水单日期\">\r\n          <template slot-scope=\"scope\">\r\n            <div v-if=\"scope.row.slipConfirmed==1\">\r\n              {{ parseTime(scope.row.slipDate, \"{y}-{m}-{d}\") }}\r\n            </div>\r\n            <el-date-picker\r\n              v-else\r\n              v-model=\"scope.row.slipDate\"\r\n              :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n              :disabled=\"isBankSlipConfirmed\" class=\"edit\" clearable\r\n              default-time=\"12:00:00\"\r\n              placeholder=\"银行时间\"\r\n              style=\"width: 100%\"\r\n              type=\"date\"\r\n              value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"相关操作单号\" prop=\"sqdRaletiveRctList\"/>\r\n        <el-table-column label=\"水单确认\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.slipConfirmed == 1 ? \"√\" : \"-\" }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column v-if=\"type==='receive'\" align=\"center\" label=\"实收金额\" prop=\"actualBankRecievedAmount\"/>\r\n        <el-table-column v-if=\"type==='pay'\" align=\"center\" label=\"实付金额\" prop=\"actualBankPaidAmount\"/>\r\n\r\n\r\n        <el-table-column align=\"center\" label=\"银行时间\" prop=\"bankRecordTime\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.bankRecordTime, \"{y}-{m}-{d}\") }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"流水审核\" prop=\"isBankRecordLocked\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ scope.row.isBankRecordLocked == 1 ? \"√\" : \"-\" }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"操作\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button v-if=\" scope.row.slipConfirmed == 0\"\r\n                       icon=\"el-icon-top-right\" type=\"text\"\r\n                       @click=\"submitForm(scope.row)\"\r\n            ></el-button>\r\n            <el-button v-if=\"scope.row.slipConfirmed == 0\"\r\n                       icon=\"el-icon-delete\" style=\"color: red\"\r\n                       type=\"text\" @click=\"handleDelete(scope.row)\"\r\n            ></el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-button type=\"text\" @click=\"handleAdd\">[+]</el-button>\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :total=\"total\"\r\n        @pagination=\"getList\"\r\n      />\r\n\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {\r\n  addBankrecord,\r\n  delBankrecord,\r\n  delImg,\r\n  getBankrecord,\r\n  listBankrecord,\r\n  updateBankrecord\r\n} from \"@/api/system/bankrecord\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport request from \"@/utils/request\"\r\nimport ImgPreview from \"@/views/system/rct/imgPreview.vue\"\r\nimport {updateRct} from \"@/api/system/rct\"\r\nimport pinyin from \"js-pinyin\"\r\nimport {listCompanyNoPage} from \"@/api/system/company\"\r\n\r\nexport default {\r\n  name: \"bankSlip\",\r\n  components: {ImgPreview, CompanySelect, Treeselect},\r\n  props: [\"scope\", \"type\"],\r\n  data() {\r\n    return {\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20\r\n      },\r\n      form: {},\r\n      rules: {},\r\n      open: false,\r\n      showDetail: false,\r\n      bankSlipOpen: false,\r\n      bankrecordList: [],\r\n      total: 0,\r\n      loading: false,\r\n      companyList: [],\r\n      fileList: [],\r\n      imageFile: null,\r\n      uploadUrl: \"/system/bankrecord/upload\",\r\n      imgUrl: \"\",\r\n      bankSlipPreview: false,\r\n      previewImgOpen: false,\r\n      clients: []\r\n    }\r\n  },\r\n  beforeMount() {\r\n\r\n  },\r\n  computed: {\r\n    isLocked() {\r\n      return this.form.isBankRecordLocked == 1\r\n    },\r\n    isBankSlipConfirmed() {\r\n      return this.form.slipConfirmed == 1\r\n    }\r\n  },\r\n  methods: {\r\n    loadCompanyOptions() {\r\n      let companyIds = [this.scope.row.clientId]\r\n      this.scope.row.relationClientIdList.split(\",\").length > 0 ? this.scope.row.relationClientIdList.split(\",\").map(item => parseInt(item) ? companyIds.push(parseInt(item)) : null) : null\r\n      listCompanyNoPage({companyIds: companyIds}).then(response => {\r\n\r\n        this.clients = response.rows // 更新选项数据\r\n      })\r\n    },\r\n    selectCompany(row, node) {\r\n      row.clearingCompanyId = node.companyId\r\n      row.sqdClearingCompanyShortname = node.companyShortName\r\n    },\r\n    async deleteBankSlip(row) {\r\n      // 删除服务器中的图片文件\r\n      try {\r\n        await delImg({url: row.slipFile})\r\n      } catch (e) {\r\n        // 更新流水中的图片地址\r\n        await updateBankrecord({\r\n          bankRecordId: row.bankRecordId,\r\n          slipFile: null,\r\n          isRecievingOrPaying: this.type === \"pay\" ? \"1\" : \"0\",\r\n          bankRecordNo: row.bankRecordNo\r\n        })\r\n      }\r\n\r\n      await this.getList()\r\n    },\r\n    customHttpRequest(options) {\r\n      const formData = new FormData()\r\n      formData.append(\"file\", options.file)\r\n\r\n      request({\r\n        url: \"/system/bankrecord/uploadImg\",\r\n        method: \"post\",\r\n        data: formData\r\n      }).then(response => {\r\n        options.onSuccess(response, options.file)\r\n        this.imgUrl = response.url\r\n        this.form.slipFile = response.url\r\n        options.row.slipFile = response.url\r\n      }).catch(error => {\r\n        options.onError(error)\r\n      })\r\n    },\r\n    handleChange(file, fileList) {\r\n\r\n      const extension = file.name.substring(file.name.lastIndexOf(\".\"))\r\n      const newFileName = `${this.form.bankRecordNo}${extension}`\r\n      this.imageFile = new File([file.raw], newFileName, {type: file.type})\r\n      this.$message.success(\"图片已选择\")\r\n    },\r\n    handleSuccess(response, file, fileList) {\r\n      this.form.slipFile = response.url\r\n    },\r\n    handleError(err, file, fileList) {\r\n      this.$message.error(\"Upload failed:\", err)\r\n    },\r\n    handleRemove(file, fileList) {\r\n      console.log(file, fileList)\r\n    },\r\n    handlePreview(file) {\r\n      console.log(file)\r\n    },\r\n    handleExceed(files, fileList) {\r\n      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)\r\n    },\r\n    beforeRemove(file, fileList) {\r\n      return this.$confirm(`确定移除 ${file.name}？`)\r\n    },\r\n    uploadImage(row) {\r\n      return new Promise((resolve, reject) => {\r\n        this.customHttpRequest({\r\n          file: this.imageFile,\r\n          onSuccess: (response) => {\r\n            this.handleSuccess(response)\r\n            resolve(response)\r\n          },\r\n          onError: (error) => {\r\n            this.handleError(error)\r\n            reject(error)\r\n          },\r\n          row\r\n        })\r\n      })\r\n    },\r\n    async submitForm(row) {\r\n      // 先上传图片\r\n      if (this.imageFile) {\r\n        // Perform the upload first and wait for it to complete\r\n        await this.uploadImage(row)\r\n      }\r\n      console.log(row)\r\n      updateBankrecord(row).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        // this.open = false\r\n        this.getList()\r\n      })\r\n\r\n      // 更新操作单上的水单信息\r\n      if (this.type === \"pay\") {\r\n        updateRct({rctId: this.scope.row.rctId, sqdDnPaySlipStatus: \"√\"}).then(response => {\r\n        })\r\n      } else {\r\n        updateRct({rctId: this.scope.row.rctId, sqdDnReceiveSlipStatus: \"√\"}).then(response => {\r\n        })\r\n      }\r\n    },\r\n    parseTime,\r\n    currency,\r\n    async openBankSlip() {\r\n      await this.getList()\r\n      this.bankSlipOpen = true\r\n    },\r\n    getList() {\r\n      this.loading = true\r\n      listBankrecord({\r\n        clearingCompanyId: this.scope.row.clientId,\r\n        isRecievingOrPaying: this.type === \"pay\" ? \"1\" : \"0\",\r\n        rctNo: this.scope.row.rctNo\r\n      }).then(response => {\r\n        response.rows.map(item => item.clients = this.clients)\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total ? response.total : 0\r\n        this.loading = false\r\n        // 应收水单状态 sqd_dn_receive_slip_status\r\n        if (response.rows && response.rows.length === 0 && this.type === \"receive\" && this.scope.row.sqdDnReceiveSlipStatus === null) {\r\n          updateRct({rctId: this.scope.row.rctId, sqdDnReceiveSlipStatus: \"-\"}).then(response => {\r\n          })\r\n        }\r\n        // 应付水单状态 sqd_dn_pay_slip_status\r\n        if (response.rows && response.rows.length === 0 && this.type === \"pay\" && this.scope.row.sqdDnPaySlipStatus === null) {\r\n          updateRct({rctId: this.scope.row.rctId, sqdDnPaySlipStatus: \"-\"}).then(response => {\r\n          })\r\n        }\r\n      })\r\n    },\r\n    selectBankAccount(row) {\r\n      console.log(row)\r\n      this.form.sqdPaymentTitleCode = row.sqdBelongToCompanyCode\r\n    },\r\n    handleDelete(row) {\r\n      const bankRecordIds = row.bankRecordId || this.ids\r\n      this.$confirm(\"是否确认删除？\", '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delBankrecord(bankRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleUpdate(row) {\r\n      this.add = false\r\n      this.reset()\r\n      const bankRecordId = row.bankRecordId || this.ids\r\n      getBankrecord(bankRecordId).then(response => {\r\n        this.form = response.data\r\n        this.form.chargeType = \"订单\"\r\n        this.open = true\r\n        this.title = \"查看银行流水-销账明细\"\r\n        this.companyList = [response.companyList]\r\n      })\r\n    },\r\n    verify() {\r\n      if (this.form.clearingCompanyId === null) {\r\n        this.$message.warning(\"请输入结算公司\")\r\n        return\r\n      }\r\n      this.form.isBankRecordLocked = 1\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$message.success(\"修改成功\")\r\n      })\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        bankRecordId: null,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: \"0\",\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        invoiceNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        chargeType: \"订单\"\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    handleAdd() {\r\n      let data = {}\r\n      data.isRecievingOrPaying = this.type === \"pay\" ? \"1\" : \"0\"\r\n      data.paymentTypeCode = \"T/T\"\r\n      data.chargeType = \"订单\"\r\n      data.chargeTypeId = 2\r\n      data.clearingCompanyId = this.scope.row.clientId\r\n      data.sqdClearingCompanyShortname = this.scope.row.clientSummary.split(\"/\")[1]\r\n      data.sqdRaletiveRctList = this.scope.row.rctNo\r\n\r\n      addBankrecord(data).then(response => {\r\n        this.form = response.data\r\n        this.$modal.msgSuccess(\"新增成功\")\r\n        // this.open = false\r\n        this.getList()\r\n      })\r\n    },\r\n    isRecievingOrPayingNormalizer(node) {\r\n      return {\r\n        id: node.value,\r\n        label: node.label\r\n      }\r\n    },\r\n    companyNormalizer(node) {\r\n      return {\r\n        id: node.companyId,\r\n        label: (node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\") + \",\" + pinyin.getFullChars((node.companyShortName != null ? node.companyShortName : \"\") + \" \" + (node.companyLocalName != null ? node.companyLocalName : \"\"))\r\n      }\r\n    },\r\n    queryCompany(node) {\r\n      this.queryParams.company = node.companyShortName\r\n      this.queryParams.companyId = node.companyId\r\n      this.handleQuery()\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n::v-deep .vue-treeselect__value-container {\r\n  display: block;\r\n}\r\n</style>\r\n"]}]}