{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\reimbursementBankRecord.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\reimbursementBankRecord.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_bankrecord", "require", "_index", "_interopRequireDefault", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_store", "_currency", "_rsCharge", "_exchangerate", "_rich", "_log", "_moment", "_request", "_reimburse", "_user", "name", "components", "Treeselect", "CompanySelect", "beforeMount", "loadStaff", "data", "userList", "size", "$store", "state", "app", "writeOffList", "salesId", "belongList", "staffList", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "bankrecordList", "reimburseList", "title", "open", "queryParams", "pageNum", "pageSize", "isRecievingOrPaying", "sqdPaymentTitleCode", "bankAccountCode", "clearingCompanyId", "sqdClearingCompanyShortname", "chargeTypeId", "chargeDescription", "bankCurrencyCode", "actualBankRecievedAmount", "actualBankPaidAmount", "bankRecievedHandlingFee", "bankPaidHandlingFee", "bankRecievedExchangeLost", "bankPaidExchangeLost", "sqdBillRecievedAmount", "sqdBillPaidAmount", "billRecievedWriteoffAmount", "billPaidWriteoffAmount", "sqdBillRecievedWriteoffBalance", "sqdBillPaidWriteoffBalance", "writeoffStatus", "bankRecordTime", "paymentTypeCode", "voucherNo", "bankRecordRemark", "bankRecordByStaffId", "bankRecordUpdateTime", "isBankRecordLocked", "isWriteoffLocked", "sqdChargeIdList", "sqdRaletiveRctList", "sqdRaletiveInvoiceList", "sqdRsStaffId", "writeoffRemark", "writeoffStaffId", "writeoffTime", "form", "rules", "required", "message", "trigger", "add", "showDetail", "selected<PERSON><PERSON>ges", "selectedReimburses", "totalAmount", "selectedReimburseAmount", "selectedAmount", "selectedBalanceAmount", "loadingCharge", "staffId", "alreadyWriteoffList", "turnBackWriteoffList", "showCompany", "companyList", "bankSlipPreview", "imageFile", "watch", "n", "formActualBankRecievedAmount", "currency", "value", "formBankRecievedHandlingFee", "formBankRecievedExchangeLost", "subtract", "formActualBankPaidAmount", "formBankPaidHandlingFee", "formBankPaidExchangeLost", "formSqdBillRecievedAmount", "formSqdBillPaidAmount", "formBillRecievedWriteoffAmount", "formBillPaidWriteoffAmount", "handler", "newVal", "oldVal", "_this", "console", "log", "map", "item", "writeoffFromDnBalance", "writeoffFromBankBalance", "divide", "exchangeRate", "sqdDnCurrencyBalance", "dnUnitRate", "multiply", "dnAmount", "deep", "actualReimbursePrice", "created", "_this2", "selectListUser", "then", "response", "getList", "loadSales", "getExchangeRate", "val", "computed", "receiveRate", "paidRate", "isLocked", "isBankSlipConfirmed", "slipConfirmed", "methods", "handleSearch", "type", "parseTime", "updateSlipSatus", "_this3", "updateBankrecord", "$message", "success", "writeOffConfirm", "_this4", "bankRecordId", "writeOffReimburse", "getCompanyCharges", "invertSelection", "_this5", "for<PERSON>ach", "row", "$refs", "writeOffTable", "toggleRowSelection", "autoSelection", "addHedging", "projectRemove", "print", "_this6", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "listWriteOffReimburse", "rows", "$nextTick", "reimbursePrice", "sort", "a", "b", "stop", "verify", "_this7", "verifyId", "verifyTime", "user", "sid", "moment", "format", "_this8", "listBankrecord", "cancel", "reset", "invoiceNo", "chargeType", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this9", "text", "status", "$confirm", "changeStatus", "$modal", "msgSuccess", "catch", "handleSelectionChange", "selection", "_this10", "length", "includes", "carousel", "pics", "openCarousel", "info", "handleAdd", "handleUpdate", "_this11", "getBankrecord", "submitForm", "_this12", "validate", "_ref", "_callee2", "valid", "_callee2$", "_context2", "clearReceiveOrPay", "addBankrecord", "_x", "apply", "arguments", "uploadImage", "_this13", "Promise", "resolve", "reject", "customHttpRequest", "file", "onSuccess", "handleSuccess", "onError", "error", "handleError", "handleDelete", "_this14", "bankRecordIds", "delBankrecord", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "staffNormalizer", "node", "children", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "id", "label", "isDisabled", "undefined", "deptId", "_this15", "salesList", "redisList", "store", "dispatch", "_this16", "allRsStaffList", "handledbClick", "selectCompany", "company", "companyId", "companyShortName", "overseaCurrency", "localCurrency", "valueDate", "_callee3", "re", "_iterator", "_step", "_callee3$", "_context3", "selectListExchangerate", "sent", "_createForOfIteratorHelper2", "s", "done", "validFrom", "validTo", "buyRate", "base", "sellRate", "err", "e", "f", "abrupt", "getBillDataExchangeRate", "_callee4", "_iterator2", "_step2", "_callee4$", "_context4", "exchangeRateShow", "chargeCurrencyCode", "_this17", "_callee5", "result", "_callee5$", "_context5", "getName", "filter", "rsStaff", "staffShortName", "selectStaff", "checkSelectable", "isAccountConfirmed", "handleDialogOpened", "_this18", "treeSelectInput", "treeSelect", "getInputElement", "focus", "isRecievingOrPayingNormalizer", "selectBankAccount", "options", "_this19", "formData", "FormData", "append", "request", "url", "method", "slipFile", "handleChange", "fileList", "extension", "substring", "lastIndexOf", "newFileName", "bankRecordNo", "File", "raw", "exports", "_default"], "sources": ["src/views/system/bankrecord/reimbursementBankRecord.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n            <el-input\r\n              v-model=\"queryParams.isRecievingOrPaying\"\r\n              clearable\r\n              placeholder=\"收支标志\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属公司\" prop=\"sqdPaymentTitleCode\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdPaymentTitleCode\"\r\n              clearable\r\n              placeholder=\"所属公司\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankAccountCode\"\r\n              clearable\r\n              placeholder=\"银行账户\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算公司\" prop=\"sqdClearingCompanyShortname\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdClearingCompanyShortname\"\r\n              clearable\r\n              placeholder=\"结算公司简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"录入人\" prop=\"bankRecordByStaffId\">\r\n            <el-select v-model=\"queryParams.bankRecordByStaffId\" filterable placeholder=\"所属员工\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\"\r\n            >\r\n              <el-option\r\n                v-for=\"staff in userList\"\r\n                :key=\"staff.staffId\"\r\n                :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                :value=\"staff.staffId\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"录入时间 ,\" prop=\"bankRecordUpdateTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordUpdateTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水录入时间 ,\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属员工\" prop=\"sqdRsStaffId\">\r\n            <el-select v-model=\"queryParams.sqdRsStaffId\" filterable placeholder=\"所属员工\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\"\r\n            >\r\n              <el-option\r\n                v-for=\"staff in userList\"\r\n                :key=\"staff.staffId\"\r\n                :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                :value=\"staff.staffId\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.writeoffStaffId\"\r\n              clearable\r\n              placeholder=\"销账人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账时间\" prop=\"writeoffTime\">\r\n            <el-date-picker v-model=\"queryParams.writeoffTime\"\r\n                            clearable\r\n                            placeholder=\"销账时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:edit']\"\r\n              :disabled=\"single\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--报销记录-->\r\n        <el-table v-loading=\"loading\" :data=\"bankrecordList\" highlight-current-row\r\n                  stripe @selection-change=\"handleSelectionChange\" @row-dblclick=\"handledbClick\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"银行流水\" prop=\"bankRecordNo\"/>\r\n          <el-table-column align=\"center\" label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.isRecievingOrPaying == 1 ? \"付\" : \"收\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"sqdPaymentTitleCode\"/>\r\n          <el-table-column align=\"center\" label=\"结算公司\" prop=\"sqdClearingCompanyShortname\"/>\r\n          <el-table-column align=\"center\" label=\"银行时间\" prop=\"bankRecordTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.bankRecordTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"银行账户\" prop=\"bankAccountCode\"/>\r\n          <el-table-column align=\"center\" label=\"水单金额\" prop=\"slipAmount\"/>\r\n          <el-table-column align=\"center\" label=\"实收金额\" prop=\"actualBankRecievedAmount\"/>\r\n          <el-table-column align=\"center\" label=\"收款手续费\" prop=\"bankRecievedHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"实付金额\" prop=\"actualBankPaidAmount\"/>\r\n          <el-table-column align=\"center\" label=\"付款手续费\" prop=\"bankPaidHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"销账状态 \" prop=\"writeoffStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.writeoffStatus == 1 ? \"=\" : (scope.row.writeoffStatus == 0 ? \"√\" : \"-\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"相关操作单号\" prop=\"sqdRaletiveRctList\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <template slot-scope=\"scope\">\r\n              {{ getName(scope.row.writeoffStaffId) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"销账备注\" prop=\"bankRecordRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账时间\" prop=\"writeoffTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.writeoffTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改记录公司账户出入账明细对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      append-to-body\r\n      height=\"60%\"\r\n      width=\"60%\"\r\n      @close=\"showDetail=false\"\r\n    >\r\n      <el-form v-if=\"open\" ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"12\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"银行流水\" prop=\"voucherNo\">\r\n                <el-input :value=\"form.bankRecordNo\" class=\"disable-form\" disabled placeholder=\"银行流水\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n                <treeselect ref=\"treeSelect\" v-model=\"form.isRecievingOrPaying\"\r\n                            :auto-focus=\"true\"\r\n                            :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\" :disable-branch-nodes=\"true\"\r\n                            :disable-fuzzy-matching=\"true\"\r\n                            :disabled=\"isLocked||isBankSlipConfirmed\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"isRecievingOrPayingNormalizer\"\r\n                            :options=\"[{label:'实收',value:'0'},{label:'实付',value:'1'}]\" :show-count=\"true\"\r\n                            placeholder=\"选择收付信息\" @select=\"form.isRecievingOrPaying=$event.value\"\r\n                >\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n                <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                             :disabled=\"isLocked||isBankSlipConfirmed\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.bankAccountCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\"\r\n                             @return=\"form.bankAccountCode=$event\" @returnData=\"selectBankAccount\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"所属员工\">\r\n                <el-select v-model=\"form.sqdRsStaffId\" :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                           :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                           filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"staff in staffList\"\r\n                    :key=\"staff.staffId\"\r\n                    :label=\"staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                    :value=\"staff.staffId\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"所属单号\" prop=\"bankAccountCode\">\r\n                <el-input v-model=\"form.sqdRaletiveRctList\" placeholder=\"银行流水\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"费用描述\" prop=\"voucherNo\">\r\n                <el-row>\r\n                  <el-col :span=\"8\">\r\n                    <tree-select :charge-name-type=\"4\" :pass=\"form.chargeTypeId\" :placeholder=\"'费用类型'\"\r\n                                 :type=\"'chargeNameType'\" style=\"width: 100%\" @return=\"form.chargeTypeId=$event\"\r\n                                 :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.chargeDescription\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                              placeholder=\"费用描述\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.bankRecordTime\"\r\n                  :class=\"isLocked?'disable-form':''\"\r\n                  :disabled=\"isLocked\"\r\n                  clearable\r\n                  default-time=\"12:00:00\"\r\n                  placeholder=\"银行时间\"\r\n                  style=\"width: 100%\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"所属公司\">\r\n                <tree-select\r\n                  :class=\"isLocked ?'disable-form':''\" :disabled=\"isLocked\"\r\n                  :pass=\"form.sqdPaymentTitleCode\" :placeholder=\"'收付路径'\" :type=\"'rsPaymentTitle'\"\r\n                  @return=\"form.sqdPaymentTitleCode=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"实收金额\" prop=\"actualBankRecievedAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                                 :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                                 :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankRecievedAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实收金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"手续费\" prop=\"bankRecievedExchangeLost\">\r\n                <el-input v-model=\"form.bankRecievedHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收款记账\" prop=\"sqdBillRecievedAmount\">\r\n                <el-input v-model=\"form.sqdBillRecievedAmount\" :class=\"'disable-form'\" disabled\r\n                          placeholder=\"收款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收账已销\" prop=\"billRecievedWriteoffAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.billRecievedWriteoffAmount\" :class=\"'disable-form'\"\r\n                              disabled placeholder=\"收账已销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-input v-model=\"form.bankRecievedExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                              :disabled=\"!isLocked\" placeholder=\"收款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" :disabled=\"isLocked\" label=\"收账未销\"\r\n                            prop=\"sqdBillRecievedWriteoffBalance\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillRecievedWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"收账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillRecievedWriteoffBalance===form.sqdBillRecievedAmount?'-':form.sqdBillRecievedWriteoffBalance===0?'√':'='\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"实付金额\" prop=\"actualBankPaidAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankPaidAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实付金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"手续费\" prop=\"bankPaidExchangeLost\">\r\n                <el-input v-model=\"form.bankPaidHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付款记账\" prop=\"sqdBillPaidAmount\">\r\n                <el-input v-model=\"form.sqdBillPaidAmount\" class=\"disable-form\" disabled\r\n                          placeholder=\"付款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账已销\" prop=\"billPaidWriteoffAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.billPaidWriteoffAmount\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账已销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-input v-model=\"form.bankPaidExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                              :disabled=\"!isLocked\" placeholder=\"付款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账未销\" prop=\"sqdBillPaidWriteoffBalance\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillPaidWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillPaidAmount===form.sqdBillPaidWriteoffBalance?'-':form.sqdBillPaidWriteoffBalance===0?'√':'='\"\r\n                      placeholder=\"收款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行备注\" prop=\"bankRecordRemark\">\r\n                <el-input v-model=\"form.bankRecordRemark\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"银行备注\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col v-if=\"!this.form.bankRecordId\" :span=\"1.5\">\r\n              <el-button :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" style=\"float: right\" type=\"primary\"\r\n                         @click=\"submitForm\"\r\n              >{{ this.form.bankRecordId === null ? \"新增\" : \"保存\" }}\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <div style=\"display: flex\">\r\n                <el-button :icon=\"form.isBankRecordLocked==1?'el-icon-check':''\" type=\"primary\"\r\n                           v-if=\"form.bankRecordId\" @click=\"verify\"\r\n                >\r\n                  {{ form.isBankRecordLocked == 1 ? \"已审核\" : \"出账审核\" }}\r\n                </el-button>\r\n                <div v-if=\"form.isBankRecordLocked == 1 \">\r\n                  <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                    {{ (form.verifyId ? getName(form.verifyId) : \"\") }}\r\n                  </div>\r\n                  <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                    {{ parseTime(form.verifyTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-button :disabled=\"!isLocked\" :loading=\"loadingCharge\" style=\"float: right\" type=\"primary\"\r\n                         @click=\"getCompanyCharges\"\r\n              >\r\n                调取相关费用明细\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table\r\n        v-show=\"showDetail\"\r\n        ref=\"writeOffTable\"\r\n        :data=\"reimburseList\"\r\n        border\r\n        max-height=\"315px\"\r\n        size=\"mini\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n        <el-table-column align=\"center\" label=\"ID\" prop=\"reimburseId\" width=\"38\"/>\r\n        <el-table-column align=\"center\" label=\"报销人\" prop=\"staffName\" width=\"68\"/>\r\n        <el-table-column align=\"center\" label=\"费用类型\" prop=\"chargeTypeName\" show-tooltip-when-overflow width=\"88\"/>\r\n        <el-table-column align=\"center\" label=\"报销概要\" prop=\"reimburseTitle\" show-tooltip-when-overflow width=\"68\"/>\r\n        <el-table-column align=\"center\" label=\"报销详情\" prop=\"reimburseContent\" show-tooltip-when-overflow/>\r\n        <el-table-column align=\"center\" label=\"参与人员\" prop=\"reimburseParticipation\" show-tooltip-when-overflow\r\n                         width=\"170\"\r\n        />\r\n        <el-table-column align=\"center\" label=\"报销金额\" prop=\"reimbursePrice\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <h1 style=\"margin: 0;font-weight:bold;\">\r\n              {{ scope.row.reimbursePrice }}\r\n            </h1>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"日期\" prop=\"happenDate\" width=\"142\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 style=\"margin: 0\">{{ \"发生日期：\" + parseTime(scope.row.happenDate, \"{y}-{m}-{d}\") }}</h6>\r\n            <h6 style=\"margin: 0\">{{ \"申请日期：\" + parseTime(scope.row.applyDate, \"{y}-{m}-{d}\") }}</h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"部门审批\" prop=\"deptConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==1&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.deptConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.deptReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"人事审批\" prop=\"hrConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==1&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.hrConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.hrReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"总经办审批\" prop=\"ceoConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==1&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.ceoConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.ceoReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"财务确认\" prop=\"financeConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==1&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.financeConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.financeReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单据附件\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button :size=\"size\" style=\"padding: 0\" type=\"info\" @click=\"carousel(scope.row.reimburseAppendix)\">\r\n              查看图片\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow width=\"100\"/>\r\n      </el-table>\r\n      <!--总计-->\r\n      <div v-if=\"showDetail\" class=\"total\">\r\n        <div style=\"width: 30%;\">全部总计: {{ (totalAmount ? totalAmount : 0) + \" RMB\" }}</div>\r\n        <div style=\"width: 30%;\">已选总计:\r\n          {{ (this.selectedReimburseAmount ? this.selectedReimburseAmount : 0) + \" RMB\" }}\r\n        </div>\r\n      </div>\r\n\r\n      <!--按钮区-->\r\n      <div v-if=\"showDetail\" class=\"table-btn-group\">\r\n        <div class=\"table-btn-left\">\r\n          <el-button type=\"primary\" @click=\"print\">打印</el-button>\r\n        </div>\r\n        <div class=\"table-btn-right\">\r\n          <el-button style=\"float: right\" type=\"primary\" @click=\"writeOffConfirm\">确定销账</el-button>\r\n        </div>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"bankSlipPreview\"\r\n        append-to-body destroy-on-close\r\n        height=\"50%\"\r\n        width=\"50%\"\r\n      >\r\n        <el-image :src=\"form.slipFile\" style=\"margin-top: 20px;\"/>\r\n      </el-dialog>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addBankrecord,\r\n  changeStatus,\r\n  delBankrecord,\r\n  getBankrecord,\r\n  listBankrecord,\r\n  updateBankrecord\r\n} from \"@/api/system/bankrecord\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport currency from \"currency.js\"\r\nimport {chargeWriteOff, selectListCharge, turnBackWriteoff} from \"@/api/system/rsCharge\"\r\nimport {selectListExchangerate} from \"@/api/system/exchangerate\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport log from \"@/views/monitor/job/log.vue\"\r\nimport moment from \"moment\"\r\nimport request from \"@/utils/request\"\r\nimport {listReimburse, listWriteOffReimburse, writeOffReimburse} from \"@/api/system/reimburse\"\r\nimport {selectListUser} from \"@/api/system/user\"\r\n\r\nexport default {\r\n  name: \"reimbursementBankRecord\",\r\n  components: {Treeselect, CompanySelect},\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  },\r\n  data() {\r\n    return {\r\n      userList: [],\r\n      size: this.$store.state.app.size || \"mini\",\r\n      writeOffList: [],\r\n      salesId: null,\r\n      belongList: [],\r\n      staffList: [],\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 记录公司账户出入账明细表格数据\r\n      bankrecordList: [],\r\n      reimburseList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: null,\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        actualBankRecievedAmount: [\r\n          {required: true, message: \"请输入实收信息\", trigger: \"blur\"}\r\n        ],\r\n        bankRecordTime: [\r\n          {required: true, message: \"请输入银行时间\", trigger: \"blur\"}\r\n        ],\r\n        actualBankPaidAmount: [\r\n          {required: true, message: \"请输入实付信息\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      add: false,\r\n      showDetail: false,\r\n      selectedCharges: [],\r\n      selectedReimburses: [],\r\n      totalAmount: null,\r\n      selectedReimburseAmount: null,\r\n      selectedAmount: null,\r\n      selectedBalanceAmount: null,\r\n      loadingCharge: false,\r\n      staffId: null,\r\n      alreadyWriteoffList: [],\r\n      turnBackWriteoffList: [],\r\n      showCompany: false,\r\n      companyList: [],\r\n      bankSlipPreview: false,\r\n      imageFile: null\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    // 实收金额\r\n    \"form.actualBankRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款手续费\r\n    \"form.bankRecievedHandlingFee\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款损益\r\n    \"form.bankRecievedExchangeLost\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 实付金额\r\n    \"form.actualBankPaidAmount\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款手续费\r\n    \"form.bankPaidHandlingFee\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款损益\r\n    \"form.bankPaidExchangeLost\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收款记账\r\n    \"form.sqdBillRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付款记账\r\n    \"form.sqdBillPaidAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收账已销\r\n    \"form.billRecievedWriteoffAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付账已销\r\n    \"form.billPaidWriteoffAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n    /*      // 收账未销\r\n         'form.sqdBillRecievedWriteoffBalance'(n) {\r\n           this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n         },\r\n         // 付账未销\r\n         'form.sqdBillPaidWriteoffBalance'(n) {\r\n           this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n         }, */\r\n    selectedCharges: {\r\n      handler: function (newVal, oldVal) {\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = 0\r\n\r\n        // 已选总计\r\n        this.selectedBalanceAmount = 0\r\n\r\n        let billRecievedWriteoffAmount = 0\r\n        let billPaidWriteoffAmount = 0\r\n\r\n        let selectedBalanceAmount = 0\r\n        console.log(newVal)\r\n        newVal.map(item => {\r\n          // 本次拟销账金额\r\n          item.writeoffFromDnBalance = currency(item.writeoffFromBankBalance).divide(item.exchangeRate).value\r\n\r\n          // 收账已销\r\n          billRecievedWriteoffAmount += currency(item.writeoffFromBankBalance).value\r\n          // 付账已销\r\n          billPaidWriteoffAmount += currency(item.writeoffFromBankBalance).value\r\n\r\n          // 已选余额总计\r\n          selectedBalanceAmount = currency(item.sqdDnCurrencyBalance).add(selectedBalanceAmount).value\r\n\r\n          // 已选总计\r\n          this.selectedAmount = null\r\n\r\n          currency(item.dnUnitRate).multiply(item.dnAmount).value === item.writeoffFromDnBalance ? item.writeoffStatus = \"0\" : item.writeoffFromDnBalance > 0 ? item.writeoffStatus = \"1\" : item.writeoffStatus = \"-1\"\r\n        })\r\n\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = billRecievedWriteoffAmount\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = billPaidWriteoffAmount\r\n\r\n        this.selectedBalanceAmount = selectedBalanceAmount\r\n\r\n      },\r\n      deep: true\r\n    },\r\n    selectedReimburses: {\r\n      handler: function (newVal, oldVal) {\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = 0\r\n\r\n        // 已选总计\r\n        this.selectedBalanceAmount = 0\r\n\r\n        let billRecievedWriteoffAmount = 0\r\n        let billPaidWriteoffAmount = 0\r\n\r\n        let selectedBalanceAmount = 0\r\n        newVal.map(item => {\r\n          // 收账已销\r\n          billRecievedWriteoffAmount += currency(item.actualReimbursePrice).value\r\n          // 付账已销\r\n          billPaidWriteoffAmount += currency(item.actualReimbursePrice).value\r\n\r\n          // 已选余额总计\r\n          selectedBalanceAmount = currency(item.sqdDnCurrencyBalance).add(selectedBalanceAmount).value\r\n\r\n        })\r\n\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = billRecievedWriteoffAmount\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = billPaidWriteoffAmount\r\n\r\n        this.selectedBalanceAmount = selectedBalanceAmount\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    selectListUser().then(response => {\r\n      this.userList = response.data\r\n    })\r\n    this.getList()\r\n    this.getList()\r\n    this.loadSales()\r\n    this.loadStaff()\r\n    this.getExchangeRate(\"USD\", \"RMB\", null, val => {\r\n      console.log(val)\r\n    })\r\n  },\r\n  computed: {\r\n    receiveRate() {\r\n      return this.form.actualBankRecievedAmount + this.form.bankRecievedHandlingFee + this.form.bankRecievedExchangeLost\r\n    },\r\n    paidRate() {\r\n      return this.form.actualBankPaidAmount + this.form.bankPaidHandlingFee + this.form.bankPaidExchangeLost\r\n    },\r\n    isLocked() {\r\n      return this.form.isBankRecordLocked == 1\r\n    },\r\n    isBankSlipConfirmed() {\r\n      return this.form.slipConfirmed == 1\r\n    }\r\n  },\r\n  methods: {\r\n    handleSearch(type) {\r\n      switch (type) {\r\n        case \"common\":\r\n          this.getList({})\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    },\r\n    parseTime,\r\n    updateSlipSatus() {\r\n      if (this.form.slipConfirmed == 1) {\r\n        this.form.slipConfirmed = \"0\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单取消确认\")\r\n        })\r\n      } else {\r\n        this.form.slipConfirmed = \"1\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单确认\")\r\n        })\r\n      }\r\n    },\r\n    writeOffConfirm() {\r\n      const data = this.selectedReimburses.map(item => {\r\n        item.bankRecordId = this.form.bankRecordId\r\n        return item\r\n      })\r\n      writeOffReimburse(data).then(response => {\r\n        this.$message.success(\"成功销账\")\r\n        this.getCompanyCharges()\r\n      })\r\n    },\r\n    invertSelection() {\r\n      this.writeOffList.forEach(row => {\r\n        // if (this.selectedCharges.indexOf(row) !== -1) {\r\n        this.$refs.writeOffTable.toggleRowSelection(row)\r\n        // }\r\n      })\r\n    },\r\n    autoSelection() {\r\n    },\r\n    addHedging() {\r\n    },\r\n    projectRemove() {\r\n    },\r\n    print() {\r\n    },\r\n    currency,\r\n    async getCompanyCharges() {\r\n      this.reimburseList = []\r\n      this.loadingCharge = true\r\n\r\n      listWriteOffReimburse({\r\n        staffId: this.form.sqdRsStaffId,\r\n        sqdRaletiveRctList: this.form.sqdRaletiveRctList,\r\n        bankRecordId: this.form.bankRecordId\r\n      }).then(response => {\r\n        this.reimburseList = response.rows\r\n\r\n        this.loadingCharge = false\r\n        this.showDetail = true\r\n\r\n        this.totalAmount = 0\r\n        this.$nextTick(() => {\r\n          this.reimburseList.map(item => {\r\n            this.totalAmount = currency(item.reimbursePrice).add(this.totalAmount).value\r\n            if (item.bankRecordId === this.form.bankRecordId) {\r\n              this.$refs.writeOffTable.toggleRowSelection(item, true)\r\n            }\r\n          })\r\n        })\r\n\r\n        this.reimburseList.sort((a, b) => {\r\n          if (a.bankRecordId === this.form.bankRecordId && b.bankRecordId !== this.form.bankRecordId) {\r\n            return -1 // a 排在 b 前\r\n          } else if (a.bankRecordId !== this.form.bankRecordId && b.bankRecordId === this.form.bankRecordId) {\r\n            return 1 // b 排在 a 前\r\n          } else {\r\n            return 0 // 保持原顺序\r\n          }\r\n        })\r\n      })\r\n    },\r\n    verify() {\r\n      if (this.form.isBankRecordLocked == 1) {\r\n        this.form.isBankRecordLocked = 0\r\n        this.form.verifyId = null\r\n        this.form.verifyTime = null\r\n      } else {\r\n        this.form.isBankRecordLocked = 1\r\n        this.form.verifyId = this.$store.state.user.sid\r\n        this.form.verifyTime = moment().format(\"yyyy-MM-DD\")\r\n      }\r\n\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$message.success(\"修改成功\")\r\n      })\r\n    },\r\n    /** 查询记录公司账户出入账明细列表 */\r\n    getList() {\r\n      this.loading = true\r\n      this.queryParams.chargeTypeId = 4\r\n      listBankrecord(this.queryParams).then(response => {\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n// 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    }\r\n    ,\r\n// 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankRecordId: null,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: \"0\",\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        invoiceNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        chargeType: \"订单\"\r\n      }\r\n      this.staffId = null\r\n      this.selectedBalanceAmount = 0\r\n      this.resetForm(\"form\")\r\n    }\r\n    ,\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    }\r\n    ,\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    }\r\n    ,\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.bankRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    }\r\n    ,\r\n// 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      // this.ids = selection.map(item => item.bankRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n\r\n      this.totalAmount = 0\r\n      this.selectedReimburseAmount = 0\r\n      this.reimburseList.map(item => {\r\n        if (selection.includes(item)) {\r\n          this.selectedReimburseAmount = currency(item.reimbursePrice).add(this.selectedReimburseAmount).value\r\n        }\r\n        this.totalAmount = currency(item.reimbursePrice).add(this.totalAmount).value\r\n      })\r\n\r\n      this.selectedReimburses = selection\r\n\r\n      // 勾选的将将金额累加并减去付账未销\r\n\r\n    },\r\n    carousel(pics) {\r\n      if (pics != null) {\r\n        this.openCarousel = true\r\n        this.pics = pics\r\n      } else {\r\n        this.$message.info(\"没有附件\")\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.showDetail = false\r\n      this.open = true\r\n      this.title = \"新建银行流水\"\r\n      this.add = true\r\n      this.form.isRecievingOrPaying = \"1\"\r\n      this.form.chargeType = \"\"\r\n      this.form.chargeTypeId = 4\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.add = false\r\n      this.reset()\r\n      const bankRecordId = row.bankRecordId || this.ids\r\n      getBankrecord(bankRecordId).then(response => {\r\n        this.form = response.data\r\n        this.form.chargeType = \"订单\"\r\n        this.open = true\r\n        this.title = \"查看银行流水-销账明细\"\r\n        this.companyList = [response.companyList]\r\n\r\n        if (this.form.isBankRecordLocked === \"1\" && ((response.data.isRecievingOrPaying === \"0\" && currency(response.data.sqdBillRecievedWriteoffBalance).value !== 0) || (response.data.isRecievingOrPaying === \"1\" && currency(response.data.sqdBillPaidWriteoffBalance).value !== 0))) {\r\n          this.getCompanyCharges()\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(async valid => {\r\n        if (valid) {\r\n          // 收款时将付款信息清空,付款时将收款信息清空\r\n          this.clearReceiveOrPay()\r\n          if (this.form.bankRecordId != null) {\r\n            updateBankrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addBankrecord(this.form).then(response => {\r\n              this.form = response.data\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    uploadImage() {\r\n      return new Promise((resolve, reject) => {\r\n        console.log(this.imageFile)\r\n        this.customHttpRequest({\r\n          file: this.imageFile,\r\n          onSuccess: (response) => {\r\n            this.handleSuccess(response)\r\n            resolve(response)\r\n          },\r\n          onError: (error) => {\r\n            this.handleError(error)\r\n            reject(error)\r\n          }\r\n        })\r\n      })\r\n    },\r\n    clearReceiveOrPay() {\r\n      if (this.form.isRecievingOrPaying === \"0\") {\r\n        // 收款\r\n        this.form.actualBankPaidAmount = 0\r\n        this.form.bankPaidHandlingFee = 0\r\n        this.form.sqdBillPaidAmount = 0\r\n        this.form.billPaidWriteoffAmount = 0\r\n        this.form.bankPaidExchangeLost = 0\r\n        this.form.sqdBillPaidWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillRecievedWriteoffBalance === this.form.sqdBillRecievedAmount ? this.form.writeoffStatus = -1 : this.form.sqdBillRecievedWriteoffBalance === 0 ? this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      } else {\r\n        // 付款\r\n        this.form.actualBankRecievedAmount = 0\r\n        this.form.bankRecievedHandlingFee = 0\r\n        this.form.sqdBillRecievedAmount = 0\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        this.form.bankRecievedExchangeLost = 0\r\n        this.form.sqdBillRecievedWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillPaidAmount === this.form.sqdBillPaidWriteoffBalance ? this.form.writeoffStatus = -1 : this.form.sqdBillPaidWriteoffBalance === 0 ? this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const bankRecordIds = row.bankRecordId || this.ids\r\n      this.$confirm(\"是否确认删除记录公司账户出入账明细编号为\\\"\" + bankRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delBankrecord(bankRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    }\r\n    ,\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/bankrecord/export\", {\r\n        ...this.queryParams\r\n      }, `bankrecord_${new Date().getTime()}.xlsx`)\r\n    }\r\n    ,\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n    ,\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    handledbClick(row) {\r\n      this.handleUpdate(row)\r\n    }\r\n    ,\r\n    selectCompany(company) {\r\n      this.form.clearingCompanyId = company.companyId\r\n      this.form.sqdClearingCompanyShortname = company.companyShortName\r\n      this.showCompany = false\r\n    }\r\n    ,\r\n    async getExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency === localCurrency || a.currency === localCurrency) && (a.currency === overseaCurrency || a.localCurrency === overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(new Date()) && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    },\r\n    async getBillDataExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re = [0, 1]\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency == localCurrency || a.currency == localCurrency)\r\n            && (a.currency == overseaCurrency || a.localCurrency == overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(valueDate)\r\n            && parseTime(valueDate) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    }\r\n    ,\r\n    async exchangeRateShow(bankCurrencyCode, chargeCurrencyCode) {\r\n      if (bankCurrencyCode === chargeCurrencyCode) {\r\n        return 1\r\n      }\r\n      let result = await this.getExchangeRate(bankCurrencyCode, chargeCurrencyCode, \"\")\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        if (id) {\r\n          let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n          if (staff) {\r\n            return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n          } else {\r\n            return \"\"\r\n          }\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    selectStaff(row) {\r\n      this.form.sqdRsStaffId = row.staff.staffId\r\n    },\r\n    checkSelectable(row) {\r\n      return row.isAccountConfirmed === \"1\"\r\n    },\r\n    handleDialogOpened() {\r\n      // 在弹出层打开时，自动聚焦姓名输入框\r\n      this.$nextTick(() => {\r\n        const treeSelectInput = this.$refs.treeSelect.getInputElement()\r\n        if (treeSelectInput) {\r\n          treeSelectInput.focus()\r\n        }\r\n      })\r\n    },\r\n    isRecievingOrPayingNormalizer(node) {\r\n      return {\r\n        id: node.value,\r\n        label: node.label\r\n      }\r\n    },\r\n    selectBankAccount(row) {\r\n      // this.form.sqdPaymentTitleCode = row.sqdBelongToCompanyCode\r\n    },\r\n    customHttpRequest(options) {\r\n      const formData = new FormData()\r\n      formData.append(\"file\", options.file)\r\n\r\n      request({\r\n        url: \"/system/bankrecord/uploadImg\",\r\n        method: \"post\",\r\n        data: formData\r\n      }).then(response => {\r\n        options.onSuccess(response, options.file)\r\n        console.log(response.url)\r\n        this.form.slipFile = response.url\r\n      }).catch(error => {\r\n        options.onError(error)\r\n      })\r\n    },\r\n    handleChange(file, fileList) {\r\n      const extension = file.name.substring(file.name.lastIndexOf(\".\"))\r\n      const newFileName = `${this.form.bankRecordNo}${extension}`\r\n      this.imageFile = new File([file.raw], newFileName, {type: file.type})\r\n    },\r\n    handleSuccess(response, file, fileList) {\r\n      this.form.slipFile = response.url\r\n    },\r\n    handleError(err, file, fileList) {\r\n      this.$message.error(\"Upload failed:\", err)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.table-btn-group {\r\n  display: flex;\r\n\r\n  .table-btn-left {\r\n    display: flex;\r\n    width: 100%;\r\n  }\r\n\r\n  .table-btn-right {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.total {\r\n  display: flex;\r\n  width: 60%;\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n::v-deep .el-input-number.is-without-controls .el-input__inner {\r\n  background-color: rgb(255, 242, 204) !important;\r\n}\r\n\r\n::v-deep .vue-treeselect__value-container {\r\n  display: block;\r\n}\r\n\r\n//固定el-table中行高度,不会被内容撑高\r\n::v-deep .cell {\r\n  overflow: hidden; /* 隐藏超出部分 */\r\n  white-space: nowrap; /* 禁止内容换行 */\r\n  text-overflow: ellipsis; /* 使用省略号表示超出的内容 */\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA+vBA,IAAAA,WAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,SAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,IAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,OAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,QAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,UAAA,GAAAb,OAAA;AACA,IAAAc,KAAA,GAAAd,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAe,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,WAAA,WAAAA,YAAA;IACA,KAAAC,SAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACAI,YAAA;MACAC,OAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACAC,aAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACA7B,wBAAA,GACA;UAAA8B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,cAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA/B,oBAAA,GACA;UAAA6B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,GAAA;MACAC,UAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,uBAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,aAAA;MACAC,OAAA;MACAC,mBAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAlE,UAAA,WAAAA,WAAAmE,CAAA;MACA,IAAAA,CAAA;QACA,KAAAxE,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACA;IACA,0CAAA0E,6BAAAD,CAAA;MACA,KAAAtB,IAAA,CAAAtB,qBAAA,OAAA8C,iBAAA,OAAAxB,IAAA,CAAA5B,wBAAA,EAAAiC,GAAA,MAAAL,IAAA,CAAA1B,uBAAA,EAAAmD,KAAA;IACA;IACA;IACA,yCAAAC,4BAAAJ,CAAA;MACA,KAAAtB,IAAA,CAAAtB,qBAAA,OAAA8C,iBAAA,OAAAxB,IAAA,CAAA5B,wBAAA,EAAAiC,GAAA,MAAAL,IAAA,CAAA1B,uBAAA,EAAAmD,KAAA;IACA;IACA;IACA,0CAAAE,6BAAAL,CAAA;MACA,KAAAtB,IAAA,CAAAlB,8BAAA,OAAA0C,iBAAA,OAAAxB,IAAA,CAAAtB,qBAAA,EAAAkD,QAAA,MAAA5B,IAAA,CAAApB,0BAAA,EAAAgD,QAAA,MAAA5B,IAAA,CAAAxB,wBAAA,EAAAiD,KAAA;IACA;IACA;IACA,sCAAAI,yBAAAP,CAAA;MACA,KAAAtB,IAAA,CAAArB,iBAAA,OAAA6C,iBAAA,OAAAxB,IAAA,CAAA3B,oBAAA,EAAAuD,QAAA,MAAA5B,IAAA,CAAAzB,mBAAA,EAAAkD,KAAA;IACA;IACA;IACA,qCAAAK,wBAAAR,CAAA;MACA,KAAAtB,IAAA,CAAArB,iBAAA,OAAA6C,iBAAA,OAAAxB,IAAA,CAAA3B,oBAAA,EAAAuD,QAAA,MAAA5B,IAAA,CAAAzB,mBAAA,EAAAkD,KAAA;IACA;IACA;IACA,sCAAAM,yBAAAT,CAAA;MACA,KAAAtB,IAAA,CAAAjB,0BAAA,OAAAyC,iBAAA,OAAAxB,IAAA,CAAArB,iBAAA,EAAAiD,QAAA,MAAA5B,IAAA,CAAAnB,sBAAA,EAAA+C,QAAA,MAAA5B,IAAA,CAAAvB,oBAAA,EAAAgD,KAAA;IACA;IAEA;IACA,uCAAAO,0BAAAV,CAAA;MACA,KAAAtB,IAAA,CAAAlB,8BAAA,OAAA0C,iBAAA,OAAAxB,IAAA,CAAAtB,qBAAA,EAAAkD,QAAA,MAAA5B,IAAA,CAAApB,0BAAA,EAAAgD,QAAA,MAAA5B,IAAA,CAAAxB,wBAAA,EAAAiD,KAAA;IACA;IACA;IACA,mCAAAQ,sBAAAX,CAAA;MACA,KAAAtB,IAAA,CAAAjB,0BAAA,OAAAyC,iBAAA,OAAAxB,IAAA,CAAArB,iBAAA,EAAAiD,QAAA,MAAA5B,IAAA,CAAAnB,sBAAA,EAAA+C,QAAA,MAAA5B,IAAA,CAAAvB,oBAAA,EAAAgD,KAAA;IACA;IAEA;IACA,4CAAAS,+BAAAZ,CAAA;MACA,KAAAtB,IAAA,CAAAlB,8BAAA,OAAA0C,iBAAA,OAAAxB,IAAA,CAAAtB,qBAAA,EAAAkD,QAAA,MAAA5B,IAAA,CAAApB,0BAAA,EAAAgD,QAAA,MAAA5B,IAAA,CAAAxB,wBAAA,EAAAiD,KAAA;IACA;IACA;IACA,wCAAAU,2BAAAb,CAAA;MACA,KAAAtB,IAAA,CAAAjB,0BAAA,OAAAyC,iBAAA,OAAAxB,IAAA,CAAArB,iBAAA,EAAAiD,QAAA,MAAA5B,IAAA,CAAAnB,sBAAA,EAAA+C,QAAA,MAAA5B,IAAA,CAAAvB,oBAAA,EAAAgD,KAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAlB,eAAA;MACA6B,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAC,KAAA;QACA;QACA,KAAAvC,IAAA,CAAApB,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA;;QAEA;QACA,KAAA+B,qBAAA;QAEA,IAAAhC,0BAAA;QACA,IAAAC,sBAAA;QAEA,IAAA+B,qBAAA;QACA4B,OAAA,CAAAC,GAAA,CAAAJ,MAAA;QACAA,MAAA,CAAAK,GAAA,WAAAC,IAAA;UACA;UACAA,IAAA,CAAAC,qBAAA,OAAApB,iBAAA,EAAAmB,IAAA,CAAAE,uBAAA,EAAAC,MAAA,CAAAH,IAAA,CAAAI,YAAA,EAAAtB,KAAA;;UAEA;UACA7C,0BAAA,QAAA4C,iBAAA,EAAAmB,IAAA,CAAAE,uBAAA,EAAApB,KAAA;UACA;UACA5C,sBAAA,QAAA2C,iBAAA,EAAAmB,IAAA,CAAAE,uBAAA,EAAApB,KAAA;;UAEA;UACAb,qBAAA,OAAAY,iBAAA,EAAAmB,IAAA,CAAAK,oBAAA,EAAA3C,GAAA,CAAAO,qBAAA,EAAAa,KAAA;;UAEA;UACAc,KAAA,CAAA5B,cAAA;UAEA,IAAAa,iBAAA,EAAAmB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,EAAA1B,KAAA,KAAAkB,IAAA,CAAAC,qBAAA,GAAAD,IAAA,CAAA3D,cAAA,SAAA2D,IAAA,CAAAC,qBAAA,OAAAD,IAAA,CAAA3D,cAAA,SAAA2D,IAAA,CAAA3D,cAAA;QACA;;QAEA;QACA,KAAAgB,IAAA,CAAApB,0BAAA,GAAAA,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA,GAAAA,sBAAA;QAEA,KAAA+B,qBAAA,GAAAA,qBAAA;MAEA;MACAwC,IAAA;IACA;IACA5C,kBAAA;MACA4B,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QACA;QACA,KAAAtC,IAAA,CAAApB,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA;;QAEA;QACA,KAAA+B,qBAAA;QAEA,IAAAhC,0BAAA;QACA,IAAAC,sBAAA;QAEA,IAAA+B,qBAAA;QACAyB,MAAA,CAAAK,GAAA,WAAAC,IAAA;UACA;UACA/D,0BAAA,QAAA4C,iBAAA,EAAAmB,IAAA,CAAAU,oBAAA,EAAA5B,KAAA;UACA;UACA5C,sBAAA,QAAA2C,iBAAA,EAAAmB,IAAA,CAAAU,oBAAA,EAAA5B,KAAA;;UAEA;UACAb,qBAAA,OAAAY,iBAAA,EAAAmB,IAAA,CAAAK,oBAAA,EAAA3C,GAAA,CAAAO,qBAAA,EAAAa,KAAA;QAEA;;QAEA;QACA,KAAAzB,IAAA,CAAApB,0BAAA,GAAAA,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA,GAAAA,sBAAA;QAEA,KAAA+B,qBAAA,GAAAA,qBAAA;MAEA;MACAwC,IAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,IAAAC,oBAAA,IAAAC,IAAA,WAAAC,QAAA;MACAH,MAAA,CAAAnH,QAAA,GAAAsH,QAAA,CAAAvH,IAAA;IACA;IACA,KAAAwH,OAAA;IACA,KAAAA,OAAA;IACA,KAAAC,SAAA;IACA,KAAA1H,SAAA;IACA,KAAA2H,eAAA,+BAAAC,GAAA;MACAtB,OAAA,CAAAC,GAAA,CAAAqB,GAAA;IACA;EACA;EACAC,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAhE,IAAA,CAAA5B,wBAAA,QAAA4B,IAAA,CAAA1B,uBAAA,QAAA0B,IAAA,CAAAxB,wBAAA;IACA;IACAyF,QAAA,WAAAA,SAAA;MACA,YAAAjE,IAAA,CAAA3B,oBAAA,QAAA2B,IAAA,CAAAzB,mBAAA,QAAAyB,IAAA,CAAAvB,oBAAA;IACA;IACAyF,QAAA,WAAAA,SAAA;MACA,YAAAlE,IAAA,CAAAT,kBAAA;IACA;IACA4E,mBAAA,WAAAA,oBAAA;MACA,YAAAnE,IAAA,CAAAoE,aAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAZ,OAAA;UACA;QACA;UACA;MACA;IACA;IACAa,SAAA,EAAAA,eAAA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAA1E,IAAA,CAAAoE,aAAA;QACA,KAAApE,IAAA,CAAAoE,aAAA;QACA,IAAAO,4BAAA,OAAA3E,IAAA,EAAAyD,IAAA,WAAAC,QAAA;UACAgB,MAAA,CAAAE,QAAA,CAAAC,OAAA;QACA;MACA;QACA,KAAA7E,IAAA,CAAAoE,aAAA;QACA,IAAAO,4BAAA,OAAA3E,IAAA,EAAAyD,IAAA,WAAAC,QAAA;UACAgB,MAAA,CAAAE,QAAA,CAAAC,OAAA;QACA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAA5I,IAAA,QAAAqE,kBAAA,CAAAkC,GAAA,WAAAC,IAAA;QACAA,IAAA,CAAAqC,YAAA,GAAAD,MAAA,CAAA/E,IAAA,CAAAgF,YAAA;QACA,OAAArC,IAAA;MACA;MACA,IAAAsC,4BAAA,EAAA9I,IAAA,EAAAsH,IAAA,WAAAC,QAAA;QACAqB,MAAA,CAAAH,QAAA,CAAAC,OAAA;QACAE,MAAA,CAAAG,iBAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA3I,YAAA,CAAA4I,OAAA,WAAAC,GAAA;QACA;QACAF,MAAA,CAAAG,KAAA,CAAAC,aAAA,CAAAC,kBAAA,CAAAH,GAAA;QACA;MACA;IACA;IACAI,aAAA,WAAAA,cAAA,GACA;IACAC,UAAA,WAAAA,WAAA,GACA;IACAC,aAAA,WAAAA,cAAA,GACA;IACAC,KAAA,WAAAA,MAAA,GACA;IACArE,QAAA,EAAAA,iBAAA;IACA0D,iBAAA,WAAAA,kBAAA;MAAA,IAAAY,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAAxI,aAAA;cACAwI,MAAA,CAAAjF,aAAA;cAEA,IAAA4F,gCAAA;gBACA3F,OAAA,EAAAgF,MAAA,CAAA9F,IAAA,CAAAJ,YAAA;gBACAF,kBAAA,EAAAoG,MAAA,CAAA9F,IAAA,CAAAN,kBAAA;gBACAsF,YAAA,EAAAc,MAAA,CAAA9F,IAAA,CAAAgF;cACA,GAAAvB,IAAA,WAAAC,QAAA;gBACAoC,MAAA,CAAAxI,aAAA,GAAAoG,QAAA,CAAAgD,IAAA;gBAEAZ,MAAA,CAAAjF,aAAA;gBACAiF,MAAA,CAAAxF,UAAA;gBAEAwF,MAAA,CAAArF,WAAA;gBACAqF,MAAA,CAAAa,SAAA;kBACAb,MAAA,CAAAxI,aAAA,CAAAoF,GAAA,WAAAC,IAAA;oBACAmD,MAAA,CAAArF,WAAA,OAAAe,iBAAA,EAAAmB,IAAA,CAAAiE,cAAA,EAAAvG,GAAA,CAAAyF,MAAA,CAAArF,WAAA,EAAAgB,KAAA;oBACA,IAAAkB,IAAA,CAAAqC,YAAA,KAAAc,MAAA,CAAA9F,IAAA,CAAAgF,YAAA;sBACAc,MAAA,CAAAP,KAAA,CAAAC,aAAA,CAAAC,kBAAA,CAAA9C,IAAA;oBACA;kBACA;gBACA;gBAEAmD,MAAA,CAAAxI,aAAA,CAAAuJ,IAAA,WAAAC,CAAA,EAAAC,CAAA;kBACA,IAAAD,CAAA,CAAA9B,YAAA,KAAAc,MAAA,CAAA9F,IAAA,CAAAgF,YAAA,IAAA+B,CAAA,CAAA/B,YAAA,KAAAc,MAAA,CAAA9F,IAAA,CAAAgF,YAAA;oBACA;kBACA,WAAA8B,CAAA,CAAA9B,YAAA,KAAAc,MAAA,CAAA9F,IAAA,CAAAgF,YAAA,IAAA+B,CAAA,CAAA/B,YAAA,KAAAc,MAAA,CAAA9F,IAAA,CAAAgF,YAAA;oBACA;kBACA;oBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAsB,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IACA;IACAc,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,SAAAlH,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAAmH,QAAA;QACA,KAAAnH,IAAA,CAAAoH,UAAA;MACA;QACA,KAAApH,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAAmH,QAAA,QAAA7K,MAAA,CAAAC,KAAA,CAAA8K,IAAA,CAAAC,GAAA;QACA,KAAAtH,IAAA,CAAAoH,UAAA,OAAAG,eAAA,IAAAC,MAAA;MACA;MAEA,IAAA7C,4BAAA,OAAA3E,IAAA,EAAAyD,IAAA,WAAAC,QAAA;QACAwD,MAAA,CAAAtC,QAAA,CAAAC,OAAA;MACA;IACA;IACA,sBACAlB,OAAA,WAAAA,QAAA;MAAA,IAAA8D,MAAA;MACA,KAAA1K,OAAA;MACA,KAAAU,WAAA,CAAAQ,YAAA;MACA,IAAAyJ,0BAAA,OAAAjK,WAAA,EAAAgG,IAAA,WAAAC,QAAA;QACA+D,MAAA,CAAApK,cAAA,GAAAqG,QAAA,CAAAgD,IAAA;QACAe,MAAA,CAAArK,KAAA,GAAAsG,QAAA,CAAAtG,KAAA;QACAqK,MAAA,CAAA1K,OAAA;MACA;IACA;IACA;IACA4K,MAAA,WAAAA,OAAA;MACA,KAAAnK,IAAA;MACA,KAAAoK,KAAA;IACA;IAEA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA5H,IAAA;QACAgF,YAAA;QACApH,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACA0I,SAAA;QACAzI,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACA+H,UAAA;MACA;MACA,KAAAhH,OAAA;MACA,KAAAF,qBAAA;MACA,KAAAmH,SAAA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAvK,WAAA,CAAAC,OAAA;MACA,KAAAiG,OAAA;IACA;IAEA,aACAsE,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IAEAE,kBAAA,WAAAA,mBAAA5C,GAAA;MAAA,IAAA6C,MAAA;MACA,IAAAC,IAAA,GAAA9C,GAAA,CAAA+C,MAAA;MACA,KAAAC,QAAA,WAAAF,IAAA,SAAA3E,IAAA;QACA,WAAA8E,wBAAA,EAAAjD,GAAA,CAAAN,YAAA,EAAAM,GAAA,CAAA+C,MAAA;MACA,GAAA5E,IAAA;QACA0E,MAAA,CAAAK,MAAA,CAAAC,UAAA,CAAAL,IAAA;MACA,GAAAM,KAAA;QACApD,GAAA,CAAA+C,MAAA,GAAA/C,GAAA,CAAA+C,MAAA;MACA;IACA;IAEA;IACAM,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAA5L,MAAA,GAAA2L,SAAA,CAAAE,MAAA;MACA,KAAA5L,QAAA,IAAA0L,SAAA,CAAAE,MAAA;MAEA,KAAArI,WAAA;MACA,KAAAC,uBAAA;MACA,KAAApD,aAAA,CAAAoF,GAAA,WAAAC,IAAA;QACA,IAAAiG,SAAA,CAAAG,QAAA,CAAApG,IAAA;UACAkG,OAAA,CAAAnI,uBAAA,OAAAc,iBAAA,EAAAmB,IAAA,CAAAiE,cAAA,EAAAvG,GAAA,CAAAwI,OAAA,CAAAnI,uBAAA,EAAAe,KAAA;QACA;QACAoH,OAAA,CAAApI,WAAA,OAAAe,iBAAA,EAAAmB,IAAA,CAAAiE,cAAA,EAAAvG,GAAA,CAAAwI,OAAA,CAAApI,WAAA,EAAAgB,KAAA;MACA;MAEA,KAAAjB,kBAAA,GAAAoI,SAAA;;MAEA;IAEA;IACAI,QAAA,WAAAA,SAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAC,YAAA;QACA,KAAAD,IAAA,GAAAA,IAAA;MACA;QACA,KAAArE,QAAA,CAAAuE,IAAA;MACA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAxB,KAAA;MACA,KAAAtH,UAAA;MACA,KAAA9C,IAAA;MACA,KAAAD,KAAA;MACA,KAAA8C,GAAA;MACA,KAAAL,IAAA,CAAApC,mBAAA;MACA,KAAAoC,IAAA,CAAA8H,UAAA;MACA,KAAA9H,IAAA,CAAA/B,YAAA;IACA;IACA,aACAoL,YAAA,WAAAA,aAAA/D,GAAA;MAAA,IAAAgE,OAAA;MACA,KAAAjJ,GAAA;MACA,KAAAuH,KAAA;MACA,IAAA5C,YAAA,GAAAM,GAAA,CAAAN,YAAA,SAAAhI,GAAA;MACA,IAAAuM,yBAAA,EAAAvE,YAAA,EAAAvB,IAAA,WAAAC,QAAA;QACA4F,OAAA,CAAAtJ,IAAA,GAAA0D,QAAA,CAAAvH,IAAA;QACAmN,OAAA,CAAAtJ,IAAA,CAAA8H,UAAA;QACAwB,OAAA,CAAA9L,IAAA;QACA8L,OAAA,CAAA/L,KAAA;QACA+L,OAAA,CAAApI,WAAA,IAAAwC,QAAA,CAAAxC,WAAA;QAEA,IAAAoI,OAAA,CAAAtJ,IAAA,CAAAT,kBAAA,aAAAmE,QAAA,CAAAvH,IAAA,CAAAyB,mBAAA,gBAAA4D,iBAAA,EAAAkC,QAAA,CAAAvH,IAAA,CAAA2C,8BAAA,EAAA2C,KAAA,UAAAiC,QAAA,CAAAvH,IAAA,CAAAyB,mBAAA,gBAAA4D,iBAAA,EAAAkC,QAAA,CAAAvH,IAAA,CAAA4C,0BAAA,EAAA0C,KAAA;UACA6H,OAAA,CAAApE,iBAAA;QACA;MACA;IACA;IAEA,WACAsE,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAlE,KAAA,SAAAmE,QAAA;QAAA,IAAAC,IAAA,OAAA5D,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0D,SAAAC,KAAA;UAAA,WAAA5D,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0D,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAAxD,IAAA,GAAAwD,SAAA,CAAAvD,IAAA;cAAA;gBACA,IAAAqD,KAAA;kBACA;kBACAJ,OAAA,CAAAO,iBAAA;kBACA,IAAAP,OAAA,CAAAzJ,IAAA,CAAAgF,YAAA;oBACA,IAAAL,4BAAA,EAAA8E,OAAA,CAAAzJ,IAAA,EAAAyD,IAAA,WAAAC,QAAA;sBACA+F,OAAA,CAAAjB,MAAA,CAAAC,UAAA;sBACA;sBACAgB,OAAA,CAAA9F,OAAA;oBACA;kBACA;oBACA,IAAAsG,yBAAA,EAAAR,OAAA,CAAAzJ,IAAA,EAAAyD,IAAA,WAAAC,QAAA;sBACA+F,OAAA,CAAAzJ,IAAA,GAAA0D,QAAA,CAAAvH,IAAA;sBACAsN,OAAA,CAAAjB,MAAA,CAAAC,UAAA;sBACA;sBACAgB,OAAA,CAAA9F,OAAA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAAoG,SAAA,CAAA/C,IAAA;YAAA;UAAA,GAAA4C,QAAA;QAAA,CACA;QAAA,iBAAAM,EAAA;UAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAjI,OAAA,CAAAC,GAAA,CAAA6H,OAAA,CAAAlJ,SAAA;QACAkJ,OAAA,CAAAI,iBAAA;UACAC,IAAA,EAAAL,OAAA,CAAAlJ,SAAA;UACAwJ,SAAA,WAAAA,UAAAlH,QAAA;YACA4G,OAAA,CAAAO,aAAA,CAAAnH,QAAA;YACA8G,OAAA,CAAA9G,QAAA;UACA;UACAoH,OAAA,WAAAA,QAAAC,KAAA;YACAT,OAAA,CAAAU,WAAA,CAAAD,KAAA;YACAN,MAAA,CAAAM,KAAA;UACA;QACA;MACA;IACA;IACAf,iBAAA,WAAAA,kBAAA;MACA,SAAAhK,IAAA,CAAApC,mBAAA;QACA;QACA,KAAAoC,IAAA,CAAA3B,oBAAA;QACA,KAAA2B,IAAA,CAAAzB,mBAAA;QACA,KAAAyB,IAAA,CAAArB,iBAAA;QACA,KAAAqB,IAAA,CAAAnB,sBAAA;QACA,KAAAmB,IAAA,CAAAvB,oBAAA;QACA,KAAAuB,IAAA,CAAAjB,0BAAA;QACA;QACA,KAAAiB,IAAA,CAAAlB,8BAAA,UAAAkB,IAAA,CAAAtB,qBAAA,QAAAsB,IAAA,CAAAhB,cAAA,aAAAgB,IAAA,CAAAlB,8BAAA,cAAAkB,IAAA,CAAAhB,cAAA,YAAAgB,IAAA,CAAAhB,cAAA;MACA;QACA;QACA,KAAAgB,IAAA,CAAA5B,wBAAA;QACA,KAAA4B,IAAA,CAAA1B,uBAAA;QACA,KAAA0B,IAAA,CAAAtB,qBAAA;QACA,KAAAsB,IAAA,CAAApB,0BAAA;QACA,KAAAoB,IAAA,CAAAxB,wBAAA;QACA,KAAAwB,IAAA,CAAAlB,8BAAA;QACA;QACA,KAAAkB,IAAA,CAAArB,iBAAA,UAAAqB,IAAA,CAAAjB,0BAAA,QAAAiB,IAAA,CAAAhB,cAAA,aAAAgB,IAAA,CAAAjB,0BAAA,cAAAiB,IAAA,CAAAhB,cAAA,YAAAgB,IAAA,CAAAhB,cAAA;MACA;IACA;IACA,aACAiM,YAAA,WAAAA,aAAA3F,GAAA;MAAA,IAAA4F,OAAA;MACA,IAAAC,aAAA,GAAA7F,GAAA,CAAAN,YAAA,SAAAhI,GAAA;MACA,KAAAsL,QAAA,4BAAA6C,aAAA,cAAA1H,IAAA;QACA,WAAA2H,yBAAA,EAAAD,aAAA;MACA,GAAA1H,IAAA;QACAyH,OAAA,CAAAvH,OAAA;QACAuH,OAAA,CAAA1C,MAAA,CAAAC,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IAEA,aACA2C,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iCAAAC,cAAA,CAAAvF,OAAA,MACA,KAAAvI,WAAA,iBAAA+N,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAA/C,MAAA;QACA,OAAA8C,IAAA,CAAAC,QAAA;MACA;MACA,IAAAC,CAAA;MACA,IAAAF,IAAA,CAAAG,KAAA;QACA,IAAAH,IAAA,CAAAG,KAAA,CAAAC,oBAAA,YAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;UACA,IAAAL,IAAA,CAAAM,IAAA,CAAAC,aAAA;YACAL,CAAA,GAAAF,IAAA,CAAAM,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAM,IAAA,CAAAC,aAAA;UACA;YACAL,CAAA,GAAAF,IAAA,CAAAU,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAU,IAAA,CAAAC,aAAA;UACA;QACA;UACAT,CAAA,GAAAF,IAAA,CAAAG,KAAA,CAAAS,SAAA,SAAAZ,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA,SAAAL,IAAA,CAAAG,KAAA,CAAAU,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAL,IAAA,CAAAc,MAAA;QACA;UACAC,EAAA,EAAAf,IAAA,CAAAc,MAAA;UACAE,KAAA,EAAAd,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAgB,UAAA,EAAAjB,IAAA,CAAA9K,OAAA,YAAA8K,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;QACA;UACAH,EAAA,EAAAf,IAAA,CAAAmB,MAAA;UACAH,KAAA,EAAAd,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAgB,UAAA,EAAAjB,IAAA,CAAA9K,OAAA,YAAA8K,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;IACA;IAEAlJ,SAAA,WAAAA,UAAA;MAAA,IAAAoJ,OAAA;MACA,SAAA1Q,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8Q,SAAA,CAAAnE,MAAA,cAAAxM,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA+Q,SAAA,CAAAD,SAAA;QACAE,cAAA,CAAAC,QAAA,iBAAA3J,IAAA;UACAuJ,OAAA,CAAArQ,UAAA,GAAAqQ,OAAA,CAAA1Q,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8Q,SAAA;QACA;MACA;QACA,KAAAtQ,UAAA,QAAAL,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8Q,SAAA;MACA;IACA;IACA/Q,SAAA,WAAAA,UAAA;MAAA,IAAAmR,OAAA;MACA,SAAA/Q,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAmR,cAAA,CAAAxE,MAAA,cAAAxM,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA+Q,SAAA,CAAAI,cAAA;QACAH,cAAA,CAAAC,QAAA,sBAAA3J,IAAA;UACA4J,OAAA,CAAAzQ,SAAA,GAAAyQ,OAAA,CAAA/Q,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAmR,cAAA;QACA;MACA;QACA,KAAA1Q,SAAA,QAAAN,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAmR,cAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAAjI,GAAA;MACA,KAAA+D,YAAA,CAAA/D,GAAA;IACA;IAEAkI,aAAA,WAAAA,cAAAC,OAAA;MACA,KAAAzN,IAAA,CAAAjC,iBAAA,GAAA0P,OAAA,CAAAC,SAAA;MACA,KAAA1N,IAAA,CAAAhC,2BAAA,GAAAyP,OAAA,CAAAE,gBAAA;MACA,KAAA1M,WAAA;IACA;IAEA4C,eAAA,WAAAA,gBAAA+J,eAAA,EAAAC,aAAA,EAAAC,SAAA;MAAA,WAAA/H,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA6H,SAAA;QAAA,IAAAC,EAAA,EAAAtK,QAAA,EAAAuK,SAAA,EAAAC,KAAA,EAAApH,CAAA;QAAA,WAAAb,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA+H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7H,IAAA,GAAA6H,SAAA,CAAA5H,IAAA;YAAA;cAAA4H,SAAA,CAAA5H,IAAA;cAAA,OAEA,IAAA6H,oCAAA;YAAA;cAAA3K,QAAA,GAAA0K,SAAA,CAAAE,IAAA;cAAA,MACAV,eAAA,aAAAC,aAAA;gBAAAO,SAAA,CAAA5H,IAAA;gBAAA;cAAA;cAAAyH,SAAA,OAAAM,2BAAA,CAAAvI,OAAA,EACAtC,QAAA,CAAAvH,IAAA;cAAA;gBAAA,KAAA8R,SAAA,CAAAO,CAAA,MAAAN,KAAA,GAAAD,SAAA,CAAA3M,CAAA,IAAAmN,IAAA;kBAAA3H,CAAA,GAAAoH,KAAA,CAAAzM,KAAA;kBACA,KAAAqF,CAAA,CAAA+G,aAAA,KAAAA,aAAA,IAAA/G,CAAA,CAAAtF,QAAA,KAAAqM,aAAA,MAAA/G,CAAA,CAAAtF,QAAA,KAAAoM,eAAA,IAAA9G,CAAA,CAAA+G,aAAA,KAAAD,eAAA,KACA,IAAApJ,eAAA,EAAAsC,CAAA,CAAA4H,SAAA,SAAAlK,eAAA,MAAAiH,IAAA,WAAAjH,eAAA,MAAAiH,IAAA,WAAAjH,eAAA,EAAAsC,CAAA,CAAA6H,OAAA;oBACA;oBACA,IAAAf,eAAA,KAAA9G,CAAA,CAAA8G,eAAA,IAAAC,aAAA,KAAA/G,CAAA,CAAA+G,aAAA;sBACAG,EAAA,WAAAxM,iBAAA,EAAAsF,CAAA,CAAA8H,OAAA,EAAA9L,MAAA,CAAAgE,CAAA,CAAA+H,IAAA,EAAApN,KAAA;oBACA;sBACAuM,EAAA,WAAAxM,iBAAA,EAAAsF,CAAA,CAAAgI,QAAA,EAAAhM,MAAA,CAAAgE,CAAA,CAAA+H,IAAA,EAAApN,KAAA;oBACA;kBACA;gBACA;cAAA,SAAAsN,GAAA;gBAAAd,SAAA,CAAAe,CAAA,CAAAD,GAAA;cAAA;gBAAAd,SAAA,CAAAgB,CAAA;cAAA;cAAA,OAAAb,SAAA,CAAAc,MAAA,WACAlB,EAAA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAApH,IAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA;IAEA;IACAoB,uBAAA,WAAAA,wBAAAvB,eAAA,EAAAC,aAAA,EAAAC,SAAA;MAAA,WAAA/H,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAkJ,SAAA;QAAA,IAAApB,EAAA,EAAAtK,QAAA,EAAA2L,UAAA,EAAAC,MAAA,EAAAxI,CAAA;QAAA,WAAAb,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAmJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjJ,IAAA,GAAAiJ,SAAA,CAAAhJ,IAAA;YAAA;cACAwH,EAAA;cAAAwB,SAAA,CAAAhJ,IAAA;cAAA,OACA,IAAA6H,oCAAA;YAAA;cAAA3K,QAAA,GAAA8L,SAAA,CAAAlB,IAAA;cAAA,MACAV,eAAA,aAAAC,aAAA;gBAAA2B,SAAA,CAAAhJ,IAAA;gBAAA;cAAA;cAAA6I,UAAA,OAAAd,2BAAA,CAAAvI,OAAA,EACAtC,QAAA,CAAAvH,IAAA;cAAA;gBAAA,KAAAkT,UAAA,CAAAb,CAAA,MAAAc,MAAA,GAAAD,UAAA,CAAA/N,CAAA,IAAAmN,IAAA;kBAAA3H,CAAA,GAAAwI,MAAA,CAAA7N,KAAA;kBACA,KAAAqF,CAAA,CAAA+G,aAAA,IAAAA,aAAA,IAAA/G,CAAA,CAAAtF,QAAA,IAAAqM,aAAA,MACA/G,CAAA,CAAAtF,QAAA,IAAAoM,eAAA,IAAA9G,CAAA,CAAA+G,aAAA,IAAAD,eAAA,KACA,IAAApJ,eAAA,EAAAsC,CAAA,CAAA4H,SAAA,SAAAlK,eAAA,EAAAsJ,SAAA,KACA,IAAAtJ,eAAA,EAAAsJ,SAAA,SAAAtJ,eAAA,EAAAsC,CAAA,CAAA6H,OAAA;oBACA;oBACA,IAAAf,eAAA,KAAA9G,CAAA,CAAA8G,eAAA,IAAAC,aAAA,KAAA/G,CAAA,CAAA+G,aAAA;sBACAG,EAAA,WAAAxM,iBAAA,EAAAsF,CAAA,CAAA8H,OAAA,EAAA9L,MAAA,CAAAgE,CAAA,CAAA+H,IAAA,EAAApN,KAAA;oBACA;sBACAuM,EAAA,WAAAxM,iBAAA,EAAAsF,CAAA,CAAAgI,QAAA,EAAAhM,MAAA,CAAAgE,CAAA,CAAA+H,IAAA,EAAApN,KAAA;oBACA;kBACA;gBACA;cAAA,SAAAsN,GAAA;gBAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;cAAA;gBAAAM,UAAA,CAAAJ,CAAA;cAAA;cAAA,OAAAO,SAAA,CAAAN,MAAA,WACAlB,EAAA;YAAA;YAAA;cAAA,OAAAwB,SAAA,CAAAxI,IAAA;UAAA;QAAA,GAAAoI,QAAA;MAAA;IAEA;IAEAK,gBAAA,WAAAA,iBAAAtR,gBAAA,EAAAuR,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA5J,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0J,SAAA;QAAA,IAAAC,MAAA;QAAA,WAAA5J,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxJ,IAAA,GAAAwJ,SAAA,CAAAvJ,IAAA;YAAA;cAAA,MACArI,gBAAA,KAAAuR,kBAAA;gBAAAK,SAAA,CAAAvJ,IAAA;gBAAA;cAAA;cAAA,OAAAuJ,SAAA,CAAAb,MAAA,WACA;YAAA;cAAAa,SAAA,CAAAvJ,IAAA;cAAA,OAEAmJ,OAAA,CAAA9L,eAAA,CAAA1F,gBAAA,EAAAuR,kBAAA;YAAA;cAAAG,MAAA,GAAAE,SAAA,CAAAzB,IAAA;YAAA;YAAA;cAAA,OAAAyB,SAAA,CAAA/I,IAAA;UAAA;QAAA,GAAA4I,QAAA;MAAA;IACA;IAEAI,OAAA,WAAAA,QAAArD,EAAA;MACA,IAAAA,EAAA;QACA,IAAAA,EAAA;UACA,IAAAZ,KAAA,QAAAzP,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAmR,cAAA,CAAA2C,MAAA,WAAAC,OAAA;YAAA,OAAAA,OAAA,CAAApP,OAAA,IAAA6L,EAAA;UAAA;UACA,IAAAZ,KAAA;YACA,OAAAA,KAAA,CAAAC,oBAAA,GAAAD,KAAA,CAAAE,oBAAA,GAAAF,KAAA,CAAAoE,cAAA;UACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA9K,GAAA;MACA,KAAAtF,IAAA,CAAAJ,YAAA,GAAA0F,GAAA,CAAAyG,KAAA,CAAAjL,OAAA;IACA;IACAuP,eAAA,WAAAA,gBAAA/K,GAAA;MACA,OAAAA,GAAA,CAAAgL,kBAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAA7J,SAAA;QACA,IAAA8J,eAAA,GAAAD,OAAA,CAAAjL,KAAA,CAAAmL,UAAA,CAAAC,eAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,KAAA;QACA;MACA;IACA;IACAC,6BAAA,WAAAA,8BAAAjF,IAAA;MACA;QACAe,EAAA,EAAAf,IAAA,CAAAnK,KAAA;QACAmL,KAAA,EAAAhB,IAAA,CAAAgB;MACA;IACA;IACAkE,iBAAA,WAAAA,kBAAAxL,GAAA;MACA;IAAA,CACA;IACAoF,iBAAA,WAAAA,kBAAAqG,OAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAAJ,OAAA,CAAApG,IAAA;MAEA,IAAAyG,gBAAA;QACAC,GAAA;QACAC,MAAA;QACAnV,IAAA,EAAA8U;MACA,GAAAxN,IAAA,WAAAC,QAAA;QACAqN,OAAA,CAAAnG,SAAA,CAAAlH,QAAA,EAAAqN,OAAA,CAAApG,IAAA;QACAnI,OAAA,CAAAC,GAAA,CAAAiB,QAAA,CAAA2N,GAAA;QACAL,OAAA,CAAAhR,IAAA,CAAAuR,QAAA,GAAA7N,QAAA,CAAA2N,GAAA;MACA,GAAA3I,KAAA,WAAAqC,KAAA;QACAgG,OAAA,CAAAjG,OAAA,CAAAC,KAAA;MACA;IACA;IACAyG,YAAA,WAAAA,aAAA7G,IAAA,EAAA8G,QAAA;MACA,IAAAC,SAAA,GAAA/G,IAAA,CAAA9O,IAAA,CAAA8V,SAAA,CAAAhH,IAAA,CAAA9O,IAAA,CAAA+V,WAAA;MACA,IAAAC,WAAA,MAAArG,MAAA,MAAAxL,IAAA,CAAA8R,YAAA,EAAAtG,MAAA,CAAAkG,SAAA;MACA,KAAAtQ,SAAA,OAAA2Q,IAAA,EAAApH,IAAA,CAAAqH,GAAA,GAAAH,WAAA;QAAAtN,IAAA,EAAAoG,IAAA,CAAApG;MAAA;IACA;IACAsG,aAAA,WAAAA,cAAAnH,QAAA,EAAAiH,IAAA,EAAA8G,QAAA;MACA,KAAAzR,IAAA,CAAAuR,QAAA,GAAA7N,QAAA,CAAA2N,GAAA;IACA;IACArG,WAAA,WAAAA,YAAA+D,GAAA,EAAApE,IAAA,EAAA8G,QAAA;MACA,KAAA7M,QAAA,CAAAmG,KAAA,mBAAAgE,GAAA;IACA;EACA;AACA;AAAAkD,OAAA,CAAAjM,OAAA,GAAAkM,QAAA"}]}