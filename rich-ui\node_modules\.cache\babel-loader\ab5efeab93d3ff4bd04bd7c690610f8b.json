{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\monitor\\job.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\monitor\\job.js", "mtime": 1678688095218}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listJob", "query", "request", "url", "method", "params", "get<PERSON>ob", "jobId", "addJob", "data", "updateJob", "<PERSON><PERSON><PERSON>", "changeJobStatus", "status", "runJob", "jobGroup"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/monitor/job.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询定时任务调度列表\r\nexport function listJob(query) {\r\n  return request({\r\n    url: '/monitor/job/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询定时任务调度详细\r\nexport function getJob(jobId) {\r\n  return request({\r\n    url: '/monitor/job/' + jobId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增定时任务调度\r\nexport function addJob(data) {\r\n  return request({\r\n    url: '/monitor/job',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改定时任务调度\r\nexport function updateJob(data) {\r\n  return request({\r\n    url: '/monitor/job',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除定时任务调度\r\nexport function delJob(jobId) {\r\n  return request({\r\n    url: '/monitor/job/' + jobId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 任务状态修改\r\nexport function changeJobStatus(jobId, status) {\r\n  const data = {\r\n    jobId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/monitor/job/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n\r\n// 定时任务立即执行一次\r\nexport function runJob(jobId, jobGroup) {\r\n  const data = {\r\n    jobId,\r\n    jobGroup\r\n  }\r\n  return request({\r\n    url: '/monitor/job/run',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,MAAMA,CAACC,KAAK,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,KAAK;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,MAAMA,CAACC,IAAI,EAAE;EAC3B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,SAASA,CAACD,IAAI,EAAE;EAC9B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,MAAMA,CAACJ,KAAK,EAAE;EAC5B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,KAAK;IAC5BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,eAAeA,CAACL,KAAK,EAAEM,MAAM,EAAE;EAC7C,IAAMJ,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAGA;AACO,SAASK,MAAMA,CAACP,KAAK,EAAEQ,QAAQ,EAAE;EACtC,IAAMN,IAAI,GAAG;IACXF,KAAK,EAALA,KAAK;IACLQ,QAAQ,EAARA;EACF,CAAC;EACD,OAAO,IAAAb,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}