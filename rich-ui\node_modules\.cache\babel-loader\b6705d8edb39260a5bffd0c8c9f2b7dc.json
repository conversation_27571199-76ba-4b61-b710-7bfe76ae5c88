{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\index.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_bankrecord", "require", "_index", "_interopRequireDefault", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_store", "_currency", "_rsCharge", "_exchangerate", "_rich", "_log", "_moment", "_request", "_imgPreview", "_auth", "_rct", "_index2", "_bankRecordFieldLabelMap", "_reimburse", "_index3", "_bankRecordSearchFields", "name", "components", "DynamicSearch", "DataAggregatorBackGround", "ImgPreview", "Treeselect", "CompanySelect", "data", "bankRecordSearchFields", "size", "$store", "state", "app", "fieldLabelMap", "bankRecordFieldLabelMap", "writeOffList", "reimburseList", "salesId", "belongList", "staffList", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "bankrecordList", "title", "open", "queryParams", "params", "pageNum", "pageSize", "isRecievingOrPaying", "sqdPaymentTitleCode", "bankAccountCode", "clearingCompanyId", "sqdClearingCompanyShortname", "chargeTypeId", "chargeDescription", "bankCurrencyCode", "actualBankRecievedAmount", "actualBankPaidAmount", "bankRecievedHandlingFee", "bankPaidHandlingFee", "bankRecievedExchangeLost", "bankPaidExchangeLost", "sqdBillRecievedAmount", "sqdBillPaidAmount", "billRecievedWriteoffAmount", "billPaidWriteoffAmount", "sqdBillRecievedWriteoffBalance", "sqdBillPaidWriteoffBalance", "writeoffStatus", "bankRecordTime", "paymentTypeCode", "voucherNo", "bankRecordRemark", "bankRecordByStaffId", "bankRecordUpdateTime", "isBankRecordLocked", "isWriteoffLocked", "sqdChargeIdList", "sqdRaletiveRctList", "sqdRaletiveInvoiceList", "sqdRsStaffId", "writeoffRemark", "writeoffStaffId", "writeoffTime", "timeArr", "statisticsList", "form", "searchAble", "writeoffType", "rules", "required", "message", "trigger", "statisticsOpen", "openAggregator", "totalRecievedUSD", "totalRecievedRMB", "totalPaidUSD", "totalPaidRMB", "exchangeRate", "paymentTitleBalances", "timeStatistics", "add", "upload", "isUploading", "updateSupport", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "showDetail", "selected<PERSON><PERSON>ges", "selectedReimburses", "totalAmount", "selectedAmount", "selectedBalanceAmount", "selectedBalanceAmountRMB", "selectedBalanceAmountUSD", "loadingCharge", "staffId", "alreadyWriteoffList", "turnBackWriteoffList", "showCompany", "companyList", "bankSlipPreview", "imageFile", "hedgingData", "exchangeRateList", "aggregatorBankRecordList", "watch", "n", "formChargeTypeId", "updateWriteoffType", "formActualBankRecievedAmount", "currency", "value", "formBankRecievedHandlingFee", "formBankRecievedExchangeLost", "subtract", "formActualBankPaidAmount", "formBankPaidHandlingFee", "formBankPaidExchangeLost", "formSqdBillRecievedAmount", "formSqdBillPaidAmount", "formBillRecievedWriteoffAmount", "formBillPaidWriteoffAmount", "handler", "newVal", "oldVal", "_this", "isHedging", "for<PERSON>ach", "val", "map", "item", "subtotalStr", "subtotal", "toString", "decimalPlaces", "includes", "split", "length", "calculatedValue", "writeoffFromBankBalance", "divide", "writeoffFromDnBalance", "Number", "toFixed", "precision", "dnCurrencyCode", "sqdDnCurrencyBalance", "dnUnitRate", "multiply", "dnAmount", "deep", "_this2", "console", "log", "reimbursePrice", "created", "getList", "store", "dispatch", "redisList", "mounted", "_this3", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "selectListExchangerate", "sent", "stop", "beforeMount", "loadStaff", "methods", "carousel", "pics", "openCarousel", "$message", "info", "chargeTypeList", "children", "v", "parentId", "listAggregatorBankRecord", "_listAggregatorBankRecord", "_x", "apply", "arguments", "config", "JSON", "stringify", "getWriteOffType", "type", "getCompanyCharges", "getReimburseCharges", "_this4", "listWriteOffReimburse", "bankRecordId", "then", "response", "rows", "$nextTick", "$refs", "writeOffReimburseTable", "toggleRowSelection", "sort", "a", "b", "handleExchangeRateInput", "row", "newValue", "exchangeRateShow", "isNumeric", "isNaN", "validateExchangeRate", "minRate", "maxRate", "parseFloat", "error", "concat", "match", "rate", "deleteBankSlip", "_this5", "_callee2", "_callee2$", "_context2", "delImg", "slipFile", "t0", "updateBankrecord", "bankRecordNo", "handleSearch", "parseTime", "updateSlipSatus", "_this6", "clearReceiveOrPay", "slipConfirmed", "success", "writeOffConfirm", "_this7", "rctSet", "Set", "sqdRctNo", "rctArr", "push", "join", "moment", "format", "$modal", "msgSuccess", "cancelledRows", "filter", "some", "selected", "chargeId", "turnBackWriteoff", "rsChargeList", "midChargeBankWriteoffs", "clearingCurrencyCode", "midChargeBankWriteoff", "exchangeRateShowing", "dnBasicRate", "chargeWriteOff", "midChargeBankWriteoffList", "rctWriteoff", "writeOffReimburse", "formatterCurrency", "currencyType", "separator", "symbol", "invertSelection", "autoSelection", "addHedging", "_this8", "isReceiving", "hedgingType", "findHedging", "_objectSpread2", "writeOff", "projectRemove", "print", "_this9", "_callee3", "writeoffStatusString", "_iterator", "_step", "result", "_callee3$", "_context3", "selectListCharge", "_createForOfIteratorHelper2", "s", "done", "getExchangeRate", "abrupt", "currencyWithPrecision", "e", "f", "finish", "setTimeout", "midChargeBankId", "writeOffTable", "isAccountConfirmed", "undefined", "verify", "_this10", "verifyId", "verifyTime", "user", "sid", "_this11", "listBankrecord", "cancel", "reset", "invoiceNo", "chargeType", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this12", "text", "status", "$confirm", "changeStatus", "catch", "handleHedgingSelectionChange", "selection", "handleAddHedging", "_this13", "_callee4", "newRow", "_callee4$", "_context4", "checkSelectableReimburse", "financeConfirmed", "handleSelectionChange", "_this14", "indexOf", "sqdDnCurrencyBalanceShow", "handleChargeSelectionChange", "_this15", "handleReimburseSelectionChange", "_this16", "dbclick", "_this17", "statisticsTimeSelect", "requestMap", "day", "searchAccountFundStatistics", "month", "year", "startTime", "endTime", "handleOpenAggregator", "accountFundStatistics", "_this18", "getAccountFundStatistics", "_iterator2", "_step2", "localCurrency", "overseaCurrency", "validFrom", "Date", "validTo", "settleRate", "base", "err", "totalRecieved", "totalPaid", "calculatePaymentTitleBalances", "summaryMap", "Map", "titleCode", "received", "paid", "has", "set", "<PERSON><PERSON><PERSON>", "sqdPaymentTitleName", "RMB", "balance", "USD", "record", "get", "Array", "from", "values", "handleImport", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "msg", "download", "submitFileForm", "submit", "handleAdd", "handleUpdate", "_this19", "getBankrecord", "submitForm", "_this20", "validate", "_ref", "_callee5", "valid", "_callee5$", "_context5", "uploadImage", "addBankrecord", "_x2", "_this21", "Promise", "resolve", "reject", "customHttpRequest", "onSuccess", "handleSuccess", "onError", "handleError", "handleDelete", "_this22", "bankRecordIds", "delBankrecord", "handleExport", "getTime", "staffNormalizer", "node", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "id", "label", "isDisabled", "deptId", "loadSales", "_this23", "salesList", "_this24", "allRsStaffList", "handledbClick", "selectCompany", "company", "companyId", "companyShortName", "valueDate", "_this25", "_callee6", "re", "_iterator3", "_step3", "_callee6$", "_context6", "buyRate", "sellRate", "getBillDataExchangeRate", "_callee7", "_iterator4", "_step4", "_callee7$", "_context7", "chargeCurrencyCode", "_this26", "_callee8", "_callee8$", "_context8", "getName", "rsStaff", "staffShortName", "selectStaff", "checkSelectable", "index", "handleDialogOpened", "_this27", "treeSelectInput", "treeSelect", "getInputElement", "focus", "isRecievingOrPayingNormalizer", "selectBankAccount", "sqdBelongToCompanyCode", "options", "_this28", "formData", "FormData", "append", "request", "method", "handleChange", "extension", "substring", "lastIndexOf", "newFileName", "File", "raw", "handleWriteoffChange", "computed", "receiveRate", "paidRate", "isLocked", "isBankSlipConfirmed", "exports", "_default"], "sources": ["src/views/system/bankrecord/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"流水\" prop=\"bankRecordNo\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordNo\"\r\n              clearable\r\n              placeholder=\"流水号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"单号\" prop=\"sqdRaletiveRctList\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdRaletiveRctList\"\r\n              clearable\r\n              placeholder=\"单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"收支\" prop=\"isRecievingOrPaying\">\r\n            <el-select v-model=\"queryParams.isRecievingOrPaying\" clearable placeholder=\"商务审核标记\"\r\n                       style=\"width: 100%\" @change=\"handleQuery\"\r\n            >\r\n              <el-option label=\"收\" value=\"0\">收</el-option>\r\n              <el-option label=\"付\" value=\"1\">付</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属\" prop=\"sqdPaymentTitleCode\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdPaymentTitleCode\"\r\n              clearable\r\n              placeholder=\"所属公司\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账户\" prop=\"bankAccountCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankAccountCode\"\r\n              clearable\r\n              placeholder=\"银行账户\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"费用\" prop=\"chargeTypeId\">\r\n            <tree-select :pass=\"queryParams.chargeTypeId\" :placeholder=\"'费用类型'\" :type=\"'chargeType'\"\r\n                         style=\"width: 100%\" @return=\"queryParams.chargeTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"公司\" prop=\"sqdClearingCompanyShortname\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdClearingCompanyShortname\"\r\n              clearable\r\n              placeholder=\"结算公司简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"币种\" prop=\"bankCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankCurrencyCode\"\r\n              clearable\r\n              placeholder=\"银行币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"时间\" prop=\"bankRecordTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            style=\"width:  100%\" type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"录入\" prop=\"bankRecordByStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordByStaffId\"\r\n              clearable\r\n              placeholder=\"银行流水录入人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"录入\" prop=\"bankRecordUpdateTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordUpdateTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水录入时间 ,\"\r\n                            style=\"width: 100%\" type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"销账\" prop=\"writeoffStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.writeoffStaffId\"\r\n              clearable\r\n              placeholder=\"销账人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账\" prop=\"writeoffTime\">\r\n            <el-date-picker v-model=\"queryParams.writeoffTime\"\r\n                            clearable\r\n                            placeholder=\"销账时间\"\r\n                            style=\"width: 100%\" type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:edit']\"\r\n              :disabled=\"single\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleImport\"\r\n            >导入\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              icon=\"el-icon-data-analysis\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"accountFundStatistics\"\r\n            >账户资金统计\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <dynamic-search\r\n              :config-type=\"'bankRecord-search'\"\r\n              :search-fields=\"bankRecordSearchFields\"\r\n              @reset=\"resetQuery\"\r\n              @search=\"handleQuery($event)\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button @click=\"handleOpenAggregator\">数据汇总</el-button>\r\n            <el-dialog v-dialogDrag v-dialogDragWidth\r\n                       :visible.sync=\"openAggregator\" append-to-body width=\"80%\"\r\n            >\r\n              <!--<data-aggregator :data-source=\"aggregatorRctList\" :field-label-map=\"fieldLabelMap\"/>-->\r\n              <data-aggregator-back-ground :aggregate-function=\"listAggregatorBankRecord\"\r\n                                           :config-type=\"'bankRecord-agg'\"\r\n                                           :data-source=\"aggregatorBankRecordList\" :data-source-type=\"'bankRecord'\"\r\n                                           :field-label-map=\"fieldLabelMap\"\r\n              />\r\n            </el-dialog>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"bankrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  highlight-current-row stripe @row-dblclick=\"handledbClick\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"银行流水\" prop=\"bankRecordNo\"/>\r\n          <el-table-column align=\"center\" label=\"费用类别\" prop=\"chargeName\"/>\r\n          <el-table-column align=\"center\" label=\"收支\" prop=\"isRecievingOrPaying\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.isRecievingOrPaying == 1 ? \"付\" : \"收\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"sqdPaymentTitleCode\"/>\r\n          <el-table-column align=\"center\" label=\"银行账户\" prop=\"bankAccountCode\"/>\r\n          <el-table-column align=\"center\" label=\"银行时间\" prop=\"bankRecordTime\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.bankRecordTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"币种\" prop=\"bankCurrencyCode\"/>\r\n          <el-table-column align=\"center\" label=\"水单金额\" prop=\"slipAmount\"/>\r\n          <el-table-column align=\"center\" label=\"实收金额\" prop=\"actualBankRecievedAmount\"/>\r\n          <el-table-column align=\"center\" label=\"收款手续费\" prop=\"bankRecievedHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"实付金额\" prop=\"actualBankPaidAmount\"/>\r\n          <el-table-column align=\"center\" label=\"付款手续费\" prop=\"bankPaidHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"结算公司\" prop=\"sqdClearingCompanyShortname\"/>\r\n          <el-table-column align=\"center\" label=\"发票状态\" prop=\"invoiceStatus\"/>\r\n          <el-table-column align=\"center\" label=\"销账状态 \" prop=\"writeoffStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.writeoffStatus == 1 ? \"=\" : (scope.row.writeoffStatus == 0 ? \"√\" : \"-\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"相关操作单号\" prop=\"sqdRaletiveRctList\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账备注\" prop=\"bankRecordRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <template slot-scope=\"scope\">\r\n              {{ getName(scope.row.writeoffStaffId) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"销账时间\" prop=\"writeoffTime\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.writeoffTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改记录公司账户出入账明细对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      append-to-body\r\n      height=\"60%\"\r\n      width=\"60%\"\r\n      @close=\"showDetail=false\"\r\n    >\r\n      <el-form v-if=\"open\" ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"12\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"银行流水\" prop=\"voucherNo\">\r\n                <el-input :value=\"form.bankRecordNo\" class=\"disable-form\" disabled placeholder=\"银行流水\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n                <treeselect ref=\"treeSelect\" v-model=\"form.isRecievingOrPaying\"\r\n                            :auto-focus=\"true\"\r\n                            :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\" :disable-branch-nodes=\"true\"\r\n                            :disable-fuzzy-matching=\"true\"\r\n                            :disabled=\"isLocked||isBankSlipConfirmed\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"isRecievingOrPayingNormalizer\"\r\n                            :options=\"[{label:'实收',value:'0'},{label:'实付',value:'1'}]\" :show-count=\"true\"\r\n                            placeholder=\"选择收付信息\" @select=\"form.isRecievingOrPaying=$event.value\"\r\n                >\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item\r\n                label=\"结算公司\"\r\n                prop=\"clearingCompanyId\"\r\n              >\r\n                <company-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                                :disabled=\"isLocked||isBankSlipConfirmed\" :load-options=\"companyList\"\r\n                                :multiple=\"false\" :no-parent=\"true\"\r\n                                :pass=\"form.clearingCompanyId\" :placeholder=\"''\"\r\n                                @returnData=\"selectCompany($event)\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"费用描述\" prop=\"voucherNo\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <!--<el-input :class=\"isLocked?'disable-form':''\" :value=\"form.chargeType\" class=\"disable-form\"\r\n                              disabled\r\n                    />-->\r\n                    <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" :pass=\"form.chargeTypeId\"\r\n                                 :placeholder=\"'类型'\" :type=\"'chargeType'\"\r\n                                 style=\"width: 100%\" @return=\"form.chargeTypeId=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.chargeDescription\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                              placeholder=\"费用描述\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n                <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                             :disabled=\"isLocked||isBankSlipConfirmed\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.bankAccountCode\" :placeholder=\"'银行账户'\"\r\n                             @returnData=\"selectBankAccount\"\r\n                             :type=\"'companyAccount'\" @return=\"form.bankAccountCode=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"水单金额\" prop=\"sqdBillRecievedAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                                 :disabled=\"isBankSlipConfirmed\"\r\n                                 :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.slipAmount\" :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                              :disabled=\"isBankSlipConfirmed\"\r\n                              placeholder=\"水单金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"水单文件\" prop=\"sqdBillRecievedAmount\">\r\n                <div>\r\n                  <div style=\"display: flex\">\r\n                    <el-upload\r\n                      v-if=\"form.slipConfirmed==0\"\r\n                      ref=\"bankSlipUpload\"\r\n                      :auto-upload=\"false\"\r\n                      :disabled=\"!form.bankRecordNo\"\r\n                      :http-request=\"customHttpRequest\"\r\n                      :on-change=\"handleChange\"\r\n                      :on-error=\"handleError\"\r\n                      :on-success=\"handleSuccess\"\r\n                      :show-file-list=\"false\"\r\n                      action=\"xxx\"\r\n                      class=\"upload-demo\" style=\"flex: 1\"\r\n                    >\r\n                      <el-button icon=\"el-icon-top-right\" style=\"color: rgb(103, 194, 58)\" type=\"text\"></el-button>\r\n                    </el-upload>\r\n                    <img-preview v-if=\"form.slipFile || (form.slipFile && form.slipConfirmed)\"\r\n                                 :scope=\"{row:{slipFile:form.slipFile}}\"\r\n                                 style=\"flex: 1\"\r\n                    />\r\n                    <el-button v-if=\"form.slipFile && form.slipConfirmed==0\" icon=\"el-icon-delete\"\r\n                               style=\"flex: 1;color: red\"\r\n                               type=\"text\"\r\n                               @click=\"deleteBankSlip(form)\"\r\n                    ></el-button>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"11\">\r\n              <el-form-item label=\"相关单号\" prop=\"voucherNo\">\r\n                <el-input :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                          v-model=\"form.sqdRaletiveRctList\" :disabled=\"isBankSlipConfirmed\"\r\n                          placeholder=\"相关操作单号List\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"水单日期\" prop=\"sqdBillRecievedAmount\">\r\n                <el-date-picker\r\n                  v-model=\"form.slipDate\"\r\n                  :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                  :disabled=\"isBankSlipConfirmed\"\r\n                  clearable\r\n                  default-time=\"12:00:00\"\r\n                  placeholder=\"银行时间\"\r\n                  style=\"width: 100%\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"实收金额\" prop=\"actualBankRecievedAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                                 :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                                 :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankRecievedAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实收金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"实付金额\" prop=\"actualBankPaidAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankPaidAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实付金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"结算方式\" prop=\"paymentTypeCode\">\r\n                <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                             :flat=\"false\" :multiple=\"false\" :pass=\"form.paymentTypeCode\"\r\n                             :placeholder=\"'结算方式'\"\r\n                             :type=\"'paymentChannelsCode'\"\r\n                             @return=\"form.paymentTypeCode=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"凭证号\" prop=\"voucherNo\">\r\n                <el-input v-model=\"form.voucherNo\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"凭证号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"银行备注\" prop=\"bankRecordRemark\">\r\n                <el-input v-model=\"form.bankRecordRemark\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"银行备注\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.bankRecordTime\"\r\n                  :class=\"isLocked?'disable-form':''\"\r\n                  :disabled=\"isLocked\"\r\n                  clearable\r\n                  default-time=\"12:00:00\"\r\n                  placeholder=\"银行时间\"\r\n                  style=\"width: 100%\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <!--实收-->\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"手续费\" prop=\"bankRecievedExchangeLost\">\r\n                <el-input v-model=\"form.bankRecievedHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收款记账\" prop=\"sqdBillRecievedAmount\">\r\n                <el-input v-model=\"form.sqdBillRecievedAmount\" :class=\"'disable-form'\" disabled\r\n                          placeholder=\"收款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收账已销\" prop=\"billRecievedWriteoffAmount\">\r\n                <el-input v-model=\"form.billRecievedWriteoffAmount\" :class=\"'disable-form'\"\r\n                          disabled placeholder=\"收账已销\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收款损益\" prop=\"billRecievedWriteoffAmount\">\r\n                <el-input v-model=\"form.bankRecievedExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                          :disabled=\"!isLocked\" placeholder=\"收款损益\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" :disabled=\"isLocked\" label=\"收账未销\"\r\n                            prop=\"sqdBillRecievedWriteoffBalance\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillRecievedWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"收账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillRecievedWriteoffBalance===form.sqdBillRecievedAmount?'-':form.sqdBillRecievedWriteoffBalance===0?'√':'='\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <!--实付-->\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"手续费\" prop=\"bankPaidExchangeLost\">\r\n                <el-input v-model=\"form.bankPaidHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付款记账\" prop=\"sqdBillPaidAmount\">\r\n                <el-input v-model=\"form.sqdBillPaidAmount\" class=\"disable-form\" disabled\r\n                          placeholder=\"付款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账已销\" prop=\"billPaidWriteoffAmount\">\r\n                <el-input v-model=\"form.billPaidWriteoffAmount\" :class=\"'disable-form'\" disabled\r\n                          placeholder=\"付账已销\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付款汇损\" prop=\"bankPaidExchangeLost\">\r\n                <el-input v-model=\"form.bankPaidExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                          :disabled=\"!isLocked\" placeholder=\"付款损益\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账未销\" prop=\"sqdBillPaidWriteoffBalance\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillPaidWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillPaidAmount===form.sqdBillPaidWriteoffBalance?'-':form.sqdBillPaidWriteoffBalance===0?'√':'='\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"9\">\r\n              <el-form-item label=\"发票号码\" prop=\"voucherNo\">\r\n                <el-input v-model=\"form.invoiceNo\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"发票号码\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"所属员工\">\r\n                <el-select v-model=\"form.sqdRsStaffId\" :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                           :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                           filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"staff in staffList\"\r\n                    :key=\"staff.staffId\"\r\n                    :label=\"staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                    :value=\"staff.staffId\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                         style=\"float: right\" type=\"primary\"\r\n                         @click=\"submitForm\"\r\n              >{{ \"保存\" }}\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"2.5\">\r\n              <el-button v-if=\"this.form.bankRecordId !== null\"\r\n                         :icon=\"form.slipConfirmed==1?'el-icon-check':''\" type=\"primary\"\r\n                         @click=\"updateSlipSatus\"\r\n              >\r\n                {{ form.slipConfirmed == 1 ? \"水单已确认\" : \"确认水单\" }}\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <el-tooltip placement=\"top\">\r\n                <div slot=\"content\">\r\n                  <div v-if=\"form.isBankRecordLocked == 1 \">\r\n                    <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                      {{ (form.verifyId ? getName(form.verifyId) : \"\") }}\r\n                    </div>\r\n                    <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                      {{ parseTime(form.verifyTime, \"{y}-{m}-{d}\") }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <el-button :icon=\"form.isBankRecordLocked==1?'el-icon-check':''\" type=\"primary\"\r\n                           @click=\"verify\"\r\n                >\r\n                  {{ form.isBankRecordLocked == 1 ? \"流水已审核\" : \"审核流水\" }}\r\n                </el-button>\r\n              </el-tooltip>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <el-button :disabled=\"!isLocked\" :loading=\"loadingCharge\" style=\"float: right\" type=\"primary\"\r\n                         v-if=\"searchAble\" @click=\"getWriteOffType(writeoffType)\"\r\n              >\r\n                调取相关费用明细\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n      </el-form>\r\n      <!--费用-->\r\n      <el-table\r\n        v-show=\"showDetail && writeoffType==='chargeType'\"\r\n        ref=\"writeOffTable\"\r\n        :data=\"writeOffList\"\r\n        border\r\n        max-height=\"315px\"\r\n        size=\"mini\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleChargeSelectionChange\"\r\n\r\n      >\r\n        <el-table-column type=\"index\" width=\"20\"/>\r\n        <el-table-column\r\n          :selectable=\"checkSelectable\"\r\n          type=\"selection\"\r\n          width=\"35\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"审核\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"30\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.isAccountConfirmed === \"1\" ? \"√\" : \"-\" }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"Rct号\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"150\"\r\n          sortable\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"费用名称\"\r\n          prop=\"chargeName\"\r\n          width=\"80\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"备注\"\r\n          prop=\"address\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"收付标志\"\r\n          prop=\"dnCurrencyCode\"\r\n          width=\"100\"\r\n          sortable\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.isRecievingOrPaying == 1 ? (\"应付\" + scope.row.dnCurrencyCode) : (\"应收\" + scope.row.dnCurrencyCode)\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"应收/付金额\"\r\n          prop=\"subtotal\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"销账余额\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"本次拟销账金额\"\r\n          prop=\"sqdDnCurrencyBalance\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.writeoffFromDnBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"汇率展示\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.exchangeRateShow\"\r\n                      @blur=\"(newValue) => validateExchangeRate(scope.row)\"\r\n                      @input=\"(newValue) => handleExchangeRateInput(scope.row, newValue)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"折算记账金额\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-input-number\r\n              v-model=\"scope.row.writeoffFromBankBalance\"\r\n              :controls=\"false\"\r\n              :min=\"0\"\r\n              :precision=\"2\"\r\n              :step=\"0.01\"\r\n              autocomplete=\"off\"\r\n              style=\"width: 100%\"\r\n              @change=\"(value) => handleWriteoffChange(scope.row, value)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账人\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ getName(scope.row.midChargeBankWriteoff.writeoffStaffId) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账时间\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.midChargeBankWriteoff.writeoffTime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"销账状态\"\r\n          width=\"50\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.sqdDnCurrencyBalance == 0 ? \"√\" : scope.row.sqdDnCurrencyBalance > 0 ? \"=\" : \"-\"\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!--销账-->\r\n      <el-table\r\n        v-show=\"showDetail && writeoffType==='reimburseType'\"\r\n        ref=\"writeOffReimburseTable\"\r\n        :data=\"reimburseList\"\r\n        border\r\n        max-height=\"315px\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleReimburseSelectionChange\"\r\n      >\r\n        <el-table-column :selectable=\"checkSelectableReimburse\" align=\"center\" type=\"selection\" width=\"28\"/>\r\n        <el-table-column align=\"center\" label=\"ID\" prop=\"reimburseId\" width=\"38\"/>\r\n        <el-table-column align=\"center\" label=\"报销人\" prop=\"staffName\" width=\"68\"/>\r\n        <el-table-column align=\"center\" label=\"费用类型\" prop=\"chargeTypeName\" show-tooltip-when-overflow width=\"88\"/>\r\n        <el-table-column align=\"center\" label=\"报销概要\" prop=\"reimburseTitle\" show-tooltip-when-overflow width=\"68\"/>\r\n        <el-table-column align=\"center\" label=\"报销详情\" prop=\"reimburseContent\" show-tooltip-when-overflow/>\r\n        <el-table-column align=\"center\" label=\"参与人员\" prop=\"reimburseParticipation\" show-tooltip-when-overflow\r\n                         width=\"170\"\r\n        />\r\n        <el-table-column align=\"center\" label=\"报销金额\" prop=\"reimbursePrice\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <h1 style=\"margin: 0;font-weight:bold;\">\r\n              {{ scope.row.reimbursePrice }}\r\n            </h1>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"日期\" prop=\"happenDate\" width=\"142\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 style=\"margin: 0\">{{ \"发生日期：\" + parseTime(scope.row.happenDate, \"{y}-{m}-{d}\") }}</h6>\r\n            <h6 style=\"margin: 0\">{{ \"申请日期：\" + parseTime(scope.row.applyDate, \"{y}-{m}-{d}\") }}</h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"部门审批\" prop=\"deptConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==1&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.deptConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.deptReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"人事审批\" prop=\"hrConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==1&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.hrConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.hrReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"总经办审批\" prop=\"ceoConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==1&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.ceoConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.ceoReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"财务确认\" prop=\"financeConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==1&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.financeConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.financeReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单据附件\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button :size=\"size\" style=\"padding: 0\" type=\"info\" @click=\"carousel(scope.row.reimburseAppendix)\">\r\n              查看图片\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow width=\"100\"/>\r\n      </el-table>\r\n      <!--总计-->\r\n      <div v-if=\"showDetail\" class=\"total\">\r\n        <div style=\"width: 30%;\">全部总计: {{ totalAmount }}</div>\r\n        <div style=\"width: 30%;\">已选总计:\r\n          {{\r\n            this.form.isRecievingOrPaying == 0 ? this.form.billRecievedWriteoffAmount : this.form.billPaidWriteoffAmount\r\n          }}\r\n        </div>\r\n        <div style=\"width: 30%;\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              已选余额总计：\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div>\r\n                {{ \"RMB \" + selectedBalanceAmountRMB }}\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div>\r\n                {{ \"USD \" + selectedBalanceAmountUSD }}\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </div>\r\n\r\n      <!--按钮区-->\r\n      <div v-if=\"showDetail\" class=\"table-btn-group\">\r\n        <div class=\"table-btn-left\">\r\n          <el-button type=\"primary\" @click=\"invertSelection\">反选</el-button>\r\n          <el-button type=\"primary\" @click=\"autoSelection\">智选</el-button>\r\n          <el-popover\r\n            placement=\"bottom\"\r\n            title=\"添加对冲费用\"\r\n            trigger=\"click\"\r\n            width=\"800\"\r\n          >\r\n            <div style=\"max-height: 400px; overflow-y: auto;\">\r\n              <el-table :data=\"hedgingData\" @selection-change=\"handleHedgingSelectionChange\">\r\n                <el-table-column type=\"index\" width=\"20\"/>\r\n                <el-table-column\r\n                  align=\"center\"\r\n                  label=\"审核\"\r\n                  prop=\"sqdRctNo\"\r\n                  width=\"30\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    {{ scope.row.isAccountConfirmed === \"1\" ? \"√\" : \"-\" }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column\r\n                  label=\"Rct号\"\r\n                  prop=\"sqdRctNo\"\r\n                  width=\"150\"\r\n                >\r\n                </el-table-column>\r\n                <el-table-column\r\n                  label=\"费用名称\"\r\n                  prop=\"chargeName\"\r\n                  width=\"80\"\r\n                >\r\n                </el-table-column>\r\n                <el-table-column\r\n                  label=\"备注\"\r\n                  prop=\"address\"\r\n                >\r\n                </el-table-column>\r\n                <el-table-column\r\n                  label=\"收付标志\"\r\n                  prop=\"dnCurrencyCode\"\r\n                  width=\"100\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    {{\r\n                      scope.row.isRecievingOrPaying == 1 ? (\"应付\" + scope.row.dnCurrencyCode) : (\"应收\" + scope.row.dnCurrencyCode)\r\n                    }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column\r\n                  align=\"right\"\r\n                  label=\"应收/付金额\"\r\n                  prop=\"subtotal\"\r\n                >\r\n                </el-table-column>\r\n                <el-table-column\r\n                  align=\"right\"\r\n                  label=\"销账余额\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    {{ scope.row.sqdDnCurrencyBalance }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column\r\n                  align=\"center\"\r\n                  label=\"销账状态\"\r\n                  width=\"50\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    {{\r\n                      scope.row.sqdDnCurrencyBalance == 0 ? \"√\" : scope.row.sqdDnCurrencyBalance > 0 ? \"=\" : \"-\"\r\n                    }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column\r\n                  align=\"center\"\r\n                  label=\"操作\"\r\n                  width=\"50\"\r\n                >\r\n                  <template slot-scope=\"scope\">\r\n                    <el-button type=\"text\" @click=\"handleAddHedging(scope.row)\">[添加]</el-button>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n            </div>\r\n            <el-button slot=\"reference\" type=\"primary\" @click=\"addHedging\">增加对冲</el-button>\r\n          </el-popover>\r\n          <el-button type=\"primary\" @click=\"projectRemove\">项目去除</el-button>\r\n          <el-button type=\"primary\" @click=\"print\">打印</el-button>\r\n        </div>\r\n        <div class=\"table-btn-right\">\r\n          <el-button style=\"float: right\" type=\"primary\" @click=\"writeOffConfirm(writeoffType)\">确定销账</el-button>\r\n        </div>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"bankSlipPreview\"\r\n        append-to-body destroy-on-close\r\n        height=\"50%\"\r\n        width=\"50%\"\r\n      >\r\n        <el-image :src=\"form.slipFile\" style=\"margin-top: 20px;\"/>\r\n      </el-dialog>\r\n    </el-dialog>\r\n\r\n    <!--    // excel 上传导入组件-->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"upload.title\"\r\n      :visible.sync=\"upload.open\"\r\n      append-to-body width=\"400px\"\r\n    >\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :action=\"upload.url + '?chargeTypeId=' + 2\"\r\n        :auto-upload=\"false\"\r\n        :disabled=\"upload.isUploading\"\r\n        :headers=\"upload.headers\"\r\n        :limit=\"1\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        accept=\".xlsx, .xls\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div slot=\"tip\" class=\"el-upload__tip text-center\">\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <!--<el-link :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" type=\"primary\"\r\n                   @click=\"importTemplate\"\r\n          >下载模板\r\n          </el-link>-->\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--查看账户资金统计-->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n      :visible.sync=\"statisticsOpen\"\r\n      append-to-body\r\n      height=\"40%\"\r\n      width=\"40%\"\r\n    >\r\n      <el-row>\r\n        <el-form class=\"edit\" label-width=\"50px\">\r\n          <el-col :span=\"5\">\r\n            <el-radio-group v-model=\"timeStatistics\">\r\n              <el-radio :label=\"1\">筛选天</el-radio>\r\n              <el-radio :label=\"2\">筛选月</el-radio>\r\n              <el-radio :label=\"3\">筛选年</el-radio>\r\n              <el-radio :label=\"4\">筛选范围</el-radio>\r\n            </el-radio-group>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===1\" :span=\"8\">\r\n            <el-date-picker v-model=\"queryParams.params.day\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            @change=\"statisticsTimeSelect\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===2\" :span=\"8\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.params.month\"\r\n              placeholder=\"选择月\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-MM\"\r\n              @change=\"statisticsTimeSelect\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===3\" :span=\"8\">\r\n            <el-date-picker v-model=\"queryParams.params.year\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"year\"\r\n                            value-format=\"yyyy\"\r\n                            @change=\"statisticsTimeSelect\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===4\" :span=\"8\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.timeArr\"\r\n              :default-time=\"['00:00:00', '23:59:59']\"\r\n              end-placeholder=\"结束日期\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              @change=\"statisticsTimeSelect\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n      <el-table\r\n        :data=\"statisticsList\"\r\n        border style=\"width: 100%;margin-top: 20px;\"\r\n        @row-dblclick=\"dbclick\"\r\n      >\r\n        <el-table-column\r\n          label=\"银行账户\"\r\n          prop=\"bankAccSummary\"\r\n          width=\"200\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"账户代码\"\r\n          prop=\"bankAccountCode\"\r\n          width=\"100\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"账户币种\"\r\n          prop=\"bankCurrencyCode\"\r\n          width=\"80\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"收入\"\r\n          prop=\"totalRecieved\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"支出\"\r\n          prop=\"totalPaid\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"余额\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ currency(scope.row.totalRecieved).subtract(scope.row.totalPaid).value }}\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!--总计-->\r\n      <div v-if=\"statisticsList.length>0\" class=\"statistics-total\">\r\n        <div class=\"total-item\">USD 收入总计: {{ formatterCurrency(totalRecievedUSD, \"$\") }} - 支出总计:\r\n          {{ formatterCurrency(totalPaidUSD, \"$\") }} =\r\n          {{ formatterCurrency(currency(totalRecievedUSD).subtract(totalPaidUSD).value, \"$\") }}\r\n        </div>\r\n        <div class=\"total-item\">RMB 收入总计: {{ formatterCurrency(totalRecievedRMB, \"¥\") }} - 支出总计:\r\n          {{ formatterCurrency(totalPaidRMB, \"¥\") }} =\r\n          {{ formatterCurrency(currency(totalRecievedRMB).subtract(totalPaidRMB).value, \"¥\") }}\r\n        </div>\r\n        <div class=\"total-item\">RMB 折算总计:\r\n          {{\r\n            formatterCurrency(currency(totalRecievedUSD).subtract(totalPaidUSD).multiply(exchangeRate).add(currency(totalRecievedRMB).subtract(totalPaidRMB)).value, \"¥\")\r\n          }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 按付款抬头汇总的数据表格 -->\r\n      <h3>按付款抬头汇总</h3>\r\n      <!-- 付款抬头余额统计表格 -->\r\n      <div v-if=\"paymentTitleBalances.length > 0\" class=\"payment-title-balance\">\r\n        <h3>付款抬头余额统计</h3>\r\n        <el-table :data=\"paymentTitleBalances\" border style=\"width: 100%\">\r\n          <el-table-column label=\"付款抬头\" prop=\"titleCode\"></el-table-column>\r\n          <!-- RMB统计 -->\r\n          <el-table-column label=\"人民币(RMB)\">\r\n            <el-table-column label=\"收入\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.RMB.received.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"支出\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.RMB.paid.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"余额\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n              <span :class=\"{'positive': scope.row.RMB.balance > 0, 'negative': scope.row.RMB.balance < 0}\">\r\n                {{ scope.row.RMB.balance.toFixed(2) }}\r\n              </span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table-column>\r\n\r\n          <!-- USD统计 -->\r\n          <el-table-column label=\"美元(USD)\">\r\n            <el-table-column label=\"收入\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.USD.received.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"支出\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.USD.paid.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"余额\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n              <span :class=\"{'positive': scope.row.USD.balance > 0, 'negative': scope.row.USD.balance < 0}\">\r\n                {{ scope.row.USD.balance.toFixed(2) }}\r\n              </span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addBankrecord,\r\n  changeStatus,\r\n  delBankrecord, delImg, getAccountFundStatistics,\r\n  getBankrecord,\r\n  listBankrecord,\r\n  updateBankrecord\r\n} from \"@/api/system/bankrecord\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport currency from \"currency.js\"\r\nimport {chargeWriteOff, findHedging, selectListCharge, turnBackWriteoff} from \"@/api/system/rsCharge\"\r\nimport {selectListExchangerate} from \"@/api/system/exchangerate\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport log from \"@/views/monitor/job/log.vue\"\r\nimport moment from \"moment\"\r\nimport request from \"@/utils/request\"\r\nimport ImgPreview from \"@/views/system/rct/imgPreview.vue\"\r\nimport {getToken} from \"@/utils/auth\"\r\nimport {listAggregatorRct, rctWriteoff, writeoff} from \"@/api/system/rct\"\r\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\r\nimport {bankRecordFieldLabelMap} from \"@/config/bankRecordFieldLabelMap\"\r\nimport {listWriteOffReimburse, writeOffReimburse} from \"@/api/system/reimburse\"\r\nimport DynamicSearch from \"@/components/DynamicSearch/index.vue\"\r\nimport {bankRecordSearchFields} from \"@/config/bankRecordSearchFields\"\r\n\r\nexport default {\r\n  name: \"Bankrecord\",\r\n  components: {DynamicSearch, DataAggregatorBackGround, ImgPreview, Treeselect, CompanySelect},\r\n  data() {\r\n    return {\r\n      bankRecordSearchFields,\r\n      size: this.$store.state.app.size || \"mini\",\r\n      fieldLabelMap: bankRecordFieldLabelMap,\r\n      writeOffList: [],\r\n      reimburseList: [],\r\n      salesId: null,\r\n      belongList: [],\r\n      staffList: [],\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 记录公司账户出入账明细表格数据\r\n      bankrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        params: {},\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: null,\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        timeArr: null\r\n      },\r\n      statisticsList: [],\r\n\r\n      // 表单参数\r\n      form: {\r\n        chargeTypeId: null\r\n      },\r\n      searchAble: false,\r\n      writeoffType: null,\r\n      // 表单校验\r\n      rules: {\r\n        actualBankRecievedAmount: [\r\n          {required: true, message: \"请输入实收信息\", trigger: \"blur\"}\r\n        ],\r\n        bankRecordTime: [\r\n          {required: true, message: \"请输入银行时间\", trigger: \"blur\"}\r\n        ],\r\n        actualBankPaidAmount: [\r\n          {required: true, message: \"请输入实付信息\", trigger: \"blur\"}\r\n        ]\r\n        /* clearingCompanyId: [\r\n          {required: true, message: \"请输入结算公司\", trigger: \"blur\"}\r\n        ] */\r\n      },\r\n      statisticsOpen: false,\r\n      openAggregator: false,\r\n      totalRecievedUSD: 0,\r\n      totalRecievedRMB: 0,\r\n      totalPaidUSD: 0,\r\n      totalPaidRMB: 0,\r\n      exchangeRate: 0,\r\n      paymentTitleBalances: 0,\r\n      timeStatistics: null,\r\n      add: false,\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: false,\r\n        // 设置上传的请求头部\r\n        headers: {Authorization: \"Bearer \" + getToken()},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/system/bankrecord/importData\"\r\n      },\r\n      showDetail: false,\r\n      selectedCharges: [],\r\n      selectedReimburses: [],\r\n      totalAmount: null,\r\n      selectedAmount: null,\r\n      selectedBalanceAmount: null,\r\n      selectedBalanceAmountRMB: 0,\r\n      selectedBalanceAmountUSD: 0,\r\n      loadingCharge: false,\r\n      staffId: null,\r\n      alreadyWriteoffList: [],\r\n      turnBackWriteoffList: [],\r\n      showCompany: false,\r\n      companyList: [],\r\n      bankSlipPreview: false,\r\n      imageFile: null,\r\n      hedgingData: [],\r\n      exchangeRateList: [],\r\n      aggregatorBankRecordList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    \"form.chargeTypeId\"(n) {\r\n      this.updateWriteoffType(n)\r\n    },\r\n    // 实收金额\r\n    \"form.actualBankRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款手续费\r\n    \"form.bankRecievedHandlingFee\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款损益\r\n    \"form.bankRecievedExchangeLost\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 实付金额\r\n    \"form.actualBankPaidAmount\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款手续费\r\n    \"form.bankPaidHandlingFee\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款损益\r\n    \"form.bankPaidExchangeLost\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收款记账\r\n    \"form.sqdBillRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付款记账\r\n    \"form.sqdBillPaidAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收账已销\r\n    \"form.billRecievedWriteoffAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付账已销\r\n    \"form.billPaidWriteoffAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n    selectedCharges: {\r\n      handler: function (newVal, oldVal) {\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = 0\r\n\r\n        // 已选总计\r\n        this.selectedBalanceAmount = 0\r\n\r\n        let billRecievedWriteoffAmount = 0\r\n        let billPaidWriteoffAmount = 0\r\n\r\n        let selectedBalanceAmountUSD = 0\r\n        let selectedBalanceAmountRMB = 0\r\n        // 是否只是对冲\r\n        let isHedging = true\r\n        newVal.forEach((val) => {\r\n          if (this.form.isRecievingOrPaying == val.isRecievingOrPaying) {\r\n            isHedging = false\r\n          }\r\n        })\r\n\r\n        newVal.map(item => {\r\n          // 本次拟销账金额\r\n          // item.writeoffFromDnBalance = currency(item.writeoffFromBankBalance).divide(item.exchangeRate).value\r\n          if (item.exchangeRate) {\r\n            // 获取subtotal的小数位数\r\n            const subtotalStr = item.subtotal.toString()\r\n            const decimalPlaces = subtotalStr.includes(\".\") ?\r\n              subtotalStr.split(\".\")[1].length : 0\r\n\r\n            // 根据subtotal的小数位数来格式化writeoffFromDnBalance\r\n            const calculatedValue = currency(item.writeoffFromBankBalance).divide(item.exchangeRate).value\r\n            item.writeoffFromDnBalance = Number(calculatedValue).toFixed(decimalPlaces)\r\n          }\r\n\r\n          if (this.form.isRecievingOrPaying == item.isRecievingOrPaying) {\r\n            // 收账已销\r\n            billRecievedWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billRecievedWriteoffAmount).value\r\n            // 付账已销\r\n            billPaidWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billPaidWriteoffAmount).value\r\n\r\n            if (item.dnCurrencyCode === \"RMB\") {\r\n              selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            } else {\r\n              selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            }\r\n          } else if (isHedging) {\r\n            // 收账已销\r\n            billRecievedWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billRecievedWriteoffAmount).value\r\n            // 付账已销\r\n            billPaidWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billPaidWriteoffAmount).value\r\n\r\n            if (item.dnCurrencyCode === \"RMB\") {\r\n              selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            } else {\r\n              selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            }\r\n          } else {\r\n            // 收账已销\r\n            billRecievedWriteoffAmount = currency(billRecievedWriteoffAmount, {precision: 4}).subtract(item.writeoffFromBankBalance).value\r\n            // 付账已销\r\n            billPaidWriteoffAmount = currency(billPaidWriteoffAmount, {precision: 4}).subtract(item.writeoffFromBankBalance).value\r\n\r\n            if (item.dnCurrencyCode === \"RMB\") {\r\n              selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 4}).subtract(item.sqdDnCurrencyBalance).value\r\n            } else {\r\n              selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 4}).subtract(item.sqdDnCurrencyBalance).value\r\n            }\r\n          }\r\n\r\n          // 已选总计\r\n          this.selectedAmount = null\r\n\r\n          currency(item.dnUnitRate).multiply(item.dnAmount).value === item.writeoffFromDnBalance ? item.writeoffStatus = \"0\" : item.writeoffFromDnBalance > 0 ? item.writeoffStatus = \"1\" : item.writeoffStatus = \"-1\"\r\n        })\r\n\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = currency(billRecievedWriteoffAmount, {precision: 2}).value\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = currency(billPaidWriteoffAmount, {precision: 2}).value\r\n\r\n        this.selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 2}).value\r\n        this.selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 2}).value\r\n\r\n      },\r\n      deep: true\r\n    },\r\n    selectedReimburses: {\r\n      handler: function (newVal, oldVal) {\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = 0\r\n\r\n        // 已选总计\r\n        this.selectedBalanceAmount = 0\r\n\r\n        let billRecievedWriteoffAmount = 0\r\n        let billPaidWriteoffAmount = 0\r\n\r\n        let selectedBalanceAmountUSD = 0\r\n        let selectedBalanceAmountRMB = 0\r\n\r\n        newVal.map(item => {\r\n          // 收账已销\r\n          console.log(item.reimbursePrice)\r\n          billRecievedWriteoffAmount = currency(item.reimbursePrice, {precision: 4}).add(billRecievedWriteoffAmount).value\r\n          // 付账已销\r\n          billPaidWriteoffAmount = currency(item.reimbursePrice, {precision: 4}).add(billPaidWriteoffAmount).value\r\n\r\n          selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 4}).add(item.reimbursePrice).value\r\n\r\n          // 已选总计\r\n          this.selectedAmount = null\r\n        })\r\n\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = currency(billRecievedWriteoffAmount, {precision: 2}).value\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = currency(billPaidWriteoffAmount, {precision: 2}).value\r\n\r\n        this.selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 2}).value\r\n        this.selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 2}).value\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    // this.loadSales()\r\n    // this.loadStaff()\r\n    // this.getExchangeRate(\"USD\", \"RMB\", null, val => {\r\n    //   console.log(val)\r\n    // })\r\n    store.dispatch(\"getChargeTypeList\")\r\n    if (this.$store.state.data.exchangeRateList.length == 0 || this.$store.state.data.redisList.exchangeRateList) {\r\n      store.dispatch(\"getExchangeRate\")\r\n    }\r\n  }\r\n  ,\r\n  async mounted() {\r\n    this.exchangeRateList = await selectListExchangerate()\r\n  }\r\n  ,\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  }\r\n  ,\r\n  methods: {\r\n    carousel(pics) {\r\n      if (pics != null) {\r\n        this.openCarousel = true\r\n        this.pics = pics\r\n      } else {\r\n        this.$message.info(\"没有附件\")\r\n      }\r\n    },\r\n    updateWriteoffType(n) {\r\n      // 只有货运流水和报销流水才可以调取费用\r\n      let data\r\n      this.$store.state.data.chargeTypeList.forEach(item => {\r\n        if (item.chargeTypeId == n) {\r\n          data = item\r\n        } else if (item.children) {\r\n          item.children.forEach(v => {\r\n            if (v.chargeTypeId == n) {\r\n              data = item\r\n            }\r\n          })\r\n        }\r\n      })\r\n      if (data) {\r\n        if (data.chargeTypeId === 2 || data.parentId === 2) {\r\n          this.searchAble = true\r\n          this.writeoffType = \"chargeType\"\r\n        } else if (data.chargeTypeId === 4 || data.parentId === 4) {\r\n          this.searchAble = true\r\n          this.writeoffType = \"reimburseType\"\r\n        }\r\n      } else {\r\n        this.searchAble = false\r\n      }\r\n    },\r\n    listAggregatorBankRecord(params) {\r\n      params.config = JSON.stringify(params.config)\r\n      this.queryParams.params = params\r\n      return listAggregatorBankRecord(this.queryParams)\r\n    },\r\n    getWriteOffType(type) {\r\n      switch (type) {\r\n        case \"chargeType\":\r\n          this.getCompanyCharges()\r\n          break\r\n        case \"reimburseType\":\r\n          this.getReimburseCharges()\r\n          break\r\n      }\r\n    },\r\n    getReimburseCharges() {\r\n      this.reimburseList = []\r\n      this.loadingCharge = true\r\n\r\n      listWriteOffReimburse({\r\n        staffId: this.form.sqdRsStaffId,\r\n        sqdRaletiveRctList: this.form.sqdRaletiveRctList,\r\n        bankRecordId: this.form.bankRecordId\r\n      }).then(response => {\r\n        this.reimburseList = response.rows\r\n\r\n        this.loadingCharge = false\r\n        this.showDetail = true\r\n\r\n        this.totalAmount = 0\r\n        this.$nextTick(() => {\r\n          this.reimburseList.map(item => {\r\n            this.totalAmount = currency(item.reimbursePrice).add(this.totalAmount).value\r\n            if (item.bankRecordId === this.form.bankRecordId) {\r\n              this.$refs.writeOffReimburseTable.toggleRowSelection(item, true)\r\n            }\r\n          })\r\n        })\r\n\r\n        this.reimburseList.sort((a, b) => {\r\n          if (a.bankRecordId === this.form.bankRecordId && b.bankRecordId !== this.form.bankRecordId) {\r\n            return -1 // a 排在 b 前\r\n          } else if (a.bankRecordId !== this.form.bankRecordId && b.bankRecordId === this.form.bankRecordId) {\r\n            return 1 // b 排在 a 前\r\n          } else {\r\n            return 0 // 保持原顺序\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleExchangeRateInput(row, newValue) {\r\n      // 实时更新展示值\r\n      row.exchangeRateShow = newValue\r\n\r\n    }\r\n    ,\r\n    isNumeric(value) {\r\n      return !isNaN(Number(value))\r\n    }\r\n    ,\r\n    validateExchangeRate(row) {\r\n      // 定义校验规则，例如：必须是 0.01 到 100 的数字\r\n      const minRate = 0.01\r\n      const maxRate = 100\r\n\r\n      const value = row.exchangeRateShow || \"\"\r\n      if (this.isNumeric(value)) {\r\n        // 转换为数字，进行范围校验\r\n        const value = parseFloat(row.exchangeRateShow)\r\n        if (isNaN(value) || value < minRate || value > maxRate) {\r\n          this.$message.error(`汇率必须在 ${minRate} 和 ${maxRate} 之间`)\r\n          // 恢复到上次有效值\r\n          row.exchangeRateShow = row.exchangeRate.toString()\r\n        } else {\r\n          // 输入合法，同步内部值\r\n          row.exchangeRate = value\r\n        }\r\n      } else {\r\n        // 检查输入是否符合 \"1/数字\" 格式\r\n        // 确保是字符串\r\n        const match = value.match(/^1\\/(\\d+(\\.\\d+)?)$/)\r\n        if (match) {\r\n          const rate = parseFloat(match[1]) // 提取分母值作为汇率\r\n          if (rate > 0) {\r\n            row.exchangeRate = (1 / rate).toFixed(4) // 自动计算 USD\r\n          }\r\n        }\r\n      }\r\n    }\r\n    ,\r\n    async deleteBankSlip(row) {\r\n      // 删除服务器中的图片文件\r\n      try {\r\n        await delImg({url: row.slipFile})\r\n      } catch (e) {\r\n        // 更新流水中的图片地址\r\n        await updateBankrecord({\r\n          bankRecordId: row.bankRecordId,\r\n          slipFile: null,\r\n          isRecievingOrPaying: this.type === \"pay\" ? \"1\" : \"0\",\r\n          bankRecordNo: row.bankRecordNo\r\n        })\r\n      }\r\n\r\n      await this.getList()\r\n    }\r\n    ,\r\n    handleSearch(type) {\r\n      switch (type) {\r\n        case \"common\":\r\n          this.getList({})\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    }\r\n    ,\r\n    parseTime,\r\n    updateSlipSatus() {\r\n      // 确认水单的时候,相关表单校验\r\n\r\n      this.clearReceiveOrPay()\r\n      if (this.form.slipConfirmed == 1) {\r\n        this.form.slipConfirmed = \"0\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单取消确认\")\r\n        })\r\n      } else {\r\n        this.form.slipConfirmed = \"1\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单确认\")\r\n        })\r\n      }\r\n    },\r\n    writeOffConfirm(type) {\r\n      this.clearReceiveOrPay()\r\n      // 更新银行流水\r\n      /* let rctSet = new Set()\r\n      this.selectedCharges.map(item => rctSet.add(item.sqdRctNo))\r\n      let rctArr = []\r\n      rctSet.forEach(item => rctArr.push(item))\r\n      this.form.sqdRaletiveRctList = rctArr.toString() */\r\n\r\n      // 销账时更新费用中的rct号到表单中\r\n      let rctSet = new Set()\r\n      this.selectedCharges.map(item => rctSet.add(item.sqdRctNo))\r\n      let rctArr = []\r\n      rctSet.forEach(item => rctArr.push(item))\r\n      this.form.sqdRaletiveRctList = rctArr.join(\",\")\r\n\r\n      this.form.writeoffTime = moment().format(\"yyyy-MM-DD\")\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        this.open = false\r\n        this.getList()\r\n\r\n        if (type === \"chargeType\") {\r\n          // 取消销账的费用,进行回退\r\n          const cancelledRows = this.alreadyWriteoffList.filter(row => {\r\n            return !this.selectedCharges.some(selected => selected.chargeId === row.chargeId)\r\n          })\r\n          turnBackWriteoff({rsChargeList: cancelledRows})\r\n\r\n          // 更新费用中的销账余额(更新费用时插入中间表)\r\n          let midChargeBankWriteoffs = []\r\n          this.selectedCharges = this.selectedCharges.map(item => {\r\n            // 每次销账的时候再计算余额写回费用明细中\r\n            item.sqdDnCurrencyBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).subtract(item.writeoffFromDnBalance).value\r\n            // 销账余额 = 上次销账剩余-本次拟销账金额\r\n            item.clearingCurrencyCode = this.form.bankCurrencyCode\r\n            item.writeoffStatus = currency(item.sqdDnCurrencyBalance).value === 0 ? \"1\" : \"0\"\r\n            let midChargeBankWriteoff = {}\r\n            midChargeBankWriteoff.chargeId = item.chargeId\r\n            midChargeBankWriteoff.bankRecordId = this.form.bankRecordId\r\n            midChargeBankWriteoff.writeoffFromDnBalance = item.writeoffFromDnBalance\r\n            midChargeBankWriteoff.exchangeRateShowing = item.exchangeRateShow\r\n            midChargeBankWriteoff.writeoffFromBankBalance = item.writeoffFromBankBalance\r\n            midChargeBankWriteoff.dnBasicRate = item.exchangeRate\r\n\r\n            midChargeBankWriteoffs.push(midChargeBankWriteoff)\r\n\r\n            return item\r\n          })\r\n\r\n          chargeWriteOff({\r\n            rsChargeList: this.selectedCharges,\r\n            midChargeBankWriteoffList: midChargeBankWriteoffs\r\n          }).then(response => {\r\n            // 销完账之后要更新操作单中的未收未付\r\n            rctWriteoff(this.form.sqdRaletiveRctList.split(\",\"))\r\n          })\r\n        } else {\r\n          const data = this.selectedReimburses.map(item => {\r\n            item.bankRecordId = this.form.bankRecordId\r\n            return item\r\n          })\r\n          writeOffReimburse(data).then(response => {\r\n            this.$message.success(\"销账成功\")\r\n            this.getReimburseCharges()\r\n          })\r\n        }\r\n      })\r\n    },\r\n    formatterCurrency(value, currencyType) {\r\n      return currency(value, {\r\n        separator: \",\",\r\n        // symbol: \"¥\",\r\n        symbol: currencyType,\r\n        precision: 0\r\n      }).format()\r\n    }\r\n    ,\r\n    invertSelection() {\r\n      /* this.writeOffList.forEach(row => {\r\n        // if (this.selectedCharges.indexOf(row) !== -1) {\r\n        this.$refs.writeOffTable.toggleRowSelection(row)\r\n        // }\r\n      }) */\r\n    }\r\n    ,\r\n    autoSelection() {\r\n    }\r\n    ,\r\n    addHedging() {\r\n      // 向销账列表中添加一条\r\n      const isReceiving = this.form.isRecievingOrPaying === \"0\"\r\n      const hedgingType = isReceiving ? \"1\" : \"0\"\r\n\r\n      findHedging({...this.form, isRecievingOrPaying: hedgingType}).then(response => {\r\n        this.hedgingData = response ? response.filter(item =>\r\n          !this.writeOffList.some(writeOff => writeOff.chargeId === item.chargeId)\r\n        ) : []\r\n      })\r\n    }\r\n    ,\r\n    projectRemove() {\r\n    }\r\n    ,\r\n    print() {\r\n    }\r\n    ,\r\n    currency,\r\n    async getCompanyCharges() {\r\n      this.writeOffList = []\r\n      this.alreadyWriteoffList = []\r\n      this.loadingCharge = true\r\n      // 销完的流水只展示销账记录\r\n      let writeoffStatusString\r\n      if (this.form.isRecievingOrPaying === \"0\" && this.form.sqdBillRecievedWriteoffBalance === 0) {\r\n        writeoffStatusString = \"ALL\"\r\n      } else if (this.form.isRecievingOrPaying === \"1\" && this.form.sqdBillPaidWriteoffBalance === 0) {\r\n        writeoffStatusString = \"ALL\"\r\n      } else if (this.form.billRecievedWriteoffAmount !== null || this.form.billPaidWriteoffAmount !== null) {\r\n        // 不是已销完的流水,费用过滤掉已经销账完成的费用\r\n        writeoffStatusString = \"Part\"\r\n      }\r\n\r\n      let response = await selectListCharge({\r\n        clearingCompanyId: this.form.clearingCompanyId,\r\n        isRecievingOrPaying: this.form.isRecievingOrPaying,\r\n        bankRecordId: this.form.bankRecordId,\r\n        writeoffStatusString: writeoffStatusString,\r\n        sqdRaletiveRctList: this.form.sqdRaletiveRctList\r\n      })\r\n      if (response && response.length > 0) {\r\n\r\n        for (let item of response) {\r\n          if (this.form.bankCurrencyCode === item.dnCurrencyCode) {\r\n            item.exchangeRateShow = 1\r\n            item.exchangeRate = 1\r\n          } else {\r\n            let result = await this.getExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, \"\")\r\n            if (!result) {\r\n              // 没有找到对应的汇率\r\n              this.$message.error(\"系统中没有对应的汇率\")\r\n              this.loadingCharge = false\r\n              return\r\n            }\r\n            // 账单日期汇率\r\n            // let billExchangeRate = await this.getBillDataExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, item.currencyRateCalculateDate)\r\n            item.exchangeRate = result[0] === 1 ? Number(result[1]).toFixed(4) : Number(this.currencyWithPrecision(1).divide(result[1]).value).toFixed(4)\r\n            item.exchangeRateShow = result[0] === 1 ? result[1] : \"1/\" + result[1]\r\n          }\r\n        }\r\n        this.writeOffList = response\r\n        setTimeout(() => {\r\n          this.writeOffList.map(item => {\r\n            // 有销账记录的要勾选并显示上次销账信息\r\n            if (item.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              // 填入信息\r\n              // 本次拟销账金额\r\n              // 销账余额\r\n              item.writeoffFromDnBalance = item.midChargeBankWriteoff.writeoffFromDnBalance\r\n              item.sqdDnCurrencyBalance = item.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n              // 折算记账金额\r\n              item.writeoffFromBankBalance = item.midChargeBankWriteoff.writeoffFromBankBalance\r\n\r\n              this.selectedCharges.push(item)\r\n              this.alreadyWriteoffList.push(item)\r\n              this.$refs.writeOffTable.toggleRowSelection(item, true)\r\n            }\r\n          })\r\n\r\n          //排序\r\n          this.writeOffList.sort((a, b) => {\r\n            // 未审核排最后\r\n            if (a.isAccountConfirmed === \"0\" && b.isAccountConfirmed === \"1\") {\r\n              return 1\r\n            }\r\n            if (a.isAccountConfirmed === \"1\" && b.isAccountConfirmed === \"0\") {\r\n              return -1\r\n            }\r\n\r\n            // 如果a对象有 midChargeBankWriteoff.midChargeBankId，而b没有，则a排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId === null && b.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              return 1\r\n            }\r\n            // 如果b对象有 midChargeBankWriteoff.midChargeBankId，而a没有，则b排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId !== null && b.midChargeBankWriteoff.midChargeBankId === null) {\r\n              return -1\r\n            }\r\n            // 检查是否都有 midChargeBankWriteoff.midChargeBankId 属性\r\n            if (a.midChargeBankWriteoff.midChargeBankId && b.midChargeBankWriteoff.midChargeBankId) {\r\n              // 如果两个对象都有 midChargeBankWriteoff.midChargeBankId，则按 writeoffFromDnBalance 进行排序\r\n              return a.midChargeBankWriteoff.sqdDnCurrencyBalance - b.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n            } else {\r\n              // 如果有一个或两个对象缺少 midChargeBankWriteoff.midChargeBankId 属性，则保持原顺序\r\n              return 0\r\n            }\r\n          })\r\n        })\r\n        // this.$nextTick()\r\n      }\r\n      this.loadingCharge = false\r\n      this.showDetail = true\r\n    },\r\n    currencyWithPrecision(value, precision = 4) {\r\n      return currency(value, {precision: precision})\r\n    },\r\n    verify() {\r\n      /* if (this.form.clearingCompanyId === null) {\r\n        this.$message.warning(\"请输入结算公司\")\r\n        return\r\n      } */\r\n      if (this.form.isBankRecordLocked == 1) {\r\n        this.form.isBankRecordLocked = 0\r\n        this.form.verifyId = null\r\n        this.form.verifyTime = null\r\n      } else {\r\n        this.form.isBankRecordLocked = 1\r\n        this.form.verifyId = this.$store.state.user.sid\r\n        this.form.verifyTime = moment().format(\"yyyy-MM-DD\")\r\n      }\r\n\r\n      this.clearReceiveOrPay()\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$message.success(\"修改成功\")\r\n      })\r\n    }\r\n    ,\r\n    /** 查询记录公司账户出入账明细列表 */\r\n    getList() {\r\n      this.loading = true\r\n      // this.queryParams.chargeTypeId = 2\r\n      listBankrecord(this.queryParams).then(response => {\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    }\r\n    ,\r\n// 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    }\r\n    ,\r\n// 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankRecordId: null,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: \"0\",\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        invoiceNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        chargeType: \"订单\"\r\n      }\r\n      this.staffId = null\r\n      this.selectedBalanceAmount = 0\r\n      this.resetForm(\"form\")\r\n    }\r\n    ,\r\n    /** 搜索按钮操作 */\r\n    handleQuery(queryParams) {\r\n      this.queryParams = {\r\n        ...this.queryParams,\r\n        ...queryParams,\r\n        pageNum: 1  // 直接在合并时设置页码为1\r\n      }\r\n      this.getList()\r\n    }\r\n    ,\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    }\r\n    ,\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.bankRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    }\r\n    ,\r\n    // 对冲勾选\r\n    handleHedgingSelectionChange(selection) {\r\n\r\n    }\r\n    ,\r\n    // 复制对冲费用到销账列表\r\n    async handleAddHedging(row) {\r\n      // 创建一个新对象，避免引用原对象\r\n      const newRow = {...row}\r\n\r\n      // 添加汇率\r\n      if (this.form.bankCurrencyCode === newRow.dnCurrencyCode) {\r\n        newRow.exchangeRateShow = \"1\"\r\n        newRow.exchangeRate = 1\r\n      } else {\r\n        // 当天汇率\r\n        let result = await this.getExchangeRate(this.form.bankCurrencyCode, newRow.dnCurrencyCode, \"\")\r\n        if (!result) {\r\n          // 没有找到对应的汇率\r\n          this.$message.error(\"系统中没有对应的汇率\")\r\n          return\r\n        }\r\n        // 设置汇率值\r\n        newRow.exchangeRate = result[0] === 1 ? Number(result[1]).toFixed(4) : Number(this.currencyWithPrecision(1).divide(result[1]).value).toFixed(4)\r\n        newRow.exchangeRateShow = result[0] === 1 ? result[1].toString() : \"1/\" + result[1]\r\n      }\r\n\r\n      // 初始化销账相关字段\r\n      newRow.writeoffFromDnBalance = currency(newRow.sqdDnCurrencyBalance).value\r\n      newRow.writeoffFromBankBalance = currency(newRow.sqdDnCurrencyBalance).multiply(newRow.exchangeRate).value\r\n\r\n      // 确保中间表对象存在\r\n      if (!newRow.midChargeBankWriteoff) {\r\n        newRow.midChargeBankWriteoff = {\r\n          midChargeBankId: null,\r\n          writeoffStaffId: null,\r\n          writeoffTime: null\r\n        }\r\n      }\r\n\r\n      // 对冲的金额为负数 - 如果需要设置为负数，取消注释下面的代码\r\n      // newRow.writeoffFromDnBalance = -Math.abs(newRow.writeoffFromDnBalance);\r\n      // newRow.writeoffFromBankBalance = -Math.abs(newRow.writeoffFromBankBalance);\r\n\r\n      this.writeOffList.push(newRow)\r\n      this.hedgingData = this.hedgingData.filter(item => item.chargeId !== newRow.chargeId)\r\n    },\r\n    checkSelectableReimburse(row) {\r\n      return row.financeConfirmed == 1\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      // this.ids = selection.map(item => item.bankRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n\r\n      // 勾选时计算销账金额\r\n      this.selectedAmount = 0\r\n      // 取消销账列表\r\n      this.turnBackWriteoffList = []\r\n      selection.map(item => {\r\n        // 上次勾选的不受影响\r\n        if (this.selectedCharges.indexOf(item) === -1) {\r\n          // 自动填入拟销账金额\r\n          if (item.sqdDnCurrencyBalance !== null && item.sqdDnCurrencyBalance > 0) {\r\n            // 本次拟销账金额\r\n            item.writeoffFromDnBalance = currency(item.sqdDnCurrencyBalance).value\r\n            // 折算记账金额\r\n            item.writeoffFromBankBalance = currency(item.sqdDnCurrencyBalance).multiply(item.exchangeRate).value\r\n          } else {\r\n            item.writeoffFromDnBalance = currency(item.dnUnitRate).multiply(item.dnAmount).value\r\n            item.writeoffFromBankBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).multiply(item.exchangeRate).value\r\n          }\r\n        }\r\n      })\r\n      // 取消勾选的费用本次拟核销金额置为0\r\n      this.selectedCharges.map(item => {\r\n        if (selection.indexOf(item) === -1) {\r\n          item.writeoffFromDnBalance = 0\r\n          item.writeoffFromBankBalance = 0\r\n          item.sqdDnCurrencyBalanceShow = currency(item.sqdDnCurrencyBalance).value\r\n\r\n          // 找出取消销账的费用\r\n          if (this.alreadyWriteoffList.indexOf(item) !== -1) {\r\n            this.turnBackWriteoffList.push(item)\r\n          }\r\n        }\r\n      })\r\n\r\n      this.selectedCharges = selection\r\n    },\r\n    handleChargeSelectionChange(selection) {\r\n      // this.ids = selection.map(item => item.bankRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n\r\n      // 勾选时计算销账金额\r\n      this.selectedAmount = 0\r\n      // 取消销账列表\r\n      this.turnBackWriteoffList = []\r\n      selection.map(item => {\r\n        // 上次勾选的不受影响\r\n        if (this.selectedCharges.indexOf(item) === -1) {\r\n          // 自动填入拟销账金额\r\n          if (item.sqdDnCurrencyBalance !== null && item.sqdDnCurrencyBalance > 0) {\r\n            // 本次拟销账金额\r\n            item.writeoffFromDnBalance = currency(item.sqdDnCurrencyBalance).value\r\n            // 折算记账金额\r\n            item.writeoffFromBankBalance = currency(item.sqdDnCurrencyBalance).multiply(item.exchangeRate).value\r\n          } else {\r\n            item.writeoffFromDnBalance = currency(item.dnUnitRate).multiply(item.dnAmount).value\r\n            item.writeoffFromBankBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).multiply(item.exchangeRate).value\r\n          }\r\n        }\r\n      })\r\n      // 取消勾选的费用本次拟核销金额置为0\r\n      this.selectedCharges.map(item => {\r\n        if (selection.indexOf(item) === -1) {\r\n          item.writeoffFromDnBalance = 0\r\n          item.writeoffFromBankBalance = 0\r\n          item.sqdDnCurrencyBalanceShow = currency(item.sqdDnCurrencyBalance).value\r\n\r\n          // 找出取消销账的费用\r\n          if (this.alreadyWriteoffList.indexOf(item) !== -1) {\r\n            this.turnBackWriteoffList.push(item)\r\n          }\r\n        }\r\n      })\r\n\r\n      this.selectedCharges = selection\r\n    },\r\n    handleReimburseSelectionChange(selection) {\r\n      // this.ids = selection.map(item => item.bankRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n\r\n      // 勾选时计算销账金额\r\n      this.selectedAmount = 0\r\n      // 取消销账列表\r\n      this.turnBackWriteoffList = []\r\n      selection.map(item => {\r\n        // 上次勾选的不受影响\r\n        if (this.selectedReimburses.indexOf(item) === -1) {\r\n          // 自动填入拟销账金额\r\n          if (item.sqdDnCurrencyBalance !== null && item.sqdDnCurrencyBalance > 0) {\r\n            // 本次拟销账金额\r\n            item.writeoffFromDnBalance = currency(item.sqdDnCurrencyBalance).value\r\n            // 折算记账金额\r\n            item.writeoffFromBankBalance = currency(item.sqdDnCurrencyBalance).multiply(item.exchangeRate).value\r\n          } else {\r\n            item.writeoffFromDnBalance = currency(item.dnUnitRate).multiply(item.dnAmount).value\r\n            item.writeoffFromBankBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).multiply(item.exchangeRate).value\r\n          }\r\n        }\r\n      })\r\n      // 取消勾选的费用本次拟核销金额置为0\r\n      this.selectedReimburses.map(item => {\r\n        if (selection.indexOf(item) === -1) {\r\n          item.writeoffFromDnBalance = 0\r\n          item.writeoffFromBankBalance = 0\r\n          item.sqdDnCurrencyBalanceShow = currency(item.sqdDnCurrencyBalance).value\r\n\r\n          // 找出取消销账的费用\r\n          if (this.alreadyWriteoffList.indexOf(item) !== -1) {\r\n            this.turnBackWriteoffList.push(item)\r\n          }\r\n        }\r\n      })\r\n\r\n      this.selectedReimburses = selection\r\n    },\r\n    dbclick(row) {\r\n      this.queryParams.bankAccountCode = row.bankAccountCode\r\n      listBankrecord(this.queryParams).then(response => {\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n        this.statisticsOpen = false\r\n      })\r\n    }\r\n    ,\r\n    statisticsTimeSelect() {\r\n      // let requestMap = new Map()\r\n      let requestMap = {}\r\n      switch (this.timeStatistics) {\r\n        case 1:\r\n          requestMap.day = this.queryParams.params.day\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n        case 2:\r\n          requestMap.month = this.queryParams.params.month\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n        case 3:\r\n          requestMap.year = this.queryParams.params.year\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n        case 4:\r\n          requestMap.startTime = this.queryParams.timeArr[0] ? this.queryParams.timeArr[0] : null\r\n          requestMap.endTime = this.queryParams.timeArr[1] ? this.queryParams.timeArr[1] : null\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n\r\n      }\r\n    },\r\n    handleOpenAggregator() {\r\n      // listAggregatorRct(this.queryParams).then(response => {\r\n      //   this.aggregatorRctList = response\r\n      // })\r\n\r\n      this.openAggregator = true\r\n    },\r\n    accountFundStatistics() {\r\n      this.statisticsOpen = true\r\n      this.searchAccountFundStatistics()\r\n    }\r\n    ,\r\n    searchAccountFundStatistics(data) {\r\n      if (!data) {\r\n        data = {}\r\n      }\r\n      getAccountFundStatistics(data).then(response => {\r\n        this.statisticsList = response.data\r\n        this.totalRecievedUSD = 0\r\n        this.totalRecievedRMB = 0\r\n        this.totalPaidUSD = 0\r\n        this.totalPaidRMB = 0\r\n\r\n        let exchangeRate\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (!exchangeRate) {\r\n            if (a.localCurrency === \"RMB\"\r\n              && \"USD\" == a.overseaCurrency\r\n              && parseTime(a.validFrom) <= parseTime(new Date())\r\n              && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n              exchangeRate = currency(a.settleRate).divide(a.base).value\r\n            }\r\n          }\r\n        }\r\n        this.exchangeRate = exchangeRate\r\n\r\n        if (response.data) {\r\n          response.data.forEach(item => {\r\n            if (item.bankCurrencyCode === \"RMB\") {\r\n              this.totalRecievedRMB += item.totalRecieved\r\n              this.totalPaidRMB += item.totalPaid\r\n            }\r\n            if (item.bankCurrencyCode === \"USD\") {\r\n              this.totalRecievedUSD += item.totalRecieved\r\n              this.totalPaidUSD += item.totalPaid\r\n            }\r\n          })\r\n\r\n          // 计算付款抬头余额\r\n          this.paymentTitleBalances = this.calculatePaymentTitleBalances()\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    // 付款抬头余额统计函数\r\n    calculatePaymentTitleBalances() {\r\n      // 检查数据是否存在\r\n      if (!this.statisticsList || this.statisticsList.length === 0) {\r\n        return []\r\n      }\r\n\r\n      // 创建一个Map用于存储汇总数据\r\n      const summaryMap = new Map()\r\n\r\n      // 遍历统计数据进行汇总\r\n      this.statisticsList.forEach(item => {\r\n        // 获取付款抬头代码，如果不存在则使用\"未知\"\r\n        const titleCode = item.sqdPaymentTitleCode || \"未知\"\r\n        const currency = item.bankCurrencyCode || \"未知\"\r\n        const received = Number(item.totalRecieved) || 0\r\n        const paid = Number(item.totalPaid) || 0\r\n\r\n        // 如果Map中不存在该付款抬头，则创建一个新的记录\r\n        if (!summaryMap.has(titleCode)) {\r\n          summaryMap.set(titleCode, {\r\n            titleCode,\r\n            titleName: item.sqdPaymentTitleName || \"未知抬头\",\r\n            RMB: {received: 0, paid: 0, balance: 0},\r\n            USD: {received: 0, paid: 0, balance: 0}\r\n          })\r\n        }\r\n\r\n        // 更新对应货币的收入和支出\r\n        const record = summaryMap.get(titleCode)\r\n        if (currency === \"RMB\") {\r\n          record.RMB.received += received\r\n          record.RMB.paid += paid\r\n          record.RMB.balance = record.RMB.received - record.RMB.paid\r\n        } else if (currency === \"USD\") {\r\n          record.USD.received += received\r\n          record.USD.paid += paid\r\n          record.USD.balance = record.USD.received - record.USD.paid\r\n        }\r\n      })\r\n\r\n      // 将Map转换为数组并返回\r\n      return Array.from(summaryMap.values())\r\n    }\r\n    ,\r\n    handleImport() {\r\n      this.upload.title = \"用户导入\"\r\n      this.upload.open = true\r\n    }\r\n    ,\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true\r\n    }\r\n    ,\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false\r\n      this.upload.isUploading = false\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info(response.msg)\r\n      if (response.msg != \"全部上传成功\") {\r\n        this.download(\"system/freight/failList\", {}, `上传失败列表.xlsx`)\r\n      }\r\n      this.getList()\r\n    }\r\n    ,\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit()\r\n    }\r\n    ,\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.showDetail = false\r\n      this.open = true\r\n      this.title = \"新建银行流水\"\r\n      this.add = true\r\n      this.form.isRecievingOrPaying = \"0\"\r\n      this.form.paymentTypeCode = \"T/T\"\r\n    }\r\n    ,\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.add = false\r\n      this.reset()\r\n      const bankRecordId = row.bankRecordId || this.ids\r\n      getBankrecord(bankRecordId).then(response => {\r\n        this.form = response.data\r\n        this.form.chargeType = \"订单\"\r\n        this.open = true\r\n        this.title = \"查看银行流水-销账明细\"\r\n        this.companyList = [response.companyList]\r\n\r\n        this.updateWriteoffType(response.data.chargeTypeId)\r\n\r\n        if (this.form.isBankRecordLocked === \"1\" && ((response.data.sqdBillRecievedWriteoffBalance !== response.data.actualBankRecievedAmount) || (response.data.sqdBillPaidWriteoffBalance !== response.data.actualBankPaidAmount))) {\r\n          this.getWriteOffType(this.writeoffType)\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(async valid => {\r\n        // this.form.chargeTypeId = 2\r\n        /* if (this.form.clearingCompanyId === null) {\r\n          this.$message.warning(\"请输入结算公司\")\r\n          return\r\n        } */\r\n        if (valid) {\r\n          // 先上传图片\r\n          if (this.imageFile) {\r\n            console.log(\"upload\")\r\n            // Perform the upload first and wait for it to complete\r\n            await this.uploadImage()\r\n          }\r\n          // 收款时将付款信息清空,付款时将收款信息清空\r\n          this.clearReceiveOrPay()\r\n          if (this.form.bankRecordId != null) {\r\n            updateBankrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addBankrecord(this.form).then(response => {\r\n              this.form = response.data\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    uploadImage() {\r\n      return new Promise((resolve, reject) => {\r\n        this.customHttpRequest({\r\n          file: this.imageFile,\r\n          onSuccess: (response) => {\r\n            this.handleSuccess(response)\r\n            resolve(response)\r\n          },\r\n          onError: (error) => {\r\n            this.handleError(error)\r\n            reject(error)\r\n          }\r\n        })\r\n      })\r\n    }\r\n    ,\r\n    clearReceiveOrPay() {\r\n      if (this.form.isRecievingOrPaying === \"0\") {\r\n        // 初始化收款\r\n        this.form.actualBankPaidAmount = 0\r\n        this.form.bankPaidHandlingFee = 0\r\n        this.form.sqdBillPaidAmount = 0\r\n        this.form.billPaidWriteoffAmount = 0\r\n        this.form.bankPaidExchangeLost = 0\r\n        this.form.sqdBillPaidWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillRecievedWriteoffBalance === this.form.sqdBillRecievedAmount ?\r\n          this.form.writeoffStatus = -1 : this.form.sqdBillRecievedWriteoffBalance === 0 ?\r\n            this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      } else {\r\n        // 初始化付款\r\n        this.form.actualBankRecievedAmount = 0\r\n        this.form.bankRecievedHandlingFee = 0\r\n        this.form.sqdBillRecievedAmount = 0\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        this.form.bankRecievedExchangeLost = 0\r\n        this.form.sqdBillRecievedWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillPaidAmount === this.form.sqdBillPaidWriteoffBalance ?\r\n          this.form.writeoffStatus = -1 : this.form.sqdBillPaidWriteoffBalance === 0 ?\r\n            this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      }\r\n    }\r\n    ,\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const bankRecordIds = row.bankRecordId || this.ids\r\n      this.$confirm(\"是否确认删除记录公司账户出入账明细编号为\\\"\" + bankRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delBankrecord(bankRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    }\r\n    ,\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/bankrecord/export\", {\r\n        ...this.queryParams\r\n      }, `bankrecord_${new Date().getTime()}.xlsx`)\r\n    }\r\n    ,\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n    ,\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    }\r\n    ,\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    }\r\n    ,\r\n    handledbClick(row) {\r\n      this.handleUpdate(row)\r\n    }\r\n    ,\r\n    selectCompany(company) {\r\n      this.form.clearingCompanyId = company.companyId\r\n      this.form.sqdClearingCompanyShortname = company.companyShortName\r\n      this.showCompany = false\r\n    }\r\n    ,\r\n    async getExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re\r\n      let response\r\n      if (this.exchangeRateList && this.exchangeRateList.length > 0) {\r\n        response = this.exchangeRateList\r\n      } else {\r\n        this.exchangeRateList = await selectListExchangerate()\r\n        response = this.exchangeRateList\r\n      }\r\n\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n          if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n            re = [0, currency(a.buyRate).divide(a.base).value]\r\n          } else {\r\n            re = [1, currency(a.sellRate).divide(a.base).value]\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    }\r\n    ,\r\n    async getBillDataExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re = [0, 1]\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency == localCurrency || a.currency == localCurrency)\r\n            && (a.currency == overseaCurrency || a.localCurrency == overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(valueDate)\r\n            && parseTime(valueDate) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    }\r\n    ,\r\n    async exchangeRateShow(bankCurrencyCode, chargeCurrencyCode) {\r\n      if (bankCurrencyCode === chargeCurrencyCode) {\r\n        return 1\r\n      }\r\n      let result = await this.getExchangeRate(bankCurrencyCode, chargeCurrencyCode, \"\")\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        if (id) {\r\n          let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n          if (staff) {\r\n            return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n          } else {\r\n            return \"\"\r\n          }\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    }\r\n    ,\r\n    selectStaff(row) {\r\n      this.form.sqdRsStaffId = row.staff.staffId\r\n    }\r\n    ,\r\n    checkSelectable(row, index) {\r\n      return row.isAccountConfirmed === \"1\"\r\n    },\r\n    handleDialogOpened() {\r\n      // 在弹出层打开时，自动聚焦姓名输入框\r\n      this.$nextTick(() => {\r\n        const treeSelectInput = this.$refs.treeSelect.getInputElement()\r\n        if (treeSelectInput) {\r\n          treeSelectInput.focus()\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    isRecievingOrPayingNormalizer(node) {\r\n      return {\r\n        id: node.value,\r\n        label: node.label\r\n      }\r\n    }\r\n    ,\r\n    selectBankAccount(row) {\r\n      this.form.sqdPaymentTitleCode = row.sqdBelongToCompanyCode\r\n    }\r\n    ,\r\n    customHttpRequest(options) {\r\n      const formData = new FormData()\r\n      formData.append(\"file\", options.file)\r\n\r\n      request({\r\n        url: \"/system/bankrecord/uploadImg\",\r\n        method: \"post\",\r\n        data: formData\r\n      }).then(response => {\r\n        options.onSuccess(response, options.file)\r\n        this.form.slipFile = response.url\r\n      }).catch(error => {\r\n        options.onError(error)\r\n      })\r\n    }\r\n    ,\r\n    handleChange(file, fileList) {\r\n      const extension = file.name.substring(file.name.lastIndexOf(\".\"))\r\n      const newFileName = `${this.form.bankRecordNo}${extension}`\r\n      this.imageFile = new File([file.raw], newFileName, {type: file.type})\r\n    }\r\n    ,\r\n    handleSuccess(response, file, fileList) {\r\n      this.form.slipFile = response.url\r\n    }\r\n    ,\r\n    handleError(err, file, fileList) {\r\n      this.$message.error(\"Upload failed:\", err)\r\n    }\r\n    ,\r\n    // 添加处理折算记账金额变化的方法\r\n    handleWriteoffChange(row, value) {\r\n      // 确保值为两位小数\r\n      row.writeoffFromBankBalance = Number(value).toFixed(2)\r\n\r\n      // 自动计算本次拟销账金额（根据汇率反算）\r\n      if (row.exchangeRate) {\r\n        // 获取subtotal的小数位数\r\n        const subtotalStr = row.subtotal.toString()\r\n        const decimalPlaces = subtotalStr.includes(\".\") ?\r\n          subtotalStr.split(\".\")[1].length : 0\r\n\r\n        // 根据subtotal的小数位数来格式化writeoffFromDnBalance\r\n        const calculatedValue = currency(value).divide(row.exchangeRate).value\r\n        row.writeoffFromDnBalance = Number(calculatedValue).toFixed(decimalPlaces)\r\n      }\r\n    }\r\n  }\r\n  ,\r\n  computed: {\r\n    receiveRate() {\r\n      return this.form.actualBankRecievedAmount + this.form.bankRecievedHandlingFee + this.form.bankRecievedExchangeLost\r\n    }\r\n    ,\r\n    paidRate() {\r\n      return this.form.actualBankPaidAmount + this.form.bankPaidHandlingFee + this.form.bankPaidExchangeLost\r\n    }\r\n    ,\r\n    isLocked() {\r\n      return this.form.isBankRecordLocked == 1\r\n    }\r\n    ,\r\n    isBankSlipConfirmed() {\r\n      return this.form.slipConfirmed == 1\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.table-btn-group {\r\n  display: flex;\r\n\r\n  .table-btn-left {\r\n    display: flex;\r\n    width: 100%;\r\n  }\r\n\r\n  .table-btn-right {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.total {\r\n  display: flex;\r\n  width: 60%;\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n::v-deep .el-input-number.is-without-controls .el-input__inner {\r\n  background-color: rgb(255, 242, 204) !important;\r\n}\r\n\r\n::v-deep .vue-treeselect__value-container {\r\n  display: block;\r\n}\r\n\r\n//固定el-table中行高度,不会被内容撑高\r\n::v-deep .cell {\r\n  overflow: hidden; /* 隐藏超出部分 */\r\n  white-space: nowrap; /* 禁止内容换行 */\r\n  text-overflow: ellipsis; /* 使用省略号表示超出的内容 */\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAq2CA,IAAAA,WAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,SAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,IAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,OAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,QAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,WAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,KAAA,GAAAd,OAAA;AACA,IAAAe,IAAA,GAAAf,OAAA;AACA,IAAAgB,OAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,wBAAA,GAAAjB,OAAA;AACA,IAAAkB,UAAA,GAAAlB,OAAA;AACA,IAAAmB,OAAA,GAAAjB,sBAAA,CAAAF,OAAA;AACA,IAAAoB,uBAAA,GAAApB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAqB,IAAA;EACAC,UAAA;IAAAC,aAAA,EAAAA,eAAA;IAAAC,wBAAA,EAAAA,eAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,sBAAA,EAAAA,8CAAA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACAI,aAAA,EAAAC,gDAAA;MACAC,YAAA;MACAC,aAAA;MACAC,OAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACAC,OAAA;MACA;MACAC,cAAA;MAEA;MACAC,IAAA;QACAjC,YAAA;MACA;MACAkC,UAAA;MACAC,YAAA;MACA;MACAC,KAAA;QACAjC,wBAAA,GACA;UAAAkC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAvB,cAAA,GACA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnC,oBAAA,GACA;UAAAiC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;AACA;AACA;MACA;;MACAC,cAAA;MACAC,cAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,YAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,GAAA;MACAC,MAAA;QACA;QACA5D,IAAA;QACA;QACAD,KAAA;QACA;QACA8D,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,UAAA;MACAC,eAAA;MACAC,kBAAA;MACAC,WAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,wBAAA;MACAC,wBAAA;MACAC,aAAA;MACAC,OAAA;MACAC,mBAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,SAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,wBAAA;IACA;EACA;EACAC,KAAA;IACA7F,UAAA,WAAAA,WAAA8F,CAAA;MACA,IAAAA,CAAA;QACA,KAAAnG,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACA,8BAAAqG,iBAAAD,CAAA;MACA,KAAAE,kBAAA,CAAAF,CAAA;IACA;IACA;IACA,0CAAAG,6BAAAH,CAAA;MACA,KAAA/C,IAAA,CAAAxB,qBAAA,OAAA2E,iBAAA,OAAAnD,IAAA,CAAA9B,wBAAA,EAAA8C,GAAA,MAAAhB,IAAA,CAAA5B,uBAAA,EAAAgF,KAAA;IACA;IACA;IACA,yCAAAC,4BAAAN,CAAA;MACA,KAAA/C,IAAA,CAAAxB,qBAAA,OAAA2E,iBAAA,OAAAnD,IAAA,CAAA9B,wBAAA,EAAA8C,GAAA,MAAAhB,IAAA,CAAA5B,uBAAA,EAAAgF,KAAA;IACA;IACA;IACA,0CAAAE,6BAAAP,CAAA;MACA,KAAA/C,IAAA,CAAApB,8BAAA,OAAAuE,iBAAA,OAAAnD,IAAA,CAAAxB,qBAAA,EAAA+E,QAAA,MAAAvD,IAAA,CAAAtB,0BAAA,EAAA6E,QAAA,MAAAvD,IAAA,CAAA1B,wBAAA,EAAA8E,KAAA;IACA;IACA;IACA,sCAAAI,yBAAAT,CAAA;MACA,KAAA/C,IAAA,CAAAvB,iBAAA,OAAA0E,iBAAA,OAAAnD,IAAA,CAAA7B,oBAAA,EAAAoF,QAAA,MAAAvD,IAAA,CAAA3B,mBAAA,EAAA+E,KAAA;IACA;IACA;IACA,qCAAAK,wBAAAV,CAAA;MACA,KAAA/C,IAAA,CAAAvB,iBAAA,OAAA0E,iBAAA,OAAAnD,IAAA,CAAA7B,oBAAA,EAAAoF,QAAA,MAAAvD,IAAA,CAAA3B,mBAAA,EAAA+E,KAAA;IACA;IACA;IACA,sCAAAM,yBAAAX,CAAA;MACA,KAAA/C,IAAA,CAAAnB,0BAAA,OAAAsE,iBAAA,OAAAnD,IAAA,CAAAvB,iBAAA,EAAA8E,QAAA,MAAAvD,IAAA,CAAArB,sBAAA,EAAA4E,QAAA,MAAAvD,IAAA,CAAAzB,oBAAA,EAAA6E,KAAA;IACA;IAEA;IACA,uCAAAO,0BAAAZ,CAAA;MACA,KAAA/C,IAAA,CAAApB,8BAAA,OAAAuE,iBAAA,OAAAnD,IAAA,CAAAxB,qBAAA,EAAA+E,QAAA,MAAAvD,IAAA,CAAAtB,0BAAA,EAAA6E,QAAA,MAAAvD,IAAA,CAAA1B,wBAAA,EAAA8E,KAAA;IACA;IACA;IACA,mCAAAQ,sBAAAb,CAAA;MACA,KAAA/C,IAAA,CAAAnB,0BAAA,OAAAsE,iBAAA,OAAAnD,IAAA,CAAAvB,iBAAA,EAAA8E,QAAA,MAAAvD,IAAA,CAAArB,sBAAA,EAAA4E,QAAA,MAAAvD,IAAA,CAAAzB,oBAAA,EAAA6E,KAAA;IACA;IAEA;IACA,4CAAAS,+BAAAd,CAAA;MACA,KAAA/C,IAAA,CAAApB,8BAAA,OAAAuE,iBAAA,OAAAnD,IAAA,CAAAxB,qBAAA,EAAA+E,QAAA,MAAAvD,IAAA,CAAAtB,0BAAA,EAAA6E,QAAA,MAAAvD,IAAA,CAAA1B,wBAAA,EAAA8E,KAAA;IACA;IACA;IACA,wCAAAU,2BAAAf,CAAA;MACA,KAAA/C,IAAA,CAAAnB,0BAAA,OAAAsE,iBAAA,OAAAnD,IAAA,CAAAvB,iBAAA,EAAA8E,QAAA,MAAAvD,IAAA,CAAArB,sBAAA,EAAA4E,QAAA,MAAAvD,IAAA,CAAAzB,oBAAA,EAAA6E,KAAA;IACA;IACAxB,eAAA;MACAmC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAC,KAAA;QACA;QACA,KAAAlE,IAAA,CAAAtB,0BAAA;QACA;QACA,KAAAsB,IAAA,CAAArB,sBAAA;;QAEA;QACA,KAAAqD,qBAAA;QAEA,IAAAtD,0BAAA;QACA,IAAAC,sBAAA;QAEA,IAAAuD,wBAAA;QACA,IAAAD,wBAAA;QACA;QACA,IAAAkC,SAAA;QACAH,MAAA,CAAAI,OAAA,WAAAC,GAAA;UACA,IAAAH,KAAA,CAAAlE,IAAA,CAAAtC,mBAAA,IAAA2G,GAAA,CAAA3G,mBAAA;YACAyG,SAAA;UACA;QACA;QAEAH,MAAA,CAAAM,GAAA,WAAAC,IAAA;UACA;UACA;UACA,IAAAA,IAAA,CAAA1D,YAAA;YACA;YACA,IAAA2D,WAAA,GAAAD,IAAA,CAAAE,QAAA,CAAAC,QAAA;YACA,IAAAC,aAAA,GAAAH,WAAA,CAAAI,QAAA,QACAJ,WAAA,CAAAK,KAAA,SAAAC,MAAA;;YAEA;YACA,IAAAC,eAAA,OAAA5B,iBAAA,EAAAoB,IAAA,CAAAS,uBAAA,EAAAC,MAAA,CAAAV,IAAA,CAAA1D,YAAA,EAAAuC,KAAA;YACAmB,IAAA,CAAAW,qBAAA,GAAAC,MAAA,CAAAJ,eAAA,EAAAK,OAAA,CAAAT,aAAA;UACA;UAEA,IAAAT,KAAA,CAAAlE,IAAA,CAAAtC,mBAAA,IAAA6G,IAAA,CAAA7G,mBAAA;YACA;YACAgB,0BAAA,OAAAyE,iBAAA,EAAAoB,IAAA,CAAAS,uBAAA;cAAAK,SAAA;YAAA,GAAArE,GAAA,CAAAtC,0BAAA,EAAA0E,KAAA;YACA;YACAzE,sBAAA,OAAAwE,iBAAA,EAAAoB,IAAA,CAAAS,uBAAA;cAAAK,SAAA;YAAA,GAAArE,GAAA,CAAArC,sBAAA,EAAAyE,KAAA;YAEA,IAAAmB,IAAA,CAAAe,cAAA;cACArD,wBAAA,OAAAkB,iBAAA,EAAAlB,wBAAA;gBAAAoD,SAAA;cAAA,GAAArE,GAAA,CAAAuD,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;cACAlB,wBAAA,OAAAiB,iBAAA,EAAAjB,wBAAA;gBAAAmD,SAAA;cAAA,GAAArE,GAAA,CAAAuD,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;UACA,WAAAe,SAAA;YACA;YACAzF,0BAAA,OAAAyE,iBAAA,EAAAoB,IAAA,CAAAS,uBAAA;cAAAK,SAAA;YAAA,GAAArE,GAAA,CAAAtC,0BAAA,EAAA0E,KAAA;YACA;YACAzE,sBAAA,OAAAwE,iBAAA,EAAAoB,IAAA,CAAAS,uBAAA;cAAAK,SAAA;YAAA,GAAArE,GAAA,CAAArC,sBAAA,EAAAyE,KAAA;YAEA,IAAAmB,IAAA,CAAAe,cAAA;cACArD,wBAAA,OAAAkB,iBAAA,EAAAlB,wBAAA;gBAAAoD,SAAA;cAAA,GAAArE,GAAA,CAAAuD,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;cACAlB,wBAAA,OAAAiB,iBAAA,EAAAjB,wBAAA;gBAAAmD,SAAA;cAAA,GAAArE,GAAA,CAAAuD,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;UACA;YACA;YACA1E,0BAAA,OAAAyE,iBAAA,EAAAzE,0BAAA;cAAA2G,SAAA;YAAA,GAAA9B,QAAA,CAAAgB,IAAA,CAAAS,uBAAA,EAAA5B,KAAA;YACA;YACAzE,sBAAA,OAAAwE,iBAAA,EAAAxE,sBAAA;cAAA0G,SAAA;YAAA,GAAA9B,QAAA,CAAAgB,IAAA,CAAAS,uBAAA,EAAA5B,KAAA;YAEA,IAAAmB,IAAA,CAAAe,cAAA;cACArD,wBAAA,OAAAkB,iBAAA,EAAAlB,wBAAA;gBAAAoD,SAAA;cAAA,GAAA9B,QAAA,CAAAgB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;cACAlB,wBAAA,OAAAiB,iBAAA,EAAAjB,wBAAA;gBAAAmD,SAAA;cAAA,GAAA9B,QAAA,CAAAgB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;UACA;;UAEA;UACAc,KAAA,CAAAnC,cAAA;UAEA,IAAAoB,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,EAAAtC,KAAA,KAAAmB,IAAA,CAAAW,qBAAA,GAAAX,IAAA,CAAAzF,cAAA,SAAAyF,IAAA,CAAAW,qBAAA,OAAAX,IAAA,CAAAzF,cAAA,SAAAyF,IAAA,CAAAzF,cAAA;QACA;;QAEA;QACA,KAAAkB,IAAA,CAAAtB,0BAAA,OAAAyE,iBAAA,EAAAzE,0BAAA;UAAA2G,SAAA;QAAA,GAAAjC,KAAA;QACA;QACA,KAAApD,IAAA,CAAArB,sBAAA,OAAAwE,iBAAA,EAAAxE,sBAAA;UAAA0G,SAAA;QAAA,GAAAjC,KAAA;QAEA,KAAAlB,wBAAA,OAAAiB,iBAAA,EAAAjB,wBAAA;UAAAmD,SAAA;QAAA,GAAAjC,KAAA;QACA,KAAAnB,wBAAA,OAAAkB,iBAAA,EAAAlB,wBAAA;UAAAoD,SAAA;QAAA,GAAAjC,KAAA;MAEA;MACAuC,IAAA;IACA;IACA9D,kBAAA;MACAkC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAA2B,MAAA;QACA;QACA,KAAA5F,IAAA,CAAAtB,0BAAA;QACA;QACA,KAAAsB,IAAA,CAAArB,sBAAA;;QAEA;QACA,KAAAqD,qBAAA;QAEA,IAAAtD,0BAAA;QACA,IAAAC,sBAAA;QAEA,IAAAuD,wBAAA;QACA,IAAAD,wBAAA;QAEA+B,MAAA,CAAAM,GAAA,WAAAC,IAAA;UACA;UACAsB,OAAA,CAAAC,GAAA,CAAAvB,IAAA,CAAAwB,cAAA;UACArH,0BAAA,OAAAyE,iBAAA,EAAAoB,IAAA,CAAAwB,cAAA;YAAAV,SAAA;UAAA,GAAArE,GAAA,CAAAtC,0BAAA,EAAA0E,KAAA;UACA;UACAzE,sBAAA,OAAAwE,iBAAA,EAAAoB,IAAA,CAAAwB,cAAA;YAAAV,SAAA;UAAA,GAAArE,GAAA,CAAArC,sBAAA,EAAAyE,KAAA;UAEAnB,wBAAA,OAAAkB,iBAAA,EAAAlB,wBAAA;YAAAoD,SAAA;UAAA,GAAArE,GAAA,CAAAuD,IAAA,CAAAwB,cAAA,EAAA3C,KAAA;;UAEA;UACAwC,MAAA,CAAA7D,cAAA;QACA;;QAEA;QACA,KAAA/B,IAAA,CAAAtB,0BAAA,OAAAyE,iBAAA,EAAAzE,0BAAA;UAAA2G,SAAA;QAAA,GAAAjC,KAAA;QACA;QACA,KAAApD,IAAA,CAAArB,sBAAA,OAAAwE,iBAAA,EAAAxE,sBAAA;UAAA0G,SAAA;QAAA,GAAAjC,KAAA;QAEA,KAAAlB,wBAAA,OAAAiB,iBAAA,EAAAjB,wBAAA;UAAAmD,SAAA;QAAA,GAAAjC,KAAA;QACA,KAAAnB,wBAAA,OAAAkB,iBAAA,EAAAlB,wBAAA;UAAAoD,SAAA;QAAA,GAAAjC,KAAA;MAEA;MACAuC,IAAA;IACA;EACA;EACAK,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA;IACA;IACA;IACA;IACA;IACAC,cAAA,CAAAC,QAAA;IACA,SAAAlK,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8G,gBAAA,CAAAkC,MAAA,cAAA7I,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAsK,SAAA,CAAAxD,gBAAA;MACAsD,cAAA,CAAAC,QAAA;IACA;EACA;EAEAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA,IAAAC,oCAAA;UAAA;YAAAX,MAAA,CAAA1D,gBAAA,GAAAkE,QAAA,CAAAI,IAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EAEAS,WAAA,WAAAA,YAAA;IACA,KAAAC,SAAA;EACA;EAEAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAC,YAAA;QACA,KAAAD,IAAA,GAAAA,IAAA;MACA;QACA,KAAAE,QAAA,CAAAC,IAAA;MACA;IACA;IACA1E,kBAAA,WAAAA,mBAAAF,CAAA;MACA;MACA,IAAAjH,IAAA;MACA,KAAAG,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8L,cAAA,CAAAxD,OAAA,WAAAG,IAAA;QACA,IAAAA,IAAA,CAAAxG,YAAA,IAAAgF,CAAA;UACAjH,IAAA,GAAAyI,IAAA;QACA,WAAAA,IAAA,CAAAsD,QAAA;UACAtD,IAAA,CAAAsD,QAAA,CAAAzD,OAAA,WAAA0D,CAAA;YACA,IAAAA,CAAA,CAAA/J,YAAA,IAAAgF,CAAA;cACAjH,IAAA,GAAAyI,IAAA;YACA;UACA;QACA;MACA;MACA,IAAAzI,IAAA;QACA,IAAAA,IAAA,CAAAiC,YAAA,UAAAjC,IAAA,CAAAiM,QAAA;UACA,KAAA9H,UAAA;UACA,KAAAC,YAAA;QACA,WAAApE,IAAA,CAAAiC,YAAA,UAAAjC,IAAA,CAAAiM,QAAA;UACA,KAAA9H,UAAA;UACA,KAAAC,YAAA;QACA;MACA;QACA,KAAAD,UAAA;MACA;IACA;IACA+H,wBAAA,YAAAC,yBAAA;MAAA,SAAAD,yBAAAE,EAAA;QAAA,OAAAD,yBAAA,CAAAE,KAAA,OAAAC,SAAA;MAAA;MAAAJ,wBAAA,CAAAtD,QAAA;QAAA,OAAAuD,yBAAA,CAAAvD,QAAA;MAAA;MAAA,OAAAsD,wBAAA;IAAA,YAAAzK,MAAA;MACAA,MAAA,CAAA8K,MAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAhL,MAAA,CAAA8K,MAAA;MACA,KAAA/K,WAAA,CAAAC,MAAA,GAAAA,MAAA;MACA,OAAAyK,wBAAA,MAAA1K,WAAA;IACA;IACAkL,eAAA,WAAAA,gBAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAC,iBAAA;UACA;QACA;UACA,KAAAC,mBAAA;UACA;MACA;IACA;IACAA,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAArM,aAAA;MACA,KAAA4F,aAAA;MAEA,IAAA0G,gCAAA;QACAzG,OAAA,OAAApC,IAAA,CAAAN,YAAA;QACAF,kBAAA,OAAAQ,IAAA,CAAAR,kBAAA;QACAsJ,YAAA,OAAA9I,IAAA,CAAA8I;MACA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAArM,aAAA,GAAAyM,QAAA,CAAAC,IAAA;QAEAL,MAAA,CAAAzG,aAAA;QACAyG,MAAA,CAAAjH,UAAA;QAEAiH,MAAA,CAAA9G,WAAA;QACA8G,MAAA,CAAAM,SAAA;UACAN,MAAA,CAAArM,aAAA,CAAA+H,GAAA,WAAAC,IAAA;YACAqE,MAAA,CAAA9G,WAAA,OAAAqB,iBAAA,EAAAoB,IAAA,CAAAwB,cAAA,EAAA/E,GAAA,CAAA4H,MAAA,CAAA9G,WAAA,EAAAsB,KAAA;YACA,IAAAmB,IAAA,CAAAuE,YAAA,KAAAF,MAAA,CAAA5I,IAAA,CAAA8I,YAAA;cACAF,MAAA,CAAAO,KAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAA9E,IAAA;YACA;UACA;QACA;QAEAqE,MAAA,CAAArM,aAAA,CAAA+M,IAAA,WAAAC,CAAA,EAAAC,CAAA;UACA,IAAAD,CAAA,CAAAT,YAAA,KAAAF,MAAA,CAAA5I,IAAA,CAAA8I,YAAA,IAAAU,CAAA,CAAAV,YAAA,KAAAF,MAAA,CAAA5I,IAAA,CAAA8I,YAAA;YACA;UACA,WAAAS,CAAA,CAAAT,YAAA,KAAAF,MAAA,CAAA5I,IAAA,CAAA8I,YAAA,IAAAU,CAAA,CAAAV,YAAA,KAAAF,MAAA,CAAA5I,IAAA,CAAA8I,YAAA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAW,uBAAA,WAAAA,wBAAAC,GAAA,EAAAC,QAAA;MACA;MACAD,GAAA,CAAAE,gBAAA,GAAAD,QAAA;IAEA;IAEAE,SAAA,WAAAA,UAAAzG,KAAA;MACA,QAAA0G,KAAA,CAAA3E,MAAA,CAAA/B,KAAA;IACA;IAEA2G,oBAAA,WAAAA,qBAAAL,GAAA;MACA;MACA,IAAAM,OAAA;MACA,IAAAC,OAAA;MAEA,IAAA7G,KAAA,GAAAsG,GAAA,CAAAE,gBAAA;MACA,SAAAC,SAAA,CAAAzG,KAAA;QACA;QACA,IAAAA,MAAA,GAAA8G,UAAA,CAAAR,GAAA,CAAAE,gBAAA;QACA,IAAAE,KAAA,CAAA1G,MAAA,KAAAA,MAAA,GAAA4G,OAAA,IAAA5G,MAAA,GAAA6G,OAAA;UACA,KAAAvC,QAAA,CAAAyC,KAAA,mCAAAC,MAAA,CAAAJ,OAAA,cAAAI,MAAA,CAAAH,OAAA;UACA;UACAP,GAAA,CAAAE,gBAAA,GAAAF,GAAA,CAAA7I,YAAA,CAAA6D,QAAA;QACA;UACA;UACAgF,GAAA,CAAA7I,YAAA,GAAAuC,MAAA;QACA;MACA;QACA;QACA;QACA,IAAAiH,KAAA,GAAAjH,KAAA,CAAAiH,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,IAAA,GAAAJ,UAAA,CAAAG,KAAA;UACA,IAAAC,IAAA;YACAZ,GAAA,CAAA7I,YAAA,QAAAyJ,IAAA,EAAAlF,OAAA;UACA;QACA;MACA;IACA;IAEAmF,cAAA,WAAAA,eAAAb,GAAA;MAAA,IAAAc,MAAA;MAAA,WAAAjE,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+D,SAAA;QAAA,WAAAhE,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8D,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5D,IAAA,GAAA4D,SAAA,CAAA3D,IAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAA3D,IAAA;cAAA,OAGA,IAAA4D,kBAAA;gBAAArJ,GAAA,EAAAmI,GAAA,CAAAmB;cAAA;YAAA;cAAAF,SAAA,CAAA3D,IAAA;cAAA;YAAA;cAAA2D,SAAA,CAAA5D,IAAA;cAAA4D,SAAA,CAAAG,EAAA,GAAAH,SAAA;cAAAA,SAAA,CAAA3D,IAAA;cAAA,OAGA,IAAA+D,4BAAA;gBACAjC,YAAA,EAAAY,GAAA,CAAAZ,YAAA;gBACA+B,QAAA;gBACAnN,mBAAA,EAAA8M,MAAA,CAAA/B,IAAA;gBACAuC,YAAA,EAAAtB,GAAA,CAAAsB;cACA;YAAA;cAAAL,SAAA,CAAA3D,IAAA;cAAA,OAGAwD,MAAA,CAAAvE,OAAA;YAAA;YAAA;cAAA,OAAA0E,SAAA,CAAAxD,IAAA;UAAA;QAAA,GAAAsD,QAAA;MAAA;IACA;IAEAQ,YAAA,WAAAA,aAAAxC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAxC,OAAA;UACA;QACA;UACA;MACA;IACA;IAEAiF,SAAA,EAAAA,eAAA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA;;MAEA,KAAAC,iBAAA;MACA,SAAArL,IAAA,CAAAsL,aAAA;QACA,KAAAtL,IAAA,CAAAsL,aAAA;QACA,IAAAP,4BAAA,OAAA/K,IAAA,EAAA+I,IAAA,WAAAC,QAAA;UACAoC,MAAA,CAAA1D,QAAA,CAAA6D,OAAA;QACA;MACA;QACA,KAAAvL,IAAA,CAAAsL,aAAA;QACA,IAAAP,4BAAA,OAAA/K,IAAA,EAAA+I,IAAA,WAAAC,QAAA;UACAoC,MAAA,CAAA1D,QAAA,CAAA6D,OAAA;QACA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA/C,IAAA;MAAA,IAAAgD,MAAA;MACA,KAAAJ,iBAAA;MACA;MACA;AACA;AACA;AACA;AACA;;MAEA;MACA,IAAAK,MAAA,OAAAC,GAAA;MACA,KAAA/J,eAAA,CAAA0C,GAAA,WAAAC,IAAA;QAAA,OAAAmH,MAAA,CAAA1K,GAAA,CAAAuD,IAAA,CAAAqH,QAAA;MAAA;MACA,IAAAC,MAAA;MACAH,MAAA,CAAAtH,OAAA,WAAAG,IAAA;QAAA,OAAAsH,MAAA,CAAAC,IAAA,CAAAvH,IAAA;MAAA;MACA,KAAAvE,IAAA,CAAAR,kBAAA,GAAAqM,MAAA,CAAAE,IAAA;MAEA,KAAA/L,IAAA,CAAAH,YAAA,OAAAmM,eAAA,IAAAC,MAAA;MACA,IAAAlB,4BAAA,OAAA/K,IAAA,EAAA+I,IAAA,WAAAC,QAAA;QACAyC,MAAA,CAAAS,MAAA,CAAAC,UAAA;QACAV,MAAA,CAAApO,IAAA;QACAoO,MAAA,CAAAxF,OAAA;QAEA,IAAAwC,IAAA;UACA;UACA,IAAA2D,aAAA,GAAAX,MAAA,CAAApJ,mBAAA,CAAAgK,MAAA,WAAA3C,GAAA;YACA,QAAA+B,MAAA,CAAA7J,eAAA,CAAA0K,IAAA,WAAAC,QAAA;cAAA,OAAAA,QAAA,CAAAC,QAAA,KAAA9C,GAAA,CAAA8C,QAAA;YAAA;UACA;UACA,IAAAC,0BAAA;YAAAC,YAAA,EAAAN;UAAA;;UAEA;UACA,IAAAO,sBAAA;UACAlB,MAAA,CAAA7J,eAAA,GAAA6J,MAAA,CAAA7J,eAAA,CAAA0C,GAAA,WAAAC,IAAA;YACA;YACAA,IAAA,CAAAgB,oBAAA,OAAApC,iBAAA,MAAAA,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,GAAAnC,QAAA,CAAAgB,IAAA,CAAAW,qBAAA,EAAA9B,KAAA;YACA;YACAmB,IAAA,CAAAqI,oBAAA,GAAAnB,MAAA,CAAAzL,IAAA,CAAA/B,gBAAA;YACAsG,IAAA,CAAAzF,cAAA,OAAAqE,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA,IAAAyJ,qBAAA;YACAA,qBAAA,CAAAL,QAAA,GAAAjI,IAAA,CAAAiI,QAAA;YACAK,qBAAA,CAAA/D,YAAA,GAAA2C,MAAA,CAAAzL,IAAA,CAAA8I,YAAA;YACA+D,qBAAA,CAAA3H,qBAAA,GAAAX,IAAA,CAAAW,qBAAA;YACA2H,qBAAA,CAAAC,mBAAA,GAAAvI,IAAA,CAAAqF,gBAAA;YACAiD,qBAAA,CAAA7H,uBAAA,GAAAT,IAAA,CAAAS,uBAAA;YACA6H,qBAAA,CAAAE,WAAA,GAAAxI,IAAA,CAAA1D,YAAA;YAEA8L,sBAAA,CAAAb,IAAA,CAAAe,qBAAA;YAEA,OAAAtI,IAAA;UACA;UAEA,IAAAyI,wBAAA;YACAN,YAAA,EAAAjB,MAAA,CAAA7J,eAAA;YACAqL,yBAAA,EAAAN;UACA,GAAA5D,IAAA,WAAAC,QAAA;YACA;YACA,IAAAkE,gBAAA,EAAAzB,MAAA,CAAAzL,IAAA,CAAAR,kBAAA,CAAAqF,KAAA;UACA;QACA;UACA,IAAA/I,IAAA,GAAA2P,MAAA,CAAA5J,kBAAA,CAAAyC,GAAA,WAAAC,IAAA;YACAA,IAAA,CAAAuE,YAAA,GAAA2C,MAAA,CAAAzL,IAAA,CAAA8I,YAAA;YACA,OAAAvE,IAAA;UACA;UACA,IAAA4I,4BAAA,EAAArR,IAAA,EAAAiN,IAAA,WAAAC,QAAA;YACAyC,MAAA,CAAA/D,QAAA,CAAA6D,OAAA;YACAE,MAAA,CAAA9C,mBAAA;UACA;QACA;MACA;IACA;IACAyE,iBAAA,WAAAA,kBAAAhK,KAAA,EAAAiK,YAAA;MACA,WAAAlK,iBAAA,EAAAC,KAAA;QACAkK,SAAA;QACA;QACAC,MAAA,EAAAF,YAAA;QACAhI,SAAA;MACA,GAAA4G,MAAA;IACA;IAEAuB,eAAA,WAAAA,gBAAA;MACA;AACA;AACA;AACA;AACA;IAJA,CAKA;IAEAC,aAAA,WAAAA,cAAA,GACA;IAEAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,WAAA,QAAA5N,IAAA,CAAAtC,mBAAA;MACA,IAAAmQ,WAAA,GAAAD,WAAA;MAEA,IAAAE,qBAAA,MAAAC,cAAA,CAAAvH,OAAA,MAAAuH,cAAA,CAAAvH,OAAA,WAAAxG,IAAA;QAAAtC,mBAAA,EAAAmQ;MAAA,IAAA9E,IAAA,WAAAC,QAAA;QACA2E,MAAA,CAAAhL,WAAA,GAAAqG,QAAA,GAAAA,QAAA,CAAAqD,MAAA,WAAA9H,IAAA;UAAA,OACA,CAAAoJ,MAAA,CAAArR,YAAA,CAAAgQ,IAAA,WAAA0B,QAAA;YAAA,OAAAA,QAAA,CAAAxB,QAAA,KAAAjI,IAAA,CAAAiI,QAAA;UAAA;QAAA,CACA;MACA;IACA;IAEAyB,aAAA,WAAAA,cAAA,GACA;IAEAC,KAAA,WAAAA,MAAA,GACA;IAEA/K,QAAA,EAAAA,iBAAA;IACAuF,iBAAA,WAAAA,kBAAA;MAAA,IAAAyF,MAAA;MAAA,WAAA5H,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0H,SAAA;QAAA,IAAAC,oBAAA,EAAArF,QAAA,EAAAsF,SAAA,EAAAC,KAAA,EAAAhK,IAAA,EAAAiK,MAAA;QAAA,WAAA/H,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA6H,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3H,IAAA,GAAA2H,SAAA,CAAA1H,IAAA;YAAA;cACAmH,MAAA,CAAA7R,YAAA;cACA6R,MAAA,CAAA9L,mBAAA;cACA8L,MAAA,CAAAhM,aAAA;cACA;;cAEA,IAAAgM,MAAA,CAAAnO,IAAA,CAAAtC,mBAAA,YAAAyQ,MAAA,CAAAnO,IAAA,CAAApB,8BAAA;gBACAyP,oBAAA;cACA,WAAAF,MAAA,CAAAnO,IAAA,CAAAtC,mBAAA,YAAAyQ,MAAA,CAAAnO,IAAA,CAAAnB,0BAAA;gBACAwP,oBAAA;cACA,WAAAF,MAAA,CAAAnO,IAAA,CAAAtB,0BAAA,aAAAyP,MAAA,CAAAnO,IAAA,CAAArB,sBAAA;gBACA;gBACA0P,oBAAA;cACA;cAAAK,SAAA,CAAA1H,IAAA;cAAA,OAEA,IAAA2H,0BAAA;gBACA9Q,iBAAA,EAAAsQ,MAAA,CAAAnO,IAAA,CAAAnC,iBAAA;gBACAH,mBAAA,EAAAyQ,MAAA,CAAAnO,IAAA,CAAAtC,mBAAA;gBACAoL,YAAA,EAAAqF,MAAA,CAAAnO,IAAA,CAAA8I,YAAA;gBACAuF,oBAAA,EAAAA,oBAAA;gBACA7O,kBAAA,EAAA2O,MAAA,CAAAnO,IAAA,CAAAR;cACA;YAAA;cANAwJ,QAAA,GAAA0F,SAAA,CAAAxH,IAAA;cAAA,MAOA8B,QAAA,IAAAA,QAAA,CAAAlE,MAAA;gBAAA4J,SAAA,CAAA1H,IAAA;gBAAA;cAAA;cAAAsH,SAAA,OAAAM,2BAAA,CAAApI,OAAA,EAEAwC,QAAA;cAAA0F,SAAA,CAAA3H,IAAA;cAAAuH,SAAA,CAAAO,CAAA;YAAA;cAAA,KAAAN,KAAA,GAAAD,SAAA,CAAAvL,CAAA,IAAA+L,IAAA;gBAAAJ,SAAA,CAAA1H,IAAA;gBAAA;cAAA;cAAAzC,IAAA,GAAAgK,KAAA,CAAAnL,KAAA;cAAA,MACA+K,MAAA,CAAAnO,IAAA,CAAA/B,gBAAA,KAAAsG,IAAA,CAAAe,cAAA;gBAAAoJ,SAAA,CAAA1H,IAAA;gBAAA;cAAA;cACAzC,IAAA,CAAAqF,gBAAA;cACArF,IAAA,CAAA1D,YAAA;cAAA6N,SAAA,CAAA1H,IAAA;cAAA;YAAA;cAAA0H,SAAA,CAAA1H,IAAA;cAAA,OAEAmH,MAAA,CAAAY,eAAA,CAAAZ,MAAA,CAAAnO,IAAA,CAAA/B,gBAAA,EAAAsG,IAAA,CAAAe,cAAA;YAAA;cAAAkJ,MAAA,GAAAE,SAAA,CAAAxH,IAAA;cAAA,IACAsH,MAAA;gBAAAE,SAAA,CAAA1H,IAAA;gBAAA;cAAA;cACA;cACAmH,MAAA,CAAAzG,QAAA,CAAAyC,KAAA;cACAgE,MAAA,CAAAhM,aAAA;cAAA,OAAAuM,SAAA,CAAAM,MAAA;YAAA;cAGA;cACA;cACAzK,IAAA,CAAA1D,YAAA,GAAA2N,MAAA,YAAArJ,MAAA,CAAAqJ,MAAA,KAAApJ,OAAA,MAAAD,MAAA,CAAAgJ,MAAA,CAAAc,qBAAA,IAAAhK,MAAA,CAAAuJ,MAAA,KAAApL,KAAA,EAAAgC,OAAA;cACAb,IAAA,CAAAqF,gBAAA,GAAA4E,MAAA,YAAAA,MAAA,aAAAA,MAAA;YAAA;cAAAE,SAAA,CAAA1H,IAAA;cAAA;YAAA;cAAA0H,SAAA,CAAA1H,IAAA;cAAA;YAAA;cAAA0H,SAAA,CAAA3H,IAAA;cAAA2H,SAAA,CAAA5D,EAAA,GAAA4D,SAAA;cAAAJ,SAAA,CAAAY,CAAA,CAAAR,SAAA,CAAA5D,EAAA;YAAA;cAAA4D,SAAA,CAAA3H,IAAA;cAAAuH,SAAA,CAAAa,CAAA;cAAA,OAAAT,SAAA,CAAAU,MAAA;YAAA;cAGAjB,MAAA,CAAA7R,YAAA,GAAA0M,QAAA;cACAqG,UAAA;gBACAlB,MAAA,CAAA7R,YAAA,CAAAgI,GAAA,WAAAC,IAAA;kBACA;kBACA,IAAAA,IAAA,CAAAsI,qBAAA,CAAAyC,eAAA;oBACA;oBACA;oBACA;oBACA/K,IAAA,CAAAW,qBAAA,GAAAX,IAAA,CAAAsI,qBAAA,CAAA3H,qBAAA;oBACAX,IAAA,CAAAgB,oBAAA,GAAAhB,IAAA,CAAAsI,qBAAA,CAAAtH,oBAAA;oBACA;oBACAhB,IAAA,CAAAS,uBAAA,GAAAT,IAAA,CAAAsI,qBAAA,CAAA7H,uBAAA;oBAEAmJ,MAAA,CAAAvM,eAAA,CAAAkK,IAAA,CAAAvH,IAAA;oBACA4J,MAAA,CAAA9L,mBAAA,CAAAyJ,IAAA,CAAAvH,IAAA;oBACA4J,MAAA,CAAAhF,KAAA,CAAAoG,aAAA,CAAAlG,kBAAA,CAAA9E,IAAA;kBACA;gBACA;;gBAEA;gBACA4J,MAAA,CAAA7R,YAAA,CAAAgN,IAAA,WAAAC,CAAA,EAAAC,CAAA;kBACA;kBACA,IAAAD,CAAA,CAAAiG,kBAAA,YAAAhG,CAAA,CAAAgG,kBAAA;oBACA;kBACA;kBACA,IAAAjG,CAAA,CAAAiG,kBAAA,YAAAhG,CAAA,CAAAgG,kBAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAjG,CAAA,CAAAsD,qBAAA,CAAAyC,eAAA,aAAA9F,CAAA,CAAAqD,qBAAA,CAAAyC,eAAA;oBACA;kBACA;kBACA;kBACA,IAAA/F,CAAA,CAAAsD,qBAAA,CAAAyC,eAAA,aAAA9F,CAAA,CAAAqD,qBAAA,CAAAyC,eAAA;oBACA;kBACA;kBACA;kBACA,IAAA/F,CAAA,CAAAsD,qBAAA,CAAAyC,eAAA,IAAA9F,CAAA,CAAAqD,qBAAA,CAAAyC,eAAA;oBACA;oBACA,OAAA/F,CAAA,CAAAsD,qBAAA,CAAAtH,oBAAA,GAAAiE,CAAA,CAAAqD,qBAAA,CAAAtH,oBAAA;kBACA;oBACA;oBACA;kBACA;gBACA;cACA;cACA;YAAA;cAEA4I,MAAA,CAAAhM,aAAA;cACAgM,MAAA,CAAAxM,UAAA;YAAA;YAAA;cAAA,OAAA+M,SAAA,CAAAvH,IAAA;UAAA;QAAA,GAAAiH,QAAA;MAAA;IACA;IACAa,qBAAA,WAAAA,sBAAA7L,KAAA;MAAA,IAAAiC,SAAA,GAAA+C,SAAA,CAAAtD,MAAA,QAAAsD,SAAA,QAAAqH,SAAA,GAAArH,SAAA;MACA,WAAAjF,iBAAA,EAAAC,KAAA;QAAAiC,SAAA,EAAAA;MAAA;IACA;IACAqK,MAAA,WAAAA,OAAA;MAAA,IAAAC,OAAA;MACA;AACA;AACA;AACA;MACA,SAAA3P,IAAA,CAAAX,kBAAA;QACA,KAAAW,IAAA,CAAAX,kBAAA;QACA,KAAAW,IAAA,CAAA4P,QAAA;QACA,KAAA5P,IAAA,CAAA6P,UAAA;MACA;QACA,KAAA7P,IAAA,CAAAX,kBAAA;QACA,KAAAW,IAAA,CAAA4P,QAAA,QAAA3T,MAAA,CAAAC,KAAA,CAAA4T,IAAA,CAAAC,GAAA;QACA,KAAA/P,IAAA,CAAA6P,UAAA,OAAA7D,eAAA,IAAAC,MAAA;MACA;MAEA,KAAAZ,iBAAA;MACA,IAAAN,4BAAA,OAAA/K,IAAA,EAAA+I,IAAA,WAAAC,QAAA;QACA2G,OAAA,CAAAjI,QAAA,CAAA6D,OAAA;MACA;IACA;IAEA,sBACAtF,OAAA,WAAAA,QAAA;MAAA,IAAA+J,OAAA;MACA,KAAAnT,OAAA;MACA;MACA,IAAAoT,0BAAA,OAAA3S,WAAA,EAAAyL,IAAA,WAAAC,QAAA;QACAgH,OAAA,CAAA7S,cAAA,GAAA6L,QAAA,CAAAC,IAAA;QACA+G,OAAA,CAAA9S,KAAA,GAAA8L,QAAA,CAAA9L,KAAA;QACA8S,OAAA,CAAAnT,OAAA;MACA;IACA;IAEA;IACAqT,MAAA,WAAAA,OAAA;MACA,KAAA7S,IAAA;MACA,KAAA8S,KAAA;IACA;IAEA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAnQ,IAAA;QACA8I,YAAA;QACApL,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAmR,SAAA;QACAlR,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACAwQ,UAAA;MACA;MACA,KAAAjO,OAAA;MACA,KAAAJ,qBAAA;MACA,KAAAsO,SAAA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAAjT,WAAA;MACA,KAAAA,WAAA,OAAAyQ,cAAA,CAAAvH,OAAA,MAAAuH,cAAA,CAAAvH,OAAA,MAAAuH,cAAA,CAAAvH,OAAA,MACA,KAAAlJ,WAAA,GACAA,WAAA;QACAE,OAAA;MAAA,EACA;;MACA,KAAAyI,OAAA;IACA;IAEA,aACAuK,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IAEAE,kBAAA,WAAAA,mBAAA/G,GAAA;MAAA,IAAAgH,OAAA;MACA,IAAAC,IAAA,GAAAjH,GAAA,CAAAkH,MAAA;MACA,KAAAC,QAAA,WAAAF,IAAA,SAAA5H,IAAA;QACA,WAAA+H,wBAAA,EAAApH,GAAA,CAAAZ,YAAA,EAAAY,GAAA,CAAAkH,MAAA;MACA,GAAA7H,IAAA;QACA2H,OAAA,CAAAxE,MAAA,CAAAC,UAAA,CAAAwE,IAAA;MACA,GAAAI,KAAA;QACArH,GAAA,CAAAkH,MAAA,GAAAlH,GAAA,CAAAkH,MAAA;MACA;IACA;IAEA;IACAI,4BAAA,WAAAA,6BAAAC,SAAA,GAEA;IAEA;IACAC,gBAAA,WAAAA,iBAAAxH,GAAA;MAAA,IAAAyH,OAAA;MAAA,WAAA5K,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA0K,SAAA;QAAA,IAAAC,MAAA,EAAA7C,MAAA;QAAA,WAAA/H,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA0K,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxK,IAAA,GAAAwK,SAAA,CAAAvK,IAAA;YAAA;cACA;cACAqK,MAAA,OAAAtD,cAAA,CAAAvH,OAAA,MAAAkD,GAAA,GAEA;cAAA,MACAyH,OAAA,CAAAnR,IAAA,CAAA/B,gBAAA,KAAAoT,MAAA,CAAA/L,cAAA;gBAAAiM,SAAA,CAAAvK,IAAA;gBAAA;cAAA;cACAqK,MAAA,CAAAzH,gBAAA;cACAyH,MAAA,CAAAxQ,YAAA;cAAA0Q,SAAA,CAAAvK,IAAA;cAAA;YAAA;cAAAuK,SAAA,CAAAvK,IAAA;cAAA,OAGAmK,OAAA,CAAApC,eAAA,CAAAoC,OAAA,CAAAnR,IAAA,CAAA/B,gBAAA,EAAAoT,MAAA,CAAA/L,cAAA;YAAA;cAAAkJ,MAAA,GAAA+C,SAAA,CAAArK,IAAA;cAAA,IACAsH,MAAA;gBAAA+C,SAAA,CAAAvK,IAAA;gBAAA;cAAA;cACA;cACAmK,OAAA,CAAAzJ,QAAA,CAAAyC,KAAA;cAAA,OAAAoH,SAAA,CAAAvC,MAAA;YAAA;cAGA;cACAqC,MAAA,CAAAxQ,YAAA,GAAA2N,MAAA,YAAArJ,MAAA,CAAAqJ,MAAA,KAAApJ,OAAA,MAAAD,MAAA,CAAAgM,OAAA,CAAAlC,qBAAA,IAAAhK,MAAA,CAAAuJ,MAAA,KAAApL,KAAA,EAAAgC,OAAA;cACAiM,MAAA,CAAAzH,gBAAA,GAAA4E,MAAA,YAAAA,MAAA,IAAA9J,QAAA,YAAA8J,MAAA;YAAA;cAGA;cACA6C,MAAA,CAAAnM,qBAAA,OAAA/B,iBAAA,EAAAkO,MAAA,CAAA9L,oBAAA,EAAAnC,KAAA;cACAiO,MAAA,CAAArM,uBAAA,OAAA7B,iBAAA,EAAAkO,MAAA,CAAA9L,oBAAA,EAAAE,QAAA,CAAA4L,MAAA,CAAAxQ,YAAA,EAAAuC,KAAA;;cAEA;cACA,KAAAiO,MAAA,CAAAxE,qBAAA;gBACAwE,MAAA,CAAAxE,qBAAA;kBACAyC,eAAA;kBACA1P,eAAA;kBACAC,YAAA;gBACA;cACA;;cAEA;cACA;cACA;;cAEAsR,OAAA,CAAA7U,YAAA,CAAAwP,IAAA,CAAAuF,MAAA;cACAF,OAAA,CAAAxO,WAAA,GAAAwO,OAAA,CAAAxO,WAAA,CAAA0J,MAAA,WAAA9H,IAAA;gBAAA,OAAAA,IAAA,CAAAiI,QAAA,KAAA6E,MAAA,CAAA7E,QAAA;cAAA;YAAA;YAAA;cAAA,OAAA+E,SAAA,CAAApK,IAAA;UAAA;QAAA,GAAAiK,QAAA;MAAA;IACA;IACAI,wBAAA,WAAAA,yBAAA9H,GAAA;MACA,OAAAA,GAAA,CAAA+H,gBAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAT,SAAA;MAAA,IAAAU,OAAA;MACA;MACA,KAAA5U,MAAA,GAAAkU,SAAA,CAAAnM,MAAA;MACA,KAAA9H,QAAA,IAAAiU,SAAA,CAAAnM,MAAA;;MAEA;MACA,KAAA/C,cAAA;MACA;MACA,KAAAO,oBAAA;MACA2O,SAAA,CAAA3M,GAAA,WAAAC,IAAA;QACA;QACA,IAAAoN,OAAA,CAAA/P,eAAA,CAAAgQ,OAAA,CAAArN,IAAA;UACA;UACA,IAAAA,IAAA,CAAAgB,oBAAA,aAAAhB,IAAA,CAAAgB,oBAAA;YACA;YACAhB,IAAA,CAAAW,qBAAA,OAAA/B,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;YACAmB,IAAA,CAAAS,uBAAA,OAAA7B,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAE,QAAA,CAAAlB,IAAA,CAAA1D,YAAA,EAAAuC,KAAA;UACA;YACAmB,IAAA,CAAAW,qBAAA,OAAA/B,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,EAAAtC,KAAA;YACAmB,IAAA,CAAAS,uBAAA,OAAA7B,iBAAA,MAAAA,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,GAAAD,QAAA,CAAAlB,IAAA,CAAA1D,YAAA,EAAAuC,KAAA;UACA;QACA;MACA;MACA;MACA,KAAAxB,eAAA,CAAA0C,GAAA,WAAAC,IAAA;QACA,IAAA0M,SAAA,CAAAW,OAAA,CAAArN,IAAA;UACAA,IAAA,CAAAW,qBAAA;UACAX,IAAA,CAAAS,uBAAA;UACAT,IAAA,CAAAsN,wBAAA,OAAA1O,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;;UAEA;UACA,IAAAuO,OAAA,CAAAtP,mBAAA,CAAAuP,OAAA,CAAArN,IAAA;YACAoN,OAAA,CAAArP,oBAAA,CAAAwJ,IAAA,CAAAvH,IAAA;UACA;QACA;MACA;MAEA,KAAA3C,eAAA,GAAAqP,SAAA;IACA;IACAa,2BAAA,WAAAA,4BAAAb,SAAA;MAAA,IAAAc,OAAA;MACA;MACA,KAAAhV,MAAA,GAAAkU,SAAA,CAAAnM,MAAA;MACA,KAAA9H,QAAA,IAAAiU,SAAA,CAAAnM,MAAA;;MAEA;MACA,KAAA/C,cAAA;MACA;MACA,KAAAO,oBAAA;MACA2O,SAAA,CAAA3M,GAAA,WAAAC,IAAA;QACA;QACA,IAAAwN,OAAA,CAAAnQ,eAAA,CAAAgQ,OAAA,CAAArN,IAAA;UACA;UACA,IAAAA,IAAA,CAAAgB,oBAAA,aAAAhB,IAAA,CAAAgB,oBAAA;YACA;YACAhB,IAAA,CAAAW,qBAAA,OAAA/B,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;YACAmB,IAAA,CAAAS,uBAAA,OAAA7B,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAE,QAAA,CAAAlB,IAAA,CAAA1D,YAAA,EAAAuC,KAAA;UACA;YACAmB,IAAA,CAAAW,qBAAA,OAAA/B,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,EAAAtC,KAAA;YACAmB,IAAA,CAAAS,uBAAA,OAAA7B,iBAAA,MAAAA,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,GAAAD,QAAA,CAAAlB,IAAA,CAAA1D,YAAA,EAAAuC,KAAA;UACA;QACA;MACA;MACA;MACA,KAAAxB,eAAA,CAAA0C,GAAA,WAAAC,IAAA;QACA,IAAA0M,SAAA,CAAAW,OAAA,CAAArN,IAAA;UACAA,IAAA,CAAAW,qBAAA;UACAX,IAAA,CAAAS,uBAAA;UACAT,IAAA,CAAAsN,wBAAA,OAAA1O,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;;UAEA;UACA,IAAA2O,OAAA,CAAA1P,mBAAA,CAAAuP,OAAA,CAAArN,IAAA;YACAwN,OAAA,CAAAzP,oBAAA,CAAAwJ,IAAA,CAAAvH,IAAA;UACA;QACA;MACA;MAEA,KAAA3C,eAAA,GAAAqP,SAAA;IACA;IACAe,8BAAA,WAAAA,+BAAAf,SAAA;MAAA,IAAAgB,OAAA;MACA;MACA,KAAAlV,MAAA,GAAAkU,SAAA,CAAAnM,MAAA;MACA,KAAA9H,QAAA,IAAAiU,SAAA,CAAAnM,MAAA;;MAEA;MACA,KAAA/C,cAAA;MACA;MACA,KAAAO,oBAAA;MACA2O,SAAA,CAAA3M,GAAA,WAAAC,IAAA;QACA;QACA,IAAA0N,OAAA,CAAApQ,kBAAA,CAAA+P,OAAA,CAAArN,IAAA;UACA;UACA,IAAAA,IAAA,CAAAgB,oBAAA,aAAAhB,IAAA,CAAAgB,oBAAA;YACA;YACAhB,IAAA,CAAAW,qBAAA,OAAA/B,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;YACA;YACAmB,IAAA,CAAAS,uBAAA,OAAA7B,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAE,QAAA,CAAAlB,IAAA,CAAA1D,YAAA,EAAAuC,KAAA;UACA;YACAmB,IAAA,CAAAW,qBAAA,OAAA/B,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,EAAAtC,KAAA;YACAmB,IAAA,CAAAS,uBAAA,OAAA7B,iBAAA,MAAAA,iBAAA,EAAAoB,IAAA,CAAAiB,UAAA,EAAAC,QAAA,CAAAlB,IAAA,CAAAmB,QAAA,GAAAD,QAAA,CAAAlB,IAAA,CAAA1D,YAAA,EAAAuC,KAAA;UACA;QACA;MACA;MACA;MACA,KAAAvB,kBAAA,CAAAyC,GAAA,WAAAC,IAAA;QACA,IAAA0M,SAAA,CAAAW,OAAA,CAAArN,IAAA;UACAA,IAAA,CAAAW,qBAAA;UACAX,IAAA,CAAAS,uBAAA;UACAT,IAAA,CAAAsN,wBAAA,OAAA1O,iBAAA,EAAAoB,IAAA,CAAAgB,oBAAA,EAAAnC,KAAA;;UAEA;UACA,IAAA6O,OAAA,CAAA5P,mBAAA,CAAAuP,OAAA,CAAArN,IAAA;YACA0N,OAAA,CAAA3P,oBAAA,CAAAwJ,IAAA,CAAAvH,IAAA;UACA;QACA;MACA;MAEA,KAAA1C,kBAAA,GAAAoP,SAAA;IACA;IACAiB,OAAA,WAAAA,QAAAxI,GAAA;MAAA,IAAAyI,OAAA;MACA,KAAA7U,WAAA,CAAAM,eAAA,GAAA8L,GAAA,CAAA9L,eAAA;MACA,IAAAqS,0BAAA,OAAA3S,WAAA,EAAAyL,IAAA,WAAAC,QAAA;QACAmJ,OAAA,CAAAhV,cAAA,GAAA6L,QAAA,CAAAC,IAAA;QACAkJ,OAAA,CAAAjV,KAAA,GAAA8L,QAAA,CAAA9L,KAAA;QACAiV,OAAA,CAAAtV,OAAA;QACAsV,OAAA,CAAA5R,cAAA;MACA;IACA;IAEA6R,oBAAA,WAAAA,qBAAA;MACA;MACA,IAAAC,UAAA;MACA,aAAAtR,cAAA;QACA;UACAsR,UAAA,CAAAC,GAAA,QAAAhV,WAAA,CAAAC,MAAA,CAAA+U,GAAA;UACA,KAAAC,2BAAA;YAAAhV,MAAA,EAAA8U;UAAA;UACA;QACA;UACAA,UAAA,CAAAG,KAAA,QAAAlV,WAAA,CAAAC,MAAA,CAAAiV,KAAA;UACA,KAAAD,2BAAA;YAAAhV,MAAA,EAAA8U;UAAA;UACA;QACA;UACAA,UAAA,CAAAI,IAAA,QAAAnV,WAAA,CAAAC,MAAA,CAAAkV,IAAA;UACA,KAAAF,2BAAA;YAAAhV,MAAA,EAAA8U;UAAA;UACA;QACA;UACAA,UAAA,CAAAK,SAAA,QAAApV,WAAA,CAAAwC,OAAA,WAAAxC,WAAA,CAAAwC,OAAA;UACAuS,UAAA,CAAAM,OAAA,QAAArV,WAAA,CAAAwC,OAAA,WAAAxC,WAAA,CAAAwC,OAAA;UACA,KAAAyS,2BAAA;YAAAhV,MAAA,EAAA8U;UAAA;UACA;MAEA;IACA;IACAO,oBAAA,WAAAA,qBAAA;MACA;MACA;MACA;;MAEA,KAAApS,cAAA;IACA;IACAqS,qBAAA,WAAAA,sBAAA;MACA,KAAAtS,cAAA;MACA,KAAAgS,2BAAA;IACA;IAEAA,2BAAA,WAAAA,4BAAAzW,IAAA;MAAA,IAAAgX,OAAA;MACA,KAAAhX,IAAA;QACAA,IAAA;MACA;MACA,IAAAiX,oCAAA,EAAAjX,IAAA,EAAAiN,IAAA,WAAAC,QAAA;QACA8J,OAAA,CAAA/S,cAAA,GAAAiJ,QAAA,CAAAlN,IAAA;QACAgX,OAAA,CAAArS,gBAAA;QACAqS,OAAA,CAAApS,gBAAA;QACAoS,OAAA,CAAAnS,YAAA;QACAmS,OAAA,CAAAlS,YAAA;QAEA,IAAAC,YAAA;QAAA,IAAAmS,UAAA,OAAApE,2BAAA,CAAApI,OAAA,EACAsM,OAAA,CAAA7W,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8G,gBAAA;UAAAqQ,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAnE,CAAA,MAAAoE,MAAA,GAAAD,UAAA,CAAAjQ,CAAA,IAAA+L,IAAA;YAAA,IAAAvF,CAAA,GAAA0J,MAAA,CAAA7P,KAAA;YACA,KAAAvC,YAAA;cACA,IAAA0I,CAAA,CAAA2J,aAAA,cACA,SAAA3J,CAAA,CAAA4J,eAAA,IACA,IAAAjI,eAAA,EAAA3B,CAAA,CAAA6J,SAAA,SAAAlI,eAAA,MAAAmI,IAAA,OACA,IAAAnI,eAAA,MAAAmI,IAAA,WAAAnI,eAAA,EAAA3B,CAAA,CAAA+J,OAAA;gBACAzS,YAAA,OAAAsC,iBAAA,EAAAoG,CAAA,CAAAgK,UAAA,EAAAtO,MAAA,CAAAsE,CAAA,CAAAiK,IAAA,EAAApQ,KAAA;cACA;YACA;UACA;QAAA,SAAAqQ,GAAA;UAAAT,UAAA,CAAA9D,CAAA,CAAAuE,GAAA;QAAA;UAAAT,UAAA,CAAA7D,CAAA;QAAA;QACA2D,OAAA,CAAAjS,YAAA,GAAAA,YAAA;QAEA,IAAAmI,QAAA,CAAAlN,IAAA;UACAkN,QAAA,CAAAlN,IAAA,CAAAsI,OAAA,WAAAG,IAAA;YACA,IAAAA,IAAA,CAAAtG,gBAAA;cACA6U,OAAA,CAAApS,gBAAA,IAAA6D,IAAA,CAAAmP,aAAA;cACAZ,OAAA,CAAAlS,YAAA,IAAA2D,IAAA,CAAAoP,SAAA;YACA;YACA,IAAApP,IAAA,CAAAtG,gBAAA;cACA6U,OAAA,CAAArS,gBAAA,IAAA8D,IAAA,CAAAmP,aAAA;cACAZ,OAAA,CAAAnS,YAAA,IAAA4D,IAAA,CAAAoP,SAAA;YACA;UACA;;UAEA;UACAb,OAAA,CAAAhS,oBAAA,GAAAgS,OAAA,CAAAc,6BAAA;QACA;MACA;IACA;IAEA;IACAA,6BAAA,WAAAA,8BAAA;MACA;MACA,UAAA7T,cAAA,SAAAA,cAAA,CAAA+E,MAAA;QACA;MACA;;MAEA;MACA,IAAA+O,UAAA,OAAAC,GAAA;;MAEA;MACA,KAAA/T,cAAA,CAAAqE,OAAA,WAAAG,IAAA;QACA;QACA,IAAAwP,SAAA,GAAAxP,IAAA,CAAA5G,mBAAA;QACA,IAAAwF,QAAA,GAAAoB,IAAA,CAAAtG,gBAAA;QACA,IAAA+V,QAAA,GAAA7O,MAAA,CAAAZ,IAAA,CAAAmP,aAAA;QACA,IAAAO,IAAA,GAAA9O,MAAA,CAAAZ,IAAA,CAAAoP,SAAA;;QAEA;QACA,KAAAE,UAAA,CAAAK,GAAA,CAAAH,SAAA;UACAF,UAAA,CAAAM,GAAA,CAAAJ,SAAA;YACAA,SAAA,EAAAA,SAAA;YACAK,SAAA,EAAA7P,IAAA,CAAA8P,mBAAA;YACAC,GAAA;cAAAN,QAAA;cAAAC,IAAA;cAAAM,OAAA;YAAA;YACAC,GAAA;cAAAR,QAAA;cAAAC,IAAA;cAAAM,OAAA;YAAA;UACA;QACA;;QAEA;QACA,IAAAE,MAAA,GAAAZ,UAAA,CAAAa,GAAA,CAAAX,SAAA;QACA,IAAA5Q,QAAA;UACAsR,MAAA,CAAAH,GAAA,CAAAN,QAAA,IAAAA,QAAA;UACAS,MAAA,CAAAH,GAAA,CAAAL,IAAA,IAAAA,IAAA;UACAQ,MAAA,CAAAH,GAAA,CAAAC,OAAA,GAAAE,MAAA,CAAAH,GAAA,CAAAN,QAAA,GAAAS,MAAA,CAAAH,GAAA,CAAAL,IAAA;QACA,WAAA9Q,QAAA;UACAsR,MAAA,CAAAD,GAAA,CAAAR,QAAA,IAAAA,QAAA;UACAS,MAAA,CAAAD,GAAA,CAAAP,IAAA,IAAAA,IAAA;UACAQ,MAAA,CAAAD,GAAA,CAAAD,OAAA,GAAAE,MAAA,CAAAD,GAAA,CAAAR,QAAA,GAAAS,MAAA,CAAAD,GAAA,CAAAP,IAAA;QACA;MACA;;MAEA;MACA,OAAAU,KAAA,CAAAC,IAAA,CAAAf,UAAA,CAAAgB,MAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MACA,KAAA7T,MAAA,CAAA7D,KAAA;MACA,KAAA6D,MAAA,CAAA5D,IAAA;IACA;IAEA;IACA0X,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAjU,MAAA,CAAAC,WAAA;IACA;IAEA;IACAiU,iBAAA,WAAAA,kBAAAnM,QAAA,EAAAiM,IAAA,EAAAC,QAAA;MACA,KAAAjU,MAAA,CAAA5D,IAAA;MACA,KAAA4D,MAAA,CAAAC,WAAA;MACA,KAAAiI,KAAA,CAAAlI,MAAA,CAAAmU,UAAA;MACA,KAAA1N,QAAA,CAAAC,IAAA,CAAAqB,QAAA,CAAAqM,GAAA;MACA,IAAArM,QAAA,CAAAqM,GAAA;QACA,KAAAC,QAAA;MACA;MACA,KAAArP,OAAA;IACA;IAEA;IACAsP,cAAA,WAAAA,eAAA;MACA,KAAApM,KAAA,CAAAlI,MAAA,CAAAuU,MAAA;IACA;IAEA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAtF,KAAA;MACA,KAAAxO,UAAA;MACA,KAAAtE,IAAA;MACA,KAAAD,KAAA;MACA,KAAA4D,GAAA;MACA,KAAAhB,IAAA,CAAAtC,mBAAA;MACA,KAAAsC,IAAA,CAAAhB,eAAA;IACA;IAEA,aACA0W,YAAA,WAAAA,aAAAhM,GAAA;MAAA,IAAAiM,OAAA;MACA,KAAA3U,GAAA;MACA,KAAAmP,KAAA;MACA,IAAArH,YAAA,GAAAY,GAAA,CAAAZ,YAAA,SAAAhM,GAAA;MACA,IAAA8Y,yBAAA,EAAA9M,YAAA,EAAAC,IAAA,WAAAC,QAAA;QACA2M,OAAA,CAAA3V,IAAA,GAAAgJ,QAAA,CAAAlN,IAAA;QACA6Z,OAAA,CAAA3V,IAAA,CAAAqQ,UAAA;QACAsF,OAAA,CAAAtY,IAAA;QACAsY,OAAA,CAAAvY,KAAA;QACAuY,OAAA,CAAAnT,WAAA,IAAAwG,QAAA,CAAAxG,WAAA;QAEAmT,OAAA,CAAA1S,kBAAA,CAAA+F,QAAA,CAAAlN,IAAA,CAAAiC,YAAA;QAEA,IAAA4X,OAAA,CAAA3V,IAAA,CAAAX,kBAAA,aAAA2J,QAAA,CAAAlN,IAAA,CAAA8C,8BAAA,KAAAoK,QAAA,CAAAlN,IAAA,CAAAoC,wBAAA,IAAA8K,QAAA,CAAAlN,IAAA,CAAA+C,0BAAA,KAAAmK,QAAA,CAAAlN,IAAA,CAAAqC,oBAAA;UACAwX,OAAA,CAAAnN,eAAA,CAAAmN,OAAA,CAAAzV,YAAA;QACA;MACA;IACA;IAEA,WACA2V,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAA3M,KAAA,SAAA4M,QAAA;QAAA,IAAAC,IAAA,OAAAzP,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuP,SAAAC,KAAA;UAAA,WAAAzP,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAuP,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAArP,IAAA,GAAAqP,SAAA,CAAApP,IAAA;cAAA;gBAAA,KAMAkP,KAAA;kBAAAE,SAAA,CAAApP,IAAA;kBAAA;gBAAA;gBAAA,KAEA8O,OAAA,CAAApT,SAAA;kBAAA0T,SAAA,CAAApP,IAAA;kBAAA;gBAAA;gBACAnB,OAAA,CAAAC,GAAA;gBACA;gBAAAsQ,SAAA,CAAApP,IAAA;gBAAA,OACA8O,OAAA,CAAAO,WAAA;cAAA;gBAEA;gBACAP,OAAA,CAAAzK,iBAAA;gBACA,IAAAyK,OAAA,CAAA9V,IAAA,CAAA8I,YAAA;kBACA,IAAAiC,4BAAA,EAAA+K,OAAA,CAAA9V,IAAA,EAAA+I,IAAA,WAAAC,QAAA;oBACA8M,OAAA,CAAA5J,MAAA,CAAAC,UAAA;oBACA;oBACA2J,OAAA,CAAA7P,OAAA;kBACA;gBACA;kBACA,IAAAqQ,yBAAA,EAAAR,OAAA,CAAA9V,IAAA,EAAA+I,IAAA,WAAAC,QAAA;oBACA8M,OAAA,CAAA9V,IAAA,GAAAgJ,QAAA,CAAAlN,IAAA;oBACAga,OAAA,CAAA5J,MAAA,CAAAC,UAAA;oBACA;oBACA2J,OAAA,CAAA7P,OAAA;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAAmQ,SAAA,CAAAjP,IAAA;YAAA;UAAA,GAAA8O,QAAA;QAAA,CAEA;QAAA,iBAAAM,GAAA;UAAA,OAAAP,IAAA,CAAA7N,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IAEAiO,WAAA,WAAAA,YAAA;MAAA,IAAAG,OAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,OAAA,CAAAI,iBAAA;UACA3B,IAAA,EAAAuB,OAAA,CAAA9T,SAAA;UACAmU,SAAA,WAAAA,UAAA7N,QAAA;YACAwN,OAAA,CAAAM,aAAA,CAAA9N,QAAA;YACA0N,OAAA,CAAA1N,QAAA;UACA;UACA+N,OAAA,WAAAA,QAAA5M,KAAA;YACAqM,OAAA,CAAAQ,WAAA,CAAA7M,KAAA;YACAwM,MAAA,CAAAxM,KAAA;UACA;QACA;MACA;IACA;IAEAkB,iBAAA,WAAAA,kBAAA;MACA,SAAArL,IAAA,CAAAtC,mBAAA;QACA;QACA,KAAAsC,IAAA,CAAA7B,oBAAA;QACA,KAAA6B,IAAA,CAAA3B,mBAAA;QACA,KAAA2B,IAAA,CAAAvB,iBAAA;QACA,KAAAuB,IAAA,CAAArB,sBAAA;QACA,KAAAqB,IAAA,CAAAzB,oBAAA;QACA,KAAAyB,IAAA,CAAAnB,0BAAA;QACA;QACA,KAAAmB,IAAA,CAAApB,8BAAA,UAAAoB,IAAA,CAAAxB,qBAAA,GACA,KAAAwB,IAAA,CAAAlB,cAAA,aAAAkB,IAAA,CAAApB,8BAAA,SACA,KAAAoB,IAAA,CAAAlB,cAAA,YAAAkB,IAAA,CAAAlB,cAAA;MACA;QACA;QACA,KAAAkB,IAAA,CAAA9B,wBAAA;QACA,KAAA8B,IAAA,CAAA5B,uBAAA;QACA,KAAA4B,IAAA,CAAAxB,qBAAA;QACA,KAAAwB,IAAA,CAAAtB,0BAAA;QACA,KAAAsB,IAAA,CAAA1B,wBAAA;QACA,KAAA0B,IAAA,CAAApB,8BAAA;QACA;QACA,KAAAoB,IAAA,CAAAvB,iBAAA,UAAAuB,IAAA,CAAAnB,0BAAA,GACA,KAAAmB,IAAA,CAAAlB,cAAA,aAAAkB,IAAA,CAAAnB,0BAAA,SACA,KAAAmB,IAAA,CAAAlB,cAAA,YAAAkB,IAAA,CAAAlB,cAAA;MACA;IACA;IAEA,aACAmY,YAAA,WAAAA,aAAAvN,GAAA;MAAA,IAAAwN,OAAA;MACA,IAAAC,aAAA,GAAAzN,GAAA,CAAAZ,YAAA,SAAAhM,GAAA;MACA,KAAA+T,QAAA,4BAAAsG,aAAA,cAAApO,IAAA;QACA,WAAAqO,yBAAA,EAAAD,aAAA;MACA,GAAApO,IAAA;QACAmO,OAAA,CAAAjR,OAAA;QACAiR,OAAA,CAAAhL,MAAA,CAAAC,UAAA;MACA,GAAA4E,KAAA,cACA;IACA;IAEA,aACAsG,YAAA,WAAAA,aAAA;MACA,KAAA/B,QAAA,iCAAAvH,cAAA,CAAAvH,OAAA,MACA,KAAAlJ,WAAA,iBAAA8M,MAAA,CACA,IAAAiJ,IAAA,GAAAiE,OAAA;IACA;IAEAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAA3P,QAAA,KAAA2P,IAAA,CAAA3P,QAAA,CAAA/C,MAAA;QACA,OAAA0S,IAAA,CAAA3P,QAAA;MACA;MACA,IAAA4P,CAAA;MACA,IAAAD,IAAA,CAAAE,KAAA;QACA,IAAAF,IAAA,CAAAE,KAAA,CAAAC,oBAAA,YAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;UACA,IAAAJ,IAAA,CAAAK,IAAA,CAAAC,aAAA;YACAL,CAAA,GAAAD,IAAA,CAAAK,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAK,IAAA,CAAAC,aAAA;UACA;YACAL,CAAA,GAAAD,IAAA,CAAAS,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAS,IAAA,CAAAC,aAAA;UACA;QACA;UACAT,CAAA,GAAAD,IAAA,CAAAE,KAAA,CAAAS,SAAA,SAAAX,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA,SAAAJ,IAAA,CAAAE,KAAA,CAAAU,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAJ,IAAA,CAAAa,MAAA;QACA;UACAC,EAAA,EAAAd,IAAA,CAAAa,MAAA;UACAE,KAAA,EAAAd,CAAA;UACA5P,QAAA,EAAA2P,IAAA,CAAA3P,QAAA;UACA2Q,UAAA,EAAAhB,IAAA,CAAApV,OAAA,YAAAoV,IAAA,CAAA3P,QAAA,IAAA4H;QACA;MACA;QACA;UACA6I,EAAA,EAAAd,IAAA,CAAAiB,MAAA;UACAF,KAAA,EAAAd,CAAA;UACA5P,QAAA,EAAA2P,IAAA,CAAA3P,QAAA;UACA2Q,UAAA,EAAAhB,IAAA,CAAApV,OAAA,YAAAoV,IAAA,CAAA3P,QAAA,IAAA4H;QACA;MACA;IACA;IAEAiJ,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MACA,SAAA1c,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8c,SAAA,CAAA9T,MAAA,cAAA7I,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAsK,SAAA,CAAAwS,SAAA;QACA1S,cAAA,CAAAC,QAAA,iBAAA4C,IAAA;UACA4P,OAAA,CAAAlc,UAAA,GAAAkc,OAAA,CAAA1c,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8c,SAAA;QACA;MACA;QACA,KAAAnc,UAAA,QAAAR,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAA8c,SAAA;MACA;IACA;IAEAvR,SAAA,WAAAA,UAAA;MAAA,IAAAwR,OAAA;MACA,SAAA5c,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAgd,cAAA,CAAAhU,MAAA,cAAA7I,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAsK,SAAA,CAAA0S,cAAA;QACA5S,cAAA,CAAAC,QAAA,sBAAA4C,IAAA;UACA8P,OAAA,CAAAnc,SAAA,GAAAmc,OAAA,CAAA5c,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAgd,cAAA;QACA;MACA;QACA,KAAApc,SAAA,QAAAT,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAgd,cAAA;MACA;IACA;IAEAC,aAAA,WAAAA,cAAArP,GAAA;MACA,KAAAgM,YAAA,CAAAhM,GAAA;IACA;IAEAsP,aAAA,WAAAA,cAAAC,OAAA;MACA,KAAAjZ,IAAA,CAAAnC,iBAAA,GAAAob,OAAA,CAAAC,SAAA;MACA,KAAAlZ,IAAA,CAAAlC,2BAAA,GAAAmb,OAAA,CAAAE,gBAAA;MACA,KAAA5W,WAAA;IACA;IAEAwM,eAAA,WAAAA,gBAAAoE,eAAA,EAAAD,aAAA,EAAAkG,SAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9S,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4S,SAAA;QAAA,IAAAC,EAAA,EAAAvQ,QAAA,EAAAwQ,UAAA,EAAAC,MAAA,EAAAlQ,CAAA;QAAA,WAAA9C,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8S,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5S,IAAA,GAAA4S,SAAA,CAAA3S,IAAA;YAAA;cAAA,MAGAqS,OAAA,CAAAzW,gBAAA,IAAAyW,OAAA,CAAAzW,gBAAA,CAAAkC,MAAA;gBAAA6U,SAAA,CAAA3S,IAAA;gBAAA;cAAA;cACAgC,QAAA,GAAAqQ,OAAA,CAAAzW,gBAAA;cAAA+W,SAAA,CAAA3S,IAAA;cAAA;YAAA;cAAA2S,SAAA,CAAA3S,IAAA;cAAA,OAEA,IAAAC,oCAAA;YAAA;cAAAoS,OAAA,CAAAzW,gBAAA,GAAA+W,SAAA,CAAAzS,IAAA;cACA8B,QAAA,GAAAqQ,OAAA,CAAAzW,gBAAA;YAAA;cAAA,MAGAuQ,eAAA,aAAAD,aAAA;gBAAAyG,SAAA,CAAA3S,IAAA;gBAAA;cAAA;cAAAwS,UAAA,OAAA5K,2BAAA,CAAApI,OAAA,EACAwC,QAAA,CAAAlN,IAAA;cAAA;gBAAA,KAAA0d,UAAA,CAAA3K,CAAA,MAAA4K,MAAA,GAAAD,UAAA,CAAAzW,CAAA,IAAA+L,IAAA;kBAAAvF,CAAA,GAAAkQ,MAAA,CAAArW,KAAA;kBACA;kBACA,IAAA+P,eAAA,KAAA5J,CAAA,CAAA4J,eAAA,IAAAD,aAAA,KAAA3J,CAAA,CAAA2J,aAAA;oBACAqG,EAAA,WAAApW,iBAAA,EAAAoG,CAAA,CAAAqQ,OAAA,EAAA3U,MAAA,CAAAsE,CAAA,CAAAiK,IAAA,EAAApQ,KAAA;kBACA;oBACAmW,EAAA,WAAApW,iBAAA,EAAAoG,CAAA,CAAAsQ,QAAA,EAAA5U,MAAA,CAAAsE,CAAA,CAAAiK,IAAA,EAAApQ,KAAA;kBACA;gBACA;cAAA,SAAAqQ,GAAA;gBAAA+F,UAAA,CAAAtK,CAAA,CAAAuE,GAAA;cAAA;gBAAA+F,UAAA,CAAArK,CAAA;cAAA;cAAA,OAAAwK,SAAA,CAAA3K,MAAA,WACAuK,EAAA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAAxS,IAAA;UAAA;QAAA,GAAAmS,QAAA;MAAA;IAEA;IAEAQ,uBAAA,WAAAA,wBAAA3G,eAAA,EAAAD,aAAA,EAAAkG,SAAA;MAAA,WAAA7S,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqT,SAAA;QAAA,IAAAR,EAAA,EAAAvQ,QAAA,EAAAgR,UAAA,EAAAC,MAAA,EAAA1Q,CAAA;QAAA,WAAA9C,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsT,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApT,IAAA,GAAAoT,SAAA,CAAAnT,IAAA;YAAA;cACAuS,EAAA;cAAAY,SAAA,CAAAnT,IAAA;cAAA,OACA,IAAAC,oCAAA;YAAA;cAAA+B,QAAA,GAAAmR,SAAA,CAAAjT,IAAA;cAAA,MACAiM,eAAA,aAAAD,aAAA;gBAAAiH,SAAA,CAAAnT,IAAA;gBAAA;cAAA;cAAAgT,UAAA,OAAApL,2BAAA,CAAApI,OAAA,EACAwC,QAAA,CAAAlN,IAAA;cAAA;gBAAA,KAAAke,UAAA,CAAAnL,CAAA,MAAAoL,MAAA,GAAAD,UAAA,CAAAjX,CAAA,IAAA+L,IAAA;kBAAAvF,CAAA,GAAA0Q,MAAA,CAAA7W,KAAA;kBACA,KAAAmG,CAAA,CAAA2J,aAAA,IAAAA,aAAA,IAAA3J,CAAA,CAAApG,QAAA,IAAA+P,aAAA,MACA3J,CAAA,CAAApG,QAAA,IAAAgQ,eAAA,IAAA5J,CAAA,CAAA2J,aAAA,IAAAC,eAAA,KACA,IAAAjI,eAAA,EAAA3B,CAAA,CAAA6J,SAAA,SAAAlI,eAAA,EAAAkO,SAAA,KACA,IAAAlO,eAAA,EAAAkO,SAAA,SAAAlO,eAAA,EAAA3B,CAAA,CAAA+J,OAAA;oBACA;oBACA,IAAAH,eAAA,KAAA5J,CAAA,CAAA4J,eAAA,IAAAD,aAAA,KAAA3J,CAAA,CAAA2J,aAAA;sBACAqG,EAAA,WAAApW,iBAAA,EAAAoG,CAAA,CAAAqQ,OAAA,EAAA3U,MAAA,CAAAsE,CAAA,CAAAiK,IAAA,EAAApQ,KAAA;oBACA;sBACAmW,EAAA,WAAApW,iBAAA,EAAAoG,CAAA,CAAAsQ,QAAA,EAAA5U,MAAA,CAAAsE,CAAA,CAAAiK,IAAA,EAAApQ,KAAA;oBACA;kBACA;gBACA;cAAA,SAAAqQ,GAAA;gBAAAuG,UAAA,CAAA9K,CAAA,CAAAuE,GAAA;cAAA;gBAAAuG,UAAA,CAAA7K,CAAA;cAAA;cAAA,OAAAgL,SAAA,CAAAnL,MAAA,WACAuK,EAAA;YAAA;YAAA;cAAA,OAAAY,SAAA,CAAAhT,IAAA;UAAA;QAAA,GAAA4S,QAAA;MAAA;IAEA;IAEAnQ,gBAAA,WAAAA,iBAAA3L,gBAAA,EAAAmc,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAA9T,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA4T,SAAA;QAAA,IAAA9L,MAAA;QAAA,WAAA/H,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA2T,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzT,IAAA,GAAAyT,SAAA,CAAAxT,IAAA;YAAA;cAAA,MACA/I,gBAAA,KAAAmc,kBAAA;gBAAAI,SAAA,CAAAxT,IAAA;gBAAA;cAAA;cAAA,OAAAwT,SAAA,CAAAxL,MAAA,WACA;YAAA;cAAAwL,SAAA,CAAAxT,IAAA;cAAA,OAEAqT,OAAA,CAAAtL,eAAA,CAAA9Q,gBAAA,EAAAmc,kBAAA;YAAA;cAAA5L,MAAA,GAAAgM,SAAA,CAAAtT,IAAA;YAAA;YAAA;cAAA,OAAAsT,SAAA,CAAArT,IAAA;UAAA;QAAA,GAAAmT,QAAA;MAAA;IACA;IAEAG,OAAA,WAAAA,QAAAnC,EAAA;MACA,IAAAA,EAAA;QACA,IAAAA,EAAA;UACA,IAAAZ,KAAA,QAAAzb,MAAA,CAAAC,KAAA,CAAAJ,IAAA,CAAAgd,cAAA,CAAAzM,MAAA,WAAAqO,OAAA;YAAA,OAAAA,OAAA,CAAAtY,OAAA,IAAAkW,EAAA;UAAA;UACA,IAAAZ,KAAA;YACA,OAAAA,KAAA,CAAAC,oBAAA,GAAAD,KAAA,CAAAE,oBAAA,GAAAF,KAAA,CAAAiD,cAAA;UACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEAC,WAAA,WAAAA,YAAAlR,GAAA;MACA,KAAA1J,IAAA,CAAAN,YAAA,GAAAgK,GAAA,CAAAgO,KAAA,CAAAtV,OAAA;IACA;IAEAyY,eAAA,WAAAA,gBAAAnR,GAAA,EAAAoR,KAAA;MACA,OAAApR,GAAA,CAAA8F,kBAAA;IACA;IACAuL,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAA9R,SAAA;QACA,IAAA+R,eAAA,GAAAD,OAAA,CAAA7R,KAAA,CAAA+R,UAAA,CAAAC,eAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,KAAA;QACA;MACA;IACA;IAEAC,6BAAA,WAAAA,8BAAA7D,IAAA;MACA;QACAc,EAAA,EAAAd,IAAA,CAAApU,KAAA;QACAmV,KAAA,EAAAf,IAAA,CAAAe;MACA;IACA;IAEA+C,iBAAA,WAAAA,kBAAA5R,GAAA;MACA,KAAA1J,IAAA,CAAArC,mBAAA,GAAA+L,GAAA,CAAA6R,sBAAA;IACA;IAEA3E,iBAAA,WAAAA,kBAAA4E,OAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAAJ,OAAA,CAAAvG,IAAA;MAEA,IAAA4G,gBAAA;QACAta,GAAA;QACAua,MAAA;QACAhgB,IAAA,EAAA4f;MACA,GAAA3S,IAAA,WAAAC,QAAA;QACAwS,OAAA,CAAA3E,SAAA,CAAA7N,QAAA,EAAAwS,OAAA,CAAAvG,IAAA;QACAwG,OAAA,CAAAzb,IAAA,CAAA6K,QAAA,GAAA7B,QAAA,CAAAzH,GAAA;MACA,GAAAwP,KAAA,WAAA5G,KAAA;QACAqR,OAAA,CAAAzE,OAAA,CAAA5M,KAAA;MACA;IACA;IAEA4R,YAAA,WAAAA,aAAA9G,IAAA,EAAAC,QAAA;MACA,IAAA8G,SAAA,GAAA/G,IAAA,CAAA1Z,IAAA,CAAA0gB,SAAA,CAAAhH,IAAA,CAAA1Z,IAAA,CAAA2gB,WAAA;MACA,IAAAC,WAAA,MAAA/R,MAAA,MAAApK,IAAA,CAAAgL,YAAA,EAAAZ,MAAA,CAAA4R,SAAA;MACA,KAAAtZ,SAAA,OAAA0Z,IAAA,EAAAnH,IAAA,CAAAoH,GAAA,GAAAF,WAAA;QAAA1T,IAAA,EAAAwM,IAAA,CAAAxM;MAAA;IACA;IAEAqO,aAAA,WAAAA,cAAA9N,QAAA,EAAAiM,IAAA,EAAAC,QAAA;MACA,KAAAlV,IAAA,CAAA6K,QAAA,GAAA7B,QAAA,CAAAzH,GAAA;IACA;IAEAyV,WAAA,WAAAA,YAAAvD,GAAA,EAAAwB,IAAA,EAAAC,QAAA;MACA,KAAAxN,QAAA,CAAAyC,KAAA,mBAAAsJ,GAAA;IACA;IAEA;IACA6I,oBAAA,WAAAA,qBAAA5S,GAAA,EAAAtG,KAAA;MACA;MACAsG,GAAA,CAAA1E,uBAAA,GAAAG,MAAA,CAAA/B,KAAA,EAAAgC,OAAA;;MAEA;MACA,IAAAsE,GAAA,CAAA7I,YAAA;QACA;QACA,IAAA2D,WAAA,GAAAkF,GAAA,CAAAjF,QAAA,CAAAC,QAAA;QACA,IAAAC,aAAA,GAAAH,WAAA,CAAAI,QAAA,QACAJ,WAAA,CAAAK,KAAA,SAAAC,MAAA;;QAEA;QACA,IAAAC,eAAA,OAAA5B,iBAAA,EAAAC,KAAA,EAAA6B,MAAA,CAAAyE,GAAA,CAAA7I,YAAA,EAAAuC,KAAA;QACAsG,GAAA,CAAAxE,qBAAA,GAAAC,MAAA,CAAAJ,eAAA,EAAAK,OAAA,CAAAT,aAAA;MACA;IACA;EACA;EAEA4X,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAAxc,IAAA,CAAA9B,wBAAA,QAAA8B,IAAA,CAAA5B,uBAAA,QAAA4B,IAAA,CAAA1B,wBAAA;IACA;IAEAme,QAAA,WAAAA,SAAA;MACA,YAAAzc,IAAA,CAAA7B,oBAAA,QAAA6B,IAAA,CAAA3B,mBAAA,QAAA2B,IAAA,CAAAzB,oBAAA;IACA;IAEAme,QAAA,WAAAA,SAAA;MACA,YAAA1c,IAAA,CAAAX,kBAAA;IACA;IAEAsd,mBAAA,WAAAA,oBAAA;MACA,YAAA3c,IAAA,CAAAsL,aAAA;IACA;EACA;AACA;AAAAsR,OAAA,CAAApW,OAAA,GAAAqW,QAAA"}]}