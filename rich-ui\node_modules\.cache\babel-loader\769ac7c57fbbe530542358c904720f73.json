{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\result.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\result.vue", "mtime": 1754876882527}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "dayRule", "dayRuleSup", "dateArr", "resultList", "isShow", "name", "methods", "expressionChange", "ruleArr", "$options", "propsData", "ex", "split", "nums", "resultArr", "nTime", "Date", "nYear", "getFullYear", "nMonth", "getMonth", "nDay", "getDate", "nHour", "getHours", "nMin", "getMinutes", "nSecond", "getSeconds", "getSecondArr", "getMinArr", "getHourArr", "getDayArr", "getMonthArr", "getWeekArr", "getYearArr", "sDate", "mDate", "hDate", "DDate", "MDate", "YDate", "sIdx", "getIndex", "mIdx", "hIdx", "DIdx", "MIdx", "YIdx", "resetSecond", "resetMin", "resetHour", "resetDay", "reset<PERSON><PERSON><PERSON>", "goYear", "<PERSON>", "length", "YY", "goMonth", "<PERSON>", "MM", "goDay", "Di", "DD", "thisDD", "checkDate", "thisWeek", "formatDate", "indexOf", "goHour", "hi", "hh", "goMin", "mi", "mm", "si", "ss", "push", "arr", "value", "i", "rule", "year", "getOrderArr", "undefined", "getCycleArr", "getAverageArr", "getAssignArr", "matchRule", "match", "Number", "min", "max", "assiginArr", "sort", "compare", "limit", "agArr", "step", "status", "cycleArr", "add", "Math", "round", "value1", "value2", "type", "time", "Y", "M", "D", "h", "m", "s", "week", "getDay", "format", "watch", "props", "mounted", "exports", "default", "_default"], "sources": ["src/components/Crontab/result.vue"], "sourcesContent": ["<template>\r\n  <div class=\"popup-result\">\r\n    <p class=\"title\">最近5次运行时间</p>\r\n    <ul class=\"popup-result-scroll\">\r\n      <template v-if='isShow'>\r\n        <li v-for='item in resultList' :key=\"item\">{{ item }}</li>\r\n      </template>\r\n      <li v-else>计算结果中...</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dayRule: '',\r\n      dayRuleSup: '',\r\n      dateArr: [],\r\n      resultList: [],\r\n      isShow: false\r\n    }\r\n  },\r\n  name: 'crontab-result',\r\n  methods: {\r\n    // 表达式值变化时，开始去计算结果\r\n    expressionChange() {\r\n\r\n      // 计算开始-隐藏结果\r\n      this.isShow = false;\r\n      // 获取规则数组[0秒、1分、2时、3日、4月、5星期、6年]\r\n      let ruleArr = this.$options.propsData.ex.split(' ');\r\n      // 用于记录进入循环的次数\r\n      let nums = 0;\r\n      // 用于暂时存符号时间规则结果的数组\r\n      let resultArr = [];\r\n      // 获取当前时间精确至[年、月、日、时、分、秒]\r\n      let nTime = new Date();\r\n      let nYear = nTime.getFullYear();\r\n      let nMonth = nTime.getMonth() + 1;\r\n      let nDay = nTime.getDate();\r\n      let nHour = nTime.getHours();\r\n      let nMin = nTime.getMinutes();\r\n      let nSecond = nTime.getSeconds();\r\n      // 根据规则获取到近100年可能年数组、月数组等等\r\n      this.getSecondArr(ruleArr[0]);\r\n      this.getMinArr(ruleArr[1]);\r\n      this.getHourArr(ruleArr[2]);\r\n      this.getDayArr(ruleArr[3]);\r\n      this.getMonthArr(ruleArr[4]);\r\n      this.getWeekArr(ruleArr[5]);\r\n      this.getYearArr(ruleArr[6], nYear);\r\n      // 将获取到的数组赋值-方便使用\r\n      let sDate = this.dateArr[0];\r\n      let mDate = this.dateArr[1];\r\n      let hDate = this.dateArr[2];\r\n      let DDate = this.dateArr[3];\r\n      let MDate = this.dateArr[4];\r\n      let YDate = this.dateArr[5];\r\n      // 获取当前时间在数组中的索引\r\n      let sIdx = this.getIndex(sDate, nSecond);\r\n      let mIdx = this.getIndex(mDate, nMin);\r\n      let hIdx = this.getIndex(hDate, nHour);\r\n      let DIdx = this.getIndex(DDate, nDay);\r\n      let MIdx = this.getIndex(MDate, nMonth);\r\n      let YIdx = this.getIndex(YDate, nYear);\r\n      // 重置月日时分秒的函数(后面用的比较多)\r\n      const resetSecond = function () {\r\n        sIdx = 0;\r\n        nSecond = sDate[sIdx]\r\n      }\r\n      const resetMin = function () {\r\n        mIdx = 0;\r\n        nMin = mDate[mIdx]\r\n        resetSecond();\r\n      }\r\n      const resetHour = function () {\r\n        hIdx = 0;\r\n        nHour = hDate[hIdx]\r\n        resetMin();\r\n      }\r\n      const resetDay = function () {\r\n        DIdx = 0;\r\n        nDay = DDate[DIdx]\r\n        resetHour();\r\n      }\r\n      const resetMonth = function () {\r\n        MIdx = 0;\r\n        nMonth = MDate[MIdx]\r\n        resetDay();\r\n      }\r\n      // 如果当前年份不为数组中当前值\r\n      if (nYear != YDate[YIdx]) {\r\n        resetMonth();\r\n      }\r\n      // 如果当前月份不为数组中当前值\r\n      if (nMonth != MDate[MIdx]) {\r\n        resetDay();\r\n      }\r\n      // 如果当前“日”不为数组中当前值\r\n      if (nDay != DDate[DIdx]) {\r\n        resetHour();\r\n      }\r\n      // 如果当前“时”不为数组中当前值\r\n      if (nHour != hDate[hIdx]) {\r\n        resetMin();\r\n      }\r\n      // 如果当前“分”不为数组中当前值\r\n      if (nMin != mDate[mIdx]) {\r\n        resetSecond();\r\n      }\r\n\r\n      // 循环年份数组\r\n      goYear: for (let Yi = YIdx; Yi < YDate.length; Yi++) {\r\n        let YY = YDate[Yi];\r\n        // 如果到达最大值时\r\n        if (nMonth > MDate[MDate.length - 1]) {\r\n          resetMonth();\r\n          continue;\r\n        }\r\n        // 循环月份数组\r\n        goMonth: for (let Mi = MIdx; Mi < MDate.length; Mi++) {\r\n          // 赋值、方便后面运算\r\n          let MM = MDate[Mi];\r\n          MM = MM < 10 ? '0' + MM : MM;\r\n          // 如果到达最大值时\r\n          if (nDay > DDate[DDate.length - 1]) {\r\n            resetDay();\r\n            if (Mi == MDate.length - 1) {\r\n              resetMonth();\r\n              continue goYear;\r\n            }\r\n            continue;\r\n          }\r\n          // 循环日期数组\r\n          goDay: for (let Di = DIdx; Di < DDate.length; Di++) {\r\n            // 赋值、方便后面运算\r\n            let DD = DDate[Di];\r\n            let thisDD = DD < 10 ? '0' + DD : DD;\r\n\r\n            // 如果到达最大值时\r\n            if (nHour > hDate[hDate.length - 1]) {\r\n              resetHour();\r\n              if (Di == DDate.length - 1) {\r\n                resetDay();\r\n                if (Mi == MDate.length - 1) {\r\n                  resetMonth();\r\n                  continue goYear;\r\n                }\r\n                continue goMonth;\r\n              }\r\n              continue;\r\n            }\r\n\r\n            // 判断日期的合法性，不合法的话也是跳出当前循环\r\n            if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true && this.dayRule != 'workDay' && this.dayRule != 'lastWeek' && this.dayRule != 'lastDay') {\r\n              resetDay();\r\n              continue goMonth;\r\n            }\r\n            // 如果日期规则中有值时\r\n            if (this.dayRule == 'lastDay') {\r\n              // 如果不是合法日期则需要将前将日期调到合法日期即月末最后一天\r\n\r\n              if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                while (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD--;\r\n\r\n                  thisDD = DD < 10 ? '0' + DD : DD;\r\n                }\r\n              }\r\n            } else if (this.dayRule == 'workDay') {\r\n              // 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\r\n              if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                while (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD--;\r\n                  thisDD = DD < 10 ? '0' + DD : DD;\r\n                }\r\n              }\r\n              // 获取达到条件的日期是星期X\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\r\n              // 当星期日时\r\n              if (thisWeek == 1) {\r\n                // 先找下一个日，并判断是否为月底\r\n                DD++;\r\n                thisDD = DD < 10 ? '0' + DD : DD;\r\n                // 判断下一日已经不是合法日期\r\n                if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD -= 3;\r\n                }\r\n              } else if (thisWeek == 7) {\r\n                // 当星期6时只需判断不是1号就可进行操作\r\n                if (this.dayRuleSup != 1) {\r\n                  DD--;\r\n                } else {\r\n                  DD += 2;\r\n                }\r\n              }\r\n            } else if (this.dayRule == 'weekDay') {\r\n              // 如果指定了是星期几\r\n              // 获取当前日期是属于星期几\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\r\n              // 校验当前星期是否在星期池（dayRuleSup）中\r\n              if (this.dayRuleSup.indexOf(thisWeek) < 0) {\r\n                // 如果到达最大值时\r\n                if (Di == DDate.length - 1) {\r\n                  resetDay();\r\n                  if (Mi == MDate.length - 1) {\r\n                    resetMonth();\r\n                    continue goYear;\r\n                  }\r\n                  continue goMonth;\r\n                }\r\n                continue;\r\n              }\r\n            } else if (this.dayRule == 'assWeek') {\r\n              // 如果指定了是第几周的星期几\r\n              // 获取每月1号是属于星期几\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\r\n              if (this.dayRuleSup[1] >= thisWeek) {\r\n                DD = (this.dayRuleSup[0] - 1) * 7 + this.dayRuleSup[1] - thisWeek + 1;\r\n              } else {\r\n                DD = this.dayRuleSup[0] * 7 + this.dayRuleSup[1] - thisWeek + 1;\r\n              }\r\n            } else if (this.dayRule == 'lastWeek') {\r\n              // 如果指定了每月最后一个星期几\r\n              // 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\r\n              if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                while (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD--;\r\n                  thisDD = DD < 10 ? '0' + DD : DD;\r\n                }\r\n              }\r\n              // 获取月末最后一天是星期几\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\r\n              // 找到要求中最近的那个星期几\r\n              if (this.dayRuleSup < thisWeek) {\r\n                DD -= thisWeek - this.dayRuleSup;\r\n              } else if (this.dayRuleSup > thisWeek) {\r\n                DD -= 7 - (this.dayRuleSup - thisWeek)\r\n              }\r\n            }\r\n            // 判断时间值是否小于10置换成“05”这种格式\r\n            DD = DD < 10 ? '0' + DD : DD;\r\n\r\n            // 循环“时”数组\r\n            goHour: for (let hi = hIdx; hi < hDate.length; hi++) {\r\n              let hh = hDate[hi] < 10 ? '0' + hDate[hi] : hDate[hi]\r\n\r\n              // 如果到达最大值时\r\n              if (nMin > mDate[mDate.length - 1]) {\r\n                resetMin();\r\n                if (hi == hDate.length - 1) {\r\n                  resetHour();\r\n                  if (Di == DDate.length - 1) {\r\n                    resetDay();\r\n                    if (Mi == MDate.length - 1) {\r\n                      resetMonth();\r\n                      continue goYear;\r\n                    }\r\n                    continue goMonth;\r\n                  }\r\n                  continue goDay;\r\n                }\r\n                continue;\r\n              }\r\n              // 循环\"分\"数组\r\n              goMin: for (let mi = mIdx; mi < mDate.length; mi++) {\r\n                let mm = mDate[mi] < 10 ? '0' + mDate[mi] : mDate[mi];\r\n\r\n                // 如果到达最大值时\r\n                if (nSecond > sDate[sDate.length - 1]) {\r\n                  resetSecond();\r\n                  if (mi == mDate.length - 1) {\r\n                    resetMin();\r\n                    if (hi == hDate.length - 1) {\r\n                      resetHour();\r\n                      if (Di == DDate.length - 1) {\r\n                        resetDay();\r\n                        if (Mi == MDate.length - 1) {\r\n                          resetMonth();\r\n                          continue goYear;\r\n                        }\r\n                        continue goMonth;\r\n                      }\r\n                      continue goDay;\r\n                    }\r\n                    continue goHour;\r\n                  }\r\n                  continue;\r\n                }\r\n                // 循环\"秒\"数组\r\n                for (let si = sIdx; si <= sDate.length - 1; si++) {\r\n                  let ss = sDate[si] < 10 ? '0' + sDate[si] : sDate[si];\r\n                  // 添加当前时间（时间合法性在日期循环时已经判断）\r\n                  if (MM != '00' && DD != '00') {\r\n                    resultArr.push(YY + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss)\r\n                    nums++;\r\n                  }\r\n                  // 如果条数满了就退出循环\r\n                  if (nums == 5) break goYear;\r\n                  // 如果到达最大值时\r\n                  if (si == sDate.length - 1) {\r\n                    resetSecond();\r\n                    if (mi == mDate.length - 1) {\r\n                      resetMin();\r\n                      if (hi == hDate.length - 1) {\r\n                        resetHour();\r\n                        if (Di == DDate.length - 1) {\r\n                          resetDay();\r\n                          if (Mi == MDate.length - 1) {\r\n                            resetMonth();\r\n                            continue goYear;\r\n                          }\r\n                          continue goMonth;\r\n                        }\r\n                        continue goDay;\r\n                      }\r\n                      continue goHour;\r\n                    }\r\n                    continue goMin;\r\n                  }\r\n                } //goSecond\r\n              } //goMin\r\n            }//goHour\r\n          }//goDay\r\n        }//goMonth\r\n      }\r\n      // 判断100年内的结果条数\r\n      if (resultArr.length == 0) {\r\n        this.resultList = ['没有达到条件的结果！'];\r\n      } else {\r\n        this.resultList = resultArr;\r\n        if (resultArr.length != 5) {\r\n          this.resultList.push('最近100年内只有上面' + resultArr.length + '条结果！')\r\n        }\r\n      }\r\n      // 计算完成-显示结果\r\n      this.isShow = true;\r\n\r\n\r\n    },\r\n    // 用于计算某位数字在数组中的索引\r\n    getIndex(arr, value) {\r\n      if (value <= arr[0] || value > arr[arr.length - 1]) {\r\n        return 0;\r\n      } else {\r\n        for (let i = 0; i < arr.length - 1; i++) {\r\n          if (value > arr[i] && value <= arr[i + 1]) {\r\n            return i + 1;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 获取\"年\"数组\r\n    getYearArr(rule, year) {\r\n      this.dateArr[5] = this.getOrderArr(year, year + 100);\r\n      if (rule != undefined) {\r\n        if (rule.indexOf('-') >= 0) {\r\n          this.dateArr[5] = this.getCycleArr(rule, year + 100, false)\r\n        } else if (rule.indexOf('/') >= 0) {\r\n          this.dateArr[5] = this.getAverageArr(rule, year + 100)\r\n        } else if (rule != '*') {\r\n          this.dateArr[5] = this.getAssignArr(rule)\r\n        }\r\n      }\r\n    },\r\n    // 获取\"月\"数组\r\n    getMonthArr(rule) {\r\n      this.dateArr[4] = this.getOrderArr(1, 12);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[4] = this.getCycleArr(rule, 12, false)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[4] = this.getAverageArr(rule, 12)\r\n      } else if (rule != '*') {\r\n        this.dateArr[4] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 获取\"日\"数组-主要为日期规则\r\n    getWeekArr(rule) {\r\n      // 只有当日期规则的两个值均为“”时则表达日期是有选项的\r\n      if (this.dayRule == '' && this.dayRuleSup == '') {\r\n        if (rule.indexOf('-') >= 0) {\r\n          this.dayRule = 'weekDay';\r\n          this.dayRuleSup = this.getCycleArr(rule, 7, false)\r\n        } else if (rule.indexOf('#') >= 0) {\r\n          this.dayRule = 'assWeek';\r\n          let matchRule = rule.match(/[0-9]{1}/g);\r\n          this.dayRuleSup = [Number(matchRule[1]), Number(matchRule[0])];\r\n          this.dateArr[3] = [1];\r\n          if (this.dayRuleSup[1] == 7) {\r\n            this.dayRuleSup[1] = 0;\r\n          }\r\n        } else if (rule.indexOf('L') >= 0) {\r\n          this.dayRule = 'lastWeek';\r\n          this.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\r\n          this.dateArr[3] = [31];\r\n          if (this.dayRuleSup == 7) {\r\n            this.dayRuleSup = 0;\r\n          }\r\n        } else if (rule != '*' && rule != '?') {\r\n          this.dayRule = 'weekDay';\r\n          this.dayRuleSup = this.getAssignArr(rule)\r\n        }\r\n      }\r\n    },\r\n    // 获取\"日\"数组-少量为日期规则\r\n    getDayArr(rule) {\r\n      this.dateArr[3] = this.getOrderArr(1, 31);\r\n      this.dayRule = '';\r\n      this.dayRuleSup = '';\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[3] = this.getCycleArr(rule, 31, false)\r\n        this.dayRuleSup = 'null';\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[3] = this.getAverageArr(rule, 31)\r\n        this.dayRuleSup = 'null';\r\n      } else if (rule.indexOf('W') >= 0) {\r\n        this.dayRule = 'workDay';\r\n        this.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\r\n        this.dateArr[3] = [this.dayRuleSup];\r\n      } else if (rule.indexOf('L') >= 0) {\r\n        this.dayRule = 'lastDay';\r\n        this.dayRuleSup = 'null';\r\n        this.dateArr[3] = [31];\r\n      } else if (rule != '*' && rule != '?') {\r\n        this.dateArr[3] = this.getAssignArr(rule)\r\n        this.dayRuleSup = 'null';\r\n      } else if (rule == '*') {\r\n        this.dayRuleSup = 'null';\r\n      }\r\n    },\r\n    // 获取\"时\"数组\r\n    getHourArr(rule) {\r\n      this.dateArr[2] = this.getOrderArr(0, 23);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[2] = this.getCycleArr(rule, 24, true)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[2] = this.getAverageArr(rule, 23)\r\n      } else if (rule != '*') {\r\n        this.dateArr[2] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 获取\"分\"数组\r\n    getMinArr(rule) {\r\n      this.dateArr[1] = this.getOrderArr(0, 59);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[1] = this.getCycleArr(rule, 60, true)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[1] = this.getAverageArr(rule, 59)\r\n      } else if (rule != '*') {\r\n        this.dateArr[1] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 获取\"秒\"数组\r\n    getSecondArr(rule) {\r\n      this.dateArr[0] = this.getOrderArr(0, 59);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[0] = this.getCycleArr(rule, 60, true)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[0] = this.getAverageArr(rule, 59)\r\n      } else if (rule != '*') {\r\n        this.dateArr[0] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 根据传进来的min-max返回一个顺序的数组\r\n    getOrderArr(min, max) {\r\n      let arr = [];\r\n      for (let i = min; i <= max; i++) {\r\n        arr.push(i);\r\n      }\r\n      return arr;\r\n    },\r\n    // 根据规则中指定的零散值返回一个数组\r\n    getAssignArr(rule) {\r\n      let arr = [];\r\n      let assiginArr = rule.split(',');\r\n      for (let i = 0; i < assiginArr.length; i++) {\r\n        arr[i] = Number(assiginArr[i])\r\n      }\r\n      arr.sort(this.compare)\r\n      return arr;\r\n    },\r\n    // 根据一定算术规则计算返回一个数组\r\n    getAverageArr(rule, limit) {\r\n      let arr = [];\r\n      let agArr = rule.split('/');\r\n      let min = Number(agArr[0]);\r\n      let step = Number(agArr[1]);\r\n      while (min <= limit) {\r\n        arr.push(min);\r\n        min += step;\r\n      }\r\n      return arr;\r\n    },\r\n    // 根据规则返回一个具有周期性的数组\r\n    getCycleArr(rule, limit, status) {\r\n      // status--表示是否从0开始（则从1开始）\r\n      let arr = [];\r\n      let cycleArr = rule.split('-');\r\n      let min = Number(cycleArr[0]);\r\n      let max = Number(cycleArr[1]);\r\n      if (min > max) {\r\n        max += limit;\r\n      }\r\n      for (let i = min; i <= max; i++) {\r\n        let add = 0;\r\n        if (status == false && i % limit == 0) {\r\n          add = limit;\r\n        }\r\n        arr.push(Math.round(i % limit + add))\r\n      }\r\n      arr.sort(this.compare)\r\n      return arr;\r\n    },\r\n    // 比较数字大小（用于Array.sort）\r\n    compare(value1, value2) {\r\n      if (value2 - value1 > 0) {\r\n        return -1;\r\n      } else {\r\n        return 1;\r\n      }\r\n    },\r\n    // 格式化日期格式如：2017-9-19 18:04:33\r\n    formatDate(value, type) {\r\n      // 计算日期相关值\r\n      let time = typeof value == 'number' ? new Date(value) : value;\r\n      let Y = time.getFullYear();\r\n      let M = time.getMonth() + 1;\r\n      let D = time.getDate();\r\n      let h = time.getHours();\r\n      let m = time.getMinutes();\r\n      let s = time.getSeconds();\r\n      let week = time.getDay();\r\n      // 如果传递了type的话\r\n      if (type == undefined) {\r\n        return Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) + ' ' + (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m) + ':' + (s < 10 ? '0' + s : s);\r\n      } else if (type == 'week') {\r\n        // 在quartz中 1为星期日\r\n        return week + 1;\r\n      }\r\n    },\r\n    // 检查日期是否存在\r\n    checkDate(value) {\r\n      let time = new Date(value);\r\n      let format = this.formatDate(time)\r\n      return value == format;\r\n    }\r\n  },\r\n  watch: {\r\n    'ex': 'expressionChange'\r\n  },\r\n  props: ['ex'],\r\n  mounted: function () {\r\n    // 初始化 获取一次结果\r\n    this.expressionChange();\r\n  }\r\n}\r\n\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;eAaA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,OAAA;MACAC,UAAA;MACAC,MAAA;IACA;EACA;EACAC,IAAA;EACAC,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MAEA;MACA,KAAAH,MAAA;MACA;MACA,IAAAI,OAAA,QAAAC,QAAA,CAAAC,SAAA,CAAAC,EAAA,CAAAC,KAAA;MACA;MACA,IAAAC,IAAA;MACA;MACA,IAAAC,SAAA;MACA;MACA,IAAAC,KAAA,OAAAC,IAAA;MACA,IAAAC,KAAA,GAAAF,KAAA,CAAAG,WAAA;MACA,IAAAC,MAAA,GAAAJ,KAAA,CAAAK,QAAA;MACA,IAAAC,IAAA,GAAAN,KAAA,CAAAO,OAAA;MACA,IAAAC,KAAA,GAAAR,KAAA,CAAAS,QAAA;MACA,IAAAC,IAAA,GAAAV,KAAA,CAAAW,UAAA;MACA,IAAAC,OAAA,GAAAZ,KAAA,CAAAa,UAAA;MACA;MACA,KAAAC,YAAA,CAAArB,OAAA;MACA,KAAAsB,SAAA,CAAAtB,OAAA;MACA,KAAAuB,UAAA,CAAAvB,OAAA;MACA,KAAAwB,SAAA,CAAAxB,OAAA;MACA,KAAAyB,WAAA,CAAAzB,OAAA;MACA,KAAA0B,UAAA,CAAA1B,OAAA;MACA,KAAA2B,UAAA,CAAA3B,OAAA,KAAAS,KAAA;MACA;MACA,IAAAmB,KAAA,QAAAlC,OAAA;MACA,IAAAmC,KAAA,QAAAnC,OAAA;MACA,IAAAoC,KAAA,QAAApC,OAAA;MACA,IAAAqC,KAAA,QAAArC,OAAA;MACA,IAAAsC,KAAA,QAAAtC,OAAA;MACA,IAAAuC,KAAA,QAAAvC,OAAA;MACA;MACA,IAAAwC,IAAA,QAAAC,QAAA,CAAAP,KAAA,EAAAT,OAAA;MACA,IAAAiB,IAAA,QAAAD,QAAA,CAAAN,KAAA,EAAAZ,IAAA;MACA,IAAAoB,IAAA,QAAAF,QAAA,CAAAL,KAAA,EAAAf,KAAA;MACA,IAAAuB,IAAA,QAAAH,QAAA,CAAAJ,KAAA,EAAAlB,IAAA;MACA,IAAA0B,IAAA,QAAAJ,QAAA,CAAAH,KAAA,EAAArB,MAAA;MACA,IAAA6B,IAAA,QAAAL,QAAA,CAAAF,KAAA,EAAAxB,KAAA;MACA;MACA,IAAAgC,WAAA,YAAAA,YAAA;QACAP,IAAA;QACAf,OAAA,GAAAS,KAAA,CAAAM,IAAA;MACA;MACA,IAAAQ,QAAA,YAAAA,SAAA;QACAN,IAAA;QACAnB,IAAA,GAAAY,KAAA,CAAAO,IAAA;QACAK,WAAA;MACA;MACA,IAAAE,SAAA,YAAAA,UAAA;QACAN,IAAA;QACAtB,KAAA,GAAAe,KAAA,CAAAO,IAAA;QACAK,QAAA;MACA;MACA,IAAAE,QAAA,YAAAA,SAAA;QACAN,IAAA;QACAzB,IAAA,GAAAkB,KAAA,CAAAO,IAAA;QACAK,SAAA;MACA;MACA,IAAAE,UAAA,YAAAA,WAAA;QACAN,IAAA;QACA5B,MAAA,GAAAqB,KAAA,CAAAO,IAAA;QACAK,QAAA;MACA;MACA;MACA,IAAAnC,KAAA,IAAAwB,KAAA,CAAAO,IAAA;QACAK,UAAA;MACA;MACA;MACA,IAAAlC,MAAA,IAAAqB,KAAA,CAAAO,IAAA;QACAK,QAAA;MACA;MACA;MACA,IAAA/B,IAAA,IAAAkB,KAAA,CAAAO,IAAA;QACAK,SAAA;MACA;MACA;MACA,IAAA5B,KAAA,IAAAe,KAAA,CAAAO,IAAA;QACAK,QAAA;MACA;MACA;MACA,IAAAzB,IAAA,IAAAY,KAAA,CAAAO,IAAA;QACAK,WAAA;MACA;;MAEA;MACAK,MAAA,WAAAC,EAAA,GAAAP,IAAA,EAAAO,EAAA,GAAAd,KAAA,CAAAe,MAAA,EAAAD,EAAA;QACA,IAAAE,EAAA,GAAAhB,KAAA,CAAAc,EAAA;QACA;QACA,IAAApC,MAAA,GAAAqB,KAAA,CAAAA,KAAA,CAAAgB,MAAA;UACAH,UAAA;UACA;QACA;QACA;QACAK,OAAA,WAAAC,EAAA,GAAAZ,IAAA,EAAAY,EAAA,GAAAnB,KAAA,CAAAgB,MAAA,EAAAG,EAAA;UACA;UACA,IAAAC,EAAA,GAAApB,KAAA,CAAAmB,EAAA;UACAC,EAAA,GAAAA,EAAA,cAAAA,EAAA,GAAAA,EAAA;UACA;UACA,IAAAvC,IAAA,GAAAkB,KAAA,CAAAA,KAAA,CAAAiB,MAAA;YACAJ,QAAA;YACA,IAAAO,EAAA,IAAAnB,KAAA,CAAAgB,MAAA;cACAH,UAAA;cACA,SAAAC,MAAA;YACA;YACA;UACA;UACA;UACAO,KAAA,WAAAC,EAAA,GAAAhB,IAAA,EAAAgB,EAAA,GAAAvB,KAAA,CAAAiB,MAAA,EAAAM,EAAA;YACA;YACA,IAAAC,EAAA,GAAAxB,KAAA,CAAAuB,EAAA;YACA,IAAAE,MAAA,GAAAD,EAAA,cAAAA,EAAA,GAAAA,EAAA;;YAEA;YACA,IAAAxC,KAAA,GAAAe,KAAA,CAAAA,KAAA,CAAAkB,MAAA;cACAL,SAAA;cACA,IAAAW,EAAA,IAAAvB,KAAA,CAAAiB,MAAA;gBACAJ,QAAA;gBACA,IAAAO,EAAA,IAAAnB,KAAA,CAAAgB,MAAA;kBACAH,UAAA;kBACA,SAAAC,MAAA;gBACA;gBACA,SAAAI,OAAA;cACA;cACA;YACA;;YAEA;YACA,SAAAO,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA,gCAAAhE,OAAA,sBAAAA,OAAA,uBAAAA,OAAA;cACAoD,QAAA;cACA,SAAAM,OAAA;YACA;YACA;YACA,SAAA1D,OAAA;cACA;;cAEA,SAAAiE,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA;gBACA,OAAAD,EAAA,aAAAE,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA;kBACAD,EAAA;kBAEAC,MAAA,GAAAD,EAAA,cAAAA,EAAA,GAAAA,EAAA;gBACA;cACA;YACA,gBAAA/D,OAAA;cACA;cACA,SAAAiE,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA;gBACA,OAAAD,EAAA,aAAAE,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA;kBACAD,EAAA;kBACAC,MAAA,GAAAD,EAAA,cAAAA,EAAA,GAAAA,EAAA;gBACA;cACA;cACA;cACA,IAAAG,QAAA,QAAAC,UAAA,KAAAnD,IAAA,CAAAyC,EAAA,SAAAG,EAAA,SAAAI,MAAA;cACA;cACA,IAAAE,QAAA;gBACA;gBACAH,EAAA;gBACAC,MAAA,GAAAD,EAAA,cAAAA,EAAA,GAAAA,EAAA;gBACA;gBACA,SAAAE,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA;kBACAD,EAAA;gBACA;cACA,WAAAG,QAAA;gBACA;gBACA,SAAAjE,UAAA;kBACA8D,EAAA;gBACA;kBACAA,EAAA;gBACA;cACA;YACA,gBAAA/D,OAAA;cACA;cACA;cACA,IAAAkE,SAAA,QAAAC,UAAA,KAAAnD,IAAA,CAAAyC,EAAA,SAAAG,EAAA,SAAAG,EAAA;cACA;cACA,SAAA9D,UAAA,CAAAmE,OAAA,CAAAF,SAAA;gBACA;gBACA,IAAAJ,EAAA,IAAAvB,KAAA,CAAAiB,MAAA;kBACAJ,QAAA;kBACA,IAAAO,EAAA,IAAAnB,KAAA,CAAAgB,MAAA;oBACAH,UAAA;oBACA,SAAAC,MAAA;kBACA;kBACA,SAAAI,OAAA;gBACA;gBACA;cACA;YACA,gBAAA1D,OAAA;cACA;cACA;cACA,IAAAkE,UAAA,QAAAC,UAAA,KAAAnD,IAAA,CAAAyC,EAAA,SAAAG,EAAA,SAAAG,EAAA;cACA,SAAA9D,UAAA,OAAAiE,UAAA;gBACAH,EAAA,SAAA9D,UAAA,oBAAAA,UAAA,MAAAiE,UAAA;cACA;gBACAH,EAAA,QAAA9D,UAAA,eAAAA,UAAA,MAAAiE,UAAA;cACA;YACA,gBAAAlE,OAAA;cACA;cACA;cACA,SAAAiE,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA;gBACA,OAAAD,EAAA,aAAAE,SAAA,CAAAR,EAAA,SAAAG,EAAA,SAAAI,MAAA;kBACAD,EAAA;kBACAC,MAAA,GAAAD,EAAA,cAAAA,EAAA,GAAAA,EAAA;gBACA;cACA;cACA;cACA,IAAAG,UAAA,QAAAC,UAAA,KAAAnD,IAAA,CAAAyC,EAAA,SAAAG,EAAA,SAAAI,MAAA;cACA;cACA,SAAA/D,UAAA,GAAAiE,UAAA;gBACAH,EAAA,IAAAG,UAAA,QAAAjE,UAAA;cACA,gBAAAA,UAAA,GAAAiE,UAAA;gBACAH,EAAA,cAAA9D,UAAA,GAAAiE,UAAA;cACA;YACA;YACA;YACAH,EAAA,GAAAA,EAAA,cAAAA,EAAA,GAAAA,EAAA;;YAEA;YACAM,MAAA,WAAAC,EAAA,GAAAzB,IAAA,EAAAyB,EAAA,GAAAhC,KAAA,CAAAkB,MAAA,EAAAc,EAAA;cACA,IAAAC,EAAA,GAAAjC,KAAA,CAAAgC,EAAA,eAAAhC,KAAA,CAAAgC,EAAA,IAAAhC,KAAA,CAAAgC,EAAA;;cAEA;cACA,IAAA7C,IAAA,GAAAY,KAAA,CAAAA,KAAA,CAAAmB,MAAA;gBACAN,QAAA;gBACA,IAAAoB,EAAA,IAAAhC,KAAA,CAAAkB,MAAA;kBACAL,SAAA;kBACA,IAAAW,EAAA,IAAAvB,KAAA,CAAAiB,MAAA;oBACAJ,QAAA;oBACA,IAAAO,EAAA,IAAAnB,KAAA,CAAAgB,MAAA;sBACAH,UAAA;sBACA,SAAAC,MAAA;oBACA;oBACA,SAAAI,OAAA;kBACA;kBACA,SAAAG,KAAA;gBACA;gBACA;cACA;cACA;cACAW,KAAA,WAAAC,EAAA,GAAA7B,IAAA,EAAA6B,EAAA,GAAApC,KAAA,CAAAmB,MAAA,EAAAiB,EAAA;gBACA,IAAAC,EAAA,GAAArC,KAAA,CAAAoC,EAAA,eAAApC,KAAA,CAAAoC,EAAA,IAAApC,KAAA,CAAAoC,EAAA;;gBAEA;gBACA,IAAA9C,OAAA,GAAAS,KAAA,CAAAA,KAAA,CAAAoB,MAAA;kBACAP,WAAA;kBACA,IAAAwB,EAAA,IAAApC,KAAA,CAAAmB,MAAA;oBACAN,QAAA;oBACA,IAAAoB,EAAA,IAAAhC,KAAA,CAAAkB,MAAA;sBACAL,SAAA;sBACA,IAAAW,EAAA,IAAAvB,KAAA,CAAAiB,MAAA;wBACAJ,QAAA;wBACA,IAAAO,EAAA,IAAAnB,KAAA,CAAAgB,MAAA;0BACAH,UAAA;0BACA,SAAAC,MAAA;wBACA;wBACA,SAAAI,OAAA;sBACA;sBACA,SAAAG,KAAA;oBACA;oBACA,SAAAQ,MAAA;kBACA;kBACA;gBACA;gBACA;gBACA,SAAAM,EAAA,GAAAjC,IAAA,EAAAiC,EAAA,IAAAvC,KAAA,CAAAoB,MAAA,MAAAmB,EAAA;kBACA,IAAAC,EAAA,GAAAxC,KAAA,CAAAuC,EAAA,eAAAvC,KAAA,CAAAuC,EAAA,IAAAvC,KAAA,CAAAuC,EAAA;kBACA;kBACA,IAAAf,EAAA,YAAAG,EAAA;oBACAjD,SAAA,CAAA+D,IAAA,CAAApB,EAAA,SAAAG,EAAA,SAAAG,EAAA,SAAAQ,EAAA,SAAAG,EAAA,SAAAE,EAAA;oBACA/D,IAAA;kBACA;kBACA;kBACA,IAAAA,IAAA,aAAAyC,MAAA;kBACA;kBACA,IAAAqB,EAAA,IAAAvC,KAAA,CAAAoB,MAAA;oBACAP,WAAA;oBACA,IAAAwB,EAAA,IAAApC,KAAA,CAAAmB,MAAA;sBACAN,QAAA;sBACA,IAAAoB,EAAA,IAAAhC,KAAA,CAAAkB,MAAA;wBACAL,SAAA;wBACA,IAAAW,EAAA,IAAAvB,KAAA,CAAAiB,MAAA;0BACAJ,QAAA;0BACA,IAAAO,EAAA,IAAAnB,KAAA,CAAAgB,MAAA;4BACAH,UAAA;4BACA,SAAAC,MAAA;0BACA;0BACA,SAAAI,OAAA;wBACA;wBACA,SAAAG,KAAA;sBACA;sBACA,SAAAQ,MAAA;oBACA;oBACA,SAAAG,KAAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;MACA;MACA,IAAA1D,SAAA,CAAA0C,MAAA;QACA,KAAArD,UAAA;MACA;QACA,KAAAA,UAAA,GAAAW,SAAA;QACA,IAAAA,SAAA,CAAA0C,MAAA;UACA,KAAArD,UAAA,CAAA0E,IAAA,iBAAA/D,SAAA,CAAA0C,MAAA;QACA;MACA;MACA;MACA,KAAApD,MAAA;IAGA;IACA;IACAuC,QAAA,WAAAA,SAAAmC,GAAA,EAAAC,KAAA;MACA,IAAAA,KAAA,IAAAD,GAAA,OAAAC,KAAA,GAAAD,GAAA,CAAAA,GAAA,CAAAtB,MAAA;QACA;MACA;QACA,SAAAwB,CAAA,MAAAA,CAAA,GAAAF,GAAA,CAAAtB,MAAA,MAAAwB,CAAA;UACA,IAAAD,KAAA,GAAAD,GAAA,CAAAE,CAAA,KAAAD,KAAA,IAAAD,GAAA,CAAAE,CAAA;YACA,OAAAA,CAAA;UACA;QACA;MACA;IACA;IACA;IACA7C,UAAA,WAAAA,WAAA8C,IAAA,EAAAC,IAAA;MACA,KAAAhF,OAAA,WAAAiF,WAAA,CAAAD,IAAA,EAAAA,IAAA;MACA,IAAAD,IAAA,IAAAG,SAAA;QACA,IAAAH,IAAA,CAAAb,OAAA;UACA,KAAAlE,OAAA,WAAAmF,WAAA,CAAAJ,IAAA,EAAAC,IAAA;QACA,WAAAD,IAAA,CAAAb,OAAA;UACA,KAAAlE,OAAA,WAAAoF,aAAA,CAAAL,IAAA,EAAAC,IAAA;QACA,WAAAD,IAAA;UACA,KAAA/E,OAAA,WAAAqF,YAAA,CAAAN,IAAA;QACA;MACA;IACA;IACA;IACAhD,WAAA,WAAAA,YAAAgD,IAAA;MACA,KAAA/E,OAAA,WAAAiF,WAAA;MACA,IAAAF,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAmF,WAAA,CAAAJ,IAAA;MACA,WAAAA,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAoF,aAAA,CAAAL,IAAA;MACA,WAAAA,IAAA;QACA,KAAA/E,OAAA,WAAAqF,YAAA,CAAAN,IAAA;MACA;IACA;IACA;IACA/C,UAAA,WAAAA,WAAA+C,IAAA;MACA;MACA,SAAAjF,OAAA,eAAAC,UAAA;QACA,IAAAgF,IAAA,CAAAb,OAAA;UACA,KAAApE,OAAA;UACA,KAAAC,UAAA,QAAAoF,WAAA,CAAAJ,IAAA;QACA,WAAAA,IAAA,CAAAb,OAAA;UACA,KAAApE,OAAA;UACA,IAAAwF,SAAA,GAAAP,IAAA,CAAAQ,KAAA;UACA,KAAAxF,UAAA,IAAAyF,MAAA,CAAAF,SAAA,MAAAE,MAAA,CAAAF,SAAA;UACA,KAAAtF,OAAA;UACA,SAAAD,UAAA;YACA,KAAAA,UAAA;UACA;QACA,WAAAgF,IAAA,CAAAb,OAAA;UACA,KAAApE,OAAA;UACA,KAAAC,UAAA,GAAAyF,MAAA,CAAAT,IAAA,CAAAQ,KAAA;UACA,KAAAvF,OAAA;UACA,SAAAD,UAAA;YACA,KAAAA,UAAA;UACA;QACA,WAAAgF,IAAA,WAAAA,IAAA;UACA,KAAAjF,OAAA;UACA,KAAAC,UAAA,QAAAsF,YAAA,CAAAN,IAAA;QACA;MACA;IACA;IACA;IACAjD,SAAA,WAAAA,UAAAiD,IAAA;MACA,KAAA/E,OAAA,WAAAiF,WAAA;MACA,KAAAnF,OAAA;MACA,KAAAC,UAAA;MACA,IAAAgF,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAmF,WAAA,CAAAJ,IAAA;QACA,KAAAhF,UAAA;MACA,WAAAgF,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAoF,aAAA,CAAAL,IAAA;QACA,KAAAhF,UAAA;MACA,WAAAgF,IAAA,CAAAb,OAAA;QACA,KAAApE,OAAA;QACA,KAAAC,UAAA,GAAAyF,MAAA,CAAAT,IAAA,CAAAQ,KAAA;QACA,KAAAvF,OAAA,YAAAD,UAAA;MACA,WAAAgF,IAAA,CAAAb,OAAA;QACA,KAAApE,OAAA;QACA,KAAAC,UAAA;QACA,KAAAC,OAAA;MACA,WAAA+E,IAAA,WAAAA,IAAA;QACA,KAAA/E,OAAA,WAAAqF,YAAA,CAAAN,IAAA;QACA,KAAAhF,UAAA;MACA,WAAAgF,IAAA;QACA,KAAAhF,UAAA;MACA;IACA;IACA;IACA8B,UAAA,WAAAA,WAAAkD,IAAA;MACA,KAAA/E,OAAA,WAAAiF,WAAA;MACA,IAAAF,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAmF,WAAA,CAAAJ,IAAA;MACA,WAAAA,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAoF,aAAA,CAAAL,IAAA;MACA,WAAAA,IAAA;QACA,KAAA/E,OAAA,WAAAqF,YAAA,CAAAN,IAAA;MACA;IACA;IACA;IACAnD,SAAA,WAAAA,UAAAmD,IAAA;MACA,KAAA/E,OAAA,WAAAiF,WAAA;MACA,IAAAF,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAmF,WAAA,CAAAJ,IAAA;MACA,WAAAA,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAoF,aAAA,CAAAL,IAAA;MACA,WAAAA,IAAA;QACA,KAAA/E,OAAA,WAAAqF,YAAA,CAAAN,IAAA;MACA;IACA;IACA;IACApD,YAAA,WAAAA,aAAAoD,IAAA;MACA,KAAA/E,OAAA,WAAAiF,WAAA;MACA,IAAAF,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAmF,WAAA,CAAAJ,IAAA;MACA,WAAAA,IAAA,CAAAb,OAAA;QACA,KAAAlE,OAAA,WAAAoF,aAAA,CAAAL,IAAA;MACA,WAAAA,IAAA;QACA,KAAA/E,OAAA,WAAAqF,YAAA,CAAAN,IAAA;MACA;IACA;IACA;IACAE,WAAA,WAAAA,YAAAQ,GAAA,EAAAC,GAAA;MACA,IAAAd,GAAA;MACA,SAAAE,CAAA,GAAAW,GAAA,EAAAX,CAAA,IAAAY,GAAA,EAAAZ,CAAA;QACAF,GAAA,CAAAD,IAAA,CAAAG,CAAA;MACA;MACA,OAAAF,GAAA;IACA;IACA;IACAS,YAAA,WAAAA,aAAAN,IAAA;MACA,IAAAH,GAAA;MACA,IAAAe,UAAA,GAAAZ,IAAA,CAAArE,KAAA;MACA,SAAAoE,CAAA,MAAAA,CAAA,GAAAa,UAAA,CAAArC,MAAA,EAAAwB,CAAA;QACAF,GAAA,CAAAE,CAAA,IAAAU,MAAA,CAAAG,UAAA,CAAAb,CAAA;MACA;MACAF,GAAA,CAAAgB,IAAA,MAAAC,OAAA;MACA,OAAAjB,GAAA;IACA;IACA;IACAQ,aAAA,WAAAA,cAAAL,IAAA,EAAAe,KAAA;MACA,IAAAlB,GAAA;MACA,IAAAmB,KAAA,GAAAhB,IAAA,CAAArE,KAAA;MACA,IAAA+E,GAAA,GAAAD,MAAA,CAAAO,KAAA;MACA,IAAAC,IAAA,GAAAR,MAAA,CAAAO,KAAA;MACA,OAAAN,GAAA,IAAAK,KAAA;QACAlB,GAAA,CAAAD,IAAA,CAAAc,GAAA;QACAA,GAAA,IAAAO,IAAA;MACA;MACA,OAAApB,GAAA;IACA;IACA;IACAO,WAAA,WAAAA,YAAAJ,IAAA,EAAAe,KAAA,EAAAG,MAAA;MACA;MACA,IAAArB,GAAA;MACA,IAAAsB,QAAA,GAAAnB,IAAA,CAAArE,KAAA;MACA,IAAA+E,GAAA,GAAAD,MAAA,CAAAU,QAAA;MACA,IAAAR,GAAA,GAAAF,MAAA,CAAAU,QAAA;MACA,IAAAT,GAAA,GAAAC,GAAA;QACAA,GAAA,IAAAI,KAAA;MACA;MACA,SAAAhB,CAAA,GAAAW,GAAA,EAAAX,CAAA,IAAAY,GAAA,EAAAZ,CAAA;QACA,IAAAqB,GAAA;QACA,IAAAF,MAAA,aAAAnB,CAAA,GAAAgB,KAAA;UACAK,GAAA,GAAAL,KAAA;QACA;QACAlB,GAAA,CAAAD,IAAA,CAAAyB,IAAA,CAAAC,KAAA,CAAAvB,CAAA,GAAAgB,KAAA,GAAAK,GAAA;MACA;MACAvB,GAAA,CAAAgB,IAAA,MAAAC,OAAA;MACA,OAAAjB,GAAA;IACA;IACA;IACAiB,OAAA,WAAAA,QAAAS,MAAA,EAAAC,MAAA;MACA,IAAAA,MAAA,GAAAD,MAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACArC,UAAA,WAAAA,WAAAY,KAAA,EAAA2B,IAAA;MACA;MACA,IAAAC,IAAA,UAAA5B,KAAA,mBAAA/D,IAAA,CAAA+D,KAAA,IAAAA,KAAA;MACA,IAAA6B,CAAA,GAAAD,IAAA,CAAAzF,WAAA;MACA,IAAA2F,CAAA,GAAAF,IAAA,CAAAvF,QAAA;MACA,IAAA0F,CAAA,GAAAH,IAAA,CAAArF,OAAA;MACA,IAAAyF,CAAA,GAAAJ,IAAA,CAAAnF,QAAA;MACA,IAAAwF,CAAA,GAAAL,IAAA,CAAAjF,UAAA;MACA,IAAAuF,CAAA,GAAAN,IAAA,CAAA/E,UAAA;MACA,IAAAsF,IAAA,GAAAP,IAAA,CAAAQ,MAAA;MACA;MACA,IAAAT,IAAA,IAAAtB,SAAA;QACA,OAAAwB,CAAA,UAAAC,CAAA,cAAAA,CAAA,GAAAA,CAAA,WAAAC,CAAA,cAAAA,CAAA,GAAAA,CAAA,WAAAC,CAAA,cAAAA,CAAA,GAAAA,CAAA,WAAAC,CAAA,cAAAA,CAAA,GAAAA,CAAA,WAAAC,CAAA,cAAAA,CAAA,GAAAA,CAAA;MACA,WAAAP,IAAA;QACA;QACA,OAAAQ,IAAA;MACA;IACA;IACA;IACAjD,SAAA,WAAAA,UAAAc,KAAA;MACA,IAAA4B,IAAA,OAAA3F,IAAA,CAAA+D,KAAA;MACA,IAAAqC,MAAA,QAAAjD,UAAA,CAAAwC,IAAA;MACA,OAAA5B,KAAA,IAAAqC,MAAA;IACA;EACA;EACAC,KAAA;IACA;EACA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA,KAAAhH,gBAAA;EACA;AACA;AAAAiH,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}