package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * 发票登记对象 rs_vat_invoice
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public class RsVatInvoice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 发票ID，主键
     */
    private Long invoiceId;

    /**
     * 发票流水号（根据订单编号衍生）
     */
    @Excel(name = "发票流水号", readConverterExp = "根=据订单编号衍生")
    private String invoiceCodeNo;

    /**
     * 发票号码
     */
    @Excel(name = "发票号码")
    private String invoiceOfficalNo;

    /**
     * 进销标志：sale=销项，buy=进项
     */
    @Excel(name = "进销标志：sale=销项，buy=进项")
    private String saleBuy;

    /**
     * 所属公司
     */
    @Excel(name = "所属公司")
    private String invoiceBelongsTo;

    /**
     * 我司账户简称（银行code）
     */
    @Excel(name = "我司账户简称", readConverterExp = "银=行code")
    private String richBankCode;

    /**
     * 我司发票抬头
     */
    @Excel(name = "我司发票抬头")
    private String richCompanyTitle;

    /**
     * 我司纳税人识别号
     */
    @Excel(name = "我司纳税人识别号")
    private String richVatSerialNo;

    /**
     * 我司账号
     */
    @Excel(name = "我司账号")
    private String richBankAccount;

    /**
     * 我司银行全称
     */
    @Excel(name = "我司银行全称")
    private String richBankFullname;

    /**
     * 对方公司ID
     */
    @Excel(name = "对方公司ID")
    private Long cooperatorId;

    /**
     * 对方公司简称
     */
    @Excel(name = "对方公司简称")
    private String cooperatorShortName;

    /**
     * 对方账户简称
     */
    @Excel(name = "对方账户简称")
    private String cooperatorBankCode;

    /**
     * 对方发票抬头
     */
    @Excel(name = "对方发票抬头")
    private String cooperatorFullname;

    /**
     * 对方纳税人识别号
     */
    @Excel(name = "对方纳税人识别号")
    private String cooperatorVatSerialNo;

    /**
     * 对方账号
     */
    @Excel(name = "对方账号")
    private String cooperatorBankAccount;

    /**
     * 对方银行全称
     */
    @Excel(name = "对方银行全称")
    private String cooperatorBankFullname;

    /**
     * 所属订单汇总，多个订单号逗号隔开
     */
    @Excel(name = "所属订单汇总，多个订单号逗号隔开")
    private String rctNoSummary;

    /**
     * 对方单号汇总（对方合同号）
     */
    @Excel(name = "对方单号汇总", readConverterExp = "对=方合同号")
    private String cooperatorReferNo;

    /**
     * 发票项目汇总
     */
    @Excel(name = "发票项目汇总")
    private String officalChargeNameSummary;

    /**
     * 结算币种
     */
    @Excel(name = "结算币种")
    private String chargeCurrencyCode;

    /**
     * 账单应收总额
     */
    @Excel(name = "账单应收总额")
    private BigDecimal dnSum;

    /**
     * 银行已收
     */
    @Excel(name = "银行已收")
    private BigDecimal dnRecieved;

    /**
     * 银行未收（自动计算）
     */
    @Excel(name = "银行未收", readConverterExp = "自=动计算")
    private BigDecimal dnBalance;

    /**
     * 账单应付总额
     */
    @Excel(name = "账单应付总额")
    private BigDecimal cnSum;

    /**
     * 银行已付
     */
    @Excel(name = "银行已付")
    private BigDecimal cnPaid;

    /**
     * 银行未付（自动计算）
     */
    @Excel(name = "银行未付", readConverterExp = "自=动计算")
    private BigDecimal cnBalance;

    /**
     * 销账状态：未销账/部分销账/销账完成
     */
    @Excel(name = "销账状态：未销账/部分销账/销账完成")
    private String chargeClearStatus;

    /**
     * 期望支付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "期望支付日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date expectedPayDate;

    /**
     * 批复支付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "批复支付日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date approvedPayDate;

    /**
     * 实际支付日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际支付日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualPayDate;

    /**
     * 对应银行流水（冗余字段）
     */
    @Excel(name = "对应银行流水", readConverterExp = "冗=余字段")
    private String sqdBankStatementList;

    /**
     * 发票币种
     */
    @Excel(name = "发票币种")
    private String invoiceCurrencyCode;

    /**
     * 发票汇率
     */
    @Excel(name = "发票汇率")
    private BigDecimal invoiceExchangeRate;

    /**
     * 不含税金额
     */
    @Excel(name = "不含税金额")
    private BigDecimal invoiceNetAmount;

    /**
     * 税金
     */
    @Excel(name = "税金")
    private BigDecimal vatAmount;

    /**
     * 价税合计
     */
    @Excel(name = "价税合计")
    private BigDecimal invoiceVatAmount;

    /**
     * 销项不含税
     */
    @Excel(name = "销项不含税")
    private BigDecimal saleNetSum;

    /**
     * 销项税金
     */
    @Excel(name = "销项税金")
    private BigDecimal saleTax;

    /**
     * 销项含税合计
     */
    @Excel(name = "销项含税合计")
    private BigDecimal saleTaxTotal;

    /**
     * 进项不含税
     */
    @Excel(name = "进项不含税")
    private BigDecimal buyNetSum;

    /**
     * 进项税金
     */
    @Excel(name = "进项税金")
    private BigDecimal buyTax;

    /**
     * 进项含税合计
     */
    @Excel(name = "进项含税合计")
    private BigDecimal buyTaxTotal;

    /**
     * 发票性质（主营业务收入/成本/费用等）
     */
    @Excel(name = "发票性质", readConverterExp = "主=营业务收入/成本/费用等")
    private String taxClass;

    /**
     * 发票类型（普通发票/增值税专用发票/收据/无票收支）
     */
    @Excel(name = "发票类型", readConverterExp = "普=通发票/增值税专用发票/收据/无票收支")
    private String invoiceType;

    /**
     * 报税所属月份（如 202503）
     */
    @Excel(name = "报税所属月份", readConverterExp = "如=,2=02503")
    private String belongsToMonth;

    /**
     * 发票状态（未申请/已申请/已开票/已报税/已作废）
     */
    @Excel(name = "发票状态", readConverterExp = "未=申请/已申请/已开票/已报税/已作废")
    private String invoiceStatus;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String invoiceRemark;

    /**
     * 合并发票标志（0=否，1=是）
     */
    @Excel(name = "合并发票标志")
    private String mergeInvoice;

    /**
     * 对方发票抬头
     */
    @Excel(name = "对方发票抬头")
    private String cooperatorCompanyTitle;

    /**
     * 相关订单号
     */
    @Excel(name = "相关订单号")
    private String relatedOrderNo;

    /**
     * 报税锁定状态（0=未锁定，1=已锁定）
     */
    @Excel(name = "报税锁定状态")
    private String taxLocked;

    /**
     * 申请人ID
     */
    @Excel(name = "申请人ID")
    private Long applyStuffId;

    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date appliedTime;

    /**
     * 开票人ID
     */
    @Excel(name = "开票人ID")
    private Long issuedStuffId;

    /**
     * 开票时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开票时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date issuedTime;

    /**
     * 报税人ID
     */
    @Excel(name = "报税人ID")
    private Long taxStuffId;

    /**
     * 报税时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报税时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date taxDeclareTime;
    private Long rctId;

    private List<RsCharge> rsChargeList;
    private String invoiceAttachment;

    public String getInvoiceAttachment() {
        return invoiceAttachment;
    }

    public void setInvoiceAttachment(String invoiceAttachment) {
        this.invoiceAttachment = invoiceAttachment;
    }

    public List<RsCharge> getRsCharges() {
        return rsChargeList;
    }

    public void setRsCharges(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getInvoiceCodeNo() {
        return invoiceCodeNo;
    }

    public void setInvoiceCodeNo(String invoiceCodeNo) {
        this.invoiceCodeNo = invoiceCodeNo;
    }

    public String getInvoiceOfficalNo() {
        return invoiceOfficalNo;
    }

    public void setInvoiceOfficalNo(String invoiceOfficalNo) {
        this.invoiceOfficalNo = invoiceOfficalNo;
    }

    public String getSaleBuy() {
        return saleBuy;
    }

    public void setSaleBuy(String saleBuy) {
        this.saleBuy = saleBuy;
    }

    public String getInvoiceBelongsTo() {
        return invoiceBelongsTo;
    }

    public void setInvoiceBelongsTo(String invoiceBelongsTo) {
        this.invoiceBelongsTo = invoiceBelongsTo;
    }

    public String getRichBankCode() {
        return richBankCode;
    }

    public void setRichBankCode(String richBankCode) {
        this.richBankCode = richBankCode;
    }

    public String getRichCompanyTitle() {
        return richCompanyTitle;
    }

    public void setRichCompanyTitle(String richCompanyTitle) {
        this.richCompanyTitle = richCompanyTitle;
    }

    public String getRichVatSerialNo() {
        return richVatSerialNo;
    }

    public void setRichVatSerialNo(String richVatSerialNo) {
        this.richVatSerialNo = richVatSerialNo;
    }

    public String getRichBankAccount() {
        return richBankAccount;
    }

    public void setRichBankAccount(String richBankAccount) {
        this.richBankAccount = richBankAccount;
    }

    public String getRichBankFullname() {
        return richBankFullname;
    }

    public void setRichBankFullname(String richBankFullname) {
        this.richBankFullname = richBankFullname;
    }

    public Long getCooperatorId() {
        return cooperatorId;
    }

    public void setCooperatorId(Long cooperatorId) {
        this.cooperatorId = cooperatorId;
    }

    public String getCooperatorShortName() {
        return cooperatorShortName;
    }

    public void setCooperatorShortName(String cooperatorShortName) {
        this.cooperatorShortName = cooperatorShortName;
    }

    public String getCooperatorBankCode() {
        return cooperatorBankCode;
    }

    public void setCooperatorBankCode(String cooperatorBankCode) {
        this.cooperatorBankCode = cooperatorBankCode;
    }

    public String getCooperatorFullname() {
        return cooperatorFullname;
    }

    public void setCooperatorFullname(String cooperatorFullname) {
        this.cooperatorFullname = cooperatorFullname;
    }

    public String getCooperatorVatSerialNo() {
        return cooperatorVatSerialNo;
    }

    public void setCooperatorVatSerialNo(String cooperatorVatSerialNo) {
        this.cooperatorVatSerialNo = cooperatorVatSerialNo;
    }

    public String getCooperatorBankAccount() {
        return cooperatorBankAccount;
    }

    public void setCooperatorBankAccount(String cooperatorBankAccount) {
        this.cooperatorBankAccount = cooperatorBankAccount;
    }

    public String getCooperatorBankFullname() {
        return cooperatorBankFullname;
    }

    public void setCooperatorBankFullname(String cooperatorBankFullname) {
        this.cooperatorBankFullname = cooperatorBankFullname;
    }

    public String getRctNoSummary() {
        return rctNoSummary;
    }

    public void setRctNoSummary(String rctNoSummary) {
        this.rctNoSummary = rctNoSummary;
    }

    public String getCooperatorReferNo() {
        return cooperatorReferNo;
    }

    public void setCooperatorReferNo(String cooperatorReferNo) {
        this.cooperatorReferNo = cooperatorReferNo;
    }

    public String getOfficalChargeNameSummary() {
        return officalChargeNameSummary;
    }

    public void setOfficalChargeNameSummary(String officalChargeNameSummary) {
        this.officalChargeNameSummary = officalChargeNameSummary;
    }

    public String getChargeCurrencyCode() {
        return chargeCurrencyCode;
    }

    public void setChargeCurrencyCode(String chargeCurrencyCode) {
        this.chargeCurrencyCode = chargeCurrencyCode;
    }

    public BigDecimal getDnSum() {
        return dnSum;
    }

    public void setDnSum(BigDecimal dnSum) {
        this.dnSum = dnSum;
    }

    public BigDecimal getDnRecieved() {
        return dnRecieved;
    }

    public void setDnRecieved(BigDecimal dnRecieved) {
        this.dnRecieved = dnRecieved;
    }

    public BigDecimal getDnBalance() {
        return dnBalance;
    }

    public void setDnBalance(BigDecimal dnBalance) {
        this.dnBalance = dnBalance;
    }

    public BigDecimal getCnSum() {
        return cnSum;
    }

    public void setCnSum(BigDecimal cnSum) {
        this.cnSum = cnSum;
    }

    public BigDecimal getCnPaid() {
        return cnPaid;
    }

    public void setCnPaid(BigDecimal cnPaid) {
        this.cnPaid = cnPaid;
    }

    public BigDecimal getCnBalance() {
        return cnBalance;
    }

    public void setCnBalance(BigDecimal cnBalance) {
        this.cnBalance = cnBalance;
    }

    public String getChargeClearStatus() {
        return chargeClearStatus;
    }

    public void setChargeClearStatus(String chargeClearStatus) {
        this.chargeClearStatus = chargeClearStatus;
    }

    public Date getExpectedPayDate() {
        return expectedPayDate;
    }

    public void setExpectedPayDate(Date expectedPayDate) {
        this.expectedPayDate = expectedPayDate;
    }

    public Date getApprovedPayDate() {
        return approvedPayDate;
    }

    public void setApprovedPayDate(Date approvedPayDate) {
        this.approvedPayDate = approvedPayDate;
    }

    public Date getActualPayDate() {
        return actualPayDate;
    }

    public void setActualPayDate(Date actualPayDate) {
        this.actualPayDate = actualPayDate;
    }

    public String getSqdBankStatementList() {
        return sqdBankStatementList;
    }

    public void setSqdBankStatementList(String sqdBankStatementList) {
        this.sqdBankStatementList = sqdBankStatementList;
    }

    public String getInvoiceCurrencyCode() {
        return invoiceCurrencyCode;
    }

    public void setInvoiceCurrencyCode(String invoiceCurrencyCode) {
        this.invoiceCurrencyCode = invoiceCurrencyCode;
    }

    public BigDecimal getInvoiceExchangeRate() {
        return invoiceExchangeRate;
    }

    public void setInvoiceExchangeRate(BigDecimal invoiceExchangeRate) {
        this.invoiceExchangeRate = invoiceExchangeRate;
    }

    public BigDecimal getInvoiceNetAmount() {
        return invoiceNetAmount;
    }

    public void setInvoiceNetAmount(BigDecimal invoiceNetAmount) {
        this.invoiceNetAmount = invoiceNetAmount;
    }

    public BigDecimal getVatAmount() {
        return vatAmount;
    }

    public void setVatAmount(BigDecimal vatAmount) {
        this.vatAmount = vatAmount;
    }

    public BigDecimal getInvoiceVatAmount() {
        return invoiceVatAmount;
    }

    public void setInvoiceVatAmount(BigDecimal invoiceVatAmount) {
        this.invoiceVatAmount = invoiceVatAmount;
    }

    public BigDecimal getSaleNetSum() {
        return saleNetSum;
    }

    public void setSaleNetSum(BigDecimal saleNetSum) {
        this.saleNetSum = saleNetSum;
    }

    public BigDecimal getSaleTax() {
        return saleTax;
    }

    public void setSaleTax(BigDecimal saleTax) {
        this.saleTax = saleTax;
    }

    public BigDecimal getSaleTaxTotal() {
        return saleTaxTotal;
    }

    public void setSaleTaxTotal(BigDecimal saleTaxTotal) {
        this.saleTaxTotal = saleTaxTotal;
    }

    public BigDecimal getBuyNetSum() {
        return buyNetSum;
    }

    public void setBuyNetSum(BigDecimal buyNetSum) {
        this.buyNetSum = buyNetSum;
    }

    public BigDecimal getBuyTax() {
        return buyTax;
    }

    public void setBuyTax(BigDecimal buyTax) {
        this.buyTax = buyTax;
    }

    public BigDecimal getBuyTaxTotal() {
        return buyTaxTotal;
    }

    public void setBuyTaxTotal(BigDecimal buyTaxTotal) {
        this.buyTaxTotal = buyTaxTotal;
    }

    public String getTaxClass() {
        return taxClass;
    }

    public void setTaxClass(String taxClass) {
        this.taxClass = taxClass;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getBelongsToMonth() {
        return belongsToMonth;
    }

    public void setBelongsToMonth(String belongsToMonth) {
        this.belongsToMonth = belongsToMonth;
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public String getInvoiceRemark() {
        return invoiceRemark;
    }

    public void setInvoiceRemark(String invoiceRemark) {
        this.invoiceRemark = invoiceRemark;
    }

    public String getMergeInvoice() {
        return mergeInvoice;
    }

    public void setMergeInvoice(String mergeInvoice) {
        this.mergeInvoice = mergeInvoice;
    }

    public String getCooperatorCompanyTitle() {
        return cooperatorCompanyTitle;
    }

    public void setCooperatorCompanyTitle(String cooperatorCompanyTitle) {
        this.cooperatorCompanyTitle = cooperatorCompanyTitle;
    }

    public String getRelatedOrderNo() {
        return relatedOrderNo;
    }

    public void setRelatedOrderNo(String relatedOrderNo) {
        this.relatedOrderNo = relatedOrderNo;
    }

    public String getTaxLocked() {
        return taxLocked;
    }

    public void setTaxLocked(String taxLocked) {
        this.taxLocked = taxLocked;
    }

    public Long getApplyStuffId() {
        return applyStuffId;
    }

    public void setApplyStuffId(Long applyStuffId) {
        this.applyStuffId = applyStuffId;
    }

    public Date getAppliedTime() {
        return appliedTime;
    }

    public void setAppliedTime(Date appliedTime) {
        this.appliedTime = appliedTime;
    }

    public Long getIssuedStuffId() {
        return issuedStuffId;
    }

    public void setIssuedStuffId(Long issuedStuffId) {
        this.issuedStuffId = issuedStuffId;
    }

    public Date getIssuedTime() {
        return issuedTime;
    }

    public void setIssuedTime(Date issuedTime) {
        this.issuedTime = issuedTime;
    }

    public Long getTaxStuffId() {
        return taxStuffId;
    }

    public void setTaxStuffId(Long taxStuffId) {
        this.taxStuffId = taxStuffId;
    }

    public Date getTaxDeclareTime() {
        return taxDeclareTime;
    }

    public void setTaxDeclareTime(Date taxDeclareTime) {
        this.taxDeclareTime = taxDeclareTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("invoiceId", getInvoiceId())
                .append("invoiceCodeNo", getInvoiceCodeNo())
                .append("invoiceOfficalNo", getInvoiceOfficalNo())
                .append("saleBuy", getSaleBuy())
                .append("invoiceBelongsTo", getInvoiceBelongsTo())
                .append("richBankCode", getRichBankCode())
                .append("richCompanyTitle", getRichCompanyTitle())
                .append("richVatSerialNo", getRichVatSerialNo())
                .append("richBankAccount", getRichBankAccount())
                .append("richBankFullname", getRichBankFullname())
                .append("cooperatorId", getCooperatorId())
                .append("cooperatorShortName", getCooperatorShortName())
                .append("cooperatorBankCode", getCooperatorBankCode())
                .append("cooperatorFullname", getCooperatorFullname())
                .append("cooperatorVatSerialNo", getCooperatorVatSerialNo())
                .append("cooperatorBankAccount", getCooperatorBankAccount())
                .append("cooperatorBankFullname", getCooperatorBankFullname())
                .append("rctNoSummary", getRctNoSummary())
                .append("cooperatorReferNo", getCooperatorReferNo())
                .append("officalChargeNameSummary", getOfficalChargeNameSummary())
                .append("chargeCurrencyCode", getChargeCurrencyCode())
                .append("dnSum", getDnSum())
                .append("dnRecieved", getDnRecieved())
                .append("dnBalance", getDnBalance())
                .append("cnSum", getCnSum())
                .append("cnPaid", getCnPaid())
                .append("cnBalance", getCnBalance())
                .append("chargeClearStatus", getChargeClearStatus())
                .append("expectedPayDate", getExpectedPayDate())
                .append("approvedPayDate", getApprovedPayDate())
                .append("actualPayDate", getActualPayDate())
                .append("sqdBankStatementList", getSqdBankStatementList())
                .append("invoiceCurrencyCode", getInvoiceCurrencyCode())
                .append("invoiceExchangeRate", getInvoiceExchangeRate())
                .append("invoiceNetAmount", getInvoiceNetAmount())
                .append("vatAmount", getVatAmount())
                .append("invoiceVatAmount", getInvoiceVatAmount())
                .append("saleNetSum", getSaleNetSum())
                .append("saleTax", getSaleTax())
                .append("saleTaxTotal", getSaleTaxTotal())
                .append("buyNetSum", getBuyNetSum())
                .append("buyTax", getBuyTax())
                .append("buyTaxTotal", getBuyTaxTotal())
                .append("taxClass", getTaxClass())
                .append("invoiceType", getInvoiceType())
                .append("belongsToMonth", getBelongsToMonth())
                .append("invoiceStatus", getInvoiceStatus())
                .append("invoiceRemark", getInvoiceRemark())
                .append("applyStuffId", getApplyStuffId())
                .append("appliedTime", getAppliedTime())
                .append("issuedStuffId", getIssuedStuffId())
                .append("issuedTime", getIssuedTime())
                .append("taxStuffId", getTaxStuffId())
                .append("taxDeclareTime", getTaxDeclareTime())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
