{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\receivablePayable.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\receivablePayable.vue", "mtime": 1718100178915}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "watch", "receivablePayable", "n", "$emit", "methods", "rowIndex", "_ref", "row", "id", "addReceivablePayable", "obj", "showClient", "showSupplier", "showQuotationCharge", "showCostCharge", "showQuotationCurrency", "showCostCurrency", "showQuotationUnit", "showCostUnit", "push", "countProfit", "profit", "Number", "quotationRate", "quotationAmount", "inquiryRate", "costAmount", "toFixed", "exports", "default", "_default"], "sources": ["src/views/system/document/receivablePayable.vue"], "sourcesContent": ["<template>\r\n  <el-col :span=\"21.5\" :style=\"{'display':openReceivablePayable?'':'none'}\">\r\n    <div :class=\"{'inactive':openReceivablePayable==false,'active':openReceivablePayable}\">\r\n      <el-table :data=\"receivablePayable\" :row-class-name=\"rowIndex\" border class=\"pd0\">\r\n        <el-table-column label=\"应收明细\">\r\n          <el-table-column label=\"客户关联单位\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showClient\" @click=\"scope.row.showClient = true\">\r\n                {{ scope.row.client }}\r\n              </div>\r\n              <tree-select v-if=\"$store.state.data.clientList.length>0&&scope.row.showClient\" :flat=\"false\"\r\n                           :multiple=\"false\" :pass=\"scope.row.clientId\" :placeholder=\"'客户'\"\r\n                           :type=\"'client'\" @return=\"scope.row.clientId=$event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"quotationChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCharge\" @click=\"scope.row.showQuotationCharge = true\">\r\n                {{ scope.row.quotationCharge }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showQuotationCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.quotationChargeId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           @return=\"scope.row.quotationChargeId = $event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"策略\" prop=\"quotationStrategyId\" width=\"58\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.quotationStrategyId\" clearable filterable\r\n                         @change=\"scope.row.quotationStrategyId==1?scope.row.quotationRate=0:null\"\r\n              >\r\n                <el-option label=\"已含\" value=\"1\">已含</el-option>\r\n                <el-option label=\"未含\" value=\"0\">未含</el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"quotationCurrencyId\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"scope.row.showQuotationCurrency = true\"\r\n              >\r\n                {{ scope.row.quotationCurrency }}\r\n              </div>\r\n              <tree-select :pass=\"scope.row.quotationCurrencyCode\" :type=\"'currency'\"\r\n                           v-if=\"scope.row.showQuotationCurrency\"\r\n                           @return=\"scope.row.currencyId = $event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"quotationRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.quotationRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"display:flex;width: 100%\" @change=\"countProfit(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"单位\" lign=\"center\" prop=\"quotationUnitId\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showQuotationUnit\" @click=\"scope.row.showQuotationUnit = true\">\r\n                {{ scope.row.quotationUnitCode }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showQuotationUnit\" :pass=\"scope.row.quotationUnitId\" :type=\"'unit'\"\r\n                           @return=\"scope.row.quotationUnitCode=$event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"quotationAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.quotationAmount\" :controls=\"false\" :min=\"1\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               @change=\"countProfit(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"quotationExchangeRate\" width=\"60\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <el-input-number v-model=\"scope.row.quotationExchangeRate\" :controls=\"false\" :min=\"1\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               @change=\"countProfit(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"quotationTaxRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.quotationTaxRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"width: 75%\" @change=\"countProfit(scope.row)\"\r\n              />\r\n              %\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"小计\" prop=\"quotationTotal\" width=\"48\"></el-table-column>\r\n        </el-table-column>\r\n        <el-table-column class-name=\"showBorder\" width=\"20px\"/>\r\n        <el-table-column label=\"应付明细\">\r\n          <el-table-column label=\"供应商关联单位\" width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showSupplier\" @click=\"scope.row.showSupplier = true\">\r\n                {{ scope.row.supplier }}\r\n              </div>\r\n              <tree-select :flat=\"false\" :multiple=\"false\"\r\n                           v-if=\"$store.state.data.supplierList.length>0&&scope.row.showSupplier\"\r\n                           :pass=\"scope.row.supplierId\" :placeholder=\"'供应商'\"\r\n                           :type=\"'supplier'\" @return=\"scope.row.supplierId=$event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"费用\" prop=\"costChargeId\" width=\"80px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostCharge\" @click=\"scope.row.showCostCharge = true\">\r\n                {{ scope.row.costCharge }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showCostCharge\" :dbn=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                           :pass=\"scope.row.inquiryChargeId\" :placeholder=\"'运费'\" :type=\"'charge'\"\r\n                           @return=\"scope.row.inquiryChargeId = $event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"策略\" prop=\"costStrategyId\" width=\"58px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-select v-model=\"scope.row.inquiryStrategyId\" clearable filterable\r\n                         @change=\"scope.row.inquiryStrategyId==1?scope.row.inquiryRate=0:null\"\r\n              >\r\n                <el-option label=\"已含\" value=\"1\">已含</el-option>\r\n                <el-option label=\"未含\" value=\"0\">未含</el-option>\r\n              </el-select>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"货币\" prop=\"costCurrencyId\" width=\"70px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostCurrency\" style=\"width: 69px ;height: 23px\"\r\n                   @click=\"scope.row.showCostCurrency = true\"\r\n              >\r\n                {{ scope.row.inquiryCurrencyCode }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showCostCurrency\" :pass=\"scope.row.inquiryCurrencyCode\"\r\n                           :type=\"'currency'\"\r\n                           @return=\"scope.row.inquiryCurrencyCode = $event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"单价\" prop=\"inquiryRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.inquiryRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"display:flex;width: 100%\" @change=\"countProfit(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"单位\" lign=\"center\" prop=\"costUnitId\" width=\"70px\">\r\n            <template slot-scope=\"scope\">\r\n              <div v-if=\"!scope.row.showCostUnit\" @click=\"scope.row.showCostUnit = true\">\r\n                {{ scope.row.inquiryUnitCode }}\r\n              </div>\r\n              <tree-select v-if=\"scope.row.showCostUnit\" :pass=\"scope.row.inquiryUnitCode\" :type=\"'unit'\"\r\n                           @return=\"scope.row.inquiryUnitCode=$event\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"数量\" prop=\"costAmount\" width=\"48px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.inquiryAmount\" :controls=\"false\" :min=\"1\"\r\n                               placeholder=\"数量\" style=\"display:flex;width: 100%\"\r\n                               @change=\"countProfit(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"costExchangeRate\" width=\"60px\">\r\n            <template slot-scope=\"scope\" style=\"display:flex;\">\r\n              <el-input-number v-model=\"scope.row.inquiryExchangeRate\" :controls=\"false\" :min=\"1\"\r\n                               :precision=\"4\" :step=\"0.0001\" style=\"width: 100%\"\r\n                               @change=\"countProfit(scope.row)\"\r\n              />\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"税率\" prop=\"costTaxRate\" width=\"68px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-input-number v-model=\"scope.row.inquiryTaxRate\" :controls=\"false\" :min=\"0\"\r\n                               style=\"width: 75%\" @change=\"countProfit(scope.row)\"\r\n              />\r\n              %\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"小计\" prop=\"costTotal\" width=\"48\"></el-table-column>\r\n        </el-table-column>\r\n        <el-table-column class-name=\"showBorder\" width=\"20px\"/>\r\n        <el-table-column label=\"辅助决策\">\r\n          <el-table-column align=\"center\" label=\"单项利润\" prop=\"profit\"></el-table-column>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"receivablePayable=receivablePayable.filter(item=>{return item!=scope.row})\"\r\n            >删除\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </div>\r\n    <el-button style=\"padding: 0\" type=\"text\"\r\n               @click=\"addReceivablePayable\"\r\n    >[＋]\r\n    </el-button>\r\n  </el-col>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'receivablePayable',\r\n  props: ['receivablePayable', 'openReceivablePayable'],\r\n  watch: {\r\n    receivablePayable(n) {\r\n      this.$emit('return', n)\r\n    }\r\n  },\r\n  methods: {\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1\r\n    },\r\n    addReceivablePayable() {\r\n      let obj = {\r\n        showClient: true,\r\n        showSupplier: true,\r\n        showQuotationCharge: true,\r\n        showCostCharge: true,\r\n        showQuotationCurrency: true,\r\n        showCostCurrency: true,\r\n        showQuotationUnit: true,\r\n        showCostUnit: true\r\n      }\r\n      this.receivablePayable.push(obj)\r\n    },\r\n    countProfit(row) {\r\n      row.profit = (Number(row.quotationRate) * Number(row.quotationAmount) - Number(row.inquiryRate) * Number(row.costAmount)).toFixed(2)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAiNA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,iBAAA,WAAAA,kBAAAC,CAAA;MACA,KAAAC,KAAA,WAAAD,CAAA;IACA;EACA;EACAE,OAAA;IACA,SACAC,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAF,QAAA,GAAAC,IAAA,CAAAD,QAAA;MACAE,GAAA,CAAAC,EAAA,GAAAH,QAAA;IACA;IACAI,oBAAA,WAAAA,qBAAA;MACA,IAAAC,GAAA;QACAC,UAAA;QACAC,YAAA;QACAC,mBAAA;QACAC,cAAA;QACAC,qBAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,YAAA;MACA;MACA,KAAAjB,iBAAA,CAAAkB,IAAA,CAAAT,GAAA;IACA;IACAU,WAAA,WAAAA,YAAAb,GAAA;MACAA,GAAA,CAAAc,MAAA,IAAAC,MAAA,CAAAf,GAAA,CAAAgB,aAAA,IAAAD,MAAA,CAAAf,GAAA,CAAAiB,eAAA,IAAAF,MAAA,CAAAf,GAAA,CAAAkB,WAAA,IAAAH,MAAA,CAAAf,GAAA,CAAAmB,UAAA,GAAAC,OAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}