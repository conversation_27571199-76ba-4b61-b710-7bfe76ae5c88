{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\TreeNodeDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\TreeNodeDialog.vue", "mtime": 1754876882604}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_index", "require", "components", "inheritAttrs", "props", "data", "id", "formData", "label", "undefined", "value", "rules", "required", "message", "trigger", "dataType", "dataTypeOptions", "computed", "watch", "formDataValue", "val", "isNumberStr", "created", "mounted", "methods", "onOpen", "onClose", "close", "$emit", "handleConfirm", "_this", "$refs", "elForm", "validate", "valid", "parseFloat", "exports", "default", "_default"], "sources": ["src/views/tool/build/TreeNodeDialog.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-dialogDrag v-dialogDragWidth\r\n      v-bind=\"$attrs\"\r\n      @close=\"onClose\"\r\n      @open=\"onOpen\"\r\n      v-on=\"$listeners\"\r\n    >\r\n      <el-row :gutter=\"0\">\r\n        <el-form\r\n          ref=\"elForm\"\r\n          :model=\"formData\"\r\n          :rules=\"rules\"\r\n          label-width=\"100px\"\r\n          size=\"mini\"\r\n        >\r\n          <el-col :span=\"24\">\r\n            <el-form-item\r\n              label=\"选项名\"\r\n              prop=\"label\"\r\n            >\r\n              <el-input\r\n                v-model=\"formData.label\"\r\n                clearable\r\n                placeholder=\"选项名\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item\r\n              label=\"选项值\"\r\n              prop=\"value\"\r\n            >\r\n              <el-input\r\n                v-model=\"formData.value\"\r\n                clearable\r\n                placeholder=\"选项值\"\r\n              >\r\n                <el-select\r\n                  slot=\"append\"\r\n                  v-model=\"dataType\"\r\n                  :style=\"{width: '100px'}\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"(item, index) in dataTypeOptions\"\r\n                    :key=\"index\"\r\n                    :disabled=\"item.disabled\"\r\n                    :label=\"item.label\"\r\n                    :value=\"item.value\"\r\n                  />\r\n                </el-select>\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n      <div slot=\"footer\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"handleConfirm\"\r\n        >\r\n          确定\r\n        </el-button>\r\n        <el-button @click=\"close\">\r\n          取消\r\n        </el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {isNumberStr} from '@/utils/index'\r\n\r\nexport default {\r\n  components: {},\r\n  inheritAttrs: false,\r\n  props: [],\r\n  data() {\r\n    return {\r\n      id: 100,\r\n      formData: {\r\n        label: undefined,\r\n        value: undefined\r\n      },\r\n      rules: {\r\n        label: [\r\n          {\r\n            required: true,\r\n            message: '选项名',\r\n            trigger: 'blur'\r\n          }\r\n        ],\r\n        value: [\r\n          {\r\n            required: true,\r\n            message: '选项值',\r\n            trigger: 'blur'\r\n          }\r\n        ]\r\n      },\r\n      dataType: 'string',\r\n      dataTypeOptions: [\r\n        {\r\n          label: '字符串',\r\n          value: 'string'\r\n        },\r\n        {\r\n          label: '数字',\r\n          value: 'number'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {},\r\n  watch: {\r\n    // eslint-disable-next-line func-names\r\n    'formData.value': function (val) {\r\n      this.dataType = isNumberStr(val) ? 'number' : 'string'\r\n    }\r\n  },\r\n  created() {\r\n  },\r\n  mounted() {\r\n  },\r\n  methods: {\r\n    onOpen() {\r\n      this.formData = {\r\n        label: undefined,\r\n        value: undefined\r\n      }\r\n    },\r\n    onClose() {\r\n    },\r\n    close() {\r\n      this.$emit('update:visible', false)\r\n    },\r\n    handleConfirm() {\r\n      this.$refs.elForm.validate(valid => {\r\n        if (!valid) return\r\n        if (this.dataType == 'number') {\r\n          this.formData.value = parseFloat(this.formData.value)\r\n        }\r\n        this.formData.id = this.id++\r\n        this.$emit('commit', this.formData)\r\n        this.close()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;AA0EA,IAAAA,MAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,UAAA;EACAC,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,EAAA;MACAC,QAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD;MACA;MACAE,KAAA;QACAH,KAAA,GACA;UACAI,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAJ,KAAA,GACA;UACAE,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,QAAA;MACAC,eAAA,GACA;QACAR,KAAA;QACAE,KAAA;MACA,GACA;QACAF,KAAA;QACAE,KAAA;MACA;IAEA;EACA;EACAO,QAAA;EACAC,KAAA;IACA;IACA,2BAAAC,cAAAC,GAAA;MACA,KAAAL,QAAA,OAAAM,kBAAA,EAAAD,GAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA,WAAAA,QAAA,GACA;EACAC,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAlB,QAAA;QACAC,KAAA,EAAAC,SAAA;QACAC,KAAA,EAAAD;MACA;IACA;IACAiB,OAAA,WAAAA,QAAA,GACA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,KAAA;IACA;IACAC,aAAA,WAAAA,cAAA;MAAA,IAAAC,KAAA;MACA,KAAAC,KAAA,CAAAC,MAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,KAAAA,KAAA;QACA,IAAAJ,KAAA,CAAAf,QAAA;UACAe,KAAA,CAAAvB,QAAA,CAAAG,KAAA,GAAAyB,UAAA,CAAAL,KAAA,CAAAvB,QAAA,CAAAG,KAAA;QACA;QACAoB,KAAA,CAAAvB,QAAA,CAAAD,EAAA,GAAAwB,KAAA,CAAAxB,EAAA;QACAwB,KAAA,CAAAF,KAAA,WAAAE,KAAA,CAAAvB,QAAA;QACAuB,KAAA,CAAAH,KAAA;MACA;IACA;EACA;AACA;AAAAS,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}