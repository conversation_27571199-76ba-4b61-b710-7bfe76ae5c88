{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\characteristics\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\characteristics\\index.vue", "mtime": 1754876882575}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRDaGFyYWN0ZXJpc3RpY3MsDQogIGRlbENoYXJhY3RlcmlzdGljcywNCiAgZ2V0Q2hhcmFjdGVyaXN0aWNzLA0KICBsaXN0Q2hhcmFjdGVyaXN0aWNzLA0KICB1cGRhdGVDaGFyYWN0ZXJpc3RpY3MNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL2NoYXJhY3RlcmlzdGljcyI7DQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCI7DQppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QubWluLmNzcyI7DQppbXBvcnQgcGlueWluIGZyb20gImpzLXBpbnlpbiI7DQppbXBvcnQgc3RvcmUgZnJvbSAiQC9zdG9yZSI7DQppbXBvcnQge3BhcnNlVGltZX0gZnJvbSAiQC91dGlscy9yaWNoIjsNCmltcG9ydCB7c2VsZWN0TGlzdFVzZXJ9IGZyb20gIkAvYXBpL3N5c3RlbS91c2VyIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiQ2hhcmFjdGVyaXN0aWNzIiwNCiAgZGljdHM6IFsnc3lzX25vcm1hbF9kaXNhYmxlJywgJ3N5c195ZXNfbm8nXSwNCiAgY29tcG9uZW50czoge1RyZWVzZWxlY3R9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzaG93TGVmdDogMywNCiAgICAgIHNob3dSaWdodDogMjEsDQogICAgICB2YWxpZFRpbWU6IFtdLA0KICAgICAgY2Fycmllckxpc3Q6IFtdLA0KICAgICAgY2FycmllcklkczogW10sDQogICAgICBxdWVyeUNhcnJpZXJJZHM6IFtdLA0KICAgICAgdGVtQ2Fycmllckxpc3Q6IFtdLA0KICAgICAgbG9jYXRpb25PcHRpb25zOiBbXSwNCiAgICAgIHVzZXJMaXN0OiBbXSwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDnianmtYHms6jmhI/kuovpobnooajmoLzmlbDmja4NCiAgICAgIGNoYXJhY3RlcmlzdGljc0xpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBhY2N1cmF0ZTogMSwNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICBpbmZvSWQ6IG51bGwsDQogICAgICAgIHNob3dNb2RlOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICBjYXJnb1R5cGVJZHM6IFtdLA0KICAgICAgICBsb2NhdGlvbkRlcGFydHVyZUlkczogW10sDQogICAgICAgIGxpbmVEZXBhcnR1cmVJZHM6IFtdLA0KICAgICAgICBsb2NhdGlvbkRlc3RpbmF0aW9uSWRzOiBbXSwNCiAgICAgICAgbGluZURlc3RpbmF0aW9uSWRzOiBbXSwNCiAgICAgICAgY2FycmllcklkczogW10sDQogICAgICAgIGNvbXBhbnlJZDogbnVsbCwNCiAgICAgICAgZXNzZW50aWFsRGV0YWlsOiBudWxsLA0KICAgICAgICBpc1ZhbGlkOiAiWSIsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGNhcmdvVHlwZUlkczoge3JlcXVpcmVkOiB0cnVlLCB0cmlnZ2VyOiAiYmx1ciJ9DQogICAgICB9DQogICAgfTsNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93U2VhcmNoKG4pIHsNCiAgICAgIGlmIChuID09IHRydWUpIHsNCiAgICAgICAgdGhpcy5zaG93UmlnaHQgPSAyMQ0KICAgICAgICB0aGlzLnNob3dMZWZ0ID0gMw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5zaG93UmlnaHQgPSAyNA0KICAgICAgICB0aGlzLnNob3dMZWZ0ID0gMA0KICAgICAgfQ0KICAgIH0sDQogICAgJ2Zvcm0uc2VydmljZVR5cGVJZCcobikgew0KICAgICAgdGhpcy5sb2FkQ2FycmllcigpDQogICAgICBsZXQgbGlzdCA9IFtdDQogICAgICBpZiAodGhpcy5jYXJyaWVyTGlzdCAhPSB1bmRlZmluZWQgJiYgbiAhPSBudWxsKSB7DQogICAgICAgIHRoaXMudGVtQ2Fycmllckxpc3QgPSB0aGlzLmNhcnJpZXJMaXN0DQogICAgICAgIC8qIGZvciAoY29uc3QgdiBvZiB0aGlzLmNhcnJpZXJMaXN0KSB7DQogICAgICAgICAgaWYgKHYuY2hpbGRyZW4gIT0gdW5kZWZpbmVkICYmIHYuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgZm9yIChjb25zdCBhIG9mIHYuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgaWYgKGEuY2hpbGRyZW4gIT0gdW5kZWZpbmVkICYmIGEuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgIGZvciAoY29uc3QgYiBvZiBhLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLmNhcnJpZXJJZHMgIT0gbnVsbCAmJiB0aGlzLmZvcm0uY2Fycmllcklkcy5pbmNsdWRlcyhiLmNhcnJpZXIuY2FycmllcklkKSAmJiAhdGhpcy5jYXJyaWVySWRzLmluY2x1ZGVzKGIuc2VydmljZVR5cGVJZCkpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYXJyaWVySWRzLnB1c2goYi5zZXJ2aWNlVHlwZUlkKQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSAqLw0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuY2Fycmllckxpc3QgIT0gdW5kZWZpbmVkICYmIG4gIT0gbnVsbCkgew0KICAgICAgICBmb3IgKGNvbnN0IGMgb2YgdGhpcy5jYXJyaWVyTGlzdCkgew0KICAgICAgICAgIGlmIChuICE9IG51bGwgJiYgbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIGlmIChjLnNlcnZpY2VUeXBlSWQgPT0gbikgew0KICAgICAgICAgICAgICBsaXN0LnB1c2goYykNCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChjLmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiBjLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBjaCBvZiBjLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgaWYgKGNoLnNlcnZpY2VUeXBlSWQgPT0gbikgew0KICAgICAgICAgICAgICAgICAgbGlzdC5wdXNoKGNoKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnRlbUNhcnJpZXJMaXN0ID0gbGlzdA0KICAgICAgICBpZiAodGhpcy50ZW1DYXJyaWVyTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLyogZm9yIChjb25zdCB2IG9mIHRoaXMudGVtQ2Fycmllckxpc3QpIHsNCiAgICAgICAgICAgIGlmICh2LmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiB2LmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBhIG9mIHYuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLmNhcnJpZXJJZHMgIT0gbnVsbCAmJiB0aGlzLmZvcm0uY2Fycmllcklkcy5pbmNsdWRlcyhhLmNhcnJpZXIuY2FycmllcklkKSAmJiAhdGhpcy5jYXJyaWVySWRzLmluY2x1ZGVzKGEuc2VydmljZVR5cGVJZCkpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuY2Fycmllcklkcy5wdXNoKGEuc2VydmljZVR5cGVJZCkNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9ICovDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogICAgdGhpcy5sb2FkQ2FycmllcigpOw0KICAgIHNlbGVjdExpc3RVc2VyKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLnVzZXJMaXN0ID0gcmVzcG9uc2UuZGF0YQ0KICAgIH0pDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBjaGFuZ2VUaW1lKHZhbCkgew0KICAgICAgaWYgKHZhbCA9PSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5mb3JtLnZhbGlkRnJvbSA9IG51bGwNCiAgICAgICAgdGhpcy5mb3JtLnZhbGlkVG8gPSBudWxsDQogICAgICB9DQogICAgICB0aGlzLmZvcm0udmFsaWRGcm9tID0gdmFsWzBdDQogICAgICB0aGlzLmZvcm0udmFsaWRUbyA9IHZhbFsxXQ0KICAgIH0sDQogICAgbG9hZENhcnJpZXIoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zZXJ2aWNlVHlwZUNhcnJpZXJzLmxlbmd0aCA9PSAwIHx8IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEucmVkaXNMaXN0LnNlcnZpY2VUeXBlQ2FycmllcnMpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goJ2dldFNlcnZpY2VUeXBlQ2FycmllcnNMaXN0JykudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5jYXJyaWVyTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVDYXJyaWVycw0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jYXJyaWVyTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVDYXJyaWVycw0KICAgICAgfQ0KICAgIH0sDQogICAgY2Fycmllck5vcm1hbGl6ZXIobm9kZSkgew0KICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgIW5vZGUuY2hpbGRyZW4ubGVuZ3RoKSB7DQogICAgICAgIGRlbGV0ZSBub2RlLmNoaWxkcmVuOw0KICAgICAgfQ0KICAgICAgbGV0IGwNCiAgICAgIGlmICghbm9kZS5jYXJyaWVyIHx8IChub2RlLmNhcnJpZXIuY2FycmllckxvY2FsTmFtZSA9PSBudWxsICYmIG5vZGUuY2Fycmllci5jYXJyaWVyRW5OYW1lID09IG51bGwpKSB7DQogICAgICAgIGwgPSBub2RlLnNlcnZpY2VMb2NhbE5hbWUgKyAnICcgKyBub2RlLnNlcnZpY2VFbk5hbWUgKyAnLCcgKyBwaW55aW4uZ2V0RnVsbENoYXJzKG5vZGUuc2VydmljZUxvY2FsTmFtZSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGwgPSAobm9kZS5jYXJyaWVyLmNhcnJpZXJJbnRsQ29kZSAhPSBudWxsID8gbm9kZS5jYXJyaWVyLmNhcnJpZXJJbnRsQ29kZSA6ICcnKSArICcgJyArIChub2RlLmNhcnJpZXIuY2FycmllckVuTmFtZSAhPSBudWxsID8gbm9kZS5jYXJyaWVyLmNhcnJpZXJFbk5hbWUgOiAnJykgKyAnICcgKyAobm9kZS5jYXJyaWVyLmNhcnJpZXJMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY2Fycmllci5jYXJyaWVyTG9jYWxOYW1lIDogJycpICsgJywnICsgcGlueWluLmdldEZ1bGxDaGFycygobm9kZS5jYXJyaWVyLmNhcnJpZXJMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY2Fycmllci5jYXJyaWVyTG9jYWxOYW1lIDogJycpKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6IG5vZGUuc2VydmljZVR5cGVJZCwNCiAgICAgICAgbGFiZWw6IGwsDQogICAgICAgIGNoaWxkcmVuOiBub2RlLmNoaWxkcmVuLA0KICAgICAgfQ0KICAgIH0sDQogICAgdGFibGVSb3dDbGFzc05hbWUoe3Jvd30pIHsNCiAgICAgIGxldCBkYXRlID0gcGFyc2VUaW1lKG5ldyBEYXRlKCksICJ7eX0te219LXtkfSIpDQogICAgICBsZXQgdmFsaWRGcm9tID0gcGFyc2VUaW1lKHJvdy52YWxpZEZyb20sICJ7eX0te219LXtkfSIpDQogICAgICBsZXQgdmFsaWRUbyA9IHBhcnNlVGltZShyb3cudmFsaWRUbywgInt5fS17bX0te2R9IikNCiAgICAgIGlmICh2YWxpZEZyb20gPCBkYXRlIDwgdmFsaWRUbykNCiAgICAgICAgcmV0dXJuICcnDQogICAgICBpZiAodmFsaWRUbyA8IGRhdGUpDQogICAgICAgIHJldHVybiAndmFsaWQtcm93JzsNCiAgICAgIGlmICh2YWxpZEZyb20gPiBkYXRlKQ0KICAgICAgICByZXR1cm4gJ3ZhbGlkLWJlZm9yZSc7DQogICAgICByZXR1cm4gJycNCiAgICB9LA0KICAgIC8qKiDmn6Xor6LnianmtYHms6jmhI/kuovpobnliJfooaggKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZTsNCiAgICAgIGxpc3RDaGFyYWN0ZXJpc3RpY3ModGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2hhcmFjdGVyaXN0aWNzTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbDsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGNoYXJhY3RlcmlzdGljc0lkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiB0aGlzLmZvcm0uc2VydmljZVR5cGVJZCAhPSB1bmRlZmluZWQgPyB0aGlzLmZvcm0uc2VydmljZVR5cGVJZCA6IDIxLA0KICAgICAgICBpbmZvSWQ6IG51bGwsDQogICAgICAgIHNob3dNb2RlOiAiMCIsDQogICAgICAgIGVzc2VudGlhbERldGFpbDogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogcGFyc2VUaW1lKG5ldyBEYXRlKCkpLA0KICAgICAgICB2YWxpZEZyb206IG51bGwsDQogICAgICAgIHZhbGlkVG86IG51bGwsDQogICAgICAgIGlzVmFsaWQ6ICJZIiwNCiAgICAgICAgc3RhdHVzOiAiMCIsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICBkZWxldGVCeTogbnVsbCwNCiAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgZGVsZXRlU3RhdHVzOiAiMCIsDQogICAgICAgIGNhcmdvVHlwZUlkczogdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyAhPSB1bmRlZmluZWQgPyB0aGlzLmZvcm0uY2FyZ29UeXBlSWRzIDogWy0xXSwNCiAgICAgICAgbG9jYXRpb25EZXBhcnR1cmVJZHM6IHRoaXMuZm9ybS5sb2NhdGlvbkRlcGFydHVyZUlkcyAhPSB1bmRlZmluZWQgPyB0aGlzLmZvcm0ubG9jYXRpb25EZXBhcnR1cmVJZHMgOiBbMTM3MTZdLA0KICAgICAgICBsaW5lRGVwYXJ0dXJlSWRzOiBbXSwNCiAgICAgICAgbG9jYXRpb25EZXN0aW5hdGlvbklkczogW10sDQogICAgICAgIGxpbmVEZXN0aW5hdGlvbklkczogW10sDQogICAgICAgIGNhcnJpZXJJZHM6IFtdLA0KICAgICAgfTsNCiAgICAgIHRoaXMudmFsaWRUaW1lID0gW10NCiAgICAgIHRoaXMuY2FycmllcklkcyA9IFtdDQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmNoYXJhY3RlcmlzdGljc0lkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOeJqea1geazqOaEj+S6i+mhuSI7DQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy4kcmVmcy5sb2NhdGlvbi5yZW1vdGVNZXRob2QoIuW5v+S4nCIpDQogICAgICB9LCAwKQ0KICAgICAgLy8gbGV0IGRhdGUgPSBuZXcgRGF0ZSgpDQogICAgICAvLyB0aGlzLnZhbGlkVGltZS5wdXNoKGRhdGUpDQogICAgICAvLyB0aGlzLnZhbGlkVGltZS5wdXNoKG5ldyBEYXRlKG5ldyBEYXRlKGRhdGUuZ2V0RnVsbFllYXIoKSwgZGF0ZS5nZXRNb250aCgpICsgMSwgMSkgLSAxMDAwICogNjAgKiA2MCAqIDI0KSkNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlDQogICAgICBjb25zdCBjaGFyYWN0ZXJpc3RpY3NJZCA9IHJvdy5jaGFyYWN0ZXJpc3RpY3NJZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0Q2hhcmFjdGVyaXN0aWNzKGNoYXJhY3RlcmlzdGljc0lkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyA9IHJlc3BvbnNlLmNhcmdvVHlwZUlkczsNCiAgICAgICAgdGhpcy5mb3JtLmxpbmVEZXBhcnR1cmVJZHMgPSByZXNwb25zZS5saW5lRGVwYXJ0dXJlSWRzOw0KICAgICAgICB0aGlzLmZvcm0ubG9jYXRpb25EZXBhcnR1cmVJZHMgPSByZXNwb25zZS5sb2NhdGlvbkRlcGFydHVyZUlkczsNCiAgICAgICAgdGhpcy5mb3JtLmxpbmVEZXN0aW5hdGlvbklkcyA9IHJlc3BvbnNlLmxpbmVEZXN0aW5hdGlvbklkczsNCiAgICAgICAgdGhpcy5mb3JtLmxvY2F0aW9uRGVzdGluYXRpb25JZHMgPSByZXNwb25zZS5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzOw0KICAgICAgICB0aGlzLmZvcm0uY2FycmllcklkcyA9IHJlc3BvbnNlLmNhcnJpZXJJZHMNCg0KICAgICAgICB0aGlzLmxvYWRDYXJyaWVyKCkNCiAgICAgICAgbGV0IGxpc3QgPSBbXQ0KICAgICAgICBpZiAodGhpcy5jYXJyaWVyTGlzdCAhPSB1bmRlZmluZWQgJiYgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWQgIT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMudGVtQ2Fycmllckxpc3QgPSB0aGlzLmNhcnJpZXJMaXN0DQogICAgICAgICAgZm9yIChjb25zdCB2IG9mIHRoaXMuY2Fycmllckxpc3QpIHsNCiAgICAgICAgICAgIGlmICh2LmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiB2LmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBhIG9mIHYuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuY2FycmllcklkcyAhPSBudWxsICYmIHJlc3BvbnNlLmNhcnJpZXJJZHMuaW5jbHVkZXMoYS5jYXJyaWVyLmNhcnJpZXJJZCkgJiYgIXJlc3BvbnNlLmNhcnJpZXJJZHMuaW5jbHVkZXMoYS5zZXJ2aWNlVHlwZUlkKSkgew0KICAgICAgICAgICAgICAgICAgdGhpcy5jYXJyaWVySWRzLnB1c2goYS5zZXJ2aWNlVHlwZUlkKQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5jYXJyaWVyTGlzdCAhPSB1bmRlZmluZWQgJiYgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWQgIT0gbnVsbCkgew0KICAgICAgICAgIGZvciAoY29uc3QgYyBvZiB0aGlzLmNhcnJpZXJMaXN0KSB7DQogICAgICAgICAgICBpZiAodGhpcy5mb3JtLnNlcnZpY2VUeXBlSWQgIT0gbnVsbCAmJiB0aGlzLmZvcm0uc2VydmljZVR5cGVJZCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgaWYgKGMuc2VydmljZVR5cGVJZCA9PSB0aGlzLmZvcm0uc2VydmljZVR5cGVJZCkgew0KICAgICAgICAgICAgICAgIGxpc3QucHVzaChjKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGlmIChjLmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiBjLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGNoIG9mIGMuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICAgIGlmIChjaC5zZXJ2aWNlVHlwZUlkID09IHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkKSB7DQogICAgICAgICAgICAgICAgICAgIGxpc3QucHVzaChjaCkNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgICAgdGhpcy50ZW1DYXJyaWVyTGlzdCA9IGxpc3QNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLnNlcnZpY2VUeXBlSWQgPT0gbnVsbCAmJiB0aGlzLnRlbUNhcnJpZXJMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGZvciAoY29uc3QgdiBvZiB0aGlzLnRlbUNhcnJpZXJMaXN0KSB7DQogICAgICAgICAgICAgIGlmICh2LmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiB2LmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGEgb2Ygdi5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLmNhcnJpZXJJZHMgIT0gbnVsbCAmJiByZXNwb25zZS5jYXJyaWVySWRzLmluY2x1ZGVzKGEuY2Fycmllci5jYXJyaWVySWQpICYmICFyZXNwb25zZS5jYXJyaWVySWRzLmluY2x1ZGVzKGEuc2VydmljZVR5cGVJZCkpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5jYXJyaWVySWRzLnB1c2goYS5zZXJ2aWNlVHlwZUlkKQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS52YWxpZEZyb20pDQogICAgICAgICAgdGhpcy52YWxpZFRpbWUucHVzaChyZXNwb25zZS5kYXRhLnZhbGlkRnJvbSkNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEudmFsaWRUbykNCiAgICAgICAgICB0aGlzLnZhbGlkVGltZS5wdXNoKHJlc3BvbnNlLmRhdGEudmFsaWRUbykNCiAgICAgICAgdGhpcy5sb2NhdGlvbk9wdGlvbnMgPSByZXNwb25zZS5sb2NhdGlvbk9wdGlvbnMNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnnianmtYHms6jmhI/kuovpobkiOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uY2hhcmFjdGVyaXN0aWNzSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlQ2hhcmFjdGVyaXN0aWNzKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkQ2hhcmFjdGVyaXN0aWNzKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGNoYXJhY3RlcmlzdGljc0lkcyA9IHJvdy5jaGFyYWN0ZXJpc3RpY3NJZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOeJqea1geazqOaEj+S6i+mhuee8luWPt+S4uiInICsgY2hhcmFjdGVyaXN0aWNzSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nLCAn5o+Q56S6Jywge2N1c3RvbUNsYXNzOiAnbW9kYWwtY29uZmlybSd9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbENoYXJhY3RlcmlzdGljcyhjaGFyYWN0ZXJpc3RpY3NJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ3N5c3RlbS9jaGFyYWN0ZXJpc3RpY3MvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgY2hhcmFjdGVyaXN0aWNzXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgcXVlcnlTZXJ2aWNlVHlwZUlkKHZhbCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5zZXJ2aWNlVHlwZUlkID0gdmFsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIHF1ZXJ5Q2FyZ29UeXBlSWRzKHZhbCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jYXJnb1R5cGVJZHMgPSB2YWwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgcXVlcnlMb2NhdGlvbkRlcGFydHVyZUlkcyh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMubG9jYXRpb25EZXBhcnR1cmVJZHMgPSB2YWwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgcXVlcnlMaW5lRGVwYXJ0dXJlSWRzKHZhbCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5saW5lRGVwYXJ0dXJlSWRzID0gdmFsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIHF1ZXJ5TG9jYXRpb25EZXN0aW5hdGlvbklkcyh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMubG9jYXRpb25EZXN0aW5hdGlvbklkcyA9IHZhbA0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICBxdWVyeUxpbmVEZXN0aW5hdGlvbklkcyh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMubGluZURlc3RpbmF0aW9uSWRzID0gdmFsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIHF1ZXJ5SW5mb0lkKHZhbCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pbmZvSWQgPSB2YWwNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgZ2V0U2VydmljZVR5cGVJZCh2YWwpIHsNCiAgICAgIGlmICh2YWwgPT0gdW5kZWZpbmVkKSB7DQogICAgICAgIHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkID0gbnVsbA0KICAgICAgICB0aGlzLmNhcnJpZXJJZHMgPSBudWxsDQogICAgICAgIHRoaXMuZm9ybS5jYXJyaWVySWRzID0gbnVsbA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWQgPSB2YWwNCiAgICAgIH0NCiAgICB9LA0KICAgIGdldEN1cnJlbmN5SWQodmFsKSB7DQogICAgICBpZiAodmFsID09IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLmZvcm0uY3VycmVuY3lJZCA9IG51bGwNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuZm9ybS5jdXJyZW5jeUlkID0gdmFsDQogICAgICB9DQogICAgfSwNCiAgICBnZXRVbml0SWQodmFsKSB7DQogICAgICBpZiAodmFsID09IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLmZvcm0udW5pdElkID0gbnVsbA0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5mb3JtLnVuaXRJZCA9IHZhbA0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0Q2FyZ29UeXBlSWRzKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyA9IHZhbA0KICAgIH0sDQogICAgZ2V0Q29tcGFueUlkKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLmNvbXBhbnlJZCA9IHZhbA0KICAgIH0sDQogICAgZ2V0TG9jYXRpb25EZXBhcnR1cmVJZHModmFsKSB7DQogICAgICB0aGlzLmZvcm0ubG9jYXRpb25EZXBhcnR1cmVJZHMgPSB2YWwNCiAgICB9LA0KICAgIGdldExpbmVEZXBhcnR1cmVJZHModmFsKSB7DQogICAgICB0aGlzLmZvcm0ubGluZURlcGFydHVyZUlkcyA9IHZhbA0KICAgIH0sDQogICAgZ2V0TG9jYXRpb25EZXN0aW5hdGlvbklkcyh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5sb2NhdGlvbkRlc3RpbmF0aW9uSWRzID0gdmFsDQogICAgfSwNCiAgICBnZXRMaW5lRGVzdGluYXRpb25JZHModmFsKSB7DQogICAgICB0aGlzLmZvcm0ubGluZURlc3RpbmF0aW9uSWRzID0gdmFsDQogICAgfSwNCiAgICBnZXRJbmZvSWQodmFsKSB7DQogICAgICB0aGlzLmZvcm0uaW5mb0lkID0gdmFsDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3RDYXJyaWVySWRzKG5vZGUpIHsNCiAgICAgIHRoaXMuZm9ybS5jYXJyaWVySWRzLnB1c2gobm9kZS5jYXJyaWVyLmNhcnJpZXJJZCkNCiAgICB9LA0KICAgIGhhbmRsZVNlbGVjdFF1ZXJ5Q2Fycmllcklkcyhub2RlKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmNhcnJpZXJJZHMucHVzaChub2RlLmNhcnJpZXIuY2FycmllcklkKQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICBoYW5kbGVEZXNlbGVjdENhcnJpZXJJZHMobm9kZSkgew0KICAgICAgdGhpcy5mb3JtLmNhcnJpZXJJZHMgPSB0aGlzLmZvcm0uY2Fycmllcklkcy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgcmV0dXJuIGl0ZW0gIT0gbm9kZS5jYXJyaWVyLmNhcnJpZXJJZA0KICAgICAgfSkNCiAgICB9LA0KICAgIGhhbmRsZURlc2VsZWN0UXVlcnlDYXJyaWVySWRzKG5vZGUpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY2FycmllcklkcyA9IHRoaXMucXVlcnlQYXJhbXMuY2Fycmllcklkcy5maWx0ZXIoKGl0ZW0pID0+IHsNCiAgICAgICAgcmV0dXJuIGl0ZW0gIT0gbm9kZS5jYXJyaWVyLmNhcnJpZXJJZA0KICAgICAgfSkNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgZGVzZWxlY3RBbGxDYXJycmllcklkcyh2YWx1ZSkgew0KICAgICAgaWYgKHZhbHVlLmxlbmd0aCA9PSAwKSB7DQogICAgICAgIHRoaXMuZm9ybS5jYXJyaWVySWRzID0gW10NCiAgICAgIH0NCiAgICB9LA0KICAgIGRlc2VsZWN0QWxsUXVlcnlDYXJyaWVySWRzKHZhbHVlKSB7DQogICAgICBpZiAodmFsdWUubGVuZ3RoID09IDApIHsNCiAgICAgICAgdGhpcy5xdWVyeVBhcmFtcy5jYXJyaWVySWRzID0gW10NCiAgICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgICB9DQogICAgfSwNCiAgICBxdWVyeUNvbXBhbnlJZCh2YWwpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMuY29tcGFueUlkID0gdmFsDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgb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file": "index.vue", "sourceRoot": "src/views/system/characteristics", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-if=\"showSearch\">\r\n          <el-form-item label=\"筛选\">\r\n            <el-select v-model=\"queryParams.accurate\" style=\"width: 100%\" @change=\"handleQuery\">\r\n              <el-option :value=\"1\" label=\"相关所有\">相关所有</el-option>\r\n              <el-option :value=\"2\" label=\"向上所属\">向上所属</el-option>\r\n              <el-option :value=\"3\" label=\"向下包含\">向下包含</el-option>\r\n              <el-option :value=\"4\" label=\"精确匹配\">精确匹配</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"服务\" prop=\"serviceTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"queryParams.serviceTypeId\"\r\n                         :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                         style=\"width: 100%\" @return=\"queryServiceTypeId\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"货物\" prop=\"cargoTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.cargoTypeIds\" :placeholder=\"'货物特征'\"\r\n                         :type=\"'cargoType'\" style=\"width: 100%\" @return=\"queryCargoTypeIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"承运\" prop=\"carrierIds\">\r\n            <treeselect v-model=\"queryCarrierIds\" :disable-fuzzy-matching=\"true\" :disable-branch-nodes=\"true\"\r\n                        :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                        :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                        placeholder=\"承运人\" style=\"width: 100%\" @input=\"deselectAllQueryCarrierIds\"\r\n                         @open=\"loadCarrier\"\r\n                        @deselect=\"handleDeselectQueryCarrierIds\" @select=\"handleSelectQueryCarrierIds\">\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{ node.raw.carrier.carrierIntlCode }}\r\n                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : '' }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\">\r\n                {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"订舱\" prop=\"companyId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"queryParams.companyId\" style=\"width: 100%\"\r\n                         :placeholder=\"'订舱口'\" :type=\"'supplier'\" @return=\"queryCompanyId\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"locationDepartureIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDepartureIds\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDepartureIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"目的\" prop=\"locationDestinationIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDestinationIds\"\r\n                             :en=\"true\" :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDestinationIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.lineDestinationIds\"\r\n                         :placeholder=\"'目的航线'\"\r\n                         :type=\"'line'\" style=\"width: 100%\" @return=\"queryLineDestinationIds\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"要素\" prop=\"infoId\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"queryParams.infoId\" :type=\"'commonInfo'\"\r\n                         @return=\"queryInfoId\" style=\"width: 100%\" :placeholder=\"'物流要素'\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"详细\" prop=\"essentialDetail\">\r\n            <el-input v-model=\"queryParams.essentialDetail\" placeholder=\"要素详细\" style=\"width: 100%\"\r\n                      @focusout.native=\"handleQuery\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"有效\" prop=\"isValid\">\r\n            <el-select v-model=\"queryParams.isValid\" placeholder=\"是否有效\" style=\"width: 100%\" @change=\"handleQuery\">\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_yes_no\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"\r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"模式\" prop=\"showMode\">\r\n            <el-select v-model=\"queryParams.showMode\" clearable placeholder=\"显示模式\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\">\r\n              <el-option value=\"0\" label=\"小白模式\"/>\r\n              <el-option value=\"1\" label=\"专业模式\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"员工\" prop=\"updateBy\">\r\n            <el-select v-model=\"queryParams.updateBy\" filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\" clearable>\r\n              <el-option\r\n                v-for=\"staff in userList\"\r\n                :key=\"staff.staffId\"\r\n                :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                :value=\"staff.staffId\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"时间\" prop=\"updateTime\">\r\n            <el-date-picker v-model=\"queryParams.updateTime\"\r\n                            clearable\r\n                            placeholder=\"录入起始时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"date\"\r\n                            @change=\"handleQuery\"\r\n                            value-format=\"yyyy-MM-dd\">\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:characteristics:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['system:characteristics:edit']\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:characteristics:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:characteristics:export']\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"characteristicsList\" @selection-change=\"handleSelectionChange\"\r\n                  :row-class-name=\"tableRowClassName\" border>\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"center\" label=\"内部序号\" prop=\"richNo\" show-tooltip-when-overflow width=\"80\"/>\r\n          <el-table-column align=\"left\" label=\"服务类型\" prop=\"serviceType\" width=\"58px\"/>\r\n          <el-table-column key=\"cargoType\" align=\"left\" label=\"货物特征\" prop=\"cargoType\" width=\"68px\"\r\n                           show-tooltip-when-overflow>\r\n          </el-table-column>\r\n          <el-table-column key=\"carrier\" align=\"left\" label=\"承运人\" width=\"58px\"\r\n                           show-tooltip-when-overflow>\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{ scope.row.carrier == null ? '全部' : scope.row.carrier }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"订舱口\" prop=\"company\" show-tooltip-when-overflow width=\"58px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{ scope.row.companyId == null ? '全部' : scope.row.company }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"启运区域\" show-tooltip-when-overflow width=\"69px\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{\r\n                  (scope.row.locationDeparture != null ? scope.row.locationDeparture : '') + (scope.row.locationDeparture != null && scope.row.lineDeparture != null ? ',' : '') + (scope.row.lineDeparture != null ? scope.row.lineDeparture : '')\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"目的区域\" show-tooltip-when-overflow width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"padding: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                {{\r\n                  (scope.row.locationDestinationEn != null ? scope.row.locationDestinationEn : '') + (scope.row.lineDestination != null && scope.row.locationDestinationEn != null ? ',' : '') + (scope.row.lineDestination != null ? scope.row.lineDestination : '')\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"物流要素\" prop=\"info\" width=\"68\"/>\r\n          <el-table-column align=\"left\" label=\"要素详细\" prop=\"essentialDetail\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tooltip v-if=\"scope.row.essentialDetail!=null&&scope.row.essentialDetail.length>13\" placement=\"top\">\r\n                <div slot=\"content\">\r\n                  <div v-for=\"data in scope.row.essentialDetail.split('\\n')\">\r\n                    <h6 style=\"margin: 0;\">\r\n                      {{ data }}\r\n                    </h6>\r\n                  </div>\r\n                </div>\r\n                <div>\r\n                  <h6 style=\"margin: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n                    {{ scope.row.essentialDetail }}\r\n                  </h6>\r\n                </div>\r\n              </el-tooltip>\r\n              <div v-else>\r\n                <h6 style=\"margin: 0;\">\r\n                  {{ scope.row.essentialDetail }}\r\n                </h6>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"有效时间\" prop=\"validTime\" width=\"130\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.validFrom, '{y}.{m}.{d}') }}-{{\r\n                  parseTime(scope.row.validTo, '{y}.{m}.{d}')\r\n                }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"有效\" prop=\"isValid\" width=\"38\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.isValid\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"显示模式\" align=\"center\" prop=\"showMode\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-tag v-if=\"scope.row.showMode == '0'\" type=\"primary\">小白模式</el-tag>\r\n              <el-tag v-if=\"scope.row.showMode == '1'\" type=\"primary\">专业模式</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <!--          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"58\">-->\r\n          <!--            <template slot-scope=\"scope\">-->\r\n          <!--              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>-->\r\n          <!--            </template>-->\r\n          <!--          </el-table-column>-->\r\n          <el-table-column align=\"left\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"center\" label=\"重要\" prop=\"orderNum\" width=\"33\"/>\r\n          <el-table-column align=\"center\" label=\"录入时间\" prop=\"updateTime\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0;\">{{ scope.row.updateByName }}</h6>\r\n              <h6 style=\"margin: 0;\">{{ parseTime(scope.row.updateTime, '{y}-{m}-{d}') }}</h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:characteristics:edit']\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:characteristics:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改物流注意事项对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-dialogDrag v-dialogDragWidth\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      width=\"500px\"\r\n      append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"78px\" class=\"edit\">\r\n        <el-form-item label=\"服务类型\" prop=\"serviceTypeId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.serviceTypeId\" :type=\"'serviceType'\"\r\n                       @return=\"getServiceTypeId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"货物特征\" prop=\"cargoTypeIds\">\r\n          <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.cargoTypeIds\" :type=\"'cargoType'\"\r\n                       @return=\"getCargoTypeIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"承运人\" prop=\"carrierIds\">\r\n          <treeselect v-model=\"carrierIds\" :disable-fuzzy-matching=\"true\"\r\n                      :flat=\"true\" :flatten-search-results=\"true\" :disable-branch-nodes=\"true\"\r\n                      :multiple=\"true\" disable-branch-nodes @open=\"loadCarrier\"\r\n                      :normalizer=\"carrierNormalizer\" placeholder=\"选择承运人\" :options=\"temCarrierList\"\r\n                      :show-count=\"true\" @input=\"deselectAllCarrrierIds\"\r\n                      @deselect=\"handleDeselectCarrierIds\" @select=\"handleSelectCarrierIds\">\r\n            <div slot=\"value-label\" slot-scope=\"{node}\">\r\n              {{ node.raw.carrier.carrierIntlCode }}\r\n              {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : '' }}\r\n            </div>\r\n            <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                   :class=\"labelClassName\">\r\n              {{ node.label.indexOf(',') != -1 ? node.label.substring(0, node.label.indexOf(',')) : node.label }}\r\n              <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n            </label>\r\n          </treeselect>\r\n        </el-form-item>\r\n        <el-form-item label=\"订舱口\" prop=\"companyId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.companyId\"\r\n                       :placeholder=\"'订舱口'\" :type=\"'supplier'\" @return=\"getCompanyId\"/>\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-form-item label=\"启运区域\" prop=\"locationDepartureIds\">\r\n            <location-select :multiple=\"true\" :pass=\"form.locationDepartureIds\"\r\n                             ref=\"location\" :load-options=\"locationOptions\"\r\n                             @return=\"getLocationDepartureIds\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"目的区域\" prop=\"locationDestinationIds\">\r\n            <location-select :multiple=\"true\" :pass=\"form.locationDestinationIds\"\r\n                             :load-options=\"locationOptions\"\r\n                             :en=\"true\" @return=\"getLocationDestinationIds\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"目的航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.lineDestinationIds\" :type=\"'line'\"\r\n                         @return=\"getLineDestinationIds\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-form-item label=\"物流要素\" prop=\"infoId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.infoId\" :type=\"'commonInfo'\" @return=\"getInfoId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"要素详细\" prop=\"essentialDetail\">\r\n          <el-input v-model=\"form.essentialDetail\" type=\"textarea\" :autosize=\"{ minRows: 5, maxRows: 20}\"\r\n                    maxlength=\"300\" placeholder=\"内容\" show-word-limit/>\r\n        </el-form-item>\r\n        <el-row :gutter=\"5\">\r\n          <el-col :span=\"15\">\r\n            <el-form-item label=\"有效时间\" prop=\"validTime\">\r\n              <el-date-picker v-model=\"validTime\"\r\n                              clearable\r\n                              placeholder=\"有效时间\"\r\n                              style=\"width: 100%\"\r\n                              default-time=\"['00:00:00', '23:59:59']\"\r\n                              @change=\"changeTime\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              type=\"daterange\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"9\">\r\n            <el-form-item label=\"录入时间\" prop=\"updateTime\">\r\n              <el-date-picker v-model=\"form.updateTime\"\r\n                              clearable\r\n                              placeholder=\"录入时间\"\r\n                              style=\"width: 100%\"\r\n                              disabled\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\">\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row :gutter=\"5\">\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"是否有效\" prop=\"isValid\">\r\n              <el-select v-model=\"form.isValid\" placeholder=\"是否有效\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_yes_no\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"状态\">\r\n              <el-select v-model=\"form.status\" placeholder=\"状态\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"显示模式\" prop=\"showMode\">\r\n          <el-select v-model=\"form.showMode\" placeholder=\"显示模式\" style=\"width: 100%\">\r\n            <el-option value=\"0\" label=\"小白模式\"/>\r\n            <el-option value=\"1\" label=\"专业模式\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"重要程度\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" :min=\"0\" placeholder=\"排序\" style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input type=\"textarea\" v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCharacteristics,\r\n  delCharacteristics,\r\n  getCharacteristics,\r\n  listCharacteristics,\r\n  updateCharacteristics\r\n} from \"@/api/system/characteristics\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\";\r\nimport pinyin from \"js-pinyin\";\r\nimport store from \"@/store\";\r\nimport {parseTime} from \"@/utils/rich\";\r\nimport {selectListUser} from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Characteristics\",\r\n  dicts: ['sys_normal_disable', 'sys_yes_no'],\r\n  components: {Treeselect},\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      validTime: [],\r\n      carrierList: [],\r\n      carrierIds: [],\r\n      queryCarrierIds: [],\r\n      temCarrierList: [],\r\n      locationOptions: [],\r\n      userList: [],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 物流注意事项表格数据\r\n      characteristicsList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        accurate: 1,\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        infoId: null,\r\n        showMode: null,\r\n        serviceTypeId: null,\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        carrierIds: [],\r\n        companyId: null,\r\n        essentialDetail: null,\r\n        isValid: \"Y\",\r\n        updateBy: null,\r\n        updateTime: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        cargoTypeIds: {required: true, trigger: \"blur\"}\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    'form.serviceTypeId'(n) {\r\n      this.loadCarrier()\r\n      let list = []\r\n      if (this.carrierList != undefined && n != null) {\r\n        this.temCarrierList = this.carrierList\r\n        /* for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (this.form.carrierIds != null && this.form.carrierIds.includes(b.carrier.carrierId) && !this.carrierIds.includes(b.serviceTypeId)) {\r\n                    this.carrierIds.push(b.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        } */\r\n      }\r\n      if (this.carrierList != undefined && n != null) {\r\n        for (const c of this.carrierList) {\r\n          if (n != null && n != undefined) {\r\n            if (c.serviceTypeId == n) {\r\n              list.push(c)\r\n            }\r\n            if (c.children != undefined && c.children.length > 0) {\r\n              for (const ch of c.children) {\r\n                if (ch.serviceTypeId == n) {\r\n                  list.push(ch)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.temCarrierList = list\r\n        if (this.temCarrierList.length > 0) {\r\n          /* for (const v of this.temCarrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (this.form.carrierIds != null && this.form.carrierIds.includes(a.carrier.carrierId) && !this.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          } */\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.loadCarrier();\r\n    selectListUser().then(response => {\r\n      this.userList = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    changeTime(val) {\r\n      if (val == undefined) {\r\n        this.form.validFrom = null\r\n        this.form.validTo = null\r\n      }\r\n      this.form.validFrom = val[0]\r\n      this.form.validTo = val[1]\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch('getServiceTypeCarriersList').then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + ' ' + node.serviceEnName + ',' + pinyin.getFullChars(node.serviceLocalName)\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : '') + ' ' + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : '') + ' ' + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : '') + ',' + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : ''))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children,\r\n      }\r\n    },\r\n    tableRowClassName({row}) {\r\n      let date = parseTime(new Date(), \"{y}-{m}-{d}\")\r\n      let validFrom = parseTime(row.validFrom, \"{y}-{m}-{d}\")\r\n      let validTo = parseTime(row.validTo, \"{y}-{m}-{d}\")\r\n      if (validFrom < date < validTo)\r\n        return ''\r\n      if (validTo < date)\r\n        return 'valid-row';\r\n      if (validFrom > date)\r\n        return 'valid-before';\r\n      return ''\r\n    },\r\n    /** 查询物流注意事项列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCharacteristics(this.queryParams).then(response => {\r\n        this.characteristicsList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        characteristicsId: null,\r\n        serviceTypeId: this.form.serviceTypeId != undefined ? this.form.serviceTypeId : 21,\r\n        infoId: null,\r\n        showMode: \"0\",\r\n        essentialDetail: null,\r\n        updateTime: parseTime(new Date()),\r\n        validFrom: null,\r\n        validTo: null,\r\n        isValid: \"Y\",\r\n        status: \"0\",\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\",\r\n        cargoTypeIds: this.form.cargoTypeIds != undefined ? this.form.cargoTypeIds : [-1],\r\n        locationDepartureIds: this.form.locationDepartureIds != undefined ? this.form.locationDepartureIds : [13716],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        carrierIds: [],\r\n      };\r\n      this.validTime = []\r\n      this.carrierIds = []\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.characteristicsId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加物流注意事项\";\r\n      setTimeout(() => {\r\n        this.$refs.location.remoteMethod(\"广东\")\r\n      }, 0)\r\n      // let date = new Date()\r\n      // this.validTime.push(date)\r\n      // this.validTime.push(new Date(new Date(date.getFullYear(), date.getMonth() + 1, 1) - 1000 * 60 * 60 * 24))\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.loading = true\r\n      const characteristicsId = row.characteristicsId || this.ids\r\n      getCharacteristics(characteristicsId).then(response => {\r\n        this.form = response.data;\r\n        this.form.cargoTypeIds = response.cargoTypeIds;\r\n        this.form.lineDepartureIds = response.lineDepartureIds;\r\n        this.form.locationDepartureIds = response.locationDepartureIds;\r\n        this.form.lineDestinationIds = response.lineDestinationIds;\r\n        this.form.locationDestinationIds = response.locationDestinationIds;\r\n        this.form.carrierIds = response.carrierIds\r\n\r\n        this.loadCarrier()\r\n        let list = []\r\n        if (this.carrierList != undefined && this.form.serviceTypeId != null) {\r\n          this.temCarrierList = this.carrierList\r\n          for (const v of this.carrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (response.carrierIds != null && response.carrierIds.includes(a.carrier.carrierId) && !response.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (this.carrierList != undefined && this.form.serviceTypeId != null) {\r\n          for (const c of this.carrierList) {\r\n            if (this.form.serviceTypeId != null && this.form.serviceTypeId != undefined) {\r\n              if (c.serviceTypeId == this.form.serviceTypeId) {\r\n                list.push(c)\r\n              }\r\n              if (c.children != undefined && c.children.length > 0) {\r\n                for (const ch of c.children) {\r\n                  if (ch.serviceTypeId == this.form.serviceTypeId) {\r\n                    list.push(ch)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.temCarrierList = list\r\n          if (this.form.serviceTypeId == null && this.temCarrierList.length > 0) {\r\n            for (const v of this.temCarrierList) {\r\n              if (v.children != undefined && v.children.length > 0) {\r\n                for (const a of v.children) {\r\n                  if (response.carrierIds != null && response.carrierIds.includes(a.carrier.carrierId) && !response.carrierIds.includes(a.serviceTypeId)) {\r\n                    this.carrierIds.push(a.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        if (response.data.validFrom)\r\n          this.validTime.push(response.data.validFrom)\r\n        if (response.data.validTo)\r\n          this.validTime.push(response.data.validTo)\r\n        this.locationOptions = response.locationOptions\r\n        this.open = true;\r\n        this.title = \"修改物流注意事项\";\r\n        this.loading = false\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.characteristicsId != null) {\r\n            updateCharacteristics(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCharacteristics(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const characteristicsIds = row.characteristicsId || this.ids;\r\n      this.$confirm('是否确认删除物流注意事项编号为\"' + characteristicsIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCharacteristics(characteristicsIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/characteristics/export', {\r\n        ...this.queryParams\r\n      }, `characteristics_${new Date().getTime()}.xlsx`)\r\n    },\r\n    queryServiceTypeId(val) {\r\n      this.queryParams.serviceTypeId = val\r\n      this.handleQuery()\r\n    },\r\n    queryCargoTypeIds(val) {\r\n      this.queryParams.cargoTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDepartureIds(val) {\r\n      this.queryParams.locationDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLineDepartureIds(val) {\r\n      this.queryParams.lineDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDestinationIds(val) {\r\n      this.queryParams.locationDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLineDestinationIds(val) {\r\n      this.queryParams.lineDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryInfoId(val) {\r\n      this.queryParams.infoId = val\r\n      this.handleQuery()\r\n    },\r\n    getServiceTypeId(val) {\r\n      if (val == undefined) {\r\n        this.form.serviceTypeId = null\r\n        this.carrierIds = null\r\n        this.form.carrierIds = null\r\n      } else {\r\n        this.form.serviceTypeId = val\r\n      }\r\n    },\r\n    getCurrencyId(val) {\r\n      if (val == undefined) {\r\n        this.form.currencyId = null\r\n      } else {\r\n        this.form.currencyId = val\r\n      }\r\n    },\r\n    getUnitId(val) {\r\n      if (val == undefined) {\r\n        this.form.unitId = null\r\n      } else {\r\n        this.form.unitId = val\r\n      }\r\n    },\r\n    getCargoTypeIds(val) {\r\n      this.form.cargoTypeIds = val\r\n    },\r\n    getCompanyId(val) {\r\n      this.form.companyId = val\r\n    },\r\n    getLocationDepartureIds(val) {\r\n      this.form.locationDepartureIds = val\r\n    },\r\n    getLineDepartureIds(val) {\r\n      this.form.lineDepartureIds = val\r\n    },\r\n    getLocationDestinationIds(val) {\r\n      this.form.locationDestinationIds = val\r\n    },\r\n    getLineDestinationIds(val) {\r\n      this.form.lineDestinationIds = val\r\n    },\r\n    getInfoId(val) {\r\n      this.form.infoId = val\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleSelectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds.push(node.carrier.carrierId)\r\n      this.handleQuery()\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    handleDeselectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n      this.handleQuery()\r\n    },\r\n    deselectAllCarrrierIds(value) {\r\n      if (value.length == 0) {\r\n        this.form.carrierIds = []\r\n      }\r\n    },\r\n    deselectAllQueryCarrierIds(value) {\r\n      if (value.length == 0) {\r\n        this.queryParams.carrierIds = []\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    queryCompanyId(val) {\r\n      this.queryParams.companyId = val\r\n      this.handleQuery()\r\n    },\r\n  }\r\n}\r\n</script>\r\n"]}]}