{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\communication.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\communication.js", "mtime": 1678688095224}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkQ29tbXVuaWNhdGlvbiA9IGFkZENvbW11bmljYXRpb247CmV4cG9ydHMuZGVsQ29tbXVuaWNhdGlvbiA9IGRlbENvbW11bmljYXRpb247CmV4cG9ydHMuZ2V0Q29tbXVuaWNhdGlvbiA9IGdldENvbW11bmljYXRpb247CmV4cG9ydHMubGlzdENvbW11bmljYXRpb24gPSBsaXN0Q29tbXVuaWNhdGlvbjsKZXhwb3J0cy51cGRhdGVDb21tdW5pY2F0aW9uID0gdXBkYXRlQ29tbXVuaWNhdGlvbjsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouS6pOa1geWIl+ihqApmdW5jdGlvbiBsaXN0Q29tbXVuaWNhdGlvbihxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9jb21tdW5pY2F0aW9uL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5Lqk5rWB6K+m57uGCmZ1bmN0aW9uIGdldENvbW11bmljYXRpb24oY29tbXVuaWNhdGlvbklkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2NvbW11bmljYXRpb24vJyArIGNvbW11bmljYXRpb25JZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5Lqk5rWBCmZ1bmN0aW9uIGFkZENvbW11bmljYXRpb24oZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9jb21tdW5pY2F0aW9uJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnkuqTmtYEKZnVuY3Rpb24gdXBkYXRlQ29tbXVuaWNhdGlvbihkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2NvbW11bmljYXRpb24nLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5Lqk5rWBCmZ1bmN0aW9uIGRlbENvbW11bmljYXRpb24oY29tbXVuaWNhdGlvbklkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2NvbW11bmljYXRpb24vJyArIGNvbW11bmljYXRpb25JZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listCommunication", "query", "request", "url", "method", "params", "getCommunication", "communicationId", "addCommunication", "data", "updateCommunication", "delCommunication"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/communication.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询交流列表\r\nexport function listCommunication(query) {\r\n  return request({\r\n    url: '/system/communication/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询交流详细\r\nexport function getCommunication(communicationId) {\r\n  return request({\r\n    url: '/system/communication/' + communicationId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增交流\r\nexport function addCommunication(data) {\r\n  return request({\r\n    url: '/system/communication',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改交流\r\nexport function updateCommunication(data) {\r\n  return request({\r\n    url: '/system/communication',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除交流\r\nexport function delCommunication(communicationId) {\r\n  return request({\r\n    url: '/system/communication/' + communicationId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACvC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,gBAAgBA,CAACC,eAAe,EAAE;EAChD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,eAAe;IAC/CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,gBAAgBA,CAACC,IAAI,EAAE;EACrC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,mBAAmBA,CAACD,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,gBAAgBA,CAACJ,eAAe,EAAE;EAChD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB,GAAGI,eAAe;IAC/CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}