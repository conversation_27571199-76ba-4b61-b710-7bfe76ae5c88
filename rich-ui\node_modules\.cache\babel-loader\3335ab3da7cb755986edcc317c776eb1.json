{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\mixins\\chargeCalculator.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\mixins\\chargeCalculator.js", "mtime": 1752032719059}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_currency", "_interopRequireDefault", "require", "_lodash", "_default", "data", "defaultChargeTemplate", "chargeOrderNum", "chargeTypeOrderNum", "sqdServiceTypeId", "typeId", "inquiryAmount", "quotationAmount", "dnAmount", "dnUnitCode", "dnUnitRate", "costCurrencyCode", "costCurrency", "quotationRate", "charge", "chargeName", "chargeEn", "freightId", "localChargeId", "profit", "taxRate", "quotationCurrencyCode", "quotationCurrency", "dnCurrencyCode", "clearingCompanyId", "companyName", "dnChargeNameId", "dutyRate", "basicCurrencyRate", "subtotal", "sqdDnCurrencyBalance", "showAmount", "showClient", "showCostCharge", "showCostCurrency", "showCostUnit", "showCurrencyRate", "showDutyRate", "showQuotationCharge", "showQuotationCurrency", "showQuotationUnit", "showStrategy", "showSupplier", "showUnitRate", "methods", "resetCharge", "rsCharge", "_", "cloneDeep", "calculateSubtotal", "currency", "multiply", "taxAmount", "divide", "add", "value", "convertCurrency", "amount", "fromCurrency", "to<PERSON><PERSON><PERSON><PERSON>", "exchangeRate", "rate", "calculateChargeTotal", "chargeList", "_this", "length", "RMB", "USD", "totalRMB", "result", "for<PERSON>ach", "rmbAmount", "calculateProfit", "receivableList", "payableList", "receivable", "payable", "subtract", "addChargeToService", "serviceObject", "rsChargeList", "push", "collectAllCharges", "allCharges", "rsClientMessage", "concat", "serviceCollections", "form", "rsOpSeaFclList", "rsOpSeaLclList", "rsOpAirList", "rsOpCtnrTruckList", "rsOpBulkTruckList", "rsOpDocDeclareList", "rsOpFreeDeclareList", "serviceList", "service", "exports", "default"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/system/document/mixins/chargeCalculator.js"], "sourcesContent": ["/**\r\n * 费用计算Mixin\r\n * 集中管理费用相关的计算逻辑\r\n */\r\nimport currency from \"currency.js\"\r\nimport _ from \"lodash\"\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 默认费用对象模板\r\n      defaultChargeTemplate: {\r\n        chargeOrderNum: 0,\r\n        chargeTypeOrderNum: 0,\r\n        sqdServiceTypeId: null,\r\n        typeId: null,\r\n        inquiryAmount: 1,\r\n        quotationAmount: 1,\r\n        dnAmount: 1,\r\n        dnUnitCode: null,\r\n        dnUnitRate: 0,\r\n        costCurrencyCode: null,\r\n        costCurrency: null,\r\n        quotationRate: 0,\r\n        charge: \"\",\r\n        chargeName: \"\",\r\n        chargeEn: \"\",\r\n        freightId: null,\r\n        localChargeId: null,\r\n        profit: 0,\r\n        taxRate: 0,\r\n        quotationCurrencyCode: null,\r\n        quotationCurrency: null,\r\n        dnCurrencyCode: null,\r\n        clearingCompanyId: null,\r\n        companyName: \"\",\r\n        dnChargeNameId: null,\r\n        dutyRate: 0,\r\n        basicCurrencyRate: 1,\r\n        subtotal: 0,\r\n        sqdDnCurrencyBalance: 0,\r\n\r\n        // UI控制字段\r\n        showAmount: false,\r\n        showClient: false,\r\n        showCostCharge: false,\r\n        showCostCurrency: false,\r\n        showCostUnit: false,\r\n        showCurrencyRate: false,\r\n        showDutyRate: false,\r\n        showQuotationCharge: false,\r\n        showQuotationCurrency: false,\r\n        showQuotationUnit: false,\r\n        showStrategy: true,\r\n        showSupplier: false,\r\n        showUnitRate: false\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 重置费用对象为默认状态\r\n     */\r\n    resetCharge() {\r\n      this.rsCharge = _.cloneDeep(this.defaultChargeTemplate)\r\n    },\r\n\r\n    /**\r\n     * 根据费用数据计算小计金额\r\n     * @param {Object} charge - 费用对象\r\n     * @returns {number} 计算后的小计金额\r\n     */\r\n    calculateSubtotal(charge) {\r\n      if (!charge) return 0\r\n\r\n      // 计算基本小计 (单价 * 数量)\r\n      let subtotal = currency(charge.dnUnitRate || 0).multiply(charge.dnAmount || 0)\r\n\r\n      // 如果有税率，加上税额\r\n      if (charge.dutyRate) {\r\n        const taxAmount = subtotal.multiply(currency(charge.dutyRate).divide(100))\r\n        subtotal = subtotal.add(taxAmount)\r\n      }\r\n\r\n      return subtotal.value\r\n    },\r\n\r\n    /**\r\n     * 根据汇率转换金额\r\n     * @param {number} amount - 原始金额\r\n     * @param {string} fromCurrency - 原始货币\r\n     * @param {string} toCurrency - 目标货币\r\n     * @param {number} exchangeRate - 汇率\r\n     * @returns {number} 转换后的金额\r\n     */\r\n    convertCurrency(amount, fromCurrency, toCurrency, exchangeRate) {\r\n      if (fromCurrency === toCurrency) return amount\r\n\r\n      // 如果无汇率默认值\r\n      const rate = exchangeRate || (fromCurrency === \"USD\" && toCurrency === \"RMB\" ? 6.5 : 0.15)\r\n\r\n      return currency(amount).multiply(rate).value\r\n    },\r\n\r\n    /**\r\n     * 计算所有费用的总和\r\n     * @param {Array} chargeList - 费用列表\r\n     * @param {string} currencyCode - 货币代码，可选\r\n     * @returns {Object} 包含各货币总额的对象\r\n     */\r\n    calculateChargeTotal(chargeList) {\r\n      if (!chargeList || !chargeList.length) {\r\n        return {\r\n          RMB: 0,\r\n          USD: 0,\r\n          totalRMB: 0 // 将USD按汇率转换后的RMB总额\r\n        }\r\n      }\r\n\r\n      const result = {\r\n        RMB: 0,\r\n        USD: 0,\r\n        totalRMB: 0\r\n      }\r\n\r\n      chargeList.forEach(charge => {\r\n        const subtotal = this.calculateSubtotal(charge)\r\n\r\n        if (charge.dnCurrencyCode === \"RMB\") {\r\n          result.RMB = currency(result.RMB).add(subtotal).value\r\n          result.totalRMB = currency(result.totalRMB).add(subtotal).value\r\n        } else if (charge.dnCurrencyCode === \"USD\") {\r\n          result.USD = currency(result.USD).add(subtotal).value\r\n\r\n          // 如果有汇率信息，转换为RMB\r\n          if (charge.basicCurrencyRate) {\r\n            const rmbAmount = currency(subtotal).multiply(charge.basicCurrencyRate).value\r\n            result.totalRMB = currency(result.totalRMB).add(rmbAmount).value\r\n          }\r\n        }\r\n      })\r\n\r\n      return result\r\n    },\r\n\r\n    /**\r\n     * 计算利润（应收 - 应付）\r\n     * @param {Array} receivableList - 应收费用列表\r\n     * @param {Array} payableList - 应付费用列表\r\n     * @returns {Object} 利润对象，包含RMB和USD\r\n     */\r\n    calculateProfit(receivableList, payableList) {\r\n      const receivable = this.calculateChargeTotal(receivableList)\r\n      const payable = this.calculateChargeTotal(payableList)\r\n\r\n      return {\r\n        RMB: currency(receivable.RMB).subtract(payable.RMB).value,\r\n        USD: currency(receivable.USD).subtract(payable.USD).value,\r\n        totalRMB: currency(receivable.totalRMB).subtract(payable.totalRMB).value\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 向服务对象中添加新费用\r\n     * @param {Object} serviceObject - 服务对象\r\n     * @param {Object} charge - 费用对象\r\n     */\r\n    addChargeToService(serviceObject, charge) {\r\n      if (!serviceObject) return\r\n\r\n      // 确保rsChargeList存在\r\n      if (!serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n\r\n      // 添加费用前计算小计\r\n      charge.subtotal = this.calculateSubtotal(charge)\r\n\r\n      // 添加费用\r\n      serviceObject.rsChargeList.push(_.cloneDeep(charge))\r\n    },\r\n\r\n    /**\r\n     * 收集所有服务的费用列表\r\n     * @returns {Array} 所有费用的合并列表\r\n     */\r\n    collectAllCharges() {\r\n      let allCharges = []\r\n\r\n      // 收集客户信息费用\r\n      if (this.rsClientMessage && this.rsClientMessage.rsChargeList) {\r\n        allCharges = allCharges.concat(this.rsClientMessage.rsChargeList)\r\n      }\r\n\r\n      // 收集各服务费用\r\n      const serviceCollections = [\r\n        this.form.rsOpSeaFclList,\r\n        this.form.rsOpSeaLclList,\r\n        this.form.rsOpAirList,\r\n        this.form.rsOpCtnrTruckList,\r\n        this.form.rsOpBulkTruckList,\r\n        this.form.rsOpDocDeclareList,\r\n        this.form.rsOpFreeDeclareList\r\n      ]\r\n\r\n      serviceCollections.forEach(serviceList => {\r\n        if (serviceList && serviceList.length) {\r\n          serviceList.forEach(service => {\r\n            if (service.rsChargeList && service.rsChargeList.length) {\r\n              allCharges = allCharges.concat(service.rsChargeList)\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      return allCharges\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAIA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;AALA;AACA;AACA;AACA;AAHA,IAAAE,QAAA,GAOe;EACbC,IAAI,WAAAA,KAAA,EAAG;IACL,OAAO;MACL;MACAC,qBAAqB,EAAE;QACrBC,cAAc,EAAE,CAAC;QACjBC,kBAAkB,EAAE,CAAC;QACrBC,gBAAgB,EAAE,IAAI;QACtBC,MAAM,EAAE,IAAI;QACZC,aAAa,EAAE,CAAC;QAChBC,eAAe,EAAE,CAAC;QAClBC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,IAAI;QAChBC,UAAU,EAAE,CAAC;QACbC,gBAAgB,EAAE,IAAI;QACtBC,YAAY,EAAE,IAAI;QAClBC,aAAa,EAAE,CAAC;QAChBC,MAAM,EAAE,EAAE;QACVC,UAAU,EAAE,EAAE;QACdC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,IAAI;QACnBC,MAAM,EAAE,CAAC;QACTC,OAAO,EAAE,CAAC;QACVC,qBAAqB,EAAE,IAAI;QAC3BC,iBAAiB,EAAE,IAAI;QACvBC,cAAc,EAAE,IAAI;QACpBC,iBAAiB,EAAE,IAAI;QACvBC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,IAAI;QACpBC,QAAQ,EAAE,CAAC;QACXC,iBAAiB,EAAE,CAAC;QACpBC,QAAQ,EAAE,CAAC;QACXC,oBAAoB,EAAE,CAAC;QAEvB;QACAC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,KAAK;QACjBC,cAAc,EAAE,KAAK;QACrBC,gBAAgB,EAAE,KAAK;QACvBC,YAAY,EAAE,KAAK;QACnBC,gBAAgB,EAAE,KAAK;QACvBC,YAAY,EAAE,KAAK;QACnBC,mBAAmB,EAAE,KAAK;QAC1BC,qBAAqB,EAAE,KAAK;QAC5BC,iBAAiB,EAAE,KAAK;QACxBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,KAAK;QACnBC,YAAY,EAAE;MAChB;IACF,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACP;AACJ;AACA;IACIC,WAAW,WAAAA,YAAA,EAAG;MACZ,IAAI,CAACC,QAAQ,GAAGC,eAAC,CAACC,SAAS,CAAC,IAAI,CAAC/C,qBAAqB,CAAC;IACzD,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIgD,iBAAiB,WAAAA,kBAACnC,MAAM,EAAE;MACxB,IAAI,CAACA,MAAM,EAAE,OAAO,CAAC;;MAErB;MACA,IAAIe,QAAQ,GAAG,IAAAqB,iBAAQ,EAACpC,MAAM,CAACJ,UAAU,IAAI,CAAC,CAAC,CAACyC,QAAQ,CAACrC,MAAM,CAACN,QAAQ,IAAI,CAAC,CAAC;;MAE9E;MACA,IAAIM,MAAM,CAACa,QAAQ,EAAE;QACnB,IAAMyB,SAAS,GAAGvB,QAAQ,CAACsB,QAAQ,CAAC,IAAAD,iBAAQ,EAACpC,MAAM,CAACa,QAAQ,CAAC,CAAC0B,MAAM,CAAC,GAAG,CAAC,CAAC;QAC1ExB,QAAQ,GAAGA,QAAQ,CAACyB,GAAG,CAACF,SAAS,CAAC;MACpC;MAEA,OAAOvB,QAAQ,CAAC0B,KAAK;IACvB,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;IACIC,eAAe,WAAAA,gBAACC,MAAM,EAAEC,YAAY,EAAEC,UAAU,EAAEC,YAAY,EAAE;MAC9D,IAAIF,YAAY,KAAKC,UAAU,EAAE,OAAOF,MAAM;;MAE9C;MACA,IAAMI,IAAI,GAAGD,YAAY,KAAKF,YAAY,KAAK,KAAK,IAAIC,UAAU,KAAK,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC;MAE1F,OAAO,IAAAT,iBAAQ,EAACO,MAAM,CAAC,CAACN,QAAQ,CAACU,IAAI,CAAC,CAACN,KAAK;IAC9C,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIO,oBAAoB,WAAAA,qBAACC,UAAU,EAAE;MAAA,IAAAC,KAAA;MAC/B,IAAI,CAACD,UAAU,IAAI,CAACA,UAAU,CAACE,MAAM,EAAE;QACrC,OAAO;UACLC,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,CAAC;UACNC,QAAQ,EAAE,CAAC,CAAC;QACd,CAAC;MACH;;MAEA,IAAMC,MAAM,GAAG;QACbH,GAAG,EAAE,CAAC;QACNC,GAAG,EAAE,CAAC;QACNC,QAAQ,EAAE;MACZ,CAAC;MAEDL,UAAU,CAACO,OAAO,CAAC,UAAAxD,MAAM,EAAI;QAC3B,IAAMe,QAAQ,GAAGmC,KAAI,CAACf,iBAAiB,CAACnC,MAAM,CAAC;QAE/C,IAAIA,MAAM,CAACS,cAAc,KAAK,KAAK,EAAE;UACnC8C,MAAM,CAACH,GAAG,GAAG,IAAAhB,iBAAQ,EAACmB,MAAM,CAACH,GAAG,CAAC,CAACZ,GAAG,CAACzB,QAAQ,CAAC,CAAC0B,KAAK;UACrDc,MAAM,CAACD,QAAQ,GAAG,IAAAlB,iBAAQ,EAACmB,MAAM,CAACD,QAAQ,CAAC,CAACd,GAAG,CAACzB,QAAQ,CAAC,CAAC0B,KAAK;QACjE,CAAC,MAAM,IAAIzC,MAAM,CAACS,cAAc,KAAK,KAAK,EAAE;UAC1C8C,MAAM,CAACF,GAAG,GAAG,IAAAjB,iBAAQ,EAACmB,MAAM,CAACF,GAAG,CAAC,CAACb,GAAG,CAACzB,QAAQ,CAAC,CAAC0B,KAAK;;UAErD;UACA,IAAIzC,MAAM,CAACc,iBAAiB,EAAE;YAC5B,IAAM2C,SAAS,GAAG,IAAArB,iBAAQ,EAACrB,QAAQ,CAAC,CAACsB,QAAQ,CAACrC,MAAM,CAACc,iBAAiB,CAAC,CAAC2B,KAAK;YAC7Ec,MAAM,CAACD,QAAQ,GAAG,IAAAlB,iBAAQ,EAACmB,MAAM,CAACD,QAAQ,CAAC,CAACd,GAAG,CAACiB,SAAS,CAAC,CAAChB,KAAK;UAClE;QACF;MACF,CAAC,CAAC;MAEF,OAAOc,MAAM;IACf,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIG,eAAe,WAAAA,gBAACC,cAAc,EAAEC,WAAW,EAAE;MAC3C,IAAMC,UAAU,GAAG,IAAI,CAACb,oBAAoB,CAACW,cAAc,CAAC;MAC5D,IAAMG,OAAO,GAAG,IAAI,CAACd,oBAAoB,CAACY,WAAW,CAAC;MAEtD,OAAO;QACLR,GAAG,EAAE,IAAAhB,iBAAQ,EAACyB,UAAU,CAACT,GAAG,CAAC,CAACW,QAAQ,CAACD,OAAO,CAACV,GAAG,CAAC,CAACX,KAAK;QACzDY,GAAG,EAAE,IAAAjB,iBAAQ,EAACyB,UAAU,CAACR,GAAG,CAAC,CAACU,QAAQ,CAACD,OAAO,CAACT,GAAG,CAAC,CAACZ,KAAK;QACzDa,QAAQ,EAAE,IAAAlB,iBAAQ,EAACyB,UAAU,CAACP,QAAQ,CAAC,CAACS,QAAQ,CAACD,OAAO,CAACR,QAAQ,CAAC,CAACb;MACrE,CAAC;IACH,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIuB,kBAAkB,WAAAA,mBAACC,aAAa,EAAEjE,MAAM,EAAE;MACxC,IAAI,CAACiE,aAAa,EAAE;;MAEpB;MACA,IAAI,CAACA,aAAa,CAACC,YAAY,EAAE;QAC/BD,aAAa,CAACC,YAAY,GAAG,EAAE;MACjC;;MAEA;MACAlE,MAAM,CAACe,QAAQ,GAAG,IAAI,CAACoB,iBAAiB,CAACnC,MAAM,CAAC;;MAEhD;MACAiE,aAAa,CAACC,YAAY,CAACC,IAAI,CAAClC,eAAC,CAACC,SAAS,CAAClC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;AACJ;AACA;AACA;IACIoE,iBAAiB,WAAAA,kBAAA,EAAG;MAClB,IAAIC,UAAU,GAAG,EAAE;;MAEnB;MACA,IAAI,IAAI,CAACC,eAAe,IAAI,IAAI,CAACA,eAAe,CAACJ,YAAY,EAAE;QAC7DG,UAAU,GAAGA,UAAU,CAACE,MAAM,CAAC,IAAI,CAACD,eAAe,CAACJ,YAAY,CAAC;MACnE;;MAEA;MACA,IAAMM,kBAAkB,GAAG,CACzB,IAAI,CAACC,IAAI,CAACC,cAAc,EACxB,IAAI,CAACD,IAAI,CAACE,cAAc,EACxB,IAAI,CAACF,IAAI,CAACG,WAAW,EACrB,IAAI,CAACH,IAAI,CAACI,iBAAiB,EAC3B,IAAI,CAACJ,IAAI,CAACK,iBAAiB,EAC3B,IAAI,CAACL,IAAI,CAACM,kBAAkB,EAC5B,IAAI,CAACN,IAAI,CAACO,mBAAmB,CAC9B;MAEDR,kBAAkB,CAAChB,OAAO,CAAC,UAAAyB,WAAW,EAAI;QACxC,IAAIA,WAAW,IAAIA,WAAW,CAAC9B,MAAM,EAAE;UACrC8B,WAAW,CAACzB,OAAO,CAAC,UAAA0B,OAAO,EAAI;YAC7B,IAAIA,OAAO,CAAChB,YAAY,IAAIgB,OAAO,CAAChB,YAAY,CAACf,MAAM,EAAE;cACvDkB,UAAU,GAAGA,UAAU,CAACE,MAAM,CAACW,OAAO,CAAChB,YAAY,CAAC;YACtD;UACF,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,OAAOG,UAAU;IACnB;EACF;AACF,CAAC;AAAAc,OAAA,CAAAC,OAAA,GAAAnG,QAAA"}]}