package com.rich.common.core.domain.entity;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import javax.xml.crypto.Data;

/**
 * 分账单对象 rs_debit_note
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public class RsDebitNote extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 账单ID，主键
     */
    private Long debitNoteId;

    /**
     * 所属订单号
     */
    @Excel(name = "所属订单号")
    private String sqdRctNo;

    /**
     * 所属服务实例ID
     */
    @Excel(name = "所属服务实例ID")
    private Long serviceId;

    /**
     * 操作单ID
     */
    @Excel(name = "操作单ID")
    private Long rctId;

    /**
     * 收付标志，0-收款，1-付款
     */
    @Excel(name = "收付标志，0-收款，1-付款")
    private Integer isRecievingOrPaying;

    /**
     * 所属公司名称
     */
    @Excel(name = "所属公司名称")
    private String companyName;

    /**
     * 结算单位ID
     */
    @Excel(name = "结算单位ID")
    private Long clearingCompanyId;

    /**
     * 结算币种
     */
    @Excel(name = "结算币种")
    private String dnCurrencyCode;

    /**
     * 账单应收
     */
    @Excel(name = "账单应收")
    private BigDecimal billReceivable;

    /**
     * 账单应付
     */
    @Excel(name = "账单应付")
    private BigDecimal billPayable;

    /**
     * 账单状态，如 draft/confirmed/closed 等
     */
    @Excel(name = "账单状态，如 draft/confirmed/closed 等")
    private String billStatus;

    /**
     * 是否合并开票：0 否，1 是
     */
    @Excel(name = "是否合并开票：0 否，1 是")
    private Integer isCombinedInvoice;

    /**
     * 所属发票流水号
     */
    @Excel(name = "所属发票流水号")
    private String invoiceCodeNo;

    /**
     * 发票状态，如 unissued/issued/canceled
     */
    @Excel(name = "发票状态，如 unissued/issued/canceled")
    private String invoiceStatus;

    /**
     * 销账状态，如 unwritten/partial/written
     */
    @Excel(name = "销账状态，如 unwritten/partial/written")
    private String writeoffStatus;

    private List<RsCharge> rsChargeList;
    private String companyBelongsTo;
    private String clearingCompanyName;
    private Date requestPaymentDate;
    private Date expectedPaymentDate;
    private Date actualPaymentDate;
    private String clearingCompanyBankAccount;
    private String bankAccountCode;
    private Long invoiceId;

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getBankAccountCode() {
        return bankAccountCode;
    }

    public void setBankAccountCode(String bankAccountCode) {
        this.bankAccountCode = bankAccountCode;
    }

    public String getClearingCompanyBankAccount() {
        return clearingCompanyBankAccount;
    }

    public void setClearingCompanyBankAccount(String clearingCompanyBankAccount) {
        this.clearingCompanyBankAccount = clearingCompanyBankAccount;
    }

    public Date getActualPaymentDate() {
        return actualPaymentDate;
    }

    public void setActualPaymentDate(Date actualPaymentDate) {
        this.actualPaymentDate = actualPaymentDate;
    }

    public Date getExpectedPaymentDate() {
        return expectedPaymentDate;
    }

    public void setExpectedPaymentDate(Date expectedPaymentDate) {
        this.expectedPaymentDate = expectedPaymentDate;
    }

    public Date getRequestPaymentDate() {
        return requestPaymentDate;
    }

    public void setRequestPaymentDate(Date requestPaymentDate) {
        this.requestPaymentDate = requestPaymentDate;
    }

    public String getClearingCompanyName() {
        return clearingCompanyName;
    }

    public void setClearingCompanyName(String clearingCompanyName) {
        this.clearingCompanyName = clearingCompanyName;
    }

    public String getCompanyBelongsTo() {
        return companyBelongsTo;
    }

    public void setCompanyBelongsTo(String companyBelongsTo) {
        this.companyBelongsTo = companyBelongsTo;
    }

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public Long getDebitNoteId() {
        return debitNoteId;
    }

    public void setDebitNoteId(Long debitNoteId) {
        this.debitNoteId = debitNoteId;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public Integer getIsRecievingOrPaying() {
        return isRecievingOrPaying;
    }

    public void setIsRecievingOrPaying(Integer isRecievingOrPaying) {
        this.isRecievingOrPaying = isRecievingOrPaying;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getClearingCompanyId() {
        return clearingCompanyId;
    }

    public void setClearingCompanyId(Long clearingCompanyId) {
        this.clearingCompanyId = clearingCompanyId;
    }

    public String getDnCurrencyCode() {
        return dnCurrencyCode;
    }

    public void setDnCurrencyCode(String dnCurrencyCode) {
        this.dnCurrencyCode = dnCurrencyCode;
    }

    public BigDecimal getBillReceivable() {
        return billReceivable;
    }

    public void setBillReceivable(BigDecimal billReceivable) {
        this.billReceivable = billReceivable;
    }

    public BigDecimal getBillPayable() {
        return billPayable;
    }

    public void setBillPayable(BigDecimal billPayable) {
        this.billPayable = billPayable;
    }

    public String getBillStatus() {
        return billStatus;
    }

    public void setBillStatus(String billStatus) {
        this.billStatus = billStatus;
    }

    public Integer getIsCombinedInvoice() {
        return isCombinedInvoice;
    }

    public void setIsCombinedInvoice(Integer isCombinedInvoice) {
        this.isCombinedInvoice = isCombinedInvoice;
    }

    public String getInvoiceCodeNo() {
        return invoiceCodeNo;
    }

    public void setInvoiceCodeNo(String invoiceCodeNo) {
        this.invoiceCodeNo = invoiceCodeNo;
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public String getWriteoffStatus() {
        return writeoffStatus;
    }

    public void setWriteoffStatus(String writeoffStatus) {
        this.writeoffStatus = writeoffStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("debitNoteId", getDebitNoteId())
                .append("sqdRctNo", getSqdRctNo())
                .append("serviceId", getServiceId())
                .append("rctId", getRctId())
                .append("isRecievingOrPaying", getIsRecievingOrPaying())
                .append("companyName", getCompanyName())
                .append("clearingCompanyId", getClearingCompanyId())
                .append("dnCurrencyCode", getDnCurrencyCode())
                .append("billReceivable", getBillReceivable())
                .append("billPayable", getBillPayable())
                .append("billStatus", getBillStatus())
                .append("isCombinedInvoice", getIsCombinedInvoice())
                .append("invoiceCodeNo", getInvoiceCodeNo())
                .append("invoiceStatus", getInvoiceStatus())
                .append("writeoffStatus", getWriteoffStatus())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
