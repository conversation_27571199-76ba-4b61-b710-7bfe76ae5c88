<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.RsRctMapper">
    <resultMap type="RsRct" id="RsRctResult">
        <result property="rctId" column="rct_id"/>
        <result property="rctNo" column="rct_no"/>
        <result property="clientId" column="client_id"/>
        <result property="clientSummary" column="client_summary"/>
        <result property="clientRoleId" column="client_role_id"/>
        <result property="clientContact" column="client_contact"/>
        <result property="clientContactTel" column="client_contact_tel"/>
        <result property="clientContactEmail" column="client_contact_email"/>
        <result property="relationClientIdList" column="relation_client_id_list"/>
        <result property="emergencyLevel" column="emergency_level"/>
        <result property="difficultyLevel" column="difficulty_level"/>
        <result property="releaseType" column="release_type"/>
        <result property="impExpType" column="imp_exp_type"/>
        <result property="tradingTerms" column="trading_terms"/>
        <result property="logisticsTerms" column="logistics_terms"/>
        <result property="tradingPaymentChannel" column="trading_payment_channel"/>
        <result property="clientContractNo" column="client_contract_no"/>
        <result property="clientInvoiceNo" column="client_invoice_no"/>
        <result property="cargoTypeCodeSum" column="cargo_type_code_sum"/>
        <result property="goodsNameSummary" column="goods_name_summary"/>
        <result property="packageQuantity" column="package_quantity"/>
        <result property="goodsVolume" column="goods_volume"/>
        <result property="grossWeight" column="gross_weight"/>
        <result property="weightUnitCode" column="weight_unit_code"/>
        <result property="goodsCurrencyCode" column="goods_currency_code"/>
        <result property="goodsValue" column="goods_value"/>
        <result property="logisticsTypeId" column="logistics_type_id"/>
        <result property="revenueTon" column="revenue_ton"/>
        <result property="polId" column="pol_id"/>
        <result property="localBasicPortId" column="local_basic_port_id"/>
        <result property="transitPortId" column="transit_port_id"/>
        <result property="podId" column="pod_id"/>
        <result property="destinationPortId" column="destination_port_id"/>
        <result property="cvClosingTime" column="cv_closing_time"/>
        <result property="siClosingTime" column="si_closing_time"/>
        <result property="firstVessel" column="first_vessel"/>
        <result property="firstVoyage" column="first_voyage"/>
        <result property="firstCyOpenTime" column="first_cy_open_time"/>
        <result property="firstCyClosingTime" column="first_cy_closing_time"/>
        <result property="firstEtd" column="first_etd"/>
        <result property="basicVessel" column="basic_vessel"/>
        <result property="basicVoyage" column="basic_voyage"/>
        <result property="basicFinalGateinTime" column="basic_final_gatein_time"/>
        <result property="basicEtd" column="basic_etd"/>
        <result property="podEta" column="pod_eta"/>
        <result property="destinationPortEta" column="destination_port_eta"/>
        <result property="carrierId" column="carrier_id"/>
        <result property="inquiryScheduleSummary" column="inquiry_schedule_summary"/>
        <result property="serviceTypeIdList" column="service_type_id_list"/>
        <result property="soNo" column="so_no"/>
        <result property="blNo" column="bl_no"/>
        <result property="sqdContainersSealsSum" column="sqd_containers_seals_sum"/>
        <result property="sqdExportCustomsType" column="sqd_export_customs_type"/>
        <result property="sqdTrailerType" column="sqd_trailer_type"/>
        <result property="rctProcessStatusSummary" column="rct_process_status_summary"/>
        <result property="transportStatusSummary" column="transport_status_summary"/>
        <result property="paymentReceivingStatusSummary" column="payment_receiving_status_summary"/>
        <result property="paymentPayingStatusSummary" column="payment_paying_status_summary"/>
        <result property="transportStatusA" column="transport_status_a"/>
        <result property="transportStatusB" column="transport_status_b"/>
        <result property="docStatusA" column="doc_status_a"/>
        <result property="docStatusB" column="doc_status_b"/>
        <result property="processStatusId" column="process_status_id"/>
        <result property="processStatusTime" column="process_status_time"/>
        <result property="qoutationNo" column="qoutation_no"/>
        <result property="qoutationSketch" column="qoutation_sketch"/>
        <result property="salesId" column="sales_id"/>
        <result property="qoutationTime" column="qoutation_time"/>
        <result property="newBookingNo" column="new_booking_no"/>
        <result property="newBookingRemark" column="new_booking_remark"/>
        <result property="salesAssistantId" column="sales_assistant_id"/>
        <result property="salesObserverId" column="sales_observer_id"/>
        <result property="newBookingTime" column="new_booking_time"/>
        <result property="inquiryNoticeSum" column="inquiry_notice_sum"/>
        <result property="inquiryInnerRemarkSum" column="inquiry_inner_remark_sum"/>
        <result property="verifyPsaId" column="verify_psa_id"/>
        <result property="psaVerifyTime" column="psa_verify_time"/>
        <result property="opLeaderNotice" column="op_leader_notice"/>
        <result property="opInnerRemark" column="op_inner_remark"/>
        <result property="opId" column="op_id"/>
        <result property="bookingOpId" column="booking_op_id"/>
        <result property="docOpId" column="doc_op_id"/>
        <result property="opObserverId" column="op_observer_id"/>
        <result property="rctCreateTime" column="rct_create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleteBy" column="delete_by"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="precarriageRegionId" column="precarriage_region_id"/>
        <result property="precarriageAddress" column="precarriage_address"/>
        <result property="precarriageTime" column="precarriage_time"/>
        <result property="precarriageContact" column="precarriage_contact"/>
        <result property="precarriageTel" column="precarriage_tel"/>
        <result property="precarriageRemark" column="precarriage_remark"/>
        <result property="dispatchRegionId" column="dispatch_region_id"/>
        <result property="dispatchAddress" column="dispatch_address"/>
        <result property="dispatchTime" column="dispatch_time"/>
        <result property="dispatchContact" column="dispatch_contact"/>
        <result property="dispatchTel" column="dispatch_tel"/>
        <result property="dispatchRemark" column="dispatch_remark"/>
        <result property="sqdWarehousingStatus" column="sqd_warehousing_status"/>
        <result property="sqdShippingBookingStatus" column="sqd_shipping_booking_status"/>
        <result property="sqdTrailerBookingStatus" column="sqd_trailer_booking_status"/>
        <result property="sqdContainerBookingStatus" column="sqd_container_booking_status"/>
        <result property="sqdContainerLoadingStatus" column="sqd_container_loading_status"/>
        <result property="sqdVesselArrangeStatus" column="sqd_vessel_arrange_status"/>
        <result property="sqdVgmStatus" column="sqd_vgm_status"/>
        <result property="sqdCustomDocsStatus" column="sqd_custom_docs_status"/>
        <result property="sqdCustomAuthorizedStatus" column="sqd_custom_authorized_status"/>
        <result property="sqdCustomExamineStatus" column="sqd_custom_examine_status"/>
        <result property="sqdCustomReleaseStatus" column="sqd_custom_release_status"/>
        <result property="sqdSiVerifyStatus" column="sqd_si_verify_status"/>
        <result property="sqdSiPostStatus" column="sqd_si_post_status"/>
        <result property="sqdAmsEnsPostStatus" column="sqd_ams_ens_post_status"/>
        <result property="sqdIsfEmnfPostStatus" column="sqd_isf_emnf_post_status"/>
        <result property="sqdMainServicePayingStatus" column="sqd_main_service_paying_status"/>
        <result property="blGettingStatus" column="bl_getting_status"/>
        <result property="blReleasingStatus" column="bl_releasing_status"/>
        <result property="sqdContainerNoSum" column="sqd_container_no_sum"/>
        <result property="sqdPolBookingAgent" column="sqd_pol_booking_agent"/>
        <result property="sqdPodHandleAgent" column="sqd_pod_handle_agent"/>
        <result property="sqdCarrierId" column="sqd_carrier_id"/>
        <result property="sqdDocDeliveryWay" column="sqd_doc_delivery_way"/>
        <result property="warehousingNo" column="warehousing_no"/>
        <result property="clientJobNo" column="client_job_no"/>
        <result property="bookingShipper" column="booking_shipper"/>
        <result property="bookingConsignee" column="booking_consignee"/>
        <result property="bookingNotifyParty" column="booking_notify_party"/>
        <result property="sqdInsuranceType" column="sqd_insurance_type"/>
        <result property="verifyOpLeaderId" column="verify_op_leader_id"/>
        <result property="opLeaderVerifyTime" column="op_leader_verify_time"/>
        <result property="destinationPort" column="destination_port"/>
        <result property="pol" column="pol"/>
        <result property="logisticsTypeEnName" column="logistics_type_en_name"/>
        <result property="carrierEnName" column="carrier_en_name"/>
        <result property="messageDisplay" column="message_display"/>
        <result property="serviceMessageFold" column="service_message_fold"/>
        <result property="orderBelongsTo" column="order_belongs_to"/>
        <result property="blTypeCode" column="bl_type_code"/>
        <result property="blFormCode" column="bl_form_code"/>
        <result property="noTransferAllowed" column="no_transfer_allowed"/>
        <result property="noDividedAllowed" column="no_divided_allowed"/>
        <result property="noAgreementShowed" column="no_agreement_showed"/>
        <result property="isCustomsIntransitShowed" column="is_customs_intransit_showed"/>
        <result property="psaVerifyStatusId" column="psa_verify_status_id"/>
        <result property="opLeaderVerifyStatusId" column="op_leader_verify_status_id"/>
        <result property="bookingAgent" column="booking_agent"/>
        <result property="supplierName" column="supplier_name"/>
        <result property="paymentNode" column="payment_node"/>
        <result property="precarriageSupplierNo" column="precarriage_supplier_no"/>
        <result property="agreementTypeCode" column="agreement_type_code"/>
        <result property="opAccept" column="op_accept"/>
        <result property="psaVerify" column="psa_verify"/>
        <result property="sqdCarrier" column="sqd_carrier"/>
        <result property="bookingAgentRemark" column="booking_agent_remark"/>
        <result property="bookingChargeRemark" column="booking_charge_remark"/>
        <result property="shippingMark" column="shipping_mark"/>
        <result property="polName" column="pol_name"/>
        <result property="podName" column="pod_name"/>
        <result property="freightPaidWayCode" column="freight_paid_way_code"/>
        <result property="ctnrTypeCode" column="ctnr_type_code"/>
        <result property="etd" column="etd"/>
        <result property="eta" column="eta"/>
        <result property="sqdDnReceiveSlipStatus"    column="sqd_dn_receive_slip_status"    />
        <result property="sqdDnPaySlipStatus"    column="sqd_dn_pay_slip_status"    />
        <result property="psaRctId"    column="psa_rct_id"    />
        <result property="sqdPsaNo"    column="sqd_psa_no"    />
        <result property="opAskingBlGetTime"    column="op_asking_bl_get_time"    />
        <result property="opAskingBlReleaseTime"    column="op_asking_bl_release_time"    />
        <result property="accPromissBlGetTime"    column="acc_promiss_bl_get_time"    />
        <result property="actualBlGotTime"    column="actual_bl_got_time"    />
        <result property="accPromissBlReleaseTime"    column="acc_promiss_bl_release_time"    />
        <result property="actualBlReleaseTime"    column="actual_bl_release_time"    />
        <result property="agentNoticeTime"    column="agent_notice_time"    />
        <result property="bookingStatus"    column="booking_status"    />
        <result property="secondVessel"    column="second_vessel"    />
        <result property="statusUpdateTime"    column="status_update_time"    />
        <result property="firstAtd"    column="first_atd"    />
        <result property="destinationPortAta"    column="destination_port_ata"    />
        <result property="quotationInRmb"    column="quotation_in_rmb"    />
        <result property="inquiryInRmb"    column="inquiry_in_rmb"    />
        <result property="estimatedProfitInRmb"    column="estimated_profit_in_rmb"    />
        <result property="dnUsd"    column="dn_usd"    />
        <result property="dnRmb"    column="dn_rmb"    />
        <result property="dnUsdBalance"    column="dn_usd_balance"    />
        <result property="dnRmbBalance"    column="dn_rmb_balance"    />
        <result property="dnInRmb"    column="dn_in_rmb"    />
        <result property="dnInRmbBalance"    column="dn_in_rmb_balance"    />
        <result property="cnUsd"    column="cn_usd"    />
        <result property="cnRmb"    column="cn_rmb"    />
        <result property="cnUsdBalance"    column="cn_usd_balance"    />
        <result property="cnRmbBalance"    column="cn_rmb_balance"    />
        <result property="cnInRmb"    column="cn_in_rmb"    />
        <result property="cnInRmbBalance"    column="cn_in_rmb_balance"    />
        <result property="profitUsd"    column="profit_usd"    />
        <result property="profitRmb"    column="profit_rmb"    />
        <result property="profitInRmb"    column="profit_in_rmb"    />
        <result property="differenceInRmb"    column="difference_in_rmb"    />
        <result property="profitRate"    column="profit_rate"    />
        <result property="statisticsSalesId"    column="statistics_sales_id"    />

        <collection property="bookingMessagesList"
                    resultMap="com.rich.system.mapper.RsBookingMessageMapper.RsBookingMessageResult"/>
    </resultMap>
    
    <resultMap id="StatisticsOpDTOResult" type="StatisticsOpDTO">
        <result property="opName"    column="op_name"    />
        <result property="number"    column="number"    />
    </resultMap>

    <resultMap id="RsRctListToExport" type="RsRctExportVO">
        <result property="deptName"    column="dept_name"    />
        <result property="rctNo"    column="rct_no"    />
        <result property="salesName"    column="sales_name"    />
        <result property="opName"    column="op_name"    />
        <result property="logisticsTypeName"    column="logistics_type_name"    />
        <result property="serviceType"    column="service_type"    />
        <result property="revenueTon"    column="revenue_ton"    />
        <result property="pol"    column="pol"    />
        <result property="destinationPort"    column="destination_port"    />
        <result property="rctCreateTime"    column="rct_create_time"    />
        <result property="etd"    column="etd"    />
    </resultMap>

    <sql id="selectRsRctVo">
        select rr.rct_id,
               rct_no,
               client_id,
               client_summary,
               client_role_id,
               client_contact,
               client_contact_tel,
               client_contact_email,
               relation_client_id_list,
               emergency_level,
               difficulty_level,
               rr.release_type,
               imp_exp_type,
               trading_terms,
               logistics_terms,
               trading_payment_channel,
               client_contract_no,
               client_invoice_no,
               cargo_type_code_sum,
               goods_name_summary,
               package_quantity,
               goods_volume,
               gross_weight,
               weight_unit_code,
               goods_currency_code,
               rr.freight_paid_way_code,
               goods_value,
               logistics_type_id,
               revenue_ton,
               pol_id,
               local_basic_port_id,
               transit_port_id,
               pod_id,
               destination_port_id,
               cv_closing_time,
               si_closing_time,
               first_vessel,
               first_voyage,
               first_cy_open_time,
               first_cy_closing_time,
               first_etd,
               basic_vessel,
               basic_voyage,
               basic_final_gatein_time,
               basic_etd,
               pod_eta,
               destination_port_eta,
               rr.carrier_id,
               inquiry_schedule_summary,
               service_type_id_list,
               so_no,
               bl_no,
               sqd_containers_seals_sum,
               sqd_export_customs_type,
               sqd_trailer_type,
               rct_process_status_summary,
               transport_status_summary,
               payment_receiving_status_summary,
               payment_paying_status_summary,
               transport_status_a,
               transport_status_b,
               doc_status_a,
               doc_status_b,
               process_status_id,
               process_status_time,
               qoutation_no,
               qoutation_sketch,
               sales_id,
               qoutation_time,
               new_booking_no,
               new_booking_remark,
               sales_assistant_id,
               sales_observer_id,
               new_booking_time,
               inquiry_notice_sum,
               inquiry_inner_remark_sum,
               verify_psa_id,
               psa_verify_time,
               op_leader_notice,
               op_inner_remark,
               op_id,
               booking_op_id,
               doc_op_id,
               op_observer_id,
               rct_create_time,
               rr.create_by,
               rr.create_time,
               rr.update_by,
               rr.update_time,
               rr.delete_by,
               rr.delete_time,
               rr.delete_status,
               precarriage_region_id,
               precarriage_address,
               precarriage_time,
               precarriage_contact,
               precarriage_tel,
               precarriage_remark,
               dispatch_region_id,
               dispatch_address,
               dispatch_time,
               dispatch_contact,
               dispatch_tel,
               dispatch_remark,
               sqd_warehousing_status,
               sqd_shipping_booking_status,
               sqd_trailer_booking_status,
               sqd_container_booking_status,
               sqd_container_loading_status,
               sqd_vessel_arrange_status,
               sqd_vgm_status,
               sqd_custom_docs_status,
               sqd_custom_authorized_status,
               sqd_custom_examine_status,
               sqd_custom_release_status,
               sqd_si_verify_status,
               sqd_si_post_status,
               sqd_ams_ens_post_status,
               sqd_isf_emnf_post_status,
               sqd_main_service_paying_status,
               sqd_container_no_sum,
               sqd_pol_booking_agent,
               sqd_pod_handle_agent,
               sqd_carrier_id,
               sqd_doc_delivery_way,
               warehousing_no,
               client_job_no,
               booking_shipper,
               booking_consignee,
               booking_notify_party,
               booking_agent,
               payment_node,
               sqd_insurance_type,
               verify_op_leader_id,
               op_leader_verify_time,
               sqd_unreceived_rmb_sum,
               sqd_unpaid_rmb_sum,
               message_display,
               service_message_fold,
               order_belongs_to,
               bl_type_code,
               bl_form_code,
               no_transfer_allowed,
               no_divided_allowed,
               no_agreement_showed,
               is_customs_intransit_showed,
               psa_verify_status_id,
               op_leader_verify_status_id,
               status_update_time,
               bdlt.service_short_name as logistics_type_en_name,
               (case
                    when bdlt.service_short_name = 'FCL' or bdlt.service_short_name = 'LCL' then concat(
                            dep.location_local_name, '(', ifnull(dep.port_code, ''),
                            ')')
                    when bdlt.service_short_name = 'RailFCL' or bdlt.service_short_name = 'RAILLCL' then concat(
                            dep.location_local_name, '(',
                            ifnull(dep.port_rail_code, ''), ')')
                    when bdlt.service_short_name = 'AirFCL' or bdlt.service_short_name = 'AIR' then concat(
                            dep.location_local_name, '(',
                            ifnull(dep.port_iata_code, ''), ')')
                    else dep.location_local_name
                   end
                   )                   as pol,
               (case
                    when bdlt.service_short_name = 'FCL' or bdlt.service_short_name = 'LCL' then concat(
                            dep2.location_en_name, '(', ifnull(dep2.port_code, ''),
                            ')')
                    when bdlt.service_short_name = 'RailFCL' or bdlt.service_short_name = 'RailLCL' then concat(
                            dep2.location_en_name, '(',
                            ifnull(dep2.port_rail_code, ''), ')')
                    when bdlt.service_short_name = 'AirFCL' or bdlt.service_short_name = 'AIR' then concat(
                            dep2.location_en_name, '(',
                            ifnull(dep2.port_iata_code, ''), ')')
                    else dep2.location_en_name
                   end
                   )                   as destination_port,
               bc.carrier_short_name   as carrier_en_name,
               ifnull(ec.company_short_name,ec.company_en_short_name)   as supplier_name,
               precarriage_supplier_no,
               rr.agreement_type_code,
               rr.op_accept,
               rr.psa_verify,
               sqd_carrier,
               rr.booking_agent_remark,
               rr.shipping_mark,
            if(sales_assistant_id is null,sales_id,sales_assistant_id) as statistics_sales_id,
            rr.profit_in_rmb/rr.dn_in_rmb as profit_rate,
               (case
                    when bdlt.service_short_name = 'FCL' or bdlt.service_short_name = 'LCL' then concat(
                            dep.location_en_name, '(', ifnull(dep.port_code, ''),
                            ')')
                    when bdlt.service_short_name = 'RailFCL' or bdlt.service_short_name = 'RAILLCL' then concat(
                            dep.location_en_name, '(',
                            ifnull(dep.port_rail_code, ''), ')')
                    when bdlt.service_short_name = 'AirFCL' or bdlt.service_short_name = 'AIR' then concat(
                            dep.location_en_name, '(',
                            ifnull(dep.port_iata_code, ''), ')')
                    else dep.location_en_name
                   end
                   )                   as pol_name,
               (case
                    when bdlt.service_short_name = 'FCL' or bdlt.service_short_name = 'LCL' then concat(
                            dep3.location_en_name, '(', ifnull(dep3.port_code, ''),
                            ')')
                    when bdlt.service_short_name = 'RailFCL' or bdlt.service_short_name = 'RAILLCL' then concat(
                            dep3.location_en_name, '(',
                            ifnull(dep3.port_rail_code, ''), ')')
                    when bdlt.service_short_name = 'AirFCL' or bdlt.service_short_name = 'AIR' then concat(
                            dep3.location_en_name, '(',
                            ifnull(dep3.port_iata_code, ''), ')')
                    else dep3.location_en_name
                   end
                   )                   as pod_name,
               ctnr_type_code,
               etd,
               eta,
               sqd_dn_receive_slip_status,
               sqd_dn_pay_slip_status,
               psa_rct_id,
               quotation_in_rmb, inquiry_in_rmb, estimated_profit_in_rmb, dn_usd, dn_rmb, dn_usd_balance, dn_rmb_balance, dn_in_rmb, dn_in_rmb_balance, cn_usd, cn_rmb, cn_usd_balance, cn_rmb_balance, cn_in_rmb, cn_in_rmb_balance, profit_usd, profit_rmb, profit_in_rmb, difference_in_rmb,
               sqd_psa_no,rr.second_vessel
               op_asking_bl_get_time, op_asking_bl_release_time, acc_promiss_bl_get_time, actual_bl_got_time, acc_promiss_bl_release_time, actual_bl_release_time, agent_notice_time, booking_status
        from rs_rct rr
                 left join bas_dist_service_type bdlt on rr.logistics_type_id = bdlt.service_type_id
                 left join bas_dist_location dep on rr.pol_id = dep.location_id
                 left join bas_dist_location dep2 on rr.destination_port_id = dep2.location_id
                 left join bas_dist_location dep3 on rr.pod_id = dep3.location_id
                 left join bas_carrier bc on bc.carrier_id = rr.carrier_id
                 left join rich.ext_company ec on ec.company_id = rr.sqd_pol_booking_agent
    </sql>

    <select id="selectRsRctList" parameterType="RsRct" resultMap="RsRctResult">
        <include refid="selectRsRctVo"/>
        <where>
            <if test="rctNo != null  and rctNo != ''">
                and rct_no = #{rctNo}
            </if>
            <if test="precarriageSupplierNo != null">
                and precarriage_supplier_no = #{precarriageSupplierNo}
            </if>
            <if test="clientId != null ">
                and client_id = #{clientId}
            </if>
            <if test="clientSummary != null ">
                and client_summary = #{clientSummary}
            </if>
            <if test="clientRoleId != null ">
                and client_role_id = #{clientRoleId}
            </if>
            <if test="clientContact != null  and clientContact != ''">
                and client_contact = #{clientContact}
            </if>
            <if test="clientContactTel != null  and clientContactTel != ''">
                and client_contact_tel = #{clientContactTel}
            </if>
            <if test="clientContactEmail != null  and clientContactEmail != ''">
                and client_contact_email = #{clientContactEmail}
            </if>
            <if test="relationClientIdList != null  and relationClientIdList != ''">
                and relation_client_id_list = #{relationClientIdList}
            </if>
            <if test="emergencyLevel != null  and emergencyLevel != ''">
                and emergency_level = #{emergencyLevel}
            </if>
            <if test="difficultyLevel != null  and difficultyLevel != ''">
                and difficulty_level = #{difficultyLevel}
            </if>
            <if test="releaseType != null  and releaseType != ''">
                and release_type = #{releaseType}
            </if>
            <if test="impExpType != null  and impExpType != ''">
                and imp_exp_type = #{impExpType}
            </if>
            <if test="tradingTerms != null  and tradingTerms != ''">
                and trading_terms = #{tradingTerms}
            </if>
            <if test="logisticsTerms != null  and logisticsTerms != ''">
                and logistics_terms = #{logisticsTerms}
            </if>
            <if test="tradingPaymentChannel != null  and tradingPaymentChannel != ''">
                and trading_payment_channel = #{tradingPaymentChannel}
            </if>
            <if test="clientContractNo != null  and clientContractNo != ''">
                and client_contract_no = #{clientContractNo}
            </if>
            <if test="clientInvoiceNo != null  and clientInvoiceNo != ''">
                and client_invoice_no = #{clientInvoiceNo}
            </if>
            <if test="cargoTypeCodeSum != null  and cargoTypeCodeSum != ''">
                and cargo_type_code_sum = #{cargoTypeCodeSum}
            </if>
            <if test="goodsNameSummary != null  and goodsNameSummary != ''">
                and goods_name_summary = #{goodsNameSummary}
            </if>
            <if test="packageQuantity != null ">
                and package_quantity = #{packageQuantity}
            </if>
            <if test="goodsVolume != null ">
                and goods_volume = #{goodsVolume}
            </if>
            <if test="grossWeight != null ">
                and gross_weight = #{grossWeight}
            </if>
            <if test="weightUnitCode != null  and weightUnitCode != ''">
                and weight_unit_code = #{weightUnitCode}
            </if>
            <if test="goodsCurrencyCode != null  and goodsCurrencyCode != ''">
                and goods_currency_code = #{goodsCurrencyCode}
            </if>
            <if test="goodsValue != null ">
                and goods_value = #{goodsValue}
            </if>
            <if test="logisticsTypeId != null ">
                and rr.logistics_type_id in (SELECT t2.service_type_id FROM(SELECT @r AS
                _id,( SELECT @r := parent_id FROM
                bas_dist_service_type WHERE service_type_id = _id ) AS parent_id,@l := @l + 1 AS lvl FROM
                ( SELECT @r := #{logisticsTypeId}, @l := 0 ) vars,bas_dist_service_type AS h WHERE @r != 0) t1
                JOIN bas_dist_service_type t2 ON t1._id = t2.service_type_id or t2.service_type_id in (select
                service_type_id from bas_dist_service_type where service_type_id = #{logisticsTypeId} or
                find_in_set(#{logisticsTypeId},ancestors)))
            </if>
            <if test="revenueTon != null  and revenueTon != ''">
                and revenue_ton  LIKE CONCAT('%', #{revenueTon},'%')
            </if>
            <if test="polId != null ">
                and pol_id = #{polId}
            </if>
            <if test="localBasicPortId != null ">
                and local_basic_port_id = #{localBasicPortId}
            </if>
            <if test="transitPortId != null ">
                and transit_port_id = #{transitPortId}
            </if>
            <if test="destinationPortId != null ">
                and destination_port_id = #{destinationPortId}
            </if>
            <if test="cvClosingTime != null ">
                and cv_closing_time = #{cvClosingTime}
            </if>
            <if test="siClosingTime != null ">
                and si_closing_time = #{siClosingTime}
            </if>
            <if test="firstVessel != null  and firstVessel != ''">
                and first_vessel = #{firstVessel}
            </if>
            <if test="firstVoyage != null  and firstVoyage != ''">
                and first_voyage = #{firstVoyage}
            </if>
            <if test="firstCyOpenTime != null ">
                and first_cy_open_time = #{firstCyOpenTime}
            </if>
            <if test="firstCyClosingTime != null ">
                and first_cy_closing_time = #{firstCyClosingTime}
            </if>
            <if test="firstEtd != null ">
                and first_etd = #{firstEtd}
            </if>
            <if test="basicVessel != null  and basicVessel != ''">
                and basic_vessel = #{basicVessel}
            </if>
            <if test="basicVoyage != null  and basicVoyage != ''">
                and basic_voyage = #{basicVoyage}
            </if>
            <if test="basicFinalGateinTime != null ">
                and basic_final_gatein_time = #{basicFinalGateinTime}
            </if>
            <if test="basicEtd != null ">
                and basic_etd = #{basicEtd}
            </if>
            <if test="podEta != null ">
                and pod_eta = #{podEta}
            </if>
            <if test="destinationPortEta != null ">
                and destination_port_eta = #{destinationPortEta}
            </if>
            <if test="carrierId != null ">
                and rr.carrier_id = #{carrierId}
            </if>
            <if test="inquiryScheduleSummary != null  and inquiryScheduleSummary != ''">
                and inquiry_schedule_summary = #{inquiryScheduleSummary}
            </if>
            <if test="serviceTypeIdList != null  and serviceTypeIdList != ''">
                and service_type_id_list = #{serviceTypeIdList}
            </if>
            <if test="soNo != null  and soNo != ''">
                and so_no = #{soNo}
            </if>
            <if test="blNo != null  and blNo != ''">
                and bl_no = #{blNo}
            </if>
            <if test="sqdContainersSealsSum != null  and sqdContainersSealsSum != ''">
                and sqd_containers_seals_sum = #{sqdContainersSealsSum}
            </if>
            <if test="sqdExportCustomsType != null  and sqdExportCustomsType != ''">
                and sqd_export_customs_type = #{sqdExportCustomsType}
            </if>
            <if test="sqdTrailerType != null  and sqdTrailerType != ''">
                and sqd_trailer_type = #{sqdTrailerType}
            </if>
            <if test="rctProcessStatusSummary != null  and rctProcessStatusSummary != ''">
                and rct_process_status_summary = #{rctProcessStatusSummary}
            </if>
            <if test="transportStatusSummary != null  and transportStatusSummary != ''">
                and transport_status_summary = #{transportStatusSummary}
            </if>
            <if test="paymentReceivingStatusSummary != null  and paymentReceivingStatusSummary != ''">
                and payment_receiving_status_summary = #{paymentReceivingStatusSummary}
            </if>
            <if test="paymentPayingStatusSummary != null  and paymentPayingStatusSummary != ''">
                and payment_paying_status_summary = #{paymentPayingStatusSummary}
            </if>
            <if test="transportStatusA != null  and transportStatusA != ''">
                and transport_status_a = #{transportStatusA}
            </if>
            <if test="transportStatusB != null  and transportStatusB != ''">
                and transport_status_b = #{transportStatusB}
            </if>
            <if test="docStatusA != null  and docStatusA != ''">
                and doc_status_a = #{docStatusA}
            </if>
            <if test="docStatusB != null  and docStatusB != ''">
                and doc_status_b = #{docStatusB}
            </if>
            <if test="processStatusId != null ">
                and process_status_id = #{processStatusId}
            </if>
            <if test="processStatusId == null ">
                and process_status_id != 8
            </if>
            <if test="processStatusTime != null ">
                and process_status_time = #{processStatusTime}
            </if>
            <if test="qoutationNo != null  and qoutationNo != ''">
                and qoutation_no = #{qoutationNo}
            </if>
            <if test="qoutationSketch != null  and qoutationSketch != ''">
                and qoutation_sketch = #{qoutationSketch}
            </if>
            <!--业务id-->
            <if test="salesId != null ">
                and sales_id = #{salesId}
            </if>
            <if test="qoutationTime != null ">
                and qoutation_time = #{qoutationTime}
            </if>
            <if test="newBookingNo != null  and newBookingNo != ''">
                and new_booking_no = #{newBookingNo}
            </if>
            <if test="newBookingRemark != null  and newBookingRemark != ''">
                and new_booking_remark = #{newBookingRemark}
            </if>
            <if test="salesAssistantId != null ">
                and sales_assistant_id = #{salesAssistantId}
            </if>
            <if test="salesObserverId != null ">
                and sales_observer_id = #{salesObserverId}
            </if>
            <if test="newBookingTime != null ">
                and new_booking_time = #{newBookingTime}
            </if>
            <if test="inquiryNoticeSum != null  and inquiryNoticeSum != ''">
                and inquiry_notice_sum = #{inquiryNoticeSum}
            </if>
            <if test="inquiryInnerRemarkSum != null  and inquiryInnerRemarkSum != ''">
                and inquiry_inner_remark_sum = #{inquiryInnerRemarkSum}
            </if>
            <if test="verifyPsaId != null ">
                and verify_psa_id = #{verifyPsaId}
            </if>
            <if test="psaVerifyTime != null ">
                and psa_verify_time = #{psaVerifyTime}
            </if>
            <if test="opLeaderNotice != null  and opLeaderNotice != ''">
                and op_leader_notice = #{opLeaderNotice}
            </if>
            <if test="opInnerRemark != null  and opInnerRemark != ''">
                and op_inner_remark = #{opInnerRemark}
            </if>
            <if test="opId != null ">
                and op_id = #{opId}
            </if>
            <if test="bookingOpId != null ">
                and booking_op_id = #{bookingOpId}
            </if>
            <if test="docOpId != null ">
                and doc_op_id = #{docOpId}
            </if>
            <if test="opObserverId != null ">
                and op_observer_id = #{opObserverId}
            </if>
            <if test="rctCreateTime != null ">
                and rct_create_time = #{rctCreateTime}
            </if>
            <if test="deleteBy != null ">
                and delete_by = #{deleteBy}
            </if>
            <if test="deleteTime != null ">
                and delete_time = #{deleteTime}
            </if>
            <if test="deleteStatus != null  and deleteStatus != ''">
                and rr.delete_status = #{deleteStatus}
            </if>
            <if test="precarriageRegionId != null ">
                and precarriage_region_id = #{precarriageRegionId}
            </if>
            <if test="precarriageAddress != null  and precarriageAddress != ''">
                and precarriage_address = #{precarriageAddress}
            </if>
            <if test="precarriageTime != null ">
                and precarriage_time = #{precarriageTime}
            </if>
            <if test="precarriageContact != null  and precarriageContact != ''">
                and precarriage_contact = #{precarriageContact}
            </if>
            <if test="precarriageTel != null  and precarriageTel != ''">
                and precarriage_tel = #{precarriageTel}
            </if>
            <if test="precarriageRemark != null  and precarriageRemark != ''">
                and precarriage_remark = #{precarriageRemark}
            </if>
            <if test="dispatchRegionId != null ">
                and dispatch_region_id = #{dispatchRegionId}
            </if>
            <if test="dispatchAddress != null  and dispatchAddress != ''">
                and dispatch_address = #{dispatchAddress}
            </if>
            <if test="dispatchTime != null ">
                and dispatch_time = #{dispatchTime}
            </if>
            <if test="dispatchContact != null  and dispatchContact != ''">
                and dispatch_contact = #{dispatchContact}
            </if>
            <if test="dispatchTel != null  and dispatchTel != ''">
                and dispatch_tel = #{dispatchTel}
            </if>
            <if test="dispatchRemark != null  and dispatchRemark != ''">
                and dispatch_remark = #{dispatchRemark}
            </if>
            <if test="sqdWarehousingStatus != null  and sqdWarehousingStatus != ''">
                and sqd_warehousing_status = #{sqdWarehousingStatus}
            </if>
            <if test="sqdShippingBookingStatus != null  and sqdShippingBookingStatus != ''">
                and sqd_shipping_booking_status = #{sqdShippingBookingStatus}
            </if>
            <if test="sqdTrailerBookingStatus != null  and sqdTrailerBookingStatus != ''">
                and sqd_trailer_booking_status = #{sqdTrailerBookingStatus}
            </if>
            <if test="sqdContainerBookingStatus != null  and sqdContainerBookingStatus != ''">
                and sqd_container_booking_status = #{sqdContainerBookingStatus}
            </if>
            <if test="sqdContainerLoadingStatus != null  and sqdContainerLoadingStatus != ''">
                and sqd_container_loading_status = #{sqdContainerLoadingStatus}
            </if>
            <if test="sqdVesselArrangeStatus != null  and sqdVesselArrangeStatus != ''">
                and sqd_vessel_arrange_status = #{sqdVesselArrangeStatus}
            </if>
            <if test="sqdVgmStatus != null  and sqdVgmStatus != ''">
                and sqd_vgm_status = #{sqdVgmStatus}
            </if>
            <if test="sqdCustomDocsStatus != null  and sqdCustomDocsStatus != ''">
                and sqd_custom_docs_status = #{sqdCustomDocsStatus}
            </if>
            <if test="sqdCustomAuthorizedStatus != null  and sqdCustomAuthorizedStatus != ''">
                and sqd_custom_authorized_status = #{sqdCustomAuthorizedStatus}
            </if>
            <if test="sqdCustomExamineStatus != null  and sqdCustomExamineStatus != ''">
                and sqd_custom_examine_status = #{sqdCustomExamineStatus}
            </if>
            <if test="sqdCustomReleaseStatus != null  and sqdCustomReleaseStatus != ''">
                and sqd_custom_release_status = #{sqdCustomReleaseStatus}
            </if>
            <if test="sqdSiVerifyStatus != null  and sqdSiVerifyStatus != ''">
                and sqd_si_verify_status = #{sqdSiVerifyStatus}
            </if>
            <if test="sqdSiPostStatus != null  and sqdSiPostStatus != ''">
                and sqd_si_post_status = #{sqdSiPostStatus}
            </if>
            <if test="sqdAmsEnsPostStatus != null  and sqdAmsEnsPostStatus != ''">
                and sqd_ams_ens_post_status = #{sqdAmsEnsPostStatus}
            </if>
            <if test="sqdIsfEmnfPostStatus != null  and sqdIsfEmnfPostStatus != ''">
                and sqd_isf_emnf_post_status = #{sqdIsfEmnfPostStatus}
            </if>
            <if test="sqdMainServicePayingStatus != null  and sqdMainServicePayingStatus != ''">
                and sqd_main_service_paying_status = #{sqdMainServicePayingStatus}
            </if>
            <if test="blGettingStatus != null  and blGettingStatus != ''">
                and bl_getting_status = #{blGettingStatus}
            </if>
            <if test="blReleasingStatus != null  and blReleasingStatus != ''">
                and bl_releasing_status = #{blReleasingStatus}
            </if>
            <if test="sqdContainerNoSum != null  and sqdContainerNoSum != ''">
                and sqd_container_no_sum = #{sqdContainerNoSum}
            </if>
            <if test="sqdPolBookingAgent != null ">
                and sqd_pol_booking_agent = #{sqdPolBookingAgent}
            </if>
            <if test="sqdPodHandleAgent != null ">
                and sqd_pod_handle_agent = #{sqdPodHandleAgent}
            </if>
            <if test="sqdCarrierId != null ">
                and sqd_carrier_id = #{sqdCarrierId}
            </if>
            <if test="sqdDocDeliveryWay != null  and sqdDocDeliveryWay != ''">
                and sqd_doc_delivery_way = #{sqdDocDeliveryWay}
            </if>
            <if test="warehousingNo != null  and warehousingNo != ''">
                and warehousing_no = #{warehousingNo}
            </if>
            <!--search-->
            <if test="polIds != null">
               and rr.pol_id in
                <foreach item="polId" collection="polIds" open="(" separator="," close=")">
                    #{polId}
                </foreach>
            </if>
            <if test="destinationPortIds != null">
              and  rr.destination_port_id in
                <foreach item="destinationPortId" collection="destinationPortIds" open="(" separator="," close=")">
                    #{destinationPortId}
                </foreach>
            </if>
            <if test="rctIds != null ">
                and rr.rct_id in
                <foreach item="rctId" collection="rctIds" open="(" separator="," close=")">
                    #{rctId}
                </foreach>
            </if>
            <if test="orderBelongsTo != null">
                and order_belongs_to = #{orderBelongsTo}
            </if>
            <if test="blTypeCode != null">
                and bl_type_code = #{blTypeCode}
            </if>
            <if test="blFormCode != null">
                and bl_form_code = #{blFormCode}
            </if>
            <if test="noTransferAllowed != null  and noTransferAllowed != ''">
                and no_transfer_allowed = #{noTransferAllowed}
            </if>
            <if test="noDividedAllowed != null  and noDividedAllowed != ''">
                and no_divided_allowed = #{noDividedAllowed}
            </if>
            <if test="noAgreementShowed != null  and noAgreementShowed != ''">
                and no_agreement_showed = #{noAgreementShowed}
            </if>
            <if test="isCustomsIntransitShowed != null  and isCustomsIntransitShowed != ''">
                and is_customs_intransit_showed = #{isCustomsIntransitShowed}
            </if>
            <if test="rctOpDate != null and rctOpDate.length == 2">
                and rct_create_time BETWEEN #{rctOpDate[0],javaType=java.util.Date,jdbcType=TIMESTAMP} AND #{rctOpDate[1],javaType=java.util.Date,jdbcType=TIMESTAMP}
            </if>
            <if test="ATDDate != null  and ATDDate.length == 2">
                and pod_eta BETWEEN #{ATDDate[0],javaType=java.util.Date,jdbcType=TIMESTAMP} AND #{ATDDate[1],javaType=java.util.Date,jdbcType=TIMESTAMP}
            </if>
            <if test="ETDDate != null  and ETDDate.length == 2">
                and etd BETWEEN #{ETDDate[0],javaType=java.util.Date,jdbcType=TIMESTAMP} AND #{ETDDate[1],javaType=java.util.Date,jdbcType=TIMESTAMP}
            </if>
            <!--未收款不等于0-->
            <if test="params != null and params.closedAccount == 'true'">
                and  (dn_usd_balance  != 0 or dn_rmb_balance != 0 )
            </if>
            and rr.delete_status = '0' and rr.psa_verify_status_id = 1
        </where>
        ORDER BY
        op_accept ASC,
        rct_create_time DESC,
        psa_verify ASC,
        psa_verify_time DESC,
        sqd_shipping_booking_status ASC,
        new_booking_time DESC
    </select>

    <select id="selectUnVerifyRsRctList" parameterType="RsRct" resultMap="RsRctResult">
        <include refid="selectRsRctVo"/>
        <where>
            rr.delete_status = '0'
            <if test="precarriageSupplierNo != null">
                and precarriage_supplier_no = #{precarriageSupplierNo}
            </if>
            <if test="rctNo != null  and rctNo != ''">
                and rct_no = #{rctNo}
            </if>
            <if test="clientId != null ">
                and client_id = #{clientId}
            </if>
            <if test="clientSummary != null ">
                and client_summary = #{clientSummary}
            </if>
            <if test="clientRoleId != null ">
                and client_role_id = #{clientRoleId}
            </if>
            <if test="clientContact != null  and clientContact != ''">
                and client_contact = #{clientContact}
            </if>
            <if test="clientContactTel != null  and clientContactTel != ''">
                and client_contact_tel = #{clientContactTel}
            </if>
            <if test="clientContactEmail != null  and clientContactEmail != ''">
                and client_contact_email = #{clientContactEmail}
            </if>
            <if test="relationClientIdList != null  and relationClientIdList != ''">
                and relation_client_id_list = #{relationClientIdList}
            </if>
            <if test="emergencyLevel != null  and emergencyLevel != ''">
                and emergency_level = #{emergencyLevel}
            </if>
            <if test="difficultyLevel != null  and difficultyLevel != ''">
                and difficulty_level = #{difficultyLevel}
            </if>
            <if test="releaseType != null  and releaseType != ''">
                and release_type = #{releaseType}
            </if>
            <if test="impExpType != null  and impExpType != ''">
                and imp_exp_type = #{impExpType}
            </if>
            <if test="tradingTerms != null  and tradingTerms != ''">
                and trading_terms = #{tradingTerms}
            </if>
            <if test="logisticsTerms != null  and logisticsTerms != ''">
                and logistics_terms = #{logisticsTerms}
            </if>
            <if test="tradingPaymentChannel != null  and tradingPaymentChannel != ''">
                and trading_payment_channel = #{tradingPaymentChannel}
            </if>
            <if test="clientContractNo != null  and clientContractNo != ''">
                and client_contract_no = #{clientContractNo}
            </if>
            <if test="clientInvoiceNo != null  and clientInvoiceNo != ''">
                and client_invoice_no = #{clientInvoiceNo}
            </if>
            <if test="cargoTypeCodeSum != null  and cargoTypeCodeSum != ''">
                and cargo_type_code_sum = #{cargoTypeCodeSum}
            </if>
            <if test="goodsNameSummary != null  and goodsNameSummary != ''">
                and goods_name_summary = #{goodsNameSummary}
            </if>
            <if test="packageQuantity != null ">
                and package_quantity = #{packageQuantity}
            </if>
            <if test="goodsVolume != null ">
                and goods_volume = #{goodsVolume}
            </if>
            <if test="grossWeight != null ">
                and gross_weight = #{grossWeight}
            </if>
            <if test="weightUnitCode != null  and weightUnitCode != ''">
                and weight_unit_code = #{weightUnitCode}
            </if>
            <if test="goodsCurrencyCode != null  and goodsCurrencyCode != ''">
                and goods_currency_code = #{goodsCurrencyCode}
            </if>
            <if test="goodsValue != null ">
                and goods_value = #{goodsValue}
            </if>
            <if test="logisticsTypeId != null ">
                and rr.logistics_type_id in (SELECT t2.service_type_id FROM(SELECT @r AS
                _id,( SELECT @r := parent_id FROM
                bas_dist_service_type WHERE service_type_id = _id ) AS parent_id,@l := @l + 1 AS lvl FROM
                ( SELECT @r := #{logisticsTypeId}, @l := 0 ) vars,bas_dist_service_type AS h WHERE @r != 0) t1
                JOIN bas_dist_service_type t2 ON t1._id = t2.service_type_id or t2.service_type_id in (select
                service_type_id from bas_dist_service_type where service_type_id = #{logisticsTypeId} or
                find_in_set(#{logisticsTypeId},ancestors)))
            </if>
            <if test="revenueTon != null  and revenueTon != ''">
                and revenue_ton = #{revenueTon}
            </if>
            <if test="polId != null ">
                and pol_id = #{polId}
            </if>
            <if test="localBasicPortId != null ">
                and local_basic_port_id = #{localBasicPortId}
            </if>
            <if test="transitPortId != null ">
                and transit_port_id = #{transitPortId}
            </if>
            <if test="podId != null ">
                and pod_id = #{podId}
            </if>
            <if test="destinationPortId != null ">
                and destination_port_id = #{destinationPortId}
            </if>
            <if test="cvClosingTime != null ">
                and cv_closing_time = #{cvClosingTime}
            </if>
            <if test="siClosingTime != null ">
                and si_closing_time = #{siClosingTime}
            </if>
            <if test="firstVessel != null  and firstVessel != ''">
                and first_vessel = #{firstVessel}
            </if>
            <if test="firstVoyage != null  and firstVoyage != ''">
                and first_voyage = #{firstVoyage}
            </if>
            <if test="firstCyOpenTime != null ">
                and first_cy_open_time = #{firstCyOpenTime}
            </if>
            <if test="firstCyClosingTime != null ">
                and first_cy_closing_time = #{firstCyClosingTime}
            </if>
            <if test="firstEtd != null ">
                and first_etd = #{firstEtd}
            </if>
            <if test="basicVessel != null  and basicVessel != ''">
                and basic_vessel = #{basicVessel}
            </if>
            <if test="basicVoyage != null  and basicVoyage != ''">
                and basic_voyage = #{basicVoyage}
            </if>
            <if test="basicFinalGateinTime != null ">
                and basic_final_gatein_time = #{basicFinalGateinTime}
            </if>
            <if test="basicEtd != null ">
                and basic_etd = #{basicEtd}
            </if>
            <if test="podEta != null ">
                and pod_eta = #{podEta}
            </if>
            <if test="destinationPortEta != null ">
                and destination_port_eta = #{destinationPortEta}
            </if>
            <if test="carrierId != null ">
                and rr.carrier_id = #{carrierId}
            </if>
            <if test="inquiryScheduleSummary != null  and inquiryScheduleSummary != ''">
                and inquiry_schedule_summary = #{inquiryScheduleSummary}
            </if>
            <if test="serviceTypeIdList != null  and serviceTypeIdList != ''">
                and service_type_id_list = #{serviceTypeIdList}
            </if>
            <if test="soNo != null  and soNo != ''">
                and so_no = #{soNo}
            </if>
            <if test="blNo != null  and blNo != ''">
                and bl_no = #{blNo}
            </if>
            <if test="sqdContainersSealsSum != null  and sqdContainersSealsSum != ''">
                and sqd_containers_seals_sum = #{sqdContainersSealsSum}
            </if>
            <if test="sqdExportCustomsType != null  and sqdExportCustomsType != ''">
                and sqd_export_customs_type = #{sqdExportCustomsType}
            </if>
            <if test="sqdTrailerType != null  and sqdTrailerType != ''">
                and sqd_trailer_type = #{sqdTrailerType}
            </if>
            <if test="rctProcessStatusSummary != null  and rctProcessStatusSummary != ''">
                and rct_process_status_summary = #{rctProcessStatusSummary}
            </if>
            <if test="transportStatusSummary != null  and transportStatusSummary != ''">
                and transport_status_summary = #{transportStatusSummary}
            </if>
            <if test="paymentReceivingStatusSummary != null  and paymentReceivingStatusSummary != ''">
                and payment_receiving_status_summary = #{paymentReceivingStatusSummary}
            </if>
            <if test="paymentPayingStatusSummary != null  and paymentPayingStatusSummary != ''">
                and payment_paying_status_summary = #{paymentPayingStatusSummary}
            </if>
            <if test="transportStatusA != null  and transportStatusA != ''">
                and transport_status_a = #{transportStatusA}
            </if>
            <if test="transportStatusB != null  and transportStatusB != ''">
                and transport_status_b = #{transportStatusB}
            </if>
            <if test="docStatusA != null  and docStatusA != ''">
                and doc_status_a = #{docStatusA}
            </if>
            <if test="docStatusB != null  and docStatusB != ''">
                and doc_status_b = #{docStatusB}
            </if>
            <if test="processStatusId != null ">
                and process_status_id = #{processStatusId}
            </if>
            <if test="processStatusId == null ">
                and process_status_id != 8
            </if>
            <if test="processStatusTime != null ">
                and process_status_time = #{processStatusTime}
            </if>
            <if test="qoutationNo != null  and qoutationNo != ''">
                and qoutation_no = #{qoutationNo}
            </if>
            <if test="qoutationSketch != null  and qoutationSketch != ''">
                and qoutation_sketch = #{qoutationSketch}
            </if>
            <if test="qoutationTime != null ">
                and qoutation_time = #{qoutationTime}
            </if>
            <if test="newBookingNo != null  and newBookingNo != ''">
                and new_booking_no = #{newBookingNo}
            </if>
            <if test="newBookingRemark != null  and newBookingRemark != ''">
                and new_booking_remark = #{newBookingRemark}
            </if>
            <if test="salesObserverId != null ">
                and sales_observer_id = #{salesObserverId}
            </if>
            <if test="newBookingTime != null ">
                and new_booking_time = #{newBookingTime}
            </if>
            <if test="inquiryNoticeSum != null  and inquiryNoticeSum != ''">
                and inquiry_notice_sum = #{inquiryNoticeSum}
            </if>
            <if test="inquiryInnerRemarkSum != null  and inquiryInnerRemarkSum != ''">
                and inquiry_inner_remark_sum = #{inquiryInnerRemarkSum}
            </if>
            <if test="psaVerifyTime != null ">
                and psa_verify_time = #{psaVerifyTime}
            </if>
            <if test="opLeaderNotice != null  and opLeaderNotice != ''">
                and op_leader_notice = #{opLeaderNotice}
            </if>
            <if test="opInnerRemark != null  and opInnerRemark != ''">
                and op_inner_remark = #{opInnerRemark}
            </if>
            <if test="opId != null ">
                and op_id = #{opId}
            </if>
            <if test="bookingOpId != null ">
                and booking_op_id = #{bookingOpId}
            </if>
            <if test="docOpId != null ">
                and doc_op_id = #{docOpId}
            </if>
            <if test="opObserverId != null ">
                and op_observer_id = #{opObserverId}
            </if>
            <if test="rctCreateTime != null ">
                and rct_create_time = #{rctCreateTime}
            </if>
            <if test="deleteBy != null ">
                and delete_by = #{deleteBy}
            </if>
            <if test="deleteTime != null ">
                and delete_time = #{deleteTime}
            </if>
            <if test="deleteStatus != null  and deleteStatus != ''">
                and rr.delete_status = #{deleteStatus}
            </if>
            <if test="precarriageRegionId != null ">
                and precarriage_region_id = #{precarriageRegionId}
            </if>
            <if test="precarriageAddress != null  and precarriageAddress != ''">
                and precarriage_address = #{precarriageAddress}
            </if>
            <if test="precarriageTime != null ">
                and precarriage_time = #{precarriageTime}
            </if>
            <if test="precarriageContact != null  and precarriageContact != ''">
                and precarriage_contact = #{precarriageContact}
            </if>
            <if test="precarriageTel != null  and precarriageTel != ''">
                and precarriage_tel = #{precarriageTel}
            </if>
            <if test="precarriageRemark != null  and precarriageRemark != ''">
                and precarriage_remark = #{precarriageRemark}
            </if>
            <if test="dispatchRegionId != null ">
                and dispatch_region_id = #{dispatchRegionId}
            </if>
            <if test="dispatchAddress != null  and dispatchAddress != ''">
                and dispatch_address = #{dispatchAddress}
            </if>
            <if test="dispatchTime != null ">
                and dispatch_time = #{dispatchTime}
            </if>
            <if test="dispatchContact != null  and dispatchContact != ''">
                and dispatch_contact = #{dispatchContact}
            </if>
            <if test="dispatchTel != null  and dispatchTel != ''">
                and dispatch_tel = #{dispatchTel}
            </if>
            <if test="dispatchRemark != null  and dispatchRemark != ''">
                and dispatch_remark = #{dispatchRemark}
            </if>
            <if test="sqdWarehousingStatus != null  and sqdWarehousingStatus != ''">
                and sqd_warehousing_status = #{sqdWarehousingStatus}
            </if>
            <if test="sqdShippingBookingStatus != null  and sqdShippingBookingStatus != ''">
                and sqd_shipping_booking_status = #{sqdShippingBookingStatus}
            </if>
            <if test="sqdTrailerBookingStatus != null  and sqdTrailerBookingStatus != ''">
                and sqd_trailer_booking_status = #{sqdTrailerBookingStatus}
            </if>
            <if test="sqdContainerBookingStatus != null  and sqdContainerBookingStatus != ''">
                and sqd_container_booking_status = #{sqdContainerBookingStatus}
            </if>
            <if test="sqdContainerLoadingStatus != null  and sqdContainerLoadingStatus != ''">
                and sqd_container_loading_status = #{sqdContainerLoadingStatus}
            </if>
            <if test="sqdVesselArrangeStatus != null  and sqdVesselArrangeStatus != ''">
                and sqd_vessel_arrange_status = #{sqdVesselArrangeStatus}
            </if>
            <if test="sqdVgmStatus != null  and sqdVgmStatus != ''">
                and sqd_vgm_status = #{sqdVgmStatus}
            </if>
            <if test="sqdCustomDocsStatus != null  and sqdCustomDocsStatus != ''">
                and sqd_custom_docs_status = #{sqdCustomDocsStatus}
            </if>
            <if test="sqdCustomAuthorizedStatus != null  and sqdCustomAuthorizedStatus != ''">
                and sqd_custom_authorized_status = #{sqdCustomAuthorizedStatus}
            </if>
            <if test="sqdCustomExamineStatus != null  and sqdCustomExamineStatus != ''">
                and sqd_custom_examine_status = #{sqdCustomExamineStatus}
            </if>
            <if test="sqdCustomReleaseStatus != null  and sqdCustomReleaseStatus != ''">
                and sqd_custom_release_status = #{sqdCustomReleaseStatus}
            </if>
            <if test="sqdSiVerifyStatus != null  and sqdSiVerifyStatus != ''">
                and sqd_si_verify_status = #{sqdSiVerifyStatus}
            </if>
            <if test="sqdSiPostStatus != null  and sqdSiPostStatus != ''">
                and sqd_si_post_status = #{sqdSiPostStatus}
            </if>
            <if test="sqdAmsEnsPostStatus != null  and sqdAmsEnsPostStatus != ''">
                and sqd_ams_ens_post_status = #{sqdAmsEnsPostStatus}
            </if>
            <if test="sqdIsfEmnfPostStatus != null  and sqdIsfEmnfPostStatus != ''">
                and sqd_isf_emnf_post_status = #{sqdIsfEmnfPostStatus}
            </if>
            <if test="sqdMainServicePayingStatus != null  and sqdMainServicePayingStatus != ''">
                and sqd_main_service_paying_status = #{sqdMainServicePayingStatus}
            </if>
            <if test="blGettingStatus != null  and blGettingStatus != ''">
                and bl_getting_status = #{blGettingStatus}
            </if>
            <if test="blReleasingStatus != null  and blReleasingStatus != ''">
                and bl_releasing_status = #{blReleasingStatus}
            </if>
            <if test="sqdContainerNoSum != null  and sqdContainerNoSum != ''">
                and sqd_container_no_sum = #{sqdContainerNoSum}
            </if>
            <if test="sqdPolBookingAgent != null ">
                and sqd_pol_booking_agent = #{sqdPolBookingAgent}
            </if>
            <if test="sqdPodHandleAgent != null ">
                and sqd_pod_handle_agent = #{sqdPodHandleAgent}
            </if>
            <if test="sqdCarrierId != null ">
                and sqd_carrier_id = #{sqdCarrierId}
            </if>
            <if test="sqdDocDeliveryWay != null  and sqdDocDeliveryWay != ''">
                and sqd_doc_delivery_way = #{sqdDocDeliveryWay}
            </if>
            <if test="warehousingNo != null  and warehousingNo != ''">
                and warehousing_no = #{warehousingNo}
            </if>
            <!--search-->
            <if test="polIds != null">
               and rr.pol_id in
                <foreach item="polId" collection="polIds" open="(" separator="," close=")">
                    #{polId}
                </foreach>
            </if>
            <if test="destinationPortIds != null">
                and rr.destination_port_id in
                <foreach item="destinationPortId" collection="destinationPortIds" open="(" separator="," close=")">
                    #{destinationPortId}
                </foreach>
            </if>
            <if test="rctIds != null ">
                and rr.rct_id in
                <foreach item="rctId" collection="rctIds" open="(" separator="," close=")">
                    #{rctId}
                </foreach>
            </if>
            <if test="orderBelongsTo != null">
                and order_belongs_to = #{orderBelongsTo}
            </if>
            <if test="blTypeCode != null">
                and bl_type_code = #{blTypeCode}
            </if>
            <if test="blFormCode != null">
                and bl_form_code = #{blFormCode}
            </if>
            <if test="noTransferAllowed != null  and noTransferAllowed != ''">
                and no_transfer_allowed = #{noTransferAllowed}
            </if>
            <if test="noDividedAllowed != null  and noDividedAllowed != ''">
                and no_divided_allowed = #{noDividedAllowed}
            </if>
            <if test="noAgreementShowed != null  and noAgreementShowed != ''">
                and no_agreement_showed = #{noAgreementShowed}
            </if>
            <if test="isCustomsIntransitShowed != null  and isCustomsIntransitShowed != ''">
                and is_customs_intransit_showed = #{isCustomsIntransitShowed}
            </if>
            <!--业务-->
            <if test="salesId != null ">
                and sales_id = #{salesId}
            </if>
            <if test="salesAssistantId != null ">
                and sales_assistant_id = #{salesAssistantId}
            </if>
            <if test="permissionLevel != null and permissionLevel.length > 0 ">
                <if test="searchValue == null">
                    and (sales_id in
                    <foreach item="staffId" collection="permissionLevel" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                    or sales_assistant_id in
                    <foreach item="staffId" collection="permissionLevel" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                    or verify_psa_id in
                    <foreach item="staffId" collection="permissionLevel" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>)
                </if>
                <if test="searchValue != null">
                    and (sales_id in
                    <foreach item="staffId" collection="permissionLevel" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                    or sales_assistant_id in
                    <foreach item="staffId" collection="permissionLevel" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                    or verify_psa_id in
                    <foreach item="staffId" collection="permissionLevel" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>)
                </if>
            </if>
        </where>
        ORDER BY
        op_accept ASC,
        rct_create_time DESC,
        psa_verify ASC,
        psa_verify_time DESC,
        sqd_shipping_booking_status ASC,
        new_booking_time DESC
    </select>

    <select id="selectRsRctByRctId" parameterType="Long" resultMap="RsRctResult">
        <include refid="selectRsRctVo"/>
        where rr.delete_status = '0' and rr.rct_id = #{rctId}
    </select>

    <insert id="insertRsRct" parameterType="RsRct" useGeneratedKeys="true" keyProperty="rctId">
        insert into rs_rct
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rctNo != null">rct_no,</if>
            <if test="clientId != null">client_id,</if>
            <if test="clientSummary != null">client_summary,</if>
            <if test="clientRoleId != null">client_role_id,</if>
            <if test="clientContact != null">client_contact,</if>
            <if test="clientContactTel != null">client_contact_tel,</if>
            <if test="clientContactEmail != null">client_contact_email,</if>
            <if test="relationClientIdList != null">relation_client_id_list,</if>
            <if test="emergencyLevel != null">emergency_level,</if>
            <if test="difficultyLevel != null">difficulty_level,</if>
            <if test="releaseType != null">release_type,</if>
            <if test="impExpType != null">imp_exp_type,</if>
            <if test="tradingTerms != null">trading_terms,</if>
            <if test="logisticsTerms != null">logistics_terms,</if>
            <if test="tradingPaymentChannel != null">trading_payment_channel,</if>
            <if test="clientContractNo != null">client_contract_no,</if>
            <if test="clientInvoiceNo != null">client_invoice_no,</if>
            <if test="cargoTypeCodeSum != null">cargo_type_code_sum,</if>
            <if test="goodsNameSummary != null">goods_name_summary,</if>
            <if test="packageQuantity != null">package_quantity,</if>
            <if test="goodsVolume != null">goods_volume,</if>
            <if test="grossWeight != null">gross_weight,</if>
            <if test="weightUnitCode != null">weight_unit_code,</if>
            <if test="goodsCurrencyCode != null">goods_currency_code,</if>
            <if test="goodsValue != null">goods_value,</if>
            <if test="logisticsTypeId != null">logistics_type_id,</if>
            <if test="revenueTon != null">revenue_ton,</if>
            <if test="polId != null">pol_id,</if>
            <if test="localBasicPortId != null">local_basic_port_id,</if>
            <if test="transitPortId != null">transit_port_id,</if>
            <if test="podId != null">pod_id,</if>
            <if test="destinationPortId != null">destination_port_id,</if>
            <if test="cvClosingTime != null">cv_closing_time,</if>
            <if test="siClosingTime != null">si_closing_time,</if>
            <if test="firstVessel != null">first_vessel,</if>
            <if test="firstVoyage != null">first_voyage,</if>
            <if test="firstCyOpenTime != null">first_cy_open_time,</if>
            <if test="firstCyClosingTime != null">first_cy_closing_time,</if>
            <if test="firstEtd != null">first_etd,</if>
            <if test="basicVessel != null">basic_vessel,</if>
            <if test="basicVoyage != null">basic_voyage,</if>
            <if test="basicFinalGateinTime != null">basic_final_gatein_time,</if>
            <if test="basicEtd != null">basic_etd,</if>
            <if test="podEta != null">pod_eta,</if>
            <if test="destinationPortEta != null">destination_port_eta,</if>
            <if test="carrierId != null">carrier_id,</if>
            <if test="sqdCarrier != null">sqd_carrier,</if>
            <if test="inquiryScheduleSummary != null">inquiry_schedule_summary,</if>
            <if test="serviceTypeIdList != null">service_type_id_list,</if>
            <if test="soNo != null">so_no,</if>
            <if test="blNo != null">bl_no,</if>
            <if test="sqdContainersSealsSum != null">sqd_containers_seals_sum,</if>
            <if test="sqdExportCustomsType != null">sqd_export_customs_type,</if>
            <if test="sqdTrailerType != null">sqd_trailer_type,</if>
            <if test="rctProcessStatusSummary != null">rct_process_status_summary,</if>
            <if test="transportStatusSummary != null">transport_status_summary,</if>
            <if test="paymentReceivingStatusSummary != null">payment_receiving_status_summary,</if>
            <if test="paymentPayingStatusSummary != null">payment_paying_status_summary,</if>
            <if test="transportStatusA != null">transport_status_a,</if>
            <if test="transportStatusB != null">transport_status_b,</if>
            <if test="docStatusA != null">doc_status_a,</if>
            <if test="docStatusB != null">doc_status_b,</if>
            <if test="processStatusId != null">process_status_id,</if>
            <if test="processStatusTime != null">process_status_time,</if>
            <if test="qoutationNo != null">qoutation_no,</if>
            <if test="qoutationSketch != null">qoutation_sketch,</if>
            <if test="salesId != null">sales_id,</if>
            <if test="qoutationTime != null">qoutation_time,</if>
            <if test="newBookingNo != null">new_booking_no,</if>
            <if test="newBookingRemark != null">new_booking_remark,</if>
            <if test="salesAssistantId != null">sales_assistant_id,</if>
            <if test="salesObserverId != null">sales_observer_id,</if>
            <if test="newBookingTime != null">new_booking_time,</if>
            <if test="inquiryNoticeSum != null">inquiry_notice_sum,</if>
            <if test="inquiryInnerRemarkSum != null">inquiry_inner_remark_sum,</if>
            <if test="verifyPsaId != null">verify_psa_id,</if>
            <if test="psaVerifyTime != null">psa_verify_time,</if>
            <if test="opLeaderNotice != null">op_leader_notice,</if>
            <if test="opInnerRemark != null">op_inner_remark,</if>
            <if test="opId != null">op_id,</if>
            <if test="bookingOpId != null">booking_op_id,</if>
            <if test="docOpId != null">doc_op_id,</if>
            <if test="opObserverId != null">op_observer_id,</if>
            <if test="rctCreateTime != null">rct_create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="deleteBy != null">delete_by,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="precarriageRegionId != null">precarriage_region_id,</if>
            <if test="precarriageAddress != null">precarriage_address,</if>
            <if test="precarriageTime != null">precarriage_time,</if>
            <if test="precarriageContact != null">precarriage_contact,</if>
            <if test="precarriageTel != null">precarriage_tel,</if>
            <if test="precarriageRemark != null">precarriage_remark,</if>
            <if test="dispatchRegionId != null">dispatch_region_id,</if>
            <if test="dispatchAddress != null">dispatch_address,</if>
            <if test="dispatchTime != null">dispatch_time,</if>
            <if test="dispatchContact != null">dispatch_contact,</if>
            <if test="dispatchTel != null">dispatch_tel,</if>
            <if test="dispatchRemark != null">dispatch_remark,</if>
            <if test="sqdWarehousingStatus != null">sqd_warehousing_status,</if>
            <if test="sqdShippingBookingStatus != null">sqd_shipping_booking_status,</if>
            <if test="sqdTrailerBookingStatus != null">sqd_trailer_booking_status,</if>
            <if test="sqdContainerBookingStatus != null">sqd_container_booking_status,</if>
            <if test="sqdContainerLoadingStatus != null">sqd_container_loading_status,</if>
            <if test="sqdVesselArrangeStatus != null">sqd_vessel_arrange_status,</if>
            <if test="sqdVgmStatus != null">sqd_vgm_status,</if>
            <if test="sqdCustomDocsStatus != null">sqd_custom_docs_status,</if>
            <if test="sqdCustomAuthorizedStatus != null">sqd_custom_authorized_status,</if>
            <if test="sqdCustomExamineStatus != null">sqd_custom_examine_status,</if>
            <if test="sqdCustomReleaseStatus != null">sqd_custom_release_status,</if>
            <if test="sqdSiVerifyStatus != null">sqd_si_verify_status,</if>
            <if test="sqdSiPostStatus != null">sqd_si_post_status,</if>
            <if test="sqdAmsEnsPostStatus != null">sqd_ams_ens_post_status,</if>
            <if test="sqdIsfEmnfPostStatus != null">sqd_isf_emnf_post_status,</if>
            <if test="sqdMainServicePayingStatus != null">sqd_main_service_paying_status,</if>
            <if test="blGettingStatus != null">bl_getting_status,</if>
            <if test="blReleasingStatus != null">bl_releasing_status,</if>
            <if test="sqdContainerNoSum != null">sqd_container_no_sum,</if>
            <if test="sqdPolBookingAgent != null">sqd_pol_booking_agent,</if>
            <if test="sqdPodHandleAgent != null">sqd_pod_handle_agent,</if>
            <if test="sqdCarrierId != null">sqd_carrier_id,</if>
            <if test="sqdDocDeliveryWay != null">sqd_doc_delivery_way,</if>
            <if test="warehousingNo != null">warehousing_no,</if>
            <if test="clientJobNo != null">client_job_no,</if>
            <if test="bookingShipper != null">booking_shipper,</if>
            <if test="bookingConsignee != null">booking_consignee,</if>
            <if test="bookingNotifyParty != null">booking_notify_party,</if>
            <if test="bookingAgent != null">booking_agent,</if>
            <if test="sqdInsuranceType != null">sqd_insurance_type,</if>
            <if test="verifyOpLeaderId != null">verify_op_leader_id,</if>
            <if test="opLeaderVerifyTime != null">op_leader_verify_time,</if>
            <if test="messageDisplay != null">message_display,</if>
            <if test="serviceMessageFold != null">service_message_fold,</if>
            <if test="orderBelongsTo != null">order_belongs_to,</if>
            <if test="blTypeCode != null">bl_type_code,</if>
            <if test="blFormCode != null">bl_form_code,</if>
            <if test="noTransferAllowed != null">no_transfer_allowed,</if>
            <if test="noDividedAllowed != null">no_divided_allowed,</if>
            <if test="noAgreementShowed != null">no_agreement_showed,</if>
            <if test="isCustomsIntransitShowed != null">is_customs_intransit_showed,</if>
            <if test="psaVerifyStatusId != null">psa_verify_status_id,</if>
            <if test="opLeaderVerifyStatusId != null">op_leader_verify_status_id,</if>
            <if test="precarriageSupplierNo != null">precarriage_supplier_no,</if>
            <if test="agreementTypeCode != null">agreement_type_code,</if>
            <if test="opAccept != null">op_accept,</if>
            <if test="psaVerify != null">psa_verify,</if>
            <if test="firstAtd != null">first_atd,</if>
            <if test="destinationPortAta != null">destination_port_ata,</if>
            <if test="bookingAgentRemark != null">booking_agent_remark,</if>
            <if test="shippingMark != null">shipping_mark,</if>
            <if test="freightPaidWayCode != null">freight_paid_way_code,</if>
            <if test="ctnrTypeCode != null">ctnr_type_code,</if>
            <if test="etd != null">etd,</if>
            <if test="eta != null">eta,</if>
            <if test="sqdDnReceiveSlipStatus != null">sqd_dn_receive_slip_status,</if>
            <if test="sqdDnPaySlipStatus != null">sqd_dn_pay_slip_status,</if>
            <if test="paymentNode != null">payment_node,</if>
            <if test="psaRctId != null">psa_rct_id,</if>
            <if test="sqdPsaNo != null">sqd_psa_no,</if>
            <if test="opAskingBlGetTime != null">op_asking_bl_get_time,</if>
            <if test="opAskingBlReleaseTime != null">op_asking_bl_release_time,</if>
            <if test="accPromissBlGetTime != null">acc_promiss_bl_get_time,</if>
            <if test="actualBlGotTime != null">actual_bl_got_time,</if>
            <if test="accPromissBlReleaseTime != null">acc_promiss_bl_release_time,</if>
            <if test="actualBlReleaseTime != null">actual_bl_release_time,</if>
            <if test="agentNoticeTime != null">agent_notice_time,</if>
            <if test="bookingStatus != null">booking_status,</if>
            <if test="secondVessel != null">second_vessel,</if>
            <if test="statusUpdateTime != null">status_update_time,</if>
            <if test="quotationInRmb != null">quotation_in_rmb,</if>
            <if test="inquiryInRmb != null">inquiry_in_rmb,</if>
            <if test="estimatedProfitInRmb != null">estimated_profit_in_rmb,</if>
            <if test="dnUsd != null">dn_usd,</if>
            <if test="dnRmb != null">dn_rmb,</if>
            <if test="dnUsdBalance != null">dn_usd_balance,</if>
            <if test="dnRmbBalance != null">dn_rmb_balance,</if>
            <if test="dnInRmb != null">dn_in_rmb,</if>
            <if test="dnInRmbBalance != null">dn_in_rmb_balance,</if>
            <if test="cnUsd != null">cn_usd,</if>
            <if test="cnRmb != null">cn_rmb,</if>
            <if test="cnUsdBalance != null">cn_usd_balance,</if>
            <if test="cnRmbBalance != null">cn_rmb_balance,</if>
            <if test="cnInRmb != null">cn_in_rmb,</if>
            <if test="cnInRmbBalance != null">cn_in_rmb_balance,</if>
            <if test="profitUsd != null">profit_usd,</if>
            <if test="profitRmb != null">profit_rmb,</if>
            <if test="profitInRmb != null">profit_in_rmb,</if>
            <if test="differenceInRmb != null">difference_in_rmb,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rctNo != null">#{rctNo},</if>
            <if test="clientId != null">#{clientId},</if>
            <if test="clientSummary != null">#{clientSummary},</if>
            <if test="clientRoleId != null">#{clientRoleId},</if>
            <if test="clientContact != null">#{clientContact},</if>
            <if test="clientContactTel != null">#{clientContactTel},</if>
            <if test="clientContactEmail != null">#{clientContactEmail},</if>
            <if test="relationClientIdList != null">#{relationClientIdList},</if>
            <if test="emergencyLevel != null">#{emergencyLevel},</if>
            <if test="difficultyLevel != null">#{difficultyLevel},</if>
            <if test="releaseType != null">#{releaseType},</if>
            <if test="impExpType != null">#{impExpType},</if>
            <if test="tradingTerms != null">#{tradingTerms},</if>
            <if test="logisticsTerms != null">#{logisticsTerms},</if>
            <if test="tradingPaymentChannel != null">#{tradingPaymentChannel},</if>
            <if test="clientContractNo != null">#{clientContractNo},</if>
            <if test="clientInvoiceNo != null">#{clientInvoiceNo},</if>
            <if test="cargoTypeCodeSum != null">#{cargoTypeCodeSum},</if>
            <if test="goodsNameSummary != null">#{goodsNameSummary},</if>
            <if test="packageQuantity != null">#{packageQuantity},</if>
            <if test="goodsVolume != null">#{goodsVolume},</if>
            <if test="grossWeight != null">#{grossWeight},</if>
            <if test="weightUnitCode != null">#{weightUnitCode},</if>
            <if test="goodsCurrencyCode != null">#{goodsCurrencyCode},</if>
            <if test="goodsValue != null">#{goodsValue},</if>
            <if test="logisticsTypeId != null">#{logisticsTypeId},</if>
            <if test="revenueTon != null">#{revenueTon},</if>
            <if test="polId != null">#{polId},</if>
            <if test="localBasicPortId != null">#{localBasicPortId},</if>
            <if test="transitPortId != null">#{transitPortId},</if>
            <if test="podId != null">#{podId},</if>
            <if test="destinationPortId != null">#{destinationPortId},</if>
            <if test="cvClosingTime != null">#{cvClosingTime},</if>
            <if test="siClosingTime != null">#{siClosingTime},</if>
            <if test="firstVessel != null">#{firstVessel},</if>
            <if test="firstVoyage != null">#{firstVoyage},</if>
            <if test="firstCyOpenTime != null">#{firstCyOpenTime},</if>
            <if test="firstCyClosingTime != null">#{firstCyClosingTime},</if>
            <if test="firstEtd != null">#{firstEtd},</if>
            <if test="basicVessel != null">#{basicVessel},</if>
            <if test="basicVoyage != null">#{basicVoyage},</if>
            <if test="basicFinalGateinTime != null">#{basicFinalGateinTime},</if>
            <if test="basicEtd != null">#{basicEtd},</if>
            <if test="podEta != null">#{podEta},</if>
            <if test="destinationPortEta != null">#{destinationPortEta},</if>
            <if test="carrierId != null">#{carrierId},</if>
            <if test="sqdCarrier != null">#{sqdCarrier},</if>
            <if test="inquiryScheduleSummary != null">#{inquiryScheduleSummary},</if>
            <if test="serviceTypeIdList != null">#{serviceTypeIdList},</if>
            <if test="soNo != null">#{soNo},</if>
            <if test="blNo != null">#{blNo},</if>
            <if test="sqdContainersSealsSum != null">#{sqdContainersSealsSum},</if>
            <if test="sqdExportCustomsType != null">#{sqdExportCustomsType},</if>
            <if test="sqdTrailerType != null">#{sqdTrailerType},</if>
            <if test="rctProcessStatusSummary != null">#{rctProcessStatusSummary},</if>
            <if test="transportStatusSummary != null">#{transportStatusSummary},</if>
            <if test="paymentReceivingStatusSummary != null">#{paymentReceivingStatusSummary},</if>
            <if test="paymentPayingStatusSummary != null">#{paymentPayingStatusSummary},</if>
            <if test="transportStatusA != null">#{transportStatusA},</if>
            <if test="transportStatusB != null">#{transportStatusB},</if>
            <if test="docStatusA != null">#{docStatusA},</if>
            <if test="docStatusB != null">#{docStatusB},</if>
            <if test="processStatusId != null">#{processStatusId},</if>
            <if test="processStatusTime != null">#{processStatusTime},</if>
            <if test="qoutationNo != null">#{qoutationNo},</if>
            <if test="qoutationSketch != null">#{qoutationSketch},</if>
            <if test="salesId != null">#{salesId},</if>
            <if test="qoutationTime != null">#{qoutationTime},</if>
            <if test="newBookingNo != null">#{newBookingNo},</if>
            <if test="newBookingRemark != null">#{newBookingRemark},</if>
            <if test="salesAssistantId != null">#{salesAssistantId},</if>
            <if test="salesObserverId != null">#{salesObserverId},</if>
            <if test="newBookingTime != null">#{newBookingTime},</if>
            <if test="inquiryNoticeSum != null">#{inquiryNoticeSum},</if>
            <if test="inquiryInnerRemarkSum != null">#{inquiryInnerRemarkSum},</if>
            <if test="verifyPsaId != null">#{verifyPsaId},</if>
            <if test="psaVerifyTime != null">#{psaVerifyTime},</if>
            <if test="opLeaderNotice != null">#{opLeaderNotice},</if>
            <if test="opInnerRemark != null">#{opInnerRemark},</if>
            <if test="opId != null">#{opId},</if>
            <if test="bookingOpId != null">#{bookingOpId},</if>
            <if test="docOpId != null">#{docOpId},</if>
            <if test="opObserverId != null">#{opObserverId},</if>
            <if test="rctCreateTime != null">#{rctCreateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="deleteBy != null">#{deleteBy},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="precarriageRegionId != null">#{precarriageRegionId},</if>
            <if test="precarriageAddress != null">#{precarriageAddress},</if>
            <if test="precarriageTime != null">#{precarriageTime},</if>
            <if test="precarriageContact != null">#{precarriageContact},</if>
            <if test="precarriageTel != null">#{precarriageTel},</if>
            <if test="precarriageRemark != null">#{precarriageRemark},</if>
            <if test="dispatchRegionId != null">#{dispatchRegionId},</if>
            <if test="dispatchAddress != null">#{dispatchAddress},</if>
            <if test="dispatchTime != null">#{dispatchTime},</if>
            <if test="dispatchContact != null">#{dispatchContact},</if>
            <if test="dispatchTel != null">#{dispatchTel},</if>
            <if test="dispatchRemark != null">#{dispatchRemark},</if>
            <if test="sqdWarehousingStatus != null">#{sqdWarehousingStatus},</if>
            <if test="sqdShippingBookingStatus != null">#{sqdShippingBookingStatus},</if>
            <if test="sqdTrailerBookingStatus != null">#{sqdTrailerBookingStatus},</if>
            <if test="sqdContainerBookingStatus != null">#{sqdContainerBookingStatus},</if>
            <if test="sqdContainerLoadingStatus != null">#{sqdContainerLoadingStatus},</if>
            <if test="sqdVesselArrangeStatus != null">#{sqdVesselArrangeStatus},</if>
            <if test="sqdVgmStatus != null">#{sqdVgmStatus},</if>
            <if test="sqdCustomDocsStatus != null">#{sqdCustomDocsStatus},</if>
            <if test="sqdCustomAuthorizedStatus != null">#{sqdCustomAuthorizedStatus},</if>
            <if test="sqdCustomExamineStatus != null">#{sqdCustomExamineStatus},</if>
            <if test="sqdCustomReleaseStatus != null">#{sqdCustomReleaseStatus},</if>
            <if test="sqdSiVerifyStatus != null">#{sqdSiVerifyStatus},</if>
            <if test="sqdSiPostStatus != null">#{sqdSiPostStatus},</if>
            <if test="sqdAmsEnsPostStatus != null">#{sqdAmsEnsPostStatus},</if>
            <if test="sqdIsfEmnfPostStatus != null">#{sqdIsfEmnfPostStatus},</if>
            <if test="sqdMainServicePayingStatus != null">#{sqdMainServicePayingStatus},</if>
            <if test="blGettingStatus != null">#{blGettingStatus},</if>
            <if test="blReleasingStatus != null">#{blReleasingStatus},</if>
            <if test="sqdContainerNoSum != null">#{sqdContainerNoSum},</if>
            <if test="sqdPolBookingAgent != null">#{sqdPolBookingAgent},</if>
            <if test="sqdPodHandleAgent != null">#{sqdPodHandleAgent},</if>
            <if test="sqdCarrierId != null">#{sqdCarrierId},</if>
            <if test="sqdDocDeliveryWay != null">#{sqdDocDeliveryWay},</if>
            <if test="warehousingNo != null">#{warehousingNo},</if>
            <if test="clientJobNo != null">#{clientJobNo},</if>
            <if test="bookingShipper != null">#{bookingShipper},</if>
            <if test="bookingConsignee != null">#{bookingConsignee},</if>
            <if test="bookingNotifyParty != null">#{bookingNotifyParty},</if>
            <if test="bookingAgent != null">#{bookingAgent},</if>
            <if test="sqdInsuranceType != null">#{sqdInsuranceType},</if>
            <if test="verifyOpLeaderId != null">#{verifyOpLeaderId},</if>
            <if test="opLeaderVerifyTime != null">#{opLeaderVerifyTime},</if>
            <if test="messageDisplay != null">#{messageDisplay},</if>
            <if test="serviceMessageFold != null">#{serviceMessageFold},</if>
            <if test="orderBelongsTo != null">#{orderBelongsTo},</if>
            <if test="blTypeCode != null">#{blTypeCode},</if>
            <if test="blFormCode != null">#{blFormCode},</if>
            <if test="noTransferAllowed != null">#{noTransferAllowed},</if>
            <if test="noDividedAllowed != null">#{noDividedAllowed},</if>
            <if test="noAgreementShowed != null">#{noAgreementShowed},</if>
            <if test="isCustomsIntransitShowed != null">#{isCustomsIntransitShowed},</if>
            <if test="psaVerifyStatusId != null">#{psaVerifyStatusId},</if>
            <if test="opLeaderVerifyStatusId != null">#{opLeaderVerifyStatusId},</if>
            <if test="precarriageSupplierNo != null">#{precarriageSupplierNo},</if>
            <if test="agreementTypeCode != null">#{agreementTypeCode},</if>
            <if test="opAccept != null">#{opAccept},</if>
            <if test="psaVerify != null">#{psaVerify},</if>
            <if test="firstAtd != null">#{firstAtd},</if>
            <if test="destinationPortAta != null">#{destinationPortAta},</if>
            <if test="bookingAgentRemark != null">#{bookingAgentRemark},</if>
            <if test="shippingMark != null">#{shippingMark},</if>
            <if test="freightPaidWayCode != null">#{freightPaidWayCode},</if>
            <if test="ctnrTypeCode != null">#{ctnrTypeCode},</if>
            <if test="etd != null">#{etd},</if>
            <if test="eta != null">#{eta},</if>
            <if test="sqdDnReceiveSlipStatus != null">#{sqdDnReceiveSlipStatus},</if>
            <if test="sqdDnPaySlipStatus != null">#{sqdDnPaySlipStatus},</if>
            <if test="paymentNode != null">#{paymentNode},</if>
            <if test="psaRctId != null">#{psaRctId},</if>
            <if test="sqdPsaNo != null">#{sqdPsaNo},</if>
            <if test="opAskingBlGetTime != null">#{opAskingBlGetTime},</if>
            <if test="opAskingBlReleaseTime != null">#{opAskingBlReleaseTime},</if>
            <if test="accPromissBlGetTime != null">#{accPromissBlGetTime},</if>
            <if test="actualBlGotTime != null">#{actualBlGotTime},</if>
            <if test="accPromissBlReleaseTime != null">#{accPromissBlReleaseTime},</if>
            <if test="actualBlReleaseTime != null">#{actualBlReleaseTime},</if>
            <if test="agentNoticeTime != null">#{agentNoticeTime},</if>
            <if test="bookingStatus != null">#{bookingStatus},</if>
            <if test="secondVessel != null">#{secondVessel},</if>
            <if test="statusUpdateTime != null">#{statusUpdateTime},</if>
            <if test="quotationInRmb != null">#{quotationInRmb},</if>
            <if test="inquiryInRmb != null">#{inquiryInRmb},</if>
            <if test="estimatedProfitInRmb != null">#{estimatedProfitInRmb},</if>
            <if test="dnUsd != null">#{dnUsd},</if>
            <if test="dnRmb != null">#{dnRmb},</if>
            <if test="dnUsdBalance != null">#{dnUsdBalance},</if>
            <if test="dnRmbBalance != null">#{dnRmbBalance},</if>
            <if test="dnInRmb != null">#{dnInRmb},</if>
            <if test="dnInRmbBalance != null">#{dnInRmbBalance},</if>
            <if test="cnUsd != null">#{cnUsd},</if>
            <if test="cnRmb != null">#{cnRmb},</if>
            <if test="cnUsdBalance != null">#{cnUsdBalance},</if>
            <if test="cnRmbBalance != null">#{cnRmbBalance},</if>
            <if test="cnInRmb != null">#{cnInRmb},</if>
            <if test="cnInRmbBalance != null">#{cnInRmbBalance},</if>
            <if test="profitUsd != null">#{profitUsd},</if>
            <if test="profitRmb != null">#{profitRmb},</if>
            <if test="profitInRmb != null">#{profitInRmb},</if>
            <if test="differenceInRmb != null">#{differenceInRmb},</if>
        </trim>
    </insert>

    <update id="updateRsRct" parameterType="RsRct">
        update rs_rct
        <trim prefix="SET" suffixOverrides=",">
            <if test="rctNo != null">rct_no = #{rctNo},</if>
            <if test="clientId != null">client_id = #{clientId},</if>
            <if test="clientSummary != null">client_summary = #{clientSummary},</if>
            <if test="clientRoleId != null">client_role_id = #{clientRoleId},</if>
            <if test="clientContact != null">client_contact = #{clientContact},</if>
            <if test="clientContactTel != null">client_contact_tel = #{clientContactTel},</if>
            <if test="clientContactEmail != null">client_contact_email = #{clientContactEmail},</if>
            <if test="relationClientIdList != null">relation_client_id_list = #{relationClientIdList},</if>
            <if test="emergencyLevel != null">emergency_level = #{emergencyLevel},</if>
            <if test="difficultyLevel != null">difficulty_level = #{difficultyLevel},</if>
            <if test="releaseType != null">release_type = #{releaseType},</if>
            <if test="impExpType != null">imp_exp_type = #{impExpType},</if>
            <if test="tradingTerms != null">trading_terms = #{tradingTerms},</if>
            <if test="logisticsTerms != null">logistics_terms = #{logisticsTerms},</if>
            <if test="tradingPaymentChannel != null">trading_payment_channel = #{tradingPaymentChannel},</if>
            <if test="clientContractNo != null">client_contract_no = #{clientContractNo},</if>
            <if test="clientInvoiceNo != null">client_invoice_no = #{clientInvoiceNo},</if>
            <if test="cargoTypeCodeSum != null">cargo_type_code_sum = #{cargoTypeCodeSum},</if>
            <if test="goodsNameSummary != null">goods_name_summary = #{goodsNameSummary},</if>
            <if test="packageQuantity != null">package_quantity = #{packageQuantity},</if>
            <if test="goodsVolume != null">goods_volume = #{goodsVolume},</if>
            <if test="grossWeight != null">gross_weight = #{grossWeight},</if>
            <if test="weightUnitCode != null">weight_unit_code = #{weightUnitCode},</if>
            <if test="goodsCurrencyCode != null">goods_currency_code = #{goodsCurrencyCode},</if>
            <if test="goodsValue != null">goods_value = #{goodsValue},</if>
            <if test="logisticsTypeId != null">logistics_type_id = #{logisticsTypeId},</if>
            <if test="revenueTon != null">revenue_ton = #{revenueTon},</if>
            <if test="polId != null">pol_id = #{polId},</if>
            <if test="localBasicPortId != null">local_basic_port_id = #{localBasicPortId},</if>
            <if test="transitPortId != null">transit_port_id = #{transitPortId},</if>
            <if test="podId != null">pod_id = #{podId},</if>
            <if test="destinationPortId != null">destination_port_id = #{destinationPortId},</if>
            <if test="cvClosingTime != null">cv_closing_time = #{cvClosingTime},</if>
            <if test="siClosingTime != null">si_closing_time = #{siClosingTime},</if>
            <if test="firstVessel != null">first_vessel = #{firstVessel},</if>
            <if test="firstVoyage != null">first_voyage = #{firstVoyage},</if>
            <if test="firstCyOpenTime != null">first_cy_open_time = #{firstCyOpenTime},</if>
            <if test="firstCyClosingTime != null">first_cy_closing_time = #{firstCyClosingTime},</if>
            <if test="firstEtd != null">first_etd = #{firstEtd},</if>
            <if test="basicVessel != null">basic_vessel = #{basicVessel},</if>
            <if test="basicVoyage != null">basic_voyage = #{basicVoyage},</if>
            <if test="basicFinalGateinTime != null">basic_final_gatein_time = #{basicFinalGateinTime},</if>
            <if test="basicEtd != null">basic_etd = #{basicEtd},</if>
            <if test="podEta != null">pod_eta = #{podEta},</if>
            <if test="destinationPortEta != null">destination_port_eta = #{destinationPortEta},</if>
            <if test="carrierId != null">carrier_id = #{carrierId},</if>
            <if test="sqdCarrier != null">sqd_carrier = #{sqdCarrier},</if>
            <if test="inquiryScheduleSummary != null">inquiry_schedule_summary = #{inquiryScheduleSummary},</if>
            <if test="serviceTypeIdList != null">service_type_id_list = #{serviceTypeIdList},</if>
            <if test="soNo != null">so_no = #{soNo},</if>
            <if test="blNo != null">bl_no = #{blNo},</if>
            <if test="sqdContainersSealsSum != null">sqd_containers_seals_sum = #{sqdContainersSealsSum},</if>
            <if test="sqdExportCustomsType != null">sqd_export_customs_type = #{sqdExportCustomsType},</if>
            <if test="sqdTrailerType != null">sqd_trailer_type = #{sqdTrailerType},</if>
            <if test="rctProcessStatusSummary != null">rct_process_status_summary = #{rctProcessStatusSummary},</if>
            <if test="transportStatusSummary != null">transport_status_summary = #{transportStatusSummary},</if>
            <if test="paymentReceivingStatusSummary != null">payment_receiving_status_summary = #{paymentReceivingStatusSummary},</if>
            <if test="paymentPayingStatusSummary != null">payment_paying_status_summary = #{paymentPayingStatusSummary},</if>
            <if test="transportStatusA != null">transport_status_a = #{transportStatusA},</if>
            <if test="transportStatusB != null">transport_status_b = #{transportStatusB},</if>
            <if test="docStatusA != null">doc_status_a = #{docStatusA},</if>
            <if test="docStatusB != null">doc_status_b = #{docStatusB},</if>
            <if test="processStatusId != null">process_status_id = #{processStatusId},</if>
            <if test="processStatusTime != null">process_status_time = #{processStatusTime},</if>
            <if test="qoutationNo != null">qoutation_no = #{qoutationNo},</if>
            <if test="qoutationSketch != null">qoutation_sketch = #{qoutationSketch},</if>
            <if test="salesId != null">sales_id = #{salesId},</if>
            <if test="qoutationTime != null">qoutation_time = #{qoutationTime},</if>
            <if test="newBookingNo != null">new_booking_no = #{newBookingNo},</if>
            <if test="newBookingRemark != null">new_booking_remark = #{newBookingRemark},</if>
            <if test="salesAssistantId != null">sales_assistant_id = #{salesAssistantId},</if>
            <if test="salesObserverId != null">sales_observer_id = #{salesObserverId},</if>
            <if test="newBookingTime != null">new_booking_time = #{newBookingTime},</if>
            <if test="inquiryNoticeSum != null">inquiry_notice_sum = #{inquiryNoticeSum},</if>
            <if test="inquiryInnerRemarkSum != null">inquiry_inner_remark_sum = #{inquiryInnerRemarkSum},</if>
            <if test="verifyPsaId != null">verify_psa_id = #{verifyPsaId},</if>
            <if test="psaVerifyTime != null">psa_verify_time = #{psaVerifyTime},</if>
            <if test="opLeaderNotice != null">op_leader_notice = #{opLeaderNotice},</if>
            <if test="opInnerRemark != null">op_inner_remark = #{opInnerRemark},</if>
            <if test="opId != null">op_id = #{opId},</if>
            <if test="opId == null">op_id = null,</if>
            <if test="bookingOpId != null">booking_op_id = #{bookingOpId},</if>
            <if test="docOpId != null">doc_op_id = #{docOpId},</if>
            <if test="opObserverId != null">op_observer_id = #{opObserverId},</if>
            <if test="rctCreateTime != null">rct_create_time = #{rctCreateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="deleteBy != null">delete_by = #{deleteBy},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="precarriageRegionId != null">precarriage_region_id = #{precarriageRegionId},</if>
            <if test="precarriageAddress != null">precarriage_address = #{precarriageAddress},</if>
            <if test="precarriageTime != null">precarriage_time = #{precarriageTime},</if>
            <if test="precarriageContact != null">precarriage_contact = #{precarriageContact},</if>
            <if test="precarriageTel != null">precarriage_tel = #{precarriageTel},</if>
            <if test="precarriageRemark != null">precarriage_remark = #{precarriageRemark},</if>
            <if test="dispatchRegionId != null">dispatch_region_id = #{dispatchRegionId},</if>
            <if test="dispatchAddress != null">dispatch_address = #{dispatchAddress},</if>
            <if test="dispatchTime != null">dispatch_time = #{dispatchTime},</if>
            <if test="dispatchContact != null">dispatch_contact = #{dispatchContact},</if>
            <if test="dispatchTel != null">dispatch_tel = #{dispatchTel},</if>
            <if test="dispatchRemark != null">dispatch_remark = #{dispatchRemark},</if>
            <if test="sqdWarehousingStatus != null">sqd_warehousing_status = #{sqdWarehousingStatus},</if>
            <if test="sqdShippingBookingStatus != null">sqd_shipping_booking_status = #{sqdShippingBookingStatus},</if>
            <if test="sqdTrailerBookingStatus != null">sqd_trailer_booking_status = #{sqdTrailerBookingStatus},</if>
            <if test="sqdContainerBookingStatus != null">sqd_container_booking_status = #{sqdContainerBookingStatus},</if>
            <if test="sqdContainerLoadingStatus != null">sqd_container_loading_status = #{sqdContainerLoadingStatus},</if>
            <if test="sqdVesselArrangeStatus != null">sqd_vessel_arrange_status = #{sqdVesselArrangeStatus},</if>
            <if test="sqdVgmStatus != null">sqd_vgm_status = #{sqdVgmStatus},</if>
            <if test="sqdCustomDocsStatus != null">sqd_custom_docs_status = #{sqdCustomDocsStatus},</if>
            <if test="sqdCustomAuthorizedStatus != null">sqd_custom_authorized_status = #{sqdCustomAuthorizedStatus},</if>
            <if test="sqdCustomExamineStatus != null">sqd_custom_examine_status = #{sqdCustomExamineStatus},</if>
            <if test="sqdCustomReleaseStatus != null">sqd_custom_release_status = #{sqdCustomReleaseStatus},</if>
            <if test="sqdSiVerifyStatus != null">sqd_si_verify_status = #{sqdSiVerifyStatus},</if>
            <if test="sqdSiPostStatus != null">sqd_si_post_status = #{sqdSiPostStatus},</if>
            <if test="sqdAmsEnsPostStatus != null">sqd_ams_ens_post_status = #{sqdAmsEnsPostStatus},</if>
            <if test="sqdIsfEmnfPostStatus != null">sqd_isf_emnf_post_status = #{sqdIsfEmnfPostStatus},</if>
            <if test="sqdMainServicePayingStatus != null">sqd_main_service_paying_status = #{sqdMainServicePayingStatus},</if>
            <if test="blGettingStatus != null">bl_getting_status = #{blGettingStatus},</if>
            <if test="blReleasingStatus != null">bl_releasing_status = #{blReleasingStatus},</if>
            <if test="sqdContainerNoSum != null">sqd_container_no_sum = #{sqdContainerNoSum},</if>
            <if test="sqdPolBookingAgent != null">sqd_pol_booking_agent = #{sqdPolBookingAgent},</if>
            <if test="sqdPodHandleAgent != null">sqd_pod_handle_agent = #{sqdPodHandleAgent},</if>
            <if test="sqdCarrierId != null">sqd_carrier_id = #{sqdCarrierId},</if>
            <if test="sqdDocDeliveryWay != null">sqd_doc_delivery_way = #{sqdDocDeliveryWay},</if>
            <if test="warehousingNo != null">warehousing_no = #{warehousingNo},</if>
            <if test="clientJobNo != null">client_job_no = #{clientJobNo},</if>
            <if test="bookingShipper != null">booking_shipper = #{bookingShipper},</if>
            <if test="bookingConsignee != null">booking_consignee = #{bookingConsignee},</if>
            <if test="bookingNotifyParty != null">booking_notify_party = #{bookingNotifyParty},</if>
            <if test="bookingAgent != null">booking_agent = #{bookingAgent},</if>
            <if test="sqdInsuranceType != null">sqd_insurance_type = #{sqdInsuranceType},</if>
            <if test="verifyOpLeaderId != null">verify_op_leader_id = #{verifyOpLeaderId},</if>
            <if test="opLeaderVerifyTime != null">op_leader_verify_time = #{opLeaderVerifyTime},</if>
            <if test="messageDisplay != null">message_display = #{messageDisplay},</if>
            <if test="serviceMessageFold != null">service_message_fold = #{serviceMessageFold},</if>
            <if test="orderBelongsTo != null">order_belongs_to = #{orderBelongsTo},</if>
            <if test="blTypeCode != null">bl_type_code = #{blTypeCode},</if>
            <if test="blFormCode != null">bl_form_code = #{blFormCode},</if>
            <if test="noTransferAllowed != null">no_transfer_allowed = #{noTransferAllowed},</if>
            <if test="noDividedAllowed != null">no_divided_allowed = #{noDividedAllowed},</if>
            <if test="noAgreementShowed != null">no_agreement_showed = #{noAgreementShowed},</if>
            <if test="isCustomsIntransitShowed != null">is_customs_intransit_showed = #{isCustomsIntransitShowed},</if>
            <if test="psaVerifyStatusId != null">psa_verify_status_id = #{psaVerifyStatusId},</if>
            <if test="opLeaderVerifyStatusId != null">op_leader_verify_status_id = #{opLeaderVerifyStatusId},</if>
            <if test="precarriageSupplierNo != null">precarriage_supplier_no = #{precarriageSupplierNo},</if>
            <if test="agreementTypeCode != null">agreement_type_code = #{agreementTypeCode},</if>
            <if test="opAccept != null">op_accept = #{opAccept},</if>
            <if test="psaVerify != null">psa_verify = #{psaVerify},</if>
            <if test="firstAtd != null">first_atd = #{firstAtd},</if>
            <if test="destinationPortAta != null">destination_port_ata = #{destinationPortAta},</if>
            <if test="bookingAgentRemark != null">booking_agent_remark = #{bookingAgentRemark},</if>
            <if test="shippingMark != null">shipping_mark = #{shippingMark},</if>
            <if test="freightPaidWayCode != null">freight_paid_way_code = #{freightPaidWayCode},</if>
            <if test="ctnrTypeCode != null">ctnr_type_code = #{ctnrTypeCode},</if>
            <if test="etd != null">etd = #{etd},</if>
            <if test="eta != null">eta = #{eta},</if>
            <if test="sqdDnReceiveSlipStatus != null">sqd_dn_receive_slip_status = #{sqdDnReceiveSlipStatus},</if>
            <if test="sqdDnPaySlipStatus != null">sqd_dn_pay_slip_status = #{sqdDnPaySlipStatus},</if>
            <if test="paymentNode != null">payment_node = #{paymentNode},</if>
            <if test="psaRctId != null">psa_rct_id = #{psaRctId},</if>
            <if test="sqdPsaNo != null">sqd_psa_no = #{sqdPsaNo},</if>
            <if test="opAskingBlGetTime != null">op_asking_bl_get_time = #{opAskingBlGetTime},</if>
            <if test="opAskingBlReleaseTime != null">op_asking_bl_release_time = #{opAskingBlReleaseTime},</if>
            <if test="accPromissBlGetTime != null">acc_promiss_bl_get_time = #{accPromissBlGetTime},</if>
            <if test="actualBlGotTime != null">actual_bl_got_time = #{actualBlGotTime},</if>
            <if test="accPromissBlReleaseTime != null">acc_promiss_bl_release_time = #{accPromissBlReleaseTime},</if>
            <if test="actualBlReleaseTime != null">actual_bl_release_time = #{actualBlReleaseTime},</if>
            <if test="agentNoticeTime != null">agent_notice_time = #{agentNoticeTime},</if>
            <if test="bookingStatus != null">booking_status = #{bookingStatus},</if>
            <if test="secondVessel != null">second_vessel = #{secondVessel},</if>
            <if test="statusUpdateTime != null">status_update_time = #{statusUpdateTime},</if>
            <if test="quotationInRmb != null">quotation_in_rmb = #{quotationInRmb},</if>
            <if test="inquiryInRmb != null">inquiry_in_rmb = #{inquiryInRmb},</if>
            <if test="estimatedProfitInRmb != null">estimated_profit_in_rmb = #{estimatedProfitInRmb},</if>
            <if test="dnUsd != null">dn_usd = #{dnUsd},</if>
            <if test="dnRmb != null">dn_rmb = #{dnRmb},</if>
            <if test="dnUsdBalance != null">dn_usd_balance = #{dnUsdBalance},</if>
            <if test="dnRmbBalance != null">dn_rmb_balance = #{dnRmbBalance},</if>
            <if test="dnInRmb != null">dn_in_rmb = #{dnInRmb},</if>
            <if test="dnInRmbBalance != null">dn_in_rmb_balance = #{dnInRmbBalance},</if>
            <if test="cnUsd != null">cn_usd = #{cnUsd},</if>
            <if test="cnRmb != null">cn_rmb = #{cnRmb},</if>
            <if test="cnUsdBalance != null">cn_usd_balance = #{cnUsdBalance},</if>
            <if test="cnRmbBalance != null">cn_rmb_balance = #{cnRmbBalance},</if>
            <if test="cnInRmb != null">cn_in_rmb = #{cnInRmb},</if>
            <if test="cnInRmbBalance != null">cn_in_rmb_balance = #{cnInRmbBalance},</if>
            <if test="profitUsd != null">profit_usd = #{profitUsd},</if>
            <if test="profitRmb != null">profit_rmb = #{profitRmb},</if>
            <if test="profitInRmb != null">profit_in_rmb = #{profitInRmb},</if>
            <if test="differenceInRmb != null">difference_in_rmb = #{differenceInRmb},</if>
        </trim>
        where rct_id = #{rctId}
    </update>


    <!--<delete id="deleteRsRctByRctId" parameterType="Long">
        delete
        from rs_rct
        where rct_id = #{rctId}
    </delete>-->
    <update id="deleteRsRctByRctId" parameterType="Long">
        update rs_rct
        set delete_status='1'
        where rct_id = #{rctId}
    </update>

    <!--<delete id="deleteRsRctByRctIds" parameterType="String">
        delete from rs_rct where rct_id in
        <foreach item="rctId" collection="array" open="(" separator="," close=")">
            #{rctId}
        </foreach>
    </delete>-->
    <update id="deleteRsRctByRctIds" parameterType="String">
        update rs_rct
        set delete_status='1'
        where rct_id in
        <foreach item="rctId" collection="array" open="(" separator="," close=")">
            #{rctId}
        </foreach>
    </update>


    <select id="getMon" resultType="java.lang.Integer">
        select count(*)
        from rs_rct
        where rs_rct.rct_no like concat(
            '',
            'RCT',
            RIGHT(YEAR(CURRENT_DATE), 2),
            LPAD(MONTH(CURRENT_DATE), 2, '0'),
            '%'
            ) and order_belongs_to <![CDATA[ <> ]]> 'GZCF' and rct_no is not null
    </select>

    <select id="selectRsRctByCompanyId" resultMap="RsRctResult">
        select rct_id,
               rct_no
        from rich.rs_rct rr
                 left join rich.ext_company ec on ec.company_id = rr.client_id
        where client_id = #{companyId}
    </select>

    <select id="getCFMon" resultType="int">
        select count(*)
        from rs_rct
        where rs_rct.rct_no like concat(
                '',
                'CFL',
                RIGHT(YEAR(CURRENT_DATE), 2),
                LPAD(MONTH(CURRENT_DATE), 2, '0'),
                '%'
            ) and order_belongs_to = 'GZCF' and rct_no is not null
    </select>

    <select id="selectRctIdByRctNo" resultType="java.lang.Long">
        select rct_id from rs_rct where rct_no = #{rctNo}
    </select>

    <select id="statisticsOp" resultMap="StatisticsOpDTOResult">
        SELECT
            rs.staff_short_name  op_name,
            COUNT(*) number
        FROM rs_rct rr
                 left join rs_staff rs on rs.staff_id = rr.op_id
        WHERE rr.op_id IS NOT NULL
        GROUP BY rr.op_id, rs.staff_short_name
        ORDER BY COUNT(*) DESC
    </select>

    <select id="selectRsRctListToExport" resultMap="RsRctListToExport">
        select bdd.dept_short_name as dept_name,
               rct_no,
               concat(rs1.staff_family_local_name, rs1.staff_giving_local_name)  as sales_name,
               concat(rs2.staff_family_local_name, rs1.staff_giving_local_name) as  op_name,
               bdlt.service_short_name                                         as logistics_type_name,
               IF(FIND_IN_SET('1', rr.service_type_id_list) > 0, 'FCL', 'LCL') AS service_type,
               rr.revenue_ton as revenue_ton,
               (case
                    when bdlt.service_short_name = 'FCL' or bdlt.service_short_name = 'LCL' then concat(
                            dep.location_local_name, '(', ifnull(dep.port_code, ''),
                            ')')
                    when bdlt.service_short_name = 'RailFCL' or bdlt.service_short_name = 'RAILLCL' then concat(
                            dep.location_local_name, '(',
                            ifnull(dep.port_rail_code, ''), ')')
                    when bdlt.service_short_name = 'AirFCL' or bdlt.service_short_name = 'AIR' then concat(
                            dep.location_local_name, '(',
                            ifnull(dep.port_iata_code, ''), ')')
                    else dep.location_local_name
                   end
                   )                                                           as pol,
               (case
                    when bdlt.service_short_name = 'FCL' or bdlt.service_short_name = 'LCL' then concat(
                            dep2.location_en_name, '(', ifnull(dep2.port_code, ''),
                            ')')
                    when bdlt.service_short_name = 'RailFCL' or bdlt.service_short_name = 'RailLCL' then concat(
                            dep2.location_en_name, '(',
                            ifnull(dep2.port_rail_code, ''), ')')
                    when bdlt.service_short_name = 'AirFCL' or bdlt.service_short_name = 'AIR' then concat(
                            dep2.location_en_name, '(',
                            ifnull(dep2.port_iata_code, ''), ')')
                    else dep2.location_en_name
                   end
                   )                                                           as destination_port,
               rr.rct_create_time,
               rr.etd
        from rs_rct rr
                 left join bas_dist_service_type bdlt on rr.logistics_type_id = bdlt.service_type_id
                 left join bas_dist_location dep on rr.pol_id = dep.location_id
                 left join bas_dist_location dep2 on rr.destination_port_id = dep2.location_id
                 left join rich.ext_company ec on ec.company_id = rr.client_id
                 left join rs_staff rs1 on rs1.staff_id = rr.sales_id
                 left join rs_staff rs2 on rs2.staff_id = rr.op_id
                 left join mid_rs_staff_role mrsr on mrsr.staff_id = rr.sales_id and mrsr.is_main = 'Y'
                 left join bas_dist_dept bdd on bdd.dept_id = mrsr.dept_id
    </select>

    <select id="getRSWHMon" resultType="int">
        select count(*)
        from rs_rct
        where rs_rct.rct_no like concat(
                '',
                'RSH',
                RIGHT(YEAR(CURRENT_DATE), 2),
                LPAD(MONTH(CURRENT_DATE), 2, '0'),
                '%'
            ) and order_belongs_to = 'RSWH' and rct_no is not null
    </select>

    <select id="opNotification" resultType="int">
        select count(*) from rs_rct
        <where>
            psa_verify_status_id = 1 and op_accept = 0 and delete_status = '0'
            <if test="opId != null">
                and op_id = #{opId}
            </if>
        </where>
    </select>

    <select id="psaNotification" resultType="int">
        select count(*)
        from rs_rct
        where sqd_shipping_booking_status = 1
          and psa_verify = 0
    </select>

    <!--<update id="updateByWriteoff">
        update rich.rs_rct set rich.rs_rct.dn_rmb_balance = (SELECT SUM(sqd_dn_currency_balance) AS total_balance
                                                             FROM rs_charge
                                                             WHERE sqd_rct_no =#{rctNo} and is_recieving_or_paying = 0 and dn_currency_code = 'RMB'),
            rich.rs_rct.dn_usd_balance = (SELECT SUM(sqd_dn_currency_balance) AS total_balance
                                          FROM rs_charge
                                          WHERE sqd_rct_no =#{rctNo} and is_recieving_or_paying = 0 and dn_currency_code = 'USD'),
            rich.rs_rct.dn_in_rmb_balance = (SELECT SUM(sqd_dn_currency_balance) AS total_balance
                                             FROM rs_charge
                                             WHERE sqd_rct_no =#{rctNo} and is_recieving_or_paying = 1 and dn_currency_code = 'RMB'),
            rich.rs_rct.cn_rmb_balance =(SELECT SUM(sqd_dn_currency_balance) AS total_balance
                                         FROM rs_charge
                                         WHERE sqd_rct_no =#{rctNo} and is_recieving_or_paying = 1 and dn_currency_code = 'RMB'),
            rich.rs_rct.cn_usd_balance = (SELECT SUM(sqd_dn_currency_balance) AS total_balance
                                          FROM rs_charge
                                          WHERE sqd_rct_no =#{rctNo} and is_recieving_or_paying = 1 and dn_currency_code = 'USD'),
            rich.rs_rct.cn_in_rmb_balance =
        where rich.rs_rct.rct_no = #{rctNo}
    </update>-->
    <update id="updateByWriteoff">
        UPDATE rs_rct r
        SET r.dn_rmb_balance = (SELECT COALESCE(SUM(
                                                        CASE
                                                            WHEN c.dn_currency_code = 'RMB'
                                                                THEN c.sqd_dn_currency_balance
                                                            else 0
                                                            END
                                                ), 0)
                                FROM rs_charge c
                                WHERE c.sqd_rct_no = r.rct_no
                                  AND c.is_recieving_or_paying = 0),
            r.dn_usd_balance = (SELECT COALESCE(SUM(
                                                        CASE
                                                            WHEN c.dn_currency_code = 'USD'
                                                                THEN c.sqd_dn_currency_balance
                                                            else 0
                                                            END
                                                ), 0)
                                FROM rs_charge c
                                WHERE c.sqd_rct_no = r.rct_no
                                  AND c.is_recieving_or_paying = 0),
            r.cn_rmb_balance     = (SELECT COALESCE(SUM(
                                                            CASE
                                                                WHEN c.dn_currency_code = 'RMB'
                                                                    THEN c.sqd_dn_currency_balance
                                                                ELSE 0
                                                                END
                                                    ), 0)
                                    FROM rs_charge c
                                    WHERE c.sqd_rct_no = r.rct_no
                                      AND c.is_recieving_or_paying = 1),
            r.cn_usd_balance     = (SELECT COALESCE(SUM(
                                                            CASE
                                                                WHEN c.dn_currency_code = 'USD'
                                                                    THEN c.sqd_dn_currency_balance
                                                                ELSE 0
                                                                END
                                                    ), 0)
                                    FROM rs_charge c
                                    WHERE c.sqd_rct_no = r.rct_no
                                      AND c.is_recieving_or_paying = 1),
            r.dn_in_rmb_balance = (SELECT COALESCE(SUM(
                                                           CASE
                                                               WHEN c.dn_currency_code = 'USD'
                                                                   THEN c.sqd_dn_currency_balance * ifnull((select CAST(settle_rate AS DECIMAL(10, 2)) / CAST(100 AS DECIMAL(10, 2)) from bas_exchange_rate where r.pod_eta between valid_from and valid_to),(select CAST(settle_rate AS DECIMAL(10, 2)) / CAST(100 AS DECIMAL(10, 2)) from bas_exchange_rate where current_timestamp between valid_from and valid_to))
                                                               ELSE c.sqd_dn_currency_balance
                                                               END
                                                   ), 0)
                                   FROM rs_charge c
                                   WHERE c.sqd_rct_no = r.rct_no
                                     AND c.is_recieving_or_paying = 0),
            r.cn_in_rmb_balance = (SELECT COALESCE(SUM(
                                                           CASE
                                                               WHEN c.dn_currency_code = 'USD'
                                                                   THEN c.sqd_dn_currency_balance * ifnull((select CAST(settle_rate AS DECIMAL(10, 2)) / CAST(100 AS DECIMAL(10, 2)) from bas_exchange_rate where r.pod_eta between valid_from and valid_to),(select CAST(settle_rate AS DECIMAL(10, 2)) / CAST(100 AS DECIMAL(10, 2)) from bas_exchange_rate where current_timestamp between valid_from and valid_to))
                                                               ELSE c.sqd_dn_currency_balance
                                                               END
                                                   ), 0)
                                   FROM rs_charge c
                                   WHERE c.sqd_rct_no = r.rct_no
                                     AND c.is_recieving_or_paying = 1)
        where r.rct_no IN
        <foreach item="rctNo" collection="list" open="(" separator="," close=")">
            #{rctNo}
        </foreach>;
    </update>
</mapper>