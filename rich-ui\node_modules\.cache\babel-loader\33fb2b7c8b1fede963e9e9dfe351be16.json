{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\communication\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\communication\\index.vue", "mtime": 1722505519704}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_communication", "require", "_extStaff", "_utils", "_processtype", "name", "props", "data", "extStaffOptions", "processtypeList", "id", "$store", "state", "user", "sid", "openContent", "mainStaffId", "oopen", "size", "app", "loading", "ids", "single", "multiple", "showSearch", "total", "communicationList", "title", "queryParams", "pageNum", "pageSize", "sqdCompanyId", "staffId", "extStaffId", "stageId", "issueId", "content", "score", "form", "rules", "watch", "loadOptions", "open", "val", "$emit", "created", "_this", "listProcesstype", "then", "response", "rows", "methods", "getList", "_this2", "company", "companyId", "listCommunication", "cancel", "reset", "communicationId", "orderNum", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "reset<PERSON><PERSON>y", "handleQuery", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "_this3", "listExtStaff", "communicationDatetime", "formatDate", "Date", "handleUpdate", "row", "_this4", "getCommunication", "submitForm", "_this5", "$refs", "validate", "valid", "updateCommunication", "$modal", "msgSuccess", "addCommunication", "handleDelete", "_this6", "communicationIds", "$confirm", "customClass", "delCommunication", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "getTime", "getStageId", "getIssueId", "exports", "_default"], "sources": ["src/views/system/communication/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag\r\n      v-dialogDragWidth\r\n      :append-to-body=\"true\"\r\n      :visible.sync=\"openContent\"\r\n      title=\"沟通记录\"\r\n      width=\"1500px\"\r\n    >\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:communication:add']\"\r\n            icon=\"el-icon-plus\"\r\n            plain\r\n            size=\"mini\"\r\n            type=\"primary\"\r\n            @click=\"handleAdd\"\r\n          >新增\r\n          </el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:communication:edit']\"\r\n            :disabled=\"single\"\r\n            icon=\"el-icon-edit\"\r\n            plain\r\n            size=\"mini\"\r\n            type=\"success\"\r\n            @click=\"handleUpdate\"\r\n          >修改\r\n          </el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:communication:remove']\"\r\n            :disabled=\"multiple\"\r\n            icon=\"el-icon-delete\"\r\n            plain\r\n            size=\"mini\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete\"\r\n          >删除\r\n          </el-button>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-table v-loading=\"loading\" :data=\"communicationList\" @selection-change=\"handleSelectionChange\" border stripe>\r\n        <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n        <el-table-column key=\"shortName\" label=\"员工信息\" show-tooltip-when-overflow width=\"100px\">\r\n          <template slot-scope=\"scope\">\r\n            {{ company.companyShortName }}\r\n            {{\r\n              scope.row.extStaff != null ? (scope.row.extStaff.staffLocalName != null ? scope.row.extStaff.staffLocalName : '') + ' ' + (scope.row.extStaff.staffEnName != null ? scope.row.extStaff.staffEnName : '') : ''\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"物流节点\" prop=\"stage.stageLocalName\" width=\"68px\"/>\r\n        <el-table-column align=\"center\" label=\"问题\" prop=\"issue.issueLocalName\" width=\"88px\"/>\r\n        <el-table-column align=\"center\" label=\"沟通详细\" prop=\"content\" show-tooltip-when-overflow/>\r\n        <el-table-column align=\"center\" label=\"评分\" prop=\"score\" width=\"48px\"/>\r\n        <el-table-column align=\"center\" label=\"优先度\" prop=\"orderNum\" width=\"58px\"/>\r\n        <el-table-column align=\"center\" label=\"备注\" prop=\"remark\"/>\r\n        <el-table-column key=\"rsStaff.staffLocalName\" align=\"center\" label=\"跟进人\" width=\"120px\">\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.rsStaff != null ? (scope.row.rsStaff.staffLocalName != null ? scope.row.rsStaff.staffLocalName : '') + ' ' + (scope.row.rsStaff.staffEnName != null ? scope.row.rsStaff.staffEnName : '') : ''\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"沟通时间\" prop=\"communicationDatetime\" width=\"135px\"/>\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"48px\">\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"height: 15px;padding: 0;margin: 0\">\r\n              <el-button v-hasPermi=\"['system:communication:edit']\" :size=\"size\" icon=\"el-icon-edit\"\r\n                         style=\"display: flex\"\r\n                         type=\"success\" @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n            </div>\r\n            <div style=\"height: 15px;padding: 0;margin: 0\">\r\n              <el-button v-if=\"id==scope.row.staffId||id==1\" v-hasPermi=\"['system:communication:remove']\" :size=\"size\"\r\n                         icon=\"el-icon-delete\"\r\n                         style=\"display: flex\" type=\"danger\"\r\n                         @click=\"handleDelete(scope.row)\"\r\n              >\r\n                删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :total=\"total\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-dialog>\r\n    <!-- 添加或修改交流对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"oopen\" append-to-body\r\n      width=\"800px\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"公司\" prop=\"companyId\">\r\n            <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ this.company.companyShortName }}</a>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"12\">\r\n          <el-form-item label=\"沟通时间\">\r\n            <el-date-picker v-model=\"form.communicationDatetime\"\r\n                            format=\"yyyy 年 MM 月 dd 日 HH 时 mm 分 ss 秒\"\r\n                            placeholder=\"选择日期\"\r\n                            style=\"width: 100%;\"\r\n                            type=\"datetime\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-form-item label=\"沟通员工\" prop=\"extStaffId\">\r\n          <el-select v-model=\"form.extStaffId\" filterable placeholder=\"\" style=\"width: 100%;\">\r\n            <el-option\r\n              v-for=\"item in extStaffOptions\"\r\n              :key=\"item.staffId\"\r\n              :label=\"(item.staffLocalName!=null?item.staffLocalName:'')+' '+(item.staffEnName!=null?item.staffEnName:'')\"\r\n              :value=\"item.staffId\"\r\n            >\r\n            </el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"物流节点\" prop=\"stageId\">\r\n          <el-select v-model=\"form.processTypeId\" placeholder=\"进度分类\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in processtypeList\"\r\n              :key=\"dict.processTypeId\"\r\n              :label=\"dict.processTypeLocalName\"\r\n              :value=\"dict.processTypeId\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"问题\" prop=\"issueId\">\r\n          <tree-select :pass=\"form.issueId\" :placeholder=\"''\" :type=\"'issue'\" @return=\"getIssueId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"沟通详细\">\r\n          <el-input v-model=\"form.content\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"300\"\r\n                    placeholder=\"沟通详细\"\r\n                    show-word-limit type=\"textarea\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :rows=\"3\" maxlength=\"150\" placeholder=\"备注\" show-word-limit\r\n                    type=\"textarea\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"评分1~10\" prop=\"score\">\r\n          <el-input-number v-model=\"form.score\" :controls=\"false\" :max=\"10\" :min=\"1\" placeholder=\"评分1~10\"\r\n                           style=\"width: 100%;\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"优先度\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" placeholder=\"优先度\" style=\"width: 100%;\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCommunication,\r\n  delCommunication,\r\n  getCommunication,\r\n  listCommunication,\r\n  updateCommunication\r\n} from '@/api/system/communication'\r\nimport {listExtStaff} from '@/api/system/extStaff'\r\nimport {formatDate} from '@/utils'\r\nimport {listProcesstype} from '@/api/system/processtype'\r\n\r\nexport default {\r\n  name: 'communications',\r\n  props: ['type', 'open', 'loadOptions', 'company'],\r\n  data() {\r\n    return {\r\n      extStaffOptions: [],\r\n      processtypeList: [],\r\n      id: this.$store.state.user.sid,\r\n      openContent: false,\r\n      mainStaffId: null,\r\n      oopen: false,\r\n      size: this.$store.state.app.size || 'mini',\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 交流表格数据\r\n      communicationList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        sqdCompanyId: null,\r\n        staffId: null,\r\n        extStaffId: null,\r\n        stageId: null,\r\n        issueId: null,\r\n        content: null,\r\n        score: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n  },\r\n  watch: {\r\n    loadOptions: function () {\r\n      this.communicationList = this.loadOptions\r\n      this.loading = false\r\n    },\r\n    open: function (val) {\r\n      this.openContent = val\r\n    },\r\n    openContent(val) {\r\n      if (val == false) {\r\n        this.$emit('openCommunications')\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    listProcesstype({pageNum: 1, pageSize: 100}).then(response => {\r\n      this.processtypeList = response.rows\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询交流列表 */\r\n    getList() {\r\n      this.loading = true\r\n      this.queryParams.sqdCompanyId = this.company.companyId\r\n      listCommunication(this.queryParams).then(response => {\r\n        this.communicationList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.oopen = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        communicationId: null,\r\n        staffId: this.$store.state.user.id,\r\n        extStaffId: this.company.mainStaffId,\r\n        stageId: null,\r\n        issueId: null,\r\n        content: null,\r\n        score: null,\r\n        orderNum: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: 0\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.communicationId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.oopen = true\r\n      this.title = '添加记录'\r\n      listExtStaff({sqdCompanyId: this.company.companyId}).then(response => {\r\n        this.extStaffOptions = response.data\r\n        this.form.extStaffId = this.company.mainStaffId\r\n      })\r\n      this.form.communicationDatetime = formatDate(new Date())\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const communicationId = row.communicationId || this.ids\r\n      getCommunication(communicationId).then(response => {\r\n        this.form = response.data\r\n        this.oopen = true\r\n        this.title = '修改交流'\r\n        this.getList()\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          this.form.sqdCompanyId = this.company.companyId\r\n          if (this.form.communicationId != null) {\r\n            updateCommunication(this.form).then(response => {\r\n              this.$modal.msgSuccess('修改成功')\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addCommunication(this.form).then(response => {\r\n              this.$modal.msgSuccess('新增成功')\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const communicationIds = row.communicationId || this.ids\r\n      this.$confirm('是否确认删除交流编号为\"' + communicationIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCommunication(communicationIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/communication/export', {\r\n        ...this.queryParams\r\n      }, `communication_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getStageId(val) {\r\n      this.form.stageId = val\r\n    },\r\n    getIssueId(val) {\r\n      this.form.issueId = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;AAmLA,IAAAA,cAAA,GAAAC,OAAA;AAOA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAI,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA;MACAC,eAAA;MACAC,EAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;MACAC,WAAA;MACAC,WAAA;MACAC,KAAA;MACAC,IAAA,OAAAP,MAAA,CAAAC,KAAA,CAAAO,GAAA,CAAAD,IAAA;MACA;MACAE,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,iBAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA;QACAC,OAAA;QACAC,UAAA;QACAC,OAAA;QACAC,OAAA;QACAC,OAAA;QACAC,KAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAf,iBAAA,QAAAe,WAAA;MACA,KAAArB,OAAA;IACA;IACAsB,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAA5B,WAAA,GAAA4B,GAAA;IACA;IACA5B,WAAA,WAAAA,YAAA4B,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,KAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,4BAAA;MAAAlB,OAAA;MAAAC,QAAA;IAAA,GAAAkB,IAAA,WAAAC,QAAA;MACAH,KAAA,CAAArC,eAAA,GAAAwC,QAAA,CAAAC,IAAA;IACA;EACA;EACAC,OAAA;IACA,aACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,MAAA;MACA,KAAAjC,OAAA;MACA,KAAAQ,WAAA,CAAAG,YAAA,QAAAuB,OAAA,CAAAC,SAAA;MACA,IAAAC,gCAAA,OAAA5B,WAAA,EAAAoB,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAA3B,iBAAA,GAAAuB,QAAA,CAAAC,IAAA;QACAG,MAAA,CAAA5B,KAAA,GAAAwB,QAAA,CAAAxB,KAAA;QACA4B,MAAA,CAAAjC,OAAA;MACA;IACA;IACA;IACAqC,MAAA,WAAAA,OAAA;MACA,KAAAxC,KAAA;MACA,KAAAyC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAApB,IAAA;QACAqB,eAAA;QACA3B,OAAA,OAAArB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAH,EAAA;QACAuB,UAAA,OAAAqB,OAAA,CAAAtC,WAAA;QACAkB,OAAA;QACAC,OAAA;QACAC,OAAA;QACAC,KAAA;QACAuB,QAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,UAAA,WAAAA,WAAA;MACA,KAAAD,SAAA;MACA,KAAAE,WAAA;IACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApD,GAAA,GAAAoD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAhB,eAAA;MAAA;MACA,KAAArC,MAAA,GAAAmD,SAAA,CAAAG,MAAA;MACA,KAAArD,QAAA,IAAAkD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAApB,KAAA;MACA,KAAAzC,KAAA;MACA,KAAAU,KAAA;MACA,IAAAoD,sBAAA;QAAAhD,YAAA,OAAAuB,OAAA,CAAAC;MAAA,GAAAP,IAAA,WAAAC,QAAA;QACA6B,MAAA,CAAAtE,eAAA,GAAAyC,QAAA,CAAA1C,IAAA;QACAuE,MAAA,CAAAxC,IAAA,CAAAL,UAAA,GAAA6C,MAAA,CAAAxB,OAAA,CAAAtC,WAAA;MACA;MACA,KAAAsB,IAAA,CAAA0C,qBAAA,OAAAC,iBAAA,MAAAC,IAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA3B,KAAA;MACA,IAAAC,eAAA,GAAAyB,GAAA,CAAAzB,eAAA,SAAAtC,GAAA;MACA,IAAAiE,+BAAA,EAAA3B,eAAA,EAAAX,IAAA,WAAAC,QAAA;QACAoC,MAAA,CAAA/C,IAAA,GAAAW,QAAA,CAAA1C,IAAA;QACA8E,MAAA,CAAApE,KAAA;QACAoE,MAAA,CAAA1D,KAAA;QACA0D,MAAA,CAAAjC,OAAA;MACA;IACA;IACA,WACAmC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAlD,IAAA,CAAAP,YAAA,GAAAyD,MAAA,CAAAlC,OAAA,CAAAC,SAAA;UACA,IAAAiC,MAAA,CAAAlD,IAAA,CAAAqB,eAAA;YACA,IAAAiC,kCAAA,EAAAJ,MAAA,CAAAlD,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAvE,KAAA;cACAuE,MAAA,CAAApC,OAAA;YACA;UACA;YACA,IAAA2C,+BAAA,EAAAP,MAAA,CAAAlD,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAvE,KAAA;cACAuE,MAAA,CAAApC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA4C,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,gBAAA,GAAAd,GAAA,CAAAzB,eAAA,SAAAtC,GAAA;MACA,KAAA8E,QAAA,kBAAAD,gBAAA;QAAAE,WAAA;MAAA,GAAApD,IAAA;QACA,WAAAqD,+BAAA,EAAAH,gBAAA;MACA,GAAAlD,IAAA;QACAiD,MAAA,CAAA7C,OAAA;QACA6C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,oCAAAC,cAAA,CAAAC,OAAA,MACA,KAAA9E,WAAA,oBAAA+E,MAAA,CACA,IAAAzB,IAAA,GAAA0B,OAAA;IACA;IACAC,UAAA,WAAAA,WAAAlE,GAAA;MACA,KAAAL,IAAA,CAAAJ,OAAA,GAAAS,GAAA;IACA;IACAmE,UAAA,WAAAA,WAAAnE,GAAA;MACA,KAAAL,IAAA,CAAAH,OAAA,GAAAQ,GAAA;IACA;EACA;AACA;AAAAoE,OAAA,CAAAL,OAAA,GAAAM,QAAA"}]}