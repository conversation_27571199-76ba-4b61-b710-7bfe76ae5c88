{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\freight.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\freight.js", "mtime": 1678688095229}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkRnJlaWdodCA9IGFkZEZyZWlnaHQ7CmV4cG9ydHMuZGVsRnJlaWdodCA9IGRlbEZyZWlnaHQ7CmV4cG9ydHMuZ2V0RnJlaWdodCA9IGdldEZyZWlnaHQ7CmV4cG9ydHMubGlzdEZyZWlnaHQgPSBsaXN0RnJlaWdodDsKZXhwb3J0cy51cGRhdGVGcmVpZ2h0ID0gdXBkYXRlRnJlaWdodDsKdmFyIF9yZXF1ZXN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJAL3V0aWxzL3JlcXVlc3QiKSk7Ci8vIOafpeivouWfuuehgOi/kOi0ueWIl+ihqApmdW5jdGlvbiBsaXN0RnJlaWdodChxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9mcmVpZ2h0L2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5Z+656GA6L+Q6LS56K+m57uGCmZ1bmN0aW9uIGdldEZyZWlnaHQoZnJlaWdodElkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2ZyZWlnaHQvJyArIGZyZWlnaHRJZCwKICAgIG1ldGhvZDogJ2dldCcKICB9KTsKfQoKLy8g5paw5aKe5Z+656GA6L+Q6LS5CmZ1bmN0aW9uIGFkZEZyZWlnaHQoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9mcmVpZ2h0JywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnln7rnoYDov5DotLkKZnVuY3Rpb24gdXBkYXRlRnJlaWdodChkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2ZyZWlnaHQnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk5Z+656GA6L+Q6LS5CmZ1bmN0aW9uIGRlbEZyZWlnaHQoZnJlaWdodElkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2ZyZWlnaHQvJyArIGZyZWlnaHRJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listFreight", "query", "request", "url", "method", "params", "getFreight", "freightId", "addFreight", "data", "updateFreight", "delFreight"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/freight.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询基础运费列表\r\nexport function listFreight(query) {\r\n  return request({\r\n    url: '/system/freight/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询基础运费详细\r\nexport function getFreight(freightId) {\r\n  return request({\r\n    url: '/system/freight/' + freightId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增基础运费\r\nexport function addFreight(data) {\r\n  return request({\r\n    url: '/system/freight',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改基础运费\r\nexport function updateFreight(data) {\r\n  return request({\r\n    url: '/system/freight',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除基础运费\r\nexport function delFreight(freightId) {\r\n  return request({\r\n    url: '/system/freight/' + freightId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,SAAS,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,SAAS;IACnCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAACD,IAAI,EAAE;EAClC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,UAAUA,CAACJ,SAAS,EAAE;EACpC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGI,SAAS;IACnCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}