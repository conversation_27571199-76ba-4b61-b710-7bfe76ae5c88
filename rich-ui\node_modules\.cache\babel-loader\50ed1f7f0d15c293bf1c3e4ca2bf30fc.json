{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\selectCompany.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\selectCompany.vue", "mtime": 1743675026860}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_company", "require", "_role", "_message", "_communication", "_agreementrecord", "_js<PERSON><PERSON>yin", "_interopRequireDefault", "_store", "_vueTreeselect", "_BlackList", "_communication2", "_agreementRecord", "_staffInfo", "_accountInfo", "_company2", "_contactor", "_location", "_role2", "_serviceType", "_departure", "_destination", "_cargoType", "_carrier", "_account", "_agreement", "_communication3", "_grade", "_achievement", "_remark", "_belong", "_auth", "_rich", "_elementUi", "_index", "_rsPaymentTitle", "name", "dicts", "components", "Confirmed", "Treeselect", "communication", "communications", "BlackList", "belong", "company", "contactor", "staffInfo", "location", "role", "serviceType", "departure", "destination", "cargoType", "carrier", "account", "accountInfo", "agreement", "agreementRecord", "grade", "achievement", "remark", "rsPaymentTitle", "props", "data", "loading", "showLeft", "showRight", "single", "multiple", "ids", "showSearch", "total", "add", "selectTwo", "size", "$store", "state", "app", "mergeList", "companyList", "staffList", "accountList", "communicationList", "agreementList", "belongList", "carrierList", "businessList", "temCarrierList", "locationOptions", "carrierIds", "companyInfo", "queryCarrierIds", "title", "merge", "openCompany", "openStaff", "openAccount", "openCommunication", "openAgreement", "openBlackList", "edit", "belongTo", "followUp", "queryBFStaffId", "queryBStaffId", "refreshTable", "ctotle", "atotle", "queryParams", "pageNum", "pageSize", "roleTypeId", "companyQuery", "locationId", "idleStatus", "queryStaffId", "showPriority", "serviceTypeIds", "cargoTypeIds", "locationDepartureIds", "lineDepartureIds", "locationDestinationIds", "lineDestinationIds", "roleIds", "form", "agreementStartDate", "agreementEndDate", "settlementDate", "rules", "companyRow", "isLock", "showConfirm", "computed", "columns", "get", "listSettings", "supplierSetting", "clientSetting", "watch", "n", "formBelongTo", "formServiceTypeIds", "loadCarrier", "list", "undefined", "includes", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "done", "v", "value", "children", "length", "_iterator2", "_step2", "a", "_iterator3", "_step3", "b", "carrierId", "serviceTypeId", "push", "err", "e", "f", "_iterator4", "_step4", "c", "_iterator7", "_step7", "_iterator8", "_step8", "ch", "_iterator5", "_step5", "_iterator6", "_step6", "salesConfirmed", "accConfirmed", "psaConfirmed", "opConfirmed", "created", "_this", "getList", "then", "loadBusinesses", "loadSales", "methods", "handleSelect", "row", "$emit", "_this2", "salesList", "redisList", "store", "dispatch", "_this3", "businessesList", "_this4", "serviceTypeCarriers", "querySame", "_this5", "companyShortName", "companyEnShortName", "deleteStatus", "getInfoByStaffId", "user", "sid", "response", "_iterator9", "_step9", "_iterator10", "_step10", "_iterator11", "_step11", "staffId", "$refs", "validate", "valid", "res", "$confirm", "confirmButtonText", "cancelButtonText", "type", "_iterator12", "_step12", "_iterator13", "_step13", "_iterator14", "_step14", "deptId", "organizationIds", "companyOrganizationIds", "msg", "toString", "indexOf", "messageOwner", "messageType", "messageFrom", "messageTitle", "split", "messageContent", "addMessage", "$message", "message", "success", "_iterator15", "_step15", "_iterator16", "_step16", "_iterator17", "_step17", "_this6", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "listCompany", "_objectSpread2", "roleClient", "permissionLevel", "permissionLevelList", "C", "rows", "isNaN", "stop", "staffNormalizer", "node", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "id", "label", "isDisabled", "carrierNormalizer", "carrierLocalName", "carrierEnName", "serviceLocalName", "serviceEnName", "carrierIntlCode", "cancel", "reset", "accountCancel", "locationDetail", "companyId", "companyIntlCode", "companyLocalName", "companyEnName", "companyTaxCode", "companyPortIds", "salesConfirmedId", "salesConfirmedName", "salesConfirmedDate", "psaConfirmedId", "psaConfirmedName", "psaConfirmedDate", "accConfirmedId", "accConfirmedName", "accConfirmedDate", "opConfirmedId", "opConfirmedName", "opConfirmedDate", "rsPaymentTitles", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "setCompanyInfo", "handleAdd", "_iterator18", "_step18", "_iterator19", "_step19", "_iterator20", "_step20", "_iterator21", "_step21", "agreementCurrencyCode", "getReturn", "_this7", "key", "getConnect", "listCommunication", "sqdCompanyId", "totle", "listAgreementrecord", "getBank", "companyLocation", "mainStaffId", "handleUpdate", "_this8", "auth", "<PERSON><PERSON><PERSON><PERSON>", "getCompany", "_iterator22", "_step22", "_iterator23", "_step23", "_iterator24", "_step24", "agreementDateRange", "formatter", "Intl", "NumberFormat", "style", "minimumFractionDigits", "maximumFractionDigits", "creditLimit", "toLocaleString", "replace", "format", "submitForm", "_this9", "startDate", "Date", "endDate", "Message", "creditCycleMonth", "updateCompany", "$modal", "msgSuccess", "info", "handleDelete", "_this10", "companyIds", "customClass", "delCompany", "catch", "handleBlackList", "handleExport", "download", "concat", "getTime", "queryLocationId", "val", "getLocationId", "getSourceId", "sourceId", "sourceShortName", "getOrganizationIds", "getServiceTypeIds", "queryServiceTypeIds", "getCargoTypeIds", "queryCargoTypeIds", "getCompanyRoleIds", "queryCompanyRoleIds", "queryLocationDepartureIds", "getLineDepartureIds", "getLocationDestinationIds", "queryLineDepartureIds", "getLocationDepartureIds", "queryLocationDestinationIds", "getLineDestinationIds", "queryLineDestinationIds", "handleSelectBelongTo", "handleDeselectBelongTo", "handleSelectFollowUp", "handleDeselectFollowUp", "handleSelectBFStaffId", "cleanBFStaffId", "cleanBStaffId", "handleSelectBStaffId", "_this11", "handleSelectCarrierIds", "handleSelectQueryCarrierIds", "handleDeselectCarrierIds", "filter", "handleDeselectQueryCarrierIds", "refreshColumns", "_this12", "$nextTick", "handleMergeCompany", "handleMerge", "save", "del", "_this13", "mergeCompany", "dept<PERSON>ock", "_this14", "deptConfirmed", "deptConfirmedId", "deptConfirmedName", "deptConfirmedDate", "parseTime", "msgError", "financeLock", "_this15", "financeConfirmed", "financeConfirmedId", "financeConfirmedName", "financeConfirmedDate", "psaLock", "_this16", "opLock", "_this17", "getCurrencyCode", "getcreditLevel", "creditLevel", "getRsPaymentTitle", "_this18", "_iterator25", "_step25", "_iterator26", "_step26", "_iterator27", "_step27", "formatCreditLimit", "formatDisplayCreditLimit", "notation", "changeDate", "$forceUpdate", "exports", "_default"], "sources": ["src/views/system/company/selectCompany.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-if=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" :size=\"size\" class=\"query\"\r\n                 label-width=\"35px\"\r\n        >\r\n          <el-form-item v-if=\"roleTypeId==='1'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBFStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBFStaffId\" @open=\"loadSales\" @select=\"handleSelectBFStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"roleTypeId==='2'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBStaffId\" @open=\"loadBusinesses\" @select=\"handleSelectBStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"roleTypeId==='1'\" label=\"闲置\">\r\n                      <el-select v-model=\"queryParams.idleStatus\" placeholder=\"是否闲置\" style=\"width: 100%\"\r\n                                 @change=\"handleQuery\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.sys_is_idle\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>-->\r\n          <el-form-item label=\"搜索\" prop=\"companyQuery\">\r\n            <el-input v-model=\"queryParams.companyQuery\" clearable placeholder=\"名称/代码\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!--行政区域-->\r\n          <el-form-item label=\"地址\" prop=\"locationId\">\r\n            <location-select :load-options=\"locationOptions\" :multiple=\"false\"\r\n                             :pass=\"queryParams.locationId\" style=\"width: 100%\"\r\n                             @return=\"queryLocationId\"\r\n            />\r\n          </el-form-item>\r\n          <!--供应商公司的角色-->\r\n          <el-form-item label=\"角色\" prop=\"roleIds\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                         :pass=\"queryParams.roleIds\" :placeholder=\"'公司角色'\" :type=\"'companyRole'\"\r\n                         style=\"width: 100%\" @return=\"queryCompanyRoleIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--供应商提供哪些服务如：海运、铁路等-->\r\n          <el-form-item label=\"服务\" prop=\"serviceTypeIds\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                         :pass=\"queryParams.serviceTypeIds\" :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                         style=\"width: 100%\" @return=\"queryServiceTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"locationDepartureIds\">\r\n            <location-select :load-options=\"locationOptions\" :multiple=\"true\"\r\n                             :pass=\"queryParams.locationDepartureIds\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDepartureIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的区域-->\r\n          <el-form-item label=\"目的\" prop=\"locationDestinationIds\">\r\n            <location-select :en=\"true\" :load-options=\"locationOptions\"\r\n                             :multiple=\"true\" :pass=\"queryParams.locationDestinationIds\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的航线-->\r\n          <el-form-item label=\"航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                         :pass=\"queryParams.lineDestinationIds\" :placeholder=\"'目的航线'\" :type=\"'line'\"\r\n                         style=\"width: 100%\" @return=\"queryLineDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"货物\" prop=\"cargoTypeIds\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.cargoTypeIds\"\r\n                         :placeholder=\"'货物特征'\" :type=\"'cargoType'\" style=\"width: 100%\" @return=\"queryCargoTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"承运\" prop=\"carrierIds\">\r\n            <treeselect v-model=\"queryCarrierIds\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                        :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                        placeholder=\"优选承运人\"\r\n                        style=\"width: 100%\" @deselect=\"handleDeselectQueryCarrierIds\" @open=\"loadCarrier\"\r\n                        @select=\"handleSelectQueryCarrierIds\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{ node.raw.carrier.carrierIntlCode }}\r\n                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"评级\" prop=\"creditLevel\">\r\n            <el-input v-model=\"queryParams.creditLevel\" placeholder=\"A~E\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button :size=\"size\" icon=\"el-icon-search\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button :size=\"size\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-dialog\r\n          v-dialogDrag\r\n          v-dialogDragWidth\r\n          :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n          :visible.sync=\"merge\"\r\n          title=\"选择保留公司\"\r\n          width=\"800px\"\r\n        >\r\n          <el-row v-if=\"mergeList.length>0\">\r\n            <el-col :span=\"12\">\r\n              <el-descriptions border direction=\"vertical\" title=\"公司1\">\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[0].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[0].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[0].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[0].companyId,mergeList[1].companyId)\">\r\n                    留下{{ mergeList[0].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-descriptions border direction=\"vertical\" title=\"公司2\">\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[1].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[1].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[1].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[1].companyId,mergeList[0].companyId)\">\r\n                    留下{{ mergeList[1].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n          </el-row>\r\n        </el-dialog>\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"companyList\" border\r\n                  @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column\r\n            v-for=\"data in columns\"\r\n            v-if=\"data.visible\"\r\n            :key=\"data.key\"\r\n            :align=\"data.align\"\r\n            :label=\"data.label\"\r\n            :width=\"data.width\"\r\n          >\r\n            <template v-slot=\"scope\">\r\n              <!--内置组件，根据:is动态加载组件-->\r\n              <!--渲染组件根据组件内部的return方法返回指针渲染内容-->\r\n              <component :is=\"data.prop\" :scope=\"scope\" @return=\"getReturn\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"结款方式\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0;\">{{ }}</h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"额度\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <!--币种-->\r\n              <span style=\"margin: 0;\">{{ scope.row.agreementCurrencyCode }}</span>\r\n              <!--额度-->\r\n              <span style=\"margin: 0;\">{{ formatDisplayCreditLimit(scope.row.creditLimit) }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"录入人\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0;\">{{ scope.row.updateByName }}</h6>\r\n              <h6 style=\"margin: 0;\">{{ parseTime(scope.row.updateTime, \"{y}.{m}.{d}\") }}</h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-hasPermi=\"['system:company:edit']\"\r\n                         :size=\"size\" icon=\"el-icon-edit\" style=\"margin: 0 3px\" type=\"success\"\r\n                         @click=\"handleSelect(scope.row)\"\r\n              >选中\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 分页 -->\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改公司对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth\r\n      :close-on-click-modal=\"false\" :title=\"title\"\r\n      :visible.sync=\"openCompany\"\r\n      append-to-body\r\n      width=\"800px\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"68px\">\r\n        <!--从属信息-->\r\n        <el-row v-if=\"roleTypeId=='1'\">\r\n          <el-divider content-position=\"left\">从属信息</el-divider>\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"业务员\">\r\n                <treeselect v-model=\"belongTo\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                            :disabled=\"!add\" :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"belongList\"\r\n                            :show-count=\"true\" class=\"sss\" placeholder=\"选择所属人\" @input=\"handleDeselectBelongTo\"\r\n                            @open=\"loadSales\" @select=\"handleSelectBelongTo\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{\r\n                      node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                    }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"业务助理\">\r\n                <treeselect v-model=\"followUp\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                            :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                            :options=\"belongList\" :show-count=\"true\" class=\"sss\"\r\n                            placeholder=\"业务员自己跟进的情况无须填写\"\r\n                            @input=\"handleDeselectFollowUp\" @open=\"loadSales\" @select=\"handleSelectFollowUp\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{\r\n                      node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                    }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--基本信息-->\r\n        <el-row>\r\n          <el-divider content-position=\"left\">基本信息</el-divider>\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"单位代码\" prop=\"companyTaxCode\">\r\n                <el-input v-model=\"form.companyTaxCode\" class=\"sss\" disabled\r\n                          placeholder=\"国际通用简称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"信用等级\" prop=\"creditLevel\">\r\n                <tree-select :disabled=\"isLock\" :pass=\"form.creditLevel\" :type=\"'creditLevel'\"\r\n                             class=\"sss\"\r\n                             @return=\"getcreditLevel\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-form-item\r\n                :rules=\"[{required: form.companyEnShortName==null||form.companyEnShortName.length==0, trigger: 'blur'}]\"\r\n                label=\"中文简称\"\r\n                prop=\"companyShortName\"\r\n              >\r\n                <el-input v-model=\"form.companyShortName\" :disabled=\"isLock\" class=\"sss\"\r\n                          placeholder=\"公司简称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"中文全称\" prop=\"companyLocalName\">\r\n                <el-input v-model=\"form.companyLocalName\" :disabled=\"isLock\"\r\n                          placeholder=\"公司母语名\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item\r\n                :rules=\"[{required: form.companyShortName==null||form.companyShortName.length==0, trigger: 'blur'}]\"\r\n                label=\"英文简称\"\r\n                prop=\"companyEnShortName\"\r\n              >\r\n                <el-input v-model=\"form.companyEnShortName\" :disabled=\"isLock\"\r\n                          class=\"sss\"\r\n                          placeholder=\"公司简称\" style=\"padding-left: 5px;\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"英文全称\" prop=\"companyEnName\">\r\n                <el-input v-model=\"form.companyEnName\" :disabled=\"isLock\" placeholder=\"公司英文名\"\r\n                          style=\"padding-left: 5px;\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"行政区域\" prop=\"locationId\">\r\n                <location-select :load-options=\"locationOptions\" :multiple=\"false\"\r\n                                 :pass=\"form.locationId\"\r\n                                 class=\"sss\" @return=\"getLocationId\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"16\">\r\n              <el-form-item label=\"详细地址\" prop=\"locationDetail\">\r\n                <el-input v-model=\"form.locationDetail\" placeholder=\"详细地址信息\" style=\"padding-left: 5px;\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"客户来源\" prop=\"sourceId\">\r\n                <tree-select :pass=\"form.sourceId\" :type=\"'source'\" class=\"sss\"\r\n                             @return=\"getSourceId\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"所属组织\" prop=\"organizationIds\">\r\n                <tree-select :multiple=\"true\" :pass=\"form.organizationIds\" :type=\"'organization'\"\r\n                             class=\"sss\" @return=\"getOrganizationIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"24\">\r\n              <el-form-item label=\"备注\" prop=\"remark\">\r\n                <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 2, maxRows: 10}\" maxlength=\"150\"\r\n                          placeholder=\"备注\" show-word-limit style=\"padding-left: 5px;\" type=\"textarea\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--分类信息-->\r\n        <el-row>\r\n          <el-divider content-position=\"left\">\r\n            分类信息\r\n          </el-divider>\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"公司角色\" prop=\"roleIds\">\r\n                <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.roleIds\" :type=\"'companyRole'\"\r\n                             class=\"sss\" @return=\"getCompanyRoleIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"服务类型\" prop=\"serviceTypeIds\">\r\n                <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.serviceTypeIds\" :type=\"'serviceType'\"\r\n                             class=\"sss\" @return=\"getServiceTypeIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"货物特征\" prop=\"cargoTypeIds\">\r\n                <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.cargoTypeIds\" :type=\"'cargoType'\"\r\n                             class=\"sss\" @return=\"getCargoTypeIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"优选承运\" prop=\"carrierIds\">\r\n                <treeselect v-model=\"carrierIds\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                            :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                            :normalizer=\"carrierNormalizer\"\r\n                            :options=\"temCarrierList\" :show-count=\"true\" class=\"sss\"\r\n                            placeholder=\"选择承运人\" @deselect=\"handleDeselectCarrierIds\" @open=\"loadCarrier\"\r\n                            @select=\"handleSelectCarrierIds\"\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{ node.raw.carrier.carrierIntlCode }}\r\n                    {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\"\r\n                  >\r\n                    {{\r\n                      node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                    }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"启运区域\" prop=\"locationDepartureIds\">\r\n                <location-select :load-options=\"locationOptions\" :multiple=\"true\"\r\n                                 :pass=\"form.locationDepartureIds\" class=\"sss\" style=\"padding-left: 5px;\"\r\n                                 @return=\"getLocationDepartureIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <!--              <el-row>\r\n                            <el-form-item label=\"启运航线\" prop=\"lineDepartureIds\">\r\n                              <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.lineDepartureIds\" :type=\"'line'\"\r\n                                           style=\"padding-left: 5px;\" @return=\"getLineDepartureIds\"\r\n                              />\r\n                            </el-form-item>\r\n                          </el-row>-->\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"目的区域\" prop=\"locationDestinationIds\">\r\n                <location-select :en=\"true\" :load-options=\"locationOptions\"\r\n                                 :multiple=\"true\" :pass=\"form.locationDestinationIds\"\r\n                                 class=\"sss\" style=\"padding-left: 5px;\"\r\n                                 @return=\"getLocationDestinationIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"目的航线\" prop=\"lineDestinationIds\">\r\n                <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.lineDestinationIds\" :type=\"'line'\"\r\n                             class=\"sss\" style=\"padding-left: 5px;\" @return=\"getLineDestinationIds\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"开发权重\" prop=\"companyIntlCode\">\r\n                <el-input v-model=\"form.companyIntlCode\" class=\"sss\"\r\n                          placeholder=\"重要程度\" style=\"padding-left: 5px;width: 100%\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--协议信息-->\r\n        <el-row>\r\n          <el-divider content-position=\"left\">协议信息</el-divider>\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"协议号\" prop=\"agreementNumber\">\r\n                <el-input v-model=\"form.agreementNumber\" :disabled=\"isLock\" class=\"sss\"\r\n                          placeholder=\"协议号\"\r\n                          style=\"padding-left: 5px;\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"协议时间\" prop=\"agreementStartDate\">\r\n                <el-date-picker\r\n                  v-model=\"form.agreementDateRange\"\r\n                  :disabled=\"isLock\"\r\n                  class=\"sss date-select\"\r\n                  end-placeholder=\"协议结束日期\"\r\n                  range-separator=\"至\"\r\n                  start-placeholder=\"协议开始日期\"\r\n                  style=\"margin-left: 5px;\"\r\n                  :default-time=\"['00:00:00', '23:59:59']\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                  type=\"daterange\"\r\n                  @input=\"changeDate\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"信用额度\" prop=\"creditLimit\">\r\n                <div class=\"creditLimit\">\r\n                  <tree-select :disabled=\"isLock\" :pass=\"form.agreementCurrencyCode\" :type=\"'currency'\"\r\n                               class=\"sss currency\"\r\n                               @return=\"getCurrencyCode\"\r\n                  />\r\n                  <el-input v-model=\"form.creditLimit\" :disabled=\"isLock\" class=\"sss limit\"\r\n                            placeholder=\"信用额度(填入整数)\"\r\n                            style=\"padding-left: 5px;\"\r\n                            @change=\"formatCreditLimit\"\r\n                  />\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"信用周期\" prop=\"creditCycleMonth\">\r\n                <el-input v-model=\"form.creditCycleMonth\" :disabled=\"isLock\" placeholder=\"信用周期(自然月)\"\r\n                          style=\"padding-left: 5px\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"放单方式\" prop=\"agreementTypeId\">\r\n                <tree-select :disabled=\"isLock\" :flat=\"false\"\r\n                             :multiple=\"false\" :pass=\"form.releaseType\"\r\n                             :placeholder=\"'收款方式'\" :type=\"'releaseType'\"\r\n                             class=\"sss\"\r\n                             @return=\"form.releaseType=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"结款日\" prop=\"settlementDate\">\r\n                <el-input v-model=\"form.settlementDate\" :disabled=\"isLock\" class=\"sss\"\r\n                          placeholder=\"结款日(每月的几号)\" style=\"padding-left: 5px\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"8\">\r\n              <el-form-item label=\"收付路径\" prop=\"creditLevel\">\r\n                <tree-select :d-load=\"true\" :disabled=\"isLock\" :flat=\"false\"\r\n                             :multiple=\"true\" :pass=\"form.rsPaymentTitles\" :placeholder=\"'收付路径'\"\r\n                             :type=\"'rsPaymentTitle'\"\r\n                             style=\"padding-left: 5px\" @return=\"getRsPaymentTitle\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row>\r\n\r\n            <el-form-item label=\"协议备注\" prop=\"agreementRemark\">\r\n              <el-input v-model=\"form.agreementRemark\" :autosize=\"{ minRows: 2, maxRows: 10}\" :disabled=\"isLock\"\r\n                        maxlength=\"150\"\r\n                        placeholder=\"协议备注\"\r\n                        show-word-limit\r\n                        style=\"padding-left: 5px;\"\r\n                        type=\"textarea\"\r\n              />\r\n            </el-form-item>\r\n          </el-row>\r\n        </el-row>\r\n\r\n        <!--审核意见-->\r\n        <el-row v-if=\"showConfirm\">\r\n          <el-divider content-position=\"left\">\r\n            审核意见\r\n          </el-divider>\r\n          <el-row>\r\n            <el-col :span=\"4\">\r\n              <confirmed :id=\"'companyId'\" :confirmed=\"this.form.salesConfirmed==0\" :row=\"form\" :type=\"'sales'\"\r\n                         @lockMethod=\"updateCompany\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <!--商务锁定-->\r\n              <confirmed :id=\"'companyId'\" :confirmed=\"this.form.psaConfirmed==0\" :row=\"form\" :type=\"'psa'\"\r\n                         @lockMethod=\"updateCompany\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <!--操作锁定-->\r\n              <confirmed :id=\"'companyId'\" :confirmed=\"this.form.opConfirmed==0\" :row=\"form\" :type=\"'op'\"\r\n                         @lockMethod=\"updateCompany\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <!--财务锁定-->\r\n              <confirmed :id=\"'companyId'\" :confirmed=\"this.form.accConfirmed==0\" :row=\"form\" :type=\"'acc'\"\r\n                         @lockMethod=\"updateCompany\"\r\n              />\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button v-if=\"!edit\" :size=\"size\" type=\"primary\" @click=\"querySame\">查重</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <div>\r\n      <staff-info :company=\"companyInfo\" :load-options=\"staffList\" :open=\"openStaff\" @openStaffs=\"cancel\"\r\n                  @refresh=\"getList\"\r\n      />\r\n      <!--    <account-info :company=\"companyInfo\" :load-options=\"accountList\" :open=\"openAccount\" :type=\"'supplier'\"\r\n                        @openAccounts=\"cancel\"\r\n          />-->\r\n      <account-info :company=\"companyInfo\" :is-lock=\"isLock\" :load-options=\"accountList\" :open=\"openAccount\"\r\n                    :type=\"'company'\"\r\n                    @openAccounts=\"accountCancel\"\r\n      />\r\n      <communications :company=\"companyInfo\" :load-options=\"communicationList\" :open=\"openCommunication\" :totle=\"ctotle\"\r\n                      @openCommunications=\"cancel\"\r\n      />\r\n      <agreement-record :company=\"companyInfo\" :load-options=\"agreementList\" :open=\"openAgreement\" :totle=\"atotle\"\r\n                        @openCommunications=\"cancel\"\r\n      />\r\n      <BlackList :company=\"companyInfo\" :open=\"openBlackList\" @openBlackList=\"cancel\"/>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  delCompany,\r\n  getBank,\r\n  getCompany,\r\n  getConnect,\r\n  listCompany,\r\n  mergeCompany,\r\n  querySame,\r\n  updateCompany\r\n} from \"@/api/system/company\"\r\nimport {getInfoByStaffId} from \"@/api/system/role\"\r\nimport {addMessage} from \"@/api/system/message\"\r\nimport {listCommunication} from \"@/api/system/communication\"\r\nimport {listAgreementrecord} from \"@/api/system/agreementrecord\"\r\n\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\"\r\n\r\nimport BlackList from \"@/components/BlackList\"\r\nimport communications from \"@/views/system/communication\"\r\nimport agreementRecord from \"@/views/system/agreementRecord\"\r\nimport staffInfo from \"@/views/system/company/staffInfo\"\r\nimport accountInfo from \"@/views/system/company/accountInfo\"\r\n\r\nimport company from \"@/views/system/company/company\"\r\nimport contactor from \"@/views/system/company/contactor\"\r\nimport location from \"@/views/system/company/location\"\r\nimport role from \"@/views/system/company/role\"\r\nimport serviceType from \"@/views/system/company/serviceType\"\r\nimport departure from \"@/views/system/company/departure\"\r\nimport destination from \"@/views/system/company/destination\"\r\nimport cargoType from \"@/views/system/company/cargoType\"\r\nimport carrier from \"@/views/system/company/carrier\"\r\nimport account from \"@/views/system/company/account\"\r\nimport agreement from \"@/views/system/company/agreement\"\r\nimport communication from \"@/views/system/company/communication\"\r\nimport grade from \"@/views/system/company/grade\"\r\nimport achievement from \"@/views/system/company/achievement\"\r\nimport remark from \"@/views/system/company/remark\"\r\nimport belong from \"@/views/system/company/belong\"\r\nimport auth from \"@/plugins/auth\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {Message} from \"element-ui\"\r\nimport Confirmed from \"@/components/Confirmed/index.vue\"\r\nimport rsPaymentTitle from \"@/views/system/company/rsPaymentTitle.vue\"\r\n\r\nexport default {\r\n  name: \"SelectCompany\",\r\n  dicts: [\"sys_is_idle\"],\r\n  components: {\r\n    Confirmed,\r\n    Treeselect,\r\n    communication,\r\n    communications,\r\n    BlackList,\r\n    belong,\r\n    company,\r\n    contactor,\r\n    staffInfo,\r\n    location,\r\n    role,\r\n    serviceType,\r\n    departure,\r\n    destination,\r\n    cargoType,\r\n    carrier,\r\n    account,\r\n    accountInfo,\r\n    agreement,\r\n    agreementRecord,\r\n    grade,\r\n    achievement,\r\n    remark,\r\n    rsPaymentTitle\r\n  },\r\n  props: [\"roleTypeId\"],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      add: false,\r\n      selectTwo: true,\r\n      size: this.$store.state.app.size || \"mini\",\r\n      // 公司表格数据\r\n      mergeList: [],\r\n      companyList: [],\r\n      staffList: [],\r\n      accountList: [],\r\n      communicationList: [],\r\n      agreementList: [],\r\n      belongList: [],\r\n      carrierList: [],\r\n      businessList: [],\r\n      temCarrierList: [],\r\n      locationOptions: [],\r\n      carrierIds: [],\r\n      companyInfo: {},\r\n      queryCarrierIds: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      merge: false,\r\n      openCompany: false,\r\n      openStaff: false,\r\n      openAccount: false,\r\n      openCommunication: false,\r\n      openAgreement: false,\r\n      openBlackList: false,\r\n      edit: false,\r\n      belongTo: null,\r\n      followUp: null,\r\n      queryBFStaffId: null,\r\n      queryBStaffId: null,\r\n      refreshTable: true,\r\n      ctotle: null,\r\n      atotle: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        roleTypeId: this.roleTypeId,\r\n        companyQuery: null,\r\n        locationId: null,\r\n        idleStatus: null,\r\n        queryStaffId: null,\r\n        showPriority: null,\r\n        serviceTypeIds: [],\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        roleIds: [],\r\n        carrierIds: []\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        agreementStartDate: null,\r\n        agreementEndDate: null,\r\n        settlementDate: null\r\n      },\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前点击修改时选中的记录\r\n      companyRow: null,\r\n      isLock: true,\r\n      showConfirm: false\r\n    }\r\n  },\r\n  computed: {\r\n    columns: {\r\n      get() {\r\n        if (this.roleTypeId == \"2\") {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleTypeId == \"1\") {\r\n          return this.$store.state.listSettings.clientSetting\r\n        }\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    queryStaffId() {\r\n      this.queryParams.queryStaffId = this.queryStaffId\r\n    },\r\n    \"form.belongTo\"() {\r\n      if (this.form.belongTo == this.form.followUp) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    \"form.serviceTypeIds\"(n) {\r\n      this.loadCarrier()\r\n      let list = []\r\n      if (this.carrierList != undefined && n != null && n.includes(-1)) {\r\n        this.temCarrierList = this.carrierList\r\n        for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (this.form.carrierIds != null && this.form.carrierIds.includes(b.carrier.carrierId) && !this.carrierIds.includes(b.serviceTypeId)) {\r\n                    this.carrierIds.push(b.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if (this.carrierList != undefined && n != null && !n.includes(-1)) {\r\n        for (const c of this.carrierList) {\r\n          if (n != null && n != undefined) {\r\n            for (const s of n) {\r\n              if (c.serviceTypeId == s) {\r\n                list.push(c)\r\n              }\r\n              if (c.children != undefined && c.children.length > 0) {\r\n                for (const ch of c.children) {\r\n                  if (ch.serviceTypeId == s) {\r\n                    list.push(ch)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.temCarrierList = list\r\n        if (this.temCarrierList.length > 0) {\r\n          for (const v of this.temCarrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (this.form.carrierIds != null && this.form.carrierIds.includes(a.carrier.carrierId) && !this.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    form() {\r\n      if (this.form.salesConfirmed == 1 || this.form.accConfirmed == 1 || this.form.psaConfirmed == 1 || this.form.opConfirmed == 1) {\r\n        this.isLock = true\r\n      } else {\r\n        this.isLock = false\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList().then(() => {\r\n      this.loadBusinesses()\r\n      this.loadCarrier()\r\n      this.loadSales()\r\n    })\r\n  },\r\n  methods: {\r\n    handleSelect(row) {\r\n      this.$emit(\"return\", row)\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesListC\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch(\"getServiceTypeCarriersList\").then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    querySame() {\r\n      let data = {\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDepartureIds: [],\r\n        lineDestinationIds: [],\r\n        companyShortName: this.form.companyShortName,\r\n        companyEnShortName: this.form.companyEnShortName,\r\n        serviceTypeIds: this.form.serviceTypeIds,\r\n        roleTypeId: this.roleTypeId,\r\n        belongTo: this.form.belongTo,\r\n        followUp: this.form.followUp,\r\n        deleteStatus: 1\r\n      }\r\n      getInfoByStaffId(this.$store.state.user.sid).then(response => {\r\n        data.cargoTypeIds = response.cargoTypeIds\r\n        data.locationDepartureIds = response.locationDepartureIds\r\n        data.locationDestinationIds = response.locationDestinationIds\r\n        data.lineDepartureIds = response.lineDepartureIds\r\n        data.lineDestinationIds = response.lineDestinationIds\r\n      })\r\n      if (data.belongTo == null) {\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == this.$store.state.user.sid) {\r\n                      data.belongTo = this.$store.state.user.sid\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          querySame(data).then(response => {\r\n            let res = response.data\r\n            if (res != undefined) {\r\n              this.$confirm(res.deleteStatus == 0 ? \"存在重复的数据，是否要显示该数据\" : \"存在重复数据，但已删除，是否重新读取\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\"\r\n              }).then(() => {\r\n                res.deleteStatus = 0\r\n                this.form = res\r\n                this.form.roleTypeId = this.roleTypeId\r\n                if (this.belongList != undefined) {\r\n                  for (const a of this.belongList) {\r\n                    if (a.children != undefined) {\r\n                      for (const b of a.children) {\r\n                        if (b.children != undefined) {\r\n                          for (const c of b.children) {\r\n                            if (c.staffId == response.data.belongTo) {\r\n                              this.belongTo = c.deptId\r\n                            }\r\n                            if (c.staffId == response.data.followUp) {\r\n                              this.followUp = c.deptId\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n                this.form.roleIds = response.roleIds\r\n                this.form.serviceTypeIds = response.serviceTypeIds\r\n                this.form.cargoTypeIds = response.cargoTypeIds\r\n                this.form.lineDepartureIds = response.lineDepartureIds\r\n                this.form.locationDepartureIds = response.locationDepartureIds\r\n                this.form.lineDestinationIds = response.lineDestinationIds\r\n                this.form.locationDestinationIds = response.locationDestinationIds\r\n                this.form.carrierIds = response.carrierIds\r\n                this.form.organizationIds = response.companyOrganizationIds\r\n                this.locationOptions = response.locationOptions\r\n                this.openCompany = true\r\n                this.title = \"修改公司信息\"\r\n                this.loading = false\r\n              })\r\n            }\r\n            if (response.msg.toString().indexOf(\"Error\") > -1) {\r\n              this.$confirm(response.msg, \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\"\r\n              }).then(() => {\r\n                let data = {\r\n                  messageOwner: 1,\r\n                  messageType: 3,\r\n                  messageFrom: null,\r\n                  messageTitle: this.$store.state.user.name.split(\" \")[1] + \"请求更新公司\",\r\n                  messageContent: response.msg\r\n                }\r\n                addMessage(data).then(response => {\r\n                  this.$message({\r\n                    type: \"success\",\r\n                    message: \"已发送请求!\"\r\n                  })\r\n                })\r\n              })\r\n            }\r\n            if (response.msg.toString().indexOf(\"Success\") > -1) {\r\n              this.$confirm(\"不存在重复的公司简称，是否确定新增客户？\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\"\r\n              }).then(() => {\r\n                data.deleteStatus = 0\r\n                querySame(data).then(response => {\r\n                  if (response.data) {\r\n                    this.$message.success(\"添加成功\")\r\n                    this.form = response.data\r\n                    this.form.roleTypeId = this.roleTypeId\r\n                    if (this.belongList != undefined) {\r\n                      for (const a of this.belongList) {\r\n                        if (a.children != undefined) {\r\n                          for (const b of a.children) {\r\n                            if (b.children != undefined) {\r\n                              for (const c of b.children) {\r\n                                if (c.staffId == response.data.belongTo) {\r\n                                  this.belongTo = c.deptId\r\n                                }\r\n                                if (c.staffId == response.data.followUp) {\r\n                                  this.followUp = c.deptId\r\n                                }\r\n                              }\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                    this.form.roleIds = response.roleIds\r\n                    this.form.serviceTypeIds = response.serviceTypeIds\r\n                    this.form.cargoTypeIds = response.cargoTypeIds\r\n                    this.form.lineDepartureIds = response.lineDepartureIds\r\n                    this.form.locationDepartureIds = response.locationDepartureIds\r\n                    this.form.lineDestinationIds = response.lineDestinationIds\r\n                    this.form.locationDestinationIds = response.locationDestinationIds\r\n                    this.form.carrierIds = response.carrierIds\r\n                    this.form.organizationIds = response.companyOrganizationIds\r\n                    this.locationOptions = response.locationOptions\r\n                    this.openCompany = true\r\n                    this.title = \"修改公司信息\"\r\n                    this.loading = false\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 查询公司列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      await listCompany({\r\n        ...this.queryParams,\r\n        roleClient: 1,\r\n        permissionLevel: this.$store.state.user.permissionLevelList.C\r\n      }).then(response => {\r\n        this.companyList = response.rows\r\n        if (!isNaN(response.total)) {\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + \" \" + node.serviceEnName + \",\" + pinyin.getFullChars(node.serviceLocalName)\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : \"\") + \" \" + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : \"\") + \" \" + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\") + \",\" + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\"))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.openCompany = false\r\n      this.openAccount = false\r\n      this.openStaff = false\r\n      this.openCommunication = false\r\n      this.openAgreement = false\r\n      this.openBlackList = false\r\n      this.add = false\r\n      this.merge = false\r\n      this.edit = false\r\n      this.reset()\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      // this.getList()\r\n    },\r\n    accountCancel() {\r\n      this.openAccount = false\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      this.carrierIds = []\r\n      this.form = {\r\n        belongTo: null,\r\n        followUp: null,\r\n        carrierIds: null,\r\n        locationDetail: null,\r\n        companyId: null,\r\n        companyIntlCode: null,\r\n        companyShortName: null,\r\n        companyEnShortName: null,\r\n        companyLocalName: null,\r\n        companyEnName: null,\r\n        companyTaxCode: null,\r\n        roleIds: null,\r\n        serviceTypeIds: null,\r\n        cargoTypeIds: null,\r\n        locationDepartureIds: null,\r\n        lineDepartureIds: null,\r\n        locationDestinationIds: null,\r\n        lineDestinationIds: null,\r\n        organizationIds: null,\r\n        companyPortIds: null,\r\n        roleTypeId: this.roleTypeId,\r\n        locationId: null,\r\n        salesConfirmed: 0,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        psaConfirmed: 0,\r\n        psaConfirmedId: null,\r\n        psaConfirmedName: null,\r\n        psaConfirmedDate: null,\r\n        accConfirmed: 0,\r\n        accConfirmedId: null,\r\n        accConfirmedName: null,\r\n        accConfirmedDate: null,\r\n        opConfirmed: 0,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        remark: null,\r\n        rsPaymentTitles: []\r\n      }\r\n      this.carrierIds = []\r\n      this.companyRow = null\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.queryBStaffId = null\r\n      this.queryParams.locationId = null\r\n      this.queryParams.serviceTypeIds = null\r\n      this.queryParams.cargoTypeIds = null\r\n      this.queryParams.locationDepartureIds = null\r\n      this.queryParams.lineDepartureIds = null\r\n      this.queryParams.locationDestinationIds = null\r\n      this.queryParams.lineDestinationIds = null\r\n      this.queryParams.organizationIds = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.companyId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n      this.selectTwo = selection.length != 2\r\n      if (selection.length == 1) {\r\n        this.setCompanyInfo(selection[0])\r\n      }\r\n      if (selection.length == 2) {\r\n        this.mergeList = selection\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.edit = false\r\n      this.form.belongTo = null\r\n      this.openCompany = true\r\n      this.title = \"新增公司信息\"\r\n      this.form.serviceTypeIds = []\r\n      this.temCarrierList = this.carrierList\r\n      if (this.temCarrierList != undefined && this.form.serviceTypeIds != null) {\r\n        for (const a of this.temCarrierList) {\r\n          this.form.serviceTypeIds.push(a.serviceTypeId)\r\n        }\r\n      }\r\n      this.add = true\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == this.$store.state.user.sid) {\r\n                    this.belongTo = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      // 币种默认为人民币RMB\r\n      this.form.agreementCurrencyCode = \"RMB\"\r\n      this.showConfirm = false\r\n    },\r\n    getReturn(row) {\r\n      if (row.key == \"contactor\") {\r\n        this.setCompanyInfo(row.value)\r\n        getConnect(row.value.companyId).then(response => {\r\n          this.staffList = response.staffList\r\n          this.openStaff = true\r\n        })\r\n      }\r\n      if (row.key == \"communication\") {\r\n        this.setCompanyInfo(row.value)\r\n        listCommunication({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.communicationList = response.rows\r\n          this.ctotle = response.totle\r\n          this.openCommunication = true\r\n        })\r\n      }\r\n      if (row.key == \"agreement\") {\r\n        this.setCompanyInfo(row.value)\r\n        listAgreementrecord({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.agreementList = response.rows\r\n          this.atotle = response.totle\r\n          this.openAgreement = true\r\n        })\r\n      }\r\n      if (row.key == \"account\") {\r\n        this.setCompanyInfo(row.value)\r\n        getBank(row.value.companyId).then(response => {\r\n          this.accountList = response.accountList\r\n          this.openAccount = true\r\n        })\r\n      }\r\n    },\r\n    // 设置客户信息\r\n    setCompanyInfo(row) {\r\n      this.companyInfo = {\r\n        companyId: row.companyId != null ? row.companyId : \"\",\r\n        companyTaxCode: row.companyTaxCode != null ? row.companyTaxCode : \"\",\r\n        companyShortName: row.companyShortName != null ? row.companyShortName : \"\",\r\n        companyEnShortName: row.companyEnShortName != null ? row.companyEnShortName : \"\",\r\n        companyLocalName: row.companyLocalName != null ? row.companyLocalName : \"\",\r\n        companyEnName: row.companyEnName != null ? row.companyEnName : \"\",\r\n        companyLocation: row.locationId != null ? row.locationId : \"\",\r\n        mainStaffId: row.staff != null ? row.staff.staffId : \"\"\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.loading = true\r\n      this.edit = true\r\n      this.companyRow = row\r\n      this.add = auth.hasPermi(\"system:client:distribute\")\r\n      const companyId = row.companyId || this.ids\r\n      this.showConfirm = true\r\n      getCompany(companyId).then(response => {\r\n        this.form = response.data\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.belongTo) {\r\n                      this.belongTo = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.followUp) {\r\n                      this.followUp = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.form.roleIds = response.roleIds\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.form.cargoTypeIds = response.cargoTypeIds\r\n        this.form.lineDepartureIds = response.lineDepartureIds\r\n        this.form.locationDepartureIds = response.locationDepartureIds\r\n        this.form.lineDestinationIds = response.lineDestinationIds\r\n        this.form.locationDestinationIds = response.locationDestinationIds\r\n        this.form.carrierIds = response.carrierIds\r\n        this.form.organizationIds = response.companyOrganizationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.openCompany = true\r\n        this.title = \"修改公司信息\"\r\n        this.form.rsPaymentTitles = response.data.rsPaymentTitle ? response.data.rsPaymentTitle.split(\",\") : []\r\n        this.loading = false\r\n\r\n        if (response.data.agreementStartDate !== null && response.data.agreementEndDate !== null) {\r\n          this.form.agreementDateRange = []\r\n          this.form.agreementDateRange.push(response.data.agreementStartDate)\r\n          this.form.agreementDateRange.push(response.data.agreementEndDate)\r\n        }\r\n\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        this.form.creditLimit = response.data.creditLimit.toLocaleString(\"en-US\")\r\n        this.form.creditLimit = this.form.creditLimit.replace(/,/g, \"\")\r\n        this.form.creditLimit = formatter.format(this.form.creditLimit)\r\n\r\n      })\r\n\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        // 转换额度显示\r\n        this.form.creditLimit = this.form.creditLimit ? this.form.creditLimit.replace(/,/g, \"\") : 0\r\n        if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n          this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n          this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n        }\r\n        // 判断日期开始时间是否大于结束时间\r\n        let startDate = new Date(this.form.agreementStartDate)\r\n        let endDate = new Date(this.form.agreementEndDate)\r\n        if (startDate > endDate) {\r\n          Message({\r\n            message: \"协议开始时间不能大于结束时间\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        // 信用周期要为整数\r\n        if (this.form.creditCycleMonth != null && this.form.creditCycleMonth % 1 != 0) {\r\n          Message({\r\n            message: \"信用周期必须为整数\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        if (valid) {\r\n          if (this.form.companyId != null) {\r\n            updateCompany(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.openCompany = false\r\n              this.getList()\r\n            })\r\n            this.reset()\r\n          } else {\r\n            this.$message.info(\"未查重，先对简称查重吧\")\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const companyIds = row.companyId || this.ids\r\n      this.$confirm(\"是否确认删除公司编号为\\\"\" + companyIds + \"\\\"的数据项？\", '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCompany(companyIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleBlackList() {\r\n      this.openBlackList = true\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/company/export\", {\r\n        ...this.queryParams\r\n      }, `company_${new Date().getTime()}.xlsx`)\r\n    },\r\n    queryLocationId(val) {\r\n      this.queryParams.locationId = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationId(val) {\r\n      this.form.locationId = val\r\n    },\r\n    getSourceId(val) {\r\n      this.form.sourceId = val.sourceShortName\r\n    },\r\n    getOrganizationIds(val) {\r\n      this.form.organizationIds = val\r\n    },\r\n    getServiceTypeIds(val) {\r\n      this.form.serviceTypeIds = val\r\n      if (val == undefined) {\r\n        this.carrierIds = null\r\n        this.form.carrierIds = null\r\n      }\r\n    },\r\n    queryServiceTypeIds(val) {\r\n      this.queryParams.serviceTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCargoTypeIds(val) {\r\n      this.form.cargoTypeIds = val\r\n    },\r\n    queryCargoTypeIds(val) {\r\n      this.queryParams.cargoTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCompanyRoleIds(val) {\r\n      this.form.roleIds = val\r\n    },\r\n    queryCompanyRoleIds(val) {\r\n      this.queryParams.roleIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDepartureIds(val) {\r\n      this.queryParams.locationDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDepartureIds(val) {\r\n      this.form.lineDepartureIds = val\r\n    },\r\n    getLocationDestinationIds(val) {\r\n      this.form.locationDestinationIds = val\r\n    },\r\n    queryLineDepartureIds(val) {\r\n      this.queryParams.lineDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationDepartureIds(val) {\r\n      this.form.locationDepartureIds = val\r\n    },\r\n    queryLocationDestinationIds(val) {\r\n      this.queryParams.locationDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDestinationIds(val) {\r\n      this.form.lineDestinationIds = val\r\n    },\r\n    queryLineDestinationIds(val) {\r\n      this.queryParams.lineDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    handleSelectBelongTo(node) {\r\n      this.form.belongTo = node.staffId\r\n    },\r\n    handleDeselectBelongTo(v) {\r\n      if (v == undefined) {\r\n        this.form.belongTo = 0\r\n        this.belongTo = null\r\n      }\r\n    },\r\n    handleSelectFollowUp(node) {\r\n      this.form.followUp = node.staffId\r\n    },\r\n    handleDeselectFollowUp(value) {\r\n      if (value == undefined) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    handleSelectBFStaffId(node) {\r\n      this.queryParams.queryBFStaffId = node.staffId\r\n      this.handleQuery()\r\n    },\r\n    cleanBFStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBFStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    cleanBStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    handleSelectBStaffId(node) {\r\n      this.queryParams.queryBStaffId = node.staffId\r\n      getInfoByStaffId(node.staffId).then(response => {\r\n        this.queryParams.cargoTypeIds = response.cargoTypeIds\r\n        this.queryParams.serviceTypeIds = response.serviceTypeIds\r\n        this.queryParams.locationDepartureIds = response.locationDepartureIds\r\n        this.queryParams.lineDepartureIds = response.lineDepartureIds\r\n        this.queryParams.locationDestinationIds = response.locationDestinationIds\r\n        this.queryParams.lineDestinationIds = response.lineDestinationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleSelectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds.push(node.carrier.carrierId)\r\n      this.handleQuery()\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    handleDeselectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n      this.handleQuery()\r\n    },\r\n    refreshColumns() {\r\n      this.refreshTable = false\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true\r\n      })\r\n    },\r\n    handleMergeCompany() {\r\n      this.merge = true\r\n    },\r\n    handleMerge(save, del) {\r\n      mergeCompany(save, del).then(response => {\r\n        this.$message.success(response.msg)\r\n        this.merge = false\r\n        this.getList()\r\n      })\r\n    },\r\n    deptLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.deptConfirmed = this.form.deptConfirmed == 0 ? 1 : 0\r\n        this.form.deptConfirmedId = this.$store.state.user.sid\r\n        this.form.deptConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.deptConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    financeLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.financeConfirmed = this.form.financeConfirmed == 0 ? 1 : 0\r\n        this.form.financeConfirmedId = this.$store.state.user.sid\r\n        this.form.financeConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.financeConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    psaLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.psaConfirmed = this.form.psaConfirmed == 0 ? 1 : 0\r\n        this.form.psaConfirmedId = this.$store.state.user.sid\r\n        this.form.psaConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.psaConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    opLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.opConfirmed = this.form.opConfirmed === 0 ? 1 : 0\r\n        this.form.opConfirmedId = this.$store.state.user.sid\r\n        this.form.opConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.opConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    getCurrencyCode(val) {\r\n      this.form.agreementCurrencyCode = val\r\n    },\r\n    getcreditLevel(val) {\r\n      this.form.creditLevel = val\r\n    },\r\n    getRsPaymentTitle(val) {\r\n      this.form.rsPaymentTitle = val.toString()\r\n    },\r\n    updateCompany(form) {\r\n      // TODO 只更新有修改的字段\r\n\r\n      this.form.creditLimit = this.form.creditLimit.replace(/,/g, \"\")\r\n\r\n      if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n        this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n        this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n      }\r\n\r\n      updateCompany(this.form).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        getCompany(this.form.companyId).then(response => {\r\n          // 更新信息\r\n          // this.$nextTick(() => {\r\n          this.form = response.data\r\n          if (this.belongList != undefined) {\r\n            for (const a of this.belongList) {\r\n              if (a.children != undefined) {\r\n                for (const b of a.children) {\r\n                  if (b.children != undefined) {\r\n                    for (const c of b.children) {\r\n                      if (c.staffId == response.data.belongTo) {\r\n                        this.belongTo = c.deptId\r\n                      }\r\n                      if (c.staffId == response.data.followUp) {\r\n                        this.followUp = c.deptId\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n          this.form.roleIds = response.roleIds\r\n          this.form.serviceTypeIds = response.serviceTypeIds\r\n          this.form.cargoTypeIds = response.cargoTypeIds\r\n          this.form.lineDepartureIds = response.lineDepartureIds\r\n          this.form.locationDepartureIds = response.locationDepartureIds\r\n          this.form.lineDestinationIds = response.lineDestinationIds\r\n          this.form.locationDestinationIds = response.locationDestinationIds\r\n          this.form.carrierIds = response.carrierIds\r\n          this.form.organizationIds = response.companyOrganizationIds\r\n          this.locationOptions = response.locationOptions\r\n          this.openCompany = true\r\n          this.title = \"修改公司信息\"\r\n          this.loading = false\r\n\r\n          const formatter = new Intl.NumberFormat(\"en-US\", {\r\n            style: \"decimal\",\r\n            minimumFractionDigits: 2,\r\n            maximumFractionDigits: 2\r\n          })\r\n          this.form.creditLimit = response.data.creditLimit.toLocaleString(\"en-US\")\r\n          this.form.creditLimit = this.form.creditLimit.replace(/,/g, \"\")\r\n          this.form.creditLimit = formatter.format(this.form.creditLimit)\r\n\r\n          // 更新日期\r\n          this.form.agreementDateRange = [response.data.agreementStartDate, response.data.agreementEndDate]\r\n          /* if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n            this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n            this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n          } */\r\n        })\r\n      })\r\n      // })\r\n    },\r\n    formatCreditLimit() {\r\n      if (this.form.creditLimit != null) {\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        this.form.creditLimit = this.form.creditLimit.replace(/,/g, \"\")\r\n        this.form.creditLimit = formatter.format(this.form.creditLimit)\r\n      }\r\n    },\r\n    formatDisplayCreditLimit(value) {\r\n      const formatter = new Intl.NumberFormat(\"en-US\", {\r\n        notation: \"compact\"\r\n      })\r\n      return formatter.format(value)\r\n    },\r\n    changeDate() {\r\n      this.$forceUpdate()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n/*.sss {\r\n  padding-left: 5px !important;\r\n  padding-right: 15px !important;\r\n  width: 100%;\r\n}*/\r\n\r\n.creditLimit {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.limit {\r\n  flex: 3\r\n}\r\n\r\n.currency {\r\n  flex: 1\r\n}\r\n\r\n.confirm {\r\n  display: flex;\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.box-card {\r\n  width: 20%;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.date-select {\r\n  max-width: 307px;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAuoBA,IAAAA,QAAA,GAAAC,OAAA;AAUA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,cAAA,GAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AAEA,IAAAK,SAAA,GAAAC,sBAAA,CAAAN,OAAA;AACA,IAAAO,MAAA,GAAAD,sBAAA,CAAAN,OAAA;AACA,IAAAQ,cAAA,GAAAF,sBAAA,CAAAN,OAAA;AACAA,OAAA;AAEA,IAAAS,UAAA,GAAAH,sBAAA,CAAAN,OAAA;AACA,IAAAU,eAAA,GAAAJ,sBAAA,CAAAN,OAAA;AACA,IAAAW,gBAAA,GAAAL,sBAAA,CAAAN,OAAA;AACA,IAAAY,UAAA,GAAAN,sBAAA,CAAAN,OAAA;AACA,IAAAa,YAAA,GAAAP,sBAAA,CAAAN,OAAA;AAEA,IAAAc,SAAA,GAAAR,sBAAA,CAAAN,OAAA;AACA,IAAAe,UAAA,GAAAT,sBAAA,CAAAN,OAAA;AACA,IAAAgB,SAAA,GAAAV,sBAAA,CAAAN,OAAA;AACA,IAAAiB,MAAA,GAAAX,sBAAA,CAAAN,OAAA;AACA,IAAAkB,YAAA,GAAAZ,sBAAA,CAAAN,OAAA;AACA,IAAAmB,UAAA,GAAAb,sBAAA,CAAAN,OAAA;AACA,IAAAoB,YAAA,GAAAd,sBAAA,CAAAN,OAAA;AACA,IAAAqB,UAAA,GAAAf,sBAAA,CAAAN,OAAA;AACA,IAAAsB,QAAA,GAAAhB,sBAAA,CAAAN,OAAA;AACA,IAAAuB,QAAA,GAAAjB,sBAAA,CAAAN,OAAA;AACA,IAAAwB,UAAA,GAAAlB,sBAAA,CAAAN,OAAA;AACA,IAAAyB,eAAA,GAAAnB,sBAAA,CAAAN,OAAA;AACA,IAAA0B,MAAA,GAAApB,sBAAA,CAAAN,OAAA;AACA,IAAA2B,YAAA,GAAArB,sBAAA,CAAAN,OAAA;AACA,IAAA4B,OAAA,GAAAtB,sBAAA,CAAAN,OAAA;AACA,IAAA6B,OAAA,GAAAvB,sBAAA,CAAAN,OAAA;AACA,IAAA8B,KAAA,GAAAxB,sBAAA,CAAAN,OAAA;AACA,IAAA+B,KAAA,GAAA/B,OAAA;AACA,IAAAgC,UAAA,GAAAhC,OAAA;AACA,IAAAiC,MAAA,GAAA3B,sBAAA,CAAAN,OAAA;AACA,IAAAkC,eAAA,GAAA5B,sBAAA,CAAAN,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAmC,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,SAAA,EAAAA,cAAA;IACAC,UAAA,EAAAA,sBAAA;IACAC,aAAA,EAAAA,uBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,OAAA,EAAAA,iBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,IAAA,EAAAA,cAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,eAAA,EAAAA,wBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,cAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,OAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,GAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACAC,GAAA;MACAC,SAAA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACA;MACAI,SAAA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,UAAA;MACAC,WAAA;MACAC,YAAA;MACAC,cAAA;MACAC,eAAA;MACAC,UAAA;MACAC,WAAA;MACAC,eAAA;MACA;MACAC,KAAA;MACA;MACAC,KAAA;MACAC,WAAA;MACAC,SAAA;MACAC,WAAA;MACAC,iBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,IAAA;MACAC,QAAA;MACAC,QAAA;MACAC,cAAA;MACAC,aAAA;MACAC,YAAA;MACAC,MAAA;MACAC,MAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA,OAAAA,UAAA;QACAC,YAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,YAAA;QACAC,cAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,gBAAA;QACAC,sBAAA;QACAC,kBAAA;QACAC,OAAA;QACAlC,UAAA;MACA;MACA;MACAmC,IAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,cAAA;MACA;MACA;MACAC,KAAA;MACA;MACAC,UAAA;MACAC,MAAA;MACAC,WAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,SAAAvB,UAAA;UACA,YAAApC,MAAA,CAAAC,KAAA,CAAA2D,YAAA,CAAAC,eAAA;QACA;QACA,SAAAzB,UAAA;UACA,YAAApC,MAAA,CAAAC,KAAA,CAAA2D,YAAA,CAAAE,aAAA;QACA;MACA;IACA;EACA;EACAC,KAAA;IACApE,UAAA,WAAAA,WAAAqE,CAAA;MACA,IAAAA,CAAA;QACA,KAAAzE,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACAkD,YAAA,WAAAA,aAAA;MACA,KAAAP,WAAA,CAAAO,YAAA,QAAAA,YAAA;IACA;IACA,0BAAAyB,aAAA;MACA,SAAAhB,IAAA,CAAAvB,QAAA,SAAAuB,IAAA,CAAAtB,QAAA;QACA,KAAAsB,IAAA,CAAAtB,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACA,gCAAAuC,mBAAAF,CAAA;MACA,KAAAG,WAAA;MACA,IAAAC,IAAA;MACA,SAAA1D,WAAA,IAAA2D,SAAA,IAAAL,CAAA,YAAAA,CAAA,CAAAM,QAAA;QACA,KAAA1D,cAAA,QAAAF,WAAA;QAAA,IAAA6D,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAA/D,WAAA;UAAAgE,KAAA;QAAA;UAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAP,CAAA,IAAAY,IAAA;YAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;YACA,IAAAD,CAAA,CAAAE,QAAA,IAAAV,SAAA,IAAAQ,CAAA,CAAAE,QAAA,CAAAC,MAAA;cAAA,IAAAC,UAAA,OAAAT,2BAAA,CAAAC,OAAA,EACAI,CAAA,CAAAE,QAAA;gBAAAG,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAAN,CAAA,MAAAO,MAAA,GAAAD,UAAA,CAAAjB,CAAA,IAAAY,IAAA;kBAAA,IAAAO,CAAA,GAAAD,MAAA,CAAAJ,KAAA;kBACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA,IAAAc,CAAA,CAAAJ,QAAA,CAAAC,MAAA;oBAAA,IAAAI,UAAA,OAAAZ,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;sBAAAM,MAAA;oBAAA;sBAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAApB,CAAA,IAAAY,IAAA;wBAAA,IAAAU,CAAA,GAAAD,MAAA,CAAAP,KAAA;wBACA,SAAA7B,IAAA,CAAAnC,UAAA,iBAAAmC,IAAA,CAAAnC,UAAA,CAAAwD,QAAA,CAAAgB,CAAA,CAAA5G,OAAA,CAAA6G,SAAA,WAAAzE,UAAA,CAAAwD,QAAA,CAAAgB,CAAA,CAAAE,aAAA;0BACA,KAAA1E,UAAA,CAAA2E,IAAA,CAAAH,CAAA,CAAAE,aAAA;wBACA;sBACA;oBAAA,SAAAE,GAAA;sBAAAN,UAAA,CAAAO,CAAA,CAAAD,GAAA;oBAAA;sBAAAN,UAAA,CAAAQ,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAAT,UAAA,CAAAU,CAAA,CAAAD,GAAA;cAAA;gBAAAT,UAAA,CAAAW,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAnB,SAAA,CAAAoB,CAAA,CAAAD,GAAA;QAAA;UAAAnB,SAAA,CAAAqB,CAAA;QAAA;MACA;MACA,SAAAlF,WAAA,IAAA2D,SAAA,IAAAL,CAAA,aAAAA,CAAA,CAAAM,QAAA;QAAA,IAAAuB,UAAA,OAAArB,2BAAA,CAAAC,OAAA,EACA,KAAA/D,WAAA;UAAAoF,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAlB,CAAA,MAAAmB,MAAA,GAAAD,UAAA,CAAA7B,CAAA,IAAAY,IAAA;YAAA,IAAAmB,CAAA,GAAAD,MAAA,CAAAhB,KAAA;YACA,IAAAd,CAAA,YAAAA,CAAA,IAAAK,SAAA;cAAA,IAAA2B,UAAA,OAAAxB,2BAAA,CAAAC,OAAA,EACAT,CAAA;gBAAAiC,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAArB,CAAA,MAAAsB,MAAA,GAAAD,UAAA,CAAAhC,CAAA,IAAAY,IAAA;kBAAA,IAAAD,CAAA,GAAAsB,MAAA,CAAAnB,KAAA;kBACA,IAAAiB,CAAA,CAAAP,aAAA,IAAAb,CAAA;oBACAP,IAAA,CAAAqB,IAAA,CAAAM,CAAA;kBACA;kBACA,IAAAA,CAAA,CAAAhB,QAAA,IAAAV,SAAA,IAAA0B,CAAA,CAAAhB,QAAA,CAAAC,MAAA;oBAAA,IAAAkB,UAAA,OAAA1B,2BAAA,CAAAC,OAAA,EACAsB,CAAA,CAAAhB,QAAA;sBAAAoB,MAAA;oBAAA;sBAAA,KAAAD,UAAA,CAAAvB,CAAA,MAAAwB,MAAA,GAAAD,UAAA,CAAAlC,CAAA,IAAAY,IAAA;wBAAA,IAAAwB,EAAA,GAAAD,MAAA,CAAArB,KAAA;wBACA,IAAAsB,EAAA,CAAAZ,aAAA,IAAAb,CAAA;0BACAP,IAAA,CAAAqB,IAAA,CAAAW,EAAA;wBACA;sBACA;oBAAA,SAAAV,GAAA;sBAAAQ,UAAA,CAAAP,CAAA,CAAAD,GAAA;oBAAA;sBAAAQ,UAAA,CAAAN,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;cAAA;gBAAAM,UAAA,CAAAJ,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAG,UAAA,CAAAF,CAAA,CAAAD,GAAA;QAAA;UAAAG,UAAA,CAAAD,CAAA;QAAA;QACA,KAAAhF,cAAA,GAAAwD,IAAA;QACA,SAAAxD,cAAA,CAAAoE,MAAA;UAAA,IAAAqB,UAAA,OAAA7B,2BAAA,CAAAC,OAAA,EACA,KAAA7D,cAAA;YAAA0F,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAA1B,CAAA,MAAA2B,MAAA,GAAAD,UAAA,CAAArC,CAAA,IAAAY,IAAA;cAAA,IAAAC,EAAA,GAAAyB,MAAA,CAAAxB,KAAA;cACA,IAAAD,EAAA,CAAAE,QAAA,IAAAV,SAAA,IAAAQ,EAAA,CAAAE,QAAA,CAAAC,MAAA;gBAAA,IAAAuB,UAAA,OAAA/B,2BAAA,CAAAC,OAAA,EACAI,EAAA,CAAAE,QAAA;kBAAAyB,MAAA;gBAAA;kBAAA,KAAAD,UAAA,CAAA5B,CAAA,MAAA6B,MAAA,GAAAD,UAAA,CAAAvC,CAAA,IAAAY,IAAA;oBAAA,IAAAO,EAAA,GAAAqB,MAAA,CAAA1B,KAAA;oBACA,SAAA7B,IAAA,CAAAnC,UAAA,iBAAAmC,IAAA,CAAAnC,UAAA,CAAAwD,QAAA,CAAAa,EAAA,CAAAzG,OAAA,CAAA6G,SAAA,WAAAzE,UAAA,CAAAwD,QAAA,CAAAa,EAAA,CAAAK,aAAA;sBACA,KAAA1E,UAAA,CAAA2E,IAAA,CAAAN,EAAA,CAAAK,aAAA;oBACA;kBACA;gBAAA,SAAAE,GAAA;kBAAAa,UAAA,CAAAZ,CAAA,CAAAD,GAAA;gBAAA;kBAAAa,UAAA,CAAAX,CAAA;gBAAA;cACA;YACA;UAAA,SAAAF,GAAA;YAAAW,UAAA,CAAAV,CAAA,CAAAD,GAAA;UAAA;YAAAW,UAAA,CAAAT,CAAA;UAAA;QACA;MACA;IACA;IACA3C,IAAA,WAAAA,KAAA;MACA,SAAAA,IAAA,CAAAwD,cAAA,cAAAxD,IAAA,CAAAyD,YAAA,cAAAzD,IAAA,CAAA0D,YAAA,cAAA1D,IAAA,CAAA2D,WAAA;QACA,KAAArD,MAAA;MACA;QACA,KAAAA,MAAA;MACA;IACA;EACA;EACAsD,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA,GAAAC,IAAA;MACAF,KAAA,CAAAG,cAAA;MACAH,KAAA,CAAA3C,WAAA;MACA2C,KAAA,CAAAI,SAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAC,KAAA,WAAAD,GAAA;IACA;IACAH,SAAA,WAAAA,UAAA;MAAA,IAAAK,MAAA;MACA,SAAAvH,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAoI,SAAA,CAAAxC,MAAA,cAAAhF,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAqI,SAAA,CAAAD,SAAA;QACAE,cAAA,CAAAC,QAAA,kBAAAX,IAAA;UACAO,MAAA,CAAA9G,UAAA,GAAA8G,MAAA,CAAAvH,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAoI,SAAA;QACA;MACA;QACA,KAAA/G,UAAA,QAAAT,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAoI,SAAA;MACA;IACA;IACAP,cAAA,WAAAA,eAAA;MAAA,IAAAW,MAAA;MACA,SAAA5H,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAyI,cAAA,CAAA7C,MAAA,cAAAhF,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAqI,SAAA,CAAAI,cAAA;QACAH,cAAA,CAAAC,QAAA,sBAAAX,IAAA;UACAY,MAAA,CAAAjH,YAAA,GAAAiH,MAAA,CAAA5H,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAyI,cAAA;QACA;MACA;QACA,KAAAlH,YAAA,QAAAX,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAyI,cAAA;MACA;IACA;IACA1D,WAAA,WAAAA,YAAA;MAAA,IAAA2D,MAAA;MACA,SAAA9H,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA2I,mBAAA,CAAA/C,MAAA,cAAAhF,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAAqI,SAAA,CAAAM,mBAAA;QACAL,cAAA,CAAAC,QAAA,+BAAAX,IAAA;UACAc,MAAA,CAAApH,WAAA,GAAAoH,MAAA,CAAA9H,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA2I,mBAAA;QACA;MACA;QACA,KAAArH,WAAA,QAAAV,MAAA,CAAAC,KAAA,CAAAb,IAAA,CAAA2I,mBAAA;MACA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAA7I,IAAA;QACAuD,YAAA;QACAC,oBAAA;QACAE,sBAAA;QACAD,gBAAA;QACAE,kBAAA;QACAmF,gBAAA,OAAAjF,IAAA,CAAAiF,gBAAA;QACAC,kBAAA,OAAAlF,IAAA,CAAAkF,kBAAA;QACAzF,cAAA,OAAAO,IAAA,CAAAP,cAAA;QACAN,UAAA,OAAAA,UAAA;QACAV,QAAA,OAAAuB,IAAA,CAAAvB,QAAA;QACAC,QAAA,OAAAsB,IAAA,CAAAtB,QAAA;QACAyG,YAAA;MACA;MACA,IAAAC,sBAAA,OAAArI,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA,EAAAvB,IAAA,WAAAwB,QAAA;QACApJ,IAAA,CAAAuD,YAAA,GAAA6F,QAAA,CAAA7F,YAAA;QACAvD,IAAA,CAAAwD,oBAAA,GAAA4F,QAAA,CAAA5F,oBAAA;QACAxD,IAAA,CAAA0D,sBAAA,GAAA0F,QAAA,CAAA1F,sBAAA;QACA1D,IAAA,CAAAyD,gBAAA,GAAA2F,QAAA,CAAA3F,gBAAA;QACAzD,IAAA,CAAA2D,kBAAA,GAAAyF,QAAA,CAAAzF,kBAAA;MACA;MACA,IAAA3D,IAAA,CAAAsC,QAAA;QACA,SAAAjB,UAAA,IAAA4D,SAAA;UAAA,IAAAoE,UAAA,OAAAjE,2BAAA,CAAAC,OAAA,EACA,KAAAhE,UAAA;YAAAiI,MAAA;UAAA;YAAA,KAAAD,UAAA,CAAA9D,CAAA,MAAA+D,MAAA,GAAAD,UAAA,CAAAzE,CAAA,IAAAY,IAAA;cAAA,IAAAO,CAAA,GAAAuD,MAAA,CAAA5D,KAAA;cACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA;gBAAA,IAAAsE,WAAA,OAAAnE,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;kBAAA6D,OAAA;gBAAA;kBAAA,KAAAD,WAAA,CAAAhE,CAAA,MAAAiE,OAAA,GAAAD,WAAA,CAAA3E,CAAA,IAAAY,IAAA;oBAAA,IAAAU,CAAA,GAAAsD,OAAA,CAAA9D,KAAA;oBACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;sBAAA,IAAAwE,WAAA,OAAArE,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;wBAAA+D,OAAA;sBAAA;wBAAA,KAAAD,WAAA,CAAAlE,CAAA,MAAAmE,OAAA,GAAAD,WAAA,CAAA7E,CAAA,IAAAY,IAAA;0BAAA,IAAAmB,CAAA,GAAA+C,OAAA,CAAAhE,KAAA;0BACA,IAAAiB,CAAA,CAAAgD,OAAA,SAAA/I,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA;4BACAnJ,IAAA,CAAAsC,QAAA,QAAA1B,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA;0BACA;wBACA;sBAAA,SAAA7C,GAAA;wBAAAmD,WAAA,CAAAlD,CAAA,CAAAD,GAAA;sBAAA;wBAAAmD,WAAA,CAAAjD,CAAA;sBAAA;oBACA;kBACA;gBAAA,SAAAF,GAAA;kBAAAiD,WAAA,CAAAhD,CAAA,CAAAD,GAAA;gBAAA;kBAAAiD,WAAA,CAAA/C,CAAA;gBAAA;cACA;YACA;UAAA,SAAAF,GAAA;YAAA+C,UAAA,CAAA9C,CAAA,CAAAD,GAAA;UAAA;YAAA+C,UAAA,CAAA7C,CAAA;UAAA;QACA;MACA;MACA,KAAAoD,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAlB,kBAAA,EAAA5I,IAAA,EAAA4H,IAAA,WAAAwB,QAAA;YACA,IAAAW,GAAA,GAAAX,QAAA,CAAApJ,IAAA;YACA,IAAA+J,GAAA,IAAA9E,SAAA;cACA4D,MAAA,CAAAmB,QAAA,CAAAD,GAAA,CAAAf,YAAA;gBACAiB,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA,GAAAvC,IAAA;gBACAmC,GAAA,CAAAf,YAAA;gBACAH,MAAA,CAAAhF,IAAA,GAAAkG,GAAA;gBACAlB,MAAA,CAAAhF,IAAA,CAAAb,UAAA,GAAA6F,MAAA,CAAA7F,UAAA;gBACA,IAAA6F,MAAA,CAAAxH,UAAA,IAAA4D,SAAA;kBAAA,IAAAmF,WAAA,OAAAhF,2BAAA,CAAAC,OAAA,EACAwD,MAAA,CAAAxH,UAAA;oBAAAgJ,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAA7E,CAAA,MAAA8E,OAAA,GAAAD,WAAA,CAAAxF,CAAA,IAAAY,IAAA;sBAAA,IAAAO,GAAA,GAAAsE,OAAA,CAAA3E,KAAA;sBACA,IAAAK,GAAA,CAAAJ,QAAA,IAAAV,SAAA;wBAAA,IAAAqF,WAAA,OAAAlF,2BAAA,CAAAC,OAAA,EACAU,GAAA,CAAAJ,QAAA;0BAAA4E,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAA/E,CAAA,MAAAgF,OAAA,GAAAD,WAAA,CAAA1F,CAAA,IAAAY,IAAA;4BAAA,IAAAU,EAAA,GAAAqE,OAAA,CAAA7E,KAAA;4BACA,IAAAQ,EAAA,CAAAP,QAAA,IAAAV,SAAA;8BAAA,IAAAuF,WAAA,OAAApF,2BAAA,CAAAC,OAAA,EACAa,EAAA,CAAAP,QAAA;gCAAA8E,OAAA;8BAAA;gCAAA,KAAAD,WAAA,CAAAjF,CAAA,MAAAkF,OAAA,GAAAD,WAAA,CAAA5F,CAAA,IAAAY,IAAA;kCAAA,IAAAmB,EAAA,GAAA8D,OAAA,CAAA/E,KAAA;kCACA,IAAAiB,EAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAsC,QAAA;oCACAuG,MAAA,CAAAvG,QAAA,GAAAqE,EAAA,CAAA+D,MAAA;kCACA;kCACA,IAAA/D,EAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAuC,QAAA;oCACAsG,MAAA,CAAAtG,QAAA,GAAAoE,EAAA,CAAA+D,MAAA;kCACA;gCACA;8BAAA,SAAApE,GAAA;gCAAAkE,WAAA,CAAAjE,CAAA,CAAAD,GAAA;8BAAA;gCAAAkE,WAAA,CAAAhE,CAAA;8BAAA;4BACA;0BACA;wBAAA,SAAAF,GAAA;0BAAAgE,WAAA,CAAA/D,CAAA,CAAAD,GAAA;wBAAA;0BAAAgE,WAAA,CAAA9D,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAA8D,WAAA,CAAA7D,CAAA,CAAAD,GAAA;kBAAA;oBAAA8D,WAAA,CAAA5D,CAAA;kBAAA;gBACA;gBACAqC,MAAA,CAAAhF,IAAA,CAAAD,OAAA,GAAAwF,QAAA,CAAAxF,OAAA;gBACAiF,MAAA,CAAAhF,IAAA,CAAAP,cAAA,GAAA8F,QAAA,CAAA9F,cAAA;gBACAuF,MAAA,CAAAhF,IAAA,CAAAN,YAAA,GAAA6F,QAAA,CAAA7F,YAAA;gBACAsF,MAAA,CAAAhF,IAAA,CAAAJ,gBAAA,GAAA2F,QAAA,CAAA3F,gBAAA;gBACAoF,MAAA,CAAAhF,IAAA,CAAAL,oBAAA,GAAA4F,QAAA,CAAA5F,oBAAA;gBACAqF,MAAA,CAAAhF,IAAA,CAAAF,kBAAA,GAAAyF,QAAA,CAAAzF,kBAAA;gBACAkF,MAAA,CAAAhF,IAAA,CAAAH,sBAAA,GAAA0F,QAAA,CAAA1F,sBAAA;gBACAmF,MAAA,CAAAhF,IAAA,CAAAnC,UAAA,GAAA0H,QAAA,CAAA1H,UAAA;gBACAmH,MAAA,CAAAhF,IAAA,CAAA8G,eAAA,GAAAvB,QAAA,CAAAwB,sBAAA;gBACA/B,MAAA,CAAApH,eAAA,GAAA2H,QAAA,CAAA3H,eAAA;gBACAoH,MAAA,CAAA9G,WAAA;gBACA8G,MAAA,CAAAhH,KAAA;gBACAgH,MAAA,CAAA5I,OAAA;cACA;YACA;YACA,IAAAmJ,QAAA,CAAAyB,GAAA,CAAAC,QAAA,GAAAC,OAAA;cACAlC,MAAA,CAAAmB,QAAA,CAAAZ,QAAA,CAAAyB,GAAA;gBACAZ,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA,GAAAvC,IAAA;gBACA,IAAA5H,IAAA;kBACAgL,YAAA;kBACAC,WAAA;kBACAC,WAAA;kBACAC,YAAA,EAAAtC,MAAA,CAAAjI,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAA9K,IAAA,CAAAgN,KAAA;kBACAC,cAAA,EAAAjC,QAAA,CAAAyB;gBACA;gBACA,IAAAS,mBAAA,EAAAtL,IAAA,EAAA4H,IAAA,WAAAwB,QAAA;kBACAP,MAAA,CAAA0C,QAAA;oBACApB,IAAA;oBACAqB,OAAA;kBACA;gBACA;cACA;YACA;YACA,IAAApC,QAAA,CAAAyB,GAAA,CAAAC,QAAA,GAAAC,OAAA;cACAlC,MAAA,CAAAmB,QAAA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAC,IAAA;cACA,GAAAvC,IAAA;gBACA5H,IAAA,CAAAgJ,YAAA;gBACA,IAAAJ,kBAAA,EAAA5I,IAAA,EAAA4H,IAAA,WAAAwB,QAAA;kBACA,IAAAA,QAAA,CAAApJ,IAAA;oBACA6I,MAAA,CAAA0C,QAAA,CAAAE,OAAA;oBACA5C,MAAA,CAAAhF,IAAA,GAAAuF,QAAA,CAAApJ,IAAA;oBACA6I,MAAA,CAAAhF,IAAA,CAAAb,UAAA,GAAA6F,MAAA,CAAA7F,UAAA;oBACA,IAAA6F,MAAA,CAAAxH,UAAA,IAAA4D,SAAA;sBAAA,IAAAyG,WAAA,OAAAtG,2BAAA,CAAAC,OAAA,EACAwD,MAAA,CAAAxH,UAAA;wBAAAsK,OAAA;sBAAA;wBAAA,KAAAD,WAAA,CAAAnG,CAAA,MAAAoG,OAAA,GAAAD,WAAA,CAAA9G,CAAA,IAAAY,IAAA;0BAAA,IAAAO,GAAA,GAAA4F,OAAA,CAAAjG,KAAA;0BACA,IAAAK,GAAA,CAAAJ,QAAA,IAAAV,SAAA;4BAAA,IAAA2G,WAAA,OAAAxG,2BAAA,CAAAC,OAAA,EACAU,GAAA,CAAAJ,QAAA;8BAAAkG,OAAA;4BAAA;8BAAA,KAAAD,WAAA,CAAArG,CAAA,MAAAsG,OAAA,GAAAD,WAAA,CAAAhH,CAAA,IAAAY,IAAA;gCAAA,IAAAU,GAAA,GAAA2F,OAAA,CAAAnG,KAAA;gCACA,IAAAQ,GAAA,CAAAP,QAAA,IAAAV,SAAA;kCAAA,IAAA6G,WAAA,OAAA1G,2BAAA,CAAAC,OAAA,EACAa,GAAA,CAAAP,QAAA;oCAAAoG,OAAA;kCAAA;oCAAA,KAAAD,WAAA,CAAAvG,CAAA,MAAAwG,OAAA,GAAAD,WAAA,CAAAlH,CAAA,IAAAY,IAAA;sCAAA,IAAAmB,GAAA,GAAAoF,OAAA,CAAArG,KAAA;sCACA,IAAAiB,GAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAsC,QAAA;wCACAuG,MAAA,CAAAvG,QAAA,GAAAqE,GAAA,CAAA+D,MAAA;sCACA;sCACA,IAAA/D,GAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAuC,QAAA;wCACAsG,MAAA,CAAAtG,QAAA,GAAAoE,GAAA,CAAA+D,MAAA;sCACA;oCACA;kCAAA,SAAApE,GAAA;oCAAAwF,WAAA,CAAAvF,CAAA,CAAAD,GAAA;kCAAA;oCAAAwF,WAAA,CAAAtF,CAAA;kCAAA;gCACA;8BACA;4BAAA,SAAAF,GAAA;8BAAAsF,WAAA,CAAArF,CAAA,CAAAD,GAAA;4BAAA;8BAAAsF,WAAA,CAAApF,CAAA;4BAAA;0BACA;wBACA;sBAAA,SAAAF,GAAA;wBAAAoF,WAAA,CAAAnF,CAAA,CAAAD,GAAA;sBAAA;wBAAAoF,WAAA,CAAAlF,CAAA;sBAAA;oBACA;oBACAqC,MAAA,CAAAhF,IAAA,CAAAD,OAAA,GAAAwF,QAAA,CAAAxF,OAAA;oBACAiF,MAAA,CAAAhF,IAAA,CAAAP,cAAA,GAAA8F,QAAA,CAAA9F,cAAA;oBACAuF,MAAA,CAAAhF,IAAA,CAAAN,YAAA,GAAA6F,QAAA,CAAA7F,YAAA;oBACAsF,MAAA,CAAAhF,IAAA,CAAAJ,gBAAA,GAAA2F,QAAA,CAAA3F,gBAAA;oBACAoF,MAAA,CAAAhF,IAAA,CAAAL,oBAAA,GAAA4F,QAAA,CAAA5F,oBAAA;oBACAqF,MAAA,CAAAhF,IAAA,CAAAF,kBAAA,GAAAyF,QAAA,CAAAzF,kBAAA;oBACAkF,MAAA,CAAAhF,IAAA,CAAAH,sBAAA,GAAA0F,QAAA,CAAA1F,sBAAA;oBACAmF,MAAA,CAAAhF,IAAA,CAAAnC,UAAA,GAAA0H,QAAA,CAAA1H,UAAA;oBACAmH,MAAA,CAAAhF,IAAA,CAAA8G,eAAA,GAAAvB,QAAA,CAAAwB,sBAAA;oBACA/B,MAAA,CAAApH,eAAA,GAAA2H,QAAA,CAAA3H,eAAA;oBACAoH,MAAA,CAAA9G,WAAA;oBACA8G,MAAA,CAAAhH,KAAA;oBACAgH,MAAA,CAAA5I,OAAA;kBACA;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA,aACA0H,OAAA,WAAAA,QAAA;MAAA,IAAAqE,MAAA;MAAA,WAAAC,kBAAA,CAAA5G,OAAA,oBAAA6G,oBAAA,CAAA7G,OAAA,IAAA8G,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAA7G,OAAA,IAAAgH,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,MAAA,CAAA/L,OAAA;cAAAsM,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,oBAAA,MAAAC,cAAA,CAAAtH,OAAA,MAAAsH,cAAA,CAAAtH,OAAA,MACA2G,MAAA,CAAAnJ,WAAA;gBACA+J,UAAA;gBACAC,eAAA,EAAAb,MAAA,CAAApL,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAA4D,mBAAA,CAAAC;cAAA,EACA,EAAAnF,IAAA,WAAAwB,QAAA;gBACA4C,MAAA,CAAAhL,WAAA,GAAAoI,QAAA,CAAA4D,IAAA;gBACA,KAAAC,KAAA,CAAA7D,QAAA,CAAA5I,KAAA;kBACAwL,MAAA,CAAAxL,KAAA,GAAA4I,QAAA,CAAA5I,KAAA;gBACA;gBACAwL,MAAA,CAAA/L,OAAA;cACA;YAAA;YAAA;cAAA,OAAAsM,QAAA,CAAAW,IAAA;UAAA;QAAA,GAAAd,OAAA;MAAA;IACA;IACAe,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAzH,QAAA,KAAAyH,IAAA,CAAAzH,QAAA,CAAAC,MAAA;QACA,OAAAwH,IAAA,CAAAzH,QAAA;MACA;MACA,IAAA0H,CAAA;MACA,IAAAD,IAAA,CAAAE,KAAA;QACA,IAAAF,IAAA,CAAAE,KAAA,CAAAC,oBAAA,YAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;UACA,IAAAJ,IAAA,CAAAnO,IAAA,CAAAwO,aAAA;YACAJ,CAAA,GAAAD,IAAA,CAAAnO,IAAA,CAAAwO,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAnO,IAAA,CAAAwO,aAAA;UACA;YACAJ,CAAA,GAAAD,IAAA,CAAAQ,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAQ,IAAA,CAAAC,aAAA;UACA;QACA;UACAR,CAAA,GAAAD,IAAA,CAAAE,KAAA,CAAAQ,SAAA,SAAAV,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA,SAAAJ,IAAA,CAAAE,KAAA,CAAAS,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAJ,IAAA,CAAAY,MAAA;QACA;UACAC,EAAA,EAAAb,IAAA,CAAAY,MAAA;UACAE,KAAA,EAAAb,CAAA;UACA1H,QAAA,EAAAyH,IAAA,CAAAzH,QAAA;UACAwI,UAAA,EAAAf,IAAA,CAAAzD,OAAA,YAAAyD,IAAA,CAAAzH,QAAA,IAAAV;QACA;MACA;QACA;UACAgJ,EAAA,EAAAb,IAAA,CAAA1C,MAAA;UACAwD,KAAA,EAAAb,CAAA;UACA1H,QAAA,EAAAyH,IAAA,CAAAzH,QAAA;UACAwI,UAAA,EAAAf,IAAA,CAAAzD,OAAA,YAAAyD,IAAA,CAAAzH,QAAA,IAAAV;QACA;MACA;IACA;IACAmJ,iBAAA,WAAAA,kBAAAhB,IAAA;MACA,IAAAA,IAAA,CAAAzH,QAAA,KAAAyH,IAAA,CAAAzH,QAAA,CAAAC,MAAA;QACA,OAAAwH,IAAA,CAAAzH,QAAA;MACA;MACA,IAAA0H,CAAA;MACA,KAAAD,IAAA,CAAA9N,OAAA,IAAA8N,IAAA,CAAA9N,OAAA,CAAA+O,gBAAA,YAAAjB,IAAA,CAAA9N,OAAA,CAAAgP,aAAA;QACAjB,CAAA,GAAAD,IAAA,CAAAmB,gBAAA,SAAAnB,IAAA,CAAAoB,aAAA,SAAAd,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAAmB,gBAAA;MACA;QACAlB,CAAA,IAAAD,IAAA,CAAA9N,OAAA,CAAAmP,eAAA,WAAArB,IAAA,CAAA9N,OAAA,CAAAmP,eAAA,gBAAArB,IAAA,CAAA9N,OAAA,CAAAgP,aAAA,WAAAlB,IAAA,CAAA9N,OAAA,CAAAgP,aAAA,gBAAAlB,IAAA,CAAA9N,OAAA,CAAA+O,gBAAA,WAAAjB,IAAA,CAAA9N,OAAA,CAAA+O,gBAAA,eAAAX,iBAAA,CAAAC,YAAA,CAAAP,IAAA,CAAA9N,OAAA,CAAA+O,gBAAA,WAAAjB,IAAA,CAAA9N,OAAA,CAAA+O,gBAAA;MACA;MACA;QACAJ,EAAA,EAAAb,IAAA,CAAAhH,aAAA;QACA8H,KAAA,EAAAb,CAAA;QACA1H,QAAA,EAAAyH,IAAA,CAAAzH;MACA;IACA;IACA;IACA+I,MAAA,WAAAA,OAAA;MACA,KAAA3M,WAAA;MACA,KAAAE,WAAA;MACA,KAAAD,SAAA;MACA,KAAAE,iBAAA;MACA,KAAAC,aAAA;MACA,KAAAC,aAAA;MACA,KAAA3B,GAAA;MACA,KAAAqB,KAAA;MACA,KAAAO,IAAA;MACA,KAAAsM,KAAA;MACA,KAAArM,QAAA;MACA,KAAAC,QAAA;MACA;IACA;IACAqM,aAAA,WAAAA,cAAA;MACA,KAAA3M,WAAA;IACA;IACA;IACA0M,KAAA,WAAAA,MAAA;MACA,KAAArM,QAAA;MACA,KAAAC,QAAA;MACA,KAAAb,UAAA;MACA,KAAAmC,IAAA;QACAvB,QAAA;QACAC,QAAA;QACAb,UAAA;QACAmN,cAAA;QACAC,SAAA;QACAC,eAAA;QACAjG,gBAAA;QACAC,kBAAA;QACAiG,gBAAA;QACAC,aAAA;QACAC,cAAA;QACAtL,OAAA;QACAN,cAAA;QACAC,YAAA;QACAC,oBAAA;QACAC,gBAAA;QACAC,sBAAA;QACAC,kBAAA;QACAgH,eAAA;QACAwE,cAAA;QACAnM,UAAA,OAAAA,UAAA;QACAE,UAAA;QACAmE,cAAA;QACA+H,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACA/H,YAAA;QACAgI,cAAA;QACAC,gBAAA;QACAC,gBAAA;QACAnI,YAAA;QACAoI,cAAA;QACAC,gBAAA;QACAC,gBAAA;QACApI,WAAA;QACAqI,aAAA;QACAC,eAAA;QACAC,eAAA;QACAlQ,MAAA;QACAmQ,eAAA;MACA;MACA,KAAAtO,UAAA;MACA,KAAAwC,UAAA;MACA,KAAA+L,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAArN,WAAA,CAAAC,OAAA;MACA,KAAA6E,OAAA;IACA;IACA,aACAwI,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAxN,aAAA;MACA,KAAAI,WAAA,CAAAK,UAAA;MACA,KAAAL,WAAA,CAAAS,cAAA;MACA,KAAAT,WAAA,CAAAU,YAAA;MACA,KAAAV,WAAA,CAAAW,oBAAA;MACA,KAAAX,WAAA,CAAAY,gBAAA;MACA,KAAAZ,WAAA,CAAAa,sBAAA;MACA,KAAAb,WAAA,CAAAc,kBAAA;MACA,KAAAd,WAAA,CAAA8H,eAAA;MACA,KAAAuF,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/P,GAAA,GAAA+P,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzB,SAAA;MAAA;MACA,KAAA1O,MAAA,GAAAiQ,SAAA,CAAAzK,MAAA;MACA,KAAAvF,QAAA,IAAAgQ,SAAA,CAAAzK,MAAA;MACA,KAAAlF,SAAA,GAAA2P,SAAA,CAAAzK,MAAA;MACA,IAAAyK,SAAA,CAAAzK,MAAA;QACA,KAAA4K,cAAA,CAAAH,SAAA;MACA;MACA,IAAAA,SAAA,CAAAzK,MAAA;QACA,KAAA7E,SAAA,GAAAsP,SAAA;MACA;IACA;IACA,aACAI,SAAA,WAAAA,UAAA;MACA,KAAA9B,KAAA;MACA,KAAAtM,IAAA;MACA,KAAAwB,IAAA,CAAAvB,QAAA;MACA,KAAAP,WAAA;MACA,KAAAF,KAAA;MACA,KAAAgC,IAAA,CAAAP,cAAA;MACA,KAAA9B,cAAA,QAAAF,WAAA;MACA,SAAAE,cAAA,IAAAyD,SAAA,SAAApB,IAAA,CAAAP,cAAA;QAAA,IAAAoN,WAAA,OAAAtL,2BAAA,CAAAC,OAAA,EACA,KAAA7D,cAAA;UAAAmP,OAAA;QAAA;UAAA,KAAAD,WAAA,CAAAnL,CAAA,MAAAoL,OAAA,GAAAD,WAAA,CAAA9L,CAAA,IAAAY,IAAA;YAAA,IAAAO,CAAA,GAAA4K,OAAA,CAAAjL,KAAA;YACA,KAAA7B,IAAA,CAAAP,cAAA,CAAA+C,IAAA,CAAAN,CAAA,CAAAK,aAAA;UACA;QAAA,SAAAE,GAAA;UAAAoK,WAAA,CAAAnK,CAAA,CAAAD,GAAA;QAAA;UAAAoK,WAAA,CAAAlK,CAAA;QAAA;MACA;MACA,KAAA/F,GAAA;MACA,SAAAY,UAAA,IAAA4D,SAAA;QAAA,IAAA2L,WAAA,OAAAxL,2BAAA,CAAAC,OAAA,EACA,KAAAhE,UAAA;UAAAwP,OAAA;QAAA;UAAA,KAAAD,WAAA,CAAArL,CAAA,MAAAsL,OAAA,GAAAD,WAAA,CAAAhM,CAAA,IAAAY,IAAA;YAAA,IAAAO,GAAA,GAAA8K,OAAA,CAAAnL,KAAA;YACA,IAAAK,GAAA,CAAAJ,QAAA,IAAAV,SAAA;cAAA,IAAA6L,WAAA,OAAA1L,2BAAA,CAAAC,OAAA,EACAU,GAAA,CAAAJ,QAAA;gBAAAoL,OAAA;cAAA;gBAAA,KAAAD,WAAA,CAAAvL,CAAA,MAAAwL,OAAA,GAAAD,WAAA,CAAAlM,CAAA,IAAAY,IAAA;kBAAA,IAAAU,CAAA,GAAA6K,OAAA,CAAArL,KAAA;kBACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;oBAAA,IAAA+L,WAAA,OAAA5L,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;sBAAAsL,OAAA;oBAAA;sBAAA,KAAAD,WAAA,CAAAzL,CAAA,MAAA0L,OAAA,GAAAD,WAAA,CAAApM,CAAA,IAAAY,IAAA;wBAAA,IAAAmB,CAAA,GAAAsK,OAAA,CAAAvL,KAAA;wBACA,IAAAiB,CAAA,CAAAgD,OAAA,SAAA/I,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA;0BACA,KAAA7G,QAAA,GAAAqE,CAAA,CAAA+D,MAAA;wBACA;sBACA;oBAAA,SAAApE,GAAA;sBAAA0K,WAAA,CAAAzK,CAAA,CAAAD,GAAA;oBAAA;sBAAA0K,WAAA,CAAAxK,CAAA;oBAAA;kBACA;gBACA;cAAA,SAAAF,GAAA;gBAAAwK,WAAA,CAAAvK,CAAA,CAAAD,GAAA;cAAA;gBAAAwK,WAAA,CAAAtK,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAsK,WAAA,CAAArK,CAAA,CAAAD,GAAA;QAAA;UAAAsK,WAAA,CAAApK,CAAA;QAAA;MACA;MACA;MACA,KAAA3C,IAAA,CAAAqN,qBAAA;MACA,KAAA9M,WAAA;IACA;IACA+M,SAAA,WAAAA,UAAAlJ,GAAA;MAAA,IAAAmJ,MAAA;MACA,IAAAnJ,GAAA,CAAAoJ,GAAA;QACA,KAAAb,cAAA,CAAAvI,GAAA,CAAAvC,KAAA;QACA,IAAA4L,mBAAA,EAAArJ,GAAA,CAAAvC,KAAA,CAAAoJ,SAAA,EAAAlH,IAAA,WAAAwB,QAAA;UACAgI,MAAA,CAAAnQ,SAAA,GAAAmI,QAAA,CAAAnI,SAAA;UACAmQ,MAAA,CAAApP,SAAA;QACA;MACA;MACA,IAAAiG,GAAA,CAAAoJ,GAAA;QACA,KAAAb,cAAA,CAAAvI,GAAA,CAAAvC,KAAA;QACA,IAAA6L,gCAAA;UAAAC,YAAA,EAAAvJ,GAAA,CAAAvC,KAAA,CAAAoJ;QAAA,GAAAlH,IAAA,WAAAwB,QAAA;UACAgI,MAAA,CAAAjQ,iBAAA,GAAAiI,QAAA,CAAA4D,IAAA;UACAoE,MAAA,CAAAzO,MAAA,GAAAyG,QAAA,CAAAqI,KAAA;UACAL,MAAA,CAAAlP,iBAAA;QACA;MACA;MACA,IAAA+F,GAAA,CAAAoJ,GAAA;QACA,KAAAb,cAAA,CAAAvI,GAAA,CAAAvC,KAAA;QACA,IAAAgM,oCAAA;UAAAF,YAAA,EAAAvJ,GAAA,CAAAvC,KAAA,CAAAoJ;QAAA,GAAAlH,IAAA,WAAAwB,QAAA;UACAgI,MAAA,CAAAhQ,aAAA,GAAAgI,QAAA,CAAA4D,IAAA;UACAoE,MAAA,CAAAxO,MAAA,GAAAwG,QAAA,CAAAqI,KAAA;UACAL,MAAA,CAAAjP,aAAA;QACA;MACA;MACA,IAAA8F,GAAA,CAAAoJ,GAAA;QACA,KAAAb,cAAA,CAAAvI,GAAA,CAAAvC,KAAA;QACA,IAAAiM,gBAAA,EAAA1J,GAAA,CAAAvC,KAAA,CAAAoJ,SAAA,EAAAlH,IAAA,WAAAwB,QAAA;UACAgI,MAAA,CAAAlQ,WAAA,GAAAkI,QAAA,CAAAlI,WAAA;UACAkQ,MAAA,CAAAnP,WAAA;QACA;MACA;IACA;IACA;IACAuO,cAAA,WAAAA,eAAAvI,GAAA;MACA,KAAAtG,WAAA;QACAmN,SAAA,EAAA7G,GAAA,CAAA6G,SAAA,WAAA7G,GAAA,CAAA6G,SAAA;QACAI,cAAA,EAAAjH,GAAA,CAAAiH,cAAA,WAAAjH,GAAA,CAAAiH,cAAA;QACApG,gBAAA,EAAAb,GAAA,CAAAa,gBAAA,WAAAb,GAAA,CAAAa,gBAAA;QACAC,kBAAA,EAAAd,GAAA,CAAAc,kBAAA,WAAAd,GAAA,CAAAc,kBAAA;QACAiG,gBAAA,EAAA/G,GAAA,CAAA+G,gBAAA,WAAA/G,GAAA,CAAA+G,gBAAA;QACAC,aAAA,EAAAhH,GAAA,CAAAgH,aAAA,WAAAhH,GAAA,CAAAgH,aAAA;QACA2C,eAAA,EAAA3J,GAAA,CAAA/E,UAAA,WAAA+E,GAAA,CAAA/E,UAAA;QACA2O,WAAA,EAAA5J,GAAA,CAAAqF,KAAA,WAAArF,GAAA,CAAAqF,KAAA,CAAA3D,OAAA;MACA;IACA;IACA,aACAmI,YAAA,WAAAA,aAAA7J,GAAA;MAAA,IAAA8J,MAAA;MACA,KAAApD,KAAA;MACA,KAAA1O,OAAA;MACA,KAAAoC,IAAA;MACA,KAAA6B,UAAA,GAAA+D,GAAA;MACA,KAAAxH,GAAA,GAAAuR,aAAA,CAAAC,QAAA;MACA,IAAAnD,SAAA,GAAA7G,GAAA,CAAA6G,SAAA,SAAAxO,GAAA;MACA,KAAA8D,WAAA;MACA,IAAA8N,mBAAA,EAAApD,SAAA,EAAAlH,IAAA,WAAAwB,QAAA;QACA2I,MAAA,CAAAlO,IAAA,GAAAuF,QAAA,CAAApJ,IAAA;QACA,IAAA+R,MAAA,CAAA1Q,UAAA,IAAA4D,SAAA;UAAA,IAAAkN,WAAA,OAAA/M,2BAAA,CAAAC,OAAA,EACA0M,MAAA,CAAA1Q,UAAA;YAAA+Q,OAAA;UAAA;YAAA,KAAAD,WAAA,CAAA5M,CAAA,MAAA6M,OAAA,GAAAD,WAAA,CAAAvN,CAAA,IAAAY,IAAA;cAAA,IAAAO,CAAA,GAAAqM,OAAA,CAAA1M,KAAA;cACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA;gBAAA,IAAAoN,WAAA,OAAAjN,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;kBAAA2M,OAAA;gBAAA;kBAAA,KAAAD,WAAA,CAAA9M,CAAA,MAAA+M,OAAA,GAAAD,WAAA,CAAAzN,CAAA,IAAAY,IAAA;oBAAA,IAAAU,CAAA,GAAAoM,OAAA,CAAA5M,KAAA;oBACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;sBAAA,IAAAsN,WAAA,OAAAnN,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;wBAAA6M,OAAA;sBAAA;wBAAA,KAAAD,WAAA,CAAAhN,CAAA,MAAAiN,OAAA,GAAAD,WAAA,CAAA3N,CAAA,IAAAY,IAAA;0BAAA,IAAAmB,CAAA,GAAA6L,OAAA,CAAA9M,KAAA;0BACA,IAAAiB,CAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAsC,QAAA;4BACAyP,MAAA,CAAAzP,QAAA,GAAAqE,CAAA,CAAA+D,MAAA;0BACA;0BACA,IAAA/D,CAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAuC,QAAA;4BACAwP,MAAA,CAAAxP,QAAA,GAAAoE,CAAA,CAAA+D,MAAA;0BACA;wBACA;sBAAA,SAAApE,GAAA;wBAAAiM,WAAA,CAAAhM,CAAA,CAAAD,GAAA;sBAAA;wBAAAiM,WAAA,CAAA/L,CAAA;sBAAA;oBACA;kBACA;gBAAA,SAAAF,GAAA;kBAAA+L,WAAA,CAAA9L,CAAA,CAAAD,GAAA;gBAAA;kBAAA+L,WAAA,CAAA7L,CAAA;gBAAA;cACA;YACA;UAAA,SAAAF,GAAA;YAAA6L,WAAA,CAAA5L,CAAA,CAAAD,GAAA;UAAA;YAAA6L,WAAA,CAAA3L,CAAA;UAAA;QACA;QACAuL,MAAA,CAAAlO,IAAA,CAAAD,OAAA,GAAAwF,QAAA,CAAAxF,OAAA;QACAmO,MAAA,CAAAlO,IAAA,CAAAP,cAAA,GAAA8F,QAAA,CAAA9F,cAAA;QACAyO,MAAA,CAAAlO,IAAA,CAAAN,YAAA,GAAA6F,QAAA,CAAA7F,YAAA;QACAwO,MAAA,CAAAlO,IAAA,CAAAJ,gBAAA,GAAA2F,QAAA,CAAA3F,gBAAA;QACAsO,MAAA,CAAAlO,IAAA,CAAAL,oBAAA,GAAA4F,QAAA,CAAA5F,oBAAA;QACAuO,MAAA,CAAAlO,IAAA,CAAAF,kBAAA,GAAAyF,QAAA,CAAAzF,kBAAA;QACAoO,MAAA,CAAAlO,IAAA,CAAAH,sBAAA,GAAA0F,QAAA,CAAA1F,sBAAA;QACAqO,MAAA,CAAAlO,IAAA,CAAAnC,UAAA,GAAA0H,QAAA,CAAA1H,UAAA;QACAqQ,MAAA,CAAAlO,IAAA,CAAA8G,eAAA,GAAAvB,QAAA,CAAAwB,sBAAA;QACAmH,MAAA,CAAAtQ,eAAA,GAAA2H,QAAA,CAAA3H,eAAA;QACAsQ,MAAA,CAAAhQ,WAAA;QACAgQ,MAAA,CAAAlQ,KAAA;QACAkQ,MAAA,CAAAlO,IAAA,CAAAmM,eAAA,GAAA5G,QAAA,CAAApJ,IAAA,CAAAF,cAAA,GAAAsJ,QAAA,CAAApJ,IAAA,CAAAF,cAAA,CAAAsL,KAAA;QACA2G,MAAA,CAAA9R,OAAA;QAEA,IAAAmJ,QAAA,CAAApJ,IAAA,CAAA8D,kBAAA,aAAAsF,QAAA,CAAApJ,IAAA,CAAA+D,gBAAA;UACAgO,MAAA,CAAAlO,IAAA,CAAA4O,kBAAA;UACAV,MAAA,CAAAlO,IAAA,CAAA4O,kBAAA,CAAApM,IAAA,CAAA+C,QAAA,CAAApJ,IAAA,CAAA8D,kBAAA;UACAiO,MAAA,CAAAlO,IAAA,CAAA4O,kBAAA,CAAApM,IAAA,CAAA+C,QAAA,CAAApJ,IAAA,CAAA+D,gBAAA;QACA;QAEA,IAAA2O,SAAA,OAAAC,IAAA,CAAAC,YAAA;UACAC,KAAA;UACAC,qBAAA;UACAC,qBAAA;QACA;QACAhB,MAAA,CAAAlO,IAAA,CAAAmP,WAAA,GAAA5J,QAAA,CAAApJ,IAAA,CAAAgT,WAAA,CAAAC,cAAA;QACAlB,MAAA,CAAAlO,IAAA,CAAAmP,WAAA,GAAAjB,MAAA,CAAAlO,IAAA,CAAAmP,WAAA,CAAAE,OAAA;QACAnB,MAAA,CAAAlO,IAAA,CAAAmP,WAAA,GAAAN,SAAA,CAAAS,MAAA,CAAApB,MAAA,CAAAlO,IAAA,CAAAmP,WAAA;MAEA;IAEA;IACA,WACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAzJ,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA;QACAuJ,MAAA,CAAAxP,IAAA,CAAAmP,WAAA,GAAAK,MAAA,CAAAxP,IAAA,CAAAmP,WAAA,GAAAK,MAAA,CAAAxP,IAAA,CAAAmP,WAAA,CAAAE,OAAA;QACA,IAAAG,MAAA,CAAAxP,IAAA,CAAA4O,kBAAA,IAAAY,MAAA,CAAAxP,IAAA,CAAA4O,kBAAA,CAAA7M,MAAA;UACAyN,MAAA,CAAAxP,IAAA,CAAAC,kBAAA,GAAAuP,MAAA,CAAAxP,IAAA,CAAA4O,kBAAA;UACAY,MAAA,CAAAxP,IAAA,CAAAE,gBAAA,GAAAsP,MAAA,CAAAxP,IAAA,CAAA4O,kBAAA;QACA;QACA;QACA,IAAAa,SAAA,OAAAC,IAAA,CAAAF,MAAA,CAAAxP,IAAA,CAAAC,kBAAA;QACA,IAAA0P,OAAA,OAAAD,IAAA,CAAAF,MAAA,CAAAxP,IAAA,CAAAE,gBAAA;QACA,IAAAuP,SAAA,GAAAE,OAAA;UACA,IAAAC,kBAAA;YACAjI,OAAA;YACArB,IAAA;UACA;UACA;QACA;QACA;QACA,IAAAkJ,MAAA,CAAAxP,IAAA,CAAA6P,gBAAA,YAAAL,MAAA,CAAAxP,IAAA,CAAA6P,gBAAA;UACA,IAAAD,kBAAA;YACAjI,OAAA;YACArB,IAAA;UACA;UACA;QACA;QACA,IAAAL,KAAA;UACA,IAAAuJ,MAAA,CAAAxP,IAAA,CAAAiL,SAAA;YACA,IAAA6E,sBAAA,EAAAN,MAAA,CAAAxP,IAAA,EAAA+D,IAAA,WAAAwB,QAAA;cACAiK,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAAtR,WAAA;cACAsR,MAAA,CAAA1L,OAAA;YACA;YACA0L,MAAA,CAAA1E,KAAA;UACA;YACA0E,MAAA,CAAA9H,QAAA,CAAAuI,IAAA;UACA;QACA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA9L,GAAA;MAAA,IAAA+L,OAAA;MACA,IAAAC,UAAA,GAAAhM,GAAA,CAAA6G,SAAA,SAAAxO,GAAA;MACA,KAAA0J,QAAA,mBAAAiK,UAAA;QAAAC,WAAA;MAAA,GAAAtM,IAAA;QACA,WAAAuM,mBAAA,EAAAF,UAAA;MACA,GAAArM,IAAA;QACAoM,OAAA,CAAArM,OAAA;QACAqM,OAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA,cACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAjS,aAAA;IACA;IACA,aACAkS,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,8BAAA5H,cAAA,CAAAtH,OAAA,MACA,KAAAxC,WAAA,cAAA2R,MAAA,CACA,IAAAjB,IAAA,GAAAkB,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,KAAA9R,WAAA,CAAAK,UAAA,GAAAyR,GAAA;MACA,KAAAzE,WAAA;IACA;IACA0E,aAAA,WAAAA,cAAAD,GAAA;MACA,KAAA9Q,IAAA,CAAAX,UAAA,GAAAyR,GAAA;IACA;IACAE,WAAA,WAAAA,YAAAF,GAAA;MACA,KAAA9Q,IAAA,CAAAiR,QAAA,GAAAH,GAAA,CAAAI,eAAA;IACA;IACAC,kBAAA,WAAAA,mBAAAL,GAAA;MACA,KAAA9Q,IAAA,CAAA8G,eAAA,GAAAgK,GAAA;IACA;IACAM,iBAAA,WAAAA,kBAAAN,GAAA;MACA,KAAA9Q,IAAA,CAAAP,cAAA,GAAAqR,GAAA;MACA,IAAAA,GAAA,IAAA1P,SAAA;QACA,KAAAvD,UAAA;QACA,KAAAmC,IAAA,CAAAnC,UAAA;MACA;IACA;IACAwT,mBAAA,WAAAA,oBAAAP,GAAA;MACA,KAAA9R,WAAA,CAAAS,cAAA,GAAAqR,GAAA;MACA,KAAAzE,WAAA;IACA;IACAiF,eAAA,WAAAA,gBAAAR,GAAA;MACA,KAAA9Q,IAAA,CAAAN,YAAA,GAAAoR,GAAA;IACA;IACAS,iBAAA,WAAAA,kBAAAT,GAAA;MACA,KAAA9R,WAAA,CAAAU,YAAA,GAAAoR,GAAA;MACA,KAAAzE,WAAA;IACA;IACAmF,iBAAA,WAAAA,kBAAAV,GAAA;MACA,KAAA9Q,IAAA,CAAAD,OAAA,GAAA+Q,GAAA;IACA;IACAW,mBAAA,WAAAA,oBAAAX,GAAA;MACA,KAAA9R,WAAA,CAAAe,OAAA,GAAA+Q,GAAA;MACA,KAAAzE,WAAA;IACA;IACAqF,yBAAA,WAAAA,0BAAAZ,GAAA;MACA,KAAA9R,WAAA,CAAAW,oBAAA,GAAAmR,GAAA;MACA,KAAAzE,WAAA;IACA;IACAsF,mBAAA,WAAAA,oBAAAb,GAAA;MACA,KAAA9Q,IAAA,CAAAJ,gBAAA,GAAAkR,GAAA;IACA;IACAc,yBAAA,WAAAA,0BAAAd,GAAA;MACA,KAAA9Q,IAAA,CAAAH,sBAAA,GAAAiR,GAAA;IACA;IACAe,qBAAA,WAAAA,sBAAAf,GAAA;MACA,KAAA9R,WAAA,CAAAY,gBAAA,GAAAkR,GAAA;MACA,KAAAzE,WAAA;IACA;IACAyF,uBAAA,WAAAA,wBAAAhB,GAAA;MACA,KAAA9Q,IAAA,CAAAL,oBAAA,GAAAmR,GAAA;IACA;IACAiB,2BAAA,WAAAA,4BAAAjB,GAAA;MACA,KAAA9R,WAAA,CAAAa,sBAAA,GAAAiR,GAAA;MACA,KAAAzE,WAAA;IACA;IACA2F,qBAAA,WAAAA,sBAAAlB,GAAA;MACA,KAAA9Q,IAAA,CAAAF,kBAAA,GAAAgR,GAAA;IACA;IACAmB,uBAAA,WAAAA,wBAAAnB,GAAA;MACA,KAAA9R,WAAA,CAAAc,kBAAA,GAAAgR,GAAA;MACA,KAAAzE,WAAA;IACA;IACA6F,oBAAA,WAAAA,qBAAA3I,IAAA;MACA,KAAAvJ,IAAA,CAAAvB,QAAA,GAAA8K,IAAA,CAAAzD,OAAA;IACA;IACAqM,sBAAA,WAAAA,uBAAAvQ,CAAA;MACA,IAAAA,CAAA,IAAAR,SAAA;QACA,KAAApB,IAAA,CAAAvB,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACA2T,oBAAA,WAAAA,qBAAA7I,IAAA;MACA,KAAAvJ,IAAA,CAAAtB,QAAA,GAAA6K,IAAA,CAAAzD,OAAA;IACA;IACAuM,sBAAA,WAAAA,uBAAAxQ,KAAA;MACA,IAAAA,KAAA,IAAAT,SAAA;QACA,KAAApB,IAAA,CAAAtB,QAAA;QACA,KAAAA,QAAA;MACA;IACA;IACA4T,qBAAA,WAAAA,sBAAA/I,IAAA;MACA,KAAAvK,WAAA,CAAAL,cAAA,GAAA4K,IAAA,CAAAzD,OAAA;MACA,KAAAuG,WAAA;IACA;IACAkG,cAAA,WAAAA,eAAAzB,GAAA;MACA,IAAAA,GAAA,IAAA1P,SAAA;QACA,KAAApC,WAAA,CAAAL,cAAA;QACA,KAAA0N,WAAA;MACA;IACA;IACAmG,aAAA,WAAAA,cAAA1B,GAAA;MACA,IAAAA,GAAA,IAAA1P,SAAA;QACA,KAAApC,WAAA,CAAAJ,aAAA;QACA,KAAAyN,WAAA;MACA;IACA;IACAoG,oBAAA,WAAAA,qBAAAlJ,IAAA;MAAA,IAAAmJ,OAAA;MACA,KAAA1T,WAAA,CAAAJ,aAAA,GAAA2K,IAAA,CAAAzD,OAAA;MACA,IAAAV,sBAAA,EAAAmE,IAAA,CAAAzD,OAAA,EAAA/B,IAAA,WAAAwB,QAAA;QACAmN,OAAA,CAAA1T,WAAA,CAAAU,YAAA,GAAA6F,QAAA,CAAA7F,YAAA;QACAgT,OAAA,CAAA1T,WAAA,CAAAS,cAAA,GAAA8F,QAAA,CAAA9F,cAAA;QACAiT,OAAA,CAAA1T,WAAA,CAAAW,oBAAA,GAAA4F,QAAA,CAAA5F,oBAAA;QACA+S,OAAA,CAAA1T,WAAA,CAAAY,gBAAA,GAAA2F,QAAA,CAAA3F,gBAAA;QACA8S,OAAA,CAAA1T,WAAA,CAAAa,sBAAA,GAAA0F,QAAA,CAAA1F,sBAAA;QACA6S,OAAA,CAAA1T,WAAA,CAAAc,kBAAA,GAAAyF,QAAA,CAAAzF,kBAAA;QACA4S,OAAA,CAAA9U,eAAA,GAAA2H,QAAA,CAAA3H,eAAA;QACA8U,OAAA,CAAArG,WAAA;MACA;IACA;IACAsG,sBAAA,WAAAA,uBAAApJ,IAAA;MACA,KAAAvJ,IAAA,CAAAnC,UAAA,CAAA2E,IAAA,CAAA+G,IAAA,CAAA9N,OAAA,CAAA6G,SAAA;IACA;IACAsQ,2BAAA,WAAAA,4BAAArJ,IAAA;MACA,KAAAvK,WAAA,CAAAnB,UAAA,CAAA2E,IAAA,CAAA+G,IAAA,CAAA9N,OAAA,CAAA6G,SAAA;MACA,KAAA+J,WAAA;IACA;IACAwG,wBAAA,WAAAA,yBAAAtJ,IAAA;MACA,KAAAvJ,IAAA,CAAAnC,UAAA,QAAAmC,IAAA,CAAAnC,UAAA,CAAAiV,MAAA,WAAApG,IAAA;QACA,OAAAA,IAAA,IAAAnD,IAAA,CAAA9N,OAAA,CAAA6G,SAAA;MACA;IACA;IACAyQ,6BAAA,WAAAA,8BAAAxJ,IAAA;MACA,KAAAvK,WAAA,CAAAnB,UAAA,QAAAmB,WAAA,CAAAnB,UAAA,CAAAiV,MAAA,WAAApG,IAAA;QACA,OAAAA,IAAA,IAAAnD,IAAA,CAAA9N,OAAA,CAAA6G,SAAA;MACA;MACA,KAAA+J,WAAA;IACA;IACA2G,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,KAAApU,YAAA;MACA,KAAAqU,SAAA;QACAD,OAAA,CAAApU,YAAA;MACA;IACA;IACAsU,kBAAA,WAAAA,mBAAA;MACA,KAAAlV,KAAA;IACA;IACAmV,WAAA,WAAAA,YAAAC,IAAA,EAAAC,GAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,qBAAA,EAAAH,IAAA,EAAAC,GAAA,EAAAvP,IAAA,WAAAwB,QAAA;QACAgO,OAAA,CAAA7L,QAAA,CAAAE,OAAA,CAAArC,QAAA,CAAAyB,GAAA;QACAuM,OAAA,CAAAtV,KAAA;QACAsV,OAAA,CAAAzP,OAAA;MACA;IACA;IACA2P,QAAA,WAAAA,SAAA;MAAA,IAAAC,OAAA;MACA,SAAA1T,IAAA,CAAAiL,SAAA;QACA,KAAAjL,IAAA,CAAA2T,aAAA,QAAA3T,IAAA,CAAA2T,aAAA;QACA,KAAA3T,IAAA,CAAA4T,eAAA,QAAA7W,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA;QACA,KAAAtF,IAAA,CAAA6T,iBAAA,QAAA9W,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAA9K,IAAA,CAAAgN,KAAA;QACA,KAAAvH,IAAA,CAAA8T,iBAAA,OAAAC,eAAA,MAAArE,IAAA;QACA,IAAAI,sBAAA,OAAA9P,IAAA,EAAA+D,IAAA,WAAAwB,QAAA;UACAmO,OAAA,CAAA3D,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAAiE,QAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,SAAAlU,IAAA,CAAAiL,SAAA;QACA,KAAAjL,IAAA,CAAAmU,gBAAA,QAAAnU,IAAA,CAAAmU,gBAAA;QACA,KAAAnU,IAAA,CAAAoU,kBAAA,QAAArX,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA;QACA,KAAAtF,IAAA,CAAAqU,oBAAA,QAAAtX,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAA9K,IAAA,CAAAgN,KAAA;QACA,KAAAvH,IAAA,CAAAsU,oBAAA,OAAAP,eAAA,MAAArE,IAAA;QACA,IAAAI,sBAAA,OAAA9P,IAAA,EAAA+D,IAAA,WAAAwB,QAAA;UACA2O,OAAA,CAAAnE,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAAiE,QAAA;MACA;IACA;IACAO,OAAA,WAAAA,QAAA;MAAA,IAAAC,OAAA;MACA,SAAAxU,IAAA,CAAAiL,SAAA;QACA,KAAAjL,IAAA,CAAA0D,YAAA,QAAA1D,IAAA,CAAA0D,YAAA;QACA,KAAA1D,IAAA,CAAA0L,cAAA,QAAA3O,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA;QACA,KAAAtF,IAAA,CAAA2L,gBAAA,QAAA5O,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAA9K,IAAA,CAAAgN,KAAA;QACA,KAAAvH,IAAA,CAAA4L,gBAAA,OAAAmI,eAAA,MAAArE,IAAA;QACA,IAAAI,sBAAA,OAAA9P,IAAA,EAAA+D,IAAA,WAAAwB,QAAA;UACAiP,OAAA,CAAAzE,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAAiE,QAAA;MACA;IACA;IACAS,MAAA,WAAAA,OAAA;MAAA,IAAAC,OAAA;MACA,SAAA1U,IAAA,CAAAiL,SAAA;QACA,KAAAjL,IAAA,CAAA2D,WAAA,QAAA3D,IAAA,CAAA2D,WAAA;QACA,KAAA3D,IAAA,CAAAgM,aAAA,QAAAjP,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAAC,GAAA;QACA,KAAAtF,IAAA,CAAAiM,eAAA,QAAAlP,MAAA,CAAAC,KAAA,CAAAqI,IAAA,CAAA9K,IAAA,CAAAgN,KAAA;QACA,KAAAvH,IAAA,CAAAkM,eAAA,OAAA6H,eAAA,MAAArE,IAAA;QACA,IAAAI,sBAAA,OAAA9P,IAAA,EAAA+D,IAAA,WAAAwB,QAAA;UACAmP,OAAA,CAAA3E,MAAA,CAAAC,UAAA;QACA;MACA;QACA,KAAAD,MAAA,CAAAiE,QAAA;MACA;IACA;IACAW,eAAA,WAAAA,gBAAA7D,GAAA;MACA,KAAA9Q,IAAA,CAAAqN,qBAAA,GAAAyD,GAAA;IACA;IACA8D,cAAA,WAAAA,eAAA9D,GAAA;MACA,KAAA9Q,IAAA,CAAA6U,WAAA,GAAA/D,GAAA;IACA;IACAgE,iBAAA,WAAAA,kBAAAhE,GAAA;MACA,KAAA9Q,IAAA,CAAA/D,cAAA,GAAA6U,GAAA,CAAA7J,QAAA;IACA;IACA6I,aAAA,WAAAA,cAAA9P,IAAA;MAAA,IAAA+U,OAAA;MACA;;MAEA,KAAA/U,IAAA,CAAAmP,WAAA,QAAAnP,IAAA,CAAAmP,WAAA,CAAAE,OAAA;MAEA,SAAArP,IAAA,CAAA4O,kBAAA,SAAA5O,IAAA,CAAA4O,kBAAA,CAAA7M,MAAA;QACA,KAAA/B,IAAA,CAAAC,kBAAA,QAAAD,IAAA,CAAA4O,kBAAA;QACA,KAAA5O,IAAA,CAAAE,gBAAA,QAAAF,IAAA,CAAA4O,kBAAA;MACA;MAEA,IAAAkB,sBAAA,OAAA9P,IAAA,EAAA+D,IAAA,WAAAwB,QAAA;QACAwP,OAAA,CAAAhF,MAAA,CAAAC,UAAA;QACA,IAAA3B,mBAAA,EAAA0G,OAAA,CAAA/U,IAAA,CAAAiL,SAAA,EAAAlH,IAAA,WAAAwB,QAAA;UACA;UACA;UACAwP,OAAA,CAAA/U,IAAA,GAAAuF,QAAA,CAAApJ,IAAA;UACA,IAAA4Y,OAAA,CAAAvX,UAAA,IAAA4D,SAAA;YAAA,IAAA4T,WAAA,OAAAzT,2BAAA,CAAAC,OAAA,EACAuT,OAAA,CAAAvX,UAAA;cAAAyX,OAAA;YAAA;cAAA,KAAAD,WAAA,CAAAtT,CAAA,MAAAuT,OAAA,GAAAD,WAAA,CAAAjU,CAAA,IAAAY,IAAA;gBAAA,IAAAO,CAAA,GAAA+S,OAAA,CAAApT,KAAA;gBACA,IAAAK,CAAA,CAAAJ,QAAA,IAAAV,SAAA;kBAAA,IAAA8T,WAAA,OAAA3T,2BAAA,CAAAC,OAAA,EACAU,CAAA,CAAAJ,QAAA;oBAAAqT,OAAA;kBAAA;oBAAA,KAAAD,WAAA,CAAAxT,CAAA,MAAAyT,OAAA,GAAAD,WAAA,CAAAnU,CAAA,IAAAY,IAAA;sBAAA,IAAAU,CAAA,GAAA8S,OAAA,CAAAtT,KAAA;sBACA,IAAAQ,CAAA,CAAAP,QAAA,IAAAV,SAAA;wBAAA,IAAAgU,WAAA,OAAA7T,2BAAA,CAAAC,OAAA,EACAa,CAAA,CAAAP,QAAA;0BAAAuT,OAAA;wBAAA;0BAAA,KAAAD,WAAA,CAAA1T,CAAA,MAAA2T,OAAA,GAAAD,WAAA,CAAArU,CAAA,IAAAY,IAAA;4BAAA,IAAAmB,CAAA,GAAAuS,OAAA,CAAAxT,KAAA;4BACA,IAAAiB,CAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAsC,QAAA;8BACAsW,OAAA,CAAAtW,QAAA,GAAAqE,CAAA,CAAA+D,MAAA;4BACA;4BACA,IAAA/D,CAAA,CAAAgD,OAAA,IAAAP,QAAA,CAAApJ,IAAA,CAAAuC,QAAA;8BACAqW,OAAA,CAAArW,QAAA,GAAAoE,CAAA,CAAA+D,MAAA;4BACA;0BACA;wBAAA,SAAApE,GAAA;0BAAA2S,WAAA,CAAA1S,CAAA,CAAAD,GAAA;wBAAA;0BAAA2S,WAAA,CAAAzS,CAAA;wBAAA;sBACA;oBACA;kBAAA,SAAAF,GAAA;oBAAAyS,WAAA,CAAAxS,CAAA,CAAAD,GAAA;kBAAA;oBAAAyS,WAAA,CAAAvS,CAAA;kBAAA;gBACA;cACA;YAAA,SAAAF,GAAA;cAAAuS,WAAA,CAAAtS,CAAA,CAAAD,GAAA;YAAA;cAAAuS,WAAA,CAAArS,CAAA;YAAA;UACA;UACAoS,OAAA,CAAA/U,IAAA,CAAAD,OAAA,GAAAwF,QAAA,CAAAxF,OAAA;UACAgV,OAAA,CAAA/U,IAAA,CAAAP,cAAA,GAAA8F,QAAA,CAAA9F,cAAA;UACAsV,OAAA,CAAA/U,IAAA,CAAAN,YAAA,GAAA6F,QAAA,CAAA7F,YAAA;UACAqV,OAAA,CAAA/U,IAAA,CAAAJ,gBAAA,GAAA2F,QAAA,CAAA3F,gBAAA;UACAmV,OAAA,CAAA/U,IAAA,CAAAL,oBAAA,GAAA4F,QAAA,CAAA5F,oBAAA;UACAoV,OAAA,CAAA/U,IAAA,CAAAF,kBAAA,GAAAyF,QAAA,CAAAzF,kBAAA;UACAiV,OAAA,CAAA/U,IAAA,CAAAH,sBAAA,GAAA0F,QAAA,CAAA1F,sBAAA;UACAkV,OAAA,CAAA/U,IAAA,CAAAnC,UAAA,GAAA0H,QAAA,CAAA1H,UAAA;UACAkX,OAAA,CAAA/U,IAAA,CAAA8G,eAAA,GAAAvB,QAAA,CAAAwB,sBAAA;UACAgO,OAAA,CAAAnX,eAAA,GAAA2H,QAAA,CAAA3H,eAAA;UACAmX,OAAA,CAAA7W,WAAA;UACA6W,OAAA,CAAA/W,KAAA;UACA+W,OAAA,CAAA3Y,OAAA;UAEA,IAAAyS,SAAA,OAAAC,IAAA,CAAAC,YAAA;YACAC,KAAA;YACAC,qBAAA;YACAC,qBAAA;UACA;UACA6F,OAAA,CAAA/U,IAAA,CAAAmP,WAAA,GAAA5J,QAAA,CAAApJ,IAAA,CAAAgT,WAAA,CAAAC,cAAA;UACA2F,OAAA,CAAA/U,IAAA,CAAAmP,WAAA,GAAA4F,OAAA,CAAA/U,IAAA,CAAAmP,WAAA,CAAAE,OAAA;UACA0F,OAAA,CAAA/U,IAAA,CAAAmP,WAAA,GAAAN,SAAA,CAAAS,MAAA,CAAAyF,OAAA,CAAA/U,IAAA,CAAAmP,WAAA;;UAEA;UACA4F,OAAA,CAAA/U,IAAA,CAAA4O,kBAAA,IAAArJ,QAAA,CAAApJ,IAAA,CAAA8D,kBAAA,EAAAsF,QAAA,CAAApJ,IAAA,CAAA+D,gBAAA;UACA;AACA;AACA;AACA;QACA;MACA;MACA;IACA;IACAoV,iBAAA,WAAAA,kBAAA;MACA,SAAAtV,IAAA,CAAAmP,WAAA;QACA,IAAAN,SAAA,OAAAC,IAAA,CAAAC,YAAA;UACAC,KAAA;UACAC,qBAAA;UACAC,qBAAA;QACA;QACA,KAAAlP,IAAA,CAAAmP,WAAA,QAAAnP,IAAA,CAAAmP,WAAA,CAAAE,OAAA;QACA,KAAArP,IAAA,CAAAmP,WAAA,GAAAN,SAAA,CAAAS,MAAA,MAAAtP,IAAA,CAAAmP,WAAA;MACA;IACA;IACAoG,wBAAA,WAAAA,yBAAA1T,KAAA;MACA,IAAAgN,SAAA,OAAAC,IAAA,CAAAC,YAAA;QACAyG,QAAA;MACA;MACA,OAAA3G,SAAA,CAAAS,MAAA,CAAAzN,KAAA;IACA;IACA4T,UAAA,WAAAA,WAAA;MACA,KAAAC,YAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAnU,OAAA,GAAAoU,QAAA"}]}