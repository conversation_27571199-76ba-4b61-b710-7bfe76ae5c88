{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\index.vue?vue&type=template&id=0ca12160&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\index.vue", "mtime": 1754876882597}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}