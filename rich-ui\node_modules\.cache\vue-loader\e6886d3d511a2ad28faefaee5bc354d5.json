{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Breadcrumb\\index.vue?vue&type=template&id=b50ef614&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1754876882524}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJlbC1icmVhZGNydW1iIiwKICAgIHsgc3RhdGljQ2xhc3M6ICJhcHAtYnJlYWRjcnVtYiIsIGF0dHJzOiB7IHNlcGFyYXRvcjogIi8iIH0gfSwKICAgIFsKICAgICAgX2MoCiAgICAgICAgInRyYW5zaXRpb24tZ3JvdXAiLAogICAgICAgIHsgYXR0cnM6IHsgbmFtZTogImJyZWFkY3J1bWIiIH0gfSwKICAgICAgICBfdm0uX2woX3ZtLmxldmVsTGlzdCwgZnVuY3Rpb24gKGl0ZW0sIGluZGV4KSB7CiAgICAgICAgICByZXR1cm4gX2MoImVsLWJyZWFkY3J1bWItaXRlbSIsIHsga2V5OiBpdGVtLnBhdGggfSwgWwogICAgICAgICAgICBpdGVtLnJlZGlyZWN0ID09ICJub1JlZGlyZWN0IiB8fCBpbmRleCA9PSBfdm0ubGV2ZWxMaXN0Lmxlbmd0aCAtIDEKICAgICAgICAgICAgICA/IF9jKCJzcGFuIiwgeyBzdGF0aWNDbGFzczogIm5vLXJlZGlyZWN0IiB9LCBbCiAgICAgICAgICAgICAgICAgIF92bS5fdihfdm0uX3MoaXRlbS5tZXRhLnRpdGxlKSksCiAgICAgICAgICAgICAgICBdKQogICAgICAgICAgICAgIDogX2MoCiAgICAgICAgICAgICAgICAgICJhIiwKICAgICAgICAgICAgICAgICAgewogICAgICAgICAgICAgICAgICAgIG9uOiB7CiAgICAgICAgICAgICAgICAgICAgICBjbGljazogZnVuY3Rpb24gKCRldmVudCkgewogICAgICAgICAgICAgICAgICAgICAgICAkZXZlbnQucHJldmVudERlZmF1bHQoKQogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gX3ZtLmhhbmRsZUxpbmsoaXRlbSkKICAgICAgICAgICAgICAgICAgICAgIH0sCiAgICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgfSwKICAgICAgICAgICAgICAgICAgW192bS5fdihfdm0uX3MoaXRlbS5tZXRhLnRpdGxlKSldCiAgICAgICAgICAgICAgICApLAogICAgICAgICAgXSkKICAgICAgICB9KSwKICAgICAgICAxCiAgICAgICksCiAgICBdLAogICAgMQogICkKfQp2YXIgc3RhdGljUmVuZGVyRm5zID0gW10KcmVuZGVyLl93aXRoU3RyaXBwZWQgPSB0cnVlCgpleHBvcnQgeyByZW5kZXIsIHN0YXRpY1JlbmRlckZucyB9"}]}