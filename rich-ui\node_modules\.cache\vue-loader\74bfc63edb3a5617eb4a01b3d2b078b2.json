{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\chargetype\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\chargetype\\index.vue", "mtime": 1754876882576}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRDaGFyZ2VUeXBlLA0KICBjaGFuZ2VTdGF0dXMsDQogIGRlbENoYXJnZVR5cGUsDQogIGdldENoYXJnZVR5cGUsDQogIGxpc3RDaGFyZ2VUeXBlLA0KICB1cGRhdGVDaGFyZ2VUeXBlDQp9IGZyb20gIkAvYXBpL3N5c3RlbS9jaGFyZ2VUeXBlIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiVHlwZSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNob3dMZWZ0OiAzLA0KICAgICAgc2hvd1JpZ2h0OiAyMSwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDotLnnlKjnsbvlnovooajmoLzmlbDmja4NCiAgICAgIHR5cGVMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICBjaGFyZ2VUeXBlUXVlcnk6IG51bGwsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fQ0KICAgICAgLA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczoge30NCiAgICB9DQogICAgICA7DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd1NlYXJjaChuKSB7DQogICAgICBpZiAobiA9PSB0cnVlKSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjENCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjQNCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDANCiAgICAgIH0NCiAgICB9DQogIH0sDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRMaXN0KCk7DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i6LS555So57G75Z6L5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0Q2hhcmdlVHlwZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50eXBlTGlzdCA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAiY2hhcmdlVHlwZUlkIik7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBjaGFyZ2VUeXBlSWQ6IG51bGwsDQogICAgICAgIGNoYXJnZVR5cGVTaG9ydE5hbWU6IG51bGwsDQogICAgICAgIGNoYXJnZVR5cGVFbk5hbWU6IG51bGwsDQogICAgICAgIGNoYXJnZVR5cGVMb2NhbE5hbWU6IG51bGwsDQogICAgICAgIG9yZGVyTnVtOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwNCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmNoYXJnZVR5cGVJZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgaWYgKHJvdyAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgdGhpcy5mb3JtLnBhcmVudElkID0gcm93LmNoYXJnZVR5cGVJZDsNCiAgICAgIH0NCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg6LS555So57G75Z6LIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBjaGFyZ2VUeXBlSWQgPSByb3cuY2hhcmdlVHlwZUlkIHx8IHRoaXMuaWRzDQogICAgICBnZXRDaGFyZ2VUeXBlKGNoYXJnZVR5cGVJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMuZm9ybS5jaGVja0RlcHRJZHMgPSByZXNwb25zZS5jaGVjaw0KICAgICAgICB0aGlzLmZvcm0uZW50ZXJEZXB0SWRzID0gcmVzcG9uc2UuZW50ZXINCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnotLnnlKjnsbvlnosiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uY2hhcmdlVHlwZUlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZUNoYXJnZVR5cGUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRDaGFyZ2VUeXBlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGNoYXJnZVR5cGVJZHMgPSByb3cuY2hhcmdlVHlwZUlkIHx8IHRoaXMuaWRzOw0KICAgICAgdGhpcy4kY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk6LS555So57G75Z6L57yW5Y+35Li6IicgKyBjaGFyZ2VUeXBlSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nLCAn5o+Q56S6Jywge2N1c3RvbUNsYXNzOiAnbW9kYWwtY29uZmlybSd9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbENoYXJnZVR5cGUoY2hhcmdlVHlwZUlkcyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7DQogICAgICBsZXQgdGV4dCA9IHJvdy5zdGF0dXMgPT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cuY2hhcmdlVHlwZUxvY2FsTmFtZSArICci5ZCX77yfJywgJ+aPkOekuicsIHtjdXN0b21DbGFzczogJ21vZGFsLWNvbmZpcm0nfSkudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBjaGFuZ2VTdGF0dXMocm93LmNoYXJnZVR5cGVJZCwgcm93LnN0YXR1cyk7DQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcyh0ZXh0ICsgIuaIkOWKnyIpOw0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgew0KICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PSAiMCIgPyAiMSIgOiAiMCI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdzeXN0ZW0vdHlwZS9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGB0eXBlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgUGFyZW50SWQodmFsKSB7DQogICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSB2YWwNCiAgICB9LA0KICAgIGdldENoZWNrRGVwdElkcyh2YWwpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0uZW50ZXJEZXB0SWRzLmluY2x1ZGVzKHZhbFt2YWwubGVuZ3RoIC0gMV0pKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi5b2V5YWl6YOo6Zeo5bey5a2Y5Zyo5q2k6YOo6Zeo77yM5bey5Y+v5pi+IikNCiAgICAgICAgdGhpcy5mb3JtLmNoZWNrRGVwdElkcyA9IHZhbC5maWx0ZXIodiA9PiB7DQogICAgICAgICAgcmV0dXJuIHYgIT0gdmFsW3ZhbC5sZW5ndGggLSAxXQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5mb3JtLmNoZWNrRGVwdElkcyA9IHZhbA0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0RW50ZXJEZXB0SWRzKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLmVudGVyRGVwdElkcyA9IHZhbA0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/chargetype", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"查询\" prop=\"chargeTypeQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.chargeTypeQuery\"\r\n              clearable\r\n              placeholder=\"中英文，简称\"\r\n              style=\"width: 100%\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <!--          <el-col :span=\"1.5\">-->\r\n          <!--            <el-button-->\r\n          <!--              type=\"primary\"-->\r\n          <!--              plain-->\r\n          <!--              icon=\"el-icon-plus\"-->\r\n          <!--              size=\"mini\"-->\r\n          <!--              @click=\"handleAdd\"-->\r\n          <!--              v-hasPermi=\"['system:chargetype:add']\"-->\r\n          <!--            >新增-->\r\n          <!--            </el-button>-->\r\n          <!--          </el-col>-->\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:chargetype:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"typeList\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n                  row-key=\"chargeTypeId\"\r\n                  @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column key=\"chargeTypeName\" align=\"left\" label=\"费用类型名称\" width=\"350\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.chargeTypeShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.chargeTypeLocalName }}</a>\r\n              {{ scope.row.chargeTypeEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column key=\"enterDepts\" align=\"left\" label=\"录入部门\" prop=\"enterDepts\" show-tooltip-when-overflow\r\n                           width=\"170\"/>\r\n          <el-table-column key=\"checkDepts\" align=\"left\" label=\"可显部门\" prop=\"checkDepts\" show-tooltip-when-overflow\r\n                           width=\"170\"/>\r\n          <el-table-column key=\"remark\" align=\"left\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow/>\r\n          <el-table-column key=\"orderNum\" align=\"center\" label=\"优先级\" prop=\"orderNum\" width=\"58\"/>\r\n          <el-table-column key=\"status\" align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:chargetype:add']\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:chargetype:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.parentId != 0\"\r\n                v-hasPermi=\"['system:chargetype:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改费用类型对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\" :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item v-if=\"form.parentId!=0\" label=\"上级类型\" prop=\"parentId\">\r\n          <tree-select :multiple=\"false\" :pass=\"form.parentId\" :placeholder=\"'上级类名'\" :type=\"'chargeType'\"\r\n                       :dbn=\"false\" @return=\"ParentId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"简称\" prop=\"chargeTypeShortName\">\r\n          <el-input v-model=\"form.chargeTypeShortName\" placeholder=\"简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"chargeTypeEnName\">\r\n          <el-input v-model=\"form.chargeTypeEnName\" placeholder=\"英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"chargeTypeLocalName\">\r\n          <el-input v-model=\"form.chargeTypeLocalName\" placeholder=\"中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"录入部门\" prop=\"enterDeptIds\">\r\n          <tree-select :multiple=\"true\" :pass=\"form.enterDeptIds\" :placeholder=\"'适用的部门'\"\r\n                       :type=\"'dept'\" @return=\"getEnterDeptIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"可显部门\" prop=\"checkDeptIds\">\r\n          <tree-select :multiple=\"true\" :pass=\"form.checkDeptIds\" :placeholder=\"'适用的部门'\"\r\n                       :type=\"'dept'\" @return=\"getCheckDeptIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" style=\"width: 100%\" v-model=\"form.orderNum\" placeholder=\"排序\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 5, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addChargeType,\r\n  changeStatus,\r\n  delChargeType,\r\n  getChargeType,\r\n  listChargeType,\r\n  updateChargeType\r\n} from \"@/api/system/chargeType\";\r\n\r\nexport default {\r\n  name: \"Type\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 费用类型表格数据\r\n      typeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        chargeTypeQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {}\r\n      ,\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n      ;\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询费用类型列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listChargeType(this.queryParams).then(response => {\r\n        this.typeList = this.handleTree(response.data, \"chargeTypeId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        chargeTypeId: null,\r\n        chargeTypeShortName: null,\r\n        chargeTypeEnName: null,\r\n        chargeTypeLocalName: null,\r\n        orderNum: null,\r\n        remark: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.chargeTypeId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.open = true;\r\n      if (row != undefined) {\r\n        this.form.parentId = row.chargeTypeId;\r\n      }\r\n      this.title = \"添加费用类型\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const chargeTypeId = row.chargeTypeId || this.ids\r\n      getChargeType(chargeTypeId).then(response => {\r\n        this.form = response.data;\r\n        this.form.checkDeptIds = response.check\r\n        this.form.enterDeptIds = response.enter\r\n        this.open = true;\r\n        this.title = \"修改费用类型\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.chargeTypeId != null) {\r\n            updateChargeType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addChargeType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const chargeTypeIds = row.chargeTypeId || this.ids;\r\n      this.$confirm('是否确认删除费用类型编号为\"' + chargeTypeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delChargeType(chargeTypeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.chargeTypeLocalName + '\"吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.chargeTypeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/type/export', {\r\n        ...this.queryParams\r\n      }, `type_${new Date().getTime()}.xlsx`)\r\n    },\r\n    ParentId(val) {\r\n      this.form.parentId = val\r\n    },\r\n    getCheckDeptIds(val) {\r\n      if (this.form.enterDeptIds.includes(val[val.length - 1])) {\r\n        this.$message.warning(\"录入部门已存在此部门，已可显\")\r\n        this.form.checkDeptIds = val.filter(v => {\r\n          return v != val[val.length - 1]\r\n        })\r\n      } else {\r\n        this.form.checkDeptIds = val\r\n      }\r\n    },\r\n    getEnterDeptIds(val) {\r\n      this.form.enterDeptIds = val\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}