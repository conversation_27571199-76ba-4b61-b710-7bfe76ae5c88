{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\IconSelect\\index.vue?vue&type=style&index=0&id=6504d548&rel=stylesheet%2Fscss&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\IconSelect\\index.vue", "mtime": 1754876882531}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLmljb24tYm9keSB7DQogIHdpZHRoOiAxMDAlOw0KICBwYWRkaW5nOiAxMHB4Ow0KICAuaWNvbi1zZWFyY2ggew0KICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsNCiAgICBtYXJnaW4tYm90dG9tOiA1cHg7DQogIH0NCiAgLmljb24tbGlzdCB7DQogICAgaGVpZ2h0OiAyMDBweDsNCiAgICA6OnYtZGVlcCAuZWwtc2Nyb2xsYmFyIHsNCiAgICAgIGhlaWdodDogMTAwJTsNCiAgICAgIDo6di1kZWVwLmVsLXNjcm9sbGJhcl9fd3JhcCB7DQogICAgICAgIG92ZXJmbG93LXg6IGhpZGRlbjsNCiAgICAgIH0NCiAgICB9DQogICAgLmxpc3QtY29udGFpbmVyIHsNCiAgICAgIGhlaWdodDogMjAwcHg7DQogICAgICBvdmVyZmxvdzogYXV0bzsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBmbGV4LXdyYXA6IHdyYXA7DQogICAgICAuaWNvbi1pdGVtLXdyYXBwZXIgew0KICAgICAgICB3aWR0aDogY2FsYygxMDAlIC8gMyk7DQogICAgICAgIGhlaWdodDogMzBweDsNCiAgICAgICAgbGluZS1oZWlnaHQ6IDMwcHg7DQogICAgICAgIG1hcmdpbi1ib3R0b206IC01cHg7DQogICAgICAgIGN1cnNvcjogcG9pbnRlcjsNCiAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgLmljb24taXRlbSB7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBtYXgtd2lkdGg6IDEwMCU7DQogICAgICAgICAgaGVpZ2h0OiAxMDAlOw0KICAgICAgICAgIHBhZGRpbmc6IDAgMnB4Ow0KICAgICAgICAgICY6aG92ZXIgew0KICAgICAgICAgICAgYmFja2dyb3VuZDogI2VjZWNlYzsNCiAgICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgICAgICAgICB9DQogICAgICAgICAgLmljb24gew0KICAgICAgICAgICAgZmxleC1zaHJpbms6IDA7DQogICAgICAgICAgfQ0KICAgICAgICAgIHNwYW4gew0KICAgICAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOw0KICAgICAgICAgICAgdmVydGljYWwtYWxpZ246IC0wLjE1ZW07DQogICAgICAgICAgICBmaWxsOiBjdXJyZW50Q29sb3I7DQogICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDJweDsNCiAgICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQogICAgICAgICAgICB0ZXh0LW92ZXJmbG93OiBlbGxpcHNpczsNCiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIC5pY29uLWl0ZW0uYWN0aXZlIHsNCiAgICAgICAgICBiYWNrZ3JvdW5kOiAjZWNlY2VjOw0KICAgICAgICAgIGJvcmRlci1yYWRpdXM6IDVweDsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\r\n<template>\r\n  <div class=\"icon-body\">\r\n    <el-input v-model=\"name\" clearable placeholder=\"图标名称\" style=\"position: relative;\" @clear=\"filterIcons\"\r\n              @input=\"filterIcons\">\r\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\"/>\r\n    </el-input>\r\n    <div class=\"icon-list\">\r\n      <div class=\"list-container\">\r\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\r\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\r\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\r\n            <span>{{ item }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport icons from './requireIcons'\r\n\r\nexport default {\r\n  name: 'IconSelect',\r\n  props: {\r\n    activeIcon: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      name: '',\r\n      iconList: icons\r\n    }\r\n  },\r\n  methods: {\r\n    filterIcons() {\r\n      this.iconList = icons\r\n      if (this.name) {\r\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\r\n      }\r\n    },\r\n    selectedIcon(name) {\r\n      this.$emit('selected', name)\r\n      document.body.click()\r\n    },\r\n    reset() {\r\n      this.name = ''\r\n      this.iconList = icons\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n.icon-body {\r\n  width: 100%;\r\n  padding: 10px;\r\n  .icon-search {\r\n    position: relative;\r\n    margin-bottom: 5px;\r\n  }\r\n  .icon-list {\r\n    height: 200px;\r\n    ::v-deep .el-scrollbar {\r\n      height: 100%;\r\n      ::v-deep.el-scrollbar__wrap {\r\n        overflow-x: hidden;\r\n      }\r\n    }\r\n    .list-container {\r\n      height: 200px;\r\n      overflow: auto;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      .icon-item-wrapper {\r\n        width: calc(100% / 3);\r\n        height: 30px;\r\n        line-height: 30px;\r\n        margin-bottom: -5px;\r\n        cursor: pointer;\r\n        display: flex;\r\n        .icon-item {\r\n          display: flex;\r\n          max-width: 100%;\r\n          height: 100%;\r\n          padding: 0 2px;\r\n          &:hover {\r\n            background: #ececec;\r\n            border-radius: 5px;\r\n          }\r\n          .icon {\r\n            flex-shrink: 0;\r\n          }\r\n          span {\r\n            display: inline-block;\r\n            vertical-align: -0.15em;\r\n            fill: currentColor;\r\n            padding-left: 2px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n          }\r\n        }\r\n        .icon-item.active {\r\n          background: #ececec;\r\n          border-radius: 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}