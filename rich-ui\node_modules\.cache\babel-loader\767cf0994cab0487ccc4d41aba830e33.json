{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\preCarriageNoInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\preCarriageNoInfo.vue", "mtime": 1754876882585}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "watch", "preCarriageNoInfo", "$emit", "openPreCarriageNoInfo", "n", "oopen", "data", "open", "form", "methods", "rowIndex", "_ref", "row", "id", "handleUpdate", "handleDelete", "filter", "item", "submitForm", "reset", "push", "soNo", "preCarriageDriverName", "preCarriageDriverTel", "preCarriageTruckNo", "preCarriageTruckRemark", "preCarriage<PERSON><PERSON><PERSON>", "preCarriageTime", "containerNo", "containerType", "sealNo", "weightPaper", "resetForm", "cancel", "exports", "default", "_default"], "sources": ["src/views/system/document/preCarriageNoInfo.vue"], "sourcesContent": ["<template>\r\n  <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n             :visible.sync=\"oopen\" append-to-body width=\"1200px\">\r\n    <div style=\"display: flex\">\r\n      <h2 style=\"font-weight: bold ;margin:10px;\">新增编号信息</h2>\r\n      <div style=\"vertical-align: middle;line-height: 41px\">\r\n        <el-button type=\"primary\" @click=\"open=true\">新增</el-button>\r\n      </div>\r\n    </div>\r\n    <el-table border :data=\"preCarriageNoInfo\">\r\n      <el-table-column label=\"SO号码\" prop=\"soNo\"></el-table-column>\r\n      <el-table-column label=\"司机姓名\" prop=\"preCarriageDriverName\"></el-table-column>\r\n      <el-table-column label=\"司机电话\" prop=\"preCarriageDriverTel\"></el-table-column>\r\n      <el-table-column label=\"司机车牌\" prop=\"preCarriageTruckNo\"></el-table-column>\r\n      <el-table-column label=\"司机备注\" prop=\"preCarriageTruckRemark\"></el-table-column>\r\n      <el-table-column label=\"装柜地址\" prop=\"preCarriageAddress\"></el-table-column>\r\n      <el-table-column label=\"到场时间\" prop=\"preCarriageTime\"></el-table-column>\r\n      <el-table-column label=\"柜号\" prop=\"containerNo\"></el-table-column>\r\n      <el-table-column label=\"柜型\" prop=\"containerType\"></el-table-column>\r\n      <el-table-column label=\"封条\" prop=\"sealNo\"></el-table-column>\r\n      <el-table-column label=\"磅单\" prop=\"weightPaper\"></el-table-column>\r\n      <el-table-column header-align=\"center\" align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\"\r\n                       width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              style=\"margin-right: -8px\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate(scope.row)\"\r\n          >修改\r\n          </el-button>\r\n          <el-button\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              style=\"margin-right: -8px\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete(scope.row)\"\r\n          >删除\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n               :visible.sync=\"open\" append-to-body width=\"500px\" title=\"新增编号信息\">\r\n      <el-form border :data=\"form\" label-width=\"105px\">\r\n        <el-form-item label=\"SO号码\" prop=\"soNo\">\r\n          <el-input v-model=\"form.soNo\" placeholder=\"SO号码\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机姓名\" prop=\"preCarriageDriverName\">\r\n          <el-input v-model=\"form.preCarriageDriverName\" placeholder=\"司机姓名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机电话\" prop=\"preCarriageDriverTel\">\r\n          <el-input v-model=\"form.preCarriageDriverTel\" placeholder=\"司机电话\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机车牌\" prop=\"preCarriageTruckNo\">\r\n          <el-input v-model=\"form.preCarriageTruckNo\" placeholder=\"司机车牌\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"司机备注\" prop=\"preCarriageTruckRemark\">\r\n          <el-input v-model=\"form.preCarriageTruckRemark\" placeholder=\"司机备注\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"装柜地址\" prop=\"preCarriageAddress\">\r\n          <el-input v-model=\"form.preCarriageAddress\" placeholder=\"装柜地址\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"到场时间\" prop=\"preCarriageTime\">\r\n          <el-input v-model=\"form.preCarriageTime\" placeholder=\"到场时间\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n          <el-input v-model=\"form.containerNo\" placeholder=\"柜号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"柜型\" prop=\"containerType\">\r\n          <el-input v-model=\"form.containerType\" placeholder=\"柜型\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"封条\" prop=\"sealNo\">\r\n          <el-input v-model=\"form.sealNo\" placeholder=\"封条\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"磅单\" prop=\"weightPaper\">\r\n          <el-input v-model=\"form.weightPaper\" placeholder=\"磅单\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: 'PreCarriageNoInfo',\r\n  props: ['openPreCarriageNoInfo'],\r\n  watch: {\r\n    preCarriageNoInfo() {\r\n      this.$emit('return', this.preCarriageNoInfo)\r\n    },\r\n    openPreCarriageNoInfo(n) {\r\n      this.oopen = n\r\n    },\r\n    oopen(n) {\r\n      if (n == false) {\r\n        this.$emit('close')\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      open: false,\r\n      oopen: false,\r\n      preCarriageNoInfo: [],\r\n      form: {},\r\n    }\r\n  },\r\n  methods: {\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1;\r\n    },\r\n    handleUpdate(row) {\r\n      this.form = row\r\n      this.open = true\r\n    },\r\n    handleDelete(row) {\r\n      this.preCarriageNoInfo = this.preCarriageNoInfo.filter(item => {\r\n        return item.id != row.id\r\n      })\r\n    },\r\n    submitForm() {\r\n      if (this.form.id != null) {\r\n        this.reset()\r\n        this.open = false\r\n      } else {\r\n        this.preCarriageNoInfo.push(this.form)\r\n        this.reset()\r\n        this.open = false\r\n      }\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        soNo: null,\r\n        preCarriageDriverName: null,\r\n        preCarriageDriverTel: null,\r\n        preCarriageTruckNo: null,\r\n        preCarriageTruckRemark: null,\r\n        preCarriageAddress: null,\r\n        preCarriageTime: null,\r\n        containerNo: null,\r\n        containerType: null,\r\n        sealNo: null,\r\n        weightPaper: null,\r\n      }\r\n      this.resetForm(\"form\");\r\n    },\r\n    cancel() {\r\n      this.open = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAwFA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,iBAAA,WAAAA,kBAAA;MACA,KAAAC,KAAA,gBAAAD,iBAAA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,CAAA;MACA,KAAAC,KAAA,GAAAD,CAAA;IACA;IACAC,KAAA,WAAAA,MAAAD,CAAA;MACA,IAAAA,CAAA;QACA,KAAAF,KAAA;MACA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAF,KAAA;MACAJ,iBAAA;MACAO,IAAA;IACA;EACA;EACAC,OAAA;IACA,SACAC,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAF,QAAA,GAAAC,IAAA,CAAAD,QAAA;MACAE,GAAA,CAAAC,EAAA,GAAAH,QAAA;IACA;IACAI,YAAA,WAAAA,aAAAF,GAAA;MACA,KAAAJ,IAAA,GAAAI,GAAA;MACA,KAAAL,IAAA;IACA;IACAQ,YAAA,WAAAA,aAAAH,GAAA;MACA,KAAAX,iBAAA,QAAAA,iBAAA,CAAAe,MAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,CAAAJ,EAAA,IAAAD,GAAA,CAAAC,EAAA;MACA;IACA;IACAK,UAAA,WAAAA,WAAA;MACA,SAAAV,IAAA,CAAAK,EAAA;QACA,KAAAM,KAAA;QACA,KAAAZ,IAAA;MACA;QACA,KAAAN,iBAAA,CAAAmB,IAAA,MAAAZ,IAAA;QACA,KAAAW,KAAA;QACA,KAAAZ,IAAA;MACA;IACA;IACAY,KAAA,WAAAA,MAAA;MACA,KAAAX,IAAA;QACAK,EAAA;QACAQ,IAAA;QACAC,qBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,kBAAA;QACAC,eAAA;QACAC,WAAA;QACAC,aAAA;QACAC,MAAA;QACAC,WAAA;MACA;MACA,KAAAC,SAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA1B,IAAA;IACA;EACA;AACA;AAAA2B,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}