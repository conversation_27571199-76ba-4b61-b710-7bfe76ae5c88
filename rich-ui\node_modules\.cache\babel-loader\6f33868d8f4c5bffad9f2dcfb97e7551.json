{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\IconsDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\IconsDialog.vue", "mtime": 1754876882603}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5Lm1hcC5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnZhciBfaWNvbiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9nZW5lcmF0b3IvaWNvbi5qc29uIikpOwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwovLwoKdmFyIG9yaWdpbkxpc3QgPSBfaWNvbi5kZWZhdWx0Lm1hcChmdW5jdGlvbiAobmFtZSkgewogIHJldHVybiAiZWwtaWNvbi0iLmNvbmNhdChuYW1lKTsKfSk7CnZhciBfZGVmYXVsdCA9IHsKICBpbmhlcml0QXR0cnM6IGZhbHNlLAogIHByb3BzOiBbJ2N1cnJlbnQnXSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgaWNvbkxpc3Q6IG9yaWdpbkxpc3QsCiAgICAgIGFjdGl2ZTogbnVsbCwKICAgICAga2V5OiAnJwogICAgfTsKICB9LAogIHdhdGNoOiB7CiAgICBrZXk6IGZ1bmN0aW9uIGtleSh2YWwpIHsKICAgICAgaWYgKHZhbCkgewogICAgICAgIHRoaXMuaWNvbkxpc3QgPSBvcmlnaW5MaXN0LmZpbHRlcihmdW5jdGlvbiAobmFtZSkgewogICAgICAgICAgcmV0dXJuIG5hbWUuaW5kZXhPZih2YWwpID4gLTE7CiAgICAgICAgfSk7CiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5pY29uTGlzdCA9IG9yaWdpbkxpc3Q7CiAgICAgIH0KICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIG9uT3BlbjogZnVuY3Rpb24gb25PcGVuKCkgewogICAgICB0aGlzLmFjdGl2ZSA9IHRoaXMuY3VycmVudDsKICAgICAgdGhpcy5rZXkgPSAnJzsKICAgIH0sCiAgICBvbkNsb3NlOiBmdW5jdGlvbiBvbkNsb3NlKCkge30sCiAgICBvblNlbGVjdDogZnVuY3Rpb24gb25TZWxlY3QoaWNvbikgewogICAgICB0aGlzLmFjdGl2ZSA9IGljb247CiAgICAgIHRoaXMuJGVtaXQoJ3NlbGVjdCcsIGljb24pOwogICAgICB0aGlzLiRlbWl0KCd1cGRhdGU6dmlzaWJsZScsIGZhbHNlKTsKICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "names": ["_icon", "_interopRequireDefault", "require", "originList", "iconList", "map", "name", "concat", "_default", "inheritAttrs", "props", "data", "active", "key", "watch", "val", "filter", "indexOf", "methods", "onOpen", "current", "onClose", "onSelect", "icon", "$emit", "exports", "default"], "sources": ["src/views/tool/build/IconsDialog.vue"], "sourcesContent": ["<template>\r\n  <div class=\"icon-dialog\">\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\"\r\n      v-bind=\"$attrs\"\r\n      width=\"980px\"\r\n      @close=\"onClose\"\r\n      @open=\"onOpen\"\r\n      v-on=\"$listeners\"\r\n    >\r\n      <div slot=\"title\">\r\n        选择图标\r\n        <el-input\r\n          v-model=\"key\"\r\n          :style=\"{width: '260px'}\"\r\n          clearable\r\n          placeholder=\"图标名称\"\r\n          prefix-icon=\"el-icon-search\"\r\n          size=\"mini\"\r\n        />\r\n      </div>\r\n      <ul class=\"icon-ul\">\r\n        <li\r\n          v-for=\"icon in iconList\"\r\n          :key=\"icon\"\r\n          :class=\"active==icon?'active-item':''\"\r\n          @click=\"onSelect(icon)\"\r\n        >\r\n          <i :class=\"icon\"/>\r\n          <div>{{ icon }}</div>\r\n        </li>\r\n      </ul>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport iconList from '@/utils/generator/icon.json'\r\n\r\nconst originList = iconList.map(name => `el-icon-${name}`)\r\n\r\nexport default {\r\n  inheritAttrs: false,\r\n  props: ['current'],\r\n  data() {\r\n    return {\r\n      iconList: originList,\r\n      active: null,\r\n      key: ''\r\n    }\r\n  },\r\n  watch: {\r\n    key(val) {\r\n      if (val) {\r\n        this.iconList = originList.filter(name => name.indexOf(val) > -1)\r\n      } else {\r\n        this.iconList = originList\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    onOpen() {\r\n      this.active = this.current\r\n      this.key = ''\r\n    },\r\n    onClose() {\r\n    },\r\n    onSelect(icon) {\r\n      this.active = icon\r\n      this.$emit('select', icon)\r\n      this.$emit('update:visible', false)\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.icon-ul {\r\n  margin: 0;\r\n  padding: 0;\r\n  font-size: 0;\r\n\r\n  li {\r\n    list-style-type: none;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    display: inline-block;\r\n    width: 16.66%;\r\n    box-sizing: border-box;\r\n    height: 108px;\r\n    padding: 15px 6px 6px 6px;\r\n    cursor: pointer;\r\n    overflow: hidden;\r\n\r\n    &:hover {\r\n      background: #f2f2f2;\r\n    }\r\n\r\n    &.active-item {\r\n      background: #e1f3fb;\r\n      color: #7a6df0\r\n    }\r\n\r\n    > i {\r\n      font-size: 30px;\r\n      line-height: 50px;\r\n    }\r\n  }\r\n}\r\n\r\n.icon-dialog {\r\n  ::v-deep .el-dialog {\r\n    border-radius: 8px;\r\n    margin-bottom: 0;\r\n    margin-top: 4vh !important;\r\n    display: flex;\r\n    flex-direction: column;\r\n    max-height: 92vh;\r\n    overflow: hidden;\r\n    box-sizing: border-box;\r\n\r\n    .el-dialog__header {\r\n      padding-top: 14px;\r\n    }\r\n\r\n    .el-dialog__body {\r\n      margin: 0 20px 20px 20px;\r\n      padding: 0;\r\n      overflow: auto;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;AAqCA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAC,UAAA,GAAAC,aAAA,CAAAC,GAAA,WAAAC,IAAA;EAAA,kBAAAC,MAAA,CAAAD,IAAA;AAAA;AAAA,IAAAE,QAAA,GAEA;EACAC,YAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAP,QAAA,EAAAD,UAAA;MACAS,MAAA;MACAC,GAAA;IACA;EACA;EACAC,KAAA;IACAD,GAAA,WAAAA,IAAAE,GAAA;MACA,IAAAA,GAAA;QACA,KAAAX,QAAA,GAAAD,UAAA,CAAAa,MAAA,WAAAV,IAAA;UAAA,OAAAA,IAAA,CAAAW,OAAA,CAAAF,GAAA;QAAA;MACA;QACA,KAAAX,QAAA,GAAAD,UAAA;MACA;IACA;EACA;EACAe,OAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAP,MAAA,QAAAQ,OAAA;MACA,KAAAP,GAAA;IACA;IACAQ,OAAA,WAAAA,QAAA,GACA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACA,KAAAX,MAAA,GAAAW,IAAA;MACA,KAAAC,KAAA,WAAAD,IAAA;MACA,KAAAC,KAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAlB,QAAA"}]}