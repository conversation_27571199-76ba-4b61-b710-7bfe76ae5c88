{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\insurance\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\insurance\\index.vue", "mtime": 1754876882590}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQppbXBvcnQgZnJlaWdodCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9mcmVpZ2h0IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiSW5zdXJhbmNlIiwNCiAgY29tcG9uZW50czoge2ZyZWlnaHR9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0eXBlSWQ6JzcnDQogICAgfTsNCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/insurance", "sourcesContent": ["<template>\r\n  <freight :typeId=\"typeId\"/>\r\n</template>\r\n\r\n<script>\r\nimport freight from \"@/views/system/freight\";\r\n\r\nexport default {\r\n  name: \"Insurance\",\r\n  components: {freight},\r\n  data() {\r\n    return {\r\n      typeId:'7'\r\n    };\r\n  },\r\n}\r\n</script>\r\n"]}]}