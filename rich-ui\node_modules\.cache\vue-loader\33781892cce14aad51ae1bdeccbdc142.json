{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\year.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\year.vue", "mtime": 1754876882528}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["year.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "year.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n  <el-form size=\"mini\">\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"1\">\r\n        不填，允许的通配符[, - * /]\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"2\">\r\n        每年\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"3\">\r\n        周期从\r\n        <el-input-number v-model='cycle01' :max=\"2098\" :min='fullYear'/>\r\n        -\r\n        <el-input-number v-model='cycle02' :max=\"2099\" :min=\"cycle01 ? cycle01 + 1 : fullYear + 1\"/>\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"4\">\r\n        从\r\n        <el-input-number v-model='average01' :max=\"2098\" :min='fullYear'/>\r\n        年开始，每\r\n        <el-input-number v-model='average02' :max=\"2099 - average01 || fullYear\" :min=\"1\"/>\r\n        年执行一次\r\n      </el-radio>\r\n\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"5\">\r\n        指定\r\n        <el-select v-model=\"checkboxList\" clearable multiple placeholder=\"可多选\">\r\n          <el-option v-for=\"item in 9\" :key=\"item\" :label=\"item -1 + fullYear\" :value=\"item - 1 + fullYear\"/>\r\n        </el-select>\r\n      </el-radio>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      fullYear: 0,\r\n      radioValue: 1,\r\n      cycle01: 0,\r\n      cycle02: 0,\r\n      average01: 0,\r\n      average02: 1,\r\n      checkboxList: [],\r\n      checkNum: this.$options.propsData.check\r\n    }\r\n  },\r\n  name: 'crontab-year',\r\n  props: ['check', 'month', 'cron'],\r\n  methods: {\r\n    // 单选按钮值变化时\r\n    radioChange() {\r\n      switch (this.radioValue) {\r\n        case 1:\r\n          this.$emit('update', 'year', '');\r\n          break;\r\n        case 2:\r\n          this.$emit('update', 'year', '*');\r\n          break;\r\n        case 3:\r\n          this.$emit('update', 'year', this.cycleTotal);\r\n          break;\r\n        case 4:\r\n          this.$emit('update', 'year', this.averageTotal);\r\n          break;\r\n        case 5:\r\n          this.$emit('update', 'year', this.checkboxString);\r\n          break;\r\n      }\r\n    },\r\n    // 周期两个值变化时\r\n    cycleChange() {\r\n      if (this.radioValue == '3') {\r\n        this.$emit('update', 'year', this.cycleTotal);\r\n      }\r\n    },\r\n    // 平均两个值变化时\r\n    averageChange() {\r\n      if (this.radioValue == '4') {\r\n        this.$emit('update', 'year', this.averageTotal);\r\n      }\r\n    },\r\n    // checkbox值变化时\r\n    checkboxChange() {\r\n      if (this.radioValue == '5') {\r\n        this.$emit('update', 'year', this.checkboxString);\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    'radioValue': 'radioChange',\r\n    'cycleTotal': 'cycleChange',\r\n    'averageTotal': 'averageChange',\r\n    'checkboxString': 'checkboxChange'\r\n  },\r\n  computed: {\r\n    // 计算两个周期值\r\n    cycleTotal: function () {\r\n      const cycle01 = this.checkNum(this.cycle01, this.fullYear, 2098)\r\n      const cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : this.fullYear + 1, 2099)\r\n      return cycle01 + '-' + cycle02;\r\n    },\r\n    // 计算平均用到的值\r\n    averageTotal: function () {\r\n      const average01 = this.checkNum(this.average01, this.fullYear, 2098)\r\n      const average02 = this.checkNum(this.average02, 1, 2099 - average01 || this.fullYear)\r\n      return average01 + '/' + average02;\r\n    },\r\n    // 计算勾选的checkbox值合集\r\n    checkboxString: function () {\r\n      let str = this.checkboxList.join();\r\n      return str;\r\n    }\r\n  },\r\n  mounted: function () {\r\n    // 仅获取当前年份\r\n    this.fullYear = Number(new Date().getFullYear());\r\n    this.cycle01 = this.fullYear\r\n    this.average01 = this.fullYear\r\n  }\r\n}\r\n</script>\r\n"]}]}