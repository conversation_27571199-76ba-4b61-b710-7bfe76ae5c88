package com.rich.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 拼柜海运服务对象 rs_op_seal_lcl
 *
 * <AUTHOR>
 * @date 2024-02-06
 */
public class RsOpSeaLcl extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long seaId;

    /**
     *
     */
    @Excel(name = "")
    private Long rctId;

    /** 所属服务实例id , */
    @Excel(name = "所属服务实例id ,")
    private Long serviceId;

    /** 所属服务类型id , */
    @Excel(name = "所属服务类型id ,")
    private Long sqdServiceTypeId;

    /** 所属操作单号 , */
    @Excel(name = "所属操作单号 ,")
    private String sqdRctNo;

    /**
     * 服务细目code
     */
    @Excel(name = "服务细目code ")
    private String sqdServiceDetailsCode;

    /** 商务单号 */
    @Excel(name = "商务单号")
    private String sqdPsaNo;

    /** SO号码 */
    @Excel(name = "SO号码")
    private String soNo;

    /** 提单号码 */
    @Excel(name = "提单号码")
    private String blNo;

    /** 柜号概览 */
    @Excel(name = "柜号概览")
    private String sqdContainersSealsSum;

    /** 船公司 */
    @Excel(name = "船公司")
    private Long carrierId;

    /** 头程船名 */
    @Excel(name = "头程船名")
    private String firstVessel;

    /** 头程航次 */
    @Excel(name = "头程航次")
    private String firstVoyage;

    /** 船期 */
    @Excel(name = "船期")
    private String inquiryScheduleSummary;

    /** 头程开船 */
    @Excel(name = "头程开船")
    private String firstCyOpenTime;

    /** 头程截重 */
    @Excel(name = "头程截重")
    private String firstCyClosingTime;

    /** 截关时间 */
    @Excel(name = "截关时间")
    private String cvClosingTime;

    /**
     * ETD
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ETD", width = 30, dateFormat = "yyyy-MM-dd")
    private Date etd;

    /**
     * ETA
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ETA", width = 30, dateFormat = "yyyy-MM-dd")
    private Date eta;

    /** 截补料 */
    @Excel(name = "截补料")
    private String siClosingTime;

    /** 截VGM */
    @Excel(name = "截VGM")
    private String sqdVgmStatus;

    /** AMS/ENS */
    @Excel(name = "AMS/ENS")
    private String sqdAmsEnsPostStatus;

    /**
     * ATD
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ATD", width = 30, dateFormat = "yyyy-MM-dd")
    private Date podEta;

    /**
     * ATA
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "ATA", width = 30, dateFormat = "yyyy-MM-dd")
    private Date destinationPortEta;

    /**
     * 订舱费用备注
     */
    @Excel(name = "订舱费用备注")
    private String bookingChargeRemark;

    /**
     * 订舱备注
     */
    @Excel(name = "订舱备注")
    private String bookingAgentRemark;

    /**
     * 应付
     */
    @Excel(name = "应付")
    private BigDecimal payable;

    /**
     * 商务单号
     */
    @Excel(name = "商务单号")
    private String psaNo;

    /**
     *
     */
    @Excel(name = "")
    private String rctNo;

    /**
     * 收款抬头
     */
    @Excel(name = "收款抬头")
    private String paymentTitleCode;

    /**
     * 进出口类型
     */
    @Excel(name = "进出口类型")
    private String impExpType;

    /**
     * 贸易条款
     */
    @Excel(name = "贸易条款")
    private String tradingTerms;

    /**
     * 运输条款
     */
    @Excel(name = "运输条款")
    private String logisticsTerms;

    /**
     * 收汇方式
     */
    @Excel(name = "收汇方式")
    private String tradingPaymentChannel;

    /**
     * 货名概要
     */
    @Excel(name = "货名概要")
    private String goodsNameSummary;

    /** 件数 */
    @Excel(name = "件数")
    private Long packageQuantity;

    /**
     * 体积
     */
    @Excel(name = "体积")
    private BigDecimal goodsVolume;

    /** 毛重 */
    @Excel(name = "毛重")
    private BigDecimal grossWeight;

    /** 货值币种 */
    @Excel(name = "货值币种")
    private String goodsCurrencyCode;

    /**
     * 货值
     */
    @Excel(name = "货值")
    private BigDecimal goodsValue;

    /**
     * 物流类型
     */
    @Excel(name = "物流类型")
    private Long logisticsTypeId;

    /**
     * 计费货量
     */
    @Excel(name = "计费货量")
    private String revenueTon;

    /**
     * 启运港
     */
    @Excel(name = "启运港")
    private Long polId;

    /**
     * 中转港
     */
    @Excel(name = "中转港")
    private Long transitPortId;

    /**
     * 卸货港
     */
    @Excel(name = "卸货港")
    private Long podId;

    /**
     * 目的港
     */
    @Excel(name = "目的港")
    private Long destinationPortId;

    /**
     * 头程装船
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "头程装船", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstEtd;

    /**
     * 基港装船
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "基港装船", width = 30, dateFormat = "yyyy-MM-dd")
    private Date basicEtd;

    /**
     * 服务类型List
     */
    @Excel(name = "服务类型List")
    private String serviceTypeIdList;

    /**
     * 报价单号
     */
    @Excel(name = "报价单号")
    private String qoutationNo;

    /**
     * 业务报价综述
     */
    @Excel(name = "业务报价综述")
    private String qoutationSketch;

    /** 业务员 */
    @Excel(name = "业务员")
    private Long salesId;

    /**
     * 报价日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "报价日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date qoutationTime;

    /**
     * 订舱单号
     */
    @Excel(name = "订舱单号")
    private String newBookingNo;

    /**
     * 业务订舱备注
     */
    @Excel(name = "业务订舱备注")
    private String newBookingRemark;

    /**
     * 业务助理
     */
    @Excel(name = "业务助理")
    private Long salesAssistantId;

    /**
     * 协助业务员
     */
    @Excel(name = "协助业务员")
    private Long salesObserverId;

    /**
     * 订舱日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "订舱日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date newBookingTime;

    /**
     * 业务须知汇总
     */
    @Excel(name = "业务须知汇总")
    private String inquiryNoticeSum;

    /**
     * 商务备注汇总
     */
    @Excel(name = "商务备注汇总")
    private String inquiryInnerRemarkSum;

    /**
     * ISF/EMNF
     */
    @Excel(name = "ISF/EMNF")
    private String sqdIsfEmnfPostStatus;

    /**
     * 交单方式
     */
    @Excel(name = "交单方式")
    private String sqdDocDeliveryWay;

    /**
     * 发货人
     */
    @Excel(name = "发货人")
    private String bookingShipper;

    /**
     * 收货人
     */
    @Excel(name = "收货人")
    private String bookingConsignee;

    /**
     * 通知人
     */
    @Excel(name = "通知人")
    private String bookingNotifyParty;

    /**
     * 不可中转
     */
    @Excel(name = "不可中转")
    private String noTransferAllowed;

    /**
     * 不可分批
     */
    @Excel(name = "不可分批")
    private String noDividedAllowed;

    /** 不可套约 */
    @Excel(name = "不可套约")
    private String noAgreementShowed;

    /** 属地清关 */
    @Excel(name = "属地清关")
    private String isCustomsIntransitShowed;

    /**
     * 实际装船日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际装船日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date firstAtd;

    /**
     * 实际到达日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际到达日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date destinationPortAta;

    /**
     * 唛头
     */
    @Excel(name = "唛头")
    private String shippingMark;

    /**
     * 运费付于
     */
    @Excel(name = "运费付于")
    private String freightPaidWayCode;

    /**
     * 箱型特征
     */
    @Excel(name = "箱型特征")
    private String ctnrTypeCode;

    /**
     *
     */
    @Excel(name = "")
    private Long psaId;

    /**
     *
     */
    @Excel(name = "")
    private Long opId;

    /**
     *
     */
    @Excel(name = "")
    private Long supplierId;

    /**
     * 询价单号
     */
    @Excel(name = "询价单号")
    private String inquiryNo;

    /**
     *
     */
    @Excel(name = "")
    private String bookingRemark;

    /**
     * 合约号
     */
    @Excel(name = "合约号")
    private String clientContractNo;

    /**
     * 出单方式
     */
    @Excel(name = "出单方式")
    private String sqdIssueType;

    /**
     * 提单类型
     */
    @Excel(name = "提单类型")
    private String blTypeCode;

    /**
     * 分配状态
     */
    @Excel(name = "分配状态")
    private String distributionStatus;

    /**
     * 主费用币种
     */
    @Excel(name = "主费用币种")
    private String mainChargeCurrencyCode;

    /**
     * 主费用单价
     */
    @Excel(name = "主费用单价")
    private BigDecimal mainChargeRate;

    /**
     * 主费用单位
     */
    @Excel(name = "主费用单位")
    private String mainChargeUnitCode;

    /**
     * 订舱人id
     */
    @Excel(name = "订舱人id")
    private Long bookingId;

    /**
     * 订舱时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "订舱时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date bookingTime;

    /**
     * 订舱状态
     */
    @Excel(name = "订舱状态")
    private String bookingStatus;

    /**
     * 客户简称
     */
    @Excel(name = "客户简称")
    private String clientShortName;

    /**
     * 货物特征
     */
    @Excel(name = "货物特征")
    private String cargoTypeCodeSum;

    /**
     *
     */
    @Excel(name = "")
    private String agreementTypeCode;

    /**
     * 结算价
     */
    @Excel(name = "结算价")
    private BigDecimal settledRate;

    /**
     * 采购成本
     */
    @Excel(name = "采购成本")
    private BigDecimal purchaseCost;

    /**
     * 平衡利润
     */
    @Excel(name = "平衡利润")
    private BigDecimal balanceProfit;

    /**
     *
     */
    @Excel(name = "")
    private BigDecimal mainChargeRateA;

    /**
     *
     */
    @Excel(name = "")
    private BigDecimal mainChargeRateB;

    /**
     *
     */
    @Excel(name = "")
    private BigDecimal mainChargeRateC;

    /**
     *
     */
    @Excel(name = "")
    private BigDecimal settledRateA;

    /**  */
    @Excel(name = "")
    private BigDecimal settledRateB;

    /**  */
    @Excel(name = "")
    private BigDecimal settledRateC;

    /**  */
    @Excel(name = "")
    private BigDecimal balanceProfitA;

    /**  */
    @Excel(name = "")
    private BigDecimal balanceProfitB;

    /**
     *
     */
    @Excel(name = "")
    private BigDecimal balanceProfitC;

    private RsServiceInstances rsServiceInstances;

    private List<RsCharge> rsChargeList;

    private List<RsDoc> rsDocList;

    private List<RsOpLog> rsOpLogList;
    private BigDecimal payableRMB;
    private BigDecimal payableUSD;
    private BigDecimal payableUSDTax;
    private BigDecimal payableRMBTax;

    private List<RsDebitNote> rsDebitNoteList;

    public List<RsDebitNote> getRsDebitNoteList() {
        return rsDebitNoteList;
    }

    public void setRsDebitNoteList(List<RsDebitNote> rsDebitNoteList) {
        this.rsDebitNoteList = rsDebitNoteList;
    }

    public BigDecimal getPayableRMBTax() {
        return payableRMBTax;
    }

    public void setPayableRMBTax(BigDecimal payableRMBTax) {
        this.payableRMBTax = payableRMBTax;
    }

    public BigDecimal getPayableUSDTax() {
        return payableUSDTax;
    }

    public void setPayableUSDTax(BigDecimal payableUSDTax) {
        this.payableUSDTax = payableUSDTax;
    }

    public BigDecimal getPayableUSD() {
        return payableUSD;
    }

    public void setPayableUSD(BigDecimal payableUSD) {
        this.payableUSD = payableUSD;
    }

    public BigDecimal getPayableRMB() {
        return payableRMB;
    }

    public void setPayableRMB(BigDecimal payableRMB) {
        this.payableRMB = payableRMB;
    }

    public RsServiceInstances getRsServiceInstances() {
        return rsServiceInstances;
    }

    public void setRsServiceInstances(RsServiceInstances rsServiceInstances) {
        this.rsServiceInstances = rsServiceInstances;
    }

    public List<RsCharge> getRsChargeList() {
        return rsChargeList;
    }

    public void setRsChargeList(List<RsCharge> rsChargeList) {
        this.rsChargeList = rsChargeList;
    }

    public List<RsDoc> getRsDocList() {
        return rsDocList;
    }

    public void setRsDocList(List<RsDoc> rsDocList) {
        this.rsDocList = rsDocList;
    }

    public List<RsOpLog> getRsOpLogList() {
        return rsOpLogList;
    }

    public void setRsOpLogList(List<RsOpLog> rsOpLogList) {
        this.rsOpLogList = rsOpLogList;
    }

    public void setSeaId(Long seaId) {
        this.seaId = seaId;
    }

    public Long getSeaId() {
        return seaId;
    }

    public Long getRctId() {
        return rctId;
    }

    public void setRctId(Long rctId) {
        this.rctId = rctId;
    }

    public void setServiceId(Long serviceId) {
        this.serviceId = serviceId;
    }

    public Long getServiceId() {
        return serviceId;
    }

    public void setSqdServiceTypeId(Long sqdServiceTypeId) {
        this.sqdServiceTypeId = sqdServiceTypeId;
    }

    public Long getSqdServiceTypeId() {
        return sqdServiceTypeId;
    }

    public void setSqdRctNo(String sqdRctNo) {
        this.sqdRctNo = sqdRctNo;
    }

    public String getSqdRctNo() {
        return sqdRctNo;
    }

    public String getSqdServiceDetailsCode() {
        return sqdServiceDetailsCode;
    }

    public void setSqdServiceDetailsCode(String sqdServiceDetailsCode) {
        this.sqdServiceDetailsCode = sqdServiceDetailsCode;
    }

    public String getSqdPsaNo() {
        return sqdPsaNo;
    }

    public void setSqdPsaNo(String sqdPsaNo) {
        this.sqdPsaNo = sqdPsaNo;
    }

    public String getSoNo() {
        return soNo;
    }

    public void setSoNo(String soNo) {
        this.soNo = soNo;
    }

    public String getBlNo() {
        return blNo;
    }

    public void setBlNo(String blNo) {
        this.blNo = blNo;
    }

    public String getSqdContainersSealsSum() {
        return sqdContainersSealsSum;
    }

    public void setSqdContainersSealsSum(String sqdContainersSealsSum) {
        this.sqdContainersSealsSum = sqdContainersSealsSum;
    }

    public Long getCarrierId() {
        return carrierId;
    }

    public void setCarrierId(Long carrierId) {
        this.carrierId = carrierId;
    }

    public String getFirstVessel() {
        return firstVessel;
    }

    public void setFirstVessel(String firstVessel) {
        this.firstVessel = firstVessel;
    }

    public String getFirstVoyage() {
        return firstVoyage;
    }

    public void setFirstVoyage(String firstVoyage) {
        this.firstVoyage = firstVoyage;
    }

    public String getInquiryScheduleSummary() {
        return inquiryScheduleSummary;
    }

    public void setInquiryScheduleSummary(String inquiryScheduleSummary) {
        this.inquiryScheduleSummary = inquiryScheduleSummary;
    }

    public String getFirstCyOpenTime() {
        return firstCyOpenTime;
    }

    public void setFirstCyOpenTime(String firstCyOpenTime) {
        this.firstCyOpenTime = firstCyOpenTime;
    }

    public String getFirstCyClosingTime() {
        return firstCyClosingTime;
    }

    public void setFirstCyClosingTime(String firstCyClosingTime) {
        this.firstCyClosingTime = firstCyClosingTime;
    }

    public String getCvClosingTime() {
        return cvClosingTime;
    }

    public void setCvClosingTime(String cvClosingTime) {
        this.cvClosingTime = cvClosingTime;
    }

    public Date getEtd() {
        return etd;
    }

    public void setEtd(Date etd) {
        this.etd = etd;
    }

    public Date getEta() {
        return eta;
    }

    public void setEta(Date eta) {
        this.eta = eta;
    }

    public String getSiClosingTime() {
        return siClosingTime;
    }

    public void setSiClosingTime(String siClosingTime) {
        this.siClosingTime = siClosingTime;
    }

    public String getSqdVgmStatus() {
        return sqdVgmStatus;
    }

    public void setSqdVgmStatus(String sqdVgmStatus) {
        this.sqdVgmStatus = sqdVgmStatus;
    }

    public String getSqdAmsEnsPostStatus() {
        return sqdAmsEnsPostStatus;
    }

    public void setSqdAmsEnsPostStatus(String sqdAmsEnsPostStatus) {
        this.sqdAmsEnsPostStatus = sqdAmsEnsPostStatus;
    }

    public Date getPodEta() {
        return podEta;
    }

    public void setPodEta(Date podEta) {
        this.podEta = podEta;
    }

    public Date getDestinationPortEta() {
        return destinationPortEta;
    }

    public void setDestinationPortEta(Date destinationPortEta) {
        this.destinationPortEta = destinationPortEta;
    }

    public String getBookingChargeRemark() {
        return bookingChargeRemark;
    }

    public void setBookingChargeRemark(String bookingChargeRemark) {
        this.bookingChargeRemark = bookingChargeRemark;
    }

    public String getBookingAgentRemark() {
        return bookingAgentRemark;
    }

    public void setBookingAgentRemark(String bookingAgentRemark) {
        this.bookingAgentRemark = bookingAgentRemark;
    }

    public BigDecimal getPayable() {
        return payable;
    }

    public void setPayable(BigDecimal payable) {
        this.payable = payable;
    }

    public String getPsaNo() {
        return psaNo;
    }

    public void setPsaNo(String psaNo) {
        this.psaNo = psaNo;
    }

    public String getRctNo() {
        return rctNo;
    }

    public void setRctNo(String rctNo) {
        this.rctNo = rctNo;
    }

    public String getPaymentTitleCode() {
        return paymentTitleCode;
    }

    public void setPaymentTitleCode(String paymentTitleCode) {
        this.paymentTitleCode = paymentTitleCode;
    }

    public String getImpExpType() {
        return impExpType;
    }

    public void setImpExpType(String impExpType) {
        this.impExpType = impExpType;
    }

    public String getTradingTerms() {
        return tradingTerms;
    }

    public void setTradingTerms(String tradingTerms) {
        this.tradingTerms = tradingTerms;
    }

    public String getLogisticsTerms() {
        return logisticsTerms;
    }

    public void setLogisticsTerms(String logisticsTerms) {
        this.logisticsTerms = logisticsTerms;
    }

    public String getTradingPaymentChannel() {
        return tradingPaymentChannel;
    }

    public void setTradingPaymentChannel(String tradingPaymentChannel) {
        this.tradingPaymentChannel = tradingPaymentChannel;
    }

    public String getGoodsNameSummary() {
        return goodsNameSummary;
    }

    public void setGoodsNameSummary(String goodsNameSummary) {
        this.goodsNameSummary = goodsNameSummary;
    }

    public Long getPackageQuantity() {
        return packageQuantity;
    }

    public void setPackageQuantity(Long packageQuantity) {
        this.packageQuantity = packageQuantity;
    }

    public BigDecimal getGoodsVolume() {
        return goodsVolume;
    }

    public void setGoodsVolume(BigDecimal goodsVolume) {
        this.goodsVolume = goodsVolume;
    }

    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getGoodsCurrencyCode() {
        return goodsCurrencyCode;
    }

    public void setGoodsCurrencyCode(String goodsCurrencyCode) {
        this.goodsCurrencyCode = goodsCurrencyCode;
    }

    public BigDecimal getGoodsValue() {
        return goodsValue;
    }

    public void setGoodsValue(BigDecimal goodsValue) {
        this.goodsValue = goodsValue;
    }

    public Long getLogisticsTypeId() {
        return logisticsTypeId;
    }

    public void setLogisticsTypeId(Long logisticsTypeId) {
        this.logisticsTypeId = logisticsTypeId;
    }

    public String getRevenueTon() {
        return revenueTon;
    }

    public void setRevenueTon(String revenueTon) {
        this.revenueTon = revenueTon;
    }

    public Long getPolId() {
        return polId;
    }

    public void setPolId(Long polId) {
        this.polId = polId;
    }

    public Long getTransitPortId() {
        return transitPortId;
    }

    public void setTransitPortId(Long transitPortId) {
        this.transitPortId = transitPortId;
    }

    public Long getPodId() {
        return podId;
    }

    public void setPodId(Long podId) {
        this.podId = podId;
    }

    public Long getDestinationPortId() {
        return destinationPortId;
    }

    public void setDestinationPortId(Long destinationPortId) {
        this.destinationPortId = destinationPortId;
    }

    public Date getFirstEtd() {
        return firstEtd;
    }

    public void setFirstEtd(Date firstEtd) {
        this.firstEtd = firstEtd;
    }

    public Date getBasicEtd() {
        return basicEtd;
    }

    public void setBasicEtd(Date basicEtd) {
        this.basicEtd = basicEtd;
    }

    public String getServiceTypeIdList() {
        return serviceTypeIdList;
    }

    public void setServiceTypeIdList(String serviceTypeIdList) {
        this.serviceTypeIdList = serviceTypeIdList;
    }

    public String getQoutationNo() {
        return qoutationNo;
    }

    public void setQoutationNo(String qoutationNo) {
        this.qoutationNo = qoutationNo;
    }

    public String getQoutationSketch() {
        return qoutationSketch;
    }

    public void setQoutationSketch(String qoutationSketch) {
        this.qoutationSketch = qoutationSketch;
    }

    public Long getSalesId() {
        return salesId;
    }

    public void setSalesId(Long salesId) {
        this.salesId = salesId;
    }

    public Date getQoutationTime() {
        return qoutationTime;
    }

    public void setQoutationTime(Date qoutationTime) {
        this.qoutationTime = qoutationTime;
    }

    public String getNewBookingNo() {
        return newBookingNo;
    }

    public void setNewBookingNo(String newBookingNo) {
        this.newBookingNo = newBookingNo;
    }

    public String getNewBookingRemark() {
        return newBookingRemark;
    }

    public void setNewBookingRemark(String newBookingRemark) {
        this.newBookingRemark = newBookingRemark;
    }

    public Long getSalesAssistantId() {
        return salesAssistantId;
    }

    public void setSalesAssistantId(Long salesAssistantId) {
        this.salesAssistantId = salesAssistantId;
    }

    public Long getSalesObserverId() {
        return salesObserverId;
    }

    public void setSalesObserverId(Long salesObserverId) {
        this.salesObserverId = salesObserverId;
    }

    public Date getNewBookingTime() {
        return newBookingTime;
    }

    public void setNewBookingTime(Date newBookingTime) {
        this.newBookingTime = newBookingTime;
    }

    public String getInquiryNoticeSum() {
        return inquiryNoticeSum;
    }

    public void setInquiryNoticeSum(String inquiryNoticeSum) {
        this.inquiryNoticeSum = inquiryNoticeSum;
    }

    public String getInquiryInnerRemarkSum() {
        return inquiryInnerRemarkSum;
    }

    public void setInquiryInnerRemarkSum(String inquiryInnerRemarkSum) {
        this.inquiryInnerRemarkSum = inquiryInnerRemarkSum;
    }

    public String getSqdIsfEmnfPostStatus() {
        return sqdIsfEmnfPostStatus;
    }

    public void setSqdIsfEmnfPostStatus(String sqdIsfEmnfPostStatus) {
        this.sqdIsfEmnfPostStatus = sqdIsfEmnfPostStatus;
    }

    public String getSqdDocDeliveryWay() {
        return sqdDocDeliveryWay;
    }

    public void setSqdDocDeliveryWay(String sqdDocDeliveryWay) {
        this.sqdDocDeliveryWay = sqdDocDeliveryWay;
    }

    public String getBookingShipper() {
        return bookingShipper;
    }

    public void setBookingShipper(String bookingShipper) {
        this.bookingShipper = bookingShipper;
    }

    public String getBookingConsignee() {
        return bookingConsignee;
    }

    public void setBookingConsignee(String bookingConsignee) {
        this.bookingConsignee = bookingConsignee;
    }

    public String getBookingNotifyParty() {
        return bookingNotifyParty;
    }

    public void setBookingNotifyParty(String bookingNotifyParty) {
        this.bookingNotifyParty = bookingNotifyParty;
    }

    public String getNoTransferAllowed() {
        return noTransferAllowed;
    }

    public void setNoTransferAllowed(String noTransferAllowed) {
        this.noTransferAllowed = noTransferAllowed;
    }

    public String getNoDividedAllowed() {
        return noDividedAllowed;
    }

    public void setNoDividedAllowed(String noDividedAllowed) {
        this.noDividedAllowed = noDividedAllowed;
    }

    public String getNoAgreementShowed() {
        return noAgreementShowed;
    }

    public void setNoAgreementShowed(String noAgreementShowed) {
        this.noAgreementShowed = noAgreementShowed;
    }

    public String getIsCustomsIntransitShowed() {
        return isCustomsIntransitShowed;
    }

    public void setIsCustomsIntransitShowed(String isCustomsIntransitShowed) {
        this.isCustomsIntransitShowed = isCustomsIntransitShowed;
    }

    public Date getFirstAtd() {
        return firstAtd;
    }

    public void setFirstAtd(Date firstAtd) {
        this.firstAtd = firstAtd;
    }

    public Date getDestinationPortAta() {
        return destinationPortAta;
    }

    public void setDestinationPortAta(Date destinationPortAta) {
        this.destinationPortAta = destinationPortAta;
    }

    public String getShippingMark() {
        return shippingMark;
    }

    public void setShippingMark(String shippingMark) {
        this.shippingMark = shippingMark;
    }

    public String getFreightPaidWayCode() {
        return freightPaidWayCode;
    }

    public void setFreightPaidWayCode(String freightPaidWayCode) {
        this.freightPaidWayCode = freightPaidWayCode;
    }

    public String getCtnrTypeCode() {
        return ctnrTypeCode;
    }

    public void setCtnrTypeCode(String ctnrTypeCode) {
        this.ctnrTypeCode = ctnrTypeCode;
    }

    public Long getPsaId() {
        return psaId;
    }

    public void setPsaId(Long psaId) {
        this.psaId = psaId;
    }

    public Long getOpId() {
        return opId;
    }

    public void setOpId(Long opId) {
        this.opId = opId;
    }

    public Long getSupplierId() {
        return supplierId;
    }

    public void setSupplierId(Long supplierId) {
        this.supplierId = supplierId;
    }

    public String getInquiryNo() {
        return inquiryNo;
    }

    public void setInquiryNo(String inquiryNo) {
        this.inquiryNo = inquiryNo;
    }

    public String getBookingRemark() {
        return bookingRemark;
    }

    public void setBookingRemark(String bookingRemark) {
        this.bookingRemark = bookingRemark;
    }

    public String getClientContractNo() {
        return clientContractNo;
    }

    public void setClientContractNo(String clientContractNo) {
        this.clientContractNo = clientContractNo;
    }

    public String getSqdIssueType() {
        return sqdIssueType;
    }

    public void setSqdIssueType(String sqdIssueType) {
        this.sqdIssueType = sqdIssueType;
    }

    public String getBlTypeCode() {
        return blTypeCode;
    }

    public void setBlTypeCode(String blTypeCode) {
        this.blTypeCode = blTypeCode;
    }

    public String getDistributionStatus() {
        return distributionStatus;
    }

    public void setDistributionStatus(String distributionStatus) {
        this.distributionStatus = distributionStatus;
    }

    public String getMainChargeCurrencyCode() {
        return mainChargeCurrencyCode;
    }

    public void setMainChargeCurrencyCode(String mainChargeCurrencyCode) {
        this.mainChargeCurrencyCode = mainChargeCurrencyCode;
    }

    public BigDecimal getMainChargeRate() {
        return mainChargeRate;
    }

    public void setMainChargeRate(BigDecimal mainChargeRate) {
        this.mainChargeRate = mainChargeRate;
    }

    public String getMainChargeUnitCode() {
        return mainChargeUnitCode;
    }

    public void setMainChargeUnitCode(String mainChargeUnitCode) {
        this.mainChargeUnitCode = mainChargeUnitCode;
    }

    public Long getBookingId() {
        return bookingId;
    }

    public void setBookingId(Long bookingId) {
        this.bookingId = bookingId;
    }

    public Date getBookingTime() {
        return bookingTime;
    }

    public void setBookingTime(Date bookingTime) {
        this.bookingTime = bookingTime;
    }

    public String getBookingStatus() {
        return bookingStatus;
    }

    public void setBookingStatus(String bookingStatus) {
        this.bookingStatus = bookingStatus;
    }

    public String getClientShortName() {
        return clientShortName;
    }

    public void setClientShortName(String clientShortName) {
        this.clientShortName = clientShortName;
    }

    public String getCargoTypeCodeSum() {
        return cargoTypeCodeSum;
    }

    public void setCargoTypeCodeSum(String cargoTypeCodeSum) {
        this.cargoTypeCodeSum = cargoTypeCodeSum;
    }

    public String getAgreementTypeCode() {
        return agreementTypeCode;
    }

    public void setAgreementTypeCode(String agreementTypeCode) {
        this.agreementTypeCode = agreementTypeCode;
    }

    public BigDecimal getSettledRate() {
        return settledRate;
    }

    public void setSettledRate(BigDecimal settledRate) {
        this.settledRate = settledRate;
    }

    public BigDecimal getPurchaseCost() {
        return purchaseCost;
    }

    public void setPurchaseCost(BigDecimal purchaseCost) {
        this.purchaseCost = purchaseCost;
    }

    public BigDecimal getBalanceProfit() {
        return balanceProfit;
    }

    public void setBalanceProfit(BigDecimal balanceProfit) {
        this.balanceProfit = balanceProfit;
    }

    public BigDecimal getMainChargeRateA() {
        return mainChargeRateA;
    }

    public void setMainChargeRateA(BigDecimal mainChargeRateA) {
        this.mainChargeRateA = mainChargeRateA;
    }

    public BigDecimal getMainChargeRateB() {
        return mainChargeRateB;
    }

    public void setMainChargeRateB(BigDecimal mainChargeRateB) {
        this.mainChargeRateB = mainChargeRateB;
    }

    public BigDecimal getMainChargeRateC() {
        return mainChargeRateC;
    }

    public void setMainChargeRateC(BigDecimal mainChargeRateC) {
        this.mainChargeRateC = mainChargeRateC;
    }

    public BigDecimal getSettledRateA() {
        return settledRateA;
    }

    public void setSettledRateA(BigDecimal settledRateA) {
        this.settledRateA = settledRateA;
    }

    public BigDecimal getSettledRateB() {
        return settledRateB;
    }

    public void setSettledRateB(BigDecimal settledRateB) {
        this.settledRateB = settledRateB;
    }

    public BigDecimal getSettledRateC()
    {
        return settledRateC;
    }

    public void setSettledRateC(BigDecimal settledRateC) {
        this.settledRateC = settledRateC;
    }

    public BigDecimal getBalanceProfitA() {
        return balanceProfitA;
    }

    public void setBalanceProfitA(BigDecimal balanceProfitA)
    {
        this.balanceProfitA = balanceProfitA;
    }

    public BigDecimal getBalanceProfitB() {
        return balanceProfitB;
    }

    public void setBalanceProfitB(BigDecimal balanceProfitB) {
        this.balanceProfitB = balanceProfitB;
    }

    public BigDecimal getBalanceProfitC() {
        return balanceProfitC;
    }

    public void setBalanceProfitC(BigDecimal balanceProfitC) {
        this.balanceProfitC = balanceProfitC;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("seaId", getSeaId())
                .append("rctId", getRctId())
                .append("serviceId", getServiceId())
                .append("sqdServiceTypeId", getSqdServiceTypeId())
                .append("sqdRctNo", getSqdRctNo())
                .append("sqdServiceDetailsCode", getSqdServiceDetailsCode())
                .append("sqdPsaNo", getSqdPsaNo())
                .append("soNo", getSoNo())
                .append("blNo", getBlNo())
                .append("sqdContainersSealsSum", getSqdContainersSealsSum())
                .append("carrierId", getCarrierId())
                .append("firstVessel", getFirstVessel())
                .append("firstVoyage", getFirstVoyage())
                .append("inquiryScheduleSummary", getInquiryScheduleSummary())
                .append("firstCyOpenTime", getFirstCyOpenTime())
                .append("firstCyClosingTime", getFirstCyClosingTime())
                .append("cvClosingTime", getCvClosingTime())
                .append("etd", getEtd())
                .append("eta", getEta())
                .append("siClosingTime", getSiClosingTime())
                .append("sqdVgmStatus", getSqdVgmStatus())
                .append("sqdAmsEnsPostStatus", getSqdAmsEnsPostStatus())
                .append("podEta", getPodEta())
                .append("destinationPortEta", getDestinationPortEta())
                .append("bookingChargeRemark", getBookingChargeRemark())
                .append("bookingAgentRemark", getBookingAgentRemark())
                .append("payable", getPayable())
                .append("psaNo", getPsaNo())
                .append("rctNo", getRctNo())
                .append("paymentTitleCode", getPaymentTitleCode())
                .append("impExpType", getImpExpType())
                .append("tradingTerms", getTradingTerms())
                .append("logisticsTerms", getLogisticsTerms())
                .append("tradingPaymentChannel", getTradingPaymentChannel())
                .append("goodsNameSummary", getGoodsNameSummary())
                .append("packageQuantity", getPackageQuantity())
                .append("goodsVolume", getGoodsVolume())
                .append("grossWeight", getGrossWeight())
                .append("goodsCurrencyCode", getGoodsCurrencyCode())
                .append("goodsValue", getGoodsValue())
                .append("logisticsTypeId", getLogisticsTypeId())
                .append("revenueTon", getRevenueTon())
                .append("polId", getPolId())
                .append("transitPortId", getTransitPortId())
                .append("podId", getPodId())
                .append("destinationPortId", getDestinationPortId())
                .append("firstEtd", getFirstEtd())
                .append("basicEtd", getBasicEtd())
                .append("serviceTypeIdList", getServiceTypeIdList())
                .append("qoutationNo", getQoutationNo())
                .append("qoutationSketch", getQoutationSketch())
                .append("salesId", getSalesId())
                .append("qoutationTime", getQoutationTime())
                .append("newBookingNo", getNewBookingNo())
                .append("newBookingRemark", getNewBookingRemark())
                .append("salesAssistantId", getSalesAssistantId())
                .append("salesObserverId", getSalesObserverId())
                .append("newBookingTime", getNewBookingTime())
                .append("inquiryNoticeSum", getInquiryNoticeSum())
                .append("inquiryInnerRemarkSum", getInquiryInnerRemarkSum())
                .append("sqdIsfEmnfPostStatus", getSqdIsfEmnfPostStatus())
                .append("sqdDocDeliveryWay", getSqdDocDeliveryWay())
                .append("bookingShipper", getBookingShipper())
                .append("bookingConsignee", getBookingConsignee())
                .append("bookingNotifyParty", getBookingNotifyParty())
                .append("noTransferAllowed", getNoTransferAllowed())
                .append("noDividedAllowed", getNoDividedAllowed())
                .append("noAgreementShowed", getNoAgreementShowed())
                .append("isCustomsIntransitShowed", getIsCustomsIntransitShowed())
                .append("firstAtd", getFirstAtd())
                .append("destinationPortAta", getDestinationPortAta())
                .append("shippingMark", getShippingMark())
                .append("freightPaidWayCode", getFreightPaidWayCode())
                .append("ctnrTypeCode", getCtnrTypeCode())
                .append("psaId", getPsaId())
                .append("opId", getOpId())
                .append("supplierId", getSupplierId())
                .append("inquiryNo", getInquiryNo())
                .append("bookingRemark", getBookingRemark())
                .append("clientContractNo", getClientContractNo())
                .append("sqdIssueType", getSqdIssueType())
                .append("blTypeCode", getBlTypeCode())
                .append("distributionStatus", getDistributionStatus())
                .append("mainChargeCurrencyCode", getMainChargeCurrencyCode())
                .append("mainChargeRate", getMainChargeRate())
                .append("mainChargeUnitCode", getMainChargeUnitCode())
                .append("bookingId", getBookingId())
                .append("bookingTime", getBookingTime())
                .append("bookingStatus", getBookingStatus())
                .append("clientShortName", getClientShortName())
                .append("cargoTypeCodeSum", getCargoTypeCodeSum())
                .append("agreementTypeCode", getAgreementTypeCode())
                .append("settledRate", getSettledRate())
                .append("purchaseCost", getPurchaseCost())
                .append("balanceProfit", getBalanceProfit())
                .append("mainChargeRateA", getMainChargeRateA())
                .append("mainChargeRateB", getMainChargeRateB())
                .append("mainChargeRateC", getMainChargeRateC())
                .append("settledRateA", getSettledRateA())
                .append("settledRateB", getSettledRateB())
                .append("settledRateC", getSettledRateC())
                .append("balanceProfitA", getBalanceProfitA())
                .append("balanceProfitB", getBalanceProfitB())
                .append("balanceProfitC", getBalanceProfitC())
                .toString();
    }
}
