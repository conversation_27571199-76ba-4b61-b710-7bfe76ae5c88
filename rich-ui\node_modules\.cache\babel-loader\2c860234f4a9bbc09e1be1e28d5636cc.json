{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\monitor\\operlog.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\monitor\\operlog.js", "mtime": 1678688095219}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuY2xlYW5PcGVybG9nID0gY2xlYW5PcGVybG9nOwpleHBvcnRzLmRlbE9wZXJsb2cgPSBkZWxPcGVybG9nOwpleHBvcnRzLmxpc3QgPSBsaXN0Owp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i5pON5L2c5pel5b+X5YiX6KGoCmZ1bmN0aW9uIGxpc3QocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL29wZXJsb2cvbGlzdCcsCiAgICBtZXRob2Q6ICdnZXQnLAogICAgcGFyYW1zOiBxdWVyeQogIH0pOwp9CgovLyDliKDpmaTmk43kvZzml6Xlv5cKZnVuY3Rpb24gZGVsT3BlcmxvZyhvcGVySWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL29wZXJsb2cvJyArIG9wZXJJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQoKLy8g5riF56m65pON5L2c5pel5b+XCmZ1bmN0aW9uIGNsZWFuT3BlcmxvZygpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9tb25pdG9yL29wZXJsb2cvY2xlYW4nLAogICAgbWV0aG9kOiAnZGVsZXRlJwogIH0pOwp9"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "list", "query", "request", "url", "method", "params", "delOperlog", "operId", "cleanOperlog"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/monitor/operlog.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询操作日志列表\r\nexport function list(query) {\r\n  return request({\r\n    url: '/monitor/operlog/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 删除操作日志\r\nexport function delOperlog(operId) {\r\n  return request({\r\n    url: '/monitor/operlog/' + operId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 清空操作日志\r\nexport function cleanOperlog() {\r\n  return request({\r\n    url: '/monitor/operlog/clean',\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,IAAIA,CAACC,KAAK,EAAE;EAC1B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,MAAM,EAAE;EACjC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,MAAM;IACjCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAAA,EAAG;EAC7B,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}