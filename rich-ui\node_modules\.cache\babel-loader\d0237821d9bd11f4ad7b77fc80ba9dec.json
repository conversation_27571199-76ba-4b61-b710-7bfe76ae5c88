{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\cache\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\cache\\index.vue", "mtime": 1754876882564}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_cache", "require", "echarts", "_interopRequireWildcard", "name", "data", "commandstats", "usedmemory", "cache", "created", "getList", "openLoading", "methods", "_this", "getCache", "then", "response", "$modal", "closeLoading", "init", "$refs", "setOption", "tooltip", "trigger", "formatter", "series", "type", "roseType", "radius", "center", "commandStats", "animationEasing", "animationDuration", "info", "used_memory_human", "min", "max", "detail", "value", "parseFloat", "loading", "exports", "default", "_default"], "sources": ["src/views/monitor/cache/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"> <span><i class=\"el-icon-monitor\"></i>基本信息</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%\">\r\n              <tbody>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">Redis版本</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.redis_version }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">运行模式</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{\r\n                      cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\"\r\n                    }}\r\n                  </div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">端口</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.tcp_port }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">客户端数</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.connected_clients }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">运行时间(天)</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.uptime_in_days }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">使用内存</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.used_memory_human }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">使用CPU</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{\r\n                      parseFloat(cache.info.used_cpu_user_children).toFixed(2)\r\n                    }}\r\n                  </div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">内存配置</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.maxmemory_human }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">AOF是否开启</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">RDB是否成功</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.rdb_last_bgsave_status }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">Key数量</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.dbSize\" class=\"cell\">{{ cache.dbSize }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">网络入口/出口</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{\r\n                      cache.info.instantaneous_input_kbps\r\n                    }}kps/{{ cache.info.instantaneous_output_kbps }}kps\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"> <span><i class=\"el-icon-pie-chart\"></i>命令统计</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"commandstats\" style=\"height: 420px\"/>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-odometer\"></i> 内存信息</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"usedmemory\" style=\"height: 420px\"/>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getCache} from \"@/api/monitor/cache\";\r\nimport * as echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"Cache\",\r\n  data() {\r\n    return {\r\n      // 统计命令信息\r\n      commandstats: null,\r\n      // 使用内存\r\n      usedmemory: null,\r\n      // cache信息\r\n      cache: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.openLoading();\r\n  },\r\n  methods: {\r\n    /** 查缓存询信息 */\r\n    getList() {\r\n      getCache().then((response) => {\r\n        this.cache = response.data;\r\n        this.$modal.closeLoading();\r\n\r\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\r\n        this.commandstats.setOption({\r\n          tooltip: {\r\n            trigger: \"item\",\r\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\r\n          },\r\n          series: [\r\n            {\r\n              name: \"命令\",\r\n              type: \"pie\",\r\n              roseType: \"radius\",\r\n              radius: [15, 95],\r\n              center: [\"50%\", \"38%\"],\r\n              data: response.data.commandStats,\r\n              animationEasing: \"cubicInOut\",\r\n              animationDuration: 1000,\r\n            }\r\n          ]\r\n        });\r\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\r\n        this.usedmemory.setOption({\r\n          tooltip: {\r\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\r\n          },\r\n          series: [\r\n            {\r\n              name: \"峰值\",\r\n              type: \"gauge\",\r\n              min: 0,\r\n              max: 1000,\r\n              detail: {\r\n                formatter: this.cache.info.used_memory_human,\r\n              },\r\n              data: [\r\n                {\r\n                  value: parseFloat(this.cache.info.used_memory_human),\r\n                  name: \"内存消耗\",\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n      });\r\n    },\r\n    // 打开加载层\r\n    openLoading() {\r\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;AA8HA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,uBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAG,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,YAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA,aACAF,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,eAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAL,KAAA,GAAAQ,QAAA,CAAAX,IAAA;QACAQ,KAAA,CAAAI,MAAA,CAAAC,YAAA;QAEAL,KAAA,CAAAP,YAAA,GAAAJ,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAd,YAAA;QACAO,KAAA,CAAAP,YAAA,CAAAe,SAAA;UACAC,OAAA;YACAC,OAAA;YACAC,SAAA;UACA;UACAC,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAC,QAAA;YACAC,MAAA;YACAC,MAAA;YACAxB,IAAA,EAAAW,QAAA,CAAAX,IAAA,CAAAyB,YAAA;YACAC,eAAA;YACAC,iBAAA;UACA;QAEA;QACAnB,KAAA,CAAAN,UAAA,GAAAL,OAAA,CAAAiB,IAAA,CAAAN,KAAA,CAAAO,KAAA,CAAAb,UAAA;QACAM,KAAA,CAAAN,UAAA,CAAAc,SAAA;UACAC,OAAA;YACAE,SAAA,sBAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;UACA;UACAT,MAAA,GACA;YACArB,IAAA;YACAsB,IAAA;YACAS,GAAA;YACAC,GAAA;YACAC,MAAA;cACAb,SAAA,EAAAX,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC;YACA;YACA7B,IAAA,GACA;cACAiC,KAAA,EAAAC,UAAA,CAAA1B,KAAA,CAAAL,KAAA,CAAAyB,IAAA,CAAAC,iBAAA;cACA9B,IAAA;YACA;UAEA;QAEA;MACA;IACA;IACA;IACAO,WAAA,WAAAA,YAAA;MACA,KAAAM,MAAA,CAAAuB,OAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}