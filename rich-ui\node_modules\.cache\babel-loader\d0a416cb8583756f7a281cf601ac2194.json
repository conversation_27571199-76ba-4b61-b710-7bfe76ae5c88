{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\location.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\location.vue", "mtime": 1695020545583}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAibG9jYXRpb24iLAogIHByb3BzOiBbJ3Njb3BlJ10KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "names": ["name", "props", "exports", "default", "_default"], "sources": ["src/views/system/company/location.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-tooltip placement=\"top\">\r\n      <div slot=\"content\">\r\n        <h6 style=\"margin: 0\">\r\n          {{\r\n            scope.row.locationName != null ? scope.row.locationName.includes(\"全部\") ? scope.row.locationName.replace(\"全部/\", '') : scope.row.locationName : ''\r\n          }}</h6>\r\n        <h6 style=\"margin: 0\">{{ scope.row.locationDetail }}</h6>\r\n      </div>\r\n      <div>\r\n        <h6 style=\"margin: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n          {{\r\n            scope.row.locationName != null ? scope.row.locationName.includes(\"全部\") ? scope.row.locationName.replace(\"全部/\", '') : scope.row.locationName : ''\r\n          }}</h6>\r\n        <h6 style=\"margin: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n          {{ scope.row.locationDetail }}</h6>\r\n      </div>\r\n    </el-tooltip>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"location\",\r\n  props: ['scope'],\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;eAuBA;EACAA,IAAA;EACAC,KAAA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}