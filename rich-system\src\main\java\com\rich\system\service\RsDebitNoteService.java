package com.rich.system.service;

import com.rich.common.core.domain.entity.RsDebitNote;

import java.util.List;

/**
 * 分账单Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface RsDebitNoteService {
    /**
     * 查询分账单
     *
     * @param debitNoteId 分账单主键
     * @return 分账单
     */
    RsDebitNote selectRsDebitNoteByDebitNoteId(Long debitNoteId);

    /**
     * 查询分账单列表
     *
     * @param rsDebitNote 分账单
     * @return 分账单集合
     */
    List<RsDebitNote> selectRsDebitNoteList(RsDebitNote rsDebitNote);

    /**
     * 新增分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    int insertRsDebitNote(RsDebitNote rsDebitNote);

    /**
     * 修改分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    int updateRsDebitNote(RsDebitNote rsDebitNote);

    /**
     * 批量删除分账单
     *
     * @param debitNoteIds 需要删除的分账单主键集合
     * @return 结果
     */
    int deleteRsDebitNoteByDebitNoteIds(Long[] debitNoteIds);

    /**
     * 删除分账单信息
     *
     * @param debitNoteId 分账单主键
     * @return 结果
     */
    int deleteRsDebitNoteByDebitNoteId(Long debitNoteId);

    int changeStatus(RsDebitNote rsDebitNote);
}
