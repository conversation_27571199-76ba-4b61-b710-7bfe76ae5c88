{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\SeaLclComponent.vue", "mtime": 1754881964238}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "_vueTreeselect", "_debitNodeList", "name", "components", "DebitNoteList", "Audit", "LogisticsProgress", "ChargeList", "Treeselect", "props", "seaLclList", "type", "Array", "default", "_default", "form", "Object", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "carrierList", "companyList", "carrierNormalizer", "Function", "data", "bookingBillConfig", "file", "templateList", "computed", "isDisabled", "methods", "handleAddDebitNote", "serviceObject", "row", "sqdRctNo", "rctNo", "rctId", "isRecievingOrPaying", "rsChargeList", "$emit", "isFieldDisabled", "item", "getServiceInstanceDisable", "rsServiceInstances", "isDateFieldDisabled", "getFormDisable", "getSupplierEmail", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "serviceInstance", "agreementTypeCode", "agreementNo", "formatCarrierLabel", "label", "commaIndex", "indexOf", "substring", "handleBookingBill", "template", "changeServiceFold", "addSeaLCL", "deleteRsOpLclSea", "openChargeSelect", "auditCharge", "event", "generateFreight", "type1", "type2", "selectPsaBookingOpen", "selectCarrier", "addProgress", "logList", "handleSettledRate", "psaBookingCancel", "copyFreight", "calculateCharge", "serviceType", "getPayable", "$parent", "getBookingStatus", "status", "getServiceObject", "serviceTypeId", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/SeaLclComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"sea-lcl-component\">\r\n    <!--拼柜海运-->\r\n    <div v-for=\"(item, index) in seaLclList\" :key=\"`sea-lcl-${index}`\" class=\"sea-lcl-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeServiceFold(item.rsServiceInstances)\">\r\n                海运-LCL\r\n              </h3>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"addSeaLCL\"\r\n              >[+]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"deleteRsOpLclSea(item)\"\r\n              >[-]\r\n              </el-button>\r\n              <el-button\r\n                class=\"service-action-btn\"\r\n                type=\"text\"\r\n                @click=\"openChargeSelect(item)\"\r\n              >[CN...]\r\n              </el-button>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"item.rsServiceInstances\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(2)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @return=\"item = $event\"\r\n            />\r\n            <div class=\"booking-bill-container\">\r\n              <el-popover\r\n                v-for=\"(billConfig, billIndex) in bookingBillConfig\"\r\n                :key=\"`bill-${billIndex}`\"\r\n                placement=\"top\"\r\n                trigger=\"click\"\r\n                width=\"100\"\r\n              >\r\n                <el-button\r\n                  v-for=\"(template, templateIndex) in billConfig.templateList\"\r\n                  :key=\"`template-${templateIndex}`\"\r\n                  @click=\"handleBookingBill(item, template)\"\r\n                >\r\n                  {{ template }}\r\n                </el-button>\r\n                <a\r\n                  slot=\"reference\"\r\n                  class=\"booking-bill-link\"\r\n                  target=\"_blank\"\r\n                >\r\n                  [{{ billConfig.file }}]\r\n                </a>\r\n              </el-popover>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"item.rsServiceInstances.serviceFold\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNo\"\r\n                  :class=\"{ 'disable-form': form.sqdPsaNo }\"\r\n                  :disabled=\"!!form.sqdPsaNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(1, 2, item)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(item.rsServiceInstances.supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"item.rsServiceInstances.supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.rsServiceInstances)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"item.rsServiceInstances.inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"订舱状态\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input\r\n                      :value=\"getBookingStatus(item.bookingStatus)\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                      placeholder=\"订舱状态\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                      class=\"cancel-btn\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel(item)\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"15\" class=\"branch-info-col\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"商务单号\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input\r\n                          :class=\"{ 'disable-form': item.sqdPsaNo }\"\r\n                          :disabled=\"!!item.sqdPsaNo\"\r\n                          :value=\"item.sqdPsaNo\"\r\n                          @focus=\"selectPsaBookingOpen(item)\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button\r\n                          :disabled=\"getServiceInstanceDisable(item.rsServiceInstances)\"\r\n                          class=\"cancel-btn\"\r\n                          size=\"mini\"\r\n                          type=\"text\"\r\n                          @click=\"psaBookingCancel(item)\"\r\n                        >\r\n                          取消\r\n                        </el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"SO号码\">\r\n                    <el-input\r\n                      v-model=\"item.soNo\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"SO号码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"提单号码\">\r\n                    <el-input\r\n                      v-model=\"item.blNo\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"提单号码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"柜号概览\">\r\n                    <el-input\r\n                      v-model=\"item.sqdContainersSealsSum\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"柜号概览\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"船公司\">\r\n                    <treeselect\r\n                      v-model=\"item.carrierId\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disable-branch-nodes=\"true\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      :disabled-fuzzy-matching=\"true\"\r\n                      :flat=\"false\"\r\n                      :flatten-search-results=\"true\"\r\n                      :multiple=\"false\"\r\n                      :normalizer=\"carrierNormalizer\"\r\n                      :options=\"carrierList\"\r\n                      :show-count=\"true\"\r\n                      placeholder=\"选择承运人\"\r\n                      @select=\"selectCarrier(item, $event)\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{ node }\">\r\n                        {{ node.raw.carrierIntlCode || \" \" }}\r\n                      </div>\r\n                      <label\r\n                        slot=\"option-label\"\r\n                        slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                        :class=\"labelClassName\"\r\n                      >\r\n                        {{ formatCarrierLabel(node.label) }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程船名\">\r\n                    <el-input\r\n                      v-model=\"item.firstVessel\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"头程船名船次\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"二程船名\">\r\n                    <el-input\r\n                      v-model=\"item.secondVessel\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"二程船名船次\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"9\">\r\n                  <el-form-item label=\"船期\">\r\n                    <el-input\r\n                      v-model=\"item.inquiryScheduleSummary\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"航班时效\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程开船\">\r\n                    <el-input\r\n                      v-model=\"item.firstCyOpenTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"头程开船\"\r\n                      @change=\"addProgress(getServiceObject(item.serviceTypeId).rsOpLogList, 15)\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"头程截重\">\r\n                    <el-input\r\n                      v-model=\"item.firstCyClosingTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"头程截重\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截关时间\">\r\n                    <el-input\r\n                      v-model=\"item.cvClosingTime\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"截关时间\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"ETD\">\r\n                    <el-date-picker\r\n                      v-model=\"item.etd\"\r\n                      :class=\"{ 'disable-form': isDateFieldDisabled(item) }\"\r\n                      :disabled=\"isDateFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"ETD\"\r\n                      type=\"date\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <el-form-item label=\"ETA\">\r\n                    <el-date-picker\r\n                      v-model=\"item.eta\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      clearable\r\n                      placeholder=\"ETA\"\r\n                      type=\"date\"\r\n                      value-format=\"yyyy-MM-dd\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截补料\">\r\n                    <el-input\r\n                      v-model=\"item.siClosingTime\"\r\n                      :class=\"{ 'disable-form': isDateFieldDisabled(item) }\"\r\n                      :disabled=\"isDateFieldDisabled(item)\"\r\n                      placeholder=\"截补料\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"截VGM\">\r\n                    <el-input\r\n                      v-model=\"item.sqdVgmStatus\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"截VGM\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"AMS/ENS\">\r\n                    <el-input\r\n                      v-model=\"item.sqdAmsEnsPostStatus\"\r\n                      :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                      :disabled=\"isFieldDisabled(item)\"\r\n                      placeholder=\"AMS/ENS\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"5\">\r\n                  <el-form-item label=\"结算价\">\r\n                    <el-row>\r\n                      <el-col :span=\"20\">\r\n                        <el-input\r\n                          v-model=\"item.settledRate\"\r\n                          :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                          :disabled=\"isFieldDisabled(item)\"\r\n                          placeholder=\"price1/price2/price3\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"4\">\r\n                        <el-button type=\"text\" @click=\"handleSettledRate(item)\">确定</el-button>\r\n                      </el-col>\r\n                    </el-row>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"19\">\r\n                  <el-form-item label=\"订舱备注\">\r\n                    <div class=\"booking-remark-group\">\r\n                      <el-input\r\n                        v-model=\"item.bookingChargeRemark\"\r\n                        :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                        :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                        :disabled=\"isFieldDisabled(item)\"\r\n                        placeholder=\"订舱费用备注\"\r\n                        type=\"textarea\"\r\n                      />\r\n                      <el-input\r\n                        v-model=\"item.bookingAgentRemark\"\r\n                        :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                        :class=\"{ 'disable-form': isFieldDisabled(item) }\"\r\n                        :disabled=\"isFieldDisabled(item)\"\r\n                        placeholder=\"订舱备注\"\r\n                        type=\"textarea\"\r\n                      />\r\n                    </div>\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getServiceInstanceDisable(item.rsServiceInstances) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"item.rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"1\"\r\n                @deleteItem=\"item.rsOpLogList = item.rsOpLogList.filter(log => log !== $event)\"\r\n                @return=\"item.rsOpLogList = $event\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"item.rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(item.serviceTypeId) || disabled\"\r\n              :hiddenSupplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"item.payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"item.payableRMBTax\"\r\n              :pay-detail-u-s-d=\"item.payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"item.payableUSDTax\"\r\n              :service-type-id=\"1\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"item.rsChargeList = []\"\r\n              @deleteItem=\"item.rsChargeList = item.rsChargeList.filter(charge => charge !== $event)\"\r\n              @return=\"calculateCharge(2, $event, item)\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"item.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(item)\"\r\n              @deleteItem=\"item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(2,$event,item)\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"../audit.vue\"\r\nimport LogisticsProgress from \"../logisticsProgress.vue\"\r\nimport ChargeList from \"../chargeList.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"SeaLclComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 拼柜海运数据列表\r\n    seaLclList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    carrierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 方法函数\r\n    carrierNormalizer: {\r\n      type: Function,\r\n      default: () => {\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      bookingBillConfig: [{\r\n        file: \"订舱单\",\r\n        templateList: [\"bookingOrder1\"]\r\n      }]\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    // 判断字段是否禁用\r\n    isFieldDisabled(item) {\r\n      return this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)\r\n    },\r\n    // 判断日期字段是否禁用（注意有些地方使用了getFormDisable）\r\n    isDateFieldDisabled(item) {\r\n      return this.disabled || this.getFormDisable(item.rsServiceInstances)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 格式化承运人标签\r\n    formatCarrierLabel(label) {\r\n      const commaIndex = label.indexOf(\",\")\r\n      return commaIndex !== -1 ? label.substring(0, commaIndex) : label\r\n    },\r\n    // 处理订舱单生成\r\n    handleBookingBill(item, template) {\r\n      this.$emit(\"getBookingBill\", item, template)\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceInstance) {\r\n      this.$emit(\"changeServiceFold\", serviceInstance)\r\n    },\r\n    addSeaLCL() {\r\n      this.$emit(\"addSeaLCL\")\r\n    },\r\n    deleteRsOpLclSea(item) {\r\n      this.$emit(\"deleteRsOpLclSea\", item)\r\n    },\r\n    openChargeSelect(item) {\r\n      this.$emit(\"openChargeSelect\", item)\r\n    },\r\n    auditCharge(item, event) {\r\n      this.$emit(\"auditCharge\", item, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    selectPsaBookingOpen(item) {\r\n      this.$emit(\"selectPsaBookingOpen\", item)\r\n    },\r\n    selectCarrier(item, event) {\r\n      this.$emit(\"selectCarrier\", item, event)\r\n    },\r\n    addProgress(logList, type) {\r\n      this.$emit(\"addProgress\", logList, type)\r\n    },\r\n    handleSettledRate(item) {\r\n      this.$emit(\"handleSettledRate\", item)\r\n    },\r\n    psaBookingCancel(item) {\r\n      this.$emit(\"psaBookingCancel\", item)\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceType, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceType, event, item)\r\n    },\r\n    getPayable(type) {\r\n      return this.$parent.getPayable ? this.$parent.getPayable(type) : null\r\n    },\r\n    getBookingStatus(status) {\r\n      return this.$parent.getBookingStatus ? this.$parent.getBookingStatus(status) : ''\r\n    },\r\n    getServiceInstanceDisable(serviceInstance) {\r\n      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.$parent.getServiceObject ? this.$parent.getServiceObject(serviceTypeId) : {}\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.$parent.getFormDisable ? this.$parent.getFormDisable(serviceTypeId) : false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// SeaLcl组件特定样式\r\n.sea-lcl-component {\r\n  width: 100%;\r\n\r\n  .sea-lcl-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .service-action-btn {\r\n        margin-left: 10px;\r\n      }\r\n    }\r\n\r\n    .booking-bill-container {\r\n      margin-left: auto;\r\n\r\n      .booking-bill-link {\r\n        color: blue;\r\n        padding: 0;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .cancel-btn {\r\n        color: red;\r\n      }\r\n    }\r\n\r\n    .branch-info-col {\r\n      .booking-remark-group {\r\n        display: flex;\r\n        gap: 10px;\r\n\r\n        .el-textarea {\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AA+cA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACAA,OAAA;AACA,IAAAI,cAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAK,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,UAAA;MACAC,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,IAAA;MACAJ,IAAA,EAAAK,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,UAAA;MACAN,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAM,aAAA;MACAR,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAO,UAAA;MACAT,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAQ,SAAA;MACAV,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAX,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAZ,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACAW,SAAA;MACAb,IAAA,EAAAO,OAAA;MACAL,OAAA;IACA;IACA;IACAY,YAAA;MACAd,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAY,WAAA;MACAf,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAa,WAAA;MACAhB,IAAA,EAAAC,KAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAc,iBAAA;MACAjB,IAAA,EAAAkB,QAAA;MACAhB,OAAA,WAAAC,SAAA,GACA;IACA;EACA;EACAgB,IAAA,WAAAA,KAAA;IACA;MACAC,iBAAA;QACAC,IAAA;QACAC,YAAA;MACA;IACA;EACA;EACAC,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAb,QAAA,SAAAE,SAAA;IACA;EACA;EACAY,OAAA;IACAC,kBAAA,WAAAA,mBAAAC,aAAA;MACA,IAAAC,GAAA;MACAA,GAAA,CAAAC,QAAA,QAAAzB,IAAA,CAAA0B,KAAA;MACAF,GAAA,CAAAG,KAAA,QAAA3B,IAAA,CAAA2B,KAAA;MACAH,GAAA,CAAAI,mBAAA;MACAJ,GAAA,CAAAK,YAAA;MACA,KAAAC,KAAA,iBAAAN,GAAA,EAAAD,aAAA;IACA;IACA;IACAQ,eAAA,WAAAA,gBAAAC,IAAA;MACA,YAAAzB,QAAA,SAAA0B,yBAAA,CAAAD,IAAA,CAAAE,kBAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAH,IAAA;MACA,YAAAzB,QAAA,SAAA6B,cAAA,CAAAJ,IAAA,CAAAE,kBAAA;IACA;IACA;IACAG,gBAAA,WAAAA,iBAAAC,UAAA;MACA,IAAAC,QAAA,QAAA7B,YAAA,CAAA8B,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAAJ,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAAC,eAAA;MACA,OAAAA,eAAA,CAAAC,iBAAA,GAAAD,eAAA,CAAAE,WAAA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,KAAA;MACA,IAAAC,UAAA,GAAAD,KAAA,CAAAE,OAAA;MACA,OAAAD,UAAA,UAAAD,KAAA,CAAAG,SAAA,IAAAF,UAAA,IAAAD,KAAA;IACA;IACA;IACAI,iBAAA,WAAAA,kBAAArB,IAAA,EAAAsB,QAAA;MACA,KAAAxB,KAAA,mBAAAE,IAAA,EAAAsB,QAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAAV,eAAA;MACA,KAAAf,KAAA,sBAAAe,eAAA;IACA;IACAW,SAAA,WAAAA,UAAA;MACA,KAAA1B,KAAA;IACA;IACA2B,gBAAA,WAAAA,iBAAAzB,IAAA;MACA,KAAAF,KAAA,qBAAAE,IAAA;IACA;IACA0B,gBAAA,WAAAA,iBAAA1B,IAAA;MACA,KAAAF,KAAA,qBAAAE,IAAA;IACA;IACA2B,WAAA,WAAAA,YAAA3B,IAAA,EAAA4B,KAAA;MACA,KAAA9B,KAAA,gBAAAE,IAAA,EAAA4B,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAA/B,IAAA;MACA,KAAAF,KAAA,oBAAAgC,KAAA,EAAAC,KAAA,EAAA/B,IAAA;IACA;IACAgC,oBAAA,WAAAA,qBAAAhC,IAAA;MACA,KAAAF,KAAA,yBAAAE,IAAA;IACA;IACAiC,aAAA,WAAAA,cAAAjC,IAAA,EAAA4B,KAAA;MACA,KAAA9B,KAAA,kBAAAE,IAAA,EAAA4B,KAAA;IACA;IACAM,WAAA,WAAAA,YAAAC,OAAA,EAAAvE,IAAA;MACA,KAAAkC,KAAA,gBAAAqC,OAAA,EAAAvE,IAAA;IACA;IACAwE,iBAAA,WAAAA,kBAAApC,IAAA;MACA,KAAAF,KAAA,sBAAAE,IAAA;IACA;IACAqC,gBAAA,WAAAA,iBAAArC,IAAA;MACA,KAAAF,KAAA,qBAAAE,IAAA;IACA;IACAsC,WAAA,WAAAA,YAAAV,KAAA;MACA,KAAA9B,KAAA,gBAAA8B,KAAA;IACA;IACAW,eAAA,WAAAA,gBAAAC,WAAA,EAAAZ,KAAA,EAAA5B,IAAA;MACA,KAAAF,KAAA,oBAAA0C,WAAA,EAAAZ,KAAA,EAAA5B,IAAA;IACA;IACAyC,UAAA,WAAAA,WAAA7E,IAAA;MACA,YAAA8E,OAAA,CAAAD,UAAA,QAAAC,OAAA,CAAAD,UAAA,CAAA7E,IAAA;IACA;IACA+E,gBAAA,WAAAA,iBAAAC,MAAA;MACA,YAAAF,OAAA,CAAAC,gBAAA,QAAAD,OAAA,CAAAC,gBAAA,CAAAC,MAAA;IACA;IACA3C,yBAAA,WAAAA,0BAAAY,eAAA;MACA,YAAA6B,OAAA,CAAAzC,yBAAA,QAAAyC,OAAA,CAAAzC,yBAAA,CAAAY,eAAA;IACA;IACAgC,gBAAA,WAAAA,iBAAAC,aAAA;MACA,YAAAJ,OAAA,CAAAG,gBAAA,QAAAH,OAAA,CAAAG,gBAAA,CAAAC,aAAA;IACA;IACA1C,cAAA,WAAAA,eAAA0C,aAAA;MACA,YAAAJ,OAAA,CAAAtC,cAAA,QAAAsC,OAAA,CAAAtC,cAAA,CAAA0C,aAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAjF,OAAA,GAAAkF,SAAA"}]}