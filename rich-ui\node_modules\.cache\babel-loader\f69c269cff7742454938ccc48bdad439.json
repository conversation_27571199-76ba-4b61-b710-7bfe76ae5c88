{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\extStaff.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\extStaff.js", "mtime": 1678688095228}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listExtStaff", "query", "request", "url", "method", "params", "getExtStaff", "staffId", "addExtStaff", "data", "updateExtStaff", "delExtStaff", "changeStaffStatus", "staffJobStatus"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/extStaff.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询外部员工列表\r\nexport function listExtStaff(query) {\r\n  return request({\r\n    url: '/system/extStaff/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询外部员工详细\r\nexport function getExtStaff(staffId) {\r\n  return request({\r\n    url: '/system/extStaff/' + staffId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增外部员工\r\nexport function addExtStaff(data) {\r\n  return request({\r\n    url: '/system/extStaff',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改外部员工\r\nexport function updateExtStaff(data) {\r\n  return request({\r\n    url: '/system/extStaff',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除外部员工\r\nexport function delExtStaff(staffId) {\r\n  return request({\r\n    url: '/system/extStaff/' + staffId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 用户状态修改\r\nexport function changeStaffStatus(staffId, staffJobStatus) {\r\n  const data = {\r\n    staffId,\r\n    staffJobStatus\r\n  }\r\n  return request({\r\n    url: '/system/extStaff',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,uBAAuB;IAC5BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,OAAO,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,OAAO;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,OAAO,EAAE;EACnC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB,GAAGI,OAAO;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,iBAAiBA,CAACL,OAAO,EAAEM,cAAc,EAAE;EACzD,IAAMJ,IAAI,GAAG;IACXF,OAAO,EAAPA,OAAO;IACPM,cAAc,EAAdA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB;IACvBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}