{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\outboundPlan.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\outboundPlan.vue", "mtime": 1754876882585}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRPdXRib3VuZHJlY29yZCwNCiAgY2hhbmdlU3RhdHVzLA0KICBkZWxPdXRib3VuZHJlY29yZCwNCiAgZ2V0T3V0Ym91bmRyZWNvcmQsDQogIGxpc3RPdXRib3VuZHJlY29yZCwNCiAgdXBkYXRlT3V0Ym91bmRyZWNvcmQNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL291dGJvdW5kcmVjb3JkIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIkAvdXRpbHMvcmljaCINCmltcG9ydCB7DQogIGxpc3RJbnZlbnRvcnksDQogIGxpc3RJbnZlbnRvcnlzLA0KICBvdXRib3VuZEludmVudG9yeSwNCiAgcHJlT3V0Ym91bmRJbnZlbnRvcnksDQogIHNldHRsZW1lbnQNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL2ludmVudG9yeSINCmltcG9ydCBtb21lbnQgZnJvbSAibW9tZW50Ig0KaW1wb3J0IGN1cnJlbmN5IGZyb20gImN1cnJlbmN5LmpzIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJPdXRib3VuZFBsYW4iLA0KICBwcm9wczogWydvcGVuT3V0Ym91bmQnLCAnb3V0Ym91bmREYXRhJywgJ291dGJvdW5kRm9ybVByb3AnXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgZGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBzaG93TGVmdDogMCwNCiAgICAgIHNob3dSaWdodDogMjQsDQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICBzZWxlY3RPdXRib3VuZExpc3Q6IFtdLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiBmYWxzZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDlh7rku5PorrDlvZXooajmoLzmlbDmja4NCiAgICAgIG91dGJvdW5kcmVjb3JkTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgb3V0Ym91bmRObzogbnVsbCwNCiAgICAgICAgY2xpZW50Q29kZTogbnVsbCwNCiAgICAgICAgY2xpZW50TmFtZTogbnVsbCwNCiAgICAgICAgb3BlcmF0b3I6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIG91dGJvdW5kRGF0ZTogbnVsbCwNCiAgICAgICAgd2FyZWhvdXNlUXVvdGU6IG51bGwsDQogICAgICAgIHdvcmtlckxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZUNvbGxlY3Rpb246IG51bGwsDQogICAgICAgIGNvbGxlY3Rpb25Ob3RlczogbnVsbCwNCiAgICAgICAgdG90YWxCb3hlczogbnVsbCwNCiAgICAgICAgdG90YWxHcm9zc1dlaWdodDogbnVsbCwNCiAgICAgICAgdG90YWxWb2x1bWU6IG51bGwsDQogICAgICAgIHRvdGFsUm93czogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRTdG9yYWdlRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHVucGFpZFBhY2thZ2luZ0ZlZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzQWR2YW5jZUZlZTogbnVsbCwNCiAgICAgICAgcmVudGFsQmFsYW5jZUZlZTogbnVsbCwNCiAgICAgICAgb3ZlcmR1ZVJlbnQ6IG51bGwsDQogICAgICAgIGZyZWVTdGFja0RheXM6IG51bGwsDQogICAgICAgIG92ZXJkdWVVbml0UHJpY2U6IG51bGwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgb3V0Ym91bmRUeXBlOiAwLA0KICAgICAgcHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0TG9hZGluZzogZmFsc2UsDQogICAgICBzZWFyY2g6IG51bGwsDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGNsaWVudENvZGU6IFsNCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLlrqLmiLfku6PnoIHkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciJ9DQogICAgICAgIF0NCiAgICAgIH0sDQogICAgICBvdXRib3VuZEZvcm06IHsNCiAgICAgICAgb3V0Ym91bmREYXRlOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKQ0KICAgICAgfSwNCiAgICAgIGNsaWVudFJvdzoge30sDQogICAgICBwcmVPdXRib3VuZEludmVudG9yeUxpc3Q6IFtdLA0KICAgICAgc2VsZWN0ZWRDYXJnb0RldGFpbDogW10NCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd1NlYXJjaChuKSB7DQogICAgICBpZiAobiA9PT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxDQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAzDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0DQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAwDQogICAgICB9DQogICAgfSwNCiAgICBvcGVuT3V0Ym91bmQobmV3VmFsKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBuZXdWYWw7DQogICAgfSwNCiAgICBkaWFsb2dWaXNpYmxlKG5ld1ZhbCkgew0KICAgICAgaWYgKCFuZXdWYWwpIHsNCiAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2VPdXRib3VuZCcpOw0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyB0aGlzLmdldExpc3QoKQ0KICAgIC8vIOWIneWni+WMlmRpYWxvZ1Zpc2libGXvvIzkuI5wcm9wc+S/neaMgeWQjOatpQ0KICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHRoaXMub3Blbk91dGJvdW5kOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgd2FyZWhvdXNlQ29uZmlybSgpIHsNCg0KICAgIH0sDQogICAgb3V0Ym91bmRPcGVuKCkgew0KICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtUHJvcCkgew0KICAgICAgICAvLyDkvb/nlKjmt7Hmi7fotJ3pgb/lhY3nm7TmjqXkv67mlLlwcm9wDQogICAgICAgIHRoaXMub3V0Ym91bmRGb3JtID0gSlNPTi5wYXJzZShKU09OLnN0cmluZ2lmeSh0aGlzLm91dGJvdW5kRm9ybVByb3ApKTsNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5vdXRib3VuZERhdGEpIHsNCiAgICAgICAgLy8g5L2/55So5rex5ou36LSd6YG/5YWN55u05o6l5L+u5pS5cHJvcA0KICAgICAgICB0aGlzLm91dGJvdW5kRm9ybSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5vdXRib3VuZERhdGEpKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNsb3NlT3V0Ym91bmQoKSB7DQogICAgICB0aGlzLmRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICB9LA0KICAgIC8vIOWKoOi9veWtkOiKgueCueaVsOaNrg0KICAgIGxvYWRDaGlsZEludmVudG9yeSh0cmVlLCB0cmVlTm9kZSwgcmVzb2x2ZSkgew0KICAgICAgLy8g5L2/55SocGFja2FnZVRv5a2X5q615p+l6K+i5a2Q6IqC54K5DQogICAgICBsaXN0SW52ZW50b3J5KHtwYWNrYWdlVG86IHRyZWUuaW52ZW50b3J5SWR9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc3Qgcm93cyA9IHJlc3BvbnNlLnJvd3MNCg0KICAgICAgICAvLyDlhYjlsIbmlbDmja7kvKDpgJLnu5nooajmoLzvvIznoa7kv53lrZDoioLngrnmuLLmn5MNCiAgICAgICAgcmVzb2x2ZShyb3dzKQ0KICAgICAgICB0cmVlLmNoaWxkcmVuID0gcm93cw0KDQogICAgICAgIC8vIOWmguaenOeItumhueiiq+mAieS4re+8jOWcqOWtkOiKgueCuea4suafk+WujOaIkOWQjumAieS4reWug+S7rA0KICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXModHJlZS5pbnZlbnRvcnlJZCkpIHsNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIHJvd3MuZm9yRWFjaChjaGlsZCA9PiB7DQogICAgICAgICAgICAgIGlmICghdGhpcy5pZHMuaW5jbHVkZXMoY2hpbGQuaW52ZW50b3J5SWQpKSB7DQogICAgICAgICAgICAgICAgdGhpcy5pZHMucHVzaChjaGlsZC5pbnZlbnRvcnlJZCkNCiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5wdXNoKGNoaWxkKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIC8vIOWcqFVJ5LiK6YCJ5Lit5a2Q6aG5DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKGNoaWxkLCB0cnVlKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9LCA1MCkgLy8g562J5b6FRE9N5pu05pawDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICB3YXJlaG91c2VSZW50U2V0dGxlbWVudCgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZEhhbmRsZXIgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWUuc3BsaXQoIiAiKVsxXQ0KICAgICAgdGhpcy5vdXRib3VuZFR5cGUgPSA0DQogICAgICB0aGlzLm9wZW5PdXRib3VuZCA9IHRydWUNCiAgICB9LA0KICAgIGNvdW50U3VtbWFyeSgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnVucmVjZWl2ZWRGcm9tQ3VzdG9tZXIgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLmFkZGl0aW9uYWxTdG9yYWdlRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkVW5sb2FkaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkUGFja2FnaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ubG9naXN0aWNzQWR2YW5jZUZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLm92ZXJkdWVSZW50YWxGZWUpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5kaWZmaWN1bHR5V29ya0ZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkRnJvbUN1c3RvbWVyID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlQ29sbGVjdGlvbikudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLmN1c3RvbWVyUmVjZWl2YWJsZUJhbGFuY2UgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS51bnJlY2VpdmVkRnJvbUN1c3RvbWVyKS5zdWJ0cmFjdCh0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZEZyb21DdXN0b21lcikudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnBheWFibGVUb1dvcmtlciA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkVW5sb2FkaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRQYWNraW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ud29ya2VyTG9hZGluZ0ZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkRnJvbVN1cHBsaWVyID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRTdXBwbGllcikuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkU3RvcmFnZUZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlU2FsZXMgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS51bnJlY2VpdmVkRnJvbUN1c3RvbWVyKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRGcm9tU3VwcGxpZXIpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZUNvc3QgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS5wYXlhYmxlVG9Xb3JrZXIpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5sb2dpc3RpY3NBZHZhbmNlRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlQWR2YW5jZU90aGVyRmVlKS52YWx1ZQ0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0ucHJvbWlzc29yeU5vdGVHcm9zc1Byb2ZpdCA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlU2FsZXMpLnN1YnRyYWN0KHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlQ29zdCkudmFsdWUNCiAgICB9LA0KICAgIGN1cnJlbmN5LA0KICAgIC8qKg0KICAgICAqDQogICAgICogQHBhcmFtIHR5cGUgMDrpooTlh7rku5MvMTrlh7rku5MvMjrnm7TmjqXlh7rku5MNCiAgICAgKi8NCiAgICBvdXRib3VuZENvbmZpcm0odHlwZSkgew0KICAgICAgLy8g5omj6LSn5LiN5Y+v5Lul5Ye65LuTDQogICAgICB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgIGlmIChpdGVtLmNhcmdvRGVkdWN0aW9uID09IDEpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLmnInmiaPotKflupPlrZjor7fph43mlrDli77pgInvvIzmtYHmsLTlj7fvvJoiICsgaXRlbS5pbmJvdW5kU2VyaWFsTm9TdWIpDQogICAgICAgICAgcmV0dXJuDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5wYXJ0aWFsT3V0Ym91bmRGbGFnID0gTnVtYmVyKGl0ZW0ucGFydGlhbE91dGJvdW5kRmxhZykNCiAgICAgIH0pDQoNCiAgICAgIC8vIOabtOaWsOeuseaVsOOAgeavm+mHjeOAgeS9k+enrw0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0udG90YWxCb3hlcyA9IDANCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsR3Jvc3NXZWlnaHQgPSAwDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS50b3RhbFZvbHVtZSA9IDANCiAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NhcmdvRGV0YWlsc0xpc3QgPyBpdGVtLnJzQ2FyZ29EZXRhaWxzTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0udG90YWxCb3hlcyA9IGN1cnJlbmN5KGl0ZW0uYm94Q291bnQpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS50b3RhbEJveGVzKS52YWx1ZQ0KICAgICAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsR3Jvc3NXZWlnaHQgPSBjdXJyZW5jeShpdGVtLnVuaXRHcm9zc1dlaWdodCkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnRvdGFsR3Jvc3NXZWlnaHQpLnZhbHVlDQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0udG90YWxWb2x1bWUgPSBjdXJyZW5jeShpdGVtLnVuaXRWb2x1bWUpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS50b3RhbFZvbHVtZSkudmFsdWUNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pDQogICAgICBpZiAodHlwZSA9PT0gMCkgew0KICAgICAgICAvLyB0aGlzLm91dGJvdW5kRm9ybS5wcmUNCiAgICAgICAgYWRkT3V0Ym91bmRyZWNvcmQodGhpcy5vdXRib3VuZEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIC8vIOWIl+ihqOWFi+mahuS4gOS7vSzmiZPkuIrpooTlh7rku5PmoIflv5cNCiAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIGl0ZW0ucHJlT3V0Ym91bmRGbGFnID0gIjEiDQogICAgICAgICAgICBpdGVtLnByZU91dGJvdW5kUmVjb3JkSWQgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgICBpdGVtLnJzQ2FyZ29EZXRhaWxzTGlzdCA/IGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgaXRlbS5wcmVPdXRib3VuZEZsYWcgPSAiMSINCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pIDogbnVsbA0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgcHJlT3V0Ym91bmRJbnZlbnRvcnkoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLpooTlh7rku5PmiJDlip8iKQ0KICAgICAgICAgICAgdGhpcy4kZW1pdCgnY2xvc2VPdXRib3VuZCcpDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOagueaNruWHuuW6k+S/oeaBr+WKoOi9veW+heWHuuW6k+eahOiusOW9lQ0KICAgIGxvYWRQcmVPdXRib3VuZEludmVudG9yeUxpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KDQogICAgICAvLyDmnoTlu7rmn6Xor6Llj4LmlbANCiAgICAgIGNvbnN0IHF1ZXJ5UGFyYW1zID0gew0KICAgICAgICBzcWRQbGFubmVkT3V0Ym91bmREYXRlOiB0aGlzLm91dGJvdW5kRm9ybS5wbGFubmVkT3V0Ym91bmREYXRlLA0KICAgICAgICBjbGllbnRDb2RlOiB0aGlzLm91dGJvdW5kRm9ybS5jbGllbnRDb2RlLA0KICAgICAgICBpbnZlbnRvcnlTdGF0dXM6ICIwIg0KICAgICAgfTsNCg0KICAgICAgLy8g5qC55o2u5Ye65bqT57G75Z6L5re75Yqg6aKE5Ye65bqT5qCH5b+XDQogICAgICBpZiAodGhpcy5vdXRib3VuZFR5cGUgPT09IDEpIHsNCiAgICAgICAgcXVlcnlQYXJhbXMucHJlT3V0Ym91bmRGbGFnID0gIjEiOw0KICAgICAgfQ0KDQogICAgICAvLyDlj5Hotbfor7fmsYINCiAgICAgIGxpc3RJbnZlbnRvcnlzKHF1ZXJ5UGFyYW1zKQ0KICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgLy8g5aSE55CG5ZON5bqU5pWw5o2uDQogICAgICAgICAgdGhpcy5wcmVPdXRib3VuZEludmVudG9yeUxpc3QgPSByZXNwb25zZS5yb3dzLmZpbHRlcihpdGVtID0+ICFpdGVtLnBhY2thZ2VUbykNCiAgICAgICAgICB0aGlzLnByZU91dGJvdW5kSW52ZW50b3J5TGlzdCA/IHJlc3BvbnNlLnJvd3MubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgLy8g6K6h566X6KGl5pS25YWl5LuT6LS5DQogICAgICAgICAgICBpZiAoaXRlbS5pbmNsdWRlc0luYm91bmRGZWUgPT09IDApIHsNCiAgICAgICAgICAgICAgY29uc3QgcmVjZWl2ZWRGZWUgPSBOdW1iZXIoaXRlbS5yZWNlaXZlZFN0b3JhZ2VGZWUgfHwgMCk7DQogICAgICAgICAgICAgIGNvbnN0IGluYm91bmRGZWUgPSBOdW1iZXIoaXRlbS5pbmJvdW5kRmVlIHx8IDApOw0KICAgICAgICAgICAgICBjb25zdCBkaWZmZXJlbmNlID0gY3VycmVuY3koaW5ib3VuZEZlZSkuc3VidHJhY3QocmVjZWl2ZWRGZWUpLnZhbHVlOw0KDQogICAgICAgICAgICAgIC8vIOWPquacieW9k+W3ruWAvOWkp+S6jjDml7bmiY3orr7nva7ooaXmlLbotLnnlKgNCiAgICAgICAgICAgICAgaXRlbS5hZGRpdGlvbmFsU3RvcmFnZUZlZSA9IGRpZmZlcmVuY2UgPiAwID8gZGlmZmVyZW5jZSA6IDA7DQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBpdGVtLmFkZGl0aW9uYWxTdG9yYWdlRmVlID0gMDsNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgLy8g5aaC5p6c5piv5omT5YyF566x77yM5qCH6K6w5Li65pyJ5a2Q6IqC54K5DQogICAgICAgICAgICBpZiAoaXRlbS5wYWNrYWdlUmVjb3JkID09PSAiMSIpIHsNCiAgICAgICAgICAgICAgaXRlbS5oYXNDaGlsZHJlbiA9IHRydWUNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgcmV0dXJuIGl0ZW07DQogICAgICAgICAgfSkgOiBbXTsNCg0KICAgICAgICAgIC8vIOabtOaWsOaAu+aVsA0KICAgICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbCB8fCAwOw0KDQogICAgICAgICAgLy8g5aaC5p6c5piv5pmu6YCa5Ye65bqT57G75Z6L77yM6Ieq5Yqo6YCJ5Lit6aKE5Ye65bqT5qCH6K6w55qE6KGMDQogICAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRUeXBlID09PSAwICYmIHRoaXMuJHJlZnMudGFibGUpIHsNCiAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5wcmVPdXRib3VuZEludmVudG9yeUxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgICAgICBpZiAoaXRlbS5wcmVPdXRib3VuZEZsYWcgPT09IDEpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKGl0ZW0sIHRydWUpOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfSk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIC5jYXRjaChlcnJvciA9PiB7DQogICAgICAgICAgY29uc29sZS5lcnJvcigi5Yqg6L296aKE5Ye65bqT5bqT5a2Y5YiX6KGo5aSx6LSlOiIsIGVycm9yKTsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLliqDovb3pooTlh7rlupPlupPlrZjliJfooajlpLHotKUiKTsNCiAgICAgICAgfSkNCiAgICAgICAgLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOmAieaLqemihOWHuuS7k+iusOW9lQ0KICAgIGhhbmRsZU91dGJvdW5kKHNlbGVjdGVkUm93cykgew0KICAgICAgdGhpcy5vdXRib3VuZFJlc2V0KCkNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtID0gc2VsZWN0ZWRSb3dzDQogICAgICB0aGlzLm91dGJvdW5kVHlwZSA9IDENCiAgICAgIHRoaXMubG9hZFByZU91dGJvdW5kSW52ZW50b3J5TGlzdCgpDQogICAgICB0aGlzLm9wZW5PdXRib3VuZCA9IHRydWUNCiAgICB9LA0KICAgIC8vIOa3u+WKoOmihOWHuuS7k+iusOW9lQ0KICAgIGhhbmRsZVByZU91dGJvdW5kKCkgew0KICAgICAgdGhpcy5vdXRib3VuZFJlc2V0KCkNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kSGFuZGxlciA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIubmFtZS5zcGxpdCgiICIpWzFdDQogICAgICB0aGlzLm91dGJvdW5kVHlwZSA9IDANCiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgIH0sDQogICAgLy8g55u05o6l5Ye65LuTDQogICAgaGFuZGxlRGlyZWN0T3V0Ym91bmQoKSB7DQogICAgICB0aGlzLm91dGJvdW5kUmVzZXQoKQ0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0ub3V0Ym91bmRIYW5kbGVyID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lLnNwbGl0KCIgIilbMV0NCiAgICAgIHRoaXMub3V0Ym91bmRUeXBlID0gMg0KICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSB0cnVlDQogICAgfSwNCiAgICAvLyDnu5Pnrpfku5Pnp58NCiAgICBoYW5kbGVSZW50U2V0dGxlbWVudCgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZEhhbmRsZXIgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLm5hbWUuc3BsaXQoIiAiKVsxXQ0KICAgICAgdGhpcy5vdXRib3VuZFR5cGUgPSAzDQogICAgICB0aGlzLm9wZW5PdXRib3VuZCA9IHRydWUNCiAgICB9LA0KICAgIHBhcnNlVGltZSwNCiAgICBoYW5kbGVPdXRib3VuZENhcmdvRGV0YWlsU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbiwgcm93KSB7DQogICAgICByb3cub3V0Ym91bmRDYXJnb0RldGFpbHNMaXN0ID0gc2VsZWN0aW9uDQogICAgICB0aGlzLnNlbGVjdGVkQ2FyZ29EZXRhaWwgPSBzZWxlY3Rpb24NCiAgICB9LA0KICAgIC8vIOWIpOaWreW9k+WJjeihjOaYr+WQpuiiq+mAieS4rQ0KICAgIGlzUm93U2VsZWN0ZWQocm93KSB7DQogICAgICByZXR1cm4gdGhpcy5zZWxlY3RlZENhcmdvRGV0YWlsLmluY2x1ZGVzKHJvdykNCiAgICB9LA0KICAgIGdldFN1bW1hcmllcyhwYXJhbSkgew0KICAgICAgY29uc3Qge2NvbHVtbnMsIGRhdGF9ID0gcGFyYW0NCiAgICAgIGNvbnN0IHN1bXMgPSBbXQ0KICAgICAgY29uc3Qgc3RhdGlzdGljYWxGaWVsZCA9IFsNCiAgICAgICAgInJlY2VpdmVkU3VwcGxpZXIiLCAidG90YWxCb3hlcyIsICJ1bnBhaWRJbmJvdW5kRmVlIiwgInRvdGFsR3Jvc3NXZWlnaHQiLA0KICAgICAgICAidG90YWxWb2x1bWUiLCAicmVjZWl2ZWRTdG9yYWdlRmVlIiwgInVucGFpZFVubG9hZGluZ0ZlZSIsICJsb2dpc3RpY3NBZHZhbmNlRmVlIiwNCiAgICAgICAgInJlbnRhbEJhbGFuY2VGZWUiLCAib3ZlcmR1ZVJlbnRhbEZlZSIsICJhZGRpdGlvbmFsU3RvcmFnZUZlZSIsICJ1bnBhaWRVbmxvYWRpbmdGZWUiLA0KICAgICAgICAidW5wYWlkUGFja2luZ0ZlZSIsICJyZWNlaXZlZFVubG9hZGluZ0ZlZSIsICJyZWNlaXZlZFBhY2tpbmdGZWUiDQogICAgICBdDQogICAgICAvLyDmsYfmgLvnu5PmnpzlrZjlgqjlr7nosaENCiAgICAgIGNvbnN0IHN1bW1hcnlSZXN1bHRzID0ge30NCiAgICAgIGNvbHVtbnMuZm9yRWFjaCgoY29sdW1uLCBpbmRleCkgPT4gew0KICAgICAgICBpZiAoaW5kZXggPT09IDApIHsNCiAgICAgICAgICBzdW1zW2luZGV4XSA9ICLmsYfmgLsiIC8vIOesrOS4gOWIl+aYvuekuuaWh+acrA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnN0IHByb3AgPSBjb2x1bW4ucHJvcGVydHkNCiAgICAgICAgICBpZiAoc3RhdGlzdGljYWxGaWVsZC5pbmNsdWRlcyhwcm9wKSkgew0KICAgICAgICAgICAgY29uc3QgdG90YWwgPSB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5yZWR1Y2UoKHN1bSwgcm93KSA9PiBjdXJyZW5jeShzdW0pLmFkZChOdW1iZXIocm93W3Byb3BdKSB8fCAwKS52YWx1ZSwgMCkNCiAgICAgICAgICAgIHN1bXNbaW5kZXhdID0gdG90YWwNCiAgICAgICAgICAgIC8vIOWwhuaxh+aAu+e7k+aenOWtmOWCqOWcqCBzdW1tYXJ5UmVzdWx0cyDlr7nosaHkuK0NCiAgICAgICAgICAgIHN1bW1hcnlSZXN1bHRzW2NvbHVtbi5wcm9wZXJ0eV0gPSB0b3RhbA0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBzdW1zW2luZGV4XSA9ICIgIg0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgLy8g5aaC5p6c6ZyA6KaB5bCG5rGH5oC757uT5p6c6LWL5YC85Yiw6KGo5Y2V5a2X5q615Lit77yM5Y+v5Lul5Zyo5q2k5aSE5pON5L2cDQogICAgICAvLyDlgYforr7ooajljZXlrZfmrrXnmoTlkb3lkI3kuI7nu5/orqHlrZfmrrXkuIDoh7QNCiAgICAgIE9iamVjdC5rZXlzKHN1bW1hcnlSZXN1bHRzKS5mb3JFYWNoKGZpZWxkID0+IHsNCiAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtKSB7DQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm1bZmllbGRdID0gc3VtbWFyeVJlc3VsdHNbZmllbGRdICAvLyDlsIbmsYfmgLvlgLzotYvnu5nooajljZXlrZfmrrUNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgdGhpcy5jb3VudFN1bW1hcnkoKQ0KDQogICAgICByZXR1cm4gc3Vtcw0KICAgIH0sDQogICAgaGFuZGxlT3V0Ym91bmRTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICAvLyDmraPnoa7ojrflj5booajmoLzmlbDmja4gLSDpgJrov4dkYXRh5bGe5oCnDQogICAgICBjb25zdCB0cmVlRGF0YSA9IHRoaXMuJHJlZnMudGFibGUuc3RvcmUuc3RhdGVzLmRhdGE7DQogICAgICAvLyDojrflj5bkuYvliY3nmoTpgInmi6nnirbmgIHvvIznlKjkuo7mr5TovoPlj5jljJYNCiAgICAgIGNvbnN0IHByZXZpb3VzSWRzID0gWy4uLnRoaXMuaWRzXTsNCg0KICAgICAgLy8g5riF56m65b2T5YmN6YCJ5oupDQogICAgICB0aGlzLmlkcyA9IFtdOw0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pbnZlbnRvcnlJZCk7DQogICAgICAvLyDmib7lh7rmlrDpgInkuK3lkozlj5bmtojpgInkuK3nmoTpobkNCiAgICAgIGNvbnN0IG5ld2x5U2VsZWN0ZWQgPSB0aGlzLmlkcy5maWx0ZXIoaWQgPT4gIXByZXZpb3VzSWRzLmluY2x1ZGVzKGlkKSk7DQogICAgICBjb25zdCBuZXdseURlc2VsZWN0ZWQgPSBwcmV2aW91c0lkcy5maWx0ZXIoaWQgPT4gIXRoaXMuaWRzLmluY2x1ZGVzKGlkKSk7DQoNCiAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0ID0gc2VsZWN0aW9uDQogICAgICB0aGlzLiRyZWZzLnRhYmxlLmRvTGF5b3V0KCkgLy8g5Yi35paw6KGo5qC85biD5bGADQoNCiAgICAgIC8vIOagueaNruS7k+enn+e7k+eul+iHs++8iHJlbnRhbF9zZXR0bGVtZW50X2RhdGXvvInvvIzorqHnrpfor6XmnaHlupPlrZjnmoTnp5/ph5ENCiAgICAgIC8vIO+8iCDlh7rlupPlvZPlpKkt5LuT56ef57uT566X6IezLeWFjeenn+acnyDvvIkgKiDnp5/ph5HljZXku7cNCiAgICAgIHNlbGVjdGlvbi5tYXAoaXRlbSA9PiB7DQogICAgICAgIGNvbnN0IGRhdGUxID0gbW9tZW50KHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kRGF0ZSkNCiAgICAgICAgY29uc3QgZGF0ZTIgPSBtb21lbnQoaXRlbS5yZW50YWxTZXR0bGVtZW50RGF0ZSkNCiAgICAgICAgaXRlbS5yZW50YWxEYXlzID0gZGF0ZTEuZGlmZihkYXRlMiwgImRheXMiKSArIDEgLy8g5beu6Led55qE5aSp5pWwDQogICAgICAgIGxldCB2b2x1bW4gPSBpdGVtLnRvdGFsVm9sdW1lDQoNCiAgICAgICAgaWYgKCFOdW1iZXIuaXNOYU4oaXRlbS5yZW50YWxEYXlzKSAmJiBpdGVtLnJlbnRhbERheXMgPiAwKSB7DQogICAgICAgICAgLy8g5Ye65LuT5pa55byP5LiN5piv5pW05p+c5rKh5pyJ5YWN56ef5aSp5pWwDQogICAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kVHlwZSAhPT0gIuaVtOafnCIpIHsNCiAgICAgICAgICAgIGl0ZW0ub3ZlcmR1ZVJlbnRhbEZlZSA9IGN1cnJlbmN5KGl0ZW0ucmVudGFsRGF5cykubXVsdGlwbHkoaXRlbS5vdmVyZHVlUmVudGFsVW5pdFByaWNlKS5tdWx0aXBseSh2b2x1bW4pLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGxldCBkYXlzID0gY3VycmVuY3koaXRlbS5yZW50YWxEYXlzKS5zdWJ0cmFjdChpdGVtLmZyZWVTdGFja1BlcmlvZCkudmFsdWUNCiAgICAgICAgICAgIGRheXMgPSBkYXlzID4gMCA/IGRheXMgOiAwDQogICAgICAgICAgICBpdGVtLnJlbnRhbERheXMgPSBkYXlzDQogICAgICAgICAgICBpdGVtLm92ZXJkdWVSZW50YWxGZWUgPSBjdXJyZW5jeShkYXlzKS5tdWx0aXBseShpdGVtLm92ZXJkdWVSZW50YWxVbml0UHJpY2UpLm11bHRpcGx5KHZvbHVtbikudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpITnkIbmlrDpgInkuK3nmoTmiZPljIXnrrHvvJroh6rliqjpgInkuK3lhbblrZDpobkNCiAgICAgICAgaWYgKGl0ZW0ucGFja2FnZVJlY29yZCA9PT0gIjEiICYmIG5ld2x5U2VsZWN0ZWQuaW5jbHVkZXMoaXRlbS5pbnZlbnRvcnlJZCkpIHsNCiAgICAgICAgICAvLyDlpoLmnpzmmK/mlrDpgInkuK3nmoTmiZPljIXnrrHoioLngrkNCg0KICAgICAgICAgIC8vIOWcqOagkeW9ouihqOagvOaVsOaNruS4reaJvuWIsOWvueW6lOeahOiKgueCuQ0KICAgICAgICAgIGNvbnN0IHBhcmVudE5vZGUgPSB0cmVlRGF0YS5maW5kKG5vZGUgPT4gbm9kZS5pbnZlbnRvcnlJZCA9PT0gaXRlbS5pbnZlbnRvcnlJZCk7DQoNCiAgICAgICAgICAvLyDmo4Dmn6XoioLngrnmmK/lkKblt7LlsZXlvIAo5bey5pyJY2hpbGRyZW7lsZ7mgKfkuJTmnInlhoXlrrkpDQogICAgICAgICAgaWYgKHBhcmVudE5vZGUgJiYgcGFyZW50Tm9kZS5jaGlsZHJlbiAmJiBwYXJlbnROb2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIC8vIOWmguaenOiKgueCueW3suWxleW8gO+8jOebtOaOpemAieS4reWFtuaJgOacieWtkOmhuQ0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW4uZm9yRWFjaChjaGlsZCA9PiB7DQogICAgICAgICAgICAgICAgaWYgKCF0aGlzLmlkcy5pbmNsdWRlcyhjaGlsZC5pbnZlbnRvcnlJZCkpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuaWRzLnB1c2goY2hpbGQuaW52ZW50b3J5SWQpOw0KICAgICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RPdXRib3VuZExpc3QucHVzaChjaGlsZCk7DQogICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgdHJ1ZSk7DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sIDUwKTsgLy8g57uZ5LiA54K55pe26Ze06K6pVUnmm7TmlrANCiAgICAgICAgICB9IGVsc2UgaWYgKHBhcmVudE5vZGUgJiYgIXBhcmVudE5vZGUuY2hpbGRyZW5Mb2FkZWQgJiYgcGFyZW50Tm9kZS5oYXNDaGlsZHJlbikgew0KICAgICAgICAgICAgLy8g5aaC5p6c6IqC54K55pyq5bGV5byA5LiU5pyq5Yqg6L296L+H5L2G5pyJ5a2Q6IqC54K55qCH6K6wDQogICAgICAgICAgICBwYXJlbnROb2RlLmNoaWxkcmVuTG9hZGVkID0gdHJ1ZTsNCg0KICAgICAgICAgICAgLy8g5omL5Yqo5bGV5byA6KGM77yM6Kem5Y+R5oeS5Yqg6L29DQogICAgICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLnRvZ2dsZVJvd0V4cGFuc2lvbihwYXJlbnROb2RlLCB0cnVlKTsNCg0KICAgICAgICAgICAgLy8g55uR5ZCs5a2Q6IqC54K55Yqg6L295a6M5oiQ5ZCO5YaN6YCJ5Lit5a6D5LusDQogICAgICAgICAgICAvLyDov5nph4zliKnnlKjkuoZsb2FkQ2hpbGRJbnZlbnRvcnnmlrnms5XkuK3nmoTpgLvovpHvvIzlroPkvJrlnKjlrZDoioLngrnliqDovb3lkI7lpITnkIbpgInkuK3nirbmgIENCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIC8vIOWkhOeQhuWPlua2iOmAieS4reeahOaJk+WMheeuse+8muWPlua2iOmAieS4reWFtuWtkOmhuQ0KICAgICAgbmV3bHlEZXNlbGVjdGVkLmZvckVhY2gocGFyZW50SWQgPT4gew0KICAgICAgICAvLyDmib7lh7rlr7nlupTnmoTniLboioLngrkNCiAgICAgICAgY29uc3QgcGFyZW50Tm9kZSA9IHRyZWVEYXRhLmZpbmQobm9kZSA9Pg0KICAgICAgICAgIG5vZGUuaW52ZW50b3J5SWQgPT09IHBhcmVudElkICYmIG5vZGUucGFja2FnZVJlY29yZCA9PT0gIjEiDQogICAgICAgICk7DQoNCiAgICAgICAgaWYgKHBhcmVudE5vZGUgJiYgcGFyZW50Tm9kZS5jaGlsZHJlbiAmJiBwYXJlbnROb2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAvLyDlj5bmtojpgInkuK3miYDmnInlrZDpobkNCiAgICAgICAgICBwYXJlbnROb2RlLmNoaWxkcmVuLmZvckVhY2goY2hpbGQgPT4gew0KICAgICAgICAgICAgY29uc3QgY2hpbGRJbmRleCA9IHRoaXMuaWRzLmluZGV4T2YoY2hpbGQuaW52ZW50b3J5SWQpOw0KICAgICAgICAgICAgaWYgKGNoaWxkSW5kZXggPiAtMSkgew0KICAgICAgICAgICAgICAvLyDku47pgInkuK3liJfooajkuK3np7vpmaQNCiAgICAgICAgICAgICAgdGhpcy5pZHMuc3BsaWNlKGNoaWxkSW5kZXgsIDEpOw0KICAgICAgICAgICAgICBjb25zdCBpdGVtSW5kZXggPSB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5maW5kSW5kZXgoDQogICAgICAgICAgICAgICAgaXRlbSA9PiBpdGVtLmludmVudG9yeUlkID09PSBjaGlsZC5pbnZlbnRvcnlJZA0KICAgICAgICAgICAgICApOw0KICAgICAgICAgICAgICBpZiAoaXRlbUluZGV4ID4gLTEpIHsNCiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5zcGxpY2UoaXRlbUluZGV4LCAxKTsNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAvLyDlnKhVSeS4iuWPlua2iOmAieS4rQ0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLnRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgZmFsc2UpOw0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KICAgICAgICB9DQogICAgICB9KTsNCg0KICAgICAgdGhpcy5jb3VudFN1bW1hcnkoKQ0KICAgIH0sDQogICAgc2VsZWN0Q29udGFpbmVyVHlwZSh0eXBlKSB7DQogICAgICBzd2l0Y2ggKHR5cGUpIHsNCiAgICAgICAgY2FzZSAiMjBHUCI6DQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlUXVvdGUgPSB0aGlzLmNsaWVudFJvdy5yYXRlMjBncA0KICAgICAgICAgIGJyZWFrDQogICAgICAgIGNhc2UgIjQwSFEiOg0KICAgICAgICAgIHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZVF1b3RlID0gdGhpcy5jbGllbnRSb3cucmF0ZTQwaHENCiAgICAgICAgICBicmVhaw0KDQogICAgICB9DQogICAgfSwNCiAgICBvdXRib3VuZENsaWVudChyb3cpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZVF1b3RlID0gcm93LnJhdGVMY2wNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLmZyZWVTdGFja0RheXMgPSByb3cuZnJlZVN0YWNrUGVyaW9kDQogICAgICB0aGlzLmNsaWVudFJvdyA9IHJvdw0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0ub3ZlcmR1ZVJlbnRhbFVuaXRQcmljZSA9IHJvdy5vdmVyZHVlUmVudA0KICAgICAgLy8gdGhpcy5vdXRib3VuZEZvcm0uY2xpZW50TmFtZSA9IHJvdy5jbGllbnROYW1lDQogICAgICAvLyB0aGlzLm91dGJvdW5kRm9ybS53b3JrZXJMb2FkaW5nRmVlPXJvdy53b3JrZXJMb2FkaW5nRmVlDQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpDQogICAgfSwNCiAgICAvKiog5p+l6K+i5Ye65LuT6K6w5b2V5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RPdXRib3VuZHJlY29yZCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5vdXRib3VuZHJlY29yZExpc3QgPSByZXNwb25zZS5yb3dzDQogICAgICAgIHRoaXMudG90YWwgPSByZXNwb25zZS50b3RhbA0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICB0aGlzLnJlc2V0KCkNCiAgICB9LA0KICAgIG91dGJvdW5kUmVzZXQoKSB7DQogICAgICB0aGlzLm91dGJvdW5kRm9ybSA9IHsNCiAgICAgICAgb3V0Ym91bmRSZWNvcmRJZDogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRTdXBwbGllcjogbnVsbCwNCiAgICAgICAgb3V0Ym91bmRObzogbnVsbCwNCiAgICAgICAgY2xpZW50Q29kZTogbnVsbCwNCiAgICAgICAgY2xpZW50TmFtZTogbnVsbCwNCiAgICAgICAgb3BlcmF0b3I6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiBudWxsLA0KICAgICAgICB3b3JrZXJMb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB3YXJlaG91c2VDb2xsZWN0aW9uOiBudWxsLA0KICAgICAgICBjb2xsZWN0aW9uTm90ZXM6IG51bGwsDQogICAgICAgIHRvdGFsQm94ZXM6IG51bGwsDQogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLA0KICAgICAgICB0b3RhbFJvd3M6IG51bGwsDQogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwNCiAgICAgICAgdW5wYWlkVW5sb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRQYWNrYWdpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBvcGVyYXRpb25SZXF1aXJlbWVudDogbnVsbCwNCiAgICAgICAgZnJlZVN0YWNrRGF5czogbnVsbCwNCiAgICAgICAgb3ZlcmR1ZVVuaXRQcmljZTogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRGcm9tU3VwcGxpZXI6IG51bGwsDQogICAgICAgIHVucmVjZWl2ZWRGcm9tQ3VzdG9tZXI6IG51bGwsDQogICAgICAgIHJlY2VpdmVkRnJvbUN1c3RvbWVyOiBudWxsLA0KICAgICAgICBjdXN0b21lclJlY2VpdmFibGVCYWxhbmNlOiBudWxsLA0KICAgICAgICBwYXlhYmxlVG9Xb3JrZXI6IG51bGwsDQogICAgICAgIHByb21pc3NvcnlOb3RlU2FsZXM6IG51bGwsDQogICAgICAgIHByb21pc3NvcnlOb3RlQ29zdDogbnVsbCwNCiAgICAgICAgcHJvbWlzc29yeU5vdGVHcm9zc1Byb2ZpdDogbnVsbCwNCiAgICAgICAgb3V0Ym91bmREYXRlOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKQ0KICAgICAgfQ0KICAgICAgdGhpcy5wcmVPdXRib3VuZEludmVudG9yeUxpc3QgPSBbXQ0KICAgICAgdGhpcy5yZXNldEZvcm0oIm91dGJvdW5kRm9ybSIpDQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgb3V0Ym91bmREYXRlOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQiKSwNCiAgICAgICAgb3V0Ym91bmRSZWNvcmRJZDogbnVsbCwNCiAgICAgICAgb3V0Ym91bmRObzogbnVsbCwNCiAgICAgICAgY2xpZW50Q29kZTogbnVsbCwNCiAgICAgICAgY2xpZW50TmFtZTogbnVsbCwNCiAgICAgICAgb3BlcmF0b3I6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiBudWxsLA0KICAgICAgICB3b3JrZXJMb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB3YXJlaG91c2VDb2xsZWN0aW9uOiBudWxsLA0KICAgICAgICBjb2xsZWN0aW9uTm90ZXM6IG51bGwsDQogICAgICAgIHRvdGFsQm94ZXM6IG51bGwsDQogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLA0KICAgICAgICB0b3RhbFJvd3M6IG51bGwsDQogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwNCiAgICAgICAgdW5wYWlkVW5sb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRQYWNrYWdpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBmcmVlU3RhY2tEYXlzOiBudWxsLA0KICAgICAgICBvdmVyZHVlVW5pdFByaWNlOiBudWxsDQogICAgICB9DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpDQogICAgfSwNCiAgICAvKiog5pCc57Si5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlUXVlcnkoKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxDQogICAgICB0aGlzLmdldExpc3QoKQ0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIikNCiAgICAgIHRoaXMuaGFuZGxlUXVlcnkoKQ0KICAgIH0sDQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlKHJvdykgew0KICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiDQogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCLnoa7orqTopoFcIiIgKyB0ZXh0ICsgIuWQl++8nyIpLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gY2hhbmdlU3RhdHVzKHJvdy5vdXRib3VuZFJlY29yZElkLCByb3cuc3RhdHVzKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKQ0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgew0KICAgICAgICByb3cuc3RhdHVzID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIjEiIDogIjAiDQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5vdXRib3VuZFJlY29yZElkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9PSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQoKSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye65LuT6K6w5b2VIg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgY29uc3Qgb3V0Ym91bmRSZWNvcmRJZCA9IHJvdy5vdXRib3VuZFJlY29yZElkIHx8IHRoaXMuaWRzDQogICAgICBnZXRPdXRib3VuZHJlY29yZChvdXRib3VuZFJlY29yZElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55Ye65LuT6K6w5b2VIg0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5vdXRib3VuZFJlY29yZElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZU91dGJvdW5kcmVjb3JkKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpDQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBhZGRPdXRib3VuZHJlY29yZCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKQ0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3Qgb3V0Ym91bmRSZWNvcmRJZHMgPSByb3cub3V0Ym91bmRSZWNvcmRJZCB8fCB0aGlzLmlkcw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgi5piv5ZCm56Gu6K6k5Yig6Zmk5Ye65LuT6K6w5b2V57yW5Y+35Li6XCIiICsgb3V0Ym91bmRSZWNvcmRJZHMgKyAiXCLnmoTmlbDmja7pobnvvJ8iKS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbE91dGJvdW5kcmVjb3JkKG91dGJvdW5kUmVjb3JkSWRzKQ0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuWIoOmZpOaIkOWKnyIpDQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoInN5c3RlbS9vdXRib3VuZHJlY29yZC9leHBvcnQiLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGBvdXRib3VuZHJlY29yZF8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIC8vIOa3u+WKoOaQnOe0ouW5tua7muWKqOWIsOWMuemFjeihjOeahOaWueazlQ0KICAgIGhhbmRsZVNlYXJjaEVudGVyKCkgew0KICAgICAgaWYgKCF0aGlzLnNlYXJjaCkgcmV0dXJuOw0KDQogICAgICAvLyDmn6Xmib7ljLnphY3nmoTooYzntKLlvJUNCiAgICAgIGNvbnN0IGluZGV4ID0gdGhpcy5wcmVPdXRib3VuZEludmVudG9yeUxpc3QuZmluZEluZGV4KA0KICAgICAgICBpdGVtID0+IHsNCiAgICAgICAgICAvLyDnoa7kv50gaW5ib3VuZFNlcmlhbE5vIOWtmOWcqOS4lOS4uuWtl+espuS4sg0KICAgICAgICAgIGNvbnN0IHNlcmlhbE5vID0gU3RyaW5nKGl0ZW0uaW5ib3VuZFNlcmlhbE5vIHx8ICcnKTsNCiAgICAgICAgICBjb25zdCBzZWFyY2hWYWx1ZSA9IFN0cmluZyh0aGlzLnNlYXJjaCk7DQogICAgICAgICAgLy8g5omT5Y2w5q+P5qyh5q+U6L6D55qE5YC877yM5biu5Yqp6LCD6K+VDQogICAgICAgICAgcmV0dXJuIHNlcmlhbE5vLmluY2x1ZGVzKHNlYXJjaFZhbHVlKTsNCiAgICAgICAgfQ0KICAgICAgKTsNCg0KICAgICAgaWYgKGluZGV4ID4gLTEpIHsNCiAgICAgICAgLy8g6I635Y+W6KGo5qC8RE9NDQogICAgICAgIGNvbnN0IHRhYmxlID0gdGhpcy4kcmVmcy50YWJsZTsNCg0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgLy8g6I635Y+W6KGo5qC855qE5rua5Yqo5a655ZmoDQogICAgICAgICAgY29uc3Qgc2Nyb2xsV3JhcHBlciA9IHRhYmxlLiRlbC5xdWVyeVNlbGVjdG9yKCcuZWwtdGFibGVfX2JvZHktd3JhcHBlcicpOw0KICAgICAgICAgIC8vIOiOt+WPluaJgOacieihjA0KICAgICAgICAgIGNvbnN0IHJvd3MgPSBzY3JvbGxXcmFwcGVyLnF1ZXJ5U2VsZWN0b3JBbGwoJy5lbC10YWJsZV9fcm93Jyk7DQoNCiAgICAgICAgICAvLyDpgY3ljobmiYDmnInooYzvvIzmib7liLDljLnphY3nmoTmtYHmsLTlj7cNCiAgICAgICAgICBsZXQgdGFyZ2V0SW5kZXggPSAtMTsNCiAgICAgICAgICByb3dzLmZvckVhY2goKHJvdywgaWR4KSA9PiB7DQogICAgICAgICAgICBjb25zdCByb3dUZXh0ID0gcm93LnRleHRDb250ZW50Ow0KICAgICAgICAgICAgaWYgKHJvd1RleHQuaW5jbHVkZXModGhpcy5zZWFyY2gpKSB7DQogICAgICAgICAgICAgIHRhcmdldEluZGV4ID0gaWR4Ow0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pOw0KDQoNCiAgICAgICAgICBpZiAodGFyZ2V0SW5kZXggPiAtMSkgew0KICAgICAgICAgICAgY29uc3QgdGFyZ2V0Um93ID0gcm93c1t0YXJnZXRJbmRleF07DQogICAgICAgICAgICAvLyDorqHnrpfpnIDopoHmu5rliqjnmoTkvY3nva4NCiAgICAgICAgICAgIGNvbnN0IHJvd1RvcCA9IHRhcmdldFJvdy5vZmZzZXRUb3A7DQoNCiAgICAgICAgICAgIC8vIOS9v+eUqOW5s+a7kea7muWKqA0KICAgICAgICAgICAgc2Nyb2xsV3JhcHBlci5zY3JvbGxUbyh7DQogICAgICAgICAgICAgIHRvcDogcm93VG9wIC0gc2Nyb2xsV3JhcHBlci5jbGllbnRIZWlnaHQgLyAyLA0KICAgICAgICAgICAgICBiZWhhdmlvcjogJ3Ntb290aCcNCiAgICAgICAgICAgIH0pOw0KDQogICAgICAgICAgICAvLyDpq5jkuq7mmL7npLror6XooYwNCiAgICAgICAgICAgIHRhcmdldFJvdy5jbGFzc0xpc3QuYWRkKCdoaWdobGlnaHQtcm93Jyk7DQogICAgICAgICAgICAvLyAx56eS5ZCO56e76Zmk6auY5LquDQogICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgICAgdGFyZ2V0Um93LmNsYXNzTGlzdC5yZW1vdmUoJ2hpZ2hsaWdodC1yb3cnKTsNCiAgICAgICAgICAgIH0sIDIwMDApOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+acquaJvuWIsOWMuemFjeeahOiusOW9lScpOw0KICAgICAgfQ0KICAgIH0sDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["outboundPlan.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAu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file": "outboundPlan.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible=\"dialogVisible\" @open=\"outboundOpen\" @update:visible=\"dialogVisible = $event\"\r\n      append-to-body\r\n      width=\"70%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"计划出仓\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.plannedOutboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\">\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table ref=\"table\" v-loading=\"preOutboundInventoryListLoading\"\r\n                        :data=\"preOutboundInventoryList\" :load=\"loadChildInventory\" :summary-method=\"getSummaries\"\r\n                        :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        max-height=\"300\" row-key=\"inventoryId\" show-summary\r\n                        @selection-change=\"handleOutboundSelectionChange\"\r\n                        style=\"width: 100%;\"\r\n              >\r\n                <el-table-column align=\"center\" fixed type=\"selection\" width=\"28\"/>\r\n                <el-table-column align=\"center\" fixed label=\"序号\" type=\"index\" width=\"28\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.$index + 1 }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\">\r\n                  <template #header>\r\n                    <el-input\r\n                      v-model=\"search\"\r\n                      clearable\r\n                      placeholder=\"输入流水号搜索\"\r\n                      size=\"mini\"\r\n                      @keyup.enter=\"handleSearchEnter\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"部分出库\" prop=\"inboundDate\" width=\"50\">\r\n                  <template #default=\"scope\">\r\n                    <el-switch v-model=\"scope.row.partialOutboundFlag\"/>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货物明细\">\r\n                  <template #default=\"scope\">\r\n                    <el-popover\r\n                      :disabled=\"scope.row.partialOutboundFlag==0\"\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                          <template #default=\"scope\">\r\n                            <el-input v-model=\"scope.row.boxCount\" :disabled=\"!isRowSelected(scope.row)\"/>\r\n                          </template>\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"体积小计\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"毛重小计\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <template #reference>\r\n                        <el-button style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                      </template>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"最新计租日\" prop=\"inboundDate\" width=\"80\">\r\n                  <template #default=\"scope\">\r\n                    <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付卸货费\" prop=\"receivedUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收打包费\" prop=\"unpaidPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付打包费\" prop=\"receivedPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n                <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n                <el-table-column align=\"center\" label=\"超租天数\" prop=\"rentalDays\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n          <el-button @click=\"closeOutbound\">关 闭</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord,\r\n  getOutboundrecord,\r\n  listOutboundrecord,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {\r\n  listInventory,\r\n  listInventorys,\r\n  outboundInventory,\r\n  preOutboundInventory,\r\n  settlement\r\n} from \"@/api/system/inventory\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\n\r\nexport default {\r\n  name: \"OutboundPlan\",\r\n  props: ['openOutbound', 'outboundData', 'outboundFormProp'],\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: 0,\r\n      preOutboundInventoryListLoading: false,\r\n      search: null,\r\n      // 表单校验\r\n      rules: {\r\n        clientCode: [\r\n          {required: true, message: \"客户代码不能为空\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      outboundForm: {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      },\r\n      clientRow: {},\r\n      preOutboundInventoryList: [],\r\n      selectedCargoDetail: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    openOutbound(newVal) {\r\n      this.dialogVisible = newVal;\r\n    },\r\n    dialogVisible(newVal) {\r\n      if (!newVal) {\r\n        this.$emit('closeOutbound');\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // this.getList()\r\n    // 初始化dialogVisible，与props保持同步\r\n    this.dialogVisible = this.openOutbound;\r\n  },\r\n  methods: {\r\n    warehouseConfirm() {\r\n\r\n    },\r\n    outboundOpen() {\r\n      if (this.outboundFormProp) {\r\n        // 使用深拷贝避免直接修改prop\r\n        this.outboundForm = JSON.parse(JSON.stringify(this.outboundFormProp));\r\n      } else if (this.outboundData) {\r\n        // 使用深拷贝避免直接修改prop\r\n        this.outboundForm = JSON.parse(JSON.stringify(this.outboundData));\r\n      }\r\n    },\r\n    closeOutbound() {\r\n      this.dialogVisible = false;\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      })\r\n    },\r\n    warehouseRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 4\r\n      this.openOutbound = true\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    currency,\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓/2:直接出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      // 扣货不可以出仓\r\n      this.selectOutboundList.map(item => {\r\n        if (item.cargoDeduction == 1) {\r\n          this.$message.error(\"有扣货库存请重新勾选，流水号：\" + item.inboundSerialNoSub)\r\n          return\r\n        }\r\n      })\r\n\r\n      this.selectOutboundList.map(item => {\r\n        item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n      })\r\n\r\n      // 更新箱数、毛重、体积\r\n      this.outboundForm.totalBoxes = 0\r\n      this.outboundForm.totalGrossWeight = 0\r\n      this.outboundForm.totalVolume = 0\r\n      this.selectOutboundList.map(item => {\r\n        item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n          this.outboundForm.totalBoxes = currency(item.boxCount).add(this.outboundForm.totalBoxes).value\r\n          this.outboundForm.totalGrossWeight = currency(item.unitGrossWeight).add(this.outboundForm.totalGrossWeight).value\r\n          this.outboundForm.totalVolume = currency(item.unitVolume).add(this.outboundForm.totalVolume).value\r\n          return item\r\n        }) : null\r\n        return item\r\n      })\r\n      if (type === 0) {\r\n        // this.outboundForm.pre\r\n        addOutboundrecord(this.outboundForm).then(response => {\r\n          // 列表克隆一份,打上预出仓标志\r\n          let data = this.selectOutboundList.map(item => {\r\n            item.preOutboundFlag = \"1\"\r\n            item.preOutboundRecordId = response.data\r\n            item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n              item.preOutboundFlag = \"1\"\r\n              return item\r\n            }) : null\r\n            return item\r\n          })\r\n\r\n          preOutboundInventory(data).then(response => {\r\n            this.getList()\r\n            this.$message.success(\"预出仓成功\")\r\n            this.$emit('closeOutbound')\r\n          })\r\n        })\r\n      }\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.loading = true;\r\n\r\n      // 构建查询参数\r\n      const queryParams = {\r\n        sqdPlannedOutboundDate: this.outboundForm.plannedOutboundDate,\r\n        clientCode: this.outboundForm.clientCode,\r\n        inventoryStatus: \"0\"\r\n      };\r\n\r\n      // 根据出库类型添加预出库标志\r\n      if (this.outboundType === 1) {\r\n        queryParams.preOutboundFlag = \"1\";\r\n      }\r\n\r\n      // 发起请求\r\n      listInventorys(queryParams)\r\n        .then(response => {\r\n          // 处理响应数据\r\n          this.preOutboundInventoryList = response.rows.filter(item => !item.packageTo)\r\n          this.preOutboundInventoryList ? response.rows.map(item => {\r\n            // 计算补收入仓费\r\n            if (item.includesInboundFee === 0) {\r\n              const receivedFee = Number(item.receivedStorageFee || 0);\r\n              const inboundFee = Number(item.inboundFee || 0);\r\n              const difference = currency(inboundFee).subtract(receivedFee).value;\r\n\r\n              // 只有当差值大于0时才设置补收费用\r\n              item.additionalStorageFee = difference > 0 ? difference : 0;\r\n            } else {\r\n              item.additionalStorageFee = 0;\r\n            }\r\n\r\n            // 如果是打包箱，标记为有子节点\r\n            if (item.packageRecord === \"1\") {\r\n              item.hasChildren = true\r\n            }\r\n\r\n            return item;\r\n          }) : [];\r\n\r\n          // 更新总数\r\n          this.total = response.total || 0;\r\n\r\n          // 如果是普通出库类型，自动选中预出库标记的行\r\n          if (this.outboundType === 0 && this.$refs.table) {\r\n            this.$nextTick(() => {\r\n              this.preOutboundInventoryList.forEach(item => {\r\n                if (item.preOutboundFlag === 1) {\r\n                  this.$refs.table.toggleRowSelection(item, true);\r\n                }\r\n              });\r\n            });\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"加载预出库库存列表失败:\", error);\r\n          this.$message.error(\"加载预出库库存列表失败\");\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 选择预出仓记录\r\n    handleOutbound(selectedRows) {\r\n      this.outboundReset()\r\n      this.outboundForm = selectedRows\r\n      this.outboundType = 1\r\n      this.loadPreOutboundInventoryList()\r\n      this.openOutbound = true\r\n    },\r\n    // 添加预出仓记录\r\n    handlePreOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 0\r\n      this.openOutbound = true\r\n    },\r\n    // 直接出仓\r\n    handleDirectOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 2\r\n      this.openOutbound = true\r\n    },\r\n    // 结算仓租\r\n    handleRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 3\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.outboundCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\"\r\n      ]\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\" // 第一列显示文本\r\n        } else {\r\n          const prop = column.property\r\n          if (statisticalField.includes(prop)) {\r\n            const total = this.selectOutboundList.reduce((sum, row) => currency(sum).add(Number(row[prop]) || 0).value, 0)\r\n            sums[index] = total\r\n            // 将汇总结果存储在 summaryResults 对象中\r\n            summaryResults[column.property] = total\r\n          } else {\r\n            sums[index] = \" \"\r\n          }\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n\r\n      return sums\r\n    },\r\n    handleOutboundSelectionChange(selection) {\r\n      // 正确获取表格数据 - 通过data属性\r\n      const treeData = this.$refs.table.store.states.data;\r\n      // 获取之前的选择状态，用于比较变化\r\n      const previousIds = [...this.ids];\r\n\r\n      // 清空当前选择\r\n      this.ids = [];\r\n      this.ids = selection.map(item => item.inventoryId);\r\n      // 找出新选中和取消选中的项\r\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id));\r\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id));\r\n\r\n      this.selectOutboundList = selection\r\n      this.$refs.table.doLayout() // 刷新表格布局\r\n\r\n      // 根据仓租结算至（rental_settlement_date），计算该条库存的租金\r\n      // （ 出库当天-仓租结算至-免租期 ） * 租金单价\r\n      selection.map(item => {\r\n        const date1 = moment(this.outboundForm.outboundDate)\r\n        const date2 = moment(item.rentalSettlementDate)\r\n        item.rentalDays = date1.diff(date2, \"days\") + 1 // 差距的天数\r\n        let volumn = item.totalVolume\r\n\r\n        if (!Number.isNaN(item.rentalDays) && item.rentalDays > 0) {\r\n          // 出仓方式不是整柜没有免租天数\r\n          if (this.outboundForm.outboundType !== \"整柜\") {\r\n            item.overdueRentalFee = currency(item.rentalDays).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          } else {\r\n            let days = currency(item.rentalDays).subtract(item.freeStackPeriod).value\r\n            days = days > 0 ? days : 0\r\n            item.rentalDays = days\r\n            item.overdueRentalFee = currency(days).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          }\r\n        }\r\n\r\n        // 处理新选中的打包箱：自动选中其子项\r\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\r\n          // 如果是新选中的打包箱节点\r\n\r\n          // 在树形表格数据中找到对应的节点\r\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId);\r\n\r\n          // 检查节点是否已展开(已有children属性且有内容)\r\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n            // 如果节点已展开，直接选中其所有子项\r\n            setTimeout(() => {\r\n              parentNode.children.forEach(child => {\r\n                if (!this.ids.includes(child.inventoryId)) {\r\n                  this.ids.push(child.inventoryId);\r\n                  this.selectOutboundList.push(child);\r\n                  this.$refs.table.toggleRowSelection(child, true);\r\n                }\r\n              });\r\n            }, 50); // 给一点时间让UI更新\r\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\r\n            // 如果节点未展开且未加载过但有子节点标记\r\n            parentNode.childrenLoaded = true;\r\n\r\n            // 手动展开行，触发懒加载\r\n            this.$refs.table.toggleRowExpansion(parentNode, true);\r\n\r\n            // 监听子节点加载完成后再选中它们\r\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\r\n          }\r\n        }\r\n      })\r\n\r\n      // 处理取消选中的打包箱：取消选中其子项\r\n      newlyDeselected.forEach(parentId => {\r\n        // 找出对应的父节点\r\n        const parentNode = treeData.find(node =>\r\n          node.inventoryId === parentId && node.packageRecord === \"1\"\r\n        );\r\n\r\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n          // 取消选中所有子项\r\n          parentNode.children.forEach(child => {\r\n            const childIndex = this.ids.indexOf(child.inventoryId);\r\n            if (childIndex > -1) {\r\n              // 从选中列表中移除\r\n              this.ids.splice(childIndex, 1);\r\n              const itemIndex = this.selectOutboundList.findIndex(\r\n                item => item.inventoryId === child.inventoryId\r\n              );\r\n              if (itemIndex > -1) {\r\n                this.selectOutboundList.splice(itemIndex, 1);\r\n              }\r\n              // 在UI上取消选中\r\n              this.$refs.table.toggleRowSelection(child, false);\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n      this.countSummary()\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.clientRow = row\r\n      this.outboundForm.overdueRentalUnitPrice = row.overdueRent\r\n      // this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecord(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        receivedSupplier: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        operationRequirement: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null,\r\n        receivedFromSupplier: null,\r\n        unreceivedFromCustomer: null,\r\n        receivedFromCustomer: null,\r\n        customerReceivableBalance: null,\r\n        payableToWorker: null,\r\n        promissoryNoteSales: null,\r\n        promissoryNoteCost: null,\r\n        promissoryNoteGrossProfit: null,\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\"),\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.outboundRecordId != null) {\r\n            updateOutboundrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addOutboundrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return;\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || '');\r\n          const searchValue = String(this.search);\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue);\r\n        }\r\n      );\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table;\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector('.el-table__body-wrapper');\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll('.el-table__row');\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1;\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent;\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx;\r\n            }\r\n          });\r\n\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex];\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop;\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: 'smooth'\r\n            });\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add('highlight-row');\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove('highlight-row');\r\n            }, 2000);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning('未找到匹配的记录');\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .edit .number .el-input__inner {\r\n  text-align: right;\r\n}\r\n\r\n// 添加高亮样式\r\n::v-deep .highlight-row {\r\n  background-color: #fdf5e6 !important;\r\n  transition: background-color 0.5s;\r\n}\r\n</style>\r\n"]}]}