{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1754876882544}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyLmpzIikpOwp2YXIgX3Z1ZXggPSByZXF1aXJlKCJ2dWV4Iik7CnZhciBfTG9nbyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiLi9Mb2dvIikpOwp2YXIgX1NpZGViYXJJdGVtID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKCIuL1NpZGViYXJJdGVtIikpOwp2YXIgX3ZhcmlhYmxlczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvYXNzZXRzL3N0eWxlcy92YXJpYWJsZXMuc2NzcyIpKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIGNvbXBvbmVudHM6IHsKICAgIFNpZGViYXJJdGVtOiBfU2lkZWJhckl0ZW0uZGVmYXVsdCwKICAgIExvZ286IF9Mb2dvLmRlZmF1bHQKICB9LAogIGNvbXB1dGVkOiAoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgKDAsIF92dWV4Lm1hcFN0YXRlKShbInNldHRpbmdzIl0pKSwgKDAsIF92dWV4Lm1hcEdldHRlcnMpKFsic2lkZWJhclJvdXRlcnMiLCAic2lkZWJhciJdKSksIHt9LCB7CiAgICBhY3RpdmVNZW51OiBmdW5jdGlvbiBhY3RpdmVNZW51KCkgewogICAgICB2YXIgcm91dGUgPSB0aGlzLiRyb3V0ZTsKICAgICAgdmFyIG1ldGEgPSByb3V0ZS5tZXRhLAogICAgICAgIHBhdGggPSByb3V0ZS5wYXRoOwogICAgICAvLyBpZiBzZXQgcGF0aCwgdGhlIHNpZGViYXIgd2lsbCBoaWdobGlnaHQgdGhlIHBhdGggeW91IHNldAogICAgICBpZiAobWV0YS5hY3RpdmVNZW51KSB7CiAgICAgICAgcmV0dXJuIG1ldGEuYWN0aXZlTWVudTsKICAgICAgfQogICAgICByZXR1cm4gcGF0aDsKICAgIH0sCiAgICBzaG93TG9nbzogZnVuY3Rpb24gc2hvd0xvZ28oKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5zaWRlYmFyTG9nbzsKICAgIH0sCiAgICB2YXJpYWJsZXM6IGZ1bmN0aW9uIHZhcmlhYmxlcygpIHsKICAgICAgcmV0dXJuIF92YXJpYWJsZXMyLmRlZmF1bHQ7CiAgICB9LAogICAgaXNDb2xsYXBzZTogZnVuY3Rpb24gaXNDb2xsYXBzZSgpIHsKICAgICAgcmV0dXJuICF0aGlzLnNpZGViYXIub3BlbmVkOwogICAgfQogIH0pCn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "names": ["_vuex", "require", "_Logo", "_interopRequireDefault", "_SidebarItem", "_variables2", "components", "SidebarItem", "Logo", "computed", "_objectSpread2", "default", "mapState", "mapGetters", "activeMenu", "route", "$route", "meta", "path", "showLogo", "$store", "state", "settings", "sidebarLogo", "variables", "isCollapse", "sidebar", "opened", "exports", "_default"], "sources": ["src/layout/components/Sidebar/index.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"{'has-logo':showLogo}\"\r\n       :style=\"{ backgroundColor: settings.sideTheme == 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\r\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\"/>\r\n    <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\r\n      <el-menu\r\n        :active-text-color=\"settings.theme\"\r\n        :background-color=\"settings.sideTheme == 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"\r\n        :collapse=\"isCollapse\"\r\n        :collapse-transition=\"false\"\r\n        :default-active=\"activeMenu\"\r\n        :text-color=\"settings.sideTheme == 'theme-dark' ? variables.menuColor : variables.menuLightColor\"\r\n        mode=\"vertical\"\r\n        unique-opened\r\n      >\r\n        <sidebar-item\r\n          v-for=\"(route, index) in sidebarRouters\"\r\n          :key=\"route.path  + index\"\r\n          :base-path=\"route.path\"\r\n          :item=\"route\"\r\n        />\r\n      </el-menu>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {mapGetters, mapState} from \"vuex\";\r\nimport Logo from \"./Logo\";\r\nimport SidebarItem from \"./SidebarItem\";\r\nimport variables from \"@/assets/styles/variables.scss\";\r\n\r\nexport default {\r\n  components: {SidebarItem, Logo},\r\n  computed: {\r\n    ...mapState([\"settings\"]),\r\n    ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\r\n    activeMenu() {\r\n      const route = this.$route;\r\n      const {meta, path} = route;\r\n      // if set path, the sidebar will highlight the path you set\r\n      if (meta.activeMenu) {\r\n        return meta.activeMenu;\r\n      }\r\n      return path;\r\n    },\r\n    showLogo() {\r\n      return this.$store.state.settings.sidebarLogo;\r\n    },\r\n    variables() {\r\n      return variables;\r\n    },\r\n    isCollapse() {\r\n      return !this.sidebar.opened;\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;AA2BA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,WAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,UAAA;IAAAC,WAAA,EAAAA,oBAAA;IAAAC,IAAA,EAAAA;EAAA;EACAC,QAAA,MAAAC,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACA,IAAAC,cAAA,kBACA,IAAAC,gBAAA;IACAC,UAAA,WAAAA,WAAA;MACA,IAAAC,KAAA,QAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,KAAA,CAAAE,IAAA;QAAAC,IAAA,GAAAH,KAAA,CAAAG,IAAA;MACA;MACA,IAAAD,IAAA,CAAAH,UAAA;QACA,OAAAG,IAAA,CAAAH,UAAA;MACA;MACA,OAAAI,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAC,WAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAA,mBAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,aAAAC,OAAA,CAAAC,MAAA;IACA;EAAA;AAEA;AAAAC,OAAA,CAAAjB,OAAA,GAAAkB,QAAA"}]}