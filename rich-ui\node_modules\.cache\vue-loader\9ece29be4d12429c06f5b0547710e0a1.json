{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\result.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\result.vue", "mtime": 1754876882527}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON><PERSON><PERSON>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"}, {"version": 3, "sources": ["result.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "result.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n  <div class=\"popup-result\">\r\n    <p class=\"title\">最近5次运行时间</p>\r\n    <ul class=\"popup-result-scroll\">\r\n      <template v-if='isShow'>\r\n        <li v-for='item in resultList' :key=\"item\">{{ item }}</li>\r\n      </template>\r\n      <li v-else>计算结果中...</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dayRule: '',\r\n      dayRuleSup: '',\r\n      dateArr: [],\r\n      resultList: [],\r\n      isShow: false\r\n    }\r\n  },\r\n  name: 'crontab-result',\r\n  methods: {\r\n    // 表达式值变化时，开始去计算结果\r\n    expressionChange() {\r\n\r\n      // 计算开始-隐藏结果\r\n      this.isShow = false;\r\n      // 获取规则数组[0秒、1分、2时、3日、4月、5星期、6年]\r\n      let ruleArr = this.$options.propsData.ex.split(' ');\r\n      // 用于记录进入循环的次数\r\n      let nums = 0;\r\n      // 用于暂时存符号时间规则结果的数组\r\n      let resultArr = [];\r\n      // 获取当前时间精确至[年、月、日、时、分、秒]\r\n      let nTime = new Date();\r\n      let nYear = nTime.getFullYear();\r\n      let nMonth = nTime.getMonth() + 1;\r\n      let nDay = nTime.getDate();\r\n      let nHour = nTime.getHours();\r\n      let nMin = nTime.getMinutes();\r\n      let nSecond = nTime.getSeconds();\r\n      // 根据规则获取到近100年可能年数组、月数组等等\r\n      this.getSecondArr(ruleArr[0]);\r\n      this.getMinArr(ruleArr[1]);\r\n      this.getHourArr(ruleArr[2]);\r\n      this.getDayArr(ruleArr[3]);\r\n      this.getMonthArr(ruleArr[4]);\r\n      this.getWeekArr(ruleArr[5]);\r\n      this.getYearArr(ruleArr[6], nYear);\r\n      // 将获取到的数组赋值-方便使用\r\n      let sDate = this.dateArr[0];\r\n      let mDate = this.dateArr[1];\r\n      let hDate = this.dateArr[2];\r\n      let DDate = this.dateArr[3];\r\n      let MDate = this.dateArr[4];\r\n      let YDate = this.dateArr[5];\r\n      // 获取当前时间在数组中的索引\r\n      let sIdx = this.getIndex(sDate, nSecond);\r\n      let mIdx = this.getIndex(mDate, nMin);\r\n      let hIdx = this.getIndex(hDate, nHour);\r\n      let DIdx = this.getIndex(DDate, nDay);\r\n      let MIdx = this.getIndex(MDate, nMonth);\r\n      let YIdx = this.getIndex(YDate, nYear);\r\n      // 重置月日时分秒的函数(后面用的比较多)\r\n      const resetSecond = function () {\r\n        sIdx = 0;\r\n        nSecond = sDate[sIdx]\r\n      }\r\n      const resetMin = function () {\r\n        mIdx = 0;\r\n        nMin = mDate[mIdx]\r\n        resetSecond();\r\n      }\r\n      const resetHour = function () {\r\n        hIdx = 0;\r\n        nHour = hDate[hIdx]\r\n        resetMin();\r\n      }\r\n      const resetDay = function () {\r\n        DIdx = 0;\r\n        nDay = DDate[DIdx]\r\n        resetHour();\r\n      }\r\n      const resetMonth = function () {\r\n        MIdx = 0;\r\n        nMonth = MDate[MIdx]\r\n        resetDay();\r\n      }\r\n      // 如果当前年份不为数组中当前值\r\n      if (nYear != YDate[YIdx]) {\r\n        resetMonth();\r\n      }\r\n      // 如果当前月份不为数组中当前值\r\n      if (nMonth != MDate[MIdx]) {\r\n        resetDay();\r\n      }\r\n      // 如果当前“日”不为数组中当前值\r\n      if (nDay != DDate[DIdx]) {\r\n        resetHour();\r\n      }\r\n      // 如果当前“时”不为数组中当前值\r\n      if (nHour != hDate[hIdx]) {\r\n        resetMin();\r\n      }\r\n      // 如果当前“分”不为数组中当前值\r\n      if (nMin != mDate[mIdx]) {\r\n        resetSecond();\r\n      }\r\n\r\n      // 循环年份数组\r\n      goYear: for (let Yi = YIdx; Yi < YDate.length; Yi++) {\r\n        let YY = YDate[Yi];\r\n        // 如果到达最大值时\r\n        if (nMonth > MDate[MDate.length - 1]) {\r\n          resetMonth();\r\n          continue;\r\n        }\r\n        // 循环月份数组\r\n        goMonth: for (let Mi = MIdx; Mi < MDate.length; Mi++) {\r\n          // 赋值、方便后面运算\r\n          let MM = MDate[Mi];\r\n          MM = MM < 10 ? '0' + MM : MM;\r\n          // 如果到达最大值时\r\n          if (nDay > DDate[DDate.length - 1]) {\r\n            resetDay();\r\n            if (Mi == MDate.length - 1) {\r\n              resetMonth();\r\n              continue goYear;\r\n            }\r\n            continue;\r\n          }\r\n          // 循环日期数组\r\n          goDay: for (let Di = DIdx; Di < DDate.length; Di++) {\r\n            // 赋值、方便后面运算\r\n            let DD = DDate[Di];\r\n            let thisDD = DD < 10 ? '0' + DD : DD;\r\n\r\n            // 如果到达最大值时\r\n            if (nHour > hDate[hDate.length - 1]) {\r\n              resetHour();\r\n              if (Di == DDate.length - 1) {\r\n                resetDay();\r\n                if (Mi == MDate.length - 1) {\r\n                  resetMonth();\r\n                  continue goYear;\r\n                }\r\n                continue goMonth;\r\n              }\r\n              continue;\r\n            }\r\n\r\n            // 判断日期的合法性，不合法的话也是跳出当前循环\r\n            if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true && this.dayRule != 'workDay' && this.dayRule != 'lastWeek' && this.dayRule != 'lastDay') {\r\n              resetDay();\r\n              continue goMonth;\r\n            }\r\n            // 如果日期规则中有值时\r\n            if (this.dayRule == 'lastDay') {\r\n              // 如果不是合法日期则需要将前将日期调到合法日期即月末最后一天\r\n\r\n              if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                while (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD--;\r\n\r\n                  thisDD = DD < 10 ? '0' + DD : DD;\r\n                }\r\n              }\r\n            } else if (this.dayRule == 'workDay') {\r\n              // 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\r\n              if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                while (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD--;\r\n                  thisDD = DD < 10 ? '0' + DD : DD;\r\n                }\r\n              }\r\n              // 获取达到条件的日期是星期X\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\r\n              // 当星期日时\r\n              if (thisWeek == 1) {\r\n                // 先找下一个日，并判断是否为月底\r\n                DD++;\r\n                thisDD = DD < 10 ? '0' + DD : DD;\r\n                // 判断下一日已经不是合法日期\r\n                if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD -= 3;\r\n                }\r\n              } else if (thisWeek == 7) {\r\n                // 当星期6时只需判断不是1号就可进行操作\r\n                if (this.dayRuleSup != 1) {\r\n                  DD--;\r\n                } else {\r\n                  DD += 2;\r\n                }\r\n              }\r\n            } else if (this.dayRule == 'weekDay') {\r\n              // 如果指定了是星期几\r\n              // 获取当前日期是属于星期几\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\r\n              // 校验当前星期是否在星期池（dayRuleSup）中\r\n              if (this.dayRuleSup.indexOf(thisWeek) < 0) {\r\n                // 如果到达最大值时\r\n                if (Di == DDate.length - 1) {\r\n                  resetDay();\r\n                  if (Mi == MDate.length - 1) {\r\n                    resetMonth();\r\n                    continue goYear;\r\n                  }\r\n                  continue goMonth;\r\n                }\r\n                continue;\r\n              }\r\n            } else if (this.dayRule == 'assWeek') {\r\n              // 如果指定了是第几周的星期几\r\n              // 获取每月1号是属于星期几\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + DD + ' 00:00:00'), 'week');\r\n              if (this.dayRuleSup[1] >= thisWeek) {\r\n                DD = (this.dayRuleSup[0] - 1) * 7 + this.dayRuleSup[1] - thisWeek + 1;\r\n              } else {\r\n                DD = this.dayRuleSup[0] * 7 + this.dayRuleSup[1] - thisWeek + 1;\r\n              }\r\n            } else if (this.dayRule == 'lastWeek') {\r\n              // 如果指定了每月最后一个星期几\r\n              // 校验并调整如果是2月30号这种日期传进来时需调整至正常月底\r\n              if (this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                while (DD > 0 && this.checkDate(YY + '-' + MM + '-' + thisDD + ' 00:00:00') != true) {\r\n                  DD--;\r\n                  thisDD = DD < 10 ? '0' + DD : DD;\r\n                }\r\n              }\r\n              // 获取月末最后一天是星期几\r\n              let thisWeek = this.formatDate(new Date(YY + '-' + MM + '-' + thisDD + ' 00:00:00'), 'week');\r\n              // 找到要求中最近的那个星期几\r\n              if (this.dayRuleSup < thisWeek) {\r\n                DD -= thisWeek - this.dayRuleSup;\r\n              } else if (this.dayRuleSup > thisWeek) {\r\n                DD -= 7 - (this.dayRuleSup - thisWeek)\r\n              }\r\n            }\r\n            // 判断时间值是否小于10置换成“05”这种格式\r\n            DD = DD < 10 ? '0' + DD : DD;\r\n\r\n            // 循环“时”数组\r\n            goHour: for (let hi = hIdx; hi < hDate.length; hi++) {\r\n              let hh = hDate[hi] < 10 ? '0' + hDate[hi] : hDate[hi]\r\n\r\n              // 如果到达最大值时\r\n              if (nMin > mDate[mDate.length - 1]) {\r\n                resetMin();\r\n                if (hi == hDate.length - 1) {\r\n                  resetHour();\r\n                  if (Di == DDate.length - 1) {\r\n                    resetDay();\r\n                    if (Mi == MDate.length - 1) {\r\n                      resetMonth();\r\n                      continue goYear;\r\n                    }\r\n                    continue goMonth;\r\n                  }\r\n                  continue goDay;\r\n                }\r\n                continue;\r\n              }\r\n              // 循环\"分\"数组\r\n              goMin: for (let mi = mIdx; mi < mDate.length; mi++) {\r\n                let mm = mDate[mi] < 10 ? '0' + mDate[mi] : mDate[mi];\r\n\r\n                // 如果到达最大值时\r\n                if (nSecond > sDate[sDate.length - 1]) {\r\n                  resetSecond();\r\n                  if (mi == mDate.length - 1) {\r\n                    resetMin();\r\n                    if (hi == hDate.length - 1) {\r\n                      resetHour();\r\n                      if (Di == DDate.length - 1) {\r\n                        resetDay();\r\n                        if (Mi == MDate.length - 1) {\r\n                          resetMonth();\r\n                          continue goYear;\r\n                        }\r\n                        continue goMonth;\r\n                      }\r\n                      continue goDay;\r\n                    }\r\n                    continue goHour;\r\n                  }\r\n                  continue;\r\n                }\r\n                // 循环\"秒\"数组\r\n                for (let si = sIdx; si <= sDate.length - 1; si++) {\r\n                  let ss = sDate[si] < 10 ? '0' + sDate[si] : sDate[si];\r\n                  // 添加当前时间（时间合法性在日期循环时已经判断）\r\n                  if (MM != '00' && DD != '00') {\r\n                    resultArr.push(YY + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss)\r\n                    nums++;\r\n                  }\r\n                  // 如果条数满了就退出循环\r\n                  if (nums == 5) break goYear;\r\n                  // 如果到达最大值时\r\n                  if (si == sDate.length - 1) {\r\n                    resetSecond();\r\n                    if (mi == mDate.length - 1) {\r\n                      resetMin();\r\n                      if (hi == hDate.length - 1) {\r\n                        resetHour();\r\n                        if (Di == DDate.length - 1) {\r\n                          resetDay();\r\n                          if (Mi == MDate.length - 1) {\r\n                            resetMonth();\r\n                            continue goYear;\r\n                          }\r\n                          continue goMonth;\r\n                        }\r\n                        continue goDay;\r\n                      }\r\n                      continue goHour;\r\n                    }\r\n                    continue goMin;\r\n                  }\r\n                } //goSecond\r\n              } //goMin\r\n            }//goHour\r\n          }//goDay\r\n        }//goMonth\r\n      }\r\n      // 判断100年内的结果条数\r\n      if (resultArr.length == 0) {\r\n        this.resultList = ['没有达到条件的结果！'];\r\n      } else {\r\n        this.resultList = resultArr;\r\n        if (resultArr.length != 5) {\r\n          this.resultList.push('最近100年内只有上面' + resultArr.length + '条结果！')\r\n        }\r\n      }\r\n      // 计算完成-显示结果\r\n      this.isShow = true;\r\n\r\n\r\n    },\r\n    // 用于计算某位数字在数组中的索引\r\n    getIndex(arr, value) {\r\n      if (value <= arr[0] || value > arr[arr.length - 1]) {\r\n        return 0;\r\n      } else {\r\n        for (let i = 0; i < arr.length - 1; i++) {\r\n          if (value > arr[i] && value <= arr[i + 1]) {\r\n            return i + 1;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 获取\"年\"数组\r\n    getYearArr(rule, year) {\r\n      this.dateArr[5] = this.getOrderArr(year, year + 100);\r\n      if (rule != undefined) {\r\n        if (rule.indexOf('-') >= 0) {\r\n          this.dateArr[5] = this.getCycleArr(rule, year + 100, false)\r\n        } else if (rule.indexOf('/') >= 0) {\r\n          this.dateArr[5] = this.getAverageArr(rule, year + 100)\r\n        } else if (rule != '*') {\r\n          this.dateArr[5] = this.getAssignArr(rule)\r\n        }\r\n      }\r\n    },\r\n    // 获取\"月\"数组\r\n    getMonthArr(rule) {\r\n      this.dateArr[4] = this.getOrderArr(1, 12);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[4] = this.getCycleArr(rule, 12, false)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[4] = this.getAverageArr(rule, 12)\r\n      } else if (rule != '*') {\r\n        this.dateArr[4] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 获取\"日\"数组-主要为日期规则\r\n    getWeekArr(rule) {\r\n      // 只有当日期规则的两个值均为“”时则表达日期是有选项的\r\n      if (this.dayRule == '' && this.dayRuleSup == '') {\r\n        if (rule.indexOf('-') >= 0) {\r\n          this.dayRule = 'weekDay';\r\n          this.dayRuleSup = this.getCycleArr(rule, 7, false)\r\n        } else if (rule.indexOf('#') >= 0) {\r\n          this.dayRule = 'assWeek';\r\n          let matchRule = rule.match(/[0-9]{1}/g);\r\n          this.dayRuleSup = [Number(matchRule[1]), Number(matchRule[0])];\r\n          this.dateArr[3] = [1];\r\n          if (this.dayRuleSup[1] == 7) {\r\n            this.dayRuleSup[1] = 0;\r\n          }\r\n        } else if (rule.indexOf('L') >= 0) {\r\n          this.dayRule = 'lastWeek';\r\n          this.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\r\n          this.dateArr[3] = [31];\r\n          if (this.dayRuleSup == 7) {\r\n            this.dayRuleSup = 0;\r\n          }\r\n        } else if (rule != '*' && rule != '?') {\r\n          this.dayRule = 'weekDay';\r\n          this.dayRuleSup = this.getAssignArr(rule)\r\n        }\r\n      }\r\n    },\r\n    // 获取\"日\"数组-少量为日期规则\r\n    getDayArr(rule) {\r\n      this.dateArr[3] = this.getOrderArr(1, 31);\r\n      this.dayRule = '';\r\n      this.dayRuleSup = '';\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[3] = this.getCycleArr(rule, 31, false)\r\n        this.dayRuleSup = 'null';\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[3] = this.getAverageArr(rule, 31)\r\n        this.dayRuleSup = 'null';\r\n      } else if (rule.indexOf('W') >= 0) {\r\n        this.dayRule = 'workDay';\r\n        this.dayRuleSup = Number(rule.match(/[0-9]{1,2}/g)[0]);\r\n        this.dateArr[3] = [this.dayRuleSup];\r\n      } else if (rule.indexOf('L') >= 0) {\r\n        this.dayRule = 'lastDay';\r\n        this.dayRuleSup = 'null';\r\n        this.dateArr[3] = [31];\r\n      } else if (rule != '*' && rule != '?') {\r\n        this.dateArr[3] = this.getAssignArr(rule)\r\n        this.dayRuleSup = 'null';\r\n      } else if (rule == '*') {\r\n        this.dayRuleSup = 'null';\r\n      }\r\n    },\r\n    // 获取\"时\"数组\r\n    getHourArr(rule) {\r\n      this.dateArr[2] = this.getOrderArr(0, 23);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[2] = this.getCycleArr(rule, 24, true)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[2] = this.getAverageArr(rule, 23)\r\n      } else if (rule != '*') {\r\n        this.dateArr[2] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 获取\"分\"数组\r\n    getMinArr(rule) {\r\n      this.dateArr[1] = this.getOrderArr(0, 59);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[1] = this.getCycleArr(rule, 60, true)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[1] = this.getAverageArr(rule, 59)\r\n      } else if (rule != '*') {\r\n        this.dateArr[1] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 获取\"秒\"数组\r\n    getSecondArr(rule) {\r\n      this.dateArr[0] = this.getOrderArr(0, 59);\r\n      if (rule.indexOf('-') >= 0) {\r\n        this.dateArr[0] = this.getCycleArr(rule, 60, true)\r\n      } else if (rule.indexOf('/') >= 0) {\r\n        this.dateArr[0] = this.getAverageArr(rule, 59)\r\n      } else if (rule != '*') {\r\n        this.dateArr[0] = this.getAssignArr(rule)\r\n      }\r\n    },\r\n    // 根据传进来的min-max返回一个顺序的数组\r\n    getOrderArr(min, max) {\r\n      let arr = [];\r\n      for (let i = min; i <= max; i++) {\r\n        arr.push(i);\r\n      }\r\n      return arr;\r\n    },\r\n    // 根据规则中指定的零散值返回一个数组\r\n    getAssignArr(rule) {\r\n      let arr = [];\r\n      let assiginArr = rule.split(',');\r\n      for (let i = 0; i < assiginArr.length; i++) {\r\n        arr[i] = Number(assiginArr[i])\r\n      }\r\n      arr.sort(this.compare)\r\n      return arr;\r\n    },\r\n    // 根据一定算术规则计算返回一个数组\r\n    getAverageArr(rule, limit) {\r\n      let arr = [];\r\n      let agArr = rule.split('/');\r\n      let min = Number(agArr[0]);\r\n      let step = Number(agArr[1]);\r\n      while (min <= limit) {\r\n        arr.push(min);\r\n        min += step;\r\n      }\r\n      return arr;\r\n    },\r\n    // 根据规则返回一个具有周期性的数组\r\n    getCycleArr(rule, limit, status) {\r\n      // status--表示是否从0开始（则从1开始）\r\n      let arr = [];\r\n      let cycleArr = rule.split('-');\r\n      let min = Number(cycleArr[0]);\r\n      let max = Number(cycleArr[1]);\r\n      if (min > max) {\r\n        max += limit;\r\n      }\r\n      for (let i = min; i <= max; i++) {\r\n        let add = 0;\r\n        if (status == false && i % limit == 0) {\r\n          add = limit;\r\n        }\r\n        arr.push(Math.round(i % limit + add))\r\n      }\r\n      arr.sort(this.compare)\r\n      return arr;\r\n    },\r\n    // 比较数字大小（用于Array.sort）\r\n    compare(value1, value2) {\r\n      if (value2 - value1 > 0) {\r\n        return -1;\r\n      } else {\r\n        return 1;\r\n      }\r\n    },\r\n    // 格式化日期格式如：2017-9-19 18:04:33\r\n    formatDate(value, type) {\r\n      // 计算日期相关值\r\n      let time = typeof value == 'number' ? new Date(value) : value;\r\n      let Y = time.getFullYear();\r\n      let M = time.getMonth() + 1;\r\n      let D = time.getDate();\r\n      let h = time.getHours();\r\n      let m = time.getMinutes();\r\n      let s = time.getSeconds();\r\n      let week = time.getDay();\r\n      // 如果传递了type的话\r\n      if (type == undefined) {\r\n        return Y + '-' + (M < 10 ? '0' + M : M) + '-' + (D < 10 ? '0' + D : D) + ' ' + (h < 10 ? '0' + h : h) + ':' + (m < 10 ? '0' + m : m) + ':' + (s < 10 ? '0' + s : s);\r\n      } else if (type == 'week') {\r\n        // 在quartz中 1为星期日\r\n        return week + 1;\r\n      }\r\n    },\r\n    // 检查日期是否存在\r\n    checkDate(value) {\r\n      let time = new Date(value);\r\n      let format = this.formatDate(time)\r\n      return value == format;\r\n    }\r\n  },\r\n  watch: {\r\n    'ex': 'expressionChange'\r\n  },\r\n  props: ['ex'],\r\n  mounted: function () {\r\n    // 初始化 获取一次结果\r\n    this.expressionChange();\r\n  }\r\n}\r\n\r\n</script>\r\n"]}]}