{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\HeaderSearch\\index.vue?vue&type=template&id=032bd1f0&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\HeaderSearch\\index.vue", "mtime": 1754876882530}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:dmFyIHJlbmRlciA9IGZ1bmN0aW9uICgpIHsKICB2YXIgX3ZtID0gdGhpcwogIHZhciBfaCA9IF92bS4kY3JlYXRlRWxlbWVudAogIHZhciBfYyA9IF92bS5fc2VsZi5fYyB8fCBfaAogIHJldHVybiBfYygKICAgICJkaXYiLAogICAgeyBzdGF0aWNDbGFzczogImhlYWRlci1zZWFyY2giLCBjbGFzczogeyBzaG93OiBfdm0uc2hvdyB9IH0sCiAgICBbCiAgICAgIF9jKCJzdmctaWNvbiIsIHsKICAgICAgICBhdHRyczogeyAiY2xhc3MtbmFtZSI6ICJzZWFyY2gtaWNvbiIsICJpY29uLWNsYXNzIjogInNlYXJjaCIgfSwKICAgICAgICBvbjogewogICAgICAgICAgY2xpY2s6IGZ1bmN0aW9uICgkZXZlbnQpIHsKICAgICAgICAgICAgJGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpCiAgICAgICAgICAgIHJldHVybiBfdm0uY2xpY2soJGV2ZW50KQogICAgICAgICAgfSwKICAgICAgICB9LAogICAgICB9KSwKICAgICAgX2MoCiAgICAgICAgImVsLXNlbGVjdCIsCiAgICAgICAgewogICAgICAgICAgcmVmOiAiaGVhZGVyU2VhcmNoU2VsZWN0IiwKICAgICAgICAgIHN0YXRpY0NsYXNzOiAiaGVhZGVyLXNlYXJjaC1zZWxlY3QiLAogICAgICAgICAgYXR0cnM6IHsKICAgICAgICAgICAgInJlbW90ZS1tZXRob2QiOiBfdm0ucXVlcnlTZWFyY2gsCiAgICAgICAgICAgICJkZWZhdWx0LWZpcnN0LW9wdGlvbiI6ICIiLAogICAgICAgICAgICBmaWx0ZXJhYmxlOiAiIiwKICAgICAgICAgICAgcGxhY2Vob2xkZXI6ICJTZWFyY2giLAogICAgICAgICAgICByZW1vdGU6ICIiLAogICAgICAgICAgfSwKICAgICAgICAgIG9uOiB7IGNoYW5nZTogX3ZtLmNoYW5nZSB9LAogICAgICAgICAgbW9kZWw6IHsKICAgICAgICAgICAgdmFsdWU6IF92bS5zZWFyY2gsCiAgICAgICAgICAgIGNhbGxiYWNrOiBmdW5jdGlvbiAoJCR2KSB7CiAgICAgICAgICAgICAgX3ZtLnNlYXJjaCA9ICQkdgogICAgICAgICAgICB9LAogICAgICAgICAgICBleHByZXNzaW9uOiAic2VhcmNoIiwKICAgICAgICAgIH0sCiAgICAgICAgfSwKICAgICAgICBfdm0uX2woX3ZtLm9wdGlvbnMsIGZ1bmN0aW9uIChvcHRpb24pIHsKICAgICAgICAgIHJldHVybiBfYygiZWwtb3B0aW9uIiwgewogICAgICAgICAgICBrZXk6IG9wdGlvbi5pdGVtLnBhdGgsCiAgICAgICAgICAgIGF0dHJzOiB7IGxhYmVsOiBvcHRpb24uaXRlbS50aXRsZS5qb2luKCIgPiAiKSwgdmFsdWU6IG9wdGlvbi5pdGVtIH0sCiAgICAgICAgICB9KQogICAgICAgIH0pLAogICAgICAgIDEKICAgICAgKSwKICAgIF0sCiAgICAxCiAgKQp9CnZhciBzdGF0aWNSZW5kZXJGbnMgPSBbXQpyZW5kZXIuX3dpdGhTdHJpcHBlZCA9IHRydWUKCmV4cG9ydCB7IHJlbmRlciwgc3RhdGljUmVuZGVyRm5zIH0="}]}