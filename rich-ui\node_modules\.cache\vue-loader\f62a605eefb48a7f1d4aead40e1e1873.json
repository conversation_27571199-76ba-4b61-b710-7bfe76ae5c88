{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\validTime.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\validTime.vue", "mtime": 1754876882589}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJ2YWxpZFRpbWUiLA0KICBwcm9wczogWydzY29wZSddLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzaXplOiB0aGlzLiRzdG9yZS5zdGF0ZS5hcHAuc2l6ZSB8fCAnbWluaScsDQogICAgfQ0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["validTime.vue"], "names": [], "mappings": ";;;;;;;;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "validTime.vue", "sourceRoot": "src/views/system/freight", "sourcesContent": ["<template>\r\n  <div>\r\n    <h6 style=\"margin: 0\">{{parseTime(scope.row.validFrom, '{m}.{d}') }}-{{ parseTime(scope.row.validTo, '{m}.{d}') }}</h6>\r\n    <h6 style=\"margin: 0\">{{ scope.row.validPeriodTimeNode }}</h6>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"validTime\",\r\n  props: ['scope'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini',\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}