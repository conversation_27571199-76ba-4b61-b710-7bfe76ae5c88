{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\regularBankRecord.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\regularBankRecord.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_bankrecord", "require", "_index", "_interopRequireDefault", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_store", "_currency", "_rsCharge", "_exchangerate", "_rich", "_log", "_moment", "_request", "name", "components", "Treeselect", "CompanySelect", "data", "writeOffList", "salesId", "belongList", "staffList", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "bankrecordList", "title", "open", "queryParams", "pageNum", "pageSize", "isRecievingOrPaying", "sqdPaymentTitleCode", "bankAccountCode", "clearingCompanyId", "sqdClearingCompanyShortname", "chargeTypeId", "chargeDescription", "bankCurrencyCode", "actualBankRecievedAmount", "actualBankPaidAmount", "bankRecievedHandlingFee", "bankPaidHandlingFee", "bankRecievedExchangeLost", "bankPaidExchangeLost", "sqdBillRecievedAmount", "sqdBillPaidAmount", "billRecievedWriteoffAmount", "billPaidWriteoffAmount", "sqdBillRecievedWriteoffBalance", "sqdBillPaidWriteoffBalance", "writeoffStatus", "bankRecordTime", "paymentTypeCode", "voucherNo", "bankRecordRemark", "bankRecordByStaffId", "bankRecordUpdateTime", "isBankRecordLocked", "isWriteoffLocked", "sqdChargeIdList", "sqdRaletiveRctList", "sqdRaletiveInvoiceList", "sqdRsStaffId", "writeoffRemark", "writeoffStaffId", "writeoffTime", "form", "rules", "required", "message", "trigger", "add", "showDetail", "selected<PERSON><PERSON>ges", "totalAmount", "selectedAmount", "selectedBalanceAmount", "loadingCharge", "staffId", "alreadyWriteoffList", "turnBackWriteoffList", "showCompany", "companyList", "bankSlipPreview", "imageFile", "beforeMount", "loadStaff", "watch", "n", "formActualBankRecievedAmount", "currency", "value", "formBankRecievedHandlingFee", "formBankRecievedExchangeLost", "subtract", "formActualBankPaidAmount", "formBankPaidHandlingFee", "formBankPaidExchangeLost", "formSqdBillRecievedAmount", "formSqdBillPaidAmount", "formBillRecievedWriteoffAmount", "formBillPaidWriteoffAmount", "handler", "newVal", "oldVal", "_this", "map", "item", "writeoffFromDnBalance", "writeoffFromBankBalance", "divide", "exchangeRate", "sqdDnCurrencyBalance", "dnUnitRate", "multiply", "dnAmount", "deep", "created", "getList", "loadSales", "getExchangeRate", "val", "console", "log", "computed", "receiveRate", "paidRate", "isLocked", "isBankSlipConfirmed", "slipConfirmed", "methods", "handleSearch", "type", "parseTime", "updateSlipSatus", "_this2", "updateBankrecord", "then", "response", "$message", "success", "writeOffConfirm", "_this3", "clearReceiveOrPay", "rctSet", "Set", "sqdRctNo", "rctArr", "for<PERSON>ach", "push", "toString", "moment", "format", "$modal", "msgSuccess", "turnBackWriteoff", "rsChargeList", "midChargeBankWriteoffs", "clearingCurrencyCode", "midChargeBankWriteoff", "chargeId", "bankRecordId", "exchangeRateShowing", "exchangeRateShow", "dnBasicRate", "chargeWriteOff", "midChargeBankWriteoffList", "invertSelection", "_this4", "row", "$refs", "writeOffTable", "toggleRowSelection", "autoSelection", "addHedging", "projectRemove", "print", "getCompanyCharges", "_this5", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "stop", "verify", "_this6", "verifyId", "verifyTime", "$store", "state", "user", "sid", "_this7", "listBankrecord", "rows", "cancel", "reset", "invoiceNo", "chargeType", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this8", "text", "status", "$confirm", "changeStatus", "catch", "handleSelectionChange", "selection", "_this9", "length", "indexOf", "sqdDnCurrencyBalanceShow", "handleAdd", "handleUpdate", "_this10", "getBankrecord", "submitForm", "_this11", "validate", "_ref", "_callee2", "valid", "_callee2$", "_context2", "addBankrecord", "_x", "apply", "arguments", "uploadImage", "_this12", "Promise", "resolve", "reject", "customHttpRequest", "file", "onSuccess", "handleSuccess", "onError", "error", "handleError", "handleDelete", "_this13", "bankRecordIds", "delBankrecord", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "staffNormalizer", "node", "children", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "id", "label", "isDisabled", "undefined", "deptId", "_this14", "salesList", "redisList", "store", "dispatch", "_this15", "allRsStaffList", "handledbClick", "selectCompany", "company", "companyId", "companyShortName", "overseaCurrency", "localCurrency", "valueDate", "_callee3", "re", "_iterator", "_step", "a", "_callee3$", "_context3", "selectListExchangerate", "sent", "_createForOfIteratorHelper2", "s", "done", "validFrom", "validTo", "buyRate", "base", "sellRate", "err", "e", "f", "abrupt", "getBillDataExchangeRate", "_callee4", "_iterator2", "_step2", "_callee4$", "_context4", "chargeCurrencyCode", "_this16", "_callee5", "result", "_callee5$", "_context5", "getName", "filter", "rsStaff", "staffShortName", "selectStaff", "checkSelectable", "isAccountConfirmed", "handleDialogOpened", "_this17", "$nextTick", "treeSelectInput", "treeSelect", "getInputElement", "focus", "isRecievingOrPayingNormalizer", "selectBankAccount", "sqdBelongToCompanyCode", "options", "_this18", "formData", "FormData", "append", "request", "url", "method", "slipFile", "handleChange", "fileList", "extension", "substring", "lastIndexOf", "newFileName", "bankRecordNo", "File", "raw", "exports", "_default"], "sources": ["src/views/system/bankrecord/regularBankRecord.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n            <el-input\r\n              v-model=\"queryParams.isRecievingOrPaying\"\r\n              clearable\r\n              placeholder=\"收支标志\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属公司\" prop=\"sqdPaymentTitleCode\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdPaymentTitleCode\"\r\n              clearable\r\n              placeholder=\"所属公司\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankAccountCode\"\r\n              clearable\r\n              placeholder=\"银行账户\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算公司\" prop=\"sqdClearingCompanyShortname\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdClearingCompanyShortname\"\r\n              clearable\r\n              placeholder=\"结算公司简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"费用描述\" prop=\"chargeDescription\">\r\n            <el-input\r\n              v-model=\"queryParams.chargeDescription\"\r\n              clearable\r\n              placeholder=\"费用描述\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行币种\" prop=\"bankCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankCurrencyCode\"\r\n              clearable\r\n              placeholder=\"银行币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"结算方式\" prop=\"paymentTypeCode\">\r\n            <el-input\r\n              v-model=\"queryParams.paymentTypeCode\"\r\n              clearable\r\n              placeholder=\"结算方式\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"凭证号\" prop=\"voucherNo\">\r\n            <el-input\r\n              v-model=\"queryParams.voucherNo\"\r\n              clearable\r\n              placeholder=\"凭证号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"流水备注\" prop=\"bankRecordRemark\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordRemark\"\r\n              clearable\r\n              placeholder=\"银行流水备注\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"录入人\" prop=\"bankRecordByStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordByStaffId\"\r\n              clearable\r\n              placeholder=\"银行流水录入人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"录入时间 ,\" prop=\"bankRecordUpdateTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordUpdateTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水录入时间 ,\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属员工\" prop=\"sqdRsStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdRsStaffId\"\r\n              clearable\r\n              placeholder=\"所属员工\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.writeoffStaffId\"\r\n              clearable\r\n              placeholder=\"销账人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账时间\" prop=\"writeoffTime\">\r\n            <el-date-picker v-model=\"queryParams.writeoffTime\"\r\n                            clearable\r\n                            placeholder=\"销账时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:edit']\"\r\n              :disabled=\"single\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"bankrecordList\" highlight-current-row\r\n                  stripe @selection-change=\"handleSelectionChange\" @row-dblclick=\"handledbClick\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"银行流水\" prop=\"bankRecordNo\"/>\r\n          <el-table-column align=\"center\" label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.isRecievingOrPaying == 1 ? \"付\" : \"收\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"sqdPaymentTitleCode\"/>\r\n          <el-table-column align=\"center\" label=\"结算公司\" prop=\"sqdClearingCompanyShortname\"/>\r\n          <el-table-column align=\"center\" label=\"银行时间\" prop=\"bankRecordTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.bankRecordTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"银行账户\" prop=\"bankAccountCode\"/>\r\n          <el-table-column align=\"center\" label=\"水单金额\" prop=\"slipAmount\"/>\r\n          <el-table-column align=\"center\" label=\"实收金额\" prop=\"actualBankRecievedAmount\"/>\r\n          <el-table-column align=\"center\" label=\"收款手续费\" prop=\"bankRecievedHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"实付金额\" prop=\"actualBankPaidAmount\"/>\r\n          <el-table-column align=\"center\" label=\"付款手续费\" prop=\"bankPaidHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"销账状态 \" prop=\"writeoffStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.writeoffStatus == 1 ? \"=\" : (scope.row.writeoffStatus == 0 ? \"√\" : \"-\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"相关操作单号\" prop=\"sqdRaletiveRctList\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <template slot-scope=\"scope\">\r\n              {{ getName(scope.row.writeoffStaffId) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"销账备注\" prop=\"bankRecordRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账时间\" prop=\"writeoffTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.writeoffTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改记录公司账户出入账明细对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      append-to-body\r\n      height=\"60%\"\r\n      width=\"60%\"\r\n      @close=\"showDetail=false\"\r\n    >\r\n      <el-form v-if=\"open\" ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"12\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"银行流水\" prop=\"voucherNo\">\r\n                <el-input :value=\"form.bankRecordNo\" class=\"disable-form\" disabled placeholder=\"银行流水\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n                <treeselect ref=\"treeSelect\" v-model=\"form.isRecievingOrPaying\"\r\n                            :auto-focus=\"true\"\r\n                            :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\" :disable-branch-nodes=\"true\"\r\n                            :disable-fuzzy-matching=\"true\"\r\n                            :disabled=\"isLocked||isBankSlipConfirmed\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"isRecievingOrPayingNormalizer\"\r\n                            :options=\"[{label:'实收',value:'0'},{label:'实付',value:'1'}]\" :show-count=\"true\"\r\n                            placeholder=\"选择收付信息\" @select=\"form.isRecievingOrPaying=$event.value\"\r\n                >\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n                <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                             :disabled=\"isLocked||isBankSlipConfirmed\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.bankAccountCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\"\r\n                             @return=\"form.bankAccountCode=$event\" @returnData=\"selectBankAccount\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"所属员工\">\r\n                <el-select v-model=\"form.sqdRsStaffId\" :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                           :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                           filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"staff in staffList\"\r\n                    :key=\"staff.staffId\"\r\n                    :label=\"staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                    :value=\"staff.staffId\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"费用描述\" prop=\"voucherNo\">\r\n                <el-row>\r\n                  <el-col :span=\"8\">\r\n                    <tree-select :charge-name-type=\"3\" :pass=\"form.chargeTypeId\" :placeholder=\"'费用类型'\"\r\n                                 :type=\"'chargeNameType'\" style=\"width: 100%\" @return=\"form.chargeTypeId=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.chargeDescription\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                              placeholder=\"费用描述\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.bankRecordTime\"\r\n                  :class=\"isLocked?'disable-form':''\"\r\n                  :disabled=\"isLocked\"\r\n                  clearable\r\n                  default-time=\"12:00:00\"\r\n                  placeholder=\"银行时间\"\r\n                  style=\"width: 100%\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"实收金额\" prop=\"actualBankRecievedAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                                 :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                                 :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankRecievedAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实收金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"手续费\" prop=\"bankRecievedExchangeLost\">\r\n                <el-input v-model=\"form.bankRecievedHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收款记账\" prop=\"sqdBillRecievedAmount\">\r\n                <el-input v-model=\"form.sqdBillRecievedAmount\" :class=\"'disable-form'\" disabled\r\n                          placeholder=\"收款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收账已销\" prop=\"billRecievedWriteoffAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.billRecievedWriteoffAmount\" :class=\"'disable-form'\"\r\n                              disabled placeholder=\"收账已销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-input v-model=\"form.bankRecievedExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                              :disabled=\"!isLocked\" placeholder=\"收款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" :disabled=\"isLocked\" label=\"收账未销\"\r\n                            prop=\"sqdBillRecievedWriteoffBalance\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillRecievedWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"收账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillRecievedWriteoffBalance===form.sqdBillRecievedAmount?'-':form.sqdBillRecievedWriteoffBalance===0?'√':'='\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"实付金额\" prop=\"actualBankPaidAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankPaidAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实付金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"手续费\" prop=\"bankPaidExchangeLost\">\r\n                <el-input v-model=\"form.bankPaidHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付款记账\" prop=\"sqdBillPaidAmount\">\r\n                <el-input v-model=\"form.sqdBillPaidAmount\" class=\"disable-form\" disabled\r\n                          placeholder=\"付款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账已销\" prop=\"billPaidWriteoffAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"16\">\r\n                    <el-input v-model=\"form.billPaidWriteoffAmount\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账已销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"8\">\r\n                    <el-input v-model=\"form.bankPaidExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                              :disabled=\"!isLocked\" placeholder=\"付款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账未销\" prop=\"sqdBillPaidWriteoffBalance\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillPaidWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillPaidAmount===form.sqdBillPaidWriteoffBalance?'-':form.sqdBillPaidWriteoffBalance===0?'√':'='\"\r\n                      placeholder=\"收款损益\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行备注\" prop=\"bankRecordRemark\">\r\n                <el-input v-model=\"form.bankRecordRemark\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"银行备注\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" style=\"float: right\" type=\"primary\"\r\n                         @click=\"submitForm\"\r\n              >{{ this.form.bankRecordId === null ? \"新增\" : \"保存\" }}\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <div style=\"display: flex\">\r\n                <el-button :icon=\"form.isBankRecordLocked==1?'el-icon-check':''\" type=\"primary\"\r\n                           v-if=\"form.bankRecordId\" @click=\"verify\"\r\n                >\r\n                  {{ form.isBankRecordLocked == 1 ? \"已审核\" : \"出账审核\" }}\r\n                </el-button>\r\n                <div v-if=\"form.isBankRecordLocked == 1 \">\r\n                  <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                    {{ (form.verifyId ? getName(form.verifyId) : \"\") }}\r\n                  </div>\r\n                  <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                    {{ parseTime(form.verifyTime, \"{y}-{m}-{d}\") }}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-button :disabled=\"!isLocked\" :loading=\"loadingCharge\" style=\"float: right\" type=\"primary\"\r\n                         @click=\"getCompanyCharges\"\r\n              >\r\n                调取相关费用明细\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table\r\n        v-if=\"showDetail\"\r\n        ref=\"writeOffTable\"\r\n        :data=\"writeOffList\"\r\n        border\r\n        max-height=\"315px\"\r\n        size=\"mini\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column type=\"index\" width=\"20\"/>\r\n        <el-table-column\r\n          :selectable=\"checkSelectable\"\r\n          type=\"selection\"\r\n          width=\"35\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"审核\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"30\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.isAccountConfirmed === \"1\" ? \"√\" : \"-\" }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"Rct号\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"150\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"费用名称\"\r\n          prop=\"chargeName\"\r\n          width=\"80\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"备注\"\r\n          prop=\"address\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"收付标志\"\r\n          prop=\"dnCurrencyCode\"\r\n          width=\"100\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.isRecievingOrPaying == 1 ? (\"应付\" + scope.row.dnCurrencyCode) : (\"应收\" + scope.row.dnCurrencyCode)\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"应收/付金额\"\r\n          prop=\"dnUnitRate\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ currency(scope.row.dnUnitRate).multiply(scope.row.dnAmount).value }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"销账余额\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"本次拟销账金额\"\r\n          prop=\"sqdDnCurrencyBalance\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.writeoffFromDnBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"汇率展示\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.exchangeRateShow\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"折算记账金额\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-input-number v-model=\"scope.row.writeoffFromBankBalance\" :controls=\"false\" autocomplete=\"off\"\r\n                             style=\"width: 100%; \"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账人\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ getName(scope.row.midChargeBankWriteoff.writeoffStaffId) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账时间\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.midChargeBankWriteoff.writeoffTime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"销账状态\"\r\n          width=\"50\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.writeoffStatus === \"0\" ? \"√\" : scope.row.writeoffStatus === \"1\" ? \"=\" : \"-\"\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <!--总计-->\r\n      <div v-if=\"showDetail\" class=\"total\">\r\n        <div style=\"width: 30%;\">全部总计: {{ totalAmount }}</div>\r\n        <div style=\"width: 30%;\">已选总计:\r\n          {{\r\n            this.form.isRecievingOrPaying == 0 ? this.form.billRecievedWriteoffAmount : this.form.billPaidWriteoffAmount\r\n          }}\r\n        </div>\r\n        <div style=\"width: 30%;\">已选余额总计: {{ form.bankCurrencyCode }} {{ selectedBalanceAmount }}</div>\r\n      </div>\r\n\r\n      <!--按钮区-->\r\n      <div v-if=\"showDetail\" class=\"table-btn-group\">\r\n        <div class=\"table-btn-left\">\r\n          <el-button type=\"primary\" @click=\"invertSelection\">反选</el-button>\r\n          <el-button type=\"primary\" @click=\"autoSelection\">智选</el-button>\r\n          <el-button type=\"primary\" @click=\"addHedging\">增加对冲</el-button>\r\n          <el-button type=\"primary\" @click=\"projectRemove\">项目去除</el-button>\r\n          <el-button type=\"primary\" @click=\"print\">打印</el-button>\r\n        </div>\r\n        <div class=\"table-btn-right\">\r\n          <el-button style=\"float: right\" type=\"primary\" @click=\"writeOffConfirm\">确定销账</el-button>\r\n        </div>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"bankSlipPreview\"\r\n        append-to-body destroy-on-close\r\n        height=\"50%\"\r\n        width=\"50%\"\r\n      >\r\n        <el-image :src=\"form.slipFile\" style=\"margin-top: 20px;\"/>\r\n      </el-dialog>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addBankrecord,\r\n  changeStatus,\r\n  delBankrecord,\r\n  getBankrecord,\r\n  listBankrecord,\r\n  updateBankrecord\r\n} from \"@/api/system/bankrecord\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport currency from \"currency.js\"\r\nimport {chargeWriteOff, selectListCharge, turnBackWriteoff} from \"@/api/system/rsCharge\"\r\nimport {selectListExchangerate} from \"@/api/system/exchangerate\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport log from \"@/views/monitor/job/log.vue\"\r\nimport moment from \"moment\"\r\nimport request from \"@/utils/request\"\r\n\r\nexport default {\r\n  name: \"reimbursementBankRecord\",\r\n  components: {Treeselect, CompanySelect},\r\n  data() {\r\n    return {\r\n      writeOffList: [],\r\n      salesId: null,\r\n      belongList: [],\r\n      staffList: [],\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 记录公司账户出入账明细表格数据\r\n      bankrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: null,\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        actualBankRecievedAmount: [\r\n          {required: true, message: \"请输入实收信息\", trigger: \"blur\"}\r\n        ],\r\n        bankRecordTime: [\r\n          {required: true, message: \"请输入银行时间\", trigger: \"blur\"}\r\n        ],\r\n        actualBankPaidAmount: [\r\n          {required: true, message: \"请输入实付信息\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      add: false,\r\n      showDetail: false,\r\n      selectedCharges: [],\r\n      totalAmount: null,\r\n      selectedAmount: null,\r\n      selectedBalanceAmount: null,\r\n      loadingCharge: false,\r\n      staffId: null,\r\n      alreadyWriteoffList: [],\r\n      turnBackWriteoffList: [],\r\n      showCompany: false,\r\n      companyList: [],\r\n      bankSlipPreview: false,\r\n      imageFile: null\r\n    }\r\n  },\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    // 实收金额\r\n    \"form.actualBankRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款手续费\r\n    \"form.bankRecievedHandlingFee\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款损益\r\n    \"form.bankRecievedExchangeLost\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 实付金额\r\n    \"form.actualBankPaidAmount\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款手续费\r\n    \"form.bankPaidHandlingFee\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款损益\r\n    \"form.bankPaidExchangeLost\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收款记账\r\n    \"form.sqdBillRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付款记账\r\n    \"form.sqdBillPaidAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收账已销\r\n    \"form.billRecievedWriteoffAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付账已销\r\n    \"form.billPaidWriteoffAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n    /*      // 收账未销\r\n         'form.sqdBillRecievedWriteoffBalance'(n) {\r\n           this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n         },\r\n         // 付账未销\r\n         'form.sqdBillPaidWriteoffBalance'(n) {\r\n           this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n         }, */\r\n    selectedCharges: {\r\n      handler: function (newVal, oldVal) {\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = 0\r\n\r\n        // 已选总计\r\n        this.selectedBalanceAmount = 0\r\n\r\n        let billRecievedWriteoffAmount = 0\r\n        let billPaidWriteoffAmount = 0\r\n\r\n        let selectedBalanceAmount = 0\r\n        newVal.map(item => {\r\n          // 本次拟销账金额\r\n          item.writeoffFromDnBalance = currency(item.writeoffFromBankBalance).divide(item.exchangeRate).value\r\n\r\n          // 收账已销\r\n          billRecievedWriteoffAmount += currency(item.writeoffFromBankBalance).value\r\n          // 付账已销\r\n          billPaidWriteoffAmount += currency(item.writeoffFromBankBalance).value\r\n\r\n          // 已选余额总计\r\n          selectedBalanceAmount = currency(item.sqdDnCurrencyBalance).add(selectedBalanceAmount).value\r\n\r\n          // 已选总计\r\n          this.selectedAmount = null\r\n\r\n          currency(item.dnUnitRate).multiply(item.dnAmount).value === item.writeoffFromDnBalance ? item.writeoffStatus = \"0\" : item.writeoffFromDnBalance > 0 ? item.writeoffStatus = \"1\" : item.writeoffStatus = \"-1\"\r\n        })\r\n\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = billRecievedWriteoffAmount\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = billPaidWriteoffAmount\r\n\r\n        this.selectedBalanceAmount = selectedBalanceAmount\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.loadSales()\r\n    this.loadStaff()\r\n    this.getExchangeRate(\"USD\", \"RMB\", null, val => {\r\n      console.log(val)\r\n    })\r\n  },\r\n  computed: {\r\n    receiveRate() {\r\n      return this.form.actualBankRecievedAmount + this.form.bankRecievedHandlingFee + this.form.bankRecievedExchangeLost\r\n    },\r\n    paidRate() {\r\n      return this.form.actualBankPaidAmount + this.form.bankPaidHandlingFee + this.form.bankPaidExchangeLost\r\n    },\r\n    isLocked() {\r\n      return this.form.isBankRecordLocked == 1\r\n    },\r\n    isBankSlipConfirmed() {\r\n      return this.form.slipConfirmed == 1\r\n    }\r\n  },\r\n  methods: {\r\n    handleSearch(type) {\r\n      switch (type) {\r\n        case \"common\":\r\n          this.getList({})\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    },\r\n    parseTime,\r\n    updateSlipSatus() {\r\n      if (this.form.slipConfirmed == 1) {\r\n        this.form.slipConfirmed = \"0\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单取消确认\")\r\n        })\r\n      } else {\r\n        this.form.slipConfirmed = \"1\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单确认\")\r\n        })\r\n      }\r\n\r\n    },\r\n    writeOffConfirm() {\r\n      this.clearReceiveOrPay()\r\n      // 更新银行流水\r\n      let rctSet = new Set()\r\n      this.selectedCharges.map(item => rctSet.add(item.sqdRctNo))\r\n      let rctArr = []\r\n      rctSet.forEach(item => rctArr.push(item))\r\n      this.form.sqdRaletiveRctList = rctArr.toString()\r\n      this.form.writeoffTime = moment().format(\"yyyy-MM-DD\")\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n\r\n      // 取消销账的费用,进行回退\r\n      turnBackWriteoff({rsChargeList: this.turnBackWriteoffList})\r\n\r\n      // 更新费用中的销账余额(更新费用时插入中间表)\r\n      let midChargeBankWriteoffs = []\r\n      this.selectedCharges.map(item => {\r\n        // 每次销账的时候再计算余额写回费用明细中\r\n        item.sqdDnCurrencyBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).subtract(item.writeoffFromDnBalance).value\r\n        item.clearingCurrencyCode = this.form.bankCurrencyCode\r\n        item.writeoffStatus = currency(item.sqdDnCurrencyBalance).value === currency(item.dnUnitRate).multiply(item.dnAmount).value ? \"-1\" : currency(item.sqdDnCurrencyBalance).value === 0 ? \"0\" : \"1\"\r\n        let midChargeBankWriteoff = {}\r\n        midChargeBankWriteoff.chargeId = item.chargeId\r\n        midChargeBankWriteoff.bankRecordId = this.form.bankRecordId\r\n        midChargeBankWriteoff.writeoffFromDnBalance = item.writeoffFromDnBalance\r\n        midChargeBankWriteoff.exchangeRateShowing = item.exchangeRateShow\r\n        midChargeBankWriteoff.writeoffFromBankBalance = item.writeoffFromBankBalance\r\n        midChargeBankWriteoff.dnBasicRate = item.exchangeRate\r\n\r\n        midChargeBankWriteoffs.push(midChargeBankWriteoff)\r\n      })\r\n\r\n      chargeWriteOff({\r\n        rsChargeList: this.selectedCharges,\r\n        midChargeBankWriteoffList: midChargeBankWriteoffs\r\n      }).then(response => {\r\n\r\n      })\r\n\r\n    },\r\n    invertSelection() {\r\n      this.writeOffList.forEach(row => {\r\n        // if (this.selectedCharges.indexOf(row) !== -1) {\r\n        this.$refs.writeOffTable.toggleRowSelection(row)\r\n        // }\r\n      })\r\n    },\r\n    autoSelection() {\r\n    },\r\n    addHedging() {\r\n    },\r\n    projectRemove() {\r\n    },\r\n    print() {\r\n    },\r\n    currency,\r\n    async getCompanyCharges() {\r\n      this.writeOffList = []\r\n      // this.loadingCharge = true\r\n      // 销完的流水只展示销账记录\r\n      let writeoffStatus\r\n      if (this.form.isRecievingOrPaying === \"0\" && this.form.sqdBillRecievedWriteoffBalance === 0) {\r\n        writeoffStatus = \"ALL\"\r\n      } else if (this.form.isRecievingOrPaying === \"1\" && this.form.sqdBillPaidWriteoffBalance === 0) {\r\n        writeoffStatus = \"ALL\"\r\n      } else if (this.form.billRecievedWriteoffAmount !== null || this.form.billPaidWriteoffAmount !== null) {\r\n        // 不是已销完的流水,费用过滤掉已经销账完成的费用\r\n        writeoffStatus = \"Part\"\r\n      }\r\n\r\n      /* let response = await selectListCharge({\r\n        clearingCompanyId: this.form.clearingCompanyId,\r\n        isRecievingOrPaying: this.form.isRecievingOrPaying,\r\n        bankRecordId: this.form.bankRecordId,\r\n        writeoffStatus: writeoffStatus\r\n      })\r\n      if (response && response.length > 0) {\r\n        for (let item of response) {\r\n          if (this.form.bankCurrencyCode === item.dnCurrencyCode) {\r\n            item.exchangeRateShow = 1\r\n            item.exchangeRate = 1\r\n          } else {\r\n            // 当天汇率\r\n            let result = await this.getExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, \"\")\r\n            if (!result) {\r\n              // 没有找到对应的汇率\r\n              this.$message.error(\"系统中没有对应的汇率\")\r\n              this.loadingCharge = false\r\n              return\r\n            }\r\n            // 账单日期汇率\r\n            let billExchangeRate = await this.getBillDataExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, item.currencyRateCalculateDate)\r\n            item.exchangeRate = result[0] === 1 ? result[1] : currency(1).divide(result[1]).value\r\n            item.exchangeRateShow = result[0] === 1 ? result[1] : \"1/\" + result[1]\r\n          }\r\n        }\r\n        this.writeOffList = response\r\n        setTimeout(() => {\r\n          this.writeOffList.map(item => {\r\n            // 有销账记录的要勾选并显示上次销账信息\r\n            if (item.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              // 填入信息\r\n              // 本次拟销账金额\r\n              // 销账余额\r\n              item.writeoffFromDnBalance = item.midChargeBankWriteoff.writeoffFromDnBalance\r\n              item.sqdDnCurrencyBalance = item.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n              // 折算记账金额\r\n              item.writeoffFromBankBalance = item.midChargeBankWriteoff.writeoffFromBankBalance\r\n\r\n              this.selectedCharges.push(item)\r\n              this.alreadyWriteoffList.push(item)\r\n              this.$refs.writeOffTable.toggleRowSelection(item, true)\r\n            }\r\n          })\r\n\r\n          //排序\r\n          this.writeOffList.sort((a, b) => {\r\n            // 未审核排最后\r\n            if (a.isAccountConfirmed === \"0\" && b.isAccountConfirmed === \"1\") {\r\n              return 1\r\n            }\r\n            if (a.isAccountConfirmed === \"1\" && b.isAccountConfirmed === \"0\") {\r\n              return -1\r\n            }\r\n\r\n            // 如果a对象有 midChargeBankWriteoff.midChargeBankId，而b没有，则a排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId === null && b.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              return 1\r\n            }\r\n            // 如果b对象有 midChargeBankWriteoff.midChargeBankId，而a没有，则b排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId !== null && b.midChargeBankWriteoff.midChargeBankId === null) {\r\n              return -1\r\n            }\r\n            // 检查是否都有 midChargeBankWriteoff.midChargeBankId 属性\r\n            if (a.midChargeBankWriteoff.midChargeBankId && b.midChargeBankWriteoff.midChargeBankId) {\r\n              // 如果两个对象都有 midChargeBankWriteoff.midChargeBankId，则按 writeoffFromDnBalance 进行排序\r\n              return a.midChargeBankWriteoff.sqdDnCurrencyBalance - b.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n            } else {\r\n              // 如果有一个或两个对象缺少 midChargeBankWriteoff.midChargeBankId 属性，则保持原顺序\r\n              return 0\r\n            }\r\n          })\r\n        })\r\n        // this.$nextTick()\r\n      }\r\n      this.loadingCharge = false\r\n      this.showDetail = true */\r\n    },\r\n    verify() {\r\n      if (this.form.isBankRecordLocked == 1) {\r\n        this.form.isBankRecordLocked = 0\r\n        this.form.verifyId = null\r\n        this.form.verifyTime = null\r\n      } else {\r\n        this.form.isBankRecordLocked = 1\r\n        this.form.verifyId = this.$store.state.user.sid\r\n        this.form.verifyTime = moment().format(\"yyyy-MM-DD\")\r\n      }\r\n\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$message.success(\"修改成功\")\r\n      })\r\n    },\r\n    /** 查询记录公司账户出入账明细列表 */\r\n    getList() {\r\n      this.loading = true\r\n      this.queryParams.chargeTypeId = 3\r\n      listBankrecord(this.queryParams).then(response => {\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n// 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    }\r\n    ,\r\n// 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankRecordId: null,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: \"0\",\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        invoiceNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        chargeType: \"订单\"\r\n      }\r\n      this.staffId = null\r\n      this.selectedBalanceAmount = 0\r\n      this.resetForm(\"form\")\r\n    }\r\n    ,\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    }\r\n    ,\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    }\r\n    ,\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.bankRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    }\r\n    ,\r\n// 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      // this.ids = selection.map(item => item.bankRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n\r\n      // 勾选时计算销账金额\r\n      this.selectedAmount = 0\r\n      this.turnBackWriteoffList = []\r\n      selection.map(item => {\r\n        // 上次勾选的不受影响\r\n        if (this.selectedCharges.indexOf(item) === -1) {\r\n          // 自动填入拟销账金额\r\n          if (item.sqdDnCurrencyBalance !== null && item.sqdDnCurrencyBalance > 0) {\r\n            // 本次拟销账金额\r\n            item.writeoffFromDnBalance = currency(item.sqdDnCurrencyBalance).value\r\n            // 折算记账金额\r\n            item.writeoffFromBankBalance = currency(item.sqdDnCurrencyBalance).multiply(item.exchangeRate).value\r\n          } else {\r\n            item.writeoffFromDnBalance = currency(item.dnUnitRate).multiply(item.dnAmount).value\r\n            item.writeoffFromBankBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).multiply(item.exchangeRate).value\r\n          }\r\n        }\r\n\r\n      })\r\n      // 取消勾选的费用本次拟核销金额置为0\r\n      this.selectedCharges.map(item => {\r\n        if (selection.indexOf(item) === -1) {\r\n          item.writeoffFromDnBalance = 0\r\n          item.writeoffFromBankBalance = 0\r\n          item.sqdDnCurrencyBalanceShow = currency(item.sqdDnCurrencyBalance).value\r\n\r\n          // 找出取消销账的费用\r\n          if (this.alreadyWriteoffList.indexOf(item) !== -1) {\r\n            this.turnBackWriteoffList.push(item)\r\n          }\r\n        }\r\n      })\r\n\r\n      this.selectedCharges = selection\r\n    }\r\n    ,\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.showDetail = false\r\n      this.open = true\r\n      this.title = \"新建银行流水\"\r\n      this.add = true\r\n      this.form.isRecievingOrPaying = \"1\"\r\n      this.form.chargeType = \"\"\r\n      this.form.chargeTypeId = 4\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.add = false\r\n      this.reset()\r\n      const bankRecordId = row.bankRecordId || this.ids\r\n      getBankrecord(bankRecordId).then(response => {\r\n        this.form = response.data\r\n        this.form.chargeType = \"订单\"\r\n        this.open = true\r\n        this.title = \"查看银行流水-销账明细\"\r\n        this.companyList = [response.companyList]\r\n\r\n        if (this.form.isBankRecordLocked === \"1\" && ((response.data.isRecievingOrPaying === \"0\" && currency(response.data.sqdBillRecievedWriteoffBalance).value !== 0) || (response.data.isRecievingOrPaying === \"1\" && currency(response.data.sqdBillPaidWriteoffBalance).value !== 0))) {\r\n          this.getCompanyCharges()\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(async valid => {\r\n        if (valid) {\r\n          // 收款时将付款信息清空,付款时将收款信息清空\r\n          this.clearReceiveOrPay()\r\n          if (this.form.bankRecordId != null) {\r\n            updateBankrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addBankrecord(this.form).then(response => {\r\n              this.form = response.data\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    uploadImage() {\r\n      return new Promise((resolve, reject) => {\r\n        console.log(this.imageFile)\r\n        this.customHttpRequest({\r\n          file: this.imageFile,\r\n          onSuccess: (response) => {\r\n            this.handleSuccess(response)\r\n            resolve(response)\r\n          },\r\n          onError: (error) => {\r\n            this.handleError(error)\r\n            reject(error)\r\n          }\r\n        })\r\n      })\r\n    },\r\n    clearReceiveOrPay() {\r\n      if (this.form.isRecievingOrPaying === \"0\") {\r\n        // 收款\r\n        this.form.actualBankPaidAmount = 0\r\n        this.form.bankPaidHandlingFee = 0\r\n        this.form.sqdBillPaidAmount = 0\r\n        this.form.billPaidWriteoffAmount = 0\r\n        this.form.bankPaidExchangeLost = 0\r\n        this.form.sqdBillPaidWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillRecievedWriteoffBalance === this.form.sqdBillRecievedAmount ? this.form.writeoffStatus = -1 : this.form.sqdBillRecievedWriteoffBalance === 0 ? this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      } else {\r\n        // 付款\r\n        this.form.actualBankRecievedAmount = 0\r\n        this.form.bankRecievedHandlingFee = 0\r\n        this.form.sqdBillRecievedAmount = 0\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        this.form.bankRecievedExchangeLost = 0\r\n        this.form.sqdBillRecievedWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillPaidAmount === this.form.sqdBillPaidWriteoffBalance ? this.form.writeoffStatus = -1 : this.form.sqdBillPaidWriteoffBalance === 0 ? this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const bankRecordIds = row.bankRecordId || this.ids\r\n      this.$confirm(\"是否确认删除记录公司账户出入账明细编号为\\\"\" + bankRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delBankrecord(bankRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    }\r\n    ,\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/bankrecord/export\", {\r\n        ...this.queryParams\r\n      }, `bankrecord_${new Date().getTime()}.xlsx`)\r\n    }\r\n    ,\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n    ,\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    handledbClick(row) {\r\n      this.handleUpdate(row)\r\n    }\r\n    ,\r\n    selectCompany(company) {\r\n      this.form.clearingCompanyId = company.companyId\r\n      this.form.sqdClearingCompanyShortname = company.companyShortName\r\n      this.showCompany = false\r\n    }\r\n    ,\r\n    async getExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency === localCurrency || a.currency === localCurrency) && (a.currency === overseaCurrency || a.localCurrency === overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(new Date()) && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    },\r\n    async getBillDataExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re = [0, 1]\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency == localCurrency || a.currency == localCurrency)\r\n            && (a.currency == overseaCurrency || a.localCurrency == overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(valueDate)\r\n            && parseTime(valueDate) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    }\r\n    ,\r\n    async exchangeRateShow(bankCurrencyCode, chargeCurrencyCode) {\r\n      if (bankCurrencyCode === chargeCurrencyCode) {\r\n        return 1\r\n      }\r\n      let result = await this.getExchangeRate(bankCurrencyCode, chargeCurrencyCode, \"\")\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        if (id) {\r\n          let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n          if (staff) {\r\n            return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n          } else {\r\n            return \"\"\r\n          }\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    },\r\n    selectStaff(row) {\r\n      this.form.sqdRsStaffId = row.staff.staffId\r\n    },\r\n    checkSelectable(row) {\r\n      return row.isAccountConfirmed === \"1\"\r\n    },\r\n    handleDialogOpened() {\r\n      // 在弹出层打开时，自动聚焦姓名输入框\r\n      this.$nextTick(() => {\r\n        const treeSelectInput = this.$refs.treeSelect.getInputElement()\r\n        if (treeSelectInput) {\r\n          treeSelectInput.focus()\r\n        }\r\n      })\r\n    },\r\n    isRecievingOrPayingNormalizer(node) {\r\n      return {\r\n        id: node.value,\r\n        label: node.label\r\n      }\r\n    },\r\n    selectBankAccount(row) {\r\n      this.form.sqdPaymentTitleCode = row.sqdBelongToCompanyCode\r\n    },\r\n    customHttpRequest(options) {\r\n      const formData = new FormData()\r\n      formData.append(\"file\", options.file)\r\n\r\n      request({\r\n        url: \"/system/bankrecord/uploadImg\",\r\n        method: \"post\",\r\n        data: formData\r\n      }).then(response => {\r\n        options.onSuccess(response, options.file)\r\n        console.log(response.url)\r\n        this.form.slipFile = response.url\r\n      }).catch(error => {\r\n        options.onError(error)\r\n      })\r\n    },\r\n    handleChange(file, fileList) {\r\n      const extension = file.name.substring(file.name.lastIndexOf(\".\"))\r\n      const newFileName = `${this.form.bankRecordNo}${extension}`\r\n      this.imageFile = new File([file.raw], newFileName, {type: file.type})\r\n    },\r\n    handleSuccess(response, file, fileList) {\r\n      this.form.slipFile = response.url\r\n    },\r\n    handleError(err, file, fileList) {\r\n      this.$message.error(\"Upload failed:\", err)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.table-btn-group {\r\n  display: flex;\r\n\r\n  .table-btn-left {\r\n    display: flex;\r\n    width: 100%;\r\n  }\r\n\r\n  .table-btn-right {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.total {\r\n  display: flex;\r\n  width: 60%;\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n::v-deep .el-input-number.is-without-controls .el-input__inner {\r\n  background-color: rgb(255, 242, 204) !important;\r\n}\r\n\r\n::v-deep .vue-treeselect__value-container {\r\n  display: block;\r\n}\r\n\r\n//固定el-table中行高度,不会被内容撑高\r\n::v-deep .cell {\r\n  overflow: hidden; /* 隐藏超出部分 */\r\n  white-space: nowrap; /* 禁止内容换行 */\r\n  text-overflow: ellipsis; /* 使用省略号表示超出的内容 */\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAurBA,IAAAA,WAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,SAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,IAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,OAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,QAAA,GAAAV,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAa,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,OAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACA7B,wBAAA,GACA;UAAA8B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnB,cAAA,GACA;UAAAiB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACA/B,oBAAA,GACA;UAAA6B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,GAAA;MACAC,UAAA;MACAC,eAAA;MACAC,WAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,aAAA;MACAC,OAAA;MACAC,mBAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,SAAA;IACA;EACA;EACAC,WAAA,WAAAA,YAAA;IACA,KAAAC,SAAA;EACA;EACAC,KAAA;IACAjE,UAAA,WAAAA,WAAAkE,CAAA;MACA,IAAAA,CAAA;QACA,KAAAvE,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACA;IACA,0CAAAyE,6BAAAD,CAAA;MACA,KAAAtB,IAAA,CAAAtB,qBAAA,OAAA8C,iBAAA,OAAAxB,IAAA,CAAA5B,wBAAA,EAAAiC,GAAA,MAAAL,IAAA,CAAA1B,uBAAA,EAAAmD,KAAA;IACA;IACA;IACA,yCAAAC,4BAAAJ,CAAA;MACA,KAAAtB,IAAA,CAAAtB,qBAAA,OAAA8C,iBAAA,OAAAxB,IAAA,CAAA5B,wBAAA,EAAAiC,GAAA,MAAAL,IAAA,CAAA1B,uBAAA,EAAAmD,KAAA;IACA;IACA;IACA,0CAAAE,6BAAAL,CAAA;MACA,KAAAtB,IAAA,CAAAlB,8BAAA,OAAA0C,iBAAA,OAAAxB,IAAA,CAAAtB,qBAAA,EAAAkD,QAAA,MAAA5B,IAAA,CAAApB,0BAAA,EAAAgD,QAAA,MAAA5B,IAAA,CAAAxB,wBAAA,EAAAiD,KAAA;IACA;IACA;IACA,sCAAAI,yBAAAP,CAAA;MACA,KAAAtB,IAAA,CAAArB,iBAAA,OAAA6C,iBAAA,OAAAxB,IAAA,CAAA3B,oBAAA,EAAAuD,QAAA,MAAA5B,IAAA,CAAAzB,mBAAA,EAAAkD,KAAA;IACA;IACA;IACA,qCAAAK,wBAAAR,CAAA;MACA,KAAAtB,IAAA,CAAArB,iBAAA,OAAA6C,iBAAA,OAAAxB,IAAA,CAAA3B,oBAAA,EAAAuD,QAAA,MAAA5B,IAAA,CAAAzB,mBAAA,EAAAkD,KAAA;IACA;IACA;IACA,sCAAAM,yBAAAT,CAAA;MACA,KAAAtB,IAAA,CAAAjB,0BAAA,OAAAyC,iBAAA,OAAAxB,IAAA,CAAArB,iBAAA,EAAAiD,QAAA,MAAA5B,IAAA,CAAAnB,sBAAA,EAAA+C,QAAA,MAAA5B,IAAA,CAAAvB,oBAAA,EAAAgD,KAAA;IACA;IAEA;IACA,uCAAAO,0BAAAV,CAAA;MACA,KAAAtB,IAAA,CAAAlB,8BAAA,OAAA0C,iBAAA,OAAAxB,IAAA,CAAAtB,qBAAA,EAAAkD,QAAA,MAAA5B,IAAA,CAAApB,0BAAA,EAAAgD,QAAA,MAAA5B,IAAA,CAAAxB,wBAAA,EAAAiD,KAAA;IACA;IACA;IACA,mCAAAQ,sBAAAX,CAAA;MACA,KAAAtB,IAAA,CAAAjB,0BAAA,OAAAyC,iBAAA,OAAAxB,IAAA,CAAArB,iBAAA,EAAAiD,QAAA,MAAA5B,IAAA,CAAAnB,sBAAA,EAAA+C,QAAA,MAAA5B,IAAA,CAAAvB,oBAAA,EAAAgD,KAAA;IACA;IAEA;IACA,4CAAAS,+BAAAZ,CAAA;MACA,KAAAtB,IAAA,CAAAlB,8BAAA,OAAA0C,iBAAA,OAAAxB,IAAA,CAAAtB,qBAAA,EAAAkD,QAAA,MAAA5B,IAAA,CAAApB,0BAAA,EAAAgD,QAAA,MAAA5B,IAAA,CAAAxB,wBAAA,EAAAiD,KAAA;IACA;IACA;IACA,wCAAAU,2BAAAb,CAAA;MACA,KAAAtB,IAAA,CAAAjB,0BAAA,OAAAyC,iBAAA,OAAAxB,IAAA,CAAArB,iBAAA,EAAAiD,QAAA,MAAA5B,IAAA,CAAAnB,sBAAA,EAAA+C,QAAA,MAAA5B,IAAA,CAAAvB,oBAAA,EAAAgD,KAAA;IACA;IACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAlB,eAAA;MACA6B,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAC,KAAA;QACA;QACA,KAAAvC,IAAA,CAAApB,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA;;QAEA;QACA,KAAA6B,qBAAA;QAEA,IAAA9B,0BAAA;QACA,IAAAC,sBAAA;QAEA,IAAA6B,qBAAA;QACA2B,MAAA,CAAAG,GAAA,WAAAC,IAAA;UACA;UACAA,IAAA,CAAAC,qBAAA,OAAAlB,iBAAA,EAAAiB,IAAA,CAAAE,uBAAA,EAAAC,MAAA,CAAAH,IAAA,CAAAI,YAAA,EAAApB,KAAA;;UAEA;UACA7C,0BAAA,QAAA4C,iBAAA,EAAAiB,IAAA,CAAAE,uBAAA,EAAAlB,KAAA;UACA;UACA5C,sBAAA,QAAA2C,iBAAA,EAAAiB,IAAA,CAAAE,uBAAA,EAAAlB,KAAA;;UAEA;UACAf,qBAAA,OAAAc,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAAzC,GAAA,CAAAK,qBAAA,EAAAe,KAAA;;UAEA;UACAc,KAAA,CAAA9B,cAAA;UAEA,IAAAe,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,EAAAxB,KAAA,KAAAgB,IAAA,CAAAC,qBAAA,GAAAD,IAAA,CAAAzD,cAAA,SAAAyD,IAAA,CAAAC,qBAAA,OAAAD,IAAA,CAAAzD,cAAA,SAAAyD,IAAA,CAAAzD,cAAA;QACA;;QAEA;QACA,KAAAgB,IAAA,CAAApB,0BAAA,GAAAA,0BAAA;QACA;QACA,KAAAoB,IAAA,CAAAnB,sBAAA,GAAAA,sBAAA;QAEA,KAAA6B,qBAAA,GAAAA,qBAAA;MAEA;MACAwC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA,KAAAC,SAAA;IACA,KAAAjC,SAAA;IACA,KAAAkC,eAAA,+BAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;IACA;EACA;EACAG,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAA3D,IAAA,CAAA5B,wBAAA,QAAA4B,IAAA,CAAA1B,uBAAA,QAAA0B,IAAA,CAAAxB,wBAAA;IACA;IACAoF,QAAA,WAAAA,SAAA;MACA,YAAA5D,IAAA,CAAA3B,oBAAA,QAAA2B,IAAA,CAAAzB,mBAAA,QAAAyB,IAAA,CAAAvB,oBAAA;IACA;IACAoF,QAAA,WAAAA,SAAA;MACA,YAAA7D,IAAA,CAAAT,kBAAA;IACA;IACAuE,mBAAA,WAAAA,oBAAA;MACA,YAAA9D,IAAA,CAAA+D,aAAA;IACA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAd,OAAA;UACA;QACA;UACA;MACA;IACA;IACAe,SAAA,EAAAA,eAAA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,SAAArE,IAAA,CAAA+D,aAAA;QACA,KAAA/D,IAAA,CAAA+D,aAAA;QACA,IAAAO,4BAAA,OAAAtE,IAAA,EAAAuE,IAAA,WAAAC,QAAA;UACAH,MAAA,CAAAI,QAAA,CAAAC,OAAA;QACA;MACA;QACA,KAAA1E,IAAA,CAAA+D,aAAA;QACA,IAAAO,4BAAA,OAAAtE,IAAA,EAAAuE,IAAA,WAAAC,QAAA;UACAH,MAAA,CAAAI,QAAA,CAAAC,OAAA;QACA;MACA;IAEA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,iBAAA;MACA;MACA,IAAAC,MAAA,OAAAC,GAAA;MACA,KAAAxE,eAAA,CAAAiC,GAAA,WAAAC,IAAA;QAAA,OAAAqC,MAAA,CAAAzE,GAAA,CAAAoC,IAAA,CAAAuC,QAAA;MAAA;MACA,IAAAC,MAAA;MACAH,MAAA,CAAAI,OAAA,WAAAzC,IAAA;QAAA,OAAAwC,MAAA,CAAAE,IAAA,CAAA1C,IAAA;MAAA;MACA,KAAAzC,IAAA,CAAAN,kBAAA,GAAAuF,MAAA,CAAAG,QAAA;MACA,KAAApF,IAAA,CAAAD,YAAA,OAAAsF,eAAA,IAAAC,MAAA;MACA,IAAAhB,4BAAA,OAAAtE,IAAA,EAAAuE,IAAA,WAAAC,QAAA;QACAI,MAAA,CAAAW,MAAA,CAAAC,UAAA;QACAZ,MAAA,CAAApH,IAAA;QACAoH,MAAA,CAAAxB,OAAA;MACA;;MAEA;MACA,IAAAqC,0BAAA;QAAAC,YAAA,OAAA5E;MAAA;;MAEA;MACA,IAAA6E,sBAAA;MACA,KAAApF,eAAA,CAAAiC,GAAA,WAAAC,IAAA;QACA;QACAA,IAAA,CAAAK,oBAAA,OAAAtB,iBAAA,MAAAA,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,GAAArB,QAAA,CAAAa,IAAA,CAAAC,qBAAA,EAAAjB,KAAA;QACAgB,IAAA,CAAAmD,oBAAA,GAAAhB,MAAA,CAAA5E,IAAA,CAAA7B,gBAAA;QACAsE,IAAA,CAAAzD,cAAA,OAAAwC,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA,SAAAD,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,EAAAxB,KAAA,cAAAD,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA;QACA,IAAAoE,qBAAA;QACAA,qBAAA,CAAAC,QAAA,GAAArD,IAAA,CAAAqD,QAAA;QACAD,qBAAA,CAAAE,YAAA,GAAAnB,MAAA,CAAA5E,IAAA,CAAA+F,YAAA;QACAF,qBAAA,CAAAnD,qBAAA,GAAAD,IAAA,CAAAC,qBAAA;QACAmD,qBAAA,CAAAG,mBAAA,GAAAvD,IAAA,CAAAwD,gBAAA;QACAJ,qBAAA,CAAAlD,uBAAA,GAAAF,IAAA,CAAAE,uBAAA;QACAkD,qBAAA,CAAAK,WAAA,GAAAzD,IAAA,CAAAI,YAAA;QAEA8C,sBAAA,CAAAR,IAAA,CAAAU,qBAAA;MACA;MAEA,IAAAM,wBAAA;QACAT,YAAA,OAAAnF,eAAA;QACA6F,yBAAA,EAAAT;MACA,GAAApB,IAAA,WAAAC,QAAA,GAEA;IAEA;IACA6B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA5J,YAAA,CAAAwI,OAAA,WAAAqB,GAAA;QACA;QACAD,MAAA,CAAAE,KAAA,CAAAC,aAAA,CAAAC,kBAAA,CAAAH,GAAA;QACA;MACA;IACA;IACAI,aAAA,WAAAA,cAAA,GACA;IACAC,UAAA,WAAAA,WAAA,GACA;IACAC,aAAA,WAAAA,cAAA,GACA;IACAC,KAAA,WAAAA,MAAA,GACA;IACAtF,QAAA,EAAAA,iBAAA;IACAuF,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,IAAArI,cAAA;QAAA,WAAAmI,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAAtK,YAAA;cACA;cACA;cAEA,IAAAsK,MAAA,CAAAhH,IAAA,CAAApC,mBAAA,YAAAoJ,MAAA,CAAAhH,IAAA,CAAAlB,8BAAA;gBACAE,cAAA;cACA,WAAAgI,MAAA,CAAAhH,IAAA,CAAApC,mBAAA,YAAAoJ,MAAA,CAAAhH,IAAA,CAAAjB,0BAAA;gBACAC,cAAA;cACA,WAAAgI,MAAA,CAAAhH,IAAA,CAAApB,0BAAA,aAAAoI,MAAA,CAAAhH,IAAA,CAAAnB,sBAAA;gBACA;gBACAG,cAAA;cACA;;cAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;YAzEA;YAAA;cAAA,OAAAwI,QAAA,CAAAG,IAAA;UAAA;QAAA,GAAAN,OAAA;MAAA;IA6EA;IACAO,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,SAAA7H,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAA8H,QAAA;QACA,KAAA9H,IAAA,CAAA+H,UAAA;MACA;QACA,KAAA/H,IAAA,CAAAT,kBAAA;QACA,KAAAS,IAAA,CAAA8H,QAAA,QAAAE,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,GAAA;QACA,KAAAnI,IAAA,CAAA+H,UAAA,OAAA1C,eAAA,IAAAC,MAAA;MACA;MAEA,IAAAhB,4BAAA,OAAAtE,IAAA,EAAAuE,IAAA,WAAAC,QAAA;QACAqD,MAAA,CAAApD,QAAA,CAAAC,OAAA;MACA;IACA;IACA,sBACAtB,OAAA,WAAAA,QAAA;MAAA,IAAAgF,MAAA;MACA,KAAApL,OAAA;MACA,KAAAS,WAAA,CAAAQ,YAAA;MACA,IAAAoK,0BAAA,OAAA5K,WAAA,EAAA8G,IAAA,WAAAC,QAAA;QACA4D,MAAA,CAAA9K,cAAA,GAAAkH,QAAA,CAAA8D,IAAA;QACAF,MAAA,CAAA/K,KAAA,GAAAmH,QAAA,CAAAnH,KAAA;QACA+K,MAAA,CAAApL,OAAA;MACA;IACA;IACA;IACAuL,MAAA,WAAAA,OAAA;MACA,KAAA/K,IAAA;MACA,KAAAgL,KAAA;IACA;IAEA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAxI,IAAA;QACA+F,YAAA;QACAnI,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAsJ,SAAA;QACArJ,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACA2I,UAAA;MACA;MACA,KAAA9H,OAAA;MACA,KAAAF,qBAAA;MACA,KAAAiI,SAAA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAnL,WAAA,CAAAC,OAAA;MACA,KAAA0F,OAAA;IACA;IAEA,aACAyF,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IAEAE,kBAAA,WAAAA,mBAAAvC,GAAA;MAAA,IAAAwC,MAAA;MACA,IAAAC,IAAA,GAAAzC,GAAA,CAAA0C,MAAA;MACA,KAAAC,QAAA,WAAAF,IAAA,SAAAzE,IAAA;QACA,WAAA4E,wBAAA,EAAA5C,GAAA,CAAAR,YAAA,EAAAQ,GAAA,CAAA0C,MAAA;MACA,GAAA1E,IAAA;QACAwE,MAAA,CAAAxD,MAAA,CAAAC,UAAA,CAAAwD,IAAA;MACA,GAAAI,KAAA;QACA7C,GAAA,CAAA0C,MAAA,GAAA1C,GAAA,CAAA0C,MAAA;MACA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAAC,SAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAArM,MAAA,GAAAoM,SAAA,CAAAE,MAAA;MACA,KAAArM,QAAA,IAAAmM,SAAA,CAAAE,MAAA;;MAEA;MACA,KAAA/I,cAAA;MACA,KAAAK,oBAAA;MACAwI,SAAA,CAAA9G,GAAA,WAAAC,IAAA;QACA;QACA,IAAA8G,MAAA,CAAAhJ,eAAA,CAAAkJ,OAAA,CAAAhH,IAAA;UACA;UACA,IAAAA,IAAA,CAAAK,oBAAA,aAAAL,IAAA,CAAAK,oBAAA;YACA;YACAL,IAAA,CAAAC,qBAAA,OAAAlB,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA;YACA;YACAgB,IAAA,CAAAE,uBAAA,OAAAnB,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAAE,QAAA,CAAAP,IAAA,CAAAI,YAAA,EAAApB,KAAA;UACA;YACAgB,IAAA,CAAAC,qBAAA,OAAAlB,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,EAAAxB,KAAA;YACAgB,IAAA,CAAAE,uBAAA,OAAAnB,iBAAA,MAAAA,iBAAA,EAAAiB,IAAA,CAAAM,UAAA,EAAAC,QAAA,CAAAP,IAAA,CAAAQ,QAAA,GAAAD,QAAA,CAAAP,IAAA,CAAAI,YAAA,EAAApB,KAAA;UACA;QACA;MAEA;MACA;MACA,KAAAlB,eAAA,CAAAiC,GAAA,WAAAC,IAAA;QACA,IAAA6G,SAAA,CAAAG,OAAA,CAAAhH,IAAA;UACAA,IAAA,CAAAC,qBAAA;UACAD,IAAA,CAAAE,uBAAA;UACAF,IAAA,CAAAiH,wBAAA,OAAAlI,iBAAA,EAAAiB,IAAA,CAAAK,oBAAA,EAAArB,KAAA;;UAEA;UACA,IAAA8H,MAAA,CAAA1I,mBAAA,CAAA4I,OAAA,CAAAhH,IAAA;YACA8G,MAAA,CAAAzI,oBAAA,CAAAqE,IAAA,CAAA1C,IAAA;UACA;QACA;MACA;MAEA,KAAAlC,eAAA,GAAA+I,SAAA;IACA;IAEA,aACAK,SAAA,WAAAA,UAAA;MACA,KAAAnB,KAAA;MACA,KAAAlI,UAAA;MACA,KAAA9C,IAAA;MACA,KAAAD,KAAA;MACA,KAAA8C,GAAA;MACA,KAAAL,IAAA,CAAApC,mBAAA;MACA,KAAAoC,IAAA,CAAA0I,UAAA;MACA,KAAA1I,IAAA,CAAA/B,YAAA;IACA;IACA,aACA2L,YAAA,WAAAA,aAAArD,GAAA;MAAA,IAAAsD,OAAA;MACA,KAAAxJ,GAAA;MACA,KAAAmI,KAAA;MACA,IAAAzC,YAAA,GAAAQ,GAAA,CAAAR,YAAA,SAAA9I,GAAA;MACA,IAAA6M,yBAAA,EAAA/D,YAAA,EAAAxB,IAAA,WAAAC,QAAA;QACAqF,OAAA,CAAA7J,IAAA,GAAAwE,QAAA,CAAA/H,IAAA;QACAoN,OAAA,CAAA7J,IAAA,CAAA0I,UAAA;QACAmB,OAAA,CAAArM,IAAA;QACAqM,OAAA,CAAAtM,KAAA;QACAsM,OAAA,CAAA7I,WAAA,IAAAwD,QAAA,CAAAxD,WAAA;QAEA,IAAA6I,OAAA,CAAA7J,IAAA,CAAAT,kBAAA,aAAAiF,QAAA,CAAA/H,IAAA,CAAAmB,mBAAA,gBAAA4D,iBAAA,EAAAgD,QAAA,CAAA/H,IAAA,CAAAqC,8BAAA,EAAA2C,KAAA,UAAA+C,QAAA,CAAA/H,IAAA,CAAAmB,mBAAA,gBAAA4D,iBAAA,EAAAgD,QAAA,CAAA/H,IAAA,CAAAsC,0BAAA,EAAA0C,KAAA;UACAoI,OAAA,CAAA9C,iBAAA;QACA;MACA;IACA;IAEA,WACAgD,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAAxD,KAAA,SAAAyD,QAAA;QAAA,IAAAC,IAAA,OAAAjD,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+C,SAAAC,KAAA;UAAA,WAAAjD,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA+C,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA7C,IAAA,GAAA6C,SAAA,CAAA5C,IAAA;cAAA;gBACA,IAAA0C,KAAA;kBACA;kBACAJ,OAAA,CAAAnF,iBAAA;kBACA,IAAAmF,OAAA,CAAAhK,IAAA,CAAA+F,YAAA;oBACA,IAAAzB,4BAAA,EAAA0F,OAAA,CAAAhK,IAAA,EAAAuE,IAAA,WAAAC,QAAA;sBACAwF,OAAA,CAAAzE,MAAA,CAAAC,UAAA;sBACA;sBACAwE,OAAA,CAAA5G,OAAA;oBACA;kBACA;oBACA,IAAAmH,yBAAA,EAAAP,OAAA,CAAAhK,IAAA,EAAAuE,IAAA,WAAAC,QAAA;sBACAwF,OAAA,CAAAhK,IAAA,GAAAwE,QAAA,CAAA/H,IAAA;sBACAuN,OAAA,CAAAzE,MAAA,CAAAC,UAAA;sBACA;sBACAwE,OAAA,CAAA5G,OAAA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAAkH,SAAA,CAAA3C,IAAA;YAAA;UAAA,GAAAwC,QAAA;QAAA,CACA;QAAA,iBAAAK,EAAA;UAAA,OAAAN,IAAA,CAAAO,KAAA,OAAAC,SAAA;QAAA;MAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,OAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAvH,OAAA,CAAAC,GAAA,CAAAmH,OAAA,CAAA1J,SAAA;QACA0J,OAAA,CAAAI,iBAAA;UACAC,IAAA,EAAAL,OAAA,CAAA1J,SAAA;UACAgK,SAAA,WAAAA,UAAA1G,QAAA;YACAoG,OAAA,CAAAO,aAAA,CAAA3G,QAAA;YACAsG,OAAA,CAAAtG,QAAA;UACA;UACA4G,OAAA,WAAAA,QAAAC,KAAA;YACAT,OAAA,CAAAU,WAAA,CAAAD,KAAA;YACAN,MAAA,CAAAM,KAAA;UACA;QACA;MACA;IACA;IACAxG,iBAAA,WAAAA,kBAAA;MACA,SAAA7E,IAAA,CAAApC,mBAAA;QACA;QACA,KAAAoC,IAAA,CAAA3B,oBAAA;QACA,KAAA2B,IAAA,CAAAzB,mBAAA;QACA,KAAAyB,IAAA,CAAArB,iBAAA;QACA,KAAAqB,IAAA,CAAAnB,sBAAA;QACA,KAAAmB,IAAA,CAAAvB,oBAAA;QACA,KAAAuB,IAAA,CAAAjB,0BAAA;QACA;QACA,KAAAiB,IAAA,CAAAlB,8BAAA,UAAAkB,IAAA,CAAAtB,qBAAA,QAAAsB,IAAA,CAAAhB,cAAA,aAAAgB,IAAA,CAAAlB,8BAAA,cAAAkB,IAAA,CAAAhB,cAAA,YAAAgB,IAAA,CAAAhB,cAAA;MACA;QACA;QACA,KAAAgB,IAAA,CAAA5B,wBAAA;QACA,KAAA4B,IAAA,CAAA1B,uBAAA;QACA,KAAA0B,IAAA,CAAAtB,qBAAA;QACA,KAAAsB,IAAA,CAAApB,0BAAA;QACA,KAAAoB,IAAA,CAAAxB,wBAAA;QACA,KAAAwB,IAAA,CAAAlB,8BAAA;QACA;QACA,KAAAkB,IAAA,CAAArB,iBAAA,UAAAqB,IAAA,CAAAjB,0BAAA,QAAAiB,IAAA,CAAAhB,cAAA,aAAAgB,IAAA,CAAAjB,0BAAA,cAAAiB,IAAA,CAAAhB,cAAA,YAAAgB,IAAA,CAAAhB,cAAA;MACA;IACA;IACA,aACAuM,YAAA,WAAAA,aAAAhF,GAAA;MAAA,IAAAiF,OAAA;MACA,IAAAC,aAAA,GAAAlF,GAAA,CAAAR,YAAA,SAAA9I,GAAA;MACA,KAAAiM,QAAA,4BAAAuC,aAAA,cAAAlH,IAAA;QACA,WAAAmH,yBAAA,EAAAD,aAAA;MACA,GAAAlH,IAAA;QACAiH,OAAA,CAAApI,OAAA;QACAoI,OAAA,CAAAjG,MAAA,CAAAC,UAAA;MACA,GAAA4D,KAAA,cACA;IACA;IAEA,aACAuC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iCAAAC,cAAA,CAAA3E,OAAA,MACA,KAAAzJ,WAAA,iBAAAqO,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IAEAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAA3C,MAAA;QACA,OAAA0C,IAAA,CAAAC,QAAA;MACA;MACA,IAAAC,CAAA;MACA,IAAAF,IAAA,CAAAG,KAAA;QACA,IAAAH,IAAA,CAAAG,KAAA,CAAAC,oBAAA,YAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;UACA,IAAAL,IAAA,CAAAM,IAAA,CAAAC,aAAA;YACAL,CAAA,GAAAF,IAAA,CAAAM,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAM,IAAA,CAAAC,aAAA;UACA;YACAL,CAAA,GAAAF,IAAA,CAAAU,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAU,IAAA,CAAAC,aAAA;UACA;QACA;UACAT,CAAA,GAAAF,IAAA,CAAAG,KAAA,CAAAS,SAAA,SAAAZ,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA,SAAAL,IAAA,CAAAG,KAAA,CAAAU,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAT,IAAA,CAAAG,KAAA,CAAAC,oBAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAL,IAAA,CAAAc,MAAA;QACA;UACAC,EAAA,EAAAf,IAAA,CAAAc,MAAA;UACAE,KAAA,EAAAd,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAgB,UAAA,EAAAjB,IAAA,CAAAtL,OAAA,YAAAsL,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;QACA;UACAH,EAAA,EAAAf,IAAA,CAAAmB,MAAA;UACAH,KAAA,EAAAd,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAgB,UAAA,EAAAjB,IAAA,CAAAtL,OAAA,YAAAsL,IAAA,CAAAC,QAAA,IAAAiB;QACA;MACA;IACA;IAEA/J,SAAA,WAAAA,UAAA;MAAA,IAAAiK,OAAA;MACA,SAAAtF,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAA8Q,SAAA,CAAA/D,MAAA,cAAAxB,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAA+Q,SAAA,CAAAD,SAAA;QACAE,cAAA,CAAAC,QAAA,iBAAAnJ,IAAA;UACA+I,OAAA,CAAA1Q,UAAA,GAAA0Q,OAAA,CAAAtF,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAA8Q,SAAA;QACA;MACA;QACA,KAAA3Q,UAAA,QAAAoL,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAA8Q,SAAA;MACA;IACA;IACAnM,SAAA,WAAAA,UAAA;MAAA,IAAAuM,OAAA;MACA,SAAA3F,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAAmR,cAAA,CAAApE,MAAA,cAAAxB,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAA+Q,SAAA,CAAAI,cAAA;QACAH,cAAA,CAAAC,QAAA,sBAAAnJ,IAAA;UACAoJ,OAAA,CAAA9Q,SAAA,GAAA8Q,OAAA,CAAA3F,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAAmR,cAAA;QACA;MACA;QACA,KAAA/Q,SAAA,QAAAmL,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAAmR,cAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAAtH,GAAA;MACA,KAAAqD,YAAA,CAAArD,GAAA;IACA;IAEAuH,aAAA,WAAAA,cAAAC,OAAA;MACA,KAAA/N,IAAA,CAAAjC,iBAAA,GAAAgQ,OAAA,CAAAC,SAAA;MACA,KAAAhO,IAAA,CAAAhC,2BAAA,GAAA+P,OAAA,CAAAE,gBAAA;MACA,KAAAlN,WAAA;IACA;IAEAuC,eAAA,WAAAA,gBAAA4K,eAAA,EAAAC,aAAA,EAAAC,SAAA;MAAA,WAAAnH,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiH,SAAA;QAAA,IAAAC,EAAA,EAAA9J,QAAA,EAAA+J,SAAA,EAAAC,KAAA,EAAAC,CAAA;QAAA,WAAAtH,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAoH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlH,IAAA,GAAAkH,SAAA,CAAAjH,IAAA;YAAA;cAAAiH,SAAA,CAAAjH,IAAA;cAAA,OAEA,IAAAkH,oCAAA;YAAA;cAAApK,QAAA,GAAAmK,SAAA,CAAAE,IAAA;cAAA,MACAX,eAAA,aAAAC,aAAA;gBAAAQ,SAAA,CAAAjH,IAAA;gBAAA;cAAA;cAAA6G,SAAA,OAAAO,2BAAA,CAAA5H,OAAA,EACA1C,QAAA,CAAA/H,IAAA;cAAA;gBAAA,KAAA8R,SAAA,CAAAQ,CAAA,MAAAP,KAAA,GAAAD,SAAA,CAAAjN,CAAA,IAAA0N,IAAA;kBAAAP,CAAA,GAAAD,KAAA,CAAA/M,KAAA;kBACA,KAAAgN,CAAA,CAAAN,aAAA,KAAAA,aAAA,IAAAM,CAAA,CAAAjN,QAAA,KAAA2M,aAAA,MAAAM,CAAA,CAAAjN,QAAA,KAAA0M,eAAA,IAAAO,CAAA,CAAAN,aAAA,KAAAD,eAAA,KACA,IAAA/J,eAAA,EAAAsK,CAAA,CAAAQ,SAAA,SAAA9K,eAAA,MAAA4H,IAAA,WAAA5H,eAAA,MAAA4H,IAAA,WAAA5H,eAAA,EAAAsK,CAAA,CAAAS,OAAA;oBACA;oBACA,IAAAhB,eAAA,KAAAO,CAAA,CAAAP,eAAA,IAAAC,aAAA,KAAAM,CAAA,CAAAN,aAAA;sBACAG,EAAA,WAAA9M,iBAAA,EAAAiN,CAAA,CAAAU,OAAA,EAAAvM,MAAA,CAAA6L,CAAA,CAAAW,IAAA,EAAA3N,KAAA;oBACA;sBACA6M,EAAA,WAAA9M,iBAAA,EAAAiN,CAAA,CAAAY,QAAA,EAAAzM,MAAA,CAAA6L,CAAA,CAAAW,IAAA,EAAA3N,KAAA;oBACA;kBACA;gBACA;cAAA,SAAA6N,GAAA;gBAAAf,SAAA,CAAAgB,CAAA,CAAAD,GAAA;cAAA;gBAAAf,SAAA,CAAAiB,CAAA;cAAA;cAAA,OAAAb,SAAA,CAAAc,MAAA,WACAnB,EAAA;YAAA;YAAA;cAAA,OAAAK,SAAA,CAAAhH,IAAA;UAAA;QAAA,GAAA0G,QAAA;MAAA;IAEA;IACAqB,uBAAA,WAAAA,wBAAAxB,eAAA,EAAAC,aAAA,EAAAC,SAAA;MAAA,WAAAnH,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuI,SAAA;QAAA,IAAArB,EAAA,EAAA9J,QAAA,EAAAoL,UAAA,EAAAC,MAAA,EAAApB,CAAA;QAAA,WAAAtH,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAwI,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtI,IAAA,GAAAsI,SAAA,CAAArI,IAAA;YAAA;cACA4G,EAAA;cAAAyB,SAAA,CAAArI,IAAA;cAAA,OACA,IAAAkH,oCAAA;YAAA;cAAApK,QAAA,GAAAuL,SAAA,CAAAlB,IAAA;cAAA,MACAX,eAAA,aAAAC,aAAA;gBAAA4B,SAAA,CAAArI,IAAA;gBAAA;cAAA;cAAAkI,UAAA,OAAAd,2BAAA,CAAA5H,OAAA,EACA1C,QAAA,CAAA/H,IAAA;cAAA;gBAAA,KAAAmT,UAAA,CAAAb,CAAA,MAAAc,MAAA,GAAAD,UAAA,CAAAtO,CAAA,IAAA0N,IAAA;kBAAAP,CAAA,GAAAoB,MAAA,CAAApO,KAAA;kBACA,KAAAgN,CAAA,CAAAN,aAAA,IAAAA,aAAA,IAAAM,CAAA,CAAAjN,QAAA,IAAA2M,aAAA,MACAM,CAAA,CAAAjN,QAAA,IAAA0M,eAAA,IAAAO,CAAA,CAAAN,aAAA,IAAAD,eAAA,KACA,IAAA/J,eAAA,EAAAsK,CAAA,CAAAQ,SAAA,SAAA9K,eAAA,EAAAiK,SAAA,KACA,IAAAjK,eAAA,EAAAiK,SAAA,SAAAjK,eAAA,EAAAsK,CAAA,CAAAS,OAAA;oBACA;oBACA,IAAAhB,eAAA,KAAAO,CAAA,CAAAP,eAAA,IAAAC,aAAA,KAAAM,CAAA,CAAAN,aAAA;sBACAG,EAAA,WAAA9M,iBAAA,EAAAiN,CAAA,CAAAU,OAAA,EAAAvM,MAAA,CAAA6L,CAAA,CAAAW,IAAA,EAAA3N,KAAA;oBACA;sBACA6M,EAAA,WAAA9M,iBAAA,EAAAiN,CAAA,CAAAY,QAAA,EAAAzM,MAAA,CAAA6L,CAAA,CAAAW,IAAA,EAAA3N,KAAA;oBACA;kBACA;gBACA;cAAA,SAAA6N,GAAA;gBAAAM,UAAA,CAAAL,CAAA,CAAAD,GAAA;cAAA;gBAAAM,UAAA,CAAAJ,CAAA;cAAA;cAAA,OAAAO,SAAA,CAAAN,MAAA,WACAnB,EAAA;YAAA;YAAA;cAAA,OAAAyB,SAAA,CAAApI,IAAA;UAAA;QAAA,GAAAgI,QAAA;MAAA;IAEA;IAEA1J,gBAAA,WAAAA,iBAAA9H,gBAAA,EAAA6R,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAhJ,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA8I,SAAA;QAAA,IAAAC,MAAA;QAAA,WAAAhJ,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA8I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA5I,IAAA,GAAA4I,SAAA,CAAA3I,IAAA;YAAA;cAAA,MACAvJ,gBAAA,KAAA6R,kBAAA;gBAAAK,SAAA,CAAA3I,IAAA;gBAAA;cAAA;cAAA,OAAA2I,SAAA,CAAAZ,MAAA,WACA;YAAA;cAAAY,SAAA,CAAA3I,IAAA;cAAA,OAEAuI,OAAA,CAAA3M,eAAA,CAAAnF,gBAAA,EAAA6R,kBAAA;YAAA;cAAAG,MAAA,GAAAE,SAAA,CAAAxB,IAAA;YAAA;YAAA;cAAA,OAAAwB,SAAA,CAAA1I,IAAA;UAAA;QAAA,GAAAuI,QAAA;MAAA;IACA;IAEAI,OAAA,WAAAA,QAAArD,EAAA;MACA,IAAAA,EAAA;QACA,IAAAA,EAAA;UACA,IAAAZ,KAAA,QAAArE,MAAA,CAAAC,KAAA,CAAAxL,IAAA,CAAAmR,cAAA,CAAA2C,MAAA,WAAAC,OAAA;YAAA,OAAAA,OAAA,CAAA5P,OAAA,IAAAqM,EAAA;UAAA;UACA,IAAAZ,KAAA;YACA,OAAAA,KAAA,CAAAC,oBAAA,GAAAD,KAAA,CAAAE,oBAAA,GAAAF,KAAA,CAAAoE,cAAA;UACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAAnK,GAAA;MACA,KAAAvG,IAAA,CAAAJ,YAAA,GAAA2G,GAAA,CAAA8F,KAAA,CAAAzL,OAAA;IACA;IACA+P,eAAA,WAAAA,gBAAApK,GAAA;MACA,OAAAA,GAAA,CAAAqK,kBAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAAC,SAAA;QACA,IAAAC,eAAA,GAAAF,OAAA,CAAAtK,KAAA,CAAAyK,UAAA,CAAAC,eAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,KAAA;QACA;MACA;IACA;IACAC,6BAAA,WAAAA,8BAAAlF,IAAA;MACA;QACAe,EAAA,EAAAf,IAAA,CAAAzK,KAAA;QACAyL,KAAA,EAAAhB,IAAA,CAAAgB;MACA;IACA;IACAmE,iBAAA,WAAAA,kBAAA9K,GAAA;MACA,KAAAvG,IAAA,CAAAnC,mBAAA,GAAA0I,GAAA,CAAA+K,sBAAA;IACA;IACAtG,iBAAA,WAAAA,kBAAAuG,OAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAAJ,OAAA,CAAAtG,IAAA;MAEA,IAAA2G,gBAAA;QACAC,GAAA;QACAC,MAAA;QACArV,IAAA,EAAAgV;MACA,GAAAlN,IAAA,WAAAC,QAAA;QACA+M,OAAA,CAAArG,SAAA,CAAA1G,QAAA,EAAA+M,OAAA,CAAAtG,IAAA;QACAzH,OAAA,CAAAC,GAAA,CAAAe,QAAA,CAAAqN,GAAA;QACAL,OAAA,CAAAxR,IAAA,CAAA+R,QAAA,GAAAvN,QAAA,CAAAqN,GAAA;MACA,GAAAzI,KAAA,WAAAiC,KAAA;QACAkG,OAAA,CAAAnG,OAAA,CAAAC,KAAA;MACA;IACA;IACA2G,YAAA,WAAAA,aAAA/G,IAAA,EAAAgH,QAAA;MACA,IAAAC,SAAA,GAAAjH,IAAA,CAAA5O,IAAA,CAAA8V,SAAA,CAAAlH,IAAA,CAAA5O,IAAA,CAAA+V,WAAA;MACA,IAAAC,WAAA,MAAAvG,MAAA,MAAA9L,IAAA,CAAAsS,YAAA,EAAAxG,MAAA,CAAAoG,SAAA;MACA,KAAAhR,SAAA,OAAAqR,IAAA,EAAAtH,IAAA,CAAAuH,GAAA,GAAAH,WAAA;QAAAnO,IAAA,EAAA+G,IAAA,CAAA/G;MAAA;IACA;IACAiH,aAAA,WAAAA,cAAA3G,QAAA,EAAAyG,IAAA,EAAAgH,QAAA;MACA,KAAAjS,IAAA,CAAA+R,QAAA,GAAAvN,QAAA,CAAAqN,GAAA;IACA;IACAvG,WAAA,WAAAA,YAAAgE,GAAA,EAAArE,IAAA,EAAAgH,QAAA;MACA,KAAAxN,QAAA,CAAA4G,KAAA,mBAAAiE,GAAA;IACA;EACA;AACA;AAAAmD,OAAA,CAAAvL,OAAA,GAAAwL,QAAA"}]}