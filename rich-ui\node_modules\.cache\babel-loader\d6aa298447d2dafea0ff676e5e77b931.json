{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1754876882543}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_path", "_interopRequireDefault", "require", "_validate", "_Item", "_Link", "_FixiOSBug", "name", "components", "<PERSON><PERSON>", "AppLink", "mixins", "FixiOSBug", "props", "item", "type", "Object", "required", "isNest", "Boolean", "default", "basePath", "String", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methods", "hasOneShowingChild", "_this", "children", "arguments", "length", "undefined", "parent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "filter", "hidden", "_objectSpread2", "path", "noShowingChildren", "<PERSON><PERSON><PERSON>", "routePath", "routeQuery", "isExternal", "query", "JSON", "parse", "resolve", "exports", "_default"], "sources": ["src/layout/components/Sidebar/SidebarItem.vue"], "sourcesContent": ["<template>\r\n  <div v-if=\"!item.hidden\">\r\n    <template\r\n      v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\r\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path, onlyOneChild.query)\">\r\n        <el-menu-item :class=\"{'submenu-title-noDropdown':!isNest}\" :index=\"resolvePath(onlyOneChild.path)\">\r\n          <item :badge=\"onlyOneChild.meta.badge\" :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\"\r\n                :title=\"onlyOneChild.meta.title\"/>\r\n        </el-menu-item>\r\n      </app-link>\r\n    </template>\r\n\r\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\r\n      <template #title>\r\n        <item\r\n          v-if=\"item.meta\"\r\n          :badge=\"item.meta.badge\"\r\n          :icon=\"item.meta && item.meta.icon\"\r\n          :is-dot=\"item.meta.isDot\"\r\n          :title=\"item.meta.title\"\r\n        />\r\n      </template>\r\n      <sidebar-item\r\n        v-for=\"child in item.children\"\r\n        :key=\"child.path\"\r\n        :base-path=\"resolvePath(child.path)\"\r\n        :is-nest=\"true\"\r\n        :item=\"child\"\r\n        class=\"nest-menu\"\r\n      />\r\n    </el-submenu>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\nimport {isExternal} from '@/utils/validate'\r\nimport Item from './Item'\r\nimport AppLink from './Link'\r\nimport FixiOSBug from './FixiOSBug'\r\n\r\nexport default {\r\n  name: 'SidebarItem',\r\n  components: {Item, AppLink},\r\n  mixins: [FixiOSBug],\r\n  props: {\r\n    // route object\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    basePath: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    this.onlyOneChild = null\r\n    return {}\r\n  },\r\n  methods: {\r\n    hasOneShowingChild(children = [], parent) {\r\n      if (!children) {\r\n        children = [];\r\n      }\r\n      const showingChildren = children.filter(item => {\r\n        if (item.hidden) {\r\n          return false\r\n        } else {\r\n          // Temp set(will be used if only has one showing child)\r\n          this.onlyOneChild = item\r\n          return true\r\n        }\r\n      })\r\n\r\n      // When there is only one child router, the child router is displayed by default\r\n      if (showingChildren.length == 1) {\r\n        return true\r\n      }\r\n\r\n      // Show parent if there are no child router to display\r\n      if (showingChildren.length == 0) {\r\n        this.onlyOneChild = {...parent, path: '', noShowingChildren: true}\r\n        return true\r\n      }\r\n\r\n      return false\r\n    },\r\n    resolvePath(routePath, routeQuery) {\r\n      if (isExternal(routePath)) {\r\n        return routePath\r\n      }\r\n      if (isExternal(this.basePath)) {\r\n        return this.basePath\r\n      }\r\n      if (routeQuery) {\r\n        let query = JSON.parse(routeQuery);\r\n        return {path: path.resolve(this.basePath, routePath), query: query}\r\n      }\r\n      return path.resolve(this.basePath, routePath)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;AAmCA,IAAAA,KAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,SAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,IAAA;EACAC,UAAA;IAAAC,IAAA,EAAAA,aAAA;IAAAC,OAAA,EAAAA;EAAA;EACAC,MAAA,GAAAC,kBAAA;EACAC,KAAA;IACA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAC,MAAA;MACAH,IAAA,EAAAI,OAAA;MACAC,OAAA;IACA;IACAC,QAAA;MACAN,IAAA,EAAAO,MAAA;MACAF,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA,KAAAC,YAAA;IACA;EACA;EACAC,OAAA;IACAC,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA;MAAA,IAAAG,MAAA,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;MACA,KAAAH,QAAA;QACAA,QAAA;MACA;MACA,IAAAK,eAAA,GAAAL,QAAA,CAAAM,MAAA,WAAApB,IAAA;QACA,IAAAA,IAAA,CAAAqB,MAAA;UACA;QACA;UACA;UACAR,KAAA,CAAAH,YAAA,GAAAV,IAAA;UACA;QACA;MACA;;MAEA;MACA,IAAAmB,eAAA,CAAAH,MAAA;QACA;MACA;;MAEA;MACA,IAAAG,eAAA,CAAAH,MAAA;QACA,KAAAN,YAAA,OAAAY,cAAA,CAAAhB,OAAA,MAAAgB,cAAA,CAAAhB,OAAA,MAAAY,MAAA;UAAAK,IAAA;UAAAC,iBAAA;QAAA;QACA;MACA;MAEA;IACA;IACAC,WAAA,WAAAA,YAAAC,SAAA,EAAAC,UAAA;MACA,QAAAC,oBAAA,EAAAF,SAAA;QACA,OAAAA,SAAA;MACA;MACA,QAAAE,oBAAA,OAAArB,QAAA;QACA,YAAAA,QAAA;MACA;MACA,IAAAoB,UAAA;QACA,IAAAE,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAJ,UAAA;QACA;UAAAJ,IAAA,EAAAA,aAAA,CAAAS,OAAA,MAAAzB,QAAA,EAAAmB,SAAA;UAAAG,KAAA,EAAAA;QAAA;MACA;MACA,OAAAN,aAAA,CAAAS,OAAA,MAAAzB,QAAA,EAAAmB,SAAA;IACA;EACA;AACA;AAAAO,OAAA,CAAA3B,OAAA,GAAA4B,QAAA"}]}