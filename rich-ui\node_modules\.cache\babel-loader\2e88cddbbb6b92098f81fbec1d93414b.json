{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\inventory.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\inventory.js", "mtime": 1748427903092}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listInventory", "query", "request", "url", "method", "params", "listAggregatorRsInventory", "listInventorys", "getInventory", "inventoryId", "getPackage", "addInventory", "data", "updateInventory", "delInventory", "changeStatus", "status", "outboundInventory", "settlement", "preOutboundInventory", "packUp", "cancelPkg"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/inventory.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询库存列表\r\nexport function listInventory(query) {\r\n  return request({\r\n    url: '/system/inventory/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function listAggregatorRsInventory(query) {\r\n  return request({\r\n    url: '/system/inventory/aggregator',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function listInventorys(query) {\r\n  return request({\r\n    url: '/system/inventory/lists',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询库存详细\r\nexport function getInventory(inventoryId) {\r\n  return request({\r\n    url: '/system/inventory/' + inventoryId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function getPackage(query) {\r\n  return request({\r\n    url: '/system/inventory/package',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 新增库存\r\nexport function addInventory(data) {\r\n  return request({\r\n    url: '/system/inventory',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改库存\r\nexport function updateInventory(data) {\r\n  return request({\r\n    url: '/system/inventory',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除库存\r\nexport function delInventory(inventoryId) {\r\n  return request({\r\n    url: '/system/inventory/' + inventoryId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(inventoryId, status) {\r\n  const data = {\r\n    inventoryId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/inventory/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function outboundInventory(data) {\r\n  return request({\r\n    url: '/system/inventory/outbound',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function settlement(data) {\r\n  return request({\r\n    url: '/system/inventory/settlement',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function preOutboundInventory(data) {\r\n  return request({\r\n    url: '/system/inventory/preOutbound',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function packUp(data) {\r\n  return request({\r\n    url: '/system/inventory/packUp',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function cancelPkg(data) {\r\n  return request({\r\n    url: '/system/inventory/cancelPkg',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASK,yBAAyBA,CAACL,KAAK,EAAE;EAC/C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASM,cAAcA,CAACN,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,YAAYA,CAACC,WAAW,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGM,WAAW;IACvCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASM,UAAUA,CAACT,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACL,WAAW,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGM,WAAW;IACvCL,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,YAAYA,CAACN,WAAW,EAAEO,MAAM,EAAE;EAChD,IAAMJ,IAAI,GAAG;IACXH,WAAW,EAAXA,WAAW;IACXO,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAd,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASK,iBAAiBA,CAACL,IAAI,EAAE;EACtC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASM,UAAUA,CAACN,IAAI,EAAE;EAC/B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASO,oBAAoBA,CAACP,IAAI,EAAE;EACzC,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASQ,MAAMA,CAACR,IAAI,EAAE;EAC3B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASS,SAASA,CAACT,IAAI,EAAE;EAC9B,OAAO,IAAAV,gBAAO,EAAC;IACbC,GAAG,EAAE,6BAA6B;IAClCC,MAAM,EAAE,KAAK;IACbQ,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}