{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\mixins\\formValidator.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\mixins\\formValidator.js", "mtime": 1752032719060}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_default", "data", "checkOrderBelongsTo", "rule", "value", "callback", "Error", "rules", "clientId", "required", "message", "trigger", "goodsNameSummary", "grossWeight", "logisticsTypeId", "polId", "destinationPortId", "serviceTypeIds", "blFormCode", "revenueTon", "salesId", "orderBelongsTo", "validator", "exports", "default"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/system/document/mixins/formValidator.js"], "sourcesContent": ["/**\r\n * 表单验证规则Mixin\r\n * 集中管理op.vue中所有的表单验证规则\r\n */\r\nexport default {\r\n  data() {\r\n    // 验证函数\r\n    const checkOrderBelongsTo = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error(\"请输入所属公司\"))\r\n      } else {\r\n        callback()\r\n      }\r\n    }\r\n\r\n    return {\r\n      rules: {\r\n        clientId: [{required: true, message: \"请选择客户\", trigger: \"blur\"}],\r\n        goodsNameSummary: [{required: true, message: \"请填写货名\", trigger: \"blur\"}],\r\n        grossWeight: [{required: true, message: \"请填写货名\", trigger: \"blur\"}],\r\n        logisticsTypeId: [{required: true, message: \"请选择物流类型\", trigger: \"blur\"}],\r\n        polId: [{required: true, message: \"请选择启运港\", trigger: \"blur\"}],\r\n        destinationPortId: [{required: true, message: \"请选择目的港\", trigger: \"blur\"}],\r\n        serviceTypeIds: [{required: true, message: \"请选择服务列表\", trigger: \"blur\"}],\r\n        blFormCode: [{required: true, message: \"请选择出单方式\", trigger: \"blur\"}],\r\n        revenueTon: [{required: true, message: \"请选择计费货量\", trigger: \"blur\"}],\r\n        salesId: [{required: true, message: \"请选择计费货量\", trigger: \"blur\"}],\r\n        // grossWeight: [{required: true, message: \"请输入毛重\", trigger: \"blur\"}],\r\n        // impExpType: [{required: true, message: \"请选择进出口类型\", trigger: \"blur\"}],\r\n        orderBelongsTo: [{validator: checkOrderBelongsTo, trigger: \"blur\"}]\r\n      }\r\n    }\r\n  }\r\n} "], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AAHA,IAAAA,QAAA,GAIe;EACbC,IAAI,WAAAA,KAAA,EAAG;IACL;IACA,IAAMC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAK;MACrD,IAAI,CAACD,KAAK,EAAE;QACVC,QAAQ,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC,CAAC;MAChC,CAAC,MAAM;QACLD,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;IAED,OAAO;MACLE,KAAK,EAAE;QACLC,QAAQ,EAAE,CAAC;UAACC,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QAC/DC,gBAAgB,EAAE,CAAC;UAACH,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACvEE,WAAW,EAAE,CAAC;UAACJ,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,OAAO;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QAClEG,eAAe,EAAE,CAAC;UAACL,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACxEI,KAAK,EAAE,CAAC;UAACN,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QAC7DK,iBAAiB,EAAE,CAAC;UAACP,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,QAAQ;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACzEM,cAAc,EAAE,CAAC;UAACR,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACvEO,UAAU,EAAE,CAAC;UAACT,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACnEQ,UAAU,EAAE,CAAC;UAACV,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QACnES,OAAO,EAAE,CAAC;UAACX,QAAQ,EAAE,IAAI;UAAEC,OAAO,EAAE,SAAS;UAAEC,OAAO,EAAE;QAAM,CAAC,CAAC;QAChE;QACA;QACAU,cAAc,EAAE,CAAC;UAACC,SAAS,EAAEpB,mBAAmB;UAAES,OAAO,EAAE;QAAM,CAAC;MACpE;IACF,CAAC;EACH;AACF,CAAC;AAAAY,OAAA,CAAAC,OAAA,GAAAxB,QAAA"}]}