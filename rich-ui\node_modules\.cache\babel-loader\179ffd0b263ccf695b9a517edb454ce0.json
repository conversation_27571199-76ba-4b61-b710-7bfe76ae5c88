{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\docdetail.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\docdetail.js", "mtime": 1754876882462}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listDocdetail", "query", "request", "url", "method", "params", "getDocdetail", "docDetailId", "addDocdetail", "data", "updateDocdetail", "delDocdetail", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/docdetail.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询操作文件列表\r\nexport function listDocdetail(query) {\r\n  return request({\r\n    url: '/system/docdetail/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询操作文件详细\r\nexport function getDocdetail(docDetailId) {\r\n  return request({\r\n    url: '/system/docdetail/' + docDetailId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增操作文件\r\nexport function addDocdetail(data) {\r\n  return request({\r\n    url: '/system/docdetail',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改操作文件\r\nexport function updateDocdetail(data) {\r\n  return request({\r\n    url: '/system/docdetail',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除操作文件\r\nexport function delDocdetail(docDetailId) {\r\n  return request({\r\n    url: '/system/docdetail/' + docDetailId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(docDetailId, status) {\r\n  const data = {\r\n    docDetailId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/docdetail/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,wBAAwB;IAC7BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,YAAYA,CAACC,WAAW,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,WAAW;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,YAAYA,CAACC,IAAI,EAAE;EACjC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,eAAeA,CAACD,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,YAAYA,CAACJ,WAAW,EAAE;EACxC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGI,WAAW;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,WAAW,EAAEM,MAAM,EAAE;EAChD,IAAMJ,IAAI,GAAG;IACXF,WAAW,EAAXA,WAAW;IACXM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}