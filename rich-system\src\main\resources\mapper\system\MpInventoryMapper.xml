<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.MpInventoryMapper">

    <resultMap type="MpInventory" id="MpInventoryResult">
        <result property="inventoryId"    column="inventory_id"    />
        <result property="inventoryStatus"    column="inventory_status"    />
        <result property="inboundSerialNo"    column="inbound_serial_no"    />
        <result property="inboundSerialSplit"    column="inbound_serial_split"    />
        <result property="inboundDate"    column="inbound_date"    />
        <result property="outboundNo"    column="outbound_no"    />
        <result property="forwarderNo"    column="forwarder_no"    />
        <result property="rentalSettlementDate"    column="rental_settlement_date"    />
        <result property="outboundDate"    column="outbound_date"    />
        <result property="clientCode"    column="client_code"    />
        <result property="subOrderNo"    column="sub_order_no"    />
        <result property="supplier"    column="supplier"    />
        <result property="driverInfo"    column="driver_info"    />
        <result property="sqdShippingMark"    column="sqd_shipping_mark"    />
        <result property="cargoName"    column="cargo_name"    />
        <result property="totalBoxes"    column="total_boxes"    />
        <result property="packageType"    column="package_type"    />
        <result property="totalGrossWeight"    column="total_gross_weight"    />
        <result property="totalVolume"    column="total_volume"    />
        <result property="damageStatus"    column="damage_status"    />
        <result property="warehouseCode"    column="warehouse_code"    />
        <result property="recordType"    column="record_type"    />
        <result property="inboundType"    column="inbound_type"    />
        <result property="cargoNature"    column="cargo_nature"    />
        <result property="preOutboundFlag"    column="pre_outbound_flag"    />
        <result property="outboundRequestFlag"    column="outbound_request_flag"    />
        <result property="sqdPlannedOutboundDate"    column="sqd_planned_outbound_date"    />
        <result property="contractType"    column="contract_type"    />
        <result property="receivedSupplier"    column="received_supplier"    />
        <result property="consigneeName"    column="consignee_name"    />
        <result property="consigneeTel"    column="consignee_tel"    />
        <result property="clientName"    column="client_name"    />
        <result property="actualDeliveryTime"    column="actual_delivery_time"    />
        <result property="ruichiSignFlowNo"    column="ruichi_sign_flow_no"    />
        <result property="signerName"    column="signer_name"    />
        <result property="cargoStatus"    column="cargo_status"    />
        <result property="outboundOrderNo"    column="outbound_order_no"    />
        <result property="deliveryType"    column="delivery_type"    />
        <result property="logisticsInfo"    column="logistics_info"    />
        <result property="preEntrySerialNo"    column="pre_entry_serial_no"    />
        <result property="estimatedArrivalTime"    column="estimated_arrival_time"    />
        <result property="actualArrivalTime"    column="actual_arrival_time"    />
        <result property="receiptSerialNo"    column="receipt_serial_no"    />
        <result property="receiptPerson"    column="receipt_person"    />
        <result property="claimTime"    column="claim_time"    />
        <result property="claimRemark"    column="claim_remark"    />
        <result property="verifyPerson"    column="verify_person"    />
        <result property="verifyTime"    column="verify_time"    />
        <result property="goodsStatus"    column="goods_status"    />
        <result property="recordStatus"    column="record_status"    />
        <result property="destinationCompanyName"    column="destination_company_name"    />
        <result property="destinationWarehouseAddress"    column="destination_warehouse_address"    />
        <result property="destinationWarehouseContact"    column="destination_warehouse_contact"    />
        <result property="remarks"    column="remarks"    />
        <result property="createdAt"    column="created_at"    />
        <result property="deleteTime"    column="delete_time"    />
        <result property="deleteStatus"    column="delete_status"    />
        <result property="consigneeCode"    column="consignee_code"    />
        <result property="clientRegion"    column="client_region"    />
        <result property="specialMark"    column="special_mark"    />
        <result property="packageTo"    column="package_to"    />
        <result property="packageInto"    column="package_into"    />
        <result property="rsInventoryId"    column="rs_inventory_id"    />
        <result property="createdBy"    column="created_by"    />
        <result property="purchaseNo"    column="purchase_no"    />
        <result property="createdUser"    column="created_user"    />
        <result property="brandName"    column="brand_name"    />
        <result property="receivedStorageFee"    column="received_storage_fee"    />
        <result property="receivedUnloadingFee"    column="received_unloading_fee"    />
        <result property="logisticsAdvanceFee"    column="logistics_advance_fee"    />
        <collection property="mpCargoDetails" resultMap="com.rich.system.mapper.MpCargoDetailsMapper.MpCargoDetailsResult"/>
    </resultMap>

    <sql id="selectMpInventoryVo">
        select mi.inventory_id,
               mi.inventory_status,
               mi.received_storage_fee,
               mi.received_unloading_fee,
               mi.logistics_advance_fee,
               mi.inbound_serial_no,
               mi.inbound_serial_split,
               mi.inbound_date,
               mi.outbound_no,
               mi.forwarder_no,
               mi.rental_settlement_date,
               mi.outbound_date,
               mi.client_code,
               mi.sub_order_no,
               mi.supplier,
               mi.driver_info,
               mi.sqd_shipping_mark,
               mi.cargo_name,
               mi.total_boxes,
               mi.package_type,
               mi.total_gross_weight,
               mi.total_volume,
               mi.damage_status,
               mi.warehouse_code,
               mi.record_type,
               mi.inbound_type,
               mi.cargo_nature,
               mi.pre_outbound_flag,
               mi.outbound_request_flag,
               mi.sqd_planned_outbound_date,
               mi.contract_type,
               mi.received_supplier,
               mi.consignee_name,
               mi.consignee_tel,
               mi.client_name,
               mi.actual_delivery_time,
               mi.ruichi_sign_flow_no,
               mi.signer_name,
               mi.cargo_status,
               mi.outbound_order_no,
               mi.delivery_type,
               mi.logistics_info,
               mi.pre_entry_serial_no,
               mi.estimated_arrival_time,
               mi.actual_arrival_time,
               mi.receipt_serial_no,
               mi.receipt_person,
               mi.claim_time,
               mi.claim_remark,
               mi.verify_person,
               mi.verify_time,
               mi.goods_status,
               mi.record_status,
               mi.destination_company_name,
               mi.destination_warehouse_address,
               mi.destination_warehouse_contact,
               mi.remarks,
               mi.created_at,
               mi.delete_by,
               mi.delete_time,
               mi.delete_status,
               mi.consignee_code,
               mi.client_region,
               mi.special_mark,
               mi.package_to,
               mi.package_into,
               mi.rs_inventory_id,
               mi.created_by,
               mi.purchase_no,
               mi.brand_name,
               mcd.cargo_details_id,
               mcd.inbound_serial_no,
               mcd.inbound_serial_split,
               mcd.client_code,
               mcd.shipping_mark,
               mcd.item_name,
               mcd.item_en_name,
               mcd.box_count,
               mcd.package_type,
               mcd.unit_gross_weight,
               mcd.unit_length,
               mcd.unit_width,
               mcd.unit_height,
               mcd.unit_volume,
               mcd.damage_status,
               mcd.barcode,
               mcd.inventory_id,
               mcd.created_at,
               mcd.pre_outbound_flag,
               mcd.inventory_status,
               mcd.outbound_record_id,
               mcd.single_piece_weight,
               mcd.single_piece_volume,
               mcd.box_item_count,
               mcd.subtotal_item_count,
               mcd.express_date,
               mcd.express_no,
               mu.full_name as created_user,
               mcd.additional_fee,
            ri2.sub_order_no as sub_order_no
        from mp_inventory mi
                 left join mp_cargo_details mcd on mcd.inventory_id = mi.inventory_id
                 left join rich.mp_user mu on mu.user_id = mi.created_by
                 left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null
                 left join rich.rs_inventory ri2 on ri2.inventory_id = mi.package_to and mi.package_to is not null
    </sql>

    <select id="selectMpInventoryList" parameterType="MpInventory" resultMap="MpInventoryResult">
        <include refid="selectMpInventoryVo"/>
        <where>
            <if test="search != null  and search != ''"> and (mi.client_code = #{search} or mi.consignee_code = #{search} or mi.consignee_name = #{search} or mi.logistics_info = #{search}or mi.inbound_serial_no = #{inboundSerialNo})</if>
            <if test="inventoryStatus != null  and inventoryStatus != ''"> and inventory_status = #{inventoryStatus}</if>
            <if test="inboundSerialNo != null  and inboundSerialNo != ''"> and mi.inbound_serial_no = #{inboundSerialNo}</if>
            <if test="inboundSerialSplit != null  and inboundSerialSplit != ''"> and inbound_serial_split = #{inboundSerialSplit}</if>
            <if test="inboundDate != null "> and inbound_date = #{inboundDate}</if>
            <if test="outboundNo != null  and outboundNo != ''"> and outbound_no = #{outboundNo}</if>
            <if test="forwarderNo != null  and forwarderNo != ''"> and forwarder_no = #{forwarderNo}</if>
            <if test="rentalSettlementDate != null "> and rental_settlement_date = #{rentalSettlementDate}</if>
            <if test="outboundDate != null "> and outbound_date = #{outboundDate}</if>
            <if test="clientCode != null  and clientCode != ''"> and mi.client_code = #{clientCode}</if>
            <if test="subOrderNo != null  and subOrderNo != ''"> and sub_order_no = #{subOrderNo}</if>
            <if test="supplier != null  and supplier != ''"> and supplier = #{supplier}</if>
            <if test="driverInfo != null  and driverInfo != ''"> and driver_info = #{driverInfo}</if>
            <if test="sqdShippingMark != null  and sqdShippingMark != ''"> and sqd_shipping_mark = #{sqdShippingMark}</if>
            <if test="cargoName != null  and cargoName != ''"> and cargo_name like concat('%', #{cargoName}, '%')</if>
            <if test="totalBoxes != null "> and total_boxes = #{totalBoxes}</if>
            <if test="packageType != null  and packageType != ''"> and package_type = #{packageType}</if>
            <if test="totalGrossWeight != null "> and total_gross_weight = #{totalGrossWeight}</if>
            <if test="totalVolume != null "> and total_volume = #{totalVolume}</if>
            <if test="damageStatus != null  and damageStatus != ''"> and damage_status = #{damageStatus}</if>
            <if test="warehouseCode != null  and warehouseCode != ''"> and warehouse_code = #{warehouseCode}</if>
            <if test="recordType != null  and recordType != ''"> and record_type = #{recordType}</if>
            <if test="inboundType != null  and inboundType != ''"> and inbound_type = #{inboundType}</if>
            <if test="cargoNature != null  and cargoNature != ''"> and cargo_nature = #{cargoNature}</if>
            <if test="preOutboundFlag != null  and preOutboundFlag != ''"> and pre_outbound_flag = #{preOutboundFlag}</if>
            <if test="outboundRequestFlag != null  and outboundRequestFlag != ''"> and outbound_request_flag = #{outboundRequestFlag}</if>
            <if test="sqdPlannedOutboundDate != null "> and sqd_planned_outbound_date = #{sqdPlannedOutboundDate}</if>
            <if test="contractType != null  and contractType != ''"> and contract_type = #{contractType}</if>
            <if test="receivedSupplier != null "> and received_supplier = #{receivedSupplier}</if>
            <if test="consigneeName != null  and consigneeName != ''"> and consignee_name like concat('%', #{consigneeName}, '%')</if>
            <if test="consigneeTel != null  and consigneeTel != ''"> and consignee_tel = #{consigneeTel}</if>
            <if test="clientName != null  and clientName != ''"> and client_name like concat('%', #{clientName}, '%')</if>
            <if test="actualDeliveryTime != null "> and actual_delivery_time = #{actualDeliveryTime}</if>
            <if test="ruichiSignFlowNo != null  and ruichiSignFlowNo != ''"> and ruichi_sign_flow_no = #{ruichiSignFlowNo}</if>
            <if test="signerName != null  and signerName != ''"> and signer_name like concat('%', #{signerName}, '%')</if>
            <if test="cargoStatus != null  and cargoStatus != ''"> and cargo_status = #{cargoStatus}</if>
            <if test="outboundOrderNo != null  and outboundOrderNo != ''"> and outbound_order_no = #{outboundOrderNo}</if>
            <if test="deliveryType != null  and deliveryType != ''"> and delivery_type = #{deliveryType}</if>
            <if test="logisticsInfo != null  and logisticsInfo != ''"> and logistics_info = #{logisticsInfo}</if>
            <if test="preEntrySerialNo != null  and preEntrySerialNo != ''"> and pre_entry_serial_no = #{preEntrySerialNo}</if>
            <if test="estimatedArrivalTime != null "> and estimated_arrival_time = #{estimatedArrivalTime}</if>
            <if test="actualArrivalTime != null "> and actual_arrival_time = #{actualArrivalTime}</if>
            <if test="receiptSerialNo != null  and receiptSerialNo != ''"> and receipt_serial_no = #{receiptSerialNo}</if>
            <if test="receiptPerson != null  and receiptPerson != ''"> and receipt_person = #{receiptPerson}</if>
            <if test="claimTime != null "> and claim_time = #{claimTime}</if>
            <if test="claimRemark != null  and claimRemark != ''"> and claim_remark = #{claimRemark}</if>
            <if test="verifyPerson != null  and verifyPerson != ''"> and verify_person = #{verifyPerson}</if>
            <if test="verifyTime != null "> and verify_time = #{verifyTime}</if>
            <if test="goodsStatus != null  and goodsStatus != ''"> and goods_status = #{goodsStatus}</if>
            <if test="recordStatus != null  and recordStatus != ''"> and record_status = #{recordStatus}</if>
<!--            <if test="recordStatus == null"> and record_status != '5'</if>-->
            <if test="destinationCompanyName != null  and destinationCompanyName != ''"> and destination_company_name like concat('%', #{destinationCompanyName}, '%')</if>
            <if test="destinationWarehouseAddress != null  and destinationWarehouseAddress != ''"> and destination_warehouse_address = #{destinationWarehouseAddress}</if>
            <if test="destinationWarehouseContact != null  and destinationWarehouseContact != ''"> and destination_warehouse_contact = #{destinationWarehouseContact}</if>
            <if test="remarks != null  and remarks != ''"> and remarks = #{remarks}</if>
            <if test="createdAt != null "> and created_at = #{createdAt}</if>
            <if test="deleteTime != null "> and delete_time = #{deleteTime}</if>
            <if test="deleteStatus != null "> and delete_status = #{deleteStatus}</if>
            <if test="consigneeCode != null  and consigneeCode != ''"> and consignee_code = #{consigneeCode}</if>
            <if test="clientRegion != null  and clientRegion != ''"> and client_region = #{clientRegion}</if>
            <if test="purchaseNo != null  and purchaseNo != ''"> and mi.purchase_no = #{purchaseNo}</if>
            and mi.delete_status = '0' and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
        </where>
        order by mi.inventory_id desc
    </select>
    
    <select id="selectMpInventoryByInventoryId" parameterType="Long" resultMap="MpInventoryResult">
        <include refid="selectMpInventoryVo"/>
        where mi.inventory_id = #{inventoryId}
    </select>

    <insert id="insertMpInventory" parameterType="MpInventory" useGeneratedKeys="true" keyProperty="inventoryId">
        insert into mp_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryStatus != null">inventory_status,</if>
            <if test="inboundSerialNo != null">inbound_serial_no,</if>
            <if test="inboundSerialSplit != null">inbound_serial_split,</if>
            <if test="inboundDate != null">inbound_date,</if>
            <if test="outboundNo != null">outbound_no,</if>
            <if test="forwarderNo != null">forwarder_no,</if>
            <if test="rentalSettlementDate != null">rental_settlement_date,</if>
            <if test="outboundDate != null">outbound_date,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="subOrderNo != null">sub_order_no,</if>
            <if test="supplier != null">supplier,</if>
            <if test="driverInfo != null">driver_info,</if>
            <if test="sqdShippingMark != null">sqd_shipping_mark,</if>
            <if test="cargoName != null">cargo_name,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="packageType != null">package_type,</if>
            <if test="totalGrossWeight != null">total_gross_weight,</if>
            <if test="totalVolume != null">total_volume,</if>
            <if test="damageStatus != null">damage_status,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="recordType != null">record_type,</if>
            <if test="inboundType != null">inbound_type,</if>
            <if test="cargoNature != null">cargo_nature,</if>
            <if test="preOutboundFlag != null">pre_outbound_flag,</if>
            <if test="outboundRequestFlag != null">outbound_request_flag,</if>
            <if test="sqdPlannedOutboundDate != null">sqd_planned_outbound_date,</if>
            <if test="contractType != null">contract_type,</if>
            <if test="receivedSupplier != null">received_supplier,</if>
            <if test="consigneeName != null">consignee_name,</if>
            <if test="consigneeTel != null">consignee_tel,</if>
            <if test="clientName != null">client_name,</if>
            <if test="actualDeliveryTime != null">actual_delivery_time,</if>
            <if test="ruichiSignFlowNo != null">ruichi_sign_flow_no,</if>
            <if test="signerName != null">signer_name,</if>
            <if test="cargoStatus != null">cargo_status,</if>
            <if test="outboundOrderNo != null">outbound_order_no,</if>
            <if test="deliveryType != null">delivery_type,</if>
            <if test="logisticsInfo != null">logistics_info,</if>
            <if test="preEntrySerialNo != null">pre_entry_serial_no,</if>
            <if test="estimatedArrivalTime != null">estimated_arrival_time,</if>
            <if test="actualArrivalTime != null">actual_arrival_time,</if>
            <if test="receiptSerialNo != null">receipt_serial_no,</if>
            <if test="receiptPerson != null">receipt_person,</if>
            <if test="claimTime != null">claim_time,</if>
            <if test="claimRemark != null">claim_remark,</if>
            <if test="verifyPerson != null">verify_person,</if>
            <if test="verifyTime != null">verify_time,</if>
            <if test="goodsStatus != null">goods_status,</if>
            <if test="recordStatus != null">record_status,</if>
            <if test="destinationCompanyName != null">destination_company_name,</if>
            <if test="destinationWarehouseAddress != null">destination_warehouse_address,</if>
            <if test="destinationWarehouseContact != null">destination_warehouse_contact,</if>
            <if test="remarks != null">remarks,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="deleteTime != null">delete_time,</if>
            <if test="deleteStatus != null">delete_status,</if>
            <if test="consigneeCode != null">consignee_code,</if>
            <if test="clientRegion != null">client_region,</if>
            <if test="specialMark != null">special_mark,</if>
            <if test="packageTo != null">package_to,</if>
            <if test="packageInto != null">package_into,</if>
            <if test="rsInventoryId != null">rs_inventory_id,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="purchaseNo != null">purchase_no,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inventoryStatus != null">#{inventoryStatus},</if>
            <if test="inboundSerialNo != null">#{inboundSerialNo},</if>
            <if test="inboundSerialSplit != null">#{inboundSerialSplit},</if>
            <if test="inboundDate != null">#{inboundDate},</if>
            <if test="outboundNo != null">#{outboundNo},</if>
            <if test="forwarderNo != null">#{forwarderNo},</if>
            <if test="rentalSettlementDate != null">#{rentalSettlementDate},</if>
            <if test="outboundDate != null">#{outboundDate},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="subOrderNo != null">#{subOrderNo},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="driverInfo != null">#{driverInfo},</if>
            <if test="sqdShippingMark != null">#{sqdShippingMark},</if>
            <if test="cargoName != null">#{cargoName},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="packageType != null">#{packageType},</if>
            <if test="totalGrossWeight != null">#{totalGrossWeight},</if>
            <if test="totalVolume != null">#{totalVolume},</if>
            <if test="damageStatus != null">#{damageStatus},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="recordType != null">#{recordType},</if>
            <if test="inboundType != null">#{inboundType},</if>
            <if test="cargoNature != null">#{cargoNature},</if>
            <if test="preOutboundFlag != null">#{preOutboundFlag},</if>
            <if test="outboundRequestFlag != null">#{outboundRequestFlag},</if>
            <if test="sqdPlannedOutboundDate != null">#{sqdPlannedOutboundDate},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="receivedSupplier != null">#{receivedSupplier},</if>
            <if test="consigneeName != null">#{consigneeName},</if>
            <if test="consigneeTel != null">#{consigneeTel},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="actualDeliveryTime != null">#{actualDeliveryTime},</if>
            <if test="ruichiSignFlowNo != null">#{ruichiSignFlowNo},</if>
            <if test="signerName != null">#{signerName},</if>
            <if test="cargoStatus != null">#{cargoStatus},</if>
            <if test="outboundOrderNo != null">#{outboundOrderNo},</if>
            <if test="deliveryType != null">#{deliveryType},</if>
            <if test="logisticsInfo != null">#{logisticsInfo},</if>
            <if test="preEntrySerialNo != null">#{preEntrySerialNo},</if>
            <if test="estimatedArrivalTime != null">#{estimatedArrivalTime},</if>
            <if test="actualArrivalTime != null">#{actualArrivalTime},</if>
            <if test="receiptSerialNo != null">#{receiptSerialNo},</if>
            <if test="receiptPerson != null">#{receiptPerson},</if>
            <if test="claimTime != null">#{claimTime},</if>
            <if test="claimRemark != null">#{claimRemark},</if>
            <if test="verifyPerson != null">#{verifyPerson},</if>
            <if test="verifyTime != null">#{verifyTime},</if>
            <if test="goodsStatus != null">#{goodsStatus},</if>
            <if test="recordStatus != null">#{recordStatus},</if>
            <if test="destinationCompanyName != null">#{destinationCompanyName},</if>
            <if test="destinationWarehouseAddress != null">#{destinationWarehouseAddress},</if>
            <if test="destinationWarehouseContact != null">#{destinationWarehouseContact},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="deleteTime != null">#{deleteTime},</if>
            <if test="deleteStatus != null">#{deleteStatus},</if>
            <if test="consigneeCode != null">#{consigneeCode},</if>
            <if test="clientRegion != null">#{clientRegion},</if>
            <if test="specialMark != null">#{specialMark},</if>
            <if test="packageTo != null">#{packageTo},</if>
            <if test="packageInto != null">#{packageInto},</if>
            <if test="rsInventoryId != null">#{rsInventoryId},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="purchaseNo != null">#{purchaseNo},</if>
         </trim>
    </insert>

    <update id="updateMpInventory" parameterType="MpInventory">
        update mp_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="inventoryStatus != null">inventory_status = #{inventoryStatus},</if>
            <if test="inboundSerialNo != null">inbound_serial_no = #{inboundSerialNo},</if>
            <if test="inboundSerialSplit != null">inbound_serial_split = #{inboundSerialSplit},</if>
            <if test="inboundDate != null">inbound_date = #{inboundDate},</if>
            <if test="outboundNo != null">outbound_no = #{outboundNo},</if>
            <if test="forwarderNo != null">forwarder_no = #{forwarderNo},</if>
            <if test="rentalSettlementDate != null">rental_settlement_date = #{rentalSettlementDate},</if>
            <if test="outboundDate != null">outbound_date = #{outboundDate},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientCode == null">client_code = null,</if>
            <if test="subOrderNo != null">sub_order_no = #{subOrderNo},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="driverInfo != null">driver_info = #{driverInfo},</if>
            <if test="sqdShippingMark != null">sqd_shipping_mark = #{sqdShippingMark},</if>
            <if test="cargoName != null">cargo_name = #{cargoName},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="packageType != null">package_type = #{packageType},</if>
            <if test="totalGrossWeight != null">total_gross_weight = #{totalGrossWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="damageStatus != null">damage_status = #{damageStatus},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="recordType != null">record_type = #{recordType},</if>
            <if test="inboundType != null">inbound_type = #{inboundType},</if>
            <if test="cargoNature != null">cargo_nature = #{cargoNature},</if>
            <if test="preOutboundFlag != null">pre_outbound_flag = #{preOutboundFlag},</if>
            <if test="outboundRequestFlag != null">outbound_request_flag = #{outboundRequestFlag},</if>
            <if test="sqdPlannedOutboundDate != null">sqd_planned_outbound_date = #{sqdPlannedOutboundDate},</if>
            <if test="contractType != null">contract_type = #{contractType},</if>
            <if test="receivedSupplier != null">received_supplier = #{receivedSupplier},</if>
            <if test="consigneeName != null">consignee_name = #{consigneeName},</if>
            <if test="consigneeName == null">consignee_name = null,</if>
            <if test="consigneeTel != null">consignee_tel = #{consigneeTel},</if>
            <if test="consigneeTel == null">consignee_tel = null,</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="clientName == null">client_name = null,</if>
            <if test="actualDeliveryTime != null">actual_delivery_time = #{actualDeliveryTime},</if>
            <if test="ruichiSignFlowNo != null">ruichi_sign_flow_no = #{ruichiSignFlowNo},</if>
            <if test="signerName != null">signer_name = #{signerName},</if>
            <if test="cargoStatus != null">cargo_status = #{cargoStatus},</if>
            <if test="outboundOrderNo != null">outbound_order_no = #{outboundOrderNo},</if>
            <if test="deliveryType != null">delivery_type = #{deliveryType},</if>
            <if test="logisticsInfo != null">logistics_info = #{logisticsInfo},</if>
            <if test="preEntrySerialNo != null">pre_entry_serial_no = #{preEntrySerialNo},</if>
            <if test="estimatedArrivalTime != null">estimated_arrival_time = #{estimatedArrivalTime},</if>
            <if test="actualArrivalTime != null">actual_arrival_time = #{actualArrivalTime},</if>
            <if test="receiptSerialNo != null">receipt_serial_no = #{receiptSerialNo},</if>
            <if test="receiptPerson != null">receipt_person = #{receiptPerson},</if>
            <if test="claimTime != null">claim_time = #{claimTime},</if>
            <if test="claimRemark != null">claim_remark = #{claimRemark},</if>
            <if test="verifyPerson != null">verify_person = #{verifyPerson},</if>
            <if test="verifyTime != null">verify_time = #{verifyTime},</if>
            <if test="goodsStatus != null">goods_status = #{goodsStatus},</if>
            <if test="recordStatus != null">record_status = #{recordStatus},</if>
            <if test="destinationCompanyName != null">destination_company_name = #{destinationCompanyName},</if>
            <if test="destinationWarehouseAddress != null">destination_warehouse_address = #{destinationWarehouseAddress},</if>
            <if test="destinationWarehouseContact != null">destination_warehouse_contact = #{destinationWarehouseContact},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="deleteTime != null">delete_time = #{deleteTime},</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="consigneeCode != null">consignee_code = #{consigneeCode},</if>
            <if test="consigneeCode == null">consignee_code = null,</if>
            <if test="clientRegion != null">client_region = #{clientRegion},</if>
            <if test="clientRegion == null">client_region = null,</if>
            <if test="specialMark != null">special_mark = #{specialMark},</if>
            <if test="packageTo != null">package_to = #{packageTo},</if>
            <if test="packageInto != null">package_into = #{packageInto},</if>
            <if test="rsInventoryId != null">rs_inventory_id = #{rsInventoryId},</if>
            <if test="createdBy != null">created_by = #{createdBy},</if>
            <if test="purchaseNo != null">purchase_no = #{purchaseNo},</if>
        </trim>
        where inventory_id = #{inventoryId}
    </update>

    <delete id="deleteMpInventoryByInventoryId" parameterType="Long">
        update mp_inventory
        set delete_status = '-1'
        where inventory_id = #{inventoryId}
    </delete>

    <delete id="deleteMpInventoryByInventoryIds" parameterType="String">
        update mp_inventory set delete_status = '-1' where inventory_id in 
        <foreach item="inventoryId" collection="array" open="(" separator="," close=")">
            #{inventoryId}
        </foreach>
    </delete>

    <select id="getInboundNo" resultType="int">
        select count(*)
        from rich.mp_inventory
        where mp_inventory.pre_entry_serial_no like concat(
                '',
                'PRE.',
                RIGHT(YEAR(CURRENT_DATE), 2),
                LPAD(MONTH(CURRENT_DATE), 2, '0'),
                '%'
                                                  ) and  pre_entry_serial_no is not null
    </select>

    <select id="selectMpInventoryByExpressNo" parameterType="String" resultMap="MpInventoryResult">
        <include refid="selectMpInventoryVo"/>
        where mi.logistics_info = #{expressNo}
    </select>

    <select id="selectMpInventoryByConsigneeCode" parameterType="String" resultMap="MpInventoryResult">
        <include refid="selectMpInventoryVo"/>
        where mi.consignee_code = #{consigneeCode}
    </select>

    <select id="getStatusNumber" resultType="java.util.Map">
        select
            (select count(*) from mp_inventory mi 
                left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null 
                where mi.cargo_status = 'Pre-entry' and mi.delete_status = '0'
                <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
            ) as preEntryCount,
            (select count(*) from mp_inventory mi 
                left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null 
                where mi.cargo_status = 'In-warehouse' and mi.delete_status = '0'
                <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
                and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
            ) as inWarehouseCount,
            (select count(*) from mp_inventory mi 
                left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null 
                where mi.record_status = '1' and mi.delete_status = '0'
                <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
                and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
            ) as unknownCount,
            (select count(*) from mp_inventory mi 
                left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null 
                where mi.record_status = '3' and mi.delete_status = '0'
                <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
                and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
            ) as uncompletedCount,
            (select count(*) from mp_inventory mi 
                left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null 
                where mi.record_status = '5' and mi.delete_status = '0'
                <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
                and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
            ) as confirmedCount,
            (select count(*) from mp_inventory mi 
                left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null 
                where mi.record_status = '4' and cargo_status = 'In-warehouse' and mi.delete_status = '0'
                <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
                and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
            ) as unconfirmedCount,
            (select count(*) from mp_inventory mi 
                left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null 
                where mi.cargo_status = 'Out-transit' and mi.delete_status = '0'
                <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
                and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
            ) as outTransitCount,
            <!--统计改客户代码的总体积-->
            (select IFNULL(sum(ri.total_volume), 0) from mp_inventory mi
            left join rich.rs_inventory ri on ri.inventory_id = mi.rs_inventory_id and mi.rs_inventory_id is not null
            where mi.cargo_status = 'In-warehouse' and mi.delete_status = '0'
            <if test="clientCode != null and clientCode != ''">and mi.client_code = #{clientCode}  </if>
            and (ri.inventory_status = '0' or mi.rs_inventory_id is null)
            ) as totalVolume
        from dual
    </select>

    
</mapper>