{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\agreementRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\agreementRecord\\index.vue", "mtime": 1722505520065}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_agreementrecord", "require", "name", "dicts", "props", "data", "size", "$store", "state", "app", "openContent", "loading", "ids", "single", "multiple", "showSearch", "total", "agreementrecordList", "title", "oopen", "queryParams", "pageNum", "pageSize", "sqdCompanyId", "company", "companyId", "agreementNumber", "staffId", "form", "rules", "watch", "loadOptions", "open", "val", "$emit", "methods", "getList", "_this", "listAgreementrecord", "then", "response", "rows", "cancel", "reset", "agreementId", "agreementTypeId", "agreementPrice", "agreementStartDate", "agreementEndDate", "isAvailable", "creditLimit", "currencyId", "paymentDateNodeId", "creditDays", "isWorkingDay", "creditLevel", "remark", "isLocked", "deptConfirmed", "deptConfirmedDate", "financeConfirmed", "financeConfirmedDate", "orderNum", "status", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "staffName", "user", "handleUpdate", "row", "_this2", "getAgreementrecord", "submitForm", "_this3", "$refs", "validate", "valid", "updateAgreementrecord", "$modal", "msgSuccess", "addAgreementrecord", "handleDelete", "_this4", "agreementIds", "$confirm", "customClass", "delAgreementrecord", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "dept<PERSON>ock", "_this5", "deptConfirmedId", "sid", "msgError", "financeLock", "_this6", "financeConfirmedId", "getCurrencyId", "exports", "_default"], "sources": ["src/views/system/agreementRecord/index.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag\r\n      v-dialogDragWidth\r\n      :modal-append-to-body=\"false\"\r\n      :visible.sync=\"openContent\"\r\n      append-to-body\r\n      title=\"协议列表\"\r\n      width=\"1600px\"\r\n    >\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:agreementrecord:add']\"\r\n            icon=\"el-icon-plus\"\r\n            plain\r\n            size=\"mini\"\r\n            type=\"primary\"\r\n            @click=\"handleAdd\"\r\n          >新增\r\n          </el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:agreementrecord:remove']\"\r\n            :disabled=\"multiple\"\r\n            icon=\"el-icon-delete\"\r\n            plain\r\n            size=\"mini\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete\"\r\n          >删除\r\n          </el-button>\r\n        </el-col>\r\n        <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:agreementrecord:export']\"\r\n            icon=\"el-icon-download\"\r\n            plain\r\n            size=\"mini\"\r\n            type=\"warning\"\r\n            @click=\"handleExport\"\r\n          >导出\r\n          </el-button>\r\n        </el-col>\r\n        <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n      </el-row>\r\n\r\n      <el-table v-loading=\"loading\" :data=\"agreementrecordList\" border stripe @selection-change=\"handleSelectionChange\">\r\n        <el-table-column align=\"left\" type=\"selection\" width=\"39px\"/>\r\n        <el-table-column align=\"center\" label=\"公司\" prop=\"sqdCompanyId\" width=\"69px\">\r\n          <div>{{ this.company.companyShortName }}</div>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"协议号\" prop=\"agreementNumber\" width=\"69px\"/>\r\n        <el-table-column align=\"center\" label=\"协议类型\" prop=\"agreementTypeId\" show-tooltip-when-overflow\r\n                         width=\"69px\"\r\n        />\r\n        <el-table-column align=\"center\" label=\"负责员工\" prop=\"staffName\" width=\"69px\"/>\r\n        <el-table-column align=\"center\" label=\"协议起始日\" prop=\"agreementStartDate\" width=\"85px\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.agreementStartDate, '{y}-{m}-{d}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"协议终止日\" prop=\"agreementEndDate\" width=\"85px\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.agreementEndDate, '{y}-{m}-{d}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"是否有效\" prop=\"isAvailable\" width=\"69px\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.isAvailable\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column key=\"creditLimit\" align=\"center\" label=\"信用额度\" width=\"69px\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.creditLimit }}万\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"协议币种\" prop=\"currencyId\" width=\"69px\"/>\r\n        <el-table-column align=\"center\" label=\"支付凭据\" prop=\"paymentDateNodeId\" width=\"69px\"/>\r\n        <el-table-column key=\"creditDays\" align=\"center\" label=\"信用周期\" width=\"69px\">\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.creditDays }}月\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"是否工作日\" prop=\"isWorkingDay\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.isWorkingDay\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"信用评级\" prop=\"creditLevel\" width=\"69px\"/>\r\n\r\n        <el-table-column align=\"center\" label=\"是否锁定\" prop=\"isLocked\" width=\"69px\">\r\n          <template slot-scope=\"scope\">\r\n            <dict-tag :options=\"dict.type.sys_is_lock\" :value=\"scope.row.isLocked\"/>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"经理确认时间\" prop=\"deptConfirmedDate\" width=\"100px\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.deptConfirmedDate, '{y}-{m}-{d}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"财务确认时间\" prop=\"financeConfirmedDate\" width=\"100px\">\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.financeConfirmedDate, '{y}-{m}-{d}') }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"结款日\" prop=\"settlementDate\" width=\"58px\"/>\r\n        <el-table-column align=\"center\" label=\"备注\" prop=\"remark\" width=\"48px\"/>\r\n        <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"48px\">\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"height: 15px;padding: 0;margin: 0\">\r\n              <el-button v-if=\"scope.row.isLocked!='1'\" v-hasPermi=\"['system:agreementrecord:edit']\" :size=\"size\"\r\n                         icon=\"el-icon-edit\"\r\n                         style=\"display: flex\" type=\"success\"\r\n                         @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n            </div>\r\n            <div style=\"height: 15px;padding: 0;margin: 0\">\r\n              <el-button v-if=\"scope.row.isLocked!='1'\" v-hasPermi=\"['system:agreementrecord:remove']\" :size=\"size\"\r\n                         icon=\"el-icon-delete\"\r\n                         style=\"display: flex\" type=\"danger\"\r\n                         @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <pagination\r\n        v-show=\"total>0\"\r\n        :limit.sync=\"queryParams.pageSize\"\r\n        :page.sync=\"queryParams.pageNum\"\r\n        :total=\"total\"\r\n        @pagination=\"getList\"\r\n      />\r\n    </el-dialog>\r\n    <!-- 添加或修改协议记录对话框 -->\r\n    <el-dialog v-dialogDrag v-dialogDragWidth :append-to-body=\"true\" :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n               :title=\"title\" :visible.sync=\"oopen\"\r\n               width=\"800px\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"合作公司\" prop=\"sqdCompanyId\">\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ this.company.companyShortName }}</a>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"是否有效\" prop=\"isAvailable\">\r\n              <el-select v-model=\"form.isAvailable\" placeholder=\"是否有效\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_yes_no\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"协议号\" prop=\"agreementNumber\">\r\n              <el-input v-model=\"form.agreementNumber\" placeholder=\"与合作公司签订的协议号\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"协议类型\" prop=\"agreementTypeId\">\r\n              <el-input v-model=\"form.agreementTypeId\" placeholder=\"协议类型\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"负责员工\" prop=\"staffName\">\r\n            {{ this.form.staffName }}\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"协议起始日\" prop=\"agreementStartDate\">\r\n              <el-date-picker v-model=\"form.agreementStartDate\"\r\n                              clearable\r\n                              placeholder=\"协议有效期从\"\r\n                              style=\"width: 100%\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"协议终止日\" prop=\"agreementEndDate\">\r\n              <el-date-picker v-model=\"form.agreementEndDate\"\r\n                              clearable\r\n                              placeholder=\"协议有效期至\"\r\n                              style=\"width: 100%\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"协议币种\" prop=\"currencyId\">\r\n              <tree-select :pass=\"form.currencyId\" :type=\"'currency'\" @return=\"getCurrencyId\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"信用额度\" prop=\"creditLimit\">\r\n              <el-input-number :controls=\"false\" v-model=\"form.creditLimit\" :precision=\"2\" :step=\"0.01\"\r\n                               placeholder=\"信用额度，默认0\"\r\n              />\r\n              万\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"支付凭据\" prop=\"paymentDateNodeId\">\r\n              <el-input v-model=\"form.paymentDateNodeId\" placeholder=\"支付时间节点凭据\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"信用周期\" prop=\"creditDays\">\r\n              <el-input-number :controls=\"false\" v-model=\"form.creditDays\" :max=\"12\" :precision=\"2\" :step=\"0.01\"\r\n                               placeholder=\"信用周期\"\r\n              />\r\n              月\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"信用评级\" prop=\"creditLevel\">\r\n              <el-input v-model=\"form.creditLevel\" placeholder=\"信用评级，待定\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"8\">\r\n            <el-form-item label=\"是否工作日\" prop=\"isWorkingDay\">\r\n              <el-select v-model=\"form.isWorkingDay\" placeholder=\"是否工作日\">\r\n                <el-option\r\n                  v-for=\"dict in dict.type.sys_yes_no\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.label\"\r\n                  :value=\"dict.value\"\r\n                />\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-form-item label=\"是否已锁定\" prop=\"isLocked\">\r\n            <dict-tag :options=\"dict.type.sys_is_lock\" :value=\"form.isLocked\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n        <el-form-item label=\"结款日\" prop=\"settlementDate\">\r\n          <el-date-picker v-model=\"form.settlementDate\"\r\n                          clearable\r\n                          placeholder=\"结款日\"\r\n                          style=\"width: 100%\"\r\n                          type=\"date\"\r\n                          value-format=\"yyyy-MM-dd\"\r\n          >\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"\r\n          />\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门确认\" prop=\"deptConfirmed\">\r\n              <el-row>\r\n                <el-col :span=\"4\">\r\n                  <dict-tag :options=\"dict.type.sys_is_confirm\" :value=\"form.deptConfirmed\"/>\r\n                </el-col>\r\n                <el-col :span=\"20\">\r\n                  <el-button\r\n                    v-hasPermi=\"['system:agreementrecord:deptlock']\"\r\n                    v-if=\"form.deptConfirmed==0\"\r\n                    icon=\"el-icon-lock\"\r\n                    plain\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    @click=\"deptLock\"\r\n                  >锁定\r\n                  </el-button>\r\n                  <el-button\r\n                    v-hasPermi=\"['system:agreementrecord:deptunlock']\"\r\n                    v-if=\"form.deptConfirmed==1\"\r\n                    icon=\"el-icon-unlock\"\r\n                    plain\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    @click=\"deptLock\"\r\n                  >解锁\r\n                  </el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门确认时间\" prop=\"deptConfirmedDate\">\r\n              {{ form.deptConfirmedDate }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"财务确认\" prop=\"financeConfirmed\">\r\n              <el-row>\r\n                <el-col :span=\"4\">\r\n                  <dict-tag :options=\"dict.type.sys_is_confirm\" :value=\"form.financeConfirmed\"/>\r\n                </el-col>\r\n                <el-col :span=\"20\">\r\n                  <el-button\r\n                    v-hasPermi=\"['system:agreementrecord:financelock']\"\r\n                    v-if=\"form.financeConfirmed==0\"\r\n                    icon=\"el-icon-lock\"\r\n                    plain\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    @click=\"financeLock\"\r\n                  >锁定\r\n                  </el-button>\r\n                  <el-button\r\n                    v-hasPermi=\"['system:agreementrecord:financeunlock']\"\r\n                    v-if=\"form.financeConfirmed==1\"\r\n                    icon=\"el-icon-unlock\"\r\n                    plain\r\n                    size=\"mini\"\r\n                    type=\"primary\"\r\n                    @click=\"financeLock\"\r\n                  >解锁\r\n                  </el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"财务确认时间\" prop=\"financeConfirmedDate\">\r\n              {{ form.financeConfirmedDate }}\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addAgreementrecord,\r\n  delAgreementrecord,\r\n  getAgreementrecord,\r\n  listAgreementrecord,\r\n  updateAgreementrecord\r\n} from '@/api/system/agreementrecord'\r\n\r\nexport default {\r\n  name: 'agreementRecord',\r\n  dicts: ['sys_yes_no', 'sys_is_lock', 'sys_is_confirm'],\r\n  props: ['type', 'open', 'loadOptions', 'company'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini',\r\n      openContent: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 协议记录表格数据\r\n      agreementrecordList: [],\r\n      // 弹出层标题\r\n      title: '',\r\n      // 是否显示弹出层\r\n      oopen: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        sqdCompanyId: this.company.companyId,\r\n        agreementNumber: null,\r\n        staffId: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n  },\r\n  watch: {\r\n    loadOptions: function () {\r\n      this.agreementrecordList = this.loadOptions\r\n      this.loading = false\r\n    },\r\n    open: function (val) {\r\n      this.openContent = val\r\n    },\r\n    openContent(val) {\r\n      if (val == false) {\r\n        this.$emit('openCommunications')\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询协议记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      this.queryParams.sqdCompanyId = this.company.companyId\r\n      listAgreementrecord(this.queryParams).then(response => {\r\n        this.agreementrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.oopen = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        agreementId: null,\r\n        sqdCompanyId: null,\r\n        agreementNumber: null,\r\n        agreementTypeId: null,\r\n        agreementPrice: null,\r\n        staffId: null,\r\n        agreementStartDate: null,\r\n        agreementEndDate: null,\r\n        isAvailable: null,\r\n        creditLimit: null,\r\n        currencyId: null,\r\n        paymentDateNodeId: null,\r\n        creditDays: null,\r\n        isWorkingDay: null,\r\n        creditLevel: null,\r\n        remark: null,\r\n        isLocked: '0',\r\n        deptConfirmed: '0',\r\n        deptConfirmedDate: null,\r\n        financeConfirmed: '0',\r\n        financeConfirmedDate: null,\r\n        orderNum: null,\r\n        status: '0'\r\n      }\r\n      this.resetForm('form')\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm('queryForm')\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.agreementId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.oopen = true\r\n      this.title = '添加协议记录'\r\n      this.form.staffName = this.$store.state.user.name\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const agreementId = row.agreementId || this.ids\r\n      getAgreementrecord(agreementId).then(response => {\r\n        this.form = response.data\r\n        this.oopen = true\r\n        this.title = '修改协议记录'\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs['form'].validate(valid => {\r\n        if (valid) {\r\n          this.form.sqdCompanyId = this.company.companyId\r\n          if (this.form.agreementId != null) {\r\n            updateAgreementrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess('修改成功')\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            this.form.sqdCompanyId = this.company.companyId\r\n            addAgreementrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess('新增成功')\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const agreementIds = row.agreementId || this.ids\r\n      this.$confirm('是否确认删除协议记录编号为\"' + agreementIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delAgreementrecord(agreementIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess('删除成功')\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/agreementrecord/export', {\r\n        ...this.queryParams\r\n      }, `agreementrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    deptLock() {\r\n      if (this.form.agreementId != null) {\r\n        this.form.deptConfirmed = this.form.deptConfirmed == 0 ? 1 : 0\r\n        this.form.deptConfirmedId = this.$store.state.user.sid\r\n        updateAgreementrecord(this.form).then(response => {\r\n          this.$modal.msgSuccess('修改成功')\r\n          this.oopen = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        this.$modal.msgError('错误操作')\r\n      }\r\n    },\r\n    financeLock() {\r\n      if (this.form.agreementId != null) {\r\n        this.form.financeConfirmed = this.form.financeConfirmed == 0 ? 1 : 0\r\n        this.form.financeConfirmedId = this.$store.state.user.sid\r\n        updateAgreementrecord(this.form).then(response => {\r\n          this.$modal.msgSuccess('修改成功')\r\n          this.oopen = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        this.$modal.msgError('错误操作')\r\n      }\r\n    },\r\n    getCurrencyId(val) {\r\n      this.form.currencyId = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;AA2WA,IAAAA,gBAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAQA;EACAC,IAAA;EACAC,KAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACAI,WAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,mBAAA;MACA;MACAC,KAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,YAAA,OAAAC,OAAA,CAAAC,SAAA;QACAC,eAAA;QACAC,OAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAd,mBAAA,QAAAc,WAAA;MACA,KAAApB,OAAA;IACA;IACAqB,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAAvB,WAAA,GAAAuB,GAAA;IACA;IACAvB,WAAA,WAAAA,YAAAuB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,KAAA;MACA;IACA;EACA;EACAC,OAAA;IACA,eACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,KAAA1B,OAAA;MACA,KAAAS,WAAA,CAAAG,YAAA,QAAAC,OAAA,CAAAC,SAAA;MACA,IAAAa,oCAAA,OAAAlB,WAAA,EAAAmB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAApB,mBAAA,GAAAuB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAArB,KAAA,GAAAwB,QAAA,CAAAxB,KAAA;QACAqB,KAAA,CAAA1B,OAAA;MACA;IACA;IACA;IACA+B,MAAA,WAAAA,OAAA;MACA,KAAAvB,KAAA;MACA,KAAAwB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAf,IAAA;QACAgB,WAAA;QACArB,YAAA;QACAG,eAAA;QACAmB,eAAA;QACAC,cAAA;QACAnB,OAAA;QACAoB,kBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,WAAA;QACAC,UAAA;QACAC,iBAAA;QACAC,UAAA;QACAC,YAAA;QACAC,WAAA;QACAC,MAAA;QACAC,QAAA;QACAC,aAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,QAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA7C,WAAA,CAAAC,OAAA;MACA,KAAAe,OAAA;IACA;IACA,aACA8B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAxD,GAAA,GAAAwD,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA1B,WAAA;MAAA;MACA,KAAA/B,MAAA,GAAAuD,SAAA,CAAAG,MAAA;MACA,KAAAzD,QAAA,IAAAsD,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAA7B,KAAA;MACA,KAAAxB,KAAA;MACA,KAAAD,KAAA;MACA,KAAAU,IAAA,CAAA6C,SAAA,QAAAlE,MAAA,CAAAC,KAAA,CAAAkE,IAAA,CAAAxE,IAAA;IACA;IACA,aACAyE,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAlC,KAAA;MACA,IAAAC,WAAA,GAAAgC,GAAA,CAAAhC,WAAA,SAAAhC,GAAA;MACA,IAAAkE,mCAAA,EAAAlC,WAAA,EAAAL,IAAA,WAAAC,QAAA;QACAqC,MAAA,CAAAjD,IAAA,GAAAY,QAAA,CAAAnC,IAAA;QACAwE,MAAA,CAAA1D,KAAA;QACA0D,MAAA,CAAA3D,KAAA;MACA;IACA;IACA,WACA6D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAApD,IAAA,CAAAL,YAAA,GAAAyD,MAAA,CAAAxD,OAAA,CAAAC,SAAA;UACA,IAAAuD,MAAA,CAAApD,IAAA,CAAAgB,WAAA;YACA,IAAAwC,sCAAA,EAAAJ,MAAA,CAAApD,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAwC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA7D,KAAA;cACA6D,MAAA,CAAA5C,OAAA;YACA;UACA;YACA4C,MAAA,CAAApD,IAAA,CAAAL,YAAA,GAAAyD,MAAA,CAAAxD,OAAA,CAAAC,SAAA;YACA,IAAA8D,mCAAA,EAAAP,MAAA,CAAApD,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACAwC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA7D,KAAA;cACA6D,MAAA,CAAA5C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,YAAA,GAAAd,GAAA,CAAAhC,WAAA,SAAAhC,GAAA;MACA,KAAA+E,QAAA,oBAAAD,YAAA;QAAAE,WAAA;MAAA,GAAArD,IAAA;QACA,WAAAsD,mCAAA,EAAAH,YAAA;MACA,GAAAnD,IAAA;QACAkD,MAAA,CAAArD,OAAA;QACAqD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,sCAAAC,cAAA,CAAAC,OAAA,MACA,KAAA9E,WAAA,sBAAA+E,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAA3E,IAAA,CAAAgB,WAAA;QACA,KAAAhB,IAAA,CAAA8B,aAAA,QAAA9B,IAAA,CAAA8B,aAAA;QACA,KAAA9B,IAAA,CAAA4E,eAAA,QAAAjG,MAAA,CAAAC,KAAA,CAAAkE,IAAA,CAAA+B,GAAA;QACA,IAAArB,sCAAA,OAAAxD,IAAA,EAAAW,IAAA,WAAAC,QAAA;UACA+D,MAAA,CAAAlB,MAAA,CAAAC,UAAA;UACAiB,MAAA,CAAApF,KAAA;UACAoF,MAAA,CAAAnE,OAAA;QACA;MACA;QACA,KAAAiD,MAAA,CAAAqB,QAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAhF,IAAA,CAAAgB,WAAA;QACA,KAAAhB,IAAA,CAAAgC,gBAAA,QAAAhC,IAAA,CAAAgC,gBAAA;QACA,KAAAhC,IAAA,CAAAiF,kBAAA,QAAAtG,MAAA,CAAAC,KAAA,CAAAkE,IAAA,CAAA+B,GAAA;QACA,IAAArB,sCAAA,OAAAxD,IAAA,EAAAW,IAAA,WAAAC,QAAA;UACAoE,MAAA,CAAAvB,MAAA,CAAAC,UAAA;UACAsB,MAAA,CAAAzF,KAAA;UACAyF,MAAA,CAAAxE,OAAA;QACA;MACA;QACA,KAAAiD,MAAA,CAAAqB,QAAA;MACA;IACA;IACAI,aAAA,WAAAA,cAAA7E,GAAA;MACA,KAAAL,IAAA,CAAAuB,UAAA,GAAAlB,GAAA;IACA;EACA;AACA;AAAA8E,OAAA,CAAAb,OAAA,GAAAc,QAAA"}]}