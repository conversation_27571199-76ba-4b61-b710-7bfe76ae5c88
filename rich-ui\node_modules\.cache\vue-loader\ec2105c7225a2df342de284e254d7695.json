{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\index.vue?vue&type=style&index=0&id=18be2bfa&scoped=true&lang=scss&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\index.vue", "mtime": 1754881964216}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouc3NzIHsNCiAgLy9wYWRkaW5nLWxlZnQ6IDVweCAhaW1wb3J0YW50Ow0KICBwYWRkaW5nLXJpZ2h0OiAxNXB4ICFpbXBvcnRhbnQ7DQogIHdpZHRoOiAxMDAlOw0KfQ0KDQouY3JlZGl0TGltaXQgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KfQ0KDQoubGltaXQgew0KICBmbGV4OiAzDQp9DQoNCi5jdXJyZW5jeSB7DQogIGZsZXg6IDENCn0NCg0KLmNvbmZpcm0gew0KICBkaXNwbGF5OiBmbGV4Ow0KICBmbGV4LWRpcmVjdGlvbjogcm93Ow0KICBmbGV4LXdyYXA6IHdyYXA7DQp9DQoNCi5ib3gtY2FyZCB7DQogIHdpZHRoOiAyMCU7DQogIGZsZXgtd3JhcDogd3JhcDsNCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0kEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/company", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-if=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" :size=\"size\"\r\n                 label-width=\"35px\"\r\n        >\r\n          <el-form-item v-if=\"roleClient==='1'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBFStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBFStaffId\" @open=\"loadSales\" @select=\"handleSelectBFStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"roleTypeId==='2'\" label=\"所属\" prop=\"queryStaffId\">\r\n            <treeselect v-model=\"queryBStaffId\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanBStaffId\" @open=\"loadBusinesses\" @select=\"handleSelectBStaffId\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <!--          <el-form-item v-if=\"roleTypeId==='1'\" label=\"闲置\">\r\n                      <el-select v-model=\"queryParams.idleStatus\" placeholder=\"是否闲置\" style=\"width: 100%\"\r\n                                 @change=\"handleQuery\"\r\n                      >\r\n                        <el-option\r\n                          v-for=\"dict in dict.type.sys_is_idle\"\r\n                          :key=\"dict.value\"\r\n                          :label=\"dict.label\"\r\n                          :value=\"dict.value\"\r\n                        ></el-option>\r\n                      </el-select>\r\n                    </el-form-item>-->\r\n          <el-form-item label=\"客户\" prop=\"companyQuery\">\r\n            <el-input v-model=\"queryParams.companyQuery\" clearable placeholder=\"客户名称/代码\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!--行政区域-->\r\n          <el-form-item label=\"地址\" prop=\"locationId\">\r\n            <location-select :multiple=\"false\" :pass=\"queryParams.locationId\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationId\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"信用\" prop=\"creditLevel\">\r\n            <tree-select :multiple=\"false\" :pass=\"queryParams.creditLevel\"\r\n                         :type=\"'creditLevel'\" style=\"width: 100%\"\r\n                         @return=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <!--供应商提供哪些服务如：海运、铁路等-->\r\n          <el-form-item label=\"服务\" prop=\"serviceTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.serviceTypeIds\"\r\n                         :placeholder=\"'服务类型'\" :type=\"'serviceType'\" style=\"width: 100%\"\r\n                         :d-load=\"true\" @return=\"queryServiceTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"locationDepartureIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDepartureIds\"\r\n                             :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDepartureIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的区域-->\r\n          <el-form-item label=\"目的\" prop=\"locationDestinationIds\">\r\n            <location-select :multiple=\"true\" :pass=\"queryParams.locationDestinationIds\"\r\n                             :en=\"true\" :load-options=\"locationOptions\" style=\"width: 100%\"\r\n                             @return=\"queryLocationDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--公司角色-->\r\n          <el-form-item label=\"角色\" prop=\"locationDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.roleIds\" :type=\"'companyRole'\"\r\n                         @return=\"queryCompanyRoleIds\"\r\n            />\r\n          </el-form-item>\r\n          <!--目的航线-->\r\n          <el-form-item label=\"航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.lineDestinationIds\"\r\n                         :placeholder=\"'目的航线'\" :type=\"'line'\" style=\"width: 100%\"\r\n                         :d-load=\"true\" @return=\"queryLineDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"货物\" prop=\"cargoTypeIds\">\r\n            <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"queryParams.cargoTypeIds\" :placeholder=\"'货物特征'\"\r\n                         :d-load=\"true\" :type=\"'cargoType'\" style=\"width: 100%\" @return=\"queryCargoTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"承运\" prop=\"carrierIds\">\r\n            <treeselect v-model=\"queryCarrierIds\" :disable-fuzzy-matching=\"true\" :disable-branch-nodes=\"true\"\r\n                        :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                        :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\" @open=\"loadCarrier\"\r\n                        placeholder=\"优选承运人\" style=\"width: 100%\" @deselect=\"handleDeselectQueryCarrierIds\"\r\n                        @select=\"handleSelectQueryCarrierIds\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{ node.raw.carrier.carrierIntlCode }}\r\n                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"评级\" prop=\"creditLevel\">\r\n            <el-input v-model=\"queryParams.creditLevel\" placeholder=\"A~E\" style=\"width: 100%\"\r\n                      @change=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button :size=\"size\" icon=\"el-icon-search\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button :size=\"size\" icon=\"el-icon-refresh\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:company:add']\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:company:export']\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              :disabled=\"single\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-user-solid\"\r\n              type=\"info\"\r\n              @click=\"handleBlackList\"\r\n            >\r\n              拉黑\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              :disabled=\"selectTwo\"\r\n              :size=\"size\"\r\n              icon=\"el-icon-connection\"\r\n              @click=\"handleMergeCompany\"\r\n            >\r\n              合并\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :columns=\"columns\" :showSearch.sync=\"showSearch\"\r\n                         :types=\"this.roleTypeId=='2'?'supplier':this.roleTypeId=='1'?'client':''\"\r\n                         @queryTable=\"getList\" @refreshColumns=\"refreshColumns\"\r\n          />\r\n        </el-row>\r\n        <!--合并公司-->\r\n        <el-dialog\r\n          :close-on-click-modal=\"false\"\r\n          :modal-append-to-body=\"false\"\r\n          v-dialogDrag v-dialogDragWidth\r\n          title=\"选择保留公司\"\r\n          :visible.sync=\"merge\"\r\n          width=\"800px\"\r\n        >\r\n          <el-row v-if=\"mergeList.length>0\" :gutter=\"5\">\r\n            <el-col :span=\"12\">\r\n              <el-descriptions title=\"公司1\" direction=\"vertical\" border>\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[0].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[0].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[0].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[0].companyId,mergeList[1].companyId)\">\r\n                    留下{{ mergeList[0].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-descriptions title=\"公司2\" direction=\"vertical\" border>\r\n                <el-descriptions-item label=\"简称\">{{ mergeList[1].companyShortName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"中文名\">{{ mergeList[1].companyLocalName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"英文名\">{{ mergeList[1].companyEnName }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"选择\">\r\n                  <el-button @click=\"handleMerge(mergeList[1].companyId,mergeList[0].companyId)\">\r\n                    留下{{ mergeList[1].companyShortName }}\r\n                  </el-button>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </el-col>\r\n          </el-row>\r\n        </el-dialog>\r\n        <!--公司列表-->\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"companyList\" border\r\n                  @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column\r\n            v-for=\"data in columns\"\r\n            v-if=\"data.visible\"\r\n            :key=\"data.key\"\r\n            :align=\"data.align\"\r\n            :label=\"data.label\"\r\n            :width=\"data.width\"\r\n            :show-overflow-tooltip=\"data.tooltip\"\r\n          >\r\n            <template v-slot=\"scope\">\r\n              <!--内置组件，根据:is动态加载组件-->\r\n              <!--渲染组件根据组件内部的return方法返回指针渲染内容-->\r\n              <component :is=\"data.prop\" :scope=\"scope\" @return=\"getReturn\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"结款方式\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0;\">{{ }}</h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"额度\" width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <!--币种-->\r\n              <span style=\"margin: 0;\">{{\r\n                  (roleClient == 1 || roleRich == 1) ? scope.row.receiveCurrencyCode : scope.row.payCurrencyCode\r\n                }}</span>\r\n              <!--额度-->\r\n              <span style=\"margin: 0;\">{{\r\n                  formatDisplayCreditLimit(roleClient == 1 || roleRich == 1 ? scope.row.receiveCreditLimit : scope.row.payCreditLimit)\r\n                }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"录入人\" show-overflow-tooltip width=\"70\">\r\n            <template slot-scope=\"scope\">\r\n              <div>\r\n                <h6 class=\"text-display\" style=\"margin: 0;\">{{ scope.row.updateByName }}</h6>\r\n                <h6 class=\"text-display\" style=\"margin: 0;\">{{ parseTime(scope.row.updateTime, \"{y}.{m}.{d}\") }}</h6>\r\n              </div>\r\n\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"90px\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button v-hasPermi=\"['system:company:edit']\"\r\n                         :size=\"size\" icon=\"el-icon-edit\" type=\"success\" style=\"margin: 0 3px\"\r\n                         @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button v-hasPermi=\"['system:company:remove']\"\r\n                         :size=\"size\" icon=\"el-icon-delete\" type=\"danger\" style=\"margin: 0\"\r\n                         @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <!-- 分页 -->\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改公司对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      append-to-body\r\n      v-dialogDrag v-dialogDragWidth\r\n      :title=\"title\"\r\n      :visible.sync=\"openCompany\"\r\n      width=\"55%\"\r\n    >\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"68px\" class=\"edit\">\r\n        <el-row :gutter=\"5\">\r\n          <el-col :span=\"12\">\r\n            <!--从属信息-->\r\n            <el-row v-if=\"roleClient==='1'\" :gutter=\"5\">\r\n              <el-divider content-position=\"left\">从属信息</el-divider>\r\n              <el-row :gutter=\"5\">\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务员\">\r\n                    <treeselect v-model=\"belongTo\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                :class=\"isLock ?'disable-form':''\" :disabled=\" isLock\"\r\n                                :flatten-search-results=\"true\"\r\n                                :normalizer=\"staffNormalizer\" :options=\"belongList\"\r\n                                :show-count=\"true\" class=\"sss\" placeholder=\"选择所属人\" @input=\"handleDeselectBelongTo\"\r\n                                @open=\"loadSales\" @select=\"handleSelectBelongTo\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务助理\">\r\n                    <treeselect v-model=\"followUp\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                                :options=\"belongList\" :show-count=\"true\" class=\"sss\"\r\n                                :class=\"isLock ?'disable-form':''\" :disabled=\"isLock\"\r\n                                placeholder=\"业务员自己跟进的情况无须填写\"\r\n                                @input=\"handleDeselectFollowUp\" @open=\"loadSales\" @select=\"handleSelectFollowUp\"\r\n                    >\r\n                      <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                        {{\r\n                          node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                        }}\r\n                      </div>\r\n                      <label slot=\"option-label\"\r\n                             slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                             :class=\"labelClassName\"\r\n                      >\r\n                        {{\r\n                          node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                        }}\r\n                        <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                      </label>\r\n                    </treeselect>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"所属公司\">\r\n                    <tree-select\r\n                      :pass=\"form.companyBelongTo\" :placeholder=\"'收付路径'\"\r\n                      :class=\"isLock ?'disable-form':''\" :disabled=\"isLock\" :type=\"'rsPaymentTitle'\"\r\n                      @return=\"form.companyBelongTo=$event\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--基本信息-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">基础资料</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"公司代码\" prop=\"companyTaxCode\">\r\n                    <el-input v-model=\"form.companyTaxCode\" class=\" disable-form\" disabled\r\n                              :class=\"'sss'\" placeholder=\"国际通用简称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"税号\" prop=\"companyTaxCode\">\r\n                    <el-input v-model=\"form.taxNo\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\"\r\n                              placeholder=\"公司税号/统一社会信用代码\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :rules=\"[{ required: true, message: '简称不能为空', trigger: 'blur'},{validator: validateCompanyShortName, trigger: 'blur'}]\"\r\n                    label=\"公司简称\"\r\n                    prop=\"companyShortName\"\r\n                  >\r\n                    <el-input v-model=\"form.companyShortName\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\"\r\n                              class=\"sss\" placeholder=\"公司简称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item :rules=\"[{ required: true, message: '抬头不能为空', trigger: 'blur'}]\" label=\"发票抬头\"\r\n                                prop=\"companyLocalName\">\r\n                    <el-input v-model=\"form.companyLocalName\" :class=\"basicLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"basicLock || commonLock\" placeholder=\"公司发票抬头\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"行政区域\" prop=\"locationId\">\r\n                    <location-select :load-options=\"locationOptions\" :multiple=\"false\" :pass=\"form.locationId\"\r\n                                     class=\"sss\" @return=\"getLocationId\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"详细地址\" prop=\"locationDetail\">\r\n                    <el-input v-model=\"form.locationDetail\"\r\n                              placeholder=\"详细地址信息\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"主联系人\" prop=\"mainStaffOfficialName\">\r\n                    <el-input v-model=\"form.mainStaffOfficialName\" class=\"sss\"\r\n                              placeholder=\"联系人名称\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"微信\" prop=\"staffWechat\">\r\n                    <el-input v-model=\"form.staffWechat\" class=\"sss\"\r\n                              placeholder=\"联系人微信\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"QQ\" prop=\"staffQq\">\r\n                    <el-input v-model=\"form.staffQq\"\r\n                              placeholder=\"联系人QQ\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"手机\" prop=\"staffMobile\">\r\n                    <el-input v-model=\"form.staffMobile\" class=\"sss\"\r\n                              placeholder=\"手机\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"邮箱\" prop=\"staffEmail\">\r\n                    <el-input v-model=\"form.staffEmail\"\r\n                              placeholder=\"联系人邮箱\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"Whatsapp\" prop=\"staffWhatsapp\">\r\n                    <el-input v-model=\"form.staffWhatsapp\" class=\"sss\"\r\n                              placeholder=\"联系人Whatsapp\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"联系方式\" prop=\"staffOtherContact\">\r\n                    <el-input v-model=\"form.staffOtherContact\"\r\n                              placeholder=\"员工其他联系方式\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--协议信息-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">协议信息</el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"协议号\" prop=\"agreementNumber\">\r\n                    <el-input v-model=\"form.agreementNumber\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\"\r\n                              class=\"sss\" placeholder=\"协议号\"\r\n\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"16\">\r\n                  <el-form-item label=\"起讫日期\" prop=\"agreementStartDate\">\r\n                    <el-date-picker\r\n                      v-model=\"form.agreementDateRange\"\r\n                      :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                      :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                      end-placeholder=\"结束日期\"\r\n                      range-separator=\"至\"\r\n                      start-placeholder=\"开始日期\"\r\n                      style=\"width: 100%;\"\r\n                      :default-time=\"['00:00:00', '23:59:59']\"\r\n                      type=\"daterange\"\r\n                      @input=\"changeDate\"\r\n                      value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                    >\r\n                    </el-date-picker>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"信用等级\" prop=\"creditLevel\">\r\n                    <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\" :pass=\"form.creditLevel\"\r\n                                 :type=\"'creditLevel'\" class=\"sss\"\r\n                                 @return=\"getcreditLevel\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款方式':roleSupplier==1|| roleSupport==1?'付款方式':''\"\r\n                    prop=\"creditLevel\"\r\n                  >\r\n                    <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\"\r\n                                 v-if=\"roleClient==1|| roleRich==1\" :multiple=\"false\"\r\n                                 :pass=\"form.receiveWay\" :placeholder=\"'收款方式'\"\r\n                                 :flat=\"false\" :type=\"'releaseTypeCode'\"\r\n                                 class=\"sss\"\r\n                                 @returnData=\"form.receiveWay=$event.releaseTypeShortName\"\r\n                    />\r\n                    <tree-select v-if=\"roleSupplier==1|| roleSupport==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :disabled=\"agreementLock || commonLock\" :pass=\"form.payWay\"\r\n                                 :placeholder=\"'付款方式'\" :type=\"'releaseTypeCode'\" class=\"sss\"\r\n                                 @returnData=\"form.payWay=$event.releaseTypeShortName\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款信用':roleSupplier==1|| roleSupport==1?'付款信用':''\"\r\n                    prop=\"creditLevel\"\r\n                  >\r\n                    <el-col v-if=\"roleClient==1|| roleRich==1\">\r\n                      <el-col :span=\"9\">\r\n                        <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                     :disabled=\"agreementLock || commonLock\"\r\n                                     :pass=\"form.receiveCurrencyCode\"\r\n                                     class=\" currency\" @return=\"form.receiveCurrencyCode=$event\"\r\n                                     :type=\"'currency'\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"15\">\r\n                        <el-input v-model=\"form.receiveCreditLimit\"\r\n                                  :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                  :disabled=\"agreementLock || commonLock\"\r\n                                  placeholder=\"信用额度(填入整数)\" @change=\"formatCreditLimit\"\r\n                                  class=\" limit\"\r\n                        />\r\n                      </el-col>\r\n                    </el-col>\r\n                    <el-col v-if=\"roleSupplier==1|| roleSupport==1\">\r\n                      <el-col :span=\"9\">\r\n                        <tree-select :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                     :disabled=\"agreementLock || commonLock\"\r\n                                     :pass=\"form.payCurrencyCode\"\r\n                                     class=\" currency\" @return=\"form.payCurrencyCode=$event\"\r\n                                     :type=\"'currency'\"\r\n                        />\r\n                      </el-col>\r\n                      <el-col :span=\"15\">\r\n                        <el-input v-model=\"form.payCreditLimit\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                  :disabled=\"agreementLock || commonLock\"\r\n                                  placeholder=\"信用额度(填入整数)\" @change=\"formatCreditLimit\"\r\n                                  class=\" limit\"\r\n                        />\r\n                      </el-col>\r\n                    </el-col>\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"账单基准\" prop=\"creditLevel\">\r\n                    <tree-select v-if=\"roleClient==1 || roleRich==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\"\r\n                                 :flat=\"false\" :multiple=\"false\"\r\n                                 :pass=\"form.receiveStandard\" :placeholder=\"'收款账单基准'\"\r\n                                 :type=\"'commonInfoOfTime'\" class=\"sss\"\r\n                                 @return=\"form.receiveStandard=$event\"\r\n                                 @returnData=\"localStandard=$event.infoLocalName\"\r\n                    />\r\n                    <tree-select v-if=\"roleSupplier==1 || roleSupport==1\"\r\n                                 :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                                 :disabled=\"agreementLock || commonLock\" :flat=\"false\"\r\n                                 :multiple=\"false\"\r\n                                 :pass=\"form.payStandard\" :placeholder=\"'付款账单基准'\"\r\n                                 :type=\"'commonInfoOfTime'\" class=\"sss\"\r\n                                 @returnData=\"form.payStandard=$event.infoShortName\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item\r\n                    :label=\"roleClient==1|| roleRich==1?'收款期限':roleSupplier==1|| roleSupport==1?'付款期限':''\"\r\n                    :class=\"agreementLock || commonLock ?'disable-form':''\" :disabled=\"agreementLock || commonLock\"\r\n                    prop=\"agreementNumber\"\r\n                  >\r\n                    <el-input v-if=\"roleClient==1|| roleRich==1\" v-model=\"form.receiveTerm\"\r\n                              :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                              placeholder=\"收款期限(填写±n)\"\r\n                              @change=\"handleTermChange\"\r\n                    />\r\n                    <el-input v-if=\"roleSupplier==1|| roleSupport==1\" v-model=\"form.payTerm\"\r\n                              :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                              :disabled=\"agreementLock || commonLock\" class=\"sss\"\r\n                              placeholder=\"付款期限(填写±n)\"\r\n                              @change=\"handleTermChange\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-input :value=\"description\" class=\"disable-form\" disabled/>\r\n                </el-col>\r\n              </el-row>\r\n              <el-row>\r\n                <el-form-item label=\"协议备注\" prop=\"agreementRemark\">\r\n                  <el-input v-model=\"form.agreementRemark\" :class=\"agreementLock || commonLock ?'disable-form':''\"\r\n                            :disabled=\"agreementLock || commonLock\"\r\n                            placeholder=\"协议备注\"\r\n                  />\r\n                </el-form-item>\r\n              </el-row>\r\n            </el-row>\r\n            <!--审核意见-->\r\n            <el-row v-if=\"showConfirm\">\r\n              <el-divider content-position=\"left\">\r\n                审核意见\r\n              </el-divider>\r\n              <el-row>\r\n                <el-col v-if=\"this.roleClient==1\" :span=\"4\">\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.salesConfirmed==0\" :row=\"form\" :type=\"'sales'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col v-if=\"this.roleSupplier==1\" :span=\"4\">\r\n                  <!--商务锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.psaConfirmed==0\" :row=\"form\" :type=\"'psa'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <!--操作锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.opConfirmed==0\" :row=\"form\" :type=\"'op'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n                <el-col :span=\"4\">\r\n                  <!--财务锁定-->\r\n                  <confirmed :id=\"'companyId'\" :confirmed=\"this.form.accConfirmed==0\" :row=\"form\" :type=\"'acc'\"\r\n                             @lockMethod=\"updateCompany\"\r\n                  />\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <!--分类信息-->\r\n            <el-row v-if=\"roleClient==1\">\r\n              <el-divider content-position=\"left\">\r\n                开发分类\r\n              </el-divider>\r\n              <el-row>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"客户来源\">\r\n                    <tree-select :pass=\"form.sourceId\" :type=\"'source'\" class=\"sss\"\r\n                                 @returnData=\"getSourceId\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"开发权重\">\r\n                    <el-input v-model=\"form.developmentWeight\" class=\"sss\"\r\n                              placeholder=\"重要程度\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n                <el-col :span=\"8\">\r\n                  <el-form-item label=\"业务标记\" prop=\"salesRemark\">\r\n                    <el-input v-model=\"form.salesRemark\" class=\"sss\"\r\n                              placeholder=\"重要程度\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--物流分类-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">\r\n                物流分类\r\n              </el-divider>\r\n              <el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <!--供应商公司的角色-->\r\n                    <el-form-item label=\"公司角色\" prop=\"roleIds\">\r\n                      <!--<tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                                   :pass=\"queryParams.roleIds\" :placeholder=\"'公司角色'\" :type=\"'companyRole'\"\r\n                                   class=\"sss\" style=\"width: 100%\" @return=\"queryCompanyRoleIds\"\r\n                      />-->\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.roleIds\" :type=\"'companyRole'\"\r\n                                   class=\"sss\" @return=\"getCompanyRoleIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"所属组织\" prop=\"organizationIds\">\r\n                      <tree-select :multiple=\"true\" :pass=\"form.organizationIds\" :type=\"'organization'\"\r\n                                   class=\"sss\" @return=\"getOrganizationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"服务类型\" prop=\"serviceTypeIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.serviceTypeIds\" :type=\"'serviceType'\"\r\n                                   class=\"sss\" @return=\"getServiceTypeIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"货物类型\" prop=\"cargoTypeIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.cargoTypeIds\" :type=\"'cargoType'\"\r\n                                   class=\"sss\" @return=\"getCargoTypeIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"启运区域\" prop=\"locationDepartureIds\">\r\n                      <location-select :load-options=\"locationOptions\" :multiple=\"true\"\r\n                                       :pass=\"form.locationDepartureIds\" class=\"sss\"\r\n                                       @return=\"getLocationDepartureIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"优选承运\" prop=\"carrierIds\">\r\n                      <treeselect v-model=\"carrierIds\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                                  :flat=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                                  :normalizer=\"carrierNormalizer\"\r\n                                  :options=\"temCarrierList\" :show-count=\"true\" class=\"sss\"\r\n                                  placeholder=\"选择承运人\" @deselect=\"handleDeselectCarrierIds\" @open=\"loadCarrier\"\r\n                                  @select=\"handleSelectCarrierIds\"\r\n                      >\r\n                        <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                          {{ node.raw.carrier.carrierIntlCode }}\r\n                          {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n                        </div>\r\n                        <label slot=\"option-label\"\r\n                               slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                               :class=\"labelClassName\"\r\n                        >\r\n                          {{\r\n                            node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n                          }}\r\n                          <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                        </label>\r\n                      </treeselect>\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"目的区域\" prop=\"locationDestinationIds\">\r\n                      <location-select :en=\"true\" :load-options=\"locationOptions\"\r\n                                       :multiple=\"true\" :pass=\"form.locationDestinationIds\"\r\n                                       class=\"sss\"\r\n                                       @return=\"getLocationDestinationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-form-item label=\"目的航线\" prop=\"lineDestinationIds\">\r\n                      <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.lineDestinationIds\" :type=\"'line'\"\r\n                                   class=\"sss\" @return=\"getLineDestinationIds\"\r\n                      />\r\n                    </el-form-item>\r\n                  </el-col>\r\n                </el-row>\r\n\r\n                <el-col :span=\"24\">\r\n                  <el-form-item label=\"备注信息\" prop=\"remark\">\r\n                    <el-input v-model=\"form.remark\" class=\"sss\"\r\n                              placeholder=\"备注信息\"\r\n                    />\r\n                  </el-form-item>\r\n                </el-col>\r\n              </el-row>\r\n            </el-row>\r\n            <!--客户习惯-->\r\n            <el-row>\r\n              <el-divider content-position=\"left\">\r\n                客户习惯\r\n              </el-divider>\r\n              <el-col :span=\"24\">\r\n                <el-input v-model=\"form.partnerHabit\" :autosize=\"{ minRows: 15, maxRows: 10}\" maxlength=\"150\"\r\n                          placeholder=\"备注\" show-word-limit type=\"textarea\"\r\n                />\r\n              </el-col>\r\n            </el-row>\r\n          </el-col>\r\n        </el-row>\r\n\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button v-if=\"!edit\" :size=\"size\" type=\"primary\" @click=\"querySame\">查重</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <div>\r\n      <staff-info :company=\"companyInfo\" :load-options=\"staffList\" :open=\"openStaff\" @openStaffs=\"cancel\"/>\r\n      <!--    <account-info :company=\"companyInfo\" :load-options=\"accountList\" :open=\"openAccount\" :type=\"'supplier'\"\r\n                        @openAccounts=\"cancel\"\r\n          />-->\r\n      <account-info :company=\"companyInfo\" :is-lock=\"isLock\" :load-options=\"accountList\" :open=\"openAccount\"\r\n                    :type=\"'company'\"\r\n                    @openAccounts=\"accountCancel\"\r\n      />\r\n      <communications :company=\"companyInfo\" :load-options=\"communicationList\" :open=\"openCommunication\" :totle=\"ctotle\"\r\n                      @openCommunications=\"cancel\"\r\n      />\r\n      <agreement-record :company=\"companyInfo\" :load-options=\"agreementList\" :open=\"openAgreement\" :totle=\"atotle\"\r\n                        @openCommunications=\"cancel\"\r\n      />\r\n      <BlackList :company=\"companyInfo\" :open=\"openBlackList\" @openBlackList=\"cancel\"/>\r\n    </div>\r\n\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  delCompany,\r\n  getBank,\r\n  getCompany,\r\n  getConnect,\r\n  listCompany,\r\n  mergeCompany,\r\n  querySame,\r\n  updateCompany\r\n} from \"@/api/system/company\"\r\nimport {getInfoByStaffId} from \"@/api/system/role\"\r\nimport {addMessage} from \"@/api/system/message\"\r\nimport {listCommunication} from \"@/api/system/communication\"\r\nimport {listAgreementrecord} from \"@/api/system/agreementrecord\"\r\n\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\"\r\n\r\nimport BlackList from \"@/components/BlackList\"\r\nimport communications from \"@/views/system/communication\"\r\nimport agreementRecord from \"@/views/system/agreementRecord\"\r\nimport staffInfo from \"@/views/system/company/staffInfo\"\r\n\r\nimport company from \"@/views/system/company/company\"\r\nimport contactor from \"@/views/system/company/contactor\"\r\nimport location from \"@/views/system/company/location\"\r\nimport role from \"@/views/system/company/role\"\r\nimport serviceType from \"@/views/system/company/serviceType\"\r\nimport departure from \"@/views/system/company/departure\"\r\nimport destination from \"@/views/system/company/destination\"\r\nimport cargoType from \"@/views/system/company/cargoType\"\r\nimport carrier from \"@/views/system/company/carrier\"\r\nimport account from \"@/views/system/company/account\"\r\nimport agreement from \"@/views/system/company/agreement\"\r\nimport communication from \"@/views/system/company/communication\"\r\nimport grade from \"@/views/system/company/grade\"\r\nimport achievement from \"@/views/system/company/achievement\"\r\nimport remark from \"@/views/system/company/remark\"\r\nimport belong from \"@/views/system/company/belong\"\r\nimport auth from \"@/plugins/auth\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {Message} from \"element-ui\"\r\nimport Confirmed from \"@/components/Confirmed/index.vue\"\r\nimport rsPaymentTitle from \"@/views/system/company/rsPaymentTitle.vue\"\r\nimport {checkRole} from \"@/utils/permission\"\r\nimport AccountInfo from \"@/views/system/company/accountInfo.vue\"\r\n\r\nexport default {\r\n  name: \"Company\",\r\n  dicts: [\"sys_is_idle\"],\r\n  components: {\r\n    AccountInfo,\r\n    Confirmed,\r\n    Treeselect,\r\n    communication,\r\n    communications,\r\n    BlackList,\r\n    belong,\r\n    company,\r\n    contactor,\r\n    staffInfo,\r\n    location,\r\n    role,\r\n    serviceType,\r\n    departure,\r\n    destination,\r\n    cargoType,\r\n    carrier,\r\n    account,\r\n    agreement,\r\n    agreementRecord,\r\n    grade,\r\n    achievement,\r\n    remark,\r\n    rsPaymentTitle\r\n  },\r\n  props: [\"roleTypeId\", \"roleRich\", \"roleClient\", \"roleSupplier\", \"roleSupport\"],\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      add: false,\r\n      selectTwo: true,\r\n      size: this.$store.state.app.size || \"mini\",\r\n      // 公司表格数据\r\n      mergeList: [],\r\n      companyList: [],\r\n      staffList: [],\r\n      accountList: [],\r\n      communicationList: [],\r\n      agreementList: [],\r\n      belongList: [],\r\n      carrierList: [],\r\n      businessList: [],\r\n      temCarrierList: [],\r\n      locationOptions: [],\r\n      carrierIds: [],\r\n      companyInfo: {},\r\n      queryCarrierIds: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      merge: false,\r\n      openCompany: false,\r\n      openStaff: false,\r\n      openAccount: false,\r\n      openCommunication: false,\r\n      openAgreement: false,\r\n      openBlackList: false,\r\n      edit: false,\r\n      belongTo: null,\r\n      followUp: null,\r\n      queryBFStaffId: null,\r\n      queryBStaffId: null,\r\n      refreshTable: true,\r\n      ctotle: null,\r\n      atotle: null,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        // roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        companyQuery: null,\r\n        locationId: null,\r\n        idleStatus: null,\r\n        queryStaffId: null,\r\n        showPriority: null,\r\n        serviceTypeIds: [],\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        lineDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDestinationIds: [],\r\n        roleIds: [],\r\n        carrierIds: []\r\n      },\r\n      // 表单参数\r\n      form: {\r\n        agreementStartDate: null,\r\n        agreementEndDate: null,\r\n        settlementDate: null\r\n      },\r\n      // 表单校验\r\n      rules: {},\r\n      // 当前点击修改时选中的记录\r\n      companyRow: null,\r\n      isLock: true,\r\n      showConfirm: false,\r\n      localStandard: null,\r\n      description: null\r\n    }\r\n  },\r\n  computed: {\r\n    columns: {\r\n      get() {\r\n        if (this.roleTypeId == \"2\") {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleTypeId == \"1\") {\r\n          return this.$store.state.listSettings.clientSetting\r\n        }\r\n        if (this.roleClient) {\r\n          return this.$store.state.listSettings.clientSetting\r\n        }\r\n        if (this.roleSupplier) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleRich) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n        if (this.roleSupport) {\r\n          return this.$store.state.listSettings.supplierSetting\r\n        }\r\n      }\r\n    },\r\n    commonLock() {\r\n      return (this.form.psaConfirmed == 1 || this.form.salesConfirmed == 1) ? true : false\r\n    },\r\n    basicLock() {\r\n      return this.form.opConfirmed == 1 ? true : false\r\n    },\r\n    agreementLock() {\r\n      return this.form.accConfirmed == 1 ? true : false\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    queryStaffId() {\r\n      this.queryParams.queryStaffId = this.queryStaffId\r\n    },\r\n    \"form.belongTo\"() {\r\n      if (this.form.belongTo == this.form.followUp) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    \"form.serviceTypeIds\"(n) {\r\n      this.loadCarrier()\r\n      let list = []\r\n      if (this.carrierList != undefined && n != null && n.includes(-1)) {\r\n        this.temCarrierList = this.carrierList\r\n        for (const v of this.carrierList) {\r\n          if (v.children != undefined && v.children.length > 0) {\r\n            for (const a of v.children) {\r\n              if (a.children != undefined && a.children.length > 0) {\r\n                for (const b of a.children) {\r\n                  if (this.form.carrierIds != null && this.form.carrierIds.includes(b.carrier.carrierId) && !this.carrierIds.includes(b.serviceTypeId)) {\r\n                    this.carrierIds.push(b.serviceTypeId)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if (this.carrierList != undefined && n != null && !n.includes(-1)) {\r\n        for (const c of this.carrierList) {\r\n          if (n != null && n != undefined) {\r\n            for (const s of n) {\r\n              if (c.serviceTypeId == s) {\r\n                list.push(c)\r\n              }\r\n              if (c.children != undefined && c.children.length > 0) {\r\n                for (const ch of c.children) {\r\n                  if (ch.serviceTypeId == s) {\r\n                    list.push(ch)\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.temCarrierList = list\r\n        if (this.temCarrierList.length > 0) {\r\n          for (const v of this.temCarrierList) {\r\n            if (v.children != undefined && v.children.length > 0) {\r\n              for (const a of v.children) {\r\n                if (this.form.carrierIds != null && this.form.carrierIds.includes(a.carrier.carrierId) && !this.carrierIds.includes(a.serviceTypeId)) {\r\n                  this.carrierIds.push(a.serviceTypeId)\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    form() {\r\n      if (this.form.salesConfirmed == 1 || this.form.accConfirmed == 1 || this.form.psaConfirmed == 1 || this.form.opConfirmed == 1) {\r\n        this.isLock = true\r\n      } else {\r\n        this.isLock = false\r\n      }\r\n    },\r\n    localStandard(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.receiveTerm\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.payTerm\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.receiveWay\"(v) {\r\n      this.handleTermChange()\r\n    },\r\n    \"form.payWay\"(v) {\r\n      this.handleTermChange()\r\n    }\r\n  },\r\n  created() {\r\n    this.getList().then(() => {\r\n      this.loadBusinesses()\r\n      this.loadCarrier()\r\n      this.loadSales()\r\n    })\r\n  },\r\n  methods: {\r\n    parseTime,\r\n    handleTermChange(v) {\r\n      if (this.form.receiveWay === \"月结\" || this.form.payWay === \"月结\") {\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.description = (this.form.receiveStandard ? this.form.receiveStandard : \"\") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === \"-\" ? (\"的下个月\" + this.form.receiveTerm.substring(1, 3) + \"号之后\") : (\"的下个月\" + this.form.receiveTerm.substring(1, 3) + \"号之前\")) : \"前\")\r\n        } else {\r\n          this.description = (this.form.payStandard ? this.form.payStandard : \"\") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === \"-\" ? (\"的下个月\" + this.form.payTerm.substring(1, 3) + \"号之后\") : (\"的下个月\" + this.form.payTerm.substring(1, 3) + \"号之前\")) : \"前\")\r\n        }\r\n      } else {\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.description = (this.form.receiveStandard ? this.form.receiveStandard : \"\") + (this.form.receiveTerm ? (this.form.receiveTerm.substring(0, 1) === \"-\" ? this.form.receiveTerm.substring(1, 3) + \"天前\" : \"后\" + this.form.receiveTerm.substring(1, 3) + \"天内\") : \"前\")\r\n        } else {\r\n          this.description = (this.form.payStandard ? this.form.payStandard : \"\") + (this.form.payTerm ? (this.form.payTerm.substring(0, 1) === \"-\" ? this.form.payTerm.substring(1, 3) + \"天前\" : \"后\" + this.form.payTerm.substring(1, 3) + \"天内\") : \"前\")\r\n        }\r\n      }\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        if (checkRole([\"Operator\"])) {\r\n          store.dispatch(\"getSalesList\").then(() => {\r\n            this.belongList = this.$store.state.data.salesList\r\n          })\r\n        } else {\r\n          store.dispatch(\"getSalesListC\").then(() => {\r\n            this.belongList = this.$store.state.data.salesList\r\n          })\r\n        }\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch(\"getServiceTypeCarriersList\").then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    // 查重\r\n    querySame() {\r\n      // 初始化请求数据,特别注意deleteStatus设置未1,后端会对这个值的数据做重复校验\r\n      let data = {\r\n        cargoTypeIds: [],\r\n        locationDepartureIds: [],\r\n        locationDestinationIds: [],\r\n        lineDepartureIds: [],\r\n        lineDestinationIds: [],\r\n        companyShortName: this.form.companyShortName,\r\n        companyLocalName: this.form.companyLocalName,\r\n        serviceTypeIds: this.form.serviceTypeIds,\r\n        roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        belongTo: this.form.belongTo,\r\n        followUp: this.form.followUp,\r\n        deleteStatus: 1\r\n      }\r\n      getInfoByStaffId(this.$store.state.user.sid).then(response => {\r\n        data.cargoTypeIds = response.cargoTypeIds\r\n        data.locationDepartureIds = response.locationDepartureIds\r\n        data.locationDestinationIds = response.locationDestinationIds\r\n        data.lineDepartureIds = response.lineDepartureIds\r\n        data.lineDestinationIds = response.lineDestinationIds\r\n      })\r\n      // 公司所属\r\n      if (data.belongTo == null) {\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == this.$store.state.user.sid) {\r\n                      data.belongTo = this.$store.state.user.sid\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          querySame(data).then(response => {\r\n            let res = response.data\r\n            if (res != undefined) {\r\n              let newRoleType = this.roleRich ? \"瑞旗分支\" : this.roleClient ? \"客户\" : this.roleSupplier ? \"供应商\" : this.roleSupport ? \"支持\" : \"\"\r\n              let oldRoleType = res.roleRich == 1 ? \"瑞旗分支\" : res.roleClient == 1 ? \"客户\" : res.roleSupplier == 1 ? \"供应商\" : res.roleSupport == 1 ? \"支持\" : \"\"\r\n              this.$confirm(res.deleteStatus == 0 ? \"此公司已存在，角色为\" + oldRoleType + \"，新增角色为\" + newRoleType + \",是否确认新增\" : \"存在重复数据，但已删除，是否重新读取\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                res.deleteStatus = 0\r\n                this.form = res\r\n                this.form.roleTypeId = this.roleTypeId\r\n                if (this.belongList != undefined) {\r\n                  for (const a of this.belongList) {\r\n                    if (a.children != undefined) {\r\n                      for (const b of a.children) {\r\n                        if (b.children != undefined) {\r\n                          for (const c of b.children) {\r\n                            if (c.staffId == response.data.belongTo) {\r\n                              this.belongTo = c.deptId\r\n                            }\r\n                            if (c.staffId == response.data.followUp) {\r\n                              this.followUp = c.deptId\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n                this.form.roleIds = response.roleIds\r\n                this.form.serviceTypeIds = response.serviceTypeIds\r\n                this.form.cargoTypeIds = response.cargoTypeIds\r\n                this.form.lineDepartureIds = response.lineDepartureIds\r\n                this.form.locationDepartureIds = response.locationDepartureIds\r\n                this.form.lineDestinationIds = response.lineDestinationIds\r\n                this.form.locationDestinationIds = response.locationDestinationIds\r\n                this.form.carrierIds = response.carrierIds\r\n                // this.form.organizationIds = response.companyOrganizationIds\r\n                this.form.organizationIds = response.organizationIds\r\n                this.locationOptions = response.locationOptions\r\n                this.openCompany = true\r\n                this.title = \"修改公司信息\"\r\n                this.loading = false\r\n              })\r\n            }\r\n\r\n            // 错误处理,弹出提示后点击确定发送请求更新公司信息\r\n            if (response.msg.toString().indexOf(\"Error\") > -1) {\r\n              this.$confirm(response.msg, \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                let data = {\r\n                  messageOwner: 1,\r\n                  messageType: 3,\r\n                  messageFrom: null,\r\n                  messageTitle: this.$store.state.user.name.split(\" \")[1] + \"请求更新公司\",\r\n                  messageContent: response.msg\r\n                }\r\n                addMessage(data).then(response => {\r\n                  this.$message({\r\n                    type: \"success\",\r\n                    message: \"已发送请求!\"\r\n                  })\r\n                })\r\n              })\r\n            }\r\n\r\n            // 新增验证通过(通过将请求数据中的deleteStatus设置未0)\r\n            if (response.msg.toString().indexOf(\"Success\") > -1) {\r\n              this.$confirm(\"不存在重复的公司简称，是否确定新增客户？\", \"提示\", {\r\n                confirmButtonText: \"确定\",\r\n                cancelButtonText: \"取消\",\r\n                type: \"warning\",\r\n                customClass: \"modal-confirm\"\r\n              }).then(() => {\r\n                data.deleteStatus = 0\r\n                // 真正开始新增\r\n                querySame(data).then(response => {\r\n                  if (response.data) {\r\n                    this.$message.success(\"添加成功\")\r\n                    this.form = response.data\r\n                    this.form.roleTypeId = this.roleTypeId\r\n                    if (this.belongList != undefined) {\r\n                      for (const a of this.belongList) {\r\n                        if (a.children != undefined) {\r\n                          for (const b of a.children) {\r\n                            if (b.children != undefined) {\r\n                              for (const c of b.children) {\r\n                                if (c.staffId == response.data.belongTo) {\r\n                                  this.belongTo = c.deptId\r\n                                }\r\n                                if (c.staffId == response.data.followUp) {\r\n                                  this.followUp = c.deptId\r\n                                }\r\n                              }\r\n                            }\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                    this.form.roleIds = response.roleIds\r\n                    this.form.serviceTypeIds = response.serviceTypeIds\r\n                    this.form.cargoTypeIds = response.cargoTypeIds\r\n                    this.form.lineDepartureIds = response.lineDepartureIds\r\n                    this.form.locationDepartureIds = response.locationDepartureIds\r\n                    this.form.lineDestinationIds = response.lineDestinationIds\r\n                    this.form.locationDestinationIds = response.locationDestinationIds\r\n                    this.form.carrierIds = response.carrierIds\r\n                    // this.form.organizationIds = response.companyOrganizationIds\r\n                    this.form.organizationIds = response.organizationIds\r\n                    this.locationOptions = response.locationOptions\r\n                    this.openCompany = true\r\n                    this.title = \"修改公司信息\"\r\n                    this.loading = false\r\n                  }\r\n                })\r\n              })\r\n            }\r\n          })\r\n        }\r\n      })\r\n    },\r\n    /** 查询公司列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      await listCompany({\r\n        ...this.queryParams,\r\n        permissionLevel: this.$store.state.user.permissionLevelList.C\r\n      }).then(response => {\r\n        this.companyList = response.rows\r\n        if (!isNaN(response.total)) {\r\n          this.total = response.total\r\n        }\r\n        this.loading = false\r\n      })\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    validateCompanyShortName(rule, value, callback) {\r\n\r\n      // 检查 value 是否为空或非字符串\r\n      if (value || typeof value === \"string\") {\r\n        // 检查是否包含多个中横线\r\n        const hyphenCount = value.split(\"-\").length - 1; // 通过 split 分割来统计中横线数量\r\n        if (hyphenCount > 1) {\r\n          return callback(new Error(\"只能包含一个中横线\"));\r\n        }\r\n      }\r\n      // 验证通过\r\n      callback();\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + \" \" + node.serviceEnName + \",\" + pinyin.getFullChars(node.serviceLocalName)\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : \"\") + \" \" + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : \"\") + \" \" + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\") + \",\" + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\"))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.openCompany = false\r\n      this.openAccount = false\r\n      this.openStaff = false\r\n      this.openCommunication = false\r\n      this.openAgreement = false\r\n      this.openBlackList = false\r\n      this.add = false\r\n      this.merge = false\r\n      this.edit = false\r\n      this.reset()\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      // this.getList()\r\n    },\r\n    accountCancel() {\r\n      this.openAccount = false\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.belongTo = null\r\n      this.followUp = null\r\n      this.carrierIds = []\r\n      this.form = {\r\n        belongTo: null,\r\n        followUp: null,\r\n        carrierIds: null,\r\n        locationDetail: null,\r\n        companyId: null,\r\n        companyIntlCode: null,\r\n        companyShortName: null,\r\n        companyEnShortName: null,\r\n        companyLocalName: null,\r\n        companyEnName: null,\r\n        companyTaxCode: null,\r\n        roleIds: null,\r\n        serviceTypeIds: null,\r\n        cargoTypeIds: null,\r\n        locationDepartureIds: null,\r\n        lineDepartureIds: null,\r\n        locationDestinationIds: null,\r\n        lineDestinationIds: null,\r\n        organizationIds: null,\r\n        companyPortIds: null,\r\n        roleTypeId: this.roleTypeId,\r\n        roleRich: this.roleRich ? this.roleRich : null,\r\n        roleClient: this.roleClient ? this.roleClient : null,\r\n        roleSupplier: this.roleSupplier ? this.roleSupplier : null,\r\n        roleSupport: this.roleSupport ? this.roleSupport : null,\r\n        locationId: null,\r\n        salesConfirmed: 0,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        psaConfirmed: 0,\r\n        psaConfirmedId: null,\r\n        psaConfirmedName: null,\r\n        psaConfirmedDate: null,\r\n        accConfirmed: 0,\r\n        accConfirmedId: null,\r\n        accConfirmedName: null,\r\n        accConfirmedDate: null,\r\n        opConfirmed: 0,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null,\r\n        remark: null,\r\n        rsPaymentTitles: []\r\n      }\r\n      this.carrierIds = []\r\n      this.companyRow = null\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.queryBStaffId = null\r\n      this.queryParams.locationId = null\r\n      this.queryParams.serviceTypeIds = null\r\n      this.queryParams.cargoTypeIds = null\r\n      this.queryParams.locationDepartureIds = null\r\n      this.queryParams.lineDepartureIds = null\r\n      this.queryParams.locationDestinationIds = null\r\n      this.queryParams.lineDestinationIds = null\r\n      this.queryParams.organizationIds = null\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.companyId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n      this.selectTwo = selection.length != 2\r\n      if (selection.length == 1) {\r\n        this.setCompanyInfo(selection[0])\r\n      }\r\n      if (selection.length == 2) {\r\n        this.mergeList = selection\r\n      }\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.edit = false\r\n      this.form.belongTo = null\r\n      this.openCompany = true\r\n      this.title = \"新增公司信息\"\r\n      this.form.serviceTypeIds = []\r\n      this.temCarrierList = this.carrierList\r\n      if (this.temCarrierList != undefined && this.form.serviceTypeIds != null) {\r\n        for (const a of this.temCarrierList) {\r\n          // this.form.serviceTypeIds.push(a.serviceTypeId)\r\n        }\r\n      }\r\n      this.add = true\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == this.$store.state.user.sid) {\r\n                    this.belongTo = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      // 币种默认为人民币RMB\r\n      this.form.agreementCurrencyCode = \"RMB\"\r\n      this.showConfirm = false\r\n    },\r\n    getReturn(row) {\r\n      if (row.key == \"contactor\") {\r\n        this.setCompanyInfo(row.value)\r\n        getConnect(row.value.companyId).then(response => {\r\n          this.staffList = response.staffList\r\n          this.openStaff = true\r\n        })\r\n      }\r\n      if (row.key == \"communication\") {\r\n        this.setCompanyInfo(row.value)\r\n        listCommunication({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.communicationList = response.rows\r\n          this.ctotle = response.totle\r\n          this.openCommunication = true\r\n        })\r\n      }\r\n      if (row.key == \"agreement\") {\r\n        this.setCompanyInfo(row.value)\r\n        listAgreementrecord({sqdCompanyId: row.value.companyId}).then(response => {\r\n          this.agreementList = response.rows\r\n          this.atotle = response.totle\r\n          this.openAgreement = true\r\n        })\r\n      }\r\n      if (row.key == \"account\") {\r\n        this.setCompanyInfo(row.value)\r\n        getBank(row.value.companyId).then(response => {\r\n          this.accountList = response.accountList\r\n          this.openAccount = true\r\n        })\r\n      }\r\n    },\r\n    // 设置客户信息\r\n    setCompanyInfo(row) {\r\n      this.companyInfo = {\r\n        companyId: row.companyId != null ? row.companyId : \"\",\r\n        companyTaxCode: row.companyTaxCode != null ? row.companyTaxCode : \"\",\r\n        companyShortName: row.companyShortName != null ? row.companyShortName : \"\",\r\n        companyEnShortName: row.companyEnShortName != null ? row.companyEnShortName : \"\",\r\n        companyLocalName: row.companyLocalName != null ? row.companyLocalName : \"\",\r\n        companyEnName: row.companyEnName != null ? row.companyEnName : \"\",\r\n        companyLocation: row.locationId != null ? row.locationId : \"\",\r\n        companyIntlCode: row.companyIntlCode != null ? row.companyIntlCode : \"\",\r\n        mainStaffId: row.staff != null ? row.staff.staffId : \"\"\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      this.loading = true\r\n      this.edit = true\r\n      this.companyRow = row\r\n      this.add = auth.hasPermi(\"system:client:distribute\")\r\n      const companyId = row.companyId || this.ids\r\n      this.showConfirm = true\r\n      getCompany(companyId).then(response => {\r\n        this.form = response.data\r\n        if (this.belongList != undefined) {\r\n          for (const a of this.belongList) {\r\n            if (a.children != undefined) {\r\n              for (const b of a.children) {\r\n                if (b.children != undefined) {\r\n                  for (const c of b.children) {\r\n                    if (c.staffId == response.data.belongTo) {\r\n                      this.belongTo = c.deptId\r\n                    }\r\n                    if (c.staffId == response.data.followUp) {\r\n                      this.followUp = c.deptId\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.form.roleIds = response.roleIds\r\n        this.form.serviceTypeIds = response.serviceTypeIds\r\n        this.form.cargoTypeIds = response.cargoTypeIds\r\n        this.form.lineDepartureIds = response.lineDepartureIds\r\n        this.form.locationDepartureIds = response.locationDepartureIds\r\n        this.form.lineDestinationIds = response.lineDestinationIds\r\n        this.form.locationDestinationIds = response.locationDestinationIds\r\n        this.form.carrierIds = response.carrierIds\r\n        // this.form.organizationIds = response.companyOrganizationIds\r\n        this.form.organizationIds = response.organizationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.openCompany = true\r\n        this.title = \"修改公司信息\"\r\n        // this.form.rsPaymentTitles = response.data.rsPaymentTitle ? response.data.rsPaymentTitle.split(',') : []\r\n        this.loading = false\r\n\r\n        if (response.data.agreementStartDate !== null && response.data.agreementEndDate !== null) {\r\n          this.form.agreementDateRange = []\r\n          this.form.agreementDateRange.push(response.data.agreementStartDate)\r\n          this.form.agreementDateRange.push(response.data.agreementEndDate)\r\n        }\r\n\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        if ((this.roleClient == 1 || this.roleRich == 1) && response.data.receiveCreditLimit !== null) {\r\n          this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString(\"en-US\")\r\n          this.form.receiveCreditLimit = this.form.receiveCreditLimit.replace(/,/g, \"\")\r\n          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n        }\r\n        if ((this.roleSupplier == 1 || this.roleSupport == 1) && response.data.payCreditLimit !== null) {\r\n          this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString(\"en-US\")\r\n          this.form.payCreditLimit = this.form.payCreditLimit.replace(/,/g, \"\")\r\n          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n        }\r\n\r\n      })\r\n\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        // 收付款描述\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.form.sqdReceiveTermsSummary = this.description\r\n        }\r\n        if (this.roleClient == 1 || this.roleRich == 1) {\r\n          this.form.sqdPayTermsSummary = this.description\r\n        }\r\n        // 公司类型\r\n        this.form.roleRich = this.roleRich ? this.roleRich : null\r\n        this.form.roleClient = this.roleClient ? this.roleClient : null\r\n        this.form.roleSupplier = this.roleSupplier ? this.roleSupplier : null\r\n        this.form.roleSupport = this.roleSupport ? this.roleSupport : null\r\n        // 转换额度显示\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? String(this.form.receiveCreditLimit).replace(/,/g, \"\") : 0\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? String(this.form.payCreditLimit).replace(/,/g, \"\") : 0\r\n        if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n          this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n          this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n        }\r\n        // 判断日期开始时间是否大于结束时间\r\n        let startDate = new Date(this.form.agreementStartDate)\r\n        let endDate = new Date(this.form.agreementEndDate)\r\n        if (startDate > endDate) {\r\n          Message({\r\n            message: \"协议开始时间不能大于结束时间\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        // 信用周期要为整数\r\n        if (this.form.creditCycleMonth != null && this.form.creditCycleMonth % 1 != 0) {\r\n          Message({\r\n            message: \"信用周期必须为整数\",\r\n            type: \"error\"\r\n          })\r\n          return\r\n        }\r\n        if (valid) {\r\n          if (this.form.companyId != null) {\r\n            updateCompany(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.openCompany = false\r\n              this.getList()\r\n            })\r\n            this.reset()\r\n          } else {\r\n            this.$message.info(\"未查重，先对简称查重吧\")\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      row.roleRich = this.roleRich ? this.roleRich : null\r\n      row.roleClient = this.roleClient ? this.roleClient : null\r\n      row.roleSupplier = this.roleSupplier ? this.roleSupplier : null\r\n      row.roleSupport = this.roleSupport ? this.roleSupport : null\r\n\r\n      const companyIds = row.companyId || this.ids\r\n      this.$confirm(\"是否确认删除公司编号为\\\"\" + companyIds + \"\\\"的数据项？\", \"提示\", {customClass: \"modal-confirm\"}).then(function () {\r\n        return delCompany(row)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    handleBlackList() {\r\n      this.openBlackList = true\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/company/export\", {\r\n        ...this.queryParams\r\n      }, `company_${new Date().getTime()}.xlsx`)\r\n    },\r\n    queryLocationId(val) {\r\n      this.queryParams.locationId = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationId(val) {\r\n      this.form.locationId = val\r\n    },\r\n    getSourceId(val) {\r\n      this.form.sourceId = val.sourceShortName\r\n    },\r\n    getOrganizationIds(val) {\r\n      this.form.organizationIds = val\r\n    },\r\n    getServiceTypeIds(val) {\r\n      this.form.serviceTypeIds = val\r\n      if (val == undefined) {\r\n        this.carrierIds = null\r\n        this.form.carrierIds = null\r\n      }\r\n    },\r\n    queryServiceTypeIds(val) {\r\n      this.queryParams.serviceTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCargoTypeIds(val) {\r\n      this.form.cargoTypeIds = val\r\n    },\r\n    queryCargoTypeIds(val) {\r\n      this.queryParams.cargoTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getCompanyRoleIds(val) {\r\n      this.form.roleIds = val\r\n    },\r\n    queryCompanyRoleIds(val) {\r\n      this.queryParams.roleIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLocationDepartureIds(val) {\r\n      this.queryParams.locationDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDepartureIds(val) {\r\n      this.form.lineDepartureIds = val\r\n    },\r\n    getLocationDestinationIds(val) {\r\n      this.form.locationDestinationIds = val\r\n    },\r\n    queryLineDepartureIds(val) {\r\n      this.queryParams.lineDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLocationDepartureIds(val) {\r\n      this.form.locationDepartureIds = val\r\n    },\r\n    queryLocationDestinationIds(val) {\r\n      this.queryParams.locationDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    getLineDestinationIds(val) {\r\n      this.form.lineDestinationIds = val\r\n    },\r\n    queryLineDestinationIds(val) {\r\n      this.queryParams.lineDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    handleSelectBelongTo(node) {\r\n      this.form.belongTo = node.staffId\r\n    },\r\n    handleDeselectBelongTo(v) {\r\n      if (v == undefined) {\r\n        this.form.belongTo = 0\r\n        this.belongTo = null\r\n      }\r\n    },\r\n    handleSelectFollowUp(node) {\r\n      this.form.followUp = node.staffId\r\n    },\r\n    handleDeselectFollowUp(value) {\r\n      if (value == undefined) {\r\n        this.form.followUp = 0\r\n        this.followUp = null\r\n      }\r\n    },\r\n    handleSelectBFStaffId(node) {\r\n      this.queryParams.queryBFStaffId = node.staffId\r\n      this.handleQuery()\r\n    },\r\n    cleanBFStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBFStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    cleanBStaffId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.queryBStaffId = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    handleSelectBStaffId(node) {\r\n      this.queryParams.queryBStaffId = node.staffId\r\n      getInfoByStaffId(node.staffId).then(response => {\r\n        this.queryParams.cargoTypeIds = response.cargoTypeIds\r\n        this.queryParams.serviceTypeIds = response.serviceTypeIds\r\n        this.queryParams.locationDepartureIds = response.locationDepartureIds\r\n        this.queryParams.lineDepartureIds = response.lineDepartureIds\r\n        this.queryParams.locationDestinationIds = response.locationDestinationIds\r\n        this.queryParams.lineDestinationIds = response.lineDestinationIds\r\n        this.locationOptions = response.locationOptions\r\n        this.handleQuery()\r\n      })\r\n    },\r\n    handleSelectCarrierIds(node) {\r\n      this.form.carrierIds.push(node.carrier.carrierId)\r\n    },\r\n    handleSelectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds.push(node.carrier.carrierId)\r\n      this.handleQuery()\r\n    },\r\n    handleDeselectCarrierIds(node) {\r\n      this.form.carrierIds = this.form.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n    },\r\n    handleDeselectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n      this.handleQuery()\r\n    },\r\n    refreshColumns() {\r\n      this.refreshTable = false\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true\r\n      })\r\n    },\r\n    handleMergeCompany() {\r\n      this.merge = true\r\n    },\r\n    handleMerge(save, del) {\r\n      mergeCompany(save, del).then(response => {\r\n        this.$message.success(response.msg)\r\n        this.merge = false\r\n        this.getList()\r\n      })\r\n    },\r\n    deptLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.deptConfirmed = this.form.deptConfirmed == 0 ? 1 : 0\r\n        this.form.deptConfirmedId = this.$store.state.user.sid\r\n        this.form.deptConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.deptConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    financeLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.financeConfirmed = this.form.financeConfirmed == 0 ? 1 : 0\r\n        this.form.financeConfirmedId = this.$store.state.user.sid\r\n        this.form.financeConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.financeConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    psaLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.psaConfirmed = this.form.psaConfirmed == 0 ? 1 : 0\r\n        this.form.psaConfirmedId = this.$store.state.user.sid\r\n        this.form.psaConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.psaConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    opLock() {\r\n      if (this.form.companyId != null) {\r\n        this.form.opConfirmed = this.form.opConfirmed === 0 ? 1 : 0\r\n        this.form.opConfirmedId = this.$store.state.user.sid\r\n        this.form.opConfirmedName = this.$store.state.user.name.split(\" \")[1]\r\n        this.form.opConfirmedDate = parseTime(new Date())\r\n        updateCompany(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    getCurrencyCode(val) {\r\n      this.form.agreementCurrencyCode = val\r\n    },\r\n    getcreditLevel(val) {\r\n      this.form.creditLevel = val\r\n    },\r\n    getRsPaymentTitle(val) {\r\n      this.form.rsPaymentTitle = val\r\n    },\r\n    async updateCompany(form) {\r\n      // TODO 只更新有修改的字段\r\n      // console.log(Object.assign(this.form, form))\r\n\r\n      // this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')\r\n\r\n      if (this.roleClient == 1 || this.roleRich == 1) {\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit ? this.form.receiveCreditLimit.replace(/,/g, \"\") : 0\r\n      }\r\n      if (this.roleSupplier == 1 || this.roleSupport == 1) {\r\n        this.form.payCreditLimit = this.form.payCreditLimit ? this.form.payCreditLimit.replace(/,/g, \"\") : 0\r\n      }\r\n\r\n      if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n        this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n        this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n      }\r\n\r\n      await updateCompany(form)\r\n      this.$modal.msgSuccess(\"修改成功\")\r\n      let response = await getCompany(form.companyId)\r\n      // 更新信息\r\n      // this.$nextTick(() => {\r\n      this.form = response.data\r\n      if (this.belongList != undefined) {\r\n        for (const a of this.belongList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.staffId == response.data.belongTo) {\r\n                    this.belongTo = c.deptId\r\n                  }\r\n                  if (c.staffId == response.data.followUp) {\r\n                    this.followUp = c.deptId\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.form.roleIds = response.roleIds\r\n      this.form.serviceTypeIds = response.serviceTypeIds\r\n      this.form.cargoTypeIds = response.cargoTypeIds\r\n      this.form.lineDepartureIds = response.lineDepartureIds\r\n      this.form.locationDepartureIds = response.locationDepartureIds\r\n      this.form.lineDestinationIds = response.lineDestinationIds\r\n      this.form.locationDestinationIds = response.locationDestinationIds\r\n      this.form.carrierIds = response.carrierIds\r\n      // this.form.organizationIds = response.companyOrganizationIds\r\n      this.form.organizationIds = response.organizationIds\r\n      this.locationOptions = response.locationOptions\r\n      this.openCompany = true\r\n      this.title = \"修改公司信息\"\r\n      this.loading = false\r\n\r\n      const formatter = new Intl.NumberFormat(\"en-US\", {\r\n        style: \"decimal\",\r\n        minimumFractionDigits: 2,\r\n        maximumFractionDigits: 2\r\n      })\r\n      /* this.form.creditLimit = response.data.creditLimit.toLocaleString('en-US')\r\n      this.form.creditLimit = this.form.creditLimit.replace(/,/g, '')\r\n      this.form.creditLimit = formatter.format(this.form.creditLimit) */\r\n\r\n      if (this.roleClient == 1 || this.roleRich == 1) {\r\n        this.form.receiveCreditLimit = response.data.receiveCreditLimit.toLocaleString(\"en-US\")\r\n        this.form.receiveCreditLimit = this.form.receiveCreditLimit.replace(/,/g, \"\")\r\n        this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n      }\r\n      if (this.roleSupplier == 1 || this.roleSupport == 1) {\r\n        this.form.payCreditLimit = response.data.payCreditLimit.toLocaleString(\"en-US\")\r\n        this.form.payCreditLimit = this.form.payCreditLimit.replace(/,/g, \"\")\r\n        this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n      }\r\n\r\n      // 更新日期\r\n      this.form.agreementDateRange = (response.data.agreementStartDate != null && response.data.agreementEndDate != null) ? [response.data.agreementStartDate, response.data.agreementEndDate] : []\r\n      /* if (this.form.agreementDateRange && this.form.agreementDateRange.length > 0) {\r\n        this.form.agreementStartDate = this.form.agreementDateRange[0]\r\n        this.form.agreementEndDate = this.form.agreementDateRange[1]\r\n      } */\r\n\r\n      // })\r\n      this.form.salesConfirmed = response.data.salesConfirmed\r\n      this.form.psaConfirmed = response.data.psaConfirmed\r\n      this.form.opConfirmed = response.data.opConfirmed\r\n      this.form.accConfirmed = response.data.accConfirmed\r\n    },\r\n    formatCreditLimit() {\r\n      if (this.form.receiveCreditLimit != null || this.form.payCreditLimit != null) {\r\n        const formatter = new Intl.NumberFormat(\"en-US\", {\r\n          style: \"decimal\",\r\n          minimumFractionDigits: 2,\r\n          maximumFractionDigits: 2\r\n        })\r\n        if (this.roleClient == 1) {\r\n          this.form.receiveCreditLimit = this.form.receiveCreditLimit.replace(/,/g, \"\")\r\n          this.form.receiveCreditLimit = formatter.format(this.form.receiveCreditLimit)\r\n        }\r\n        if (this.roleSupplier == 1) {\r\n          this.form.payCreditLimit = this.form.payCreditLimit.replace(/,/g, \"\")\r\n          this.form.payCreditLimit = formatter.format(this.form.payCreditLimit)\r\n        }\r\n\r\n      }\r\n    },\r\n    formatDisplayCreditLimit(value) {\r\n      const formatter = new Intl.NumberFormat(\"en-US\", {\r\n        notation: \"compact\"\r\n      })\r\n      return formatter.format(value)\r\n    },\r\n    changeDate() {\r\n      this.$forceUpdate()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n\r\n<style scoped lang=\"scss\">\r\n.sss {\r\n  //padding-left: 5px !important;\r\n  padding-right: 15px !important;\r\n  width: 100%;\r\n}\r\n\r\n.creditLimit {\r\n  display: flex;\r\n  flex-direction: row;\r\n}\r\n\r\n.limit {\r\n  flex: 3\r\n}\r\n\r\n.currency {\r\n  flex: 1\r\n}\r\n\r\n.confirm {\r\n  display: flex;\r\n  flex-direction: row;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.box-card {\r\n  width: 20%;\r\n  flex-wrap: wrap;\r\n}\r\n</style>\r\n"]}]}