<template>
  <div class="ctnr-truck-component">
    <!--整柜拖车-->
    <div v-for="(item, index) in ctnrTruckList" :key="`ctnr-truck-${index}`" class="ctnr-truck-item">
      <!--标题栏-->
      <el-row>
        <el-col :span="18">
          <div class="service-bar">
            <a :class="[
                'service-toggle-icon',
                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
              ]"
            />
            <div class="service-title-group">
              <h3 class="service-title" @click="changeServiceFold(item.rsServiceInstances)">
                拖车-CtnrTruck
              </h3>
              <el-button
                class="service-action-btn"
                type="text"
                @click="addCtnrTruck"
              >[+]
              </el-button>
              <el-button
                class="service-action-btn"
                type="text"
                @click="deleteRsOpCtnrTruck(item)"
              >[-]
              </el-button>
              <el-button
                class="service-action-btn"
                type="text"
                @click="openChargeSelect(item)"
              >[CN...]
              </el-button>
            </div>
            <!--审核信息-->
            <audit
              v-if="auditInfo"
              :audit="true"
              :basic-info="item.rsServiceInstances"
              :disabled="disabled"
              :payable="getPayable(item.serviceTypeId, item)"
              :rs-charge-list="item.rsChargeList"
              @auditFee="auditCharge(item,$event)"
              @return="item = $event"
            />
            <div class="dispatch-bill-container">
              <el-popover
                v-for="(billConfig, billIndex) in dispatchBillConfig"
                :key="`bill-${billIndex}`"
                placement="top"
                trigger="click"
                width="100"
              >
                <el-button
                  v-for="(template, templateIndex) in billConfig.templateList"
                  :key="`template-${templateIndex}`"
                  @click="getDispatchingBill(item)"
                >
                  {{ template }}
                </el-button>
                <a
                  slot="reference"
                  class="dispatch-bill-link"
                  target="_blank"
                >
                  [{{ billConfig.file }}]
                </a>
              </el-popover>
            </div>
          </div>
        </el-col>
      </el-row>
      <!--内容区域-->
      <transition name="fade">
        <el-row
          v-if="item.rsServiceInstances.serviceFold"
          :gutter="10"
          class="service-content-area"
        >
          <!--服务信息栏-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="3" class="service-info-col">
              <el-form-item label="询价单号">
                <el-input
                  v-model="item.rsServiceInstances.inquiryNo"
                  placeholder="询价单号"
                  @focus="generateFreight(5, 50, item)"
                />
              </el-form-item>

              <el-form-item v-if="!booking && branchInfo" label="供应商">
                <el-popover
                  :content="getSupplierEmail(item.rsServiceInstances.supplierId)"
                  placement="bottom"
                  trigger="hover"
                  width="200"
                >
                  <el-input
                    slot="reference"
                    :value="item.rsServiceInstances.supplierName"
                    class="disable-form"
                    disabled
                  />
                </el-popover>
              </el-form-item>

              <el-form-item label="合约类型">
                <el-input
                  :value="getAgreementDisplay(item.rsServiceInstances)"
                  class="disable-form"
                  disabled
                  placeholder="合约类型"
                />
              </el-form-item>

              <el-form-item label="业务须知">
                <el-input
                  v-model="item.rsServiceInstances.inquiryNotice"
                  class="disable-form"
                  disabled
                  placeholder="业务须知"
                />
              </el-form-item>
            </el-col>
          </transition>
          <!--分支信息-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="15">
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-form-item label="派车单号">
                    <el-input
                      v-model="item.precarriageSupplierNo"
                      :class="isFieldDisabled(item) ? 'disable-form' : ''"
                      :disabled="isFieldDisabled(item)"
                      placeholder="拖车公司单号"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="装运时间">
                    <el-date-picker
                      v-model="item.precarriageTime"
                      :class="isFieldDisabled(item) ? 'disable-form' : ''"
                      :disabled="isFieldDisabled(item)"
                      clearable
                      placeholder="装运时间"
                      style="width:100%"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="装运区域">
                    <location-select
                      :class="isFieldDisabled(item) ? 'disable-form' : ''"
                      :disabled="isFieldDisabled(item)"
                      :load-options="locationOptions"
                      :multiple="false"
                      :pass="item.precarriageRegionId"
                      :placeholder="'装运区域'"
                      @return="item.precarriageRegionId=$event"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="装运详址">
                    <el-row :gutter="5">
                      <el-col :span="19">
                        <el-input
                          v-model="item.precarriageAddress"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="装运详址"
                        />
                      </el-col>
                      <el-col :span="2">
                        <el-button
                          :disabled="getServiceInstanceDisable(item.rsServiceInstances)"
                          style="color: blue"
                          type="text"
                          @click="handleAddCommon('dispatch')"
                        >[↗]
                        </el-button>
                      </el-col>
                      <el-col :span="1">
                        <el-button
                          :disabled="getServiceInstanceDisable(item.rsServiceInstances)"
                          style="color: blue"
                          type="text"
                          @click="openDispatchCommon"
                        >[...]
                        </el-button>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-form-item label="联系人">
                    <el-input
                      v-model="item.precarriageContact"
                      :class="isFieldDisabled(item) ? 'disable-form' : ''"
                      :disabled="isFieldDisabled(item)"
                      placeholder="装运联系人"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item label="装运电话">
                    <el-input
                      v-model="item.precarriageTel"
                      :class="isFieldDisabled(item) ? 'disable-form' : ''"
                      :disabled="isFieldDisabled(item)"
                      placeholder="装运电话"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="14">
                  <el-form-item label="装运备注">
                    <el-input
                      v-model="item.precarriageRemark"
                      :class="isFieldDisabled(item) ? 'disable-form' : ''"
                      :disabled="isFieldDisabled(item)"
                      placeholder="装运备注"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col>
                  <el-table
                    :data="item.rsOpTruckList"
                    border
                    class="pd0"
                  >
                    <el-table-column
                      label="序号"
                      type="index"
                      width="50"
                    />
                    <el-table-column align="center" label="提货司机姓名">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.precarriageDriverName"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="提货司机姓名"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="提货司机电话">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.precarriageDriverTel"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="提货司机电话"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="提货司机车牌">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.precarriageTruckNo"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="提货司机车牌"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="司机备注">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.precarriageTruckRemark"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="提货司机备注"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="柜号">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.containerNo"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="柜号"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="柜型">
                      <template slot-scope="scope">
                        <tree-select
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          :pass="scope.row.containerTypeCode"
                          :type="'unit'"
                          placeholder="选择柜型"
                          @returnData="scope.row.containerTypeCode = $event.unitCode"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="封条">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.sealNo"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="封条"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="磅单">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.weightPaper"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="磅单"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="提货须知">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.precarriageNote"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="提货须知"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="提货备注">
                      <template slot-scope="scope">
                        <el-input
                          v-model="scope.row.precarriageRemark"
                          :class="isFieldDisabled(item) ? 'disable-form' : ''"
                          :disabled="isFieldDisabled(item)"
                          placeholder="提货备注"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作">
                      <template slot-scope="scope">
                        <el-button
                          v-if="!disabled && !getServiceInstanceDisable(item.rsServiceInstances)"
                          style="color: red"
                          type="text"
                          @click="deleteTruckItem(item, scope.row)"
                        >删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-button
                    :disabled="isFieldDisabled(item)"
                    style="padding: 0"
                    type="text"
                    @click="addTuck(item.rsOpTruckList)"
                  >[＋]
                  </el-button>
                </el-col>
              </el-row>
            </el-col>
          </transition>
          <!--物流进度-->
          <transition name="fade">
            <el-col v-if="logisticsInfo" :span="4">
              <div>
                <logistics-progress
                  :disabled="isFieldDisabled(item)"
                  :logistics-progress-data="item.rsOpLogList"
                  :open-logistics-progress-list="true"
                  :process-type="4"
                  :service-type="50"
                  @deleteItem="deleteLogItem(item, $event)"
                  @return="item.rsOpLogList=$event"
                />
              </div>
            </el-col>
          </transition>
          <!--费用列表-->
          <el-col v-if="chargeInfo" :span="10.3">
            <!--<charge-list
              :a-t-d="form.podEta"
              :charge-data="item.rsChargeList"
              :company-list="companyList"
              :disabled="disabled || getServiceInstanceDisable(item.rsServiceInstances)"
              :hiddenSupplier="booking"
              :is-receivable="false"
              :open-charge-list="true"
              :pay-detail-r-m-b="item.payableRMB"
              :pay-detail-r-m-b-tax="item.payableRMBTax"
              :pay-detail-u-s-d="item.payableUSD"
              :pay-detail-u-s-d-tax="item.payableUSDTax"
              :service-type-id="50"
              @copyFreight="copyFreight($event)"
              @deleteAll="item.rsChargeList=[]"
              @deleteItem="deleteChargeItem(item, $event)"
              @return="calculateCharge(50, $event, item)"
            />-->
            <debit-note-list
              :company-list="companyList"
              :debit-note-list="item.rsDebitNoteList"
              :disabled="false"
              :hidden-supplier="false"
              :is-receivable="0"
              :rct-id="form.rctId"
              @addDebitNote="handleAddDebitNote(item)"
              @deleteItem="item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)"
              @return="calculateCharge(50,$event,item)"
            />
          </el-col>
        </el-row>
      </transition>
    </div>
  </div>
</template>

<script>
import Audit from "@/views/system/document/audit.vue"
import LogisticsProgress from "@/views/system/document/logisticsProgress.vue"
import ChargeList from "@/views/system/document/chargeList.vue"
import locationSelect from "@/components/LocationSelect/index.vue"
import TreeSelect from "@/components/TreeSelect/index.vue"
import DebitNoteList from "@/views/system/document/debitNodeList.vue"


export default {
  name: "CtnrTruckComponent",
  components: {
    DebitNoteList,
    Audit,
    LogisticsProgress,
    ChargeList,
    locationSelect,
    TreeSelect
  },
  props: {
    // 整柜拖车数据列表
    ctnrTruckList: {
      type: Array,
      default: () => []
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({})
    },
    // 显示控制
    branchInfo: {
      type: Boolean,
      default: true
    },
    logisticsInfo: {
      type: Boolean,
      default: true
    },
    chargeInfo: {
      type: Boolean,
      default: true
    },
    auditInfo: {
      type: Boolean,
      default: false
    },
    // 状态控制
    disabled: {
      type: Boolean,
      default: false
    },
    booking: {
      type: Boolean,
      default: false
    },
    psaVerify: {
      type: Boolean,
      default: false
    },
    // 数据列表
    supplierList: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    },
    locationOptions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dispatchBillConfig: [{
        file: "派车单",
        templateList: ["Dispatch Bill"]
      }]
    }
  },
  computed: {
    // 判断是否禁用状态
    isDisabled() {
      return this.disabled || this.psaVerify
    }
  },
  methods: {
    handleAddDebitNote(serviceObject) {
      let row = {}
      row.sqdRctNo = this.form.rctNo
      row.rctId = this.form.rctId
      row.isRecievingOrPaying = 1
      row.rsChargeList = []
      this.$emit('addDebitNote', row, serviceObject)
    },
    // 判断字段是否禁用
    isFieldDisabled(item) {
      return this.psaVerify || this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)
    },
    // 获取供应商邮箱
    getSupplierEmail(supplierId) {
      const supplier = this.supplierList.find(v => v.companyId === supplierId)
      return supplier ? supplier.staffEmail : ''
    },
    // 获取合约显示文本
    getAgreementDisplay(serviceInstance) {
      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo
    },
    // 处理派车单生成
    getDispatchingBill(item) {
      this.$emit("getDispatchingBill", item)
    },
    // 添加拖车记录
    addTuck(rsOpTruckList) {
      if (!rsOpTruckList) return
      rsOpTruckList.push({})
    },
    // 删除拖车记录
    deleteTruckItem(item, truckRow) {
      item.rsOpTruckList = item.rsOpTruckList.filter(truck => truck !== truckRow)
    },
    // 删除物流进度
    deleteLogItem(item, logItem) {
      item.rsOpLogList = item.rsOpLogList.filter(log => log !== logItem)
    },
    // 删除费用项
    deleteChargeItem(item, chargeItem) {
      item.rsChargeList = item.rsChargeList.filter(charge => charge !== chargeItem)
    },
    // 事件转发给父组件
    changeServiceFold(serviceInstance) {
      this.$emit("changeServiceFold", serviceInstance)
    },
    addCtnrTruck() {
      this.$emit("addCtnrTruck")
    },
    deleteRsOpCtnrTruck(item) {
      this.$emit("deleteRsOpCtnrTruck", item)
    },
    openChargeSelect(item) {
      this.$emit("openChargeSelect", item)
    },
    auditCharge(item, event) {
      this.$emit("auditCharge", item, event)
    },
    generateFreight(type1, type2, item) {
      this.$emit("generateFreight", type1, type2, item)
    },
    handleAddCommon(type) {
      this.$emit("handleAddCommon", type)
    },
    openDispatchCommon() {
      this.$emit("openDispatchCommon")
    },
    copyFreight(event) {
      this.$emit("copyFreight", event)
    },
    calculateCharge(serviceType, event, item) {
      this.$emit("calculateCharge", serviceType, event, item)
    },
    getPayable(serviceType, item) {
      return this.$parent.getPayable ? this.$parent.getPayable(serviceType, item) : null
    },
    getServiceInstanceDisable(serviceInstance) {
      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/op-document';

// CtnrTruck组件特定样式
.ctnr-truck-component {
  width: 100%;

  .ctnr-truck-item {
    margin-bottom: 10px;
  }

  .service-bar {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;

    .service-toggle-icon {
      cursor: pointer;
      margin-right: 5px;
    }

    .service-title-group {
      display: flex;
      align-items: center;
      width: 250px;

      .service-title {
        margin: 0;
        cursor: pointer;
      }

      .service-action-btn {
        margin-left: 10px;
      }
    }

    .dispatch-bill-container {
      margin-left: auto;

      .dispatch-bill-link {
        color: blue;
        padding: 0;
        margin-left: 5px;
        text-decoration: none;
        cursor: pointer;
      }
    }
  }

  .service-content-area {
    margin-bottom: 15px;
    display: -webkit-box;

    .service-info-col {
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }

  // 优化表单输入框样式
  .el-input {
    width: 100%;
  }

  // 优化日期选择器样式
  .el-date-picker {
    width: 100%;
  }

  // 司机信息表格样式
  .pd0 {
    padding: 0;
  }
}
</style>
