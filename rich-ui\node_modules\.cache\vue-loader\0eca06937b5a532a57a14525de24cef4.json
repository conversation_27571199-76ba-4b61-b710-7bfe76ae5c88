{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\cache\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\monitor\\cache\\index.vue", "mtime": 1754876882564}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2dldENhY2hlfSBmcm9tICJAL2FwaS9tb25pdG9yL2NhY2hlIjsNCmltcG9ydCAqIGFzIGVjaGFydHMgZnJvbSAiZWNoYXJ0cyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkNhY2hlIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgLy8g57uf6K6h5ZG95Luk5L+h5oGvDQogICAgICBjb21tYW5kc3RhdHM6IG51bGwsDQogICAgICAvLyDkvb/nlKjlhoXlrZgNCiAgICAgIHVzZWRtZW1vcnk6IG51bGwsDQogICAgICAvLyBjYWNoZeS/oeaBrw0KICAgICAgY2FjaGU6IFtdDQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMub3BlbkxvYWRpbmcoKTsNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIC8qKiDmn6XnvJPlrZjor6Lkv6Hmga8gKi8NCiAgICBnZXRMaXN0KCkgew0KICAgICAgZ2V0Q2FjaGUoKS50aGVuKChyZXNwb25zZSkgPT4gew0KICAgICAgICB0aGlzLmNhY2hlID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy4kbW9kYWwuY2xvc2VMb2FkaW5nKCk7DQoNCiAgICAgICAgdGhpcy5jb21tYW5kc3RhdHMgPSBlY2hhcnRzLmluaXQodGhpcy4kcmVmcy5jb21tYW5kc3RhdHMsICJtYWNhcm9ucyIpOw0KICAgICAgICB0aGlzLmNvbW1hbmRzdGF0cy5zZXRPcHRpb24oew0KICAgICAgICAgIHRvb2x0aXA6IHsNCiAgICAgICAgICAgIHRyaWdnZXI6ICJpdGVtIiwNCiAgICAgICAgICAgIGZvcm1hdHRlcjogInthfSA8YnIvPntifSA6IHtjfSAoe2R9JSkiLA0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICLlkb3ku6QiLA0KICAgICAgICAgICAgICB0eXBlOiAicGllIiwNCiAgICAgICAgICAgICAgcm9zZVR5cGU6ICJyYWRpdXMiLA0KICAgICAgICAgICAgICByYWRpdXM6IFsxNSwgOTVdLA0KICAgICAgICAgICAgICBjZW50ZXI6IFsiNTAlIiwgIjM4JSJdLA0KICAgICAgICAgICAgICBkYXRhOiByZXNwb25zZS5kYXRhLmNvbW1hbmRTdGF0cywNCiAgICAgICAgICAgICAgYW5pbWF0aW9uRWFzaW5nOiAiY3ViaWNJbk91dCIsDQogICAgICAgICAgICAgIGFuaW1hdGlvbkR1cmF0aW9uOiAxMDAwLA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIF0NCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMudXNlZG1lbW9yeSA9IGVjaGFydHMuaW5pdCh0aGlzLiRyZWZzLnVzZWRtZW1vcnksICJtYWNhcm9ucyIpOw0KICAgICAgICB0aGlzLnVzZWRtZW1vcnkuc2V0T3B0aW9uKHsNCiAgICAgICAgICB0b29sdGlwOiB7DQogICAgICAgICAgICBmb3JtYXR0ZXI6ICJ7Yn0gPGJyLz57YX0gOiAiICsgdGhpcy5jYWNoZS5pbmZvLnVzZWRfbWVtb3J5X2h1bWFuLA0KICAgICAgICAgIH0sDQogICAgICAgICAgc2VyaWVzOiBbDQogICAgICAgICAgICB7DQogICAgICAgICAgICAgIG5hbWU6ICLls7DlgLwiLA0KICAgICAgICAgICAgICB0eXBlOiAiZ2F1Z2UiLA0KICAgICAgICAgICAgICBtaW46IDAsDQogICAgICAgICAgICAgIG1heDogMTAwMCwNCiAgICAgICAgICAgICAgZGV0YWlsOiB7DQogICAgICAgICAgICAgICAgZm9ybWF0dGVyOiB0aGlzLmNhY2hlLmluZm8udXNlZF9tZW1vcnlfaHVtYW4sDQogICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgIGRhdGE6IFsNCiAgICAgICAgICAgICAgICB7DQogICAgICAgICAgICAgICAgICB2YWx1ZTogcGFyc2VGbG9hdCh0aGlzLmNhY2hlLmluZm8udXNlZF9tZW1vcnlfaHVtYW4pLA0KICAgICAgICAgICAgICAgICAgbmFtZTogIuWGheWtmOa2iOiAlyIsDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICBdDQogICAgICAgICAgICB9DQogICAgICAgICAgXQ0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5omT5byA5Yqg6L295bGCDQogICAgb3BlbkxvYWRpbmcoKSB7DQogICAgICB0aGlzLiRtb2RhbC5sb2FkaW5nKCLmraPlnKjliqDovb3nvJPlrZjnm5HmjqfmlbDmja7vvIzor7fnqI3lgJnvvIEiKTsNCiAgICB9DQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8HA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/monitor/cache", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row>\r\n      <el-col :span=\"24\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"> <span><i class=\"el-icon-monitor\"></i>基本信息</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <table cellspacing=\"0\" style=\"width: 100%\">\r\n              <tbody>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">Redis版本</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.redis_version }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">运行模式</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{\r\n                      cache.info.redis_mode == \"standalone\" ? \"单机\" : \"集群\"\r\n                    }}\r\n                  </div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">端口</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.tcp_port }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">客户端数</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.connected_clients }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">运行时间(天)</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.uptime_in_days }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">使用内存</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.used_memory_human }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">使用CPU</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{\r\n                      parseFloat(cache.info.used_cpu_user_children).toFixed(2)\r\n                    }}\r\n                  </div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">内存配置</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.maxmemory_human }}</div>\r\n                </td>\r\n              </tr>\r\n              <tr>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">AOF是否开启</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.aof_enabled == \"0\" ? \"否\" : \"是\" }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">RDB是否成功</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{ cache.info.rdb_last_bgsave_status }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">Key数量</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.dbSize\" class=\"cell\">{{ cache.dbSize }}</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div class=\"cell\">网络入口/出口</div>\r\n                </td>\r\n                <td class=\"el-table__cell is-leaf\">\r\n                  <div v-if=\"cache.info\" class=\"cell\">{{\r\n                      cache.info.instantaneous_input_kbps\r\n                    }}kps/{{ cache.info.instantaneous_output_kbps }}kps\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\"> <span><i class=\"el-icon-pie-chart\"></i>命令统计</span></div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"commandstats\" style=\"height: 420px\"/>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <el-col :span=\"12\" class=\"card-box\">\r\n        <el-card>\r\n          <div slot=\"header\">\r\n            <span><i class=\"el-icon-odometer\"></i> 内存信息</span>\r\n          </div>\r\n          <div class=\"el-table el-table--enable-row-hover el-table--medium\">\r\n            <div ref=\"usedmemory\" style=\"height: 420px\"/>\r\n          </div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getCache} from \"@/api/monitor/cache\";\r\nimport * as echarts from \"echarts\";\r\n\r\nexport default {\r\n  name: \"Cache\",\r\n  data() {\r\n    return {\r\n      // 统计命令信息\r\n      commandstats: null,\r\n      // 使用内存\r\n      usedmemory: null,\r\n      // cache信息\r\n      cache: []\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.openLoading();\r\n  },\r\n  methods: {\r\n    /** 查缓存询信息 */\r\n    getList() {\r\n      getCache().then((response) => {\r\n        this.cache = response.data;\r\n        this.$modal.closeLoading();\r\n\r\n        this.commandstats = echarts.init(this.$refs.commandstats, \"macarons\");\r\n        this.commandstats.setOption({\r\n          tooltip: {\r\n            trigger: \"item\",\r\n            formatter: \"{a} <br/>{b} : {c} ({d}%)\",\r\n          },\r\n          series: [\r\n            {\r\n              name: \"命令\",\r\n              type: \"pie\",\r\n              roseType: \"radius\",\r\n              radius: [15, 95],\r\n              center: [\"50%\", \"38%\"],\r\n              data: response.data.commandStats,\r\n              animationEasing: \"cubicInOut\",\r\n              animationDuration: 1000,\r\n            }\r\n          ]\r\n        });\r\n        this.usedmemory = echarts.init(this.$refs.usedmemory, \"macarons\");\r\n        this.usedmemory.setOption({\r\n          tooltip: {\r\n            formatter: \"{b} <br/>{a} : \" + this.cache.info.used_memory_human,\r\n          },\r\n          series: [\r\n            {\r\n              name: \"峰值\",\r\n              type: \"gauge\",\r\n              min: 0,\r\n              max: 1000,\r\n              detail: {\r\n                formatter: this.cache.info.used_memory_human,\r\n              },\r\n              data: [\r\n                {\r\n                  value: parseFloat(this.cache.info.used_memory_human),\r\n                  name: \"内存消耗\",\r\n                }\r\n              ]\r\n            }\r\n          ]\r\n        });\r\n      });\r\n    },\r\n    // 打开加载层\r\n    openLoading() {\r\n      this.$modal.loading(\"正在加载缓存监控数据，请稍候！\");\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}