package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsDebitNote;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsDebitNoteMapper;
import com.rich.system.service.RsDebitNoteService;

/**
 * 分账单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class RsDebitNoteServiceImpl implements RsDebitNoteService {
    @Autowired
    private RsDebitNoteMapper rsDebitNoteMapper;

    /**
     * 查询分账单
     *
     * @param debitNoteId 分账单主键
     * @return 分账单
     */
    @Override
    public RsDebitNote selectRsDebitNoteByDebitNoteId(Long debitNoteId) {
        return rsDebitNoteMapper.selectRsDebitNoteByDebitNoteId(debitNoteId);
    }

    /**
     * 查询分账单列表
     *
     * @param rsDebitNote 分账单
     * @return 分账单
     */
    @Override
    public List<RsDebitNote> selectRsDebitNoteList(RsDebitNote rsDebitNote) {
        return rsDebitNoteMapper.selectRsDebitNoteList(rsDebitNote);
    }

    /**
     * 新增分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    @Override
    public int insertRsDebitNote(RsDebitNote rsDebitNote) {
        rsDebitNote.setCreateTime(DateUtils.getNowDate());
        rsDebitNote.setCreateBy(SecurityUtils.getUserId());
        return rsDebitNoteMapper.insertRsDebitNote(rsDebitNote);
    }

    /**
     * 修改分账单
     *
     * @param rsDebitNote 分账单
     * @return 结果
     */
    @Override
    public int updateRsDebitNote(RsDebitNote rsDebitNote) {
        rsDebitNote.setUpdateTime(DateUtils.getNowDate());
        rsDebitNote.setUpdateBy(SecurityUtils.getUserId());
        return rsDebitNoteMapper.updateRsDebitNote(rsDebitNote);
    }

    /**
     * 修改分账单状态
     *
     * @param rsDebitNote 分账单
     * @return 分账单
     */
    @Override
    public int changeStatus(RsDebitNote rsDebitNote) {
        return rsDebitNoteMapper.updateRsDebitNote(rsDebitNote);
    }

    /**
     * 批量删除分账单
     *
     * @param debitNoteIds 需要删除的分账单主键
     * @return 结果
     */
    @Override
    public int deleteRsDebitNoteByDebitNoteIds(Long[] debitNoteIds) {
        return rsDebitNoteMapper.deleteRsDebitNoteByDebitNoteIds(debitNoteIds);
    }

    /**
     * 删除分账单信息
     *
     * @param debitNoteId 分账单主键
     * @return 结果
     */
    @Override
    public int deleteRsDebitNoteByDebitNoteId(Long debitNoteId) {
        return rsDebitNoteMapper.deleteRsDebitNoteByDebitNoteId(debitNoteId);
    }
}
