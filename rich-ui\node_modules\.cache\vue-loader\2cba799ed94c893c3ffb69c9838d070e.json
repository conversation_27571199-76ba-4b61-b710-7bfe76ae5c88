{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\AppMain.vue?vue&type=style&index=1&id=078753dd&lang=scss&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\AppMain.vue", "mtime": 1754876882540}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8vIGZpeCBjc3Mgc3R5bGUgYnVnIGluIG9wZW4gZWwtZGlhbG9nDQouZWwtcG9wdXAtcGFyZW50LS1oaWRkZW4gew0KICAuZml4ZWQtaGVhZGVyIHsNCiAgICBwYWRkaW5nLXJpZ2h0OiA2cHg7DQogIH0NCn0NCg0KOjotd2Via2l0LXNjcm9sbGJhciB7DQogIHdpZHRoOiA2cHg7DQogIGhlaWdodDogNnB4Ow0KfQ0KDQo6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2YxZjFmMTsNCn0NCg0KOjotd2Via2l0LXNjcm9sbGJhci10aHVtYiB7DQogIGJhY2tncm91bmQtY29sb3I6ICNjMGMwYzA7DQogIGJvcmRlci1yYWRpdXM6IDNweDsNCn0NCg=="}, {"version": 3, "sources": ["AppMain.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "AppMain.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <section class=\"app-main\">\r\n    <transition mode=\"out-in\" name=\"fade-transform\">\r\n      <keep-alive :include=\"cachedViews\">\r\n        <router-view v-if=\"!$route.meta.link\" :key=\"key\"/>\r\n      </keep-alive>\r\n    </transition>\r\n    <iframe-toggle/>\r\n  </section>\r\n</template>\r\n\r\n<script>\r\nimport iframeToggle from \"./IframeToggle/index\"\r\n\r\nexport default {\r\n  name: 'AppMain',\r\n  components: {iframeToggle},\r\n  computed: {\r\n    cachedViews() {\r\n      return this.$store.state.tagsView.cachedViews\r\n    },\r\n    key() {\r\n      return this.$route.path\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.app-main {\r\n  /* 50= navbar  50  */\r\n  min-height: calc(100vh - 50px);\r\n  width: 100%;\r\n  position: relative;\r\n  overflow: visible;\r\n}\r\n\r\n.fixed-header + .app-main {\r\n  padding-top: 50px;\r\n}\r\n\r\n.hasTagsView {\r\n  .app-main {\r\n    /* 84 = navbar + tags-view = 50 + 34 */\r\n    min-height: calc(100vh - 84px);\r\n  }\r\n\r\n  .fixed-header + .app-main {\r\n    padding-top: 84px;\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n// fix css style bug in open el-dialog\r\n.el-popup-parent--hidden {\r\n  .fixed-header {\r\n    padding-right: 6px;\r\n  }\r\n}\r\n\r\n::-webkit-scrollbar {\r\n  width: 6px;\r\n  height: 6px;\r\n}\r\n\r\n::-webkit-scrollbar-track {\r\n  background-color: #f1f1f1;\r\n}\r\n\r\n::-webkit-scrollbar-thumb {\r\n  background-color: #c0c0c0;\r\n  border-radius: 3px;\r\n}\r\n</style>\r\n\r\n"]}]}