{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\IconSelect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\IconSelect\\index.vue", "mtime": 1754876882531}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgaWNvbnMgZnJvbSAnLi9yZXF1aXJlSWNvbnMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0ljb25TZWxlY3QnLA0KICBwcm9wczogew0KICAgIGFjdGl2ZUljb246IHsNCiAgICAgIHR5cGU6IFN0cmluZw0KICAgIH0NCiAgfSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgbmFtZTogJycsDQogICAgICBpY29uTGlzdDogaWNvbnMNCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBmaWx0ZXJJY29ucygpIHsNCiAgICAgIHRoaXMuaWNvbkxpc3QgPSBpY29ucw0KICAgICAgaWYgKHRoaXMubmFtZSkgew0KICAgICAgICB0aGlzLmljb25MaXN0ID0gdGhpcy5pY29uTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLmluY2x1ZGVzKHRoaXMubmFtZSkpDQogICAgICB9DQogICAgfSwNCiAgICBzZWxlY3RlZEljb24obmFtZSkgew0KICAgICAgdGhpcy4kZW1pdCgnc2VsZWN0ZWQnLCBuYW1lKQ0KICAgICAgZG9jdW1lbnQuYm9keS5jbGljaygpDQogICAgfSwNCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMubmFtZSA9ICcnDQogICAgICB0aGlzLmljb25MaXN0ID0gaWNvbnMNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAqBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/IconSelect", "sourcesContent": ["<!-- <AUTHOR> -->\r\n<template>\r\n  <div class=\"icon-body\">\r\n    <el-input v-model=\"name\" clearable placeholder=\"图标名称\" style=\"position: relative;\" @clear=\"filterIcons\"\r\n              @input=\"filterIcons\">\r\n      <i slot=\"suffix\" class=\"el-icon-search el-input__icon\"/>\r\n    </el-input>\r\n    <div class=\"icon-list\">\r\n      <div class=\"list-container\">\r\n        <div v-for=\"(item, index) in iconList\" class=\"icon-item-wrapper\" :key=\"index\" @click=\"selectedIcon(item)\">\r\n          <div :class=\"['icon-item', { active: activeIcon === item }]\">\r\n            <svg-icon :icon-class=\"item\" class-name=\"icon\" style=\"height: 25px;width: 16px;\"/>\r\n            <span>{{ item }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport icons from './requireIcons'\r\n\r\nexport default {\r\n  name: 'IconSelect',\r\n  props: {\r\n    activeIcon: {\r\n      type: String\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      name: '',\r\n      iconList: icons\r\n    }\r\n  },\r\n  methods: {\r\n    filterIcons() {\r\n      this.iconList = icons\r\n      if (this.name) {\r\n        this.iconList = this.iconList.filter(item => item.includes(this.name))\r\n      }\r\n    },\r\n    selectedIcon(name) {\r\n      this.$emit('selected', name)\r\n      document.body.click()\r\n    },\r\n    reset() {\r\n      this.name = ''\r\n      this.iconList = icons\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style rel=\"stylesheet/scss\" lang=\"scss\" scoped>\r\n.icon-body {\r\n  width: 100%;\r\n  padding: 10px;\r\n  .icon-search {\r\n    position: relative;\r\n    margin-bottom: 5px;\r\n  }\r\n  .icon-list {\r\n    height: 200px;\r\n    ::v-deep .el-scrollbar {\r\n      height: 100%;\r\n      ::v-deep.el-scrollbar__wrap {\r\n        overflow-x: hidden;\r\n      }\r\n    }\r\n    .list-container {\r\n      height: 200px;\r\n      overflow: auto;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      .icon-item-wrapper {\r\n        width: calc(100% / 3);\r\n        height: 30px;\r\n        line-height: 30px;\r\n        margin-bottom: -5px;\r\n        cursor: pointer;\r\n        display: flex;\r\n        .icon-item {\r\n          display: flex;\r\n          max-width: 100%;\r\n          height: 100%;\r\n          padding: 0 2px;\r\n          &:hover {\r\n            background: #ececec;\r\n            border-radius: 5px;\r\n          }\r\n          .icon {\r\n            flex-shrink: 0;\r\n          }\r\n          span {\r\n            display: inline-block;\r\n            vertical-align: -0.15em;\r\n            fill: currentColor;\r\n            padding-left: 2px;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            white-space: nowrap;\r\n          }\r\n        }\r\n        .icon-item.active {\r\n          background: #ececec;\r\n          border-radius: 5px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}