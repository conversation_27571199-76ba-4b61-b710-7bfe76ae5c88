<template>
  <div class="data-aggregator">
    <el-row :gutter="20">
      <!-- 配置区域 - 左侧 -->
      <el-col :span="showResult ? 10 : 10">
        <el-card class="config-card">
          <template #header>
            <div class="header-with-operations">
              <span>汇总配置</span>
            </div>
          </template>
          <el-form class="edit" label-width="80px">
            <!-- 速查名称 -->
            <el-form-item label="速查名称" required>
              <el-row :gutter="10">
                <el-col :span="18">
                  <el-input v-model="config.name" placeholder="请输入速查名称"/>
                </el-col>
                <el-col :span="5">
                  <el-button size="small" type="text" @click="saveConfig">[↗]</el-button>
                  <el-button size="small" type="text" @click="loadConfigs">[...]</el-button>
                </el-col>
              </el-row>
            </el-form-item>

            <!-- 分组依据 -->
            <el-form-item label="分组依据">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-select v-model="config.primaryField" clearable filterable placeholder="操作单号">
                    <el-option
                      v-for="field in availableFields"
                      :key="field"
                      :label="getFieldLabel(field)"
                      :value="field"
                    />
                  </el-select>
                </el-col>
                <el-col :span="15">
                  <el-checkbox v-model="config.matchOptions.exact">精确匹配</el-checkbox>
                  <el-checkbox v-model="config.matchOptions.caseSensitive">区分大小写</el-checkbox>
                </el-col>
              </el-row>
            </el-form-item>

            <!-- 分组日期 -->
            <el-form-item label="分组日期">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-select v-model="config.dateField" clearable filterable placeholder="分组日期">
                    <el-option
                      v-for="field in dateFields"
                      :key="field"
                      :label="getFieldLabel(field)"
                      :value="field"
                    />
                  </el-select>
                </el-col>
                <el-col :span="15">
                  <el-checkbox v-model="config.dateOptions.convertToNumber">转换为数字</el-checkbox>
                  <el-radio-group v-model="config.dateOptions.formatType" style="display: flex;line-height: 26px">
                    <el-radio label="year">按年</el-radio>
                    <el-radio label="month">按月</el-radio>
                    <el-radio label="day">按天</el-radio>
                  </el-radio-group>
                </el-col>
              </el-row>
            </el-form-item>

            <!-- 数据筛选 -->
            <!--<el-form-item label="数据筛选">-->
            <!--  <el-button type="primary" plain size="small" @click="openFilterDialog">设置筛选条件</el-button>-->
            <!--  <div v-if="config.filters && config.filters.length" class="filter-tags">-->
            <!--    <el-tag-->
            <!--      v-for="(filter, index) in config.filters"-->
            <!--      :key="index"-->
            <!--      closable-->
            <!--      @close="removeFilter(index)"-->
            <!--    >-->
            <!--      {{getFieldLabel(filter.field)}} {{getOperatorLabel(filter.operator)}} {{filter.value}}-->
            <!--    </el-tag>-->
            <!--  </div>-->
            <!--</el-form-item>-->

            <!-- 显示方式 -->
            <el-form-item label="显示方式">
              <el-checkbox v-model="config.showDetails" style="padding-left: 5px;">含明细</el-checkbox>
            </el-form-item>

            <!-- 动态字段配置 -->
            <el-table
              :data="config.fields"
              border
              style="width: 100%"
            >
              <el-table-column
                align="center"
                label="序号"
                type="index"
                width="60"
              />

              <el-table-column label="表头名称" min-width="100">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.fieldKey"
                    filterable
                    placeholder="选择字段"
                    style="width: 100%"
                    @change="handleFieldSelect(scope.$index)"
                  >
                    <el-option
                      v-for="(config, key) in fieldLabelMap"
                      :key="key"
                      :label="config.name"
                      :value="key"
                    />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="排序" width="80">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.sort"
                    style="width: 100%"
                  >
                    <el-option label="-" value="none"/>
                    <el-option label="∧" value="asc"/>
                    <el-option label="∨ " value="desc"/>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="汇总方式" width="80">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.aggregation"
                    :disabled="!isAggregatable(scope.row.fieldKey)"
                    style="width: 100%"
                  >
                    <el-option label="-" value="none"/>
                    <el-option label="计数" value="count"/>
                    <el-option label="求和" value="sum"/>
                    <el-option label="平均值" value="avg"/>
                    <el-option label="最大值" value="max"/>
                    <el-option label="最小值" value="min"/>
                    <el-option label="方差" value="variance"/>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="显示格式" width="100">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.format"
                    style="width: 100%"
                  >
                    <el-option label="-" value="none"/>
                    <template v-if="getFieldDisplay(scope.row.fieldKey) === 'date'">
                      <el-option label="YYYYMM" value="YYYYMM"/>
                      <el-option label="MM-DD" value="MM-DD"/>
                      <el-option label="YYYY-MM-DD" value="YYYY-MM-DD"/>
                    </template>
                    <template v-if="getFieldDisplay(scope.row.fieldKey) === 'number'">
                      <el-option label="0.00" value="decimal"/>
                      <el-option label="0.00%" value="percent"/>
                      <el-option label="¥0.00" value="currency"/>
                      <el-option label="$0.00" value="usd"/>
                      <el-option label="0不显示" value="hideZero"/>
                    </template>
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="零值隐藏" width="80">
                <template #default="scope">
                  <el-checkbox
                    v-model="scope.row.hideZeroValues"
                    :disabled="getFieldDisplay(scope.row.fieldKey) !== 'number'"
                  />
                </template>
              </el-table-column>

              <el-table-column align="center" label="操作" width="120">
                <template #default="scope">
                  <el-button-group>
                    <el-button
                      :disabled="scope.$index === 0"
                      size="mini"
                      type="text"
                      @click="moveField(scope.$index, 'up')"
                    >[∧]
                    </el-button>
                    <el-button
                      :disabled="scope.$index === config.fields.length - 1"
                      size="mini"
                      type="text"
                      @click="moveField(scope.$index, 'down')"
                    >[∨]
                    </el-button>
                    <el-button
                      icon="el-icon-delete"
                      size="mini"
                      style="color: red"
                      type="text"
                      @click="removeField(scope.$index)"
                    >
                    </el-button>
                  </el-button-group>
                </template>
              </el-table-column>
            </el-table>

            <div style="margin-top: 10px;">
              <el-button plain type="text" @click="addField">[ + ]</el-button>
            </div>

            <el-form-item>
              <el-button type="primary" @click="handleServerAggregate">分类汇总</el-button>
              <el-button @click="resetConfig">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>

      <!-- 结果展示 - 右侧 -->
      <el-col v-if="showResult" :span="14">
        <el-card class="result-card">
          <template #header>
            <div class="header-with-operations">
              <span>汇总结果</span>
              <div class="operations">
                <el-switch
                  v-model="isLandscape"
                  active-text="横向"
                  inactive-text="纵向"
                  style="margin-right: 15px"
                />
                <el-button size="small" @click="printTable">打印</el-button>
                <el-button size="small" type="primary" @click="exportToPDF">导出PDF</el-button>
              </div>
            </div>
          </template>
          <el-table
            ref="resultTable"
            v-loading="loading"
            :data="processedData"
            :summary-method="getSummary"
            border
            show-summary
            style="width: 100%"
          >
            <!-- 分组字段列 -->
            <el-table-column
              :align="config.primaryField ? fieldLabelMap[config.primaryField].align : 'left'"
              :label="groupFieldName"
              :width="config.primaryField ? fieldLabelMap[config.primaryField].width : ''"
            >
              <template #default="scope">
                {{ formatGroupKey(scope.row.groupKey) }}
              </template>
            </el-table-column>

            <!-- 动态字段列 - 按照config.fields的顺序渲染 -->
            <template v-for="(field, fieldIndex) in config.fields">
              <el-table-column
                v-if="field.fieldKey"
                :key="field.fieldKey + '_' + fieldIndex"
                :align="getColumnAlign(field.fieldKey)"
                :label="getResultLabel(field)"
                :width="getColumnWidth(field.fieldKey)"
              >
                <template #default="scope">
                  {{ formatCellValue(scope.row[getResultProp(field)], field) }}
                </template>
              </el-table-column>
            </template>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 加载配置对话框 -->
    <el-dialog :visible.sync="configDialogVisible" append-to-body title="加载配置" width="550px">
      <el-table
        v-loading="configLoading"
        :data="savedConfigs"
        style="width: 100%"
        @row-click="handleConfigSelect"
      >
        <el-table-column label="配置名称" prop="name"/>
        <el-table-column label="创建时间" prop="createTime"/>
        <el-table-column width="50">
          <template #default="scope">
            <el-button type="text" @click.stop="deleteConfig(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 筛选条件对话框 -->
    <el-dialog :visible.sync="filterDialogVisible" title="设置筛选条件" width="650px">
      <el-form :model="currentFilter" label-width="100px">
        <el-form-item label="字段">
          <el-select v-model="currentFilter.field" filterable placeholder="选择字段" style="width: 100%">
            <el-option
              v-for="(config, key) in fieldLabelMap"
              :key="key"
              :label="config.name"
              :value="key"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="操作符">
          <el-select v-model="currentFilter.operator" placeholder="选择操作符" style="width: 100%">
            <el-option label="等于" value="eq"/>
            <el-option label="不等于" value="ne"/>
            <el-option label="大于" value="gt"/>
            <el-option label="大于等于" value="ge"/>
            <el-option label="小于" value="lt"/>
            <el-option label="小于等于" value="le"/>
            <el-option label="包含" value="contains"/>
            <el-option label="开始于" value="startsWith"/>
            <el-option label="结束于" value="endsWith"/>
          </el-select>
        </el-form-item>

        <el-form-item label="值">
          <el-input v-model="currentFilter.value" placeholder="输入筛选值"/>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="addFilter">添加筛选条件</el-button>
        </el-form-item>
      </el-form>

      <div v-if="config.filters && config.filters.length">
        <h4>已添加的筛选条件</h4>
        <el-table :data="config.filters" border>
          <el-table-column label="字段" prop="field">
            <template #default="scope">
              {{ getFieldLabel(scope.row.field) }}
            </template>
          </el-table-column>
          <el-table-column label="操作符" prop="operator">
            <template #default="scope">
              {{ getOperatorLabel(scope.row.operator) }}
            </template>
          </el-table-column>
          <el-table-column label="值" prop="value"/>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button
                circle
                icon="el-icon-delete"
                size="mini"
                type="danger"
                @click="removeFilter(scope.$index)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer">
        <el-button @click="filterDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment"
import currency from "currency.js"
import {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from "@/api/system/aggregator"
import html2pdf from "html2pdf.js"

export default {
  name: "DataAggregatorBackGround",
  props: {
    // 可用字段配置映射
    fieldLabelMap: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 指定数据来源类型，用于后端查询
    dataSourceType: {
      type: String,
      required: true,
      default: 'rct' // 默认是操作单数据
    },
    // 从父组件传入的汇总函数
    aggregateFunction: {
      type: Function,
      required: true
    },
    configType: {
      type: String,
      required: false
    }
  },
  data() {
    return {
      configName: "",
      config: {
        name: "",
        primaryField: "",
        matchOptions: {
          exact: true,
          caseSensitive: false
        },
        dateField: "",
        dateOptions: {
          convertToNumber: false,
          formatType: "day"
        },
        showDetails: false,
        fields: [],
        filters: [] // 存储筛选条件
      },
      dateOptions: [
        {label: "按年", value: "year"},
        {label: "按月", value: "month"},
        {label: "按周", value: "week"},
        {label: "按日", value: "day"},
        {label: "按时", value: "hour"},
        {label: "按分", value: "minute"}
      ],
      aggregationOptions: [
        {label: "计数", value: "count"},
        {label: "求和", value: "sum"},
        {label: "平均值", value: "avg"},
        {label: "方差", value: "variance"},
        {label: "最大值", value: "max"},
        {label: "最小值", value: "min"}
      ],
      operatorOptions: [
        {label: "等于", value: "eq"},
        {label: "不等于", value: "ne"},
        {label: "大于", value: "gt"},
        {label: "大于等于", value: "ge"},
        {label: "小于", value: "lt"},
        {label: "小于等于", value: "le"},
        {label: "包含", value: "contains"},
        {label: "开始于", value: "startsWith"},
        {label: "结束于", value: "endsWith"}
      ],
      loading: false,
      configDialogVisible: false,
      filterDialogVisible: false,
      savedConfigs: [],
      configLoading: false,
      isLandscape: false,
      showResult: false,
      processedData: [],
      currentFilter: {
        field: "",
        operator: "eq",
        value: ""
      }
    }
  },
  computed: {
    // 可用字段列表
    availableFields() {
      // 返回在 fieldLabelMap 中定义的所有字段
      return Object.keys(this.fieldLabelMap)
    },

    // 日期类型字段列表
    dateFields() {
      return this.availableFields.filter(field => {
        // 检查 fieldLabelMap 中的 display 属性
        return this.fieldLabelMap[field] && this.fieldLabelMap[field].display === "date"
      })
    },

    // 分组字段名称
    groupFieldName() {
      if (this.config.primaryField && this.config.dateField) {
        return `${this.getFieldLabel(this.config.dateField)}+${this.getFieldLabel(this.config.primaryField)}`
      } else if (this.config.primaryField) {
        return this.getFieldLabel(this.config.primaryField)
      } else if (this.config.dateField) {
        return this.getFieldLabel(this.config.dateField)
      }
      return "分组"
    }
  },
  methods: {
    /**
     * 根据字段键获取字段标签
     * @param {String} field - 字段的键
     * @returns {String} 字段的标签
     */
    getFieldLabel(field) {
      return this.fieldLabelMap[field]?.name || field
    },

    /**
     * 根据操作符代码获取操作符标签
     * @param {String} op - 操作符代码
     * @returns {String} 操作符标签
     */
    getOperatorLabel(op) {
      const operator = this.operatorOptions.find(item => item.value === op)
      return operator ? operator.label : op
    },

    /**
     * 打开筛选条件对话框
     */
    openFilterDialog() {
      this.currentFilter = {
        field: "",
        operator: "eq",
        value: ""
      }
      this.filterDialogVisible = true
    },

    /**
     * 添加筛选条件
     */
    addFilter() {
      if (!this.currentFilter.field) {
        this.$message.warning("请选择筛选字段")
        return
      }

      if (!this.currentFilter.value && this.currentFilter.value !== 0) {
        this.$message.warning("请输入筛选值")
        return
      }

      // 初始化filters数组（如果不存在）
      if (!this.config.filters) {
        this.$set(this.config, 'filters', [])
      }

      // 添加新的筛选条件
      this.config.filters.push({...this.currentFilter})

      // 重置当前筛选条件
      this.currentFilter = {
        field: "",
        operator: "eq",
        value: ""
      }
    },

    /**
     * 移除筛选条件
     * @param {Number} index - 要移除的筛选条件索引
     */
    removeFilter(index) {
      this.config.filters.splice(index, 1)
    },

    /**
     * 获取字段的显示类型
     * @param {string} fieldKey - 字段标识
     * @returns {string} 字段显示类型（text/number/date/boolean/custom）
     */
    getFieldDisplay(fieldKey) {
      const fieldConfig = this.fieldLabelMap[fieldKey]
      if (!fieldConfig) return "text"

      // 检查是否是自定义方法
      if (fieldConfig.display && typeof this[fieldConfig.display] === "function") {
        return "custom"
      }

      return fieldConfig.display || "text"
    },

    /**
     * 判断字段是否可以进行汇总计算
     * @param {string} fieldKey - 字段标识
     * @returns {boolean} 是否可汇总
     */
    isAggregatable(fieldKey) {
      const fieldConfig = this.fieldLabelMap[fieldKey]
      return fieldConfig?.aggregated || false
    },

    /**
     * 添加新的字段配置行
     */
    addField() {
      this.config.fields.push({
        fieldKey: "",
        aggregation: "none",
        format: "none",
        sort: "none",
        hideZeroValues: false
      })
    },

    /**
     * 删除指定索引的字段配置行
     * @param {number} index - 要删除的字段索引
     */
    removeField(index) {
      this.config.fields.splice(index, 1)
    },

    /**
     * 移动字段配置行的位置
     * @param {number} index - 当前字段的索引
     * @param {string} direction - 移动方向，'up' 或 'down'
     */
    moveField(index, direction) {
      const fields = [...this.config.fields] // 创建数组副本

      if (direction === "up" && index > 0) {
        // 向上移动，与上一个元素交换位置
        [fields[index], fields[index - 1]] = [fields[index - 1], fields[index]]
      } else if (direction === "down" && index < fields.length - 1) {
        // 向下移动，与下一个元素交换位置
        [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]
      }

      // 使用整个新数组替换，确保响应式更新
      this.$set(this.config, "fields", fields)
    },

    /**
     * 处理字段选择变更事件
     * @param {number} index - 变更的字段索引
     */
    handleFieldSelect(index) {
      const field = this.config.fields[index]
      const fieldConfig = this.fieldLabelMap[field.fieldKey]
      if (fieldConfig) {
        // 根据字段配置设置默认值
        field.format = this.getDefaultFormat(fieldConfig.display)
        field.aggregation = fieldConfig.aggregated ? "sum" : "none"
        field.sort = "none" // 默认不排序
      }
    },

    /**
     * 根据显示类型获取默认的格式化方式
     * @param {string} displayType - 显示类型
     * @returns {string} 默认格式
     */
    getDefaultFormat(displayType) {
      switch (displayType) {
        case "date":
          return "YYYY-MM-DD"
        case "number":
          return "decimal"
        default:
          return "none"
      }
    },

    /**
     * 重置配置到初始状态
     */
    resetConfig() {
      this.config = {
        name: "",
        primaryField: "",
        matchOptions: {
          exact: true,
          caseSensitive: false
        },
        dateField: "",
        dateOptions: {
          convertToNumber: false,
          formatType: "day"
        },
        showDetails: false,
        fields: [],
        filters: []
      }
      this.showResult = false
    },

    /**
     * 获取日期格式化模式
     * @returns {String} 日期格式
     */
    getDateFormat() {
      switch (this.config.dateOptions.formatType) {
        case "year":
          return "YYYY"
        case "month":
          return "YYYY-MM"
        case "day":
        default:
          return "YYYY-MM-DD"
      }
    },

    /**
     * 调用后端服务进行数据汇总
     */
    async handleServerAggregate() {
      // 验证分组依据和分组日期至少填写一个
      if (!this.config.primaryField && !this.config.dateField) {
        this.$message.warning("请至少选择分组依据或分组日期其中之一");
        return;
      }

      // 验证是否有字段配置
      if (!this.config.fields.length) {
        this.$message.warning("请添加至少一个字段");
        return;
      }

      // 验证字段配置是否完整
      const incompleteField = this.config.fields.find(field => !field.fieldKey);
      if (incompleteField) {
        this.$message.warning("请完成所有字段的配置");
        return;
      }

      // 确保汇总函数已经传入
      if (!this.aggregateFunction) {
        this.$message.error("汇总函数未定义")
        return
      }

      try {
        this.loading = true

        // 准备请求参数
        const params = {
          dataSourceType: this.dataSourceType,
          config: {
            primaryField: this.config.primaryField,
            matchOptions: this.config.matchOptions,
            dateField: this.config.dateField,
            dateOptions: this.config.dateOptions,
            showDetails: this.config.showDetails,
            fields: this.config.fields,
            filters: this.config.filters || []
          }
        }

        // 调用从父组件传入的汇总函数
        const response = await this.aggregateFunction(params)

        if (response.code === 200) {
          // 过滤零值记录
          this.processedData = this.filterZeroValueRecords(response.data)
          this.showResult = true
        } else {
          this.$message.error(response.msg || "汇总数据失败")
        }
      } catch (error) {
        console.error("数据汇总失败:", error)
        this.$message.error("汇总处理失败：" + (error.message || "未知错误"))
      } finally {
        this.loading = false
      }
    },

    /**
     * 过滤零值记录
     * @param {Array} data - 原始数据
     * @returns {Array} 过滤后的数据
     */
    filterZeroValueRecords(data) {
      // 找出设置了hideZeroValues为true的字段
      const zeroFilterFields = this.config.fields
        .filter(field => field.hideZeroValues === true)
        .map(field => ({
          key: field.fieldKey,
          aggProp: this.getResultProp(field)
        }));

      // 如果没有需要过滤的字段，直接返回原始数据
      if (zeroFilterFields.length === 0) {
        return data;
      }

      // 过滤数据
      return data.filter(record => {
        // 检查每个需要过滤零值的字段
        for (const field of zeroFilterFields) {
          const value = record[field.aggProp];
          // 如果字段值为0，过滤掉这条记录
          if (value === 0 || value === "0" || value === "0.00") {
            return false;
          }
        }
        // 所有字段都不为零，保留这条记录
        return true;
      });
    },

    /**
     * 获取结果数据的属性名
     * @param {Object} field - 字段配置
     * @returns {string} 属性名
     */
    getResultProp(field) {
      // 如果有汇总方式，属性名为 fieldKey_aggregation
      if (field.aggregation && field.aggregation !== "none") {
        return `${field.fieldKey}_${field.aggregation}`
      }
      return field.fieldKey
    },

    /**
     * 获取结果表格的列标题
     * @param {Object} field - 字段配置
     * @returns {string} 列标题
     */
    getResultLabel(field) {
      const baseLabel = this.getFieldLabel(field.fieldKey)

      // 如果有汇总方式，在标签中添加汇总方式信息
      if (field.aggregation && field.aggregation !== "none") {
        // 获取汇总方式的中文名称
        const aggregationLabel = this.aggregationOptions.find(opt => opt.value === field.aggregation)?.label || field.aggregation
        return `${baseLabel}(${aggregationLabel})`
      }

      return baseLabel
    },

    /**
     * 格式化单元格的值
     * @param {*} value - 原始值
     * @param {Object} field - 字段配置
     * @returns {string} 格式化后的值
     */
    formatCellValue(value, field) {
      if (value == null) return "-"

      const fieldConfig = this.fieldLabelMap[field.fieldKey]
      if (!fieldConfig) return value

      // 处理自定义 display 方法
      if (fieldConfig.display && typeof this[fieldConfig.display] === "function") {
        // 调用组件中定义的方法
        return this[fieldConfig.display](value)
      }

      // 根据字段类型进行格式化
      switch (fieldConfig.display) {
        case "number":
          const numValue = Number(value)
          if (isNaN(numValue)) return "-"

          switch (field.format) {
            case "decimal":
              return numValue.toFixed(2)
            case "percent":
              return (numValue * 100).toFixed(2) + "%"
            case "currency":
              return "¥" + numValue.toFixed(2)
            case "usd":
              return "$" + numValue.toFixed(2)
            case "hideZero":
              return numValue === 0 ? "-" : numValue.toFixed(2)
            default:
              return numValue.toFixed(2)
          }

        case "date":
          return moment(value).format(field.format || "YYYY-MM-DD")

        case "boolean":
          if (field.aggregation === "avg") {
            return (Number(value) * 100).toFixed(2) + "%"
          }
          return value ? "是" : "否"

        default:
          return value
      }
    },

    /**
     * 格式化分组键
     * @param {Object|string} groupKey - 分组键
     * @returns {string} 格式化后的分组键
     */
    formatGroupKey(groupKey) {
      if (typeof groupKey === "object" && groupKey !== null) {
        if (groupKey.primary !== undefined && groupKey.date !== undefined) {
          // 获取主分组字段的配置
          const primaryFieldConfig = this.fieldLabelMap[this.config.primaryField]
          let primaryValue = groupKey.primary

          // 如果主分组字段有自定义 display 方法，应用它
          if (primaryFieldConfig && primaryFieldConfig.display &&
            typeof this[primaryFieldConfig.display] === "function") {
            primaryValue = this[primaryFieldConfig.display](primaryValue)
          }

          // 日期值在前，主值在后
          return `${groupKey.date} ${primaryValue}`
        }
      }

      // 如果是简单值，检查是否需要应用自定义 display 方法
      if (this.config.primaryField) {
        const fieldConfig = this.fieldLabelMap[this.config.primaryField]
        if (fieldConfig && fieldConfig.display &&
          typeof this[fieldConfig.display] === "function") {
          return this[fieldConfig.display](groupKey)
        }
      }

      return String(groupKey || "")
    },

    /**
     * 计算表格合计行
     * @param {Object} param0 - 包含列信息和数据的对象
     * @returns {Array} 合计行数据
     */
    getSummary({columns, data}) {
      const sums = []

      columns.forEach((column, index) => {
        // 第一列显示"合计"文本
        if (index === 0) {
          sums[index] = "合计"
          return
        }

        // 使用索引获取当前列对应的字段配置
        const fieldIndex = index - 1
        const field = this.config.fields[fieldIndex]

        if (!field || !field.fieldKey) {
          sums[index] = ""
          return
        }

        // 检查字段是否配置了汇总方式
        if (!field.aggregation || field.aggregation === "none") {
          sums[index] = ""
          return
        }

        // 获取字段配置
        const fieldConfig = this.fieldLabelMap[field.fieldKey]

        // 对于不是数字类型的字段但有percentage显示方法的特殊处理
        if (!fieldConfig) {
          sums[index] = ""
          return
        }

        if (fieldConfig.display !== "number" &&
          fieldConfig.display !== "percentage" &&
          typeof this[fieldConfig.display] !== "function") {
          sums[index] = ""
          return
        }

        // 获取列数据并转换为数字
        const values = data.map(item => {
          const prop = this.getResultProp(field)
          const val = Number(item[prop])
          return isNaN(val) ? 0 : val // 处理非数字值
        }).filter(val => !isNaN(val))

        if (values.length === 0) {
          sums[index] = ""
          return
        }

        // 根据汇总方式计算结果
        let sum = 0
        switch (field.aggregation) {
          case "sum":
            sum = values.reduce((a, b) => a + b, 0)
            break
          case "avg":
            sum = values.reduce((a, b) => a + b, 0) / values.length
            break
          case "max":
            sum = Math.max(...values)
            break
          case "min":
            sum = Math.min(...values)
            break
          case "variance":
            const mean = values.reduce((a, b) => a + b, 0) / values.length
            sum = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length
            break
          default:
            sum = values.reduce((a, b) => a + b, 0)
        }

        // 根据字段显示类型和格式设置来格式化结果
        if (fieldConfig.display === "percentage" || fieldConfig.display === "percent") {
          // 使用percentage方法格式化
          sums[index] = this.percentage(sum)
        } else if (field.format === "decimal") {
          sums[index] = sum.toFixed(2)
        } else if (field.format === "percent") {
          sums[index] = (sum * 100).toFixed(2) + "%"
        } else if (field.format === "currency") {
          sums[index] = "¥" + sum.toFixed(2)
        } else if (field.format === "usd") {
          sums[index] = "$" + sum.toFixed(2)
        } else {
          sums[index] = sum.toFixed(2)
        }
      })

      return sums
    },

    /**
     * 保存当前配置
     */
    async saveConfig() {
      try {
        // 验证配置名称
        if (!this.config.name) {
          this.$message.warning("请输入速查名称")
          return
        }

        // 验证分组依据和分组日期至少填写一个
        if (!this.config.primaryField && !this.config.dateField) {
          this.$message.warning("请至少选择分组依据或分组日期其中之一")
          return
        }

        if (!this.config.fields.length) {
          this.$message.warning("请添加至少一个字段")
          return
        }

        // 验证字段配置是否完整
        const incompleteField = this.config.fields.find(field => !field.fieldKey)
        if (incompleteField) {
          this.$message.warning("请完成所有字段的配置")
          return
        }

        // 构造符合 AggregatorConfigDTO 的数据结构
        const configToSave = {
          name: this.config.name,
          type: this.configType,
          config: this.config
        }

        // 发送请求
        await saveAggregatorConfig(configToSave)

        this.$message.success("配置保存成功")
      } catch (err) {
        if (err !== "cancel") {
          this.$message.error("保存配置失败：" + (err.message || "未知错误"))
        }
      }
    },

    /**
     * 加载保存的配置列表
     */
    async loadConfigs() {
      this.configLoading = true
      this.configDialogVisible = true
      try {
        const result = await loadAggregatorConfigs({configType: this.configType})
        this.savedConfigs = result.rows
      } catch (err) {
        console.error("加载配置失败:", err)
        this.$message.error(
          err.response?.data?.message ||
          err.message ||
          "加载配置列表失败，请稍后重试"
        )
      } finally {
        this.configLoading = false
      }
    },

    /**
     * 选择并加载配置
     * @param {Object} row - 选中的配置行
     */
    async handleConfigSelect(row) {
      try {
        // 解析配置JSON
        var config = JSON.parse(row.config)
        config.name = row.name
        this.config = config
        // this.config.name = row.name

        this.configDialogVisible = false
        this.$message.success("配置加载成功")
      } catch (err) {
        console.error("加载配置失败:", err)
        this.$message.error("加载配置失败：" + err.message)
      }
    },

    /**
     * 删除配置
     * @param {Object} row - 要删除的配置行
     */
    async deleteConfig(row) {
      try {
        await this.$confirm("确认删除该配置？", "提示", {
          type: "warning"
        })

        await deleteAggregatorConfig(row.id)
        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)
        this.$message.success("配置删除成功")
      } catch (err) {
        if (err !== "cancel") {
          this.$message.error("删除配置失败：" + err.message)
        }
      }
    },

    /**
     * 打印表格
     */
    printTable() {
      // 实现与原组件相同的打印功能
      const printWindow = window.open("", "_blank")
      const table = this.$refs.resultTable.$el.cloneNode(true)
      const title = "汇总数据"
      const date = new Date().toLocaleDateString()

      // 公司标志和标题的HTML模板
      const headerTemplate = `
        <div class="company-header">
          <div class="company-logo">
            <img src="/logo.png" alt="Rich Shipping Logo" />
            <div class="company-name">
              <div class="company-name-cn">广州瑞旗国际货运代理有限公司</div>
              <div class="company-name-en">GUANGZHOU RICH SHIPPING INT'L CO.,LTD.</div>
            </div>
          </div>
          <div class="document-title">
            <div class="title-cn"></div>
            <div class="title-en"></div>
          </div>
        </div>
      `
      printWindow.document.write(`
        <html lang="">
          <head>
            <title>${title}</title>
            <style>
              /* 基础样式 */
              body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
              }

              /* 打印样式 - 必须放在这里才能生效 */
              @media print {
                @page {
                  size: ${this.isLandscape ? "landscape" : "portrait"};
                  margin: 1.5cm 1cm 1cm 1cm;
                }

                /* 重要：使用重复表头技术 */
                thead {
                  display: table-header-group;
                }

                /* 页眉作为表格的一部分，放在thead中 */
                .page-header {
                  display: table-header-group;
                }

                /* 内容部分 */
                .page-content {
                  display: table-row-group;
                }

                /* 避免元素内部分页 */
                .company-header, .header-content {
                  page-break-inside: avoid;
                }

                /* 表格样式 */
                table.main-table {
                  width: 100%;
                  border-collapse: collapse;
                  border: none;
                }

                /* 确保表头在每页都显示 */
                table.data-table thead {
                  display: table-header-group;
                }

                /* 避免行内分页 */
                table.data-table tr {
                  page-break-inside: avoid;
                }
              }

              /* 表格样式 */
              table.data-table {
                border-collapse: collapse;
                width: 100%;
                margin-top: 20px;
                table-layout: fixed;
              }

              table.data-table th, table.data-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
                font-size: 12px;
                word-wrap: break-word;
                word-break: break-all;
                white-space: normal;
              }

              table.data-table th {
                background-color: #f2f2f2;
              }

              /* Element UI 表格样式模拟 */
              .el-table {
                border-collapse: collapse;
                width: 100%;
                table-layout: fixed;
              }

              .el-table th, .el-table td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
                font-size: 12px;
                word-wrap: break-word;
                word-break: break-all;
                white-space: normal;
              }

              .el-table th {
                background-color: #f2f2f2;
                font-weight: bold;
              }

              .el-table__footer {
                background-color: #f8f8f9;
                font-weight: bold;
              }

              .el-table__footer td {
                border: 1px solid #ddd;
                padding: 8px;
              }

              /* 公司标题和标志样式 */
              .company-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
                width: 100%;
              }

              .company-logo {
                display: flex;
                align-items: center;
              }

              .company-logo img {
                height: 50px;
                margin-right: 10px;
              }

              .company-name {
                display: flex;
                flex-direction: column;
              }

              .company-name-cn {
                font-size: 18px;
                font-weight: bold;
                color: #ff0000;
              }

              .company-name-en {
                font-size: 14px;
              }

              .document-title {
                text-align: right;
              }

              .title-cn {
                font-size: 18px;
                font-weight: bold;
              }

              .title-en {
                font-size: 16px;
                font-weight: bold;
              }

              /* 清除表格边框 */
              table.main-table, table.main-table td {
                border: none;
              }

              /* 页眉容器 */
              .header-container {
                width: 100%;
                margin-bottom: 20px;
              }
            </style>
          </head>
          <body>
            <!-- 使用表格布局确保页眉在每页重复 -->
            <table class="main-table">
              <thead class="page-header">
                <tr>
                  <td>
                    <div class="header-container">
                      ${headerTemplate}
                    </div>
                  </td>
                </tr>
              </thead>
              <tbody class="page-content">
                <tr>
                  <td>
                    <!-- 保留原始表格的类名并添加data-table类 -->
                    ${table.outerHTML.replace('<table', '<table class="el-table data-table"')}
                  </td>
                </tr>
              </tbody>
            </table>
          </body>
        </html>
      `)

      printWindow.document.close()

      setTimeout(() => {
        try {
          printWindow.focus();
          printWindow.print();
        } catch (e) {
          console.error("打印过程中发生错误:", e);
        }
      }, 1000)
    },

    /**
     * 导出PDF
     */
    async exportToPDF() {
      try {
        this.loading = true
        const element = this.$refs.resultTable.$el
        const opt = {
          margin: [0.8, 0.8, 0.8, 0.8],
          filename: "汇总数据.pdf",
          image: {type: "jpeg", quality: 0.98},
          html2canvas: {scale: 2},
          jsPDF: {
            unit: "in",
            format: "a3",
            orientation: this.isLandscape ? "landscape" : "portrait"
          },
          pagebreak: {mode: ["avoid-all", "css", "legacy"]},
          header: [
            {text: "汇总数据", style: "headerStyle"},
            {text: new Date().toLocaleDateString(), style: "headerStyle", alignment: "right"}
          ],
          footer: {
            height: "20px",
            contents: {
              default: "<span style=\"float:right\">{{page}}/{{pages}}</span>"
            }
          }
        }

        await html2pdf().set(opt).from(element).save()
        this.$message.success("PDF导出成功")
      } catch (error) {
        this.$message.error("PDF导出失败：" + error.message)
      } finally {
        this.loading = false
      }
    },

    /**
     * 获取人员姓名
     * @param {Number} id - 人员ID
     * @returns {String} 人员姓名
     */
    getName(id) {
      if (id !== null) {
        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]
        if (staff && staff !== undefined) {
          return staff.staffShortName + staff.staffFamilyEnName
        }
      }
      return ""
    },

    /**
     * 格式化百分比值
     * @param {*} value - 要格式化的值
     * @returns {string} 格式化后的百分比
     */
    percentage(value) {
      if (value == null || value === '') {
        return '-';
      }

      // 处理已经带有%的情况
      if (typeof value === 'string' && value.includes('%')) {
        return value;
      }

      // 将数值转换为百分比格式
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return '-';
      }

      // 如果值已经是百分比形式(例如0.25表示25%)，则直接乘以100
      // 如果值已经是整数形式(例如25表示25%)，则不需要乘以100
      const isDecimal = numValue > 0 && numValue <= 1;
      const percentValue = isDecimal ? numValue * 100 : numValue;

      // 格式化为2位小数的百分比
      return percentValue.toFixed(2) + '%';
    },

    /**
     * 获取列对齐方式
     * @param {string} fieldKey - 字段键
     * @returns {string} 对齐方式
     */
    getColumnAlign(fieldKey) {
      const fieldConfig = this.fieldLabelMap[fieldKey]
      return fieldConfig && fieldConfig.align ? fieldConfig.align : 'left'
    },

    /**
     * 获取列宽度
     * @param {string} fieldKey - 字段键
     * @returns {string|number} 列宽度
     */
    getColumnWidth(fieldKey) {
      const fieldConfig = this.fieldLabelMap[fieldKey]
      return fieldConfig && fieldConfig.width ? fieldConfig.width : ''
    }
  }
}
</script>

<style scoped>
.data-aggregator {
  padding: 20px;
}

.config-card, .result-card {
  height: 100%;
  overflow: auto;
}

.result-card {
  margin-bottom: 20px;
}

.header-with-operations {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operations {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-tags {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
}

.el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.field-config-table {
  border: 1px solid #EBEEF5;
  margin-bottom: 10px;
}

.table-header,
.table-row {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #EBEEF5;
}

.table-header {
  background-color: #F5F7FA;
  font-weight: bold;
}

.col {
  flex: 1;
  padding: 0 5px;
  min-width: 120px;
}

.col:first-child {
  flex: 0 0 60px;
  min-width: 60px;
}

.col-operation {
  flex: 0 0 120px;
  text-align: center;
}

.el-select {
  width: 100%;
}

.el-input-number {
  width: 100%;
}
</style>

