{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\components\\VatinvoiceDialog.vue", "mtime": 1754883462716}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2dldENvbXBhbnl9IGZyb20gIkAvYXBpL3N5c3RlbS9jb21wYW55Ig0KaW1wb3J0IHtsaXN0QWNjb3VudH0gZnJvbSAiQC9hcGkvc3lzdGVtL2FjY291bnQiDQppbXBvcnQgRmlsZVVwbG9hZCBmcm9tICJAL2NvbXBvbmVudHMvRmlsZVVwbG9hZCINCmltcG9ydCBUcmVlc2VsZWN0IGZyb20gIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0Ig0KaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0LmNzcyINCg0KLy8g6Ziy5oqW5Ye95pWwDQpmdW5jdGlvbiBkZWJvdW5jZShmbiwgZGVsYXkpIHsNCiAgbGV0IHRpbWVyID0gbnVsbA0KICByZXR1cm4gZnVuY3Rpb24gKCkgew0KICAgIGNvbnN0IGNvbnRleHQgPSB0aGlzDQogICAgY29uc3QgYXJncyA9IGFyZ3VtZW50cw0KICAgIGNsZWFyVGltZW91dCh0aW1lcikNCiAgICB0aW1lciA9IHNldFRpbWVvdXQoZnVuY3Rpb24gKCkgew0KICAgICAgZm4uYXBwbHkoY29udGV4dCwgYXJncykNCiAgICB9LCBkZWxheSkNCiAgfQ0KfQ0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJWYXRpbnZvaWNlRGlhbG9nIiwNCiAgY29tcG9uZW50czogew0KICAgIEZpbGVVcGxvYWQsDQogICAgVHJlZXNlbGVjdA0KICB9LA0KICBwcm9wczogew0KICAgIC8vIOaYr+WQpuaYvuekuuWvueivneahhg0KICAgIHZpc2libGU6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgLy8g5qCH6aKYDQogICAgdGl0bGU6IHsNCiAgICAgIHR5cGU6IFN0cmluZywNCiAgICAgIGRlZmF1bHQ6ICIiDQogICAgfSwNCiAgICAvLyDooajljZXmlbDmja4NCiAgICBmb3JtOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfSwNCiAgICAvLyDooajljZXpqozor4Hop4TliJkNCiAgICBydWxlczogew0KICAgICAgdHlwZTogT2JqZWN0LA0KICAgICAgZGVmYXVsdDogKCkgPT4gKHt9KQ0KICAgIH0sDQogICAgLy8g5Y+R56Wo5piO57uG5YiX6KGoDQogICAgaW52b2ljZUl0ZW1zOiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICBjb21wYW55TGlzdDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgLy8g6ZO26KGM6LSm5oi35YiX6KGoDQogICAgYmFua0FjY291bnRMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfQ0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDlhoXpg6jlr7nor53moYblj6/op4HmgKfnirbmgIENCiAgICAgIGRpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgLy8g6KGo5Y2V5pWw5o2u55qE5Ymv5pysDQogICAgICBmb3JtRGF0YToge30sDQogICAgICAvLyDlj5HnpajmmI7nu4bliJfooajnmoTlia/mnKwNCiAgICAgIGludm9pY2VJdGVtTGlzdDogW10sDQogICAgICAvLyDpmLLmipblkI7nmoTojrflj5blhazlj7jkv6Hmga/mlrnms5UNCiAgICAgIGRlYm91bmNlZEZldGNoQ29tcGFueUluZm86IG51bGwsDQogICAgICAvLyDlhazlj7jpk7booYzotKbmiLfliJfooagNCiAgICAgIGNvbXBhbnlCYW5rTGlzdDogW10sDQogICAgICAvLyDlvIDnpajpobnnm67pgInpobnmlbDmja4NCiAgICAgIGludm9pY2luZ0l0ZW1PcHRpb25zOiBbDQogICAgICAgIHsNCiAgICAgICAgICBpZDogIjEiLA0KICAgICAgICAgIGludm9pY2luZ0l0ZW1OYW1lOiAi57uP57qq5Luj55CG5pyN5YqhIiwNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAge2lkOiAiMS0xIiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIbov5DotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMiIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Zu96ZmF6LSn54mp6L+Q6L6T5Luj55CG5pyN5Yqh6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTMiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhua4r+adgui0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS00IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLlm73pmYXotKfnianov5DovpPku6PnkIbmnI3liqEifSwNCiAgICAgICAgICAgIHtpZDogIjEtNSIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5oql5YWz5pyN5Yqh6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTYiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhuacjeWKoei0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS03IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIbmiqXlhbPotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtOCIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5ouW6L2m6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTkiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuWbvemZhei0p+eJqei/kOi+k+S7o+eQhuacjeWKoS3ku6PnkIbov5DotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMTAiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhuWbveWGhei/kOi0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0xMSIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Zu96ZmF6LSn54mp6L+Q6L6T5Luj55CG5rW36L+Q6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTEyIiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIbov5DotLnotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMTMiLCBpbnZvaWNpbmdJdGVtTmFtZTogIui/kOi+k+S7o+eQhui0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0xNCIsIGludm9pY2luZ0l0ZW1OYW1lOiAi6LSn54mp6L+Q6L6T5Luj55CG5pyN5Yqh6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTE1IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLlm73pmYXotKfnianov5DovpPku6PnkIbmuK/mnYLotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMTYiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuWbvemZhei0p+eJqei/kOi+k+S7o+eQhui/kOi0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0xNyIsIGludm9pY2luZ0l0ZW1OYW1lOiAi6LSn54mp6L+Q6L6T5Luj55CG6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTE4IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLlm73pmYXotKfnianov5DovpPku6PnkIbotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMTkiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhuadgui0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0yMCIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5paH5Lu26LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTIxIiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIborr7lpIfkuqTmjqXljZXotLnnlKgifSwNCiAgICAgICAgICAgIHtpZDogIjEtMjIiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhuiIseWNleeUs+aKpei0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0yMyIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG5pON5L2c6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTI0IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIblsIHmnaHotLkifSwNCiAgICAgICAgICAgIHtpZDogIjEtMjUiLCBpbnZvaWNpbmdJdGVtTmFtZTogIuS7o+eQhueggeWktOaTjeS9nOi0uSJ9LA0KICAgICAgICAgICAge2lkOiAiMS0yNiIsIGludm9pY2luZ0l0ZW1OYW1lOiAi5Luj55CG55S15pS+6LS5In0sDQogICAgICAgICAgICB7aWQ6ICIxLTI3IiwgaW52b2ljaW5nSXRlbU5hbWU6ICLku6PnkIbmoLjph43otLkifQ0KICAgICAgICAgIF0NCiAgICAgICAgfQ0KICAgICAgXQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAvLyDlj6/nlKjnmoTpk7booYzotKbmiLfliJfooajvvJrkvJjlhYjkvb/nlKhjb21wYW55QmFua0xpc3TvvIzkuLrnqbrml7bkvb/nlKjkvKDlhaXnmoRiYW5rQWNjb3VudExpc3QNCiAgICBhdmFpbGFibGVCYW5rTGlzdCgpIHsNCiAgICAgIHJldHVybiB0aGlzLmNvbXBhbnlCYW5rTGlzdC5sZW5ndGggPiAwID8gdGhpcy5jb21wYW55QmFua0xpc3QgOiB0aGlzLmJhbmtBY2NvdW50TGlzdA0KICAgIH0NCiAgfSwNCg0KICBjcmVhdGVkKCkgew0KICAgIC8vIOWIm+W7uumYsuaKlueJiOacrOeahGZldGNoQ29tcGFueUluZm/mlrnms5XvvIzorr7nva4zMDBtc+W7tui/nw0KICAgIHRoaXMuZGVib3VuY2VkRmV0Y2hDb21wYW55SW5mbyA9IGRlYm91bmNlKHRoaXMuZmV0Y2hDb21wYW55SW5mbywgMzAwKQ0KICB9LA0KICB3YXRjaDogew0KICAgIHZpc2libGU6IHsNCiAgICAgIGhhbmRsZXIodmFsKSB7DQogICAgICAgIHRoaXMuZGlhbG9nVmlzaWJsZSA9IHZhbA0KICAgICAgICBpZiAodmFsKSB7DQogICAgICAgICAgLy8g5b2T5a+56K+d5qGG5pi+56S65pe277yM5aSN5Yi25Lyg5YWl55qE5pWw5o2uDQogICAgICAgICAgdGhpcy5mb3JtRGF0YSA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5mb3JtKSkNCiAgICAgICAgICB0aGlzLmludm9pY2VJdGVtTGlzdCA9IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodGhpcy5pbnZvaWNlSXRlbXMpKQ0KDQogICAgICAgICAgLy8g56Gu5L+d5Y+R56Wo6ZmE5Lu25a2X5q615a2Y5ZyoDQogICAgICAgICAgaWYgKCF0aGlzLmZvcm1EYXRhLmludm9pY2VBdHRhY2htZW50KSB7DQogICAgICAgICAgICB0aGlzLmZvcm1EYXRhLmludm9pY2VBdHRhY2htZW50ID0gIiINCiAgICAgICAgICB9DQoNCiAgICAgICAgICAvLyDlpoLmnpzmiYDlsZ7lhazlj7jlt7LmnInlgLzvvIzoh6rliqjloavlhYXnm7jlhbPkv6Hmga8NCiAgICAgICAgICBpZiAodGhpcy5mb3JtRGF0YS5pbnZvaWNlQmVsb25nc1RvKSB7DQogICAgICAgICAgICB0aGlzLmF1dG9GaWxsQ29tcGFueUluZm8odGhpcy5mb3JtRGF0YS5pbnZvaWNlQmVsb25nc1RvKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSwNCiAgICAgIGltbWVkaWF0ZTogdHJ1ZQ0KICAgIH0sDQogICAgLy8g55uR5ZCs5a+55pa55YWs5Y+4SUTlj5jljJYNCiAgICAiZm9ybURhdGEuY29vcGVyYXRvcklkIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwsIG9sZFZhbCkgew0KICAgICAgICAvLyDlj6rmnInlvZPlgLznnJ/mraPlj5jljJbml7bmiY3op6blj5Hmn6Xor6INCiAgICAgICAgaWYgKG5ld1ZhbCAmJiBuZXdWYWwgIT09IG9sZFZhbCkgew0KICAgICAgICAgIHRoaXMuZGVib3VuY2VkRmV0Y2hDb21wYW55SW5mbyhuZXdWYWwpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOebkeWQrOaJgOWxnuWFrOWPuOWPmOWMlu+8jOiHquWKqOWhq+WFheaIkeWPuOWPkeelqOaKrOWktOWSjOeojuWPtw0KICAgICJmb3JtRGF0YS5pbnZvaWNlQmVsb25nc1RvIjogew0KICAgICAgaGFuZGxlcihuZXdWYWwpIHsNCiAgICAgICAgaWYgKG5ld1ZhbCkgew0KICAgICAgICAgIHRoaXMuYXV0b0ZpbGxDb21wYW55SW5mbyhuZXdWYWwpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDlvIDnpajpobnnm67mlbDmja7moIflh4bljJblh73mlbANCiAgICBpbnZvaWNpbmdJdGVtTm9ybWFsaXplcihub2RlKSB7DQogICAgICBjb25zdCBub3JtYWxpemVkID0gew0KICAgICAgICBpZDogbm9kZS5pbnZvaWNpbmdJdGVtTmFtZSwgLy8g5L2/55SoaW52b2ljaW5nSXRlbU5hbWXkvZzkuLppZA0KICAgICAgICBsYWJlbDogbm9kZS5pbnZvaWNpbmdJdGVtTmFtZSwNCiAgICAgICAgaW52b2ljaW5nSXRlbU5hbWU6IG5vZGUuaW52b2ljaW5nSXRlbU5hbWUNCiAgICAgIH0NCg0KICAgICAgaWYgKG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgIG5vcm1hbGl6ZWQuY2hpbGRyZW4gPSBub2RlLmNoaWxkcmVuLm1hcChjaGlsZCA9PiB0aGlzLmludm9pY2luZ0l0ZW1Ob3JtYWxpemVyKGNoaWxkKSkNCiAgICAgIH0NCg0KICAgICAgcmV0dXJuIG5vcm1hbGl6ZWQNCiAgICB9LA0KICAgIC8vIOaPkOS6pOihqOWNlQ0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICB0aGlzLiRlbWl0KCJzdWJtaXQiLCB0aGlzLmZvcm1EYXRhKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgaGFuZGxlQ2FuY2VsKCkgew0KICAgICAgdGhpcy4kZW1pdCgiY2FuY2VsIikNCiAgICAgIHRoaXMuaGFuZGxlQ2xvc2UoKQ0KICAgIH0sDQogICAgLy8g5YWz6Zet5a+56K+d5qGGDQogICAgaGFuZGxlQ2xvc2UoKSB7DQogICAgICB0aGlzLiRlbWl0KCJ1cGRhdGU6dmlzaWJsZSIsIGZhbHNlKQ0KICAgIH0sDQogICAgc2VhcmNoQXZhaWxhYmxlSW52b2ljZSgpIHsNCiAgICAgIGNvbnNvbGUubG9nKCLmo4DntKLlj6/nlKjlj5HnpagiKQ0KICAgIH0sDQoNCiAgICAvLyDojrflj5blhazlj7jpk7booYzotKbmiLfliJfooagNCiAgICBmZXRjaENvbXBhbnlCYW5rQWNjb3VudHMoKSB7DQogICAgICAvLyDmo4Dmn6XmmK/lkKbmnInpgInmi6nlr7nmlrnlhazlj7gNCiAgICAgIGlmICghdGhpcy5mb3JtRGF0YS5jb29wZXJhdG9ySWQpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjpgInmi6nlr7nmlrnlhazlj7giKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g6LCD55SoQVBJ6I635Y+W6K+l5YWs5Y+455qE6ZO26KGM6LSm5oi3DQogICAgICBsaXN0QWNjb3VudCh7YmVsb25nVG9Db21wYW55OiB0aGlzLmZvcm1EYXRhLmNvb3BlcmF0b3JJZH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7DQogICAgICAgICAgdGhpcy5jb21wYW55QmFua0xpc3QgPSByZXNwb25zZS5yb3dzIHx8IFtdDQogICAgICAgICAgLy8g5aaC5p6c5rKh5pyJ6LSm5oi377yM5pi+56S65o+Q56S6DQogICAgICAgICAgaWYgKHRoaXMuY29tcGFueUJhbmtMaXN0Lmxlbmd0aCA9PT0gMCkgew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5pbmZvKCLor6Xlhazlj7jmsqHmnInpk7booYzotKbmiLforrDlvZUiKQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCLojrflj5blhazlj7jpk7booYzotKbmiLflpLHotKUiLCBlcnJvcikNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6I635Y+W5YWs5Y+46ZO26KGM6LSm5oi35aSx6LSlIikNCiAgICAgIH0pDQogICAgfSwNCg0KICAgIC8vIOWkhOeQhumTtuihjOi0puaIt+mAieaLqeWPmOWMlg0KICAgIGhhbmRsZUJhbmtBY2NvdW50Q2hhbmdlKGJhbmtDb2RlKSB7DQogICAgICAvLyDmoLnmja7pgInmi6nnmoRiYW5rQ29kZeaJvuWIsOWvueW6lOeahOmTtuihjOi0puaIt+S/oeaBrw0KICAgICAgY29uc3Qgc2VsZWN0ZWRBY2NvdW50ID0gdGhpcy5hdmFpbGFibGVCYW5rTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5iYW5rQ29kZSA9PT0gYmFua0NvZGUpDQoNCiAgICAgIGlmIChzZWxlY3RlZEFjY291bnQpIHsNCiAgICAgICAgLy8g6Ieq5Yqo5aGr5YWF6ZO26KGM5YWo56ew5ZKM6ZO26KGM6LSm5Y+3DQogICAgICAgIHRoaXMuZm9ybURhdGEuY29vcGVyYXRvckJhbmtGdWxsbmFtZSA9IHNlbGVjdGVkQWNjb3VudC5iYW5rTmFtZSB8fCAiIg0KICAgICAgICB0aGlzLmZvcm1EYXRhLmNvb3BlcmF0b3JCYW5rQWNjb3VudCA9IHNlbGVjdGVkQWNjb3VudC5iYW5rQWNjb3VudCB8fCAiIg0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g6I635Y+W5YWs5Y+45L+h5oGvDQogICAgZmV0Y2hDb21wYW55SW5mbyhjb21wYW55SWQpIHsNCiAgICAgIGdldENvbXBhbnkoY29tcGFueUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIGNvbnN0IGNvbXBhbnlJbmZvID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgIC8vIOabtOaWsOihqOWNleS4reS4juWvueaWueWFrOWPuOebuOWFs+eahOWtl+autQ0KICAgICAgICAgIHRoaXMuZm9ybURhdGEuY29vcGVyYXRvckNvbXBhbnlUaXRsZSA9IGNvbXBhbnlJbmZvLmNvbXBhbnlMb2NhbE5hbWUgfHwgIiINCiAgICAgICAgICB0aGlzLmZvcm1EYXRhLmNvb3BlcmF0b3JWYXRTZXJpYWxObyA9IGNvbXBhbnlJbmZvLnRheE5vIHx8ICIiDQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcigi6I635Y+W5YWs5Y+45L+h5oGv5aSx6LSlIiwgZXJyb3IpDQogICAgICB9KQ0KICAgIH0sDQoNCiAgICAvLyDoh6rliqjloavlhYXmiJHlj7jlj5HnpajmiqzlpLTlkoznqI7lj7cNCiAgICBhdXRvRmlsbENvbXBhbnlJbmZvKGNvbXBhbnlDb2RlKSB7DQogICAgICAvLyDmoLnmja7miYDlsZ7lhazlj7jku6PnoIEoR1pSUy9IS1JTL1NaUlMvR1pDRinoh6rliqjloavlhYXmiJHlj7jlj5HnpajmiqzlpLTlkoznqI7lj7cNCiAgICAgIGNvbnN0IGNvbXBhbnlJbmZvTWFwID0gew0KICAgICAgICAiR1pSUyI6IHsNCiAgICAgICAgICB0aXRsZTogIuW5v+W3nueRnuaXl+WbvemZhei0p+i/kOS7o+eQhuaciemZkOWFrOWPuCIsDQogICAgICAgICAgdGF4Tm86ICI5MTQ0MDEwMU1BNTlVUVhYN0IiDQogICAgICAgIH0sDQogICAgICAgICJIS1JTIjogew0KICAgICAgICAgIHRpdGxlOiAi6aaZ5riv55Ge5peX5Zu96ZmF6LSn6L+Q5Luj55CG5pyJ6ZmQ5YWs5Y+4IiwNCiAgICAgICAgICB0YXhObzogIkhLMTIzNDU2NzgiDQogICAgICAgIH0sDQogICAgICAgICJTWlJTIjogew0KICAgICAgICAgIHRpdGxlOiAi5rex5Zyz5biC55Ge5peX5Zu96ZmF6LSn6L+Q5Luj55CG5pyJ6ZmQ5YWs5Y+4IiwNCiAgICAgICAgICB0YXhObzogIjkxNDQwMzAwTUE1RzlVQjU3USINCiAgICAgICAgfSwNCiAgICAgICAgIkdaQ0YiOiB7DQogICAgICAgICAgdGl0bGU6ICLlub/lt57mraPms73lm73pmYXotKfov5Dku6PnkIbmnInpmZDlhazlj7giLA0KICAgICAgICAgIHRheE5vOiAiOTE0NDAxMDFNQTlYUkdMSDBGIg0KICAgICAgICB9DQogICAgICB9DQoNCiAgICAgIC8vIOiOt+WPluWvueW6lOWFrOWPuOeahOS/oeaBrw0KICAgICAgY29uc3QgY29tcGFueUluZm8gPSBjb21wYW55SW5mb01hcFtjb21wYW55Q29kZV0NCg0KICAgICAgLy8g5aaC5p6c5om+5Yiw5a+55bqU55qE5YWs5Y+45L+h5oGv77yM5YiZ5aGr5YWF6KGo5Y2VDQogICAgICBpZiAoY29tcGFueUluZm8pIHsNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5yaWNoQ29tcGFueVRpdGxlID0gY29tcGFueUluZm8udGl0bGUNCiAgICAgICAgdGhpcy5mb3JtRGF0YS5yaWNoVmF0U2VyaWFsTm8gPSBjb21wYW55SW5mby50YXhObw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliKTmlq3ooajljZXpobnmmK/lkKbnpoHnlKgNCiAgICBpc0Rpc2FibGVkKCkgew0KICAgICAgLy8g5qC55o2u5Lul5LiL5p2h5Lu25Yik5pat6KGo5Y2V5piv5ZCm5bqU6K+l56aB55So77yaDQogICAgICAvLyAxLiDlpoLmnpzlj5HnpajnirbmgIHkuLrlt7LlvIDnpagNCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLmludm9pY2VTdGF0dXMgPT09ICIxIikgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KDQogICAgICAvLyAyLiDlpoLmnpzmiqXnqI7lt7LplIHlrpoNCiAgICAgIGlmICh0aGlzLmZvcm1EYXRhLnRheExvY2tlZCA9PT0gIjEiKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQoNCiAgICAgIC8vIDMuIOWmguaenOW3suaUr+S7mA0KICAgICAgaWYgKHRoaXMuZm9ybURhdGEuYWN0dWFsUGF5RGF0ZSkgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KDQogICAgICAvLyDlpoLmnpzmsqHmnInnpoHnlKjmnaHku7bvvIzliJnooajljZXlj6/nvJbovpENCiAgICAgIHJldHVybiBmYWxzZQ0KICAgIH0sDQogICAgaGFuZGxlUmljaEJhbmtDb2RlQ2hhbmdlKHJvdykgew0KICAgICAgdGhpcy5mb3JtRGF0YS5yaWNoQmFua0Z1bGxuYW1lID0gcm93LmJhbmtOYW1lDQogICAgICB0aGlzLmZvcm1EYXRhLnJpY2hCYW5rQWNjb3VudCA9IHJvdy5iYW5rQWNjb3VudA0KICAgIH0sDQogICAgLy8g6I635Y+W5Y+R56Wo54q25oCB57G75Z6LDQogICAgZ2V0SW52b2ljZVN0YXR1c1R5cGUoc3RhdHVzKSB7DQogICAgICBjb25zdCBzdGF0dXNNYXAgPSB7DQogICAgICAgICJ1bmlzc3VlZCI6ICJpbmZvIiwNCiAgICAgICAgImlzc3VlZCI6ICJzdWNjZXNzIiwNCiAgICAgICAgImFwcGxpZWQiOiAid2FybmluZyIsDQogICAgICAgICJjYW5jZWxlZCI6ICJkYW5nZXIiDQogICAgICB9DQogICAgICByZXR1cm4gc3RhdHVzTWFwW3N0YXR1c10gfHwgImluZm8iDQogICAgfSwNCiAgICAvLyDojrflj5blj5HnpajnirbmgIHmlofmnKwNCiAgICBnZXRJbnZvaWNlU3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIGNvbnN0IHN0YXR1c01hcCA9IHsNCiAgICAgICAgInVuaXNzdWVkIjogIuacquW8gOelqCIsDQogICAgICAgICJpc3N1ZWQiOiAi5bey5byA56WoIiwNCiAgICAgICAgImFwcGxpZWQiOiAi5bey55Sz6K+3IiwNCiAgICAgICAgImNhbmNlbGVkIjogIuW3suS9nOW6nyINCiAgICAgIH0NCiAgICAgIHJldHVybiBzdGF0dXNNYXBbc3RhdHVzXSB8fCAi5pyq55+lIg0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["VatinvoiceDialog.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4cA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "VatinvoiceDialog.vue", "sourceRoot": "src/views/system/vatinvoice/components", "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-dialogDrag\r\n    v-dialogDragWidth\r\n    :close-on-click-modal=\"false\"\r\n    :modal-append-to-body=\"false\"\r\n    :title=\"title\"\r\n    :visible.sync=\"dialogVisible\"\r\n    append-to-body\r\n    width=\"80%\"\r\n    @close=\"handleClose\"\r\n  >\r\n    <el-form ref=\"form\" :model=\"formData\" :rules=\"rules\" class=\"edit\" label-width=\"80px\" size=\"mini\">\r\n      <!-- 第一行 - 基本信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票流水号\">\r\n            <el-input v-model=\"formData.invoiceCodeNo\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票流水号\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"进销标志\">\r\n            <el-select v-model=\"formData.saleBuy\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"进销标志\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"销项\" value=\"sale\"/>\r\n              <el-option label=\"进项\" value=\"buy\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票性质\">\r\n            <!-- 主营业务收入/非主营收入/营业外收入/成本/费用 -->\r\n            <el-select v-model=\"formData.taxClass\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票性质\"\r\n                       style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"主营业务收入\" value=\"主营业务收入\"/>\r\n              <el-option label=\"非主营业务收入\" value=\"非主营业务收入\"/>\r\n              <el-option label=\"营业外收入\" value=\"营业外收入\"/>\r\n              <el-option label=\"成本\" value=\"成本\"/>\r\n              <el-option label=\"费用\" value=\"费用\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票类型\">\r\n            <el-select v-model=\"formData.invoiceType\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                       placeholder=\"发票类型\" style=\"width: 100%\"\r\n            >\r\n              <el-option label=\"增值税专用发票\" value=\"增值税专用发票\"/>\r\n              <el-option label=\"普通发票\" value=\"普通发票\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"合并发票\">\r\n            <el-row>\r\n              <el-col :span=\"8\">\r\n                <el-checkbox v-model=\"formData.mergeInvoice\" :class=\"{'disable-form': isDisabled()}\"\r\n                             :disabled=\"isDisabled()\" false-label=\"0\"\r\n                             true-label=\"1\"\r\n                >√\r\n                </el-checkbox>\r\n              </el-col>\r\n              <el-col :span=\"16\">\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"searchAvailableInvoice\">检索可用发票</el-button>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票号码\">\r\n            <el-input v-model=\"formData.invoiceOfficalNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                      :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票号码\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第二行 - 公司和账户信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"所属公司\">\r\n                <el-input v-model=\"formData.invoiceBelongsTo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"所属公司\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"我司账户\">\r\n                <tree-select :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"formData.richBankCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\" @return=\"formData.richBankCode=$event\"\r\n                             :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                             @returnData=\"handleRichBankCodeChange\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方公司\">\r\n                <company-select :load-options=\"companyList\"\r\n                                :multiple=\"false\" :no-parent=\"true\"\r\n                                :pass=\"formData.cooperatorId\" :placeholder=\"''\"\r\n                                @return=\"formData.cooperatorId=$event\"\r\n                                :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"对方账户\">\r\n                <el-select v-model=\"formData.cooperatorBankCode\" :class=\"{'disable-form': isDisabled()}\"\r\n                           :disabled=\"isDisabled()\"\r\n                           placeholder=\"对方账户\" style=\"width: 100%\"\r\n                           @change=\"handleBankAccountChange\" @click.native=\"fetchCompanyBankAccounts\"\r\n                >\r\n                  <el-option v-for=\"item in availableBankList\" :key=\"item.bankAccId\"\r\n                             :label=\"item.bankAccCode+ '('+item.bankAccount+')'\" :value=\"item.bankAccCode\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.richCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"发票抬头\">\r\n                <el-input v-model=\"formData.cooperatorCompanyTitle\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方发票抬头\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"8\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.officalChargeNameSummary\" :minrows=\"3\" :rows=\"2\"\r\n                        placeholder=\"发票项目汇总\" type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"formData.relatedOrderNo\" :minrows=\"3\" :rows=\"2\" placeholder=\"相关订单号\"\r\n                        type=\"textarea\"\r\n                        :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n              />\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 第四行 - 税号信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"16\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.richVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"税号\">\r\n                <el-input v-model=\"formData.cooperatorVatSerialNo\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\" placeholder=\"对方纳税人识别号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.richBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行全称\">\r\n                <el-input v-model=\"formData.cooperatorBankFullname\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方银行全称\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.richBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"我司账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-form-item label=\"银行账号\">\r\n                <el-input v-model=\"formData.cooperatorBankAccount\" :class=\"{'disable-form': isDisabled()}\"\r\n                          :disabled=\"isDisabled()\"\r\n                          placeholder=\"对方账号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-input v-model=\"formData.invoiceRemark\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                    :minrows=\"4\" :rows=\"4\"\r\n                    placeholder=\"备注\" type=\"textarea\"\r\n          />\r\n        </el-col>\r\n\r\n        <el-col :span=\"4\">\r\n          <el-col>\r\n            <el-form-item label=\"期望支付日\">\r\n              <el-date-picker v-model=\"formData.expectedPayDate\"\r\n                              clearable\r\n                              placeholder=\"期望支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"批复支付日\">\r\n              <el-date-picker v-model=\"formData.approvedPayDate\"\r\n                              clearable\r\n                              placeholder=\"批复支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col>\r\n            <el-form-item label=\"实际支付日\">\r\n              <el-date-picker v-model=\"formData.actualPayDate\"\r\n                              clearable\r\n                              placeholder=\"实际支付日期\"\r\n                              style=\"width: 100%;\"\r\n                              type=\"date\"\r\n                              value-format=\"yyyy-MM-dd\"\r\n                              :class=\"{'disable-form': isDisabled()}\"\r\n                              :disabled=\"isDisabled()\"\r\n              >\r\n              </el-date-picker>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-col>\r\n\r\n      </el-row>\r\n\r\n      <!-- 第七行 - 发票信息 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票汇率\">\r\n            <el-input v-model=\"formData.invoiceExchangeRate\" :class=\"{'disable-form': isDisabled()}\"\r\n                      :disabled=\"isDisabled()\"\r\n                      placeholder=\"发票汇率\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票币种\">\r\n            <tree-select :pass=\"formData.invoiceCurrencyCode\"\r\n                         :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                         style=\"width: 100%\" @return=\"formData.invoiceCurrencyCode=$event\"\r\n                         :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"8\">\r\n          <el-form-item label=\"发票金额\">\r\n            <el-input v-model=\"formData.invoiceNetAmount\" :class=\"{'disable-form': isDisabled()}\"\r\n                      :disabled=\"isDisabled()\"\r\n                      placeholder=\"金额计算公式\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"发票状态\">\r\n            <el-row>\r\n              <el-col :offset=\"1\" :span=\"8\">\r\n                <el-tag :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                        :type=\"getInvoiceStatusType(formData.invoiceStatus)\"\r\n                >{{ getInvoiceStatusText(formData.invoiceStatus) }}\r\n                </el-tag>\r\n              </el-col>\r\n              <el-col :span=\"10\">\r\n                <el-popover\r\n                  placement=\"top-start\"\r\n                  title=\"发票附件管理\"\r\n                  trigger=\"hover\"\r\n                  width=\"300\"\r\n                >\r\n                  <div>\r\n                    <file-upload\r\n                      :class=\"isDisabled()?'disable-form':''\"\r\n                      :file-type=\"['pdf']\"\r\n                      :is-disabled=\"isDisabled()\"\r\n                      :is-tip-flex=\"true\"\r\n                      :value=\"formData.invoiceAttachment\"\r\n                      @input=\"formData.invoiceAttachment=$event\"\r\n                    />\r\n                  </div>\r\n                  <template #reference>\r\n                    <el-button size=\"mini\" type=\"primary\">\r\n                      <i class=\"el-icon-upload\"></i>\r\n                      发票附件\r\n                      <i v-if=\"formData.invoiceAttachment && formData.invoiceAttachment.trim()\" class=\"el-icon-check\"\r\n                         style=\"color: #67C23A; margin-left: 5px;\"></i>\r\n                    </el-button>\r\n                  </template>\r\n                </el-popover>\r\n              </el-col>\r\n            </el-row>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"4\">\r\n          <el-form-item label=\"报税月份\">\r\n            <el-input v-model=\"formData.belongsToMonth\" :class=\"{'disable-form': isDisabled()}\" :disabled=\"isDisabled()\"\r\n                      class=\"yellow-bg\" placeholder=\"2025/7/31\"\r\n            />\r\n          </el-form-item>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <el-divider></el-divider>\r\n\r\n      <!-- 发票明细表格 -->\r\n      <el-row :gutter=\"10\">\r\n        <el-col :span=\"24\">\r\n          <el-table :data=\"formData.rsCharges\" border size=\"mini\" style=\"width: 100%\">\r\n            <el-table-column align=\"center\" type=\"selection\" width=\"35\"/>\r\n            <el-table-column align=\"center\" label=\"账单编号\" prop=\"debitNoteId\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"RCT号\" prop=\"sqdRctNo\"/>\r\n            <el-table-column align=\"center\" label=\"所属服务\" prop=\"serviceLocalName\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                {{ scope.row.sqdServiceTypeId == 0 ? \"客户应收\" : scope.row.serviceLocalName }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"费用名称\" prop=\"chargeName\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"备注\" prop=\"chargeRemark\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"收付标志\" prop=\"isReceivingOrPaying\" width=\"80\">\r\n              <template #default=\"scope\">{{ scope.row.isReceivingOrPaying == 0 ? \"应收\" : \"应付\" }}</template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"报价币种\" prop=\"quoteCurrency\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单价\" prop=\"dnUnitRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"数量\" prop=\"dnAmount\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"单位\" prop=\"dnUnitCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算汇率\" prop=\"basicCurrencyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"结算币种\" prop=\"dnCurrencyCode\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"税率\" prop=\"dutyRate\" width=\"80\"/>\r\n            <el-table-column align=\"center\" label=\"含税小计\" prop=\"subtotal\" width=\"100\"/>\r\n            <el-table-column align=\"center\" label=\"开票项目名称\" prop=\"invoicingItem\" width=\"150\">\r\n              <template #default=\"scope\">\r\n                <treeselect v-model=\"scope.row.invoicingItem\"\r\n                            :class=\"{'disable-form': isDisabled()}\"\r\n                            :default-expand-level=\"1\"\r\n                            :disable-branch-nodes=\"true\"\r\n                            :disabled=\"isDisabled()\"\r\n                            :normalizer=\"invoicingItemNormalizer\"\r\n                            :options=\"invoicingItemOptions\"\r\n                            :show-count=\"true\"\r\n                            :z-index=\"9999\"\r\n                            append-to-body\r\n                            placeholder=\"开票项目名称\"\r\n                            searchable\r\n                >\r\n                  <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                    {{ node.raw.invoicingItemName || node.label }}\r\n                  </div>\r\n                  <label slot=\"option-label\"\r\n                         slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                         :class=\"labelClassName\">\r\n                    {{ node.label }}\r\n                    <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n                  </label>\r\n                </treeselect>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column align=\"center\" label=\"税收编码\" prop=\"taxCode\" width=\"100\"/>\r\n          </el-table>\r\n        </el-col>\r\n      </el-row>\r\n\r\n      <!-- 操作按钮组 -->\r\n      <el-row :gutter=\"10\" style=\"margin-top: 10px;\">\r\n        <el-col :span=\"3\">\r\n          <el-button icon=\"el-icon-check\" size=\"mini\" type=\"primary\">√默认对冲</el-button>\r\n          <div>已选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">智选</el-button>\r\n          <div>未选总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">反选</el-button>\r\n          <div>全部总计:</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">申请开票</el-button>\r\n          <div>申请人+时间</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"primary\">信息审核</el-button>\r\n          <div>确认人+时间</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"success\">发送开票</el-button>\r\n          <div>开票人+时间</div>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"warning\">打印</el-button>\r\n        </el-col>\r\n        <el-col :span=\"3\">\r\n          <el-button size=\"mini\" type=\"info\">报税锁定</el-button>\r\n          <div>报税人+时间</div>\r\n        </el-col>\r\n      </el-row>\r\n    </el-form>\r\n\r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">{{ formData.invoiceId ? \"更 新\" : \"确 定\" }}</el-button>\r\n        <el-button @click=\"handleCancel\">取 消</el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {getCompany} from \"@/api/system/company\"\r\nimport {listAccount} from \"@/api/system/account\"\r\nimport FileUpload from \"@/components/FileUpload\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\n\r\n// 防抖函数\r\nfunction debounce(fn, delay) {\r\n  let timer = null\r\n  return function () {\r\n    const context = this\r\n    const args = arguments\r\n    clearTimeout(timer)\r\n    timer = setTimeout(function () {\r\n      fn.apply(context, args)\r\n    }, delay)\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: \"VatinvoiceDialog\",\r\n  components: {\r\n    FileUpload,\r\n    Treeselect\r\n  },\r\n  props: {\r\n    // 是否显示对话框\r\n    visible: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 标题\r\n    title: {\r\n      type: String,\r\n      default: \"\"\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 表单验证规则\r\n    rules: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 发票明细列表\r\n    invoiceItems: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 银行账户列表\r\n    bankAccountList: {\r\n      type: Array,\r\n      default: () => []\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      // 内部对话框可见性状态\r\n      dialogVisible: false,\r\n      // 表单数据的副本\r\n      formData: {},\r\n      // 发票明细列表的副本\r\n      invoiceItemList: [],\r\n      // 防抖后的获取公司信息方法\r\n      debouncedFetchCompanyInfo: null,\r\n      // 公司银行账户列表\r\n      companyBankList: [],\r\n      // 开票项目选项数据\r\n      invoicingItemOptions: [\r\n        {\r\n          id: \"1\",\r\n          invoicingItemName: \"经纪代理服务\",\r\n          children: [\r\n            {id: \"1-1\", invoicingItemName: \"代理运费\"},\r\n            {id: \"1-2\", invoicingItemName: \"国际货物运输代理服务费\"},\r\n            {id: \"1-3\", invoicingItemName: \"代理港杂费\"},\r\n            {id: \"1-4\", invoicingItemName: \"国际货物运输代理服务\"},\r\n            {id: \"1-5\", invoicingItemName: \"代理报关服务费\"},\r\n            {id: \"1-6\", invoicingItemName: \"代理服务费\"},\r\n            {id: \"1-7\", invoicingItemName: \"代理报关费\"},\r\n            {id: \"1-8\", invoicingItemName: \"代理拖车费\"},\r\n            {id: \"1-9\", invoicingItemName: \"国际货物运输代理服务-代理运费\"},\r\n            {id: \"1-10\", invoicingItemName: \"代理国内运费\"},\r\n            {id: \"1-11\", invoicingItemName: \"国际货物运输代理海运费\"},\r\n            {id: \"1-12\", invoicingItemName: \"代理运费费\"},\r\n            {id: \"1-13\", invoicingItemName: \"运输代理费\"},\r\n            {id: \"1-14\", invoicingItemName: \"货物运输代理服务费\"},\r\n            {id: \"1-15\", invoicingItemName: \"国际货物运输代理港杂费\"},\r\n            {id: \"1-16\", invoicingItemName: \"国际货物运输代理运费\"},\r\n            {id: \"1-17\", invoicingItemName: \"货物运输代理费\"},\r\n            {id: \"1-18\", invoicingItemName: \"国际货物运输代理费\"},\r\n            {id: \"1-19\", invoicingItemName: \"代理杂费\"},\r\n            {id: \"1-20\", invoicingItemName: \"代理文件费\"},\r\n            {id: \"1-21\", invoicingItemName: \"代理设备交接单费用\"},\r\n            {id: \"1-22\", invoicingItemName: \"代理舱单申报费\"},\r\n            {id: \"1-23\", invoicingItemName: \"代理操作费\"},\r\n            {id: \"1-24\", invoicingItemName: \"代理封条费\"},\r\n            {id: \"1-25\", invoicingItemName: \"代理码头操作费\"},\r\n            {id: \"1-26\", invoicingItemName: \"代理电放费\"},\r\n            {id: \"1-27\", invoicingItemName: \"代理核重费\"}\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用的银行账户列表：优先使用companyBankList，为空时使用传入的bankAccountList\r\n    availableBankList() {\r\n      return this.companyBankList.length > 0 ? this.companyBankList : this.bankAccountList\r\n    }\r\n  },\r\n\r\n  created() {\r\n    // 创建防抖版本的fetchCompanyInfo方法，设置300ms延迟\r\n    this.debouncedFetchCompanyInfo = debounce(this.fetchCompanyInfo, 300)\r\n  },\r\n  watch: {\r\n    visible: {\r\n      handler(val) {\r\n        this.dialogVisible = val\r\n        if (val) {\r\n          // 当对话框显示时，复制传入的数据\r\n          this.formData = JSON.parse(JSON.stringify(this.form))\r\n          this.invoiceItemList = JSON.parse(JSON.stringify(this.invoiceItems))\r\n\r\n          // 确保发票附件字段存在\r\n          if (!this.formData.invoiceAttachment) {\r\n            this.formData.invoiceAttachment = \"\"\r\n          }\r\n\r\n          // 如果所属公司已有值，自动填充相关信息\r\n          if (this.formData.invoiceBelongsTo) {\r\n            this.autoFillCompanyInfo(this.formData.invoiceBelongsTo)\r\n          }\r\n        }\r\n      },\r\n      immediate: true\r\n    },\r\n    // 监听对方公司ID变化\r\n    \"formData.cooperatorId\": {\r\n      handler(newVal, oldVal) {\r\n        // 只有当值真正变化时才触发查询\r\n        if (newVal && newVal !== oldVal) {\r\n          this.debouncedFetchCompanyInfo(newVal)\r\n        }\r\n      }\r\n    },\r\n    // 监听所属公司变化，自动填充我司发票抬头和税号\r\n    \"formData.invoiceBelongsTo\": {\r\n      handler(newVal) {\r\n        if (newVal) {\r\n          this.autoFillCompanyInfo(newVal)\r\n        }\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    // 开票项目数据标准化函数\r\n    invoicingItemNormalizer(node) {\r\n      const normalized = {\r\n        id: node.invoicingItemName, // 使用invoicingItemName作为id\r\n        label: node.invoicingItemName,\r\n        invoicingItemName: node.invoicingItemName\r\n      }\r\n\r\n      if (node.children && node.children.length > 0) {\r\n        normalized.children = node.children.map(child => this.invoicingItemNormalizer(child))\r\n      }\r\n\r\n      return normalized\r\n    },\r\n    // 提交表单\r\n    submitForm() {\r\n      this.$refs.form.validate(valid => {\r\n        if (valid) {\r\n          this.$emit(\"submit\", this.formData)\r\n        }\r\n      })\r\n    },\r\n    // 取消按钮\r\n    handleCancel() {\r\n      this.$emit(\"cancel\")\r\n      this.handleClose()\r\n    },\r\n    // 关闭对话框\r\n    handleClose() {\r\n      this.$emit(\"update:visible\", false)\r\n    },\r\n    searchAvailableInvoice() {\r\n      console.log(\"检索可用发票\")\r\n    },\r\n\r\n    // 获取公司银行账户列表\r\n    fetchCompanyBankAccounts() {\r\n      // 检查是否有选择对方公司\r\n      if (!this.formData.cooperatorId) {\r\n        this.$message.warning(\"请先选择对方公司\")\r\n        return\r\n      }\r\n\r\n      // 调用API获取该公司的银行账户\r\n      listAccount({belongToCompany: this.formData.cooperatorId}).then(response => {\r\n        if (response.code === 200) {\r\n          this.companyBankList = response.rows || []\r\n          // 如果没有账户，显示提示\r\n          if (this.companyBankList.length === 0) {\r\n            this.$message.info(\"该公司没有银行账户记录\")\r\n          }\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司银行账户失败\", error)\r\n        this.$message.error(\"获取公司银行账户失败\")\r\n      })\r\n    },\r\n\r\n    // 处理银行账户选择变化\r\n    handleBankAccountChange(bankCode) {\r\n      // 根据选择的bankCode找到对应的银行账户信息\r\n      const selectedAccount = this.availableBankList.find(item => item.bankCode === bankCode)\r\n\r\n      if (selectedAccount) {\r\n        // 自动填充银行全称和银行账号\r\n        this.formData.cooperatorBankFullname = selectedAccount.bankName || \"\"\r\n        this.formData.cooperatorBankAccount = selectedAccount.bankAccount || \"\"\r\n      }\r\n    },\r\n    // 获取公司信息\r\n    fetchCompanyInfo(companyId) {\r\n      getCompany(companyId).then(response => {\r\n        if (response.code === 200) {\r\n          const companyInfo = response.data\r\n          // 更新表单中与对方公司相关的字段\r\n          this.formData.cooperatorCompanyTitle = companyInfo.companyLocalName || \"\"\r\n          this.formData.cooperatorVatSerialNo = companyInfo.taxNo || \"\"\r\n        }\r\n      }).catch(error => {\r\n        console.error(\"获取公司信息失败\", error)\r\n      })\r\n    },\r\n\r\n    // 自动填充我司发票抬头和税号\r\n    autoFillCompanyInfo(companyCode) {\r\n      // 根据所属公司代码(GZRS/HKRS/SZRS/GZCF)自动填充我司发票抬头和税号\r\n      const companyInfoMap = {\r\n        \"GZRS\": {\r\n          title: \"广州瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440101MA59UQXX7B\"\r\n        },\r\n        \"HKRS\": {\r\n          title: \"香港瑞旗国际货运代理有限公司\",\r\n          taxNo: \"HK12345678\"\r\n        },\r\n        \"SZRS\": {\r\n          title: \"深圳市瑞旗国际货运代理有限公司\",\r\n          taxNo: \"91440300MA5G9UB57Q\"\r\n        },\r\n        \"GZCF\": {\r\n          title: \"广州正泽国际货运代理有限公司\",\r\n          taxNo: \"91440101MA9XRGLH0F\"\r\n        }\r\n      }\r\n\r\n      // 获取对应公司的信息\r\n      const companyInfo = companyInfoMap[companyCode]\r\n\r\n      // 如果找到对应的公司信息，则填充表单\r\n      if (companyInfo) {\r\n        this.formData.richCompanyTitle = companyInfo.title\r\n        this.formData.richVatSerialNo = companyInfo.taxNo\r\n      }\r\n    },\r\n\r\n    // 判断表单项是否禁用\r\n    isDisabled() {\r\n      // 根据以下条件判断表单是否应该禁用：\r\n      // 1. 如果发票状态为已开票\r\n      if (this.formData.invoiceStatus === \"1\") {\r\n        return true\r\n      }\r\n\r\n      // 2. 如果报税已锁定\r\n      if (this.formData.taxLocked === \"1\") {\r\n        return true\r\n      }\r\n\r\n      // 3. 如果已支付\r\n      if (this.formData.actualPayDate) {\r\n        return true\r\n      }\r\n\r\n      // 如果没有禁用条件，则表单可编辑\r\n      return false\r\n    },\r\n    handleRichBankCodeChange(row) {\r\n      this.formData.richBankFullname = row.bankName\r\n      this.formData.richBankAccount = row.bankAccount\r\n    },\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.yellow-bg {\r\n  background-color: #fffbe6;\r\n}\r\n\r\n.disable-form {\r\n  background-color: #f5f7fa;\r\n  border-color: #e4e7ed;\r\n  color: #c0c4cc;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.uploaded-file-preview {\r\n  margin-top: 10px;\r\n  padding: 10px;\r\n  background-color: #f8f9fa;\r\n  border-radius: 4px;\r\n  border: 1px solid #e9ecef;\r\n}\r\n\r\n.preview-title {\r\n  font-weight: bold;\r\n  color: #495057;\r\n  margin-bottom: 8px;\r\n  font-size: 14px;\r\n}\r\n\r\n.file-links {\r\n  display: flex;\r\n  gap: 10px;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.file-links .el-link {\r\n  font-size: 12px;\r\n}\r\n\r\n/* treeselect组件样式 */\r\n:deep(.vue-treeselect__menu) {\r\n  z-index: 9999 !important;\r\n  position: fixed !important;\r\n}\r\n\r\n:deep(.vue-treeselect__menu-container) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__dropdown) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n/* 确保下拉框在表格之上 */\r\n:deep(.vue-treeselect__menu-arrow) {\r\n  z-index: 9999 !important;\r\n}\r\n\r\n:deep(.vue-treeselect__list) {\r\n  z-index: 9999 !important;\r\n}\r\n</style>\r\n"]}]}