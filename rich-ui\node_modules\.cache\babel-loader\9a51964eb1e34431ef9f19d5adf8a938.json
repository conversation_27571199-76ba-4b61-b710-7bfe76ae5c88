{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\OrderDifficultySelect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\OrderDifficultySelect\\index.vue", "mtime": 1718100178791}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAnT3JkZXJEaWZmaWN1bHR5U2VsZWN0JywKICBwcm9wczogWydwYXNzJ10sCiAgd2F0Y2g6IHsKICAgIHBhc3M6IGZ1bmN0aW9uIHBhc3ModmFsKSB7CiAgICAgIHRoaXMudmFsdWUgPSB2YWw7CiAgICB9CiAgfSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgb3B0aW9uczogW3sKICAgICAgICB2YWx1ZTogJzAnLAogICAgICAgIGxhYmVsOiAn566A5piTJwogICAgICB9LCB7CiAgICAgICAgdmFsdWU6ICcxJywKICAgICAgICBsYWJlbDogJ+agh+WHhicKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAnMicsCiAgICAgICAgbGFiZWw6ICfpq5jnuqcnCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogJzMnLAogICAgICAgIGxhYmVsOiAn54m55YirJwogICAgICB9XSwKICAgICAgdmFsdWU6IHRoaXMucGFzcwogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7fSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZWxlY3Q6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdCgpIHsKICAgICAgdGhpcy4kZW1pdCgnZGlmZmljdWx0eScsIHRoaXMudmFsdWUpOwogICAgfQogIH0KfTsKLyoNCiogMCAgIOeugOaYkyAgIOatpemqpOS4jeWFqC/mk43kvZznroDkvr8NCjEgICDmoIflh4YgICDluLjop4TorqLljZUNCjIgICDpq5jnuqcgICDpnIDopoHkuIDkupvpmYTliqDmnI3liqHvvIzlpoLliLbkvZznrrHljZUvQlND562JDQozICAg54m55YirICAg6Z2e5bi46KeE55qE5LiA5Lqb6K6i5Y2V77yM54m556eN5p+cL+mhueebrueJqea1geetiQ0KKiAgKi8KZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "names": ["name", "props", "watch", "pass", "val", "value", "data", "options", "label", "computed", "methods", "handleSelect", "$emit", "exports", "default", "_default"], "sources": ["src/components/OrderDifficultySelect/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-select v-model=\"value\" class=\"selector\" filterable placeholder=\"订单难度\" @change=\"handleSelect\">\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n\r\n      >\r\n      </el-option>\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'OrderDifficultySelect',\r\n  props: ['pass'],\r\n  watch:{\r\n    pass:function(val){\r\n      this.value=val\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      options: [{\r\n        value: '0',\r\n        label: '简易'\r\n      }, {\r\n        value: '1',\r\n        label: '标准'\r\n      }, {\r\n        value: '2',\r\n        label: '高级'\r\n      }, {\r\n        value: '3',\r\n        label: '特别'\r\n      }],\r\n      value: this.pass\r\n    }\r\n  },\r\n  computed:{},\r\n  methods: {\r\n    handleSelect() {\r\n      this.$emit('difficulty', this.value)\r\n    }\r\n  }\r\n}\r\n/*\r\n* 0   简易   步骤不全/操作简便\r\n1   标准   常规订单\r\n2   高级   需要一些附加服务，如制作箱单/BSC等\r\n3   特别   非常规的一些订单，特种柜/项目物流等\r\n*  */\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n\r\n.selector {\r\n  width: 100%;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;eAgBA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAAC,KAAA,GAAAD,GAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;QACAF,KAAA;QACAG,KAAA;MACA;QACAH,KAAA;QACAG,KAAA;MACA;QACAH,KAAA;QACAG,KAAA;MACA;QACAH,KAAA;QACAG,KAAA;MACA;MACAH,KAAA,OAAAF;IACA;EACA;EACAM,QAAA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,oBAAAP,KAAA;IACA;EACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AALAQ,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}