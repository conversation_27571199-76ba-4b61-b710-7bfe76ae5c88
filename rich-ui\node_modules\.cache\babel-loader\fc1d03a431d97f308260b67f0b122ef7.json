{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\config\\rctSearchFields.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\config\\rctSearchFields.js", "mtime": 1744858990763}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["rctSearchFields", "rctNo", "label", "type", "placeholder", "ATDDate", "rctOpDate", "ETDDate", "client", "multiple", "noParent", "roleClient", "roleTypeId", "polIds", "en", "destinationPortIds", "salesId", "dataSource", "loadMethod", "valueField", "disableBranchNodes", "disabledFuzzyMatching", "flattenSearchResults", "showCount", "normalizer", "valueSlot", "optionSlot", "salesAssistantId", "verifyPsaId", "opId", "processStatusId", "treeType", "dLoad", "flat", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/config/rctSearchFields.js"], "sourcesContent": ["// 搜索字段配置\r\nexport const rctSearchFields = {\r\n  // 文本输入类型\r\n  rctNo: {\r\n    label: \"单号\",\r\n    type: \"input\",\r\n    placeholder: \"请输入单号\"\r\n  },\r\n  ATDDate: {\r\n    label: \"ATD\",\r\n    type: \"date\",\r\n    placeholder: \"请选择ATD日期\"\r\n  },\r\n  rctOpDate: {\r\n    label: \"日期\",\r\n    type: \"date\",\r\n    placeholder: \"请选择操作日期\"\r\n  },\r\n  ETDDate: {\r\n    label: \"ETD\",\r\n    type: \"date\",\r\n    placeholder: \"请选择ETD日期\"\r\n  },\r\n  client: {\r\n    label: \"客户\",\r\n    type: \"company\",\r\n    placeholder: \"请选择客户\",\r\n    multiple: false,    // 是否多选\r\n    noParent: true,    // 是否禁用父节点选择\r\n    roleClient: \"1\",   // 角色客户标识\r\n    roleTypeId: 1      // 角色类型ID\r\n  },\r\n  polIds: {\r\n    label: \"启运港\",\r\n    type: \"location\",\r\n    placeholder: \"请选择港口\",\r\n    multiple: true,\r\n    en: true\r\n  },\r\n  destinationPortIds: {\r\n    label: \"目的港\",\r\n    type: \"location\",\r\n    placeholder: \"请选择港口\",\r\n    multiple: true,\r\n    en: true\r\n  },\r\n  salesId: {\r\n    label: \"业务\",\r\n    type: \"treeselect\",\r\n    placeholder: \"选择业务员\",\r\n    dataSource: \"salesList\",\r\n    loadMethod: \"loadSales\",\r\n    valueField: \"staffId\",\r\n    disableBranchNodes: true,\r\n    disabledFuzzyMatching: true,\r\n    flattenSearchResults: true,\r\n    showCount: true,\r\n    normalizer: \"staffNormalizer\", // 需要在父组件中定义该方法\r\n    valueSlot: true,\r\n    optionSlot: true\r\n  },\r\n  salesAssistantId: {\r\n    label: \"助理\",\r\n    type: \"treeselect\",\r\n    placeholder: \"选择业务员\",\r\n    dataSource: \"salesList\",\r\n    loadMethod: \"loadSales\",\r\n    valueField: \"salesAssistantId\",\r\n    disableBranchNodes: true,\r\n    disabledFuzzyMatching: true,\r\n    flattenSearchResults: true,\r\n    showCount: true,\r\n    normalizer: \"staffNormalizer\", // 需要在父组件中定义该方法\r\n    valueSlot: true,\r\n    optionSlot: true\r\n  },\r\n  verifyPsaId: {\r\n    label: \"商务\",\r\n    type: \"treeselect\",\r\n    placeholder: \"选择业务员\",\r\n    dataSource: \"salesList\",\r\n    loadMethod: \"loadBusinesses\",\r\n    valueField: \"verifyPsaId\",\r\n    disableBranchNodes: true,\r\n    disabledFuzzyMatching: true,\r\n    flattenSearchResults: true,\r\n    showCount: true,\r\n    normalizer: \"staffNormalizer\", // 需要在父组件中定义该方法\r\n    valueSlot: true,\r\n    optionSlot: true\r\n  },\r\n  opId: {\r\n    label: \"操作\",\r\n    type: \"treeselect\",\r\n    placeholder: \"选择业务员\",\r\n    dataSource: \"salesList\",\r\n    loadMethod: \"loadOp\",\r\n    valueField: \"opId\",\r\n    disableBranchNodes: true,\r\n    disabledFuzzyMatching: true,\r\n    flattenSearchResults: true,\r\n    showCount: true,\r\n    normalizer: \"staffNormalizer\", // 需要在父组件中定义该方法\r\n    valueSlot: true,\r\n    optionSlot: true\r\n  },\r\n  processStatusId: {\r\n    type: \"tree-select\",\r\n    label: \"进度\",\r\n    placeholder: \"请选择进度状态\",\r\n    treeType: \"processStatus\",\r\n    dLoad: true,\r\n    flat: false,\r\n    multiple: false\r\n  }\r\n}\r\n"], "mappings": ";;;;;;AAAA;AACO,IAAMA,eAAe,GAAG;EAC7B;EACAC,KAAK,EAAE;IACLC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,OAAO;IACbC,WAAW,EAAE;EACf,CAAC;EACDC,OAAO,EAAE;IACPH,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE;EACf,CAAC;EACDE,SAAS,EAAE;IACTJ,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE;EACf,CAAC;EACDG,OAAO,EAAE;IACPL,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE;EACf,CAAC;EACDI,MAAM,EAAE;IACNN,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,SAAS;IACfC,WAAW,EAAE,OAAO;IACpBK,QAAQ,EAAE,KAAK;IAAK;IACpBC,QAAQ,EAAE,IAAI;IAAK;IACnBC,UAAU,EAAE,GAAG;IAAI;IACnBC,UAAU,EAAE,CAAC,CAAM;EACrB,CAAC;;EACDC,MAAM,EAAE;IACNX,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,OAAO;IACpBK,QAAQ,EAAE,IAAI;IACdK,EAAE,EAAE;EACN,CAAC;EACDC,kBAAkB,EAAE;IAClBb,KAAK,EAAE,KAAK;IACZC,IAAI,EAAE,UAAU;IAChBC,WAAW,EAAE,OAAO;IACpBK,QAAQ,EAAE,IAAI;IACdK,EAAE,EAAE;EACN,CAAC;EACDE,OAAO,EAAE;IACPd,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,OAAO;IACpBa,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,SAAS;IACrBC,kBAAkB,EAAE,IAAI;IACxBC,qBAAqB,EAAE,IAAI;IAC3BC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,iBAAiB;IAAE;IAC/BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE;EACd,CAAC;EACDC,gBAAgB,EAAE;IAChBzB,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,OAAO;IACpBa,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,kBAAkB;IAC9BC,kBAAkB,EAAE,IAAI;IACxBC,qBAAqB,EAAE,IAAI;IAC3BC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,iBAAiB;IAAE;IAC/BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE;EACd,CAAC;EACDE,WAAW,EAAE;IACX1B,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,OAAO;IACpBa,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,gBAAgB;IAC5BC,UAAU,EAAE,aAAa;IACzBC,kBAAkB,EAAE,IAAI;IACxBC,qBAAqB,EAAE,IAAI;IAC3BC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,iBAAiB;IAAE;IAC/BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE;EACd,CAAC;EACDG,IAAI,EAAE;IACJ3B,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,YAAY;IAClBC,WAAW,EAAE,OAAO;IACpBa,UAAU,EAAE,WAAW;IACvBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE,MAAM;IAClBC,kBAAkB,EAAE,IAAI;IACxBC,qBAAqB,EAAE,IAAI;IAC3BC,oBAAoB,EAAE,IAAI;IAC1BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE,iBAAiB;IAAE;IAC/BC,SAAS,EAAE,IAAI;IACfC,UAAU,EAAE;EACd,CAAC;EACDI,eAAe,EAAE;IACf3B,IAAI,EAAE,aAAa;IACnBD,KAAK,EAAE,IAAI;IACXE,WAAW,EAAE,SAAS;IACtB2B,QAAQ,EAAE,eAAe;IACzBC,KAAK,EAAE,IAAI;IACXC,IAAI,EAAE,KAAK;IACXxB,QAAQ,EAAE;EACZ;AACF,CAAC;AAAAyB,OAAA,CAAAlC,eAAA,GAAAA,eAAA"}]}