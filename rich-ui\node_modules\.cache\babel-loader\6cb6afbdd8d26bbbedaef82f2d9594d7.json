{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\freightSelect.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\freightSelect.vue", "mtime": 1743675026032}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_freight", "require", "_vueTreeselect", "_interopRequireDefault", "_js<PERSON><PERSON>yin", "_auth", "_store", "_company", "_businessRemark", "_logisticsType", "_serviceType", "_company2", "_contract", "_currency", "_cargoPrice", "_cargoType", "_demurrage", "_departureToDestination", "_freeStorage", "_loading", "_price", "_recorder", "_salesRemark", "_shippingDate", "_unit", "_validTime", "_carrier", "_local", "_weight", "_utils", "_rich", "_quotation", "_destination", "_departure", "_carrierNoSupplier", "name", "dicts", "components", "Treeselect", "destination", "departure", "businessRemark", "logisticsType", "serviceType", "cargoPrice", "cargoType", "company", "contract", "currency", "demurrage", "departureTodestination", "freeStorage", "loading", "price", "recorder", "salesRemark", "shippingDate", "unit", "validTime", "carrier", "carrierNoSupplier", "local", "weight", "props", "data", "showLeft", "showRight", "carrierCode", "unitCode", "queryCarrierIds", "temCarrierList", "locationOptions", "Set", "logisticsEfficiencyNodeId", "validPeriodTimeNodeId", "serviceTypeList", "businessList", "carrierList", "commonInfoList", "refreshTable", "serviceTypeId", "logisticsTypeId", "updateBy", "charge", "chargeId", "types", "edit", "ids", "single", "multiple", "showSearch", "total", "freightList", "title", "open", "queryParams", "pageNum", "pageSize", "typeId", "freightSelectData", "inquiryNo", "cargoTypeIds", "carrierIds", "companyIds", "departureIds", "polId", "concat", "lineDepartureIds", "transitPortIds", "destinationIds", "destinationPortId", "lineDestinationIds", "contractTypeIds", "unitIds", "validFrom", "validTo", "freightQuery", "recordFrom", "recordTo", "upload", "isUploading", "updateSupport", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "form", "pattern", "rules", "required", "trigger", "supplierId", "formValue", "quotation", "select", "computed", "columns", "get", "roleId", "ttUnitCode", "unitId", "$store", "state", "listSettings", "seafreightSetting2", "airfreightSetting", "expressdeliverySetting", "trailerSetting", "declareSetting", "warehouseSetting", "watch", "n", "formLogisticsTypeId", "loadCarrier", "list", "undefined", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "done", "c", "value", "push", "children", "length", "_iterator2", "_step2", "ch", "err", "e", "f", "_iterator3", "_step3", "v", "carrierId", "_iterator4", "_step4", "a", "created", "_this2", "getList", "then", "loadCommonInfo", "methods", "changeTime", "val", "remoteSupplier", "_this3", "roleSupplier", "serviceTypeIds", "listCompany", "response", "$nextTick", "$refs", "supplier", "getLoadOptions", "rows", "cancel", "reset", "freightId", "agreementNo", "agreementCode", "importExport", "precarriageRegionId", "transitPortId", "maxWeight", "weightUnitId", "cargoValue", "cargoCurrencyCode", "chargeTypeId", "currencyId", "priceA", "priceB", "priceC", "priceD", "priceE", "shippingWeek", "logisticsSchedule", "logisticsTimeliness", "storageUnitCode", "demurrageUnitCode", "<PERSON><PERSON><PERSON><PERSON>", "status", "psaRemark", "noticeForSales", "requireRemark", "createBy", "createTime", "user", "sid", "updateTime", "parseTime", "Date", "deleteBy", "deleteTime", "deleteStatus", "isSalesRequired", "replyMark", "resetForm", "handleQuery", "date<PERSON><PERSON><PERSON>", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "handleAdd", "$route", "matched", "meta", "_iterator5", "_step5", "basCommonInfo", "infoId", "infoTypeId", "date", "getFullYear", "getMonth", "handleUpdate", "row", "console", "log", "autoCompletion", "split", "join", "submitForm", "_this4", "validate", "valid", "Number", "toString", "updateFreight", "$modal", "msgSuccess", "addFreight", "handleDelete", "freightIds", "_this", "confirm", "delFreight", "handleExport", "download", "_objectSpread2", "formatDate", "handleImport", "importTemplate", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "$message", "info", "msg", "submitFileForm", "submit", "queryLogisticsTypeId", "queryLogisticsType", "node", "serviceLocalName", "queryContractTypeIds", "queryUnitIds", "queryCompanyIds", "queryPrecarriageRegionId", "queryDepartureIds", "queryLineDepartureIds", "queryTransitPortIds", "queryDestinationIds", "queryLineDestinationIds", "queryCargoTypeIds", "getContractTypeId", "handleSelectQueryCarrierIds", "handleDeselectQueryCarrierIds", "filter", "deselectAllQueryCarrierIds", "handleSelectRecordBy", "staffId", "cleanRecordBy", "handleSelectCarrierId", "carrierIntlCode", "deselectCarrierId", "getLogisticsTypeId", "getServiceTypeId", "getChargeId", "getCompanyId", "getLoadingId", "getDepartureId", "getTransitPortId", "getDestinationId", "getCargoCurrencyId", "getCargoUnitId", "getStorageUnitId", "getDemurrageUnitId", "getCurrencyId", "currencyCode", "getUnitId", "getLogisticsUnitId", "getValidPeriodTimeNodeId", "deselectValidPeriodTimeNodeId", "getCargoTypeIds", "refreshColumns", "_this5", "loadBusinesses", "_this6", "businessesList", "redisList", "store", "dispatch", "_this7", "serviceTypeCarriers", "_this8", "logisticsTimeNodeList", "_this9", "_asyncToGenerator2", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "listFreight", "stop", "tableRowClassName", "_ref", "carrierNormalizer", "l", "carrierLocalName", "carrierEnName", "serviceEnName", "pinyin", "getFullChars", "id", "label", "staffNormalizer", "staff", "staffFamilyLocalName", "staffGivingLocalName", "role", "roleLocalName", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "isDisabled", "deptId", "nodeNormalizer", "infoTypeShortName", "infoTypeLocalName", "infoTypeEnName", "infoShortName", "infoLocalName", "infoEnName", "dbclick", "column", "getType", "_iterator6", "_step6", "_iterator7", "_step7", "sc", "getFreightList", "_this10", "queryFreight", "confirmRequire", "_this11", "isReplied", "requireSalesId", "success", "warning", "handleSelect", "_this12", "_callee2", "_callee2$", "_context2", "$emit", "exports", "_default"], "sources": ["src/views/system/freight/freightSelect.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <!--搜索区域-->\r\n      <el-col :span=\"showLeft\" style=\"margin: 0;padding: 0;\">\r\n        <el-form v-if=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" label-width=\"35px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"服务\" prop=\"logisticsTypeId\">\r\n            <tree-select :d-load=\"true\" :dbn=\"true\" :flat=\"false\"\r\n                         :multiple=\"false\" :pass=\"queryParams.serviceTypeId\" :placeholder=\"'服务类型'\"\r\n                         :type=\"'serviceTypeLoad'\" :typeId=\"typeId\" style=\"width: 100%\" @return=\"queryLogisticsTypeId\"\r\n                         @returnData=\"queryLogisticsType\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"序号\" prop=\"inquiryNo\">\r\n            <el-input v-model=\"queryParams.inquiryNo\" placeholder=\"询价单号\" style=\"width: 100%\"/>\r\n          </el-form-item>\r\n          <el-form-item v-if=\"[5].indexOf(parseInt(typeId))!==-1\" label=\"装运\" prop=\"departureIds\"\r\n          >\r\n            <location-select :load-options=\"locationOptions\" :multiple=\"false\" :pass=\"queryParams.precarriageRegionId\"\r\n                             @return=\"queryPrecarriageRegionId\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"[1,2,3,4,5,6,7].indexOf(parseInt(typeId))!==-1\" label=\"启运\" prop=\"departureIds\">\r\n            <location-select :load-options=\"freightSelectData.locationOptions\" :multiple=\"true\"\r\n                             :pass=\"queryParams.departureIds\" style=\"width: 100%\"\r\n                             @return=\"queryDepartureIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"[1].indexOf(parseInt(typeId))!==-1\" label=\"中转\" prop=\"transitPortIds\">\r\n            <location-select :load-options=\"locationOptions\" :multiple=\"true\"\r\n                             :pass=\"queryParams.transitPortIds\" style=\"width: 100%\"\r\n                             @return=\"queryTransitPortIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"[1,2,3,4,6,7].indexOf(parseInt(typeId))!==-1\" label=\"目的\" prop=\"destinationIds\">\r\n            <location-select :en=\"true\" :load-options=\"freightSelectData.locationOptions\"\r\n                             :multiple=\"true\" :pass=\"queryParams.destinationIds\" style=\"width: 100%\"\r\n                             @return=\"queryDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item v-if=\"[1].indexOf(parseInt(typeId))!==-1\" label=\"航线\" prop=\"lineDestinationIds\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                         :pass=\"queryParams.lineDestinationIds\" :placeholder=\"'目的航线'\" :type=\"'line'\"\r\n                         style=\"width: 100%\"\r\n                         @return=\"queryLineDestinationIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"柜型\" prop=\"unitIds\">\r\n            <tree-select :d-load=\"true\" :multiple=\"true\" :pass=\"queryParams.unitIds\" :placeholder=\"'柜型'\"\r\n                         :type=\"'unit'\" style=\"width: 100%\" @return=\"queryUnitIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"货物\" prop=\"cargoTypeIds\">\r\n            <tree-select :d-load=\"true\" :flat=\"true\" :multiple=\"true\" :pass=\"queryParams.cargoTypeIds\"\r\n                         :placeholder=\"'货物特征'\" :type=\"'cargoType'\" style=\"width: 100%\" @return=\"queryCargoTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"承运\" prop=\"carrierIds\">\r\n            <treeselect v-model=\"queryCarrierIds\" :disable-branch-nodes=\"true\"\r\n                        :disable-fuzzy-matching=\"true\" :flatten-search-results=\"true\" :multiple=\"true\"\r\n                        :normalizer=\"carrierNormalizer\" :options=\"carrierList\" :show-count=\"true\"\r\n                        placeholder=\"承运人\" style=\"width: 100%\" @deselect=\"handleDeselectQueryCarrierIds\"\r\n                        @input=\"deselectAllQueryCarrierIds\" @open=\"loadCarrier\"\r\n                        @select=\"handleSelectQueryCarrierIds\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{ node.raw.carrier.carrierIntlCode }}\r\n                {{ node.raw.carrier.carrierIntlCode == null ? node.raw.carrier.carrierShortName : \"\" }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item :label=\"company.substring(0,2)\" prop=\"companyIds\">\r\n            <tree-select ref=\"supplier\" :d-load=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                         :pass=\"queryParams.companyIds\" :placeholder=\"company\" :type=\"'supplier'\"\r\n                         style=\"width: 100%\" @return=\"queryCompanyIds\" @click.native=\"remoteSupplier(true)\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"合约\" prop=\"contractTypeIds\">\r\n            <tree-select :d-load=\"true\" :dbn=\"true\" :flat=\"false\" :multiple=\"true\"\r\n                         :pass=\"queryParams.contractTypeIds\" :placeholder=\"'合约类别'\"\r\n                         :type=\"'contractType'\" style=\"width: 100%\" @return=\"queryContractTypeIds\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"有效\" prop=\"validFrom\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.dateRange\"\r\n              end-placeholder=\"有效期结束日期\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"有效期开始日期\"\r\n              style=\"width: 100%\"\r\n              :default-time=\"['00:00:00', '23:59:59']\"\r\n              type=\"daterange\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n              @change=\"handleQuery\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"搜索\" prop=\"freightQuery\">\r\n            <el-input v-model=\"queryParams.freightQuery\" placeholder=\"合约号/船期/报价总述/备注\" style=\"width: 100%\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"录入\" prop=\"updateBy\">\r\n            <treeselect v-model=\"updateBy\" :disable-branch-nodes=\"true\" :disable-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"所属人\" style=\"width: 100%\"\r\n                        @input=\"cleanRecordBy\" @open=\"loadBusinesses\" @select=\"handleSelectRecordBy\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\" slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"来源\" prop=\"isSalesRequired\">\r\n            <el-select v-model=\"queryParams.isSalesRequired\" clearable @change=\"handleQuery\">\r\n              <el-option label=\"业务询价\" value=\"1\"/>\r\n              <el-option label=\"商务播报\" value=\"0\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"回复\" prop=\"replyMark\">\r\n            <el-select v-model=\"queryParams.replyMark\" clearable @change=\"handleQuery\">\r\n              <el-option label=\"未回复\" value=\"0\"/>\r\n              <el-option label=\"已回复\" value=\"1\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" style=\"margin-left: 3px\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <!--表头-->\r\n        <div style=\"display: flex\">\r\n          <h4 style=\"margin: 0;font-weight:bold;\">\r\n            服务类型：{{ serviceType }}-{{ charge }}\r\n          </h4>\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"confirmRequire\"\r\n          >\r\n            请求更新\r\n          </el-button>\r\n        </div>\r\n        <!--表格-->\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"freightList\" :row-class-name=\"tableRowClassName\" border\r\n                  @row-dblclick=\"dbclick\" @selection-change=\"handleSelectionChange\"\r\n        >\r\n          <!--<el-table-column type=\"selection\" width=\"28\" align=\"center\"/>-->\r\n          <el-table-column\r\n            v-for=\"data in columns\"\r\n            v-if=\"data.visible\"\r\n            :key=\"data.key\"\r\n            :align=\"data.align\"\r\n            :label=\"data.label\"\r\n            :width=\"data.width\"\r\n          >\r\n            <template v-slot=\"scope\">\r\n              <component :is=\"data.prop\" :scope=\"scope\" :typeId=\"typeId\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column key=\"isSalesRequired\" align=\"center\" label=\"价格来源\" width=\"75\">\r\n            <template slot-scope=\"scope\">\r\n              <h6 style=\"margin: 0\">{{ scope.row.salesName }}</h6>\r\n              <h6 style=\"margin: 0\">\r\n                {{ scope.row.isSalesRequired == 1 ? \"业务询价\" : \"\" }}\r\n                {{ scope.row.isSalesRequired == 0 ? \"商务播报\" : \"\" }}\r\n              </h6>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <!--<el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                icon=\"el-icon-edit\"\r\n                style=\"margin: 0 3px\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:freight:edit']\"\r\n              >确认选中\r\n              </el-button>-->\r\n              <el-dropdown\r\n                trigger=\"click\" @command=\"handleSelect\"\r\n              >\r\n                <el-button\r\n                  size=\"mini\"\r\n                  style=\"margin: 2px\"\r\n                  type=\"success\"\r\n                  @click=\"select=scope.row\"\r\n                >确认选择\r\n                </el-button>\r\n                <el-dropdown-menu slot=\"dropdown\">\r\n                  <el-dropdown-item v-for=\"(item,index) in freightSelectData.revenueTonList\" :key=\"index\"\r\n                                    :command=\"item\"\r\n                  >\r\n                    {{ item }}\r\n                  </el-dropdown-item>\r\n                </el-dropdown-menu>\r\n              </el-dropdown>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addFreight, delFreight, getFreight, listFreight, updateFreight} from \"@/api/system/freight\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\"\r\nimport pinyin from \"js-pinyin\"\r\nimport {getToken} from \"@/utils/auth\"\r\nimport store from \"@/store\"\r\nimport {listCompany} from \"@/api/system/company\"\r\n\r\nimport businessRemark from \"@/views/system/freight/businessRemark\"\r\nimport logisticsType from \"@/views/system/freight/logisticsType\"\r\nimport serviceType from \"@/views/system/freight/serviceType\"\r\nimport company from \"@/views/system/freight/company\"\r\nimport contract from \"@/views/system/freight/contract\"\r\nimport currency from \"@/views/system/freight/currency\"\r\nimport cargoPrice from \"@/views/system/freight/cargoPrice\"\r\nimport cargoType from \"@/views/system/freight/cargoType\"\r\nimport demurrage from \"@/views/system/freight/demurrage\"\r\nimport departureTodestination from \"@/views/system/freight/departureToDestination\"\r\nimport freeStorage from \"@/views/system/freight/freeStorage\"\r\nimport loading from \"@/views/system/freight/loading\"\r\nimport price from \"@/views/system/freight/price\"\r\nimport recorder from \"@/views/system/freight/recorder\"\r\nimport salesRemark from \"@/views/system/freight/salesRemark\"\r\nimport shippingDate from \"@/views/system/freight/shippingDate\"\r\nimport unit from \"@/views/system/freight/unit\"\r\nimport validTime from \"@/views/system/freight/validTime\"\r\nimport carrier from \"@/views/system/freight/carrier\"\r\nimport local from \"@/views/system/freight/local\"\r\nimport weight from \"@/views/system/freight/weight\"\r\nimport {formatDate} from \"@/utils\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {queryFreight} from \"@/api/system/quotation\"\r\nimport destination from \"@/views/system/freight/destination.vue\"\r\nimport departure from \"@/views/system/freight/departure.vue\"\r\nimport carrierNoSupplier from \"@/views/system/quotation/carrierNoSupplier.vue\"\r\n\r\nexport default {\r\n  name: \"FreightSelect\",\r\n  dicts: [\"sys_normal_disable\", \"sys_yes_no\"],\r\n  components: {\r\n    Treeselect,\r\n    destination,\r\n    departure,\r\n    businessRemark,\r\n    logisticsType,\r\n    serviceType,\r\n    cargoPrice,\r\n    cargoType,\r\n    company,\r\n    contract,\r\n    currency,\r\n    demurrage,\r\n    departureTodestination,\r\n    freeStorage,\r\n    loading,\r\n    price,\r\n    recorder,\r\n    salesRemark,\r\n    shippingDate,\r\n    unit,\r\n    validTime,\r\n    carrier,\r\n    carrierNoSupplier,\r\n    local,\r\n    weight\r\n  },\r\n  props: [\"typeId\", \"freightSelectData\"],\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      carrierCode: null,\r\n      unitCode: null,\r\n      queryCarrierIds: null,\r\n      temCarrierList: [],\r\n      locationOptions: new Set(),\r\n      logisticsEfficiencyNodeId: null,\r\n      validPeriodTimeNodeId: null,\r\n      serviceTypeList: [],\r\n      businessList: [],\r\n      carrierList: [],\r\n      commonInfoList: [],\r\n      serviceType: null,\r\n      refreshTable: true,\r\n      // logisticsNodeId: null,\r\n      // ttUnitCode: null,\r\n      serviceTypeId: null,\r\n      logisticsTypeId: null,\r\n      updateBy: null,\r\n      charge: null,\r\n      chargeId: null,\r\n      validTime: [],\r\n      company: \"订舱口\",\r\n      types: null,\r\n      edit: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 基础运费表格数据\r\n      freightList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        typeId: this.freightSelectData.typeId,\r\n        serviceTypeId: this.freightSelectData.serviceTypeId,\r\n        // logisticsTypeId: this.freightSelectData.serviceTypeId,\r\n        inquiryNo: null,\r\n        cargoTypeIds: [],\r\n        carrierIds: [],\r\n        companyIds: [],\r\n        departureIds: this.freightSelectData.polId ? [].concat(this.freightSelectData.polId) : [],\r\n        lineDepartureIds: [],\r\n        transitPortIds: [],\r\n        destinationIds: this.freightSelectData.destinationPortId ? [].concat(this.freightSelectData.destinationPortId) : [],\r\n        lineDestinationIds: [],\r\n        contractTypeIds: [],\r\n        unitIds: [],\r\n        validFrom: null,\r\n        validTo: null,\r\n        freightQuery: null,\r\n        updateBy: null,\r\n        recordFrom: null,\r\n        recordTo: null\r\n      },\r\n      // 用户导入参数\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: true,\r\n        // 设置上传的请求头部\r\n        headers: {Authorization: \"Bearer \" + getToken()},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/system/freight/importData\"\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      pattern: this.typeId == \"2\" || this.typeId == \"3\" ? \"/((\\-)?([0-9]{1,6})(\\.?)(\\d{0,2})\\/){0,5}$/\" :\r\n        (this.typeId == \"5\" || this.typeId == \"6\" || this.typeId == \"7\" ? \"/((\\-)?([0-9]{1,6})(\\.?)(\\d{0,2})\\/){0,1}$/\" : \"/((\\-)?([0-9]{1,6})(\\.?)(\\d{0,2})\\/){0,4}$/\"),\r\n      rules: {\r\n        polId: [{required: true, trigger: \"blur\"}],\r\n        destinationPortId: [{required: this.typeId != \"5\", trigger: \"blur\"}],\r\n        carrierCode: [{required: true, trigger: \"blur\"}],\r\n        supplierId: [{required: true, trigger: \"blur\"}],\r\n        unitCode: [{required: true, trigger: \"blur\"}],\r\n        formValue: [{\r\n          required: true,\r\n          pattern: this.pattern,\r\n          trigger: \"blur\"\r\n        }]\r\n      },\r\n      quotation: {},\r\n      select: {}\r\n    }\r\n  },\r\n  computed: {\r\n    columns: {\r\n      get() {\r\n        if (this.typeId == \"1\") {\r\n          this.logisticsTypeId = 1\r\n          this.roleId = 200\r\n          this.ttUnitCode = \"Day\"\r\n          this.chargeId = 1\r\n          this.unitId = 2\r\n          this.unitCode = \"Ctnr\"\r\n          this.serviceType = \"整柜海运\"\r\n          this.charge = \"海运费\"\r\n          this.types = \"seafreight\"\r\n          return this.$store.state.listSettings.seafreightSetting2\r\n        }\r\n        if (this.typeId == \"2\") {\r\n          this.logisticsTypeId = 10\r\n          this.roleId = 200\r\n          this.ttUnitCode = \"Day\"\r\n          this.chargeId = 7\r\n          this.unitId = 5\r\n          this.unitCode = \"KGS\"\r\n          this.serviceType = \"空运\"\r\n          this.charge = \"空运费\"\r\n          this.types = \"airfreight\"\r\n          return this.$store.state.listSettings.airfreightSetting\r\n        }\r\n        if (this.typeId == \"3\") {\r\n          this.logisticsTypeId = 20\r\n          this.roleId = 200\r\n          this.ttUnitCode = \"Day\"\r\n          this.chargeId = 52\r\n          this.unitId = 5\r\n          this.unitCode = \"KGS\"\r\n          this.serviceType = \"铁路\"\r\n          this.charge = \"铁路费\"\r\n          this.company = \"铁路公司\"\r\n          this.types = \"Railway\"\r\n          return this.$store.state.listSettings.expressdeliverySetting\r\n        }\r\n        if (this.typeId == \"4\") {\r\n          this.logisticsTypeId = 40\r\n          this.roleId = 200\r\n          this.ttUnitCode = \"Day\"\r\n          this.chargeId = 43\r\n          this.unitId = 5\r\n          this.unitCode = \"KGS\"\r\n          this.serviceType = \"快递\"\r\n          this.charge = \"快递费\"\r\n          this.company = \"快递公司\"\r\n          this.types = \"expressdelivery\"\r\n          return this.$store.state.listSettings.expressdeliverySetting\r\n        }\r\n        if (this.typeId == \"5\") {\r\n          this.logisticsTypeId = 50\r\n          this.roleId = 203\r\n          this.serviceTypeId = 21\r\n          this.chargeId = 8\r\n          this.unitId = 2\r\n          this.unitCode = \"Ctnr\"\r\n          this.serviceType = \"整柜拖车\"\r\n          this.charge = \"拖车费\"\r\n          this.company = \"拖车行\"\r\n          this.types = \"trailer\"\r\n          return this.$store.state.listSettings.trailerSetting\r\n        }\r\n        if (this.typeId == \"6\") {\r\n          this.logisticsTypeId = 60\r\n          this.roleId = 205\r\n          this.serviceTypeId = 60\r\n          this.chargeId = 42\r\n          this.unitId = 7\r\n          this.unitCode = \"BL\"\r\n          this.serviceType = \"报关\"\r\n          this.charge = \"报关费\"\r\n          this.company = \"报关行\"\r\n          this.types = \"declare\"\r\n          return this.$store.state.listSettings.declareSetting\r\n        }\r\n        if (this.typeId == \"7\") {\r\n          this.logisticsTypeId = 90\r\n          this.chargeId = 44\r\n          this.roleId = 206\r\n          this.serviceType = \"清关\"\r\n          this.charge = \"清关费\"\r\n          this.company = \"报关行\"\r\n          this.types = \"benchmark\"\r\n          return this.$store.state.listSettings.declareSetting\r\n        }\r\n        if (this.typeId == \"8\") {\r\n          this.logisticsTypeId = 100\r\n          this.chargeId = 45\r\n          this.roleId = 207\r\n          this.serviceType = \"仓储\"\r\n          this.charge = \"仓储费\"\r\n          this.company = \"仓储公司\"\r\n          this.types = \"custom\"\r\n          return this.$store.state.listSettings.warehouseSetting\r\n        }\r\n        if (this.typeId == \"9\") {\r\n          this.logisticsTypeId = 80\r\n          this.chargeId = 46\r\n          this.roleId = 204\r\n          this.serviceType = \"拓展服务\"\r\n          this.charge = \"拓展服务费\"\r\n          this.company = \"第三方公司\"\r\n          this.types = \"warehouse\"\r\n          return this.$store.state.listSettings.warehouseSetting\r\n        }\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    \"form.logisticsTypeId\"(n) {\r\n      this.loadCarrier()\r\n      let list = []\r\n      if (this.carrierList != undefined) {\r\n        for (const c of this.carrierList) {\r\n          if (c.serviceTypeId == n) {\r\n            list.push(c)\r\n          }\r\n          if (c.children != undefined && c.children.length > 0) {\r\n            for (const ch of c.children) {\r\n              if (ch.serviceTypeId == n) {\r\n                list.push(ch)\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.temCarrierList = list\r\n      for (const v of this.carrierList) {\r\n        if (v.carrier != null && v.carrier.carrierId != null && v.carrier.carrierId != undefined && this.carrierId != null && this.carrierId != undefined) {\r\n          if (v.carrier.carrierId == this.carrierId && this.carrierId == -1) {\r\n            this.carrierId = v.serviceTypeId\r\n          }\r\n        }\r\n        if (v.children != undefined && v.children.length > 0) {\r\n          for (const a of v.children) {\r\n            if (a.carrier != null && a.carrier.carrierId != null && a.carrier.carrierId != undefined && this.carrierId != null && this.carrierId != undefined) {\r\n              if (a.carrier.carrierId == this.carrierId && this.carrierId == -1) {\r\n                this.carrierId = a.serviceTypeId\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList().then(() => {\r\n      this.loadCarrier()\r\n      this.loadCommonInfo()\r\n    })\r\n  },\r\n  methods: {\r\n    changeTime(val) {\r\n      if (val == undefined) {\r\n        this.form.validFrom = null\r\n        this.form.validTo = null\r\n      }\r\n      this.form.validFrom = val[0]\r\n      this.form.validTo = val[1]\r\n    },\r\n    remoteSupplier(v) {\r\n      let queryParams = {\r\n        pageNum: 1,\r\n        pageSize: 99999,\r\n        roleSupplier: 1,\r\n        // roleIds: [this.roleId],\r\n        serviceTypeIds: [v ? this.queryParams.logisticsTypeId : this.form.logisticsTypeId],\r\n        // cargoTypeIds: v ? this.queryParams.cargoTypeIds : this.form.cargoTypeIds,\r\n        // locationDepartureIds: [v ? this.queryParams.departureIds : this.form.polId],\r\n        // locationDestinationIds: [v ? this.queryParams.destinationIds : this.form.destinationPortId],\r\n        // carrierIds: v ? this.queryCarrierIds : [this.form.carrierId]\r\n      }\r\n      listCompany(queryParams).then(response => {\r\n        this.$nextTick(() => {\r\n          this.$refs.supplier.getLoadOptions(response.rows)\r\n        })\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.edit = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        freightId: null,\r\n        serviceTypeId: this.serviceTypeId,\r\n        agreementNo: null,\r\n        agreementCode: null,\r\n        carrierId: null,\r\n        carrierCode: null,\r\n        supplierId: null,\r\n        logisticsTypeId: this.logisticsTypeId,\r\n        importExport: \"1\",\r\n        precarriageRegionId: null,\r\n        polId: null,\r\n        transitPortId: null,\r\n        destinationPortId: null,\r\n        maxWeight: null,\r\n        cargoTypeIds: [1],\r\n        weightUnitId: 5,\r\n        cargoValue: null,\r\n        cargoCurrencyCode: null,\r\n        chargeId: this.chargeId,\r\n        chargeTypeId: null,\r\n        currencyId: this.typeId == \"1\" ? 10 : 1,\r\n        unitId: this.unitId,\r\n        unitCode: this.unitCode,\r\n        priceA: null,\r\n        priceB: null,\r\n        priceC: null,\r\n        priceD: null,\r\n        priceE: null,\r\n        shippingDate: null,\r\n        shippingWeek: [\"Mon\", \"Tue\", \"Wed\", \"Thur\", \"Fri\", \"Sat\", \"Sun\"],\r\n        logisticsSchedule: null,\r\n        logisticsEfficiencyNodeId: null,\r\n        logisticsTimeliness: null,\r\n        ttUnitCode: this.ttUnitCode,\r\n        validPeriodTimeNodeId: null,\r\n        freeStorage: null,\r\n        demurrage: null,\r\n        storageUnitCode: null,\r\n        demurrageUnitCode: null,\r\n        validFrom: null,\r\n        validTo: null,\r\n        isValid: \"Y\",\r\n        status: \"0\",\r\n        psaRemark: null,\r\n        noticeForSales: null,\r\n        requireRemark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: this.$store.state.user.sid,\r\n        updateTime: parseTime(new Date()),\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\",\r\n        isSalesRequired: 0,\r\n        replyMark: 0\r\n      }\r\n      this.validTime = []\r\n      this.carrierId = null\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.queryParams.validFrom = (this.queryParams.dateRange && this.queryParams.dateRange[0]) ? this.queryParams.dateRange[0] : null\r\n      this.queryParams.validTo = (this.queryParams.dateRange && this.queryParams.dateRange[1]) ? this.queryParams.dateRange[1] : null\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.freightId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加\" + this.$route.matched[1].meta.title\r\n      for (const c of this.commonInfoList[0].children) {\r\n        if (c.basCommonInfo.infoId == 46) {\r\n          this.logisticsEfficiencyNodeId = c.infoTypeId\r\n          this.validPeriodTimeNodeId = c.infoTypeId\r\n        }\r\n      }\r\n      if (this.typeId != 7) {\r\n        let date = new Date()\r\n        this.validTime.push(date)\r\n        this.validTime.push(new Date(new Date(date.getFullYear(), date.getMonth() + 1, 1) - 1000 * 60 * 60 * 24))\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      console.log(row)\r\n    },\r\n    autoCompletion() {\r\n      let value = this.form.formValue.split(\"/\")\r\n      if (value.length == 2) {\r\n        value[2] = value[1]\r\n      }\r\n      this.form.formValue = value.join(\"/\")\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm(val) {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          let value = this.form.formValue.split(\"/\")\r\n          if (value.length > 1 && this.typeId != 2) {\r\n            this.form.priceB = value[0] != undefined ? Number(value[0]) : null\r\n            this.form.priceC = value[1] != undefined ? Number(value[1]) : null\r\n            this.form.priceD = value[2] != undefined ? Number(value[2]) : value[1] != undefined ? Number(value[1]) : null\r\n            this.form.priceA = value[3] != undefined ? Number(value[3]) : null\r\n            this.form.unitId = this.unitId\r\n            this.form.unitCode = this.unitCode\r\n          } else if (value.length > 1 && this.typeId == 2) {\r\n            this.form.priceB = value[0] != undefined ? Number(value[0]) : null\r\n            this.form.priceC = value[1] != undefined ? Number(value[1]) : null\r\n            this.form.priceD = value[2] != undefined ? Number(value[2]) : null\r\n            this.form.priceE = value[3] != undefined ? Number(value[3]) : null\r\n            this.form.priceA = value[4] != undefined ? Number(value[4]) : null\r\n            this.form.unitId = this.unitId\r\n            this.form.unitCode = this.unitCode\r\n          } else if (value.length == 1) {\r\n            this.form.priceB = null\r\n            this.form.priceC = null\r\n            this.form.priceD = null\r\n            this.form.priceE = null\r\n            this.form.priceA = Number(this.form.formValue)\r\n            if (this.form.unitCode == \"Ctnr\") {\r\n              this.form.unitId = 11\r\n              this.form.unitCode = \"20GP\"\r\n            }\r\n          }\r\n          this.form.replyMark = 1\r\n          if (this.form.shippingWeek != null && this.form.shippingWeek.length > 0) {\r\n            this.form.shippingWeek = this.form.shippingWeek.toString()\r\n          } else {\r\n            this.form.shippingWeek = null\r\n          }\r\n          if (val == \"true\") {\r\n            this.form.freightId = null\r\n            this.form.inquiryNo = null\r\n          }\r\n          if (this.form.freightId != null) {\r\n            updateFreight(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.edit = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addFreight(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.reset()\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const freightIds = row.freightId || this.ids\r\n      const _this = this\r\n      _this.$modal.confirm(\"是否确认删除基础运费编号为\\\"\" + freightIds + \"\\\"的数据项？\").then(function () {\r\n        return delFreight(freightIds).then(response => {\r\n          _this.getList()\r\n          _this.$modal.msgSuccess(\"成功删除\" + freightIds.length + \"个数据，但有\" + response.data + \"不能删除\")\r\n        })\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/freight/export\", {\r\n        ...this.queryParams\r\n      }, `SeaFCL_Export_${formatDate(new Date())}.xlsx`)\r\n    },\r\n    /** 导入按钮操作 */\r\n    handleImport() {\r\n      this.upload.title = \"用户导入\"\r\n      this.upload.open = true\r\n    },\r\n    /** 下载模板操作 */\r\n    importTemplate() {\r\n      this.download(\"system/freight/importTemplate\", {}, `SeaFCL_Template_${formatDate(new Date())}.xlsx`)\r\n    },\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true\r\n    },\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false\r\n      this.upload.isUploading = false\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info(response.msg)\r\n      if (response.msg != \"全部上传成功\") {\r\n        this.download(\"system/freight/failList\", {}, `上传失败列表.xlsx`)\r\n      }\r\n      this.getList()\r\n    },\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit()\r\n    },\r\n    queryLogisticsTypeId(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.logisticsTypeId = 21\r\n        this.serviceType = \"海运整箱\"\r\n      }\r\n    },\r\n    queryLogisticsType(node) {\r\n      this.queryParams.logisticsTypeId = node.serviceTypeId\r\n      this.serviceType = node.serviceLocalName\r\n      this.handleQuery()\r\n    },\r\n    queryContractTypeIds(val) {\r\n      this.queryParams.contractTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryUnitIds(val) {\r\n      this.queryParams.unitIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryCompanyIds(val) {\r\n      this.queryParams.companyIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryPrecarriageRegionId(val) {\r\n      this.queryParams.precarriageRegionId = val\r\n      this.handleQuery()\r\n    },\r\n    queryDepartureIds(val) {\r\n      this.queryParams.departureIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLineDepartureIds(val) {\r\n      this.queryParams.lineDepartureIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryTransitPortIds(val) {\r\n      this.queryParams.transitPortIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryDestinationIds(val) {\r\n      this.queryParams.destinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryLineDestinationIds(val) {\r\n      this.queryParams.lineDestinationIds = val\r\n      this.handleQuery()\r\n    },\r\n    queryCargoTypeIds(val) {\r\n      this.queryParams.cargoTypeIds = val\r\n      this.handleQuery()\r\n    },\r\n    getContractTypeId(val) {\r\n      this.form.agreementCode = val\r\n    },\r\n    handleSelectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds.push(node.carrier.carrierId)\r\n      this.handleQuery()\r\n    },\r\n    handleDeselectQueryCarrierIds(node) {\r\n      this.queryParams.carrierIds = this.queryParams.carrierIds.filter((item) => {\r\n        return item != node.carrier.carrierId\r\n      })\r\n      this.handleQuery()\r\n    },\r\n    deselectAllQueryCarrierIds(value) {\r\n      if (value.length == 0) {\r\n        this.queryParams.carrierIds = []\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    handleSelectRecordBy(node) {\r\n      this.queryParams.updateBy = node.staffId\r\n      this.handleQuery()\r\n    },\r\n    cleanRecordBy(val) {\r\n      if (val == undefined) {\r\n        this.queryParams.updateBy = null\r\n        this.handleQuery()\r\n      }\r\n    },\r\n    handleSelectCarrierId(node) {\r\n      // this.form.carrierId = node.carrier.carrierId\r\n      this.form.carrierCode = node.carrier.carrierIntlCode\r\n    },\r\n    deselectCarrierId(value) {\r\n      if (value == undefined) {\r\n        this.form.carrierId = null\r\n      }\r\n    },\r\n    getLogisticsTypeId(val) {\r\n      this.form.logisticsTypeId = val\r\n    },\r\n    getServiceTypeId(val) {\r\n      this.form.serviceTypeId = val\r\n    },\r\n    getChargeId(val) {\r\n      this.form.chargeId = val\r\n    },\r\n    getCompanyId(val) {\r\n      this.form.supplierId = val\r\n    },\r\n    getLoadingId(val) {\r\n      this.form.precarriageRegionId = val\r\n    },\r\n    getDepartureId(val) {\r\n      this.form.polId = val\r\n    },\r\n    getTransitPortId(val) {\r\n      this.form.transitPortId = val\r\n    },\r\n    getDestinationId(val) {\r\n      this.form.destinationPortId = val\r\n    },\r\n    getCargoCurrencyId(val) {\r\n      this.form.cargoCurrencyCode = val\r\n    },\r\n    getCargoUnitId(val) {\r\n      this.form.weightUnitId = val\r\n    },\r\n    getStorageUnitId(val) {\r\n      this.form.storageUnitCode = val\r\n    },\r\n    getDemurrageUnitId(val) {\r\n      this.form.demurrageUnitCode = val\r\n    },\r\n    getCurrencyId(val) {\r\n      this.form.currencyCode = val\r\n    },\r\n    getUnitId(val) {\r\n      this.form.unitCode = val\r\n    },\r\n    getLogisticsUnitId(val) {\r\n      this.form.ttUnitCode = val\r\n    },\r\n    getValidPeriodTimeNodeId(node) {\r\n      this.form.validPeriodTimeNodeId = node.basCommonInfo.infoId\r\n    },\r\n    deselectValidPeriodTimeNodeId(val) {\r\n      if (val == undefined) {\r\n        this.form.validPeriodTimeNodeId = null\r\n      }\r\n    },\r\n    getCargoTypeIds(val) {\r\n      this.form.cargoTypeIds = val\r\n    },\r\n    refreshColumns() {\r\n      this.refreshTable = false\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true\r\n      })\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.serviceTypeCarriers.length == 0 || this.$store.state.data.redisList.serviceTypeCarriers) {\r\n        store.dispatch(\"getServiceTypeCarriersList\").then(() => {\r\n          this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n        })\r\n      } else {\r\n        this.carrierList = this.$store.state.data.serviceTypeCarriers\r\n      }\r\n    },\r\n    loadCommonInfo() {\r\n      if (this.$store.state.data.logisticsTimeNodeList.length == 0 || this.$store.state.data.redisList.logisticsTimeNodeList) {\r\n        store.dispatch(\"getLogisticsTimeNodeList\").then(() => {\r\n          this.commonInfoList = this.$store.state.data.logisticsTimeNodeList\r\n        })\r\n      } else {\r\n        this.commonInfoList = this.$store.state.data.logisticsTimeNodeList\r\n      }\r\n    },\r\n    /** 查询基础运费列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      await listFreight(this.queryParams).then(response => {\r\n        this.freightList = response.rows\r\n        this.total = response.total ? response.total : 0\r\n        this.loading = false\r\n      })\r\n    },\r\n    tableRowClassName({row}) {\r\n      let date = parseTime(new Date(), \"{y}-{m}-{d}\")\r\n      let validFrom = parseTime(row.validFrom, \"{y}-{m}-{d}\")\r\n      let validTo = parseTime(row.validTo, \"{y}-{m}-{d}\")\r\n      if (validFrom < date < validTo) {\r\n        return \"\"\r\n      }\r\n      if (validTo < date) {\r\n        return \"valid-row\"\r\n      }\r\n      if (validFrom > date) {\r\n        return \"valid-before\"\r\n      }\r\n      return \"\"\r\n    },\r\n    carrierNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (!node.carrier || (node.carrier.carrierLocalName == null && node.carrier.carrierEnName == null)) {\r\n        l = node.serviceLocalName + \" \" + node.serviceEnName + \",\" + pinyin.getFullChars(node.serviceLocalName)\r\n      } else {\r\n        l = (node.carrier.carrierIntlCode != null ? node.carrier.carrierIntlCode : \"\") + \" \" + (node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\") + \" \" + (node.carrier.carrierEnName != null ? node.carrier.carrierEnName : \"\") + \",\" + pinyin.getFullChars((node.carrier.carrierLocalName != null ? node.carrier.carrierLocalName : \"\"))\r\n      }\r\n      return {\r\n        id: node.serviceTypeId,\r\n        label: l,\r\n        children: node.children\r\n      }\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    },\r\n    nodeNormalizer(node) {\r\n      let l\r\n      if (node.basCommonInfo == null) {\r\n        l = (node.infoTypeShortName != null ? node.infoTypeShortName : \"\") + \" \" + (node.infoTypeLocalName != null ? node.infoTypeLocalName : \"\") + \" \" + (node.infoTypeEnName != null ? node.infoTypeEnName : \"\") + \",\" + pinyin.getFullChars(node.infoTypeShortName + node.infoTypeLocalName)\r\n      } else {\r\n        l = (node.basCommonInfo.infoShortName != null ? node.basCommonInfo.infoShortName : \"\") + \" \" + (node.basCommonInfo.infoLocalName != null ? node.basCommonInfo.infoLocalName : \"\") + \" \" + (node.basCommonInfo.infoEnName != null ? node.basCommonInfo.infoEnName : \"\") + \",\" + pinyin.getFullChars(node.basCommonInfo.infoShortName + node.basCommonInfo.infoLocalName)\r\n      }\r\n      return {\r\n        id: node.infoTypeId,\r\n        label: l\r\n      }\r\n    },\r\n    dbclick(row, column, event) {\r\n\r\n    },\r\n    getType(node) {\r\n      this.serviceTypeId = node.serviceTypeId\r\n      if (this.serviceTypeList) {\r\n        for (const s of this.serviceTypeList) {\r\n          if (s.serviceTypeId == node.serviceTypeId) {\r\n            this.typeId = s.typeId\r\n          }\r\n          if (s.children) {\r\n            for (const sc of s.children) {\r\n              if (sc.serviceTypeId == node.serviceTypeId) {\r\n                this.typeId = s.typeId\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n      this.freightSelectData.logisticsTypeId = node.serviceTypeId\r\n      this.getFreightList()\r\n    },\r\n    getFreightList() {\r\n      this.quotation.pageSize = this.queryParams.pageSize\r\n      this.quotation.pageNum = this.queryParams.pageNum\r\n      this.quotation.typeId = this.freightSelectData.typeId\r\n      this.quotation.logisticsTypeId = this.serviceTypeId\r\n      queryFreight(this.quotation).then(response => {\r\n        this.freightList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 请求更新（添加一条费用）\r\n    confirmRequire() {\r\n      let quotation = {}\r\n      quotation.serviceTypeId = this.freightSelectData.serviceTypeId\r\n      quotation.isSalesRequired = 1\r\n      quotation.isReplied = 0\r\n      quotation.requireSalesId = this.$store.state.user.sid\r\n\r\n      quotation.polId = this.freightSelectData.polId\r\n      quotation.destinationPortId = this.freightSelectData.destinationPortId\r\n\r\n      if (this.freightSelectData.polId != null && this.freightSelectData.destinationPortId != null) {\r\n        addFreight(quotation).then(response => {\r\n          this.$message.success(response.msg)\r\n        })\r\n      }\r\n      if (this.freightSelectData.polId == null) {\r\n        this.$message.warning(\"启运港为空\")\r\n      } else if (this.freightSelectData.destinationPortId == null) {\r\n        this.$message.warning(\"目的港为空\")\r\n      }\r\n    },\r\n    async handleSelect(val) {\r\n      this.$emit(\"returnFreight\", this.freightSelectData.serviceTypeId, val, this.select)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAqOA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,QAAA,GAAAN,OAAA;AAEA,IAAAO,eAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,cAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,YAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,SAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,SAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,SAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,WAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,UAAA,GAAAZ,sBAAA,CAAAF,OAAA;AACA,IAAAe,UAAA,GAAAb,sBAAA,CAAAF,OAAA;AACA,IAAAgB,uBAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,YAAA,GAAAf,sBAAA,CAAAF,OAAA;AACA,IAAAkB,QAAA,GAAAhB,sBAAA,CAAAF,OAAA;AACA,IAAAmB,MAAA,GAAAjB,sBAAA,CAAAF,OAAA;AACA,IAAAoB,SAAA,GAAAlB,sBAAA,CAAAF,OAAA;AACA,IAAAqB,YAAA,GAAAnB,sBAAA,CAAAF,OAAA;AACA,IAAAsB,aAAA,GAAApB,sBAAA,CAAAF,OAAA;AACA,IAAAuB,KAAA,GAAArB,sBAAA,CAAAF,OAAA;AACA,IAAAwB,UAAA,GAAAtB,sBAAA,CAAAF,OAAA;AACA,IAAAyB,QAAA,GAAAvB,sBAAA,CAAAF,OAAA;AACA,IAAA0B,MAAA,GAAAxB,sBAAA,CAAAF,OAAA;AACA,IAAA2B,OAAA,GAAAzB,sBAAA,CAAAF,OAAA;AACA,IAAA4B,MAAA,GAAA5B,OAAA;AACA,IAAA6B,KAAA,GAAA7B,OAAA;AACA,IAAA8B,UAAA,GAAA9B,OAAA;AACA,IAAA+B,YAAA,GAAA7B,sBAAA,CAAAF,OAAA;AACA,IAAAgC,UAAA,GAAA9B,sBAAA,CAAAF,OAAA;AACA,IAAAiC,kBAAA,GAAA/B,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAkC,IAAA;EACAC,KAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,sBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,OAAA,EAAAA,iBAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,sBAAA,EAAAA,+BAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,YAAA,EAAAA,qBAAA;IACAC,IAAA,EAAAA,aAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,OAAA,EAAAA,gBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,MAAA,EAAAA;EACA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,WAAA;MACAC,QAAA;MACAC,eAAA;MACAC,cAAA;MACAC,eAAA,MAAAC,GAAA;MACAC,yBAAA;MACAC,qBAAA;MACAC,eAAA;MACAC,YAAA;MACAC,WAAA;MACAC,cAAA;MACAnC,WAAA;MACAoC,YAAA;MACA;MACA;MACAC,aAAA;MACAC,eAAA;MACAC,QAAA;MACAC,MAAA;MACAC,QAAA;MACA1B,SAAA;MACAZ,OAAA;MACAuC,KAAA;MACAC,IAAA;MACA;MACAlC,OAAA;MACA;MACAmC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA,OAAAC,iBAAA,CAAAD,MAAA;QACAlB,aAAA,OAAAmB,iBAAA,CAAAnB,aAAA;QACA;QACAoB,SAAA;QACAC,YAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA,OAAAL,iBAAA,CAAAM,KAAA,MAAAC,MAAA,MAAAP,iBAAA,CAAAM,KAAA;QACAE,gBAAA;QACAC,cAAA;QACAC,cAAA,OAAAV,iBAAA,CAAAW,iBAAA,MAAAJ,MAAA,MAAAP,iBAAA,CAAAW,iBAAA;QACAC,kBAAA;QACAC,eAAA;QACAC,OAAA;QACAC,SAAA;QACAC,OAAA;QACAC,YAAA;QACAlC,QAAA;QACAmC,UAAA;QACAC,QAAA;MACA;MACA;MACAC,MAAA;QACA;QACAzB,IAAA;QACA;QACAD,KAAA;QACA;QACA2B,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,OAAA,OAAAhC,MAAA,gBAAAA,MAAA,0DACA,KAAAA,MAAA,gBAAAA,MAAA,gBAAAA,MAAA;MACAiC,KAAA;QACA1B,KAAA;UAAA2B,QAAA;UAAAC,OAAA;QAAA;QACAvB,iBAAA;UAAAsB,QAAA,OAAAlC,MAAA;UAAAmC,OAAA;QAAA;QACAlE,WAAA;UAAAiE,QAAA;UAAAC,OAAA;QAAA;QACAC,UAAA;UAAAF,QAAA;UAAAC,OAAA;QAAA;QACAjE,QAAA;UAAAgE,QAAA;UAAAC,OAAA;QAAA;QACAE,SAAA;UACAH,QAAA;UACAF,OAAA,OAAAA,OAAA;UACAG,OAAA;QACA;MACA;MACAG,SAAA;MACAC,MAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA;MACAC,GAAA,WAAAA,IAAA;QACA,SAAA1C,MAAA;UACA,KAAAjB,eAAA;UACA,KAAA4D,MAAA;UACA,KAAAC,UAAA;UACA,KAAA1D,QAAA;UACA,KAAA2D,MAAA;UACA,KAAA3E,QAAA;UACA,KAAAzB,WAAA;UACA,KAAAwC,MAAA;UACA,KAAAE,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAC,kBAAA;QACA;QACA,SAAAjD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAA4D,MAAA;UACA,KAAAC,UAAA;UACA,KAAA1D,QAAA;UACA,KAAA2D,MAAA;UACA,KAAA3E,QAAA;UACA,KAAAzB,WAAA;UACA,KAAAwC,MAAA;UACA,KAAAE,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAE,iBAAA;QACA;QACA,SAAAlD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAA4D,MAAA;UACA,KAAAC,UAAA;UACA,KAAA1D,QAAA;UACA,KAAA2D,MAAA;UACA,KAAA3E,QAAA;UACA,KAAAzB,WAAA;UACA,KAAAwC,MAAA;UACA,KAAArC,OAAA;UACA,KAAAuC,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAG,sBAAA;QACA;QACA,SAAAnD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAA4D,MAAA;UACA,KAAAC,UAAA;UACA,KAAA1D,QAAA;UACA,KAAA2D,MAAA;UACA,KAAA3E,QAAA;UACA,KAAAzB,WAAA;UACA,KAAAwC,MAAA;UACA,KAAArC,OAAA;UACA,KAAAuC,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAG,sBAAA;QACA;QACA,SAAAnD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAA4D,MAAA;UACA,KAAA7D,aAAA;UACA,KAAAI,QAAA;UACA,KAAA2D,MAAA;UACA,KAAA3E,QAAA;UACA,KAAAzB,WAAA;UACA,KAAAwC,MAAA;UACA,KAAArC,OAAA;UACA,KAAAuC,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAI,cAAA;QACA;QACA,SAAApD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAA4D,MAAA;UACA,KAAA7D,aAAA;UACA,KAAAI,QAAA;UACA,KAAA2D,MAAA;UACA,KAAA3E,QAAA;UACA,KAAAzB,WAAA;UACA,KAAAwC,MAAA;UACA,KAAArC,OAAA;UACA,KAAAuC,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAK,cAAA;QACA;QACA,SAAArD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAAG,QAAA;UACA,KAAAyD,MAAA;UACA,KAAAlG,WAAA;UACA,KAAAwC,MAAA;UACA,KAAArC,OAAA;UACA,KAAAuC,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAK,cAAA;QACA;QACA,SAAArD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAAG,QAAA;UACA,KAAAyD,MAAA;UACA,KAAAlG,WAAA;UACA,KAAAwC,MAAA;UACA,KAAArC,OAAA;UACA,KAAAuC,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAM,gBAAA;QACA;QACA,SAAAtD,MAAA;UACA,KAAAjB,eAAA;UACA,KAAAG,QAAA;UACA,KAAAyD,MAAA;UACA,KAAAlG,WAAA;UACA,KAAAwC,MAAA;UACA,KAAArC,OAAA;UACA,KAAAuC,KAAA;UACA,YAAA2D,MAAA,CAAAC,KAAA,CAAAC,YAAA,CAAAM,gBAAA;QACA;MACA;IACA;EACA;EACAC,KAAA;IACA/D,UAAA,WAAAA,WAAAgE,CAAA;MACA,IAAAA,CAAA;QACA,KAAAxF,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACA,iCAAA0F,oBAAAD,CAAA;MACA,KAAAE,WAAA;MACA,IAAAC,IAAA;MACA,SAAAhF,WAAA,IAAAiF,SAAA;QAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACA,KAAApF,WAAA;UAAAqF,KAAA;QAAA;UAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAL,CAAA,IAAAU,IAAA;YAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;YACA,IAAAD,CAAA,CAAArF,aAAA,IAAA0E,CAAA;cACAG,IAAA,CAAAU,IAAA,CAAAF,CAAA;YACA;YACA,IAAAA,CAAA,CAAAG,QAAA,IAAAV,SAAA,IAAAO,CAAA,CAAAG,QAAA,CAAAC,MAAA;cAAA,IAAAC,UAAA,OAAAV,2BAAA,CAAAC,OAAA,EACAI,CAAA,CAAAG,QAAA;gBAAAG,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAAP,CAAA,MAAAQ,MAAA,GAAAD,UAAA,CAAAhB,CAAA,IAAAU,IAAA;kBAAA,IAAAQ,EAAA,GAAAD,MAAA,CAAAL,KAAA;kBACA,IAAAM,EAAA,CAAA5F,aAAA,IAAA0E,CAAA;oBACAG,IAAA,CAAAU,IAAA,CAAAK,EAAA;kBACA;gBACA;cAAA,SAAAC,GAAA;gBAAAH,UAAA,CAAAI,CAAA,CAAAD,GAAA;cAAA;gBAAAH,UAAA,CAAAK,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAd,SAAA,CAAAe,CAAA,CAAAD,GAAA;QAAA;UAAAd,SAAA,CAAAgB,CAAA;QAAA;MACA;MACA,KAAAzG,cAAA,GAAAuF,IAAA;MAAA,IAAAmB,UAAA,OAAAhB,2BAAA,CAAAC,OAAA,EACA,KAAApF,WAAA;QAAAoG,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAAb,CAAA,MAAAc,MAAA,GAAAD,UAAA,CAAAtB,CAAA,IAAAU,IAAA;UAAA,IAAAc,CAAA,GAAAD,MAAA,CAAAX,KAAA;UACA,IAAAY,CAAA,CAAAvH,OAAA,YAAAuH,CAAA,CAAAvH,OAAA,CAAAwH,SAAA,YAAAD,CAAA,CAAAvH,OAAA,CAAAwH,SAAA,IAAArB,SAAA,SAAAqB,SAAA,iBAAAA,SAAA,IAAArB,SAAA;YACA,IAAAoB,CAAA,CAAAvH,OAAA,CAAAwH,SAAA,SAAAA,SAAA,SAAAA,SAAA;cACA,KAAAA,SAAA,GAAAD,CAAA,CAAAlG,aAAA;YACA;UACA;UACA,IAAAkG,CAAA,CAAAV,QAAA,IAAAV,SAAA,IAAAoB,CAAA,CAAAV,QAAA,CAAAC,MAAA;YAAA,IAAAW,UAAA,OAAApB,2BAAA,CAAAC,OAAA,EACAiB,CAAA,CAAAV,QAAA;cAAAa,MAAA;YAAA;cAAA,KAAAD,UAAA,CAAAjB,CAAA,MAAAkB,MAAA,GAAAD,UAAA,CAAA1B,CAAA,IAAAU,IAAA;gBAAA,IAAAkB,CAAA,GAAAD,MAAA,CAAAf,KAAA;gBACA,IAAAgB,CAAA,CAAA3H,OAAA,YAAA2H,CAAA,CAAA3H,OAAA,CAAAwH,SAAA,YAAAG,CAAA,CAAA3H,OAAA,CAAAwH,SAAA,IAAArB,SAAA,SAAAqB,SAAA,iBAAAA,SAAA,IAAArB,SAAA;kBACA,IAAAwB,CAAA,CAAA3H,OAAA,CAAAwH,SAAA,SAAAA,SAAA,SAAAA,SAAA;oBACA,KAAAA,SAAA,GAAAG,CAAA,CAAAtG,aAAA;kBACA;gBACA;cACA;YAAA,SAAA6F,GAAA;cAAAO,UAAA,CAAAN,CAAA,CAAAD,GAAA;YAAA;cAAAO,UAAA,CAAAL,CAAA;YAAA;UACA;QACA;MAAA,SAAAF,GAAA;QAAAG,UAAA,CAAAF,CAAA,CAAAD,GAAA;MAAA;QAAAG,UAAA,CAAAD,CAAA;MAAA;IACA;EACA;EACAQ,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IACA,KAAAC,OAAA,GAAAC,IAAA;MACAF,MAAA,CAAA5B,WAAA;MACA4B,MAAA,CAAAG,cAAA;IACA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MACA,IAAAA,GAAA,IAAAhC,SAAA;QACA,KAAA7B,IAAA,CAAAf,SAAA;QACA,KAAAe,IAAA,CAAAd,OAAA;MACA;MACA,KAAAc,IAAA,CAAAf,SAAA,GAAA4E,GAAA;MACA,KAAA7D,IAAA,CAAAd,OAAA,GAAA2E,GAAA;IACA;IACAC,cAAA,WAAAA,eAAAb,CAAA;MAAA,IAAAc,MAAA;MACA,IAAAjG,WAAA;QACAC,OAAA;QACAC,QAAA;QACAgG,YAAA;QACA;QACAC,cAAA,GAAAhB,CAAA,QAAAnF,WAAA,CAAAd,eAAA,QAAAgD,IAAA,CAAAhD,eAAA;QACA;QACA;QACA;QACA;MACA;;MACA,IAAAkH,oBAAA,EAAApG,WAAA,EAAA2F,IAAA,WAAAU,QAAA;QACAJ,MAAA,CAAAK,SAAA;UACAL,MAAA,CAAAM,KAAA,CAAAC,QAAA,CAAAC,cAAA,CAAAJ,QAAA,CAAAK,IAAA;QACA;MACA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA5G,IAAA;MACA,KAAAR,IAAA;MACA,KAAAqH,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA1E,IAAA;QACA2E,SAAA;QACA5H,aAAA,OAAAA,aAAA;QACA6H,WAAA;QACAC,aAAA;QACA3B,SAAA;QACAhH,WAAA;QACAmE,UAAA;QACArD,eAAA,OAAAA,eAAA;QACA8H,YAAA;QACAC,mBAAA;QACAvG,KAAA;QACAwG,aAAA;QACAnG,iBAAA;QACAoG,SAAA;QACA7G,YAAA;QACA8G,YAAA;QACAC,UAAA;QACAC,iBAAA;QACAjI,QAAA,OAAAA,QAAA;QACAkI,YAAA;QACAC,UAAA,OAAArH,MAAA;QACA6C,MAAA,OAAAA,MAAA;QACA3E,QAAA,OAAAA,QAAA;QACAoJ,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;QACApK,YAAA;QACAqK,YAAA;QACAC,iBAAA;QACArJ,yBAAA;QACAsJ,mBAAA;QACAjF,UAAA,OAAAA,UAAA;QACApE,qBAAA;QACAvB,WAAA;QACAF,SAAA;QACA+K,eAAA;QACAC,iBAAA;QACA/G,SAAA;QACAC,OAAA;QACA+G,OAAA;QACAC,MAAA;QACAC,SAAA;QACAC,cAAA;QACAC,aAAA;QACAC,QAAA;QACAC,UAAA;QACAtJ,QAAA,OAAA8D,MAAA,CAAAC,KAAA,CAAAwF,IAAA,CAAAC,GAAA;QACAC,UAAA,MAAAC,eAAA,MAAAC,IAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;QACAC,eAAA;QACAC,SAAA;MACA;MACA,KAAAxL,SAAA;MACA,KAAAyH,SAAA;MACA,KAAAgE,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAArJ,WAAA,CAAAC,OAAA;MACA,KAAAD,WAAA,CAAAmB,SAAA,QAAAnB,WAAA,CAAAsJ,SAAA,SAAAtJ,WAAA,CAAAsJ,SAAA,WAAAtJ,WAAA,CAAAsJ,SAAA;MACA,KAAAtJ,WAAA,CAAAoB,OAAA,QAAApB,WAAA,CAAAsJ,SAAA,SAAAtJ,WAAA,CAAAsJ,SAAA,WAAAtJ,WAAA,CAAAsJ,SAAA;MACA,KAAA5D,OAAA;IACA;IACA,aACA6D,UAAA,WAAAA,WAAA;MACA,KAAAH,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAjK,GAAA,GAAAiK,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA9C,SAAA;MAAA;MACA,KAAApH,MAAA,GAAAgK,SAAA,CAAA/E,MAAA;MACA,KAAAhF,QAAA,IAAA+J,SAAA,CAAA/E,MAAA;IACA;IACA,aACAkF,SAAA,WAAAA,UAAA;MACA,KAAAhD,KAAA;MACA,KAAA7G,IAAA;MACA,KAAAD,KAAA,eAAA+J,MAAA,CAAAC,OAAA,IAAAC,IAAA,CAAAjK,KAAA;MAAA,IAAAkK,UAAA,OAAA/F,2BAAA,CAAAC,OAAA,EACA,KAAAnF,cAAA,IAAA0F,QAAA;QAAAwF,MAAA;MAAA;QAAA,KAAAD,UAAA,CAAA5F,CAAA,MAAA6F,MAAA,GAAAD,UAAA,CAAArG,CAAA,IAAAU,IAAA;UAAA,IAAAC,CAAA,GAAA2F,MAAA,CAAA1F,KAAA;UACA,IAAAD,CAAA,CAAA4F,aAAA,CAAAC,MAAA;YACA,KAAAzL,yBAAA,GAAA4F,CAAA,CAAA8F,UAAA;YACA,KAAAzL,qBAAA,GAAA2F,CAAA,CAAA8F,UAAA;UACA;QACA;MAAA,SAAAtF,GAAA;QAAAkF,UAAA,CAAAjF,CAAA,CAAAD,GAAA;MAAA;QAAAkF,UAAA,CAAAhF,CAAA;MAAA;MACA,SAAA7E,MAAA;QACA,IAAAkK,IAAA,OAAAvB,IAAA;QACA,KAAAnL,SAAA,CAAA6G,IAAA,CAAA6F,IAAA;QACA,KAAA1M,SAAA,CAAA6G,IAAA,KAAAsE,IAAA,KAAAA,IAAA,CAAAuB,IAAA,CAAAC,WAAA,IAAAD,IAAA,CAAAE,QAAA;MACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA;IACA;IACAG,cAAA,WAAAA,eAAA;MACA,IAAArG,KAAA,QAAArC,IAAA,CAAAM,SAAA,CAAAqI,KAAA;MACA,IAAAtG,KAAA,CAAAG,MAAA;QACAH,KAAA,MAAAA,KAAA;MACA;MACA,KAAArC,IAAA,CAAAM,SAAA,GAAA+B,KAAA,CAAAuG,IAAA;IACA;IACA,WACAC,UAAA,WAAAA,WAAAhF,GAAA;MAAA,IAAAiF,MAAA;MACA,KAAAzE,KAAA,SAAA0E,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAA3G,KAAA,GAAAyG,MAAA,CAAA9I,IAAA,CAAAM,SAAA,CAAAqI,KAAA;UACA,IAAAtG,KAAA,CAAAG,MAAA,QAAAsG,MAAA,CAAA7K,MAAA;YACA6K,MAAA,CAAA9I,IAAA,CAAAwF,MAAA,GAAAnD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAAyF,MAAA,GAAApD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAA0F,MAAA,GAAArD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA,OAAAA,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAAuF,MAAA,GAAAlD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAAc,MAAA,GAAAgI,MAAA,CAAAhI,MAAA;YACAgI,MAAA,CAAA9I,IAAA,CAAA7D,QAAA,GAAA2M,MAAA,CAAA3M,QAAA;UACA,WAAAkG,KAAA,CAAAG,MAAA,QAAAsG,MAAA,CAAA7K,MAAA;YACA6K,MAAA,CAAA9I,IAAA,CAAAwF,MAAA,GAAAnD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAAyF,MAAA,GAAApD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAA0F,MAAA,GAAArD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAA2F,MAAA,GAAAtD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAAuF,MAAA,GAAAlD,KAAA,OAAAR,SAAA,GAAAoH,MAAA,CAAA5G,KAAA;YACAyG,MAAA,CAAA9I,IAAA,CAAAc,MAAA,GAAAgI,MAAA,CAAAhI,MAAA;YACAgI,MAAA,CAAA9I,IAAA,CAAA7D,QAAA,GAAA2M,MAAA,CAAA3M,QAAA;UACA,WAAAkG,KAAA,CAAAG,MAAA;YACAsG,MAAA,CAAA9I,IAAA,CAAAwF,MAAA;YACAsD,MAAA,CAAA9I,IAAA,CAAAyF,MAAA;YACAqD,MAAA,CAAA9I,IAAA,CAAA0F,MAAA;YACAoD,MAAA,CAAA9I,IAAA,CAAA2F,MAAA;YACAmD,MAAA,CAAA9I,IAAA,CAAAuF,MAAA,GAAA0D,MAAA,CAAAH,MAAA,CAAA9I,IAAA,CAAAM,SAAA;YACA,IAAAwI,MAAA,CAAA9I,IAAA,CAAA7D,QAAA;cACA2M,MAAA,CAAA9I,IAAA,CAAAc,MAAA;cACAgI,MAAA,CAAA9I,IAAA,CAAA7D,QAAA;YACA;UACA;UACA2M,MAAA,CAAA9I,IAAA,CAAAiH,SAAA;UACA,IAAA6B,MAAA,CAAA9I,IAAA,CAAA4F,YAAA,YAAAkD,MAAA,CAAA9I,IAAA,CAAA4F,YAAA,CAAApD,MAAA;YACAsG,MAAA,CAAA9I,IAAA,CAAA4F,YAAA,GAAAkD,MAAA,CAAA9I,IAAA,CAAA4F,YAAA,CAAAsD,QAAA;UACA;YACAJ,MAAA,CAAA9I,IAAA,CAAA4F,YAAA;UACA;UACA,IAAA/B,GAAA;YACAiF,MAAA,CAAA9I,IAAA,CAAA2E,SAAA;YACAmE,MAAA,CAAA9I,IAAA,CAAA7B,SAAA;UACA;UACA,IAAA2K,MAAA,CAAA9I,IAAA,CAAA2E,SAAA;YACA,IAAAwE,sBAAA,EAAAL,MAAA,CAAA9I,IAAA,EAAAyD,IAAA,WAAAU,QAAA;cACA2E,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAAjL,IAAA;cACAiL,MAAA,CAAAzL,IAAA;cACAyL,MAAA,CAAAtF,OAAA;YACA;UACA;YACA,IAAA8F,mBAAA,EAAAR,MAAA,CAAA9I,IAAA,EAAAyD,IAAA,WAAAU,QAAA;cACA2E,MAAA,CAAAM,MAAA,CAAAC,UAAA;cACAP,MAAA,CAAApE,KAAA;cACAoE,MAAA,CAAAtF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA+F,YAAA,WAAAA,aAAAhB,GAAA;MACA,IAAAiB,UAAA,GAAAjB,GAAA,CAAA5D,SAAA,SAAArH,GAAA;MACA,IAAAmM,KAAA;MACAA,KAAA,CAAAL,MAAA,CAAAM,OAAA,qBAAAF,UAAA,cAAA/F,IAAA;QACA,WAAAkG,mBAAA,EAAAH,UAAA,EAAA/F,IAAA,WAAAU,QAAA;UACAsF,KAAA,CAAAjG,OAAA;UACAiG,KAAA,CAAAL,MAAA,CAAAC,UAAA,UAAAG,UAAA,CAAAhH,MAAA,cAAA2B,QAAA,CAAApI,IAAA;QACA;MACA;IACA;IACA,aACA6N,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,8BAAAC,cAAA,CAAA9H,OAAA,MACA,KAAAlE,WAAA,oBAAAW,MAAA,CACA,IAAAsL,iBAAA,MAAAnD,IAAA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAA;MACA,KAAA1K,MAAA,CAAA1B,KAAA;MACA,KAAA0B,MAAA,CAAAzB,IAAA;IACA;IACA,aACAoM,cAAA,WAAAA,eAAA;MACA,KAAAJ,QAAA,yDAAApL,MAAA,KAAAsL,iBAAA,MAAAnD,IAAA;IACA;IACA;IACAsD,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAA/K,MAAA,CAAAC,WAAA;IACA;IACA;IACA+K,iBAAA,WAAAA,kBAAAnG,QAAA,EAAAiG,IAAA,EAAAC,QAAA;MACA,KAAA/K,MAAA,CAAAzB,IAAA;MACA,KAAAyB,MAAA,CAAAC,WAAA;MACA,KAAA8E,KAAA,CAAA/E,MAAA,CAAAiL,UAAA;MACA,KAAAC,QAAA,CAAAC,IAAA,CAAAtG,QAAA,CAAAuG,GAAA;MACA,IAAAvG,QAAA,CAAAuG,GAAA;QACA,KAAAb,QAAA;MACA;MACA,KAAArG,OAAA;IACA;IACA;IACAmH,cAAA,WAAAA,eAAA;MACA,KAAAtG,KAAA,CAAA/E,MAAA,CAAAsL,MAAA;IACA;IACAC,oBAAA,WAAAA,qBAAAhH,GAAA;MACA,IAAAA,GAAA,IAAAhC,SAAA;QACA,KAAA/D,WAAA,CAAAd,eAAA;QACA,KAAAtC,WAAA;MACA;IACA;IACAoQ,kBAAA,WAAAA,mBAAAC,IAAA;MACA,KAAAjN,WAAA,CAAAd,eAAA,GAAA+N,IAAA,CAAAhO,aAAA;MACA,KAAArC,WAAA,GAAAqQ,IAAA,CAAAC,gBAAA;MACA,KAAA7D,WAAA;IACA;IACA8D,oBAAA,WAAAA,qBAAApH,GAAA;MACA,KAAA/F,WAAA,CAAAiB,eAAA,GAAA8E,GAAA;MACA,KAAAsD,WAAA;IACA;IACA+D,YAAA,WAAAA,aAAArH,GAAA;MACA,KAAA/F,WAAA,CAAAkB,OAAA,GAAA6E,GAAA;MACA,KAAAsD,WAAA;IACA;IACAgE,eAAA,WAAAA,gBAAAtH,GAAA;MACA,KAAA/F,WAAA,CAAAQ,UAAA,GAAAuF,GAAA;MACA,KAAAsD,WAAA;IACA;IACAiE,wBAAA,WAAAA,yBAAAvH,GAAA;MACA,KAAA/F,WAAA,CAAAiH,mBAAA,GAAAlB,GAAA;MACA,KAAAsD,WAAA;IACA;IACAkE,iBAAA,WAAAA,kBAAAxH,GAAA;MACA,KAAA/F,WAAA,CAAAS,YAAA,GAAAsF,GAAA;MACA,KAAAsD,WAAA;IACA;IACAmE,qBAAA,WAAAA,sBAAAzH,GAAA;MACA,KAAA/F,WAAA,CAAAY,gBAAA,GAAAmF,GAAA;MACA,KAAAsD,WAAA;IACA;IACAoE,mBAAA,WAAAA,oBAAA1H,GAAA;MACA,KAAA/F,WAAA,CAAAa,cAAA,GAAAkF,GAAA;MACA,KAAAsD,WAAA;IACA;IACAqE,mBAAA,WAAAA,oBAAA3H,GAAA;MACA,KAAA/F,WAAA,CAAAc,cAAA,GAAAiF,GAAA;MACA,KAAAsD,WAAA;IACA;IACAsE,uBAAA,WAAAA,wBAAA5H,GAAA;MACA,KAAA/F,WAAA,CAAAgB,kBAAA,GAAA+E,GAAA;MACA,KAAAsD,WAAA;IACA;IACAuE,iBAAA,WAAAA,kBAAA7H,GAAA;MACA,KAAA/F,WAAA,CAAAM,YAAA,GAAAyF,GAAA;MACA,KAAAsD,WAAA;IACA;IACAwE,iBAAA,WAAAA,kBAAA9H,GAAA;MACA,KAAA7D,IAAA,CAAA6E,aAAA,GAAAhB,GAAA;IACA;IACA+H,2BAAA,WAAAA,4BAAAb,IAAA;MACA,KAAAjN,WAAA,CAAAO,UAAA,CAAAiE,IAAA,CAAAyI,IAAA,CAAArP,OAAA,CAAAwH,SAAA;MACA,KAAAiE,WAAA;IACA;IACA0E,6BAAA,WAAAA,8BAAAd,IAAA;MACA,KAAAjN,WAAA,CAAAO,UAAA,QAAAP,WAAA,CAAAO,UAAA,CAAAyN,MAAA,WAAArE,IAAA;QACA,OAAAA,IAAA,IAAAsD,IAAA,CAAArP,OAAA,CAAAwH,SAAA;MACA;MACA,KAAAiE,WAAA;IACA;IACA4E,0BAAA,WAAAA,2BAAA1J,KAAA;MACA,IAAAA,KAAA,CAAAG,MAAA;QACA,KAAA1E,WAAA,CAAAO,UAAA;QACA,KAAA8I,WAAA;MACA;IACA;IACA6E,oBAAA,WAAAA,qBAAAjB,IAAA;MACA,KAAAjN,WAAA,CAAAb,QAAA,GAAA8N,IAAA,CAAAkB,OAAA;MACA,KAAA9E,WAAA;IACA;IACA+E,aAAA,WAAAA,cAAArI,GAAA;MACA,IAAAA,GAAA,IAAAhC,SAAA;QACA,KAAA/D,WAAA,CAAAb,QAAA;QACA,KAAAkK,WAAA;MACA;IACA;IACAgF,qBAAA,WAAAA,sBAAApB,IAAA;MACA;MACA,KAAA/K,IAAA,CAAA9D,WAAA,GAAA6O,IAAA,CAAArP,OAAA,CAAA0Q,eAAA;IACA;IACAC,iBAAA,WAAAA,kBAAAhK,KAAA;MACA,IAAAA,KAAA,IAAAR,SAAA;QACA,KAAA7B,IAAA,CAAAkD,SAAA;MACA;IACA;IACAoJ,kBAAA,WAAAA,mBAAAzI,GAAA;MACA,KAAA7D,IAAA,CAAAhD,eAAA,GAAA6G,GAAA;IACA;IACA0I,gBAAA,WAAAA,iBAAA1I,GAAA;MACA,KAAA7D,IAAA,CAAAjD,aAAA,GAAA8G,GAAA;IACA;IACA2I,WAAA,WAAAA,YAAA3I,GAAA;MACA,KAAA7D,IAAA,CAAA7C,QAAA,GAAA0G,GAAA;IACA;IACA4I,YAAA,WAAAA,aAAA5I,GAAA;MACA,KAAA7D,IAAA,CAAAK,UAAA,GAAAwD,GAAA;IACA;IACA6I,YAAA,WAAAA,aAAA7I,GAAA;MACA,KAAA7D,IAAA,CAAA+E,mBAAA,GAAAlB,GAAA;IACA;IACA8I,cAAA,WAAAA,eAAA9I,GAAA;MACA,KAAA7D,IAAA,CAAAxB,KAAA,GAAAqF,GAAA;IACA;IACA+I,gBAAA,WAAAA,iBAAA/I,GAAA;MACA,KAAA7D,IAAA,CAAAgF,aAAA,GAAAnB,GAAA;IACA;IACAgJ,gBAAA,WAAAA,iBAAAhJ,GAAA;MACA,KAAA7D,IAAA,CAAAnB,iBAAA,GAAAgF,GAAA;IACA;IACAiJ,kBAAA,WAAAA,mBAAAjJ,GAAA;MACA,KAAA7D,IAAA,CAAAoF,iBAAA,GAAAvB,GAAA;IACA;IACAkJ,cAAA,WAAAA,eAAAlJ,GAAA;MACA,KAAA7D,IAAA,CAAAkF,YAAA,GAAArB,GAAA;IACA;IACAmJ,gBAAA,WAAAA,iBAAAnJ,GAAA;MACA,KAAA7D,IAAA,CAAA+F,eAAA,GAAAlC,GAAA;IACA;IACAoJ,kBAAA,WAAAA,mBAAApJ,GAAA;MACA,KAAA7D,IAAA,CAAAgG,iBAAA,GAAAnC,GAAA;IACA;IACAqJ,aAAA,WAAAA,cAAArJ,GAAA;MACA,KAAA7D,IAAA,CAAAmN,YAAA,GAAAtJ,GAAA;IACA;IACAuJ,SAAA,WAAAA,UAAAvJ,GAAA;MACA,KAAA7D,IAAA,CAAA7D,QAAA,GAAA0H,GAAA;IACA;IACAwJ,kBAAA,WAAAA,mBAAAxJ,GAAA;MACA,KAAA7D,IAAA,CAAAa,UAAA,GAAAgD,GAAA;IACA;IACAyJ,wBAAA,WAAAA,yBAAAvC,IAAA;MACA,KAAA/K,IAAA,CAAAvD,qBAAA,GAAAsO,IAAA,CAAA/C,aAAA,CAAAC,MAAA;IACA;IACAsF,6BAAA,WAAAA,8BAAA1J,GAAA;MACA,IAAAA,GAAA,IAAAhC,SAAA;QACA,KAAA7B,IAAA,CAAAvD,qBAAA;MACA;IACA;IACA+Q,eAAA,WAAAA,gBAAA3J,GAAA;MACA,KAAA7D,IAAA,CAAA5B,YAAA,GAAAyF,GAAA;IACA;IACA4J,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAA5Q,YAAA;MACA,KAAAsH,SAAA;QACAsJ,MAAA,CAAA5Q,YAAA;MACA;IACA;IACA6Q,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,SAAA7M,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAA8R,cAAA,CAAArL,MAAA,cAAAzB,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAA+R,SAAA,CAAAD,cAAA;QACAE,cAAA,CAAAC,QAAA,sBAAAvK,IAAA;UACAmK,MAAA,CAAAjR,YAAA,GAAAiR,MAAA,CAAA7M,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAA8R,cAAA;QACA;MACA;QACA,KAAAlR,YAAA,QAAAoE,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAA8R,cAAA;MACA;IACA;IACAlM,WAAA,WAAAA,YAAA;MAAA,IAAAsM,MAAA;MACA,SAAAlN,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAAmS,mBAAA,CAAA1L,MAAA,cAAAzB,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAA+R,SAAA,CAAAI,mBAAA;QACAH,cAAA,CAAAC,QAAA,+BAAAvK,IAAA;UACAwK,MAAA,CAAArR,WAAA,GAAAqR,MAAA,CAAAlN,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAAmS,mBAAA;QACA;MACA;QACA,KAAAtR,WAAA,QAAAmE,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAAmS,mBAAA;MACA;IACA;IACAxK,cAAA,WAAAA,eAAA;MAAA,IAAAyK,MAAA;MACA,SAAApN,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAAqS,qBAAA,CAAA5L,MAAA,cAAAzB,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAA+R,SAAA,CAAAM,qBAAA;QACAL,cAAA,CAAAC,QAAA,6BAAAvK,IAAA;UACA0K,MAAA,CAAAtR,cAAA,GAAAsR,MAAA,CAAApN,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAAqS,qBAAA;QACA;MACA;QACA,KAAAvR,cAAA,QAAAkE,MAAA,CAAAC,KAAA,CAAAjF,IAAA,CAAAqS,qBAAA;MACA;IACA;IACA,eACA5K,OAAA,WAAAA,QAAA;MAAA,IAAA6K,MAAA;MAAA,WAAAC,kBAAA,CAAAtM,OAAA,oBAAAuM,oBAAA,CAAAvM,OAAA,IAAAwM,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAvM,OAAA,IAAA0M,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAT,MAAA,CAAAlT,OAAA;cAAAyT,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAC,oBAAA,EAAAV,MAAA,CAAAvQ,WAAA,EAAA2F,IAAA,WAAAU,QAAA;gBACAkK,MAAA,CAAA1Q,WAAA,GAAAwG,QAAA,CAAAK,IAAA;gBACA6J,MAAA,CAAA3Q,KAAA,GAAAyG,QAAA,CAAAzG,KAAA,GAAAyG,QAAA,CAAAzG,KAAA;gBACA2Q,MAAA,CAAAlT,OAAA;cACA;YAAA;YAAA;cAAA,OAAAyT,QAAA,CAAAI,IAAA;UAAA;QAAA,GAAAP,OAAA;MAAA;IACA;IACAQ,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAA3G,GAAA,GAAA2G,IAAA,CAAA3G,GAAA;MACA,IAAAJ,IAAA,OAAAxB,eAAA,MAAAC,IAAA;MACA,IAAA3H,SAAA,OAAA0H,eAAA,EAAA4B,GAAA,CAAAtJ,SAAA;MACA,IAAAC,OAAA,OAAAyH,eAAA,EAAA4B,GAAA,CAAArJ,OAAA;MACA,IAAAD,SAAA,GAAAkJ,IAAA,GAAAjJ,OAAA;QACA;MACA;MACA,IAAAA,OAAA,GAAAiJ,IAAA;QACA;MACA;MACA,IAAAlJ,SAAA,GAAAkJ,IAAA;QACA;MACA;MACA;IACA;IACAgH,iBAAA,WAAAA,kBAAApE,IAAA;MACA,IAAAA,IAAA,CAAAxI,QAAA,KAAAwI,IAAA,CAAAxI,QAAA,CAAAC,MAAA;QACA,OAAAuI,IAAA,CAAAxI,QAAA;MACA;MACA,IAAA6M,CAAA;MACA,KAAArE,IAAA,CAAArP,OAAA,IAAAqP,IAAA,CAAArP,OAAA,CAAA2T,gBAAA,YAAAtE,IAAA,CAAArP,OAAA,CAAA4T,aAAA;QACAF,CAAA,GAAArE,IAAA,CAAAC,gBAAA,SAAAD,IAAA,CAAAwE,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAA1E,IAAA,CAAAC,gBAAA;MACA;QACAoE,CAAA,IAAArE,IAAA,CAAArP,OAAA,CAAA0Q,eAAA,WAAArB,IAAA,CAAArP,OAAA,CAAA0Q,eAAA,gBAAArB,IAAA,CAAArP,OAAA,CAAA2T,gBAAA,WAAAtE,IAAA,CAAArP,OAAA,CAAA2T,gBAAA,gBAAAtE,IAAA,CAAArP,OAAA,CAAA4T,aAAA,WAAAvE,IAAA,CAAArP,OAAA,CAAA4T,aAAA,eAAAE,iBAAA,CAAAC,YAAA,CAAA1E,IAAA,CAAArP,OAAA,CAAA2T,gBAAA,WAAAtE,IAAA,CAAArP,OAAA,CAAA2T,gBAAA;MACA;MACA;QACAK,EAAA,EAAA3E,IAAA,CAAAhO,aAAA;QACA4S,KAAA,EAAAP,CAAA;QACA7M,QAAA,EAAAwI,IAAA,CAAAxI;MACA;IACA;IACAqN,eAAA,WAAAA,gBAAA7E,IAAA;MACA,IAAAA,IAAA,CAAAxI,QAAA,KAAAwI,IAAA,CAAAxI,QAAA,CAAAC,MAAA;QACA,OAAAuI,IAAA,CAAAxI,QAAA;MACA;MACA,IAAA6M,CAAA;MACA,IAAArE,IAAA,CAAA8E,KAAA;QACA,IAAA9E,IAAA,CAAA8E,KAAA,CAAAC,oBAAA,YAAA/E,IAAA,CAAA8E,KAAA,CAAAE,oBAAA;UACA,IAAAhF,IAAA,CAAAiF,IAAA,CAAAC,aAAA;YACAb,CAAA,GAAArE,IAAA,CAAAiF,IAAA,CAAAC,aAAA,SAAAT,iBAAA,CAAAC,YAAA,CAAA1E,IAAA,CAAAiF,IAAA,CAAAC,aAAA;UACA;YACAb,CAAA,GAAArE,IAAA,CAAAmF,IAAA,CAAAC,aAAA,SAAAX,iBAAA,CAAAC,YAAA,CAAA1E,IAAA,CAAAmF,IAAA,CAAAC,aAAA;UACA;QACA;UACAf,CAAA,GAAArE,IAAA,CAAA8E,KAAA,CAAAO,SAAA,SAAArF,IAAA,CAAA8E,KAAA,CAAAC,oBAAA,GAAA/E,IAAA,CAAA8E,KAAA,CAAAE,oBAAA,SAAAhF,IAAA,CAAA8E,KAAA,CAAAQ,iBAAA,SAAAb,iBAAA,CAAAC,YAAA,CAAA1E,IAAA,CAAA8E,KAAA,CAAAC,oBAAA,GAAA/E,IAAA,CAAA8E,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAhF,IAAA,CAAAnK,MAAA;QACA;UACA8O,EAAA,EAAA3E,IAAA,CAAAnK,MAAA;UACA+O,KAAA,EAAAP,CAAA;UACA7M,QAAA,EAAAwI,IAAA,CAAAxI,QAAA;UACA+N,UAAA,EAAAvF,IAAA,CAAAkB,OAAA,YAAAlB,IAAA,CAAAxI,QAAA,IAAAV;QACA;MACA;QACA;UACA6N,EAAA,EAAA3E,IAAA,CAAAwF,MAAA;UACAZ,KAAA,EAAAP,CAAA;UACA7M,QAAA,EAAAwI,IAAA,CAAAxI,QAAA;UACA+N,UAAA,EAAAvF,IAAA,CAAAkB,OAAA,YAAAlB,IAAA,CAAAxI,QAAA,IAAAV;QACA;MACA;IACA;IACA2O,cAAA,WAAAA,eAAAzF,IAAA;MACA,IAAAqE,CAAA;MACA,IAAArE,IAAA,CAAA/C,aAAA;QACAoH,CAAA,IAAArE,IAAA,CAAA0F,iBAAA,WAAA1F,IAAA,CAAA0F,iBAAA,gBAAA1F,IAAA,CAAA2F,iBAAA,WAAA3F,IAAA,CAAA2F,iBAAA,gBAAA3F,IAAA,CAAA4F,cAAA,WAAA5F,IAAA,CAAA4F,cAAA,eAAAnB,iBAAA,CAAAC,YAAA,CAAA1E,IAAA,CAAA0F,iBAAA,GAAA1F,IAAA,CAAA2F,iBAAA;MACA;QACAtB,CAAA,IAAArE,IAAA,CAAA/C,aAAA,CAAA4I,aAAA,WAAA7F,IAAA,CAAA/C,aAAA,CAAA4I,aAAA,gBAAA7F,IAAA,CAAA/C,aAAA,CAAA6I,aAAA,WAAA9F,IAAA,CAAA/C,aAAA,CAAA6I,aAAA,gBAAA9F,IAAA,CAAA/C,aAAA,CAAA8I,UAAA,WAAA/F,IAAA,CAAA/C,aAAA,CAAA8I,UAAA,eAAAtB,iBAAA,CAAAC,YAAA,CAAA1E,IAAA,CAAA/C,aAAA,CAAA4I,aAAA,GAAA7F,IAAA,CAAA/C,aAAA,CAAA6I,aAAA;MACA;MACA;QACAnB,EAAA,EAAA3E,IAAA,CAAA7C,UAAA;QACAyH,KAAA,EAAAP;MACA;IACA;IACA2B,OAAA,WAAAA,QAAAxI,GAAA,EAAAyI,MAAA,EAAA7G,KAAA,GAEA;IACA8G,OAAA,WAAAA,QAAAlG,IAAA;MACA,KAAAhO,aAAA,GAAAgO,IAAA,CAAAhO,aAAA;MACA,SAAAL,eAAA;QAAA,IAAAwU,UAAA,OAAAnP,2BAAA,CAAAC,OAAA,EACA,KAAAtF,eAAA;UAAAyU,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAhP,CAAA,MAAAiP,MAAA,GAAAD,UAAA,CAAAzP,CAAA,IAAAU,IAAA;YAAA,IAAAD,CAAA,GAAAiP,MAAA,CAAA9O,KAAA;YACA,IAAAH,CAAA,CAAAnF,aAAA,IAAAgO,IAAA,CAAAhO,aAAA;cACA,KAAAkB,MAAA,GAAAiE,CAAA,CAAAjE,MAAA;YACA;YACA,IAAAiE,CAAA,CAAAK,QAAA;cAAA,IAAA6O,UAAA,OAAArP,2BAAA,CAAAC,OAAA,EACAE,CAAA,CAAAK,QAAA;gBAAA8O,MAAA;cAAA;gBAAA,KAAAD,UAAA,CAAAlP,CAAA,MAAAmP,MAAA,GAAAD,UAAA,CAAA3P,CAAA,IAAAU,IAAA;kBAAA,IAAAmP,EAAA,GAAAD,MAAA,CAAAhP,KAAA;kBACA,IAAAiP,EAAA,CAAAvU,aAAA,IAAAgO,IAAA,CAAAhO,aAAA;oBACA,KAAAkB,MAAA,GAAAiE,CAAA,CAAAjE,MAAA;kBACA;gBACA;cAAA,SAAA2E,GAAA;gBAAAwO,UAAA,CAAAvO,CAAA,CAAAD,GAAA;cAAA;gBAAAwO,UAAA,CAAAtO,CAAA;cAAA;YACA;UACA;QAAA,SAAAF,GAAA;UAAAsO,UAAA,CAAArO,CAAA,CAAAD,GAAA;QAAA;UAAAsO,UAAA,CAAApO,CAAA;QAAA;MACA;MACA,KAAA5E,iBAAA,CAAAlB,eAAA,GAAA+N,IAAA,CAAAhO,aAAA;MACA,KAAAwU,cAAA;IACA;IACAA,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,KAAAjR,SAAA,CAAAvC,QAAA,QAAAF,WAAA,CAAAE,QAAA;MACA,KAAAuC,SAAA,CAAAxC,OAAA,QAAAD,WAAA,CAAAC,OAAA;MACA,KAAAwC,SAAA,CAAAtC,MAAA,QAAAC,iBAAA,CAAAD,MAAA;MACA,KAAAsC,SAAA,CAAAvD,eAAA,QAAAD,aAAA;MACA,IAAA0U,uBAAA,OAAAlR,SAAA,EAAAkD,IAAA,WAAAU,QAAA;QACAqN,OAAA,CAAA7T,WAAA,GAAAwG,QAAA,CAAAK,IAAA;QACAgN,OAAA,CAAA9T,KAAA,GAAAyG,QAAA,CAAAzG,KAAA;QACA8T,OAAA,CAAArW,OAAA;MACA;IACA;IACA;IACAuW,cAAA,WAAAA,eAAA;MAAA,IAAAC,OAAA;MACA,IAAApR,SAAA;MACAA,SAAA,CAAAxD,aAAA,QAAAmB,iBAAA,CAAAnB,aAAA;MACAwD,SAAA,CAAAyG,eAAA;MACAzG,SAAA,CAAAqR,SAAA;MACArR,SAAA,CAAAsR,cAAA,QAAA9Q,MAAA,CAAAC,KAAA,CAAAwF,IAAA,CAAAC,GAAA;MAEAlG,SAAA,CAAA/B,KAAA,QAAAN,iBAAA,CAAAM,KAAA;MACA+B,SAAA,CAAA1B,iBAAA,QAAAX,iBAAA,CAAAW,iBAAA;MAEA,SAAAX,iBAAA,CAAAM,KAAA,iBAAAN,iBAAA,CAAAW,iBAAA;QACA,IAAAyK,mBAAA,EAAA/I,SAAA,EAAAkD,IAAA,WAAAU,QAAA;UACAwN,OAAA,CAAAnH,QAAA,CAAAsH,OAAA,CAAA3N,QAAA,CAAAuG,GAAA;QACA;MACA;MACA,SAAAxM,iBAAA,CAAAM,KAAA;QACA,KAAAgM,QAAA,CAAAuH,OAAA;MACA,gBAAA7T,iBAAA,CAAAW,iBAAA;QACA,KAAA2L,QAAA,CAAAuH,OAAA;MACA;IACA;IACAC,YAAA,WAAAA,aAAAnO,GAAA;MAAA,IAAAoO,OAAA;MAAA,WAAA3D,kBAAA,CAAAtM,OAAA,oBAAAuM,oBAAA,CAAAvM,OAAA,IAAAwM,IAAA,UAAA0D,SAAA;QAAA,WAAA3D,oBAAA,CAAAvM,OAAA,IAAA0M,IAAA,UAAAyD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvD,IAAA,GAAAuD,SAAA,CAAAtD,IAAA;YAAA;cACAmD,OAAA,CAAAI,KAAA,kBAAAJ,OAAA,CAAA/T,iBAAA,CAAAnB,aAAA,EAAA8G,GAAA,EAAAoO,OAAA,CAAAzR,MAAA;YAAA;YAAA;cAAA,OAAA4R,SAAA,CAAApD,IAAA;UAAA;QAAA,GAAAkD,QAAA;MAAA;IACA;EACA;AACA;AAAAI,OAAA,CAAAtQ,OAAA,GAAAuQ,QAAA"}]}