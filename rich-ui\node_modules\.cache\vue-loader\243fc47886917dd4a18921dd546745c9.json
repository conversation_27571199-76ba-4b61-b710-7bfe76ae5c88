{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\TopNav\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\TopNav\\index.vue", "mtime": 1754876882535}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/TopNav", "sourcesContent": ["<template>\r\n  <el-menu\r\n    :default-active=\"activeMenu\"\r\n    mode=\"horizontal\"\r\n    @select=\"handleSelect\"\r\n  >\r\n    <template v-for=\"(item, index) in topMenus\">\r\n      <el-menu-item v-if=\"index < visibleNumber\" :key=\"index\" :index=\"item.path\" :style=\"{'--theme': theme}\"\r\n      >\r\n        <svg-icon :icon-class=\"item.meta.icon\"/>\r\n        {{ item.meta.title }}\r\n      </el-menu-item\r\n      >\r\n    </template>\r\n\r\n    <!-- 顶部菜单超出数量折叠 -->\r\n    <el-submenu v-if=\"topMenus.length > visibleNumber\" :style=\"{'--theme': theme}\" index=\"more\">\r\n      <template slot=\"title\">更多菜单</template>\r\n      <template v-for=\"(item, index) in topMenus\">\r\n        <el-menu-item\r\n          v-if=\"index >= visibleNumber\"\r\n          :key=\"index\"\r\n          :index=\"item.path\"\r\n        >\r\n          <svg-icon :icon-class=\"item.meta.icon\"/>\r\n          {{ item.meta.title }}\r\n        </el-menu-item\r\n        >\r\n      </template>\r\n    </el-submenu>\r\n  </el-menu>\r\n</template>\r\n\r\n<script>\r\nimport {constantRoutes} from \"@/router\";\r\n\r\n// 隐藏侧边栏路由\r\nconst hideList = ['/index', '/user/profile'];\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 顶部栏初始数\r\n      visibleNumber: 5,\r\n      // 当前激活菜单的 index\r\n      currentIndex: undefined\r\n    };\r\n  },\r\n  computed: {\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    },\r\n    // 顶部显示菜单\r\n    topMenus() {\r\n      let topMenus = [];\r\n      this.routers.map((menu) => {\r\n        if (menu.hidden != true) {\r\n          // 兼容顶部栏一级菜单内部跳转\r\n          if (menu.path == \"/\") {\r\n            topMenus.push(menu.children[0]);\r\n          } else {\r\n            topMenus.push(menu);\r\n          }\r\n        }\r\n      });\r\n      return topMenus;\r\n    },\r\n    // 所有的路由信息\r\n    routers() {\r\n      return this.$store.state.permission.topbarRouters;\r\n    },\r\n    // 设置子路由\r\n    childrenMenus() {\r\n      var childrenMenus = [];\r\n      this.routers.map((router) => {\r\n        for (var item in router.children) {\r\n          if (router.children[item].parentPath == undefined) {\r\n            if (router.path == \"/\") {\r\n              router.children[item].path = \"/\" + router.children[item].path;\r\n            } else {\r\n              if (!this.ishttp(router.children[item].path)) {\r\n                router.children[item].path = router.path + \"/\" + router.children[item].path;\r\n              }\r\n            }\r\n            router.children[item].parentPath = router.path;\r\n          }\r\n          childrenMenus.push(router.children[item]);\r\n        }\r\n      });\r\n      return constantRoutes.concat(childrenMenus);\r\n    },\r\n    // 默认激活的菜单\r\n    activeMenu() {\r\n      const path = this.$route.path;\r\n      let activePath = path;\r\n      if (path != undefined && path.lastIndexOf(\"/\") > 0 && hideList.indexOf(path) == -1) {\r\n        const tmpPath = path.substring(1, path.length);\r\n        activePath = \"/\" + tmpPath.substring(0, tmpPath.indexOf(\"/\"));\r\n        if (!this.$route.meta.link) {\r\n          this.$store.dispatch('app/toggleSideBarHide', false);\r\n        }\r\n      } else if (!this.$route.children) {\r\n        activePath = path;\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      }\r\n      this.activeRoutes(activePath);\r\n      return activePath;\r\n    },\r\n  },\r\n  beforeMount() {\r\n    window.addEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.setVisibleNumber)\r\n  },\r\n  mounted() {\r\n    this.setVisibleNumber();\r\n  },\r\n  methods: {\r\n    // 根据宽度计算设置显示栏数\r\n    setVisibleNumber() {\r\n      const width = document.body.getBoundingClientRect().width / 3;\r\n      this.visibleNumber = parseInt(width / 85);\r\n    },\r\n    // 菜单选择事件\r\n    handleSelect(key, keyPath) {\r\n      this.currentIndex = key;\r\n      const route = this.routers.find(item => item.path == key);\r\n      if (this.ishttp(key)) {\r\n        // http(s):// 路径新窗口打开\r\n        window.open(key, \"_blank\");\r\n      } else if (!route || !route.children) {\r\n        // 没有子路由路径内部打开\r\n        const routeMenu = this.childrenMenus.find(item => item.path === key);\r\n        if (routeMenu && routeMenu.query) {\r\n          let query = JSON.parse(routeMenu.query);\r\n          this.$router.push({ path: key, query: query });\r\n        } else {\r\n          this.$router.push({ path: key });\r\n        }\r\n        this.$store.dispatch('app/toggleSideBarHide', true);\r\n      } else {\r\n        // 显示左侧联动菜单\r\n        this.activeRoutes(key);\r\n        this.$store.dispatch('app/toggleSideBarHide', false);\r\n      }\r\n    },\r\n    // 当前激活的路由\r\n    activeRoutes(key) {\r\n      var routes = [];\r\n      if (this.childrenMenus && this.childrenMenus.length > 0) {\r\n        this.childrenMenus.map((item) => {\r\n          if (key == item.parentPath || (key == \"index\" && \"\" == item.path)) {\r\n            routes.push(item);\r\n          }\r\n        });\r\n      }\r\n      if (routes.length > 0) {\r\n        this.$store.commit(\"SET_SIDEBAR_ROUTERS\", routes);\r\n      }\r\n    },\r\n    ishttp(url) {\r\n      return url.indexOf('http://') != -1 || url.indexOf('https://') != -1\r\n    }\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.topmenu-container.el-menu--horizontal > .el-menu-item {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n\r\n.topmenu-container.el-menu--horizontal > .el-menu-item.is-active, .el-menu--horizontal > .el-submenu.is-active .el-submenu__title {\r\n  border-bottom: 2px solid #{'var(--theme)'} !important;\r\n  color: #303133;\r\n}\r\n\r\n/* submenu item */\r\n.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title {\r\n  float: left;\r\n  height: 50px !important;\r\n  line-height: 50px !important;\r\n  color: #999093 !important;\r\n  padding: 0 5px !important;\r\n  margin: 0 10px !important;\r\n}\r\n</style>\r\n"]}]}