{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\UrgencyDegreeSelect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\UrgencyDegreeSelect\\index.vue", "mtime": 1718100178797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAnVXJnZW5jeURlZ3JlZVNlbGVjdCcsCiAgcHJvcHM6IFsncGFzcyddLAogIHdhdGNoOiB7CiAgICBwYXNzOiBmdW5jdGlvbiBwYXNzKHZhbCkgewogICAgICB0aGlzLnZhbHVlID0gdmFsOwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIG9wdGlvbnM6IFt7CiAgICAgICAgdmFsdWU6ICcwJywKICAgICAgICBsYWJlbDogJ+mihOWumicKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAnMScsCiAgICAgICAgbGFiZWw6ICflvZPlpKknCiAgICAgIH0sIHsKICAgICAgICB2YWx1ZTogJzInLAogICAgICAgIGxhYmVsOiAn5bi46KeEJwogICAgICB9LCB7CiAgICAgICAgdmFsdWU6ICczJywKICAgICAgICBsYWJlbDogJ+e0p+aApScKICAgICAgfSwgewogICAgICAgIHZhbHVlOiAnNCcsCiAgICAgICAgbGFiZWw6ICfnq4vljbMnCiAgICAgIH1dLAogICAgICB2YWx1ZTogJycKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVTZWxlY3Q6IGZ1bmN0aW9uIGhhbmRsZVNlbGVjdCgpIHsKICAgICAgdGhpcy4kZW1pdCgndXJnZW5jeURlZ3JlZScsIHRoaXMudmFsdWUpOwogICAgfQogIH0KfTsKLyoNCjAgICDpooTlrpogICDmj5DliY3pooTlrprvvIzkuIDoiKwxLTLlpKnlhoXlrozmiJDljbPlj68NCjEgICDlvZPlpKkgICDlvZPlpKnkuIvnj63liY3lrozmiJDljbPlj68NCjIgICDluLjop4QgICDmjInpg6jlsLHnj63vvIzmjInpu5jorqTnmoTmoIflh4bmtYHnqIvmjqjov5sNCjMgICDntKfmgKUgICDljYrlsI/ml7blhoXlhbPms6gNCjQgICDnq4vljbMgICDpnIDopoHnq4vljbPlpITnkIbnmoTkuovliqENCiogICovCmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "names": ["name", "props", "watch", "pass", "val", "value", "data", "options", "label", "methods", "handleSelect", "$emit", "exports", "default", "_default"], "sources": ["src/components/UrgencyDegreeSelect/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-select v-model=\"value\" filterable placeholder=\"紧急程度\" @change=\"handleSelect\">\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.value\"\r\n        :label=\"item.label\"\r\n        :value=\"item.value\"\r\n\r\n      >\r\n      </el-option>\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'UrgencyDegreeSelect',\r\n  props: ['pass'],\r\n  watch:{\r\n    pass:function(val){\r\n      this.value=val\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      options: [{\r\n        value: '0',\r\n        label: '预定'\r\n      }, {\r\n        value: '1',\r\n        label: '当天'\r\n      }, {\r\n        value: '2',\r\n        label: '常规'\r\n      }, {\r\n        value: '3',\r\n        label: '紧急'\r\n      }, {\r\n        value: '4',\r\n        label: '立即'\r\n      }],\r\n      value: ''\r\n    }\r\n  },\r\n  methods: {\r\n    handleSelect() {\r\n      this.$emit('urgencyDegree', this.value)\r\n    }\r\n  }\r\n}\r\n/*\r\n0   预定   提前预定，一般1-2天内完成即可\r\n1   当天   当天下班前完成即可\r\n2   常规   按部就班，按默认的标准流程推进\r\n3   紧急   半小时内关注\r\n4   立即   需要立即处理的事务\r\n*  */\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;eAgBA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAAC,KAAA,GAAAD,GAAA;IACA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;QACAF,KAAA;QACAG,KAAA;MACA;QACAH,KAAA;QACAG,KAAA;MACA;QACAH,KAAA;QACAG,KAAA;MACA;QACAH,KAAA;QACAG,KAAA;MACA;QACAH,KAAA;QACAG,KAAA;MACA;MACAH,KAAA;IACA;EACA;EACAI,OAAA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,uBAAAN,KAAA;IACA;EACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANAO,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}