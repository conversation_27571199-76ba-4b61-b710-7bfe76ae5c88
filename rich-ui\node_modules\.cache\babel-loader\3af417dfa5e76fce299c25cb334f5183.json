{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\communication.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\communication.vue", "mtime": 1718100178852}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiY29tbXVuaWNhdGlvbiIsCiAgcHJvcHM6IFsnc2NvcGUnXSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgc2l6ZTogdGhpcy4kc3RvcmUuc3RhdGUuYXBwLnNpemUgfHwgJ21pbmknCiAgICB9OwogIH0sCiAgbWV0aG9kczogewogICAgY2hlY2tDb21tdW5pY2F0ZVJlY29yZDogZnVuY3Rpb24gY2hlY2tDb21tdW5pY2F0ZVJlY29yZCh2YWwpIHsKICAgICAgdGhpcy4kZW1pdCgncmV0dXJuJywgewogICAgICAgIGtleTogJ2NvbW11bmljYXRpb24nLAogICAgICAgIHZhbHVlOiB2YWwKICAgICAgfSk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["name", "props", "data", "size", "$store", "state", "app", "methods", "checkCommunicateRecord", "val", "$emit", "key", "value", "exports", "default", "_default"], "sources": ["src/views/system/company/communication.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <!--    <el-tooltip v-if=\"scope.row.communication != null && scope.row.communication.content != null\" placement=\"top\">\r\n          <div slot=\"content\">\r\n            <h6 style=\"margin: 0\">\r\n              {{\r\n                scope.row.communication != null ? (scope.row.communication.content != null ? scope.row.communication.content : '') : ''\r\n              }}\r\n            </h6>\r\n          </div>\r\n          <div>\r\n            <el-row>\r\n              <el-col :span=\"18\">\r\n                <h6 style=\"margin: 0;float: left\">\r\n                  {{\r\n                    scope.row.communication != null ? (scope.row.communication.rsStaff.staffLocalName != null ? scope.row.communication.rsStaff.staffLocalName : '') : ''\r\n                  }}\r\n                </h6>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-button v-hasPermi=\"['system:communication:list']\" :size=\"size\"\r\n                           style=\"padding: 0;display: flex;margin: auto;float: right\" type=\"text\"\r\n                           @click=\"checkCommunicateRecord(scope.row)\">\r\n                  {{ '[' + scope.row.communicationNum + ']' }}\r\n                </el-button>\r\n              </el-col>\r\n            </el-row>\r\n            <h6 style=\"margin: 0;color: darkgray\">\r\n              {{\r\n                scope.row.communication != null ? (scope.row.communication.communicationDatetime != null ? scope.row.communication.communicationDatetime.toString().substring(0, 10) : '') : ''\r\n              }}\r\n            </h6>\r\n          </div>\r\n        </el-tooltip>\r\n        <el-row v-else>\r\n          <el-col :span=\"18\">\r\n            <h6 style=\"margin: 0;float: left\">\r\n              {{\r\n                scope.row.communication != null ? scope.row.communication.rsStaff != null ? (scope.row.communication.rsStaff.staffLocalName != null ? scope.row.communication.rsStaff.staffLocalName : '') : '' : ''\r\n              }}\r\n            </h6>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <el-button v-hasPermi=\"['system:communication:list']\" :size=\"size\"\r\n                       style=\"padding: 0;display: flex;margin: auto;float: right\"\r\n                       type=\"text\"\r\n                       @click=\"checkCommunicateRecord(scope.row)\">\r\n              {{ '[' + scope.row.communicationNum + ']' }}\r\n            </el-button>\r\n          </el-col>\r\n          <h6 style=\"margin: 0;color: darkgray\">\r\n            {{\r\n              scope.row.communication != null ? (scope.row.communication.communicationDatetime != null ? scope.row.communication.communicationDatetime.toString().substring(0, 10) : '') : ''\r\n            }}\r\n          </h6>\r\n        </el-row>-->\r\n    <el-button v-hasPermi=\"['system:communication:list']\" :size=\"size\"\r\n               style=\"padding: 0;display: flex;margin: auto;float: right\"\r\n               type=\"text\"\r\n               @click=\"checkCommunicateRecord(scope.row)\"\r\n    >\r\n      {{ '[···]' }}\r\n    </el-button>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n  name: \"communication\",\r\n  props: ['scope'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini',\r\n    }\r\n  },\r\n  methods: {\r\n    checkCommunicateRecord(val) {\r\n      this.$emit('return', {key: 'communication', value: val})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n</style>;\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAoEA;EACAA,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;IACA;EACA;EACAI,OAAA;IACAC,sBAAA,WAAAA,uBAAAC,GAAA;MACA,KAAAC,KAAA;QAAAC,GAAA;QAAAC,KAAA,EAAAH;MAAA;IACA;EACA;AACA;AAAAI,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}