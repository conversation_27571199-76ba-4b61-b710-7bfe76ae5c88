{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\commoninfotype\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\commoninfotype\\index.vue", "mtime": 1754876882577}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRDb21tb25pbmZvdHlwZSwNCiAgY2hhbmdlU3RhdHVzLA0KICBkZWxDb21tb25pbmZvdHlwZSwNCiAgZ2V0Q29tbW9uaW5mb3R5cGUsDQogIGxpc3RDb21tb25pbmZvdHlwZSwNCiAgdXBkYXRlQ29tbW9uaW5mb3R5cGUNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL2NvbW1vbmluZm90eXBlIjsNCmltcG9ydCB7bGlzdERvY30gZnJvbSAiQC9hcGkvc3lzdGVtL2RvYyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkNvbW1vbmluZm90eXBlIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0xlZnQ6IDMsDQogICAgICBzaG93UmlnaHQ6IDIxLA0KICAgICAgZG9jTGlzdDpbXSwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOmAmueUqOS/oeaBr+exu+Wei+ihqOagvOaVsOaNrg0KICAgICAgY29tbW9uaW5mb3R5cGVMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgaW5mb1R5cGVRdWVyeTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczoge30NCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgIHNob3dTZWFyY2gobikgew0KICAgICAgaWYgKG4gPT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxDQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAzDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0DQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAwDQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICBsaXN0RG9jKHtwYWdlTnVtOiAxLCBwYWdlU2l6ZTogMTAwfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLmRvY0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgIH0pOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoumAmueUqOS/oeaBr+exu+Wei+WIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdENvbW1vbmluZm90eXBlKHRoaXMucXVlcnlQYXJhbXMpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmNvbW1vbmluZm90eXBlTGlzdCA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAiaW5mb1R5cGVJZCIpOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgaW5mb1R5cGVJZDogbnVsbCwNCiAgICAgICAgaW5mb1R5cGVTaG9ydE5hbWU6IG51bGwsDQogICAgICAgIGluZm9UeXBlTG9jYWxOYW1lOiBudWxsLA0KICAgICAgICBpbmZvVHlwZUVuTmFtZTogbnVsbCwNCiAgICAgICAgb3JkZXJOdW06IG51bGwsDQogICAgICAgIHN0YXR1czogIjAiLA0KICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgdXBkYXRlVGltZTogbnVsbCwNCiAgICAgICAgZGVsZXRlQnk6IG51bGwsDQogICAgICAgIGRlbGV0ZVRpbWU6IG51bGwsDQogICAgICAgIGRlbGV0ZVN0YXR1czogIjAiDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5pbmZvVHlwZUlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoICE9IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOmAmueUqOS/oeaBr+exu+WeiyI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3QgaW5mb1R5cGVJZCA9IHJvdy5pbmZvVHlwZUlkIHx8IHRoaXMuaWRzDQogICAgICBnZXRDb21tb25pbmZvdHlwZShpbmZvVHlwZUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnpgJrnlKjkv6Hmga/nsbvlnosiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW5mb1R5cGVJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVDb21tb25pbmZvdHlwZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZENvbW1vbmluZm90eXBlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGluZm9UeXBlSWRzID0gcm93LmluZm9UeXBlSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTpgJrnlKjkv6Hmga/nsbvlnovnvJblj7fkuLoiJyArIGluZm9UeXBlSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nLCAn5o+Q56S6Jywge2N1c3RvbUNsYXNzOiAnbW9kYWwtY29uZmlybSd9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbENvbW1vbmluZm90eXBlKGluZm9UeXBlSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdzeXN0ZW0vY29tbW9uaW5mb3R5cGUvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgY29tbW9uaW5mb3R5cGVfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7DQogICAgICBsZXQgdGV4dCA9IHJvdy5zdGF0dXMgPT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIjsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgSInICsgdGV4dCArICciIicgKyByb3cuaW5mb1R5cGVMb2NhbE5hbWUgKyAnIuWQl++8nycsICfmj5DnpLonLCB7Y3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJ30pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gY2hhbmdlU3RhdHVzKHJvdy5pbmZvVHlwZUlkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7DQogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09ICIwIiA/ICIxIiA6ICIwIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgZ2V0RG9jVHlwZUlkcyh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5kb2NJZHMgPSB2YWwNCiAgICB9LA0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6IA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/commoninfotype", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"infoTypeQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.infoTypeQuery\"\r\n              clearable\r\n              placeholder=\"中英文简称\"\r\n              style=\"width: 100%\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:commoninfotype:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:commoninfotype:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"commoninfotypeList\"\r\n                  :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n                  row-key=\"infoTypeId\"\r\n                  @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"left\" label=\"类别名称\" prop=\"infoTypeShortName\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.infoTypeShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.infoTypeLocalName }}</a>\r\n              {{ scope.row.infoTypeEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"文件类型\" prop=\"docType\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"center\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"48\"/>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:commoninfotype:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:commoninfotype:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改通用信息类型对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item label=\"简称\" prop=\"infoTypeShortName\">\r\n          <el-input v-model=\"form.infoTypeShortName\" placeholder=\"简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"infoTypeLocalName\">\r\n          <el-input v-model=\"form.infoTypeLocalName\" placeholder=\"中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"infoTypeEnName\">\r\n          <el-input v-model=\"form.infoTypeEnName\" placeholder=\"英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"文件类型\" prop=\"docIds\">\r\n          <el-select v-model=\"form.docId\" placeholder=\"进度分类\" style=\"width: 100%\" multiple>\r\n            <el-option\r\n              v-for=\"dict in docList\"\r\n              :key=\"dict.docId\"\r\n              :label=\"dict.docLocalName\"\r\n              :value=\"dict.docId\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" placeholder=\"排序\" style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCommoninfotype,\r\n  changeStatus,\r\n  delCommoninfotype,\r\n  getCommoninfotype,\r\n  listCommoninfotype,\r\n  updateCommoninfotype\r\n} from \"@/api/system/commoninfotype\";\r\nimport {listDoc} from \"@/api/system/doc\";\r\n\r\nexport default {\r\n  name: \"Commoninfotype\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      docList:[],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 通用信息类型表格数据\r\n      commoninfotypeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        infoTypeQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    listDoc({pageNum: 1, pageSize: 100}).then(response => {\r\n      this.docList = response.rows;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询通用信息类型列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCommoninfotype(this.queryParams).then(response => {\r\n        this.commoninfotypeList = this.handleTree(response.data, \"infoTypeId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        infoTypeId: null,\r\n        infoTypeShortName: null,\r\n        infoTypeLocalName: null,\r\n        infoTypeEnName: null,\r\n        orderNum: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.infoTypeId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加通用信息类型\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const infoTypeId = row.infoTypeId || this.ids\r\n      getCommoninfotype(infoTypeId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改通用信息类型\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.infoTypeId != null) {\r\n            updateCommoninfotype(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCommoninfotype(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const infoTypeIds = row.infoTypeId || this.ids;\r\n      this.$confirm('是否确认删除通用信息类型编号为\"' + infoTypeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCommoninfotype(infoTypeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/commoninfotype/export', {\r\n        ...this.queryParams\r\n      }, `commoninfotype_${new Date().getTime()}.xlsx`)\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.infoTypeLocalName + '\"吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.infoTypeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    getDocTypeIds(val) {\r\n      this.form.docIds = val\r\n    },\r\n  }\r\n}\r\n</script>\r\n"]}]}