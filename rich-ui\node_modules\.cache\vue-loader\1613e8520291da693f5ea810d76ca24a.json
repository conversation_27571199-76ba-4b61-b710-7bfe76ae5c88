{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1754876882543}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["SidebarItem.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SidebarItem.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div v-if=\"!item.hidden\">\r\n    <template\r\n      v-if=\"hasOneShowingChild(item.children,item) && (!onlyOneChild.children||onlyOneChild.noShowingChildren)&&!item.alwaysShow\">\r\n      <app-link v-if=\"onlyOneChild.meta\" :to=\"resolvePath(onlyOneChild.path, onlyOneChild.query)\">\r\n        <el-menu-item :class=\"{'submenu-title-noDropdown':!isNest}\" :index=\"resolvePath(onlyOneChild.path)\">\r\n          <item :badge=\"onlyOneChild.meta.badge\" :icon=\"onlyOneChild.meta.icon||(item.meta&&item.meta.icon)\"\r\n                :title=\"onlyOneChild.meta.title\"/>\r\n        </el-menu-item>\r\n      </app-link>\r\n    </template>\r\n\r\n    <el-submenu v-else ref=\"subMenu\" :index=\"resolvePath(item.path)\" popper-append-to-body>\r\n      <template #title>\r\n        <item\r\n          v-if=\"item.meta\"\r\n          :badge=\"item.meta.badge\"\r\n          :icon=\"item.meta && item.meta.icon\"\r\n          :is-dot=\"item.meta.isDot\"\r\n          :title=\"item.meta.title\"\r\n        />\r\n      </template>\r\n      <sidebar-item\r\n        v-for=\"child in item.children\"\r\n        :key=\"child.path\"\r\n        :base-path=\"resolvePath(child.path)\"\r\n        :is-nest=\"true\"\r\n        :item=\"child\"\r\n        class=\"nest-menu\"\r\n      />\r\n    </el-submenu>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport path from 'path'\r\nimport {isExternal} from '@/utils/validate'\r\nimport Item from './Item'\r\nimport AppLink from './Link'\r\nimport FixiOSBug from './FixiOSBug'\r\n\r\nexport default {\r\n  name: 'SidebarItem',\r\n  components: {Item, AppLink},\r\n  mixins: [FixiOSBug],\r\n  props: {\r\n    // route object\r\n    item: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    isNest: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    basePath: {\r\n      type: String,\r\n      default: ''\r\n    }\r\n  },\r\n  data() {\r\n    this.onlyOneChild = null\r\n    return {}\r\n  },\r\n  methods: {\r\n    hasOneShowingChild(children = [], parent) {\r\n      if (!children) {\r\n        children = [];\r\n      }\r\n      const showingChildren = children.filter(item => {\r\n        if (item.hidden) {\r\n          return false\r\n        } else {\r\n          // Temp set(will be used if only has one showing child)\r\n          this.onlyOneChild = item\r\n          return true\r\n        }\r\n      })\r\n\r\n      // When there is only one child router, the child router is displayed by default\r\n      if (showingChildren.length == 1) {\r\n        return true\r\n      }\r\n\r\n      // Show parent if there are no child router to display\r\n      if (showingChildren.length == 0) {\r\n        this.onlyOneChild = {...parent, path: '', noShowingChildren: true}\r\n        return true\r\n      }\r\n\r\n      return false\r\n    },\r\n    resolvePath(routePath, routeQuery) {\r\n      if (isExternal(routePath)) {\r\n        return routePath\r\n      }\r\n      if (isExternal(this.basePath)) {\r\n        return this.basePath\r\n      }\r\n      if (routeQuery) {\r\n        let query = JSON.parse(routeQuery);\r\n        return {path: path.resolve(this.basePath, routePath), query: query}\r\n      }\r\n      return path.resolve(this.basePath, routePath)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}