{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue?vue&type=style&index=0&id=9d64d45e&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue", "mtime": 1754881964226}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCkBpbXBvcnQgJ0AvYXNzZXRzL3N0eWxlcy9vcC1kb2N1bWVudC5zY3NzJzsNCg=="}, {"version": 3, "sources": ["BillOfLadingInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAovBA", "file": "BillOfLadingInfo.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div>\r\n    <!--title-->\r\n    <el-row>\r\n      <el-col :span=\"18\">\r\n        <div class=\"service-bar\" style=\"display: flex;margin-top: 10px;margin-bottom: 10px;width: 100%\">\r\n          <a :class=\"{'el-icon-arrow-down':visible,'el-icon-arrow-right':!visible}\"/>\r\n          <div style=\"width:150px;display: flex\">\r\n            <h3 style=\"margin: 0;width: 250px;text-align: left\" @click=\"toggleVisible\">提单信息</h3>\r\n            <el-button style=\"margin-left: 10px;\" type=\"text\" @click=\"$emit('openChargeSelect', rsClientMessage)\">\r\n              [DN...]\r\n            </el-button>\r\n          </div>\r\n\r\n          <el-button type=\"primary\" size=\"mini\" style=\"margin-left: 10px;\" @click=\"profitOpen=true\">利润</el-button>\r\n\r\n          <el-col v-if=\"auditInfo\"\r\n                  :span=\"15\" style=\"display: flex\"\r\n          >\r\n            <div v-hasPermi=\"['system:booking:opapproval','system:rct:opapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnOpConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'op')\"\r\n              >操作确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ opConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ opConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:salesapproval','system:rct:salesapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnSalesConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'sales')\"\r\n              >业务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ salesConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ salesConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:clientapproval','system:rct:clientapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnClientConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'client')\"\r\n              >客户确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ clientConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ clientConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:financeapproval','system:rct:financeapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isAccountConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'account', rsClientMessage.rsChargeList)\"\r\n              >财务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ accountConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ accountConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n\r\n          <div style=\"margin-left: auto\">\r\n            <el-popover\r\n              v-for=\"(item,index) in fileOptions\" :key=\"index\"\r\n              placement=\"top\" trigger=\"click\" width=\"100\"\r\n            >\r\n              <el-button v-for=\"(item2,index) in item.templateList\" :key=\"index\"\r\n                         @click=\"handleFileAction(item.link, item2)\"\r\n              >{{ item2 }}\r\n              </el-button>\r\n              <a slot=\"reference\" style=\"color: blue;padding: 0;margin-left: 10px\" target=\"_blank\"\r\n              >[{{ item.file }}]</a>\r\n            </el-popover>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!--content-->\r\n    <transition name=\"fade\">\r\n      <el-row v-if=\"visible\" :gutter=\"10\" style=\"margin-bottom:15px;display:-webkit-box\">\r\n        <!--主表信息-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"branchInfo\" :span=\"18\">\r\n            <el-table :data=\"bookingMessageList\" border @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"50\"\r\n              />\r\n              <el-table-column label=\"MB/L No\" prop=\"mBlNo\"/>\r\n              <el-table-column label=\"HB/L No\" prop=\"hBlNo\"/>\r\n              <el-table-column label=\"发货人\" prop=\"bookingShipper\"/>\r\n              <el-table-column label=\"收货人\" prop=\"bookingConsignee\"/>\r\n              <el-table-column label=\"通知人\" prop=\"bookingNotifyParty\"/>\r\n              <el-table-column label=\"代理\" prop=\"bookingAgent\"/>\r\n              <el-table-column label=\"柜号\" prop=\"containerNo\"/>\r\n              <el-table-column label=\"封号\" prop=\"sealNo\"/>\r\n              <el-table-column label=\"柜型\" prop=\"containerType\"/>\r\n              <el-table-column label=\"唛头\" prop=\"shippingMark\"/>\r\n              <el-table-column label=\"件数\" prop=\"packageQuantity\"/>\r\n              <el-table-column label=\"货描\" prop=\"goodsDescription\"/>\r\n              <el-table-column label=\"体积\" prop=\"goodsVolume\"/>\r\n              <el-table-column label=\"重量\" prop=\"grossWeight\"/>\r\n              <el-table-column label=\"提单类型\" prop=\"blTypeCode\"/>\r\n              <el-table-column label=\"出单方式\" prop=\"blFormCode\"/>\r\n              <el-table-column label=\"交单方式\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <tree-select :class=\"'disable-form'\" :disabled=\"true\"\r\n                               :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"scope.row.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"scope.row.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" @click=\"handleBookingMessageUpdate(scope.row)\">修改</el-button>\r\n                  <el-button style=\"color: red\" type=\"text\"\r\n                             @click=\"deleteBookingMessage(scope.row)\"\r\n                  >删除\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!--弹出层-->\r\n            <el-dialog\r\n              v-dialogDrag\r\n              v-dialogDragWidth\r\n              :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n              :show-close=\"false\" :title=\"bookingMessageTitle\" @close=\"closeBookingMessage\"\r\n              :visible.sync=\"openBookingMessage\" append-to-body width=\"30%\"\r\n            >\r\n              <el-form ref=\"bookingMessageForm\" :model=\"bookingMessageForm\" class=\"edit\" label-width=\"80px\"\r\n                       style=\"\"\r\n              >\r\n                <div v-if=\"bookingMessageForm.blTypeCode==='MBL'\">\r\n                  <el-form-item label=\"提单号码\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <div v-else>\r\n                  <el-form-item label=\"MB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"HB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.hBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <el-form-item label=\"发货人\">\r\n                  <template slot=\"label\">\r\n                    <div>发货人</div>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('handleAddCommon', 'release')\">[↗]\r\n                    </el-button>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('openReleaseUsed')\">[...]</el-button>\r\n                  </template>\r\n                  <el-input v-model=\"bookingMessageForm.bookingShipper\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"收货人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingConsignee\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"通知人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingNotifyParty\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"代理\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingAgent\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"启运港\">\r\n                  <el-input v-model=\"bookingMessageForm.polName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"卸货港\">\r\n                  <el-input v-model=\"bookingMessageForm.podName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"目的港\">\r\n                  <el-input v-model=\"bookingMessageForm.destinationPort\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"柜号\">\r\n                  <el-input v-model=\"bookingMessageForm.containerNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"封号\">\r\n                  <el-input v-model=\"bookingMessageForm.sealNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"柜型\">\r\n                  <el-input v-model=\"bookingMessageForm.containerType\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"唛头\">\r\n                  <el-input v-model=\"bookingMessageForm.shippingMark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"件数\">\r\n                  <el-input v-model=\"bookingMessageForm.packageQuantity\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"件数\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"货描\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsDescription\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"重量\">\r\n                  <el-input v-model=\"bookingMessageForm.grossWeight\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"重量\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"体积\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsVolume\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"体积\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\">\r\n                  <el-input v-model=\"bookingMessageForm.blRemark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单地\">\r\n                  <location-select :load-options=\"psaBookingSelectData.locationOptions\" :no-parent=\"true\"\r\n                                   :pass=\"bookingMessageForm.polIds\" :placeholder=\"'启运港'\"\r\n                                   @returnData=\"bookingMessageForm.city=$event.locationEnShortName\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"开船日期\">\r\n                  <el-date-picker\r\n                    v-model=\"bookingMessageForm.onBoardDate\"\r\n                    placeholder=\"选择日期\"\r\n                    type=\"date\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"付款方式\">\r\n                  <el-select v-model=\"bookingMessageForm.payWay\" placeholder=\"请选择\">\r\n                    <el-option\r\n                      v-for=\"item in payWayOptions\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"提单类型\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blTypeCode\"\r\n                               :placeholder=\"'提单类型'\" :type=\"'blType'\"\r\n                               @return=\"bookingMessageForm.blTypeCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blFormCode\"\r\n                               :placeholder=\"'出单方式'\" :type=\"'blForm'\"\r\n                               @return=\"bookingMessageForm.blFormCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"交单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"bookingMessageForm.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n              <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"bookingMessageConfirm\">确 定</el-button>\r\n                <el-button size=\"mini\" @click=\"closeBookingMessage\">取 消</el-button>\r\n              </div>\r\n            </el-dialog>\r\n\r\n            <el-button :disabled=\"psaVerify || disabled\"\r\n                       style=\"padding: 0\"\r\n                       type=\"text\"\r\n                       @click=\"addBookingMessage\"\r\n            >[＋]\r\n            </el-button>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--物流进度-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n            <el-form-item label=\"进度需求\" prop=\"goodsNameSummary\"/>\r\n            <div>\r\n              <logistics-progress :disabled=\"rsClientMessageFormDisable || disabled || psaVerify\"\r\n                                  :logistics-progress-data=\"rsClientMessage.rsOpLogList\"\r\n                                  :open-logistics-progress-list=\"true\"\r\n                                  @deleteItem=\"deleteLogisticsItem\"\r\n                                  @return=\"updateLogisticsProgress\"\r\n              />\r\n            </div>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--费用列表-->\r\n        <!--分帐单列表-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.5\">\r\n            <debit-note-list\r\n              :is-receivable=\"1\"\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"rsClientMessage.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :rct-id=\"form.rctId\"\r\n              @return=\"$emit('rsClientMessageDebitNote', $event)\"\r\n              @addDebitNote=\"handleAddDebitNote\"\r\n              @copyFreight=\"handleCopyFreight\"\r\n              @deleteAll=\"handleDeleteAll\"\r\n              @deleteItem=\"rsClientMessage.rsDebitNoteList = rsClientMessage.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @selection-change=\"handleSelectionChange\"\r\n            />\r\n          </el-col>\r\n        </transition>\r\n      </el-row>\r\n    </transition>\r\n\r\n    <!-- 利润对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"profitOpen\"\r\n      title=\"单票利润\"\r\n      width=\"30%\"\r\n      @open=\"openProfit\"\r\n    >\r\n      <el-table\r\n        :data=\"profitTableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column\r\n          label=\"货币\"\r\n          prop=\"currencyCode\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"应收\"\r\n          prop=\"receivable\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"应付\"\r\n          prop=\"payable\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"不含税利润\" prop=\"profit\"\r\n          style=\"color: #0d0dfd\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税应收\"\r\n          prop=\"receivableTax\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税应付\"\r\n          prop=\"payableTax\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税利润\"\r\n          prop=\"profitTax\"\r\n        >\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-row>\r\n        <el-col :span=\"5\">\r\n          <el-form-item label=\"折合币种\" prop=\"rctOpDate\">\r\n            <el-select v-model=\"currencyCode\" @change=\"profitCount(currencyCode)\">\r\n              <el-option label=\"RMB\" value=\"RMB\"/>\r\n              <el-option label=\"USD\" value=\"USD\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"7\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div style=\"color: #0d0dfd\">不含税利润</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"profit\" placeholder=\"不含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n        <el-col :span=\"7\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div>含税利润</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"profitTax\" placeholder=\"含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n        <el-col :span=\"5\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div>折算汇率</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"exchangeRate\" placeholder=\"含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LogisticsProgress from \"../logisticsProgress\"\r\nimport ChargeList from \"../chargeList\"\r\nimport TreeSelect from \"@/components/TreeSelect\"\r\nimport LocationSelect from \"@/components/LocationSelect\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport currency from \"currency.js\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"BillOfLadingInfo\",\r\n  components: {\r\n    DebitNoteList,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    TreeSelect,\r\n    LocationSelect\r\n  },\r\n  props: {\r\n    bookingMessageForm: {\r\n      type: Object,\r\n      required: true,\r\n      default: null\r\n    },\r\n    openBookingMessage: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    bookingMessageStatus: {\r\n      type: String,\r\n      default: \"<UNK>\"\r\n    },\r\n    bookingMessageList: {\r\n      type: Array,\r\n      default: []\r\n    },\r\n    rsClientMessage: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    form: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    rsClientMessageReceivableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      visible: true,\r\n      bookingMessageTitle: \"提单信息\",\r\n      psaBookingSelectData: {\r\n        locationOptions: []\r\n      },\r\n      rsClientMessageFormDisable: false,\r\n      fileOptions: [\r\n        {file: \"操作单\", link: \"getOpBill\", templateList: [\"整柜\", \"散货\", \"空运\", \"其他\"]},\r\n        {file: \"提单\", link: \"getBillOfLading\", templateList: [\"套打提单\", \"电放提单\"]},\r\n        {\r\n          file: \"费用清单\",\r\n          link: \"getChargeListBill\",\r\n          templateList: [\"CN-广州瑞旗[招行USD+工行RMB]\", \"CN-广州瑞旗[USD->RMB]\", \"EN-广州瑞旗[招行USD]\", \"EN-广州瑞旗[RMB->USD]\", \"EN- 瑞旗香港账户[HSBC RMB->USD]\", \"EN- 香港瑞旗[HSBC]\", \"CN-广州正泽[招行USD+RMB]\", \"CN-广州正泽[USD->RMB]\"]\r\n        }\r\n      ],\r\n      payWayOptions: [\r\n        {label: \"预付\", value: \"FREIGHTP REPAID\"},\r\n        {label: \"到付\", value: \"FREIGHTP COLLECT\"}\r\n      ],\r\n      profitOpen: false,\r\n      profitTableData: [],\r\n      currencyCode: \"RMB\",\r\n      profit: 0,\r\n      profitTax: 0,\r\n      exchangeRate: 1\r\n    }\r\n  },\r\n  computed: {\r\n    rsClientServiceInstance() {\r\n      return this.rsClientMessage || {}\r\n    },\r\n    opConfirmedName() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedName || \"\"\r\n    },\r\n    opConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedDate || \"\"\r\n    },\r\n    salesConfirmedName() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedName || \"\"\r\n    },\r\n    salesConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedDate || \"\"\r\n    },\r\n    clientConfirmedName() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedName || \"\"\r\n    },\r\n    clientConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedDate || \"\"\r\n    },\r\n    accountConfirmedName() {\r\n      return this.rsClientServiceInstance.accountConfirmedName || \"\"\r\n    },\r\n    accountConfirmedDate() {\r\n      return this.rsClientServiceInstance.accountConfirmedDate || \"\"\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化时的操作\r\n  },\r\n  methods: {\r\n    handleChargeSelectionChange(selection) {\r\n      this.$emit(\"selection-charge-change\", selection)\r\n    },\r\n    handleAddDebitNote() {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 0\r\n      row.rsChargeList = []\r\n      this.$emit(\"addDebitNote\", row, this.rsClientMessage)\r\n    },\r\n    toggleVisible() {\r\n      this.visible = !this.visible\r\n    },\r\n    handleFileAction(methodName, templateType) {\r\n      this.$emit(methodName, templateType)\r\n    },\r\n    handleSelectionChange(selection) {\r\n      // 只能勾选一个\r\n      if (selection.length > 1) {\r\n        this.$message.warning(\"只能勾选一个账单\")\r\n        return\r\n      }\r\n      this.$emit(\"handleReceiveSelected\", selection)\r\n    },\r\n    handleBookingMessageUpdate(row) {\r\n      this.$emit(\"handleBookingMessageUpdate\", row)\r\n    },\r\n    addBookingMessage() {\r\n      this.$emit(\"handleAddBookingMessage\", this.bookingMessageForm)\r\n    },\r\n    bookingMessageConfirm() {\r\n      // 将操作通过事件发送给父组件\r\n      this.$emit(\"bookingMessageConfirm\", this.bookingMessageForm)\r\n\r\n    },\r\n    closeBookingMessage() {\r\n      this.$emit(\"closeBookingMessage\")\r\n    },\r\n    deleteBookingMessage(row) {\r\n      // 通过事件发送给父组件\r\n      this.$emit(\"deleteBookingMessage\", row)\r\n    },\r\n    deleteLogisticsItem(item) {\r\n      if (this.rsClientMessage && this.rsClientMessage.rsOpLogList) {\r\n        this.rsClientMessage.rsOpLogList = this.rsClientMessage.rsOpLogList.filter(log => log !== item)\r\n      }\r\n    },\r\n    updateLogisticsProgress(data) {\r\n      if (this.rsClientMessage) {\r\n        this.rsClientMessage.rsOpLogList = data\r\n      }\r\n    },\r\n    openProfit() {\r\n      this.profitTableData = []\r\n\r\n      let RMB = {}\r\n      RMB.currencyCode = \"RMB\"\r\n      RMB.receivable = this.rsClientMessageReceivableRMB\r\n      RMB.payable = this.rsClientMessagePayableRMB\r\n      // 不含税利润\r\n      RMB.profit = this.rsClientMessageProfitRMB\r\n      // 含税应收\r\n      RMB.receivableTax = this.rsClientMessageReceivableTaxRMB\r\n      // 含税应付\r\n      RMB.payableTax = this.rsClientMessagePayableTaxRMB\r\n      // 含税利润\r\n      RMB.profitTax = this.rsClientMessageProfitTaxRMB\r\n\r\n      let USD = {}\r\n      USD.currencyCode = \"USD\"\r\n      USD.receivable = this.rsClientMessageReceivableUSD\r\n      USD.payable = this.rsClientMessagePayableUSD\r\n      USD.profit = this.rsClientMessageProfitUSD\r\n      USD.receivableTax = this.rsClientMessageReceivableTaxUSD\r\n      USD.payableTax = this.rsClientMessagePayableTaxUSD\r\n      USD.profitTax = this.rsClientMessageProfitTaxUSD\r\n\r\n      this.profitTableData.push(RMB)\r\n      this.profitTableData.push(USD)\r\n\r\n      this.profitCount(\"RMB\")\r\n    },\r\n    profitCount(type) {\r\n      let exchangeRate\r\n      for (const a of this.$store.state.data.exchangeRateList) {\r\n        if (this.form.podEta) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(this.form.podEta)\r\n            && parseTime(this.form.podEta) <= parseTime(a.validTo)\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n        if (!exchangeRate) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(new Date())\r\n            && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      }\r\n      this.exchangeRate = exchangeRate\r\n\r\n      if (type === \"RMB\") {\r\n        // 都折算成人民币\r\n        this.profit = currency(this.rsClientMessageProfitUSD).multiply(exchangeRate).add(this.rsClientMessageProfitRMB).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxUSD).multiply(exchangeRate).add(this.rsClientMessageProfitTaxRMB).value\r\n      } else {\r\n        this.profit = currency(this.rsClientMessageProfitRMB).divide(exchangeRate).add(this.rsClientMessageProfitUSD).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxRMB).divide(exchangeRate).add(this.rsClientMessageProfitTaxUSD).value\r\n      }\r\n    },\r\n    handleCopyFreight(charge) {\r\n      this.$emit(\"copyFreight\", charge)\r\n    },\r\n    handleDeleteAll() {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    handleDeleteItem(charge) {\r\n      this.$emit(\"deleteItem\", charge)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document.scss';\r\n</style>\r\n"]}]}