package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsDebitNote;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsDebitNoteService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 分账单Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/system/debitnote")
public class RsDebitNoteController extends BaseController {
    @Autowired
    private RsDebitNoteService rsDebitNoteService;

    /**
     * 查询分账单列表
     */
    @PreAuthorize("@ss.hasPermi('system:debitnote:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsDebitNote rsDebitNote) {
        startPage();
        List<RsDebitNote> list = rsDebitNoteService.selectRsDebitNoteList(rsDebitNote);
        return getDataTable(list);
    }

    /**
     * 导出分账单列表
     */
    @PreAuthorize("@ss.hasPermi('system:debitnote:export')")
    @Log(title = "分账单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsDebitNote rsDebitNote) {
        List<RsDebitNote> list = rsDebitNoteService.selectRsDebitNoteList(rsDebitNote);
        ExcelUtil<RsDebitNote> util = new ExcelUtil<RsDebitNote>(RsDebitNote.class);
        util.exportExcel(response, list, "分账单数据");
    }

    /**
     * 获取分账单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:debitnote:query')")
    @GetMapping(value = "/{debitNoteId}")
    public AjaxResult getInfo(@PathVariable("debitNoteId") Long debitNoteId) {
        return AjaxResult.success(rsDebitNoteService.selectRsDebitNoteByDebitNoteId(debitNoteId));
    }

    /**
     * 新增分账单
     */
    @PreAuthorize("@ss.hasPermi('system:debitnote:add')")
    @Log(title = "分账单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsDebitNote rsDebitNote) {
        return toAjax(rsDebitNoteService.insertRsDebitNote(rsDebitNote));
    }

    /**
     * 修改分账单
     */
    @PreAuthorize("@ss.hasPermi('system:debitnote:edit')")
    @Log(title = "分账单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsDebitNote rsDebitNote) {
        return toAjax(rsDebitNoteService.updateRsDebitNote(rsDebitNote));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:debitnote:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsDebitNote rsDebitNote) {
        rsDebitNote.setUpdateBy(getUserId());
        return toAjax(rsDebitNoteService.changeStatus(rsDebitNote));
    }

    /**
     * 删除分账单
     */
    @PreAuthorize("@ss.hasPermi('system:debitnote:remove')")
    @Log(title = "分账单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{debitNoteIds}")
    public AjaxResult remove(@PathVariable Long[] debitNoteIds) {
        return toAjax(rsDebitNoteService.deleteRsDebitNoteByDebitNoteIds(debitNoteIds));
    }
}
