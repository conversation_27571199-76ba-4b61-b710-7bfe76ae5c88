{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue", "mtime": 1754881964226}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgTG9naXN0aWNzUHJvZ3Jlc3MgZnJvbSAiLi4vbG9naXN0aWNzUHJvZ3Jlc3MiDQppbXBvcnQgQ2hhcmdlTGlzdCBmcm9tICIuLi9jaGFyZ2VMaXN0Ig0KaW1wb3J0IFRyZWVTZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL1RyZWVTZWxlY3QiDQppbXBvcnQgTG9jYXRpb25TZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL0xvY2F0aW9uU2VsZWN0Ig0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIkAvdXRpbHMvcmljaCINCmltcG9ydCBjdXJyZW5jeSBmcm9tICJjdXJyZW5jeS5qcyINCmltcG9ydCBEZWJpdE5vdGVMaXN0IGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2RlYml0Tm9kZUxpc3QudnVlIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJCaWxsT2ZMYWRpbmdJbmZvIiwNCiAgY29tcG9uZW50czogew0KICAgIERlYml0Tm90ZUxpc3QsDQogICAgTG9naXN0aWNzUHJvZ3Jlc3MsDQogICAgQ2hhcmdlTGlzdCwNCiAgICBUcmVlU2VsZWN0LA0KICAgIExvY2F0aW9uU2VsZWN0DQogIH0sDQogIHByb3BzOiB7DQogICAgYm9va2luZ01lc3NhZ2VGb3JtOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgIGRlZmF1bHQ6IG51bGwNCiAgICB9LA0KICAgIG9wZW5Cb29raW5nTWVzc2FnZTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBib29raW5nTWVzc2FnZVN0YXR1czogew0KICAgICAgdHlwZTogU3RyaW5nLA0KICAgICAgZGVmYXVsdDogIjxVTks+Ig0KICAgIH0sDQogICAgYm9va2luZ01lc3NhZ2VMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6IFtdDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2U6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIHJlcXVpcmVkOiB0cnVlDQogICAgfSwNCiAgICBmb3JtOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICByZXF1aXJlZDogdHJ1ZQ0KICAgIH0sDQogICAgZGlzYWJsZWQ6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgcHNhVmVyaWZ5OiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIGF1ZGl0SW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGJyYW5jaEluZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICBsb2dpc3RpY3NJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgY2hhcmdlSW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGNvbXBhbnlMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlUk1COiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVVNEOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VQYXlhYmxlUk1COiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VQYXlhYmxlVVNEOiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VQcm9maXRSTUI6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZVByb2ZpdFVTRDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0sDQogICAgcnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVRheFJNQjogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0sDQogICAgcnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVRheFVTRDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0sDQogICAgcnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFJNQjogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0sDQogICAgcnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFVTRDogew0KICAgICAgdHlwZTogTnVtYmVyLA0KICAgICAgZGVmYXVsdDogMA0KICAgIH0sDQogICAgcnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1COiB7DQogICAgICB0eXBlOiBOdW1iZXIsDQogICAgICBkZWZhdWx0OiAwDQogICAgfSwNCiAgICByc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0Q6IHsNCiAgICAgIHR5cGU6IE51bWJlciwNCiAgICAgIGRlZmF1bHQ6IDANCiAgICB9DQogIH0sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHZpc2libGU6IHRydWUsDQogICAgICBib29raW5nTWVzc2FnZVRpdGxlOiAi5o+Q5Y2V5L+h5oGvIiwNCiAgICAgIHBzYUJvb2tpbmdTZWxlY3REYXRhOiB7DQogICAgICAgIGxvY2F0aW9uT3B0aW9uczogW10NCiAgICAgIH0sDQogICAgICByc0NsaWVudE1lc3NhZ2VGb3JtRGlzYWJsZTogZmFsc2UsDQogICAgICBmaWxlT3B0aW9uczogWw0KICAgICAgICB7ZmlsZTogIuaTjeS9nOWNlSIsIGxpbms6ICJnZXRPcEJpbGwiLCB0ZW1wbGF0ZUxpc3Q6IFsi5pW05p+cIiwgIuaVo+i0pyIsICLnqbrov5AiLCAi5YW25LuWIl19LA0KICAgICAgICB7ZmlsZTogIuaPkOWNlSIsIGxpbms6ICJnZXRCaWxsT2ZMYWRpbmciLCB0ZW1wbGF0ZUxpc3Q6IFsi5aWX5omT5o+Q5Y2VIiwgIueUteaUvuaPkOWNlSJdfSwNCiAgICAgICAgew0KICAgICAgICAgIGZpbGU6ICLotLnnlKjmuIXljZUiLA0KICAgICAgICAgIGxpbms6ICJnZXRDaGFyZ2VMaXN0QmlsbCIsDQogICAgICAgICAgdGVtcGxhdGVMaXN0OiBbIkNOLeW5v+W3nueRnuaXl1vmi5vooYxVU0Qr5bel6KGMUk1CXSIsICJDTi3lub/lt57nkZ7ml5dbVVNELT5STUJdIiwgIkVOLeW5v+W3nueRnuaXl1vmi5vooYxVU0RdIiwgIkVOLeW5v+W3nueRnuaXl1tSTUItPlVTRF0iLCAiRU4tIOeRnuaXl+mmmea4r+i0puaIt1tIU0JDIFJNQi0+VVNEXSIsICJFTi0g6aaZ5riv55Ge5peXW0hTQkNdIiwgIkNOLeW5v+W3nuato+azvVvmi5vooYxVU0QrUk1CXSIsICJDTi3lub/lt57mraPms71bVVNELT5STUJdIl0NCiAgICAgICAgfQ0KICAgICAgXSwNCiAgICAgIHBheVdheU9wdGlvbnM6IFsNCiAgICAgICAge2xhYmVsOiAi6aKE5LuYIiwgdmFsdWU6ICJGUkVJR0hUUCBSRVBBSUQifSwNCiAgICAgICAge2xhYmVsOiAi5Yiw5LuYIiwgdmFsdWU6ICJGUkVJR0hUUCBDT0xMRUNUIn0NCiAgICAgIF0sDQogICAgICBwcm9maXRPcGVuOiBmYWxzZSwNCiAgICAgIHByb2ZpdFRhYmxlRGF0YTogW10sDQogICAgICBjdXJyZW5jeUNvZGU6ICJSTUIiLA0KICAgICAgcHJvZml0OiAwLA0KICAgICAgcHJvZml0VGF4OiAwLA0KICAgICAgZXhjaGFuZ2VSYXRlOiAxDQogICAgfQ0KICB9LA0KICBjb21wdXRlZDogew0KICAgIHJzQ2xpZW50U2VydmljZUluc3RhbmNlKCkgew0KICAgICAgcmV0dXJuIHRoaXMucnNDbGllbnRNZXNzYWdlIHx8IHt9DQogICAgfSwNCiAgICBvcENvbmZpcm1lZE5hbWUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5kbk9wQ29uZmlybWVkTmFtZSB8fCAiIg0KICAgIH0sDQogICAgb3BDb25maXJtZWREYXRlKCkgew0KICAgICAgcmV0dXJuIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuZG5PcENvbmZpcm1lZERhdGUgfHwgIiINCiAgICB9LA0KICAgIHNhbGVzQ29uZmlybWVkTmFtZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmRuU2FsZXNDb25maXJtZWROYW1lIHx8ICIiDQogICAgfSwNCiAgICBzYWxlc0NvbmZpcm1lZERhdGUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5kblNhbGVzQ29uZmlybWVkRGF0ZSB8fCAiIg0KICAgIH0sDQogICAgY2xpZW50Q29uZmlybWVkTmFtZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmRuQ2xpZW50Q29uZmlybWVkTmFtZSB8fCAiIg0KICAgIH0sDQogICAgY2xpZW50Q29uZmlybWVkRGF0ZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmRuQ2xpZW50Q29uZmlybWVkRGF0ZSB8fCAiIg0KICAgIH0sDQogICAgYWNjb3VudENvbmZpcm1lZE5hbWUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5hY2NvdW50Q29uZmlybWVkTmFtZSB8fCAiIg0KICAgIH0sDQogICAgYWNjb3VudENvbmZpcm1lZERhdGUoKSB7DQogICAgICByZXR1cm4gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5hY2NvdW50Q29uZmlybWVkRGF0ZSB8fCAiIg0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICAvLyDliJ3lp4vljJbml7bnmoTmk43kvZwNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZUNoYXJnZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuJGVtaXQoInNlbGVjdGlvbi1jaGFyZ2UtY2hhbmdlIiwgc2VsZWN0aW9uKQ0KICAgIH0sDQogICAgaGFuZGxlQWRkRGViaXROb3RlKCkgew0KICAgICAgbGV0IHJvdyA9IHt9DQogICAgICByb3cuc3FkUmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIHJvdy5yY3RJZCA9IHRoaXMuZm9ybS5yY3RJZA0KICAgICAgcm93LmlzUmVjaWV2aW5nT3JQYXlpbmcgPSAwDQogICAgICByb3cucnNDaGFyZ2VMaXN0ID0gW10NCiAgICAgIHRoaXMuJGVtaXQoImFkZERlYml0Tm90ZSIsIHJvdywgdGhpcy5yc0NsaWVudE1lc3NhZ2UpDQogICAgfSwNCiAgICB0b2dnbGVWaXNpYmxlKCkgew0KICAgICAgdGhpcy52aXNpYmxlID0gIXRoaXMudmlzaWJsZQ0KICAgIH0sDQogICAgaGFuZGxlRmlsZUFjdGlvbihtZXRob2ROYW1lLCB0ZW1wbGF0ZVR5cGUpIHsNCiAgICAgIHRoaXMuJGVtaXQobWV0aG9kTmFtZSwgdGVtcGxhdGVUeXBlKQ0KICAgIH0sDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgLy8g5Y+q6IO95Yu+6YCJ5LiA5LiqDQogICAgICBpZiAoc2VsZWN0aW9uLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLlj6rog73li77pgInkuIDkuKrotKbljZUiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuJGVtaXQoImhhbmRsZVJlY2VpdmVTZWxlY3RlZCIsIHNlbGVjdGlvbikNCiAgICB9LA0KICAgIGhhbmRsZUJvb2tpbmdNZXNzYWdlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy4kZW1pdCgiaGFuZGxlQm9va2luZ01lc3NhZ2VVcGRhdGUiLCByb3cpDQogICAgfSwNCiAgICBhZGRCb29raW5nTWVzc2FnZSgpIHsNCiAgICAgIHRoaXMuJGVtaXQoImhhbmRsZUFkZEJvb2tpbmdNZXNzYWdlIiwgdGhpcy5ib29raW5nTWVzc2FnZUZvcm0pDQogICAgfSwNCiAgICBib29raW5nTWVzc2FnZUNvbmZpcm0oKSB7DQogICAgICAvLyDlsIbmk43kvZzpgJrov4fkuovku7blj5HpgIHnu5nniLbnu4Tku7YNCiAgICAgIHRoaXMuJGVtaXQoImJvb2tpbmdNZXNzYWdlQ29uZmlybSIsIHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtKQ0KDQogICAgfSwNCiAgICBjbG9zZUJvb2tpbmdNZXNzYWdlKCkgew0KICAgICAgdGhpcy4kZW1pdCgiY2xvc2VCb29raW5nTWVzc2FnZSIpDQogICAgfSwNCiAgICBkZWxldGVCb29raW5nTWVzc2FnZShyb3cpIHsNCiAgICAgIC8vIOmAmui/h+S6i+S7tuWPkemAgee7meeItue7hOS7tg0KICAgICAgdGhpcy4kZW1pdCgiZGVsZXRlQm9va2luZ01lc3NhZ2UiLCByb3cpDQogICAgfSwNCiAgICBkZWxldGVMb2dpc3RpY3NJdGVtKGl0ZW0pIHsNCiAgICAgIGlmICh0aGlzLnJzQ2xpZW50TWVzc2FnZSAmJiB0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc09wTG9nTGlzdCkgew0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc09wTG9nTGlzdCA9IHRoaXMucnNDbGllbnRNZXNzYWdlLnJzT3BMb2dMaXN0LmZpbHRlcihsb2cgPT4gbG9nICE9PSBpdGVtKQ0KICAgICAgfQ0KICAgIH0sDQogICAgdXBkYXRlTG9naXN0aWNzUHJvZ3Jlc3MoZGF0YSkgew0KICAgICAgaWYgKHRoaXMucnNDbGllbnRNZXNzYWdlKSB7DQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlLnJzT3BMb2dMaXN0ID0gZGF0YQ0KICAgICAgfQ0KICAgIH0sDQogICAgb3BlblByb2ZpdCgpIHsNCiAgICAgIHRoaXMucHJvZml0VGFibGVEYXRhID0gW10NCg0KICAgICAgbGV0IFJNQiA9IHt9DQogICAgICBSTUIuY3VycmVuY3lDb2RlID0gIlJNQiINCiAgICAgIFJNQi5yZWNlaXZhYmxlID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlUk1CDQogICAgICBSTUIucGF5YWJsZSA9IHRoaXMucnNDbGllbnRNZXNzYWdlUGF5YWJsZVJNQg0KICAgICAgLy8g5LiN5ZCr56iO5Yip5ramDQogICAgICBSTUIucHJvZml0ID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRSTUINCiAgICAgIC8vIOWQq+eojuW6lOaUtg0KICAgICAgUk1CLnJlY2VpdmFibGVUYXggPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhSTUINCiAgICAgIC8vIOWQq+eojuW6lOS7mA0KICAgICAgUk1CLnBheWFibGVUYXggPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhSTUINCiAgICAgIC8vIOWQq+eojuWIqea2pg0KICAgICAgUk1CLnByb2ZpdFRheCA9IHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1CDQoNCiAgICAgIGxldCBVU0QgPSB7fQ0KICAgICAgVVNELmN1cnJlbmN5Q29kZSA9ICJVU0QiDQogICAgICBVU0QucmVjZWl2YWJsZSA9IHRoaXMucnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVVTRA0KICAgICAgVVNELnBheWFibGUgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVVU0QNCiAgICAgIFVTRC5wcm9maXQgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFVTRA0KICAgICAgVVNELnJlY2VpdmFibGVUYXggPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhVU0QNCiAgICAgIFVTRC5wYXlhYmxlVGF4ID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4VVNEDQogICAgICBVU0QucHJvZml0VGF4ID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QNCg0KICAgICAgdGhpcy5wcm9maXRUYWJsZURhdGEucHVzaChSTUIpDQogICAgICB0aGlzLnByb2ZpdFRhYmxlRGF0YS5wdXNoKFVTRCkNCg0KICAgICAgdGhpcy5wcm9maXRDb3VudCgiUk1CIikNCiAgICB9LA0KICAgIHByb2ZpdENvdW50KHR5cGUpIHsNCiAgICAgIGxldCBleGNoYW5nZVJhdGUNCiAgICAgIGZvciAoY29uc3QgYSBvZiB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmV4Y2hhbmdlUmF0ZUxpc3QpIHsNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5wb2RFdGEpIHsNCiAgICAgICAgICBpZiAoYS5sb2NhbEN1cnJlbmN5ID09PSAiUk1CIg0KICAgICAgICAgICAgJiYgIlVTRCIgPT0gYS5vdmVyc2VhQ3VycmVuY3kNCiAgICAgICAgICAgICYmIHBhcnNlVGltZShhLnZhbGlkRnJvbSkgPD0gcGFyc2VUaW1lKHRoaXMuZm9ybS5wb2RFdGEpDQogICAgICAgICAgICAmJiBwYXJzZVRpbWUodGhpcy5mb3JtLnBvZEV0YSkgPD0gcGFyc2VUaW1lKGEudmFsaWRUbykNCiAgICAgICAgICApIHsNCiAgICAgICAgICAgIGV4Y2hhbmdlUmF0ZSA9IGN1cnJlbmN5KGEuc2V0dGxlUmF0ZSkuZGl2aWRlKGEuYmFzZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYgKCFleGNoYW5nZVJhdGUpIHsNCiAgICAgICAgICBpZiAoYS5sb2NhbEN1cnJlbmN5ID09PSAiUk1CIg0KICAgICAgICAgICAgJiYgIlVTRCIgPT0gYS5vdmVyc2VhQ3VycmVuY3kNCiAgICAgICAgICAgICYmIHBhcnNlVGltZShhLnZhbGlkRnJvbSkgPD0gcGFyc2VUaW1lKG5ldyBEYXRlKCkpDQogICAgICAgICAgICAmJiBwYXJzZVRpbWUobmV3IERhdGUoKSkgPD0gcGFyc2VUaW1lKGEudmFsaWRUbykpIHsNCiAgICAgICAgICAgIGV4Y2hhbmdlUmF0ZSA9IGN1cnJlbmN5KGEuc2V0dGxlUmF0ZSkuZGl2aWRlKGEuYmFzZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuZXhjaGFuZ2VSYXRlID0gZXhjaGFuZ2VSYXRlDQoNCiAgICAgIGlmICh0eXBlID09PSAiUk1CIikgew0KICAgICAgICAvLyDpg73mipjnrpfmiJDkurrmsJHluIENCiAgICAgICAgdGhpcy5wcm9maXQgPSBjdXJyZW5jeSh0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFVTRCkubXVsdGlwbHkoZXhjaGFuZ2VSYXRlKS5hZGQodGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRSTUIpLnZhbHVlDQogICAgICAgIHRoaXMucHJvZml0VGF4ID0gY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QpLm11bHRpcGx5KGV4Y2hhbmdlUmF0ZSkuYWRkKHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1CKS52YWx1ZQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5wcm9maXQgPSBjdXJyZW5jeSh0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFJNQikuZGl2aWRlKGV4Y2hhbmdlUmF0ZSkuYWRkKHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VVNEKS52YWx1ZQ0KICAgICAgICB0aGlzLnByb2ZpdFRheCA9IGN1cnJlbmN5KHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1CKS5kaXZpZGUoZXhjaGFuZ2VSYXRlKS5hZGQodGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QpLnZhbHVlDQogICAgICB9DQogICAgfSwNCiAgICBoYW5kbGVDb3B5RnJlaWdodChjaGFyZ2UpIHsNCiAgICAgIHRoaXMuJGVtaXQoImNvcHlGcmVpZ2h0IiwgY2hhcmdlKQ0KICAgIH0sDQogICAgaGFuZGxlRGVsZXRlQWxsKCkgew0KICAgICAgdGhpcy4kZW1pdCgiZGVsZXRlQWxsIikNCiAgICB9LA0KICAgIGhhbmRsZURlbGV0ZUl0ZW0oY2hhcmdlKSB7DQogICAgICB0aGlzLiRlbWl0KCJkZWxldGVJdGVtIiwgY2hhcmdlKQ0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["BillOfLadingInfo.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6bA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "BillOfLadingInfo.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div>\r\n    <!--title-->\r\n    <el-row>\r\n      <el-col :span=\"18\">\r\n        <div class=\"service-bar\" style=\"display: flex;margin-top: 10px;margin-bottom: 10px;width: 100%\">\r\n          <a :class=\"{'el-icon-arrow-down':visible,'el-icon-arrow-right':!visible}\"/>\r\n          <div style=\"width:150px;display: flex\">\r\n            <h3 style=\"margin: 0;width: 250px;text-align: left\" @click=\"toggleVisible\">提单信息</h3>\r\n            <el-button style=\"margin-left: 10px;\" type=\"text\" @click=\"$emit('openChargeSelect', rsClientMessage)\">\r\n              [DN...]\r\n            </el-button>\r\n          </div>\r\n\r\n          <el-button type=\"primary\" size=\"mini\" style=\"margin-left: 10px;\" @click=\"profitOpen=true\">利润</el-button>\r\n\r\n          <el-col v-if=\"auditInfo\"\r\n                  :span=\"15\" style=\"display: flex\"\r\n          >\r\n            <div v-hasPermi=\"['system:booking:opapproval','system:rct:opapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnOpConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'op')\"\r\n              >操作确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ opConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ opConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:salesapproval','system:rct:salesapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnSalesConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'sales')\"\r\n              >业务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ salesConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ salesConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:clientapproval','system:rct:clientapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnClientConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'client')\"\r\n              >客户确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ clientConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ clientConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:financeapproval','system:rct:financeapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isAccountConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'account', rsClientMessage.rsChargeList)\"\r\n              >财务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ accountConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ accountConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n\r\n          <div style=\"margin-left: auto\">\r\n            <el-popover\r\n              v-for=\"(item,index) in fileOptions\" :key=\"index\"\r\n              placement=\"top\" trigger=\"click\" width=\"100\"\r\n            >\r\n              <el-button v-for=\"(item2,index) in item.templateList\" :key=\"index\"\r\n                         @click=\"handleFileAction(item.link, item2)\"\r\n              >{{ item2 }}\r\n              </el-button>\r\n              <a slot=\"reference\" style=\"color: blue;padding: 0;margin-left: 10px\" target=\"_blank\"\r\n              >[{{ item.file }}]</a>\r\n            </el-popover>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!--content-->\r\n    <transition name=\"fade\">\r\n      <el-row v-if=\"visible\" :gutter=\"10\" style=\"margin-bottom:15px;display:-webkit-box\">\r\n        <!--主表信息-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"branchInfo\" :span=\"18\">\r\n            <el-table :data=\"bookingMessageList\" border @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"50\"\r\n              />\r\n              <el-table-column label=\"MB/L No\" prop=\"mBlNo\"/>\r\n              <el-table-column label=\"HB/L No\" prop=\"hBlNo\"/>\r\n              <el-table-column label=\"发货人\" prop=\"bookingShipper\"/>\r\n              <el-table-column label=\"收货人\" prop=\"bookingConsignee\"/>\r\n              <el-table-column label=\"通知人\" prop=\"bookingNotifyParty\"/>\r\n              <el-table-column label=\"代理\" prop=\"bookingAgent\"/>\r\n              <el-table-column label=\"柜号\" prop=\"containerNo\"/>\r\n              <el-table-column label=\"封号\" prop=\"sealNo\"/>\r\n              <el-table-column label=\"柜型\" prop=\"containerType\"/>\r\n              <el-table-column label=\"唛头\" prop=\"shippingMark\"/>\r\n              <el-table-column label=\"件数\" prop=\"packageQuantity\"/>\r\n              <el-table-column label=\"货描\" prop=\"goodsDescription\"/>\r\n              <el-table-column label=\"体积\" prop=\"goodsVolume\"/>\r\n              <el-table-column label=\"重量\" prop=\"grossWeight\"/>\r\n              <el-table-column label=\"提单类型\" prop=\"blTypeCode\"/>\r\n              <el-table-column label=\"出单方式\" prop=\"blFormCode\"/>\r\n              <el-table-column label=\"交单方式\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <tree-select :class=\"'disable-form'\" :disabled=\"true\"\r\n                               :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"scope.row.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"scope.row.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" @click=\"handleBookingMessageUpdate(scope.row)\">修改</el-button>\r\n                  <el-button style=\"color: red\" type=\"text\"\r\n                             @click=\"deleteBookingMessage(scope.row)\"\r\n                  >删除\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!--弹出层-->\r\n            <el-dialog\r\n              v-dialogDrag\r\n              v-dialogDragWidth\r\n              :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n              :show-close=\"false\" :title=\"bookingMessageTitle\" @close=\"closeBookingMessage\"\r\n              :visible.sync=\"openBookingMessage\" append-to-body width=\"30%\"\r\n            >\r\n              <el-form ref=\"bookingMessageForm\" :model=\"bookingMessageForm\" class=\"edit\" label-width=\"80px\"\r\n                       style=\"\"\r\n              >\r\n                <div v-if=\"bookingMessageForm.blTypeCode==='MBL'\">\r\n                  <el-form-item label=\"提单号码\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <div v-else>\r\n                  <el-form-item label=\"MB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"HB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.hBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <el-form-item label=\"发货人\">\r\n                  <template slot=\"label\">\r\n                    <div>发货人</div>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('handleAddCommon', 'release')\">[↗]\r\n                    </el-button>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('openReleaseUsed')\">[...]</el-button>\r\n                  </template>\r\n                  <el-input v-model=\"bookingMessageForm.bookingShipper\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"收货人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingConsignee\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"通知人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingNotifyParty\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"代理\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingAgent\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"启运港\">\r\n                  <el-input v-model=\"bookingMessageForm.polName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"卸货港\">\r\n                  <el-input v-model=\"bookingMessageForm.podName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"目的港\">\r\n                  <el-input v-model=\"bookingMessageForm.destinationPort\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"柜号\">\r\n                  <el-input v-model=\"bookingMessageForm.containerNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"封号\">\r\n                  <el-input v-model=\"bookingMessageForm.sealNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"柜型\">\r\n                  <el-input v-model=\"bookingMessageForm.containerType\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"唛头\">\r\n                  <el-input v-model=\"bookingMessageForm.shippingMark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"件数\">\r\n                  <el-input v-model=\"bookingMessageForm.packageQuantity\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"件数\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"货描\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsDescription\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"重量\">\r\n                  <el-input v-model=\"bookingMessageForm.grossWeight\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"重量\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"体积\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsVolume\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"体积\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\">\r\n                  <el-input v-model=\"bookingMessageForm.blRemark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单地\">\r\n                  <location-select :load-options=\"psaBookingSelectData.locationOptions\" :no-parent=\"true\"\r\n                                   :pass=\"bookingMessageForm.polIds\" :placeholder=\"'启运港'\"\r\n                                   @returnData=\"bookingMessageForm.city=$event.locationEnShortName\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"开船日期\">\r\n                  <el-date-picker\r\n                    v-model=\"bookingMessageForm.onBoardDate\"\r\n                    placeholder=\"选择日期\"\r\n                    type=\"date\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"付款方式\">\r\n                  <el-select v-model=\"bookingMessageForm.payWay\" placeholder=\"请选择\">\r\n                    <el-option\r\n                      v-for=\"item in payWayOptions\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"提单类型\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blTypeCode\"\r\n                               :placeholder=\"'提单类型'\" :type=\"'blType'\"\r\n                               @return=\"bookingMessageForm.blTypeCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blFormCode\"\r\n                               :placeholder=\"'出单方式'\" :type=\"'blForm'\"\r\n                               @return=\"bookingMessageForm.blFormCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"交单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"bookingMessageForm.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n              <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"bookingMessageConfirm\">确 定</el-button>\r\n                <el-button size=\"mini\" @click=\"closeBookingMessage\">取 消</el-button>\r\n              </div>\r\n            </el-dialog>\r\n\r\n            <el-button :disabled=\"psaVerify || disabled\"\r\n                       style=\"padding: 0\"\r\n                       type=\"text\"\r\n                       @click=\"addBookingMessage\"\r\n            >[＋]\r\n            </el-button>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--物流进度-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n            <el-form-item label=\"进度需求\" prop=\"goodsNameSummary\"/>\r\n            <div>\r\n              <logistics-progress :disabled=\"rsClientMessageFormDisable || disabled || psaVerify\"\r\n                                  :logistics-progress-data=\"rsClientMessage.rsOpLogList\"\r\n                                  :open-logistics-progress-list=\"true\"\r\n                                  @deleteItem=\"deleteLogisticsItem\"\r\n                                  @return=\"updateLogisticsProgress\"\r\n              />\r\n            </div>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--费用列表-->\r\n        <!--分帐单列表-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.5\">\r\n            <debit-note-list\r\n              :is-receivable=\"1\"\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"rsClientMessage.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :rct-id=\"form.rctId\"\r\n              @return=\"$emit('rsClientMessageDebitNote', $event)\"\r\n              @addDebitNote=\"handleAddDebitNote\"\r\n              @copyFreight=\"handleCopyFreight\"\r\n              @deleteAll=\"handleDeleteAll\"\r\n              @deleteItem=\"rsClientMessage.rsDebitNoteList = rsClientMessage.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @selection-change=\"handleSelectionChange\"\r\n            />\r\n          </el-col>\r\n        </transition>\r\n      </el-row>\r\n    </transition>\r\n\r\n    <!-- 利润对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"profitOpen\"\r\n      title=\"单票利润\"\r\n      width=\"30%\"\r\n      @open=\"openProfit\"\r\n    >\r\n      <el-table\r\n        :data=\"profitTableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column\r\n          label=\"货币\"\r\n          prop=\"currencyCode\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"应收\"\r\n          prop=\"receivable\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"应付\"\r\n          prop=\"payable\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"不含税利润\" prop=\"profit\"\r\n          style=\"color: #0d0dfd\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税应收\"\r\n          prop=\"receivableTax\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税应付\"\r\n          prop=\"payableTax\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税利润\"\r\n          prop=\"profitTax\"\r\n        >\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-row>\r\n        <el-col :span=\"5\">\r\n          <el-form-item label=\"折合币种\" prop=\"rctOpDate\">\r\n            <el-select v-model=\"currencyCode\" @change=\"profitCount(currencyCode)\">\r\n              <el-option label=\"RMB\" value=\"RMB\"/>\r\n              <el-option label=\"USD\" value=\"USD\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"7\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div style=\"color: #0d0dfd\">不含税利润</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"profit\" placeholder=\"不含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n        <el-col :span=\"7\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div>含税利润</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"profitTax\" placeholder=\"含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n        <el-col :span=\"5\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div>折算汇率</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"exchangeRate\" placeholder=\"含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LogisticsProgress from \"../logisticsProgress\"\r\nimport ChargeList from \"../chargeList\"\r\nimport TreeSelect from \"@/components/TreeSelect\"\r\nimport LocationSelect from \"@/components/LocationSelect\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport currency from \"currency.js\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"BillOfLadingInfo\",\r\n  components: {\r\n    DebitNoteList,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    TreeSelect,\r\n    LocationSelect\r\n  },\r\n  props: {\r\n    bookingMessageForm: {\r\n      type: Object,\r\n      required: true,\r\n      default: null\r\n    },\r\n    openBookingMessage: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    bookingMessageStatus: {\r\n      type: String,\r\n      default: \"<UNK>\"\r\n    },\r\n    bookingMessageList: {\r\n      type: Array,\r\n      default: []\r\n    },\r\n    rsClientMessage: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    form: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    rsClientMessageReceivableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      visible: true,\r\n      bookingMessageTitle: \"提单信息\",\r\n      psaBookingSelectData: {\r\n        locationOptions: []\r\n      },\r\n      rsClientMessageFormDisable: false,\r\n      fileOptions: [\r\n        {file: \"操作单\", link: \"getOpBill\", templateList: [\"整柜\", \"散货\", \"空运\", \"其他\"]},\r\n        {file: \"提单\", link: \"getBillOfLading\", templateList: [\"套打提单\", \"电放提单\"]},\r\n        {\r\n          file: \"费用清单\",\r\n          link: \"getChargeListBill\",\r\n          templateList: [\"CN-广州瑞旗[招行USD+工行RMB]\", \"CN-广州瑞旗[USD->RMB]\", \"EN-广州瑞旗[招行USD]\", \"EN-广州瑞旗[RMB->USD]\", \"EN- 瑞旗香港账户[HSBC RMB->USD]\", \"EN- 香港瑞旗[HSBC]\", \"CN-广州正泽[招行USD+RMB]\", \"CN-广州正泽[USD->RMB]\"]\r\n        }\r\n      ],\r\n      payWayOptions: [\r\n        {label: \"预付\", value: \"FREIGHTP REPAID\"},\r\n        {label: \"到付\", value: \"FREIGHTP COLLECT\"}\r\n      ],\r\n      profitOpen: false,\r\n      profitTableData: [],\r\n      currencyCode: \"RMB\",\r\n      profit: 0,\r\n      profitTax: 0,\r\n      exchangeRate: 1\r\n    }\r\n  },\r\n  computed: {\r\n    rsClientServiceInstance() {\r\n      return this.rsClientMessage || {}\r\n    },\r\n    opConfirmedName() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedName || \"\"\r\n    },\r\n    opConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedDate || \"\"\r\n    },\r\n    salesConfirmedName() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedName || \"\"\r\n    },\r\n    salesConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedDate || \"\"\r\n    },\r\n    clientConfirmedName() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedName || \"\"\r\n    },\r\n    clientConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedDate || \"\"\r\n    },\r\n    accountConfirmedName() {\r\n      return this.rsClientServiceInstance.accountConfirmedName || \"\"\r\n    },\r\n    accountConfirmedDate() {\r\n      return this.rsClientServiceInstance.accountConfirmedDate || \"\"\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化时的操作\r\n  },\r\n  methods: {\r\n    handleChargeSelectionChange(selection) {\r\n      this.$emit(\"selection-charge-change\", selection)\r\n    },\r\n    handleAddDebitNote() {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 0\r\n      row.rsChargeList = []\r\n      this.$emit(\"addDebitNote\", row, this.rsClientMessage)\r\n    },\r\n    toggleVisible() {\r\n      this.visible = !this.visible\r\n    },\r\n    handleFileAction(methodName, templateType) {\r\n      this.$emit(methodName, templateType)\r\n    },\r\n    handleSelectionChange(selection) {\r\n      // 只能勾选一个\r\n      if (selection.length > 1) {\r\n        this.$message.warning(\"只能勾选一个账单\")\r\n        return\r\n      }\r\n      this.$emit(\"handleReceiveSelected\", selection)\r\n    },\r\n    handleBookingMessageUpdate(row) {\r\n      this.$emit(\"handleBookingMessageUpdate\", row)\r\n    },\r\n    addBookingMessage() {\r\n      this.$emit(\"handleAddBookingMessage\", this.bookingMessageForm)\r\n    },\r\n    bookingMessageConfirm() {\r\n      // 将操作通过事件发送给父组件\r\n      this.$emit(\"bookingMessageConfirm\", this.bookingMessageForm)\r\n\r\n    },\r\n    closeBookingMessage() {\r\n      this.$emit(\"closeBookingMessage\")\r\n    },\r\n    deleteBookingMessage(row) {\r\n      // 通过事件发送给父组件\r\n      this.$emit(\"deleteBookingMessage\", row)\r\n    },\r\n    deleteLogisticsItem(item) {\r\n      if (this.rsClientMessage && this.rsClientMessage.rsOpLogList) {\r\n        this.rsClientMessage.rsOpLogList = this.rsClientMessage.rsOpLogList.filter(log => log !== item)\r\n      }\r\n    },\r\n    updateLogisticsProgress(data) {\r\n      if (this.rsClientMessage) {\r\n        this.rsClientMessage.rsOpLogList = data\r\n      }\r\n    },\r\n    openProfit() {\r\n      this.profitTableData = []\r\n\r\n      let RMB = {}\r\n      RMB.currencyCode = \"RMB\"\r\n      RMB.receivable = this.rsClientMessageReceivableRMB\r\n      RMB.payable = this.rsClientMessagePayableRMB\r\n      // 不含税利润\r\n      RMB.profit = this.rsClientMessageProfitRMB\r\n      // 含税应收\r\n      RMB.receivableTax = this.rsClientMessageReceivableTaxRMB\r\n      // 含税应付\r\n      RMB.payableTax = this.rsClientMessagePayableTaxRMB\r\n      // 含税利润\r\n      RMB.profitTax = this.rsClientMessageProfitTaxRMB\r\n\r\n      let USD = {}\r\n      USD.currencyCode = \"USD\"\r\n      USD.receivable = this.rsClientMessageReceivableUSD\r\n      USD.payable = this.rsClientMessagePayableUSD\r\n      USD.profit = this.rsClientMessageProfitUSD\r\n      USD.receivableTax = this.rsClientMessageReceivableTaxUSD\r\n      USD.payableTax = this.rsClientMessagePayableTaxUSD\r\n      USD.profitTax = this.rsClientMessageProfitTaxUSD\r\n\r\n      this.profitTableData.push(RMB)\r\n      this.profitTableData.push(USD)\r\n\r\n      this.profitCount(\"RMB\")\r\n    },\r\n    profitCount(type) {\r\n      let exchangeRate\r\n      for (const a of this.$store.state.data.exchangeRateList) {\r\n        if (this.form.podEta) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(this.form.podEta)\r\n            && parseTime(this.form.podEta) <= parseTime(a.validTo)\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n        if (!exchangeRate) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(new Date())\r\n            && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      }\r\n      this.exchangeRate = exchangeRate\r\n\r\n      if (type === \"RMB\") {\r\n        // 都折算成人民币\r\n        this.profit = currency(this.rsClientMessageProfitUSD).multiply(exchangeRate).add(this.rsClientMessageProfitRMB).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxUSD).multiply(exchangeRate).add(this.rsClientMessageProfitTaxRMB).value\r\n      } else {\r\n        this.profit = currency(this.rsClientMessageProfitRMB).divide(exchangeRate).add(this.rsClientMessageProfitUSD).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxRMB).divide(exchangeRate).add(this.rsClientMessageProfitTaxUSD).value\r\n      }\r\n    },\r\n    handleCopyFreight(charge) {\r\n      this.$emit(\"copyFreight\", charge)\r\n    },\r\n    handleDeleteAll() {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    handleDeleteItem(charge) {\r\n      this.$emit(\"deleteItem\", charge)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document.scss';\r\n</style>\r\n"]}]}