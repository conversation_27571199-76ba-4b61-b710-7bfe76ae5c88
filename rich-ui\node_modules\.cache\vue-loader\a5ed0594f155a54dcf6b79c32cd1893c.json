{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Breadcrumb\\index.vue?vue&type=template&id=b50ef614&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Breadcrumb\\index.vue", "mtime": 1754876882524}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxlbC1icmVhZGNydW1iIGNsYXNzPSJhcHAtYnJlYWRjcnVtYiIgc2VwYXJhdG9yPSIvIj4KICA8dHJhbnNpdGlvbi1ncm91cCBuYW1lPSJicmVhZGNydW1iIj4KICAgIDxlbC1icmVhZGNydW1iLWl0ZW0gdi1mb3I9IihpdGVtLGluZGV4KSBpbiBsZXZlbExpc3QiIDprZXk9Iml0ZW0ucGF0aCI+CiAgICAgIDxzcGFuIHYtaWY9Iml0ZW0ucmVkaXJlY3QgPT0gJ25vUmVkaXJlY3QnIHx8IGluZGV4ID09IGxldmVsTGlzdC5sZW5ndGggLSAxIgogICAgICAgICAgICBjbGFzcz0ibm8tcmVkaXJlY3QiPnt7IGl0ZW0ubWV0YS50aXRsZSB9fTwvc3Bhbj4KICAgICAgPGEgdi1lbHNlIEBjbGljay5wcmV2ZW50PSJoYW5kbGVMaW5rKGl0ZW0pIj57eyBpdGVtLm1ldGEudGl0bGUgfX08L2E+CiAgICA8L2VsLWJyZWFkY3J1bWItaXRlbT4KICA8L3RyYW5zaXRpb24tZ3JvdXA+CjwvZWwtYnJlYWRjcnVtYj4K"}, null]}