{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\commoninfotype\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\commoninfotype\\index.vue", "mtime": 1754876882577}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_commoninfotype", "require", "_doc", "name", "data", "showLeft", "showRight", "docList", "loading", "ids", "single", "multiple", "showSearch", "commoninfotypeList", "title", "open", "queryParams", "infoTypeQuery", "form", "rules", "watch", "n", "created", "_this", "getList", "listDoc", "pageNum", "pageSize", "then", "response", "rows", "methods", "_this2", "listCommoninfotype", "handleTree", "cancel", "reset", "infoTypeId", "infoTypeShortName", "infoTypeLocalName", "infoTypeEnName", "orderNum", "status", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this3", "getCommoninfotype", "submitForm", "_this4", "$refs", "validate", "valid", "updateCommoninfotype", "$modal", "msgSuccess", "addCommoninfotype", "handleDelete", "_this5", "infoTypeIds", "$confirm", "customClass", "delCommoninfotype", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleStatusChange", "_this6", "text", "changeStatus", "getDocTypeIds", "val", "docIds", "exports", "_default"], "sources": ["src/views/system/commoninfotype/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"infoTypeQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.infoTypeQuery\"\r\n              clearable\r\n              placeholder=\"中英文简称\"\r\n              style=\"width: 100%\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:commoninfotype:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:commoninfotype:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"commoninfotypeList\"\r\n                  :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n                  row-key=\"infoTypeId\"\r\n                  @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"left\" label=\"类别名称\" prop=\"infoTypeShortName\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.infoTypeShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.infoTypeLocalName }}</a>\r\n              {{ scope.row.infoTypeEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"文件类型\" prop=\"docType\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"center\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"48\"/>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:commoninfotype:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:commoninfotype:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改通用信息类型对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item label=\"简称\" prop=\"infoTypeShortName\">\r\n          <el-input v-model=\"form.infoTypeShortName\" placeholder=\"简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"infoTypeLocalName\">\r\n          <el-input v-model=\"form.infoTypeLocalName\" placeholder=\"中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"infoTypeEnName\">\r\n          <el-input v-model=\"form.infoTypeEnName\" placeholder=\"英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"文件类型\" prop=\"docIds\">\r\n          <el-select v-model=\"form.docId\" placeholder=\"进度分类\" style=\"width: 100%\" multiple>\r\n            <el-option\r\n              v-for=\"dict in docList\"\r\n              :key=\"dict.docId\"\r\n              :label=\"dict.docLocalName\"\r\n              :value=\"dict.docId\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" placeholder=\"排序\" style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCommoninfotype,\r\n  changeStatus,\r\n  delCommoninfotype,\r\n  getCommoninfotype,\r\n  listCommoninfotype,\r\n  updateCommoninfotype\r\n} from \"@/api/system/commoninfotype\";\r\nimport {listDoc} from \"@/api/system/doc\";\r\n\r\nexport default {\r\n  name: \"Commoninfotype\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      docList:[],\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 通用信息类型表格数据\r\n      commoninfotypeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        infoTypeQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    listDoc({pageNum: 1, pageSize: 100}).then(response => {\r\n      this.docList = response.rows;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询通用信息类型列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCommoninfotype(this.queryParams).then(response => {\r\n        this.commoninfotypeList = this.handleTree(response.data, \"infoTypeId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        infoTypeId: null,\r\n        infoTypeShortName: null,\r\n        infoTypeLocalName: null,\r\n        infoTypeEnName: null,\r\n        orderNum: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.infoTypeId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加通用信息类型\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const infoTypeId = row.infoTypeId || this.ids\r\n      getCommoninfotype(infoTypeId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改通用信息类型\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.infoTypeId != null) {\r\n            updateCommoninfotype(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCommoninfotype(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const infoTypeIds = row.infoTypeId || this.ids;\r\n      this.$confirm('是否确认删除通用信息类型编号为\"' + infoTypeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCommoninfotype(infoTypeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/commoninfotype/export', {\r\n        ...this.queryParams\r\n      }, `commoninfotype_${new Date().getTime()}.xlsx`)\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.infoTypeLocalName + '\"吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.infoTypeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    getDocTypeIds(val) {\r\n      this.form.docIds = val\r\n    },\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;AA6IA,IAAAA,eAAA,GAAAC,OAAA;AAQA,IAAAC,IAAA,GAAAD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAE,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,OAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,kBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,aAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAR,UAAA,WAAAA,WAAAS,CAAA;MACA,IAAAA,CAAA;QACA,KAAAf,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAiB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,IAAAC,YAAA;MAAAC,OAAA;MAAAC,QAAA;IAAA,GAAAC,IAAA,WAAAC,QAAA;MACAN,KAAA,CAAAhB,OAAA,GAAAsB,QAAA,CAAAC,IAAA;IACA;EACA;EACAC,OAAA;IACA,iBACAP,OAAA,WAAAA,QAAA;MAAA,IAAAQ,MAAA;MACA,KAAAxB,OAAA;MACA,IAAAyB,kCAAA,OAAAjB,WAAA,EAAAY,IAAA,WAAAC,QAAA;QACAG,MAAA,CAAAnB,kBAAA,GAAAmB,MAAA,CAAAE,UAAA,CAAAL,QAAA,CAAAzB,IAAA;QACA4B,MAAA,CAAAxB,OAAA;MACA;IACA;IACA;IACA2B,MAAA,WAAAA,OAAA;MACA,KAAApB,IAAA;MACA,KAAAqB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAlB,IAAA;QACAmB,UAAA;QACAC,iBAAA;QACAC,iBAAA;QACAC,cAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAApC,WAAA,CAAAU,OAAA;MACA,KAAAF,OAAA;IACA;IACA,aACA6B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9C,GAAA,GAAA8C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAApB,UAAA;MAAA;MACA,KAAA3B,MAAA,GAAA6C,SAAA,CAAAG,MAAA;MACA,KAAA/C,QAAA,IAAA4C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAvB,KAAA;MACA,KAAArB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA8C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA1B,KAAA;MACA,IAAAC,UAAA,GAAAwB,GAAA,CAAAxB,UAAA,SAAA5B,GAAA;MACA,IAAAsD,iCAAA,EAAA1B,UAAA,EAAAT,IAAA,WAAAC,QAAA;QACAiC,MAAA,CAAA5C,IAAA,GAAAW,QAAA,CAAAzB,IAAA;QACA0D,MAAA,CAAA/C,IAAA;QACA+C,MAAA,CAAAhD,KAAA;MACA;IACA;IACA,WACAkD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/C,IAAA,CAAAmB,UAAA;YACA,IAAAgC,oCAAA,EAAAJ,MAAA,CAAA/C,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAoC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAAzC,OAAA;YACA;UACA;YACA,IAAAgD,iCAAA,EAAAP,MAAA,CAAA/C,IAAA,EAAAU,IAAA,WAAAC,QAAA;cACAoC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAAzC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAiD,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,WAAA,GAAAd,GAAA,CAAAxB,UAAA,SAAA5B,GAAA;MACA,KAAAmE,QAAA,sBAAAD,WAAA;QAAAE,WAAA;MAAA,GAAAjD,IAAA;QACA,WAAAkD,iCAAA,EAAAH,WAAA;MACA,GAAA/C,IAAA;QACA8C,MAAA,CAAAlD,OAAA;QACAkD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnE,WAAA,qBAAAoE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA1B,GAAA;MAAA,IAAA2B,MAAA;MACA,IAAAC,IAAA,GAAA5B,GAAA,CAAAnB,MAAA;MACA,KAAAkC,QAAA,UAAAa,IAAA,UAAA5B,GAAA,CAAAtB,iBAAA;QAAAsC,WAAA;MAAA,GAAAjD,IAAA;QACA,WAAA8D,4BAAA,EAAA7B,GAAA,CAAAxB,UAAA,EAAAwB,GAAA,CAAAnB,MAAA;MACA,GAAAd,IAAA;QACA4D,MAAA,CAAAlB,MAAA,CAAAC,UAAA,CAAAkB,IAAA;MACA,GAAAV,KAAA;QACAlB,GAAA,CAAAnB,MAAA,GAAAmB,GAAA,CAAAnB,MAAA;MACA;IACA;IACAiD,aAAA,WAAAA,cAAAC,GAAA;MACA,KAAA1E,IAAA,CAAA2E,MAAA,GAAAD,GAAA;IACA;EACA;AACA;AAAAE,OAAA,CAAAX,OAAA,GAAAY,QAAA"}]}