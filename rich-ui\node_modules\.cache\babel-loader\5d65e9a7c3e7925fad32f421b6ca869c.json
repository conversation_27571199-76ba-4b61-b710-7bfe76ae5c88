{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\dashboard\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\dashboard\\PieChart.vue", "mtime": 1678688095349}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0Owp2YXIgX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkLmpzIikuZGVmYXVsdDsKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnZhciBlY2hhcnRzID0gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQocmVxdWlyZSgiZWNoYXJ0cyIpKTsKdmFyIF9yZXNpemUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIi4vbWl4aW5zL3Jlc2l6ZSIpKTsKLy8KLy8KLy8KLy8KCnJlcXVpcmUoJ2VjaGFydHMvdGhlbWUvbWFjYXJvbnMnKTsgLy8gZWNoYXJ0cyB0aGVtZQp2YXIgX2RlZmF1bHQgPSB7CiAgbWl4aW5zOiBbX3Jlc2l6ZS5kZWZhdWx0XSwKICBwcm9wczogewogICAgY2xhc3NOYW1lOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJ2NoYXJ0JwogICAgfSwKICAgIHdpZHRoOiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzEwMCUnCiAgICB9LAogICAgaGVpZ2h0OiB7CiAgICAgIHR5cGU6IFN0cmluZywKICAgICAgZGVmYXVsdDogJzMwMHB4JwogICAgfQogIH0sCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIGNoYXJ0OiBudWxsCiAgICB9OwogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24gbW91bnRlZCgpIHsKICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICB0aGlzLiRuZXh0VGljayhmdW5jdGlvbiAoKSB7CiAgICAgIF90aGlzLmluaXRDaGFydCgpOwogICAgfSk7CiAgfSwKICBiZWZvcmVEZXN0cm95OiBmdW5jdGlvbiBiZWZvcmVEZXN0cm95KCkgewogICAgaWYgKCF0aGlzLmNoYXJ0KSB7CiAgICAgIHJldHVybjsKICAgIH0KICAgIHRoaXMuY2hhcnQuZGlzcG9zZSgpOwogICAgdGhpcy5jaGFydCA9IG51bGw7CiAgfSwKICBtZXRob2RzOiB7CiAgICBpbml0Q2hhcnQ6IGZ1bmN0aW9uIGluaXRDaGFydCgpIHsKICAgICAgdGhpcy5jaGFydCA9IGVjaGFydHMuaW5pdCh0aGlzLiRlbCwgJ21hY2Fyb25zJyk7CiAgICAgIHRoaXMuY2hhcnQuc2V0T3B0aW9uKHsKICAgICAgICB0b29sdGlwOiB7CiAgICAgICAgICB0cmlnZ2VyOiAnaXRlbScsCiAgICAgICAgICBmb3JtYXR0ZXI6ICd7YX0gPGJyLz57Yn0gOiB7Y30gKHtkfSUpJwogICAgICAgIH0sCiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBsZWZ0OiAnY2VudGVyJywKICAgICAgICAgIGJvdHRvbTogJzEwJywKICAgICAgICAgIGRhdGE6IFsnSW5kdXN0cmllcycsICdUZWNobm9sb2d5JywgJ0ZvcmV4JywgJ0dvbGQnLCAnRm9yZWNhc3RzJ10KICAgICAgICB9LAogICAgICAgIHNlcmllczogW3sKICAgICAgICAgIG5hbWU6ICdXRUVLTFkgV1JJVEUgQVJUSUNMRVMnLAogICAgICAgICAgdHlwZTogJ3BpZScsCiAgICAgICAgICByb3NlVHlwZTogJ3JhZGl1cycsCiAgICAgICAgICByYWRpdXM6IFsxNSwgOTVdLAogICAgICAgICAgY2VudGVyOiBbJzUwJScsICczOCUnXSwKICAgICAgICAgIGRhdGE6IFt7CiAgICAgICAgICAgIHZhbHVlOiAzMjAsCiAgICAgICAgICAgIG5hbWU6ICdJbmR1c3RyaWVzJwogICAgICAgICAgfSwgewogICAgICAgICAgICB2YWx1ZTogMjQwLAogICAgICAgICAgICBuYW1lOiAnVGVjaG5vbG9neScKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgdmFsdWU6IDE0OSwKICAgICAgICAgICAgbmFtZTogJ0ZvcmV4JwogICAgICAgICAgfSwgewogICAgICAgICAgICB2YWx1ZTogMTAwLAogICAgICAgICAgICBuYW1lOiAnR29sZCcKICAgICAgICAgIH0sIHsKICAgICAgICAgICAgdmFsdWU6IDU5LAogICAgICAgICAgICBuYW1lOiAnRm9yZWNhc3RzJwogICAgICAgICAgfV0sCiAgICAgICAgICBhbmltYXRpb25FYXNpbmc6ICdjdWJpY0luT3V0JywKICAgICAgICAgIGFuaW1hdGlvbkR1cmF0aW9uOiAyNjAwCiAgICAgICAgfV0KICAgICAgfSk7CiAgICB9CiAgfQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["echarts", "_interopRequireWildcard", "require", "_resize", "_interopRequireDefault", "_default", "mixins", "resize", "props", "className", "type", "String", "default", "width", "height", "data", "chart", "mounted", "_this", "$nextTick", "initChart", "<PERSON><PERSON><PERSON><PERSON>", "dispose", "methods", "init", "$el", "setOption", "tooltip", "trigger", "formatter", "legend", "left", "bottom", "series", "name", "roseType", "radius", "center", "value", "animationEasing", "animationDuration", "exports"], "sources": ["src/views/dashboard/PieChart.vue"], "sourcesContent": ["<template>\r\n  <div :class=\"className\" :style=\"{height:height,width:width}\"/>\r\n</template>\r\n\r\n<script>\r\nimport * as echarts from 'echarts'\r\nimport resize from './mixins/resize'\r\n\r\nrequire('echarts/theme/macarons') // echarts theme\r\n\r\nexport default {\r\n  mixins: [resize],\r\n  props: {\r\n    className: {\r\n      type: String,\r\n      default: 'chart'\r\n    },\r\n    width: {\r\n      type: String,\r\n      default: '100%'\r\n    },\r\n    height: {\r\n      type: String,\r\n      default: '300px'\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      chart: null\r\n    }\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (!this.chart) {\r\n      return\r\n    }\r\n    this.chart.dispose()\r\n    this.chart = null\r\n  },\r\n  methods: {\r\n    initChart() {\r\n      this.chart = echarts.init(this.$el, 'macarons')\r\n\r\n      this.chart.setOption({\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b} : {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          left: 'center',\r\n          bottom: '10',\r\n          data: ['Industries', 'Technology', 'Forex', 'Gold', 'Forecasts']\r\n        },\r\n        series: [\r\n          {\r\n            name: 'WEEKLY WRITE ARTICLES',\r\n            type: 'pie',\r\n            roseType: 'radius',\r\n            radius: [15, 95],\r\n            center: ['50%', '38%'],\r\n            data: [\r\n              {value: 320, name: 'Industries'},\r\n              {value: 240, name: 'Technology'},\r\n              {value: 149, name: 'Forex'},\r\n              {value: 100, name: 'Gold'},\r\n              {value: 59, name: 'Forecasts'}\r\n            ],\r\n            animationEasing: 'cubicInOut',\r\n            animationDuration: 2600\r\n          }\r\n        ]\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;AAKA,IAAAA,OAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;AAEAA,OAAA;AAAA,IAAAG,QAAA,GAEA;EACAC,MAAA,GAAAC,eAAA;EACAC,KAAA;IACAC,SAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,MAAA;MACAJ,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;EACA;EACAG,IAAA,WAAAA,KAAA;IACA;MACAC,KAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,SAAA;MACAD,KAAA,CAAAE,SAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,UAAAL,KAAA;MACA;IACA;IACA,KAAAA,KAAA,CAAAM,OAAA;IACA,KAAAN,KAAA;EACA;EACAO,OAAA;IACAH,SAAA,WAAAA,UAAA;MACA,KAAAJ,KAAA,GAAAhB,OAAA,CAAAwB,IAAA,MAAAC,GAAA;MAEA,KAAAT,KAAA,CAAAU,SAAA;QACAC,OAAA;UACAC,OAAA;UACAC,SAAA;QACA;QACAC,MAAA;UACAC,IAAA;UACAC,MAAA;UACAjB,IAAA;QACA;QACAkB,MAAA,GACA;UACAC,IAAA;UACAxB,IAAA;UACAyB,QAAA;UACAC,MAAA;UACAC,MAAA;UACAtB,IAAA,GACA;YAAAuB,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,GACA;YAAAI,KAAA;YAAAJ,IAAA;UAAA,EACA;UACAK,eAAA;UACAC,iBAAA;QACA;MAEA;IACA;EACA;AACA;AAAAC,OAAA,CAAA7B,OAAA,GAAAP,QAAA"}]}