{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\outboundPlan.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\outboundPlan.vue", "mtime": 1754876882585}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_outboundrecord", "require", "_rich", "_inventory", "_moment", "_interopRequireDefault", "_currency", "name", "props", "data", "dialogVisible", "showLeft", "showRight", "loading", "selectOutboundList", "ids", "single", "multiple", "showSearch", "total", "outboundrecordList", "title", "open", "queryParams", "pageNum", "pageSize", "outboundNo", "clientCode", "clientName", "operator", "containerType", "containerNo", "sealNo", "outboundDate", "warehouseQuote", "workerLoadingFee", "warehouseCollection", "collectionNotes", "totalBoxes", "totalGrossWeight", "totalVolume", "totalRows", "receivedStorageFee", "unpaidUnloadingFee", "unpaidPackagingFee", "logisticsAdvanceFee", "rentalBalanceFee", "overdueRent", "freeStackDays", "overdueUnitPrice", "form", "outboundType", "preOutboundInventoryListLoading", "search", "rules", "required", "message", "trigger", "outboundForm", "moment", "format", "clientRow", "preOutboundInventoryList", "selectedCargoDetail", "watch", "n", "openOutbound", "newVal", "$emit", "created", "methods", "warehouseConfirm", "outboundOpen", "outboundFormProp", "JSON", "parse", "stringify", "outboundData", "closeOutbound", "loadChildInventory", "tree", "treeNode", "resolve", "_this", "listInventory", "packageTo", "inventoryId", "then", "response", "rows", "children", "includes", "setTimeout", "for<PERSON>ach", "child", "push", "$refs", "table", "toggleRowSelection", "warehouseRentSettlement", "outbound<PERSON><PERSON><PERSON>", "outbound<PERSON><PERSON><PERSON>", "$store", "state", "user", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unreceivedFromCustomer", "currency", "add", "additionalStorageFee", "overdueRentalFee", "difficultyWorkFee", "value", "receivedFromCustomer", "customerReceivableBalance", "subtract", "payableToWorker", "receivedUnloadingFee", "receivedPackingFee", "receivedFromSupplier", "receivedSupplier", "promissoryNoteSales", "promissoryNoteCost", "warehouseAdvanceOtherFee", "promissoryNoteGrossProfit", "outboundConfirm", "type", "_this2", "map", "item", "cargoDeduction", "$message", "error", "inboundSerialNoSub", "partialOutboundFlag", "Number", "rsCargoDetailsList", "boxCount", "unitGrossWeight", "unitVolume", "addOutboundrecord", "preOutboundFlag", "preOutboundRecordId", "preOutboundInventory", "getList", "success", "loadPreOutboundInventoryList", "_this3", "sqdPlannedOutboundDate", "plannedOutboundDate", "inventoryStatus", "listInventorys", "filter", "includesInboundFee", "receivedFee", "inboundFee", "difference", "packageRecord", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "$nextTick", "catch", "console", "finally", "handleOutbound", "selectedRows", "handlePreOutbound", "handleDirectOutbound", "handleRentSettlement", "parseTime", "handleOutboundCargoDetailSelectionChange", "selection", "row", "outboundCargoDetailsList", "isRowSelected", "getSummaries", "param", "_this4", "columns", "sums", "statisticalField", "summaryResults", "column", "index", "prop", "property", "reduce", "sum", "Object", "keys", "field", "handleOutboundSelectionChange", "_this5", "treeData", "store", "states", "previousIds", "_toConsumableArray2", "default", "newlySelected", "id", "newlyDeselected", "doLayout", "date1", "date2", "rentalSettlementDate", "rentalDays", "diff", "volumn", "isNaN", "multiply", "overdueRentalUnitPrice", "days", "freeStackPeriod", "parentNode", "find", "node", "length", "childrenLoaded", "toggleRowExpansion", "parentId", "childIndex", "indexOf", "splice", "itemIndex", "findIndex", "selectContainerType", "rate20gp", "rate40hq", "outboundClient", "rateLcl", "$forceUpdate", "_this6", "listOutboundrecord", "cancel", "reset", "outboundRecordId", "operationRequirement", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this7", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "handleSelectionChange", "handleAdd", "handleUpdate", "_this8", "getOutboundrecord", "submitForm", "_this9", "validate", "valid", "updateOutboundrecord", "handleDelete", "_this10", "outboundRecordIds", "delOutboundrecord", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "handleSearchEnter", "_this11", "serialNo", "String", "inboundSerialNo", "searchValue", "scrollWrapper", "$el", "querySelector", "querySelectorAll", "targetIndex", "idx", "rowText", "textContent", "targetRow", "rowTop", "offsetTop", "scrollTo", "top", "clientHeight", "behavior", "classList", "remove", "warning", "exports", "_default"], "sources": ["src/views/system/document/outboundPlan.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible=\"dialogVisible\" @open=\"outboundOpen\" @update:visible=\"dialogVisible = $event\"\r\n      append-to-body\r\n      width=\"70%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"计划出仓\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.plannedOutboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\">\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table ref=\"table\" v-loading=\"preOutboundInventoryListLoading\"\r\n                        :data=\"preOutboundInventoryList\" :load=\"loadChildInventory\" :summary-method=\"getSummaries\"\r\n                        :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        max-height=\"300\" row-key=\"inventoryId\" show-summary\r\n                        @selection-change=\"handleOutboundSelectionChange\"\r\n                        style=\"width: 100%;\"\r\n              >\r\n                <el-table-column align=\"center\" fixed type=\"selection\" width=\"28\"/>\r\n                <el-table-column align=\"center\" fixed label=\"序号\" type=\"index\" width=\"28\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.$index + 1 }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\">\r\n                  <template #header>\r\n                    <el-input\r\n                      v-model=\"search\"\r\n                      clearable\r\n                      placeholder=\"输入流水号搜索\"\r\n                      size=\"mini\"\r\n                      @keyup.enter=\"handleSearchEnter\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"部分出库\" prop=\"inboundDate\" width=\"50\">\r\n                  <template #default=\"scope\">\r\n                    <el-switch v-model=\"scope.row.partialOutboundFlag\"/>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货物明细\">\r\n                  <template #default=\"scope\">\r\n                    <el-popover\r\n                      :disabled=\"scope.row.partialOutboundFlag==0\"\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                          <template #default=\"scope\">\r\n                            <el-input v-model=\"scope.row.boxCount\" :disabled=\"!isRowSelected(scope.row)\"/>\r\n                          </template>\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"体积小计\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"毛重小计\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <template #reference>\r\n                        <el-button style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                      </template>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"最新计租日\" prop=\"inboundDate\" width=\"80\">\r\n                  <template #default=\"scope\">\r\n                    <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付卸货费\" prop=\"receivedUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收打包费\" prop=\"unpaidPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付打包费\" prop=\"receivedPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n                <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n                <el-table-column align=\"center\" label=\"超租天数\" prop=\"rentalDays\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n          <el-button @click=\"closeOutbound\">关 闭</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord,\r\n  getOutboundrecord,\r\n  listOutboundrecord,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {\r\n  listInventory,\r\n  listInventorys,\r\n  outboundInventory,\r\n  preOutboundInventory,\r\n  settlement\r\n} from \"@/api/system/inventory\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\n\r\nexport default {\r\n  name: \"OutboundPlan\",\r\n  props: ['openOutbound', 'outboundData', 'outboundFormProp'],\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: 0,\r\n      preOutboundInventoryListLoading: false,\r\n      search: null,\r\n      // 表单校验\r\n      rules: {\r\n        clientCode: [\r\n          {required: true, message: \"客户代码不能为空\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      outboundForm: {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      },\r\n      clientRow: {},\r\n      preOutboundInventoryList: [],\r\n      selectedCargoDetail: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    openOutbound(newVal) {\r\n      this.dialogVisible = newVal;\r\n    },\r\n    dialogVisible(newVal) {\r\n      if (!newVal) {\r\n        this.$emit('closeOutbound');\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    // this.getList()\r\n    // 初始化dialogVisible，与props保持同步\r\n    this.dialogVisible = this.openOutbound;\r\n  },\r\n  methods: {\r\n    warehouseConfirm() {\r\n\r\n    },\r\n    outboundOpen() {\r\n      if (this.outboundFormProp) {\r\n        // 使用深拷贝避免直接修改prop\r\n        this.outboundForm = JSON.parse(JSON.stringify(this.outboundFormProp));\r\n      } else if (this.outboundData) {\r\n        // 使用深拷贝避免直接修改prop\r\n        this.outboundForm = JSON.parse(JSON.stringify(this.outboundData));\r\n      }\r\n    },\r\n    closeOutbound() {\r\n      this.dialogVisible = false;\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      })\r\n    },\r\n    warehouseRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 4\r\n      this.openOutbound = true\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    currency,\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓/2:直接出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      // 扣货不可以出仓\r\n      this.selectOutboundList.map(item => {\r\n        if (item.cargoDeduction == 1) {\r\n          this.$message.error(\"有扣货库存请重新勾选，流水号：\" + item.inboundSerialNoSub)\r\n          return\r\n        }\r\n      })\r\n\r\n      this.selectOutboundList.map(item => {\r\n        item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n      })\r\n\r\n      // 更新箱数、毛重、体积\r\n      this.outboundForm.totalBoxes = 0\r\n      this.outboundForm.totalGrossWeight = 0\r\n      this.outboundForm.totalVolume = 0\r\n      this.selectOutboundList.map(item => {\r\n        item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n          this.outboundForm.totalBoxes = currency(item.boxCount).add(this.outboundForm.totalBoxes).value\r\n          this.outboundForm.totalGrossWeight = currency(item.unitGrossWeight).add(this.outboundForm.totalGrossWeight).value\r\n          this.outboundForm.totalVolume = currency(item.unitVolume).add(this.outboundForm.totalVolume).value\r\n          return item\r\n        }) : null\r\n        return item\r\n      })\r\n      if (type === 0) {\r\n        // this.outboundForm.pre\r\n        addOutboundrecord(this.outboundForm).then(response => {\r\n          // 列表克隆一份,打上预出仓标志\r\n          let data = this.selectOutboundList.map(item => {\r\n            item.preOutboundFlag = \"1\"\r\n            item.preOutboundRecordId = response.data\r\n            item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n              item.preOutboundFlag = \"1\"\r\n              return item\r\n            }) : null\r\n            return item\r\n          })\r\n\r\n          preOutboundInventory(data).then(response => {\r\n            this.getList()\r\n            this.$message.success(\"预出仓成功\")\r\n            this.$emit('closeOutbound')\r\n          })\r\n        })\r\n      }\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.loading = true;\r\n\r\n      // 构建查询参数\r\n      const queryParams = {\r\n        sqdPlannedOutboundDate: this.outboundForm.plannedOutboundDate,\r\n        clientCode: this.outboundForm.clientCode,\r\n        inventoryStatus: \"0\"\r\n      };\r\n\r\n      // 根据出库类型添加预出库标志\r\n      if (this.outboundType === 1) {\r\n        queryParams.preOutboundFlag = \"1\";\r\n      }\r\n\r\n      // 发起请求\r\n      listInventorys(queryParams)\r\n        .then(response => {\r\n          // 处理响应数据\r\n          this.preOutboundInventoryList = response.rows.filter(item => !item.packageTo)\r\n          this.preOutboundInventoryList ? response.rows.map(item => {\r\n            // 计算补收入仓费\r\n            if (item.includesInboundFee === 0) {\r\n              const receivedFee = Number(item.receivedStorageFee || 0);\r\n              const inboundFee = Number(item.inboundFee || 0);\r\n              const difference = currency(inboundFee).subtract(receivedFee).value;\r\n\r\n              // 只有当差值大于0时才设置补收费用\r\n              item.additionalStorageFee = difference > 0 ? difference : 0;\r\n            } else {\r\n              item.additionalStorageFee = 0;\r\n            }\r\n\r\n            // 如果是打包箱，标记为有子节点\r\n            if (item.packageRecord === \"1\") {\r\n              item.hasChildren = true\r\n            }\r\n\r\n            return item;\r\n          }) : [];\r\n\r\n          // 更新总数\r\n          this.total = response.total || 0;\r\n\r\n          // 如果是普通出库类型，自动选中预出库标记的行\r\n          if (this.outboundType === 0 && this.$refs.table) {\r\n            this.$nextTick(() => {\r\n              this.preOutboundInventoryList.forEach(item => {\r\n                if (item.preOutboundFlag === 1) {\r\n                  this.$refs.table.toggleRowSelection(item, true);\r\n                }\r\n              });\r\n            });\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"加载预出库库存列表失败:\", error);\r\n          this.$message.error(\"加载预出库库存列表失败\");\r\n        })\r\n        .finally(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n    // 选择预出仓记录\r\n    handleOutbound(selectedRows) {\r\n      this.outboundReset()\r\n      this.outboundForm = selectedRows\r\n      this.outboundType = 1\r\n      this.loadPreOutboundInventoryList()\r\n      this.openOutbound = true\r\n    },\r\n    // 添加预出仓记录\r\n    handlePreOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 0\r\n      this.openOutbound = true\r\n    },\r\n    // 直接出仓\r\n    handleDirectOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 2\r\n      this.openOutbound = true\r\n    },\r\n    // 结算仓租\r\n    handleRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 3\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.outboundCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\"\r\n      ]\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\" // 第一列显示文本\r\n        } else {\r\n          const prop = column.property\r\n          if (statisticalField.includes(prop)) {\r\n            const total = this.selectOutboundList.reduce((sum, row) => currency(sum).add(Number(row[prop]) || 0).value, 0)\r\n            sums[index] = total\r\n            // 将汇总结果存储在 summaryResults 对象中\r\n            summaryResults[column.property] = total\r\n          } else {\r\n            sums[index] = \" \"\r\n          }\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n\r\n      return sums\r\n    },\r\n    handleOutboundSelectionChange(selection) {\r\n      // 正确获取表格数据 - 通过data属性\r\n      const treeData = this.$refs.table.store.states.data;\r\n      // 获取之前的选择状态，用于比较变化\r\n      const previousIds = [...this.ids];\r\n\r\n      // 清空当前选择\r\n      this.ids = [];\r\n      this.ids = selection.map(item => item.inventoryId);\r\n      // 找出新选中和取消选中的项\r\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id));\r\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id));\r\n\r\n      this.selectOutboundList = selection\r\n      this.$refs.table.doLayout() // 刷新表格布局\r\n\r\n      // 根据仓租结算至（rental_settlement_date），计算该条库存的租金\r\n      // （ 出库当天-仓租结算至-免租期 ） * 租金单价\r\n      selection.map(item => {\r\n        const date1 = moment(this.outboundForm.outboundDate)\r\n        const date2 = moment(item.rentalSettlementDate)\r\n        item.rentalDays = date1.diff(date2, \"days\") + 1 // 差距的天数\r\n        let volumn = item.totalVolume\r\n\r\n        if (!Number.isNaN(item.rentalDays) && item.rentalDays > 0) {\r\n          // 出仓方式不是整柜没有免租天数\r\n          if (this.outboundForm.outboundType !== \"整柜\") {\r\n            item.overdueRentalFee = currency(item.rentalDays).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          } else {\r\n            let days = currency(item.rentalDays).subtract(item.freeStackPeriod).value\r\n            days = days > 0 ? days : 0\r\n            item.rentalDays = days\r\n            item.overdueRentalFee = currency(days).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          }\r\n        }\r\n\r\n        // 处理新选中的打包箱：自动选中其子项\r\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\r\n          // 如果是新选中的打包箱节点\r\n\r\n          // 在树形表格数据中找到对应的节点\r\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId);\r\n\r\n          // 检查节点是否已展开(已有children属性且有内容)\r\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n            // 如果节点已展开，直接选中其所有子项\r\n            setTimeout(() => {\r\n              parentNode.children.forEach(child => {\r\n                if (!this.ids.includes(child.inventoryId)) {\r\n                  this.ids.push(child.inventoryId);\r\n                  this.selectOutboundList.push(child);\r\n                  this.$refs.table.toggleRowSelection(child, true);\r\n                }\r\n              });\r\n            }, 50); // 给一点时间让UI更新\r\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\r\n            // 如果节点未展开且未加载过但有子节点标记\r\n            parentNode.childrenLoaded = true;\r\n\r\n            // 手动展开行，触发懒加载\r\n            this.$refs.table.toggleRowExpansion(parentNode, true);\r\n\r\n            // 监听子节点加载完成后再选中它们\r\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\r\n          }\r\n        }\r\n      })\r\n\r\n      // 处理取消选中的打包箱：取消选中其子项\r\n      newlyDeselected.forEach(parentId => {\r\n        // 找出对应的父节点\r\n        const parentNode = treeData.find(node =>\r\n          node.inventoryId === parentId && node.packageRecord === \"1\"\r\n        );\r\n\r\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n          // 取消选中所有子项\r\n          parentNode.children.forEach(child => {\r\n            const childIndex = this.ids.indexOf(child.inventoryId);\r\n            if (childIndex > -1) {\r\n              // 从选中列表中移除\r\n              this.ids.splice(childIndex, 1);\r\n              const itemIndex = this.selectOutboundList.findIndex(\r\n                item => item.inventoryId === child.inventoryId\r\n              );\r\n              if (itemIndex > -1) {\r\n                this.selectOutboundList.splice(itemIndex, 1);\r\n              }\r\n              // 在UI上取消选中\r\n              this.$refs.table.toggleRowSelection(child, false);\r\n            }\r\n          });\r\n        }\r\n      });\r\n\r\n      this.countSummary()\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.clientRow = row\r\n      this.outboundForm.overdueRentalUnitPrice = row.overdueRent\r\n      // this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecord(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        receivedSupplier: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        operationRequirement: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null,\r\n        receivedFromSupplier: null,\r\n        unreceivedFromCustomer: null,\r\n        receivedFromCustomer: null,\r\n        customerReceivableBalance: null,\r\n        payableToWorker: null,\r\n        promissoryNoteSales: null,\r\n        promissoryNoteCost: null,\r\n        promissoryNoteGrossProfit: null,\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\"),\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.outboundRecordId != null) {\r\n            updateOutboundrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addOutboundrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return;\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || '');\r\n          const searchValue = String(this.search);\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue);\r\n        }\r\n      );\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table;\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector('.el-table__body-wrapper');\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll('.el-table__row');\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1;\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent;\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx;\r\n            }\r\n          });\r\n\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex];\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop;\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: 'smooth'\r\n            });\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add('highlight-row');\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove('highlight-row');\r\n            }, 2000);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning('未找到匹配的记录');\r\n      }\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .edit .number .el-input__inner {\r\n  text-align: right;\r\n}\r\n\r\n// 添加高亮样式\r\n::v-deep .highlight-row {\r\n  background-color: #fdf5e6 !important;\r\n  transition: background-color 0.5s;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAuYA,IAAAA,eAAA,GAAAC,OAAA;AAQA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAOA,IAAAG,OAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAD,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAM,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACAC,kBAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,kBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA;MACAC,IAAA;MACAC,YAAA;MACAC,+BAAA;MACAC,MAAA;MACA;MACAC,KAAA;QACA3B,UAAA,GACA;UAAA4B,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,YAAA;QACAzB,YAAA,MAAA0B,eAAA,IAAAC,MAAA;MACA;MACAC,SAAA;MACAC,wBAAA;MACAC,mBAAA;IACA;EACA;EACAC,KAAA;IACA9C,UAAA,WAAAA,WAAA+C,CAAA;MACA,IAAAA,CAAA;QACA,KAAArD,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACAuD,YAAA,WAAAA,aAAAC,MAAA;MACA,KAAAzD,aAAA,GAAAyD,MAAA;IACA;IACAzD,aAAA,WAAAA,cAAAyD,MAAA;MACA,KAAAA,MAAA;QACA,KAAAC,KAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;IACA;IACA,KAAA3D,aAAA,QAAAwD,YAAA;EACA;EACAI,OAAA;IACAC,gBAAA,WAAAA,iBAAA,GAEA;IACAC,YAAA,WAAAA,aAAA;MACA,SAAAC,gBAAA;QACA;QACA,KAAAf,YAAA,GAAAgB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAH,gBAAA;MACA,gBAAAI,YAAA;QACA;QACA,KAAAnB,YAAA,GAAAgB,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAAC,YAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACA,KAAApE,aAAA;IACA;IACA;IACAqE,kBAAA,WAAAA,mBAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA;MAAA,IAAAC,KAAA;MACA;MACA,IAAAC,wBAAA;QAAAC,SAAA,EAAAL,IAAA,CAAAM;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA,IAAAC,IAAA,GAAAD,QAAA,CAAAC,IAAA;;QAEA;QACAP,OAAA,CAAAO,IAAA;QACAT,IAAA,CAAAU,QAAA,GAAAD,IAAA;;QAEA;QACA,IAAAN,KAAA,CAAApE,GAAA,CAAA4E,QAAA,CAAAX,IAAA,CAAAM,WAAA;UACAM,UAAA;YACAH,IAAA,CAAAI,OAAA,WAAAC,KAAA;cACA,KAAAX,KAAA,CAAApE,GAAA,CAAA4E,QAAA,CAAAG,KAAA,CAAAR,WAAA;gBACAH,KAAA,CAAApE,GAAA,CAAAgF,IAAA,CAAAD,KAAA,CAAAR,WAAA;gBACAH,KAAA,CAAArE,kBAAA,CAAAiF,IAAA,CAAAD,KAAA;cACA;cACA;cACAX,KAAA,CAAAa,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,KAAA;YACA;UACA;QACA;MACA;IACA;IACAK,uBAAA,WAAAA,wBAAA;MACA,KAAAC,aAAA;MACA,KAAA1C,YAAA,CAAA2C,eAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAjG,IAAA,CAAAkG,KAAA;MACA,KAAAtD,YAAA;MACA,KAAAe,YAAA;IACA;IACAwC,YAAA,WAAAA,aAAA;MACA,KAAAhD,YAAA,CAAAiD,sBAAA,OAAAC,iBAAA,OAAAlD,YAAA,CAAAxB,cAAA,EAAA2E,GAAA,MAAAnD,YAAA,CAAAoD,oBAAA,EAAAD,GAAA,MAAAnD,YAAA,CAAAf,kBAAA,EAAAkE,GAAA,MAAAnD,YAAA,CAAAd,kBAAA,EAAAiE,GAAA,MAAAnD,YAAA,CAAAb,mBAAA,EAAAgE,GAAA,MAAAnD,YAAA,CAAAqD,gBAAA,EAAAF,GAAA,MAAAnD,YAAA,CAAAsD,iBAAA,EAAAC,KAAA;MACA,KAAAvD,YAAA,CAAAwD,oBAAA,OAAAN,iBAAA,OAAAlD,YAAA,CAAAtB,mBAAA,EAAA6E,KAAA;MACA,KAAAvD,YAAA,CAAAyD,yBAAA,OAAAP,iBAAA,OAAAlD,YAAA,CAAAiD,sBAAA,EAAAS,QAAA,MAAA1D,YAAA,CAAAwD,oBAAA,EAAAD,KAAA;MACA,KAAAvD,YAAA,CAAA2D,eAAA,OAAAT,iBAAA,OAAAlD,YAAA,CAAA4D,oBAAA,EAAAT,GAAA,MAAAnD,YAAA,CAAA6D,kBAAA,EAAAV,GAAA,MAAAnD,YAAA,CAAAvB,gBAAA,EAAA8E,KAAA;MACA,KAAAvD,YAAA,CAAA8D,oBAAA,OAAAZ,iBAAA,OAAAlD,YAAA,CAAA+D,gBAAA,EAAAZ,GAAA,MAAAnD,YAAA,CAAAhB,kBAAA,EAAAuE,KAAA;MACA,KAAAvD,YAAA,CAAAgE,mBAAA,OAAAd,iBAAA,OAAAlD,YAAA,CAAAiD,sBAAA,EAAAE,GAAA,MAAAnD,YAAA,CAAA8D,oBAAA,EAAAP,KAAA;MACA,KAAAvD,YAAA,CAAAiE,kBAAA,OAAAf,iBAAA,OAAAlD,YAAA,CAAA2D,eAAA,EAAAR,GAAA,MAAAnD,YAAA,CAAAb,mBAAA,EAAAgE,GAAA,MAAAnD,YAAA,CAAAkE,wBAAA,EAAAX,KAAA;MACA,KAAAvD,YAAA,CAAAmE,yBAAA,OAAAjB,iBAAA,OAAAlD,YAAA,CAAAgE,mBAAA,EAAAN,QAAA,MAAA1D,YAAA,CAAAiE,kBAAA,EAAAV,KAAA;IACA;IACAL,QAAA,EAAAA,iBAAA;IACA;AACA;AACA;AACA;IACAkB,eAAA,WAAAA,gBAAAC,IAAA;MAAA,IAAAC,MAAA;MACA;MACA,KAAAlH,kBAAA,CAAAmH,GAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAC,cAAA;UACAH,MAAA,CAAAI,QAAA,CAAAC,KAAA,qBAAAH,IAAA,CAAAI,kBAAA;UACA;QACA;MACA;MAEA,KAAAxH,kBAAA,CAAAmH,GAAA,WAAAC,IAAA;QACAA,IAAA,CAAAK,mBAAA,GAAAC,MAAA,CAAAN,IAAA,CAAAK,mBAAA;MACA;;MAEA;MACA,KAAA7E,YAAA,CAAApB,UAAA;MACA,KAAAoB,YAAA,CAAAnB,gBAAA;MACA,KAAAmB,YAAA,CAAAlB,WAAA;MACA,KAAA1B,kBAAA,CAAAmH,GAAA,WAAAC,IAAA;QACAA,IAAA,CAAAO,kBAAA,GAAAP,IAAA,CAAAO,kBAAA,CAAAR,GAAA,WAAAC,IAAA;UACAF,MAAA,CAAAtE,YAAA,CAAApB,UAAA,OAAAsE,iBAAA,EAAAsB,IAAA,CAAAQ,QAAA,EAAA7B,GAAA,CAAAmB,MAAA,CAAAtE,YAAA,CAAApB,UAAA,EAAA2E,KAAA;UACAe,MAAA,CAAAtE,YAAA,CAAAnB,gBAAA,OAAAqE,iBAAA,EAAAsB,IAAA,CAAAS,eAAA,EAAA9B,GAAA,CAAAmB,MAAA,CAAAtE,YAAA,CAAAnB,gBAAA,EAAA0E,KAAA;UACAe,MAAA,CAAAtE,YAAA,CAAAlB,WAAA,OAAAoE,iBAAA,EAAAsB,IAAA,CAAAU,UAAA,EAAA/B,GAAA,CAAAmB,MAAA,CAAAtE,YAAA,CAAAlB,WAAA,EAAAyE,KAAA;UACA,OAAAiB,IAAA;QACA;QACA,OAAAA,IAAA;MACA;MACA,IAAAH,IAAA;QACA;QACA,IAAAc,iCAAA,OAAAnF,YAAA,EAAA6B,IAAA,WAAAC,QAAA;UACA;UACA,IAAA/E,IAAA,GAAAuH,MAAA,CAAAlH,kBAAA,CAAAmH,GAAA,WAAAC,IAAA;YACAA,IAAA,CAAAY,eAAA;YACAZ,IAAA,CAAAa,mBAAA,GAAAvD,QAAA,CAAA/E,IAAA;YACAyH,IAAA,CAAAO,kBAAA,GAAAP,IAAA,CAAAO,kBAAA,CAAAR,GAAA,WAAAC,IAAA;cACAA,IAAA,CAAAY,eAAA;cACA,OAAAZ,IAAA;YACA;YACA,OAAAA,IAAA;UACA;UAEA,IAAAc,+BAAA,EAAAvI,IAAA,EAAA8E,IAAA,WAAAC,QAAA;YACAwC,MAAA,CAAAiB,OAAA;YACAjB,MAAA,CAAAI,QAAA,CAAAc,OAAA;YACAlB,MAAA,CAAA5D,KAAA;UACA;QACA;MACA;IACA;IACA;IACA+E,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,KAAAvI,OAAA;;MAEA;MACA,IAAAU,WAAA;QACA8H,sBAAA,OAAA3F,YAAA,CAAA4F,mBAAA;QACA3H,UAAA,OAAA+B,YAAA,CAAA/B,UAAA;QACA4H,eAAA;MACA;;MAEA;MACA,SAAApG,YAAA;QACA5B,WAAA,CAAAuH,eAAA;MACA;;MAEA;MACA,IAAAU,yBAAA,EAAAjI,WAAA,EACAgE,IAAA,WAAAC,QAAA;QACA;QACA4D,MAAA,CAAAtF,wBAAA,GAAA0B,QAAA,CAAAC,IAAA,CAAAgE,MAAA,WAAAvB,IAAA;UAAA,QAAAA,IAAA,CAAA7C,SAAA;QAAA;QACA+D,MAAA,CAAAtF,wBAAA,GAAA0B,QAAA,CAAAC,IAAA,CAAAwC,GAAA,WAAAC,IAAA;UACA;UACA,IAAAA,IAAA,CAAAwB,kBAAA;YACA,IAAAC,WAAA,GAAAnB,MAAA,CAAAN,IAAA,CAAAxF,kBAAA;YACA,IAAAkH,UAAA,GAAApB,MAAA,CAAAN,IAAA,CAAA0B,UAAA;YACA,IAAAC,UAAA,OAAAjD,iBAAA,EAAAgD,UAAA,EAAAxC,QAAA,CAAAuC,WAAA,EAAA1C,KAAA;;YAEA;YACAiB,IAAA,CAAApB,oBAAA,GAAA+C,UAAA,OAAAA,UAAA;UACA;YACA3B,IAAA,CAAApB,oBAAA;UACA;;UAEA;UACA,IAAAoB,IAAA,CAAA4B,aAAA;YACA5B,IAAA,CAAA6B,WAAA;UACA;UAEA,OAAA7B,IAAA;QACA;;QAEA;QACAkB,MAAA,CAAAjI,KAAA,GAAAqE,QAAA,CAAArE,KAAA;;QAEA;QACA,IAAAiI,MAAA,CAAAjG,YAAA,UAAAiG,MAAA,CAAApD,KAAA,CAAAC,KAAA;UACAmD,MAAA,CAAAY,SAAA;YACAZ,MAAA,CAAAtF,wBAAA,CAAA+B,OAAA,WAAAqC,IAAA;cACA,IAAAA,IAAA,CAAAY,eAAA;gBACAM,MAAA,CAAApD,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAgC,IAAA;cACA;YACA;UACA;QACA;MACA,GACA+B,KAAA,WAAA5B,KAAA;QACA6B,OAAA,CAAA7B,KAAA,iBAAAA,KAAA;QACAe,MAAA,CAAAhB,QAAA,CAAAC,KAAA;MACA,GACA8B,OAAA;QACAf,MAAA,CAAAvI,OAAA;MACA;IACA;IACA;IACAuJ,cAAA,WAAAA,eAAAC,YAAA;MACA,KAAAjE,aAAA;MACA,KAAA1C,YAAA,GAAA2G,YAAA;MACA,KAAAlH,YAAA;MACA,KAAAgG,4BAAA;MACA,KAAAjF,YAAA;IACA;IACA;IACAoG,iBAAA,WAAAA,kBAAA;MACA,KAAAlE,aAAA;MACA,KAAA1C,YAAA,CAAA2C,eAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAjG,IAAA,CAAAkG,KAAA;MACA,KAAAtD,YAAA;MACA,KAAAe,YAAA;IACA;IACA;IACAqG,oBAAA,WAAAA,qBAAA;MACA,KAAAnE,aAAA;MACA,KAAA1C,YAAA,CAAA2C,eAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAjG,IAAA,CAAAkG,KAAA;MACA,KAAAtD,YAAA;MACA,KAAAe,YAAA;IACA;IACA;IACAsG,oBAAA,WAAAA,qBAAA;MACA,KAAApE,aAAA;MACA,KAAA1C,YAAA,CAAA2C,eAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAjG,IAAA,CAAAkG,KAAA;MACA,KAAAtD,YAAA;MACA,KAAAe,YAAA;IACA;IACAuG,SAAA,EAAAA,eAAA;IACAC,wCAAA,WAAAA,yCAAAC,SAAA,EAAAC,GAAA;MACAA,GAAA,CAAAC,wBAAA,GAAAF,SAAA;MACA,KAAA5G,mBAAA,GAAA4G,SAAA;IACA;IACA;IACAG,aAAA,WAAAA,cAAAF,GAAA;MACA,YAAA7G,mBAAA,CAAA4B,QAAA,CAAAiF,GAAA;IACA;IACAG,YAAA,WAAAA,aAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,OAAA,GAAAF,KAAA,CAAAE,OAAA;QAAAzK,IAAA,GAAAuK,KAAA,CAAAvK,IAAA;MACA,IAAA0K,IAAA;MACA,IAAAC,gBAAA,IACA,0EACA,kFACA,sFACA,iEACA;MACA;MACA,IAAAC,cAAA;MACAH,OAAA,CAAArF,OAAA,WAAAyF,MAAA,EAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,IAAA,CAAAI,KAAA;QACA;UACA,IAAAC,IAAA,GAAAF,MAAA,CAAAG,QAAA;UACA,IAAAL,gBAAA,CAAAzF,QAAA,CAAA6F,IAAA;YACA,IAAArK,KAAA,GAAA8J,MAAA,CAAAnK,kBAAA,CAAA4K,MAAA,WAAAC,GAAA,EAAAf,GAAA;cAAA,WAAAhE,iBAAA,EAAA+E,GAAA,EAAA9E,GAAA,CAAA2B,MAAA,CAAAoC,GAAA,CAAAY,IAAA,SAAAvE,KAAA;YAAA;YACAkE,IAAA,CAAAI,KAAA,IAAApK,KAAA;YACA;YACAkK,cAAA,CAAAC,MAAA,CAAAG,QAAA,IAAAtK,KAAA;UACA;YACAgK,IAAA,CAAAI,KAAA;UACA;QACA;MACA;;MAEA;MACA;MACAK,MAAA,CAAAC,IAAA,CAAAR,cAAA,EAAAxF,OAAA,WAAAiG,KAAA;QACA,IAAAb,MAAA,CAAAvH,YAAA;UACAuH,MAAA,CAAAvH,YAAA,CAAAoI,KAAA,IAAAT,cAAA,CAAAS,KAAA;QACA;MACA;;MAEA,KAAApF,YAAA;MAEA,OAAAyE,IAAA;IACA;IACAY,6BAAA,WAAAA,8BAAApB,SAAA;MAAA,IAAAqB,MAAA;MACA;MACA,IAAAC,QAAA,QAAAjG,KAAA,CAAAC,KAAA,CAAAiG,KAAA,CAAAC,MAAA,CAAA1L,IAAA;MACA;MACA,IAAA2L,WAAA,OAAAC,mBAAA,CAAAC,OAAA,OAAAvL,GAAA;;MAEA;MACA,KAAAA,GAAA;MACA,KAAAA,GAAA,GAAA4J,SAAA,CAAA1C,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA5C,WAAA;MAAA;MACA;MACA,IAAAiH,aAAA,QAAAxL,GAAA,CAAA0I,MAAA,WAAA+C,EAAA;QAAA,QAAAJ,WAAA,CAAAzG,QAAA,CAAA6G,EAAA;MAAA;MACA,IAAAC,eAAA,GAAAL,WAAA,CAAA3C,MAAA,WAAA+C,EAAA;QAAA,QAAAR,MAAA,CAAAjL,GAAA,CAAA4E,QAAA,CAAA6G,EAAA;MAAA;MAEA,KAAA1L,kBAAA,GAAA6J,SAAA;MACA,KAAA3E,KAAA,CAAAC,KAAA,CAAAyG,QAAA;;MAEA;MACA;MACA/B,SAAA,CAAA1C,GAAA,WAAAC,IAAA;QACA,IAAAyE,KAAA,OAAAhJ,eAAA,EAAAqI,MAAA,CAAAtI,YAAA,CAAAzB,YAAA;QACA,IAAA2K,KAAA,OAAAjJ,eAAA,EAAAuE,IAAA,CAAA2E,oBAAA;QACA3E,IAAA,CAAA4E,UAAA,GAAAH,KAAA,CAAAI,IAAA,CAAAH,KAAA;QACA,IAAAI,MAAA,GAAA9E,IAAA,CAAA1F,WAAA;QAEA,KAAAgG,MAAA,CAAAyE,KAAA,CAAA/E,IAAA,CAAA4E,UAAA,KAAA5E,IAAA,CAAA4E,UAAA;UACA;UACA,IAAAd,MAAA,CAAAtI,YAAA,CAAAP,YAAA;YACA+E,IAAA,CAAAnB,gBAAA,OAAAH,iBAAA,EAAAsB,IAAA,CAAA4E,UAAA,EAAAI,QAAA,CAAAhF,IAAA,CAAAiF,sBAAA,EAAAD,QAAA,CAAAF,MAAA,EAAA/F,KAAA;UACA;YACA,IAAAmG,IAAA,OAAAxG,iBAAA,EAAAsB,IAAA,CAAA4E,UAAA,EAAA1F,QAAA,CAAAc,IAAA,CAAAmF,eAAA,EAAApG,KAAA;YACAmG,IAAA,GAAAA,IAAA,OAAAA,IAAA;YACAlF,IAAA,CAAA4E,UAAA,GAAAM,IAAA;YACAlF,IAAA,CAAAnB,gBAAA,OAAAH,iBAAA,EAAAwG,IAAA,EAAAF,QAAA,CAAAhF,IAAA,CAAAiF,sBAAA,EAAAD,QAAA,CAAAF,MAAA,EAAA/F,KAAA;UACA;QACA;;QAEA;QACA,IAAAiB,IAAA,CAAA4B,aAAA,YAAAyC,aAAA,CAAA5G,QAAA,CAAAuC,IAAA,CAAA5C,WAAA;UACA;;UAEA;UACA,IAAAgI,UAAA,GAAArB,QAAA,CAAAsB,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAlI,WAAA,KAAA4C,IAAA,CAAA5C,WAAA;UAAA;;UAEA;UACA,IAAAgI,UAAA,IAAAA,UAAA,CAAA5H,QAAA,IAAA4H,UAAA,CAAA5H,QAAA,CAAA+H,MAAA;YACA;YACA7H,UAAA;cACA0H,UAAA,CAAA5H,QAAA,CAAAG,OAAA,WAAAC,KAAA;gBACA,KAAAkG,MAAA,CAAAjL,GAAA,CAAA4E,QAAA,CAAAG,KAAA,CAAAR,WAAA;kBACA0G,MAAA,CAAAjL,GAAA,CAAAgF,IAAA,CAAAD,KAAA,CAAAR,WAAA;kBACA0G,MAAA,CAAAlL,kBAAA,CAAAiF,IAAA,CAAAD,KAAA;kBACAkG,MAAA,CAAAhG,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,KAAA;gBACA;cACA;YACA;UACA,WAAAwH,UAAA,KAAAA,UAAA,CAAAI,cAAA,IAAAJ,UAAA,CAAAvD,WAAA;YACA;YACAuD,UAAA,CAAAI,cAAA;;YAEA;YACA1B,MAAA,CAAAhG,KAAA,CAAAC,KAAA,CAAA0H,kBAAA,CAAAL,UAAA;;YAEA;YACA;UACA;QACA;MACA;;MAEA;MACAb,eAAA,CAAA5G,OAAA,WAAA+H,QAAA;QACA;QACA,IAAAN,UAAA,GAAArB,QAAA,CAAAsB,IAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAAlI,WAAA,KAAAsI,QAAA,IAAAJ,IAAA,CAAA1D,aAAA;QAAA,CACA;QAEA,IAAAwD,UAAA,IAAAA,UAAA,CAAA5H,QAAA,IAAA4H,UAAA,CAAA5H,QAAA,CAAA+H,MAAA;UACA;UACAH,UAAA,CAAA5H,QAAA,CAAAG,OAAA,WAAAC,KAAA;YACA,IAAA+H,UAAA,GAAA7B,MAAA,CAAAjL,GAAA,CAAA+M,OAAA,CAAAhI,KAAA,CAAAR,WAAA;YACA,IAAAuI,UAAA;cACA;cACA7B,MAAA,CAAAjL,GAAA,CAAAgN,MAAA,CAAAF,UAAA;cACA,IAAAG,SAAA,GAAAhC,MAAA,CAAAlL,kBAAA,CAAAmN,SAAA,CACA,UAAA/F,IAAA;gBAAA,OAAAA,IAAA,CAAA5C,WAAA,KAAAQ,KAAA,CAAAR,WAAA;cAAA,CACA;cACA,IAAA0I,SAAA;gBACAhC,MAAA,CAAAlL,kBAAA,CAAAiN,MAAA,CAAAC,SAAA;cACA;cACA;cACAhC,MAAA,CAAAhG,KAAA,CAAAC,KAAA,CAAAC,kBAAA,CAAAJ,KAAA;YACA;UACA;QACA;MACA;MAEA,KAAAY,YAAA;IACA;IACAwH,mBAAA,WAAAA,oBAAAnG,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAArE,YAAA,CAAAxB,cAAA,QAAA2B,SAAA,CAAAsK,QAAA;UACA;QACA;UACA,KAAAzK,YAAA,CAAAxB,cAAA,QAAA2B,SAAA,CAAAuK,QAAA;UACA;MAEA;IACA;IACAC,cAAA,WAAAA,eAAAzD,GAAA;MACA,KAAAlH,YAAA,CAAAxB,cAAA,GAAA0I,GAAA,CAAA0D,OAAA;MACA,KAAA5K,YAAA,CAAAV,aAAA,GAAA4H,GAAA,CAAAyC,eAAA;MACA,KAAAxJ,SAAA,GAAA+G,GAAA;MACA,KAAAlH,YAAA,CAAAyJ,sBAAA,GAAAvC,GAAA,CAAA7H,WAAA;MACA;MACA;MACA,KAAAwL,YAAA;IACA;IACA,eACAtF,OAAA,WAAAA,QAAA;MAAA,IAAAuF,MAAA;MACA,KAAA3N,OAAA;MACA,IAAA4N,kCAAA,OAAAlN,WAAA,EAAAgE,IAAA,WAAAC,QAAA;QACAgJ,MAAA,CAAApN,kBAAA,GAAAoE,QAAA,CAAAC,IAAA;QACA+I,MAAA,CAAArN,KAAA,GAAAqE,QAAA,CAAArE,KAAA;QACAqN,MAAA,CAAA3N,OAAA;MACA;IACA;IACA;IACA6N,MAAA,WAAAA,OAAA;MACA,KAAApN,IAAA;MACA,KAAAqN,KAAA;IACA;IACAvI,aAAA,WAAAA,cAAA;MACA,KAAA1C,YAAA;QACAkL,gBAAA;QACAnH,gBAAA;QACA/F,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAE,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACA8L,oBAAA;QACA7L,aAAA;QACAC,gBAAA;QACAuE,oBAAA;QACAb,sBAAA;QACAO,oBAAA;QACAC,yBAAA;QACAE,eAAA;QACAK,mBAAA;QACAC,kBAAA;QACAE,yBAAA;QACA5F,YAAA,MAAA0B,eAAA,IAAAC,MAAA;MACA;MACA,KAAAE,wBAAA;MACA,KAAAgL,SAAA;IACA;IACA;IACAH,KAAA,WAAAA,MAAA;MACA,KAAAzL,IAAA;QACAjB,YAAA,MAAA0B,eAAA,IAAAC,MAAA;QACAgL,gBAAA;QACAlN,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAE,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA,KAAA6L,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAxN,WAAA,CAAAC,OAAA;MACA,KAAAyH,OAAA;IACA;IACA,aACA+F,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAArE,GAAA;MAAA,IAAAsE,MAAA;MACA,IAAAC,IAAA,GAAAvE,GAAA,CAAAwE,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAA5J,IAAA;QACA,WAAAgK,4BAAA,EAAA3E,GAAA,CAAAgE,gBAAA,EAAAhE,GAAA,CAAAwE,MAAA;MACA,GAAA7J,IAAA;QACA2J,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAAlF,KAAA;QACAW,GAAA,CAAAwE,MAAA,GAAAxE,GAAA,CAAAwE,MAAA;MACA;IACA;IACA;IACAK,qBAAA,WAAAA,sBAAA9E,SAAA;MACA,KAAA5J,GAAA,GAAA4J,SAAA,CAAA1C,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA0G,gBAAA;MAAA;MACA,KAAA5N,MAAA,GAAA2J,SAAA,CAAA8C,MAAA;MACA,KAAAxM,QAAA,IAAA0J,SAAA,CAAA8C,MAAA;IACA;IACA,aACAiC,SAAA,WAAAA,UAAA;MACA,KAAAf,KAAA;MACA,KAAArN,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAsO,YAAA,WAAAA,aAAA/E,GAAA;MAAA,IAAAgF,MAAA;MACA,KAAAjB,KAAA;MACA,IAAAC,gBAAA,GAAAhE,GAAA,CAAAgE,gBAAA,SAAA7N,GAAA;MACA,IAAA8O,iCAAA,EAAAjB,gBAAA,EAAArJ,IAAA,WAAAC,QAAA;QACAoK,MAAA,CAAA1M,IAAA,GAAAsC,QAAA,CAAA/E,IAAA;QACAmP,MAAA,CAAAtO,IAAA;QACAsO,MAAA,CAAAvO,KAAA;MACA;IACA;IACA,WACAyO,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAA/J,KAAA,SAAAgK,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,MAAA,CAAA7M,IAAA,CAAA0L,gBAAA;YACA,IAAAsB,oCAAA,EAAAH,MAAA,CAAA7M,IAAA,EAAAqC,IAAA,WAAAC,QAAA;cACAuK,MAAA,CAAAV,MAAA,CAAAG,UAAA;cACAO,MAAA,CAAAzO,IAAA;cACAyO,MAAA,CAAA9G,OAAA;YACA;UACA;YACA,IAAAJ,iCAAA,EAAAkH,MAAA,CAAA7M,IAAA,EAAAqC,IAAA,WAAAC,QAAA;cACAuK,MAAA,CAAAV,MAAA,CAAAG,UAAA;cACAO,MAAA,CAAAzO,IAAA;cACAyO,MAAA,CAAA9G,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAkH,YAAA,WAAAA,aAAAvF,GAAA;MAAA,IAAAwF,OAAA;MACA,IAAAC,iBAAA,GAAAzF,GAAA,CAAAgE,gBAAA,SAAA7N,GAAA;MACA,KAAAsO,MAAA,CAAAC,OAAA,qBAAAe,iBAAA,cAAA9K,IAAA;QACA,WAAA+K,iCAAA,EAAAD,iBAAA;MACA,GAAA9K,IAAA;QACA6K,OAAA,CAAAnH,OAAA;QACAmH,OAAA,CAAAf,MAAA,CAAAG,UAAA;MACA,GAAAvF,KAAA,cACA;IACA;IACA,aACAsG,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,qCAAAC,cAAA,CAAAnE,OAAA,MACA,KAAA/K,WAAA,qBAAAmP,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,OAAA;MACA,UAAAzN,MAAA;;MAEA;MACA,IAAAkI,KAAA,QAAAzH,wBAAA,CAAAmK,SAAA,CACA,UAAA/F,IAAA;QACA;QACA,IAAA6I,QAAA,GAAAC,MAAA,CAAA9I,IAAA,CAAA+I,eAAA;QACA,IAAAC,WAAA,GAAAF,MAAA,CAAAF,OAAA,CAAAzN,MAAA;QACA;QACA,OAAA0N,QAAA,CAAApL,QAAA,CAAAuL,WAAA;MACA,CACA;MAEA,IAAA3F,KAAA;QACA;QACA,IAAAtF,KAAA,QAAAD,KAAA,CAAAC,KAAA;QAEA,KAAA+D,SAAA;UACA;UACA,IAAAmH,aAAA,GAAAlL,KAAA,CAAAmL,GAAA,CAAAC,aAAA;UACA;UACA,IAAA5L,IAAA,GAAA0L,aAAA,CAAAG,gBAAA;;UAEA;UACA,IAAAC,WAAA;UACA9L,IAAA,CAAAI,OAAA,WAAA+E,GAAA,EAAA4G,GAAA;YACA,IAAAC,OAAA,GAAA7G,GAAA,CAAA8G,WAAA;YACA,IAAAD,OAAA,CAAA9L,QAAA,CAAAmL,OAAA,CAAAzN,MAAA;cACAkO,WAAA,GAAAC,GAAA;YACA;UACA;UAGA,IAAAD,WAAA;YACA,IAAAI,SAAA,GAAAlM,IAAA,CAAA8L,WAAA;YACA;YACA,IAAAK,MAAA,GAAAD,SAAA,CAAAE,SAAA;;YAEA;YACAV,aAAA,CAAAW,QAAA;cACAC,GAAA,EAAAH,MAAA,GAAAT,aAAA,CAAAa,YAAA;cACAC,QAAA;YACA;;YAEA;YACAN,SAAA,CAAAO,SAAA,CAAArL,GAAA;YACA;YACAjB,UAAA;cACA+L,SAAA,CAAAO,SAAA,CAAAC,MAAA;YACA;UACA;QACA;MACA;QACA,KAAA/J,QAAA,CAAAgK,OAAA;MACA;IACA;EACA;AACA;AAAAC,OAAA,CAAA/F,OAAA,GAAAgG,QAAA"}]}