{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\WhsComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\WhsComponent.vue", "mtime": 1754881964239}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_audit", "_interopRequireDefault", "require", "_logisticsProgress", "_chargeList", "_debitNodeList", "name", "components", "DebitNoteList", "Audit", "LogisticsProgress", "ChargeList", "props", "whsServices", "type", "Array", "Set", "default", "_default", "form", "Object", "branchInfo", "Boolean", "logisticsInfo", "chargeInfo", "auditInfo", "disabled", "booking", "psaVerify", "supplierList", "companyList", "foldState", "serviceInstance", "serviceObject", "formDisable", "computed", "isDisabled", "methods", "handleAddDebitNote", "row", "sqdRctNo", "rctNo", "rctId", "isRecievingOrPaying", "rsChargeList", "$emit", "getSupplierEmail", "getServiceInstance", "supplierId", "supplier", "find", "v", "companyId", "staffEmail", "getAgreementDisplay", "agreementTypeCode", "agreementNo", "changeFold", "serviceTypeId", "getFold", "getServiceObject", "getPayable", "payable", "getFormDisable", "changeServiceObject", "auditCharge", "event", "generateFreight", "type1", "type2", "item", "psaBookingCancel", "copyFreight", "calculateCharge", "outboundPlan", "deleteLogItem", "rsOpLogList", "filter", "updateLogList", "deleteAllCharge", "deleteChargeItem", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/WhsComponent.vue"], "sourcesContent": ["<template>\r\n  <div class=\"whs-component\">\r\n    <!--仓储-->\r\n    <div v-for=\"(item, index) in whsServices\" :key=\"`whs-${index}`\" class=\"whs-item\">\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <!--标题栏-->\r\n          <div class=\"service-bar\">\r\n            <a\r\n              :class=\"[\r\n                'service-toggle-icon',\r\n                getFold(item.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n            />\r\n            <h3 class=\"service-title\" @click=\"changeFold(item.serviceTypeId)\">\r\n              仓储-{{ item.serviceShortName }}\r\n            </h3>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"getServiceInstance(item.serviceTypeId)\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(item.serviceTypeId)\"\r\n              :rs-charge-list=\"item.rsChargeList\"\r\n              @auditFee=\"auditCharge(item.serviceTypeId, $event)\"\r\n              @return=\"changeServiceObject(item.serviceTypeId, $event)\"\r\n            />\r\n            <div class=\"outbound-plan-container\">\r\n              <a\r\n                class=\"outbound-plan-link\"\r\n                target=\"_blank\"\r\n                @click=\"outboundPlan\"\r\n              >\r\n                [出仓计划]\r\n              </a>\r\n            </div>\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(item.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(item.serviceTypeId).inquiryNo\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(8, item.serviceTypeId, getServiceObject(item.serviceTypeId))\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail()\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <template #reference>\r\n                    <el-input\r\n                      :value=\"getServiceInstance().supplierName\"\r\n                      class=\"disable-form\"\r\n                      disabled\r\n                    />\r\n                  </template>\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(item.serviceTypeId)\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(item.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--主表信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\">\r\n              <el-form-item label=\"商务单号\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input :value=\"form.psaNo\" class=\"disable-form\" disabled/>\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\">\r\n              <el-form-item label=\"入仓号\">\r\n                <el-input\r\n                  v-model=\"getServiceObject(item.serviceTypeId).warehousingNo\"\r\n                  :class=\"psaVerify || disabled ? 'disable-form' : ''\"\r\n                  :disabled=\"psaVerify || disabled\"\r\n                  placeholder=\"入仓号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--占位-->\r\n          <el-col v-if=\"branchInfo\" :span=\"9\"></el-col>\r\n\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getFormDisable() || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject().rsOpLogList || []\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"80\"\r\n                @deleteItem=\"deleteLogItem(item.serviceTypeId, $event)\"\r\n                @return=\"updateLogList(item.serviceTypeId, $event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject().rsChargeList || []\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable() || disabled\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject().payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject().payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject().payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject().payableUSDTax\"\r\n              :service-type-id=\"item.serviceTypeId\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(item.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(item.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(item.serviceTypeId, $event, getServiceObject())\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"getServiceObject().rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(getServiceObject())\"\r\n              @deleteItem=\"getServiceObject().rsDebitNoteList = getServiceObject().rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(item.serviceTypeId,$event,getServiceObject())\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"WhsComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    // 码头与仓储服务数据集合\r\n    whsServices: {\r\n      type: [Array, Set],\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增的props，直接从父组件传入\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail() {\r\n      const serviceInstance = this.getServiceInstance()\r\n      if (!serviceInstance || !serviceInstance.supplierId) return ''\r\n\r\n      const supplier = this.supplierList.find(v => v.companyId === serviceInstance.supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay() {\r\n      const serviceInstance = this.getServiceInstance()\r\n      if (!serviceInstance) return ''\r\n      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo\r\n    },\r\n    // 事件转发给父组件\r\n    changeFold(serviceTypeId) {\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    getFold() {\r\n      return this.foldState\r\n    },\r\n    getServiceInstance() {\r\n      return this.serviceInstance || {}\r\n    },\r\n    getServiceObject() {\r\n      return this.serviceObject || {}\r\n    },\r\n    getPayable() {\r\n      return this.serviceObject && this.serviceObject.payable || null\r\n    },\r\n    getFormDisable() {\r\n      return this.formDisable\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      this.$emit(\"changeServiceObject\", serviceTypeId, serviceObject)\r\n    },\r\n    auditCharge(serviceTypeId, event) {\r\n      this.$emit(\"auditCharge\", serviceTypeId, event)\r\n    },\r\n    generateFreight(type1, type2, item) {\r\n      this.$emit(\"generateFreight\", type1, type2, item)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, item) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, item)\r\n    },\r\n    outboundPlan() {\r\n      this.$emit(\"outboundPlan\")\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject()\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// WHS组件特定样式\r\n.whs-component {\r\n  width: 100%;\r\n\r\n  .whs-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title {\r\n      margin: 0;\r\n      width: 250px;\r\n      text-align: left;\r\n      cursor: pointer;\r\n    }\r\n\r\n    .outbound-plan-container {\r\n      margin-left: auto;\r\n\r\n      .outbound-plan-link {\r\n        color: blue;\r\n        padding: 0;\r\n        margin-left: 5px;\r\n        text-decoration: none;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n\r\n  // 优化日期选择器样式\r\n  .el-date-picker {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;AA4LA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,cAAA,GAAAJ,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAI,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,KAAA,EAAAA,cAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA;EACA;EACAC,KAAA;IACA;IACAC,WAAA;MACAC,IAAA,GAAAC,KAAA,EAAAC,GAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAC,IAAA;MACAL,IAAA,EAAAM,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAG,UAAA;MACAP,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACAM,aAAA;MACAT,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACAO,UAAA;MACAV,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACAQ,SAAA;MACAX,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACA;IACAS,QAAA;MACAZ,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACAU,OAAA;MACAb,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACAW,SAAA;MACAd,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACA;IACAY,YAAA;MACAf,IAAA,EAAAC,KAAA;MACAE,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAY,WAAA;MACAhB,IAAA,EAAAC,KAAA;MACAE,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACA;IACAa,SAAA;MACAjB,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;IACAe,eAAA;MACAlB,IAAA,EAAAM,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAe,aAAA;MACAnB,IAAA,EAAAM,MAAA;MACAH,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAgB,WAAA;MACApB,IAAA,EAAAQ,OAAA;MACAL,OAAA;IACA;EACA;EACAkB,QAAA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA,YAAAV,QAAA,SAAAE,SAAA;IACA;EACA;EACAS,OAAA;IACAC,kBAAA,WAAAA,mBAAAL,aAAA;MACA,IAAAM,GAAA;MACAA,GAAA,CAAAC,QAAA,QAAArB,IAAA,CAAAsB,KAAA;MACAF,GAAA,CAAAG,KAAA,QAAAvB,IAAA,CAAAuB,KAAA;MACAH,GAAA,CAAAI,mBAAA;MACAJ,GAAA,CAAAK,YAAA;MACA,KAAAC,KAAA,iBAAAN,GAAA,EAAAN,aAAA;IACA;IACA;IACAa,gBAAA,WAAAA,iBAAA;MACA,IAAAd,eAAA,QAAAe,kBAAA;MACA,KAAAf,eAAA,KAAAA,eAAA,CAAAgB,UAAA;MAEA,IAAAC,QAAA,QAAApB,YAAA,CAAAqB,IAAA,WAAAC,CAAA;QAAA,OAAAA,CAAA,CAAAC,SAAA,KAAApB,eAAA,CAAAgB,UAAA;MAAA;MACA,OAAAC,QAAA,GAAAA,QAAA,CAAAI,UAAA;IACA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,IAAAtB,eAAA,QAAAe,kBAAA;MACA,KAAAf,eAAA;MACA,OAAAA,eAAA,CAAAuB,iBAAA,GAAAvB,eAAA,CAAAwB,WAAA;IACA;IACA;IACAC,UAAA,WAAAA,WAAAC,aAAA;MACA,KAAAb,KAAA,eAAAa,aAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAA5B,SAAA;IACA;IACAgB,kBAAA,WAAAA,mBAAA;MACA,YAAAf,eAAA;IACA;IACA4B,gBAAA,WAAAA,iBAAA;MACA,YAAA3B,aAAA;IACA;IACA4B,UAAA,WAAAA,WAAA;MACA,YAAA5B,aAAA,SAAAA,aAAA,CAAA6B,OAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,YAAA7B,WAAA;IACA;IACA8B,mBAAA,WAAAA,oBAAAN,aAAA,EAAAzB,aAAA;MACA,KAAAY,KAAA,wBAAAa,aAAA,EAAAzB,aAAA;IACA;IACAgC,WAAA,WAAAA,YAAAP,aAAA,EAAAQ,KAAA;MACA,KAAArB,KAAA,gBAAAa,aAAA,EAAAQ,KAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,KAAA,EAAAC,KAAA,EAAAC,IAAA;MACA,KAAAzB,KAAA,oBAAAuB,KAAA,EAAAC,KAAA,EAAAC,IAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAA1B,KAAA;IACA;IACA2B,WAAA,WAAAA,YAAAN,KAAA;MACA,KAAArB,KAAA,gBAAAqB,KAAA;IACA;IACAO,eAAA,WAAAA,gBAAAf,aAAA,EAAAQ,KAAA,EAAAI,IAAA;MACA,KAAAzB,KAAA,oBAAAa,aAAA,EAAAQ,KAAA,EAAAI,IAAA;IACA;IACAI,YAAA,WAAAA,aAAA;MACA,KAAA7B,KAAA;IACA;IACA;IACA8B,aAAA,WAAAA,cAAAjB,aAAA,EAAAQ,KAAA;MACA,IAAAjC,aAAA,QAAA2B,gBAAA;MACA,IAAA3B,aAAA,IAAAA,aAAA,CAAA2C,WAAA;QACA3C,aAAA,CAAA2C,WAAA,GAAA3C,aAAA,CAAA2C,WAAA,CAAAC,MAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;IACAY,aAAA,WAAAA,cAAApB,aAAA,EAAAQ,KAAA;MACA,IAAAjC,aAAA,QAAA2B,gBAAA;MACA,IAAA3B,aAAA;QACAA,aAAA,CAAA2C,WAAA,GAAAV,KAAA;MACA;IACA;IACA;IACAa,eAAA,WAAAA,gBAAArB,aAAA;MACA,IAAAzB,aAAA,QAAA2B,gBAAA;MACA,IAAA3B,aAAA;QACAA,aAAA,CAAAW,YAAA;MACA;IACA;IACAoC,gBAAA,WAAAA,iBAAAtB,aAAA,EAAAQ,KAAA;MACA,IAAAjC,aAAA,QAAA2B,gBAAA;MACA,IAAA3B,aAAA,IAAAA,aAAA,CAAAW,YAAA;QACAX,aAAA,CAAAW,YAAA,GAAAX,aAAA,CAAAW,YAAA,CAAAiC,MAAA,WAAAP,IAAA;UAAA,OAAAA,IAAA,KAAAJ,KAAA;QAAA;MACA;IACA;EACA;AACA;AAAAe,OAAA,CAAAhE,OAAA,GAAAiE,SAAA"}]}