{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1754876882544}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge21hcEdldHRlcnMsIG1hcFN0YXRlfSBmcm9tICJ2dWV4IjsNCmltcG9ydCBMb2dvIGZyb20gIi4vTG9nbyI7DQppbXBvcnQgU2lkZWJhckl0ZW0gZnJvbSAiLi9TaWRlYmFySXRlbSI7DQppbXBvcnQgdmFyaWFibGVzIGZyb20gIkAvYXNzZXRzL3N0eWxlcy92YXJpYWJsZXMuc2NzcyI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgY29tcG9uZW50czoge1NpZGViYXJJdGVtLCBMb2dvfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICAuLi5tYXBTdGF0ZShbInNldHRpbmdzIl0pLA0KICAgIC4uLm1hcEdldHRlcnMoWyJzaWRlYmFyUm91dGVycyIsICJzaWRlYmFyIl0pLA0KICAgIGFjdGl2ZU1lbnUoKSB7DQogICAgICBjb25zdCByb3V0ZSA9IHRoaXMuJHJvdXRlOw0KICAgICAgY29uc3Qge21ldGEsIHBhdGh9ID0gcm91dGU7DQogICAgICAvLyBpZiBzZXQgcGF0aCwgdGhlIHNpZGViYXIgd2lsbCBoaWdobGlnaHQgdGhlIHBhdGggeW91IHNldA0KICAgICAgaWYgKG1ldGEuYWN0aXZlTWVudSkgew0KICAgICAgICByZXR1cm4gbWV0YS5hY3RpdmVNZW51Ow0KICAgICAgfQ0KICAgICAgcmV0dXJuIHBhdGg7DQogICAgfSwNCiAgICBzaG93TG9nbygpIHsNCiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS5zZXR0aW5ncy5zaWRlYmFyTG9nbzsNCiAgICB9LA0KICAgIHZhcmlhYmxlcygpIHsNCiAgICAgIHJldHVybiB2YXJpYWJsZXM7DQogICAgfSwNCiAgICBpc0NvbGxhcHNlKCkgew0KICAgICAgcmV0dXJuICF0aGlzLnNpZGViYXIub3BlbmVkOw0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Sidebar", "sourcesContent": ["<template>\r\n  <div :class=\"{'has-logo':showLogo}\"\r\n       :style=\"{ backgroundColor: settings.sideTheme == 'theme-dark' ? variables.menuBackground : variables.menuLightBackground }\">\r\n    <logo v-if=\"showLogo\" :collapse=\"isCollapse\"/>\r\n    <el-scrollbar :class=\"settings.sideTheme\" wrap-class=\"scrollbar-wrapper\">\r\n      <el-menu\r\n        :active-text-color=\"settings.theme\"\r\n        :background-color=\"settings.sideTheme == 'theme-dark' ? variables.menuBackground : variables.menuLightBackground\"\r\n        :collapse=\"isCollapse\"\r\n        :collapse-transition=\"false\"\r\n        :default-active=\"activeMenu\"\r\n        :text-color=\"settings.sideTheme == 'theme-dark' ? variables.menuColor : variables.menuLightColor\"\r\n        mode=\"vertical\"\r\n        unique-opened\r\n      >\r\n        <sidebar-item\r\n          v-for=\"(route, index) in sidebarRouters\"\r\n          :key=\"route.path  + index\"\r\n          :base-path=\"route.path\"\r\n          :item=\"route\"\r\n        />\r\n      </el-menu>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {mapGetters, mapState} from \"vuex\";\r\nimport Logo from \"./Logo\";\r\nimport SidebarItem from \"./SidebarItem\";\r\nimport variables from \"@/assets/styles/variables.scss\";\r\n\r\nexport default {\r\n  components: {SidebarItem, Logo},\r\n  computed: {\r\n    ...mapState([\"settings\"]),\r\n    ...mapGetters([\"sidebarRouters\", \"sidebar\"]),\r\n    activeMenu() {\r\n      const route = this.$route;\r\n      const {meta, path} = route;\r\n      // if set path, the sidebar will highlight the path you set\r\n      if (meta.activeMenu) {\r\n        return meta.activeMenu;\r\n      }\r\n      return path;\r\n    },\r\n    showLogo() {\r\n      return this.$store.state.settings.sidebarLogo;\r\n    },\r\n    variables() {\r\n      return variables;\r\n    },\r\n    isCollapse() {\r\n      return !this.sidebar.opened;\r\n    }\r\n  }\r\n};\r\n</script>\r\n"]}]}