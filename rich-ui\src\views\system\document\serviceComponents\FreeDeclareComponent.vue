<template>
  <div class="free-declare-component">
    <!--全包报关-->
    <div v-for="(item, index) in freeDeclareList" :key="`free-declare-${index}`" class="free-declare-item">
      <el-row>
        <el-col :span="18">
          <!--标题栏-->
          <div class="service-bar">
            <a
              :class="[
                'service-toggle-icon',
                item.rsServiceInstances.serviceFold ? 'el-icon-arrow-down' : 'el-icon-arrow-right'
              ]"
            />
            <div class="service-title-group">
              <h3 class="service-title" @click="changeServiceFold(item.rsServiceInstances)">
                报关-FreeDeclare
              </h3>
              <el-button
                class="service-action-btn"
                type="text"
                @click="addFreeDeclare"
              >[+]
              </el-button>
              <el-button
                class="service-action-btn"
                type="text"
                @click="deleteRsOpFreeDeclare(item)"
              >[-]
              </el-button>
              <el-button
                class="service-action-btn"
                type="text"
                @click="openChargeSelect(item)"
              >[CN...]
              </el-button>
            </div>
            <!--审核信息-->
            <audit
              v-if="auditInfo"
              :audit="true"
              :basic-info="item.rsServiceInstances"
              :disabled="disabled"
              :payable="getPayable(item.serviceTypeId, item)"
              :rs-charge-list="item.rsChargeList"
              @auditFee="auditCharge(item, $event)"
              @return="item = $event"
            />
            <div class="booking-bill-container">
              <el-popover
                v-for="(billConfig, billIndex) in bookingBillConfig"
                :key="`bill-${billIndex}`"
                placement="top"
                trigger="click"
                width="100"
              >
                <el-button
                  v-for="(template, templateIndex) in billConfig.templateList"
                  :key="`template-${templateIndex}`"
                  @click="getBookingBill(item, template)"
                >
                  {{ template }}
                </el-button>
                <a
                  slot="reference"
                  class="booking-bill-link"
                  target="_blank"
                >
                  [{{ billConfig.file }}]
                </a>
              </el-popover>
            </div>
          </div>
        </el-col>
      </el-row>
      <!--内容区域-->
      <transition name="fade">
        <el-row
          v-if="item.rsServiceInstances.serviceFold"
          :gutter="10"
          class="service-content-area"
        >
          <!--服务信息栏-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="3" class="service-info-col">
              <el-form-item label="询价单号">
                <el-input
                  v-model="item.rsServiceInstances.inquiryNo"
                  placeholder="询价单号"
                  @focus="generateFreight(6, 61, item)"
                />
              </el-form-item>

              <el-form-item v-if="!booking && branchInfo" label="供应商">
                <el-popover
                  :content="getSupplierEmail(item.rsServiceInstances.supplierId)"
                  placement="bottom"
                  trigger="hover"
                  width="200"
                >
                  <el-input
                    slot="reference"
                    :value="item.rsServiceInstances.supplierName"
                    class="disable-form"
                    disabled
                  />
                </el-popover>
              </el-form-item>

              <el-form-item label="合约类型">
                <el-input
                  :value="getAgreementDisplay(item.rsServiceInstances)"
                  class="disable-form"
                  disabled
                  placeholder="合约类型"
                />
              </el-form-item>

              <el-form-item label="业务须知">
                <el-input
                  v-model="item.rsServiceInstances.inquiryNotice"
                  class="disable-form"
                  disabled
                  placeholder="业务须知"
                />
              </el-form-item>
            </el-col>
          </transition>
          <!--分支信息-->
          <transition name="fade">
            <el-col v-if="branchInfo" :span="15">
              <el-col :span="5">
                <el-form-item label="商务单号">
                  <el-row>
                    <el-col :span="20">
                      <!-- 空白区域 -->
                    </el-col>
                    <el-col :span="4">
                      <!-- 空白区域 -->
                    </el-col>
                  </el-row>
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item label="参考号">
                  <el-input
                    :class="isFieldDisabled(item) ? 'disable-form' : ''"
                    :disabled="isFieldDisabled(item)"
                    placeholder="参考号"
                  />
                </el-form-item>
              </el-col>
            </el-col>
          </transition>
          <!--物流进度-->
          <transition name="fade">
            <el-col v-if="logisticsInfo" :span="4">
              <logistics-progress
                :disabled="isFieldDisabled(item)"
                :logistics-progress-data="item.rsOpLogList"
                :open-logistics-progress-list="true"
                :process-type="4"
                :service-type="60"
                @deleteItem="deleteLogItem(item, $event)"
                @return="item.rsOpLogList=$event"
              />
            </el-col>
          </transition>
          <!--费用列表-->
          <el-col v-if="chargeInfo" :span="10.3">
            <!--<charge-list
              :a-t-d="form.podEta"
              :charge-data="item.rsChargeList"
              :company-list="companyList"
              :disabled="disabled || getServiceInstanceDisable(item.rsServiceInstances)"
              :hiddenSupplier="booking"
              :is-receivable="false"
              :open-charge-list="true"
              :pay-detail-r-m-b="item.payableRMB"
              :pay-detail-r-m-b-tax="item.payableRMBTax"
              :pay-detail-u-s-d="item.payableUSD"
              :pay-detail-u-s-d-tax="item.payableUSDTax"
              :pay-detail="getPayable(item.serviceTypeId, item)"
              :service-type-id="item.serviceTypeId"
              @copyFreight="copyFreight($event)"
              @deleteAll="item.rsChargeList=[]"
              @deleteItem="deleteChargeItem(item, $event)"
              @return="calculateCharge(61, $event, item)"
            />-->
            <debit-note-list
              :company-list="companyList"
              :debit-note-list="item.rsDebitNoteList"
              :disabled="false"
              :hidden-supplier="false"
              :is-receivable="0"
              :rct-id="form.rctId"
              @addDebitNote="handleAddDebitNote(item)"
              @deleteItem="item.rsDebitNoteList = item.rsDebitNoteList.filter(v=>v!==$event)"
              @return="calculateCharge(61,$event,item)"
            />
          </el-col>
        </el-row>
      </transition>
    </div>
  </div>
</template>

<script>
import Audit from "@/views/system/document/audit.vue"
import LogisticsProgress from "@/views/system/document/logisticsProgress.vue"
import ChargeList from "@/views/system/document/chargeList.vue"
import DebitNoteList from "@/views/system/document/debitNodeList.vue"

export default {
  name: "FreeDeclareComponent",
  components: {
    DebitNoteList,
    Audit,
    LogisticsProgress,
    ChargeList
  },
  props: {
    // 全包报关数据列表
    freeDeclareList: {
      type: Array,
      default: () => []
    },
    // 表单数据
    form: {
      type: Object,
      default: () => ({})
    },
    // 显示控制
    branchInfo: {
      type: Boolean,
      default: true
    },
    logisticsInfo: {
      type: Boolean,
      default: true
    },
    chargeInfo: {
      type: Boolean,
      default: true
    },
    auditInfo: {
      type: Boolean,
      default: false
    },
    // 状态控制
    disabled: {
      type: Boolean,
      default: false
    },
    booking: {
      type: Boolean,
      default: false
    },
    psaVerify: {
      type: Boolean,
      default: false
    },
    // 数据列表
    supplierList: {
      type: Array,
      default: () => []
    },
    companyList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      bookingBillConfig: [{
        file: "订舱单",
        templateList: ["bookingOrder1"]
      }]
    }
  },
  computed: {
    // 判断是否禁用状态
    isDisabled() {
      return this.disabled || this.psaVerify
    }
  },
  methods: {
    handleAddDebitNote(serviceObject) {
      let row = {}
      row.sqdRctNo = this.form.rctNo
      row.rctId = this.form.rctId
      row.isRecievingOrPaying = 1
      row.rsChargeList = []
      this.$emit('addDebitNote', row, serviceObject)
    },
    // 判断字段是否禁用
    isFieldDisabled(item) {
      return this.psaVerify || this.disabled || this.getServiceInstanceDisable(item.rsServiceInstances)
    },
    // 获取供应商邮箱
    getSupplierEmail(supplierId) {
      const supplier = this.supplierList.find(v => v.companyId === supplierId)
      return supplier ? supplier.staffEmail : ''
    },
    // 获取合约显示文本
    getAgreementDisplay(serviceInstance) {
      return serviceInstance.agreementTypeCode + serviceInstance.agreementNo
    },
    // 处理订舱单生成
    getBookingBill(item, template) {
      this.$emit("getBookingBill", item, template)
    },
    // 删除物流进度
    deleteLogItem(item, logItem) {
      item.rsOpLogList = item.rsOpLogList.filter(log => log !== logItem)
    },
    // 删除费用项
    deleteChargeItem(item, chargeItem) {
      item.rsChargeList = item.rsChargeList.filter(charge => charge !== chargeItem)
    },
    // 事件转发给父组件
    changeServiceFold(serviceInstance) {
      this.$emit("changeServiceFold", serviceInstance)
    },
    addFreeDeclare() {
      this.$emit("addFreeDeclare")
    },
    deleteRsOpFreeDeclare(item) {
      this.$emit("deleteRsOpFreeDeclare", item)
    },
    openChargeSelect(item) {
      this.$emit("openChargeSelect", item)
    },
    auditCharge(item, event) {
      this.$emit("auditCharge", item, event)
    },
    generateFreight(type1, type2, item) {
      this.$emit("generateFreight", type1, type2, item)
    },
    copyFreight(event) {
      this.$emit("copyFreight", event)
    },
    calculateCharge(serviceType, event, item) {
      this.$emit("calculateCharge", serviceType, event, item)
    },
    getPayable(serviceType, item) {
      return this.$parent.getPayable ? this.$parent.getPayable(serviceType, item) : null
    },
    getServiceInstanceDisable(serviceInstance) {
      return this.$parent.getServiceInstanceDisable ? this.$parent.getServiceInstanceDisable(serviceInstance) : false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/op-document';

// FreeDeclare组件特定样式
.free-declare-component {
  width: 100%;

  .free-declare-item {
    margin-bottom: 10px;
  }

  .service-bar {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 10px;
    width: 100%;

    .service-toggle-icon {
      cursor: pointer;
      margin-right: 5px;
    }

    .service-title-group {
      display: flex;
      align-items: center;
      width: 250px;

      .service-title {
        margin: 0;
        cursor: pointer;
      }

      .service-action-btn {
        margin-left: 10px;
      }
    }

    .booking-bill-container {
      margin-left: auto;

      .booking-bill-link {
        color: blue;
        padding: 0;
        text-decoration: none;
        cursor: pointer;
      }
    }
  }

  .service-content-area {
    margin-bottom: 15px;
    display: -webkit-box;

    .service-info-col {
      .el-form-item {
        margin-bottom: 10px;
      }
    }
  }

  // 优化表单输入框样式
  .el-input {
    width: 100%;
  }

  // 优化日期选择器样式
  .el-date-picker {
    width: 100%;
  }
}
</style>
