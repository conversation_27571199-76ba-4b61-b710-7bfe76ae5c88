{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue?vue&type=style&index=0&id=38b539a9&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outbound\\index.vue", "mtime": 1754881964240}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCjo6di1kZWVwIC5lZGl0IC5udW1iZXIgLmVsLWlucHV0X19pbm5lciB7DQogIHRleHQtYWxpZ246IHJpZ2h0Ow0KfQ0KDQovLyDmt7vliqDpq5jkuq7moLflvI8NCjo6di1kZWVwIC5oaWdobGlnaHQtcm93IHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI2ZkZjVlNiAhaW1wb3J0YW50Ow0KICB0cmFuc2l0aW9uOiBiYWNrZ3JvdW5kLWNvbG9yIDAuNXM7DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAk7CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/outbound", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"单号\" prop=\"outboundNo\">\r\n            <el-input\r\n              v-model=\"queryParams.outboundNo\"\r\n              clearable\r\n              placeholder=\"出仓单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clientCode\"\r\n              clearable\r\n              placeholder=\"客户代码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"名称\" prop=\"clientName\">\r\n            <el-input\r\n              v-model=\"queryParams.clientName\"\r\n              clearable\r\n              placeholder=\"客户名称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n            <el-input\r\n              v-model=\"queryParams.containerNo\"\r\n              clearable\r\n              placeholder=\"柜号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"封号\" prop=\"sealNo\">\r\n            <el-input\r\n              v-model=\"queryParams.sealNo\"\r\n              clearable\r\n              placeholder=\"封号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"日期\" prop=\"outboundDate\">\r\n            <el-date-picker v-model=\"queryParams.outboundDate\"\r\n                            clearable style=\"width: 100%\"\r\n                            placeholder=\"出仓日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handlePreOutbound()\"\r\n            >操作预出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleDirectOutbound()\"\r\n            >直接出仓\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:edit']\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleRentSettlement()\"\r\n            >结算仓租\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--出仓记录列表(预出仓记录列表)-->\r\n        <el-table v-loading=\"loading\" :data=\"outboundrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"(selectedRows) =>handleOutbound(selectedRows)\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"出仓单号\" prop=\"outboundNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n          <el-table-column align=\"center\" label=\"客户名称\" prop=\"clientName\"/>\r\n          <el-table-column align=\"center\" label=\"操作员\" prop=\"operator\"/>\r\n          <el-table-column align=\"center\" label=\"柜型\" prop=\"containerType\"/>\r\n          <el-table-column align=\"center\" label=\"柜号\" prop=\"containerNo\"/>\r\n          <el-table-column align=\"center\" label=\"封号\" prop=\"sealNo\"/>\r\n          <el-table-column align=\"center\" label=\"出仓日期\" prop=\"outboundDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.outboundDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"仓库报价\" prop=\"warehouseQuote\"/>\r\n          <el-table-column align=\"center\" label=\"工人装柜费\" prop=\"workerLoadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"仓管代收\" prop=\"warehouseCollection\"/>\r\n          <el-table-column align=\"center\" label=\"代收备注\" prop=\"collectionNotes\"/>\r\n          <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\r\n          <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\r\n          <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\r\n          <el-table-column align=\"center\" label=\"总行数\" prop=\"totalRows\"/>\r\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackagingFee\"/>\r\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"租金平衡费\" prop=\"rentalBalanceFee\"/>\r\n          <el-table-column align=\"center\" label=\"超期仓租\" prop=\"overdueRent\"/>\r\n          <el-table-column align=\"center\" label=\"免堆天数\" prop=\"freeStackDays\"/>\r\n          <el-table-column align=\"center\" label=\"超期单价\" prop=\"overdueUnitPrice\"/>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible.sync=\"openOutbound\"\r\n      append-to-body\r\n      width=\"70%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓日期\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.outboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"下单日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\"\r\n                    >\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table v-loading=\"preOutboundInventoryListLoading\" :data=\"preOutboundInventoryList\"\r\n                        ref=\"table\" :summary-method=\"getSummaries\" max-height=\"300\"\r\n                        show-summary @selection-change=\"handleOutboundSelectionChange\"\r\n                        :load=\"loadChildInventory\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        element-loading-text=\"加载中...\" row-key=\"inventoryId\"\r\n                        style=\"width: 100%;\"\r\n              >\r\n                <el-table-column align=\"center\" fixed type=\"selection\" width=\"28\"/>\r\n                <el-table-column align=\"center\" fixed label=\"序号\" type=\"index\" width=\"28\">\r\n                  <template slot-scope=\"scope\">\r\n                    {{ scope.$index + 1 }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\">\r\n                  <template slot=\"header\" slot-scope=\"scope\">\r\n                    <el-input\r\n                      v-model=\"search\"\r\n                      clearable\r\n                      placeholder=\"输入流水号搜索\"\r\n                      size=\"mini\"\r\n                      @keyup.enter.native=\"handleSearchEnter\"\r\n                    />\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"部分出库\" prop=\"inboundDate\" width=\"50\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-switch v-model=\"scope.row.partialOutboundFlag\"/>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货物明细\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      :disabled=\"scope.row.partialOutboundFlag==0\"\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                          <template slot-scope=\"scope\">\r\n                            <el-input v-model=\"scope.row.boxCount\" :disabled=\"!isRowSelected(scope.row)\"/>\r\n                          </template>\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"体积小计\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"毛重小计\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"最新计租日\" prop=\"inboundDate\" width=\"80\">\r\n                  <template slot-scope=\"scope\">\r\n                    <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收卸货费\" prop=\"unpaidUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付卸货费\" prop=\"receivedUnloadingFee\"/>\r\n                <el-table-column align=\"center\" label=\"补收打包费\" prop=\"unpaidPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"应付打包费\" prop=\"receivedPackingFee\"/>\r\n                <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\r\n                <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n                <el-table-column align=\"center\" label=\"超租天数\" prop=\"rentalDays\"/>\r\n                <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"printOutboundPlant\">打印出仓计划</el-button>\r\n        <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n        <el-button v-else type=\"primary\" @click=\"outboundConfirm(outboundType)\">{{\r\n            outboundType === 3 ? \"结 算\" : \"出 仓\"\r\n          }}</el-button>\r\n        <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n        <el-button @click=\"openOutbound = false\">关 闭</el-button>\r\n  </span>\r\n    </el-dialog>\r\n\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord,\r\n  getOutboundrecord,\r\n  listOutboundrecord,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport {\r\n  listInventory,\r\n  listInventorys,\r\n  outboundInventory,\r\n  preOutboundInventory,\r\n  settlement\r\n} from \"@/api/system/inventory\"\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport warehouseReceipt from \"@/print-template/warehouseReceipt\"\r\nimport {defaultElementTypeProvider, hiprint} from \"@\"\r\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\r\nimport outboundPlant from \"@/print-template/outboundPlant\"\r\n\r\nlet hiprintTemplate\r\nexport default {\r\n  name: \"Outboundrecord\",\r\n  components: {printPreview},\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: null,\r\n      preOutboundInventoryListLoading: false,\r\n      search: null,\r\n      // 表单校验\r\n      rules: {\r\n        clientCode: [\r\n          {required: true, message: \"客户代码不能为空\", trigger: \"blur\"}\r\n        ]\r\n      },\r\n      outboundForm: {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      },\r\n      clientRow: {},\r\n      openOutbound: false,\r\n      preOutboundInventoryList: [],\r\n      selectedCargoDetail: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  mounted() {\r\n    this.initPrint()\r\n  },\r\n  methods: {\r\n    initPrint() {\r\n      hiprint.init({\r\n        providers: [new defaultElementTypeProvider()]\r\n      })\r\n    },\r\n    printOutboundPlant() {\r\n      // 准备打印数据\r\n      const printData = {\r\n        title: \"瑞旗仓库出仓计划\",\r\n        // 表单数据\r\n        outboundNo: this.outboundForm.outboundNo || \"\",\r\n        customerOrderNo: this.outboundForm.customerOrderNo || \"\",\r\n        clientCode: this.outboundForm.clientCode || \"\",\r\n        clientName: this.outboundForm.clientName || \"\",\r\n        plannedOutboundDate: moment(this.outboundForm.plannedOutboundDate).format(\"yyyy-MM-DD HH:mm\") || \"\",\r\n        outboundType: this.outboundForm.outboundType || \"\",\r\n        containerType: this.outboundForm.containerType || \"\",\r\n        cargoType: this.form.cargoType || \"\",\r\n        containerNo: this.outboundForm.containerNo || \"\",\r\n        sealNo: this.outboundForm.sealNo || \"\",\r\n        plateNumber: this.outboundForm.plateNumber || \"\",\r\n        driverPhone: this.outboundForm.driverPhone || \"\",\r\n        warehouseQuote: this.outboundForm.warehouseQuote || \"\",\r\n        warehouseCollection: this.outboundForm.warehouseCollection || \"\",\r\n        workerLoadingFee: this.outboundForm.workerLoadingFee || \"\",\r\n        warehousePay: this.outboundForm.warehousePay || \"\",\r\n        operationRequirement: this.outboundForm.operationRequirement || \"\",\r\n        outboundNote: this.outboundForm.outboundNote || \"\",\r\n        operator: this.outboundForm.operator || \"\",\r\n        orderDate: this.outboundForm.orderDate || \"\",\r\n        outboundHandler: this.outboundForm.outboundHandler || \"\",\r\n        warehouseConfirm: \"√ 已确认 \" + this.parseTime(new Date(), \"{y}-{m}-{d}\"),\r\n\r\n        // 汇总数据\r\n        totalBoxes: this.outboundForm.totalBoxes || 0,\r\n        totalGrossWeight: this.outboundForm.totalGrossWeight || 0,\r\n        totalVolume: this.outboundForm.totalVolume || 0,\r\n        totalSummary: `件数: ${this.outboundForm.totalBoxes || 0} / 毛重: ${this.outboundForm.totalGrossWeight || 0} / 体积: ${this.outboundForm.totalVolume || 0}`,\r\n        totalQuantity: this.outboundForm.totalBoxes || 0,\r\n\r\n        // 勾选的库存列表\r\n        inventoryList: this.selectOutboundList.map(item => {\r\n          return {\r\n            inboundSerialNo: item.inboundSerialNo || \"\",\r\n            clientCode: `${item.subOrderNo || \"\"} ${item.consigneeName || \"\"}`,\r\n            totalBoxes: (this.outboundForm.sqdShippingMark || \"\") + \" / \" + (item.itemName || \"\") + \" / \" + (item.totalBoxes || 0) + \" / \" + (item.totalGrossWeight || 0) + \"KGS / \" + (item.totalVolume || 0) + \"CBM\",\r\n            totalGrossWeight: item.totalGrossWeight || 0,\r\n            totalVolume: item.totalVolume || 0,\r\n            driverInfo: item.driverInfo || \"\",\r\n            outboundQuantity: item.totalBoxes || 0\r\n          }\r\n        })\r\n      }\r\n\r\n      // 创建打印模板并预览打印\r\n      hiprintTemplate = new hiprint.PrintTemplate({template: outboundPlant})\r\n      this.$refs.preView.print(hiprintTemplate, printData)\r\n    },\r\n    warehouseConfirm() {\r\n      // 检查客户代码是否已选择\r\n      if (!this.outboundForm.clientCode) {\r\n        this.$message.warning(\"请先选择客户\")\r\n        return\r\n      }\r\n\r\n      // 设置操作员为当前用户\r\n      this.outboundForm.operator = this.$store.state.user.name.split(\" \")[1]\r\n\r\n      // 设置下单日期为当前日期\r\n      this.outboundForm.orderDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n      // 提示确认成功\r\n      this.$message.success(\"仓管确认成功\")\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 设置当前行的加载状态\r\n      this.$set(tree, 'loading', true)\r\n\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      }).finally(() => {\r\n        // 无论请求成功还是失败，都需要关闭加载状态\r\n        this.$set(tree, 'loading', false)\r\n      })\r\n    },\r\n    warehouseRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 4\r\n      this.openOutbound = true\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    currency,\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓/2:直接出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      // 执行前再次提醒\r\n      this.$confirm(\"确定要\" + (type === 0 ? \"预出仓\" : type === 1 ? \"出仓\" : \"直接出仓\") + \"吗？\", \"提示\", {\r\n        confirmButtonText: \"确定\",\r\n        cancelButtonText: \"取消\",\r\n        type: \"warning\"\r\n      }).then(() => {\r\n        // 扣货不可以出仓\r\n        this.selectOutboundList.map(item => {\r\n          if (item.cargoDeduction == 1) {\r\n            this.$message.error(\"有扣货库存请重新勾选，流水号：\" + item.inboundSerialNoSub)\r\n            return\r\n          }\r\n        })\r\n\r\n        this.selectOutboundList.map(item => {\r\n          item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n        })\r\n\r\n        // 更新箱数、毛重、体积\r\n        this.outboundForm.totalBoxes = 0\r\n        this.outboundForm.totalGrossWeight = 0\r\n        this.outboundForm.totalVolume = 0\r\n        this.selectOutboundList.map(item => {\r\n          item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n            this.outboundForm.totalBoxes = currency(item.boxCount).add(this.outboundForm.totalBoxes).value\r\n            this.outboundForm.totalGrossWeight = currency(item.unitGrossWeight).add(this.outboundForm.totalGrossWeight).value\r\n            this.outboundForm.totalVolume = currency(item.unitVolume).add(this.outboundForm.totalVolume).value\r\n            return item\r\n          }) : null\r\n          return item\r\n        })\r\n        if (type === 0) {\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上预出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.preOutboundFlag = \"1\"\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.preOutboundFlag = \"1\"\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            preOutboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"预出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 1) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"1\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 2) {\r\n          // 直接出仓\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        } else if (type === 3) {\r\n          // 结算仓租\r\n          this.outboundForm.isRentSettlement = 1 // 仓租结算记录\r\n          addOutboundrecord(this.outboundForm).then(response => {\r\n            const outboundRecordId = response.data\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rentalSettlementDate = this.outboundForm.outboundDate\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            settlement(data).then(response => {\r\n              this.$message.success(\"结算成功\")\r\n              this.loadPreOutboundInventoryList()\r\n            })\r\n          })\r\n        } else {\r\n          const outboundRecordId = this.outboundForm.outboundRecordId\r\n          this.outboundForm.preOutboundFlag = \"0\"\r\n          this.outboundForm.outboundDate = moment().format(\"yyyy-MM-DD\")\r\n          updateOutboundrecord(this.outboundForm).then(response => {\r\n            // 列表克隆一份,打上出仓标志\r\n            let data = this.selectOutboundList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n                item.outboundRecordId = outboundRecordId\r\n                return item\r\n              }) : null\r\n              return item\r\n            })\r\n\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          })\r\n        }\r\n      })\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.preOutboundInventoryListLoading = true\r\n      this.loading = true\r\n\r\n      // 构建查询参数\r\n      const queryParams = {\r\n        sqdPlannedOutboundDate: this.outboundForm.plannedOutboundDate,\r\n        clientCode: this.outboundForm.clientCode,\r\n        inventoryStatus: \"0\"\r\n      }\r\n\r\n      // 根据出库类型添加预出库标志\r\n      if (this.queryParams.preOutboundFlag) {\r\n        queryParams.preOutboundFlag = this.queryParams.preOutboundFlag\r\n      }\r\n\r\n      if (this.queryParams.preOutboundRecordId) {\r\n        queryParams.preOutboundRecordId = this.queryParams.preOutboundRecordId\r\n      }\r\n\r\n      // 发起请求\r\n      listInventorys(queryParams)\r\n        .then(response => {\r\n          // 处理响应数据\r\n          this.preOutboundInventoryList = response.rows.filter(item => !item.packageTo)\r\n          this.preOutboundInventoryList ? response.rows.map(item => {\r\n            // 计算补收入仓费\r\n            if (item.includesInboundFee === 0) {\r\n              const receivedFee = Number(item.receivedStorageFee || 0)\r\n              const inboundFee = Number(item.inboundFee || 0)\r\n              const difference = currency(inboundFee).subtract(receivedFee).value\r\n\r\n              // 只有当差值大于0时才设置补收费用\r\n              item.additionalStorageFee = difference > 0 ? difference : 0\r\n            } else {\r\n              item.additionalStorageFee = 0\r\n            }\r\n\r\n            // 如果是打包箱，标记为有子节点\r\n            if (item.packageRecord === \"1\") {\r\n              item.hasChildren = true\r\n            }\r\n\r\n            if (this.outboundForm.outboundRecordId === item.preOutboundRecordId) {\r\n              this.selectOutboundList.push(item)\r\n              this.$nextTick(() => {\r\n                this.$refs.table.toggleRowSelection(item, true)\r\n              })\r\n            }\r\n\r\n            return item\r\n          }) : []\r\n\r\n          // 更新总数\r\n          this.total = response.total || 0\r\n\r\n          // 如果是普通出库类型，自动选中预出库标记的行\r\n          if (this.outboundType === 0 && this.$refs.table) {\r\n            this.$nextTick(() => {\r\n              this.preOutboundInventoryList.forEach(item => {\r\n                if (item.preOutboundFlag === 1) {\r\n                  this.$refs.table.toggleRowSelection(item, true)\r\n                }\r\n              })\r\n            })\r\n          }\r\n        })\r\n        .catch(error => {\r\n          console.error(\"加载预出库库存列表失败:\", error)\r\n          this.$message.error(\"加载预出库库存列表失败\")\r\n        })\r\n        .finally(() => {\r\n          this.queryParams.preOutboundRecordId = null\r\n          this.queryParams.preOutboundFlag = null\r\n          this.loading = false\r\n          this.preOutboundInventoryListLoading = false\r\n        })\r\n    },\r\n    // 选择预出仓记录\r\n    handleOutbound(selectedRows) {\r\n      this.outboundReset()\r\n      this.outboundForm = selectedRows\r\n      this.outboundType = 1\r\n      this.queryParams.preOutboundRecordId = this.outboundForm.outboundRecordId\r\n      this.queryParams.preOutboundFlag = \"1\"\r\n      this.loadPreOutboundInventoryList()\r\n      this.openOutbound = true\r\n    },\r\n    // 添加预出仓记录\r\n    handlePreOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 0\r\n      this.openOutbound = true\r\n    },\r\n    // 直接出仓\r\n    handleDirectOutbound() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 2\r\n      this.openOutbound = true\r\n    },\r\n    // 结算仓租\r\n    handleRentSettlement() {\r\n      this.outboundReset()\r\n      this.outboundForm.outboundHandler = this.$store.state.user.name.split(\" \")[1]\r\n      this.outboundType = 3\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.outboundCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\"\r\n      ]\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\" // 第一列显示文本\r\n        } else {\r\n          const prop = column.property\r\n          let total = 0; // 在条件块之前定义total变量\r\n\r\n          if (prop === \"totalBoxes\" || prop === \"totalVolume\" || prop === \"totalGrossWeight\") {\r\n            total = this.selectOutboundList.reduce((sum, row) => {\r\n              if (row.packageTo) {\r\n                return currency(sum).add(Number(row[prop]) || 0).value\r\n              }\r\n              return sum\r\n            }, 0)\r\n          } else {\r\n            total = this.selectOutboundList.reduce((sum, row) =>\r\n              currency(sum).add(Number(row[prop]) || 0).value, 0)\r\n          }\r\n\r\n          sums[index] = total\r\n          // 现在可以安全地使用total\r\n          summaryResults[column.property] = total\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n\r\n      return sums\r\n    },\r\n    handleOutboundSelectionChange(selection) {\r\n      // 正确获取表格数据 - 通过data属性\r\n      const treeData = this.$refs.table.store.states.data\r\n      // 获取之前的选择状态，用于比较变化\r\n      const previousIds = [...this.ids]\r\n\r\n      // 清空当前选择\r\n      this.ids = []\r\n      this.ids = selection.map(item => item.inventoryId)\r\n      // 找出新选中和取消选中的项\r\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id))\r\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id))\r\n\r\n      this.selectOutboundList = selection\r\n      this.$refs.table.doLayout() // 刷新表格布局\r\n\r\n      // 根据仓租结算至（rental_settlement_date），计算该条库存的租金\r\n      // （ 出库当天-仓租结算至-免租期 ） * 租金单价\r\n      selection.map(item => {\r\n        const date1 = moment(this.outboundForm.outboundDate)\r\n        const date2 = moment(item.rentalSettlementDate)\r\n        item.rentalDays = date1.diff(date2, \"days\") + 1 // 差距的天数\r\n        let volumn = item.totalVolume\r\n\r\n        if (!Number.isNaN(item.rentalDays) && item.rentalDays > 0) {\r\n          // 出仓方式不是整柜没有免租天数\r\n          if (this.outboundForm.outboundType !== \"整柜\") {\r\n            item.overdueRentalFee = currency(item.rentalDays).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          } else {\r\n            let days = currency(item.rentalDays).subtract(item.freeStackPeriod).value\r\n            days = days > 0 ? days : 0\r\n            item.rentalDays = days\r\n            item.overdueRentalFee = currency(days).multiply(item.overdueRentalUnitPrice).multiply(volumn).value\r\n          }\r\n        }\r\n\r\n        // 处理新选中的打包箱：自动选中其子项\r\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\r\n          // 如果是新选中的打包箱节点\r\n\r\n          // 在树形表格数据中找到对应的节点\r\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId)\r\n\r\n          // 检查节点是否已展开(已有children属性且有内容)\r\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n            // 如果节点已展开，直接选中其所有子项\r\n            setTimeout(() => {\r\n              parentNode.children.forEach(child => {\r\n                if (!this.ids.includes(child.inventoryId)) {\r\n                  this.ids.push(child.inventoryId)\r\n                  this.selectOutboundList.push(child)\r\n                  this.$refs.table.toggleRowSelection(child, true)\r\n                }\r\n              })\r\n            }, 50) // 给一点时间让UI更新\r\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\r\n            // 如果节点未展开且未加载过但有子节点标记\r\n            parentNode.childrenLoaded = true\r\n\r\n            // 手动展开行，触发懒加载\r\n            this.$refs.table.toggleRowExpansion(parentNode, true)\r\n\r\n            // 监听子节点加载完成后再选中它们\r\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\r\n          }\r\n        }\r\n      })\r\n\r\n      // 处理取消选中的打包箱：取消选中其子项\r\n      newlyDeselected.forEach(parentId => {\r\n        // 找出对应的父节点\r\n        const parentNode = treeData.find(node =>\r\n          node.inventoryId === parentId && node.packageRecord === \"1\"\r\n        )\r\n\r\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\r\n          // 取消选中所有子项\r\n          parentNode.children.forEach(child => {\r\n            const childIndex = this.ids.indexOf(child.inventoryId)\r\n            if (childIndex > -1) {\r\n              // 从选中列表中移除\r\n              this.ids.splice(childIndex, 1)\r\n              const itemIndex = this.selectOutboundList.findIndex(\r\n                item => item.inventoryId === child.inventoryId\r\n              )\r\n              if (itemIndex > -1) {\r\n                this.selectOutboundList.splice(itemIndex, 1)\r\n              }\r\n              // 在UI上取消选中\r\n              this.$refs.table.toggleRowSelection(child, false)\r\n            }\r\n          })\r\n        }\r\n      })\r\n\r\n      this.countSummary()\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.clientRow = row\r\n      this.outboundForm.overdueRentalUnitPrice = row.overdueRent\r\n      this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecord(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n      }).finally(() => {\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        receivedSupplier: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        operationRequirement: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null,\r\n        receivedFromSupplier: null,\r\n        unreceivedFromCustomer: null,\r\n        receivedFromCustomer: null,\r\n        customerReceivableBalance: null,\r\n        payableToWorker: null,\r\n        promissoryNoteSales: null,\r\n        promissoryNoteCost: null,\r\n        promissoryNoteGrossProfit: null,\r\n        outboundDate: moment().format(\"yyyy-MM-DD\")\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundDate: moment().format(\"yyyy-MM-DD\"),\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operator: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackagingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"outboundForm\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.outboundForm.outboundRecordId != null) {\r\n            updateOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || \"\")\r\n          const searchValue = String(this.search)\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue)\r\n        }\r\n      )\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector(\".el-table__body-wrapper\")\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll(\".el-table__row\")\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx\r\n            }\r\n          })\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex]\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: \"smooth\"\r\n            })\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add(\"highlight-row\")\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove(\"highlight-row\")\r\n            }, 2000)\r\n          }\r\n        })\r\n      } else {\r\n        this.$message.warning(\"未找到匹配的记录\")\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n::v-deep .edit .number .el-input__inner {\r\n  text-align: right;\r\n}\r\n\r\n// 添加高亮样式\r\n::v-deep .highlight-row {\r\n  background-color: #fdf5e6 !important;\r\n  transition: background-color 0.5s;\r\n}\r\n</style>\r\n"]}]}