{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\seafreight\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\seafreight\\index.vue", "mtime": 1754876882597}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQppbXBvcnQgZnJlaWdodCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9mcmVpZ2h0IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAic2VhZnJlaWdodCIsDQogIGNvbXBvbmVudHM6IHtmcmVpZ2h0fSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgdHlwZUlkOiAnMScNCiAgICB9Ow0KICB9LA0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/seafreight", "sourcesContent": ["<template>\r\n  <freight :typeId=\"typeId\"/>\r\n</template>\r\n\r\n<script>\r\nimport freight from \"@/views/system/freight\";\r\n\r\nexport default {\r\n  name: \"seafreight\",\r\n  components: {freight},\r\n  data() {\r\n    return {\r\n      typeId: '1'\r\n    };\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}