{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\menu\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\menu\\index.vue", "mtime": 1737429728560}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_menu", "require", "_vueTreeselect", "_interopRequireDefault", "_IconSelect", "_post", "name", "dicts", "components", "Treeselect", "IconSelect", "data", "showLeft", "showRight", "loading", "showSearch", "menuList", "menuOptions", "postOptions", "title", "open", "isExpandAll", "refreshTable", "queryParams", "menu<PERSON><PERSON>y", "form", "rules", "menuName", "required", "trigger", "orderNum", "path", "watch", "n", "created", "_this", "getList", "listPost", "then", "response", "rows", "methods", "selected", "icon", "_this2", "listMenu", "handleTree", "normalizer", "node", "children", "length", "id", "menuId", "label", "getTreeselect", "_this3", "menu", "push", "cancel", "reset", "parentId", "menuType", "isFrame", "isCache", "visible", "status", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "toggleExpandAll", "_this4", "$nextTick", "handleUpdate", "_this5", "getMenu", "submitForm", "_this6", "$refs", "validate", "valid", "updateMenu", "$modal", "msgSuccess", "addMenu", "handleDelete", "_this7", "$confirm", "customClass", "delMenu", "catch", "batchDeptIds", "val", "distributeRoles", "deptIds", "deptList", "includes", "deptLocalName", "filter", "res", "exports", "default", "_default"], "sources": ["src/views/system/menu/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"menuQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.menuQuery\"\r\n              clearable\r\n              placeholder=\"菜单名称\"\r\n              style=\"width: 100%\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:menu:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              icon=\"el-icon-sort\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"info\"\r\n              @click=\"toggleExpandAll\"\r\n            >展开/折叠\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table\r\n          v-if=\"refreshTable\"\r\n          v-loading=\"loading\"\r\n          :data=\"menuList\"\r\n          :default-expand-all=\"isExpandAll\"\r\n          :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n          row-key=\"menuId\">\r\n          <el-table-column :show-tooltip-when-overflow=\"true\" label=\"菜单名称\" prop=\"menuName\"></el-table-column>\r\n          <el-table-column label=\"权限标识\" prop=\"perms\" width=\"300\"></el-table-column>\r\n          <el-table-column label=\"组件路径\" prop=\"component\" width=\"300\"></el-table-column>\r\n          <el-table-column key=\"orderNum\" align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"48\"></el-table-column>\r\n          <el-table-column align=\"center\" label=\"图标\" prop=\"icon\" width=\"48\">\r\n            <template slot-scope=\"scope\">\r\n              <svg-icon :icon-class=\"scope.row.icon\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:menu:add']\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:menu:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.parentId !=0\"\r\n                v-hasPermi=\"['system:menu:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改菜单对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"680px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"100px\">\r\n        <el-row>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"上级菜单\">\r\n              <treeselect\r\n                v-model=\"form.parentId\"\r\n                :normalizer=\"normalizer\"\r\n                :options=\"menuOptions\"\r\n                :show-count=\"true\"\r\n                placeholder=\"选择上级菜单\"\r\n              />\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"菜单类型\" prop=\"menuType\">\r\n              <el-radio-group v-model=\"form.menuType\">\r\n                <el-radio label=\"M\">目录</el-radio>\r\n                <el-radio label=\"C\">菜单</el-radio>\r\n                <el-radio label=\"F\">按钮</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType != 'F'\" :span=\"24\">\r\n            <el-form-item label=\"菜单图标\" prop=\"icon\">\r\n              <el-popover\r\n                placement=\"bottom-start\"\r\n                trigger=\"click\"\r\n                width=\"460\"\r\n                @show=\"$refs['iconSelect'].reset()\"\r\n              >\r\n                <IconSelect ref=\"iconSelect\" @selected=\"selected\" :active-icon=\"form.icon\"/>\r\n                <el-input slot=\"reference\" v-model=\"form.icon\" placeholder=\"点击选择图标\" readonly>\r\n                  <svg-icon\r\n                    v-if=\"form.icon\"\r\n                    slot=\"prefix\"\r\n                    :icon-class=\"form.icon\"\r\n                    class=\"el-input__icon\"\r\n                    style=\"width: 25px;\"/>\r\n                  <i v-else slot=\"prefix\" class=\"el-icon-search el-input__icon\"/>\r\n                </el-input>\r\n              </el-popover>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"菜单名称\" prop=\"menuName\">\r\n              <el-input v-model=\"form.menuName\" placeholder=\"菜单名称\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number :controls=\"false\" v-model=\"form.orderNum\" :min=\"0\" controls-position=\"right\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType != 'F'\" :span=\"12\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是外链则路由地址需要以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否外链\r\n              </span>\r\n              <el-radio-group v-model=\"form.isFrame\">\r\n                <el-radio label=\"0\">是</el-radio>\r\n                <el-radio label=\"1\">否</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType != 'F'\" :span=\"12\">\r\n            <el-form-item prop=\"path\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由地址\r\n              </span>\r\n              <el-input v-model=\"form.path\" placeholder=\"路由地址\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType == 'C'\" :span=\"12\">\r\n            <el-form-item prop=\"component\">\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"访问的组件路径，如：`system/user/index`，默认在`views`目录下\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                组件路径\r\n              </span>\r\n              <el-input v-model=\"form.component\" placeholder=\"组件路径\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType != 'M'\" :span=\"12\">\r\n            <el-form-item>\r\n              <el-input v-model=\"form.perms\" maxlength=\"100\" placeholder=\"权限标识\"/>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)\"\r\n                            placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                权限字符\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType == 'C'\" :span=\"12\">\r\n            <el-form-item>\r\n              <el-input v-model=\"form.query\" maxlength=\"255\" placeholder=\"路由参数\"/>\r\n              <span slot=\"label\">\r\n                <el-tooltip content='访问路由的默认传递参数，如：`{\"id\": 1, \"name\": \"ry\"}`' placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                路由参数\r\n              </span>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType == 'C'\" :span=\"12\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                是否缓存\r\n              </span>\r\n              <el-radio-group v-model=\"form.isCache\">\r\n                <el-radio label=\"0\">缓存</el-radio>\r\n                <el-radio label=\"1\">不缓存</el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType != 'F'\" :span=\"12\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                显示状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.visible\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_show_hide\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{ dict.label }}\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType != 'F'\" :span=\"12\">\r\n            <el-form-item>\r\n              <span slot=\"label\">\r\n                <el-tooltip content=\"选择停用则路由将不会出现在侧边栏，也不能被访问\" placement=\"top\">\r\n                <i class=\"el-icon-question\"></i>\r\n                </el-tooltip>\r\n                菜单状态\r\n              </span>\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{ dict.label }}\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType == 'C'\" :span=\"12\">\r\n            <el-form-item label=\"部门\">\r\n              <tree-select :pass=\"form.deptList\" :dbn=\"true\" :multiple=\"true\" :placeholder=\"'选择部门'\" :type=\"'dept'\"\r\n                           @return=\"batchDeptIds\" @returnData=\"deptList\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col v-if=\"form.menuType=='C'\" :span=\"12\">\r\n            <el-form-item label=\"职级\">\r\n              <el-select v-model=\"form.positionId\" clearable filterable placeholder=\"选择权限授权的最低职级\"\r\n                         ref=\"post\" style=\"width: 100%;\">\r\n                <el-option\r\n                  v-for=\"item in postOptions\"\r\n                  :key=\"item.positionId\"\r\n                  :disabled=\"item.status == 1\"\r\n                  :label=\"item.positionShortName\"\r\n                  :value=\"item.positionId\">\r\n                  <span>{{item.positionShortName}}</span>\r\n                  <span>{{item.positionLocalName}}</span>\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addMenu, delMenu, getMenu, listMenu, updateMenu} from \"@/api/system/menu\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport IconSelect from \"@/components/IconSelect\";\r\nimport {listPost} from \"@/api/system/post\";\r\n\r\nexport default {\r\n  name: \"Menu\",\r\n  dicts: ['sys_show_hide', 'sys_normal_disable'],\r\n  components: {Treeselect, IconSelect},\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      postOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部折叠\r\n      isExpandAll: false,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        menuQuery: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        menuName: [\r\n          {required: true, trigger: \"blur\"}\r\n        ],\r\n        orderNum: [\r\n          {required: true,  trigger: \"blur\"}\r\n        ],\r\n        path: [\r\n          {required: true,  trigger: \"blur\"}\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n    listPost().then(response => {\r\n      this.postOptions = response.rows\r\n    })\r\n  },\r\n  methods: {\r\n    // 选择图标\r\n    selected(name) {\r\n      this.form.icon = name;\r\n    },\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listMenu(this.queryParams).then(response => {\r\n        this.menuList = this.handleTree(response.data, \"menuId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.menuId,\r\n        label: node.menuName,\r\n        children: node.children\r\n      };\r\n    },\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = [];\r\n        const menu = {menuId: 0, menuName: '主类目', children: []};\r\n        menu.children = this.handleTree(response.data, \"menuId\");\r\n        this.menuOptions.push(menu);\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: null,\r\n        parentId: 0,\r\n        menuName: null,\r\n        icon: null,\r\n        menuType: \"M\",\r\n        orderNum: null,\r\n        isFrame: \"1\",\r\n        isCache: \"0\",\r\n        visible: \"0\",\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      if (row != null && row.menuId != null) {\r\n        this.form.parentId = row.menuId;\r\n      } else {\r\n        this.form.parentId = 0;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加菜单\";\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      this.getTreeselect();\r\n      getMenu(row.menuId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改菜单\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function () {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.menuId != null) {\r\n            updateMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addMenu(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$confirm('是否确认删除名称为\"' + row.menuName + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delMenu(row.menuId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    batchDeptIds(val) {\r\n      this.distributeRoles.deptIds = val\r\n    },\r\n    deptList(val) {\r\n      if (this.form.deptList.includes(val.deptLocalName)) {\r\n        this.form.deptList.push(val.deptLocalName)\r\n      } else {\r\n        this.form.deptList = this.form.deptList.filter(res => {\r\n          return res != val.deptLocalName\r\n        })\r\n      }\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;AA8SA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,cAAA,GAAAC,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAG,WAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,IAAA;EACAC,KAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,WAAA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,SAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;QAAA,EACA;QACAC,QAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;QAAA,EACA;QACAE,IAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EACAG,KAAA;IACAjB,UAAA,WAAAA,WAAAkB,CAAA;MACA,IAAAA,CAAA;QACA,KAAApB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAsB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,IAAAC,cAAA,IAAAC,IAAA,WAAAC,QAAA;MACAJ,KAAA,CAAAjB,WAAA,GAAAqB,QAAA,CAAAC,IAAA;IACA;EACA;EACAC,OAAA;IACA;IACAC,QAAA,WAAAA,SAAApC,IAAA;MACA,KAAAmB,IAAA,CAAAkB,IAAA,GAAArC,IAAA;IACA;IACA,aACA8B,OAAA,WAAAA,QAAA;MAAA,IAAAQ,MAAA;MACA,KAAA9B,OAAA;MACA,IAAA+B,cAAA,OAAAtB,WAAA,EAAAe,IAAA,WAAAC,QAAA;QACAK,MAAA,CAAA5B,QAAA,GAAA4B,MAAA,CAAAE,UAAA,CAAAP,QAAA,CAAA5B,IAAA;QACAiC,MAAA,CAAA9B,OAAA;MACA;IACA;IACA,eACAiC,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAAC,MAAA;QACA,OAAAF,IAAA,CAAAC,QAAA;MACA;MACA;QACAE,EAAA,EAAAH,IAAA,CAAAI,MAAA;QACAC,KAAA,EAAAL,IAAA,CAAArB,QAAA;QACAsB,QAAA,EAAAD,IAAA,CAAAC;MACA;IACA;IACA,gBACAK,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAV,cAAA,IAAAP,IAAA,WAAAC,QAAA;QACAgB,MAAA,CAAAtC,WAAA;QACA,IAAAuC,IAAA;UAAAJ,MAAA;UAAAzB,QAAA;UAAAsB,QAAA;QAAA;QACAO,IAAA,CAAAP,QAAA,GAAAM,MAAA,CAAAT,UAAA,CAAAP,QAAA,CAAA5B,IAAA;QACA4C,MAAA,CAAAtC,WAAA,CAAAwC,IAAA,CAAAD,IAAA;MACA;IACA;IACA;IACAE,MAAA,WAAAA,OAAA;MACA,KAAAtC,IAAA;MACA,KAAAuC,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAlC,IAAA;QACA2B,MAAA;QACAQ,QAAA;QACAjC,QAAA;QACAgB,IAAA;QACAkB,QAAA;QACA/B,QAAA;QACAgC,OAAA;QACAC,OAAA;QACAC,OAAA;QACAC,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA/B,OAAA;IACA;IACA,aACAgC,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAX,KAAA;MACA,KAAAL,aAAA;MACA,IAAAgB,GAAA,YAAAA,GAAA,CAAAlB,MAAA;QACA,KAAA3B,IAAA,CAAAmC,QAAA,GAAAU,GAAA,CAAAlB,MAAA;MACA;QACA,KAAA3B,IAAA,CAAAmC,QAAA;MACA;MACA,KAAAxC,IAAA;MACA,KAAAD,KAAA;IACA;IACA,cACAoD,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAlD,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAAoD,SAAA;QACAD,MAAA,CAAAlD,YAAA;MACA;IACA;IACA,aACAoD,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAAhB,KAAA;MACA,KAAAL,aAAA;MACA,IAAAsB,aAAA,EAAAN,GAAA,CAAAlB,MAAA,EAAAd,IAAA,WAAAC,QAAA;QACAoC,MAAA,CAAAlD,IAAA,GAAAc,QAAA,CAAA5B,IAAA;QACAgE,MAAA,CAAAvD,IAAA;QACAuD,MAAA,CAAAxD,KAAA;MACA;IACA;IACA;IACA0D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAArD,IAAA,CAAA2B,MAAA;YACA,IAAA8B,gBAAA,EAAAJ,MAAA,CAAArD,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAA1C,OAAA;YACA;UACA;YACA,IAAAiD,aAAA,EAAAP,MAAA,CAAArD,IAAA,EAAAa,IAAA,WAAAC,QAAA;cACAuC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA1D,IAAA;cACA0D,MAAA,CAAA1C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAkD,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAC,QAAA,gBAAAlB,GAAA,CAAA3C,QAAA;QAAA8D,WAAA;MAAA,GAAAnD,IAAA;QACA,WAAAoD,aAAA,EAAApB,GAAA,CAAAlB,MAAA;MACA,GAAAd,IAAA;QACAiD,MAAA,CAAAnD,OAAA;QACAmD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA,cACA;IACA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAC,eAAA,CAAAC,OAAA,GAAAF,GAAA;IACA;IACAG,QAAA,WAAAA,SAAAH,GAAA;MACA,SAAApE,IAAA,CAAAuE,QAAA,CAAAC,QAAA,CAAAJ,GAAA,CAAAK,aAAA;QACA,KAAAzE,IAAA,CAAAuE,QAAA,CAAAvC,IAAA,CAAAoC,GAAA,CAAAK,aAAA;MACA;QACA,KAAAzE,IAAA,CAAAuE,QAAA,QAAAvE,IAAA,CAAAuE,QAAA,CAAAG,MAAA,WAAAC,GAAA;UACA,OAAAA,GAAA,IAAAP,GAAA,CAAAK,aAAA;QACA;MACA;IACA;EACA;AACA;AAAAG,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}