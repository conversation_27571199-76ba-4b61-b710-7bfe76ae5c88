{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\Item.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\Item.vue", "mtime": 1742960287368}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "functional", "props", "icon", "type", "String", "default", "title", "isDot", "Boolean", "badge", "Number", "render", "h", "context", "_context$props", "vnodes", "push", "length", "exports", "_default"], "sources": ["src/layout/components/Sidebar/Item.vue"], "sourcesContent": ["<script>\r\nexport default {\r\n  name: 'MenuItem',\r\n  functional: true,\r\n  props: {\r\n    icon: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    title: {\r\n      type: String,\r\n      default: ''\r\n    },\r\n    isDot: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    badge: {\r\n      type: [Number, String],\r\n      default: null\r\n    }\r\n  },\r\n  render(h, context) {\r\n    const {icon, title, badge, isDot} = context.props\r\n    const vnodes = []\r\n\r\n    if (icon) {\r\n      vnodes.push(<svg-icon icon-class={icon}/>)\r\n    }\r\n\r\n    if (title) {\r\n      if (badge) {\r\n        vnodes.push(\r\n          <span class=\"title\">\r\n            <el-badge value={badge} is-dot={isDot} class=\"menu-badge\">\r\n              <span title={title}>{title}</span>\r\n            </el-badge>\r\n          </span>\r\n        )\r\n      } else if (isDot) {\r\n        vnodes.push(\r\n          <span class=\"title\">\r\n            <el-badge is-dot={isDot} class=\"menu-badge\">\r\n              <span title={title}>{title}</span>\r\n            </el-badge>\r\n          </span>\r\n        )\r\n      } else {\r\n        if (title.length > 5) {\r\n          vnodes.push(<span class=\"title\" title={title}>{title}</span>)\r\n        } else {\r\n          vnodes.push(<span class=\"title\">{title}</span>)\r\n        }\r\n      }\r\n    }\r\n    return vnodes\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;eACA;EACAA,IAAA;EACAC,UAAA;EACAC,KAAA;IACAC,IAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAC,MAAA;MACAC,OAAA;IACA;IACAE,KAAA;MACAJ,IAAA,EAAAK,OAAA;MACAH,OAAA;IACA;IACAI,KAAA;MACAN,IAAA,GAAAO,MAAA,EAAAN,MAAA;MACAC,OAAA;IACA;EACA;EACAM,MAAA,WAAAA,OAAAC,CAAA,EAAAC,OAAA;IACA,IAAAC,cAAA,GAAAD,OAAA,CAAAZ,KAAA;MAAAC,IAAA,GAAAY,cAAA,CAAAZ,IAAA;MAAAI,KAAA,GAAAQ,cAAA,CAAAR,KAAA;MAAAG,KAAA,GAAAK,cAAA,CAAAL,KAAA;MAAAF,KAAA,GAAAO,cAAA,CAAAP,KAAA;IACA,IAAAQ,MAAA;IAEA,IAAAb,IAAA;MACAa,MAAA,CAAAC,IAAA,CAAAJ,CAAA;QAAA;UAAA,cAAAV;QAAA;MAAA;IACA;IAEA,IAAAI,KAAA;MACA,IAAAG,KAAA;QACAM,MAAA,CAAAC,IAAA,CAAAJ,CAAA;UAAA,SACA;QAAA,IAAAA,CAAA;UAAA;YAAA,SACAH,KAAA;YAAA,UAAAF;UAAA;UAAA;QAAA,IAAAK,CAAA;UAAA;YAAA,SACAN;UAAA;QAAA,IAAAA,KAAA,MAGA;MACA,WAAAC,KAAA;QACAQ,MAAA,CAAAC,IAAA,CAAAJ,CAAA;UAAA,SACA;QAAA,IAAAA,CAAA;UAAA;YAAA,UACAL;UAAA;UAAA;QAAA,IAAAK,CAAA;UAAA;YAAA,SACAN;UAAA;QAAA,IAAAA,KAAA,MAGA;MACA;QACA,IAAAA,KAAA,CAAAW,MAAA;UACAF,MAAA,CAAAC,IAAA,CAAAJ,CAAA;YAAA;YAAA;cAAA,SAAAN;YAAA;UAAA,IAAAA,KAAA;QACA;UACAS,MAAA,CAAAC,IAAA,CAAAJ,CAAA;YAAA;UAAA,IAAAN,KAAA;QACA;MACA;IACA;IACA,OAAAS,MAAA;EACA;AACA;AAAAG,OAAA,CAAAb,OAAA,GAAAc,QAAA"}]}