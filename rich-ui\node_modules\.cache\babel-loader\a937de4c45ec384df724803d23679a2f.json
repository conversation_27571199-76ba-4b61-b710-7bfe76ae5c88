{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\design\\font-size.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\design\\font-size.js", "mtime": 1737429728483}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["t", "name", "prototype", "css", "e", "length", "style", "fontSize", "createTarget", "list", "fontSizeList", "for<PERSON>ach", "target", "$", "find", "append", "getValue", "val", "parseFloat", "toString", "setValue", "prepend", "destroy", "remove", "exports", "default", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/print/demo/design/font-size.js"], "sourcesContent": ["export default (function () {\r\n  function t() {\r\n    this.name = \"fontSize\"; // 重写的参数 key\r\n  }\r\n\r\n  // 涉及修改元素样式， 添加一个 css 方法\r\n  return t.prototype.css = function (t, e) {\r\n    if (t && t.length) {\r\n      if (e) return t.css(\"font-size\", e + \"pt\"), \"font-size:\" + e + \"pt\";\r\n      t[0].style.fontSize = \"\";\r\n    }\r\n    return null;\r\n  },\r\n    // 创建 DOM\r\n    t.prototype.createTarget = function () {\r\n      let list = [8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 26, 28, 36, 48, 72];\r\n      let fontSizeList = '\\n            <option value=\"\" >默认</option>';\r\n      list.forEach(function (e) {\r\n        fontSizeList += '\\n            <option value=\"' + e + '\">' + e + 'pt</option>';\r\n      })\r\n      this.target = $(' <div class=\"hiprint-option-item\">\\n        <div class=\"hiprint-option-item-label\">\\n        字体大小\\n        </div>\\n        <div class=\"hiprint-option-item-field\">\\n        <select class=\"auto-submit\">        </select>\\n        </div>\\n    </div>');\r\n      this.target.find(\".auto-submit\").append($(fontSizeList));\r\n      return this.target;\r\n    },\r\n    // 获取值\r\n    t.prototype.getValue = function () {\r\n      var t = this.target.find(\"select\").val();\r\n      if (t) return parseFloat(t.toString());\r\n    },\r\n    // 设置值\r\n    t.prototype.setValue = function (t) {\r\n      t && (this.target.find('option[value=\"' + t + '\"]').length || this.target.find(\"select\").prepend('<option value=\"' + t + '\" >' + t + \"</option>\"));\r\n      this.target.find(\"select\").val(t);\r\n    },\r\n    // 销毁 DOM\r\n    t.prototype.destroy = function () {\r\n      this.target.remove();\r\n    }, t;\r\n}())\r\n"], "mappings": ";;;;;;;;;;eAAgB,YAAY;EAC1B,SAASA,CAACA,CAAA,EAAG;IACX,IAAI,CAACC,IAAI,GAAG,UAAU,CAAC,CAAC;EAC1B;;EAEA;EACA,OAAOD,CAAC,CAACE,SAAS,CAACC,GAAG,GAAG,UAAUH,CAAC,EAAEI,CAAC,EAAE;IACvC,IAAIJ,CAAC,IAAIA,CAAC,CAACK,MAAM,EAAE;MACjB,IAAID,CAAC,EAAE,OAAOJ,CAAC,CAACG,GAAG,CAAC,WAAW,EAAEC,CAAC,GAAG,IAAI,CAAC,EAAE,YAAY,GAAGA,CAAC,GAAG,IAAI;MACnEJ,CAAC,CAAC,CAAC,CAAC,CAACM,KAAK,CAACC,QAAQ,GAAG,EAAE;IAC1B;IACA,OAAO,IAAI;EACb,CAAC;EACC;EACAP,CAAC,CAACE,SAAS,CAACM,YAAY,GAAG,YAAY;IACrC,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACzE,IAAIC,YAAY,GAAG,6CAA6C;IAChED,IAAI,CAACE,OAAO,CAAC,UAAUP,CAAC,EAAE;MACxBM,YAAY,IAAI,+BAA+B,GAAGN,CAAC,GAAG,IAAI,GAAGA,CAAC,GAAG,aAAa;IAChF,CAAC,CAAC;IACF,IAAI,CAACQ,MAAM,GAAGC,CAAC,CAAC,uPAAuP,CAAC;IACxQ,IAAI,CAACD,MAAM,CAACE,IAAI,CAAC,cAAc,CAAC,CAACC,MAAM,CAACF,CAAC,CAACH,YAAY,CAAC,CAAC;IACxD,OAAO,IAAI,CAACE,MAAM;EACpB,CAAC;EACD;EACAZ,CAAC,CAACE,SAAS,CAACc,QAAQ,GAAG,YAAY;IACjC,IAAIhB,CAAC,GAAG,IAAI,CAACY,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC,CAACG,GAAG,CAAC,CAAC;IACxC,IAAIjB,CAAC,EAAE,OAAOkB,UAAU,CAAClB,CAAC,CAACmB,QAAQ,CAAC,CAAC,CAAC;EACxC,CAAC;EACD;EACAnB,CAAC,CAACE,SAAS,CAACkB,QAAQ,GAAG,UAAUpB,CAAC,EAAE;IAClCA,CAAC,KAAK,IAAI,CAACY,MAAM,CAACE,IAAI,CAAC,gBAAgB,GAAGd,CAAC,GAAG,IAAI,CAAC,CAACK,MAAM,IAAI,IAAI,CAACO,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC,CAACO,OAAO,CAAC,iBAAiB,GAAGrB,CAAC,GAAG,KAAK,GAAGA,CAAC,GAAG,WAAW,CAAC,CAAC;IAClJ,IAAI,CAACY,MAAM,CAACE,IAAI,CAAC,QAAQ,CAAC,CAACG,GAAG,CAACjB,CAAC,CAAC;EACnC,CAAC;EACD;EACAA,CAAC,CAACE,SAAS,CAACoB,OAAO,GAAG,YAAY;IAChC,IAAI,CAACV,MAAM,CAACW,MAAM,CAAC,CAAC;EACtB,CAAC,EAAEvB,CAAC;AACR,CAAC,CAAC,CAAC;AAAAwB,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}