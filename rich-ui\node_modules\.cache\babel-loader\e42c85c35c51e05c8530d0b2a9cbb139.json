{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\agreementrecord.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\agreementrecord.js", "mtime": 1678688095220}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkQWdyZWVtZW50cmVjb3JkID0gYWRkQWdyZWVtZW50cmVjb3JkOwpleHBvcnRzLmRlbEFncmVlbWVudHJlY29yZCA9IGRlbEFncmVlbWVudHJlY29yZDsKZXhwb3J0cy5nZXRBZ3JlZW1lbnRyZWNvcmQgPSBnZXRBZ3JlZW1lbnRyZWNvcmQ7CmV4cG9ydHMubGlzdEFncmVlbWVudHJlY29yZCA9IGxpc3RBZ3JlZW1lbnRyZWNvcmQ7CmV4cG9ydHMudXBkYXRlQWdyZWVtZW50cmVjb3JkID0gdXBkYXRlQWdyZWVtZW50cmVjb3JkOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i5Y2P6K6u6K6w5b2V5YiX6KGoCmZ1bmN0aW9uIGxpc3RBZ3JlZW1lbnRyZWNvcmQocXVlcnkpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vYWdyZWVtZW50cmVjb3JkL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogcXVlcnkKICB9KTsKfQoKLy8g5p+l6K+i5Y2P6K6u6K6w5b2V6K+m57uGCmZ1bmN0aW9uIGdldEFncmVlbWVudHJlY29yZChhZ3JlZW1lbnRJZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9hZ3JlZW1lbnRyZWNvcmQvJyArIGFncmVlbWVudElkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7ljY/orq7orrDlvZUKZnVuY3Rpb24gYWRkQWdyZWVtZW50cmVjb3JkKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vYWdyZWVtZW50cmVjb3JkJywKICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgZGF0YTogZGF0YQogIH0pOwp9CgovLyDkv67mlLnljY/orq7orrDlvZUKZnVuY3Rpb24gdXBkYXRlQWdyZWVtZW50cmVjb3JkKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vYWdyZWVtZW50cmVjb3JkJywKICAgIG1ldGhvZDogJ3B1dCcsCiAgICBkYXRhOiBkYXRhCiAgfSk7Cn0KCi8vIOWIoOmZpOWNj+iuruiusOW9lQpmdW5jdGlvbiBkZWxBZ3JlZW1lbnRyZWNvcmQoYWdyZWVtZW50SWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vYWdyZWVtZW50cmVjb3JkLycgKyBhZ3JlZW1lbnRJZCwKICAgIG1ldGhvZDogJ2RlbGV0ZScKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listAgreementrecord", "query", "request", "url", "method", "params", "getAgreementrecord", "agreementId", "addAgreementrecord", "data", "updateAgreementrecord", "delAgreementrecord"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/agreementrecord.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询协议记录列表\r\nexport function listAgreementrecord(query) {\r\n  return request({\r\n    url: '/system/agreementrecord/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询协议记录详细\r\nexport function getAgreementrecord(agreementId) {\r\n  return request({\r\n    url: '/system/agreementrecord/' + agreementId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增协议记录\r\nexport function addAgreementrecord(data) {\r\n  return request({\r\n    url: '/system/agreementrecord',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改协议记录\r\nexport function updateAgreementrecord(data) {\r\n  return request({\r\n    url: '/system/agreementrecord',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除协议记录\r\nexport function delAgreementrecord(agreementId) {\r\n  return request({\r\n    url: '/system/agreementrecord/' + agreementId,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACC,WAAW,EAAE;EAC9C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,WAAW;IAC7CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,qBAAqBA,CAACD,IAAI,EAAE;EAC1C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,kBAAkBA,CAACJ,WAAW,EAAE;EAC9C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,WAAW;IAC7CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}