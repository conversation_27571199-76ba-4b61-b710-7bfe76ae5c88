{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\logisticsNoInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\logisticsNoInfo.vue", "mtime": 1754876882583}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "watch", "logisticsNoInfo", "$emit", "openLogisticsNoInfo", "n", "oopen", "data", "open", "form", "methods", "rowIndex", "_ref", "row", "id", "handleUpdate", "handleDelete", "filter", "item", "submitForm", "reset", "push", "soNo", "mblNo", "hblNo", "containersInfo", "shipper", "consignee", "notify<PERSON><PERSON><PERSON>", "polBookingAgent", "podHandleAgent", "shippingMark", "goodsDescription", "blIssueDate", "blIssueLocation", "resetForm", "cancel", "exports", "default", "_default"], "sources": ["src/views/system/document/logisticsNoInfo.vue"], "sourcesContent": ["<template>\r\n  <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n             :visible.sync=\"oopen\" append-to-body width=\"1500px\">\r\n    <div style=\"display: flex\">\r\n      <h2 style=\"font-weight: bold ;margin:10px;\">新增编号信息</h2>\r\n      <div style=\"vertical-align: middle;line-height: 41px\">\r\n        <el-button type=\"primary\" @click=\"open=true\">新增</el-button>\r\n      </div>\r\n    </div>\r\n    <el-table border :data=\"logisticsNoInfo\" :row-class-name=\"rowIndex\">\r\n      <el-table-column header-align=\"center\" label=\"SO号码\" prop=\"soNo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"主提单号\" prop=\"mblNo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"货代单号\" prop=\"hblNo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"柜号信息\" prop=\"containersInfo\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"发货人\" prop=\"shipper\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"收货人\" prop=\"consignee\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"通知人\" prop=\"notifyParty\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"启运港放舱代理\" prop=\"polBookingAgent\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"目的港换单代理\" prop=\"podHandleAgent\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"唛头\" prop=\"shippingMark\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"货描\" prop=\"goodsDescription\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"签单日期\" prop=\"blIssueDate\"></el-table-column>\r\n      <el-table-column header-align=\"center\" label=\"签单地点\" prop=\"blIssueLocation\"></el-table-column>\r\n      <el-table-column header-align=\"center\" align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\"\r\n                       width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            icon=\"el-icon-edit\"\r\n            size=\"mini\"\r\n            style=\"margin-right: -8px\"\r\n            type=\"success\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n          >修改\r\n          </el-button>\r\n          <el-button\r\n            icon=\"el-icon-delete\"\r\n            size=\"mini\"\r\n            style=\"margin-right: -8px\"\r\n            type=\"danger\"\r\n            @click=\"handleDelete(scope.row)\"\r\n          >删除\r\n          </el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-dialog :close-on-click-modal=\"false\" v-dialogDrag v-dialogDragWidth :modal-append-to-body=\"false\"\r\n               :visible.sync=\"open\" append-to-body width=\"500px\" title=\"新增编号信息\">\r\n      <el-form border :data=\"form\" label-width=\"105px\">\r\n        <el-form-item label=\"SO号码\" prop=\"soNo\">\r\n          <el-input v-model=\"form.soNo\" placeholder=\"SO号码\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"主提单号\" prop=\"mblNo\">\r\n          <el-input v-model=\"form.mblNo\" placeholder=\"主提单号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"货代单号\" prop=\"hblNo\">\r\n          <el-input v-model=\"form.hblNo\" placeholder=\"货代单号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"柜号信息\" prop=\"containersInfo\">\r\n          <el-input v-model=\"form.containersInfo\" placeholder=\"柜号信息\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"发货人\" prop=\"shipper\">\r\n          <el-input v-model=\"form.shipper\" placeholder=\"发货人\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"收货人\" prop=\"consignee\">\r\n          <el-input v-model=\"form.consignee\" placeholder=\"收货人\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"通知人\" prop=\"notifyParty\">\r\n          <el-input v-model=\"form.notifyParty\" placeholder=\"通知人\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"启运港放舱代理\" prop=\"polBookingAgent\">\r\n          <el-input v-model=\"form.polBookingAgent\" placeholder=\"启运港放舱代理\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"目的港换单代理\" prop=\"podHandleAgent\">\r\n          <el-input v-model=\"form.podHandleAgent\" placeholder=\"目的港换单代理\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"唛头\" prop=\"shippingMark\">\r\n          <el-input v-model=\"form.shippingMark\" placeholder=\"唛头\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"货描\" prop=\"goodsDescription\">\r\n          <el-input v-model=\"form.goodsDescription\" placeholder=\"货描\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"签单日期\" prop=\"blIssueDate\">\r\n          <el-input v-model=\"form.blIssueDate\" placeholder=\"签单日期\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"签单地点\" prop=\"blIssueLocation\">\r\n          <el-input v-model=\"form.blIssueLocation\" placeholder=\"签单地点\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </el-dialog>\r\n</template>\r\n<script>\r\n\r\nexport default {\r\n  name: 'logisticsNoInfo',\r\n  props: ['openLogisticsNoInfo'],\r\n  watch: {\r\n    logisticsNoInfo() {\r\n      this.$emit('return', this.logisticsNoInfo)\r\n    },\r\n    openLogisticsNoInfo(n) {\r\n      this.oopen = n\r\n    },\r\n    oopen(n) {\r\n      if (n == false) {\r\n        this.$emit('close')\r\n      }\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      open: false,\r\n      oopen: false,\r\n      logisticsNoInfo: [],\r\n      form: {},\r\n    }\r\n  },\r\n  methods: {\r\n    /** 序号 */\r\n    rowIndex({row, rowIndex}) {\r\n      row.id = rowIndex + 1;\r\n    },\r\n    handleUpdate(row) {\r\n      this.form = row\r\n      this.open = true\r\n    },\r\n    handleDelete(row) {\r\n      this.logisticsNoInfo = this.logisticsNoInfo.filter(item => {\r\n        return item.id != row.id\r\n      })\r\n    },\r\n    submitForm() {\r\n      if (this.form.id != null) {\r\n        this.reset()\r\n        this.open = false\r\n      } else {\r\n        this.logisticsNoInfo.push(this.form)\r\n        this.reset()\r\n        this.open = false\r\n      }\r\n    },\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        soNo: null,\r\n        mblNo: null,\r\n        hblNo: null,\r\n        containersInfo: null,\r\n        shipper: null,\r\n        consignee: null,\r\n        notifyParty: null,\r\n        polBookingAgent: null,\r\n        podHandleAgent: null,\r\n        shippingMark: null,\r\n        goodsDescription: null,\r\n        blIssueDate: null,\r\n        blIssueLocation: null,\r\n      }\r\n      this.resetForm(\"form\");\r\n    },\r\n    cancel() {\r\n      this.open = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAiGA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAC,KAAA,gBAAAD,eAAA;IACA;IACAE,mBAAA,WAAAA,oBAAAC,CAAA;MACA,KAAAC,KAAA,GAAAD,CAAA;IACA;IACAC,KAAA,WAAAA,MAAAD,CAAA;MACA,IAAAA,CAAA;QACA,KAAAF,KAAA;MACA;IACA;EACA;EACAI,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA;MACAF,KAAA;MACAJ,eAAA;MACAO,IAAA;IACA;EACA;EACAC,OAAA;IACA,SACAC,QAAA,WAAAA,SAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAF,QAAA,GAAAC,IAAA,CAAAD,QAAA;MACAE,GAAA,CAAAC,EAAA,GAAAH,QAAA;IACA;IACAI,YAAA,WAAAA,aAAAF,GAAA;MACA,KAAAJ,IAAA,GAAAI,GAAA;MACA,KAAAL,IAAA;IACA;IACAQ,YAAA,WAAAA,aAAAH,GAAA;MACA,KAAAX,eAAA,QAAAA,eAAA,CAAAe,MAAA,WAAAC,IAAA;QACA,OAAAA,IAAA,CAAAJ,EAAA,IAAAD,GAAA,CAAAC,EAAA;MACA;IACA;IACAK,UAAA,WAAAA,WAAA;MACA,SAAAV,IAAA,CAAAK,EAAA;QACA,KAAAM,KAAA;QACA,KAAAZ,IAAA;MACA;QACA,KAAAN,eAAA,CAAAmB,IAAA,MAAAZ,IAAA;QACA,KAAAW,KAAA;QACA,KAAAZ,IAAA;MACA;IACA;IACAY,KAAA,WAAAA,MAAA;MACA,KAAAX,IAAA;QACAK,EAAA;QACAQ,IAAA;QACAC,KAAA;QACAC,KAAA;QACAC,cAAA;QACAC,OAAA;QACAC,SAAA;QACAC,WAAA;QACAC,eAAA;QACAC,cAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,eAAA;MACA;MACA,KAAAC,SAAA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA5B,IAAA;IACA;EACA;AACA;AAAA6B,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}