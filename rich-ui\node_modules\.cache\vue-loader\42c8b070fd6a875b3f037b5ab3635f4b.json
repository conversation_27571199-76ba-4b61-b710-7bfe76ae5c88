{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\operationalprocess\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\operationalprocess\\index.vue", "mtime": 1754876882592}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRPcGVyYXRpb25hbHByb2Nlc3MsDQogIGNoYW5nZVN0YXR1cywNCiAgZGVsT3BlcmF0aW9uYWxwcm9jZXNzLA0KICBnZXRPcGVyYXRpb25hbHByb2Nlc3MsDQogIGxpc3RPcGVyYXRpb25hbHByb2Nlc3MsDQogIHVwZGF0ZU9wZXJhdGlvbmFscHJvY2Vzcw0KfSBmcm9tICJAL2FwaS9zeXN0ZW0vb3BlcmF0aW9uYWxwcm9jZXNzIjsNCmltcG9ydCB7bGlzdFByb2Nlc3N9IGZyb20gIkAvYXBpL3N5c3RlbS9wcm9jZXNzIjsNCmltcG9ydCB7bGlzdFByb2Nlc3NzdGF0dXN9IGZyb20gIkAvYXBpL3N5c3RlbS9wcm9jZXNzc3RhdHVzIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiT3BlcmF0aW9uYWxwcm9jZXNzIiwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0xlZnQ6IDMsDQogICAgICBzaG93UmlnaHQ6IDIxLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5oC75p2h5pWwDQogICAgICB0b3RhbDogMCwNCiAgICAgIC8vIOaTjeS9nOi/m+W6puihqOagvOaVsOaNrg0KICAgICAgb3BlcmF0aW9uYWxwcm9jZXNzTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIHBhZ2VOdW06IDEsDQogICAgICAgIHBhZ2VTaXplOiAyMCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHByb2Nlc3NJZDogbnVsbCwNCiAgICAgICAgcHJvY2Vzc1N0YXR1c0lkOiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICBwcm9jZXNzTGlzdDogW10sDQogICAgICBwcm9jZXNzU3RhdHVzTGlzdDogW10sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fQ0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd1NlYXJjaChuKSB7DQogICAgICBpZiAobiA9PT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxDQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAzDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0DQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAwDQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICBsaXN0UHJvY2Vzcyh7cGFnZU51bTogMSwgcGFnZVNpemU6IDIwMH0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy5wcm9jZXNzTGlzdCA9IHJlc3BvbnNlLnJvd3M7DQogICAgfSk7DQogICAgbGlzdFByb2Nlc3NzdGF0dXMoe3BhZ2VOdW06IDEsIHBhZ2VTaXplOiAxMDB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMucHJvY2Vzc1N0YXR1c0xpc3QgPSByZXNwb25zZS5yb3dzOw0KICAgIH0pOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouaTjeS9nOi/m+W6puWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdE9wZXJhdGlvbmFscHJvY2Vzcyh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5vcGVyYXRpb25hbHByb2Nlc3NMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgb3BlcmF0aW9uYWxQcm9jZXNzSWQ6IG51bGwsDQogICAgICAgIG9wZXJhdGlvbk5vOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcHJvY2Vzc0lkOiBudWxsLA0KICAgICAgICBwcm9jZXNzU3RhdHVzSWQ6IG51bGwsDQogICAgICAgIGhhcHBlblRpbWU6IG51bGwsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICBkZWxldGVCeTogbnVsbCwNCiAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgZGVsZXRlU3RhdHVzOiAiMCINCiAgICAgIH07DQogICAgICB0aGlzLnJlc2V0Rm9ybSgiZm9ybSIpOw0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMTsNCiAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIH0sDQogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLw0KICAgIHJlc2V0UXVlcnkoKSB7DQogICAgICB0aGlzLnJlc2V0Rm9ybSgicXVlcnlGb3JtIik7DQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCk7DQogICAgfSwNCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7DQogICAgICBsZXQgdGV4dCA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICLlkK/nlKgiIDogIuWBnOeUqCI7DQogICAgICB0aGlzLiRjb25maXJtKCfnoa7orqTopoEiJyArIHRleHQgKyAn5ZCX77yfJywgJ+aPkOekuicsIHtjdXN0b21DbGFzczogJ21vZGFsLWNvbmZpcm0nfSkudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBjaGFuZ2VTdGF0dXMocm93Lm9wZXJhdGlvbmFsUHJvY2Vzc0lkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7DQogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub3BlcmF0aW9uYWxQcm9jZXNzSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOaTjeS9nOi/m+W6piI7DQogICAgfSwNCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlVXBkYXRlKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgY29uc3Qgb3BlcmF0aW9uYWxQcm9jZXNzSWQgPSByb3cub3BlcmF0aW9uYWxQcm9jZXNzSWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldE9wZXJhdGlvbmFscHJvY2VzcyhvcGVyYXRpb25hbFByb2Nlc3NJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55pON5L2c6L+b5bqmIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUodmFsaWQgPT4gew0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLm9wZXJhdGlvbmFsUHJvY2Vzc0lkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZU9wZXJhdGlvbmFscHJvY2Vzcyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZE9wZXJhdGlvbmFscHJvY2Vzcyh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBvcGVyYXRpb25hbFByb2Nlc3NJZHMgPSByb3cub3BlcmF0aW9uYWxQcm9jZXNzSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmk43kvZzov5vluqbnvJblj7fkuLoiJyArIG9wZXJhdGlvbmFsUHJvY2Vzc0lkcyArICci55qE5pWw5o2u6aG577yfJywgJ+aPkOekuicsIHtjdXN0b21DbGFzczogJ21vZGFsLWNvbmZpcm0nfSkudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBkZWxPcGVyYXRpb25hbHByb2Nlc3Mob3BlcmF0aW9uYWxQcm9jZXNzSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdzeXN0ZW0vb3BlcmF0aW9uYWxwcm9jZXNzL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYG9wZXJhdGlvbmFscHJvY2Vzc18ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9LA0KICAgIGdldFNlcnZpY2VUeXBlSWQodmFsKSB7DQogICAgICB0aGlzLmZvcm0uc2VydmljZVR5cGVJZCA9IHZhbA0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6MA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/operationalprocess", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\"\r\n                 label-width=\"68px\">\r\n          <el-form-item label=\"服务类型\" prop=\"serviceTypeId\">\r\n            <el-input\r\n              v-model=\"queryParams.serviceTypeId\"\r\n              placeholder=\"服务类型\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"服务ID\" prop=\"rctId\">\r\n            <el-input\r\n              v-model=\"queryParams.rctId\"\r\n              placeholder=\"服务ID\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度名称\" prop=\"processId\">\r\n            <el-input\r\n              v-model=\"queryParams.processId\"\r\n              placeholder=\"进度名称\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度状态\" prop=\"processStatusId\">\r\n            <el-input\r\n              v-model=\"queryParams.processStatusId\"\r\n              placeholder=\"进度状态\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:operationalprocess:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-edit\"\r\n              size=\"mini\"\r\n              :disabled=\"single\"\r\n              @click=\"handleUpdate\"\r\n              v-hasPermi=\"['system:operationalprocess:edit']\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:operationalprocess:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:operationalprocess:export']\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"operationalprocessList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"center\" label=\"操作单号\" prop=\"operationNo\" show-tooltip-when-overflow width=\"120\"/>\r\n          <el-table-column label=\"服务类型\" align=\"center\" prop=\"serviceType\" width=\"68\"/>\r\n          <el-table-column label=\"服务\" align=\"center\" prop=\"rctId\" width=\"68\"/>\r\n          <el-table-column label=\"进度名称\" align=\"center\" prop=\"process\" width=\"68\"/>\r\n          <el-table-column label=\"进度状态\" align=\"center\" prop=\"processStatus\" width=\"68\"/>\r\n          <el-table-column label=\"发生时间\" align=\"center\" prop=\"happenTime\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.happenTime, '{y}-{m}-{d}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注\" align=\"center\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"录入人\" prop=\"updateBy\" show-tooltip-when-overflow width=\"78\"/>\r\n          <el-table-column label=\"录入时间\" align=\"center\" prop=\"updateTime\" width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:operationalprocess:edit']\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:operationalprocess:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改操作进度对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" width=\"500px\"\r\n      append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item label=\"操作单号\" prop=\"operationNo\">\r\n          <el-input v-model=\"form.operationNo\" placeholder=\"操作单号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务类型\" prop=\"serviceTypeId\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.serviceTypeId\"\r\n                       :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                       @return=\"getServiceTypeId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务\" prop=\"rctId\">\r\n          <el-input v-model=\"form.rctId\" placeholder=\"服务ID\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"进度名称\" prop=\"processId\">\r\n          <el-select v-model=\"form.processId\" placeholder=\"进度名称\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in processList\"\r\n              :key=\"dict.processId\"\r\n              :label=\"dict.serviceType+'/'+dict.processLocalName\"\r\n              :value=\"dict.processId\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"进度状态\" prop=\"processStatusId\">\r\n          <el-select v-model=\"form.processStatusId\" placeholder=\"进度状态\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in processStatusList\"\r\n              :key=\"dict.processStatusId\"\r\n              :label=\"dict.processStatusLocalName\"\r\n              :value=\"dict.processStatusId\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"发生时间\" prop=\"happenTime\">\r\n          <el-date-picker clearable\r\n                          v-model=\"form.happenTime\"\r\n                          type=\"date\"\r\n                          value-format=\"yyyy-MM-dd\"\r\n                          placeholder=\"发生时间\"\r\n                          style=\"width: 100%\">\r\n          </el-date-picker>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOperationalprocess,\r\n  changeStatus,\r\n  delOperationalprocess,\r\n  getOperationalprocess,\r\n  listOperationalprocess,\r\n  updateOperationalprocess\r\n} from \"@/api/system/operationalprocess\";\r\nimport {listProcess} from \"@/api/system/process\";\r\nimport {listProcessstatus} from \"@/api/system/processstatus\";\r\n\r\nexport default {\r\n  name: \"Operationalprocess\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 操作进度表格数据\r\n      operationalprocessList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        serviceTypeId: null,\r\n        rctId: null,\r\n        processId: null,\r\n        processStatusId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      processList: [],\r\n      processStatusList: [],\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    listProcess({pageNum: 1, pageSize: 200}).then(response => {\r\n      this.processList = response.rows;\r\n    });\r\n    listProcessstatus({pageNum: 1, pageSize: 100}).then(response => {\r\n      this.processStatusList = response.rows;\r\n    });\r\n  },\r\n  methods: {\r\n    /** 查询操作进度列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listOperationalprocess(this.queryParams).then(response => {\r\n        this.operationalprocessList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        operationalProcessId: null,\r\n        operationNo: null,\r\n        serviceTypeId: null,\r\n        rctId: null,\r\n        processId: null,\r\n        processStatusId: null,\r\n        happenTime: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.operationalProcessId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.operationalProcessId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加操作进度\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const operationalProcessId = row.operationalProcessId || this.ids\r\n      getOperationalprocess(operationalProcessId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改操作进度\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.operationalProcessId != null) {\r\n            updateOperationalprocess(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addOperationalprocess(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const operationalProcessIds = row.operationalProcessId || this.ids;\r\n      this.$confirm('是否确认删除操作进度编号为\"' + operationalProcessIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delOperationalprocess(operationalProcessIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/operationalprocess/export', {\r\n        ...this.queryParams\r\n      }, `operationalprocess_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getServiceTypeId(val) {\r\n      this.form.serviceTypeId = val\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}