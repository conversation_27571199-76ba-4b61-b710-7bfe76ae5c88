{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\currency\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\currency\\index.vue", "mtime": 1754876882580}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_currency", "require", "name", "data", "showLeft", "showRight", "locationOptions", "Set", "loading", "ids", "single", "multiple", "showSearch", "total", "currencyList", "title", "open", "queryParams", "pageNum", "pageSize", "currencyQuery", "locationId", "form", "rules", "watch", "n", "created", "getList", "methods", "_this", "listCurrency", "then", "response", "rows", "cancel", "reset", "currencyId", "currencyCode", "currencyLocalName", "exchangeRate", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getCurrency", "submitForm", "_this3", "$refs", "validate", "valid", "updateCurrency", "$modal", "msgSuccess", "addCurrency", "handleDelete", "_this4", "currencyIds", "$confirm", "customClass", "delCur<PERSON>cy", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "queryLocationId", "val", "getLocationId", "handleStatusChange", "_this5", "text", "status", "changeStatus", "exports", "_default"], "sources": ["src/views/system/currency/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"currencyQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.currencyQuery\"\r\n              clearable\r\n              placeholder=\"直接编码，名称\"\r\n              style=\"width: 100%\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"地址\" prop=\"locationId\">\r\n            <location-select :multiple=\"false\" :pass=\"queryParams.locationId\" :load-options=\"locationOptions\"\r\n                             :type=\"'location'\" @return=\"queryLocationId\"/>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:currency:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:currency:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:currency:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"currencyList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"center\" label=\"编码\" prop=\"currencyCode\" width=\"48\"/>\r\n          <el-table-column align=\"center\" label=\"区域\" prop=\"location\" width=\"100\"/>\r\n          <el-table-column align=\"center\" label=\"中文名\" prop=\"currencyLocalName\" width=\"100\"/>\r\n          <el-table-column align=\"center\" label=\"汇率\" prop=\"exchangeRate\" width=\"100\"/>\r\n          <el-table-column align=\"center\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"44\"/>\r\n          <el-table-column key=\"status\" align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:currency:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:currency:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改币种对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item label=\"编码\" prop=\"currencyCode\">\r\n          <el-input v-model=\"form.currencyCode\" placeholder=\"编码，如 RMB ，USD\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"区域\" prop=\"locationId\">\r\n          <location-select :multiple=\"false\" :pass=\"form.locationId\" :load-options=\"locationOptions\"\r\n                           :type=\"'location'\" @return=\"getLocationId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"currencyLocalName\">\r\n          <el-input v-model=\"form.currencyLocalName\" placeholder=\"币种本地语名称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"汇率\" prop=\"exchangeRate\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.exchangeRate\" :min=\"0\"\r\n                           style=\"width: 100%\" :precision=\"2\" :step=\"0.01\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" style=\"width: 100%\" v-model=\"form.orderNum\" placeholder=\"排序\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 5, maxRows: 20}\" maxlength=\"150\" placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addCurrency, changeStatus, delCurrency, getCurrency, listCurrency, updateCurrency} from \"@/api/system/currency\";\r\n\r\nexport default {\r\n  name: \"Currency\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      locationOptions: new Set(),\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 币种表格数据\r\n      currencyList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        currencyQuery: null,\r\n        locationId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询币种列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCurrency(this.queryParams).then(response => {\r\n        this.currencyList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        currencyId: null,\r\n        currencyCode: null,\r\n        locationId: null,\r\n        currencyLocalName: null,\r\n        exchangeRate: null,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: 0\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.currencyId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加币种\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const currencyId = row.currencyId || this.ids\r\n      getCurrency(currencyId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改币种\";\r\n        this.locationOptions = response.locationOptions\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.currencyId != null) {\r\n            updateCurrency(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCurrency(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const currencyIds = row.currencyId || this.ids;\r\n      this.$confirm('是否确认删除币种编号为\"' + currencyIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCurrency(currencyIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/currency/export', {\r\n        ...this.queryParams\r\n      }, `currency_${new Date().getTime()}.xlsx`)\r\n    },\r\n    queryLocationId(val) {\r\n      this.queryParams.locationId = val\r\n    },\r\n    getLocationId(val) {\r\n      this.form.locationId = val\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.currencyLocalName + '\"吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.currencyId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;AAuJA,IAAAA,SAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,eAAA,MAAAC,GAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,aAAA;QACAC,UAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAZ,UAAA,WAAAA,WAAAa,CAAA;MACA,IAAAA,CAAA;QACA,KAAApB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAsB,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAArB,OAAA;MACA,IAAAsB,sBAAA,OAAAb,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAf,YAAA,GAAAkB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAhB,KAAA,GAAAmB,QAAA,CAAAnB,KAAA;QACAgB,KAAA,CAAArB,OAAA;MACA;IACA;IACA;IACA0B,MAAA,WAAAA,OAAA;MACA,KAAAlB,IAAA;MACA,KAAAmB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAb,IAAA;QACAc,UAAA;QACAC,YAAA;QACAhB,UAAA;QACAiB,iBAAA;QACAC,YAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAhC,WAAA,CAAAC,OAAA;MACA,KAAAS,OAAA;IACA;IACA,aACAuB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3C,GAAA,GAAA2C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAlB,UAAA;MAAA;MACA,KAAA1B,MAAA,GAAA0C,SAAA,CAAAG,MAAA;MACA,KAAA5C,QAAA,IAAAyC,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAArB,KAAA;MACA,KAAAnB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA0C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAxB,KAAA;MACA,IAAAC,UAAA,GAAAsB,GAAA,CAAAtB,UAAA,SAAA3B,GAAA;MACA,IAAAmD,qBAAA,EAAAxB,UAAA,EAAAL,IAAA,WAAAC,QAAA;QACA2B,MAAA,CAAArC,IAAA,GAAAU,QAAA,CAAA7B,IAAA;QACAwD,MAAA,CAAA3C,IAAA;QACA2C,MAAA,CAAA5C,KAAA;QACA4C,MAAA,CAAArD,eAAA,GAAA0B,QAAA,CAAA1B,eAAA;MACA;IACA;IACA,WACAuD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAAxC,IAAA,CAAAc,UAAA;YACA,IAAA8B,wBAAA,EAAAJ,MAAA,CAAAxC,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACA8B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA9C,IAAA;cACA8C,MAAA,CAAAnC,OAAA;YACA;UACA;YACA,IAAA0C,qBAAA,EAAAP,MAAA,CAAAxC,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACA8B,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAA9C,IAAA;cACA8C,MAAA,CAAAnC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,WAAA,GAAAd,GAAA,CAAAtB,UAAA,SAAA3B,GAAA;MACA,KAAAgE,QAAA,kBAAAD,WAAA;QAAAE,WAAA;MAAA,GAAA3C,IAAA;QACA,WAAA4C,qBAAA,EAAAH,WAAA;MACA,GAAAzC,IAAA;QACAwC,MAAA,CAAA5C,OAAA;QACA4C,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,+BAAAC,cAAA,CAAAC,OAAA,MACA,KAAA/D,WAAA,eAAAgE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,GAAA;MACA,KAAApE,WAAA,CAAAI,UAAA,GAAAgE,GAAA;IACA;IACAC,aAAA,WAAAA,cAAAD,GAAA;MACA,KAAA/D,IAAA,CAAAD,UAAA,GAAAgE,GAAA;IACA;IACAE,kBAAA,WAAAA,mBAAA7B,GAAA;MAAA,IAAA8B,MAAA;MACA,IAAAC,IAAA,GAAA/B,GAAA,CAAAgC,MAAA;MACA,KAAAjB,QAAA,UAAAgB,IAAA,UAAA/B,GAAA,CAAApB,iBAAA;QAAAoC,WAAA;MAAA,GAAA3C,IAAA;QACA,WAAA4D,sBAAA,EAAAjC,GAAA,CAAAtB,UAAA,EAAAsB,GAAA,CAAAgC,MAAA;MACA,GAAA3D,IAAA;QACAyD,MAAA,CAAArB,MAAA,CAAAC,UAAA,CAAAqB,IAAA;MACA,GAAAb,KAAA;QACAlB,GAAA,CAAAgC,MAAA,GAAAhC,GAAA,CAAAgC,MAAA;MACA;IACA;EACA;AACA;AAAAE,OAAA,CAAAZ,OAAA,GAAAa,QAAA"}]}