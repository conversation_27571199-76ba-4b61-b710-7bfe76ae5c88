import request from '@/utils/request'

// 查询发票明细信息列表
export function listVatinvoicedetails(query) {
  return request({
    url: '/system/vatinvoicedetails/list',
    method: 'get',
    params: query
  })
}

// 查询发票明细信息详细
export function getVatinvoicedetails(invoiceDetailsId) {
  return request({
    url: '/system/vatinvoicedetails/' + invoiceDetailsId,
    method: 'get'
  })
}

// 新增发票明细信息
export function addVatinvoicedetails(data) {
  return request({
    url: '/system/vatinvoicedetails',
    method: 'post',
    data: data
  })
}

// 修改发票明细信息
export function updateVatinvoicedetails(data) {
  return request({
    url: '/system/vatinvoicedetails',
    method: 'put',
    data: data
  })
}

// 删除发票明细信息
export function delVatinvoicedetails(invoiceDetailsId) {
  return request({
    url: '/system/vatinvoicedetails/' + invoiceDetailsId,
    method: 'delete'
  })
}

// 状态修改
export function changeStatus(invoiceDetailsId, status) {
  const data = {
    invoiceDetailsId,
    status
  }
  return request({
    url: '/system/vatinvoicedetails/changeStatus',
    method: 'put',
    data: data
  })
}
