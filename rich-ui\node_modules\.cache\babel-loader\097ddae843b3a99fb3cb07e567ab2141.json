{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\mixins\\serviceManager.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\mixins\\serviceManager.js", "mtime": 1752032719061}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_lodash", "_interopRequireDefault", "require", "_default", "data", "serviceTypeMap", "methods", "createServiceInstance", "accountConfirmTime", "agreementNo", "agreementTypeCode", "clientConfirmedTime", "confirmAccountId", "createBy", "createByName", "createTime", "deleteBy", "deleteByName", "deleteStatus", "deleteTime", "inquiryInnerRemark", "inquiryLeatestUpdatedTime", "inquiryNo", "inquiryNotice", "inquiryPsaId", "isAccountConfirmed", "isDnClientConfirmed", "isDnOpConfirmed", "isDnPsaConfirmed", "isDnSalesConfirmed", "isDnSupplierConfirmed", "logisticsPaymentTermsCode", "maxWeight", "opConfirmedTime", "paymentTitleCode", "permissionLevel", "psaConfirmedTime", "rctId", "rctNo", "remark", "salesConfirmedTime", "serviceBelongTo", "serviceId", "serviceTypeId", "supplierConfirmedTime", "supplierContact", "supplierId", "supplierName", "supplierSummary", "supplierTel", "updateBy", "updateByName", "updateTime", "createServiceObject", "includeServiceInstance", "arguments", "length", "undefined", "additionalFields", "_objectSpread2", "default", "rsServiceInstances", "rsChargeList", "rsOpLogList", "rsDocList", "getServiceObject", "parseInt", "form", "rsOpSeaFclList", "rsOpSeaLclList", "rsOpAirList", "rsOpCtnrTruckList", "rsOpBulkTruckList", "rsOpDocDeclareList", "rsOpFreeDeclareList", "rsOpRailFCL", "rsOpRailLCL", "rsOpExpress", "rsOpDOAgent", "rsOpClearAgent", "rsOpWHS", "getServiceInstance", "serviceObject", "rsOpSeaFclServiceInstance", "rsOpSeaLclServiceInstance", "rsOpAirServiceInstance", "rsOpRailFclServiceInstance", "rsOpRailLclServiceInstance", "rsOpExpressServiceInstance", "rsOpCtnrTruckServiceInstance", "rsOpBulkTruckServiceInstance", "rsOpDocDeclareServiceInstance", "rsOpFreeDeclareServiceInstance", "rsOpDOAgentServiceInstance", "rsOpClearAgentServiceInstance", "rsOpWHSServiceInstance", "addEmptyService", "rsOpSeaFcl", "push", "rsOpSeaLcl", "rsOpAir", "rsOpCtnrTruck", "rsOpTruckList", "rsOpBulkTruck", "rsOpDocDeclare", "rsOpFreeDeclare", "getServiceTypeName", "toggleServiceFold", "rsOpSealFclFold", "rsOpSealLclFold", "rsOpAirFold", "rsOpRailFclFold", "rsOpRailLclFold", "rsOpExpressFold", "rsOpCtnrTruckFold", "rsOpBulkTruckFold", "rsOpDocDeclareFold", "rsOpFreeDeclareFold", "rsOpDOAgentFold", "rsOpClearAgentFold", "rsOpWHSFold", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/system/document/mixins/serviceManager.js"], "sourcesContent": ["/**\r\n * 服务管理Mixin\r\n * 集中管理服务相关的方法\r\n */\r\nimport _ from \"lodash\"\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      // 服务类型映射\r\n      serviceTypeMap: {\r\n        1: \"整柜海运\",\r\n        2: \"拼柜海运\",\r\n        10: \"空运\",\r\n        20: \"整柜铁路\",\r\n        21: \"拼柜铁路\",\r\n        40: \"快递\",\r\n        50: \"整柜拖车\",\r\n        51: \"散货拖车\",\r\n        60: \"单证报关\",\r\n        61: \"全包报关\",\r\n        70: \"代理放单\",\r\n        71: \"清关代理\",\r\n        80: \"仓储服务\"\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 创建服务实例基础对象\r\n     * @returns {Object} 服务实例对象\r\n     */\r\n    createServiceInstance() {\r\n      return {\r\n        accountConfirmTime: null,\r\n        agreementNo: \"\",\r\n        agreementTypeCode: null,\r\n        clientConfirmedTime: null,\r\n        confirmAccountId: null,\r\n        createBy: null,\r\n        createByName: null,\r\n        createTime: null,\r\n        deleteBy: null,\r\n        deleteByName: null,\r\n        deleteStatus: null,\r\n        deleteTime: null,\r\n        inquiryInnerRemark: \"\",\r\n        inquiryLeatestUpdatedTime: null,\r\n        inquiryNo: null,\r\n        inquiryNotice: null,\r\n        inquiryPsaId: null,\r\n        isAccountConfirmed: null,\r\n        isDnClientConfirmed: null,\r\n        isDnOpConfirmed: null,\r\n        isDnPsaConfirmed: null,\r\n        isDnSalesConfirmed: null,\r\n        isDnSupplierConfirmed: null,\r\n        logisticsPaymentTermsCode: null,\r\n        maxWeight: null,\r\n        opConfirmedTime: null,\r\n        paymentTitleCode: null,\r\n        permissionLevel: null,\r\n        psaConfirmedTime: null,\r\n        rctId: null,\r\n        rctNo: null,\r\n        remark: null,\r\n        salesConfirmedTime: null,\r\n        serviceBelongTo: null,\r\n        serviceId: null,\r\n        serviceTypeId: null,\r\n        supplierConfirmedTime: null,\r\n        supplierContact: null,\r\n        supplierId: null,\r\n        supplierName: null,\r\n        supplierSummary: null,\r\n        supplierTel: null,\r\n        updateBy: null,\r\n        updateByName: null,\r\n        updateTime: null\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 创建服务对象\r\n     * @param {boolean} includeServiceInstance - 是否包含服务实例\r\n     * @param {Object} additionalFields - 额外字段\r\n     * @returns {Object} 服务对象\r\n     */\r\n    createServiceObject(includeServiceInstance = true, additionalFields = {}) {\r\n      return {\r\n        ...(includeServiceInstance && {rsServiceInstances: this.createServiceInstance()}),\r\n        rsChargeList: [],\r\n        rsOpLogList: [],\r\n        rsDocList: [],\r\n        ...additionalFields\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 根据服务类型ID获取服务对象\r\n     * @param {number} serviceTypeId - 服务类型ID\r\n     * @returns {Object|null} 服务对象\r\n     */\r\n    getServiceObject(serviceTypeId) {\r\n      if (!serviceTypeId) return null\r\n\r\n      switch (parseInt(serviceTypeId)) {\r\n        case 1:\r\n          return this.form.rsOpSeaFclList && this.form.rsOpSeaFclList.length > 0 ?\r\n            this.form.rsOpSeaFclList[0] : null\r\n        case 2:\r\n          return this.form.rsOpSeaLclList && this.form.rsOpSeaLclList.length > 0 ?\r\n            this.form.rsOpSeaLclList[0] : null\r\n        case 10:\r\n          return this.form.rsOpAirList && this.form.rsOpAirList.length > 0 ?\r\n            this.form.rsOpAirList[0] : null\r\n        case 50:\r\n          return this.form.rsOpCtnrTruckList && this.form.rsOpCtnrTruckList.length > 0 ?\r\n            this.form.rsOpCtnrTruckList[0] : null\r\n        case 51:\r\n          return this.form.rsOpBulkTruckList && this.form.rsOpBulkTruckList.length > 0 ?\r\n            this.form.rsOpBulkTruckList[0] : null\r\n        case 60:\r\n          return this.form.rsOpDocDeclareList && this.form.rsOpDocDeclareList.length > 0 ?\r\n            this.form.rsOpDocDeclareList[0] : null\r\n        case 61:\r\n          return this.form.rsOpFreeDeclareList && this.form.rsOpFreeDeclareList.length > 0 ?\r\n            this.form.rsOpFreeDeclareList[0] : null\r\n        case 20:\r\n          return this.rsOpRailFCL\r\n        case 21:\r\n          return this.rsOpRailLCL\r\n        case 40:\r\n          return this.rsOpExpress\r\n        case 70:\r\n          return this.rsOpDOAgent\r\n        case 71:\r\n          return this.rsOpClearAgent\r\n        case 80:\r\n          return this.rsOpWHS\r\n        default:\r\n          return null\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取服务实例对象\r\n     * @param {number} serviceTypeId - 服务类型ID\r\n     * @returns {Object|null} 服务实例对象\r\n     */\r\n    getServiceInstance(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n\r\n      if (serviceObject && serviceObject.rsServiceInstances) {\r\n        return serviceObject.rsServiceInstances\r\n      }\r\n\r\n      switch (parseInt(serviceTypeId)) {\r\n        case 1:\r\n          return this.rsOpSeaFclServiceInstance\r\n        case 2:\r\n          return this.rsOpSeaLclServiceInstance\r\n        case 10:\r\n          return this.rsOpAirServiceInstance\r\n        case 20:\r\n          return this.rsOpRailFclServiceInstance\r\n        case 21:\r\n          return this.rsOpRailLclServiceInstance\r\n        case 40:\r\n          return this.rsOpExpressServiceInstance\r\n        case 50:\r\n          return this.rsOpCtnrTruckServiceInstance\r\n        case 51:\r\n          return this.rsOpBulkTruckServiceInstance\r\n        case 60:\r\n          return this.rsOpDocDeclareServiceInstance\r\n        case 61:\r\n          return this.rsOpFreeDeclareServiceInstance\r\n        case 70:\r\n          return this.rsOpDOAgentServiceInstance\r\n        case 71:\r\n          return this.rsOpClearAgentServiceInstance\r\n        case 80:\r\n          return this.rsOpWHSServiceInstance\r\n        default:\r\n          return null\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 添加空服务\r\n     * @param {number} serviceTypeId - 服务类型ID\r\n     */\r\n    addEmptyService(serviceTypeId) {\r\n      if (!serviceTypeId) return\r\n\r\n      switch (parseInt(serviceTypeId)) {\r\n        case 1:\r\n          if (!this.form.rsOpSeaFclList || this.form.rsOpSeaFclList.length === 0) {\r\n            if (!this.form.rsOpSeaFclList) {\r\n              this.form.rsOpSeaFclList = []\r\n            }\r\n            const rsOpSeaFcl = this.createServiceObject()\r\n            rsOpSeaFcl.rsServiceInstances = this.createServiceInstance()\r\n            this.form.rsOpSeaFclList.push(rsOpSeaFcl)\r\n          }\r\n          break\r\n        case 2:\r\n          if (!this.form.rsOpSeaLclList || this.form.rsOpSeaLclList.length === 0) {\r\n            if (!this.form.rsOpSeaLclList) {\r\n              this.form.rsOpSeaLclList = []\r\n            }\r\n            const rsOpSeaLcl = this.createServiceObject()\r\n            rsOpSeaLcl.rsServiceInstances = this.createServiceInstance()\r\n            this.form.rsOpSeaLclList.push(rsOpSeaLcl)\r\n          }\r\n          break\r\n        case 10:\r\n          if (!this.form.rsOpAirList || this.form.rsOpAirList.length === 0) {\r\n            if (!this.form.rsOpAirList) {\r\n              this.form.rsOpAirList = []\r\n            }\r\n            const rsOpAir = this.createServiceObject()\r\n            rsOpAir.rsServiceInstances = this.createServiceInstance()\r\n            this.form.rsOpAirList.push(rsOpAir)\r\n          }\r\n          break\r\n        case 50:\r\n          if (!this.form.rsOpCtnrTruckList || this.form.rsOpCtnrTruckList.length === 0) {\r\n            if (!this.form.rsOpCtnrTruckList) {\r\n              this.form.rsOpCtnrTruckList = []\r\n            }\r\n            const rsOpCtnrTruck = this.createServiceObject(true, {rsOpTruckList: []})\r\n            rsOpCtnrTruck.rsServiceInstances = this.createServiceInstance()\r\n            this.form.rsOpCtnrTruckList.push(rsOpCtnrTruck)\r\n          }\r\n          break\r\n        case 51:\r\n          if (!this.form.rsOpBulkTruckList || this.form.rsOpBulkTruckList.length === 0) {\r\n            if (!this.form.rsOpBulkTruckList) {\r\n              this.form.rsOpBulkTruckList = []\r\n            }\r\n            const rsOpBulkTruck = this.createServiceObject(true, {rsOpTruckList: []})\r\n            rsOpBulkTruck.rsServiceInstances = this.createServiceInstance()\r\n            this.form.rsOpBulkTruckList.push(rsOpBulkTruck)\r\n          }\r\n          break\r\n        case 60:\r\n          if (!this.form.rsOpDocDeclareList || this.form.rsOpDocDeclareList.length === 0) {\r\n            if (!this.form.rsOpDocDeclareList) {\r\n              this.form.rsOpDocDeclareList = []\r\n            }\r\n            const rsOpDocDeclare = this.createServiceObject()\r\n            rsOpDocDeclare.rsServiceInstances = this.createServiceInstance()\r\n            this.form.rsOpDocDeclareList.push(rsOpDocDeclare)\r\n          }\r\n          break\r\n        case 61:\r\n          if (!this.form.rsOpFreeDeclareList || this.form.rsOpFreeDeclareList.length === 0) {\r\n            if (!this.form.rsOpFreeDeclareList) {\r\n              this.form.rsOpFreeDeclareList = []\r\n            }\r\n            const rsOpFreeDeclare = this.createServiceObject()\r\n            rsOpFreeDeclare.rsServiceInstances = this.createServiceInstance()\r\n            this.form.rsOpFreeDeclareList.push(rsOpFreeDeclare)\r\n          }\r\n          break\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取服务类型名称\r\n     * @param {number} serviceTypeId - 服务类型ID\r\n     * @returns {string} 服务类型名称\r\n     */\r\n    getServiceTypeName(serviceTypeId) {\r\n      return this.serviceTypeMap[serviceTypeId] || '未知服务类型'\r\n    },\r\n\r\n    /**\r\n     * 切换服务面板的折叠状态\r\n     * @param {number} serviceTypeId - 服务类型ID\r\n     */\r\n    toggleServiceFold(serviceTypeId) {\r\n      if (!serviceTypeId) return\r\n\r\n      switch (parseInt(serviceTypeId)) {\r\n        case 1:\r\n          this.rsOpSealFclFold = !this.rsOpSealFclFold\r\n          break\r\n        case 2:\r\n          this.rsOpSealLclFold = !this.rsOpSealLclFold\r\n          break\r\n        case 10:\r\n          this.rsOpAirFold = !this.rsOpAirFold\r\n          break\r\n        case 20:\r\n          this.rsOpRailFclFold = !this.rsOpRailFclFold\r\n          break\r\n        case 21:\r\n          this.rsOpRailLclFold = !this.rsOpRailLclFold\r\n          break\r\n        case 40:\r\n          this.rsOpExpressFold = !this.rsOpExpressFold\r\n          break\r\n        case 50:\r\n          this.rsOpCtnrTruckFold = !this.rsOpCtnrTruckFold\r\n          break\r\n        case 51:\r\n          this.rsOpBulkTruckFold = !this.rsOpBulkTruckFold\r\n          break\r\n        case 60:\r\n          this.rsOpDocDeclareFold = !this.rsOpDocDeclareFold\r\n          break\r\n        case 61:\r\n          this.rsOpFreeDeclareFold = !this.rsOpFreeDeclareFold\r\n          break\r\n        case 70:\r\n          this.rsOpDOAgentFold = !this.rsOpDOAgentFold\r\n          break\r\n        case 71:\r\n          this.rsOpClearAgentFold = !this.rsOpClearAgentFold\r\n          break\r\n        case 80:\r\n          this.rsOpWHSFold = !this.rsOpWHSFold\r\n          break\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;AAIA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AAJA;AACA;AACA;AACA;AAHA,IAAAC,QAAA,GAMe;EACbC,IAAI,WAAAA,KAAA,EAAG;IACL,OAAO;MACL;MACAC,cAAc,EAAE;QACd,CAAC,EAAE,MAAM;QACT,CAAC,EAAE,MAAM;QACT,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,IAAI;QACR,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE,MAAM;QACV,EAAE,EAAE;MACN;IACF,CAAC;EACH,CAAC;EACDC,OAAO,EAAE;IACP;AACJ;AACA;AACA;IACIC,qBAAqB,WAAAA,sBAAA,EAAG;MACtB,OAAO;QACLC,kBAAkB,EAAE,IAAI;QACxBC,WAAW,EAAE,EAAE;QACfC,iBAAiB,EAAE,IAAI;QACvBC,mBAAmB,EAAE,IAAI;QACzBC,gBAAgB,EAAE,IAAI;QACtBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBC,kBAAkB,EAAE,EAAE;QACtBC,yBAAyB,EAAE,IAAI;QAC/BC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,IAAI;QAClBC,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE,IAAI;QACzBC,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,IAAI;QACtBC,kBAAkB,EAAE,IAAI;QACxBC,qBAAqB,EAAE,IAAI;QAC3BC,yBAAyB,EAAE,IAAI;QAC/BC,SAAS,EAAE,IAAI;QACfC,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,IAAI;QACtBC,eAAe,EAAE,IAAI;QACrBC,gBAAgB,EAAE,IAAI;QACtBC,KAAK,EAAE,IAAI;QACXC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE,IAAI;QACZC,kBAAkB,EAAE,IAAI;QACxBC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAE,IAAI;QACfC,aAAa,EAAE,IAAI;QACnBC,qBAAqB,EAAE,IAAI;QAC3BC,eAAe,EAAE,IAAI;QACrBC,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClBC,eAAe,EAAE,IAAI;QACrBC,WAAW,EAAE,IAAI;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE;MACd,CAAC;IACH,CAAC;IAED;AACJ;AACA;AACA;AACA;AACA;IACIC,mBAAmB,WAAAA,oBAAA,EAAuD;MAAA,IAAtDC,sBAAsB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAAA,IAAEG,gBAAgB,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MACtE,WAAAI,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACMN,sBAAsB,IAAI;QAACO,kBAAkB,EAAE,IAAI,CAACtD,qBAAqB,CAAC;MAAC,CAAC;QAChFuD,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,EAAE;QACfC,SAAS,EAAE;MAAE,GACVN,gBAAgB;IAEvB,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIO,gBAAgB,WAAAA,iBAACtB,aAAa,EAAE;MAC9B,IAAI,CAACA,aAAa,EAAE,OAAO,IAAI;MAE/B,QAAQuB,QAAQ,CAACvB,aAAa,CAAC;QAC7B,KAAK,CAAC;UACJ,OAAO,IAAI,CAACwB,IAAI,CAACC,cAAc,IAAI,IAAI,CAACD,IAAI,CAACC,cAAc,CAACZ,MAAM,GAAG,CAAC,GACpE,IAAI,CAACW,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI;QACtC,KAAK,CAAC;UACJ,OAAO,IAAI,CAACD,IAAI,CAACE,cAAc,IAAI,IAAI,CAACF,IAAI,CAACE,cAAc,CAACb,MAAM,GAAG,CAAC,GACpE,IAAI,CAACW,IAAI,CAACE,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI;QACtC,KAAK,EAAE;UACL,OAAO,IAAI,CAACF,IAAI,CAACG,WAAW,IAAI,IAAI,CAACH,IAAI,CAACG,WAAW,CAACd,MAAM,GAAG,CAAC,GAC9D,IAAI,CAACW,IAAI,CAACG,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI;QACnC,KAAK,EAAE;UACL,OAAO,IAAI,CAACH,IAAI,CAACI,iBAAiB,IAAI,IAAI,CAACJ,IAAI,CAACI,iBAAiB,CAACf,MAAM,GAAG,CAAC,GAC1E,IAAI,CAACW,IAAI,CAACI,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;QACzC,KAAK,EAAE;UACL,OAAO,IAAI,CAACJ,IAAI,CAACK,iBAAiB,IAAI,IAAI,CAACL,IAAI,CAACK,iBAAiB,CAAChB,MAAM,GAAG,CAAC,GAC1E,IAAI,CAACW,IAAI,CAACK,iBAAiB,CAAC,CAAC,CAAC,GAAG,IAAI;QACzC,KAAK,EAAE;UACL,OAAO,IAAI,CAACL,IAAI,CAACM,kBAAkB,IAAI,IAAI,CAACN,IAAI,CAACM,kBAAkB,CAACjB,MAAM,GAAG,CAAC,GAC5E,IAAI,CAACW,IAAI,CAACM,kBAAkB,CAAC,CAAC,CAAC,GAAG,IAAI;QAC1C,KAAK,EAAE;UACL,OAAO,IAAI,CAACN,IAAI,CAACO,mBAAmB,IAAI,IAAI,CAACP,IAAI,CAACO,mBAAmB,CAAClB,MAAM,GAAG,CAAC,GAC9E,IAAI,CAACW,IAAI,CAACO,mBAAmB,CAAC,CAAC,CAAC,GAAG,IAAI;QAC3C,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,WAAW;QACzB,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,WAAW;QACzB,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,WAAW;QACzB,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,WAAW;QACzB,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,cAAc;QAC5B,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,OAAO;QACrB;UACE,OAAO,IAAI;MACf;IACF,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIC,kBAAkB,WAAAA,mBAACtC,aAAa,EAAE;MAChC,IAAMuC,aAAa,GAAG,IAAI,CAACjB,gBAAgB,CAACtB,aAAa,CAAC;MAE1D,IAAIuC,aAAa,IAAIA,aAAa,CAACrB,kBAAkB,EAAE;QACrD,OAAOqB,aAAa,CAACrB,kBAAkB;MACzC;MAEA,QAAQK,QAAQ,CAACvB,aAAa,CAAC;QAC7B,KAAK,CAAC;UACJ,OAAO,IAAI,CAACwC,yBAAyB;QACvC,KAAK,CAAC;UACJ,OAAO,IAAI,CAACC,yBAAyB;QACvC,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,sBAAsB;QACpC,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,0BAA0B;QACxC,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,0BAA0B;QACxC,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,0BAA0B;QACxC,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,4BAA4B;QAC1C,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,4BAA4B;QAC1C,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,6BAA6B;QAC3C,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,8BAA8B;QAC5C,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,0BAA0B;QACxC,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,6BAA6B;QAC3C,KAAK,EAAE;UACL,OAAO,IAAI,CAACC,sBAAsB;QACpC;UACE,OAAO,IAAI;MACf;IACF,CAAC;IAED;AACJ;AACA;AACA;IACIC,eAAe,WAAAA,gBAACrD,aAAa,EAAE;MAC7B,IAAI,CAACA,aAAa,EAAE;MAEpB,QAAQuB,QAAQ,CAACvB,aAAa,CAAC;QAC7B,KAAK,CAAC;UACJ,IAAI,CAAC,IAAI,CAACwB,IAAI,CAACC,cAAc,IAAI,IAAI,CAACD,IAAI,CAACC,cAAc,CAACZ,MAAM,KAAK,CAAC,EAAE;YACtE,IAAI,CAAC,IAAI,CAACW,IAAI,CAACC,cAAc,EAAE;cAC7B,IAAI,CAACD,IAAI,CAACC,cAAc,GAAG,EAAE;YAC/B;YACA,IAAM6B,UAAU,GAAG,IAAI,CAAC5C,mBAAmB,CAAC,CAAC;YAC7C4C,UAAU,CAACpC,kBAAkB,GAAG,IAAI,CAACtD,qBAAqB,CAAC,CAAC;YAC5D,IAAI,CAAC4D,IAAI,CAACC,cAAc,CAAC8B,IAAI,CAACD,UAAU,CAAC;UAC3C;UACA;QACF,KAAK,CAAC;UACJ,IAAI,CAAC,IAAI,CAAC9B,IAAI,CAACE,cAAc,IAAI,IAAI,CAACF,IAAI,CAACE,cAAc,CAACb,MAAM,KAAK,CAAC,EAAE;YACtE,IAAI,CAAC,IAAI,CAACW,IAAI,CAACE,cAAc,EAAE;cAC7B,IAAI,CAACF,IAAI,CAACE,cAAc,GAAG,EAAE;YAC/B;YACA,IAAM8B,UAAU,GAAG,IAAI,CAAC9C,mBAAmB,CAAC,CAAC;YAC7C8C,UAAU,CAACtC,kBAAkB,GAAG,IAAI,CAACtD,qBAAqB,CAAC,CAAC;YAC5D,IAAI,CAAC4D,IAAI,CAACE,cAAc,CAAC6B,IAAI,CAACC,UAAU,CAAC;UAC3C;UACA;QACF,KAAK,EAAE;UACL,IAAI,CAAC,IAAI,CAAChC,IAAI,CAACG,WAAW,IAAI,IAAI,CAACH,IAAI,CAACG,WAAW,CAACd,MAAM,KAAK,CAAC,EAAE;YAChE,IAAI,CAAC,IAAI,CAACW,IAAI,CAACG,WAAW,EAAE;cAC1B,IAAI,CAACH,IAAI,CAACG,WAAW,GAAG,EAAE;YAC5B;YACA,IAAM8B,OAAO,GAAG,IAAI,CAAC/C,mBAAmB,CAAC,CAAC;YAC1C+C,OAAO,CAACvC,kBAAkB,GAAG,IAAI,CAACtD,qBAAqB,CAAC,CAAC;YACzD,IAAI,CAAC4D,IAAI,CAACG,WAAW,CAAC4B,IAAI,CAACE,OAAO,CAAC;UACrC;UACA;QACF,KAAK,EAAE;UACL,IAAI,CAAC,IAAI,CAACjC,IAAI,CAACI,iBAAiB,IAAI,IAAI,CAACJ,IAAI,CAACI,iBAAiB,CAACf,MAAM,KAAK,CAAC,EAAE;YAC5E,IAAI,CAAC,IAAI,CAACW,IAAI,CAACI,iBAAiB,EAAE;cAChC,IAAI,CAACJ,IAAI,CAACI,iBAAiB,GAAG,EAAE;YAClC;YACA,IAAM8B,aAAa,GAAG,IAAI,CAAChD,mBAAmB,CAAC,IAAI,EAAE;cAACiD,aAAa,EAAE;YAAE,CAAC,CAAC;YACzED,aAAa,CAACxC,kBAAkB,GAAG,IAAI,CAACtD,qBAAqB,CAAC,CAAC;YAC/D,IAAI,CAAC4D,IAAI,CAACI,iBAAiB,CAAC2B,IAAI,CAACG,aAAa,CAAC;UACjD;UACA;QACF,KAAK,EAAE;UACL,IAAI,CAAC,IAAI,CAAClC,IAAI,CAACK,iBAAiB,IAAI,IAAI,CAACL,IAAI,CAACK,iBAAiB,CAAChB,MAAM,KAAK,CAAC,EAAE;YAC5E,IAAI,CAAC,IAAI,CAACW,IAAI,CAACK,iBAAiB,EAAE;cAChC,IAAI,CAACL,IAAI,CAACK,iBAAiB,GAAG,EAAE;YAClC;YACA,IAAM+B,aAAa,GAAG,IAAI,CAAClD,mBAAmB,CAAC,IAAI,EAAE;cAACiD,aAAa,EAAE;YAAE,CAAC,CAAC;YACzEC,aAAa,CAAC1C,kBAAkB,GAAG,IAAI,CAACtD,qBAAqB,CAAC,CAAC;YAC/D,IAAI,CAAC4D,IAAI,CAACK,iBAAiB,CAAC0B,IAAI,CAACK,aAAa,CAAC;UACjD;UACA;QACF,KAAK,EAAE;UACL,IAAI,CAAC,IAAI,CAACpC,IAAI,CAACM,kBAAkB,IAAI,IAAI,CAACN,IAAI,CAACM,kBAAkB,CAACjB,MAAM,KAAK,CAAC,EAAE;YAC9E,IAAI,CAAC,IAAI,CAACW,IAAI,CAACM,kBAAkB,EAAE;cACjC,IAAI,CAACN,IAAI,CAACM,kBAAkB,GAAG,EAAE;YACnC;YACA,IAAM+B,cAAc,GAAG,IAAI,CAACnD,mBAAmB,CAAC,CAAC;YACjDmD,cAAc,CAAC3C,kBAAkB,GAAG,IAAI,CAACtD,qBAAqB,CAAC,CAAC;YAChE,IAAI,CAAC4D,IAAI,CAACM,kBAAkB,CAACyB,IAAI,CAACM,cAAc,CAAC;UACnD;UACA;QACF,KAAK,EAAE;UACL,IAAI,CAAC,IAAI,CAACrC,IAAI,CAACO,mBAAmB,IAAI,IAAI,CAACP,IAAI,CAACO,mBAAmB,CAAClB,MAAM,KAAK,CAAC,EAAE;YAChF,IAAI,CAAC,IAAI,CAACW,IAAI,CAACO,mBAAmB,EAAE;cAClC,IAAI,CAACP,IAAI,CAACO,mBAAmB,GAAG,EAAE;YACpC;YACA,IAAM+B,eAAe,GAAG,IAAI,CAACpD,mBAAmB,CAAC,CAAC;YAClDoD,eAAe,CAAC5C,kBAAkB,GAAG,IAAI,CAACtD,qBAAqB,CAAC,CAAC;YACjE,IAAI,CAAC4D,IAAI,CAACO,mBAAmB,CAACwB,IAAI,CAACO,eAAe,CAAC;UACrD;UACA;MACJ;IACF,CAAC;IAED;AACJ;AACA;AACA;AACA;IACIC,kBAAkB,WAAAA,mBAAC/D,aAAa,EAAE;MAChC,OAAO,IAAI,CAACtC,cAAc,CAACsC,aAAa,CAAC,IAAI,QAAQ;IACvD,CAAC;IAED;AACJ;AACA;AACA;IACIgE,iBAAiB,WAAAA,kBAAChE,aAAa,EAAE;MAC/B,IAAI,CAACA,aAAa,EAAE;MAEpB,QAAQuB,QAAQ,CAACvB,aAAa,CAAC;QAC7B,KAAK,CAAC;UACJ,IAAI,CAACiE,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;UAC5C;QACF,KAAK,CAAC;UACJ,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;UAC5C;QACF,KAAK,EAAE;UACL,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;UACpC;QACF,KAAK,EAAE;UACL,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;UAC5C;QACF,KAAK,EAAE;UACL,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;UAC5C;QACF,KAAK,EAAE;UACL,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;UAC5C;QACF,KAAK,EAAE;UACL,IAAI,CAACC,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;UAChD;QACF,KAAK,EAAE;UACL,IAAI,CAACC,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;UAChD;QACF,KAAK,EAAE;UACL,IAAI,CAACC,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;UAClD;QACF,KAAK,EAAE;UACL,IAAI,CAACC,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;UACpD;QACF,KAAK,EAAE;UACL,IAAI,CAACC,eAAe,GAAG,CAAC,IAAI,CAACA,eAAe;UAC5C;QACF,KAAK,EAAE;UACL,IAAI,CAACC,kBAAkB,GAAG,CAAC,IAAI,CAACA,kBAAkB;UAClD;QACF,KAAK,EAAE;UACL,IAAI,CAACC,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;UACpC;MACJ;IACF;EACF;AACF,CAAC;AAAAC,OAAA,CAAA7D,OAAA,GAAAzD,QAAA"}]}