{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\permission.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\permission.js", "mtime": 1754876882560}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "check<PERSON><PERSON><PERSON>", "value", "Array", "length", "permissions", "store", "getters", "permissionDatas", "all_permission", "hasPermission", "some", "permission", "includes", "console", "error", "checkRole", "roles", "permissionRoles", "super_admin", "hasRole", "role"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/utils/permission.js"], "sourcesContent": ["import store from '@/store'\r\n\r\n/**\r\n * 字符权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\r\nexport function checkPermi(value) {\r\n  if (value && value instanceof Array && value.length > 0) {\r\n    const permissions = store.getters && store.getters.permissions\r\n    const permissionDatas = value\r\n    const all_permission = \"*:*:*\";\r\n\r\n    const hasPermission = permissions.some(permission => {\r\n      return all_permission == permission || permissionDatas.includes(permission)\r\n    })\r\n\r\n    if (!hasPermission) {\r\n      return false\r\n    }\r\n    return true\r\n  } else {\r\n    console.error(`need roles! Like checkPermi=\"['system:user:add','system:user:edit']\"`)\r\n    return false\r\n  }\r\n}\r\n\r\n/**\r\n * 角色权限校验\r\n * @param {Array} value 校验值\r\n * @returns {Boolean}\r\n */\r\nexport function checkRole(value) {\r\n  if (value && value instanceof Array && value.length > 0) {\r\n    const roles = store.getters && store.getters.roles\r\n    const permissionRoles = value\r\n    const super_admin = \"admin\";\r\n\r\n    const hasRole = roles.some(role => {\r\n      return super_admin == role || permissionRoles.includes(role)\r\n    })\r\n\r\n    if (!hasRole) {\r\n      return false\r\n    }\r\n    return true\r\n  } else {\r\n    console.error(`need roles! Like checkRole=\"['admin','editor']\"`)\r\n    return false\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,UAAUA,CAACC,KAAK,EAAE;EAChC,IAAIA,KAAK,IAAIA,KAAK,YAAYC,KAAK,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMC,WAAW,GAAGC,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACF,WAAW;IAC9D,IAAMG,eAAe,GAAGN,KAAK;IAC7B,IAAMO,cAAc,GAAG,OAAO;IAE9B,IAAMC,aAAa,GAAGL,WAAW,CAACM,IAAI,CAAC,UAAAC,UAAU,EAAI;MACnD,OAAOH,cAAc,IAAIG,UAAU,IAAIJ,eAAe,CAACK,QAAQ,CAACD,UAAU,CAAC;IAC7E,CAAC,CAAC;IAEF,IAAI,CAACF,aAAa,EAAE;MAClB,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,MAAM;IACLI,OAAO,CAACC,KAAK,yEAAuE,CAAC;IACrF,OAAO,KAAK;EACd;AACF;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASC,SAASA,CAACd,KAAK,EAAE;EAC/B,IAAIA,KAAK,IAAIA,KAAK,YAAYC,KAAK,IAAID,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;IACvD,IAAMa,KAAK,GAAGX,cAAK,CAACC,OAAO,IAAID,cAAK,CAACC,OAAO,CAACU,KAAK;IAClD,IAAMC,eAAe,GAAGhB,KAAK;IAC7B,IAAMiB,WAAW,GAAG,OAAO;IAE3B,IAAMC,OAAO,GAAGH,KAAK,CAACN,IAAI,CAAC,UAAAU,IAAI,EAAI;MACjC,OAAOF,WAAW,IAAIE,IAAI,IAAIH,eAAe,CAACL,QAAQ,CAACQ,IAAI,CAAC;IAC9D,CAAC,CAAC;IAEF,IAAI,CAACD,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;IACA,OAAO,IAAI;EACb,CAAC,MAAM;IACLN,OAAO,CAACC,KAAK,oDAAkD,CAAC;IAChE,OAAO,KAAK;EACd;AACF"}]}