{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\vatinvoice\\index.vue", "mtime": 1754882319639}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vatInvoice", "require", "_VatinvoiceDialog", "_interopRequireDefault", "name", "components", "VatinvoiceDialog", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "vatinvoiceList", "title", "open", "queryParams", "pageNum", "pageSize", "invoiceCodeNo", "invoiceOfficalNo", "saleBuy", "invoiceBelongsTo", "richBankCode", "richCompanyTitle", "richVatSerialNo", "rich<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "richBankFullname", "cooperatorId", "cooperatorShortName", "cooperatorBankCode", "cooperatorFullname", "cooperatorVatSerialNo", "cooperator<PERSON><PERSON>k<PERSON><PERSON>unt", "cooperatorBankFullname", "rctNoSummary", "cooperatorReferNo", "officalChargeNameSummary", "chargeCurrencyCode", "dnSum", "dnRecieved", "dnBalance", "cnSum", "cnPaid", "cnBalance", "chargeClearStatus", "expectedPayDate", "approvedPayDate", "actualPayDate", "sqdBankStatementList", "invoiceCurrencyCode", "invoiceExchangeRate", "invoiceNetAmount", "vatAmount", "invoiceVatAmount", "saleNetSum", "saleTax", "saleTaxTotal", "buyNetSum", "buyTax", "buyTaxTotal", "taxClass", "invoiceType", "belongsToMonth", "invoiceStatus", "invoiceRemark", "applyStuffId", "appliedTime", "issuedStuffId", "issuedTime", "taxStuffId", "taxDeclareTime", "form", "rules", "required", "message", "trigger", "invoiceItemList", "companyList", "bankAccountList", "watch", "n", "created", "getList", "methods", "handleRowClick", "row", "handleUpdate", "_this", "listVatinvoice", "then", "response", "rows", "cancel", "reset", "invoiceId", "createTime", "updateTime", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this2", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "catch", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "_this3", "getVatinvoice", "extCompanyList", "basAccountList", "submitForm", "handleDialogSubmit", "formData", "_this4", "updateVatinvoice", "addVatinvoice", "handleDelete", "_this5", "invoiceIds", "delVatinvoice", "handleExportTheInvoicingInformation", "_this6", "msgError", "generateInvoiceExcel", "fileName", "concat", "Date", "getTime", "blob", "Blob", "type", "link", "document", "createElement", "url", "window", "URL", "createObjectURL", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "error", "console", "handleExport", "_objectSpread2", "default", "exports", "_default"], "sources": ["src/views/system/vatinvoice/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"发票流水号\" prop=\"invoiceCodeNo\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceCodeNo\"\r\n                clearable\r\n                placeholder=\"发票流水号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票号码\" prop=\"invoiceOfficalNo\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceOfficalNo\"\r\n                clearable\r\n                placeholder=\"发票号码\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属公司\" prop=\"invoiceBelongsTo\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceBelongsTo\"\r\n                clearable\r\n                placeholder=\"所属公司\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账户简称\" prop=\"richBankCode\">\r\n            <el-input\r\n                v-model=\"queryParams.richBankCode\"\r\n                clearable\r\n                placeholder=\"我司账户简称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司发票抬头\" prop=\"richCompanyTitle\">\r\n            <el-input\r\n                v-model=\"queryParams.richCompanyTitle\"\r\n                clearable\r\n                placeholder=\"我司发票抬头\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司纳税人识别号\" prop=\"richVatSerialNo\">\r\n            <el-input\r\n                v-model=\"queryParams.richVatSerialNo\"\r\n                clearable\r\n                placeholder=\"我司纳税人识别号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司账号\" prop=\"richBankAccount\">\r\n            <el-input\r\n                v-model=\"queryParams.richBankAccount\"\r\n                clearable\r\n                placeholder=\"我司账号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"我司银行全称\" prop=\"richBankFullname\">\r\n            <el-input\r\n                v-model=\"queryParams.richBankFullname\"\r\n                clearable\r\n                placeholder=\"我司银行全称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司ID\" prop=\"cooperatorId\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorId\"\r\n                clearable\r\n                placeholder=\"对方公司ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方公司简称\" prop=\"cooperatorShortName\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorShortName\"\r\n                clearable\r\n                placeholder=\"对方公司简称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账户简称\" prop=\"cooperatorBankCode\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorBankCode\"\r\n                clearable\r\n                placeholder=\"对方账户简称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\">\r\n            <el-input\r\n              v-model=\"queryParams.cooperatorCompanyTitle\"\r\n                clearable\r\n                placeholder=\"对方发票抬头\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方纳税人识别号\" prop=\"cooperatorVatSerialNo\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorVatSerialNo\"\r\n                clearable\r\n                placeholder=\"对方纳税人识别号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方账号\" prop=\"cooperatorBankAccount\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorBankAccount\"\r\n                clearable\r\n                placeholder=\"对方账号\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"对方银行全称\" prop=\"cooperatorBankFullname\">\r\n            <el-input\r\n                v-model=\"queryParams.cooperatorBankFullname\"\r\n                clearable\r\n                placeholder=\"对方银行全称\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"结算币种\" prop=\"chargeCurrencyCode\">\r\n            <el-input\r\n                v-model=\"queryParams.chargeCurrencyCode\"\r\n                clearable\r\n                placeholder=\"结算币种\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应收总额\" prop=\"dnSum\">\r\n            <el-input\r\n                v-model=\"queryParams.dnSum\"\r\n                clearable\r\n                placeholder=\"账单应收总额\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已收\" prop=\"dnRecieved\">\r\n            <el-input\r\n                v-model=\"queryParams.dnRecieved\"\r\n                clearable\r\n                placeholder=\"银行已收\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未收\" prop=\"dnBalance\">\r\n            <el-input\r\n                v-model=\"queryParams.dnBalance\"\r\n                clearable\r\n                placeholder=\"银行未收\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账单应付总额\" prop=\"cnSum\">\r\n            <el-input\r\n                v-model=\"queryParams.cnSum\"\r\n                clearable\r\n                placeholder=\"账单应付总额\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行已付\" prop=\"cnPaid\">\r\n            <el-input\r\n                v-model=\"queryParams.cnPaid\"\r\n                clearable\r\n                placeholder=\"银行已付\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"银行未付\" prop=\"cnBalance\">\r\n            <el-input\r\n                v-model=\"queryParams.cnBalance\"\r\n                clearable\r\n                placeholder=\"银行未付\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"期望支付日期\" prop=\"expectedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.expectedPayDate\"\r\n                            clearable\r\n                            placeholder=\"期望支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"批复支付日期\" prop=\"approvedPayDate\">\r\n            <el-date-picker v-model=\"queryParams.approvedPayDate\"\r\n                            clearable\r\n                            placeholder=\"批复支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"实际支付日期\" prop=\"actualPayDate\">\r\n            <el-date-picker v-model=\"queryParams.actualPayDate\"\r\n                            clearable\r\n                            placeholder=\"实际支付日期\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"发票币种\" prop=\"invoiceCurrencyCode\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceCurrencyCode\"\r\n                clearable\r\n                placeholder=\"发票币种\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票汇率\" prop=\"invoiceExchangeRate\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceExchangeRate\"\r\n                clearable\r\n                placeholder=\"发票汇率\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"不含税金额\" prop=\"invoiceNetAmount\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceNetAmount\"\r\n                clearable\r\n                placeholder=\"不含税金额\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"税金\" prop=\"vatAmount\">\r\n            <el-input\r\n                v-model=\"queryParams.vatAmount\"\r\n                clearable\r\n                placeholder=\"税金\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"价税合计\" prop=\"invoiceVatAmount\">\r\n            <el-input\r\n                v-model=\"queryParams.invoiceVatAmount\"\r\n                clearable\r\n                placeholder=\"价税合计\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项不含税\" prop=\"saleNetSum\">\r\n            <el-input\r\n                v-model=\"queryParams.saleNetSum\"\r\n                clearable\r\n                placeholder=\"销项不含税\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项税金\" prop=\"saleTax\">\r\n            <el-input\r\n                v-model=\"queryParams.saleTax\"\r\n                clearable\r\n                placeholder=\"销项税金\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销项含税合计\" prop=\"saleTaxTotal\">\r\n            <el-input\r\n                v-model=\"queryParams.saleTaxTotal\"\r\n                clearable\r\n                placeholder=\"销项含税合计\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项不含税\" prop=\"buyNetSum\">\r\n            <el-input\r\n                v-model=\"queryParams.buyNetSum\"\r\n                clearable\r\n                placeholder=\"进项不含税\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项税金\" prop=\"buyTax\">\r\n            <el-input\r\n                v-model=\"queryParams.buyTax\"\r\n                clearable\r\n                placeholder=\"进项税金\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进项含税合计\" prop=\"buyTaxTotal\">\r\n            <el-input\r\n                v-model=\"queryParams.buyTaxTotal\"\r\n                clearable\r\n                placeholder=\"进项含税合计\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"发票性质\" prop=\"taxClass\">\r\n            <el-input\r\n                v-model=\"queryParams.taxClass\"\r\n                clearable\r\n                placeholder=\"发票性质\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税所属月份\" prop=\"belongsToMonth\">\r\n            <el-input\r\n                v-model=\"queryParams.belongsToMonth\"\r\n                clearable\r\n                placeholder=\"报税所属月份\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请人ID\" prop=\"applyStuffId\">\r\n            <el-input\r\n                v-model=\"queryParams.applyStuffId\"\r\n                clearable\r\n                placeholder=\"申请人ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"申请时间\" prop=\"appliedTime\">\r\n            <el-date-picker v-model=\"queryParams.appliedTime\"\r\n                            clearable\r\n                            placeholder=\"申请时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"开票人ID\" prop=\"issuedStuffId\">\r\n            <el-input\r\n                v-model=\"queryParams.issuedStuffId\"\r\n                clearable\r\n                placeholder=\"开票人ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"开票时间\" prop=\"issuedTime\">\r\n            <el-date-picker v-model=\"queryParams.issuedTime\"\r\n                            clearable\r\n                            placeholder=\"开票时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"报税人ID\" prop=\"taxStuffId\">\r\n            <el-input\r\n                v-model=\"queryParams.taxStuffId\"\r\n                clearable\r\n                placeholder=\"报税人ID\"\r\n                @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"报税时间\" prop=\"taxDeclareTime\">\r\n            <el-date-picker v-model=\"queryParams.taxDeclareTime\"\r\n                            clearable\r\n                            placeholder=\"报税时间\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:add']\"\r\n                icon=\"el-icon-plus\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:edit']\"\r\n                :disabled=\"single\"\r\n                icon=\"el-icon-edit\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:remove']\"\r\n                :disabled=\"multiple\"\r\n                icon=\"el-icon-delete\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n                v-hasPermi=\"['system:vatinvoice:export']\"\r\n                icon=\"el-icon-download\"\r\n                plain\r\n                size=\"mini\"\r\n                type=\"warning\"\r\n                @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:vatinvoice:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExportTheInvoicingInformation\"\r\n            >导出开票资料\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"vatinvoiceList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"handleRowClick\">\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"发票流水号\" prop=\"invoiceCodeNo\" width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"发票号码\" prop=\"invoiceOfficalNo\"/>\r\n          <el-table-column align=\"center\" label=\"进销标志\" prop=\"saleBuy\"/>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"invoiceBelongsTo\"/>\r\n          <el-table-column align=\"center\" label=\"我司账户简称\" prop=\"richBankCode\"/>\r\n          <el-table-column align=\"center\" label=\"我司发票抬头\" prop=\"richCompanyTitle\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"我司纳税人识别号\" prop=\"richVatSerialNo\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"我司账号\" prop=\"richBankAccount\" show-overflow-tooltip width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"我司银行全称\" prop=\"richBankFullname\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"对方公司简称\" prop=\"cooperatorShortName\"/>\r\n          <el-table-column align=\"center\" label=\"对方账户简称\" prop=\"cooperatorBankCode\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"对方发票抬头\" prop=\"cooperatorCompanyTitle\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"对方纳税人识别号\" prop=\"cooperatorVatSerialNo\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"对方账号\" prop=\"cooperatorBankAccount\" show-overflow-tooltip\r\n                           width=\"120\"/>\r\n          <el-table-column align=\"center\" label=\"对方银行全称\" prop=\"cooperatorBankFullname\"/>\r\n          <el-table-column align=\"center\" label=\"所属订单汇总\" prop=\"rctNoSummary\"/>\r\n          <el-table-column align=\"center\" label=\"对方单号汇总\" prop=\"cooperatorReferNo\"/>\r\n          <el-table-column align=\"center\" label=\"发票项目汇总\" prop=\"officalChargeNameSummary\"/>\r\n          <el-table-column align=\"center\" label=\"结算币种\" prop=\"chargeCurrencyCode\"/>\r\n          <el-table-column align=\"center\" label=\"账单应收总额\" prop=\"dnSum\"/>\r\n          <el-table-column align=\"center\" label=\"银行已收\" prop=\"dnRecieved\"/>\r\n          <el-table-column align=\"center\" label=\"银行未收\" prop=\"dnBalance\"/>\r\n          <el-table-column align=\"center\" label=\"账单应付总额\" prop=\"cnSum\"/>\r\n          <el-table-column align=\"center\" label=\"银行已付\" prop=\"cnPaid\"/>\r\n          <el-table-column align=\"center\" label=\"银行未付\" prop=\"cnBalance\"/>\r\n          <el-table-column align=\"center\" label=\"销账状态\" prop=\"chargeClearStatus\"/>\r\n          <el-table-column align=\"center\" label=\"期望支付日期\" prop=\"expectedPayDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.expectedPayDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"批复支付日期\" prop=\"approvedPayDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.approvedPayDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实际支付日期\" prop=\"actualPayDate\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.actualPayDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"对应银行流水\" prop=\"sqdBankStatementList\"/>\r\n          <el-table-column align=\"center\" label=\"发票币种\" prop=\"invoiceCurrencyCode\"/>\r\n          <el-table-column align=\"center\" label=\"发票汇率\" prop=\"invoiceExchangeRate\"/>\r\n          <el-table-column align=\"center\" label=\"不含税金额\" prop=\"invoiceNetAmount\"/>\r\n          <el-table-column align=\"center\" label=\"税金\" prop=\"vatAmount\"/>\r\n          <el-table-column align=\"center\" label=\"价税合计\" prop=\"invoiceVatAmount\"/>\r\n          <el-table-column align=\"center\" label=\"销项不含税\" prop=\"saleNetSum\"/>\r\n          <el-table-column align=\"center\" label=\"销项税金\" prop=\"saleTax\"/>\r\n          <el-table-column align=\"center\" label=\"销项含税合计\" prop=\"saleTaxTotal\"/>\r\n          <el-table-column align=\"center\" label=\"进项不含税\" prop=\"buyNetSum\"/>\r\n          <el-table-column align=\"center\" label=\"进项税金\" prop=\"buyTax\"/>\r\n          <el-table-column align=\"center\" label=\"进项含税合计\" prop=\"buyTaxTotal\"/>\r\n          <el-table-column align=\"center\" label=\"发票性质\" prop=\"taxClass\"/>\r\n          <el-table-column align=\"center\" label=\"发票类型\" prop=\"invoiceType\"/>\r\n          <el-table-column align=\"center\" label=\"报税所属月份\" prop=\"belongsToMonth\"/>\r\n          <el-table-column align=\"center\" label=\"发票状态\" prop=\"invoiceStatus\"/>\r\n          <el-table-column align=\"center\" label=\"备注\" prop=\"invoiceRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"申请人ID\" prop=\"applyStuffId\"/>\r\n          <el-table-column align=\"center\" label=\"申请时间\" prop=\"appliedTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.appliedTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"开票人ID\" prop=\"issuedStuffId\"/>\r\n          <el-table-column align=\"center\" label=\"开票时间\" prop=\"issuedTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.issuedTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"报税人ID\" prop=\"taxStuffId\"/>\r\n          <el-table-column align=\"center\" label=\"报税时间\" prop=\"taxDeclareTime\" width=\"180\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.taxDeclareTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                  v-hasPermi=\"['system:vatinvoice:edit']\"\r\n                  icon=\"el-icon-edit\"\r\n                  size=\"mini\"\r\n                  style=\"margin-right: -8px\"\r\n                  type=\"success\"\r\n                  @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                  v-hasPermi=\"['system:vatinvoice:remove']\"\r\n                  icon=\"el-icon-delete\"\r\n                  size=\"mini\"\r\n                  style=\"margin-right: -8px\"\r\n                  type=\"danger\"\r\n                  @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n            v-show=\"total>0\"\r\n            :limit.sync=\"queryParams.pageSize\"\r\n            :page.sync=\"queryParams.pageNum\"\r\n            :total=\"total\"\r\n            @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改发票登记对话框 -->\r\n    <vatinvoice-dialog\r\n      :form=\"form\"\r\n      :invoice-items=\"invoiceItemList\"\r\n      :bank-account-list=\"bankAccountList\"\r\n      :company-list=\"companyList\"\r\n      :rules=\"rules\"\r\n      :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      @cancel=\"cancel\"\r\n      @submit=\"handleDialogSubmit\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addVatinvoice,\r\n  changeStatus,\r\n  delVatinvoice,\r\n  getVatinvoice,\r\n  listVatinvoice,\r\n  updateVatinvoice,\r\n  generateInvoiceExcel\r\n} from \"@/api/system/vatInvoice\"\r\nimport VatinvoiceDialog from './components/VatinvoiceDialog'\r\n\r\nexport default {\r\n  name: \"Vatinvoice\",\r\n  components: {\r\n    VatinvoiceDialog\r\n  },\r\n  data() {\r\n    return {\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 发票登记表格数据\r\n      vatinvoiceList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: null,\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: null,\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        invoiceCodeNo: [\r\n          {\r\n            required: true,\r\n            message: \"发票流水号不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        saleBuy: [\r\n          {\r\n            required: true,\r\n            message: \"进销标志：sale=销项，buy=进项不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      },\r\n      invoiceItemList: [],\r\n      companyList: [],\r\n      bankAccountList: [],\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    handleRowClick(row) {\r\n      this.handleUpdate(row)\r\n    },\r\n    /** 查询发票登记列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listVatinvoice(this.queryParams).then(response => {\r\n        this.vatinvoiceList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        invoiceId: null,\r\n        invoiceCodeNo: null,\r\n        invoiceOfficalNo: null,\r\n        saleBuy: null,\r\n        invoiceBelongsTo: null,\r\n        richBankCode: null,\r\n        richCompanyTitle: null,\r\n        richVatSerialNo: null,\r\n        richBankAccount: null,\r\n        richBankFullname: null,\r\n        cooperatorId: null,\r\n        cooperatorShortName: null,\r\n        cooperatorBankCode: null,\r\n        cooperatorFullname: null,\r\n        cooperatorVatSerialNo: null,\r\n        cooperatorBankAccount: null,\r\n        cooperatorBankFullname: null,\r\n        rctNoSummary: null,\r\n        cooperatorReferNo: null,\r\n        officalChargeNameSummary: null,\r\n        chargeCurrencyCode: null,\r\n        dnSum: null,\r\n        dnRecieved: null,\r\n        dnBalance: null,\r\n        cnSum: null,\r\n        cnPaid: null,\r\n        cnBalance: null,\r\n        chargeClearStatus: \"0\",\r\n        expectedPayDate: null,\r\n        approvedPayDate: null,\r\n        actualPayDate: null,\r\n        sqdBankStatementList: null,\r\n        invoiceCurrencyCode: null,\r\n        invoiceExchangeRate: null,\r\n        invoiceNetAmount: null,\r\n        vatAmount: null,\r\n        invoiceVatAmount: null,\r\n        saleNetSum: null,\r\n        saleTax: null,\r\n        saleTaxTotal: null,\r\n        buyNetSum: null,\r\n        buyTax: null,\r\n        buyTaxTotal: null,\r\n        taxClass: null,\r\n        invoiceType: null,\r\n        belongsToMonth: null,\r\n        invoiceStatus: \"0\",\r\n        invoiceRemark: null,\r\n        applyStuffId: null,\r\n        appliedTime: null,\r\n        issuedStuffId: null,\r\n        issuedTime: null,\r\n        taxStuffId: null,\r\n        taxDeclareTime: null,\r\n        createTime: null,\r\n        updateTime: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.invoiceId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.invoiceId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加发票登记\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const invoiceId = row.invoiceId || this.ids\r\n      getVatinvoice(invoiceId).then(response => {\r\n        this.form = response.data\r\n        this.companyList = response.extCompanyList\r\n        this.bankAccountList = response.basAccountList\r\n        this.open = true\r\n        this.title = \"修改发票登记\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      // 已移至handleDialogSubmit方法\r\n    },\r\n\r\n    /** 处理对话框提交 */\r\n    handleDialogSubmit(formData) {\r\n      if (formData.invoiceId != null) {\r\n        updateVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        addVatinvoice(formData).then(response => {\r\n          this.$modal.msgSuccess(\"新增成功\")\r\n          this.open = false\r\n          this.getList()\r\n        })\r\n      }\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const invoiceIds = row.invoiceId || this.ids\r\n      this.$modal.confirm(\"是否确认删除发票登记编号为\\\"\" + invoiceIds + \"\\\"的数据项？\").then(function () {\r\n        return delVatinvoice(invoiceIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出开票资料按钮操作 */\r\n    handleExportTheInvoicingInformation() {\r\n      // 检查是否有选中的发票\r\n      if (this.ids.length === 0) {\r\n        this.$modal.msgError(\"请先选择要导出的发票\")\r\n        return\r\n      }\r\n\r\n      generateInvoiceExcel(this.ids)\r\n        .then(response => {\r\n          // 获取文件的字节数组 (ArrayBuffer)\r\n          const data = response\r\n\r\n          // 生成文件名\r\n          let fileName = `Invoices_${new Date().getTime()}.xlsx`\r\n\r\n          // 创建一个 Blob 对象来存储文件\r\n          const blob = new Blob([data], {\r\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"  // Excel 文件类型\r\n          })\r\n\r\n          // 创建一个临时链接，模拟点击来下载文件\r\n          const link = document.createElement(\"a\")\r\n          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象\r\n          link.href = url\r\n          link.download = fileName  // 设置下载的文件名\r\n\r\n          // 模拟点击链接，触发下载\r\n          document.body.appendChild(link)\r\n          link.click()\r\n\r\n          // 下载完成后移除链接，并释放 URL 对象\r\n          document.body.removeChild(link)\r\n          window.URL.revokeObjectURL(url)\r\n\r\n          this.$modal.msgSuccess(\"Excel文件导出成功\")\r\n        })\r\n        .catch(error => {\r\n          console.error(\"文件下载失败:\", error)\r\n          this.$modal.msgError(\"Excel文件导出失败\")\r\n        })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/vatinvoice/export\", {\r\n        ...this.queryParams\r\n      }, `vatinvoice_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;AAujBA,IAAAA,WAAA,GAAAC,OAAA;AASA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAG,IAAA;EACAC,UAAA;IACAC,gBAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,aAAA;QACAC,gBAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,mBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,wBAAA;QACAC,kBAAA;QACAC,KAAA;QACAC,UAAA;QACAC,SAAA;QACAC,KAAA;QACAC,MAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,aAAA;QACAC,oBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,WAAA;QACAC,aAAA;QACAC,UAAA;QACAC,UAAA;QACAC,cAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAtD,aAAA,GACA;UACAuD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA,EACA;QACAvD,OAAA,GACA;UACAqD,QAAA;UACAC,OAAA;UACAC,OAAA;QACA;MAEA;MACAC,eAAA;MACAC,WAAA;MACAC,eAAA;IACA;EACA;EACAC,KAAA;IACArE,UAAA,WAAAA,WAAAsE,CAAA;MACA,IAAAA,CAAA;QACA,KAAA3E,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACA6E,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,GAAA;MACA,KAAAC,YAAA,CAAAD,GAAA;IACA;IACA,eACAH,OAAA,WAAAA,QAAA;MAAA,IAAAK,KAAA;MACA,KAAAjF,OAAA;MACA,IAAAkF,0BAAA,OAAAzE,WAAA,EAAA0E,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA3E,cAAA,GAAA8E,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAA5E,KAAA,GAAA+E,QAAA,CAAA/E,KAAA;QACA4E,KAAA,CAAAjF,OAAA;MACA;IACA;IACA;IACAsF,MAAA,WAAAA,OAAA;MACA,KAAA9E,IAAA;MACA,KAAA+E,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAtB,IAAA;QACAuB,SAAA;QACA5E,aAAA;QACAC,gBAAA;QACAC,OAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,mBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,qBAAA;QACAC,qBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,wBAAA;QACAC,kBAAA;QACAC,KAAA;QACAC,UAAA;QACAC,SAAA;QACAC,KAAA;QACAC,MAAA;QACAC,SAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,eAAA;QACAC,aAAA;QACAC,oBAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,OAAA;QACAC,YAAA;QACAC,SAAA;QACAC,MAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,cAAA;QACAC,aAAA;QACAC,aAAA;QACAC,YAAA;QACAC,WAAA;QACAC,aAAA;QACAC,UAAA;QACAC,UAAA;QACAC,cAAA;QACAyB,UAAA;QACAC,UAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAnF,WAAA,CAAAC,OAAA;MACA,KAAAkE,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAAf,GAAA;MAAA,IAAAgB,MAAA;MACA,IAAAC,IAAA,GAAAjB,GAAA,CAAAkB,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAAb,IAAA;QACA,WAAAiB,wBAAA,EAAArB,GAAA,CAAAS,SAAA,EAAAT,GAAA,CAAAkB,MAAA;MACA,GAAAd,IAAA;QACAY,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAAM,KAAA;QACAvB,GAAA,CAAAkB,MAAA,GAAAlB,GAAA,CAAAkB,MAAA;MACA;IACA;IACA;IACAM,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAvG,GAAA,GAAAuG,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAlB,SAAA;MAAA;MACA,KAAAtF,MAAA,GAAAsG,SAAA,CAAAG,MAAA;MACA,KAAAxG,QAAA,IAAAqG,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAArB,KAAA;MACA,KAAA/E,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAyE,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAA8B,MAAA;MACA,KAAAtB,KAAA;MACA,IAAAC,SAAA,GAAAT,GAAA,CAAAS,SAAA,SAAAvF,GAAA;MACA,IAAA6G,yBAAA,EAAAtB,SAAA,EAAAL,IAAA,WAAAC,QAAA;QACAyB,MAAA,CAAA5C,IAAA,GAAAmB,QAAA,CAAAvF,IAAA;QACAgH,MAAA,CAAAtC,WAAA,GAAAa,QAAA,CAAA2B,cAAA;QACAF,MAAA,CAAArC,eAAA,GAAAY,QAAA,CAAA4B,cAAA;QACAH,MAAA,CAAArG,IAAA;QACAqG,MAAA,CAAAtG,KAAA;MACA;IACA;IACA,WACA0G,UAAA,WAAAA,WAAA;MACA;IAAA,CACA;IAEA,cACAC,kBAAA,WAAAA,mBAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,QAAA,CAAA3B,SAAA;QACA,IAAA6B,4BAAA,EAAAF,QAAA,EAAAhC,IAAA,WAAAC,QAAA;UACAgC,MAAA,CAAAlB,MAAA,CAAAG,UAAA;UACAe,MAAA,CAAA5G,IAAA;UACA4G,MAAA,CAAAxC,OAAA;QACA;MACA;QACA,IAAA0C,yBAAA,EAAAH,QAAA,EAAAhC,IAAA,WAAAC,QAAA;UACAgC,MAAA,CAAAlB,MAAA,CAAAG,UAAA;UACAe,MAAA,CAAA5G,IAAA;UACA4G,MAAA,CAAAxC,OAAA;QACA;MACA;IACA;IACA,aACA2C,YAAA,WAAAA,aAAAxC,GAAA;MAAA,IAAAyC,MAAA;MACA,IAAAC,UAAA,GAAA1C,GAAA,CAAAS,SAAA,SAAAvF,GAAA;MACA,KAAAiG,MAAA,CAAAC,OAAA,qBAAAsB,UAAA,cAAAtC,IAAA;QACA,WAAAuC,yBAAA,EAAAD,UAAA;MACA,GAAAtC,IAAA;QACAqC,MAAA,CAAA5C,OAAA;QACA4C,MAAA,CAAAtB,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,iBACAqB,mCAAA,WAAAA,oCAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA3H,GAAA,CAAA0G,MAAA;QACA,KAAAT,MAAA,CAAA2B,QAAA;QACA;MACA;MAEA,IAAAC,gCAAA,OAAA7H,GAAA,EACAkF,IAAA,WAAAC,QAAA;QACA;QACA,IAAAvF,IAAA,GAAAuF,QAAA;;QAEA;QACA,IAAA2C,QAAA,eAAAC,MAAA,KAAAC,IAAA,GAAAC,OAAA;;QAEA;QACA,IAAAC,IAAA,OAAAC,IAAA,EAAAvI,IAAA;UACAwI,IAAA;QACA;;QAEA;QACA,IAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAT,IAAA;QACAG,IAAA,CAAAO,IAAA,GAAAJ,GAAA;QACAH,IAAA,CAAAQ,QAAA,GAAAf,QAAA;;QAEA;QACAQ,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAV,IAAA;QACAA,IAAA,CAAAW,KAAA;;QAEA;QACAV,QAAA,CAAAQ,IAAA,CAAAG,WAAA,CAAAZ,IAAA;QACAI,MAAA,CAAAC,GAAA,CAAAQ,eAAA,CAAAV,GAAA;QAEAb,MAAA,CAAA1B,MAAA,CAAAG,UAAA;MACA,GACAC,KAAA,WAAA8C,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;QACAxB,MAAA,CAAA1B,MAAA,CAAA2B,QAAA;MACA;IACA;IACA,aACAyB,YAAA,WAAAA,aAAA;MACA,KAAAR,QAAA,iCAAAS,cAAA,CAAAC,OAAA,MACA,KAAA/I,WAAA,iBAAAuH,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;EACA;AACA;AAAAuB,OAAA,CAAAD,OAAA,GAAAE,QAAA"}]}