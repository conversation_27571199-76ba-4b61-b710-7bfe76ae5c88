{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExtendServiceComponent.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\ExtendServiceComponent.vue", "mtime": 1754881964233}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgQXVkaXQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvYXVkaXQudnVlIg0KaW1wb3J0IExvZ2lzdGljc1Byb2dyZXNzIGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2xvZ2lzdGljc1Byb2dyZXNzLnZ1ZSINCmltcG9ydCBDaGFyZ2VMaXN0IGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L2NoYXJnZUxpc3QudnVlIg0KaW1wb3J0IERlYml0Tm90ZUxpc3QgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvZGViaXROb2RlTGlzdC52dWUiDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkV4dGVuZFNlcnZpY2VDb21wb25lbnQiLA0KICBjb21wb25lbnRzOiB7DQogICAgRGViaXROb3RlTGlzdCwNCiAgICBBdWRpdCwNCiAgICBMb2dpc3RpY3NQcm9ncmVzcywNCiAgICBDaGFyZ2VMaXN0DQogIH0sDQogIHByb3BzOiB7DQogICAgc2VydmljZUl0ZW06IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9LA0KICAgIC8vIOaLk+WxleacjeWKoeaVsOaNruWIl+ihqA0KICAgIGV4dGVuZFNlcnZpY2VMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICAvLyDooajljZXmlbDmja4NCiAgICBmb3JtOiB7DQogICAgICB0eXBlOiBPYmplY3QsDQogICAgICBkZWZhdWx0OiAoKSA9PiAoe30pDQogICAgfSwNCiAgICAvLyDmmL7npLrmjqfliLYNCiAgICBicmFuY2hJbmZvOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogdHJ1ZQ0KICAgIH0sDQogICAgbG9naXN0aWNzSW5mbzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IHRydWUNCiAgICB9LA0KICAgIGNoYXJnZUluZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiB0cnVlDQogICAgfSwNCiAgICBhdWRpdEluZm86IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgLy8g54q25oCB5o6n5Yi2DQogICAgZGlzYWJsZWQ6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgYm9va2luZzogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBwc2FWZXJpZnk6IHsNCiAgICAgIHR5cGU6IEJvb2xlYW4sDQogICAgICBkZWZhdWx0OiBmYWxzZQ0KICAgIH0sDQogICAgLy8g5pWw5o2u5YiX6KGoDQogICAgc3VwcGxpZXJMaXN0OiB7DQogICAgICB0eXBlOiBBcnJheSwNCiAgICAgIGRlZmF1bHQ6ICgpID0+IFtdDQogICAgfSwNCiAgICBjb21wYW55TGlzdDogew0KICAgICAgdHlwZTogQXJyYXksDQogICAgICBkZWZhdWx0OiAoKSA9PiBbXQ0KICAgIH0sDQogICAgLy8g5paw5aKe5bGe5oCn77yM5LiN5YaN5L6d6LWWJHBhcmVudA0KICAgIGZvbGRTdGF0ZTogew0KICAgICAgdHlwZTogQm9vbGVhbiwNCiAgICAgIGRlZmF1bHQ6IGZhbHNlDQogICAgfSwNCiAgICBzZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9LA0KICAgIHNlcnZpY2VPYmplY3Q6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6ICgpID0+ICh7fSkNCiAgICB9LA0KICAgIGZvcm1EaXNhYmxlOiB7DQogICAgICB0eXBlOiBCb29sZWFuLA0KICAgICAgZGVmYXVsdDogZmFsc2UNCiAgICB9LA0KICAgIHBheWFibGU6IHsNCiAgICAgIHR5cGU6IE9iamVjdCwNCiAgICAgIGRlZmF1bHQ6IG51bGwNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgLy8g5Yik5pat5piv5ZCm56aB55So54q25oCBDQogICAgaXNEaXNhYmxlZCgpIHsNCiAgICAgIHJldHVybiB0aGlzLmRpc2FibGVkIHx8IHRoaXMucHNhVmVyaWZ5DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlQWRkRGViaXROb3RlKHNlcnZpY2VPYmplY3QpIHsNCiAgICAgIGxldCByb3cgPSB7fQ0KICAgICAgcm93LnNxZFJjdE5vID0gdGhpcy5mb3JtLnJjdE5vDQogICAgICByb3cucmN0SWQgPSB0aGlzLmZvcm0ucmN0SWQNCiAgICAgIHJvdy5pc1JlY2lldmluZ09yUGF5aW5nID0gMQ0KICAgICAgcm93LnJzQ2hhcmdlTGlzdCA9IFtdDQogICAgICB0aGlzLiRlbWl0KCdhZGREZWJpdE5vdGUnLCByb3csIHNlcnZpY2VPYmplY3QpDQogICAgfSwNCiAgICBjaGFuZ2VGb2xkKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIGNvbnNvbGUubG9nKHNlcnZpY2VUeXBlSWQpDQogICAgICB0aGlzLiRlbWl0KCJjaGFuZ2VGb2xkIiwgc2VydmljZVR5cGVJZCkNCiAgICB9LA0KICAgIGNoYW5nZVNlcnZpY2VPYmplY3Qoc2VydmljZVR5cGVJZCwgc2VydmljZU9iamVjdCkgew0KICAgICAgdGhpcy4kZW1pdCgiY2hhbmdlU2VydmljZU9iamVjdCIsIHNlcnZpY2VUeXBlSWQsIHNlcnZpY2VPYmplY3QpDQogICAgfSwNCiAgICAvLyDnianmtYHov5vluqbnm7jlhbPmlrnms5UNCiAgICBkZWxldGVMb2dJdGVtKHNlcnZpY2VUeXBlSWQsIGV2ZW50KSB7DQogICAgICBjb25zdCBzZXJ2aWNlT2JqZWN0ID0gdGhpcy5nZXRTZXJ2aWNlT2JqZWN0KHNlcnZpY2VUeXBlSWQpDQogICAgICBpZiAoc2VydmljZU9iamVjdCAmJiBzZXJ2aWNlT2JqZWN0LnJzT3BMb2dMaXN0KSB7DQogICAgICAgIHNlcnZpY2VPYmplY3QucnNPcExvZ0xpc3QgPSBzZXJ2aWNlT2JqZWN0LnJzT3BMb2dMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0gIT09IGV2ZW50KQ0KICAgICAgfQ0KICAgIH0sDQogICAgdXBkYXRlTG9nTGlzdChzZXJ2aWNlVHlwZUlkLCBldmVudCkgew0KICAgICAgY29uc3Qgc2VydmljZU9iamVjdCA9IHRoaXMuZ2V0U2VydmljZU9iamVjdChzZXJ2aWNlVHlwZUlkKQ0KICAgICAgaWYgKHNlcnZpY2VPYmplY3QpIHsNCiAgICAgICAgc2VydmljZU9iamVjdC5yc09wTG9nTGlzdCA9IGV2ZW50DQogICAgICB9DQogICAgfSwNCiAgICBnZXRGb2xkKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLmZvbGRTdGF0ZQ0KICAgIH0sDQogICAgZ2V0U2VydmljZUluc3RhbmNlKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlcnZpY2VJbnN0YW5jZQ0KICAgIH0sDQogICAgZ2V0U2VydmljZU9iamVjdChzZXJ2aWNlVHlwZUlkKSB7DQogICAgICByZXR1cm4gdGhpcy5zZXJ2aWNlT2JqZWN0DQogICAgfSwNCiAgICBnZXRQYXlhYmxlKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLnBheWFibGUNCiAgICB9LA0KICAgIGdldEZvcm1EaXNhYmxlKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIHJldHVybiB0aGlzLmZvcm1EaXNhYmxlDQogICAgfSwNCiAgICAvLyDojrflj5bkvpvlupTllYbpgq7nrrENCiAgICBnZXRTdXBwbGllckVtYWlsKHN1cHBsaWVySWQpIHsNCiAgICAgIGNvbnN0IHN1cHBsaWVyID0gdGhpcy5zdXBwbGllckxpc3QuZmluZCh2ID0+IHYuY29tcGFueUlkID09PSBzdXBwbGllcklkKQ0KICAgICAgcmV0dXJuIHN1cHBsaWVyID8gc3VwcGxpZXIuc3RhZmZFbWFpbCA6ICcnDQogICAgfSwNCiAgICAvLyDojrflj5blkIjnuqbmmL7npLrmlofmnKwNCiAgICBnZXRBZ3JlZW1lbnREaXNwbGF5KHNlcnZpY2VJbnN0YW5jZSkgew0KICAgICAgcmV0dXJuIChzZXJ2aWNlSW5zdGFuY2UuYWdyZWVtZW50VHlwZUNvZGUgfHwgJycpICsgKHNlcnZpY2VJbnN0YW5jZS5hZ3JlZW1lbnRObyB8fCAnJykNCiAgICB9LA0KICAgIC8vIOS6i+S7tui9rOWPkee7meeItue7hOS7tg0KICAgIGNoYW5nZVNlcnZpY2VGb2xkKHNlcnZpY2VEYXRhKSB7DQogICAgICBzZXJ2aWNlRGF0YS5zZXJ2aWNlRm9sZCA9ICFzZXJ2aWNlRGF0YS5zZXJ2aWNlRm9sZA0KICAgICAgdGhpcy4kZW1pdCgiY2hhbmdlU2VydmljZUZvbGQiLCBzZXJ2aWNlRGF0YSkNCiAgICB9LA0KICAgIGF1ZGl0Q2hhcmdlKHNlcnZpY2VEYXRhLCBldmVudCkgew0KICAgICAgdGhpcy4kZW1pdCgiYXVkaXRDaGFyZ2UiLCBzZXJ2aWNlRGF0YSwgZXZlbnQpDQogICAgfSwNCiAgICBnZW5lcmF0ZUZyZWlnaHQodHlwZTEsIHR5cGUyLCBzZXJ2aWNlRGF0YSkgew0KICAgICAgdGhpcy4kZW1pdCgiZ2VuZXJhdGVGcmVpZ2h0IiwgdHlwZTEsIHR5cGUyLCBzZXJ2aWNlRGF0YSkNCiAgICB9LA0KICAgIHBzYUJvb2tpbmdDYW5jZWwoKSB7DQogICAgICB0aGlzLiRlbWl0KCJwc2FCb29raW5nQ2FuY2VsIikNCiAgICB9LA0KICAgIGNvcHlGcmVpZ2h0KGV2ZW50KSB7DQogICAgICB0aGlzLiRlbWl0KCJjb3B5RnJlaWdodCIsIGV2ZW50KQ0KICAgIH0sDQogICAgY2FsY3VsYXRlQ2hhcmdlKHNlcnZpY2VUeXBlSWQsIGV2ZW50LCBzZXJ2aWNlRGF0YSkgew0KICAgICAgdGhpcy4kZW1pdCgiY2FsY3VsYXRlQ2hhcmdlIiwgc2VydmljZVR5cGVJZCwgZXZlbnQsIHNlcnZpY2VEYXRhKQ0KICAgIH0sDQogICAgLy8g6LS555So5YiX6KGo55u45YWz5pa55rOVDQogICAgZGVsZXRlQWxsQ2hhcmdlKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIGNvbnN0IHNlcnZpY2VPYmplY3QgPSB0aGlzLmdldFNlcnZpY2VPYmplY3Qoc2VydmljZVR5cGVJZCkNCiAgICAgIGlmIChzZXJ2aWNlT2JqZWN0KSB7DQogICAgICAgIHNlcnZpY2VPYmplY3QucnNDaGFyZ2VMaXN0ID0gW10NCiAgICAgIH0NCiAgICB9LA0KICAgIGRlbGV0ZUNoYXJnZUl0ZW0oc2VydmljZVR5cGVJZCwgZXZlbnQpIHsNCiAgICAgIGNvbnN0IHNlcnZpY2VPYmplY3QgPSB0aGlzLmdldFNlcnZpY2VPYmplY3Qoc2VydmljZVR5cGVJZCkNCiAgICAgIGlmIChzZXJ2aWNlT2JqZWN0ICYmIHNlcnZpY2VPYmplY3QucnNDaGFyZ2VMaXN0KSB7DQogICAgICAgIHNlcnZpY2VPYmplY3QucnNDaGFyZ2VMaXN0ID0gc2VydmljZU9iamVjdC5yc0NoYXJnZUxpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPT0gZXZlbnQpDQogICAgICB9DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["ExtendServiceComponent.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmKA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "ExtendServiceComponent.vue", "sourceRoot": "src/views/system/document/serviceComponents", "sourcesContent": ["<template>\r\n  <div class=\"extend-service-component\">\r\n    <!--拓展服务-->\r\n    <div class=\"extend-service-item\">\r\n      <!--标题栏-->\r\n      <el-row>\r\n        <el-col :span=\"18\">\r\n          <div class=\"service-bar\">\r\n            <a :class=\"[\r\n                'service-toggle-icon',\r\n                getFold(serviceItem.serviceTypeId) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'\r\n              ]\"\r\n               @click=\"changeFold(serviceItem.serviceTypeId)\"\r\n            />\r\n            <div class=\"service-title-group\">\r\n              <h3 class=\"service-title\" @click=\"changeFold(serviceItem.serviceTypeId)\">\r\n                拓展服务-{{ serviceItem.serviceShortName }}\r\n              </h3>\r\n            </div>\r\n            <!--审核信息-->\r\n            <audit\r\n              v-if=\"auditInfo\"\r\n              :audit=\"true\"\r\n              :basic-info=\"getServiceInstance(serviceItem.serviceTypeId)\"\r\n              :disabled=\"disabled\"\r\n              :payable=\"getPayable(serviceItem.serviceTypeId)\"\r\n              :rs-charge-list=\"serviceItem.rsChargeList\"\r\n              @auditFee=\"auditCharge(serviceItem, $event)\"\r\n              @return=\"changeServiceObject(serviceItem.serviceTypeId, $event)\"\r\n            />\r\n          </div>\r\n        </el-col>\r\n      </el-row>\r\n      <!--内容区域-->\r\n      <transition name=\"fade\">\r\n        <el-row\r\n          v-if=\"getFold(serviceItem.serviceTypeId)\"\r\n          :gutter=\"10\"\r\n          class=\"service-content-area\"\r\n        >\r\n          <!--服务信息栏-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"3\" class=\"service-info-col\">\r\n              <el-form-item label=\"询价单号\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNo\"\r\n                  :class=\"{ 'disable-form': disabled }\"\r\n                  :disabled=\"disabled\"\r\n                  placeholder=\"询价单号\"\r\n                  @focus=\"generateFreight(9, serviceItem.serviceTypeId, serviceItem)\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item v-if=\"!booking && branchInfo\" label=\"供应商\">\r\n                <el-popover\r\n                  :content=\"getSupplierEmail(getServiceInstance(serviceItem.serviceTypeId).supplierId)\"\r\n                  placement=\"bottom\"\r\n                  trigger=\"hover\"\r\n                  width=\"200\"\r\n                >\r\n                  <el-input\r\n                    slot=\"reference\"\r\n                    :value=\"getServiceInstance(serviceItem.serviceTypeId).supplierName\"\r\n                    class=\"disable-form\"\r\n                    disabled\r\n                  />\r\n                </el-popover>\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"合约类型\">\r\n                <el-input\r\n                  :value=\"getAgreementDisplay(getServiceInstance(serviceItem.serviceTypeId))\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"合约类型\"\r\n                />\r\n              </el-form-item>\r\n\r\n              <el-form-item label=\"业务须知\">\r\n                <el-input\r\n                  v-model=\"getServiceInstance(serviceItem.serviceTypeId).inquiryNotice\"\r\n                  class=\"disable-form\"\r\n                  disabled\r\n                  placeholder=\"业务须知\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--分支信息-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"branchInfo\" :span=\"12\">\r\n              <el-form-item label=\"商务单号\" prop=\"supplierId\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input :value=\"form.sqdPsaNo\" class=\"disable-form\"/>\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-button\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"psaBookingCancel\"\r\n                    >\r\n                      取消\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </transition>\r\n          <!--物流进度-->\r\n          <transition name=\"fade\">\r\n            <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n              <logistics-progress\r\n                :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled || psaVerify\"\r\n                :logistics-progress-data=\"getServiceObject(serviceItem.serviceTypeId).rsOpLogList\"\r\n                :open-logistics-progress-list=\"true\"\r\n                :process-type=\"4\"\r\n                :service-type=\"101\"\r\n                @deleteItem=\"deleteLogItem(serviceItem.serviceTypeId, $event)\"\r\n                @return=\"updateLogList(serviceItem.serviceTypeId, $event)\"\r\n              />\r\n            </el-col>\r\n          </transition>\r\n          <!--费用列表-->\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.3\">\r\n            <!--<charge-list\r\n              :a-t-d=\"form.podEta\"\r\n              :charge-data=\"getServiceObject(serviceItem.serviceTypeId).rsChargeList\"\r\n              :company-list=\"companyList\"\r\n              :disabled=\"getFormDisable(serviceItem.serviceTypeId) || disabled\"\r\n              :hidden-supplier=\"booking\"\r\n              :is-receivable=\"false\"\r\n              :open-charge-list=\"true\"\r\n              :pay-detail-r-m-b=\"getServiceObject(serviceItem.serviceTypeId).payableRMB\"\r\n              :pay-detail-r-m-b-tax=\"getServiceObject(serviceItem.serviceTypeId).payableRMBTax\"\r\n              :pay-detail-u-s-d=\"getServiceObject(serviceItem.serviceTypeId).payableUSD\"\r\n              :pay-detail-u-s-d-tax=\"getServiceObject(serviceItem.serviceTypeId).payableUSDTax\"\r\n              :service-type-id=\"101\"\r\n              @copyFreight=\"copyFreight($event)\"\r\n              @deleteAll=\"deleteAllCharge(serviceItem.serviceTypeId)\"\r\n              @deleteItem=\"deleteChargeItem(serviceItem.serviceTypeId, $event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId, $event, getServiceObject(serviceItem.serviceTypeId))\"\r\n            />-->\r\n            <debit-note-list\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"getServiceObject(serviceItem.serviceTypeId).rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :is-receivable=\"0\"\r\n              :rct-id=\"form.rctId\"\r\n              @addDebitNote=\"handleAddDebitNote(getServiceObject(serviceItem.serviceTypeId))\"\r\n              @deleteItem=\"getServiceObject(serviceItem.serviceTypeId).rsDebitNoteList = getServiceObject(serviceItem.serviceTypeId).rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @return=\"calculateCharge(serviceItem.serviceTypeId,$event,getServiceObject(serviceItem.serviceTypeId))\"\r\n            />\r\n          </el-col>\r\n        </el-row>\r\n      </transition>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport Audit from \"@/views/system/document/audit.vue\"\r\nimport LogisticsProgress from \"@/views/system/document/logisticsProgress.vue\"\r\nimport ChargeList from \"@/views/system/document/chargeList.vue\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"ExtendServiceComponent\",\r\n  components: {\r\n    DebitNoteList,\r\n    Audit,\r\n    LogisticsProgress,\r\n    ChargeList\r\n  },\r\n  props: {\r\n    serviceItem: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 拓展服务数据列表\r\n    extendServiceList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 表单数据\r\n    form: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    // 显示控制\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 状态控制\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    booking: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    // 数据列表\r\n    supplierList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    // 新增属性，不再依赖$parent\r\n    foldState: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    serviceInstance: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    serviceObject: {\r\n      type: Object,\r\n      default: () => ({})\r\n    },\r\n    formDisable: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    payable: {\r\n      type: Object,\r\n      default: null\r\n    }\r\n  },\r\n  computed: {\r\n    // 判断是否禁用状态\r\n    isDisabled() {\r\n      return this.disabled || this.psaVerify\r\n    }\r\n  },\r\n  methods: {\r\n    handleAddDebitNote(serviceObject) {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 1\r\n      row.rsChargeList = []\r\n      this.$emit('addDebitNote', row, serviceObject)\r\n    },\r\n    changeFold(serviceTypeId) {\r\n      console.log(serviceTypeId)\r\n      this.$emit(\"changeFold\", serviceTypeId)\r\n    },\r\n    changeServiceObject(serviceTypeId, serviceObject) {\r\n      this.$emit(\"changeServiceObject\", serviceTypeId, serviceObject)\r\n    },\r\n    // 物流进度相关方法\r\n    deleteLogItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsOpLogList) {\r\n        serviceObject.rsOpLogList = serviceObject.rsOpLogList.filter(item => item !== event)\r\n      }\r\n    },\r\n    updateLogList(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsOpLogList = event\r\n      }\r\n    },\r\n    getFold(serviceTypeId) {\r\n      return this.foldState\r\n    },\r\n    getServiceInstance(serviceTypeId) {\r\n      return this.serviceInstance\r\n    },\r\n    getServiceObject(serviceTypeId) {\r\n      return this.serviceObject\r\n    },\r\n    getPayable(serviceTypeId) {\r\n      return this.payable\r\n    },\r\n    getFormDisable(serviceTypeId) {\r\n      return this.formDisable\r\n    },\r\n    // 获取供应商邮箱\r\n    getSupplierEmail(supplierId) {\r\n      const supplier = this.supplierList.find(v => v.companyId === supplierId)\r\n      return supplier ? supplier.staffEmail : ''\r\n    },\r\n    // 获取合约显示文本\r\n    getAgreementDisplay(serviceInstance) {\r\n      return (serviceInstance.agreementTypeCode || '') + (serviceInstance.agreementNo || '')\r\n    },\r\n    // 事件转发给父组件\r\n    changeServiceFold(serviceData) {\r\n      serviceData.serviceFold = !serviceData.serviceFold\r\n      this.$emit(\"changeServiceFold\", serviceData)\r\n    },\r\n    auditCharge(serviceData, event) {\r\n      this.$emit(\"auditCharge\", serviceData, event)\r\n    },\r\n    generateFreight(type1, type2, serviceData) {\r\n      this.$emit(\"generateFreight\", type1, type2, serviceData)\r\n    },\r\n    psaBookingCancel() {\r\n      this.$emit(\"psaBookingCancel\")\r\n    },\r\n    copyFreight(event) {\r\n      this.$emit(\"copyFreight\", event)\r\n    },\r\n    calculateCharge(serviceTypeId, event, serviceData) {\r\n      this.$emit(\"calculateCharge\", serviceTypeId, event, serviceData)\r\n    },\r\n    // 费用列表相关方法\r\n    deleteAllCharge(serviceTypeId) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject) {\r\n        serviceObject.rsChargeList = []\r\n      }\r\n    },\r\n    deleteChargeItem(serviceTypeId, event) {\r\n      const serviceObject = this.getServiceObject(serviceTypeId)\r\n      if (serviceObject && serviceObject.rsChargeList) {\r\n        serviceObject.rsChargeList = serviceObject.rsChargeList.filter(item => item !== event)\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document';\r\n\r\n// ExtendService组件特定样式\r\n.extend-service-component {\r\n  width: 100%;\r\n\r\n  .extend-service-item {\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .service-bar {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 10px;\r\n    width: 100%;\r\n\r\n    .service-toggle-icon {\r\n      cursor: pointer;\r\n      margin-right: 5px;\r\n    }\r\n\r\n    .service-title-group {\r\n      display: flex;\r\n      align-items: center;\r\n      width: 250px;\r\n\r\n      .service-title {\r\n        margin: 0;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n  }\r\n\r\n  .service-content-area {\r\n    margin-bottom: 15px;\r\n    display: -webkit-box;\r\n\r\n    .service-info-col {\r\n      .el-form-item {\r\n        margin-bottom: 10px;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 优化表单输入框样式\r\n  .el-input {\r\n    width: 100%;\r\n  }\r\n}\r\n</style>\r\n"]}]}