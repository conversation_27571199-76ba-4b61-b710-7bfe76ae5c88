{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Navbar.vue?vue&type=style&index=0&id=d16d6306&lang=scss&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Navbar.vue", "mtime": 1754876882541}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Navbar.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Navbar.vue", "sourceRoot": "src/layout/components", "sourcesContent": ["<template>\r\n  <div class=\"navbar\">\r\n    <hamburger id=\"hamburger-container\" :is-active=\"sidebar.opened\" class=\"hamburger-container\"\r\n               @toggleClick=\"toggleSideBar\"/>\r\n\r\n    <breadcrumb v-if=\"!topNav\" id=\"breadcrumb-container\" class=\"breadcrumb-container\"/>\r\n    <top-nav v-if=\"topNav\" id=\"topmenu-container\" class=\"topmenu-container\"/>\r\n\r\n    <div class=\"right-menu\">\r\n      <template v-if=\"device!='mobile'\">\r\n        <message id=\"message\" class=\"right-menu-item hover-effect\"/>\r\n\r\n        <search id=\"header-search\" class=\"right-menu-item\"/>\r\n\r\n        <screenfull id=\"screenfull\" class=\"right-menu-item hover-effect\"/>\r\n\r\n        <el-tooltip content=\"布局大小\" effect=\"dark\" placement=\"bottom\">\r\n          <size-select id=\"size-select\" class=\"right-menu-item hover-effect\"/>\r\n        </el-tooltip>\r\n\r\n        <div class=\"right-menu-item\">\r\n          <h6 style=\"margin: 5px auto auto;display: table\">{{ $store.state.user.name.split(' ')[0] }}</h6>\r\n          <h6 style=\"margin: auto;display: table\">{{ $store.state.user.name.split(' ')[1] }}</h6>\r\n          <h6 style=\"margin: auto;display: table\">{{ $store.state.user.name.split(' ')[2] }}</h6>\r\n        </div>\r\n\r\n      </template>\r\n\r\n      <el-dropdown class=\"avatar-container right-menu-item hover-effect\" trigger=\"click\">\r\n        <div class=\"avatar-wrapper\">\r\n          <img :src=\"avatar\" class=\"user-avatar\">\r\n          <i class=\"el-icon-caret-bottom\"/>\r\n        </div>\r\n        <el-dropdown-menu slot=\"dropdown\">\r\n          <router-link to=\"/user/profile\">\r\n            <el-dropdown-item>个人中心</el-dropdown-item>\r\n          </router-link>\r\n          <el-dropdown-item @click.native=\"setting = true\">\r\n            <span>布局设置</span>\r\n          </el-dropdown-item>\r\n          <el-dropdown-item divided @click.native=\"logout\">\r\n            <span>退出登录</span>\r\n          </el-dropdown-item>\r\n        </el-dropdown-menu>\r\n      </el-dropdown>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {mapGetters} from 'vuex'\r\nimport Breadcrumb from '@/components/Breadcrumb'\r\nimport TopNav from '@/components/TopNav'\r\nimport Hamburger from '@/components/Hamburger'\r\nimport Screenfull from '@/components/Screenfull'\r\nimport SizeSelect from '@/components/SizeSelect'\r\nimport Search from '@/components/HeaderSearch'\r\nimport Message from \"@/components/Message\";\r\n\r\nexport default {\r\n  components: {\r\n    Breadcrumb,\r\n    TopNav,\r\n    Hamburger,\r\n    Screenfull,\r\n    SizeSelect,\r\n    Search,\r\n    Message,\r\n  },\r\n  computed: {\r\n    ...mapGetters([\r\n      'sidebar',\r\n      'avatar',\r\n      'device'\r\n    ]),\r\n    setting: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'showSettings',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    toggleSideBar() {\r\n      this.$store.dispatch('app/toggleSideBar')\r\n    },\r\n    async logout() {\r\n      this.$confirm('确定注销并退出系统吗？', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => {\r\n        this.$store.dispatch('LogOut').then(() => {\r\n          location.href = '/index';\r\n        })\r\n      }).catch(() => {\r\n      });\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.navbar {\r\n  height: 50px;\r\n  overflow: hidden;\r\n  position: relative;\r\n  background: #fff;\r\n  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);\r\n\r\n  .hamburger-container {\r\n    line-height: 46px;\r\n    height: 100%;\r\n    float: left;\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n    -webkit-tap-highlight-color: transparent;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .025)\r\n    }\r\n  }\r\n\r\n  .breadcrumb-container {\r\n    float: left;\r\n  }\r\n\r\n  .topmenu-container {\r\n    position: absolute;\r\n    left: 50px;\r\n  }\r\n\r\n  .errLog-container {\r\n    display: inline-block;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .right-menu {\r\n    float: right;\r\n    height: 100%;\r\n    line-height: 50px;\r\n\r\n    &:focus {\r\n      outline: none;\r\n    }\r\n\r\n    .right-menu-item {\r\n      display: inline-block;\r\n      padding: 0 8px;\r\n      height: 100%;\r\n      font-size: 18px;\r\n      color: #5a5e66;\r\n      vertical-align: text-bottom;\r\n\r\n      &.hover-effect {\r\n        cursor: pointer;\r\n        transition: background .3s;\r\n\r\n        &:hover {\r\n          background: rgba(0, 0, 0, .025)\r\n        }\r\n      }\r\n    }\r\n\r\n    .avatar-container {\r\n      margin-right: 30px;\r\n\r\n      .avatar-wrapper {\r\n        margin-top: 5px;\r\n        position: relative;\r\n\r\n        .user-avatar {\r\n          cursor: pointer;\r\n          width: 40px;\r\n          height: 40px;\r\n          border-radius: 10px;\r\n        }\r\n\r\n        .el-icon-caret-bottom {\r\n          cursor: pointer;\r\n          position: absolute;\r\n          right: -20px;\r\n          top: 25px;\r\n          font-size: 12px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}