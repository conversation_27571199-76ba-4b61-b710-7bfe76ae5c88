{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\second.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\second.vue", "mtime": 1754876882527}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["second.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "second.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n  <el-form size=\"mini\">\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"1\">\r\n        秒，允许的通配符[, - * /]\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"2\">\r\n        周期从\r\n        <el-input-number v-model='cycle01' :max=\"58\" :min=\"0\"/>\r\n        -\r\n        <el-input-number v-model='cycle02' :max=\"59\" :min=\"cycle01 ? cycle01 + 1 : 1\"/>\r\n        秒\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"3\">\r\n        从\r\n        <el-input-number v-model='average01' :max=\"58\" :min=\"0\"/>\r\n        秒开始，每\r\n        <el-input-number v-model='average02' :max=\"59 - average01 || 0\" :min=\"1\"/>\r\n        秒执行一次\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"4\">\r\n        指定\r\n        <el-select v-model=\"checkboxList\" clearable multiple placeholder=\"可多选\" style=\"width:100%\">\r\n          <el-option v-for=\"item in 60\" :key=\"item\" :value=\"item-1\">{{ item - 1 }}</el-option>\r\n        </el-select>\r\n      </el-radio>\r\n    </el-form-item>\r\n  </el-form>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      radioValue: 1,\r\n      cycle01: 1,\r\n      cycle02: 2,\r\n      average01: 0,\r\n      average02: 1,\r\n      checkboxList: [],\r\n      checkNum: this.$options.propsData.check\r\n    }\r\n  },\r\n  name: 'crontab-second',\r\n  props: ['check', 'radioParent'],\r\n  methods: {\r\n    // 单选按钮值变化时\r\n    radioChange() {\r\n      switch (this.radioValue) {\r\n        case 1:\r\n          this.$emit('update', 'second', '*', 'second');\r\n          break;\r\n        case 2:\r\n          this.$emit('update', 'second', this.cycleTotal);\r\n          break;\r\n        case 3:\r\n          this.$emit('update', 'second', this.averageTotal);\r\n          break;\r\n        case 4:\r\n          this.$emit('update', 'second', this.checkboxString);\r\n          break;\r\n      }\r\n    },\r\n    // 周期两个值变化时\r\n    cycleChange() {\r\n      if (this.radioValue == '2') {\r\n        this.$emit('update', 'second', this.cycleTotal);\r\n      }\r\n    },\r\n    // 平均两个值变化时\r\n    averageChange() {\r\n      if (this.radioValue == '3') {\r\n        this.$emit('update', 'second', this.averageTotal);\r\n      }\r\n    },\r\n    // checkbox值变化时\r\n    checkboxChange() {\r\n      if (this.radioValue == '4') {\r\n        this.$emit('update', 'second', this.checkboxString);\r\n      }\r\n    }\r\n  },\r\n  watch: {\r\n    'radioValue': 'radioChange',\r\n    'cycleTotal': 'cycleChange',\r\n    'averageTotal': 'averageChange',\r\n    'checkboxString': 'checkboxChange',\r\n    radioParent() {\r\n      this.radioValue = this.radioParent\r\n    }\r\n  },\r\n  computed: {\r\n    // 计算两个周期值\r\n    cycleTotal: function () {\r\n      const cycle01 = this.checkNum(this.cycle01, 0, 58)\r\n      const cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 59)\r\n      return cycle01 + '-' + cycle02;\r\n    },\r\n    // 计算平均用到的值\r\n    averageTotal: function () {\r\n      const average01 = this.checkNum(this.average01, 0, 58)\r\n      const average02 = this.checkNum(this.average02, 1, 59 - average01 || 0)\r\n      return average01 + '/' + average02;\r\n    },\r\n    // 计算勾选的checkbox值合集\r\n    checkboxString: function () {\r\n      let str = this.checkboxList.join();\r\n      return str == '' ? '*' : str;\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}