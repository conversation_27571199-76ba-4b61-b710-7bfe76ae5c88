{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\booking.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\booking.vue", "mtime": 1754876882583}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQppbXBvcnQgZG9jdW1lbnQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQiOw0KDQovLyDku47miqXku7fliJfooajkuK3ngrnlh7vorqLoiLHkvJrot6/nlLHliLDov5nph4wNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogImJvb2tpbmciLA0KICBjb21wb25lbnRzOiB7ZG9jdW1lbnR9LA0KfQ0K"}, {"version": 3, "sources": ["booking.vue"], "names": [], "mappings": ";;;;;AAKA;;AAEA;AACA;AACA;AACA;AACA", "file": "booking.vue", "sourceRoot": "src/views/system/document", "sourcesContent": ["<template>\r\n  <document :type=\"'booking'\"/>\r\n</template>\r\n\r\n<script>\r\nimport document from \"@/views/system/document\";\r\n\r\n// 从报价列表中点击订舱会路由到这里\r\nexport default {\r\n  name: \"booking\",\r\n  components: {document},\r\n}\r\n</script>\r\n"]}]}