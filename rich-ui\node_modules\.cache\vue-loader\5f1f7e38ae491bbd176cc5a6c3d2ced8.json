{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\DraggableItem.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\DraggableItem.vue", "mtime": 1754876882602}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["DraggableItem.vue"], "names": [], "mappings": ";AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "DraggableItem.vue", "sourceRoot": "src/views/tool/build", "sourcesContent": ["<script>\r\nimport draggable from 'vuedraggable'\r\nimport render from '@/utils/generator/render'\r\n\r\nconst components = {\r\n  itemBtns(h, element, index, parent) {\r\n    const {copyItem, deleteItem} = this.$listeners\r\n    return [\r\n      <span class=\"drawing-item-copy\" title=\"复制\" onClick={event => {\r\n        copyItem(element, parent);\r\n        event.stopPropagation()\r\n      }}>\r\n        <i class=\"el-icon-copy-document\"/>\r\n      </span>,\r\n      <span class=\"drawing-item-delete\" title=\"删除\" onClick={event => {\r\n        deleteItem(index, parent);\r\n        event.stopPropagation()\r\n      }}>\r\n        <i class=\"el-icon-delete\"/>\r\n      </span>\r\n    ]\r\n  }\r\n}\r\nconst layouts = {\r\n  colFormItem(h, element, index, parent) {\r\n    const {activeItem} = this.$listeners\r\n    let className = this.activeId == element.formId ? 'drawing-item active-from-item' : 'drawing-item'\r\n    if (this.formConf.unFocusedComponentBorder) className += ' unfocus-bordered'\r\n    return (\r\n      <el-col span={element.span} class={className}\r\n              nativeOnClick={event => {\r\n                activeItem(element);\r\n                event.stopPropagation()\r\n              }}>\r\n        <el-form-item label-width={element.labelWidth ? `${element.labelWidth}px` : null}\r\n                      label={element.label} required={element.required}>\r\n          <render key={element.renderKey} conf={element} onInput={event => {\r\n            this.$set(element, 'defaultValue', event)\r\n          }}/>\r\n        </el-form-item>\r\n        {components.itemBtns.apply(this, arguments)}\r\n      </el-col>\r\n    )\r\n  },\r\n  rowFormItem(h, element, index, parent) {\r\n    const {activeItem} = this.$listeners\r\n    const className = this.activeId == element.formId ? 'drawing-row-item active-from-item' : 'drawing-row-item'\r\n    let child = renderChildren.apply(this, arguments)\r\n    if (element.type == 'flex') {\r\n      child = <el-row type={element.type} justify={element.justify} align={element.align}>\r\n        {child}\r\n      </el-row>\r\n    }\r\n    return (\r\n      <el-col span={element.span}>\r\n        <el-row gutter={element.gutter} class={className}\r\n                nativeOnClick={event => {\r\n                  activeItem(element);\r\n                  event.stopPropagation()\r\n                }}>\r\n          <span class=\"component-name\">{element.componentName}</span>\r\n          <draggable list={element.children} animation={340} group=\"componentsGroup\" class=\"drag-wrapper\">\r\n            {child}\r\n          </draggable>\r\n          {components.itemBtns.apply(this, arguments)}\r\n        </el-row>\r\n      </el-col>\r\n    )\r\n  }\r\n}\r\n\r\nfunction renderChildren(h, element, index, parent) {\r\n  if (!Array.isArray(element.children)) return null\r\n  return element.children.map((el, i) => {\r\n    const layout = layouts[el.layout]\r\n    if (layout) {\r\n      return layout.call(this, h, el, i, element.children)\r\n    }\r\n    return layoutIsNotFound()\r\n  })\r\n}\r\n\r\nfunction layoutIsNotFound() {\r\n  throw new Error(`没有与${this.element.layout}匹配的layout`)\r\n}\r\n\r\nexport default {\r\n  components: {\r\n    render,\r\n    draggable\r\n  },\r\n  props: [\r\n    'element',\r\n    'index',\r\n    'drawingList',\r\n    'activeId',\r\n    'formConf'\r\n  ],\r\n  render(h) {\r\n    const layout = layouts[this.element.layout]\r\n\r\n    if (layout) {\r\n      return layout.call(this, h, this.element, this.index, this.drawingList)\r\n    }\r\n    return layoutIsNotFound()\r\n  }\r\n}\r\n</script>\r\n"]}]}