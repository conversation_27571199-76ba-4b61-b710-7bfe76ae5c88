{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\cargotype\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\cargotype\\index.vue", "mtime": 1754876882575}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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<PERSON>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"}, {"version": 3, "names": ["_cargoType", "require", "name", "dicts", "data", "showLeft", "showRight", "defaultProps", "children", "label", "refreshTable", "isExpandAll", "loading", "ids", "single", "multiple", "showSearch", "typeList", "title", "open", "queryParams", "cargoTypeQuery", "form", "rules", "watch", "n", "created", "getList", "methods", "_this", "listCargoType", "then", "response", "handleTree", "cancel", "reset", "cargoTypeId", "parentId", "cargoTypeShortName", "cargoTypeLocalName", "cargoTypeEnName", "cargoTypeLevel", "isLocked", "verticalSort", "orderNum", "status", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "pageNum", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "row", "handleUpdate", "_this2", "getCargoType", "submitForm", "_this3", "$refs", "validate", "valid", "updateCargoType", "$modal", "msgSuccess", "addCargoType", "handleLockedChange", "_this4", "text", "$confirm", "customClass", "changeLocked", "catch", "handleStatusChange", "_this5", "changeStatus", "handleDelete", "_this6", "cargoTypeIds", "delCargoType", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "getParentId", "val", "exports", "_default"], "sources": ["src/views/system/cargotype/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"cargoTypeQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.cargoTypeQuery\"\r\n              clearable\r\n              placeholder=\"中英文/简称\"\r\n              style=\"width: 158px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:cargotype:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:cargotype:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:cargotype:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"typeList\"\r\n                  :default-expand-all=\"isExpandAll\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n                  row-key=\"cargoTypeId\">\r\n          <el-table-column align=\"left\" label=\"货物特征名称\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.cargoTypeShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.cargoTypeLocalName }}</a>\r\n              {{ scope.row.cargoTypeEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"等级\" prop=\"cargoTypeLevel\" width=\"48\"/>\r\n          <el-table-column align=\"center\" label=\"上下排序\" prop=\"verticalSort\" width=\"68\"/>\r\n          <el-table-column align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"48\"/>\r\n          <el-table-column align=\"center\" label=\"是否上锁\" prop=\"isLocked\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.isLocked\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleLockedChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:cargotype:add']\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:cargotype:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.parentId != 0\"\r\n                v-hasPermi=\"['system:cargotype:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改货物特征对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item v-if=\"form.parentId != 0\" label=\"上级\" prop=\"cargoTypeId\">\r\n          <tree-select :multiple=\"false\" :pass=\"form.parentId\" :placeholder=\"'上级类名'\"\r\n                       :type=\"'cargoType'\" @return=\"getParentId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"简称\" prop=\"cargoTypeShortName\">\r\n          <el-input v-model=\"form.cargoTypeShortName\" placeholder=\"货物特征名缩写\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"cargoTypeLocalName\">\r\n          <el-input v-model=\"form.cargoTypeLocalName\" placeholder=\"货物特征中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"cargoTypeEnName\">\r\n          <el-input v-model=\"form.cargoTypeEnName\" placeholder=\"货物特征英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"等级\" prop=\"cargoTypeLevel\">\r\n          <el-input v-model=\"form.cargoTypeLevel\" placeholder=\"货物特征等级\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否上锁\" prop=\"isLocked\">\r\n          <el-select v-model=\"form.isLocked\" placeholder=\"是否上锁\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dict.type.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"纵向优先级\" prop=\"verticalSort\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.verticalSort\" min=\"0\" placeholder=\"纵向优先级\"\r\n                           style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"横向优先级\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" min=\"0\" placeholder=\"横向优先级\"\r\n                           style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCargoType,\r\n  changeLocked,\r\n  changeStatus,\r\n  delCargoType,\r\n  getCargoType,\r\n  listCargoType,\r\n  updateCargoType\r\n} from \"@/api/system/cargoType\";\r\n\r\nexport default {\r\n  name: \"CargoType\",\r\n  dicts: ['sys_yes_no'],\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 货物特征表格数据\r\n      typeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        cargoTypeQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询货物特征列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCargoType(this.queryParams).then(response => {\r\n        this.typeList = this.handleTree(response.data, \"cargoTypeId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        cargoTypeId: null,\r\n        parentId: null,\r\n        cargoTypeShortName: null,\r\n        cargoTypeLocalName: null,\r\n        cargoTypeEnName: null,\r\n        cargoTypeLevel: null,\r\n        isLocked: null,\r\n        verticalSort: null,\r\n        orderNum: null,\r\n        status: 0,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: 0\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.cargoTypeId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加货物特征\";\r\n      if (row) {\r\n        this.form.parentId = row.cargoTypeId\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const cargoTypeId = row.cargoTypeId || this.ids\r\n      getCargoType(cargoTypeId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改货物特征\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.cargoTypeId != null) {\r\n            updateCargoType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCargoType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    // 状态修改\r\n    handleLockedChange(row) {\r\n      let text = row.isLocked == \"0\" ? \"上锁\" : \"解锁\";\r\n      this.$confirm('确认要' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeLocked(row.cargoTypeId, row.isLocked);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.isLocked = row.isLocked == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要修改状态吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.cargoTypeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const cargoTypeIds = row.cargoTypeId || this.ids;\r\n      this.$confirm('是否确认删除货物特征编号为\"' + cargoTypeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCargoType(cargoTypeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/type/export', {\r\n        ...this.queryParams\r\n      }, `type_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getParentId(val) {\r\n      this.form.parentId = val\r\n    },\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;;;;AAuLA,IAAAA,UAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAUA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACAC,YAAA;QACAC,QAAA;QACAC,KAAA;MACA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,cAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAR,UAAA,WAAAA,WAAAS,CAAA;MACA,IAAAA,CAAA;QACA,KAAAnB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAqB,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAAjB,OAAA;MACA,IAAAkB,wBAAA,OAAAV,WAAA,EAAAW,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAZ,QAAA,GAAAY,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAA5B,IAAA;QACAyB,KAAA,CAAAjB,OAAA;MACA;IACA;IACA;IACAsB,MAAA,WAAAA,OAAA;MACA,KAAAf,IAAA;MACA,KAAAgB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAb,IAAA;QACAc,WAAA;QACAC,QAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,eAAA;QACAC,cAAA;QACAC,QAAA;QACAC,YAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAnC,WAAA,CAAAoC,OAAA;MACA,KAAA7B,OAAA;IACA;IACA,aACA8B,UAAA,WAAAA,WAAA;MACA,KAAAH,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAG,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA9C,GAAA,GAAA8C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAzB,WAAA;MAAA;MACA,KAAAtB,MAAA,GAAA6C,SAAA,CAAAG,MAAA;MACA,KAAA/C,QAAA,IAAA4C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAA7B,KAAA;MACA,KAAAhB,IAAA;MACA,KAAAD,KAAA;MACA,IAAA8C,GAAA;QACA,KAAA1C,IAAA,CAAAe,QAAA,GAAA2B,GAAA,CAAA5B,WAAA;MACA;IACA;IACA,aACA6B,YAAA,WAAAA,aAAAD,GAAA;MAAA,IAAAE,MAAA;MACA,KAAA/B,KAAA;MACA,IAAAC,WAAA,GAAA4B,GAAA,CAAA5B,WAAA,SAAAvB,GAAA;MACA,IAAAsD,uBAAA,EAAA/B,WAAA,EAAAL,IAAA,WAAAC,QAAA;QACAkC,MAAA,CAAA5C,IAAA,GAAAU,QAAA,CAAA5B,IAAA;QACA8D,MAAA,CAAA/C,IAAA;QACA+C,MAAA,CAAAhD,KAAA;MACA;IACA;IACA,WACAkD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA/C,IAAA,CAAAc,WAAA;YACA,IAAAqC,0BAAA,EAAAJ,MAAA,CAAA/C,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACAqC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAA1C,OAAA;YACA;UACA;YACA,IAAAiD,uBAAA,EAAAP,MAAA,CAAA/C,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACAqC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAA1C,OAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAkD,kBAAA,WAAAA,mBAAAb,GAAA;MAAA,IAAAc,MAAA;MACA,IAAAC,IAAA,GAAAf,GAAA,CAAAtB,QAAA;MACA,KAAAsC,QAAA,SAAAD,IAAA;QAAAE,WAAA;MAAA,GAAAlD,IAAA;QACA,WAAAmD,uBAAA,EAAAlB,GAAA,CAAA5B,WAAA,EAAA4B,GAAA,CAAAtB,QAAA;MACA,GAAAX,IAAA;QACA+C,MAAA,CAAAJ,MAAA,CAAAC,UAAA,CAAAI,IAAA;MACA,GAAAI,KAAA;QACAnB,GAAA,CAAAtB,QAAA,GAAAsB,GAAA,CAAAtB,QAAA;MACA;IACA;IACA;IACA0C,kBAAA,WAAAA,mBAAApB,GAAA;MAAA,IAAAqB,MAAA;MACA,IAAAN,IAAA,GAAAf,GAAA,CAAAnB,MAAA;MACA,KAAAmC,QAAA;QAAAC,WAAA;MAAA,GAAAlD,IAAA;QACA,WAAAuD,uBAAA,EAAAtB,GAAA,CAAA5B,WAAA,EAAA4B,GAAA,CAAAnB,MAAA;MACA,GAAAd,IAAA;QACAsD,MAAA,CAAAX,MAAA,CAAAC,UAAA,CAAAI,IAAA;MACA,GAAAI,KAAA;QACAnB,GAAA,CAAAnB,MAAA,GAAAmB,GAAA,CAAAnB,MAAA;MACA;IACA;IACA,aACA0C,YAAA,WAAAA,aAAAvB,GAAA;MAAA,IAAAwB,MAAA;MACA,IAAAC,YAAA,GAAAzB,GAAA,CAAA5B,WAAA,SAAAvB,GAAA;MACA,KAAAmE,QAAA,oBAAAS,YAAA;QAAAR,WAAA;MAAA,GAAAlD,IAAA;QACA,WAAA2D,uBAAA,EAAAD,YAAA;MACA,GAAA1D,IAAA;QACAyD,MAAA,CAAA7D,OAAA;QACA6D,MAAA,CAAAd,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAQ,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,2BAAAC,cAAA,CAAAC,OAAA,MACA,KAAA1E,WAAA,WAAA2E,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAA7E,IAAA,CAAAe,QAAA,GAAA8D,GAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAN,OAAA,GAAAO,QAAA"}]}