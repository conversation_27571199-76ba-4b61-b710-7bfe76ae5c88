{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\cargotype\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\cargotype\\index.vue", "mtime": 1754876882575}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRDYXJnb1R5cGUsDQogIGNoYW5nZUxvY2tlZCwNCiAgY2hhbmdlU3RhdHVzLA0KICBkZWxDYXJnb1R5cGUsDQogIGdldENhcmdvVHlwZSwNCiAgbGlzdENhcmdvVHlwZSwNCiAgdXBkYXRlQ2FyZ29UeXBlDQp9IGZyb20gIkAvYXBpL3N5c3RlbS9jYXJnb1R5cGUiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJDYXJnb1R5cGUiLA0KICBkaWN0czogWydzeXNfeWVzX25vJ10sDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNob3dMZWZ0OiAzLA0KICAgICAgc2hvd1JpZ2h0OiAyMSwNCiAgICAgIGRlZmF1bHRQcm9wczogew0KICAgICAgICBjaGlsZHJlbjogImNoaWxkcmVuIiwNCiAgICAgICAgbGFiZWw6ICJsYWJlbCINCiAgICAgIH0sDQogICAgICAvLyDph43mlrDmuLLmn5PooajmoLznirbmgIENCiAgICAgIHJlZnJlc2hUYWJsZTogdHJ1ZSwNCiAgICAgIC8vIOaYr+WQpuWxleW8gO+8jOm7mOiupOWFqOmDqOWxleW8gA0KICAgICAgaXNFeHBhbmRBbGw6IGZhbHNlLA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g6LSn54mp54m55b6B6KGo5qC85pWw5o2uDQogICAgICB0eXBlTGlzdDogW10sDQogICAgICAvLyDlvLnlh7rlsYLmoIfpopgNCiAgICAgIHRpdGxlOiAiIiwNCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxgg0KICAgICAgb3BlbjogZmFsc2UsDQogICAgICAvLyDmn6Xor6Llj4LmlbANCiAgICAgIHF1ZXJ5UGFyYW1zOiB7DQogICAgICAgIGNhcmdvVHlwZVF1ZXJ5OiBudWxsLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fQ0KICAgIH07DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd1NlYXJjaChuKSB7DQogICAgICBpZiAobiA9PSB0cnVlKSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjENCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjQNCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDANCiAgICAgIH0NCiAgICB9LA0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivoui0p+eJqeeJueW+geWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdENhcmdvVHlwZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy50eXBlTGlzdCA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5kYXRhLCAiY2FyZ29UeXBlSWQiKTsNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGNhcmdvVHlwZUlkOiBudWxsLA0KICAgICAgICBwYXJlbnRJZDogbnVsbCwNCiAgICAgICAgY2FyZ29UeXBlU2hvcnROYW1lOiBudWxsLA0KICAgICAgICBjYXJnb1R5cGVMb2NhbE5hbWU6IG51bGwsDQogICAgICAgIGNhcmdvVHlwZUVuTmFtZTogbnVsbCwNCiAgICAgICAgY2FyZ29UeXBlTGV2ZWw6IG51bGwsDQogICAgICAgIGlzTG9ja2VkOiBudWxsLA0KICAgICAgICB2ZXJ0aWNhbFNvcnQ6IG51bGwsDQogICAgICAgIG9yZGVyTnVtOiBudWxsLA0KICAgICAgICBzdGF0dXM6IDAsDQogICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICBkZWxldGVCeTogbnVsbCwNCiAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgZGVsZXRlU3RhdHVzOiAwDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5jYXJnb1R5cGVJZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPSAxDQogICAgICB0aGlzLm11bHRpcGxlID0gIXNlbGVjdGlvbi5sZW5ndGgNCiAgICB9LA0KICAgIC8qKiDmlrDlop7mjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVBZGQocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICB0aGlzLm9wZW4gPSB0cnVlOw0KICAgICAgdGhpcy50aXRsZSA9ICLmt7vliqDotKfniannibnlvoEiOw0KICAgICAgaWYgKHJvdykgew0KICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSByb3cuY2FyZ29UeXBlSWQNCiAgICAgIH0NCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBjYXJnb1R5cGVJZCA9IHJvdy5jYXJnb1R5cGVJZCB8fCB0aGlzLmlkcw0KICAgICAgZ2V0Q2FyZ29UeXBlKGNhcmdvVHlwZUlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnotKfniannibnlvoEiOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uY2FyZ29UeXBlSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlQ2FyZ29UeXBlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkQ2FyZ29UeXBlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuaWsOWinuaIkOWKnyIpOw0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g54q25oCB5L+u5pS5DQogICAgaGFuZGxlTG9ja2VkQ2hhbmdlKHJvdykgew0KICAgICAgbGV0IHRleHQgPSByb3cuaXNMb2NrZWQgPT0gIjAiID8gIuS4iumUgSIgOiAi6Kej6ZSBIjsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+ehruiupOimgScgKyB0ZXh0ICsgJ+WQl++8nycsICfmj5DnpLonLCB7Y3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJ30pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gY2hhbmdlTG9ja2VkKHJvdy5jYXJnb1R5cGVJZCwgcm93LmlzTG9ja2VkKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7DQogICAgICAgIHJvdy5pc0xvY2tlZCA9IHJvdy5pc0xvY2tlZCA9PSAiMCIgPyAiMSIgOiAiMCI7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOeKtuaAgeS/ruaUuQ0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOw0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k6KaB5L+u5pS554q25oCB5ZCX77yfJywgJ+aPkOekuicsIHtjdXN0b21DbGFzczogJ21vZGFsLWNvbmZpcm0nfSkudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBjaGFuZ2VTdGF0dXMocm93LmNhcmdvVHlwZUlkLCByb3cuc3RhdHVzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7DQogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09ICIwIiA/ICIxIiA6ICIwIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGNhcmdvVHlwZUlkcyA9IHJvdy5jYXJnb1R5cGVJZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOi0p+eJqeeJueW+gee8luWPt+S4uiInICsgY2FyZ29UeXBlSWRzICsgJyLnmoTmlbDmja7pobnvvJ8nLCAn5o+Q56S6Jywge2N1c3RvbUNsYXNzOiAnbW9kYWwtY29uZmlybSd9KS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGRlbENhcmdvVHlwZShjYXJnb1R5cGVJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ3N5c3RlbS90eXBlL2V4cG9ydCcsIHsNCiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcw0KICAgICAgfSwgYHR5cGVfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICBnZXRQYXJlbnRJZCh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5wYXJlbnRJZCA9IHZhbA0KICAgIH0sDQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuLA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/cargotype", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"cargoTypeQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.cargoTypeQuery\"\r\n              clearable\r\n              placeholder=\"中英文/简称\"\r\n              style=\"width: 158px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:cargotype:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:cargotype:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:cargotype:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-if=\"refreshTable\" v-loading=\"loading\" :data=\"typeList\"\r\n                  :default-expand-all=\"isExpandAll\" :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n                  row-key=\"cargoTypeId\">\r\n          <el-table-column align=\"left\" label=\"货物特征名称\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.cargoTypeShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.cargoTypeLocalName }}</a>\r\n              {{ scope.row.cargoTypeEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column align=\"center\" label=\"等级\" prop=\"cargoTypeLevel\" width=\"48\"/>\r\n          <el-table-column align=\"center\" label=\"上下排序\" prop=\"verticalSort\" width=\"68\"/>\r\n          <el-table-column align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"48\"/>\r\n          <el-table-column align=\"center\" label=\"是否上锁\" prop=\"isLocked\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.isLocked\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleLockedChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:cargotype:add']\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:cargotype:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.parentId != 0\"\r\n                v-hasPermi=\"['system:cargotype:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改货物特征对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\" class=\"edit\">\r\n        <el-form-item v-if=\"form.parentId != 0\" label=\"上级\" prop=\"cargoTypeId\">\r\n          <tree-select :multiple=\"false\" :pass=\"form.parentId\" :placeholder=\"'上级类名'\"\r\n                       :type=\"'cargoType'\" @return=\"getParentId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"简称\" prop=\"cargoTypeShortName\">\r\n          <el-input v-model=\"form.cargoTypeShortName\" placeholder=\"货物特征名缩写\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"cargoTypeLocalName\">\r\n          <el-input v-model=\"form.cargoTypeLocalName\" placeholder=\"货物特征中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"cargoTypeEnName\">\r\n          <el-input v-model=\"form.cargoTypeEnName\" placeholder=\"货物特征英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"等级\" prop=\"cargoTypeLevel\">\r\n          <el-input v-model=\"form.cargoTypeLevel\" placeholder=\"货物特征等级\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否上锁\" prop=\"isLocked\">\r\n          <el-select v-model=\"form.isLocked\" placeholder=\"是否上锁\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dict.type.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"纵向优先级\" prop=\"verticalSort\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.verticalSort\" min=\"0\" placeholder=\"纵向优先级\"\r\n                           style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"横向优先级\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" min=\"0\" placeholder=\"横向优先级\"\r\n                           style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCargoType,\r\n  changeLocked,\r\n  changeStatus,\r\n  delCargoType,\r\n  getCargoType,\r\n  listCargoType,\r\n  updateCargoType\r\n} from \"@/api/system/cargoType\";\r\n\r\nexport default {\r\n  name: \"CargoType\",\r\n  dicts: ['sys_yes_no'],\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: false,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 货物特征表格数据\r\n      typeList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        cargoTypeQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询货物特征列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCargoType(this.queryParams).then(response => {\r\n        this.typeList = this.handleTree(response.data, \"cargoTypeId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        cargoTypeId: null,\r\n        parentId: null,\r\n        cargoTypeShortName: null,\r\n        cargoTypeLocalName: null,\r\n        cargoTypeEnName: null,\r\n        cargoTypeLevel: null,\r\n        isLocked: null,\r\n        verticalSort: null,\r\n        orderNum: null,\r\n        status: 0,\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: 0\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.cargoTypeId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加货物特征\";\r\n      if (row) {\r\n        this.form.parentId = row.cargoTypeId\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const cargoTypeId = row.cargoTypeId || this.ids\r\n      getCargoType(cargoTypeId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改货物特征\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.cargoTypeId != null) {\r\n            updateCargoType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCargoType(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    // 状态修改\r\n    handleLockedChange(row) {\r\n      let text = row.isLocked == \"0\" ? \"上锁\" : \"解锁\";\r\n      this.$confirm('确认要' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeLocked(row.cargoTypeId, row.isLocked);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.isLocked = row.isLocked == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 状态修改\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要修改状态吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.cargoTypeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const cargoTypeIds = row.cargoTypeId || this.ids;\r\n      this.$confirm('是否确认删除货物特征编号为\"' + cargoTypeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCargoType(cargoTypeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/type/export', {\r\n        ...this.queryParams\r\n      }, `type_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getParentId(val) {\r\n      this.form.parentId = val\r\n    },\r\n  }\r\n};\r\n</script>\r\n"]}]}