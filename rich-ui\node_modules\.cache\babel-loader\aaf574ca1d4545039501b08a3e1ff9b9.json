{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754876882545}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_ScrollPane", "_interopRequireDefault", "require", "_path", "_store", "components", "ScrollPane", "data", "visible", "top", "left", "selectedTag", "affixTags", "computed", "visitedViews", "$store", "state", "tagsView", "routes", "permission", "theme", "settings", "watch", "$route", "addTags", "moveToCurrentTag", "value", "document", "body", "addEventListener", "closeMenu", "removeEventListener", "mounted", "initTags", "methods", "isActive", "route", "path", "activeStyle", "tag", "isAffix", "meta", "affix", "isFirstView", "fullPath", "err", "isLastView", "length", "filterAffixTags", "_this", "basePath", "arguments", "undefined", "tags", "for<PERSON>ach", "tagPath", "resolve", "push", "name", "_objectSpread2", "default", "children", "tempTags", "concat", "_toConsumableArray2", "_iterator", "_createForOfIteratorHelper2", "_step", "s", "n", "done", "dispatch", "e", "f", "link", "_this2", "$refs", "$nextTick", "_iterator2", "_step2", "to", "scrollPane", "move<PERSON><PERSON><PERSON>arget", "refreshSelectedTag", "view", "$tab", "refreshPage", "closeSelectedTag", "_this3", "closePage", "then", "_ref", "toLastView", "closeRightTags", "_this4", "closeRightPage", "find", "i", "closeLeftTags", "_this5", "closeLeftPage", "closeOthersTags", "_this6", "$router", "catch", "closeOtherPage", "closeAllTags", "_this7", "closeAllPage", "_ref2", "some", "latestView", "slice", "replace", "openMenu", "menu<PERSON>in<PERSON>idth", "offsetLeft", "$el", "getBoundingClientRect", "offsetWidth", "maxLeft", "clientX", "clientY", "handleScroll", "exports", "_default"], "sources": ["src/layout/components/TagsView/index.vue"], "sourcesContent": ["<template>\r\n  <div id=\"tags-view-container\" class=\"tags-view-container\">\r\n    <scroll-pane ref=\"scrollPane\" class=\"tags-view-wrapper\" @scroll=\"handleScroll\">\r\n      <router-link\r\n        v-for=\"tag in visitedViews\"\r\n        :key=\"tag.path\"\r\n        ref=\"tag\"\r\n        :class=\"isActive(tag)?'active':''\"\r\n        :style=\"activeStyle(tag)\"\r\n        :to=\"{ path: tag.path, query: tag.query, fullPath: tag.fullPath }\"\r\n        class=\"tags-view-item\"\r\n        tag=\"span\"\r\n        @click.middle.native=\"!isAffix(tag)?closeSelectedTag(tag):''\"\r\n        @contextmenu.prevent.native=\"openMenu(tag,$event)\"\r\n      >\r\n        {{ tag.title }}\r\n        <span v-if=\"!isAffix(tag)\" class=\"el-icon-close\" @click.prevent.stop=\"closeSelectedTag(tag)\"/>\r\n      </router-link>\r\n    </scroll-pane>\r\n    <ul v-show=\"visible\" :style=\"{left:left+'px',top:top+'px'}\" class=\"contextmenu\">\r\n      <li @click=\"refreshSelectedTag(selectedTag)\"><i class=\"el-icon-refresh-right\"></i> 刷新页面</li>\r\n      <li v-if=\"!isAffix(selectedTag)\" @click=\"closeSelectedTag(selectedTag)\"><i class=\"el-icon-close\"></i> 关闭当前\r\n      </li>\r\n      <li @click=\"closeOthersTags\"><i class=\"el-icon-circle-close\"></i> 关闭其他</li>\r\n      <li v-if=\"!isFirstView()\" @click=\"closeLeftTags\"><i class=\"el-icon-back\"></i> 关闭左侧</li>\r\n      <li v-if=\"!isLastView()\" @click=\"closeRightTags\"><i class=\"el-icon-right\"></i> 关闭右侧</li>\r\n      <li @click=\"closeAllTags(selectedTag)\"><i class=\"el-icon-circle-close\"></i> 全部关闭</li>\r\n    </ul>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport ScrollPane from './ScrollPane'\r\nimport path from 'path'\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  components: {ScrollPane},\r\n  data() {\r\n    return {\r\n      visible: false,\r\n      top: 0,\r\n      left: 0,\r\n      selectedTag: {},\r\n      affixTags: []\r\n    }\r\n  },\r\n  computed: {\r\n    visitedViews() {\r\n      return this.$store.state.tagsView.visitedViews\r\n    },\r\n    routes() {\r\n      return this.$store.state.permission.routes\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addTags()\r\n      this.moveToCurrentTag()\r\n    },\r\n    visible(value) {\r\n      if (value) {\r\n        document.body.addEventListener('click', this.closeMenu)\r\n      } else {\r\n        document.body.removeEventListener('click', this.closeMenu)\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    this.initTags()\r\n    this.addTags()\r\n  },\r\n  methods: {\r\n    isActive(route) {\r\n      return route.path == this.$route.path\r\n    },\r\n    activeStyle(tag) {\r\n      if (!this.isActive(tag)) return {};\r\n      return {\r\n        \"background-color\": this.theme,\r\n        \"border-color\": this.theme\r\n      };\r\n    },\r\n    isAffix(tag) {\r\n      return tag.meta && tag.meta.affix\r\n    },\r\n    isFirstView() {\r\n      try {\r\n        return this.selectedTag.fullPath == '/index' || this.selectedTag.fullPath == this.visitedViews[1].fullPath\r\n      } catch (err) {\r\n        return false\r\n      }\r\n    },\r\n    isLastView() {\r\n      try {\r\n        return this.selectedTag.fullPath == this.visitedViews[this.visitedViews.length - 1].fullPath\r\n      } catch (err) {\r\n        return false\r\n      }\r\n    },\r\n    filterAffixTags(routes, basePath = '/') {\r\n      let tags = []\r\n      routes.forEach(route => {\r\n        if (route.meta && route.meta.affix) {\r\n          const tagPath = path.resolve(basePath, route.path)\r\n          tags.push({\r\n            fullPath: tagPath,\r\n            path: tagPath,\r\n            name: route.name,\r\n            meta: {...route.meta}\r\n          })\r\n        }\r\n        if (route.children) {\r\n          const tempTags = this.filterAffixTags(route.children, route.path)\r\n          if (tempTags.length >= 1) {\r\n            tags = [...tags, ...tempTags]\r\n          }\r\n        }\r\n      })\r\n      return tags\r\n    },\r\n    initTags() {\r\n      const affixTags = this.affixTags = this.filterAffixTags(this.routes)\r\n      for (const tag of affixTags) {\r\n        // Must have tag name\r\n        if (tag.name) {\r\n          this.$store.dispatch('tagsView/addVisitedView', tag)\r\n        }\r\n      }\r\n    },\r\n    addTags() {\r\n      const {name} = this.$route\r\n      if (name) {\r\n        this.$store.dispatch('tagsView/addView', this.$route)\r\n        if (this.$route.meta.link) {\r\n          this.$store.dispatch('tagsView/addIframeView', this.$route)\r\n        }\r\n      }\r\n      return false\r\n    },\r\n    moveToCurrentTag() {\r\n      const tags = this.$refs.tag\r\n      this.$nextTick(() => {\r\n        for (const tag of tags) {\r\n          if (tag.to.path == this.$route.path) {\r\n            this.$refs.scrollPane.moveToTarget(tag)\r\n            // when query is different then update\r\n            if (tag.to.fullPath != this.$route.fullPath) {\r\n              this.$store.dispatch('tagsView/updateVisitedView', this.$route)\r\n            }\r\n            break\r\n          }\r\n        }\r\n      })\r\n    },\r\n    refreshSelectedTag(view) {\r\n      this.$tab.refreshPage(view);\r\n      if (this.$route.meta.link) {\r\n        this.$store.dispatch('tagsView/delIframeView', this.$route)\r\n      }\r\n    },\r\n    closeSelectedTag(view) {\r\n      this.$tab.closePage(view).then(({visitedViews}) => {\r\n        if (this.isActive(view)) {\r\n          this.toLastView(visitedViews, view)\r\n        }\r\n      })\r\n    },\r\n    closeRightTags() {\r\n      this.$tab.closeRightPage(this.selectedTag).then(visitedViews => {\r\n        if (!visitedViews.find(i => i.fullPath == this.$route.fullPath)) {\r\n          this.toLastView(visitedViews)\r\n        }\r\n      })\r\n    },\r\n    closeLeftTags() {\r\n      this.$tab.closeLeftPage(this.selectedTag).then(visitedViews => {\r\n        if (!visitedViews.find(i => i.fullPath == this.$route.fullPath)) {\r\n          this.toLastView(visitedViews)\r\n        }\r\n      })\r\n    },\r\n    closeOthersTags() {\r\n      this.$router.push(this.selectedTag.fullPath).catch(() => {\r\n      });\r\n      this.$tab.closeOtherPage(this.selectedTag).then(() => {\r\n        this.moveToCurrentTag()\r\n      })\r\n    },\r\n    closeAllTags(view) {\r\n      this.$tab.closeAllPage().then(({visitedViews}) => {\r\n        if (this.affixTags.some(tag => tag.path == this.$route.path)) {\r\n          return\r\n        }\r\n        this.toLastView(visitedViews, view)\r\n      })\r\n    },\r\n    toLastView(visitedViews, view) {\r\n      const latestView = visitedViews.slice(-1)[0]\r\n      if (latestView) {\r\n        this.$router.push(latestView.fullPath)\r\n      } else {\r\n        // now the default is to redirect to the home page if there is no tags-view,\r\n        // you can adjust it according to your needs.\r\n        if (view.name == 'Dashboard') {\r\n          // to reload home page\r\n          this.$router.replace({path: '/redirect' + view.fullPath})\r\n        } else {\r\n          this.$router.push('/')\r\n        }\r\n      }\r\n    },\r\n    openMenu(tag, e) {\r\n      const menuMinWidth = 105\r\n      const offsetLeft = this.$el.getBoundingClientRect().left // container margin left\r\n      const offsetWidth = this.$el.offsetWidth // container width\r\n      const maxLeft = offsetWidth - menuMinWidth // left boundary\r\n      const left = e.clientX - offsetLeft + 15 // 15: margin right\r\n\r\n      if (left > maxLeft) {\r\n        this.left = maxLeft\r\n      } else {\r\n        this.left = left\r\n      }\r\n\r\n      this.top = e.clientY\r\n      this.visible = true\r\n      this.selectedTag = tag\r\n    },\r\n    closeMenu() {\r\n      this.visible = false\r\n    },\r\n    handleScroll() {\r\n      this.closeMenu()\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.tags-view-container {\r\n  height: 34px;\r\n  width: 100%;\r\n  background: #fff;\r\n  border-bottom: 1px solid #d8dce5;\r\n  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, .12), 0 0 3px 0 rgba(0, 0, 0, .04);\r\n\r\n  .tags-view-wrapper {\r\n    .tags-view-item {\r\n      display: inline-block;\r\n      position: relative;\r\n      cursor: pointer;\r\n      height: 26px;\r\n      line-height: 26px;\r\n      border: 1px solid #d8dce5;\r\n      color: #495060;\r\n      background: #fff;\r\n      padding: 0 8px;\r\n      font-size: 12px;\r\n      margin-left: 5px;\r\n      margin-top: 4px;\r\n\r\n      &:first-of-type {\r\n        margin-left: 15px;\r\n      }\r\n\r\n      &:last-of-type {\r\n        margin-right: 15px;\r\n      }\r\n\r\n      &.active {\r\n        background-color: #42b983;\r\n        color: #fff;\r\n        border-color: #42b983;\r\n\r\n        &::before {\r\n          content: '';\r\n          background: #fff;\r\n          display: inline-block;\r\n          width: 8px;\r\n          height: 8px;\r\n          border-radius: 50%;\r\n          position: relative;\r\n          margin-right: 2px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .contextmenu {\r\n    margin: 0;\r\n    background: #fff;\r\n    z-index: 3000;\r\n    position: absolute;\r\n    list-style-type: none;\r\n    padding: 5px 0;\r\n    border-radius: 4px;\r\n    font-size: 12px;\r\n    font-weight: 400;\r\n    color: #333;\r\n    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, .3);\r\n\r\n    li {\r\n      margin: 0;\r\n      padding: 7px 16px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background: #eee;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n<style lang=\"scss\">\r\n//reset element css of el-icon-close\r\n.tags-view-wrapper {\r\n  .tags-view-item {\r\n    .el-icon-close {\r\n      width: 16px;\r\n      height: 16px;\r\n      vertical-align: 2px;\r\n      border-radius: 50%;\r\n      text-align: center;\r\n      transition: all .3s cubic-bezier(.645, .045, .355, 1);\r\n      transform-origin: 100% 50%;\r\n\r\n      &:before {\r\n        transform: scale(.6);\r\n        display: inline-block;\r\n        vertical-align: -3px;\r\n      }\r\n\r\n      &:hover {\r\n        background-color: #b4bccc;\r\n        color: #fff;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAgCA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,KAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAG,UAAA;IAAAC,UAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,GAAA;MACAC,IAAA;MACAC,WAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACAC,YAAA,WAAAA,aAAA;MACA,YAAAC,MAAA,CAAAC,KAAA,CAAAC,QAAA,CAAAH,YAAA;IACA;IACAI,MAAA,WAAAA,OAAA;MACA,YAAAH,MAAA,CAAAC,KAAA,CAAAG,UAAA,CAAAD,MAAA;IACA;IACAE,KAAA,WAAAA,MAAA;MACA,YAAAL,MAAA,CAAAC,KAAA,CAAAK,QAAA,CAAAD,KAAA;IACA;EACA;EACAE,KAAA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAAC,OAAA;MACA,KAAAC,gBAAA;IACA;IACAjB,OAAA,WAAAA,QAAAkB,KAAA;MACA,IAAAA,KAAA;QACAC,QAAA,CAAAC,IAAA,CAAAC,gBAAA,eAAAC,SAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAG,mBAAA,eAAAD,SAAA;MACA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,QAAA;IACA,KAAAT,OAAA;EACA;EACAU,OAAA;IACAC,QAAA,WAAAA,SAAAC,KAAA;MACA,OAAAA,KAAA,CAAAC,IAAA,SAAAd,MAAA,CAAAc,IAAA;IACA;IACAC,WAAA,WAAAA,YAAAC,GAAA;MACA,UAAAJ,QAAA,CAAAI,GAAA;MACA;QACA,yBAAAnB,KAAA;QACA,qBAAAA;MACA;IACA;IACAoB,OAAA,WAAAA,QAAAD,GAAA;MACA,OAAAA,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAE,IAAA,CAAAC,KAAA;IACA;IACAC,WAAA,WAAAA,YAAA;MACA;QACA,YAAAhC,WAAA,CAAAiC,QAAA,qBAAAjC,WAAA,CAAAiC,QAAA,SAAA9B,YAAA,IAAA8B,QAAA;MACA,SAAAC,GAAA;QACA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACA;QACA,YAAAnC,WAAA,CAAAiC,QAAA,SAAA9B,YAAA,MAAAA,YAAA,CAAAiC,MAAA,MAAAH,QAAA;MACA,SAAAC,GAAA;QACA;MACA;IACA;IACAG,eAAA,WAAAA,gBAAA9B,MAAA;MAAA,IAAA+B,KAAA;MAAA,IAAAC,QAAA,GAAAC,SAAA,CAAAJ,MAAA,QAAAI,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAE,IAAA;MACAnC,MAAA,CAAAoC,OAAA,WAAAlB,KAAA;QACA,IAAAA,KAAA,CAAAK,IAAA,IAAAL,KAAA,CAAAK,IAAA,CAAAC,KAAA;UACA,IAAAa,OAAA,GAAAlB,aAAA,CAAAmB,OAAA,CAAAN,QAAA,EAAAd,KAAA,CAAAC,IAAA;UACAgB,IAAA,CAAAI,IAAA;YACAb,QAAA,EAAAW,OAAA;YACAlB,IAAA,EAAAkB,OAAA;YACAG,IAAA,EAAAtB,KAAA,CAAAsB,IAAA;YACAjB,IAAA,MAAAkB,cAAA,CAAAC,OAAA,MAAAxB,KAAA,CAAAK,IAAA;UACA;QACA;QACA,IAAAL,KAAA,CAAAyB,QAAA;UACA,IAAAC,QAAA,GAAAb,KAAA,CAAAD,eAAA,CAAAZ,KAAA,CAAAyB,QAAA,EAAAzB,KAAA,CAAAC,IAAA;UACA,IAAAyB,QAAA,CAAAf,MAAA;YACAM,IAAA,MAAAU,MAAA,KAAAC,mBAAA,CAAAJ,OAAA,EAAAP,IAAA,OAAAW,mBAAA,CAAAJ,OAAA,EAAAE,QAAA;UACA;QACA;MACA;MACA,OAAAT,IAAA;IACA;IACApB,QAAA,WAAAA,SAAA;MACA,IAAArB,SAAA,QAAAA,SAAA,QAAAoC,eAAA,MAAA9B,MAAA;MAAA,IAAA+C,SAAA,OAAAC,2BAAA,CAAAN,OAAA,EACAhD,SAAA;QAAAuD,KAAA;MAAA;QAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;UAAA,IAAA/B,GAAA,GAAA4B,KAAA,CAAAzC,KAAA;UACA;UACA,IAAAa,GAAA,CAAAmB,IAAA;YACA,KAAA3C,MAAA,CAAAwD,QAAA,4BAAAhC,GAAA;UACA;QACA;MAAA,SAAAM,GAAA;QAAAoB,SAAA,CAAAO,CAAA,CAAA3B,GAAA;MAAA;QAAAoB,SAAA,CAAAQ,CAAA;MAAA;IACA;IACAjD,OAAA,WAAAA,QAAA;MACA,IAAAkC,IAAA,QAAAnC,MAAA,CAAAmC,IAAA;MACA,IAAAA,IAAA;QACA,KAAA3C,MAAA,CAAAwD,QAAA,0BAAAhD,MAAA;QACA,SAAAA,MAAA,CAAAkB,IAAA,CAAAiC,IAAA;UACA,KAAA3D,MAAA,CAAAwD,QAAA,gCAAAhD,MAAA;QACA;MACA;MACA;IACA;IACAE,gBAAA,WAAAA,iBAAA;MAAA,IAAAkD,MAAA;MACA,IAAAtB,IAAA,QAAAuB,KAAA,CAAArC,GAAA;MACA,KAAAsC,SAAA;QAAA,IAAAC,UAAA,OAAAZ,2BAAA,CAAAN,OAAA,EACAP,IAAA;UAAA0B,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAV,CAAA,MAAAW,MAAA,GAAAD,UAAA,CAAAT,CAAA,IAAAC,IAAA;YAAA,IAAA/B,GAAA,GAAAwC,MAAA,CAAArD,KAAA;YACA,IAAAa,GAAA,CAAAyC,EAAA,CAAA3C,IAAA,IAAAsC,MAAA,CAAApD,MAAA,CAAAc,IAAA;cACAsC,MAAA,CAAAC,KAAA,CAAAK,UAAA,CAAAC,YAAA,CAAA3C,GAAA;cACA;cACA,IAAAA,GAAA,CAAAyC,EAAA,CAAApC,QAAA,IAAA+B,MAAA,CAAApD,MAAA,CAAAqB,QAAA;gBACA+B,MAAA,CAAA5D,MAAA,CAAAwD,QAAA,+BAAAI,MAAA,CAAApD,MAAA;cACA;cACA;YACA;UACA;QAAA,SAAAsB,GAAA;UAAAiC,UAAA,CAAAN,CAAA,CAAA3B,GAAA;QAAA;UAAAiC,UAAA,CAAAL,CAAA;QAAA;MACA;IACA;IACAU,kBAAA,WAAAA,mBAAAC,IAAA;MACA,KAAAC,IAAA,CAAAC,WAAA,CAAAF,IAAA;MACA,SAAA7D,MAAA,CAAAkB,IAAA,CAAAiC,IAAA;QACA,KAAA3D,MAAA,CAAAwD,QAAA,gCAAAhD,MAAA;MACA;IACA;IACAgE,gBAAA,WAAAA,iBAAAH,IAAA;MAAA,IAAAI,MAAA;MACA,KAAAH,IAAA,CAAAI,SAAA,CAAAL,IAAA,EAAAM,IAAA,WAAAC,IAAA;QAAA,IAAA7E,YAAA,GAAA6E,IAAA,CAAA7E,YAAA;QACA,IAAA0E,MAAA,CAAArD,QAAA,CAAAiD,IAAA;UACAI,MAAA,CAAAI,UAAA,CAAA9E,YAAA,EAAAsE,IAAA;QACA;MACA;IACA;IACAS,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MACA,KAAAT,IAAA,CAAAU,cAAA,MAAApF,WAAA,EAAA+E,IAAA,WAAA5E,YAAA;QACA,KAAAA,YAAA,CAAAkF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArD,QAAA,IAAAkD,MAAA,CAAAvE,MAAA,CAAAqB,QAAA;QAAA;UACAkD,MAAA,CAAAF,UAAA,CAAA9E,YAAA;QACA;MACA;IACA;IACAoF,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAd,IAAA,CAAAe,aAAA,MAAAzF,WAAA,EAAA+E,IAAA,WAAA5E,YAAA;QACA,KAAAA,YAAA,CAAAkF,IAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAArD,QAAA,IAAAuD,MAAA,CAAA5E,MAAA,CAAAqB,QAAA;QAAA;UACAuD,MAAA,CAAAP,UAAA,CAAA9E,YAAA;QACA;MACA;IACA;IACAuF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,OAAA,CAAA9C,IAAA,MAAA9C,WAAA,CAAAiC,QAAA,EAAA4D,KAAA,cACA;MACA,KAAAnB,IAAA,CAAAoB,cAAA,MAAA9F,WAAA,EAAA+E,IAAA;QACAY,MAAA,CAAA7E,gBAAA;MACA;IACA;IACAiF,YAAA,WAAAA,aAAAtB,IAAA;MAAA,IAAAuB,MAAA;MACA,KAAAtB,IAAA,CAAAuB,YAAA,GAAAlB,IAAA,WAAAmB,KAAA;QAAA,IAAA/F,YAAA,GAAA+F,KAAA,CAAA/F,YAAA;QACA,IAAA6F,MAAA,CAAA/F,SAAA,CAAAkG,IAAA,WAAAvE,GAAA;UAAA,OAAAA,GAAA,CAAAF,IAAA,IAAAsE,MAAA,CAAApF,MAAA,CAAAc,IAAA;QAAA;UACA;QACA;QACAsE,MAAA,CAAAf,UAAA,CAAA9E,YAAA,EAAAsE,IAAA;MACA;IACA;IACAQ,UAAA,WAAAA,WAAA9E,YAAA,EAAAsE,IAAA;MACA,IAAA2B,UAAA,GAAAjG,YAAA,CAAAkG,KAAA;MACA,IAAAD,UAAA;QACA,KAAAR,OAAA,CAAA9C,IAAA,CAAAsD,UAAA,CAAAnE,QAAA;MACA;QACA;QACA;QACA,IAAAwC,IAAA,CAAA1B,IAAA;UACA;UACA,KAAA6C,OAAA,CAAAU,OAAA;YAAA5E,IAAA,gBAAA+C,IAAA,CAAAxC;UAAA;QACA;UACA,KAAA2D,OAAA,CAAA9C,IAAA;QACA;MACA;IACA;IACAyD,QAAA,WAAAA,SAAA3E,GAAA,EAAAiC,CAAA;MACA,IAAA2C,YAAA;MACA,IAAAC,UAAA,QAAAC,GAAA,CAAAC,qBAAA,GAAA5G,IAAA;MACA,IAAA6G,WAAA,QAAAF,GAAA,CAAAE,WAAA;MACA,IAAAC,OAAA,GAAAD,WAAA,GAAAJ,YAAA;MACA,IAAAzG,IAAA,GAAA8D,CAAA,CAAAiD,OAAA,GAAAL,UAAA;;MAEA,IAAA1G,IAAA,GAAA8G,OAAA;QACA,KAAA9G,IAAA,GAAA8G,OAAA;MACA;QACA,KAAA9G,IAAA,GAAAA,IAAA;MACA;MAEA,KAAAD,GAAA,GAAA+D,CAAA,CAAAkD,OAAA;MACA,KAAAlH,OAAA;MACA,KAAAG,WAAA,GAAA4B,GAAA;IACA;IACAT,SAAA,WAAAA,UAAA;MACA,KAAAtB,OAAA;IACA;IACAmH,YAAA,WAAAA,aAAA;MACA,KAAA7F,SAAA;IACA;EACA;AACA;AAAA8F,OAAA,CAAAhE,OAAA,GAAAiE,QAAA"}]}