{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\post.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\post.js", "mtime": 1700558590447}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listPost", "query", "request", "url", "method", "params", "getPost", "postId", "addPost", "data", "updatePost", "delPost", "listPostByUnderUserId", "userId", "listPostByUserId"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/post.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询岗位列表\r\nexport function listPost(query) {\r\n  return request({\r\n    url: '/system/post/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询岗位详细\r\nexport function getPost(postId) {\r\n  return request({\r\n    url: '/system/post/' + postId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增岗位\r\nexport function addPost(data) {\r\n  return request({\r\n    url: '/system/post',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改岗位\r\nexport function updatePost(data) {\r\n  return request({\r\n    url: '/system/post',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除岗位\r\nexport function delPost(postId) {\r\n  return request({\r\n    url: '/system/post/' + postId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\nexport function listPostByUnderUserId(userId) {\r\n  return request({\r\n    url: '/system/post/underUser/' + userId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\nexport function listPostByUserId(userId) {\r\n  return request({\r\n    url: '/system/post/user/' + userId,\r\n    method: 'get'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,QAAQA,CAACC,KAAK,EAAE;EAC9B,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,mBAAmB;IACxBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,OAAOA,CAACC,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,OAAOA,CAACC,IAAI,EAAE;EAC5B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,UAAUA,CAACD,IAAI,EAAE;EAC/B,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,cAAc;IACnBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,OAAOA,CAACJ,MAAM,EAAE;EAC9B,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe,GAAGI,MAAM;IAC7BH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASQ,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB,GAAGU,MAAM;IACvCT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;AAEO,SAASU,gBAAgBA,CAACD,MAAM,EAAE;EACvC,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB,GAAGU,MAAM;IAClCT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}