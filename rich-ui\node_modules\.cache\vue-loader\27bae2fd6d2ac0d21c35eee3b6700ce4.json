{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Settings\\index.vue?vue&type=template&id=126b135a&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1754876882542}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}