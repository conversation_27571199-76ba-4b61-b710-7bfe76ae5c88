{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\distribute\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\distribute\\index.vue", "mtime": 1754876882581}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGREaXN0cmlidXRlLA0KICBjaGFuZ2VTdGF0dXMsDQogIGRlbERpc3RyaWJ1dGUsDQogIGZsYXNoRGlzdHJpYnV0ZSwNCiAgZ2V0RGlzdHJpYnV0ZSwNCiAgbGlzdERpc3RyaWJ1dGUsDQogIHVwZGF0ZURpc3RyaWJ1dGUNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL2Rpc3RyaWJ1dGUiOw0KaW1wb3J0IHtsaXN0UG9zdH0gZnJvbSAiQC9hcGkvc3lzdGVtL3Bvc3QiOw0KaW1wb3J0IHtsaXN0TWVudX0gZnJvbSAiQC9hcGkvc3lzdGVtL21lbnUiOw0KaW1wb3J0IHtsaXN0UGVybXN0eXBlfSBmcm9tICJAL2FwaS9zeXN0ZW0vcGVybXN0eXBlIjsNCmltcG9ydCBzdG9yZSBmcm9tICJAL3N0b3JlIjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiRGlzdHJpYnV0ZSIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIHNob3dMZWZ0OiAzLA0KICAgICAgc2hvd1JpZ2h0OiAyMSwNCiAgICAgIC8vIOmBrue9qeWxgg0KICAgICAgbG9hZGluZzogdHJ1ZSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogdHJ1ZSwNCiAgICAgIC8vIOaAu+adoeaVsA0KICAgICAgdG90YWw6IDAsDQogICAgICAvLyDmnYPpmZDop4TliJLooajmoLzmlbDmja4NCiAgICAgIGRpc3RyaWJ1dGVMaXN0OiBbXSwNCiAgICAgIG1lbnVPcHRpb25zOiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgfSwNCiAgICAgIC8vIOihqOWNleWPguaVsA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7fSwNCiAgICAgIHBvc3RPcHRpb25zOiBbXSwNCiAgICAgIHBlcnNUeXBlT3B0aW9uczogW10sDQogICAgfTsNCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBzaG93U2VhcmNoKG4pIHsNCiAgICAgIGlmIChuID09PSB0cnVlKSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjENCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc2hvd1JpZ2h0ID0gMjQNCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDANCiAgICAgIH0NCiAgICB9LA0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgIHRoaXMuZ2V0TWVudVRyZWVzZWxlY3QoKQ0KICAgIGxpc3RQb3N0KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICB0aGlzLnBvc3RPcHRpb25zID0gcmVzcG9uc2Uucm93cw0KICAgIH0pOw0KICAgIGxpc3RQZXJtc3R5cGUoKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgIHRoaXMucGVyc1R5cGVPcHRpb25zID0gcmVzcG9uc2UuZGF0YQ0KICAgIH0pDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBnZXREZXB0TGlzdCgpIHsNCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmRlcHRMaXN0Lmxlbmd0aCA9PSAwIHx8IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEucmVkaXNMaXN0LmRlcHQpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goJ2dldERlcHRMaXN0JykudGhlbigoKSA9PiB7DQogICAgICAgICAgbGV0IGRlcHRMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5kZXB0TGlzdFswXS5jaGlsZHJlbg0KICAgICAgICAgIGZvciAobGV0IGQgb2YgZGVwdExpc3RbMF0uY2hpbGRyZW4pIHsNCiAgICAgICAgICAgIGlmIChkLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgIGRlbGV0ZSBkLmNoaWxkcmVuDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICAgIHRoaXMuJHJlZnMuZGVwdC5nZXRMb2FkT3B0aW9ucyhkZXB0TGlzdCkNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGxldCBkZXB0TGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuZGVwdExpc3RbMF0uY2hpbGRyZW4NCiAgICAgICAgZm9yIChsZXQgZCBvZiBkZXB0TGlzdFswXS5jaGlsZHJlbikgew0KICAgICAgICAgIGlmIChkLmNoaWxkcmVuKSB7DQogICAgICAgICAgICBkZWxldGUgZC5jaGlsZHJlbg0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICB0aGlzLiRyZWZzLmRlcHQuZ2V0TG9hZE9wdGlvbnMoZGVwdExpc3QpDQogICAgICB9DQogICAgfSwNCiAgICAvKiog5p+l6K+i5p2D6ZmQ6KeE5YiS5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWU7DQogICAgICBsaXN0RGlzdHJpYnV0ZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5kaXN0cmlidXRlTGlzdCA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVGbGFzaCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGZsYXNoRGlzdHJpYnV0ZSgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLliLfmlrDmiJDlip8iKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmn6Xor6Loj5zljZXmoJHnu5PmnoQgKi8NCiAgICBnZXRNZW51VHJlZXNlbGVjdCgpIHsNCiAgICAgIGxpc3RNZW51KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMubWVudU9wdGlvbnMgPSAodGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLmRhdGEsICJtZW51SWQiKSk7DQogICAgICB9KQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgfSwNCiAgICAvLyDooajljZXph43nva4NCiAgICByZXNldCgpIHsNCiAgICAgIHRoaXMuZm9ybSA9IHsNCiAgICAgICAgZGlzdHJpYnV0ZUlkOiBudWxsLA0KICAgICAgICBkaXN0cmlidXRlTmFtZTogbnVsbCwNCiAgICAgICAgZGVwdElkczogW10sDQogICAgICAgIGRlcHRMaXN0OiBudWxsLA0KICAgICAgICBtZW51SWRzOiBbXSwNCiAgICAgICAgbWVudUxpc3Q6IG51bGwsDQogICAgICAgIHBvc2l0aW9uOiBudWxsLA0KICAgICAgICBwb3NpdGlvbklkOiBudWxsLA0KICAgICAgICBwZXJtc0lkczogW10sDQogICAgICAgIHBlcm1zTGlzdDogbnVsbCwNCiAgICAgICAgb3JkZXJOdW06IDAsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlKHJvdykgew0KICAgICAgbGV0IHRleHQgPSByb3cuc3RhdHVzID09PSAiMCIgPyAi5ZCv55SoIiA6ICLlgZznlKgiOw0KICAgICAgdGhpcy4kY29uZmlybSgn56Gu6K6k6KaBIicgKyB0ZXh0ICsgJ+WQl++8nycsICfmj5DnpLonLCB7Y3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJ30pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gY2hhbmdlU3RhdHVzKHJvdy5kaXN0cmlidXRlSWQsIHJvdy5zdGF0dXMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3ModGV4dCArICLmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIjsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5kaXN0cmlidXRlSWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDENCiAgICAgIHRoaXMubXVsdGlwbGUgPSAhc2VsZWN0aW9uLmxlbmd0aA0KICAgIH0sDQogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUFkZCgpIHsNCiAgICAgIHRoaXMucmVzZXQoKTsNCiAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICB0aGlzLnRpdGxlID0gIua3u+WKoOadg+mZkOinhOWIkiI7DQogICAgICB0aGlzLmZsYXNoU2VsZWN0KCkNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCkNCiAgICAgIGdldERpc3RyaWJ1dGUocm93LmRpc3RyaWJ1dGVJZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMub3BlbiA9IHRydWU7DQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55p2D6ZmQ6KeE5YiSIjsNCiAgICAgICAgdGhpcy5mbGFzaFNlbGVjdCgpDQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uZGlzdHJpYnV0ZUlkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZURpc3RyaWJ1dGUodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIik7DQogICAgICAgICAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB9KTsNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkRGlzdHJpYnV0ZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMubG9hZGluZyA9IGZhbHNlOw0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgICAgIH0pOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlRGVsZXRlKHJvdykgew0KICAgICAgY29uc3QgZGlzdHJpYnV0ZUlkcyA9IHJvdy5kaXN0cmlidXRlSWQgfHwgdGhpcy5pZHM7DQogICAgICB0aGlzLiRjb25maXJtKCfmmK/lkKbnoa7orqTliKDpmaTmnYPpmZDop4TliJLnvJblj7fkuLoiJyArIGRpc3RyaWJ1dGVJZHMgKyAnIueahOaVsOaNrumhue+8nycsICfmj5DnpLonLCB7Y3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJ30pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICByZXR1cm4gZGVsRGlzdHJpYnV0ZShkaXN0cmlidXRlSWRzKTsNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKTsNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIik7DQogICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCdzeXN0ZW0vZGlzdHJpYnV0ZS9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGBkaXN0cmlidXRlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgZmxhc2hTZWxlY3QoKSB7DQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgIHRoaXMuJHJlZnMubWVudS5nZXRMb2FkT3B0aW9ucyh0aGlzLm1lbnVPcHRpb25zKQ0KICAgICAgICB0aGlzLmdldERlcHRMaXN0KCkNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0KA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/distribute", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\"\r\n                 label-width=\"68px\">\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:distribute:add']\"\r\n            >新建权规\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:distribute:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:distribute:export']\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleFlash\"\r\n              v-hasPermi=\"['system:distribute:flash']\"\r\n            >刷新权限\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"distributeList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"center\" label=\"权规编号\" prop=\"distributeId\" width=\"68\"/>\r\n          <el-table-column align=\"left\" label=\"权规名称\" prop=\"distributeName\" width=\"170\"/>\r\n          <el-table-column align=\"left\" label=\"部门列表\" prop=\"deptList\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"center\" label=\"最低职级\" prop=\"position\" width=\"68\"/>\r\n          <el-table-column align=\"left\" label=\"菜单权限\" prop=\"menuList\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"left\" label=\"权限动作\" prop=\"permsList\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"left\" label=\"排序\" prop=\"orderNum\" width=\"48\"/>\r\n          <el-table-column align=\"left\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:distribute:edit']\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:distribute:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改权限规划对话框 -->\r\n    <el-dialog :close-on-click-modal=\"false\"\r\n               :modal-append-to-body=\"false\"\r\n               v-dialogDrag v-dialogDragWidth\r\n               :title=\"title\" :visible.sync=\"open\"\r\n               append-to-body\r\n               width=\"400px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"68px\">\r\n        <el-form-item label=\"权规名称\" prop=\"distributeName\">\r\n          <el-input v-model=\"form.distributeName\" placeholder=\"权限规划名称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门列表\">\r\n          <tree-select ref=\"dept\" :multiple=\"true\" :pass=\"form.deptIds\" :type=\"'dept'\"\r\n                       @return=\"form.deptIds=$event\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"最低职级\">\r\n          <el-select v-model=\"form.positionId\" clearable filterable placeholder=\"选择权限授权的最低职级\"\r\n                     ref=\"post\" style=\"width: 100%;\">\r\n            <el-option-group>\r\n              <el-option\r\n                v-for=\"item in postOptions\"\r\n                :key=\"item.positionId\"\r\n                :disabled=\"item.status == 1\"\r\n                :label=\"item.positionLocalName\"\r\n                :value=\"item.positionId\"\r\n              ></el-option>\r\n            </el-option-group>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"菜单权限\">\r\n          <tree-select ref=\"menu\" :multiple=\"true\" :pass=\"form.menuIds\" :type=\"'menu'\"\r\n                       @return=\"form.menuIds=$event\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"权限动作\">\r\n          <el-select v-model=\"form.permsIds\" clearable filterable multiple\r\n                     placeholder=\"权限\" style=\"width: 100%;\">\r\n            <el-option\r\n              v-for=\"item in persTypeOptions\"\r\n              :key=\"item.permsId\"\r\n              :disabled=\"item.status == 1\"\r\n              :label=\"item.permsDetail\"\r\n              :value=\"item.permsType\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input type=\"textarea\" v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" :min=\"0\" controls-position=\"right\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\"\r\n                   v-loading=\"loading\"\r\n                   element-loading-text=\"拼命加载中，请稍候\"\r\n                   element-loading-spinner=\"el-icon-loading\"\r\n                   element-loading-background=\"rgba(0, 0, 0, 0.8)\">确 定\r\n        </el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addDistribute,\r\n  changeStatus,\r\n  delDistribute,\r\n  flashDistribute,\r\n  getDistribute,\r\n  listDistribute,\r\n  updateDistribute\r\n} from \"@/api/system/distribute\";\r\nimport {listPost} from \"@/api/system/post\";\r\nimport {listMenu} from \"@/api/system/menu\";\r\nimport {listPermstype} from \"@/api/system/permstype\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"Distribute\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 权限规划表格数据\r\n      distributeList: [],\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      postOptions: [],\r\n      persTypeOptions: [],\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getMenuTreeselect()\r\n    listPost().then(response => {\r\n      this.postOptions = response.rows\r\n    });\r\n    listPermstype().then(response => {\r\n      this.persTypeOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    getDeptList() {\r\n      if (this.$store.state.data.deptList.length == 0 || this.$store.state.data.redisList.dept) {\r\n        store.dispatch('getDeptList').then(() => {\r\n          let deptList = this.$store.state.data.deptList[0].children\r\n          for (let d of deptList[0].children) {\r\n            if (d.children) {\r\n              delete d.children\r\n            }\r\n          }\r\n          this.$refs.dept.getLoadOptions(deptList)\r\n        })\r\n      } else {\r\n        let deptList = this.$store.state.data.deptList[0].children\r\n        for (let d of deptList[0].children) {\r\n          if (d.children) {\r\n            delete d.children\r\n          }\r\n        }\r\n        this.$refs.dept.getLoadOptions(deptList)\r\n      }\r\n    },\r\n    /** 查询权限规划列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDistribute(this.queryParams).then(response => {\r\n        this.distributeList = response.data;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleFlash() {\r\n      this.loading = true\r\n      flashDistribute().then(response => {\r\n        this.loading = false;\r\n        this.$message.success(\"刷新成功\")\r\n      })\r\n    },\r\n    /** 查询菜单树结构 */\r\n    getMenuTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = (this.handleTree(response.data, \"menuId\"));\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        distributeId: null,\r\n        distributeName: null,\r\n        deptIds: [],\r\n        deptList: null,\r\n        menuIds: [],\r\n        menuList: null,\r\n        position: null,\r\n        positionId: null,\r\n        permsIds: [],\r\n        permsList: null,\r\n        orderNum: 0,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.distributeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.distributeId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加权限规划\";\r\n      this.flashSelect()\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      getDistribute(row.distributeId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改权限规划\";\r\n        this.flashSelect()\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true\r\n          if (this.form.distributeId != null) {\r\n            updateDistribute(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.loading = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDistribute(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.loading = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const distributeIds = row.distributeId || this.ids;\r\n      this.$confirm('是否确认删除权限规划编号为\"' + distributeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delDistribute(distributeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/distribute/export', {\r\n        ...this.queryParams\r\n      }, `distribute_${new Date().getTime()}.xlsx`)\r\n    },\r\n    flashSelect() {\r\n      this.$nextTick(() => {\r\n        this.$refs.menu.getLoadOptions(this.menuOptions)\r\n        this.getDeptList()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}