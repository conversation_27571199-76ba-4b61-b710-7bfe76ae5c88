{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\index.vue", "mtime": 1754876882597}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_rctSearchFields", "require", "_js<PERSON><PERSON>yin", "_interopRequireDefault", "_vueTreeselect", "_store", "_rct", "_currency", "_bankSlip", "_rich", "_moment2", "_index", "_permission", "_index2", "_rctFieldLabelMap", "_", "_dispatchBill", "_preview", "_reconciliationBill", "_index3", "_index4", "hiprintTemplate", "_default", "name", "components", "DataAggregatorBackGround", "DynamicSearch", "printPreview", "DataAggregator", "CompanySelect", "Treeselect", "bankSlip", "data", "rctSearchFields", "fieldLabelMap", "rctFieldLabelMap", "yourDataSource", "date", "category", "region", "sales", "profit", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "statisticsOp", "total", "salesId", "verifyPsaId", "salesAssistantId", "opId", "belongList", "opList", "businessList", "openAggregator", "rctList", "aggregatorRctList", "queryParams", "pageNum", "pageSize", "params", "Map", "form", "rules", "watch", "n", "created", "load", "$route", "query", "no", "newBookingNo", "getList", "then", "loadSales", "loadOp", "loadBusinesses", "loadStaffList", "computed", "moment", "methods", "listAggregatorRct", "config", "JSON", "stringify", "handleOpenAggregator", "getReconciliationBill", "receivableUSD", "receivableRMB", "balanceUSD", "balanceRMB", "receivable", "balance", "company", "clientSummary", "split", "cloneDeep", "map", "item", "etd", "parseTime", "eta", "pol", "destinationPort", "console", "log", "dnRmbBalance", "currency", "add", "dnUsd", "value", "separator", "symbol", "precision", "format", "dnRmb", "dnUsdBalance", "<PERSON><PERSON><PERSON>", "PrintTemplate", "template", "reconciliationBill", "$refs", "preView", "print", "checkRole", "getReturn", "tableRowClassName", "_ref", "row", "rowIndex", "opAccept", "sqdDocDeliveryWay", "type", "getReleaseType", "id", "getName", "staff", "$store", "state", "allRsStaffList", "filter", "rsStaff", "staffId", "undefined", "staffFamilyLocalName", "staffGivingLocalName", "staffGivingEnName", "logisticsPaymentTerms", "v", "emergencyLevel", "difficultyLevel", "processStatus", "_this", "salesList", "length", "redisList", "store", "dispatch", "_this2", "businessesList", "_this3", "_this4", "staffList", "_this5", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "permissionLevel", "user", "permissionLevelList", "C", "listRct", "response", "rows", "stop", "handleQuery", "_objectSpread2", "rctNo", "trim", "reset<PERSON><PERSON>y", "resetForm", "handleSelectionChange", "selection", "rctId", "handleAdd", "$tab", "openPage", "handleUpdate", "rId", "dbclick", "column", "event", "handleDelete", "_this6", "rctIds", "$confirm", "customClass", "delRct", "$modal", "msgSuccess", "catch", "handleExport", "download", "concat", "Date", "getTime", "staffNormalizer", "node", "children", "l", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "roleId", "label", "isDisabled", "deptId", "exports"], "sources": ["src/views/system/rct/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <!--搜索条件-->\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\">\r\n          <el-form-item label=\"单号\" prop=\"rctNo\">\r\n            <el-input v-model=\"queryParams.rctNo\" placeholder=\"操作单号\" @keydown.enter.native=\"handleQuery\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.rctOpDate\" clearable\r\n                            placeholder=\"操作日期\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ATD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ATDDate\" clearable\r\n                            placeholder=\"ATD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"ETD\" prop=\"rctOpDate\">\r\n            <el-date-picker v-model=\"queryParams.ETDDate\" clearable\r\n                            placeholder=\"ETD\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"审核\" prop=\"pasVerifyTime\">\r\n            <el-date-picker v-model=\"queryParams.pasVerifyTime\" clearable\r\n                            placeholder=\"商务审核时间\"\r\n                            style=\"width:100%\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isOpAllotted\">\r\n            <el-select v-model=\"queryParams.isOpAllotted\" clearable placeholder=\"操作分配标记\" style=\"width: 100%\">\r\n              <el-option label=\"未分配\" value=\"0\">未分配</el-option>\r\n              <el-option label=\"已分配\" value=\"1\">已分配</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"客户\" prop=\"clientId\">\r\n            <company-select :multiple=\"false\" :no-parent=\"true\"\r\n                            :pass=\"queryParams.clientId\" :placeholder=\"'客户'\" :role-client=\"'1'\"\r\n                            :roleTypeId=\"1\" @return=\"queryParams.clientId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"进度\" prop=\"processStatusId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.processStatusId\" :placeholder=\"'进度状态'\"\r\n                         :type=\"'processStatus'\" @return=\"queryParams.processStatusId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"物流\" prop=\"logisticsTypeId\">\r\n            <tree-select :d-load=\"true\" :flat=\"false\" :multiple=\"false\"\r\n                         :pass=\"queryParams.logisticsTypeId\" :placeholder=\"'物流类型'\"\r\n                         :type=\"'serviceType'\" @return=\"queryParams.logisticsTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"启运\" prop=\"polIds\">\r\n            <location-select :multiple=\"true\" :no-parent=\"true\"\r\n                             :pass=\"queryParams.polIds\" :placeholder=\"'启运港'\" @return=\"queryParams.polIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"目的\" prop=\"destinationPortIds\">\r\n            <location-select :en=\"true\" :multiple=\"true\"\r\n                             :pass=\"queryParams.destinationPortIds\" :placeholder=\"'目的港'\"\r\n                             @return=\"queryParams.destinationPortIds=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"业务\" prop=\"salesId\">\r\n            <treeselect v-model=\"salesId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务员\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"助理\" prop=\"salesAssistantId\">\r\n            <treeselect v-model=\"salesAssistantId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"belongList\" :show-count=\"true\" placeholder=\"业务助理\"\r\n                        @open=\"loadSales\" @select=\"queryParams.salesAssistantId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.salesAssistantId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"标记\" prop=\"isPsaVerified\">\r\n            <el-select v-model=\"queryParams.isPsaVerified\" clearable placeholder=\"商务审核标记\" style=\"width: 100%\">\r\n              <el-option value=\"0\" label=\"已审\">已审</el-option>\r\n              <el-option value=\"1\" label=\"未审\">未审</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"商务\" prop=\"verifyPsaId\">\r\n            <treeselect v-model=\"verifyPsaId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\" :options=\"businessList\"\r\n                        :show-count=\"true\" placeholder=\"商务\"\r\n                        @open=\"loadBusinesses\" @select=\"queryParams.verifyPsaId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.verifyPsaId=null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"操作\" prop=\"opId\">\r\n            <treeselect v-model=\"opId\" :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                        :flatten-search-results=\"true\" :normalizer=\"staffNormalizer\"\r\n                        :options=\"opList.filter(v => {return v.role.roleLocalName=='操作员'})\"\r\n                        :show-count=\"true\" placeholder=\"操作员\"\r\n                        @open=\"loadOp\" @select=\"queryParams.opId = $event.staffId\"\r\n                        @input=\"$event==undefined?queryParams.opId = null:null\"\r\n            >\r\n              <div slot=\"value-label\" slot-scope=\"{node}\">\r\n                {{\r\n                  node.raw.staff != undefined ? node.raw.staff.staffFamilyLocalName + node.raw.staff.staffGivingLocalName + \" \" + node.raw.staff.staffGivingEnName : \"\"\r\n                }}\r\n              </div>\r\n              <label slot=\"option-label\"\r\n                     slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                     :class=\"labelClassName\"\r\n              >\r\n                {{ node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label }}\r\n                <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n              </label>\r\n            </treeselect>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属\">\r\n            <tree-select :flat=\"false\" :multiple=\"false\" :placeholder=\"'收付路径'\"\r\n                         :type=\"'rsPaymentTitle'\" @return=\"queryParams.orderBelongsTo=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"应收\">\r\n            <el-switch\r\n              v-model=\"queryParams.params.closedAccount\" @change=\"handleQuery\"\r\n            >\r\n            </el-switch>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <!--顶部操作按钮-->\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:rct:add']\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:rct:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <dynamic-search\r\n              :search-fields=\"rctSearchFields\"\r\n              :config-type=\"'rct-search'\"\r\n              @reset=\"resetQuery\"\r\n              @search=\"handleQuery($event)\"\r\n            />\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button @click=\"handleOpenAggregator\">数据汇总</el-button>\r\n            <el-dialog v-dialogDrag v-dialogDragWidth\r\n                       :visible.sync=\"openAggregator\" append-to-body width=\"80%\"\r\n            >\r\n              <!--<data-aggregator :data-source=\"aggregatorRctList\" :field-label-map=\"fieldLabelMap\"/>-->\r\n              <data-aggregator-back-ground :aggregate-function=\"listAggregatorRct\" :data-source=\"aggregatorRctList\"\r\n                                           :config-type=\"'rct-agg'\" :data-source-type=\"'rct'\"\r\n                                           :field-label-map=\"fieldLabelMap\"/>\r\n            </el-dialog>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              icon=\"el-icon-export\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-popover\r\n              v-for=\"(item,index) in [{file:'打印',link:getReconciliationBill,templateList:['对账单']}]\"\r\n              :key=\"index\" placement=\"top\" trigger=\"click\"\r\n              width=\"100\"\r\n            >\r\n              <el-button v-for=\"(item2,index) in item.templateList\" :key=\"index\"\r\n                         size=\"mini\" type=\"primary\"\r\n                         @click=\"item.link(item2)\"\r\n              >{{ item2 }}\r\n              </el-button>\r\n              <el-button slot=\"reference\" size=\"mini\" type=\"primary\"\r\n              >{{ item.file }}\r\n              </el-button>\r\n            </el-popover>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--表格-->\r\n        <el-table v-loading=\"loading\" :data=\"rctList\"\r\n                  stripe @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"dbclick\"\r\n        >\r\n          <el-table-column align=\"left\" label=\"序号\" type=\"index\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-badge :value=\"scope.row.opAccept==0?'new':''\" class=\"item\">\r\n                <div style=\"width: 15px\">{{ scope.$index + 1 }}</div>\r\n              </el-badge>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作单号\" prop=\"clientId\" show-overflow-tooltip width=\"120\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\" style=\"font-size: 18px;height: 23px\"\r\n              >{{ scope.row.rctNo }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"width: 100px;\">{{\r\n                  parseTime(scope.row.rctCreateTime, \"{m}.{d}\") + \" \" + emergencyLevel(scope.row.emergencyLevel) + \" \" + difficultyLevel(scope.row.difficultyLevel)\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"委托单位\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"column-text highlight-text\">\r\n                {{ scope.row.clientSummary ? scope.row.clientSummary.split(\"/\")[1] : null }}\r\n              </div>\r\n              <div class=\"unHighlight-text\" style=\"height: 23px\">\r\n                {{\r\n                  (scope.row.orderBelongsTo ? scope.row.orderBelongsTo : \"\") + \" \" + (scope.row.releaseType ? getReleaseType(scope.row.releaseType) : \"\") + \" \" + scope.row.paymentNode\r\n                }}\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流类型\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" font-weight: 600;\">{{ scope.row.logisticsTypeEnName }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" color: #b7bbc2;height: 23px\">\r\n                  {{ scope.row.impExpType === \"1\" ? \"出口\" : \"\" }}\r\n                  {{ scope.row.impExpType === \"2\" ? \"进口\" : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"启运港\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box \" style=\" font-size: 15px\">\r\n                  {{ scope.row.pol ? scope.row.pol.split(\"(\")[0] : scope.row.pol }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.pol ? \"(\" + scope.row.pol.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"目的港\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div style=\"width: 95px;overflow: hidden\">\r\n                <p class=\"column-text bottom-box highlight-text\" style=\" \">\r\n                  {{ scope.row.destinationPort ? scope.row.destinationPort.split(\"(\")[0] : scope.row.destinationPort }}\r\n                </p>\r\n                <p class=\"unHighlight-text\">\r\n                  {{ scope.row.destinationPort ? \"(\" + scope.row.destinationPort.split(\"(\")[1] : \"\" }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"计费货量\" width=\"140\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box highlight-text\" style=\" \">{{ scope.row.revenueTon }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">{{ scope.row.goodsNameSummary }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单\" width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.blTypeCode ? scope.row.blTypeCode : \"\")\r\n                  }}\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">\r\n                  {{ (scope.row.blFormCode ? scope.row.blFormCode : \"\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订舱\" show-overflow-tooltip width=\"90\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text bottom-box\"\r\n                   style=\"text-overflow: ellipsis; white-space: nowrap;font-weight: 600;  font-size: 13px\"\r\n                >{{\r\n                    scope.row.carrierEnName\r\n                  }} <span class=\"column-text unHighlight-text\" style=\" font-size: 12px\">{{\r\n                    \"(\" + scope.row.agreementTypeCode + \")\"\r\n                    }}</span></p>\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{ scope.row.supplierName }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <!--<el-table-column align=\"left\" label=\"注意事项\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{ scope.row.newBookingRemark + \"  \" + scope.row.inquiryInnerRemarkSum }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">{{\r\n                    scope.row.opLeaderNotice + \"  \" + scope.row.opInnerRemark\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>-->\r\n          <el-table-column align=\"left\" label=\"入仓与SO号\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.warehousingNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">{{ scope.row.soNo }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"提单与柜号\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{ scope.row.blNo }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" height: 23px\">{{ scope.row.sqdContainersSealsSum }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"订单状态\" show-overflow-tooltip width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  {{ processStatus(scope.row.processStatusId) }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"物流进度\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.podEta ? (\"ATD: \" + parseTime(scope.row.podEta, \"{m}-{d}\")) : (\"ETD: \" + parseTime(scope.row.etd, \"{m}-{d}\")))\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\"height: 23px \">{{\r\n                    (scope.row.destinationPortEta ? (\"ATA: \" + parseTime(scope.row.destinationPortEta, \"{m}-{d}\")) : (\"ETA: \" + parseTime(scope.row.eta, \"{m}-{d}\")))\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"文件进度\" show-overflow-tooltip>\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">{{\r\n                    (scope.row.docStatusA ? (scope.row.docStatusA.split(\":\")[0] + \": \" + scope.row.docStatusA.split(\":\")[1]) : \"\")\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box\" style=\" \">\r\n                  {{\r\n                    (scope.row.docStatusB ? (scope.row.docStatusB.split(\":\")[0] + \": \" + scope.row.docStatusB.split(\":\")[1]) : \"\")\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"收款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  <component :is=\"'bankSlip'\" :scope=\"scope\" :type=\"'receive'\" @return=\"getReturn\"/>\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">{{\r\n                    currency(currency(scope.row.dnUsdBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"$\",\r\n                      precision: 2\r\n                    }).format() + \" / \" + currency(currency(scope.row.dnRmbBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"主服务付款\" show-overflow-tooltip width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box \">\r\n                <p class=\"column-text top-box\" style=\" \">\r\n                  <component :is=\"'bankSlip'\" :scope=\"scope\" :type=\"'pay'\" @return=\"getReturn\"/>\r\n                </p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">{{\r\n                    currency(currency(scope.row.cnUsdBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"$\",\r\n                      precision: 2\r\n                    }).format() + \" / \" + currency(currency(scope.row.cnRmbBalance).value, {\r\n                      separator: \",\",\r\n                      symbol: \"¥\",\r\n                      precision: 2\r\n                    }).format()\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"right\" label=\"业绩\" width=\"100\">\r\n            <template slot=\"header\" slot-scope=\"scope\">\r\n              <div style=\"margin-right: 5px\">\r\n                业绩\r\n              </div>\r\n            </template>\r\n            <template slot-scope=\"scope\">\r\n              <div :class=\"currency(scope.row.profitInRmb).divide(scope.row.dnInRmb).value<0?'warning':''\"\r\n                   class=\"flex-box\"\r\n                   style=\"margin-right: 5px\"\r\n              >\r\n                <p class=\"column-text top-box\" style=\"height: 23px \">{{\r\n                    currency(scope.row.profitInRmb, {separator: \",\", symbol: \"¥\", precision: 2}).format()\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" height: 23px\">{{\r\n                    currency(scope.row.profitInRmb, {precision: 2}).divide(scope.row.dnInRmb).multiply(100).value + \"%\"\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"业务/助理\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\"overflow: hidden\">{{\r\n                    (getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId))) ? getName(scope.row.salesId) + (scope.row.salesId ? (\"/\" + getName(scope.row.salesAssistantId)) : getName(scope.row.salesAssistantId)) : null\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\"height: 23px \">\r\n                  {{ parseTime(scope.row.newBookingTime, \"{m}.{d}\") }}\r\n                </p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"商务审核\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\" width: 55px;overflow: hidden \">{{\r\n                    getName(scope.row.verifyPsaId)\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.psaVerifyTime, \"{m}.{d}\") + \" \" + (scope.row.psaVerifyStatusId == 1 ? \"通过\" : \"驳回\")\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"操作员\" show-overflow-tooltip width=\"60\">\r\n            <template slot-scope=\"scope\">\r\n              <div class=\"flex-box\" style=\"width: 55px;overflow: hidden\">\r\n                <p class=\"column-text top-box\" style=\" width: 55px;overflow: hidden \">{{\r\n                    getName(scope.row.opId)\r\n                  }}</p>\r\n                <p class=\"column-text bottom-box unHighlight-text\" style=\" \">\r\n                  {{\r\n                    parseTime(scope.row.statusUpdateTime, \"{m}.{d}\") + \" \" + (processStatus(scope.row.processStatusId))\r\n                  }}</p>\r\n              </div>\r\n            </template>\r\n          </el-table-column>\r\n          <!--<el-table-column align=\"left\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"50\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:rct:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>-->\r\n        </el-table>\r\n\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 预览 -->\r\n    <print-preview ref=\"preView\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {rctSearchFields} from \"@/config/rctSearchFields\"\r\nimport pinyin from \"js-pinyin\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\"\r\nimport store from \"@/store\"\r\nimport {delRct, listAggregatorRct, listRct, op} from \"@/api/system/rct\"\r\nimport currency from \"currency.js\"\r\nimport bankSlip from \"@/views/system/rct/bankSlip.vue\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport moment from \"moment\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport {checkRole} from \"@/utils/permission\"\r\nimport DataAggregator from \"@/views/system/DataAggregator/index.vue\"\r\nimport {rctFieldLabelMap} from \"@/config/rctFieldLabelMap\"\r\nimport {hiprint} from \"@\"\r\nimport dispatchBill from \"@/print-template/dispatchBill\"\r\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\r\nimport reconciliationBill from \"@/print-template/reconciliationBill\"\r\nimport DynamicSearch from \"@/components/DynamicSearch/index.vue\"\r\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\r\n\r\nlet hiprintTemplate\r\n\r\nexport default {\r\n  name: \"Rct\",\r\n  components: {\r\n    DataAggregatorBackGround,\r\n    DynamicSearch, printPreview, DataAggregator, CompanySelect, Treeselect, bankSlip\r\n  },\r\n  data() {\r\n    return {\r\n      rctSearchFields,\r\n      fieldLabelMap: rctFieldLabelMap,\r\n      yourDataSource: [\r\n        {\r\n          date: \"2023-01-01\",\r\n          category: \"Electronics\",\r\n          region: \"North\",\r\n          sales: 1000,\r\n          profit: 200\r\n        }\r\n        /* 你的数据格式示例：\r\n        {\r\n          date: '2023-01-01',\r\n          category: 'Electronics',\r\n          region: 'North',\r\n          sales: 1000,\r\n          profit: 200\r\n        }\r\n        */\r\n      ],\r\n      showLeft: 3,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      statisticsOp: [],\r\n      // 总条数\r\n      total: 0,\r\n      // 操作单列表表格数据\r\n      salesId: null,\r\n      verifyPsaId: null,\r\n      salesAssistantId: null,\r\n      opId: null,\r\n      belongList: [],\r\n      opList: [],\r\n      businessList: [],\r\n      openAggregator: false,\r\n      rctList: [],\r\n      aggregatorRctList: [],\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        params: new Map()\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    let load = false\r\n    if (this.$route.query.no) {\r\n      this.queryParams.newBookingNo = this.$route.query.no\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    } else {\r\n      this.getList().then(() => {\r\n        load = true\r\n      })\r\n    }\r\n    if (load) {\r\n      this.loadSales()\r\n      this.loadOp()\r\n      this.loadBusinesses()\r\n    }\r\n    this.loadStaffList()\r\n  },\r\n  computed: {\r\n    moment() {\r\n      return moment\r\n    }\r\n  },\r\n  methods: {\r\n    listAggregatorRct(params) {\r\n      params.config = JSON.stringify(params.config)\r\n      this.queryParams.params = params;\r\n      return listAggregatorRct(this.queryParams)\r\n    },\r\n    handleOpenAggregator() {\r\n      this.openAggregator = true\r\n    },\r\n    getReconciliationBill() {\r\n      let data = {}\r\n      let receivableUSD = 0\r\n      let receivableRMB = 0\r\n      let balanceUSD = 0\r\n      let balanceRMB = 0\r\n      data.receivable = \"\"\r\n      data.balance = \"\"\r\n      data.company = this.rctList[0].clientSummary.split(\"/\")[1]\r\n      data.rctList = this._.cloneDeep(this.rctList)\r\n      data.rctList.map(item => {\r\n        item.etd = parseTime(item.etd, \"{m}-{d}\")\r\n        item.eta = parseTime(item.eta, \"{m}-{d}\")\r\n        item.pol = item.pol.split(\"(\")[0]\r\n        item.destinationPort = item.destinationPort.split(\"(\")[0]\r\n        console.log(item.dnRmbBalance)\r\n        receivableUSD = currency(receivableUSD).add(item.dnUsd)\r\n        item.dnUsd = currency(currency(item.dnUsd).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        receivableRMB = currency(receivableUSD).add(item.dnRmb)\r\n        item.dnRmb = currency(currency(item.dnRmb).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        balanceUSD = currency(balanceUSD).add(item.dnUsdBalance)\r\n        item.dnUsdBalance = currency(currency(item.dnUsdBalance).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        balanceRMB = currency(balanceRMB).add(item.dnRmbBalance)\r\n        item.dnRmbBalance = currency(currency(item.dnRmbBalance).value, {\r\n          separator: \",\",\r\n          symbol: \"\",\r\n          precision: 2\r\n        }).format()\r\n        return item\r\n      })\r\n\r\n      receivableUSD = currency(currency(receivableUSD).value, {\r\n        separator: \",\",\r\n        symbol: \"$\",\r\n        precision: 2\r\n      }).format()\r\n      receivableRMB = currency(currency(receivableRMB).value, {\r\n        separator: \",\",\r\n        symbol: \"￥\",\r\n        precision: 2\r\n      }).format()\r\n      balanceUSD = currency(currency(balanceUSD).value, {\r\n        separator: \",\",\r\n        symbol: \"$\",\r\n        precision: 2\r\n      }).format()\r\n      balanceRMB = currency(currency(balanceRMB).value, {\r\n        separator: \",\",\r\n        symbol: \"￥\",\r\n        precision: 2\r\n      }).format()\r\n      data.receivable = receivableUSD + \"</br>\" + receivableRMB\r\n      data.balance = balanceUSD + \"</br>\" + balanceRMB\r\n\r\n      hiprintTemplate = new hiprint.PrintTemplate({template: reconciliationBill})\r\n      // 打开预览组件\r\n      this.$refs.preView.print(hiprintTemplate, data)\r\n    },\r\n    checkRole,\r\n    parseTime,\r\n    getReturn() {\r\n\r\n    },\r\n    currency,\r\n    tableRowClassName({row, rowIndex}) {\r\n      if (row.opAccept == 0) {\r\n        return \"unconfirmed\"\r\n      }\r\n      return \"\"\r\n    },\r\n    sqdDocDeliveryWay(type) {\r\n      if (type == 1) return \" 境外快递\"\r\n      if (type == 2) return \" 境内快递\"\r\n      if (type == 3) return \" 跑腿\"\r\n      if (type == 4) return \" 业务送达\"\r\n      if (type == 5) return \" 客户自取\"\r\n      if (type == 6) return \" QQ\"\r\n      if (type == 7) return \" 微信\"\r\n      if (type == 8) return \" 电邮\"\r\n      if (type == 9) return \" 公众号\"\r\n      if (type == 10) return \" 承运人系统\"\r\n      if (type == 11) return \" 订舱口系统\"\r\n      if (type == 12) return \" 第三方系统\"\r\n      return \"\"\r\n    },\r\n    getReleaseType(id) {\r\n      if (id == 1) return \"月结\"\r\n      if (id == 2) return \"押放\"\r\n      if (id == 3) return \"票结\"\r\n      if (id == 4) return \"签放\"\r\n      if (id == 5) return \"订金\"\r\n      if (id == 6) return \"预付\"\r\n      if (id == 7) return \"扣货\"\r\n      if (id == 9) return \"居间\"\r\n      return \"\"\r\n    },\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffGivingEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n    logisticsPaymentTerms(v) {\r\n      if (v == 1) return \"月结\"\r\n      if (v == 2) return \"押单\"\r\n      if (v == 3) return \"此票结清\"\r\n      if (v == 4) return \"经理签单\"\r\n      if (v == 5) return \"预收订金\"\r\n      if (v == 6) return \"全额预付\"\r\n      if (v == 7) return \"扣货\"\r\n      if (v == 8) return \"背靠背\"\r\n      return \"\"\r\n    },\r\n    emergencyLevel(v) {\r\n      if (v == 0) return \"预定\"\r\n      if (v == 1) return \"当天\"\r\n      if (v == 2) return \"常规\"\r\n      if (v == 3) return \"紧急\"\r\n      if (v == 4) return \"立即\"\r\n      return \"\"\r\n    },\r\n    difficultyLevel(v) {\r\n      if (v == 0) return \"简易\"\r\n      if (v == 1) return \"标准\"\r\n      if (v == 2) return \"高级\"\r\n      if (v == 3) return \"特别\"\r\n      return \"\"\r\n    },\r\n    processStatus(v) {\r\n      if (v == 1) return \"等待\"\r\n      if (v == 2) return \"进行\"\r\n      if (v == 3) return \"变更\"\r\n      if (v == 4) return \"异常\"\r\n      if (v == 5) return \"质押\"\r\n      if (v == 6) return \"确认\"\r\n      if (v == 7) return \"完成\"\r\n      if (v == 8) return \"取消\"\r\n      if (v == 9) return \"驳回\"\r\n      if (v == 10) return \"回收\"\r\n      return \"\"\r\n    },\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    },\r\n    loadBusinesses() {\r\n      if (this.$store.state.data.businessesList.length == 0 || this.$store.state.data.redisList.businessesList) {\r\n        store.dispatch(\"getBusinessesList\").then(() => {\r\n          this.businessList = this.$store.state.data.businessesList\r\n        })\r\n      } else {\r\n        this.businessList = this.$store.state.data.businessesList\r\n      }\r\n    },\r\n    loadOp() {\r\n      if (this.$store.state.data.opList.length == 0 || this.$store.state.data.redisList.opList) {\r\n        store.dispatch(\"getOpList\").then(() => {\r\n          this.opList = this.$store.state.data.opList\r\n        })\r\n      } else {\r\n        this.opList = this.$store.state.data.opList\r\n      }\r\n    },\r\n    loadStaffList() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    /** 查询操作单列表列表 */\r\n    async getList() {\r\n      this.loading = true\r\n      this.queryParams.permissionLevel = this.$store.state.user.permissionLevelList.C\r\n      await listRct(this.queryParams).then(response => {\r\n        this.rctList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery(queryParams) {\r\n      this.queryParams = {\r\n        ...this.queryParams,\r\n        ...queryParams,\r\n        pageNum: 1  // 直接在合并时设置页码为1\r\n      }\r\n\r\n      // 处理rctNo\r\n      this.queryParams.rctNo = this.queryParams.rctNo ? this.queryParams.rctNo.trim() : this.queryParams.rctNo\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // handleStatusChange(row) {\r\n    //   let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n    //   this.$modal.confirm('确认要\"' + text + '吗？').then(function () {\r\n    //     return changeStatus(row.rctId, row.status);\r\n    //   }).then(() => {\r\n    //     this.$modal.msgSuccess(text + \"成功\");\r\n    //   }).catch(function () {\r\n    //     row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n    //   });\r\n    // },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.rctId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {})\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: row.rctId})\r\n    },\r\n    dbclick(row, column, event) {\r\n      this.$tab.openPage(\"操作单\", \"/opprocess/opdetail\", {rId: row.rctId, type: \"op\"})\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const rctIds = row.rctId || this.ids\r\n      this.$confirm(\"是否确认删除操作单列表编号为\\\"\" + rctIds + \"\\\"的数据项？\", \"提示\", {customClass: \"modal-confirm\"}).then(function () {\r\n        return delRct(rctIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/rct/export\", {\r\n        ...this.queryParams\r\n      }, `rct_${new Date().getTime()}.xlsx`)\r\n    },\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.column-text {\r\n  margin: 0;\r\n  padding: 0;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.highlight-text {\r\n  font-weight: 600;\r\n  font-size: 15px\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n.el-table .warning-row {\r\n  background: oldlace;\r\n}\r\n\r\n.item {\r\n  margin-top: 10px;\r\n  margin-right: 40px;\r\n}\r\n\r\n::v-deep .el-badge__content.is-fixed {\r\n  font-size: 12px;\r\n  top: 0px;\r\n  right: 2px;\r\n}\r\n\r\n\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAgjBA,IAAAA,gBAAA,GAAAC,OAAA;AACA,IAAAC,SAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,OAAA;AACAA,OAAA;AACA,IAAAI,MAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,IAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,QAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,MAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,WAAA,GAAAX,OAAA;AACA,IAAAY,OAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,iBAAA,GAAAb,OAAA;AACA,IAAAc,CAAA,GAAAd,OAAA;AACA,IAAAe,aAAA,GAAAb,sBAAA,CAAAF,OAAA;AACA,IAAAgB,QAAA,GAAAd,sBAAA,CAAAF,OAAA;AACA,IAAAiB,mBAAA,GAAAf,sBAAA,CAAAF,OAAA;AACA,IAAAkB,OAAA,GAAAhB,sBAAA,CAAAF,OAAA;AACA,IAAAmB,OAAA,GAAAjB,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAoB,eAAA;AAAA,IAAAC,QAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IACAC,wBAAA,EAAAA,eAAA;IACAC,aAAA,EAAAA,eAAA;IAAAC,YAAA,EAAAA,gBAAA;IAAAC,cAAA,EAAAA,eAAA;IAAAC,aAAA,EAAAA,cAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,eAAA,EAAAA,gCAAA;MACAC,aAAA,EAAAC,kCAAA;MACAC,cAAA,GACA;QACAC,IAAA;QACAC,QAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;MACA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QARA,CASA;;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACAC,YAAA;MACA;MACAC,KAAA;MACA;MACAC,OAAA;MACAC,WAAA;MACAC,gBAAA;MACAC,IAAA;MACAC,UAAA;MACAC,MAAA;MACAC,YAAA;MACAC,cAAA;MACAC,OAAA;MACAC,iBAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,MAAA,MAAAC,GAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACApB,UAAA,WAAAA,WAAAqB,CAAA;MACA,IAAAA,CAAA;QACA,KAAA1B,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACA4B,OAAA,WAAAA,QAAA;IACA,IAAAC,IAAA;IACA,SAAAC,MAAA,CAAAC,KAAA,CAAAC,EAAA;MACA,KAAAb,WAAA,CAAAc,YAAA,QAAAH,MAAA,CAAAC,KAAA,CAAAC,EAAA;MACA,KAAAE,OAAA,GAAAC,IAAA;QACAN,IAAA;MACA;IACA;MACA,KAAAK,OAAA,GAAAC,IAAA;QACAN,IAAA;MACA;IACA;IACA,IAAAA,IAAA;MACA,KAAAO,SAAA;MACA,KAAAC,MAAA;MACA,KAAAC,cAAA;IACA;IACA,KAAAC,aAAA;EACA;EACAC,QAAA;IACAC,MAAA,WAAAA,OAAA;MACA,OAAAA,gBAAA;IACA;EACA;EACAC,OAAA;IACAC,iBAAA,WAAAA,kBAAArB,MAAA;MACAA,MAAA,CAAAsB,MAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAxB,MAAA,CAAAsB,MAAA;MACA,KAAAzB,WAAA,CAAAG,MAAA,GAAAA,MAAA;MACA,WAAAqB,sBAAA,OAAAxB,WAAA;IACA;IACA4B,oBAAA,WAAAA,qBAAA;MACA,KAAA/B,cAAA;IACA;IACAgC,qBAAA,WAAAA,sBAAA;MACA,IAAA1D,IAAA;MACA,IAAA2D,aAAA;MACA,IAAAC,aAAA;MACA,IAAAC,UAAA;MACA,IAAAC,UAAA;MACA9D,IAAA,CAAA+D,UAAA;MACA/D,IAAA,CAAAgE,OAAA;MACAhE,IAAA,CAAAiE,OAAA,QAAAtC,OAAA,IAAAuC,aAAA,CAAAC,KAAA;MACAnE,IAAA,CAAA2B,OAAA,QAAA5C,CAAA,CAAAqF,SAAA,MAAAzC,OAAA;MACA3B,IAAA,CAAA2B,OAAA,CAAA0C,GAAA,WAAAC,IAAA;QACAA,IAAA,CAAAC,GAAA,OAAAC,eAAA,EAAAF,IAAA,CAAAC,GAAA;QACAD,IAAA,CAAAG,GAAA,OAAAD,eAAA,EAAAF,IAAA,CAAAG,GAAA;QACAH,IAAA,CAAAI,GAAA,GAAAJ,IAAA,CAAAI,GAAA,CAAAP,KAAA;QACAG,IAAA,CAAAK,eAAA,GAAAL,IAAA,CAAAK,eAAA,CAAAR,KAAA;QACAS,OAAA,CAAAC,GAAA,CAAAP,IAAA,CAAAQ,YAAA;QACAnB,aAAA,OAAAoB,iBAAA,EAAApB,aAAA,EAAAqB,GAAA,CAAAV,IAAA,CAAAW,KAAA;QACAX,IAAA,CAAAW,KAAA,OAAAF,iBAAA,MAAAA,iBAAA,EAAAT,IAAA,CAAAW,KAAA,EAAAC,KAAA;UACAC,SAAA;UACAC,MAAA;UACAC,SAAA;QACA,GAAAC,MAAA;QACA1B,aAAA,OAAAmB,iBAAA,EAAApB,aAAA,EAAAqB,GAAA,CAAAV,IAAA,CAAAiB,KAAA;QACAjB,IAAA,CAAAiB,KAAA,OAAAR,iBAAA,MAAAA,iBAAA,EAAAT,IAAA,CAAAiB,KAAA,EAAAL,KAAA;UACAC,SAAA;UACAC,MAAA;UACAC,SAAA;QACA,GAAAC,MAAA;QACAzB,UAAA,OAAAkB,iBAAA,EAAAlB,UAAA,EAAAmB,GAAA,CAAAV,IAAA,CAAAkB,YAAA;QACAlB,IAAA,CAAAkB,YAAA,OAAAT,iBAAA,MAAAA,iBAAA,EAAAT,IAAA,CAAAkB,YAAA,EAAAN,KAAA;UACAC,SAAA;UACAC,MAAA;UACAC,SAAA;QACA,GAAAC,MAAA;QACAxB,UAAA,OAAAiB,iBAAA,EAAAjB,UAAA,EAAAkB,GAAA,CAAAV,IAAA,CAAAQ,YAAA;QACAR,IAAA,CAAAQ,YAAA,OAAAC,iBAAA,MAAAA,iBAAA,EAAAT,IAAA,CAAAQ,YAAA,EAAAI,KAAA;UACAC,SAAA;UACAC,MAAA;UACAC,SAAA;QACA,GAAAC,MAAA;QACA,OAAAhB,IAAA;MACA;MAEAX,aAAA,OAAAoB,iBAAA,MAAAA,iBAAA,EAAApB,aAAA,EAAAuB,KAAA;QACAC,SAAA;QACAC,MAAA;QACAC,SAAA;MACA,GAAAC,MAAA;MACA1B,aAAA,OAAAmB,iBAAA,MAAAA,iBAAA,EAAAnB,aAAA,EAAAsB,KAAA;QACAC,SAAA;QACAC,MAAA;QACAC,SAAA;MACA,GAAAC,MAAA;MACAzB,UAAA,OAAAkB,iBAAA,MAAAA,iBAAA,EAAAlB,UAAA,EAAAqB,KAAA;QACAC,SAAA;QACAC,MAAA;QACAC,SAAA;MACA,GAAAC,MAAA;MACAxB,UAAA,OAAAiB,iBAAA,MAAAA,iBAAA,EAAAjB,UAAA,EAAAoB,KAAA;QACAC,SAAA;QACAC,MAAA;QACAC,SAAA;MACA,GAAAC,MAAA;MACAtF,IAAA,CAAA+D,UAAA,GAAAJ,aAAA,aAAAC,aAAA;MACA5D,IAAA,CAAAgE,OAAA,GAAAH,UAAA,aAAAC,UAAA;MAEAzE,eAAA,OAAAoG,SAAA,CAAAC,aAAA;QAAAC,QAAA,EAAAC;MAAA;MACA;MACA,KAAAC,KAAA,CAAAC,OAAA,CAAAC,KAAA,CAAA1G,eAAA,EAAAW,IAAA;IACA;IACAgG,SAAA,EAAAA,qBAAA;IACAxB,SAAA,EAAAA,eAAA;IACAyB,SAAA,WAAAA,UAAA,GAEA;IACAlB,QAAA,EAAAA,iBAAA;IACAmB,iBAAA,WAAAA,kBAAAC,IAAA;MAAA,IAAAC,GAAA,GAAAD,IAAA,CAAAC,GAAA;QAAAC,QAAA,GAAAF,IAAA,CAAAE,QAAA;MACA,IAAAD,GAAA,CAAAE,QAAA;QACA;MACA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA,IAAAA,IAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA,IAAAA,EAAA;MACA;IACA;IACAC,OAAA,WAAAA,QAAAD,EAAA;MACA,IAAAA,EAAA;QACA,IAAAE,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+G,cAAA,CAAAC,MAAA,WAAAC,OAAA;UAAA,OAAAA,OAAA,CAAAC,OAAA,IAAAR,EAAA;QAAA;QACA,IAAAE,KAAA,IAAAA,KAAA,KAAAO,SAAA;UACA,OAAAP,KAAA,CAAAQ,oBAAA,GAAAR,KAAA,CAAAS,oBAAA,GAAAT,KAAA,CAAAU,iBAAA;QACA;MACA;MACA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAD,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA;IACA;IACAE,eAAA,WAAAA,gBAAAF,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA;IACA;IACAG,aAAA,WAAAA,cAAAH,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA,IAAAA,CAAA;MACA;IACA;IACA1E,SAAA,WAAAA,UAAA;MAAA,IAAA8E,KAAA;MACA,SAAAf,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA6H,SAAA,CAAAC,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+H,SAAA,CAAAF,SAAA;QACAG,cAAA,CAAAC,QAAA,iBAAApF,IAAA;UACA+E,KAAA,CAAArG,UAAA,GAAAqG,KAAA,CAAAf,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA6H,SAAA;QACA;MACA;QACA,KAAAtG,UAAA,QAAAsF,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA6H,SAAA;MACA;IACA;IACA7E,cAAA,WAAAA,eAAA;MAAA,IAAAkF,MAAA;MACA,SAAArB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAAmI,cAAA,CAAAL,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+H,SAAA,CAAAI,cAAA;QACAH,cAAA,CAAAC,QAAA,sBAAApF,IAAA;UACAqF,MAAA,CAAAzG,YAAA,GAAAyG,MAAA,CAAArB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAAmI,cAAA;QACA;MACA;QACA,KAAA1G,YAAA,QAAAoF,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAAmI,cAAA;MACA;IACA;IACApF,MAAA,WAAAA,OAAA;MAAA,IAAAqF,MAAA;MACA,SAAAvB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAAwB,MAAA,CAAAsG,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+H,SAAA,CAAAvG,MAAA;QACAwG,cAAA,CAAAC,QAAA,cAAApF,IAAA;UACAuF,MAAA,CAAA5G,MAAA,GAAA4G,MAAA,CAAAvB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAAwB,MAAA;QACA;MACA;QACA,KAAAA,MAAA,QAAAqF,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAAwB,MAAA;MACA;IACA;IACAyB,aAAA,WAAAA,cAAA;MAAA,IAAAoF,MAAA;MACA,SAAAxB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+G,cAAA,CAAAe,MAAA,cAAAjB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+H,SAAA,CAAAhB,cAAA;QACAiB,cAAA,CAAAC,QAAA,sBAAApF,IAAA;UACAwF,MAAA,CAAAC,SAAA,GAAAD,MAAA,CAAAxB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+G,cAAA;QACA;MACA;QACA,KAAAuB,SAAA,QAAAzB,MAAA,CAAAC,KAAA,CAAA9G,IAAA,CAAA+G,cAAA;MACA;IACA;IACA,gBACAnE,OAAA,WAAAA,QAAA;MAAA,IAAA2F,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAA3H,OAAA;cACA2H,MAAA,CAAA1G,WAAA,CAAAqH,eAAA,GAAAX,MAAA,CAAA1B,MAAA,CAAAC,KAAA,CAAAqC,IAAA,CAAAC,mBAAA,CAAAC,CAAA;cAAAN,QAAA,CAAAE,IAAA;cAAA,OACA,IAAAK,YAAA,EAAAf,MAAA,CAAA1G,WAAA,EAAAgB,IAAA,WAAA0G,QAAA;gBACAhB,MAAA,CAAA5G,OAAA,GAAA4H,QAAA,CAAAC,IAAA;gBACAjB,MAAA,CAAArH,KAAA,GAAAqI,QAAA,CAAArI,KAAA;gBACAqH,MAAA,CAAA3H,OAAA;cACA;YAAA;YAAA;cAAA,OAAAmI,QAAA,CAAAU,IAAA;UAAA;QAAA,GAAAb,OAAA;MAAA;IACA;IACA,aACAc,WAAA,WAAAA,YAAA7H,WAAA;MACA,KAAAA,WAAA,OAAA8H,cAAA,CAAAlB,OAAA,MAAAkB,cAAA,CAAAlB,OAAA,MAAAkB,cAAA,CAAAlB,OAAA,MACA,KAAA5G,WAAA,GACAA,WAAA;QACAC,OAAA;MAAA,EACA;;MAEA;MACA,KAAAD,WAAA,CAAA+H,KAAA,QAAA/H,WAAA,CAAA+H,KAAA,QAAA/H,WAAA,CAAA+H,KAAA,CAAAC,IAAA,UAAAhI,WAAA,CAAA+H,KAAA;MACA,KAAAhH,OAAA;IACA;IACA,aACAkH,UAAA,WAAAA,WAAA;MACA,KAAAC,SAAA;MACA,KAAAL,WAAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAM,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAApJ,GAAA,GAAAoJ,SAAA,CAAA5F,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA4F,KAAA;MAAA;MACA,KAAApJ,MAAA,GAAAmJ,SAAA,CAAAnC,MAAA;MACA,KAAA/G,QAAA,IAAAkJ,SAAA,CAAAnC,MAAA;IACA;IACA,aACAqC,SAAA,WAAAA,UAAA;MACA,KAAAC,IAAA,CAAAC,QAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAlE,GAAA;MACA,KAAAgE,IAAA,CAAAC,QAAA;QAAAE,GAAA,EAAAnE,GAAA,CAAA8D;MAAA;IACA;IACAM,OAAA,WAAAA,QAAApE,GAAA,EAAAqE,MAAA,EAAAC,KAAA;MACA,KAAAN,IAAA,CAAAC,QAAA;QAAAE,GAAA,EAAAnE,GAAA,CAAA8D,KAAA;QAAA1D,IAAA;MAAA;IACA;IACA,aACAmE,YAAA,WAAAA,aAAAvE,GAAA;MAAA,IAAAwE,MAAA;MACA,IAAAC,MAAA,GAAAzE,GAAA,CAAA8D,KAAA,SAAArJ,GAAA;MACA,KAAAiK,QAAA,sBAAAD,MAAA;QAAAE,WAAA;MAAA,GAAAlI,IAAA;QACA,WAAAmI,WAAA,EAAAH,MAAA;MACA,GAAAhI,IAAA;QACA+H,MAAA,CAAAhI,OAAA;QACAgI,MAAA,CAAAK,MAAA,CAAAC,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,0BAAA1B,cAAA,CAAAlB,OAAA,MACA,KAAA5G,WAAA,UAAAyJ,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAAC,QAAA,KAAAD,IAAA,CAAAC,QAAA,CAAA7D,MAAA;QACA,OAAA4D,IAAA,CAAAC,QAAA;MACA;MACA,IAAAC,CAAA;MACA,IAAAF,IAAA,CAAA9E,KAAA;QACA,IAAA8E,IAAA,CAAA9E,KAAA,CAAAQ,oBAAA,YAAAsE,IAAA,CAAA9E,KAAA,CAAAS,oBAAA;UACA,IAAAqE,IAAA,CAAAG,IAAA,CAAAC,aAAA;YACAF,CAAA,GAAAF,IAAA,CAAAG,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAAG,IAAA,CAAAC,aAAA;UACA;YACAF,CAAA,GAAAF,IAAA,CAAAO,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAAO,IAAA,CAAAC,aAAA;UACA;QACA;UACAN,CAAA,GAAAF,IAAA,CAAA9E,KAAA,CAAAuF,SAAA,SAAAT,IAAA,CAAA9E,KAAA,CAAAQ,oBAAA,GAAAsE,IAAA,CAAA9E,KAAA,CAAAS,oBAAA,SAAAqE,IAAA,CAAA9E,KAAA,CAAAU,iBAAA,SAAAyE,iBAAA,CAAAC,YAAA,CAAAN,IAAA,CAAA9E,KAAA,CAAAQ,oBAAA,GAAAsE,IAAA,CAAA9E,KAAA,CAAAS,oBAAA;QACA;MACA;MACA,IAAAqE,IAAA,CAAAU,MAAA;QACA;UACA1F,EAAA,EAAAgF,IAAA,CAAAU,MAAA;UACAC,KAAA,EAAAT,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAW,UAAA,EAAAZ,IAAA,CAAAxE,OAAA,YAAAwE,IAAA,CAAAC,QAAA,IAAAxE;QACA;MACA;QACA;UACAT,EAAA,EAAAgF,IAAA,CAAAa,MAAA;UACAF,KAAA,EAAAT,CAAA;UACAD,QAAA,EAAAD,IAAA,CAAAC,QAAA;UACAW,UAAA,EAAAZ,IAAA,CAAAxE,OAAA,YAAAwE,IAAA,CAAAC,QAAA,IAAAxE;QACA;MACA;IACA;EACA;AACA;AAAAqF,OAAA,CAAA/D,OAAA,GAAAnJ,QAAA"}]}