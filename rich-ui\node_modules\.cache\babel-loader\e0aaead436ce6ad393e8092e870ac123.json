{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeSelect.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\chargeSelect.vue", "mtime": 1741689110704}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_rct", "require", "_index", "_interopRequireDefault", "_currency", "_charge", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_store", "name", "components", "Treeselect", "CompanySelect", "props", "mounted", "loadCarrier", "data", "carrierList", "charges", "selected<PERSON><PERSON>ges", "queryParams", "pageNum", "pageSize", "rctNo", "supplierId", "searchData", "polIds", "polId", "destinationPortIds", "destinationPortId", "carrierId", "params", "Map", "tableData", "methods", "getName", "id", "staff", "$store", "state", "allRsStaffList", "filter", "rsStaff", "staffId", "staffFamilyLocalName", "staffGivingLocalName", "staffShortName", "querySupplier", "getList", "_this", "length", "redisList", "carrier", "store", "dispatch", "then", "carrierNormalizer", "node", "label", "carrierShortName", "carrierLocalName", "carrierEnName", "pinyin", "getFullChars", "currency", "getServiceObject", "serviceTypeId", "serviceObject", "rsOpSeaFclList", "rsOpSeaLclList", "rsOpAirList", "rsOpRailFCL", "rsOpRailLCL", "rsOpExpress", "rsOpCtnrTruckList", "rsOpBulkTruckList", "rsOpDocDeclareList", "rsOpFreeDeclareList", "rsOpDOAgent", "rsOpClearAgent", "rsOpWHS", "rsOp3rdCert", "rsOpINS", "rsOpTrading", "rsOpFumigation", "rsOpCO", "rsOpOther", "getCharges", "val", "_this2", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "isReceivable", "wrap", "_callee$", "_context", "prev", "next", "getRct", "rctId", "response", "rsClientMessage", "console", "log", "rsChargeList", "$nextTick", "for<PERSON>ach", "row", "clearingCompanyId", "clientId", "$refs", "chargesTable", "toggleRowSelection", "stop", "handleSelectionChange", "search", "copyCharge", "$emit", "handleQueryPol", "pols", "handleQueryDestinationPortId", "handleQueryCarrier", "_this3", "_callee2", "_callee2$", "_context2", "loading", "op", "listRct", "rows", "total", "exports", "_default"], "sources": ["src/views/system/document/chargeSelect.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-table\r\n      :data=\"tableData\" border stripe\r\n      style=\"width: 100%\"\r\n    >\r\n      <el-table-column label=\"序号\"\r\n                       type=\"index\"\r\n      />\r\n      <el-table-column\r\n        align=\"left\"\r\n      >\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <el-input\r\n            v-model=\"queryParams.rctNo\"\r\n            placeholder=\"操作单号\" size=\"mini\"\r\n            @keydown.enter.native=\"getList\"\r\n          />\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.rctNo }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column>\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <location-select :load-options=\"searchData.locationOptions\" :multiple=\"true\" :no-parent=\"true\"\r\n                           :pass=\"queryParams.polIds\" :placeholder=\"'启运港'\" @return=\"handleQueryPol($event)\"\r\n          />\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.pol }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column>\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <location-select :en=\"true\" :load-options=\"searchData.locationOptions\" :multiple=\"true\"\r\n                           :pass=\"queryParams.destinationPortIds\" :placeholder=\"'目的港'\"\r\n                           @return=\"handleQueryDestinationPortId($event)\"\r\n          />\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.destinationPort }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column>\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <el-input\r\n            v-model=\"queryParams.revenueTon\"\r\n            placeholder=\"柜型\" @keydown.enter.native=\"getList\"\r\n            size=\"mini\"\r\n          />\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.revenueTon }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column>\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <treeselect v-model=\"queryParams.carrierId\"\r\n                      :disable-branch-nodes=\"true\" :disabled-fuzzy-matching=\"true\"\r\n                      :flat=\"false\" :flatten-search-results=\"true\"\r\n                      :multiple=\"false\" :normalizer=\"carrierNormalizer\"\r\n                      :options=\"carrierList\" :show-count=\"true\" placeholder=\"选择承运人\"\r\n                      style=\"overflow: visible\" @select=\"handleQueryCarrier($event)\"\r\n          >\r\n            <div slot=\"value-label\" slot-scope=\"{node}\">\r\n              {{\r\n                (node.raw.carrierIntlCode != null) ? node.raw.carrierIntlCode : \" \"\r\n              }}\r\n            </div>\r\n            <label slot=\"option-label\"\r\n                   slot-scope=\"{ node, shouldShowCount, count, labelClassName, countClassName }\"\r\n                   :class=\"labelClassName\"\r\n            >\r\n              {{\r\n                node.label.indexOf(\",\") != -1 ? node.label.substring(0, node.label.indexOf(\",\")) : node.label\r\n              }}\r\n              <span v-if=\"shouldShowCount\" :class=\"countClassName\">({{ count }})</span>\r\n            </label>\r\n          </treeselect>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column>\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <company-select :multiple=\"false\"\r\n                          :no-parent=\"true\" :pass=\"queryParams.clientId\" :placeholder=\"'订舱口'\"\r\n                          :role-control=\"false\" @return=\"querySupplier($event)\"\r\n          />\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.supplierName }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column>\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <el-input\r\n            v-model=\"queryParams.opId\"\r\n            placeholder=\"录入人\"\r\n            size=\"mini\"\r\n          />\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ getName(scope.row.opId) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column>\r\n        <template slot=\"header\" slot-scope=\"scope\">\r\n          <el-input\r\n            v-model=\"queryParams.verifyTime\"\r\n            placeholder=\"审核日期\"\r\n            size=\"mini\"\r\n          />\r\n        </template>\r\n        <template slot-scope=\"scope\">\r\n          {{ scope.row.psaVerifyTime }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column\r\n        label=\"操作\"\r\n      >\r\n        <template slot-scope=\"scope\">\r\n          <el-popover\r\n            placement=\"left\"\r\n            trigger=\"click\"\r\n            width=\"400\"\r\n          >\r\n            <el-table ref=\"chargesTable\" :data=\"charges\" @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\"\r\n              >\r\n              </el-table-column>\r\n              <!--<el-table-column  align=\"center\" label=\"供应商\" width=\"90\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{ scope.row.companyName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>-->\r\n              <el-table-column align=\"center\" label=\"费用\" prop=\"costChargeId\" width=\"80px\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{ scope.row.chargeName }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"货币\" prop=\"costCurrencyId\" width=\"70px\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{ scope.row.dnCurrencyCode }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"单价\" prop=\"inquiryRate\" width=\"68px\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{\r\n                      scope.row.dnCurrencyCode === \"RMB\" ? currency(scope.row.dnUnitRate, {\r\n                        separator: \",\",\r\n                        symbol: \"¥\"\r\n                      }).format() : scope.row.dnCurrencyCode === \"USD\" ? currency(scope.row.dnUnitRate, {\r\n                        separator: \",\",\r\n                        symbol: \"$\"\r\n                      }).format() : scope.row.dnUnitRate\r\n                    }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"单位\" prop=\"costUnitId\" width=\"50px\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{ scope.row.dnUnitCode }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"数量\" prop=\"costAmount\" width=\"48px\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{ scope.row.dnAmount }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"汇率\" prop=\"costExchangeRate\" width=\"60px\">\r\n                <template slot-scope=\"scope\" style=\"display:flex;\">\r\n                  <div>\r\n                    {{ currency(scope.row.basicCurrencyRate).value }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"税率\" prop=\"costTaxRate\" width=\"68px\">\r\n                <template slot-scope=\"scope\">\r\n                  <div style=\"display: flex;justify-content: center\">\r\n                    <div>\r\n                      {{ scope.row.dutyRate }}\r\n                    </div>\r\n                    <div>%</div>\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"小计\" prop=\"costTotal\" width=\"65\">\r\n                <template slot-scope=\"scope\">\r\n                  <div>\r\n                    {{ currency(scope.row.subtotal, {separator: \",\", symbol: \"¥\"}).format() }}\r\n                  </div>\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"费用备注\">\r\n                <template slot-scope=\"scope\">\r\n                  <input v-model=\"scope.row.chargeRemark\" :disabled=\"scope.row.isAccountConfirmed == '1'\"\r\n                         style=\"border: none\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"审核状态\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row.isAccountConfirmed == 1 ? \"已审核\" : \"未审核\" }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"已付金额\">\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    currency(currency(scope.row.dnUnitRate).multiply(scope.row.dnAmount)).subtract(scope.row.sqdDnCurrencyBalance)\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column align=\"center\" label=\"未付余额\">\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row.sqdDnCurrencyBalance }}\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <el-button slot=\"reference\" type=\"text\" @click.native=\"getCharges(scope.row)\">[浏览费用明细]</el-button>\r\n          </el-popover>\r\n          <el-button style=\"float: right\" type=\"text\" @click.native=\"copyCharge\">[复 制]</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {getRct, listRct} from \"@/api/system/rct\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport currency from \"currency.js\"\r\nimport {getChargeByServiceId} from \"@/api/system/charge\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\n\r\nexport default {\r\n  name: \"chargeSelect\",\r\n  components: {Treeselect, CompanySelect},\r\n  props: [\"serviceTypeId\", \"searchData\", \"clientId\"],\r\n  mounted() {\r\n    this.loadCarrier()\r\n  },\r\n  data() {\r\n    return {\r\n      carrierList: [],\r\n      charges: [],\r\n      selectedCharges: [],\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        rctNo: null,\r\n        supplierId: this.searchData.supplierId,\r\n        polIds: [this.searchData.polId ? this.searchData.polId : null],\r\n        destinationPortIds: [this.searchData.destinationPortId ? this.searchData.destinationPortId : null],\r\n        carrierId: this.searchData.carrierId,\r\n        params: new Map()\r\n      },\r\n      tableData: []\r\n    }\r\n  },\r\n  methods: {\r\n    getName(id) {\r\n      if (id) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff) {\r\n          return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n        }\r\n      }\r\n    },\r\n    querySupplier(id) {\r\n\r\n      this.getList()\r\n    },\r\n    loadCarrier() {\r\n      if (this.$store.state.data.carrierList.length == 0 || this.$store.state.data.redisList.carrier) {\r\n        store.dispatch(\"getCarrierList\").then(() => {\r\n            this.carrierList = this.$store.state.data.carrierList\r\n          }\r\n        )\r\n      } else {\r\n        this.carrierList = this.$store.state.data.carrierList\r\n      }\r\n    },\r\n    carrierNormalizer(node) {\r\n      return {\r\n        id: node.carrierId,\r\n        label: (node.carrierShortName != null ? node.carrierShortName : \"\") + \" \" + (node.carrierLocalName != null ? node.carrierLocalName : \"\") + \" \" + (node.carrierEnName != null ? node.carrierEnName : \"\") + \",\" + pinyin.getFullChars((node.carrierShortName != null ? node.carrierShortName : \"\") + \" \" + (node.carrierLocalName != null ? node.carrierLocalName : \"\"))\r\n      }\r\n    },\r\n    currency,\r\n    getServiceObject(serviceTypeId, serviceObject) {\r\n      if (serviceTypeId === 1) {\r\n        return serviceObject.rsOpSeaFclList ? serviceObject.rsOpSeaFclList[0] : null\r\n      }\r\n      if (serviceTypeId === 2) {\r\n        return serviceObject.rsOpSeaLclList ? serviceObject.rsOpSeaLclList[0] : null\r\n      }\r\n      if (serviceTypeId === 10) {\r\n        return serviceObject.rsOpAirList ? serviceObject.rsOpAirList[0] : null\r\n      }\r\n      if (serviceTypeId === 20) {\r\n        return serviceObject.rsOpRailFCL\r\n      }\r\n      if (serviceTypeId === 21) {\r\n        return serviceObject.rsOpRailLCL\r\n      }\r\n      if (serviceTypeId === 40) {\r\n        return serviceObject.rsOpExpress\r\n      }\r\n      if (serviceTypeId === 50) {\r\n        return serviceObject.rsOpCtnrTruckList ? serviceObject.rsOpCtnrTruckList[0] : null\r\n      }\r\n      if (serviceTypeId === 51) {\r\n        return serviceObject.rsOpBulkTruckList ? serviceObject.rsOpBulkTruckList[0] : null\r\n      }\r\n      if (serviceTypeId === 60) {\r\n        return serviceObject.rsOpDocDeclareList ? serviceObject.rsOpDocDeclareList[0] : null\r\n      }\r\n      if (serviceTypeId === 61) {\r\n        return serviceObject.rsOpFreeDeclareList ? serviceObject.rsOpFreeDeclareList[0] : null\r\n      }\r\n      if (serviceTypeId === 70) {\r\n        return serviceObject.rsOpDOAgent\r\n      }\r\n      if (serviceTypeId === 71) {\r\n        return serviceObject.rsOpClearAgent\r\n      }\r\n      if (serviceTypeId === 80) {\r\n        return serviceObject.rsOpWHS\r\n      }\r\n      if (serviceTypeId === 90) {\r\n        return serviceObject.rsOp3rdCert\r\n      }\r\n      if (serviceTypeId === 100) {\r\n        return serviceObject.rsOpINS\r\n      }\r\n      if (serviceTypeId === 101) {\r\n        return serviceObject.rsOpTrading\r\n      }\r\n      if (serviceTypeId === 102) {\r\n        return serviceObject.rsOpFumigation\r\n      }\r\n      if (serviceTypeId === 103) {\r\n        return serviceObject.rsOpCO\r\n      }\r\n      if (serviceTypeId === 104) {\r\n        return serviceObject.rsOpOther\r\n      }\r\n    },\r\n    async getCharges(val) {\r\n      let isReceivable = false\r\n      let serviceObject\r\n      await getRct(val.rctId).then(response => {\r\n        if (this.serviceTypeId) {\r\n          serviceObject = this.getServiceObject(this.serviceTypeId, response.data)\r\n        } else {\r\n          isReceivable = true\r\n          serviceObject = response.data.rsClientMessage\r\n        }\r\n      })\r\n\r\n      /* if (serviceObject) {\r\n        getChargeByServiceId(serviceObject.serviceId).then(response => {\r\n          this.charges = response.data\r\n          this.$nextTick(() => {\r\n            this.charges.forEach(row => {\r\n              this.$refs.chargesTable.toggleRowSelection(row)\r\n            })\r\n          })\r\n        })\r\n      } */\r\n      console.log(serviceObject)\r\n      if (serviceObject) {\r\n        this.charges = serviceObject.rsChargeList\r\n        this.$nextTick(() => {\r\n          this.charges.forEach(row => {\r\n            if (isReceivable) {\r\n              // 如果是应收，把结算单位改为客户\r\n              row.clearingCompanyId = this.clientId\r\n            }\r\n            this.$refs.chargesTable.toggleRowSelection(row)\r\n          })\r\n        })\r\n      }\r\n    },\r\n    handleSelectionChange(val) {\r\n      this.selectedCharges = val\r\n    },\r\n    search() {\r\n\r\n    },\r\n    copyCharge() {\r\n      this.$emit(\"returnCharge\", this.selectedCharges)\r\n    },\r\n    handleQueryPol(pols) {\r\n      this.queryParams.polIds = pols\r\n      this.getList()\r\n    },\r\n    handleQueryDestinationPortId(destinationPortIds) {\r\n      this.queryParams.destinationPortIds = destinationPortIds\r\n      this.getList()\r\n    },\r\n    handleQueryCarrier(carrier) {\r\n      this.queryParams.carrierId = carrier.carrierId\r\n      this.getList()\r\n    },\r\n    async getList() {\r\n      this.loading = true\r\n      this.queryParams.params.op = 1\r\n      await listRct(this.queryParams).then(response => {\r\n        this.tableData = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;AAiPA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,SAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AACA,IAAAK,cAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,MAAA,GAAAL,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAQ,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;MACAC,OAAA;MACAC,eAAA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,KAAA;QACAC,UAAA,OAAAC,UAAA,CAAAD,UAAA;QACAE,MAAA,QAAAD,UAAA,CAAAE,KAAA,QAAAF,UAAA,CAAAE,KAAA;QACAC,kBAAA,QAAAH,UAAA,CAAAI,iBAAA,QAAAJ,UAAA,CAAAI,iBAAA;QACAC,SAAA,OAAAL,UAAA,CAAAK,SAAA;QACAC,MAAA,MAAAC,GAAA;MACA;MACAC,SAAA;IACA;EACA;EACAC,OAAA;IACAC,OAAA,WAAAA,QAAAC,EAAA;MACA,IAAAA,EAAA;QACA,IAAAC,KAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAvB,IAAA,CAAAwB,cAAA,CAAAC,MAAA,WAAAC,OAAA;UAAA,OAAAA,OAAA,CAAAC,OAAA,IAAAP,EAAA;QAAA;QACA,IAAAC,KAAA;UACA,OAAAA,KAAA,CAAAO,oBAAA,GAAAP,KAAA,CAAAQ,oBAAA,GAAAR,KAAA,CAAAS,cAAA;QACA;MACA;IACA;IACAC,aAAA,WAAAA,cAAAX,EAAA;MAEA,KAAAY,OAAA;IACA;IACAjC,WAAA,WAAAA,YAAA;MAAA,IAAAkC,KAAA;MACA,SAAAX,MAAA,CAAAC,KAAA,CAAAvB,IAAA,CAAAC,WAAA,CAAAiC,MAAA,cAAAZ,MAAA,CAAAC,KAAA,CAAAvB,IAAA,CAAAmC,SAAA,CAAAC,OAAA;QACAC,cAAA,CAAAC,QAAA,mBAAAC,IAAA;UACAN,KAAA,CAAAhC,WAAA,GAAAgC,KAAA,CAAAX,MAAA,CAAAC,KAAA,CAAAvB,IAAA,CAAAC,WAAA;QACA,CACA;MACA;QACA,KAAAA,WAAA,QAAAqB,MAAA,CAAAC,KAAA,CAAAvB,IAAA,CAAAC,WAAA;MACA;IACA;IACAuC,iBAAA,WAAAA,kBAAAC,IAAA;MACA;QACArB,EAAA,EAAAqB,IAAA,CAAA3B,SAAA;QACA4B,KAAA,GAAAD,IAAA,CAAAE,gBAAA,WAAAF,IAAA,CAAAE,gBAAA,gBAAAF,IAAA,CAAAG,gBAAA,WAAAH,IAAA,CAAAG,gBAAA,gBAAAH,IAAA,CAAAI,aAAA,WAAAJ,IAAA,CAAAI,aAAA,eAAAC,iBAAA,CAAAC,YAAA,EAAAN,IAAA,CAAAE,gBAAA,WAAAF,IAAA,CAAAE,gBAAA,gBAAAF,IAAA,CAAAG,gBAAA,WAAAH,IAAA,CAAAG,gBAAA;MACA;IACA;IACAI,QAAA,EAAAA,iBAAA;IACAC,gBAAA,WAAAA,iBAAAC,aAAA,EAAAC,aAAA;MACA,IAAAD,aAAA;QACA,OAAAC,aAAA,CAAAC,cAAA,GAAAD,aAAA,CAAAC,cAAA;MACA;MACA,IAAAF,aAAA;QACA,OAAAC,aAAA,CAAAE,cAAA,GAAAF,aAAA,CAAAE,cAAA;MACA;MACA,IAAAH,aAAA;QACA,OAAAC,aAAA,CAAAG,WAAA,GAAAH,aAAA,CAAAG,WAAA;MACA;MACA,IAAAJ,aAAA;QACA,OAAAC,aAAA,CAAAI,WAAA;MACA;MACA,IAAAL,aAAA;QACA,OAAAC,aAAA,CAAAK,WAAA;MACA;MACA,IAAAN,aAAA;QACA,OAAAC,aAAA,CAAAM,WAAA;MACA;MACA,IAAAP,aAAA;QACA,OAAAC,aAAA,CAAAO,iBAAA,GAAAP,aAAA,CAAAO,iBAAA;MACA;MACA,IAAAR,aAAA;QACA,OAAAC,aAAA,CAAAQ,iBAAA,GAAAR,aAAA,CAAAQ,iBAAA;MACA;MACA,IAAAT,aAAA;QACA,OAAAC,aAAA,CAAAS,kBAAA,GAAAT,aAAA,CAAAS,kBAAA;MACA;MACA,IAAAV,aAAA;QACA,OAAAC,aAAA,CAAAU,mBAAA,GAAAV,aAAA,CAAAU,mBAAA;MACA;MACA,IAAAX,aAAA;QACA,OAAAC,aAAA,CAAAW,WAAA;MACA;MACA,IAAAZ,aAAA;QACA,OAAAC,aAAA,CAAAY,cAAA;MACA;MACA,IAAAb,aAAA;QACA,OAAAC,aAAA,CAAAa,OAAA;MACA;MACA,IAAAd,aAAA;QACA,OAAAC,aAAA,CAAAc,WAAA;MACA;MACA,IAAAf,aAAA;QACA,OAAAC,aAAA,CAAAe,OAAA;MACA;MACA,IAAAhB,aAAA;QACA,OAAAC,aAAA,CAAAgB,WAAA;MACA;MACA,IAAAjB,aAAA;QACA,OAAAC,aAAA,CAAAiB,cAAA;MACA;MACA,IAAAlB,aAAA;QACA,OAAAC,aAAA,CAAAkB,MAAA;MACA;MACA,IAAAnB,aAAA;QACA,OAAAC,aAAA,CAAAmB,SAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAAC,GAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,IAAAC,YAAA,EAAA5B,aAAA;QAAA,WAAAyB,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAL,YAAA;cAAAG,QAAA,CAAAE,IAAA;cAAA,OAEA,IAAAC,WAAA,EAAAb,GAAA,CAAAc,KAAA,EAAA/C,IAAA,WAAAgD,QAAA;gBACA,IAAAd,MAAA,CAAAvB,aAAA;kBACAC,aAAA,GAAAsB,MAAA,CAAAxB,gBAAA,CAAAwB,MAAA,CAAAvB,aAAA,EAAAqC,QAAA,CAAAvF,IAAA;gBACA;kBACA+E,YAAA;kBACA5B,aAAA,GAAAoC,QAAA,CAAAvF,IAAA,CAAAwF,eAAA;gBACA;cACA;YAAA;cAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;cACAC,OAAA,CAAAC,GAAA,CAAAvC,aAAA;cACA,IAAAA,aAAA;gBACAsB,MAAA,CAAAvE,OAAA,GAAAiD,aAAA,CAAAwC,YAAA;gBACAlB,MAAA,CAAAmB,SAAA;kBACAnB,MAAA,CAAAvE,OAAA,CAAA2F,OAAA,WAAAC,GAAA;oBACA,IAAAf,YAAA;sBACA;sBACAe,GAAA,CAAAC,iBAAA,GAAAtB,MAAA,CAAAuB,QAAA;oBACA;oBACAvB,MAAA,CAAAwB,KAAA,CAAAC,YAAA,CAAAC,kBAAA,CAAAL,GAAA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAZ,QAAA,CAAAkB,IAAA;UAAA;QAAA,GAAAtB,OAAA;MAAA;IACA;IACAuB,qBAAA,WAAAA,sBAAA7B,GAAA;MACA,KAAArE,eAAA,GAAAqE,GAAA;IACA;IACA8B,MAAA,WAAAA,OAAA,GAEA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,KAAA,sBAAArG,eAAA;IACA;IACAsG,cAAA,WAAAA,eAAAC,IAAA;MACA,KAAAtG,WAAA,CAAAM,MAAA,GAAAgG,IAAA;MACA,KAAA1E,OAAA;IACA;IACA2E,4BAAA,WAAAA,6BAAA/F,kBAAA;MACA,KAAAR,WAAA,CAAAQ,kBAAA,GAAAA,kBAAA;MACA,KAAAoB,OAAA;IACA;IACA4E,kBAAA,WAAAA,mBAAAxE,OAAA;MACA,KAAAhC,WAAA,CAAAU,SAAA,GAAAsB,OAAA,CAAAtB,SAAA;MACA,KAAAkB,OAAA;IACA;IACAA,OAAA,WAAAA,QAAA;MAAA,IAAA6E,MAAA;MAAA,WAAAnC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAiC,SAAA;QAAA,WAAAlC,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cACAyB,MAAA,CAAAI,OAAA;cACAJ,MAAA,CAAAzG,WAAA,CAAAW,MAAA,CAAAmG,EAAA;cAAAF,SAAA,CAAA5B,IAAA;cAAA,OACA,IAAA+B,YAAA,EAAAN,MAAA,CAAAzG,WAAA,EAAAmC,IAAA,WAAAgD,QAAA;gBACAsB,MAAA,CAAA5F,SAAA,GAAAsE,QAAA,CAAA6B,IAAA;gBACAP,MAAA,CAAAQ,KAAA,GAAA9B,QAAA,CAAA8B,KAAA;gBACAR,MAAA,CAAAI,OAAA;cACA;YAAA;YAAA;cAAA,OAAAD,SAAA,CAAAZ,IAAA;UAAA;QAAA,GAAAU,QAAA;MAAA;IACA;EACA;AACA;AAAAQ,OAAA,CAAA3C,OAAA,GAAA4C,QAAA"}]}