{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\bankRecordStatistics.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bankrecord\\bankRecordStatistics.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_bankrecord", "require", "_index", "_interopRequireDefault", "_vueTreeselect", "_js<PERSON><PERSON>yin", "_store", "_currency", "_rsCharge", "_exchangerate", "_rich", "_log", "_moment", "_request", "_imgPreview", "_auth", "_rct", "_reimburse", "name", "components", "ImgPreview", "Treeselect", "CompanySelect", "data", "writeOffList", "reimburseList", "size", "$store", "state", "app", "salesId", "belongList", "staffList", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "bankrecordList", "title", "open", "queryParams", "params", "pageNum", "pageSize", "isRecievingOrPaying", "sqdPaymentTitleCode", "bankAccountCode", "clearingCompanyId", "sqdClearingCompanyShortname", "chargeTypeId", "chargeDescription", "bankCurrencyCode", "actualBankRecievedAmount", "actualBankPaidAmount", "bankRecievedHandlingFee", "bankPaidHandlingFee", "bankRecievedExchangeLost", "bankPaidExchangeLost", "sqdBillRecievedAmount", "sqdBillPaidAmount", "billRecievedWriteoffAmount", "billPaidWriteoffAmount", "sqdBillRecievedWriteoffBalance", "sqdBillPaidWriteoffBalance", "writeoffStatus", "bankRecordTime", "paymentTypeCode", "voucherNo", "bankRecordRemark", "bankRecordByStaffId", "bankRecordUpdateTime", "isBankRecordLocked", "isWriteoffLocked", "sqdChargeIdList", "sqdRaletiveRctList", "sqdRaletiveInvoiceList", "sqdRsStaffId", "writeoffRemark", "writeoffStaffId", "writeoffTime", "timeArr", "statisticsList", "form", "searchAble", "writeoffType", "rules", "required", "message", "trigger", "statisticsOpen", "totalRecievedUSD", "totalRecievedRMB", "totalPaidUSD", "totalPaidRMB", "exchangeRate", "paymentTitleBalances", "timeStatistics", "add", "upload", "isUploading", "updateSupport", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "showDetail", "selected<PERSON><PERSON>ges", "totalAmount", "selectedAmount", "selectedBalanceAmount", "selectedBalanceAmountRMB", "selectedBalanceAmountUSD", "loadingCharge", "staffId", "alreadyWriteoffList", "turnBackWriteoffList", "showCompany", "companyList", "bankSlipPreview", "imageFile", "hedgingData", "exchangeRateList", "watch", "n", "formChargeTypeId", "chargeTypeList", "for<PERSON>ach", "item", "children", "v", "parentId", "formActualBankRecievedAmount", "currency", "value", "formBankRecievedHandlingFee", "formBankRecievedExchangeLost", "subtract", "formActualBankPaidAmount", "formBankPaidHandlingFee", "formBankPaidExchangeLost", "formSqdBillRecievedAmount", "formSqdBillPaidAmount", "formBillRecievedWriteoffAmount", "formBillPaidWriteoffAmount", "handler", "newVal", "oldVal", "_this", "isHedging", "val", "map", "subtotalStr", "subtotal", "toString", "decimalPlaces", "includes", "split", "length", "calculatedValue", "writeoffFromBankBalance", "divide", "writeoffFromDnBalance", "Number", "toFixed", "precision", "dnCurrencyCode", "sqdDnCurrencyBalance", "dnUnitRate", "multiply", "dnAmount", "deep", "created", "getList", "store", "dispatch", "redisList", "mounted", "_this2", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "selectListExchangerate", "sent", "stop", "beforeMount", "loadStaff", "methods", "carousel", "pics", "openCarousel", "$message", "info", "getWriteOffType", "type", "getCompanyCharges", "getReimburseCharges", "_this3", "listWriteOffReimburse", "bankRecordId", "then", "response", "rows", "$nextTick", "reimbursePrice", "$refs", "writeOffReimburseTable", "toggleRowSelection", "sort", "a", "b", "handleExchangeRateInput", "row", "newValue", "exchangeRateShow", "isNumeric", "isNaN", "validateExchangeRate", "minRate", "maxRate", "parseFloat", "error", "concat", "match", "rate", "deleteBankSlip", "_this4", "_callee2", "_callee2$", "_context2", "delImg", "slipFile", "t0", "updateBankrecord", "bankRecordNo", "handleSearch", "parseTime", "updateSlipSatus", "_this5", "clearReceiveOrPay", "slipConfirmed", "success", "writeOffConfirm", "_this6", "moment", "format", "$modal", "msgSuccess", "cancelledRows", "filter", "some", "selected", "chargeId", "turnBackWriteoff", "rsChargeList", "midChargeBankWriteoffs", "clearingCurrencyCode", "midChargeBankWriteoff", "exchangeRateShowing", "dnBasicRate", "push", "chargeWriteOff", "midChargeBankWriteoffList", "rctWriteoff", "formatterCurrency", "currencyType", "separator", "symbol", "invertSelection", "autoSelection", "addHedging", "_this7", "isReceiving", "hedgingType", "findHedging", "_objectSpread2", "writeOff", "projectRemove", "print", "_this8", "_callee3", "_iterator", "_step", "result", "_callee3$", "_context3", "selectListCharge", "_createForOfIteratorHelper2", "s", "done", "getExchangeRate", "abrupt", "currencyWithPrecision", "e", "f", "finish", "setTimeout", "midChargeBankId", "isAccountConfirmed", "writeOffChargeTable", "arguments", "undefined", "verify", "_this9", "verifyId", "verifyTime", "user", "sid", "_this10", "listBankrecord", "cancel", "reset", "invoiceNo", "chargeType", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this11", "text", "status", "$confirm", "changeStatus", "catch", "handleHedgingSelectionChange", "selection", "handleAddHedging", "_this12", "_callee4", "newRow", "_callee4$", "_context4", "handleSelectionChange", "_this13", "indexOf", "sqdDnCurrencyBalanceShow", "dbclick", "_this14", "statisticsTimeSelect", "requestMap", "day", "searchAccountFundStatistics", "month", "year", "startTime", "endTime", "accountFundStatistics", "_this15", "getAccountFundStatistics", "_iterator2", "_step2", "localCurrency", "overseaCurrency", "validFrom", "Date", "validTo", "settleRate", "base", "err", "totalRecieved", "totalPaid", "calculatePaymentTitleBalances", "summaryMap", "Map", "titleCode", "received", "paid", "has", "set", "<PERSON><PERSON><PERSON>", "sqdPaymentTitleName", "RMB", "balance", "USD", "record", "get", "Array", "from", "values", "handleImport", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "clearFiles", "msg", "download", "submitFileForm", "submit", "handleAdd", "handleUpdate", "_this16", "getBankrecord", "submitForm", "_this17", "validate", "_ref", "_callee5", "valid", "_callee5$", "_context5", "console", "log", "uploadImage", "addBankrecord", "_x", "apply", "_this18", "Promise", "resolve", "reject", "customHttpRequest", "onSuccess", "handleSuccess", "onError", "handleError", "handleDelete", "_this19", "bankRecordIds", "delBankrecord", "handleExport", "getTime", "staffNormalizer", "node", "l", "staff", "staffFamilyLocalName", "staffGivingLocalName", "role", "roleLocalName", "pinyin", "getFullChars", "dept", "deptLocalName", "staffCode", "staffGivingEnName", "roleId", "id", "label", "isDisabled", "deptId", "loadSales", "_this20", "salesList", "_this21", "allRsStaffList", "handledbClick", "selectCompany", "company", "companyId", "companyShortName", "valueDate", "_this22", "_callee6", "re", "_iterator3", "_step3", "_callee6$", "_context6", "buyRate", "sellRate", "getBillDataExchangeRate", "_callee7", "_iterator4", "_step4", "_callee7$", "_context7", "chargeCurrencyCode", "_this23", "_callee8", "_callee8$", "_context8", "getName", "rsStaff", "staffShortName", "selectStaff", "checkSelectable", "handleDialogOpened", "_this24", "treeSelectInput", "treeSelect", "getInputElement", "focus", "isRecievingOrPayingNormalizer", "selectBankAccount", "sqdBelongToCompanyCode", "options", "_this25", "formData", "FormData", "append", "request", "method", "handleChange", "extension", "substring", "lastIndexOf", "newFileName", "File", "raw", "handleWriteoffChange", "computed", "receiveRate", "paidRate", "isLocked", "isBankSlipConfirmed", "exports", "_default"], "sources": ["src/views/system/bankrecord/bankRecordStatistics.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"流水\" prop=\"isRecievingOrPaying\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordNo\"\r\n              clearable\r\n              placeholder=\"流水号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"单号\" prop=\"isRecievingOrPaying\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdRaletiveRctList\"\r\n              clearable\r\n              placeholder=\"收支\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"收支\" prop=\"isRecievingOrPaying\">\r\n            <el-select v-model=\"queryParams.isRecievingOrPaying\" clearable placeholder=\"商务审核标记\"\r\n                       style=\"width: 100%\" @change=\"handleQuery\"\r\n            >\r\n              <el-option label=\"收\" value=\"0\">收</el-option>\r\n              <el-option label=\"付\" value=\"1\">付</el-option>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"所属\" prop=\"sqdPaymentTitleCode\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdPaymentTitleCode\"\r\n              clearable\r\n              placeholder=\"所属公司\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"费用\" prop=\"sqdPaymentTitleCode\">\r\n            <tree-select :pass=\"queryParams.chargeTypeId\" :placeholder=\"'费用类型'\" :type=\"'chargeType'\"\r\n                         style=\"width: 100%\" @return=\"queryParams.chargeTypeId=$event\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"账户\" prop=\"bankAccountCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankAccountCode\"\r\n              clearable\r\n              placeholder=\"银行账户\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"公司\" prop=\"sqdClearingCompanyShortname\">\r\n            <el-input\r\n              v-model=\"queryParams.sqdClearingCompanyShortname\"\r\n              clearable\r\n              placeholder=\"结算公司简称\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"币种\" prop=\"bankCurrencyCode\">\r\n            <el-input\r\n              v-model=\"queryParams.bankCurrencyCode\"\r\n              clearable\r\n              placeholder=\"银行币种\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"时间\" prop=\"bankRecordTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            style=\"width:  100%\" type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n\r\n          <el-form-item label=\"录入\" prop=\"bankRecordByStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.bankRecordByStaffId\"\r\n              clearable\r\n              placeholder=\"银行流水录入人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"录入\" prop=\"bankRecordUpdateTime\">\r\n            <el-date-picker v-model=\"queryParams.bankRecordUpdateTime\"\r\n                            clearable\r\n                            placeholder=\"银行流水录入时间 ,\"\r\n                            style=\"width: 100%\" type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item label=\"销账\" prop=\"writeoffStaffId\">\r\n            <el-input\r\n              v-model=\"queryParams.writeoffStaffId\"\r\n              clearable\r\n              placeholder=\"销账人\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"销账\" prop=\"writeoffTime\">\r\n            <el-date-picker v-model=\"queryParams.writeoffTime\"\r\n                            clearable\r\n                            placeholder=\"销账时间\"\r\n                            style=\"width: 100%\" type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:edit']\"\r\n              :disabled=\"single\"\r\n              icon=\"el-icon-edit\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"handleUpdate\"\r\n            >修改\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleImport\"\r\n            >导入\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:bankrecord:add']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              icon=\"el-icon-data-analysis\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"accountFundStatistics\"\r\n            >账户资金统计\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"bankrecordList\" highlight-current-row\r\n                  stripe @selection-change=\"handleSelectionChange\" @row-dblclick=\"handledbClick\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"银行流水\" prop=\"bankRecordNo\"/>\r\n          <el-table-column align=\"center\" label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.isRecievingOrPaying == 1 ? \"付\" : \"收\" }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属公司\" prop=\"sqdPaymentTitleCode\"/>\r\n          <el-table-column align=\"center\" label=\"费用类型\" prop=\"chargeName\"/>\r\n          <el-table-column align=\"center\" label=\"结算公司\" prop=\"sqdClearingCompanyShortname\"/>\r\n          <el-table-column align=\"center\" label=\"银行时间\" prop=\"bankRecordTime\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.bankRecordTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"银行账户\" prop=\"bankAccountCode\"/>\r\n          <el-table-column align=\"center\" label=\"币种\" prop=\"bankCurrencyCode\"/>\r\n          <el-table-column align=\"center\" label=\"水单金额\" prop=\"slipAmount\"/>\r\n          <el-table-column align=\"center\" label=\"实收金额\" prop=\"actualBankRecievedAmount\"/>\r\n          <el-table-column align=\"center\" label=\"收款手续费\" prop=\"bankRecievedHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"实付金额\" prop=\"actualBankPaidAmount\"/>\r\n          <el-table-column align=\"center\" label=\"付款手续费\" prop=\"bankPaidHandlingFee\"/>\r\n          <el-table-column align=\"center\" label=\"销账状态 \" prop=\"writeoffStatus\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ scope.row.writeoffStatus == 1 ? \"=\" : (scope.row.writeoffStatus == 0 ? \"√\" : \"-\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"相关操作单号\" prop=\"sqdRaletiveRctList\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账人\" prop=\"writeoffStaffId\">\r\n            <template slot-scope=\"scope\">\r\n              {{ getName(scope.row.writeoffStaffId) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"销账备注\" prop=\"bankRecordRemark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"销账时间\" prop=\"writeoffTime\" width=\"80\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.writeoffTime, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:bankrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改记录公司账户出入账明细对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"title\"\r\n      :visible.sync=\"open\"\r\n      append-to-body\r\n      height=\"60%\"\r\n      width=\"60%\"\r\n      @close=\"showDetail=false\"\r\n    >\r\n      <el-form v-if=\"open\" ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"12\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"银行流水\" prop=\"voucherNo\">\r\n                <el-input :value=\"form.bankRecordNo\" class=\"disable-form\" disabled placeholder=\"银行流水\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"收支标志\" prop=\"isRecievingOrPaying\">\r\n                <treeselect ref=\"treeSelect\" v-model=\"form.isRecievingOrPaying\"\r\n                            :auto-focus=\"true\"\r\n                            :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\" :disable-branch-nodes=\"true\"\r\n                            :disable-fuzzy-matching=\"true\"\r\n                            :disabled=\"isLocked||isBankSlipConfirmed\" :flatten-search-results=\"true\"\r\n                            :normalizer=\"isRecievingOrPayingNormalizer\"\r\n                            :options=\"[{label:'实收',value:'0'},{label:'实付',value:'1'}]\" :show-count=\"true\"\r\n                            placeholder=\"选择收付信息\" @select=\"form.isRecievingOrPaying=$event.value\"\r\n                >\r\n                </treeselect>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item\r\n                label=\"结算公司\"\r\n                prop=\"clearingCompanyId\"\r\n              >\r\n                <company-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                                :disabled=\"isLocked||isBankSlipConfirmed\" :load-options=\"companyList\"\r\n                                :multiple=\"false\" :no-parent=\"true\"\r\n                                :pass=\"form.clearingCompanyId\" :placeholder=\"''\"\r\n                                @returnData=\"selectCompany($event)\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"费用描述\" prop=\"voucherNo\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <!--<el-input :class=\"isLocked?'disable-form':''\" :value=\"form.chargeType\" class=\"disable-form\"\r\n                              disabled\r\n                    />-->\r\n                    <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" :pass=\"form.chargeTypeId\"\r\n                                 :placeholder=\"'费用类型'\" :type=\"'chargeType'\"\r\n                                 style=\"width: 100%\" @return=\"form.chargeTypeId=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.chargeDescription\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                              placeholder=\"费用描述\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"银行账户\" prop=\"bankAccountCode\">\r\n                <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                             :disabled=\"isLocked||isBankSlipConfirmed\" :flat=\"false\" :multiple=\"false\"\r\n                             :pass=\"form.bankAccountCode\" :placeholder=\"'银行账户'\"\r\n                             :type=\"'companyAccount'\"\r\n                             @return=\"form.bankAccountCode=$event\" @returnData=\"selectBankAccount\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"水单金额\" prop=\"sqdBillRecievedAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                                 :disabled=\"isBankSlipConfirmed\"\r\n                                 :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.slipAmount\" :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                              :disabled=\"isBankSlipConfirmed\"\r\n                              placeholder=\"水单金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"水单文件\" prop=\"sqdBillRecievedAmount\">\r\n                <div>\r\n                  <div style=\"display: flex\">\r\n                    <el-upload\r\n                      v-if=\"form.slipConfirmed==0\"\r\n                      ref=\"bankSlipUpload\"\r\n                      :auto-upload=\"false\"\r\n                      :disabled=\"!form.bankRecordNo\"\r\n                      :http-request=\"customHttpRequest\"\r\n                      :on-change=\"handleChange\"\r\n                      :on-error=\"handleError\"\r\n                      :on-success=\"handleSuccess\"\r\n                      :show-file-list=\"false\"\r\n                      action=\"xxx\"\r\n                      class=\"upload-demo\" style=\"flex: 1\"\r\n                    >\r\n                      <el-button icon=\"el-icon-top-right\" style=\"color: rgb(103, 194, 58)\" type=\"text\"></el-button>\r\n                    </el-upload>\r\n                    <img-preview v-if=\"form.slipFile || (form.slipFile && form.slipConfirmed)\"\r\n                                 :scope=\"{row:{slipFile:form.slipFile}}\"\r\n                                 style=\"flex: 1\"\r\n                    />\r\n                    <el-button v-if=\"form.slipFile && form.slipConfirmed==0\" icon=\"el-icon-delete\"\r\n                               style=\"flex: 1;color: red\"\r\n                               type=\"text\"\r\n                               @click=\"deleteBankSlip(form)\"\r\n                    ></el-button>\r\n                  </div>\r\n                </div>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"11\">\r\n              <el-form-item label=\"相关单号\" prop=\"voucherNo\">\r\n                <el-input v-model=\"form.sqdRaletiveRctList\"\r\n                          :class=\"isBankSlipConfirmed?'disable-form':''\" :disabled=\"isBankSlipConfirmed\"\r\n                          placeholder=\"相关操作单号List\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"水单日期\" prop=\"sqdBillRecievedAmount\">\r\n                <el-date-picker\r\n                  v-model=\"form.slipDate\"\r\n                  :class=\"isBankSlipConfirmed?'disable-form':''\"\r\n                  :disabled=\"isBankSlipConfirmed\"\r\n                  clearable\r\n                  default-time=\"12:00:00\"\r\n                  placeholder=\"银行时间\"\r\n                  style=\"width: 100%\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"实收金额\" prop=\"actualBankRecievedAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                                 :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                                 :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankRecievedAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实收金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"实付金额\" prop=\"actualBankPaidAmount\">\r\n                <el-row>\r\n                  <el-col :span=\"10\">\r\n                    <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\" :pass=\"form.bankCurrencyCode\"\r\n                                 :placeholder=\"'币种'\" :type=\"'currency'\"\r\n                                 style=\"width: 100%\" @return=\"form.bankCurrencyCode=$event\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"14\">\r\n                    <el-input v-model=\"form.actualBankPaidAmount\" :class=\"isLocked?'disable-form':''\"\r\n                              :disabled=\"isLocked\" placeholder=\"实付金额\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"结算方式\" prop=\"paymentTypeCode\">\r\n                <tree-select :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                             :flat=\"false\" :multiple=\"false\" :pass=\"form.paymentTypeCode\"\r\n                             :placeholder=\"'结算方式'\"\r\n                             :type=\"'paymentChannelsCode'\"\r\n                             @return=\"form.paymentTypeCode=$event\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"凭证号\" prop=\"voucherNo\">\r\n                <el-input v-model=\"form.voucherNo\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"凭证号\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item label=\"银行备注\" prop=\"bankRecordRemark\">\r\n                <el-input v-model=\"form.bankRecordRemark\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"银行备注\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item label=\"银行时间\" prop=\"bankRecordTime\">\r\n                <el-date-picker\r\n                  v-model=\"form.bankRecordTime\"\r\n                  :class=\"isLocked?'disable-form':''\"\r\n                  :disabled=\"isLocked\"\r\n                  clearable\r\n                  default-time=\"12:00:00\"\r\n                  placeholder=\"银行时间\"\r\n                  style=\"width: 100%\"\r\n                  type=\"datetime\"\r\n                  value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n                >\r\n                </el-date-picker>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <!--实收-->\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"手续费\" prop=\"bankRecievedExchangeLost\">\r\n                <el-input v-model=\"form.bankRecievedHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收款记账\" prop=\"sqdBillRecievedAmount\">\r\n                <el-input v-model=\"form.sqdBillRecievedAmount\" :class=\"'disable-form'\" disabled\r\n                          placeholder=\"收款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收账已销\" prop=\"billRecievedWriteoffAmount\">\r\n                <el-input v-model=\"form.billRecievedWriteoffAmount\" :class=\"'disable-form'\"\r\n                          disabled placeholder=\"收账已销\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" label=\"收款损益\" prop=\"billRecievedWriteoffAmount\">\r\n                <el-input v-model=\"form.bankRecievedExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                          :disabled=\"!isLocked\" placeholder=\"收款损益\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='0'\" :disabled=\"isLocked\" label=\"收账未销\"\r\n                            prop=\"sqdBillRecievedWriteoffBalance\"\r\n              >\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillRecievedWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"收账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillRecievedWriteoffBalance===form.sqdBillRecievedAmount?'-':form.sqdBillRecievedWriteoffBalance===0?'√':'='\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <!--实付-->\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"手续费\" prop=\"bankPaidExchangeLost\">\r\n                <el-input v-model=\"form.bankPaidHandlingFee\" placeholder=\"手续费\"/>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付款记账\" prop=\"sqdBillPaidAmount\">\r\n                <el-input v-model=\"form.sqdBillPaidAmount\" class=\"disable-form\" disabled\r\n                          placeholder=\"付款记账\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账已销\" prop=\"billPaidWriteoffAmount\">\r\n                <el-input v-model=\"form.billPaidWriteoffAmount\" :class=\"'disable-form'\" disabled\r\n                          placeholder=\"付账已销\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"6\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付款汇损\" prop=\"bankPaidExchangeLost\">\r\n                <el-input v-model=\"form.bankPaidExchangeLost\" :class=\"isLocked?'':'disable-form'\"\r\n                          :disabled=\"!isLocked\" placeholder=\"付款损益\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"4\">\r\n              <el-form-item v-if=\"form.isRecievingOrPaying==='1'\" label=\"付账未销\" prop=\"sqdBillPaidWriteoffBalance\">\r\n                <el-row>\r\n                  <el-col :span=\"20\">\r\n                    <el-input v-model=\"form.sqdBillPaidWriteoffBalance\" :class=\"'disable-form'\" disabled\r\n                              placeholder=\"付账未销\"\r\n                    />\r\n                  </el-col>\r\n                  <el-col :span=\"4\">\r\n                    <el-input\r\n                      :class=\"'disable-form'\"\r\n                      :value=\"form.sqdBillPaidAmount===form.sqdBillPaidWriteoffBalance?'-':form.sqdBillPaidWriteoffBalance===0?'√':'='\"\r\n                    />\r\n                  </el-col>\r\n                </el-row>\r\n              </el-form-item>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col :span=\"9\">\r\n              <el-form-item label=\"发票号码\" prop=\"voucherNo\">\r\n                <el-input v-model=\"form.invoiceNo\" :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                          placeholder=\"发票号码\"\r\n                />\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"5\">\r\n              <el-form-item label=\"所属员工\">\r\n                <el-select v-model=\"form.sqdRsStaffId\" :class=\"isLocked||isBankSlipConfirmed?'disable-form':''\"\r\n                           :disabled=\"isLocked||isBankSlipConfirmed\"\r\n                           filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"staff in staffList\"\r\n                    :key=\"staff.staffId\"\r\n                    :label=\"staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                    :value=\"staff.staffId\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n            </el-col>\r\n            <el-col :span=\"1.5\">\r\n              <el-button :class=\"isLocked?'disable-form':''\" :disabled=\"isLocked\"\r\n                         style=\"float: right\" type=\"primary\"\r\n                         @click=\"submitForm\"\r\n              >{{ \"保存\" }}\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"2.5\">\r\n              <el-button v-if=\"this.form.bankRecordId !== null\"\r\n                         :icon=\"form.slipConfirmed==1?'el-icon-check':''\" type=\"primary\"\r\n                         @click=\"updateSlipSatus\"\r\n              >\r\n                {{ form.slipConfirmed == 1 ? \"水单已确认\" : \"确认水单\" }}\r\n              </el-button>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <el-tooltip placement=\"top\">\r\n                <div slot=\"content\">\r\n                  <div v-if=\"form.isBankRecordLocked == 1 \">\r\n                    <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                      {{ (form.verifyId ? getName(form.verifyId) : \"\") }}\r\n                    </div>\r\n                    <div style=\"float: right;color: #b7bbc2;  font-size: 12px;\">\r\n                      {{ parseTime(form.verifyTime, \"{y}-{m}-{d}\") }}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                <el-button :icon=\"form.isBankRecordLocked==1?'el-icon-check':''\" type=\"primary\"\r\n                           @click=\"verify\"\r\n                >\r\n                  {{ form.isBankRecordLocked == 1 ? \"流水已审核\" : \"审核流水\" }}\r\n                </el-button>\r\n              </el-tooltip>\r\n            </el-col>\r\n            <el-col :span=\"3\">\r\n              <el-button v-if=\"searchAble\" :disabled=\"!isLocked\" :loading=\"loadingCharge\" style=\"float: right\"\r\n                         type=\"primary\" @click=\"getWriteOffType(writeoffType)\"\r\n              >\r\n                调取相关费用明细\r\n              </el-button>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n      </el-form>\r\n      <el-table\r\n        v-if=\"showDetail && writeoffType==='chargeType'\"\r\n        ref=\"writeOffChargeTable\"\r\n        :data=\"writeOffList\"\r\n        border\r\n        max-height=\"315px\"\r\n        size=\"mini\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n\r\n      >\r\n        <el-table-column type=\"index\" width=\"20\"/>\r\n        <el-table-column\r\n          :selectable=\"checkSelectable\"\r\n          type=\"selection\"\r\n          width=\"35\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"审核\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"30\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.isAccountConfirmed === \"1\" ? \"√\" : \"-\" }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"Rct号\"\r\n          prop=\"sqdRctNo\"\r\n          width=\"150\"\r\n          sortable\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"费用名称\"\r\n          prop=\"chargeName\"\r\n          width=\"80\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"备注\"\r\n          prop=\"address\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"收付标志\"\r\n          prop=\"dnCurrencyCode\"\r\n          width=\"100\"\r\n          sortable\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.isRecievingOrPaying == 1 ? (\"应付\" + scope.row.dnCurrencyCode) : (\"应收\" + scope.row.dnCurrencyCode)\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"应收/付金额\"\r\n          prop=\"subtotal\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"销账余额\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.sqdDnCurrencyBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"本次拟销账金额\"\r\n          prop=\"sqdDnCurrencyBalance\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.writeoffFromDnBalance }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"汇率展示\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-input v-model=\"scope.row.exchangeRateShow\"\r\n                      @blur=\"(newValue) => validateExchangeRate(scope.row)\"\r\n                      @input=\"(newValue) => handleExchangeRateInput(scope.row, newValue)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"right\"\r\n          label=\"折算记账金额\"\r\n          prop=\"dnCurrencyCode\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            <el-input-number\r\n              v-model=\"scope.row.writeoffFromBankBalance\"\r\n              :controls=\"false\"\r\n              :min=\"0\"\r\n              :precision=\"2\"\r\n              :step=\"0.01\"\r\n              autocomplete=\"off\"\r\n              style=\"width: 100%\"\r\n              @change=\"(value) => handleWriteoffChange(scope.row, value)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账人\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ getName(scope.row.midChargeBankWriteoff.writeoffStaffId) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"销账时间\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ scope.row.midChargeBankWriteoff.writeoffTime }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column\r\n          align=\"center\"\r\n          label=\"销账状态\"\r\n          width=\"50\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{\r\n              scope.row.sqdDnCurrencyBalance == 0 ? \"√\" : scope.row.sqdDnCurrencyBalance > 0 ? \"=\" : \"-\"\r\n            }}\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-table\r\n        v-show=\"showDetail && writeoffType==='reimburseType'\"\r\n        ref=\"writeOffReimburseTable\"\r\n        :data=\"reimburseList\"\r\n        border\r\n        max-height=\"315px\"\r\n        size=\"mini\"\r\n        style=\"width: 100%\"\r\n        @selection-change=\"handleSelectionChange\"\r\n      >\r\n        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n        <el-table-column align=\"center\" label=\"ID\" prop=\"reimburseId\" width=\"38\"/>\r\n        <el-table-column align=\"center\" label=\"报销人\" prop=\"staffName\" width=\"68\"/>\r\n        <el-table-column align=\"center\" label=\"费用类型\" prop=\"chargeTypeName\" show-tooltip-when-overflow width=\"88\"/>\r\n        <el-table-column align=\"center\" label=\"报销概要\" prop=\"reimburseTitle\" show-tooltip-when-overflow width=\"68\"/>\r\n        <el-table-column align=\"center\" label=\"报销详情\" prop=\"reimburseContent\" show-tooltip-when-overflow/>\r\n        <el-table-column align=\"center\" label=\"参与人员\" prop=\"reimburseParticipation\" show-tooltip-when-overflow\r\n                         width=\"170\"\r\n        />\r\n        <el-table-column align=\"center\" label=\"报销金额\" prop=\"reimbursePrice\" width=\"100\">\r\n          <template slot-scope=\"scope\">\r\n            <h1 style=\"margin: 0;font-weight:bold;\">\r\n              {{ scope.row.reimbursePrice }}\r\n            </h1>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"日期\" prop=\"happenDate\" width=\"142\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 style=\"margin: 0\">{{ \"发生日期：\" + parseTime(scope.row.happenDate, \"{y}-{m}-{d}\") }}</h6>\r\n            <h6 style=\"margin: 0\">{{ \"申请日期：\" + parseTime(scope.row.applyDate, \"{y}-{m}-{d}\") }}</h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"部门审批\" prop=\"deptConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==1&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.deptConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.deptReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.deptReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.deptConfirmed==0&&scope.row.deptReimburseConfirm!=null&&scope.row.deptReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"人事审批\" prop=\"hrConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==1&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.hrConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.hrReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.hrReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.hrConfirmed==0&&scope.row.hrReimburseConfirm!=null&&scope.row.hrReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"总经办审批\" prop=\"ceoConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==1&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.ceoConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.ceoReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.ceoReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.ceoConfirmed==0&&scope.row.ceoReimburseConfirm!=null&&scope.row.ceoReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"财务确认\" prop=\"financeConfirmed\" width=\"88\">\r\n          <template slot-scope=\"scope\">\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==1&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeConfirmedName }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ parseTime(scope.row.financeConfirmedDate, \"{y}-{m}-{d}\") }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已审批\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==0\"\r\n            >\r\n              <el-tooltip :open-delay=\"500\"\r\n                          placement=\"top\"\r\n              >\r\n                <div slot=\"content\">\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeReimburseConfirm.staffName }}</h6>\r\n                  <h6 style=\"margin: 0\">\r\n                    {{\r\n                      parseTime(scope.row.financeReimburseConfirm.createDate, \"{y}-{m}-{d}\")\r\n                    }}</h6>\r\n                  <h6 style=\"margin: 0\">{{ scope.row.financeReimburseConfirm.reimburseContent }}</h6>\r\n                </div>\r\n                <div>\r\n                  {{ \"已驳回\" }}\r\n                </div>\r\n              </el-tooltip>\r\n            </div>\r\n            <div v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm==null\">\r\n              {{ \"未审批\" }}\r\n            </div>\r\n            <div\r\n              v-if=\"scope.row.financeConfirmed==0&&scope.row.financeReimburseConfirm!=null&&scope.row.financeReimburseConfirm.reimburseConfirm==1\"\r\n            >\r\n              {{ \"已驳回\" }}\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"单据附件\" width=\"70\">\r\n          <template slot-scope=\"scope\">\r\n            <el-button :size=\"size\" style=\"padding: 0\" type=\"info\" @click=\"carousel(scope.row.reimburseAppendix)\">\r\n              查看图片\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow width=\"100\"/>\r\n      </el-table>\r\n      <!--总计-->\r\n      <div v-if=\"showDetail\" class=\"total\">\r\n        <div style=\"width: 30%;\">全部总计: {{ totalAmount }}</div>\r\n        <div style=\"width: 30%;\">已选总计:\r\n          {{\r\n            this.form.isRecievingOrPaying == 0 ? this.form.billRecievedWriteoffAmount : this.form.billPaidWriteoffAmount\r\n          }}\r\n        </div>\r\n        <div style=\"width: 30%;\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              已选余额总计：\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div>\r\n                {{ \"RMB \" + selectedBalanceAmountRMB }}\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <div>\r\n                {{ \"USD \" + selectedBalanceAmountUSD }}\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </div>\r\n      </div>\r\n\r\n      <!--按钮区-->\r\n      <div v-if=\"showDetail\" class=\"table-btn-group\">\r\n        <div class=\"table-btn-left\">\r\n          <el-button type=\"primary\" @click=\"invertSelection\">反选</el-button>\r\n          <el-button type=\"primary\" @click=\"autoSelection\">智选</el-button>\r\n          <el-popover\r\n            placement=\"bottom\"\r\n            title=\"添加对冲费用\"\r\n            trigger=\"click\"\r\n            width=\"800\"\r\n          >\r\n            <el-table :data=\"hedgingData\" @selection-change=\"handleHedgingSelectionChange\">\r\n              <el-table-column type=\"index\" width=\"20\"/>\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"审核\"\r\n                prop=\"sqdRctNo\"\r\n                width=\"30\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row.isAccountConfirmed === \"1\" ? \"√\" : \"-\" }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"Rct号\"\r\n                prop=\"sqdRctNo\"\r\n                width=\"150\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"费用名称\"\r\n                prop=\"chargeName\"\r\n                width=\"80\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"备注\"\r\n                prop=\"address\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"收付标志\"\r\n                prop=\"dnCurrencyCode\"\r\n                width=\"100\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.row.isRecievingOrPaying == 1 ? (\"应付\" + scope.row.dnCurrencyCode) : (\"应收\" + scope.row.dnCurrencyCode)\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                align=\"right\"\r\n                label=\"应收/付金额\"\r\n                prop=\"subtotal\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                align=\"right\"\r\n                label=\"销账余额\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{ scope.row.sqdDnCurrencyBalance }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"销账状态\"\r\n                width=\"50\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  {{\r\n                    scope.row.sqdDnCurrencyBalance == 0 ? \"√\" : scope.row.sqdDnCurrencyBalance > 0 ? \"=\" : \"-\"\r\n                  }}\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"操作\"\r\n                width=\"50\"\r\n              >\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" @click=\"handleAddHedging(scope.row)\">[添加]</el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n            <el-button slot=\"reference\" type=\"primary\" @click=\"addHedging\">增加对冲</el-button>\r\n          </el-popover>\r\n          <el-button type=\"primary\" @click=\"projectRemove\">项目去除</el-button>\r\n          <el-button type=\"primary\" @click=\"print\">打印</el-button>\r\n        </div>\r\n        <div class=\"table-btn-right\">\r\n          <el-button style=\"float: right\" type=\"primary\" @click=\"writeOffConfirm\">确定销账</el-button>\r\n        </div>\r\n      </div>\r\n      <el-dialog\r\n        v-dialogDrag\r\n        v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n        :visible.sync=\"bankSlipPreview\"\r\n        append-to-body destroy-on-close\r\n        height=\"50%\"\r\n        width=\"50%\"\r\n      >\r\n        <el-image :src=\"form.slipFile\" style=\"margin-top: 20px;\"/>\r\n      </el-dialog>\r\n    </el-dialog>\r\n\r\n    <!--    // excel 上传导入组件-->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"upload.title\"\r\n      :visible.sync=\"upload.open\"\r\n      append-to-body width=\"400px\"\r\n    >\r\n      <el-upload\r\n        ref=\"upload\"\r\n        :action=\"upload.url + '?chargeTypeId=' + 2\"\r\n        :auto-upload=\"false\"\r\n        :disabled=\"upload.isUploading\"\r\n        :headers=\"upload.headers\"\r\n        :limit=\"1\"\r\n        :on-progress=\"handleFileUploadProgress\"\r\n        :on-success=\"handleFileSuccess\"\r\n        accept=\".xlsx, .xls\"\r\n        drag\r\n      >\r\n        <i class=\"el-icon-upload\"></i>\r\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\r\n        <div slot=\"tip\" class=\"el-upload__tip text-center\">\r\n          <span>仅允许导入xls、xlsx格式文件。</span>\r\n          <!--<el-link :underline=\"false\" style=\"font-size:12px;vertical-align: baseline;\" type=\"primary\"\r\n                   @click=\"importTemplate\"\r\n          >下载模板\r\n          </el-link>-->\r\n        </div>\r\n      </el-upload>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\r\n        <el-button @click=\"upload.open = false\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!--查看账户资金统计-->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n      :visible.sync=\"statisticsOpen\"\r\n      append-to-body\r\n      height=\"40%\"\r\n      width=\"40%\"\r\n    >\r\n      <el-row>\r\n        <el-form class=\"edit\" label-width=\"50px\">\r\n          <el-col :span=\"5\">\r\n            <el-radio-group v-model=\"timeStatistics\">\r\n              <el-radio :label=\"1\">筛选天</el-radio>\r\n              <el-radio :label=\"2\">筛选月</el-radio>\r\n              <el-radio :label=\"3\">筛选年</el-radio>\r\n              <el-radio :label=\"4\">筛选范围</el-radio>\r\n            </el-radio-group>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===1\" :span=\"8\">\r\n            <el-date-picker v-model=\"queryParams.params.day\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"date\"\r\n                            value-format=\"yyyy-MM-dd\"\r\n                            @change=\"statisticsTimeSelect\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===2\" :span=\"8\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.params.month\"\r\n              placeholder=\"选择月\"\r\n              type=\"month\"\r\n              value-format=\"yyyy-MM\"\r\n              @change=\"statisticsTimeSelect\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===3\" :span=\"8\">\r\n            <el-date-picker v-model=\"queryParams.params.year\"\r\n                            clearable\r\n                            placeholder=\"银行流水发生时间\"\r\n                            style=\"width: 100%\"\r\n                            type=\"year\"\r\n                            value-format=\"yyyy\"\r\n                            @change=\"statisticsTimeSelect\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n          <el-col v-if=\"timeStatistics===4\" :span=\"8\">\r\n            <el-date-picker\r\n              v-model=\"queryParams.timeArr\"\r\n              end-placeholder=\"结束日期\"\r\n              range-separator=\"至\"\r\n              start-placeholder=\"开始日期\"\r\n              :default-time=\"['00:00:00', '23:59:59']\"\r\n              type=\"daterange\"\r\n              @change=\"statisticsTimeSelect\"\r\n              value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-col>\r\n        </el-form>\r\n      </el-row>\r\n      <el-table\r\n        :data=\"statisticsList\"\r\n        border @row-dblclick=\"dbclick\"\r\n        style=\"width: 100%;margin-top: 20px;\"\r\n      >\r\n        <el-table-column\r\n          label=\"银行账户\"\r\n          prop=\"bankAccSummary\"\r\n          width=\"200\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"账户代码\"\r\n          prop=\"bankAccountCode\"\r\n          width=\"100\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"账户币种\"\r\n          prop=\"bankCurrencyCode\"\r\n          width=\"80\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"收入\"\r\n          prop=\"totalRecieved\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"支出\"\r\n          prop=\"totalPaid\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"余额\"\r\n        >\r\n          <template slot-scope=\"scope\">\r\n            {{ currency(scope.row.totalRecieved).subtract(scope.row.totalPaid).value }}\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!--总计-->\r\n      <div v-if=\"statisticsList.length>0\" class=\"statistics-total\">\r\n        <div class=\"total-item\">USD 收入总计: {{ formatterCurrency(totalRecievedUSD, \"$\") }} - 支出总计:\r\n          {{ formatterCurrency(totalPaidUSD, \"$\") }} =\r\n          {{ formatterCurrency(currency(totalRecievedUSD).subtract(totalPaidUSD).value, \"$\") }}\r\n        </div>\r\n        <div class=\"total-item\">RMB 收入总计: {{ formatterCurrency(totalRecievedRMB, \"¥\") }} - 支出总计:\r\n          {{ formatterCurrency(totalPaidRMB, \"¥\") }} =\r\n          {{ formatterCurrency(currency(totalRecievedRMB).subtract(totalPaidRMB).value, \"¥\") }}\r\n        </div>\r\n        <div class=\"total-item\">RMB 折算总计:\r\n          {{\r\n            formatterCurrency(currency(totalRecievedUSD).subtract(totalPaidUSD).multiply(exchangeRate).add(currency(totalRecievedRMB).subtract(totalPaidRMB)).value, \"¥\")\r\n          }}\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 按付款抬头汇总的数据表格 -->\r\n      <h3>按付款抬头汇总</h3>\r\n      <!-- 付款抬头余额统计表格 -->\r\n      <div v-if=\"paymentTitleBalances.length > 0\" class=\"payment-title-balance\">\r\n        <h3>付款抬头余额统计</h3>\r\n        <el-table :data=\"paymentTitleBalances\" border style=\"width: 100%\">\r\n          <el-table-column label=\"付款抬头\" prop=\"titleCode\"></el-table-column>\r\n          <!-- RMB统计 -->\r\n          <el-table-column label=\"人民币(RMB)\">\r\n            <el-table-column label=\"收入\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.RMB.received.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"支出\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.RMB.paid.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"余额\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n              <span :class=\"{'positive': scope.row.RMB.balance > 0, 'negative': scope.row.RMB.balance < 0}\">\r\n                {{ scope.row.RMB.balance.toFixed(2) }}\r\n              </span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table-column>\r\n\r\n          <!-- USD统计 -->\r\n          <el-table-column label=\"美元(USD)\">\r\n            <el-table-column label=\"收入\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.USD.received.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"支出\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n                {{ scope.row.USD.paid.toFixed(2) }}\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column label=\"余额\" width=\"110\">\r\n              <template slot-scope=\"scope\">\r\n              <span :class=\"{'positive': scope.row.USD.balance > 0, 'negative': scope.row.USD.balance < 0}\">\r\n                {{ scope.row.USD.balance.toFixed(2) }}\r\n              </span>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addBankrecord,\r\n  changeStatus,\r\n  delBankrecord, delImg, getAccountFundStatistics,\r\n  getBankrecord,\r\n  listBankrecord,\r\n  updateBankrecord\r\n} from \"@/api/system/bankrecord\"\r\nimport CompanySelect from \"@/components/CompanySelect/index.vue\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport pinyin from \"js-pinyin\"\r\nimport store from \"@/store\"\r\nimport currency from \"currency.js\"\r\nimport {chargeWriteOff, findHedging, selectListCharge, turnBackWriteoff} from \"@/api/system/rsCharge\"\r\nimport {selectListExchangerate} from \"@/api/system/exchangerate\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport log from \"@/views/monitor/job/log.vue\"\r\nimport moment from \"moment\"\r\nimport request from \"@/utils/request\"\r\nimport ImgPreview from \"@/views/system/rct/imgPreview.vue\"\r\nimport {getToken} from \"@/utils/auth\"\r\nimport {rctWriteoff, writeoff} from \"@/api/system/rct\"\r\nimport {listWriteOffReimburse} from \"@/api/system/reimburse\"\r\n\r\nexport default {\r\n  name: \"otherBankRecord\",\r\n  components: {ImgPreview, Treeselect, CompanySelect},\r\n  data() {\r\n    return {\r\n      writeOffList: [],\r\n      reimburseList: [],\r\n      size: this.$store.state.app.size || \"mini\",\r\n      salesId: null,\r\n      belongList: [],\r\n      staffList: [],\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 记录公司账户出入账明细表格数据\r\n      bankrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        params: {},\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: null,\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        timeArr: null\r\n      },\r\n      statisticsList: [],\r\n\r\n      // 表单参数\r\n      form: {\r\n        chargeTypeId: null\r\n      },\r\n      searchAble: false,\r\n      writeoffType: null,\r\n      // 表单校验\r\n      rules: {\r\n        actualBankRecievedAmount: [\r\n          {required: true, message: \"请输入实收信息\", trigger: \"blur\"}\r\n        ],\r\n        bankRecordTime: [\r\n          {required: true, message: \"请输入银行时间\", trigger: \"blur\"}\r\n        ],\r\n        actualBankPaidAmount: [\r\n          {required: true, message: \"请输入实付信息\", trigger: \"blur\"}\r\n        ]\r\n        /* clearingCompanyId: [\r\n          {required: true, message: \"请输入结算公司\", trigger: \"blur\"}\r\n        ] */\r\n      },\r\n      statisticsOpen: false,\r\n      totalRecievedUSD: 0,\r\n      totalRecievedRMB: 0,\r\n      totalPaidUSD: 0,\r\n      totalPaidRMB: 0,\r\n      exchangeRate: 0,\r\n      paymentTitleBalances: 0,\r\n      timeStatistics: null,\r\n      add: false,\r\n      upload: {\r\n        // 是否显示弹出层（用户导入）\r\n        open: false,\r\n        // 弹出层标题（用户导入）\r\n        title: \"\",\r\n        // 是否禁用上传\r\n        isUploading: false,\r\n        // 是否更新已经存在的用户数据\r\n        updateSupport: false,\r\n        // 设置上传的请求头部\r\n        headers: {Authorization: \"Bearer \" + getToken()},\r\n        // 上传的地址\r\n        url: process.env.VUE_APP_BASE_API + \"/system/bankrecord/importData\"\r\n      },\r\n      showDetail: false,\r\n      selectedCharges: [],\r\n      totalAmount: null,\r\n      selectedAmount: null,\r\n      selectedBalanceAmount: null,\r\n      selectedBalanceAmountRMB: 0,\r\n      selectedBalanceAmountUSD: 0,\r\n      loadingCharge: false,\r\n      staffId: null,\r\n      alreadyWriteoffList: [],\r\n      turnBackWriteoffList: [],\r\n      showCompany: false,\r\n      companyList: [],\r\n      bankSlipPreview: false,\r\n      imageFile: null,\r\n      hedgingData: [],\r\n      exchangeRateList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n    \"form.chargeTypeId\"(n) {\r\n      // 只有货运流水和报销流水才可以调取费用\r\n      let data\r\n      this.$store.state.data.chargeTypeList.forEach(item => {\r\n        if (item.chargeTypeId == n) {\r\n          data = item\r\n        } else if (item.children) {\r\n          item.children.forEach(v => {\r\n            if (v.chargeTypeId == n) {\r\n              data = item\r\n            }\r\n          })\r\n        }\r\n      })\r\n      if (data) {\r\n        if (data.chargeTypeId === 2 || data.parentId === 2) {\r\n          this.searchAble = true\r\n          this.writeoffType = \"chargeType\"\r\n        } else if (data.chargeTypeId === 4 || data.parentId === 4) {\r\n          this.searchAble = true\r\n          this.writeoffType = \"reimburseType\"\r\n        }\r\n      } else {\r\n        this.searchAble = false\r\n      }\r\n    },\r\n    // 实收金额\r\n    \"form.actualBankRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款手续费\r\n    \"form.bankRecievedHandlingFee\"(n) {\r\n      this.form.sqdBillRecievedAmount = currency(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value\r\n    },\r\n    // 收款损益\r\n    \"form.bankRecievedExchangeLost\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 实付金额\r\n    \"form.actualBankPaidAmount\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款手续费\r\n    \"form.bankPaidHandlingFee\"(n) {\r\n      this.form.sqdBillPaidAmount = currency(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value\r\n    },\r\n    // 付款损益\r\n    \"form.bankPaidExchangeLost\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收款记账\r\n    \"form.sqdBillRecievedAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付款记账\r\n    \"form.sqdBillPaidAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n\r\n    // 收账已销\r\n    \"form.billRecievedWriteoffAmount\"(n) {\r\n      this.form.sqdBillRecievedWriteoffBalance = currency(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value\r\n    },\r\n    // 付账已销\r\n    \"form.billPaidWriteoffAmount\"(n) {\r\n      this.form.sqdBillPaidWriteoffBalance = currency(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value\r\n    },\r\n    selectedCharges: {\r\n      handler: function (newVal, oldVal) {\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = 0\r\n\r\n        // 已选总计\r\n        this.selectedBalanceAmount = 0\r\n\r\n        let billRecievedWriteoffAmount = 0\r\n        let billPaidWriteoffAmount = 0\r\n\r\n        let selectedBalanceAmountUSD = 0\r\n        let selectedBalanceAmountRMB = 0\r\n        // 是否只是对冲\r\n        let isHedging = true\r\n        newVal.forEach((val) => {\r\n          if (this.form.isRecievingOrPaying == val.isRecievingOrPaying) {\r\n            isHedging = false\r\n          }\r\n        })\r\n\r\n        newVal.map(item => {\r\n          // 本次拟销账金额\r\n          // item.writeoffFromDnBalance = currency(item.writeoffFromBankBalance).divide(item.exchangeRate).value\r\n          if (item.exchangeRate) {\r\n            // 获取subtotal的小数位数\r\n            const subtotalStr = item.subtotal.toString()\r\n            const decimalPlaces = subtotalStr.includes(\".\") ?\r\n              subtotalStr.split(\".\")[1].length : 0\r\n\r\n            // 根据subtotal的小数位数来格式化writeoffFromDnBalance\r\n            const calculatedValue = currency(item.writeoffFromBankBalance).divide(item.exchangeRate).value\r\n            item.writeoffFromDnBalance = Number(calculatedValue).toFixed(decimalPlaces)\r\n          }\r\n\r\n          if (this.form.isRecievingOrPaying == item.isRecievingOrPaying) {\r\n            // 收账已销\r\n            billRecievedWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billRecievedWriteoffAmount).value\r\n            // 付账已销\r\n            billPaidWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billPaidWriteoffAmount).value\r\n\r\n            if (item.dnCurrencyCode === \"RMB\") {\r\n              selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            } else {\r\n              selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            }\r\n          } else if (isHedging) {\r\n            // 收账已销\r\n            billRecievedWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billRecievedWriteoffAmount).value\r\n            // 付账已销\r\n            billPaidWriteoffAmount = currency(item.writeoffFromBankBalance, {precision: 4}).add(billPaidWriteoffAmount).value\r\n\r\n            if (item.dnCurrencyCode === \"RMB\") {\r\n              selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            } else {\r\n              selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 4}).add(item.sqdDnCurrencyBalance).value\r\n            }\r\n          } else {\r\n            // 收账已销\r\n            billRecievedWriteoffAmount = currency(billRecievedWriteoffAmount, {precision: 4}).subtract(item.writeoffFromBankBalance).value\r\n            // 付账已销\r\n            billPaidWriteoffAmount = currency(billPaidWriteoffAmount, {precision: 4}).subtract(item.writeoffFromBankBalance).value\r\n\r\n            if (item.dnCurrencyCode === \"RMB\") {\r\n              selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 4}).subtract(item.sqdDnCurrencyBalance).value\r\n            } else {\r\n              selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 4}).subtract(item.sqdDnCurrencyBalance).value\r\n            }\r\n          }\r\n\r\n          // 已选总计\r\n          this.selectedAmount = null\r\n\r\n          currency(item.dnUnitRate).multiply(item.dnAmount).value === item.writeoffFromDnBalance ? item.writeoffStatus = \"0\" : item.writeoffFromDnBalance > 0 ? item.writeoffStatus = \"1\" : item.writeoffStatus = \"-1\"\r\n        })\r\n\r\n        // 收账已销\r\n        this.form.billRecievedWriteoffAmount = currency(billRecievedWriteoffAmount, {precision: 2}).value\r\n        // 付账已销\r\n        this.form.billPaidWriteoffAmount = currency(billPaidWriteoffAmount, {precision: 2}).value\r\n\r\n        this.selectedBalanceAmountUSD = currency(selectedBalanceAmountUSD, {precision: 2}).value\r\n        this.selectedBalanceAmountRMB = currency(selectedBalanceAmountRMB, {precision: 2}).value\r\n\r\n      },\r\n      deep: true\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    // this.loadSales()\r\n    // this.loadStaff()\r\n    // this.getExchangeRate(\"USD\", \"RMB\", null, val => {\r\n    //   console.log(val)\r\n    // })\r\n    store.dispatch(\"getChargeTypeList\")\r\n    if (this.$store.state.data.exchangeRateList.length == 0 || this.$store.state.data.redisList.exchangeRateList) {\r\n      store.dispatch(\"getExchangeRate\")\r\n    }\r\n  },\r\n  async mounted() {\r\n    this.exchangeRateList = await selectListExchangerate()\r\n  },\r\n  beforeMount() {\r\n    this.loadStaff()\r\n  },\r\n  methods: {\r\n    carousel(pics) {\r\n      if (pics != null) {\r\n        this.openCarousel = true\r\n        this.pics = pics\r\n      } else {\r\n        this.$message.info(\"没有附件\")\r\n      }\r\n    },\r\n    getWriteOffType(type) {\r\n      switch (type) {\r\n        case \"chargeType\":\r\n          this.getCompanyCharges()\r\n          break\r\n        case \"reimburseType\":\r\n          this.getReimburseCharges()\r\n          break\r\n      }\r\n    },\r\n    getReimburseCharges() {\r\n      this.reimburseList = []\r\n      this.loadingCharge = true\r\n\r\n      listWriteOffReimburse({\r\n        staffId: this.form.sqdRsStaffId,\r\n        sqdRaletiveRctList: this.form.sqdRaletiveRctList,\r\n        bankRecordId: this.form.bankRecordId\r\n      }).then(response => {\r\n        this.reimburseList = response.rows\r\n\r\n        this.loadingCharge = false\r\n        this.showDetail = true\r\n\r\n        this.totalAmount = 0\r\n        this.$nextTick(() => {\r\n          this.reimburseList.map(item => {\r\n            this.totalAmount = currency(item.reimbursePrice).add(this.totalAmount).value\r\n            if (item.bankRecordId === this.form.bankRecordId) {\r\n              this.$refs.writeOffReimburseTable.toggleRowSelection(item, true)\r\n            }\r\n          })\r\n        })\r\n\r\n        this.reimburseList.sort((a, b) => {\r\n          if (a.bankRecordId === this.form.bankRecordId && b.bankRecordId !== this.form.bankRecordId) {\r\n            return -1 // a 排在 b 前\r\n          } else if (a.bankRecordId !== this.form.bankRecordId && b.bankRecordId === this.form.bankRecordId) {\r\n            return 1 // b 排在 a 前\r\n          } else {\r\n            return 0 // 保持原顺序\r\n          }\r\n        })\r\n      })\r\n    },\r\n    handleExchangeRateInput(row, newValue) {\r\n      // 实时更新展示值\r\n      row.exchangeRateShow = newValue\r\n\r\n    }\r\n    ,\r\n    isNumeric(value) {\r\n      return !isNaN(Number(value))\r\n    }\r\n    ,\r\n    validateExchangeRate(row) {\r\n      // 定义校验规则，例如：必须是 0.01 到 100 的数字\r\n      const minRate = 0.01\r\n      const maxRate = 100\r\n\r\n      const value = row.exchangeRateShow || \"\"\r\n      if (this.isNumeric(value)) {\r\n        // 转换为数字，进行范围校验\r\n        const value = parseFloat(row.exchangeRateShow)\r\n        if (isNaN(value) || value < minRate || value > maxRate) {\r\n          this.$message.error(`汇率必须在 ${minRate} 和 ${maxRate} 之间`)\r\n          // 恢复到上次有效值\r\n          row.exchangeRateShow = row.exchangeRate.toString()\r\n        } else {\r\n          // 输入合法，同步内部值\r\n          row.exchangeRate = value\r\n        }\r\n      } else {\r\n        // 检查输入是否符合 \"1/数字\" 格式\r\n        // 确保是字符串\r\n        const match = value.match(/^1\\/(\\d+(\\.\\d+)?)$/)\r\n        if (match) {\r\n          const rate = parseFloat(match[1]) // 提取分母值作为汇率\r\n          if (rate > 0) {\r\n            row.exchangeRate = (1 / rate).toFixed(4) // 自动计算 USD\r\n          }\r\n        }\r\n      }\r\n    }\r\n    ,\r\n    async deleteBankSlip(row) {\r\n      // 删除服务器中的图片文件\r\n      try {\r\n        await delImg({url: row.slipFile})\r\n      } catch (e) {\r\n        // 更新流水中的图片地址\r\n        await updateBankrecord({\r\n          bankRecordId: row.bankRecordId,\r\n          slipFile: null,\r\n          isRecievingOrPaying: this.type === \"pay\" ? \"1\" : \"0\",\r\n          bankRecordNo: row.bankRecordNo\r\n        })\r\n      }\r\n\r\n      await this.getList()\r\n    }\r\n    ,\r\n    handleSearch(type) {\r\n      switch (type) {\r\n        case \"common\":\r\n          this.getList({})\r\n          break\r\n        default:\r\n          break\r\n      }\r\n    }\r\n    ,\r\n    parseTime,\r\n    updateSlipSatus() {\r\n      // 确认水单的时候,相关表单校验\r\n\r\n      this.clearReceiveOrPay()\r\n      if (this.form.slipConfirmed == 1) {\r\n        this.form.slipConfirmed = \"0\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单取消确认\")\r\n        })\r\n      } else {\r\n        this.form.slipConfirmed = \"1\"\r\n        updateBankrecord(this.form).then(response => {\r\n          this.$message.success(\"水单确认\")\r\n        })\r\n      }\r\n\r\n    }\r\n    ,\r\n    writeOffConfirm() {\r\n      this.clearReceiveOrPay()\r\n      // 更新银行流水\r\n      /* let rctSet = new Set()\r\n      this.selectedCharges.map(item => rctSet.add(item.sqdRctNo))\r\n      let rctArr = []\r\n      rctSet.forEach(item => rctArr.push(item))\r\n      this.form.sqdRaletiveRctList = rctArr.toString() */\r\n      this.form.writeoffTime = moment().format(\"yyyy-MM-DD\")\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        this.open = false\r\n        this.getList()\r\n      })\r\n\r\n      // 取消销账的费用,进行回退\r\n      const cancelledRows = this.alreadyWriteoffList.filter(row => {\r\n        return !this.selectedCharges.some(selected => selected.chargeId === row.chargeId)\r\n      })\r\n      turnBackWriteoff({rsChargeList: cancelledRows})\r\n\r\n      // 更新费用中的销账余额(更新费用时插入中间表)\r\n      let midChargeBankWriteoffs = []\r\n      this.selectedCharges = this.selectedCharges.map(item => {\r\n        // 每次销账的时候再计算余额写回费用明细中\r\n        item.sqdDnCurrencyBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).subtract(item.writeoffFromDnBalance).value\r\n        // 销账余额 = 上次销账剩余-本次拟销账金额\r\n        item.clearingCurrencyCode = this.form.bankCurrencyCode\r\n        item.writeoffStatus = currency(item.sqdDnCurrencyBalance).value === 0 ? \"1\" : \"0\"\r\n        let midChargeBankWriteoff = {}\r\n        midChargeBankWriteoff.chargeId = item.chargeId\r\n        midChargeBankWriteoff.bankRecordId = this.form.bankRecordId\r\n        midChargeBankWriteoff.writeoffFromDnBalance = item.writeoffFromDnBalance\r\n        midChargeBankWriteoff.exchangeRateShowing = item.exchangeRateShow\r\n        midChargeBankWriteoff.writeoffFromBankBalance = item.writeoffFromBankBalance\r\n        midChargeBankWriteoff.dnBasicRate = item.exchangeRate\r\n\r\n        midChargeBankWriteoffs.push(midChargeBankWriteoff)\r\n\r\n        return item\r\n      })\r\n\r\n      chargeWriteOff({\r\n        rsChargeList: this.selectedCharges,\r\n        midChargeBankWriteoffList: midChargeBankWriteoffs\r\n      }).then(response => {\r\n        // 销完账之后要更新操作单中的未收未付\r\n        rctWriteoff(this.form.sqdRaletiveRctList.split(\",\"))\r\n      })\r\n\r\n    },\r\n    formatterCurrency(value, currencyType) {\r\n      return currency(value, {\r\n        separator: \",\",\r\n        // symbol: \"¥\",\r\n        symbol: currencyType,\r\n        precision: 0\r\n      }).format()\r\n    }\r\n    ,\r\n    invertSelection() {\r\n      /* this.writeOffList.forEach(row => {\r\n        // if (this.selectedCharges.indexOf(row) !== -1) {\r\n        this.$refs.writeOffTable.toggleRowSelection(row)\r\n        // }\r\n      }) */\r\n    }\r\n    ,\r\n    autoSelection() {\r\n    }\r\n    ,\r\n    addHedging() {\r\n      // 向销账列表中添加一条\r\n      const isReceiving = this.form.isRecievingOrPaying === \"0\"\r\n      const hedgingType = isReceiving ? \"1\" : \"0\"\r\n\r\n      findHedging({...this.form, isRecievingOrPaying: hedgingType}).then(response => {\r\n        this.hedgingData = response ? response.filter(item =>\r\n          !this.writeOffList.some(writeOff => writeOff.chargeId === item.chargeId)\r\n        ) : []\r\n      })\r\n    }\r\n    ,\r\n    projectRemove() {\r\n    }\r\n    ,\r\n    print() {\r\n    }\r\n    ,\r\n    currency,\r\n    async getCompanyCharges() {\r\n      this.writeOffList = []\r\n      this.alreadyWriteoffList = []\r\n      this.loadingCharge = true\r\n      // 销完的流水只展示销账记录\r\n      let writeoffStatus\r\n      if (this.form.isRecievingOrPaying === \"0\" && this.form.sqdBillRecievedWriteoffBalance === 0) {\r\n        writeoffStatus = \"ALL\"\r\n      } else if (this.form.isRecievingOrPaying === \"1\" && this.form.sqdBillPaidWriteoffBalance === 0) {\r\n        writeoffStatus = \"ALL\"\r\n      } else if (this.form.billRecievedWriteoffAmount !== null || this.form.billPaidWriteoffAmount !== null) {\r\n        // 不是已销完的流水,费用过滤掉已经销账完成的费用\r\n        writeoffStatus = \"Part\"\r\n      }\r\n\r\n      let response = await selectListCharge({\r\n        clearingCompanyId: this.form.clearingCompanyId,\r\n        isRecievingOrPaying: this.form.isRecievingOrPaying,\r\n        bankRecordId: this.form.bankRecordId,\r\n        writeoffStatus: writeoffStatus,\r\n        sqdRaletiveRctList: this.form.sqdRaletiveRctList\r\n      })\r\n      if (response && response.length > 0) {\r\n\r\n        for (let item of response) {\r\n          if (this.form.bankCurrencyCode === item.dnCurrencyCode) {\r\n            item.exchangeRateShow = 1\r\n            item.exchangeRate = 1\r\n          } else {\r\n            let result = await this.getExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, \"\")\r\n            if (!result) {\r\n              // 没有找到对应的汇率\r\n              this.$message.error(\"系统中没有对应的汇率\")\r\n              this.loadingCharge = false\r\n              return\r\n            }\r\n            // 账单日期汇率\r\n            // let billExchangeRate = await this.getBillDataExchangeRate(this.form.bankCurrencyCode, item.dnCurrencyCode, item.currencyRateCalculateDate)\r\n            item.exchangeRate = result[0] === 1 ? Number(result[1]).toFixed(4) : Number(this.currencyWithPrecision(1).divide(result[1]).value).toFixed(4)\r\n            item.exchangeRateShow = result[0] === 1 ? result[1] : \"1/\" + result[1]\r\n          }\r\n        }\r\n        this.writeOffList = response\r\n        setTimeout(() => {\r\n          this.writeOffList.map(item => {\r\n            // 有销账记录的要勾选并显示上次销账信息\r\n            if (item.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              // 填入信息\r\n              // 本次拟销账金额\r\n              // 销账余额\r\n              item.writeoffFromDnBalance = item.midChargeBankWriteoff.writeoffFromDnBalance\r\n              item.sqdDnCurrencyBalance = item.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n              // 折算记账金额\r\n              item.writeoffFromBankBalance = item.midChargeBankWriteoff.writeoffFromBankBalance\r\n\r\n              this.selectedCharges.push(item)\r\n              this.alreadyWriteoffList.push(item)\r\n\r\n              // 确保isAccountConfirmed值为\"1\"，否则将无法选中\r\n              if (item.isAccountConfirmed !== \"1\") {\r\n                item.isAccountConfirmed = \"1\"\r\n              }\r\n\r\n              // 使用$nextTick确保DOM已更新\r\n              this.$nextTick(() => {\r\n                this.$refs.writeOffChargeTable.toggleRowSelection(item, true)\r\n              })\r\n            }\r\n          })\r\n\r\n          // 排序放在选中逻辑之后\r\n          this.writeOffList.sort((a, b) => {\r\n            // 未审核排最后\r\n            if (a.isAccountConfirmed === \"0\" && b.isAccountConfirmed === \"1\") {\r\n              return 1\r\n            }\r\n            if (a.isAccountConfirmed === \"1\" && b.isAccountConfirmed === \"0\") {\r\n              return -1\r\n            }\r\n\r\n            // 如果a对象有 midChargeBankWriteoff.midChargeBankId，而b没有，则a排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId === null && b.midChargeBankWriteoff.midChargeBankId !== null) {\r\n              return 1\r\n            }\r\n            // 如果b对象有 midChargeBankWriteoff.midChargeBankId，而a没有，则b排在前面\r\n            if (a.midChargeBankWriteoff.midChargeBankId !== null && b.midChargeBankWriteoff.midChargeBankId === null) {\r\n              return -1\r\n            }\r\n            // 检查是否都有 midChargeBankWriteoff.midChargeBankId 属性\r\n            if (a.midChargeBankWriteoff.midChargeBankId && b.midChargeBankWriteoff.midChargeBankId) {\r\n              // 如果两个对象都有 midChargeBankWriteoff.midChargeBankId，则按 writeoffFromDnBalance 进行排序\r\n              return a.midChargeBankWriteoff.sqdDnCurrencyBalance - b.midChargeBankWriteoff.sqdDnCurrencyBalance\r\n            } else {\r\n              // 如果有一个或两个对象缺少 midChargeBankWriteoff.midChargeBankId 属性，则保持原顺序\r\n              return 0\r\n            }\r\n          })\r\n\r\n          // 排序后再次确认选中状态\r\n          this.$nextTick(() => {\r\n            this.alreadyWriteoffList.forEach(item => {\r\n              this.$refs.writeOffChargeTable.toggleRowSelection(item, true)\r\n            })\r\n          })\r\n        }, 0)\r\n      }\r\n      this.loadingCharge = false\r\n      this.showDetail = true\r\n    }\r\n    ,\r\n    currencyWithPrecision(value, precision = 4) {\r\n      return currency(value, {precision: precision})\r\n    }\r\n    ,\r\n    verify() {\r\n      /* if (this.form.clearingCompanyId === null) {\r\n        this.$message.warning(\"请输入结算公司\")\r\n        return\r\n      } */\r\n      if (this.form.isBankRecordLocked == 1) {\r\n        this.form.isBankRecordLocked = 0\r\n        this.form.verifyId = null\r\n        this.form.verifyTime = null\r\n      } else {\r\n        this.form.isBankRecordLocked = 1\r\n        this.form.verifyId = this.$store.state.user.sid\r\n        this.form.verifyTime = moment().format(\"yyyy-MM-DD\")\r\n      }\r\n\r\n      this.clearReceiveOrPay()\r\n      updateBankrecord(this.form).then(response => {\r\n        this.$message.success(\"修改成功\")\r\n      })\r\n    }\r\n    ,\r\n    /** 查询记录公司账户出入账明细列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listBankrecord(this.queryParams).then(response => {\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    }\r\n    ,\r\n// 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    }\r\n    ,\r\n// 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankRecordId: null,\r\n        isRecievingOrPaying: null,\r\n        sqdPaymentTitleCode: null,\r\n        bankAccountCode: null,\r\n        clearingCompanyId: null,\r\n        sqdClearingCompanyShortname: null,\r\n        chargeTypeId: null,\r\n        chargeDescription: null,\r\n        bankCurrencyCode: null,\r\n        actualBankRecievedAmount: null,\r\n        actualBankPaidAmount: null,\r\n        bankRecievedHandlingFee: null,\r\n        bankPaidHandlingFee: null,\r\n        bankRecievedExchangeLost: null,\r\n        bankPaidExchangeLost: null,\r\n        sqdBillRecievedAmount: null,\r\n        sqdBillPaidAmount: null,\r\n        billRecievedWriteoffAmount: null,\r\n        billPaidWriteoffAmount: null,\r\n        sqdBillRecievedWriteoffBalance: null,\r\n        sqdBillPaidWriteoffBalance: null,\r\n        writeoffStatus: \"0\",\r\n        bankRecordTime: null,\r\n        paymentTypeCode: null,\r\n        voucherNo: null,\r\n        invoiceNo: null,\r\n        bankRecordRemark: null,\r\n        bankRecordByStaffId: null,\r\n        bankRecordUpdateTime: null,\r\n        isBankRecordLocked: null,\r\n        isWriteoffLocked: null,\r\n        sqdChargeIdList: null,\r\n        sqdRaletiveRctList: null,\r\n        sqdRaletiveInvoiceList: null,\r\n        sqdRsStaffId: null,\r\n        writeoffRemark: null,\r\n        writeoffStaffId: null,\r\n        writeoffTime: null,\r\n        chargeType: \"订单\"\r\n      }\r\n      this.staffId = null\r\n      this.selectedBalanceAmount = 0\r\n      this.resetForm(\"form\")\r\n    }\r\n    ,\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    }\r\n    ,\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    }\r\n    ,\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.bankRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    }\r\n    ,\r\n    // 对冲勾选\r\n    handleHedgingSelectionChange(selection) {\r\n\r\n    }\r\n    ,\r\n    // 复制对冲费用到销账列表\r\n    async handleAddHedging(row) {\r\n      // 创建一个新对象，避免引用原对象\r\n      const newRow = {...row}\r\n\r\n      // 添加汇率\r\n      if (this.form.bankCurrencyCode === newRow.dnCurrencyCode) {\r\n        newRow.exchangeRateShow = \"1\"\r\n        newRow.exchangeRate = 1\r\n      } else {\r\n        // 当天汇率\r\n        let result = await this.getExchangeRate(this.form.bankCurrencyCode, newRow.dnCurrencyCode, \"\")\r\n        if (!result) {\r\n          // 没有找到对应的汇率\r\n          this.$message.error(\"系统中没有对应的汇率\")\r\n          return\r\n        }\r\n        // 设置汇率值\r\n        newRow.exchangeRate = result[0] === 1 ? Number(result[1]).toFixed(4) : Number(this.currencyWithPrecision(1).divide(result[1]).value).toFixed(4)\r\n        newRow.exchangeRateShow = result[0] === 1 ? result[1].toString() : \"1/\" + result[1]\r\n      }\r\n\r\n      // 初始化销账相关字段\r\n      newRow.writeoffFromDnBalance = currency(newRow.sqdDnCurrencyBalance).value\r\n      newRow.writeoffFromBankBalance = currency(newRow.sqdDnCurrencyBalance).multiply(newRow.exchangeRate).value\r\n\r\n      // 确保中间表对象存在\r\n      if (!newRow.midChargeBankWriteoff) {\r\n        newRow.midChargeBankWriteoff = {\r\n          midChargeBankId: null,\r\n          writeoffStaffId: null,\r\n          writeoffTime: null\r\n        }\r\n      }\r\n\r\n      // 对冲的金额为负数 - 如果需要设置为负数，取消注释下面的代码\r\n      // newRow.writeoffFromDnBalance = -Math.abs(newRow.writeoffFromDnBalance);\r\n      // newRow.writeoffFromBankBalance = -Math.abs(newRow.writeoffFromBankBalance);\r\n\r\n      this.writeOffList.push(newRow)\r\n      this.hedgingData = this.hedgingData.filter(item => item.chargeId !== newRow.chargeId)\r\n    }\r\n    ,\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      // this.ids = selection.map(item => item.bankRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n\r\n      // 勾选时计算销账金额\r\n      this.selectedAmount = 0\r\n      // 取消销账列表\r\n      this.turnBackWriteoffList = []\r\n      selection.map(item => {\r\n        // 上次勾选的不受影响\r\n        if (this.selectedCharges.indexOf(item) === -1) {\r\n          // 自动填入拟销账金额\r\n          if (item.sqdDnCurrencyBalance !== null && item.sqdDnCurrencyBalance > 0) {\r\n            // 本次拟销账金额\r\n            item.writeoffFromDnBalance = currency(item.sqdDnCurrencyBalance).value\r\n            // 折算记账金额\r\n            item.writeoffFromBankBalance = currency(item.sqdDnCurrencyBalance).multiply(item.exchangeRate).value\r\n          } else {\r\n            item.writeoffFromDnBalance = currency(item.dnUnitRate).multiply(item.dnAmount).value\r\n            item.writeoffFromBankBalance = currency(currency(item.dnUnitRate).multiply(item.dnAmount)).multiply(item.exchangeRate).value\r\n          }\r\n        }\r\n      })\r\n      // 取消勾选的费用本次拟核销金额置为0\r\n      this.selectedCharges.map(item => {\r\n        if (selection.indexOf(item) === -1) {\r\n          item.writeoffFromDnBalance = 0\r\n          item.writeoffFromBankBalance = 0\r\n          item.sqdDnCurrencyBalanceShow = currency(item.sqdDnCurrencyBalance).value\r\n\r\n          // 找出取消销账的费用\r\n          if (this.alreadyWriteoffList.indexOf(item) !== -1) {\r\n            this.turnBackWriteoffList.push(item)\r\n          }\r\n        }\r\n      })\r\n\r\n      this.selectedCharges = selection\r\n    }\r\n    ,\r\n    dbclick(row) {\r\n      this.queryParams.bankAccountCode = row.bankAccountCode\r\n      listBankrecord(this.queryParams).then(response => {\r\n        this.bankrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n        this.statisticsOpen = false\r\n      })\r\n    }\r\n    ,\r\n    statisticsTimeSelect() {\r\n      // let requestMap = new Map()\r\n      let requestMap = {}\r\n      switch (this.timeStatistics) {\r\n        case 1:\r\n          requestMap.day = this.queryParams.params.day\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n        case 2:\r\n          requestMap.month = this.queryParams.params.month\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n        case 3:\r\n          requestMap.year = this.queryParams.params.year\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n        case 4:\r\n          requestMap.startTime = this.queryParams.timeArr[0] ? this.queryParams.timeArr[0] : null\r\n          requestMap.endTime = this.queryParams.timeArr[1] ? this.queryParams.timeArr[1] : null\r\n          this.searchAccountFundStatistics({params: requestMap})\r\n          break\r\n\r\n      }\r\n    }\r\n    ,\r\n    accountFundStatistics() {\r\n      this.statisticsOpen = true\r\n      this.searchAccountFundStatistics()\r\n    }\r\n    ,\r\n    searchAccountFundStatistics(data) {\r\n      if (!data) {\r\n        data = {}\r\n      }\r\n      getAccountFundStatistics(data).then(response => {\r\n        this.statisticsList = response.data\r\n        this.totalRecievedUSD = 0\r\n        this.totalRecievedRMB = 0\r\n        this.totalPaidUSD = 0\r\n        this.totalPaidRMB = 0\r\n\r\n        let exchangeRate\r\n        for (const a of this.$store.state.data.exchangeRateList) {\r\n          if (!exchangeRate) {\r\n            if (a.localCurrency === \"RMB\"\r\n              && \"USD\" == a.overseaCurrency\r\n              && parseTime(a.validFrom) <= parseTime(new Date())\r\n              && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n              exchangeRate = currency(a.settleRate).divide(a.base).value\r\n            }\r\n          }\r\n        }\r\n        this.exchangeRate = exchangeRate\r\n\r\n        if (response.data) {\r\n          response.data.forEach(item => {\r\n            if (item.bankCurrencyCode === \"RMB\") {\r\n              this.totalRecievedRMB += item.totalRecieved\r\n              this.totalPaidRMB += item.totalPaid\r\n            }\r\n            if (item.bankCurrencyCode === \"USD\") {\r\n              this.totalRecievedUSD += item.totalRecieved\r\n              this.totalPaidUSD += item.totalPaid\r\n            }\r\n          })\r\n\r\n          // 计算付款抬头余额\r\n          this.paymentTitleBalances = this.calculatePaymentTitleBalances()\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    // 付款抬头余额统计函数\r\n    calculatePaymentTitleBalances() {\r\n      // 检查数据是否存在\r\n      if (!this.statisticsList || this.statisticsList.length === 0) {\r\n        return []\r\n      }\r\n\r\n      // 创建一个Map用于存储汇总数据\r\n      const summaryMap = new Map()\r\n\r\n      // 遍历统计数据进行汇总\r\n      this.statisticsList.forEach(item => {\r\n        // 获取付款抬头代码，如果不存在则使用\"未知\"\r\n        const titleCode = item.sqdPaymentTitleCode || \"未知\"\r\n        const currency = item.bankCurrencyCode || \"未知\"\r\n        const received = Number(item.totalRecieved) || 0\r\n        const paid = Number(item.totalPaid) || 0\r\n\r\n        // 如果Map中不存在该付款抬头，则创建一个新的记录\r\n        if (!summaryMap.has(titleCode)) {\r\n          summaryMap.set(titleCode, {\r\n            titleCode,\r\n            titleName: item.sqdPaymentTitleName || \"未知抬头\",\r\n            RMB: {received: 0, paid: 0, balance: 0},\r\n            USD: {received: 0, paid: 0, balance: 0}\r\n          })\r\n        }\r\n\r\n        // 更新对应货币的收入和支出\r\n        const record = summaryMap.get(titleCode)\r\n        if (currency === \"RMB\") {\r\n          record.RMB.received += received\r\n          record.RMB.paid += paid\r\n          record.RMB.balance = record.RMB.received - record.RMB.paid\r\n        } else if (currency === \"USD\") {\r\n          record.USD.received += received\r\n          record.USD.paid += paid\r\n          record.USD.balance = record.USD.received - record.USD.paid\r\n        }\r\n      })\r\n\r\n      // 将Map转换为数组并返回\r\n      return Array.from(summaryMap.values())\r\n    }\r\n    ,\r\n    handleImport() {\r\n      this.upload.title = \"用户导入\"\r\n      this.upload.open = true\r\n    }\r\n    ,\r\n    // 文件上传中处理\r\n    handleFileUploadProgress(event, file, fileList) {\r\n      this.upload.isUploading = true\r\n    }\r\n    ,\r\n    // 文件上传成功处理\r\n    handleFileSuccess(response, file, fileList) {\r\n      this.upload.open = false\r\n      this.upload.isUploading = false\r\n      this.$refs.upload.clearFiles()\r\n      this.$message.info(response.msg)\r\n      if (response.msg != \"全部上传成功\") {\r\n        this.download(\"system/freight/failList\", {}, `上传失败列表.xlsx`)\r\n      }\r\n      this.getList()\r\n    }\r\n    ,\r\n    // 提交上传文件\r\n    submitFileForm() {\r\n      this.$refs.upload.submit()\r\n    }\r\n    ,\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.showDetail = false\r\n      this.open = true\r\n      this.title = \"新建银行流水\"\r\n      this.add = true\r\n      this.form.isRecievingOrPaying = \"0\"\r\n      this.form.paymentTypeCode = \"T/T\"\r\n      this.form.chargeType = \"订单\"\r\n      this.form.chargeTypeId = 2\r\n    }\r\n    ,\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.add = false\r\n      this.reset()\r\n      const bankRecordId = row.bankRecordId || this.ids\r\n      getBankrecord(bankRecordId).then(response => {\r\n        this.form = response.data\r\n        this.form.chargeType = \"订单\"\r\n        this.open = true\r\n        this.title = \"查看银行流水-销账明细\"\r\n        this.companyList = [response.companyList]\r\n\r\n        this.$nextTick(() => {\r\n          if (this.form.writeoffStatus == 0) {\r\n            switch (this.writeoffType) {\r\n              case \"chargeType\":\r\n                this.getCompanyCharges()\r\n                break\r\n              case \"reimburseType\":\r\n                this.getReimburseCharges()\r\n                break\r\n            }\r\n\r\n          }\r\n        })\r\n\r\n      })\r\n    }\r\n    ,\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(async valid => {\r\n        this.form.chargeTypeId = 2\r\n        /* if (this.form.clearingCompanyId === null) {\r\n          this.$message.warning(\"请输入结算公司\")\r\n          return\r\n        } */\r\n        if (valid) {\r\n          // 先上传图片\r\n          if (this.imageFile) {\r\n            console.log(\"upload\")\r\n            // Perform the upload first and wait for it to complete\r\n            await this.uploadImage()\r\n          }\r\n          // 收款时将付款信息清空,付款时将收款信息清空\r\n          this.clearReceiveOrPay()\r\n          if (this.form.bankRecordId != null) {\r\n            updateBankrecord(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addBankrecord(this.form).then(response => {\r\n              this.form = response.data\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              // this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    uploadImage() {\r\n      return new Promise((resolve, reject) => {\r\n        this.customHttpRequest({\r\n          file: this.imageFile,\r\n          onSuccess: (response) => {\r\n            this.handleSuccess(response)\r\n            resolve(response)\r\n          },\r\n          onError: (error) => {\r\n            this.handleError(error)\r\n            reject(error)\r\n          }\r\n        })\r\n      })\r\n    }\r\n    ,\r\n    clearReceiveOrPay() {\r\n      if (this.form.isRecievingOrPaying === \"0\") {\r\n        // 初始化收款\r\n        this.form.actualBankPaidAmount = 0\r\n        this.form.bankPaidHandlingFee = 0\r\n        this.form.sqdBillPaidAmount = 0\r\n        this.form.billPaidWriteoffAmount = 0\r\n        this.form.bankPaidExchangeLost = 0\r\n        this.form.sqdBillPaidWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillRecievedWriteoffBalance === this.form.sqdBillRecievedAmount ?\r\n          this.form.writeoffStatus = -1 : this.form.sqdBillRecievedWriteoffBalance === 0 ?\r\n            this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      } else {\r\n        // 初始化付款\r\n        this.form.actualBankRecievedAmount = 0\r\n        this.form.bankRecievedHandlingFee = 0\r\n        this.form.sqdBillRecievedAmount = 0\r\n        this.form.billRecievedWriteoffAmount = 0\r\n        this.form.bankRecievedExchangeLost = 0\r\n        this.form.sqdBillRecievedWriteoffBalance = 0\r\n        // 销账状态\r\n        this.form.sqdBillPaidAmount === this.form.sqdBillPaidWriteoffBalance ?\r\n          this.form.writeoffStatus = -1 : this.form.sqdBillPaidWriteoffBalance === 0 ?\r\n            this.form.writeoffStatus = 0 : this.form.writeoffStatus = 1\r\n      }\r\n    }\r\n    ,\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const bankRecordIds = row.bankRecordId || this.ids\r\n      this.$confirm(\"是否确认删除记录公司账户出入账明细编号为\\\"\" + bankRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delBankrecord(bankRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    }\r\n    ,\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/bankrecord/export\", {\r\n        ...this.queryParams\r\n      }, `bankrecord_${new Date().getTime()}.xlsx`)\r\n    }\r\n    ,\r\n    staffNormalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      let l\r\n      if (node.staff) {\r\n        if (node.staff.staffFamilyLocalName == null && node.staff.staffGivingLocalName == null) {\r\n          if (node.role.roleLocalName != null) {\r\n            l = node.role.roleLocalName + \",\" + pinyin.getFullChars(node.role.roleLocalName)\r\n          } else {\r\n            l = node.dept.deptLocalName + \",\" + pinyin.getFullChars(node.dept.deptLocalName)\r\n          }\r\n        } else {\r\n          l = node.staff.staffCode + \" \" + node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName + \" \" + node.staff.staffGivingEnName + \",\" + pinyin.getFullChars(node.staff.staffFamilyLocalName + node.staff.staffGivingLocalName)\r\n        }\r\n      }\r\n      if (node.roleId) {\r\n        return {\r\n          id: node.roleId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      } else {\r\n        return {\r\n          id: node.deptId,\r\n          label: l,\r\n          children: node.children,\r\n          isDisabled: node.staffId == null && node.children == undefined\r\n        }\r\n      }\r\n    }\r\n    ,\r\n    loadSales() {\r\n      if (this.$store.state.data.salesList.length == 0 || this.$store.state.data.redisList.salesList) {\r\n        store.dispatch(\"getSalesList\").then(() => {\r\n          this.belongList = this.$store.state.data.salesList\r\n        })\r\n      } else {\r\n        this.belongList = this.$store.state.data.salesList\r\n      }\r\n    }\r\n    ,\r\n    loadStaff() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    }\r\n    ,\r\n    handledbClick(row) {\r\n      this.handleUpdate(row)\r\n    }\r\n    ,\r\n    selectCompany(company) {\r\n      this.form.clearingCompanyId = company.companyId\r\n      this.form.sqdClearingCompanyShortname = company.companyShortName\r\n      this.showCompany = false\r\n    }\r\n    ,\r\n    async getExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re\r\n      let response\r\n      if (this.exchangeRateList && this.exchangeRateList.length > 0) {\r\n        response = this.exchangeRateList\r\n      } else {\r\n        this.exchangeRateList = await selectListExchangerate()\r\n        response = this.exchangeRateList\r\n      }\r\n\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n          if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n            re = [0, currency(a.buyRate).divide(a.base).value]\r\n          } else {\r\n            re = [1, currency(a.sellRate).divide(a.base).value]\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    }\r\n    ,\r\n    async getBillDataExchangeRate(overseaCurrency, localCurrency, valueDate) {\r\n      let re = [0, 1]\r\n      let response = await selectListExchangerate()\r\n      if (overseaCurrency !== null && localCurrency !== null) {\r\n        for (let a of response.data) {\r\n          if ((a.localCurrency == localCurrency || a.currency == localCurrency)\r\n            && (a.currency == overseaCurrency || a.localCurrency == overseaCurrency)\r\n            && parseTime(a.validFrom) <= parseTime(valueDate)\r\n            && parseTime(valueDate) <= parseTime(a.validTo)) {\r\n            // (银行币种==外汇币种 and 账单币种==本地币种) -----> 买入\r\n            if (overseaCurrency === a.overseaCurrency && localCurrency === a.localCurrency) {\r\n              re = [0, currency(a.buyRate).divide(a.base).value]\r\n            } else {\r\n              re = [1, currency(a.sellRate).divide(a.base).value]\r\n            }\r\n          }\r\n        }\r\n        return re\r\n      }\r\n    }\r\n    ,\r\n    async exchangeRateShow(bankCurrencyCode, chargeCurrencyCode) {\r\n      if (bankCurrencyCode === chargeCurrencyCode) {\r\n        return 1\r\n      }\r\n      let result = await this.getExchangeRate(bankCurrencyCode, chargeCurrencyCode, \"\")\r\n    }\r\n    ,\r\n    getName(id) {\r\n      if (id) {\r\n        if (id) {\r\n          let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n          if (staff) {\r\n            return staff.staffFamilyLocalName + staff.staffGivingLocalName + staff.staffShortName\r\n          } else {\r\n            return \"\"\r\n          }\r\n        }\r\n      } else {\r\n        return \"\"\r\n      }\r\n    }\r\n    ,\r\n    selectStaff(row) {\r\n      this.form.sqdRsStaffId = row.staff.staffId\r\n    }\r\n    ,\r\n    checkSelectable(row) {\r\n      return row.isAccountConfirmed === \"1\"\r\n    }\r\n    ,\r\n    handleDialogOpened() {\r\n      // 在弹出层打开时，自动聚焦姓名输入框\r\n      this.$nextTick(() => {\r\n        const treeSelectInput = this.$refs.treeSelect.getInputElement()\r\n        if (treeSelectInput) {\r\n          treeSelectInput.focus()\r\n        }\r\n      })\r\n    }\r\n    ,\r\n    isRecievingOrPayingNormalizer(node) {\r\n      return {\r\n        id: node.value,\r\n        label: node.label\r\n      }\r\n    }\r\n    ,\r\n    selectBankAccount(row) {\r\n      this.form.sqdPaymentTitleCode = row.sqdBelongToCompanyCode\r\n    }\r\n    ,\r\n    customHttpRequest(options) {\r\n      const formData = new FormData()\r\n      formData.append(\"file\", options.file)\r\n\r\n      request({\r\n        url: \"/system/bankrecord/uploadImg\",\r\n        method: \"post\",\r\n        data: formData\r\n      }).then(response => {\r\n        options.onSuccess(response, options.file)\r\n        this.form.slipFile = response.url\r\n      }).catch(error => {\r\n        options.onError(error)\r\n      })\r\n    }\r\n    ,\r\n    handleChange(file, fileList) {\r\n      const extension = file.name.substring(file.name.lastIndexOf(\".\"))\r\n      const newFileName = `${this.form.bankRecordNo}${extension}`\r\n      this.imageFile = new File([file.raw], newFileName, {type: file.type})\r\n    }\r\n    ,\r\n    handleSuccess(response, file, fileList) {\r\n      this.form.slipFile = response.url\r\n    }\r\n    ,\r\n    handleError(err, file, fileList) {\r\n      this.$message.error(\"Upload failed:\", err)\r\n    }\r\n    ,\r\n    // 添加处理折算记账金额变化的方法\r\n    handleWriteoffChange(row, value) {\r\n      // 确保值为两位小数\r\n      row.writeoffFromBankBalance = Number(value).toFixed(2)\r\n\r\n      // 自动计算本次拟销账金额（根据汇率反算）\r\n      if (row.exchangeRate) {\r\n        // 获取subtotal的小数位数\r\n        const subtotalStr = row.subtotal.toString()\r\n        const decimalPlaces = subtotalStr.includes(\".\") ?\r\n          subtotalStr.split(\".\")[1].length : 0\r\n\r\n        // 根据subtotal的小数位数来格式化writeoffFromDnBalance\r\n        const calculatedValue = currency(value).divide(row.exchangeRate).value\r\n        row.writeoffFromDnBalance = Number(calculatedValue).toFixed(decimalPlaces)\r\n      }\r\n    }\r\n  }\r\n  ,\r\n  computed: {\r\n    receiveRate() {\r\n      return this.form.actualBankRecievedAmount + this.form.bankRecievedHandlingFee + this.form.bankRecievedExchangeLost\r\n    }\r\n    ,\r\n    paidRate() {\r\n      return this.form.actualBankPaidAmount + this.form.bankPaidHandlingFee + this.form.bankPaidExchangeLost\r\n    }\r\n    ,\r\n    isLocked() {\r\n      return this.form.isBankRecordLocked == 1\r\n    }\r\n    ,\r\n    isBankSlipConfirmed() {\r\n      return this.form.slipConfirmed == 1\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.table-btn-group {\r\n  display: flex;\r\n\r\n  .table-btn-left {\r\n    display: flex;\r\n    width: 100%;\r\n  }\r\n\r\n  .table-btn-right {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.total {\r\n  display: flex;\r\n  width: 60%;\r\n  margin-top: 10px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n::v-deep .el-input-number.is-without-controls .el-input__inner {\r\n  background-color: rgb(255, 242, 204) !important;\r\n}\r\n\r\n::v-deep .vue-treeselect__value-container {\r\n  display: block;\r\n}\r\n\r\n//固定el-table中行高度,不会被内容撑高\r\n::v-deep .cell {\r\n  overflow: hidden; /* 隐藏超出部分 */\r\n  white-space: nowrap; /* 禁止内容换行 */\r\n  text-overflow: ellipsis; /* 使用省略号表示超出的内容 */\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA40CA,IAAAA,WAAA,GAAAC,OAAA;AAQA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,cAAA,GAAAD,sBAAA,CAAAF,OAAA;AACA,IAAAI,SAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,MAAA,GAAAH,sBAAA,CAAAF,OAAA;AACA,IAAAM,SAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,SAAA,GAAAP,OAAA;AACA,IAAAQ,aAAA,GAAAR,OAAA;AACA,IAAAS,KAAA,GAAAT,OAAA;AACA,IAAAU,IAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,OAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,QAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,WAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,KAAA,GAAAd,OAAA;AACA,IAAAe,IAAA,GAAAf,OAAA;AACA,IAAAgB,UAAA,GAAAhB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAiB,IAAA;EACAC,UAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,UAAA,EAAAA,sBAAA;IAAAC,aAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA;MACAC,aAAA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACAI,OAAA;MACAC,UAAA;MACAC,SAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,MAAA;QACAC,OAAA;QACAC,QAAA;QACAC,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACAC,OAAA;MACA;MACAC,cAAA;MAEA;MACAC,IAAA;QACAjC,YAAA;MACA;MACAkC,UAAA;MACAC,YAAA;MACA;MACAC,KAAA;QACAjC,wBAAA,GACA;UAAAkC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAvB,cAAA,GACA;UAAAqB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAnC,oBAAA,GACA;UAAAiC,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QAEA;AACA;AACA;MACA;;MACAC,cAAA;MACAC,gBAAA;MACAC,gBAAA;MACAC,YAAA;MACAC,YAAA;MACAC,YAAA;MACAC,oBAAA;MACAC,cAAA;MACAC,GAAA;MACAC,MAAA;QACA;QACA3D,IAAA;QACA;QACAD,KAAA;QACA;QACA6D,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACAC,UAAA;MACAC,eAAA;MACAC,WAAA;MACAC,cAAA;MACAC,qBAAA;MACAC,wBAAA;MACAC,wBAAA;MACAC,aAAA;MACAC,OAAA;MACAC,mBAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,WAAA;MACAC,eAAA;MACAC,SAAA;MACAC,WAAA;MACAC,gBAAA;IACA;EACA;EACAC,KAAA;IACA1F,UAAA,WAAAA,WAAA2F,CAAA;MACA,IAAAA,CAAA;QACA,KAAAhG,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;IACA,8BAAAkG,iBAAAD,CAAA;MACA;MACA,IAAA3G,IAAA;MACA,KAAAI,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAA6G,cAAA,CAAAC,OAAA,WAAAC,IAAA;QACA,IAAAA,IAAA,CAAAjF,YAAA,IAAA6E,CAAA;UACA3G,IAAA,GAAA+G,IAAA;QACA,WAAAA,IAAA,CAAAC,QAAA;UACAD,IAAA,CAAAC,QAAA,CAAAF,OAAA,WAAAG,CAAA;YACA,IAAAA,CAAA,CAAAnF,YAAA,IAAA6E,CAAA;cACA3G,IAAA,GAAA+G,IAAA;YACA;UACA;QACA;MACA;MACA,IAAA/G,IAAA;QACA,IAAAA,IAAA,CAAA8B,YAAA,UAAA9B,IAAA,CAAAkH,QAAA;UACA,KAAAlD,UAAA;UACA,KAAAC,YAAA;QACA,WAAAjE,IAAA,CAAA8B,YAAA,UAAA9B,IAAA,CAAAkH,QAAA;UACA,KAAAlD,UAAA;UACA,KAAAC,YAAA;QACA;MACA;QACA,KAAAD,UAAA;MACA;IACA;IACA;IACA,0CAAAmD,6BAAAR,CAAA;MACA,KAAA5C,IAAA,CAAAxB,qBAAA,OAAA6E,iBAAA,OAAArD,IAAA,CAAA9B,wBAAA,EAAA6C,GAAA,MAAAf,IAAA,CAAA5B,uBAAA,EAAAkF,KAAA;IACA;IACA;IACA,yCAAAC,4BAAAX,CAAA;MACA,KAAA5C,IAAA,CAAAxB,qBAAA,OAAA6E,iBAAA,OAAArD,IAAA,CAAA9B,wBAAA,EAAA6C,GAAA,MAAAf,IAAA,CAAA5B,uBAAA,EAAAkF,KAAA;IACA;IACA;IACA,0CAAAE,6BAAAZ,CAAA;MACA,KAAA5C,IAAA,CAAApB,8BAAA,OAAAyE,iBAAA,OAAArD,IAAA,CAAAxB,qBAAA,EAAAiF,QAAA,MAAAzD,IAAA,CAAAtB,0BAAA,EAAA+E,QAAA,MAAAzD,IAAA,CAAA1B,wBAAA,EAAAgF,KAAA;IACA;IACA;IACA,sCAAAI,yBAAAd,CAAA;MACA,KAAA5C,IAAA,CAAAvB,iBAAA,OAAA4E,iBAAA,OAAArD,IAAA,CAAA7B,oBAAA,EAAAsF,QAAA,MAAAzD,IAAA,CAAA3B,mBAAA,EAAAiF,KAAA;IACA;IACA;IACA,qCAAAK,wBAAAf,CAAA;MACA,KAAA5C,IAAA,CAAAvB,iBAAA,OAAA4E,iBAAA,OAAArD,IAAA,CAAA7B,oBAAA,EAAAsF,QAAA,MAAAzD,IAAA,CAAA3B,mBAAA,EAAAiF,KAAA;IACA;IACA;IACA,sCAAAM,yBAAAhB,CAAA;MACA,KAAA5C,IAAA,CAAAnB,0BAAA,OAAAwE,iBAAA,OAAArD,IAAA,CAAAvB,iBAAA,EAAAgF,QAAA,MAAAzD,IAAA,CAAArB,sBAAA,EAAA8E,QAAA,MAAAzD,IAAA,CAAAzB,oBAAA,EAAA+E,KAAA;IACA;IAEA;IACA,uCAAAO,0BAAAjB,CAAA;MACA,KAAA5C,IAAA,CAAApB,8BAAA,OAAAyE,iBAAA,OAAArD,IAAA,CAAAxB,qBAAA,EAAAiF,QAAA,MAAAzD,IAAA,CAAAtB,0BAAA,EAAA+E,QAAA,MAAAzD,IAAA,CAAA1B,wBAAA,EAAAgF,KAAA;IACA;IACA;IACA,mCAAAQ,sBAAAlB,CAAA;MACA,KAAA5C,IAAA,CAAAnB,0BAAA,OAAAwE,iBAAA,OAAArD,IAAA,CAAAvB,iBAAA,EAAAgF,QAAA,MAAAzD,IAAA,CAAArB,sBAAA,EAAA8E,QAAA,MAAAzD,IAAA,CAAAzB,oBAAA,EAAA+E,KAAA;IACA;IAEA;IACA,4CAAAS,+BAAAnB,CAAA;MACA,KAAA5C,IAAA,CAAApB,8BAAA,OAAAyE,iBAAA,OAAArD,IAAA,CAAAxB,qBAAA,EAAAiF,QAAA,MAAAzD,IAAA,CAAAtB,0BAAA,EAAA+E,QAAA,MAAAzD,IAAA,CAAA1B,wBAAA,EAAAgF,KAAA;IACA;IACA;IACA,wCAAAU,2BAAApB,CAAA;MACA,KAAA5C,IAAA,CAAAnB,0BAAA,OAAAwE,iBAAA,OAAArD,IAAA,CAAAvB,iBAAA,EAAAgF,QAAA,MAAAzD,IAAA,CAAArB,sBAAA,EAAA8E,QAAA,MAAAzD,IAAA,CAAAzB,oBAAA,EAAA+E,KAAA;IACA;IACA3B,eAAA;MACAsC,OAAA,WAAAA,QAAAC,MAAA,EAAAC,MAAA;QAAA,IAAAC,KAAA;QACA;QACA,KAAApE,IAAA,CAAAtB,0BAAA;QACA;QACA,KAAAsB,IAAA,CAAArB,sBAAA;;QAEA;QACA,KAAAmD,qBAAA;QAEA,IAAApD,0BAAA;QACA,IAAAC,sBAAA;QAEA,IAAAqD,wBAAA;QACA,IAAAD,wBAAA;QACA;QACA,IAAAsC,SAAA;QACAH,MAAA,CAAAnB,OAAA,WAAAuB,GAAA;UACA,IAAAF,KAAA,CAAApE,IAAA,CAAAtC,mBAAA,IAAA4G,GAAA,CAAA5G,mBAAA;YACA2G,SAAA;UACA;QACA;QAEAH,MAAA,CAAAK,GAAA,WAAAvB,IAAA;UACA;UACA;UACA,IAAAA,IAAA,CAAApC,YAAA;YACA;YACA,IAAA4D,WAAA,GAAAxB,IAAA,CAAAyB,QAAA,CAAAC,QAAA;YACA,IAAAC,aAAA,GAAAH,WAAA,CAAAI,QAAA,QACAJ,WAAA,CAAAK,KAAA,SAAAC,MAAA;;YAEA;YACA,IAAAC,eAAA,OAAA1B,iBAAA,EAAAL,IAAA,CAAAgC,uBAAA,EAAAC,MAAA,CAAAjC,IAAA,CAAApC,YAAA,EAAA0C,KAAA;YACAN,IAAA,CAAAkC,qBAAA,GAAAC,MAAA,CAAAJ,eAAA,EAAAK,OAAA,CAAAT,aAAA;UACA;UAEA,IAAAP,KAAA,CAAApE,IAAA,CAAAtC,mBAAA,IAAAsF,IAAA,CAAAtF,mBAAA;YACA;YACAgB,0BAAA,OAAA2E,iBAAA,EAAAL,IAAA,CAAAgC,uBAAA;cAAAK,SAAA;YAAA,GAAAtE,GAAA,CAAArC,0BAAA,EAAA4E,KAAA;YACA;YACA3E,sBAAA,OAAA0E,iBAAA,EAAAL,IAAA,CAAAgC,uBAAA;cAAAK,SAAA;YAAA,GAAAtE,GAAA,CAAApC,sBAAA,EAAA2E,KAAA;YAEA,IAAAN,IAAA,CAAAsC,cAAA;cACAvD,wBAAA,OAAAsB,iBAAA,EAAAtB,wBAAA;gBAAAsD,SAAA;cAAA,GAAAtE,GAAA,CAAAiC,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;YACA;cACAtB,wBAAA,OAAAqB,iBAAA,EAAArB,wBAAA;gBAAAqD,SAAA;cAAA,GAAAtE,GAAA,CAAAiC,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;YACA;UACA,WAAAe,SAAA;YACA;YACA3F,0BAAA,OAAA2E,iBAAA,EAAAL,IAAA,CAAAgC,uBAAA;cAAAK,SAAA;YAAA,GAAAtE,GAAA,CAAArC,0BAAA,EAAA4E,KAAA;YACA;YACA3E,sBAAA,OAAA0E,iBAAA,EAAAL,IAAA,CAAAgC,uBAAA;cAAAK,SAAA;YAAA,GAAAtE,GAAA,CAAApC,sBAAA,EAAA2E,KAAA;YAEA,IAAAN,IAAA,CAAAsC,cAAA;cACAvD,wBAAA,OAAAsB,iBAAA,EAAAtB,wBAAA;gBAAAsD,SAAA;cAAA,GAAAtE,GAAA,CAAAiC,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;YACA;cACAtB,wBAAA,OAAAqB,iBAAA,EAAArB,wBAAA;gBAAAqD,SAAA;cAAA,GAAAtE,GAAA,CAAAiC,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;YACA;UACA;YACA;YACA5E,0BAAA,OAAA2E,iBAAA,EAAA3E,0BAAA;cAAA2G,SAAA;YAAA,GAAA5B,QAAA,CAAAT,IAAA,CAAAgC,uBAAA,EAAA1B,KAAA;YACA;YACA3E,sBAAA,OAAA0E,iBAAA,EAAA1E,sBAAA;cAAA0G,SAAA;YAAA,GAAA5B,QAAA,CAAAT,IAAA,CAAAgC,uBAAA,EAAA1B,KAAA;YAEA,IAAAN,IAAA,CAAAsC,cAAA;cACAvD,wBAAA,OAAAsB,iBAAA,EAAAtB,wBAAA;gBAAAsD,SAAA;cAAA,GAAA5B,QAAA,CAAAT,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;YACA;cACAtB,wBAAA,OAAAqB,iBAAA,EAAArB,wBAAA;gBAAAqD,SAAA;cAAA,GAAA5B,QAAA,CAAAT,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;YACA;UACA;;UAEA;UACAc,KAAA,CAAAvC,cAAA;UAEA,IAAAwB,iBAAA,EAAAL,IAAA,CAAAwC,UAAA,EAAAC,QAAA,CAAAzC,IAAA,CAAA0C,QAAA,EAAApC,KAAA,KAAAN,IAAA,CAAAkC,qBAAA,GAAAlC,IAAA,CAAAlE,cAAA,SAAAkE,IAAA,CAAAkC,qBAAA,OAAAlC,IAAA,CAAAlE,cAAA,SAAAkE,IAAA,CAAAlE,cAAA;QACA;;QAEA;QACA,KAAAkB,IAAA,CAAAtB,0BAAA,OAAA2E,iBAAA,EAAA3E,0BAAA;UAAA2G,SAAA;QAAA,GAAA/B,KAAA;QACA;QACA,KAAAtD,IAAA,CAAArB,sBAAA,OAAA0E,iBAAA,EAAA1E,sBAAA;UAAA0G,SAAA;QAAA,GAAA/B,KAAA;QAEA,KAAAtB,wBAAA,OAAAqB,iBAAA,EAAArB,wBAAA;UAAAqD,SAAA;QAAA,GAAA/B,KAAA;QACA,KAAAvB,wBAAA,OAAAsB,iBAAA,EAAAtB,wBAAA;UAAAsD,SAAA;QAAA,GAAA/B,KAAA;MAEA;MACAqC,IAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;IACA;IACA;IACA;IACA;IACA;IACAC,cAAA,CAAAC,QAAA;IACA,SAAA1J,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAyG,gBAAA,CAAAoC,MAAA,cAAAzI,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAA+J,SAAA,CAAAtD,gBAAA;MACAoD,cAAA,CAAAC,QAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IAAA,IAAAC,MAAA;IAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;MAAA,WAAAF,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAC,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;UAAA;YAAAF,QAAA,CAAAE,IAAA;YAAA,OACA,IAAAC,oCAAA;UAAA;YAAAX,MAAA,CAAAxD,gBAAA,GAAAgE,QAAA,CAAAI,IAAA;UAAA;UAAA;YAAA,OAAAJ,QAAA,CAAAK,IAAA;QAAA;MAAA,GAAAR,OAAA;IAAA;EACA;EACAS,WAAA,WAAAA,YAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACA,IAAAA,IAAA;QACA,KAAAC,YAAA;QACA,KAAAD,IAAA,GAAAA,IAAA;MACA;QACA,KAAAE,QAAA,CAAAC,IAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAAC,iBAAA;UACA;QACA;UACA,KAAAC,mBAAA;UACA;MACA;IACA;IACAA,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,KAAAzL,aAAA;MACA,KAAA8F,aAAA;MAEA,IAAA4F,gCAAA;QACA3F,OAAA,OAAAlC,IAAA,CAAAN,YAAA;QACAF,kBAAA,OAAAQ,IAAA,CAAAR,kBAAA;QACAsI,YAAA,OAAA9H,IAAA,CAAA8H;MACA,GAAAC,IAAA,WAAAC,QAAA;QACAJ,MAAA,CAAAzL,aAAA,GAAA6L,QAAA,CAAAC,IAAA;QAEAL,MAAA,CAAA3F,aAAA;QACA2F,MAAA,CAAAlG,UAAA;QAEAkG,MAAA,CAAAhG,WAAA;QACAgG,MAAA,CAAAM,SAAA;UACAN,MAAA,CAAAzL,aAAA,CAAAoI,GAAA,WAAAvB,IAAA;YACA4E,MAAA,CAAAhG,WAAA,OAAAyB,iBAAA,EAAAL,IAAA,CAAAmF,cAAA,EAAApH,GAAA,CAAA6G,MAAA,CAAAhG,WAAA,EAAA0B,KAAA;YACA,IAAAN,IAAA,CAAA8E,YAAA,KAAAF,MAAA,CAAA5H,IAAA,CAAA8H,YAAA;cACAF,MAAA,CAAAQ,KAAA,CAAAC,sBAAA,CAAAC,kBAAA,CAAAtF,IAAA;YACA;UACA;QACA;QAEA4E,MAAA,CAAAzL,aAAA,CAAAoM,IAAA,WAAAC,CAAA,EAAAC,CAAA;UACA,IAAAD,CAAA,CAAAV,YAAA,KAAAF,MAAA,CAAA5H,IAAA,CAAA8H,YAAA,IAAAW,CAAA,CAAAX,YAAA,KAAAF,MAAA,CAAA5H,IAAA,CAAA8H,YAAA;YACA;UACA,WAAAU,CAAA,CAAAV,YAAA,KAAAF,MAAA,CAAA5H,IAAA,CAAA8H,YAAA,IAAAW,CAAA,CAAAX,YAAA,KAAAF,MAAA,CAAA5H,IAAA,CAAA8H,YAAA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAY,uBAAA,WAAAA,wBAAAC,GAAA,EAAAC,QAAA;MACA;MACAD,GAAA,CAAAE,gBAAA,GAAAD,QAAA;IAEA;IAEAE,SAAA,WAAAA,UAAAxF,KAAA;MACA,QAAAyF,KAAA,CAAA5D,MAAA,CAAA7B,KAAA;IACA;IAEA0F,oBAAA,WAAAA,qBAAAL,GAAA;MACA;MACA,IAAAM,OAAA;MACA,IAAAC,OAAA;MAEA,IAAA5F,KAAA,GAAAqF,GAAA,CAAAE,gBAAA;MACA,SAAAC,SAAA,CAAAxF,KAAA;QACA;QACA,IAAAA,MAAA,GAAA6F,UAAA,CAAAR,GAAA,CAAAE,gBAAA;QACA,IAAAE,KAAA,CAAAzF,MAAA,KAAAA,MAAA,GAAA2F,OAAA,IAAA3F,MAAA,GAAA4F,OAAA;UACA,KAAA5B,QAAA,CAAA8B,KAAA,mCAAAC,MAAA,CAAAJ,OAAA,cAAAI,MAAA,CAAAH,OAAA;UACA;UACAP,GAAA,CAAAE,gBAAA,GAAAF,GAAA,CAAA/H,YAAA,CAAA8D,QAAA;QACA;UACA;UACAiE,GAAA,CAAA/H,YAAA,GAAA0C,MAAA;QACA;MACA;QACA;QACA;QACA,IAAAgG,KAAA,GAAAhG,KAAA,CAAAgG,KAAA;QACA,IAAAA,KAAA;UACA,IAAAC,IAAA,GAAAJ,UAAA,CAAAG,KAAA;UACA,IAAAC,IAAA;YACAZ,GAAA,CAAA/H,YAAA,QAAA2I,IAAA,EAAAnE,OAAA;UACA;QACA;MACA;IACA;IAEAoE,cAAA,WAAAA,eAAAb,GAAA;MAAA,IAAAc,MAAA;MAAA,WAAAtD,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAoD,SAAA;QAAA,WAAArD,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAmD,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjD,IAAA,GAAAiD,SAAA,CAAAhD,IAAA;YAAA;cAAAgD,SAAA,CAAAjD,IAAA;cAAAiD,SAAA,CAAAhD,IAAA;cAAA,OAGA,IAAAiD,kBAAA;gBAAAvI,GAAA,EAAAqH,GAAA,CAAAmB;cAAA;YAAA;cAAAF,SAAA,CAAAhD,IAAA;cAAA;YAAA;cAAAgD,SAAA,CAAAjD,IAAA;cAAAiD,SAAA,CAAAG,EAAA,GAAAH,SAAA;cAAAA,SAAA,CAAAhD,IAAA;cAAA,OAGA,IAAAoD,4BAAA;gBACAlC,YAAA,EAAAa,GAAA,CAAAb,YAAA;gBACAgC,QAAA;gBACApM,mBAAA,EAAA+L,MAAA,CAAAhC,IAAA;gBACAwC,YAAA,EAAAtB,GAAA,CAAAsB;cACA;YAAA;cAAAL,SAAA,CAAAhD,IAAA;cAAA,OAGA6C,MAAA,CAAA5D,OAAA;YAAA;YAAA;cAAA,OAAA+D,SAAA,CAAA7C,IAAA;UAAA;QAAA,GAAA2C,QAAA;MAAA;IACA;IAEAQ,YAAA,WAAAA,aAAAzC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAA5B,OAAA;UACA;QACA;UACA;MACA;IACA;IAEAsE,SAAA,EAAAA,eAAA;IACAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA;;MAEA,KAAAC,iBAAA;MACA,SAAAtK,IAAA,CAAAuK,aAAA;QACA,KAAAvK,IAAA,CAAAuK,aAAA;QACA,IAAAP,4BAAA,OAAAhK,IAAA,EAAA+H,IAAA,WAAAC,QAAA;UACAqC,MAAA,CAAA/C,QAAA,CAAAkD,OAAA;QACA;MACA;QACA,KAAAxK,IAAA,CAAAuK,aAAA;QACA,IAAAP,4BAAA,OAAAhK,IAAA,EAAA+H,IAAA,WAAAC,QAAA;UACAqC,MAAA,CAAA/C,QAAA,CAAAkD,OAAA;QACA;MACA;IAEA;IAEAC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAJ,iBAAA;MACA;MACA;AACA;AACA;AACA;AACA;MACA,KAAAtK,IAAA,CAAAH,YAAA,OAAA8K,eAAA,IAAAC,MAAA;MACA,IAAAZ,4BAAA,OAAAhK,IAAA,EAAA+H,IAAA,WAAAC,QAAA;QACA0C,MAAA,CAAAG,MAAA,CAAAC,UAAA;QACAJ,MAAA,CAAArN,IAAA;QACAqN,MAAA,CAAA7E,OAAA;MACA;;MAEA;MACA,IAAAkF,aAAA,QAAA5I,mBAAA,CAAA6I,MAAA,WAAArC,GAAA;QACA,QAAA+B,MAAA,CAAA/I,eAAA,CAAAsJ,IAAA,WAAAC,QAAA;UAAA,OAAAA,QAAA,CAAAC,QAAA,KAAAxC,GAAA,CAAAwC,QAAA;QAAA;MACA;MACA,IAAAC,0BAAA;QAAAC,YAAA,EAAAN;MAAA;;MAEA;MACA,IAAAO,sBAAA;MACA,KAAA3J,eAAA,QAAAA,eAAA,CAAA4C,GAAA,WAAAvB,IAAA;QACA;QACAA,IAAA,CAAAuC,oBAAA,OAAAlC,iBAAA,MAAAA,iBAAA,EAAAL,IAAA,CAAAwC,UAAA,EAAAC,QAAA,CAAAzC,IAAA,CAAA0C,QAAA,GAAAjC,QAAA,CAAAT,IAAA,CAAAkC,qBAAA,EAAA5B,KAAA;QACA;QACAN,IAAA,CAAAuI,oBAAA,GAAAb,MAAA,CAAA1K,IAAA,CAAA/B,gBAAA;QACA+E,IAAA,CAAAlE,cAAA,OAAAuE,iBAAA,EAAAL,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;QACA,IAAAkI,qBAAA;QACAA,qBAAA,CAAAL,QAAA,GAAAnI,IAAA,CAAAmI,QAAA;QACAK,qBAAA,CAAA1D,YAAA,GAAA4C,MAAA,CAAA1K,IAAA,CAAA8H,YAAA;QACA0D,qBAAA,CAAAtG,qBAAA,GAAAlC,IAAA,CAAAkC,qBAAA;QACAsG,qBAAA,CAAAC,mBAAA,GAAAzI,IAAA,CAAA6F,gBAAA;QACA2C,qBAAA,CAAAxG,uBAAA,GAAAhC,IAAA,CAAAgC,uBAAA;QACAwG,qBAAA,CAAAE,WAAA,GAAA1I,IAAA,CAAApC,YAAA;QAEA0K,sBAAA,CAAAK,IAAA,CAAAH,qBAAA;QAEA,OAAAxI,IAAA;MACA;MAEA,IAAA4I,wBAAA;QACAP,YAAA,OAAA1J,eAAA;QACAkK,yBAAA,EAAAP;MACA,GAAAvD,IAAA,WAAAC,QAAA;QACA;QACA,IAAA8D,gBAAA,EAAApB,MAAA,CAAA1K,IAAA,CAAAR,kBAAA,CAAAqF,KAAA;MACA;IAEA;IACAkH,iBAAA,WAAAA,kBAAAzI,KAAA,EAAA0I,YAAA;MACA,WAAA3I,iBAAA,EAAAC,KAAA;QACA2I,SAAA;QACA;QACAC,MAAA,EAAAF,YAAA;QACA3G,SAAA;MACA,GAAAuF,MAAA;IACA;IAEAuB,eAAA,WAAAA,gBAAA;MACA;AACA;AACA;AACA;AACA;IAJA,CAKA;IAEAC,aAAA,WAAAA,cAAA,GACA;IAEAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,WAAA,QAAAvM,IAAA,CAAAtC,mBAAA;MACA,IAAA8O,WAAA,GAAAD,WAAA;MAEA,IAAAE,qBAAA,MAAAC,cAAA,CAAAtG,OAAA,MAAAsG,cAAA,CAAAtG,OAAA,WAAApG,IAAA;QAAAtC,mBAAA,EAAA8O;MAAA,IAAAzE,IAAA,WAAAC,QAAA;QACAsE,MAAA,CAAA7J,WAAA,GAAAuF,QAAA,GAAAA,QAAA,CAAAgD,MAAA,WAAAhI,IAAA;UAAA,OACA,CAAAsJ,MAAA,CAAApQ,YAAA,CAAA+O,IAAA,WAAA0B,QAAA;YAAA,OAAAA,QAAA,CAAAxB,QAAA,KAAAnI,IAAA,CAAAmI,QAAA;UAAA;QAAA,CACA;MACA;IACA;IAEAyB,aAAA,WAAAA,cAAA,GACA;IAEAC,KAAA,WAAAA,MAAA,GACA;IAEAxJ,QAAA,EAAAA,iBAAA;IACAqE,iBAAA,WAAAA,kBAAA;MAAA,IAAAoF,MAAA;MAAA,WAAA3G,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAyG,SAAA;QAAA,IAAAjO,cAAA,EAAAkJ,QAAA,EAAAgF,SAAA,EAAAC,KAAA,EAAAjK,IAAA,EAAAkK,MAAA;QAAA,WAAA7G,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA2G,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAzG,IAAA,GAAAyG,SAAA,CAAAxG,IAAA;YAAA;cACAkG,MAAA,CAAA5Q,YAAA;cACA4Q,MAAA,CAAA3K,mBAAA;cACA2K,MAAA,CAAA7K,aAAA;cACA;;cAEA,IAAA6K,MAAA,CAAA9M,IAAA,CAAAtC,mBAAA,YAAAoP,MAAA,CAAA9M,IAAA,CAAApB,8BAAA;gBACAE,cAAA;cACA,WAAAgO,MAAA,CAAA9M,IAAA,CAAAtC,mBAAA,YAAAoP,MAAA,CAAA9M,IAAA,CAAAnB,0BAAA;gBACAC,cAAA;cACA,WAAAgO,MAAA,CAAA9M,IAAA,CAAAtB,0BAAA,aAAAoO,MAAA,CAAA9M,IAAA,CAAArB,sBAAA;gBACA;gBACAG,cAAA;cACA;cAAAsO,SAAA,CAAAxG,IAAA;cAAA,OAEA,IAAAyG,0BAAA;gBACAxP,iBAAA,EAAAiP,MAAA,CAAA9M,IAAA,CAAAnC,iBAAA;gBACAH,mBAAA,EAAAoP,MAAA,CAAA9M,IAAA,CAAAtC,mBAAA;gBACAoK,YAAA,EAAAgF,MAAA,CAAA9M,IAAA,CAAA8H,YAAA;gBACAhJ,cAAA,EAAAA,cAAA;gBACAU,kBAAA,EAAAsN,MAAA,CAAA9M,IAAA,CAAAR;cACA;YAAA;cANAwI,QAAA,GAAAoF,SAAA,CAAAtG,IAAA;cAAA,MAOAkB,QAAA,IAAAA,QAAA,CAAAlD,MAAA;gBAAAsI,SAAA,CAAAxG,IAAA;gBAAA;cAAA;cAAAoG,SAAA,OAAAM,2BAAA,CAAAlH,OAAA,EAEA4B,QAAA;cAAAoF,SAAA,CAAAzG,IAAA;cAAAqG,SAAA,CAAAO,CAAA;YAAA;cAAA,KAAAN,KAAA,GAAAD,SAAA,CAAApK,CAAA,IAAA4K,IAAA;gBAAAJ,SAAA,CAAAxG,IAAA;gBAAA;cAAA;cAAA5D,IAAA,GAAAiK,KAAA,CAAA3J,KAAA;cAAA,MACAwJ,MAAA,CAAA9M,IAAA,CAAA/B,gBAAA,KAAA+E,IAAA,CAAAsC,cAAA;gBAAA8H,SAAA,CAAAxG,IAAA;gBAAA;cAAA;cACA5D,IAAA,CAAA6F,gBAAA;cACA7F,IAAA,CAAApC,YAAA;cAAAwM,SAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,SAAA,CAAAxG,IAAA;cAAA,OAEAkG,MAAA,CAAAW,eAAA,CAAAX,MAAA,CAAA9M,IAAA,CAAA/B,gBAAA,EAAA+E,IAAA,CAAAsC,cAAA;YAAA;cAAA4H,MAAA,GAAAE,SAAA,CAAAtG,IAAA;cAAA,IACAoG,MAAA;gBAAAE,SAAA,CAAAxG,IAAA;gBAAA;cAAA;cACA;cACAkG,MAAA,CAAAxF,QAAA,CAAA8B,KAAA;cACA0D,MAAA,CAAA7K,aAAA;cAAA,OAAAmL,SAAA,CAAAM,MAAA;YAAA;cAGA;cACA;cACA1K,IAAA,CAAApC,YAAA,GAAAsM,MAAA,YAAA/H,MAAA,CAAA+H,MAAA,KAAA9H,OAAA,MAAAD,MAAA,CAAA2H,MAAA,CAAAa,qBAAA,IAAA1I,MAAA,CAAAiI,MAAA,KAAA5J,KAAA,EAAA8B,OAAA;cACApC,IAAA,CAAA6F,gBAAA,GAAAqE,MAAA,YAAAA,MAAA,aAAAA,MAAA;YAAA;cAAAE,SAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,SAAA,CAAAxG,IAAA;cAAA;YAAA;cAAAwG,SAAA,CAAAzG,IAAA;cAAAyG,SAAA,CAAArD,EAAA,GAAAqD,SAAA;cAAAJ,SAAA,CAAAY,CAAA,CAAAR,SAAA,CAAArD,EAAA;YAAA;cAAAqD,SAAA,CAAAzG,IAAA;cAAAqG,SAAA,CAAAa,CAAA;cAAA,OAAAT,SAAA,CAAAU,MAAA;YAAA;cAGAhB,MAAA,CAAA5Q,YAAA,GAAA8L,QAAA;cACA+F,UAAA;gBACAjB,MAAA,CAAA5Q,YAAA,CAAAqI,GAAA,WAAAvB,IAAA;kBACA;kBACA,IAAAA,IAAA,CAAAwI,qBAAA,CAAAwC,eAAA;oBACA;oBACA;oBACA;oBACAhL,IAAA,CAAAkC,qBAAA,GAAAlC,IAAA,CAAAwI,qBAAA,CAAAtG,qBAAA;oBACAlC,IAAA,CAAAuC,oBAAA,GAAAvC,IAAA,CAAAwI,qBAAA,CAAAjG,oBAAA;oBACA;oBACAvC,IAAA,CAAAgC,uBAAA,GAAAhC,IAAA,CAAAwI,qBAAA,CAAAxG,uBAAA;oBAEA8H,MAAA,CAAAnL,eAAA,CAAAgK,IAAA,CAAA3I,IAAA;oBACA8J,MAAA,CAAA3K,mBAAA,CAAAwJ,IAAA,CAAA3I,IAAA;;oBAEA;oBACA,IAAAA,IAAA,CAAAiL,kBAAA;sBACAjL,IAAA,CAAAiL,kBAAA;oBACA;;oBAEA;oBACAnB,MAAA,CAAA5E,SAAA;sBACA4E,MAAA,CAAA1E,KAAA,CAAA8F,mBAAA,CAAA5F,kBAAA,CAAAtF,IAAA;oBACA;kBACA;gBACA;;gBAEA;gBACA8J,MAAA,CAAA5Q,YAAA,CAAAqM,IAAA,WAAAC,CAAA,EAAAC,CAAA;kBACA;kBACA,IAAAD,CAAA,CAAAyF,kBAAA,YAAAxF,CAAA,CAAAwF,kBAAA;oBACA;kBACA;kBACA,IAAAzF,CAAA,CAAAyF,kBAAA,YAAAxF,CAAA,CAAAwF,kBAAA;oBACA;kBACA;;kBAEA;kBACA,IAAAzF,CAAA,CAAAgD,qBAAA,CAAAwC,eAAA,aAAAvF,CAAA,CAAA+C,qBAAA,CAAAwC,eAAA;oBACA;kBACA;kBACA;kBACA,IAAAxF,CAAA,CAAAgD,qBAAA,CAAAwC,eAAA,aAAAvF,CAAA,CAAA+C,qBAAA,CAAAwC,eAAA;oBACA;kBACA;kBACA;kBACA,IAAAxF,CAAA,CAAAgD,qBAAA,CAAAwC,eAAA,IAAAvF,CAAA,CAAA+C,qBAAA,CAAAwC,eAAA;oBACA;oBACA,OAAAxF,CAAA,CAAAgD,qBAAA,CAAAjG,oBAAA,GAAAkD,CAAA,CAAA+C,qBAAA,CAAAjG,oBAAA;kBACA;oBACA;oBACA;kBACA;gBACA;;gBAEA;gBACAuH,MAAA,CAAA5E,SAAA;kBACA4E,MAAA,CAAA3K,mBAAA,CAAAY,OAAA,WAAAC,IAAA;oBACA8J,MAAA,CAAA1E,KAAA,CAAA8F,mBAAA,CAAA5F,kBAAA,CAAAtF,IAAA;kBACA;gBACA;cACA;YAAA;cAEA8J,MAAA,CAAA7K,aAAA;cACA6K,MAAA,CAAApL,UAAA;YAAA;YAAA;cAAA,OAAA0L,SAAA,CAAArG,IAAA;UAAA;QAAA,GAAAgG,QAAA;MAAA;IACA;IAEAY,qBAAA,WAAAA,sBAAArK,KAAA;MAAA,IAAA+B,SAAA,GAAA8I,SAAA,CAAArJ,MAAA,QAAAqJ,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,WAAA9K,iBAAA,EAAAC,KAAA;QAAA+B,SAAA,EAAAA;MAAA;IACA;IAEAgJ,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA;AACA;AACA;AACA;MACA,SAAAtO,IAAA,CAAAX,kBAAA;QACA,KAAAW,IAAA,CAAAX,kBAAA;QACA,KAAAW,IAAA,CAAAuO,QAAA;QACA,KAAAvO,IAAA,CAAAwO,UAAA;MACA;QACA,KAAAxO,IAAA,CAAAX,kBAAA;QACA,KAAAW,IAAA,CAAAuO,QAAA,QAAAlS,MAAA,CAAAC,KAAA,CAAAmS,IAAA,CAAAC,GAAA;QACA,KAAA1O,IAAA,CAAAwO,UAAA,OAAA7D,eAAA,IAAAC,MAAA;MACA;MAEA,KAAAN,iBAAA;MACA,IAAAN,4BAAA,OAAAhK,IAAA,EAAA+H,IAAA,WAAAC,QAAA;QACAsG,MAAA,CAAAhH,QAAA,CAAAkD,OAAA;MACA;IACA;IAEA,sBACA3E,OAAA,WAAAA,QAAA;MAAA,IAAA8I,OAAA;MACA,KAAA9R,OAAA;MACA,IAAA+R,0BAAA,OAAAtR,WAAA,EAAAyK,IAAA,WAAAC,QAAA;QACA2G,OAAA,CAAAxR,cAAA,GAAA6K,QAAA,CAAAC,IAAA;QACA0G,OAAA,CAAAzR,KAAA,GAAA8K,QAAA,CAAA9K,KAAA;QACAyR,OAAA,CAAA9R,OAAA;MACA;IACA;IAEA;IACAgS,MAAA,WAAAA,OAAA;MACA,KAAAxR,IAAA;MACA,KAAAyR,KAAA;IACA;IAEA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAA9O,IAAA;QACA8H,YAAA;QACApK,mBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,2BAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,uBAAA;QACAC,mBAAA;QACAC,wBAAA;QACAC,oBAAA;QACAC,qBAAA;QACAC,iBAAA;QACAC,0BAAA;QACAC,sBAAA;QACAC,8BAAA;QACAC,0BAAA;QACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,SAAA;QACA8P,SAAA;QACA7P,gBAAA;QACAC,mBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,sBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,eAAA;QACAC,YAAA;QACAmP,UAAA;MACA;MACA,KAAA9M,OAAA;MACA,KAAAJ,qBAAA;MACA,KAAAmN,SAAA;IACA;IAEA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA5R,WAAA,CAAAE,OAAA;MACA,KAAAqI,OAAA;IACA;IAEA,aACAsJ,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IAEAE,kBAAA,WAAAA,mBAAAzG,GAAA;MAAA,IAAA0G,OAAA;MACA,IAAAC,IAAA,GAAA3G,GAAA,CAAA4G,MAAA;MACA,KAAAC,QAAA,WAAAF,IAAA,SAAAvH,IAAA;QACA,WAAA0H,wBAAA,EAAA9G,GAAA,CAAAb,YAAA,EAAAa,GAAA,CAAA4G,MAAA;MACA,GAAAxH,IAAA;QACAsH,OAAA,CAAAxE,MAAA,CAAAC,UAAA,CAAAwE,IAAA;MACA,GAAAI,KAAA;QACA/G,GAAA,CAAA4G,MAAA,GAAA5G,GAAA,CAAA4G,MAAA;MACA;IACA;IAEA;IACAI,4BAAA,WAAAA,6BAAAC,SAAA,GAEA;IAEA;IACAC,gBAAA,WAAAA,iBAAAlH,GAAA;MAAA,IAAAmH,OAAA;MAAA,WAAA3J,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAyJ,SAAA;QAAA,IAAAC,MAAA,EAAA9C,MAAA;QAAA,WAAA7G,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAyJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvJ,IAAA,GAAAuJ,SAAA,CAAAtJ,IAAA;YAAA;cACA;cACAoJ,MAAA,OAAAtD,cAAA,CAAAtG,OAAA,MAAAuC,GAAA,GAEA;cAAA,MACAmH,OAAA,CAAA9P,IAAA,CAAA/B,gBAAA,KAAA+R,MAAA,CAAA1K,cAAA;gBAAA4K,SAAA,CAAAtJ,IAAA;gBAAA;cAAA;cACAoJ,MAAA,CAAAnH,gBAAA;cACAmH,MAAA,CAAApP,YAAA;cAAAsP,SAAA,CAAAtJ,IAAA;cAAA;YAAA;cAAAsJ,SAAA,CAAAtJ,IAAA;cAAA,OAGAkJ,OAAA,CAAArC,eAAA,CAAAqC,OAAA,CAAA9P,IAAA,CAAA/B,gBAAA,EAAA+R,MAAA,CAAA1K,cAAA;YAAA;cAAA4H,MAAA,GAAAgD,SAAA,CAAApJ,IAAA;cAAA,IACAoG,MAAA;gBAAAgD,SAAA,CAAAtJ,IAAA;gBAAA;cAAA;cACA;cACAkJ,OAAA,CAAAxI,QAAA,CAAA8B,KAAA;cAAA,OAAA8G,SAAA,CAAAxC,MAAA;YAAA;cAGA;cACAsC,MAAA,CAAApP,YAAA,GAAAsM,MAAA,YAAA/H,MAAA,CAAA+H,MAAA,KAAA9H,OAAA,MAAAD,MAAA,CAAA2K,OAAA,CAAAnC,qBAAA,IAAA1I,MAAA,CAAAiI,MAAA,KAAA5J,KAAA,EAAA8B,OAAA;cACA4K,MAAA,CAAAnH,gBAAA,GAAAqE,MAAA,YAAAA,MAAA,IAAAxI,QAAA,YAAAwI,MAAA;YAAA;cAGA;cACA8C,MAAA,CAAA9K,qBAAA,OAAA7B,iBAAA,EAAA2M,MAAA,CAAAzK,oBAAA,EAAAjC,KAAA;cACA0M,MAAA,CAAAhL,uBAAA,OAAA3B,iBAAA,EAAA2M,MAAA,CAAAzK,oBAAA,EAAAE,QAAA,CAAAuK,MAAA,CAAApP,YAAA,EAAA0C,KAAA;;cAEA;cACA,KAAA0M,MAAA,CAAAxE,qBAAA;gBACAwE,MAAA,CAAAxE,qBAAA;kBACAwC,eAAA;kBACApO,eAAA;kBACAC,YAAA;gBACA;cACA;;cAEA;cACA;cACA;;cAEAiQ,OAAA,CAAA5T,YAAA,CAAAyP,IAAA,CAAAqE,MAAA;cACAF,OAAA,CAAArN,WAAA,GAAAqN,OAAA,CAAArN,WAAA,CAAAuI,MAAA,WAAAhI,IAAA;gBAAA,OAAAA,IAAA,CAAAmI,QAAA,KAAA6E,MAAA,CAAA7E,QAAA;cAAA;YAAA;YAAA;cAAA,OAAA+E,SAAA,CAAAnJ,IAAA;UAAA;QAAA,GAAAgJ,QAAA;MAAA;IACA;IAEA;IACAI,qBAAA,WAAAA,sBAAAP,SAAA;MAAA,IAAAQ,OAAA;MACA;MACA,KAAArT,MAAA,GAAA6S,SAAA,CAAA9K,MAAA;MACA,KAAA9H,QAAA,IAAA4S,SAAA,CAAA9K,MAAA;;MAEA;MACA,KAAAjD,cAAA;MACA;MACA,KAAAO,oBAAA;MACAwN,SAAA,CAAArL,GAAA,WAAAvB,IAAA;QACA;QACA,IAAAoN,OAAA,CAAAzO,eAAA,CAAA0O,OAAA,CAAArN,IAAA;UACA;UACA,IAAAA,IAAA,CAAAuC,oBAAA,aAAAvC,IAAA,CAAAuC,oBAAA;YACA;YACAvC,IAAA,CAAAkC,qBAAA,OAAA7B,iBAAA,EAAAL,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;YACA;YACAN,IAAA,CAAAgC,uBAAA,OAAA3B,iBAAA,EAAAL,IAAA,CAAAuC,oBAAA,EAAAE,QAAA,CAAAzC,IAAA,CAAApC,YAAA,EAAA0C,KAAA;UACA;YACAN,IAAA,CAAAkC,qBAAA,OAAA7B,iBAAA,EAAAL,IAAA,CAAAwC,UAAA,EAAAC,QAAA,CAAAzC,IAAA,CAAA0C,QAAA,EAAApC,KAAA;YACAN,IAAA,CAAAgC,uBAAA,OAAA3B,iBAAA,MAAAA,iBAAA,EAAAL,IAAA,CAAAwC,UAAA,EAAAC,QAAA,CAAAzC,IAAA,CAAA0C,QAAA,GAAAD,QAAA,CAAAzC,IAAA,CAAApC,YAAA,EAAA0C,KAAA;UACA;QACA;MACA;MACA;MACA,KAAA3B,eAAA,CAAA4C,GAAA,WAAAvB,IAAA;QACA,IAAA4M,SAAA,CAAAS,OAAA,CAAArN,IAAA;UACAA,IAAA,CAAAkC,qBAAA;UACAlC,IAAA,CAAAgC,uBAAA;UACAhC,IAAA,CAAAsN,wBAAA,OAAAjN,iBAAA,EAAAL,IAAA,CAAAuC,oBAAA,EAAAjC,KAAA;;UAEA;UACA,IAAA8M,OAAA,CAAAjO,mBAAA,CAAAkO,OAAA,CAAArN,IAAA;YACAoN,OAAA,CAAAhO,oBAAA,CAAAuJ,IAAA,CAAA3I,IAAA;UACA;QACA;MACA;MAEA,KAAArB,eAAA,GAAAiO,SAAA;IACA;IAEAW,OAAA,WAAAA,QAAA5H,GAAA;MAAA,IAAA6H,OAAA;MACA,KAAAlT,WAAA,CAAAM,eAAA,GAAA+K,GAAA,CAAA/K,eAAA;MACA,IAAAgR,0BAAA,OAAAtR,WAAA,EAAAyK,IAAA,WAAAC,QAAA;QACAwI,OAAA,CAAArT,cAAA,GAAA6K,QAAA,CAAAC,IAAA;QACAuI,OAAA,CAAAtT,KAAA,GAAA8K,QAAA,CAAA9K,KAAA;QACAsT,OAAA,CAAA3T,OAAA;QACA2T,OAAA,CAAAjQ,cAAA;MACA;IACA;IAEAkQ,oBAAA,WAAAA,qBAAA;MACA;MACA,IAAAC,UAAA;MACA,aAAA5P,cAAA;QACA;UACA4P,UAAA,CAAAC,GAAA,QAAArT,WAAA,CAAAC,MAAA,CAAAoT,GAAA;UACA,KAAAC,2BAAA;YAAArT,MAAA,EAAAmT;UAAA;UACA;QACA;UACAA,UAAA,CAAAG,KAAA,QAAAvT,WAAA,CAAAC,MAAA,CAAAsT,KAAA;UACA,KAAAD,2BAAA;YAAArT,MAAA,EAAAmT;UAAA;UACA;QACA;UACAA,UAAA,CAAAI,IAAA,QAAAxT,WAAA,CAAAC,MAAA,CAAAuT,IAAA;UACA,KAAAF,2BAAA;YAAArT,MAAA,EAAAmT;UAAA;UACA;QACA;UACAA,UAAA,CAAAK,SAAA,QAAAzT,WAAA,CAAAwC,OAAA,WAAAxC,WAAA,CAAAwC,OAAA;UACA4Q,UAAA,CAAAM,OAAA,QAAA1T,WAAA,CAAAwC,OAAA,WAAAxC,WAAA,CAAAwC,OAAA;UACA,KAAA8Q,2BAAA;YAAArT,MAAA,EAAAmT;UAAA;UACA;MAEA;IACA;IAEAO,qBAAA,WAAAA,sBAAA;MACA,KAAA1Q,cAAA;MACA,KAAAqQ,2BAAA;IACA;IAEAA,2BAAA,WAAAA,4BAAA3U,IAAA;MAAA,IAAAiV,OAAA;MACA,KAAAjV,IAAA;QACAA,IAAA;MACA;MACA,IAAAkV,oCAAA,EAAAlV,IAAA,EAAA8L,IAAA,WAAAC,QAAA;QACAkJ,OAAA,CAAAnR,cAAA,GAAAiI,QAAA,CAAA/L,IAAA;QACAiV,OAAA,CAAA1Q,gBAAA;QACA0Q,OAAA,CAAAzQ,gBAAA;QACAyQ,OAAA,CAAAxQ,YAAA;QACAwQ,OAAA,CAAAvQ,YAAA;QAEA,IAAAC,YAAA;QAAA,IAAAwQ,UAAA,OAAA9D,2BAAA,CAAAlH,OAAA,EACA8K,OAAA,CAAA7U,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAyG,gBAAA;UAAA2O,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAA7D,CAAA,MAAA8D,MAAA,GAAAD,UAAA,CAAAxO,CAAA,IAAA4K,IAAA;YAAA,IAAAhF,CAAA,GAAA6I,MAAA,CAAA/N,KAAA;YACA,KAAA1C,YAAA;cACA,IAAA4H,CAAA,CAAA8I,aAAA,cACA,SAAA9I,CAAA,CAAA+I,eAAA,IACA,IAAApH,eAAA,EAAA3B,CAAA,CAAAgJ,SAAA,SAAArH,eAAA,MAAAsH,IAAA,OACA,IAAAtH,eAAA,MAAAsH,IAAA,WAAAtH,eAAA,EAAA3B,CAAA,CAAAkJ,OAAA;gBACA9Q,YAAA,OAAAyC,iBAAA,EAAAmF,CAAA,CAAAmJ,UAAA,EAAA1M,MAAA,CAAAuD,CAAA,CAAAoJ,IAAA,EAAAtO,KAAA;cACA;YACA;UACA;QAAA,SAAAuO,GAAA;UAAAT,UAAA,CAAAxD,CAAA,CAAAiE,GAAA;QAAA;UAAAT,UAAA,CAAAvD,CAAA;QAAA;QACAqD,OAAA,CAAAtQ,YAAA,GAAAA,YAAA;QAEA,IAAAoH,QAAA,CAAA/L,IAAA;UACA+L,QAAA,CAAA/L,IAAA,CAAA8G,OAAA,WAAAC,IAAA;YACA,IAAAA,IAAA,CAAA/E,gBAAA;cACAiT,OAAA,CAAAzQ,gBAAA,IAAAuC,IAAA,CAAA8O,aAAA;cACAZ,OAAA,CAAAvQ,YAAA,IAAAqC,IAAA,CAAA+O,SAAA;YACA;YACA,IAAA/O,IAAA,CAAA/E,gBAAA;cACAiT,OAAA,CAAA1Q,gBAAA,IAAAwC,IAAA,CAAA8O,aAAA;cACAZ,OAAA,CAAAxQ,YAAA,IAAAsC,IAAA,CAAA+O,SAAA;YACA;UACA;;UAEA;UACAb,OAAA,CAAArQ,oBAAA,GAAAqQ,OAAA,CAAAc,6BAAA;QACA;MACA;IACA;IAEA;IACAA,6BAAA,WAAAA,8BAAA;MACA;MACA,UAAAjS,cAAA,SAAAA,cAAA,CAAA+E,MAAA;QACA;MACA;;MAEA;MACA,IAAAmN,UAAA,OAAAC,GAAA;;MAEA;MACA,KAAAnS,cAAA,CAAAgD,OAAA,WAAAC,IAAA;QACA;QACA,IAAAmP,SAAA,GAAAnP,IAAA,CAAArF,mBAAA;QACA,IAAA0F,QAAA,GAAAL,IAAA,CAAA/E,gBAAA;QACA,IAAAmU,QAAA,GAAAjN,MAAA,CAAAnC,IAAA,CAAA8O,aAAA;QACA,IAAAO,IAAA,GAAAlN,MAAA,CAAAnC,IAAA,CAAA+O,SAAA;;QAEA;QACA,KAAAE,UAAA,CAAAK,GAAA,CAAAH,SAAA;UACAF,UAAA,CAAAM,GAAA,CAAAJ,SAAA;YACAA,SAAA,EAAAA,SAAA;YACAK,SAAA,EAAAxP,IAAA,CAAAyP,mBAAA;YACAC,GAAA;cAAAN,QAAA;cAAAC,IAAA;cAAAM,OAAA;YAAA;YACAC,GAAA;cAAAR,QAAA;cAAAC,IAAA;cAAAM,OAAA;YAAA;UACA;QACA;;QAEA;QACA,IAAAE,MAAA,GAAAZ,UAAA,CAAAa,GAAA,CAAAX,SAAA;QACA,IAAA9O,QAAA;UACAwP,MAAA,CAAAH,GAAA,CAAAN,QAAA,IAAAA,QAAA;UACAS,MAAA,CAAAH,GAAA,CAAAL,IAAA,IAAAA,IAAA;UACAQ,MAAA,CAAAH,GAAA,CAAAC,OAAA,GAAAE,MAAA,CAAAH,GAAA,CAAAN,QAAA,GAAAS,MAAA,CAAAH,GAAA,CAAAL,IAAA;QACA,WAAAhP,QAAA;UACAwP,MAAA,CAAAD,GAAA,CAAAR,QAAA,IAAAA,QAAA;UACAS,MAAA,CAAAD,GAAA,CAAAP,IAAA,IAAAA,IAAA;UACAQ,MAAA,CAAAD,GAAA,CAAAD,OAAA,GAAAE,MAAA,CAAAD,GAAA,CAAAR,QAAA,GAAAS,MAAA,CAAAD,GAAA,CAAAP,IAAA;QACA;MACA;;MAEA;MACA,OAAAU,KAAA,CAAAC,IAAA,CAAAf,UAAA,CAAAgB,MAAA;IACA;IAEAC,YAAA,WAAAA,aAAA;MACA,KAAAlS,MAAA,CAAA5D,KAAA;MACA,KAAA4D,MAAA,CAAA3D,IAAA;IACA;IAEA;IACA8V,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAtS,MAAA,CAAAC,WAAA;IACA;IAEA;IACAsS,iBAAA,WAAAA,kBAAAvL,QAAA,EAAAqL,IAAA,EAAAC,QAAA;MACA,KAAAtS,MAAA,CAAA3D,IAAA;MACA,KAAA2D,MAAA,CAAAC,WAAA;MACA,KAAAmH,KAAA,CAAApH,MAAA,CAAAwS,UAAA;MACA,KAAAlM,QAAA,CAAAC,IAAA,CAAAS,QAAA,CAAAyL,GAAA;MACA,IAAAzL,QAAA,CAAAyL,GAAA;QACA,KAAAC,QAAA;MACA;MACA,KAAA7N,OAAA;IACA;IAEA;IACA8N,cAAA,WAAAA,eAAA;MACA,KAAAvL,KAAA,CAAApH,MAAA,CAAA4S,MAAA;IACA;IAEA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAA/E,KAAA;MACA,KAAApN,UAAA;MACA,KAAArE,IAAA;MACA,KAAAD,KAAA;MACA,KAAA2D,GAAA;MACA,KAAAf,IAAA,CAAAtC,mBAAA;MACA,KAAAsC,IAAA,CAAAhB,eAAA;MACA,KAAAgB,IAAA,CAAAgP,UAAA;MACA,KAAAhP,IAAA,CAAAjC,YAAA;IACA;IAEA,aACA+V,YAAA,WAAAA,aAAAnL,GAAA;MAAA,IAAAoL,OAAA;MACA,KAAAhT,GAAA;MACA,KAAA+N,KAAA;MACA,IAAAhH,YAAA,GAAAa,GAAA,CAAAb,YAAA,SAAAhL,GAAA;MACA,IAAAkX,yBAAA,EAAAlM,YAAA,EAAAC,IAAA,WAAAC,QAAA;QACA+L,OAAA,CAAA/T,IAAA,GAAAgI,QAAA,CAAA/L,IAAA;QACA8X,OAAA,CAAA/T,IAAA,CAAAgP,UAAA;QACA+E,OAAA,CAAA1W,IAAA;QACA0W,OAAA,CAAA3W,KAAA;QACA2W,OAAA,CAAAzR,WAAA,IAAA0F,QAAA,CAAA1F,WAAA;QAEAyR,OAAA,CAAA7L,SAAA;UACA,IAAA6L,OAAA,CAAA/T,IAAA,CAAAlB,cAAA;YACA,QAAAiV,OAAA,CAAA7T,YAAA;cACA;gBACA6T,OAAA,CAAArM,iBAAA;gBACA;cACA;gBACAqM,OAAA,CAAApM,mBAAA;gBACA;YACA;UAEA;QACA;MAEA;IACA;IAEA,WACAsM,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAA9L,KAAA,SAAA+L,QAAA;QAAA,IAAAC,IAAA,OAAAjO,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA+N,SAAAC,KAAA;UAAA,WAAAjO,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAA+N,UAAAC,SAAA;YAAA,kBAAAA,SAAA,CAAA7N,IAAA,GAAA6N,SAAA,CAAA5N,IAAA;cAAA;gBACAsN,OAAA,CAAAlU,IAAA,CAAAjC,YAAA;gBACA;AACA;AACA;AACA;gBAHA,KAIAuW,KAAA;kBAAAE,SAAA,CAAA5N,IAAA;kBAAA;gBAAA;gBAAA,KAEAsN,OAAA,CAAA1R,SAAA;kBAAAgS,SAAA,CAAA5N,IAAA;kBAAA;gBAAA;gBACA6N,OAAA,CAAAC,GAAA;gBACA;gBAAAF,SAAA,CAAA5N,IAAA;gBAAA,OACAsN,OAAA,CAAAS,WAAA;cAAA;gBAEA;gBACAT,OAAA,CAAA5J,iBAAA;gBACA,IAAA4J,OAAA,CAAAlU,IAAA,CAAA8H,YAAA;kBACA,IAAAkC,4BAAA,EAAAkK,OAAA,CAAAlU,IAAA,EAAA+H,IAAA,WAAAC,QAAA;oBACAkM,OAAA,CAAArJ,MAAA,CAAAC,UAAA;oBACA;oBACAoJ,OAAA,CAAArO,OAAA;kBACA;gBACA;kBACA,IAAA+O,yBAAA,EAAAV,OAAA,CAAAlU,IAAA,EAAA+H,IAAA,WAAAC,QAAA;oBACAkM,OAAA,CAAAlU,IAAA,GAAAgI,QAAA,CAAA/L,IAAA;oBACAiY,OAAA,CAAArJ,MAAA,CAAAC,UAAA;oBACA;oBACAoJ,OAAA,CAAArO,OAAA;kBACA;gBACA;cAAA;cAAA;gBAAA,OAAA2O,SAAA,CAAAzN,IAAA;YAAA;UAAA,GAAAsN,QAAA;QAAA,CAEA;QAAA,iBAAAQ,EAAA;UAAA,OAAAT,IAAA,CAAAU,KAAA,OAAA3G,SAAA;QAAA;MAAA;IACA;IAEAwG,WAAA,WAAAA,YAAA;MAAA,IAAAI,OAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,OAAA,CAAAI,iBAAA;UACA9B,IAAA,EAAA0B,OAAA,CAAAvS,SAAA;UACA4S,SAAA,WAAAA,UAAApN,QAAA;YACA+M,OAAA,CAAAM,aAAA,CAAArN,QAAA;YACAiN,OAAA,CAAAjN,QAAA;UACA;UACAsN,OAAA,WAAAA,QAAAlM,KAAA;YACA2L,OAAA,CAAAQ,WAAA,CAAAnM,KAAA;YACA8L,MAAA,CAAA9L,KAAA;UACA;QACA;MACA;IACA;IAEAkB,iBAAA,WAAAA,kBAAA;MACA,SAAAtK,IAAA,CAAAtC,mBAAA;QACA;QACA,KAAAsC,IAAA,CAAA7B,oBAAA;QACA,KAAA6B,IAAA,CAAA3B,mBAAA;QACA,KAAA2B,IAAA,CAAAvB,iBAAA;QACA,KAAAuB,IAAA,CAAArB,sBAAA;QACA,KAAAqB,IAAA,CAAAzB,oBAAA;QACA,KAAAyB,IAAA,CAAAnB,0BAAA;QACA;QACA,KAAAmB,IAAA,CAAApB,8BAAA,UAAAoB,IAAA,CAAAxB,qBAAA,GACA,KAAAwB,IAAA,CAAAlB,cAAA,aAAAkB,IAAA,CAAApB,8BAAA,SACA,KAAAoB,IAAA,CAAAlB,cAAA,YAAAkB,IAAA,CAAAlB,cAAA;MACA;QACA;QACA,KAAAkB,IAAA,CAAA9B,wBAAA;QACA,KAAA8B,IAAA,CAAA5B,uBAAA;QACA,KAAA4B,IAAA,CAAAxB,qBAAA;QACA,KAAAwB,IAAA,CAAAtB,0BAAA;QACA,KAAAsB,IAAA,CAAA1B,wBAAA;QACA,KAAA0B,IAAA,CAAApB,8BAAA;QACA;QACA,KAAAoB,IAAA,CAAAvB,iBAAA,UAAAuB,IAAA,CAAAnB,0BAAA,GACA,KAAAmB,IAAA,CAAAlB,cAAA,aAAAkB,IAAA,CAAAnB,0BAAA,SACA,KAAAmB,IAAA,CAAAlB,cAAA,YAAAkB,IAAA,CAAAlB,cAAA;MACA;IACA;IAEA,aACA0W,YAAA,WAAAA,aAAA7M,GAAA;MAAA,IAAA8M,OAAA;MACA,IAAAC,aAAA,GAAA/M,GAAA,CAAAb,YAAA,SAAAhL,GAAA;MACA,KAAA0S,QAAA,4BAAAkG,aAAA,cAAA3N,IAAA;QACA,WAAA4N,yBAAA,EAAAD,aAAA;MACA,GAAA3N,IAAA;QACA0N,OAAA,CAAA5P,OAAA;QACA4P,OAAA,CAAA5K,MAAA,CAAAC,UAAA;MACA,GAAA4E,KAAA,cACA;IACA;IAEA,aACAkG,YAAA,WAAAA,aAAA;MACA,KAAAlC,QAAA,iCAAAhH,cAAA,CAAAtG,OAAA,MACA,KAAA9I,WAAA,iBAAA+L,MAAA,CACA,IAAAoI,IAAA,GAAAoE,OAAA;IACA;IAEAC,eAAA,WAAAA,gBAAAC,IAAA;MACA,IAAAA,IAAA,CAAA9S,QAAA,KAAA8S,IAAA,CAAA9S,QAAA,CAAA6B,MAAA;QACA,OAAAiR,IAAA,CAAA9S,QAAA;MACA;MACA,IAAA+S,CAAA;MACA,IAAAD,IAAA,CAAAE,KAAA;QACA,IAAAF,IAAA,CAAAE,KAAA,CAAAC,oBAAA,YAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;UACA,IAAAJ,IAAA,CAAAK,IAAA,CAAAC,aAAA;YACAL,CAAA,GAAAD,IAAA,CAAAK,IAAA,CAAAC,aAAA,SAAAC,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAK,IAAA,CAAAC,aAAA;UACA;YACAL,CAAA,GAAAD,IAAA,CAAAS,IAAA,CAAAC,aAAA,SAAAH,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAS,IAAA,CAAAC,aAAA;UACA;QACA;UACAT,CAAA,GAAAD,IAAA,CAAAE,KAAA,CAAAS,SAAA,SAAAX,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA,SAAAJ,IAAA,CAAAE,KAAA,CAAAU,iBAAA,SAAAL,iBAAA,CAAAC,YAAA,CAAAR,IAAA,CAAAE,KAAA,CAAAC,oBAAA,GAAAH,IAAA,CAAAE,KAAA,CAAAE,oBAAA;QACA;MACA;MACA,IAAAJ,IAAA,CAAAa,MAAA;QACA;UACAC,EAAA,EAAAd,IAAA,CAAAa,MAAA;UACAE,KAAA,EAAAd,CAAA;UACA/S,QAAA,EAAA8S,IAAA,CAAA9S,QAAA;UACA8T,UAAA,EAAAhB,IAAA,CAAA7T,OAAA,YAAA6T,IAAA,CAAA9S,QAAA,IAAAmL;QACA;MACA;QACA;UACAyI,EAAA,EAAAd,IAAA,CAAAiB,MAAA;UACAF,KAAA,EAAAd,CAAA;UACA/S,QAAA,EAAA8S,IAAA,CAAA9S,QAAA;UACA8T,UAAA,EAAAhB,IAAA,CAAA7T,OAAA,YAAA6T,IAAA,CAAA9S,QAAA,IAAAmL;QACA;MACA;IACA;IAEA6I,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MACA,SAAA7a,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAkb,SAAA,CAAArS,MAAA,cAAAzI,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAA+J,SAAA,CAAAmR,SAAA;QACArR,cAAA,CAAAC,QAAA,iBAAAgC,IAAA;UACAmP,OAAA,CAAAza,UAAA,GAAAya,OAAA,CAAA7a,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAkb,SAAA;QACA;MACA;QACA,KAAA1a,UAAA,QAAAJ,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAkb,SAAA;MACA;IACA;IAEAlQ,SAAA,WAAAA,UAAA;MAAA,IAAAmQ,OAAA;MACA,SAAA/a,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAob,cAAA,CAAAvS,MAAA,cAAAzI,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAA+J,SAAA,CAAAqR,cAAA;QACAvR,cAAA,CAAAC,QAAA,sBAAAgC,IAAA;UACAqP,OAAA,CAAA1a,SAAA,GAAA0a,OAAA,CAAA/a,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAob,cAAA;QACA;MACA;QACA,KAAA3a,SAAA,QAAAL,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAob,cAAA;MACA;IACA;IAEAC,aAAA,WAAAA,cAAA3O,GAAA;MACA,KAAAmL,YAAA,CAAAnL,GAAA;IACA;IAEA4O,aAAA,WAAAA,cAAAC,OAAA;MACA,KAAAxX,IAAA,CAAAnC,iBAAA,GAAA2Z,OAAA,CAAAC,SAAA;MACA,KAAAzX,IAAA,CAAAlC,2BAAA,GAAA0Z,OAAA,CAAAE,gBAAA;MACA,KAAArV,WAAA;IACA;IAEAoL,eAAA,WAAAA,gBAAA8D,eAAA,EAAAD,aAAA,EAAAqG,SAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzR,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuR,SAAA;QAAA,IAAAC,EAAA,EAAA9P,QAAA,EAAA+P,UAAA,EAAAC,MAAA,EAAAxP,CAAA;QAAA,WAAAnC,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAyR,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvR,IAAA,GAAAuR,SAAA,CAAAtR,IAAA;YAAA;cAAA,MAGAgR,OAAA,CAAAlV,gBAAA,IAAAkV,OAAA,CAAAlV,gBAAA,CAAAoC,MAAA;gBAAAoT,SAAA,CAAAtR,IAAA;gBAAA;cAAA;cACAoB,QAAA,GAAA4P,OAAA,CAAAlV,gBAAA;cAAAwV,SAAA,CAAAtR,IAAA;cAAA;YAAA;cAAAsR,SAAA,CAAAtR,IAAA;cAAA,OAEA,IAAAC,oCAAA;YAAA;cAAA+Q,OAAA,CAAAlV,gBAAA,GAAAwV,SAAA,CAAApR,IAAA;cACAkB,QAAA,GAAA4P,OAAA,CAAAlV,gBAAA;YAAA;cAAA,MAGA6O,eAAA,aAAAD,aAAA;gBAAA4G,SAAA,CAAAtR,IAAA;gBAAA;cAAA;cAAAmR,UAAA,OAAAzK,2BAAA,CAAAlH,OAAA,EACA4B,QAAA,CAAA/L,IAAA;cAAA;gBAAA,KAAA8b,UAAA,CAAAxK,CAAA,MAAAyK,MAAA,GAAAD,UAAA,CAAAnV,CAAA,IAAA4K,IAAA;kBAAAhF,CAAA,GAAAwP,MAAA,CAAA1U,KAAA;kBACA;kBACA,IAAAiO,eAAA,KAAA/I,CAAA,CAAA+I,eAAA,IAAAD,aAAA,KAAA9I,CAAA,CAAA8I,aAAA;oBACAwG,EAAA,WAAAzU,iBAAA,EAAAmF,CAAA,CAAA2P,OAAA,EAAAlT,MAAA,CAAAuD,CAAA,CAAAoJ,IAAA,EAAAtO,KAAA;kBACA;oBACAwU,EAAA,WAAAzU,iBAAA,EAAAmF,CAAA,CAAA4P,QAAA,EAAAnT,MAAA,CAAAuD,CAAA,CAAAoJ,IAAA,EAAAtO,KAAA;kBACA;gBACA;cAAA,SAAAuO,GAAA;gBAAAkG,UAAA,CAAAnK,CAAA,CAAAiE,GAAA;cAAA;gBAAAkG,UAAA,CAAAlK,CAAA;cAAA;cAAA,OAAAqK,SAAA,CAAAxK,MAAA,WACAoK,EAAA;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAAnR,IAAA;UAAA;QAAA,GAAA8Q,QAAA;MAAA;IAEA;IAEAQ,uBAAA,WAAAA,wBAAA9G,eAAA,EAAAD,aAAA,EAAAqG,SAAA;MAAA,WAAAxR,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAgS,SAAA;QAAA,IAAAR,EAAA,EAAA9P,QAAA,EAAAuQ,UAAA,EAAAC,MAAA,EAAAhQ,CAAA;QAAA,WAAAnC,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAiS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA/R,IAAA,GAAA+R,SAAA,CAAA9R,IAAA;YAAA;cACAkR,EAAA;cAAAY,SAAA,CAAA9R,IAAA;cAAA,OACA,IAAAC,oCAAA;YAAA;cAAAmB,QAAA,GAAA0Q,SAAA,CAAA5R,IAAA;cAAA,MACAyK,eAAA,aAAAD,aAAA;gBAAAoH,SAAA,CAAA9R,IAAA;gBAAA;cAAA;cAAA2R,UAAA,OAAAjL,2BAAA,CAAAlH,OAAA,EACA4B,QAAA,CAAA/L,IAAA;cAAA;gBAAA,KAAAsc,UAAA,CAAAhL,CAAA,MAAAiL,MAAA,GAAAD,UAAA,CAAA3V,CAAA,IAAA4K,IAAA;kBAAAhF,CAAA,GAAAgQ,MAAA,CAAAlV,KAAA;kBACA,KAAAkF,CAAA,CAAA8I,aAAA,IAAAA,aAAA,IAAA9I,CAAA,CAAAnF,QAAA,IAAAiO,aAAA,MACA9I,CAAA,CAAAnF,QAAA,IAAAkO,eAAA,IAAA/I,CAAA,CAAA8I,aAAA,IAAAC,eAAA,KACA,IAAApH,eAAA,EAAA3B,CAAA,CAAAgJ,SAAA,SAAArH,eAAA,EAAAwN,SAAA,KACA,IAAAxN,eAAA,EAAAwN,SAAA,SAAAxN,eAAA,EAAA3B,CAAA,CAAAkJ,OAAA;oBACA;oBACA,IAAAH,eAAA,KAAA/I,CAAA,CAAA+I,eAAA,IAAAD,aAAA,KAAA9I,CAAA,CAAA8I,aAAA;sBACAwG,EAAA,WAAAzU,iBAAA,EAAAmF,CAAA,CAAA2P,OAAA,EAAAlT,MAAA,CAAAuD,CAAA,CAAAoJ,IAAA,EAAAtO,KAAA;oBACA;sBACAwU,EAAA,WAAAzU,iBAAA,EAAAmF,CAAA,CAAA4P,QAAA,EAAAnT,MAAA,CAAAuD,CAAA,CAAAoJ,IAAA,EAAAtO,KAAA;oBACA;kBACA;gBACA;cAAA,SAAAuO,GAAA;gBAAA0G,UAAA,CAAA3K,CAAA,CAAAiE,GAAA;cAAA;gBAAA0G,UAAA,CAAA1K,CAAA;cAAA;cAAA,OAAA6K,SAAA,CAAAhL,MAAA,WACAoK,EAAA;YAAA;YAAA;cAAA,OAAAY,SAAA,CAAA3R,IAAA;UAAA;QAAA,GAAAuR,QAAA;MAAA;IAEA;IAEAzP,gBAAA,WAAAA,iBAAA5K,gBAAA,EAAA0a,kBAAA;MAAA,IAAAC,OAAA;MAAA,WAAAzS,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAuS,SAAA;QAAA,IAAA3L,MAAA;QAAA,WAAA7G,oBAAA,CAAAD,OAAA,IAAAI,IAAA,UAAAsS,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAApS,IAAA,GAAAoS,SAAA,CAAAnS,IAAA;YAAA;cAAA,MACA3I,gBAAA,KAAA0a,kBAAA;gBAAAI,SAAA,CAAAnS,IAAA;gBAAA;cAAA;cAAA,OAAAmS,SAAA,CAAArL,MAAA,WACA;YAAA;cAAAqL,SAAA,CAAAnS,IAAA;cAAA,OAEAgS,OAAA,CAAAnL,eAAA,CAAAxP,gBAAA,EAAA0a,kBAAA;YAAA;cAAAzL,MAAA,GAAA6L,SAAA,CAAAjS,IAAA;YAAA;YAAA;cAAA,OAAAiS,SAAA,CAAAhS,IAAA;UAAA;QAAA,GAAA8R,QAAA;MAAA;IACA;IAEAG,OAAA,WAAAA,QAAAnC,EAAA;MACA,IAAAA,EAAA;QACA,IAAAA,EAAA;UACA,IAAAZ,KAAA,QAAA5Z,MAAA,CAAAC,KAAA,CAAAL,IAAA,CAAAob,cAAA,CAAArM,MAAA,WAAAiO,OAAA;YAAA,OAAAA,OAAA,CAAA/W,OAAA,IAAA2U,EAAA;UAAA;UACA,IAAAZ,KAAA;YACA,OAAAA,KAAA,CAAAC,oBAAA,GAAAD,KAAA,CAAAE,oBAAA,GAAAF,KAAA,CAAAiD,cAAA;UACA;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IAEAC,WAAA,WAAAA,YAAAxQ,GAAA;MACA,KAAA3I,IAAA,CAAAN,YAAA,GAAAiJ,GAAA,CAAAsN,KAAA,CAAA/T,OAAA;IACA;IAEAkX,eAAA,WAAAA,gBAAAzQ,GAAA;MACA,OAAAA,GAAA,CAAAsF,kBAAA;IACA;IAEAoL,kBAAA,WAAAA,mBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAApR,SAAA;QACA,IAAAqR,eAAA,GAAAD,OAAA,CAAAlR,KAAA,CAAAoR,UAAA,CAAAC,eAAA;QACA,IAAAF,eAAA;UACAA,eAAA,CAAAG,KAAA;QACA;MACA;IACA;IAEAC,6BAAA,WAAAA,8BAAA5D,IAAA;MACA;QACAc,EAAA,EAAAd,IAAA,CAAAzS,KAAA;QACAwT,KAAA,EAAAf,IAAA,CAAAe;MACA;IACA;IAEA8C,iBAAA,WAAAA,kBAAAjR,GAAA;MACA,KAAA3I,IAAA,CAAArC,mBAAA,GAAAgL,GAAA,CAAAkR,sBAAA;IACA;IAEA1E,iBAAA,WAAAA,kBAAA2E,OAAA;MAAA,IAAAC,OAAA;MACA,IAAAC,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAAJ,OAAA,CAAAzG,IAAA;MAEA,IAAA8G,gBAAA;QACA7Y,GAAA;QACA8Y,MAAA;QACAne,IAAA,EAAA+d;MACA,GAAAjS,IAAA,WAAAC,QAAA;QACA8R,OAAA,CAAA1E,SAAA,CAAApN,QAAA,EAAA8R,OAAA,CAAAzG,IAAA;QACA0G,OAAA,CAAA/Z,IAAA,CAAA8J,QAAA,GAAA9B,QAAA,CAAA1G,GAAA;MACA,GAAAoO,KAAA,WAAAtG,KAAA;QACA0Q,OAAA,CAAAxE,OAAA,CAAAlM,KAAA;MACA;IACA;IAEAiR,YAAA,WAAAA,aAAAhH,IAAA,EAAAC,QAAA;MACA,IAAAgH,SAAA,GAAAjH,IAAA,CAAAzX,IAAA,CAAA2e,SAAA,CAAAlH,IAAA,CAAAzX,IAAA,CAAA4e,WAAA;MACA,IAAAC,WAAA,MAAApR,MAAA,MAAArJ,IAAA,CAAAiK,YAAA,EAAAZ,MAAA,CAAAiR,SAAA;MACA,KAAA9X,SAAA,OAAAkY,IAAA,EAAArH,IAAA,CAAAsH,GAAA,GAAAF,WAAA;QAAAhT,IAAA,EAAA4L,IAAA,CAAA5L;MAAA;IACA;IAEA4N,aAAA,WAAAA,cAAArN,QAAA,EAAAqL,IAAA,EAAAC,QAAA;MACA,KAAAtT,IAAA,CAAA8J,QAAA,GAAA9B,QAAA,CAAA1G,GAAA;IACA;IAEAiU,WAAA,WAAAA,YAAA1D,GAAA,EAAAwB,IAAA,EAAAC,QAAA;MACA,KAAAhM,QAAA,CAAA8B,KAAA,mBAAAyI,GAAA;IACA;IAEA;IACA+I,oBAAA,WAAAA,qBAAAjS,GAAA,EAAArF,KAAA;MACA;MACAqF,GAAA,CAAA3D,uBAAA,GAAAG,MAAA,CAAA7B,KAAA,EAAA8B,OAAA;;MAEA;MACA,IAAAuD,GAAA,CAAA/H,YAAA;QACA;QACA,IAAA4D,WAAA,GAAAmE,GAAA,CAAAlE,QAAA,CAAAC,QAAA;QACA,IAAAC,aAAA,GAAAH,WAAA,CAAAI,QAAA,QACAJ,WAAA,CAAAK,KAAA,SAAAC,MAAA;;QAEA;QACA,IAAAC,eAAA,OAAA1B,iBAAA,EAAAC,KAAA,EAAA2B,MAAA,CAAA0D,GAAA,CAAA/H,YAAA,EAAA0C,KAAA;QACAqF,GAAA,CAAAzD,qBAAA,GAAAC,MAAA,CAAAJ,eAAA,EAAAK,OAAA,CAAAT,aAAA;MACA;IACA;EACA;EAEAkW,QAAA;IACAC,WAAA,WAAAA,YAAA;MACA,YAAA9a,IAAA,CAAA9B,wBAAA,QAAA8B,IAAA,CAAA5B,uBAAA,QAAA4B,IAAA,CAAA1B,wBAAA;IACA;IAEAyc,QAAA,WAAAA,SAAA;MACA,YAAA/a,IAAA,CAAA7B,oBAAA,QAAA6B,IAAA,CAAA3B,mBAAA,QAAA2B,IAAA,CAAAzB,oBAAA;IACA;IAEAyc,QAAA,WAAAA,SAAA;MACA,YAAAhb,IAAA,CAAAX,kBAAA;IACA;IAEA4b,mBAAA,WAAAA,oBAAA;MACA,YAAAjb,IAAA,CAAAuK,aAAA;IACA;EACA;AACA;AAAA2Q,OAAA,CAAA9U,OAAA,GAAA+U,QAAA"}]}