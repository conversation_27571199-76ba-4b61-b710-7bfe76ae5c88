{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\BillOfLadingInfo.vue", "mtime": 1754881964226}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_logisticsProgress", "_interopRequireDefault", "require", "_chargeList", "_TreeSelect", "_LocationSelect", "_rich", "_currency", "_debitNodeList", "name", "components", "DebitNoteList", "LogisticsProgress", "ChargeList", "TreeSelect", "LocationSelect", "props", "bookingMessageForm", "type", "Object", "required", "default", "openBookingMessage", "Boolean", "bookingMessageStatus", "String", "bookingMessageList", "Array", "rsClientMessage", "form", "disabled", "psaVerify", "auditInfo", "branchInfo", "logisticsInfo", "chargeInfo", "companyList", "_default", "rsClientMessageReceivableRMB", "Number", "rsClientMessageReceivableUSD", "rsClientMessagePayableRMB", "rsClientMessagePayableUSD", "rsClientMessageProfitRMB", "rsClientMessageProfitUSD", "rsClientMessageReceivableTaxRMB", "rsClientMessageReceivableTaxUSD", "rsClientMessagePayableTaxRMB", "rsClientMessagePayableTaxUSD", "rsClientMessageProfitTaxRMB", "rsClientMessageProfitTaxUSD", "data", "visible", "bookingMessageTitle", "psaBookingSelectData", "locationOptions", "rsClientMessageFormDisable", "fileOptions", "file", "link", "templateList", "payWayOptions", "label", "value", "profitOpen", "profitTableData", "currencyCode", "profit", "profitTax", "exchangeRate", "computed", "rsClientServiceInstance", "opConfirmedName", "dnOpConfirmedName", "opConfirmedDate", "dnOpConfirmedDate", "salesConfirmedName", "dnSalesConfirmedName", "salesConfirmedDate", "dnSalesConfirmedDate", "clientConfirmedName", "dnClientConfirmedName", "clientConfirmedDate", "dnClientConfirmedDate", "accountConfirmedName", "accountConfirmedDate", "created", "methods", "handleChargeSelectionChange", "selection", "$emit", "handleAddDebitNote", "row", "sqdRctNo", "rctNo", "rctId", "isRecievingOrPaying", "rsChargeList", "toggleVisible", "handleFileAction", "methodName", "templateType", "handleSelectionChange", "length", "$message", "warning", "handleBookingMessageUpdate", "addBookingMessage", "bookingMessageConfirm", "closeBookingMessage", "deleteBookingMessage", "deleteLogisticsItem", "item", "rsOpLogList", "filter", "log", "updateLogisticsProgress", "openProfit", "RMB", "receivable", "payable", "receivableTax", "payableTax", "USD", "push", "profitCount", "_iterator", "_createForOfIteratorHelper2", "$store", "state", "exchangeRateList", "_step", "s", "n", "done", "a", "podEta", "localCurrency", "overseaCurrency", "parseTime", "validFrom", "validTo", "currency", "settleRate", "divide", "base", "Date", "err", "e", "f", "multiply", "add", "handleCopyFreight", "charge", "handleDeleteAll", "handleDeleteItem", "exports", "_default2"], "sources": ["src/views/system/document/serviceComponents/BillOfLadingInfo.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <!--title-->\r\n    <el-row>\r\n      <el-col :span=\"18\">\r\n        <div class=\"service-bar\" style=\"display: flex;margin-top: 10px;margin-bottom: 10px;width: 100%\">\r\n          <a :class=\"{'el-icon-arrow-down':visible,'el-icon-arrow-right':!visible}\"/>\r\n          <div style=\"width:150px;display: flex\">\r\n            <h3 style=\"margin: 0;width: 250px;text-align: left\" @click=\"toggleVisible\">提单信息</h3>\r\n            <el-button style=\"margin-left: 10px;\" type=\"text\" @click=\"$emit('openChargeSelect', rsClientMessage)\">\r\n              [DN...]\r\n            </el-button>\r\n          </div>\r\n\r\n          <el-button type=\"primary\" size=\"mini\" style=\"margin-left: 10px;\" @click=\"profitOpen=true\">利润</el-button>\r\n\r\n          <el-col v-if=\"auditInfo\"\r\n                  :span=\"15\" style=\"display: flex\"\r\n          >\r\n            <div v-hasPermi=\"['system:booking:opapproval','system:rct:opapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnOpConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'op')\"\r\n              >操作确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ opConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ opConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:salesapproval','system:rct:salesapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnSalesConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'sales')\"\r\n              >业务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ salesConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ salesConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:clientapproval','system:rct:clientapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isDnClientConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'client')\"\r\n              >客户确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ clientConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ clientConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n            <div v-hasPermi=\"['system:booking:financeapproval','system:rct:financeapproval']\"\r\n                 style=\"width:25%;display: flex;font-size: 12px\"\r\n            >\r\n              <el-button :icon=\"rsClientServiceInstance.isAccountConfirmed?'el-icon-check':'el-icon-minus'\"\r\n                         style=\"padding: 0\" type=\"text\"\r\n                         @click=\"$emit('confirmed', 'account', rsClientMessage.rsChargeList)\"\r\n              >财务确认\r\n              </el-button>\r\n              <div style=\"text-align: left;width: 120px\">\r\n                <div><i class=\"el-icon-user\"/>{{ accountConfirmedName }}</div>\r\n                <div><i class=\"el-icon-alarm-clock\"/>{{ accountConfirmedDate }}</div>\r\n              </div>\r\n            </div>\r\n          </el-col>\r\n\r\n          <div style=\"margin-left: auto\">\r\n            <el-popover\r\n              v-for=\"(item,index) in fileOptions\" :key=\"index\"\r\n              placement=\"top\" trigger=\"click\" width=\"100\"\r\n            >\r\n              <el-button v-for=\"(item2,index) in item.templateList\" :key=\"index\"\r\n                         @click=\"handleFileAction(item.link, item2)\"\r\n              >{{ item2 }}\r\n              </el-button>\r\n              <a slot=\"reference\" style=\"color: blue;padding: 0;margin-left: 10px\" target=\"_blank\"\r\n              >[{{ item.file }}]</a>\r\n            </el-popover>\r\n          </div>\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!--content-->\r\n    <transition name=\"fade\">\r\n      <el-row v-if=\"visible\" :gutter=\"10\" style=\"margin-bottom:15px;display:-webkit-box\">\r\n        <!--主表信息-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"branchInfo\" :span=\"18\">\r\n            <el-table :data=\"bookingMessageList\" border @selection-change=\"handleSelectionChange\">\r\n              <el-table-column\r\n                type=\"selection\"\r\n                width=\"55\"\r\n              >\r\n              </el-table-column>\r\n              <el-table-column\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"50\"\r\n              />\r\n              <el-table-column label=\"MB/L No\" prop=\"mBlNo\"/>\r\n              <el-table-column label=\"HB/L No\" prop=\"hBlNo\"/>\r\n              <el-table-column label=\"发货人\" prop=\"bookingShipper\"/>\r\n              <el-table-column label=\"收货人\" prop=\"bookingConsignee\"/>\r\n              <el-table-column label=\"通知人\" prop=\"bookingNotifyParty\"/>\r\n              <el-table-column label=\"代理\" prop=\"bookingAgent\"/>\r\n              <el-table-column label=\"柜号\" prop=\"containerNo\"/>\r\n              <el-table-column label=\"封号\" prop=\"sealNo\"/>\r\n              <el-table-column label=\"柜型\" prop=\"containerType\"/>\r\n              <el-table-column label=\"唛头\" prop=\"shippingMark\"/>\r\n              <el-table-column label=\"件数\" prop=\"packageQuantity\"/>\r\n              <el-table-column label=\"货描\" prop=\"goodsDescription\"/>\r\n              <el-table-column label=\"体积\" prop=\"goodsVolume\"/>\r\n              <el-table-column label=\"重量\" prop=\"grossWeight\"/>\r\n              <el-table-column label=\"提单类型\" prop=\"blTypeCode\"/>\r\n              <el-table-column label=\"出单方式\" prop=\"blFormCode\"/>\r\n              <el-table-column label=\"交单方式\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <tree-select :class=\"'disable-form'\" :disabled=\"true\"\r\n                               :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"scope.row.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"scope.row.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n              <el-table-column label=\"操作\" prop=\"sqdDocDeliveryWay\">\r\n                <template slot-scope=\"scope\">\r\n                  <el-button type=\"text\" @click=\"handleBookingMessageUpdate(scope.row)\">修改</el-button>\r\n                  <el-button style=\"color: red\" type=\"text\"\r\n                             @click=\"deleteBookingMessage(scope.row)\"\r\n                  >删除\r\n                  </el-button>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <!--弹出层-->\r\n            <el-dialog\r\n              v-dialogDrag\r\n              v-dialogDragWidth\r\n              :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n              :show-close=\"false\" :title=\"bookingMessageTitle\" @close=\"closeBookingMessage\"\r\n              :visible.sync=\"openBookingMessage\" append-to-body width=\"30%\"\r\n            >\r\n              <el-form ref=\"bookingMessageForm\" :model=\"bookingMessageForm\" class=\"edit\" label-width=\"80px\"\r\n                       style=\"\"\r\n              >\r\n                <div v-if=\"bookingMessageForm.blTypeCode==='MBL'\">\r\n                  <el-form-item label=\"提单号码\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <div v-else>\r\n                  <el-form-item label=\"MB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.mBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                  <el-form-item label=\"HB/L No\">\r\n                    <el-input v-model=\"bookingMessageForm.hBlNo\" style=\"padding: 0;margin: 0;\"/>\r\n                  </el-form-item>\r\n                </div>\r\n                <el-form-item label=\"发货人\">\r\n                  <template slot=\"label\">\r\n                    <div>发货人</div>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('handleAddCommon', 'release')\">[↗]\r\n                    </el-button>\r\n                    <el-button style=\"color: blue\" type=\"text\" @click=\"$emit('openReleaseUsed')\">[...]</el-button>\r\n                  </template>\r\n                  <el-input v-model=\"bookingMessageForm.bookingShipper\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"收货人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingConsignee\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"通知人\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingNotifyParty\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"代理\">\r\n                  <el-input v-model=\"bookingMessageForm.bookingAgent\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"启运港\">\r\n                  <el-input v-model=\"bookingMessageForm.polName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"卸货港\">\r\n                  <el-input v-model=\"bookingMessageForm.podName\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"目的港\">\r\n                  <el-input v-model=\"bookingMessageForm.destinationPort\"/>\r\n                </el-form-item>\r\n                <el-form-item label=\"柜号\">\r\n                  <el-input v-model=\"bookingMessageForm.containerNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"封号\">\r\n                  <el-input v-model=\"bookingMessageForm.sealNo\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"柜型\">\r\n                  <el-input v-model=\"bookingMessageForm.containerType\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"唛头\">\r\n                  <el-input v-model=\"bookingMessageForm.shippingMark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"件数\">\r\n                  <el-input v-model=\"bookingMessageForm.packageQuantity\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"件数\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"货描\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsDescription\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"重量\">\r\n                  <el-input v-model=\"bookingMessageForm.grossWeight\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"重量\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"体积\">\r\n                  <el-input v-model=\"bookingMessageForm.goodsVolume\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\"\r\n                            placeholder=\"体积\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"备注\">\r\n                  <el-input v-model=\"bookingMessageForm.blRemark\" :autosize=\"{ minRows: 2.5, maxRows: 5}\"\r\n                            maxlength=\"500\" show-word-limit style=\"padding: 0;margin: 0;\" type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单地\">\r\n                  <location-select :load-options=\"psaBookingSelectData.locationOptions\" :no-parent=\"true\"\r\n                                   :pass=\"bookingMessageForm.polIds\" :placeholder=\"'启运港'\"\r\n                                   @returnData=\"bookingMessageForm.city=$event.locationEnShortName\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"开船日期\">\r\n                  <el-date-picker\r\n                    v-model=\"bookingMessageForm.onBoardDate\"\r\n                    placeholder=\"选择日期\"\r\n                    type=\"date\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n                <el-form-item label=\"付款方式\">\r\n                  <el-select v-model=\"bookingMessageForm.payWay\" placeholder=\"请选择\">\r\n                    <el-option\r\n                      v-for=\"item in payWayOptions\"\r\n                      :key=\"item.value\"\r\n                      :label=\"item.label\"\r\n                      :value=\"item.value\"\r\n                    >\r\n                    </el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n                <el-form-item label=\"提单类型\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blTypeCode\"\r\n                               :placeholder=\"'提单类型'\" :type=\"'blType'\"\r\n                               @return=\"bookingMessageForm.blTypeCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"出单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.blFormCode\"\r\n                               :placeholder=\"'出单方式'\" :type=\"'blForm'\"\r\n                               @return=\"bookingMessageForm.blFormCode=$event\"\r\n                  />\r\n                </el-form-item>\r\n                <el-form-item label=\"交单方式\">\r\n                  <tree-select :flat=\"false\"\r\n                               :multiple=\"false\" :pass=\"bookingMessageForm.sqdDocDeliveryWay\"\r\n                               :placeholder=\"'货代单交单方式'\"\r\n                               :type=\"'docReleaseWay'\" @return=\"bookingMessageForm.sqdDocDeliveryWay=$event\"\r\n                  />\r\n                </el-form-item>\r\n              </el-form>\r\n              <div slot=\"footer\" class=\"dialog-footer\">\r\n                <el-button size=\"mini\" type=\"primary\" @click=\"bookingMessageConfirm\">确 定</el-button>\r\n                <el-button size=\"mini\" @click=\"closeBookingMessage\">取 消</el-button>\r\n              </div>\r\n            </el-dialog>\r\n\r\n            <el-button :disabled=\"psaVerify || disabled\"\r\n                       style=\"padding: 0\"\r\n                       type=\"text\"\r\n                       @click=\"addBookingMessage\"\r\n            >[＋]\r\n            </el-button>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--物流进度-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"logisticsInfo\" :span=\"4\">\r\n            <el-form-item label=\"进度需求\" prop=\"goodsNameSummary\"/>\r\n            <div>\r\n              <logistics-progress :disabled=\"rsClientMessageFormDisable || disabled || psaVerify\"\r\n                                  :logistics-progress-data=\"rsClientMessage.rsOpLogList\"\r\n                                  :open-logistics-progress-list=\"true\"\r\n                                  @deleteItem=\"deleteLogisticsItem\"\r\n                                  @return=\"updateLogisticsProgress\"\r\n              />\r\n            </div>\r\n          </el-col>\r\n        </transition>\r\n\r\n        <!--费用列表-->\r\n        <!--分帐单列表-->\r\n        <transition name=\"fade\">\r\n          <el-col v-if=\"chargeInfo\" :span=\"10.5\">\r\n            <debit-note-list\r\n              :is-receivable=\"1\"\r\n              :company-list=\"companyList\"\r\n              :debit-note-list=\"rsClientMessage.rsDebitNoteList\"\r\n              :disabled=\"false\"\r\n              :hidden-supplier=\"false\"\r\n              :rct-id=\"form.rctId\"\r\n              @return=\"$emit('rsClientMessageDebitNote', $event)\"\r\n              @addDebitNote=\"handleAddDebitNote\"\r\n              @copyFreight=\"handleCopyFreight\"\r\n              @deleteAll=\"handleDeleteAll\"\r\n              @deleteItem=\"rsClientMessage.rsDebitNoteList = rsClientMessage.rsDebitNoteList.filter(v=>v!==$event)\"\r\n              @selection-change=\"handleSelectionChange\"\r\n            />\r\n          </el-col>\r\n        </transition>\r\n      </el-row>\r\n    </transition>\r\n\r\n    <!-- 利润对话框 -->\r\n    <el-dialog\r\n      :visible.sync=\"profitOpen\"\r\n      title=\"单票利润\"\r\n      width=\"30%\"\r\n      @open=\"openProfit\"\r\n    >\r\n      <el-table\r\n        :data=\"profitTableData\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column\r\n          label=\"货币\"\r\n          prop=\"currencyCode\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"应收\"\r\n          prop=\"receivable\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"应付\"\r\n          prop=\"payable\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"不含税利润\" prop=\"profit\"\r\n          style=\"color: #0d0dfd\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税应收\"\r\n          prop=\"receivableTax\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税应付\"\r\n          prop=\"payableTax\"\r\n        >\r\n        </el-table-column>\r\n        <el-table-column\r\n          label=\"含税利润\"\r\n          prop=\"profitTax\"\r\n        >\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <el-row>\r\n        <el-col :span=\"5\">\r\n          <el-form-item label=\"折合币种\" prop=\"rctOpDate\">\r\n            <el-select v-model=\"currencyCode\" @change=\"profitCount(currencyCode)\">\r\n              <el-option label=\"RMB\" value=\"RMB\"/>\r\n              <el-option label=\"USD\" value=\"USD\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n        </el-col>\r\n        <el-col :span=\"7\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div style=\"color: #0d0dfd\">不含税利润</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"profit\" placeholder=\"不含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n        <el-col :span=\"7\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div>含税利润</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"profitTax\" placeholder=\"含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n        <el-col :span=\"5\">\r\n          <el-row>\r\n            <el-col :span=\"12\">\r\n              <div>折算汇率</div>\r\n            </el-col>\r\n            <el-col :span=\"12\">\r\n              <el-input v-model=\"exchangeRate\" placeholder=\"含税利润\"/>\r\n            </el-col>\r\n          </el-row>\r\n        </el-col>\r\n      </el-row>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport LogisticsProgress from \"../logisticsProgress\"\r\nimport ChargeList from \"../chargeList\"\r\nimport TreeSelect from \"@/components/TreeSelect\"\r\nimport LocationSelect from \"@/components/LocationSelect\"\r\nimport {parseTime} from \"@/utils/rich\"\r\nimport currency from \"currency.js\"\r\nimport DebitNoteList from \"@/views/system/document/debitNodeList.vue\"\r\n\r\nexport default {\r\n  name: \"BillOfLadingInfo\",\r\n  components: {\r\n    DebitNoteList,\r\n    LogisticsProgress,\r\n    ChargeList,\r\n    TreeSelect,\r\n    LocationSelect\r\n  },\r\n  props: {\r\n    bookingMessageForm: {\r\n      type: Object,\r\n      required: true,\r\n      default: null\r\n    },\r\n    openBookingMessage: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    bookingMessageStatus: {\r\n      type: String,\r\n      default: \"<UNK>\"\r\n    },\r\n    bookingMessageList: {\r\n      type: Array,\r\n      default: []\r\n    },\r\n    rsClientMessage: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    form: {\r\n      type: Object,\r\n      required: true\r\n    },\r\n    disabled: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    psaVerify: {\r\n      type: Boolean,\r\n      default: false\r\n    },\r\n    auditInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    branchInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    logisticsInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    chargeInfo: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    companyList: {\r\n      type: Array,\r\n      default: () => []\r\n    },\r\n    rsClientMessageReceivableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageReceivableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessagePayableTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxRMB: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    rsClientMessageProfitTaxUSD: {\r\n      type: Number,\r\n      default: 0\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      visible: true,\r\n      bookingMessageTitle: \"提单信息\",\r\n      psaBookingSelectData: {\r\n        locationOptions: []\r\n      },\r\n      rsClientMessageFormDisable: false,\r\n      fileOptions: [\r\n        {file: \"操作单\", link: \"getOpBill\", templateList: [\"整柜\", \"散货\", \"空运\", \"其他\"]},\r\n        {file: \"提单\", link: \"getBillOfLading\", templateList: [\"套打提单\", \"电放提单\"]},\r\n        {\r\n          file: \"费用清单\",\r\n          link: \"getChargeListBill\",\r\n          templateList: [\"CN-广州瑞旗[招行USD+工行RMB]\", \"CN-广州瑞旗[USD->RMB]\", \"EN-广州瑞旗[招行USD]\", \"EN-广州瑞旗[RMB->USD]\", \"EN- 瑞旗香港账户[HSBC RMB->USD]\", \"EN- 香港瑞旗[HSBC]\", \"CN-广州正泽[招行USD+RMB]\", \"CN-广州正泽[USD->RMB]\"]\r\n        }\r\n      ],\r\n      payWayOptions: [\r\n        {label: \"预付\", value: \"FREIGHTP REPAID\"},\r\n        {label: \"到付\", value: \"FREIGHTP COLLECT\"}\r\n      ],\r\n      profitOpen: false,\r\n      profitTableData: [],\r\n      currencyCode: \"RMB\",\r\n      profit: 0,\r\n      profitTax: 0,\r\n      exchangeRate: 1\r\n    }\r\n  },\r\n  computed: {\r\n    rsClientServiceInstance() {\r\n      return this.rsClientMessage || {}\r\n    },\r\n    opConfirmedName() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedName || \"\"\r\n    },\r\n    opConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnOpConfirmedDate || \"\"\r\n    },\r\n    salesConfirmedName() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedName || \"\"\r\n    },\r\n    salesConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnSalesConfirmedDate || \"\"\r\n    },\r\n    clientConfirmedName() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedName || \"\"\r\n    },\r\n    clientConfirmedDate() {\r\n      return this.rsClientServiceInstance.dnClientConfirmedDate || \"\"\r\n    },\r\n    accountConfirmedName() {\r\n      return this.rsClientServiceInstance.accountConfirmedName || \"\"\r\n    },\r\n    accountConfirmedDate() {\r\n      return this.rsClientServiceInstance.accountConfirmedDate || \"\"\r\n    }\r\n  },\r\n  created() {\r\n    // 初始化时的操作\r\n  },\r\n  methods: {\r\n    handleChargeSelectionChange(selection) {\r\n      this.$emit(\"selection-charge-change\", selection)\r\n    },\r\n    handleAddDebitNote() {\r\n      let row = {}\r\n      row.sqdRctNo = this.form.rctNo\r\n      row.rctId = this.form.rctId\r\n      row.isRecievingOrPaying = 0\r\n      row.rsChargeList = []\r\n      this.$emit(\"addDebitNote\", row, this.rsClientMessage)\r\n    },\r\n    toggleVisible() {\r\n      this.visible = !this.visible\r\n    },\r\n    handleFileAction(methodName, templateType) {\r\n      this.$emit(methodName, templateType)\r\n    },\r\n    handleSelectionChange(selection) {\r\n      // 只能勾选一个\r\n      if (selection.length > 1) {\r\n        this.$message.warning(\"只能勾选一个账单\")\r\n        return\r\n      }\r\n      this.$emit(\"handleReceiveSelected\", selection)\r\n    },\r\n    handleBookingMessageUpdate(row) {\r\n      this.$emit(\"handleBookingMessageUpdate\", row)\r\n    },\r\n    addBookingMessage() {\r\n      this.$emit(\"handleAddBookingMessage\", this.bookingMessageForm)\r\n    },\r\n    bookingMessageConfirm() {\r\n      // 将操作通过事件发送给父组件\r\n      this.$emit(\"bookingMessageConfirm\", this.bookingMessageForm)\r\n\r\n    },\r\n    closeBookingMessage() {\r\n      this.$emit(\"closeBookingMessage\")\r\n    },\r\n    deleteBookingMessage(row) {\r\n      // 通过事件发送给父组件\r\n      this.$emit(\"deleteBookingMessage\", row)\r\n    },\r\n    deleteLogisticsItem(item) {\r\n      if (this.rsClientMessage && this.rsClientMessage.rsOpLogList) {\r\n        this.rsClientMessage.rsOpLogList = this.rsClientMessage.rsOpLogList.filter(log => log !== item)\r\n      }\r\n    },\r\n    updateLogisticsProgress(data) {\r\n      if (this.rsClientMessage) {\r\n        this.rsClientMessage.rsOpLogList = data\r\n      }\r\n    },\r\n    openProfit() {\r\n      this.profitTableData = []\r\n\r\n      let RMB = {}\r\n      RMB.currencyCode = \"RMB\"\r\n      RMB.receivable = this.rsClientMessageReceivableRMB\r\n      RMB.payable = this.rsClientMessagePayableRMB\r\n      // 不含税利润\r\n      RMB.profit = this.rsClientMessageProfitRMB\r\n      // 含税应收\r\n      RMB.receivableTax = this.rsClientMessageReceivableTaxRMB\r\n      // 含税应付\r\n      RMB.payableTax = this.rsClientMessagePayableTaxRMB\r\n      // 含税利润\r\n      RMB.profitTax = this.rsClientMessageProfitTaxRMB\r\n\r\n      let USD = {}\r\n      USD.currencyCode = \"USD\"\r\n      USD.receivable = this.rsClientMessageReceivableUSD\r\n      USD.payable = this.rsClientMessagePayableUSD\r\n      USD.profit = this.rsClientMessageProfitUSD\r\n      USD.receivableTax = this.rsClientMessageReceivableTaxUSD\r\n      USD.payableTax = this.rsClientMessagePayableTaxUSD\r\n      USD.profitTax = this.rsClientMessageProfitTaxUSD\r\n\r\n      this.profitTableData.push(RMB)\r\n      this.profitTableData.push(USD)\r\n\r\n      this.profitCount(\"RMB\")\r\n    },\r\n    profitCount(type) {\r\n      let exchangeRate\r\n      for (const a of this.$store.state.data.exchangeRateList) {\r\n        if (this.form.podEta) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(this.form.podEta)\r\n            && parseTime(this.form.podEta) <= parseTime(a.validTo)\r\n          ) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n        if (!exchangeRate) {\r\n          if (a.localCurrency === \"RMB\"\r\n            && \"USD\" == a.overseaCurrency\r\n            && parseTime(a.validFrom) <= parseTime(new Date())\r\n            && parseTime(new Date()) <= parseTime(a.validTo)) {\r\n            exchangeRate = currency(a.settleRate).divide(a.base).value\r\n          }\r\n        }\r\n      }\r\n      this.exchangeRate = exchangeRate\r\n\r\n      if (type === \"RMB\") {\r\n        // 都折算成人民币\r\n        this.profit = currency(this.rsClientMessageProfitUSD).multiply(exchangeRate).add(this.rsClientMessageProfitRMB).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxUSD).multiply(exchangeRate).add(this.rsClientMessageProfitTaxRMB).value\r\n      } else {\r\n        this.profit = currency(this.rsClientMessageProfitRMB).divide(exchangeRate).add(this.rsClientMessageProfitUSD).value\r\n        this.profitTax = currency(this.rsClientMessageProfitTaxRMB).divide(exchangeRate).add(this.rsClientMessageProfitTaxUSD).value\r\n      }\r\n    },\r\n    handleCopyFreight(charge) {\r\n      this.$emit(\"copyFreight\", charge)\r\n    },\r\n    handleDeleteAll() {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n    handleDeleteItem(charge) {\r\n      this.$emit(\"deleteItem\", charge)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '@/assets/styles/op-document.scss';\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;AA6bA,IAAAA,kBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,WAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,eAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,KAAA,GAAAJ,OAAA;AACA,IAAAK,SAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,cAAA,GAAAP,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAEA;EACAO,IAAA;EACAC,UAAA;IACAC,aAAA,EAAAA,sBAAA;IACAC,iBAAA,EAAAA,0BAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA;EACA;EACAC,KAAA;IACAC,kBAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,QAAA;MACAC,OAAA;IACA;IACAC,kBAAA;MACAJ,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;IACAG,oBAAA;MACAN,IAAA,EAAAO,MAAA;MACAJ,OAAA;IACA;IACAK,kBAAA;MACAR,IAAA,EAAAS,KAAA;MACAN,OAAA;IACA;IACAO,eAAA;MACAV,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAS,IAAA;MACAX,IAAA,EAAAC,MAAA;MACAC,QAAA;IACA;IACAU,QAAA;MACAZ,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;IACAU,SAAA;MACAb,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;IACAW,SAAA;MACAd,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;IACAY,UAAA;MACAf,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;IACAa,aAAA;MACAhB,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;IACAc,UAAA;MACAjB,IAAA,EAAAK,OAAA;MACAF,OAAA;IACA;IACAe,WAAA;MACAlB,IAAA,EAAAS,KAAA;MACAN,OAAA,WAAAgB,SAAA;QAAA;MAAA;IACA;IACAC,4BAAA;MACApB,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACAmB,4BAAA;MACAtB,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACAoB,yBAAA;MACAvB,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACAqB,yBAAA;MACAxB,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACAsB,wBAAA;MACAzB,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACAuB,wBAAA;MACA1B,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACAwB,+BAAA;MACA3B,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACAyB,+BAAA;MACA5B,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACA0B,4BAAA;MACA7B,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACA2B,4BAAA;MACA9B,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACA4B,2BAAA;MACA/B,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;IACA6B,2BAAA;MACAhC,IAAA,EAAAqB,MAAA;MACAlB,OAAA;IACA;EACA;EACA8B,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,mBAAA;MACAC,oBAAA;QACAC,eAAA;MACA;MACAC,0BAAA;MACAC,WAAA,GACA;QAAAC,IAAA;QAAAC,IAAA;QAAAC,YAAA;MAAA,GACA;QAAAF,IAAA;QAAAC,IAAA;QAAAC,YAAA;MAAA,GACA;QACAF,IAAA;QACAC,IAAA;QACAC,YAAA;MACA,EACA;MACAC,aAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,EACA;MACAC,UAAA;MACAC,eAAA;MACAC,YAAA;MACAC,MAAA;MACAC,SAAA;MACAC,YAAA;IACA;EACA;EACAC,QAAA;IACAC,uBAAA,WAAAA,wBAAA;MACA,YAAA3C,eAAA;IACA;IACA4C,eAAA,WAAAA,gBAAA;MACA,YAAAD,uBAAA,CAAAE,iBAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,YAAAH,uBAAA,CAAAI,iBAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,YAAAL,uBAAA,CAAAM,oBAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA;MACA,YAAAP,uBAAA,CAAAQ,oBAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,YAAAT,uBAAA,CAAAU,qBAAA;IACA;IACAC,mBAAA,WAAAA,oBAAA;MACA,YAAAX,uBAAA,CAAAY,qBAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,YAAAb,uBAAA,CAAAa,oBAAA;IACA;IACAC,oBAAA,WAAAA,qBAAA;MACA,YAAAd,uBAAA,CAAAc,oBAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA;EAAA,CACA;EACAC,OAAA;IACAC,2BAAA,WAAAA,4BAAAC,SAAA;MACA,KAAAC,KAAA,4BAAAD,SAAA;IACA;IACAE,kBAAA,WAAAA,mBAAA;MACA,IAAAC,GAAA;MACAA,GAAA,CAAAC,QAAA,QAAAhE,IAAA,CAAAiE,KAAA;MACAF,GAAA,CAAAG,KAAA,QAAAlE,IAAA,CAAAkE,KAAA;MACAH,GAAA,CAAAI,mBAAA;MACAJ,GAAA,CAAAK,YAAA;MACA,KAAAP,KAAA,iBAAAE,GAAA,OAAAhE,eAAA;IACA;IACAsE,aAAA,WAAAA,cAAA;MACA,KAAA9C,OAAA,SAAAA,OAAA;IACA;IACA+C,gBAAA,WAAAA,iBAAAC,UAAA,EAAAC,YAAA;MACA,KAAAX,KAAA,CAAAU,UAAA,EAAAC,YAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAb,SAAA;MACA;MACA,IAAAA,SAAA,CAAAc,MAAA;QACA,KAAAC,QAAA,CAAAC,OAAA;QACA;MACA;MACA,KAAAf,KAAA,0BAAAD,SAAA;IACA;IACAiB,0BAAA,WAAAA,2BAAAd,GAAA;MACA,KAAAF,KAAA,+BAAAE,GAAA;IACA;IACAe,iBAAA,WAAAA,kBAAA;MACA,KAAAjB,KAAA,iCAAAzE,kBAAA;IACA;IACA2F,qBAAA,WAAAA,sBAAA;MACA;MACA,KAAAlB,KAAA,+BAAAzE,kBAAA;IAEA;IACA4F,mBAAA,WAAAA,oBAAA;MACA,KAAAnB,KAAA;IACA;IACAoB,oBAAA,WAAAA,qBAAAlB,GAAA;MACA;MACA,KAAAF,KAAA,yBAAAE,GAAA;IACA;IACAmB,mBAAA,WAAAA,oBAAAC,IAAA;MACA,SAAApF,eAAA,SAAAA,eAAA,CAAAqF,WAAA;QACA,KAAArF,eAAA,CAAAqF,WAAA,QAAArF,eAAA,CAAAqF,WAAA,CAAAC,MAAA,WAAAC,GAAA;UAAA,OAAAA,GAAA,KAAAH,IAAA;QAAA;MACA;IACA;IACAI,uBAAA,WAAAA,wBAAAjE,IAAA;MACA,SAAAvB,eAAA;QACA,KAAAA,eAAA,CAAAqF,WAAA,GAAA9D,IAAA;MACA;IACA;IACAkE,UAAA,WAAAA,WAAA;MACA,KAAApD,eAAA;MAEA,IAAAqD,GAAA;MACAA,GAAA,CAAApD,YAAA;MACAoD,GAAA,CAAAC,UAAA,QAAAjF,4BAAA;MACAgF,GAAA,CAAAE,OAAA,QAAA/E,yBAAA;MACA;MACA6E,GAAA,CAAAnD,MAAA,QAAAxB,wBAAA;MACA;MACA2E,GAAA,CAAAG,aAAA,QAAA5E,+BAAA;MACA;MACAyE,GAAA,CAAAI,UAAA,QAAA3E,4BAAA;MACA;MACAuE,GAAA,CAAAlD,SAAA,QAAAnB,2BAAA;MAEA,IAAA0E,GAAA;MACAA,GAAA,CAAAzD,YAAA;MACAyD,GAAA,CAAAJ,UAAA,QAAA/E,4BAAA;MACAmF,GAAA,CAAAH,OAAA,QAAA9E,yBAAA;MACAiF,GAAA,CAAAxD,MAAA,QAAAvB,wBAAA;MACA+E,GAAA,CAAAF,aAAA,QAAA3E,+BAAA;MACA6E,GAAA,CAAAD,UAAA,QAAA1E,4BAAA;MACA2E,GAAA,CAAAvD,SAAA,QAAAlB,2BAAA;MAEA,KAAAe,eAAA,CAAA2D,IAAA,CAAAN,GAAA;MACA,KAAArD,eAAA,CAAA2D,IAAA,CAAAD,GAAA;MAEA,KAAAE,WAAA;IACA;IACAA,WAAA,WAAAA,YAAA3G,IAAA;MACA,IAAAmD,YAAA;MAAA,IAAAyD,SAAA,OAAAC,2BAAA,CAAA1G,OAAA,EACA,KAAA2G,MAAA,CAAAC,KAAA,CAAA9E,IAAA,CAAA+E,gBAAA;QAAAC,KAAA;MAAA;QAAA,KAAAL,SAAA,CAAAM,CAAA,MAAAD,KAAA,GAAAL,SAAA,CAAAO,CAAA,IAAAC,IAAA;UAAA,IAAAC,CAAA,GAAAJ,KAAA,CAAApE,KAAA;UACA,SAAAlC,IAAA,CAAA2G,MAAA;YACA,IAAAD,CAAA,CAAAE,aAAA,cACA,SAAAF,CAAA,CAAAG,eAAA,IACA,IAAAC,eAAA,EAAAJ,CAAA,CAAAK,SAAA,SAAAD,eAAA,OAAA9G,IAAA,CAAA2G,MAAA,KACA,IAAAG,eAAA,OAAA9G,IAAA,CAAA2G,MAAA,SAAAG,eAAA,EAAAJ,CAAA,CAAAM,OAAA,GACA;cACAxE,YAAA,OAAAyE,iBAAA,EAAAP,CAAA,CAAAQ,UAAA,EAAAC,MAAA,CAAAT,CAAA,CAAAU,IAAA,EAAAlF,KAAA;YACA;UACA;UACA,KAAAM,YAAA;YACA,IAAAkE,CAAA,CAAAE,aAAA,cACA,SAAAF,CAAA,CAAAG,eAAA,IACA,IAAAC,eAAA,EAAAJ,CAAA,CAAAK,SAAA,SAAAD,eAAA,MAAAO,IAAA,OACA,IAAAP,eAAA,MAAAO,IAAA,WAAAP,eAAA,EAAAJ,CAAA,CAAAM,OAAA;cACAxE,YAAA,OAAAyE,iBAAA,EAAAP,CAAA,CAAAQ,UAAA,EAAAC,MAAA,CAAAT,CAAA,CAAAU,IAAA,EAAAlF,KAAA;YACA;UACA;QACA;MAAA,SAAAoF,GAAA;QAAArB,SAAA,CAAAsB,CAAA,CAAAD,GAAA;MAAA;QAAArB,SAAA,CAAAuB,CAAA;MAAA;MACA,KAAAhF,YAAA,GAAAA,YAAA;MAEA,IAAAnD,IAAA;QACA;QACA,KAAAiD,MAAA,OAAA2E,iBAAA,OAAAlG,wBAAA,EAAA0G,QAAA,CAAAjF,YAAA,EAAAkF,GAAA,MAAA5G,wBAAA,EAAAoB,KAAA;QACA,KAAAK,SAAA,OAAA0E,iBAAA,OAAA5F,2BAAA,EAAAoG,QAAA,CAAAjF,YAAA,EAAAkF,GAAA,MAAAtG,2BAAA,EAAAc,KAAA;MACA;QACA,KAAAI,MAAA,OAAA2E,iBAAA,OAAAnG,wBAAA,EAAAqG,MAAA,CAAA3E,YAAA,EAAAkF,GAAA,MAAA3G,wBAAA,EAAAmB,KAAA;QACA,KAAAK,SAAA,OAAA0E,iBAAA,OAAA7F,2BAAA,EAAA+F,MAAA,CAAA3E,YAAA,EAAAkF,GAAA,MAAArG,2BAAA,EAAAa,KAAA;MACA;IACA;IACAyF,iBAAA,WAAAA,kBAAAC,MAAA;MACA,KAAA/D,KAAA,gBAAA+D,MAAA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAhE,KAAA;IACA;IACAiE,gBAAA,WAAAA,iBAAAF,MAAA;MACA,KAAA/D,KAAA,eAAA+D,MAAA;IACA;EACA;AACA;AAAAG,OAAA,CAAAvI,OAAA,GAAAwI,SAAA"}]}