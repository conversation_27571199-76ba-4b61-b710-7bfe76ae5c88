{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\bankSlip.vue?vue&type=template&id=b0d50104&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\bankSlip.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}