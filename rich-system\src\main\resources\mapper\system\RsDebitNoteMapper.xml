<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.RsDebitNoteMapper">
    
    <resultMap type="RsDebitNote" id="RsDebitNoteResult">
        <result property="debitNoteId"    column="debit_note_id"    />
        <result property="invoiceId"    column="invoice_id"    />
        <result property="sqdRctNo"    column="sqd_rct_no"    />
        <result property="serviceId"    column="service_id"    />
        <result property="rctId"    column="rct_id"    />
        <result property="isRecievingOrPaying"    column="is_recieving_or_paying"    />
        <result property="companyName"    column="company_name"    />
        <result property="bankAccountCode"    column="bank_account_code"    />
        <result property="clearingCompanyId"    column="clearing_company_id"    />
        <result property="dnCurrencyCode"    column="dn_currency_code"    />
        <result property="billReceivable"    column="bill_receivable"    />
        <result property="billPayable"    column="bill_payable"    />
        <result property="billStatus"    column="bill_status"    />
        <result property="isCombinedInvoice"    column="is_combined_invoice"    />
        <result property="invoiceCodeNo"    column="invoice_code_no"    />
        <result property="invoiceStatus"    column="invoice_status"    />
        <result property="writeoffStatus"    column="writeoff_status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="companyBelongsTo"    column="company_belongs_to"    />
        <result property="clearingCompanyName"    column="clearing_company_name"    />
        <result property="requestPaymentDate"    column="request_payment_date"    />
        <result property="expectedPaymentDate"    column="expected_payment_date"    />
        <result property="actualPaymentDate"    column="actual_payment_date"    />
        <result property="clearingCompanyBankAccount"    column="clearing_company_bank_account"    />
    </resultMap>

    <sql id="selectRsDebitNoteVo">
        select debit_note_id,invoice_id, sqd_rct_no,clearing_company_bank_account, service_id, rct_id, is_recieving_or_paying, company_name,bank_account_code, clearing_company_id, dn_currency_code, bill_receivable, bill_payable, bill_status, is_combined_invoice, invoice_code_no, invoice_status, writeoff_status, create_time, update_time, company_belongs_to, clearing_company_name, request_payment_date, expected_payment_date, actual_payment_date from rs_debit_note
    </sql>

    <select id="selectRsDebitNoteList" parameterType="RsDebitNote" resultMap="RsDebitNoteResult">
        <include refid="selectRsDebitNoteVo"/>
        <where>  
            <if test="sqdRctNo != null  and sqdRctNo != ''"> and sqd_rct_no = #{sqdRctNo}</if>
            <if test="serviceId != null "> and service_id = #{serviceId}</if>
            <if test="rctId != null "> and rct_id = #{rctId}</if>
            <if test="isRecievingOrPaying != null "> and is_recieving_or_paying = #{isRecievingOrPaying}</if>
            <if test="companyName != null  and companyName != ''"> and company_name like concat('%', #{companyName}, '%')</if>
            <if test="clearingCompanyId != null "> and clearing_company_id = #{clearingCompanyId}</if>
            <if test="dnCurrencyCode != null  and dnCurrencyCode != ''"> and dn_currency_code = #{dnCurrencyCode}</if>
            <if test="billReceivable != null "> and bill_receivable = #{billReceivable}</if>
            <if test="billPayable != null "> and bill_payable = #{billPayable}</if>
            <if test="billStatus != null  and billStatus != ''"> and bill_status = #{billStatus}</if>
            <if test="isCombinedInvoice != null "> and is_combined_invoice = #{isCombinedInvoice}</if>
            <if test="invoiceCodeNo != null  and invoiceCodeNo != ''"> and invoice_code_no = #{invoiceCodeNo}</if>
            <if test="invoiceStatus != null  and invoiceStatus != ''"> and invoice_status = #{invoiceStatus}</if>
            <if test="writeoffStatus != null  and writeoffStatus != ''"> and writeoff_status = #{writeoffStatus}</if>
        </where>
    </select>
    
    <select id="selectRsDebitNoteByDebitNoteId" parameterType="Long" resultMap="RsDebitNoteResult">
        <include refid="selectRsDebitNoteVo"/>
        where debit_note_id = #{debitNoteId}
    </select>
        
    <insert id="insertRsDebitNote" parameterType="RsDebitNote" useGeneratedKeys="true" keyProperty="debitNoteId">
        insert into rs_debit_note
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sqdRctNo != null and sqdRctNo != ''">sqd_rct_no,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="rctId != null">rct_id,</if>
            <if test="isRecievingOrPaying != null">is_recieving_or_paying,</if>
            <if test="companyName != null">company_name,</if>
            <if test="clearingCompanyId != null">clearing_company_id,</if>
            <if test="dnCurrencyCode != null">dn_currency_code,</if>
            <if test="billReceivable != null">bill_receivable,</if>
            <if test="billPayable != null">bill_payable,</if>
            <if test="billStatus != null">bill_status,</if>
            <if test="isCombinedInvoice != null">is_combined_invoice,</if>
            <if test="invoiceCodeNo != null">invoice_code_no,</if>
            <if test="invoiceStatus != null">invoice_status,</if>
            <if test="writeoffStatus != null">writeoff_status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="companyBelongsTo != null">company_belongs_to,</if>
            <if test="clearingCompanyName != null">clearing_company_name,</if>
            <if test="requestPaymentDate != null">request_payment_date,</if>
            <if test="expectedPaymentDate != null">expected_payment_date,</if>
            <if test="actualPaymentDate != null">actual_payment_date,</if>
            <if test="clearingCompanyBankAccount != null">clearing_company_bank_account,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sqdRctNo != null and sqdRctNo != ''">#{sqdRctNo},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="rctId != null">#{rctId},</if>
            <if test="isRecievingOrPaying != null">#{isRecievingOrPaying},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="clearingCompanyId != null">#{clearingCompanyId},</if>
            <if test="dnCurrencyCode != null">#{dnCurrencyCode},</if>
            <if test="billReceivable != null">#{billReceivable},</if>
            <if test="billPayable != null">#{billPayable},</if>
            <if test="billStatus != null">#{billStatus},</if>
            <if test="isCombinedInvoice != null">#{isCombinedInvoice},</if>
            <if test="invoiceCodeNo != null">#{invoiceCodeNo},</if>
            <if test="invoiceStatus != null">#{invoiceStatus},</if>
            <if test="writeoffStatus != null">#{writeoffStatus},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="companyBelongsTo != null">#{companyBelongsTo},</if>
            <if test="clearingCompanyName != null">#{clearingCompanyName},</if>
            <if test="requestPaymentDate != null">#{requestPaymentDate},</if>
            <if test="expectedPaymentDate != null">#{expectedPaymentDate},</if>
            <if test="actualPaymentDate != null">#{actualPaymentDate},</if>
            <if test="clearingCompanyBankAccount != null">#{clearingCompanyBankAccount},</if>
         </trim>
    </insert>

    <update id="updateRsDebitNote" parameterType="RsDebitNote">
        update rs_debit_note
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoiceId != null ">invoice_id = #{invoiceId},</if>
            <if test="sqdRctNo != null and sqdRctNo != ''">sqd_rct_no = #{sqdRctNo},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="rctId != null">rct_id = #{rctId},</if>
            <if test="isRecievingOrPaying != null">is_recieving_or_paying = #{isRecievingOrPaying},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="clearingCompanyId != null">clearing_company_id = #{clearingCompanyId},</if>
            <if test="dnCurrencyCode != null">dn_currency_code = #{dnCurrencyCode},</if>
            <if test="billReceivable != null">bill_receivable = #{billReceivable},</if>
            <if test="billPayable != null">bill_payable = #{billPayable},</if>
            <if test="billStatus != null">bill_status = #{billStatus},</if>
            <if test="isCombinedInvoice != null">is_combined_invoice = #{isCombinedInvoice},</if>
            <if test="invoiceCodeNo != null">invoice_code_no = #{invoiceCodeNo},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="writeoffStatus != null">writeoff_status = #{writeoffStatus},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="companyBelongsTo != null">company_belongs_to = #{companyBelongsTo},</if>
            <if test="clearingCompanyName != null">clearing_company_name = #{clearingCompanyName},</if>
            <if test="requestPaymentDate != null">request_payment_date = #{requestPaymentDate},</if>
            <if test="expectedPaymentDate != null">expected_payment_date = #{expectedPaymentDate},</if>
            <if test="actualPaymentDate != null">actual_payment_date = #{actualPaymentDate},</if>
            <if test="clearingCompanyBankAccount != null">clearing_company_bank_account = #{clearingCompanyBankAccount},</if>
        </trim>
        where debit_note_id = #{debitNoteId}
    </update>

    <delete id="deleteRsDebitNoteByDebitNoteId" parameterType="Long">
        delete from rs_debit_note where debit_note_id = #{debitNoteId}
    </delete>

    <delete id="deleteRsDebitNoteByDebitNoteIds" parameterType="String">
        delete from rs_debit_note where debit_note_id in 
        <foreach item="debitNoteId" collection="array" open="(" separator="," close=")">
            #{debitNoteId}
        </foreach>
    </delete>

    <select id="selectRsDebitNoteByRctId" resultMap="RsDebitNoteResult">
        <include refid="selectRsDebitNoteVo"/>
        where rct_id = #{rctId}
    </select>

    <delete id="deleteRsDebitNoteByRctId">
        delete from rs_debit_note where rct_id = #{rctId}
    </delete>

    <insert id="upsertRsDebitNote" parameterType="RsDebitNote" useGeneratedKeys="true" keyProperty="debitNoteId">
        insert into rs_debit_note
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="debitNoteId != null">debit_note_id,</if>
            <if test="invoiceId != null">invoice_id,</if>
            <if test="sqdRctNo != null and sqdRctNo != ''">sqd_rct_no,</if>
            <if test="serviceId != null">service_id,</if>
            <if test="rctId != null">rct_id,</if>
            <if test="isRecievingOrPaying != null">is_recieving_or_paying,</if>
            <if test="companyName != null">company_name,</if>
            <if test="bankAccountCode != null">bank_account_code,</if>
            <if test="companyBelongsTo != null">company_belongs_to,</if>
            <if test="clearingCompanyId != null">clearing_company_id,</if>
            <if test="clearingCompanyName != null">clearing_company_name,</if>
            <if test="clearingCompanyBankAccount != null">clearing_company_bank_account,</if> <!-- 新增 -->
            <if test="dnCurrencyCode != null">dn_currency_code,</if>
            <if test="billReceivable != null">bill_receivable,</if>
            <if test="billPayable != null">bill_payable,</if>
            <if test="billStatus != null">bill_status,</if>
            <if test="isCombinedInvoice != null">is_combined_invoice,</if>
            <if test="invoiceCodeNo != null">invoice_code_no,</if>
            <if test="invoiceStatus != null">invoice_status,</if>
            <if test="writeoffStatus != null">writeoff_status,</if>
            <if test="requestPaymentDate != null">request_payment_date,</if>
            <if test="expectedPaymentDate != null">expected_payment_date,</if>
            <if test="actualPaymentDate != null">actual_payment_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="debitNoteId != null">#{debitNoteId},</if>
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="sqdRctNo != null and sqdRctNo != ''">#{sqdRctNo},</if>
            <if test="serviceId != null">#{serviceId},</if>
            <if test="rctId != null">#{rctId},</if>
            <if test="isRecievingOrPaying != null">#{isRecievingOrPaying},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="bankAccountCode != null">#{bankAccountCode},</if>
            <if test="companyBelongsTo != null">#{companyBelongsTo},</if>
            <if test="clearingCompanyId != null">#{clearingCompanyId},</if>
            <if test="clearingCompanyName != null">#{clearingCompanyName},</if>
            <if test="clearingCompanyBankAccount != null">#{clearingCompanyBankAccount},</if> <!-- 新增 -->
            <if test="dnCurrencyCode != null">#{dnCurrencyCode},</if>
            <if test="billReceivable != null">#{billReceivable},</if>
            <if test="billPayable != null">#{billPayable},</if>
            <if test="billStatus != null">#{billStatus},</if>
            <if test="isCombinedInvoice != null">#{isCombinedInvoice},</if>
            <if test="invoiceCodeNo != null">#{invoiceCodeNo},</if>
            <if test="invoiceStatus != null">#{invoiceStatus},</if>
            <if test="writeoffStatus != null">#{writeoffStatus},</if>
            <if test="requestPaymentDate != null">#{requestPaymentDate},</if>
            <if test="expectedPaymentDate != null">#{expectedPaymentDate},</if>
            <if test="actualPaymentDate != null">#{actualPaymentDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
        ON DUPLICATE KEY UPDATE
        <trim suffixOverrides=",">
            <if test="sqdRctNo != null and sqdRctNo != ''">sqd_rct_no = #{sqdRctNo},</if>
            <if test="invoiceId != null ">invoice_id = #{invoiceId},</if>
            <if test="serviceId != null">service_id = #{serviceId},</if>
            <if test="rctId != null">rct_id = #{rctId},</if>
            <if test="isRecievingOrPaying != null">is_recieving_or_paying = #{isRecievingOrPaying},</if>
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="bankAccountCode != null">bank_account_code = #{bankAccountCode},</if>
            <if test="companyBelongsTo != null">company_belongs_to = #{companyBelongsTo},</if>
            <if test="clearingCompanyId != null">clearing_company_id = #{clearingCompanyId},</if>
            <if test="clearingCompanyName != null">clearing_company_name = #{clearingCompanyName},</if>
            <if test="clearingCompanyBankAccount != null">clearing_company_bank_account = #{clearingCompanyBankAccount},</if> <!-- 新增 -->
            <if test="dnCurrencyCode != null">dn_currency_code = #{dnCurrencyCode},</if>
            <if test="billReceivable != null">bill_receivable = #{billReceivable},</if>
            <if test="billPayable != null">bill_payable = #{billPayable},</if>
            <if test="billStatus != null">bill_status = #{billStatus},</if>
            <if test="isCombinedInvoice != null">is_combined_invoice = #{isCombinedInvoice},</if>
            <if test="invoiceCodeNo != null">invoice_code_no = #{invoiceCodeNo},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="writeoffStatus != null">writeoff_status = #{writeoffStatus},</if>
            <if test="requestPaymentDate != null">request_payment_date = #{requestPaymentDate},</if>
            <if test="expectedPaymentDate != null">expected_payment_date = #{expectedPaymentDate},</if>
            <if test="actualPaymentDate != null">actual_payment_date = #{actualPaymentDate},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
    </insert>


</mapper>