package com.rich.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.rich.common.core.domain.entity.RsVatInvoiceDetails;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.enums.BusinessType;
import com.rich.system.service.RsVatInvoiceDetailsService;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.common.core.page.TableDataInfo;

/**
 * 发票明细信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@RestController
@RequestMapping("/system/vatinvoicedetails")
public class RsVatInvoiceDetailsController extends BaseController {
    @Autowired
    private RsVatInvoiceDetailsService rsVatInvoiceDetailsService;

    /**
     * 查询发票明细信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoicedetails:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsVatInvoiceDetails rsVatInvoiceDetails) {
        startPage();
        List<RsVatInvoiceDetails> list = rsVatInvoiceDetailsService.selectRsVatInvoiceDetailsList(rsVatInvoiceDetails);
        return getDataTable(list);
    }

    /**
     * 导出发票明细信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoicedetails:export')")
    @Log(title = "发票明细信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsVatInvoiceDetails rsVatInvoiceDetails) {
        List<RsVatInvoiceDetails> list = rsVatInvoiceDetailsService.selectRsVatInvoiceDetailsList(rsVatInvoiceDetails);
        ExcelUtil<RsVatInvoiceDetails> util = new ExcelUtil<RsVatInvoiceDetails>(RsVatInvoiceDetails.class);
        util.exportExcel(response, list, "发票明细信息数据");
    }

    /**
     * 获取发票明细信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoicedetails:query')")
    @GetMapping(value = "/{invoiceDetailsId}")
    public AjaxResult getInfo(@PathVariable("invoiceDetailsId") Long invoiceDetailsId) {
        return AjaxResult.success(rsVatInvoiceDetailsService.selectRsVatInvoiceDetailsByInvoiceDetailsId(invoiceDetailsId));
    }

    /**
     * 新增发票明细信息
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoicedetails:add')")
    @Log(title = "发票明细信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsVatInvoiceDetails rsVatInvoiceDetails) {
        return toAjax(rsVatInvoiceDetailsService.insertRsVatInvoiceDetails(rsVatInvoiceDetails));
    }

    /**
     * 修改发票明细信息
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoicedetails:edit')")
    @Log(title = "发票明细信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsVatInvoiceDetails rsVatInvoiceDetails) {
        return toAjax(rsVatInvoiceDetailsService.updateRsVatInvoiceDetails(rsVatInvoiceDetails));
    }

    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoicedetails:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsVatInvoiceDetails rsVatInvoiceDetails) {
        rsVatInvoiceDetails.setUpdateBy(getUserId());
        return toAjax(rsVatInvoiceDetailsService.changeStatus(rsVatInvoiceDetails));
    }

    /**
     * 删除发票明细信息
     */
    @PreAuthorize("@ss.hasPermi('system:vatinvoicedetails:remove')")
    @Log(title = "发票明细信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{invoiceDetailsIds}")
    public AjaxResult remove(@PathVariable Long[] invoiceDetailsIds) {
        return toAjax(rsVatInvoiceDetailsService.deleteRsVatInvoiceDetailsByInvoiceDetailsIds(invoiceDetailsIds));
    }
}
