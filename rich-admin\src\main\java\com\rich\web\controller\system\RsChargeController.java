package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.MidChargeBankWriteoff;
import com.rich.common.core.domain.entity.RsCharge;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.DataAggregatorService;
import com.rich.system.service.RsChargeService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 费用明细Controller
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@RestController
@RequestMapping("/system/rscharge")
public class RsChargeController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(RsChargeController.class);

    @Autowired
    private RsChargeService rsChargeService;

    @Autowired
    private DataAggregatorService dataAggregatorService;

    /**
     * 查询费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:list')")
    @GetMapping("/list")
    public TableDataInfo list(RsCharge rsCharge) {
        startPage();
        List<RsCharge> list = rsChargeService.selectRsChargeList(rsCharge);
        return getDataTable(list);
    }

    /**
     * 查询费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:list')")
    @PostMapping("/aggregator")
    public AjaxResult aggregator(@RequestBody RsCharge rsCharge) {

        List<RsCharge> list = new ArrayList<>();
        if (rsCharge.getRsChargeList() == null || rsCharge.getRsChargeList().isEmpty()) {
            list = rsChargeService.selectRsChargeList(rsCharge);
        } else {
            list = rsCharge.getRsChargeList();
        }

        // 3. 使用Service提取聚合配置
        Map<String, Object> aggregatorConfig;
        try {
            aggregatorConfig = dataAggregatorService.extractAggregatorConfig(rsCharge);
        } catch (RuntimeException e) {
            logger.error("提取聚合配置失败", e);
            return AjaxResult.error(e.getMessage());
        }

        // 5. 执行数据聚合
        List<Map<String, Object>> aggregatedData = dataAggregatorService.aggregateData(list, aggregatorConfig);

        return AjaxResult.success(aggregatedData);
    }


    /**
     * 导出费用明细列表
     */
    @PreAuthorize("@ss.hasPermi('system:charge:export')")
    @Log(title = "费用明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RsCharge rsCharge) {
        List<RsCharge> list = rsChargeService.selectRsChargeList(rsCharge);
        ExcelUtil<RsCharge> util = new ExcelUtil<RsCharge>(RsCharge.class);
        util.exportExcel(response, list, "费用明细数据");
    }

    /**
     * 获取费用明细详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:charge:query')")
    @GetMapping(value = "/{chargeId}")
    public AjaxResult getInfo(@PathVariable("chargeId") Long chargeId) {
        return AjaxResult.success(rsChargeService.selectRsChargeByChargeId(chargeId));
    }

    @GetMapping(value = "/service/{serviceId}")
    public AjaxResult getCharges(@PathVariable("serviceId") Long serviceId) {
        return AjaxResult.success(rsChargeService.selectRsChargeByServiceId(serviceId));
    }

    /**
     * 新增费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:add')")
    @Log(title = "费用明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RsCharge rsCharge) {
        return toAjax(rsChargeService.insertRsCharge(rsCharge));
    }

    /**
     * 修改费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "费用明细", businessType = BusinessType.UPDATE)
    @PostMapping("/batchCharges")
    public AjaxResult batchCharges(@RequestParam List<RsCharge> rsCharge) {
        for (RsCharge charge : rsCharge) {
            rsChargeService.updateRsCharge(charge);
        }
        return AjaxResult.success();
    }


    /**
     * 状态状态
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody RsCharge rsCharge) {
        rsCharge.setUpdateBy(getUserId());
        return toAjax(rsChargeService.changeStatus(rsCharge));
    }

    /**
     * 删除费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:remove')")
    @Log(title = "费用明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{chargeIds}")
    public AjaxResult remove(@PathVariable Long[] chargeIds) {
        return toAjax(rsChargeService.deleteRsChargeByChargeIds(chargeIds));
    }

    @GetMapping(value = "/charges")
    public TableDataInfo getRsChargeList(RsCharge rsCharge) {
        startPage();
        List<RsCharge> data = rsChargeService.selectRsChargeByrctId(rsCharge);
        return getDataTable(data);
    }

    /**
     * 销账时查询费用明细
     *
     * @param rsCharge
     * @return
     */
    @GetMapping("/selectList")
    public List<RsCharge> selectList(RsCharge rsCharge) {
        return rsChargeService.selectWriteOffRsChargeList(rsCharge);
    }

    /**
     * 增加对冲费用
     *
     * @param rsCharge
     * @return
     */
    @GetMapping("/findHedging")
    public List<RsCharge> findHedging(RsCharge rsCharge) {
        return rsChargeService.findHedging(rsCharge);
    }

    /**
     * 销账操作
     *
     * @param rsCharge 费用对象，包含费用列表和销账记录列表
     * @return 操作结果
     * <p>
     * 我已经成功优化了 RsChargeController 中的 writeOff 方法。主要改进如下：
     * <p>
     * 1. 代码结构优化
     * <p>
     * - 将复杂逻辑拆分为多个清晰的辅助方法
     * - 每个方法职责单一，提高了代码可读性和可维护性
     * <p>
     * 2. 性能优化
     * <p>
     * - 使用 Map 替代嵌套循环查找，时间复杂度从 O(n²) 降至 O(n)
     * - 使用 Stream API 进行数据过滤和转换，代码更简洁高效
     * - 使用 Set 集合快速判断费用ID是否需要保留
     * <p>
     * 3. 代码质量提升
     * <p>
     * - 添加了详细的日志记录，便于问题排查
     * - 增强了参数校验逻辑
     * - 所有方法都添加了清晰的JavaDoc注释
     * - 使用了更现代的Java 8+ 特性
     * <p>
     * 4. 主要改进点
     * <p>
     * - validateWriteOffParams: 独立的参数验证方法
     * - createWriteoffMap: 创建销账记录查找Map，提高查找效率
     * - filterChargesForWriteOff: 过滤需要保留的费用记录
     * - isChargeFullyWrittenOff: 判断费用是否已完全销账
     * - filterWriteoffsForRetainedCharges: 过滤需要保留的销账记录
     * <p>
     * 5. 业务逻辑保持不变
     * <p>
     * - 仍然保留原有的业务规则：只有销账余额为0的费用才会被移除
     * - 保持了原有的事务处理逻辑
     * - 确保了数据的一致性
     * <p>
     * 优化后的代码更加清晰、高效，同时保持了原有的业务逻辑不变。
     */
    @PostMapping("/writeoff")
    @Log(title = "费用销账", businessType = BusinessType.UPDATE)
    public AjaxResult writeOff(@RequestBody RsCharge rsCharge) {
        log.info("开始执行销账操作");

        // 参数校验
        if (!validateWriteOffParams(rsCharge)) {
            log.debug("销账参数为空，直接返回成功");
            return AjaxResult.success();
        }

        List<RsCharge> rsChargeList = rsCharge.getRsChargeList();
        List<MidChargeBankWriteoff> midChargeBankWriteoffList = rsCharge.getMidChargeBankWriteoffList();

        log.info("待处理费用数量: {}, 销账记录数量: {}", rsChargeList.size(), midChargeBankWriteoffList.size());

        // 创建销账记录的查找Map，提高查找效率
        Map<Long, MidChargeBankWriteoff> writeoffMap = createWriteoffMap(midChargeBankWriteoffList);

        // 过滤出需要保留的费用记录
        List<RsCharge> retainedCharges = filterChargesForWriteOff(rsChargeList, writeoffMap);

        // 过滤出需要保留的销账记录
        List<MidChargeBankWriteoff> retainedWriteoffs = filterWriteoffsForRetainedCharges(midChargeBankWriteoffList, retainedCharges);

        // 执行销账操作
        if (!retainedCharges.isEmpty()) {
            log.info("执行销账操作，保留费用数量: {}, 保留销账记录数量: {}", retainedCharges.size(), retainedWriteoffs.size());
            rsChargeService.insertRsChargeAndMidChargeBankWriteoff(retainedCharges, retainedWriteoffs);
        } else {
            log.info("所有费用已完成销账，无需处理");
        }

        return AjaxResult.success();
    }

    /**
     * 验证销账参数
     */
    private boolean validateWriteOffParams(RsCharge rsCharge) {
        if (rsCharge == null) {
            return false;
        }

        List<RsCharge> rsChargeList = rsCharge.getRsChargeList();
        List<MidChargeBankWriteoff> midChargeBankWriteoffList = rsCharge.getMidChargeBankWriteoffList();

        return !CollectionUtils.isEmpty(rsChargeList) && !CollectionUtils.isEmpty(midChargeBankWriteoffList);
    }

    /**
     * 创建销账记录的查找Map
     */
    private Map<Long, MidChargeBankWriteoff> createWriteoffMap(List<MidChargeBankWriteoff> writeoffList) {
        return writeoffList.stream()
                .filter(Objects::nonNull)
                .filter(writeoff -> writeoff.getChargeId() != null)
                .collect(Collectors.toMap(
                        MidChargeBankWriteoff::getChargeId,
                        writeoff -> writeoff,
                        (existing, replacement) -> existing // 如果有重复，保留第一个
                ));
    }

    /**
     * 过滤费用记录，返回需要保留的费用
     */
    private List<RsCharge> filterChargesForWriteOff(List<RsCharge> charges,
                                                    Map<Long, MidChargeBankWriteoff> writeoffMap) {
        return charges.stream()
                .filter(Objects::nonNull)
                .filter(charge -> charge.getChargeId() != null)
                .filter(charge -> !isChargeFullyWrittenOff(charge, writeoffMap))
                .collect(Collectors.toList());
    }

    /**
     * 判断费用是否已完全销账
     */
    private boolean isChargeFullyWrittenOff(RsCharge charge, Map<Long, MidChargeBankWriteoff> writeoffMap) {
        // 检查是否有对应的销账记录
        if (!writeoffMap.containsKey(charge.getChargeId())) {
            return false;
        }

        // 检查销账余额是否为0
        MidChargeBankWriteoff chargeWriteoff = charge.getMidChargeBankWriteoff();
        if (chargeWriteoff == null || chargeWriteoff.getSqdDnCurrencyBalance() == null) {
            return false;
        }

        boolean isFullyWrittenOff = chargeWriteoff.getSqdDnCurrencyBalance()
                .compareTo(BigDecimal.ZERO) == 0;

        if (isFullyWrittenOff) {
            log.debug("费用ID: {} 已完全销账，余额为0", charge.getChargeId());
        }

        return isFullyWrittenOff;
    }

    /**
     * 过滤销账记录，只保留与保留费用相关的记录
     */
    private List<MidChargeBankWriteoff> filterWriteoffsForRetainedCharges(
            List<MidChargeBankWriteoff> writeoffs, List<RsCharge> retainedCharges) {

        Set<Long> retainedChargeIds = retainedCharges.stream()
                .map(RsCharge::getChargeId)
                .collect(Collectors.toSet());

        return writeoffs.stream()
                .filter(Objects::nonNull)
                .filter(writeoff -> writeoff.getChargeId() != null)
                .filter(writeoff -> retainedChargeIds.contains(writeoff.getChargeId()))
                .collect(Collectors.toList());
    }

    @PostMapping("/verify")
    public AjaxResult verify(@RequestBody RsCharge rsCharge) {
        List<RsCharge> rsChargeList = rsCharge.getRsChargeList();
        rsChargeService.verifyRsCharge(rsChargeList);
        return AjaxResult.success();
    }

    @PostMapping("/turnback")
    public AjaxResult turnBackWriteoff(@RequestBody RsCharge rsCharge) {
        List<RsCharge> rsChargeList = rsCharge.getRsChargeList();
        rsChargeService.turnBackWriteoffList(rsChargeList);
        return AjaxResult.success();
    }

    /**
     * 修改费用明细
     */
    @PreAuthorize("@ss.hasPermi('system:charge:edit')")
    @Log(title = "费用明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RsCharge rsCharge) {
        return toAjax(rsChargeService.updateRsCharge(rsCharge));
    }

}
