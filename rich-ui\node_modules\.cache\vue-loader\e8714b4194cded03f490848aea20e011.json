{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\DocDeclareComponent.vue?vue&type=template&id=07b46249&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\serviceComponents\\DocDeclareComponent.vue", "mtime": 1754881964231}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}