package com.rich.system.mapper;

import java.util.List;

import com.rich.common.core.domain.entity.RsVatInvoiceDetails;
import org.apache.ibatis.annotations.Mapper;

/**
 * 发票明细信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Mapper
public interface RsVatInvoiceDetailsMapper {
    /**
     * 查询发票明细信息
     *
     * @param invoiceDetailsId 发票明细信息主键
     * @return 发票明细信息
     */
    RsVatInvoiceDetails selectRsVatInvoiceDetailsByInvoiceDetailsId(Long invoiceDetailsId);

    /**
     * 查询发票明细信息列表
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 发票明细信息集合
     */
    List<RsVatInvoiceDetails> selectRsVatInvoiceDetailsList(RsVatInvoiceDetails rsVatInvoiceDetails);

    /**
     * 新增发票明细信息
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 结果
     */
    int insertRsVatInvoiceDetails(RsVatInvoiceDetails rsVatInvoiceDetails);

    /**
     * 修改发票明细信息
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 结果
     */
    int updateRsVatInvoiceDetails(RsVatInvoiceDetails rsVatInvoiceDetails);

    /**
     * 删除发票明细信息
     *
     * @param invoiceDetailsId 发票明细信息主键
     * @return 结果
     */
    int deleteRsVatInvoiceDetailsByInvoiceDetailsId(Long invoiceDetailsId);

    /**
     * 批量删除发票明细信息
     *
     * @param invoiceDetailsIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteRsVatInvoiceDetailsByInvoiceDetailsIds(Long[] invoiceDetailsIds);
}
