{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\message.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\message.js", "mtime": 1678688095230}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listMessage", "query", "request", "url", "method", "params", "countNewMessage", "getMessage", "messageId", "addMessage", "data", "updateMessage", "delMessage", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/message.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询消息通知列表\r\nexport function listMessage(query) {\r\n  return request({\r\n    url: '/system/message/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询消息通知列表\r\nexport function countNewMessage(query) {\r\n  return request({\r\n    url: '/system/message/newMessage',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询消息通知详细\r\nexport function getMessage(messageId) {\r\n  return request({\r\n    url: '/system/message/' + messageId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增消息通知\r\nexport function addMessage(data) {\r\n  return request({\r\n    url: '/system/message',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改消息通知\r\nexport function updateMessage(data) {\r\n  return request({\r\n    url: '/system/message',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除消息通知\r\nexport function delMessage(messageId) {\r\n  return request({\r\n    url: '/system/message/' + messageId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(messageId, status) {\r\n  const data = {\r\n    messageId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/message/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,eAAeA,CAACL,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,4BAA4B;IACjCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,UAAUA,CAACC,SAAS,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGK,SAAS;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAACD,IAAI,EAAE;EAClC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,UAAUA,CAACJ,SAAS,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGK,SAAS;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACL,SAAS,EAAEM,MAAM,EAAE;EAC9C,IAAMJ,IAAI,GAAG;IACXF,SAAS,EAATA,SAAS;IACTM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}