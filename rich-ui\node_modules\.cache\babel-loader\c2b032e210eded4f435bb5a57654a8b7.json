{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\characteristics.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\characteristics.js", "mtime": 1678688095222}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listCharacteristics", "query", "request", "url", "method", "params", "getCharacteristics", "characteristicsId", "addCharacteristics", "data", "updateCharacteristics", "delCharacteristics"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/characteristics.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询物流注意事项列表\r\nexport function listCharacteristics(query) {\r\n  return request({\r\n    url: '/system/characteristics/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询物流注意事项详细\r\nexport function getCharacteristics(characteristicsId) {\r\n  return request({\r\n    url: '/system/characteristics/' + characteristicsId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增物流注意事项\r\nexport function addCharacteristics(data) {\r\n  return request({\r\n    url: '/system/characteristics',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改物流注意事项\r\nexport function updateCharacteristics(data) {\r\n  return request({\r\n    url: '/system/characteristics',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除物流注意事项\r\nexport function delCharacteristics(characteristicsId) {\r\n  return request({\r\n    url: '/system/characteristics/' + characteristicsId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,mBAAmBA,CAACC,KAAK,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,kBAAkBA,CAACC,iBAAiB,EAAE;EACpD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,iBAAiB;IACnDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,qBAAqBA,CAACD,IAAI,EAAE;EAC1C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,kBAAkBA,CAACJ,iBAAiB,EAAE;EACpD,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B,GAAGI,iBAAiB;IACnDH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}