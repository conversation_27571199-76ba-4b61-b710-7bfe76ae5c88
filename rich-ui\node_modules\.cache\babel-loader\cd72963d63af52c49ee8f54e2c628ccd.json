{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\ProgressStatus\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\ProgressStatus\\index.vue", "mtime": 1718100178794}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_store", "_interopRequireDefault", "require", "name", "props", "watch", "pass", "val", "value", "created", "loadProcessStatus", "data", "options", "label", "methods", "_this", "$store", "state", "processStatusList", "length", "redisList", "store", "dispatch", "then", "handleSelect", "$emit", "exports", "default", "_default"], "sources": ["src/components/ProgressStatus/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-select v-model=\"value\" filterable :placeholder=\"placeholder\" @change=\"handleSelect\" :disabled=\"disable\">\r\n      <el-option\r\n        v-for=\"item in options\"\r\n        :key=\"item.processStatusId\"\r\n        :label=\"item.processStatusShortName\"\r\n        :value=\"item.processStatusId\"\r\n\r\n      >\r\n      </el-option>\r\n    </el-select>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport store from '@/store'\r\n\r\nexport default {\r\n  name: 'ProgressStatus',\r\n  props: ['pass', 'placeholder','disable'],\r\n  watch: {\r\n    pass: function(val) {\r\n      this.value = val\r\n    }\r\n  },\r\n  created() {\r\n    this.loadProcessStatus()\r\n  },\r\n  data() {\r\n    return {\r\n      options: [{\r\n        value: 0,\r\n        label: '等待'\r\n      }, {\r\n        value: 1,\r\n        label: '行进'\r\n      }, {\r\n        value: 2,\r\n        label: '变更'\r\n      }, {\r\n        value: 3,\r\n        label: '异常'\r\n      }, {\r\n        value: 4,\r\n        label: '质押'\r\n      }, {\r\n        value: 5,\r\n        label: '驳回'\r\n      }, {\r\n        value: 6,\r\n        label: '确认'\r\n      }, {\r\n        value: 7,\r\n        label: '完成'\r\n      }, {\r\n        value: 8,\r\n        label: '取消'\r\n      }, {\r\n        value: 9,\r\n        label: '回收'\r\n      }],\r\n      value: this.pass\r\n    }\r\n  },\r\n  methods: {\r\n    loadProcessStatus() {\r\n      if (this.$store.state.data.processStatusList.length == 0 || this.$store.state.data.redisList.processStatusList) {\r\n        store.dispatch('getProcessStatus').then(() => {\r\n          this.options = this.$store.state.data.processStatusList\r\n        })\r\n      } else {\r\n        this.options = this.$store.state.data.processStatusList\r\n      }\r\n    },\r\n    handleSelect() {\r\n      this.$emit('progressStatus', this.value)\r\n    }\r\n  }\r\n}\r\n/*\r\n0   预定   提前预定，一般1-2天内完成即可\r\n1   当天   当天下班前完成即可\r\n2   常规   按部就班，按默认的标准流程推进\r\n3   紧急   半小时内关注\r\n4   立即   需要立即处理的事务\r\n*  */\r\n</script>\r\n\r\n<style scoped>\r\n.app-container {\r\n  margin: 0;\r\n  padding: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;AAgBA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,KAAA;EACAC,KAAA;IACAC,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAAC,KAAA,GAAAD,GAAA;IACA;EACA;EACAE,OAAA,WAAAA,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;QACAJ,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;QACAL,KAAA;QACAK,KAAA;MACA;MACAL,KAAA,OAAAF;IACA;EACA;EACAQ,OAAA;IACAJ,iBAAA,WAAAA,kBAAA;MAAA,IAAAK,KAAA;MACA,SAAAC,MAAA,CAAAC,KAAA,CAAAN,IAAA,CAAAO,iBAAA,CAAAC,MAAA,cAAAH,MAAA,CAAAC,KAAA,CAAAN,IAAA,CAAAS,SAAA,CAAAF,iBAAA;QACAG,cAAA,CAAAC,QAAA,qBAAAC,IAAA;UACAR,KAAA,CAAAH,OAAA,GAAAG,KAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAN,IAAA,CAAAO,iBAAA;QACA;MACA;QACA,KAAAN,OAAA,QAAAI,MAAA,CAAAC,KAAA,CAAAN,IAAA,CAAAO,iBAAA;MACA;IACA;IACAM,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA,wBAAAjB,KAAA;IACA;EACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AANAkB,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}