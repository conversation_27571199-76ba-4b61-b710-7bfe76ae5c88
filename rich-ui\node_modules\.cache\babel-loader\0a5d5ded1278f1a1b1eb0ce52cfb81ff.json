{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\contactor.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\contactor.vue", "mtime": 1718100178858}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAnY29udGFjdG9yJywKICBwcm9wczogWydzY29wZSddLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaXplOiB0aGlzLiRzdG9yZS5zdGF0ZS5hcHAuc2l6ZSB8fCAnbWluaScKICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBjaGVja0Nvbm5lY3Q6IGZ1bmN0aW9uIGNoZWNrQ29ubmVjdChyb3cpIHsKICAgICAgdGhpcy4kZW1pdCgncmV0dXJuJywgewogICAgICAgIGtleTogJ2NvbnRhY3RvcicsCiAgICAgICAgdmFsdWU6IHJvdwogICAgICB9KTsKICAgIH0KICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "names": ["name", "props", "data", "size", "$store", "state", "app", "methods", "checkConnect", "row", "$emit", "key", "value", "exports", "default", "_default"], "sources": ["src/views/system/company/contactor.vue"], "sourcesContent": ["<template>\r\n  <!--联系人中的内容（插槽）  [0]-->\r\n  <div>\r\n    <el-tooltip :open-delay=\"500\"\r\n                :disabled=\"scope.row.staff==null\"\r\n                placement=\"top\"\r\n    >\r\n      <div slot=\"content\">\r\n        <!--        <h6 style=\"margin: 0;\">\r\n                  {{\r\n                    scope.row.staff != null ? scope.row.staff.staffShortName != null ? scope.row.staff.staffShortName : '' : ''\r\n                  }}\r\n                </h6>\r\n                <h6\r\n                  style=\"margin: 0;font-weight:bold;\"\r\n                >\r\n                  {{\r\n                    scope.row.staff != null ? scope.row.staff.staffLocalName != null ? scope.row.staff.staffLocalName : '' : ''\r\n                  }}\r\n                </h6>\r\n                <h6 style=\"margin: 0;\">\r\n                  {{\r\n                    scope.row.staff != null ? scope.row.staff.staffEnName != null ? scope.row.staff.staffEnName : '' : ''\r\n                  }}\r\n                </h6>-->\r\n      </div>\r\n      <div class=\"content\">\r\n        <!--        <el-row style=\"display: flex\">\r\n                  <el-col :span=\"18\" style=\"padding: 0\">\r\n                    <h6 style=\"margin: 0;float: left\">\r\n                      {{\r\n                        scope.row.staff != null ? scope.row.staff.staffShortName != null ? scope.row.staff.staffShortName.length > 3 ? scope.row.staff.staffShortName.substring(0, 3) + '...' : scope.row.staff.staffShortName : '' : ''\r\n                      }}\r\n                    </h6>\r\n                  </el-col>\r\n                  <el-col :span=\"6\">\r\n                    <el-button v-hasPermi=\"['system:company:list']\" :size=\"size\"\r\n                               style=\"padding: 0;display: flex\"\r\n                               type=\"text\"\r\n                               @click=\"checkConnect(scope.row)\"\r\n                    >\r\n                      <div style=\"width: 50%;height: 50%\"> 222{{ scope.row.sqdMainAttn}}</div>\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n                <h6\r\n                  style=\"margin: 0;font-weight:bold;\"\r\n                >\r\n                  {{\r\n                    scope.row.staff != null ? scope.row.staff.staffLocalName != null ? scope.row.staff.staffLocalName.length > 3 ? scope.row.staff.staffLocalName.substring(0, 3) + '...' : scope.row.staff.staffLocalName : '' : ''\r\n                  }}\r\n                </h6>\r\n                <h6 style=\"margin: 0;\">\r\n                  {{\r\n                    scope.row.staff != null ? scope.row.staff.staffEnName != null ? scope.row.staff.staffEnName.length > 6 ? scope.row.staff.staffEnName.substring(0, 6) + '...' : scope.row.staff.staffEnName : '' : ''\r\n                  }}\r\n                </h6>-->\r\n        <span></span>\r\n        <el-button v-hasPermi=\"['system:company:list']\" :size=\"size\"\r\n                   style=\"padding: 0;display: flex\"\r\n                   type=\"text\"\r\n                   @click=\"checkConnect(scope.row)\"\r\n        >\r\n          <div style=\"width: 50%;height: 50%\">\r\n            {{ scope.row.mainStaffOfficialName !== null ? (\"[\" + scope.row.mainStaffOfficialName + \"]\") : \"[···]\" }}\r\n          </div>\r\n        </el-button>\r\n      </div>\r\n    </el-tooltip>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\n\r\nexport default {\r\n  name: 'contactor',\r\n  props: ['scope'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini'\r\n    }\r\n  },\r\n  methods: {\r\n    checkConnect(row) {\r\n      this.$emit('return', {key: 'contactor', value: row})\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.content {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eA0EA;EACAA,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;IACA;EACA;EACAI,OAAA;IACAC,YAAA,WAAAA,aAAAC,GAAA;MACA,KAAAC,KAAA;QAAAC,GAAA;QAAAC,KAAA,EAAAH;MAAA;IACA;EACA;AACA;AAAAI,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}