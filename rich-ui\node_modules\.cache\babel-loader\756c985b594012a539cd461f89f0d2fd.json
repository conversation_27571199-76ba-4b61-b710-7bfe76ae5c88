{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\belong.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\belong.vue", "mtime": 1700705944693}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiaW5kZXgiLAogIGRpY3RzOiBbJ3N5c19pc19pZGxlJ10sCiAgcHJvcHM6IFsnc2NvcGUnXQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["name", "dicts", "props", "exports", "default", "_default"], "sources": ["src/views/system/company/belong.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-tooltip placement=\"top\">\r\n      <div slot=\"content\">\r\n        <h6 style=\"margin: 0\">\r\n          {{\r\n            (scope.row.belongLocalName != null ? scope.row.belongLocalName : '') + ' ' + (scope.row.belongEnName != null ? scope.row.belongEnName : '')\r\n          }}\r\n        </h6>\r\n        <h6 class=\"column-text\" style=\"margin: 0\">\r\n          {{\r\n            (scope.row.followLocalName != null ? scope.row.followLocalName : '') + ' ' + (scope.row.followEnName != null ? scope.row.followEnName : '')\r\n          }}\r\n        </h6>\r\n      </div>\r\n      <div>\r\n        <dict-tag v-show=\"scope.row.belongTo==null||scope.row.belongTo==0\" :options=\"dict.type.sys_is_idle\"\r\n                  value=\"Y\"/>\r\n        <h6 style=\"margin: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n          {{\r\n            (scope.row.belongLocalName != null ? scope.row.belongLocalName : '') + ' ' + (scope.row.belongEnName != null ? scope.row.belongEnName : '')\r\n          }}\r\n        </h6>\r\n        <h6 class=\"column-text\" style=\"margin: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n          {{\r\n            (scope.row.followLocalName != null ? scope.row.followLocalName : '') + ' ' + (scope.row.followEnName != null ? scope.row.followEnName : '')\r\n          }}\r\n        </h6>\r\n      </div>\r\n    </el-tooltip>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"index\",\r\n  dicts: ['sys_is_idle'],\r\n  props: ['scope']\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.column-text {\r\n  color: #b7bbc2;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAkCA;EACAA,IAAA;EACAC,KAAA;EACAC,KAAA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}