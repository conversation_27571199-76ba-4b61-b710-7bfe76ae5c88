{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\warehouse\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\warehouse\\index.vue", "mtime": 1754876882601}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQppbXBvcnQgZnJlaWdodCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9mcmVpZ2h0IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiV2FyZWhvdXNlIiwNCiAgY29tcG9uZW50czoge2ZyZWlnaHR9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB0eXBlSWQ6ICc4Jw0KICAgIH07DQogIH0sDQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/warehouse", "sourcesContent": ["<template>\r\n  <freight :typeId=\"typeId\"/>\r\n</template>\r\n\r\n<script>\r\nimport freight from \"@/views/system/freight\";\r\n\r\nexport default {\r\n  name: \"Warehouse\",\r\n  components: {freight},\r\n  data() {\r\n    return {\r\n      typeId: '8'\r\n    };\r\n  },\r\n}\r\n</script>\r\n"]}]}