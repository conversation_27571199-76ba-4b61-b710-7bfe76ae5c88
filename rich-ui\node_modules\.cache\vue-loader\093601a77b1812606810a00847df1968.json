{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\IframeToggle\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\IframeToggle\\index.vue", "mtime": 1754876882540}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgSW5uZXJMaW5rIGZyb20gIi4uL0lubmVyTGluay9pbmRleCINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBjb21wb25lbnRzOiB7SW5uZXJMaW5rfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBpZnJhbWVWaWV3cygpIHsNCiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5zdGF0ZS50YWdzVmlldy5pZnJhbWVWaWV3cw0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;AAaA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/IframeToggle", "sourcesContent": ["<template>\r\n  <transition-group mode=\"out-in\" name=\"fade-transform\">\r\n    <inner-link\r\n      v-for=\"(item, index) in iframeViews\"\r\n      v-show=\"$route.path == item.path\"\r\n      :key=\"item.path\"\r\n      :iframeId=\"'iframe' + index\"\r\n      :src=\"item.meta.link\"\r\n    ></inner-link>\r\n  </transition-group>\r\n</template>\r\n\r\n<script>\r\nimport InnerLink from \"../InnerLink/index\"\r\n\r\nexport default {\r\n  components: {InnerLink},\r\n  computed: {\r\n    iframeViews() {\r\n      return this.$store.state.tagsView.iframeViews\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}