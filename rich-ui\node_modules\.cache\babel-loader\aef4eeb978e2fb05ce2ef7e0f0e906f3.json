{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\components\\icons\\svg-icons.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\components\\icons\\svg-icons.js", "mtime": 1678688095347}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLml0ZXJhdG9yLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiKTsKcmVxdWlyZSgiY29yZS1qcy9tb2R1bGVzL2VzLnJlZ2V4cC5leGVjLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcubWF0Y2guanMiKTsKdmFyIHJlcSA9IHJlcXVpcmUuY29udGV4dCgnLi4vLi4vLi4vYXNzZXRzL2ljb25zL3N2ZycsIGZhbHNlLCAvXC5zdmckLyk7CnZhciByZXF1aXJlQWxsID0gZnVuY3Rpb24gcmVxdWlyZUFsbChyZXF1aXJlQ29udGV4dCkgewogIHJldHVybiByZXF1aXJlQ29udGV4dC5rZXlzKCk7Cn07CnZhciByZSA9IC9cLlwvKC4qKVwuc3ZnLzsKdmFyIHN2Z0ljb25zID0gcmVxdWlyZUFsbChyZXEpLm1hcChmdW5jdGlvbiAoaSkgewogIHJldHVybiBpLm1hdGNoKHJlKVsxXTsKfSk7CnZhciBfZGVmYXVsdCA9IHN2Z0ljb25zOwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["req", "require", "context", "requireAll", "requireContext", "keys", "re", "svgIcons", "map", "i", "match", "_default", "exports", "default"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/components/icons/svg-icons.js"], "sourcesContent": ["const req = require.context('../../../assets/icons/svg', false, /\\.svg$/)\r\nconst requireAll = requireContext => requireContext.keys()\r\n\r\nconst re = /\\.\\/(.*)\\.svg/\r\n\r\nconst svgIcons = requireAll(req).map(i => {\r\n  return i.match(re)[1]\r\n})\r\n\r\nexport default svgIcons\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAMA,GAAG,GAAGC,OAAO,CAACC,OAAO,CAAC,2BAA2B,EAAE,KAAK,EAAE,QAAQ,CAAC;AACzE,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAGC,cAAc;EAAA,OAAIA,cAAc,CAACC,IAAI,CAAC,CAAC;AAAA;AAE1D,IAAMC,EAAE,GAAG,eAAe;AAE1B,IAAMC,QAAQ,GAAGJ,UAAU,CAACH,GAAG,CAAC,CAACQ,GAAG,CAAC,UAAAC,CAAC,EAAI;EACxC,OAAOA,CAAC,CAACC,KAAK,CAACJ,EAAE,CAAC,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AAAA,IAAAK,QAAA,GAEaJ,QAAQ;AAAAK,OAAA,CAAAC,OAAA,GAAAF,QAAA"}]}