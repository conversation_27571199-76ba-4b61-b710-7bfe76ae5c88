{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\booking.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\booking.js", "mtime": 1754876882460}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listBooking", "query", "request", "url", "method", "params", "psaBooking", "getBooking", "bookingId", "addBooking", "data", "updateBooking", "delBooking", "changeStatus", "status", "saveBookingLogistics", "saveBookingPreCarriage", "saveBookingExportDeclaration", "saveBookingImportClearance"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/booking.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询订舱单列表列表\r\nexport function listBooking(query) {\r\n  return request({\r\n    url: '/system/booking/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询订舱单列表列表\r\nexport function psaBooking(query) {\r\n  return request({\r\n    url: '/system/booking/psalist',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询订舱单列表详细\r\nexport function getBooking(bookingId) {\r\n  return request({\r\n    url: '/system/booking/' + bookingId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增订舱单列表\r\nexport function addBooking(data) {\r\n  return request({\r\n    url: '/system/booking',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改订舱单列表\r\nexport function updateBooking(data) {\r\n  return request({\r\n    url: '/system/booking',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除订舱单列表\r\nexport function delBooking(bookingId) {\r\n  return request({\r\n    url: '/system/booking/' + bookingId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(bookingId, status) {\r\n  const data = {\r\n    bookingId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/booking/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function saveBookingLogistics(data) {\r\n  return request({\r\n    url: '/system/booking/saveBookingLogistics',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function saveBookingPreCarriage(data) {\r\n  return request({\r\n    url: '/system/booking/saveBookingPreCarriage',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function saveBookingExportDeclaration(data) {\r\n  return request({\r\n    url: '/system/booking/saveBookingExportDeclaration',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function saveBookingImportClearance(data) {\r\n  return request({\r\n    url: '/system/booking/saveBookingImportClearance',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB;IAC3BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACL,KAAK,EAAE;EAChC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,UAAUA,CAACC,SAAS,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGK,SAAS;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,UAAUA,CAACC,IAAI,EAAE;EAC/B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,aAAaA,CAACD,IAAI,EAAE;EAClC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,iBAAiB;IACtBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,UAAUA,CAACJ,SAAS,EAAE;EACpC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,kBAAkB,GAAGK,SAAS;IACnCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACL,SAAS,EAAEM,MAAM,EAAE;EAC9C,IAAMJ,IAAI,GAAG;IACXF,SAAS,EAATA,SAAS;IACTM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASK,oBAAoBA,CAACL,IAAI,EAAE;EACzC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,sCAAsC;IAC3CC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASM,sBAAsBA,CAACN,IAAI,EAAE;EAC3C,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,wCAAwC;IAC7CC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASO,4BAA4BA,CAACP,IAAI,EAAE;EACjD,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,8CAA8C;IACnDC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASQ,0BAA0BA,CAACR,IAAI,EAAE;EAC/C,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,4CAA4C;IACjDC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}