{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\aggregator.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\aggregator.js", "mtime": 1754876882459}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVsZXRlQWdncmVnYXRvckNvbmZpZyA9IGRlbGV0ZUFnZ3JlZ2F0b3JDb25maWc7CmV4cG9ydHMubG9hZEFnZ3JlZ2F0b3JDb25maWdzID0gbG9hZEFnZ3JlZ2F0b3JDb25maWdzOwpleHBvcnRzLnNhdmVBZ2dyZWdhdG9yQ29uZmlnID0gc2F2ZUFnZ3JlZ2F0b3JDb25maWc7CnZhciBfcmVxdWVzdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZSgiQC91dGlscy9yZXF1ZXN0IikpOwovLyDkv53lrZjmsYfmgLvphY3nva4KZnVuY3Rpb24gc2F2ZUFnZ3JlZ2F0b3JDb25maWcoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9hZ2dyZWdhdG9yY29uZmlncycsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g6I635Y+W5rGH5oC76YWN572u5YiX6KGoCmZ1bmN0aW9uIGxvYWRBZ2dyZWdhdG9yQ29uZmlncyhkYXRhKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL2FnZ3JlZ2F0b3Jjb25maWdzL2xpc3QnLAogICAgbWV0aG9kOiAnZ2V0JywKICAgIHBhcmFtczogZGF0YQogIH0pOwp9CgovLyDliKDpmaTmsYfmgLvphY3nva4KZnVuY3Rpb24gZGVsZXRlQWdncmVnYXRvckNvbmZpZyhpZCkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAiL3N5c3RlbS9hZ2dyZWdhdG9yY29uZmlncy8iLmNvbmNhdChpZCksCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "saveAggregatorConfig", "data", "request", "url", "method", "loadAggregatorConfigs", "params", "deleteAggregatorConfig", "id", "concat"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/aggregator.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 保存汇总配置\r\nexport function saveAggregatorConfig(data) {\r\n  return request({\r\n    url: '/system/aggregatorconfigs',\r\n    method: 'post',\r\n    data\r\n  })\r\n}\r\n\r\n// 获取汇总配置列表\r\nexport function loadAggregatorConfigs(data) {\r\n  return request({\r\n    url: '/system/aggregatorconfigs/list',\r\n    method: 'get',\r\n    params: data\r\n  })\r\n}\r\n\r\n// 删除汇总配置\r\nexport function deleteAggregatorConfig(id) {\r\n  return request({\r\n    url: `/system/aggregatorconfigs/${id}`,\r\n    method: 'delete'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdH,IAAI,EAAJA;EACF,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,qBAAqBA,CAACJ,IAAI,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbE,MAAM,EAAEL;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,sBAAsBA,CAACC,EAAE,EAAE;EACzC,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,+BAAAM,MAAA,CAA+BD,EAAE,CAAE;IACtCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ"}]}