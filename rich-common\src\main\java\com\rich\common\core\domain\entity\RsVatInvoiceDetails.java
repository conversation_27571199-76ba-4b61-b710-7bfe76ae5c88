package com.rich.common.core.domain.entity;

import java.math.BigDecimal;

import com.rich.common.annotation.Excel;
import com.rich.common.core.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * 发票明细信息对象 rs_vat_invoice_details
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public class RsVatInvoiceDetails extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 发票明细ID，主键
     */
    private Long invoiceDetailsId;

    /**
     * 所属发票ID
     */
    @Excel(name = "所属发票ID")
    private Long invoiceId;

    /**
     * 所属账单明细ID，对应账单费用记录
     */
    @Excel(name = "所属账单明细ID，对应账单费用记录")
    private Long chargeId;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String officalChargeName;

    /**
     * 税收编码
     */
    @Excel(name = "税收编码")
    private String dutyCode;

    /**
     * 规格型号
     */
    @Excel(name = "规格型号")
    private String type;

    /**
     * 单位
     */
    @Excel(name = "单位")
    private String unit;

    /**
     * 数量
     */
    @Excel(name = "数量")
    private BigDecimal quantity;

    /**
     * 单价
     */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /**
     * 金额（不含税）
     */
    @Excel(name = "金额", readConverterExp = "不=含税")
    private BigDecimal subTotal;

    /**
     * 税率（%）
     */
    @Excel(name = "税率", readConverterExp = "%=")
    private BigDecimal vatRate;

    public Long getInvoiceDetailsId() {
        return invoiceDetailsId;
    }

    public void setInvoiceDetailsId(Long invoiceDetailsId) {
        this.invoiceDetailsId = invoiceDetailsId;
    }

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Long getChargeId() {
        return chargeId;
    }

    public void setChargeId(Long chargeId) {
        this.chargeId = chargeId;
    }

    public String getOfficalChargeName() {
        return officalChargeName;
    }

    public void setOfficalChargeName(String officalChargeName) {
        this.officalChargeName = officalChargeName;
    }

    public String getDutyCode() {
        return dutyCode;
    }

    public void setDutyCode(String dutyCode) {
        this.dutyCode = dutyCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getSubTotal() {
        return subTotal;
    }

    public void setSubTotal(BigDecimal subTotal) {
        this.subTotal = subTotal;
    }

    public BigDecimal getVatRate() {
        return vatRate;
    }

    public void setVatRate(BigDecimal vatRate) {
        this.vatRate = vatRate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("invoiceDetailsId", getInvoiceDetailsId())
                .append("invoiceId", getInvoiceId())
                .append("chargeId", getChargeId())
                .append("officalChargeName", getOfficalChargeName())
                .append("dutyCode", getDutyCode())
                .append("type", getType())
                .append("unit", getUnit())
                .append("quantity", getQuantity())
                .append("unitPrice", getUnitPrice())
                .append("subTotal", getSubTotal())
                .append("vatRate", getVatRate())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
