{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregatorBackGround\\index.vue?vue&type=style&index=0&id=572d9a5a&scoped=true&lang=css&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\DataAggregatorBackGround\\index.vue", "mtime": 1754881964211}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZGF0YS1hZ2dyZWdhdG9yIHsNCiAgcGFkZGluZzogMjBweDsNCn0NCg0KLmNvbmZpZy1jYXJkLCAucmVzdWx0LWNhcmQgew0KICBoZWlnaHQ6IDEwMCU7DQogIG92ZXJmbG93OiBhdXRvOw0KfQ0KDQoucmVzdWx0LWNhcmQgew0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KfQ0KDQouaGVhZGVyLXdpdGgtb3BlcmF0aW9ucyB7DQogIGRpc3BsYXk6IGZsZXg7DQogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCn0NCg0KLm9wZXJhdGlvbnMgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBnYXA6IDEwcHg7DQp9DQoNCi5maWx0ZXItdGFncyB7DQogIG1hcmdpbi10b3A6IDEwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGZsZXgtd3JhcDogd3JhcDsNCiAgZ2FwOiA1cHg7DQp9DQoNCi5lbC10YWcgew0KICBtYXJnaW4tcmlnaHQ6IDVweDsNCiAgbWFyZ2luLWJvdHRvbTogNXB4Ow0KfQ0KDQouZmllbGQtY29uZmlnLXRhYmxlIHsNCiAgYm9yZGVyOiAxcHggc29saWQgI0VCRUVGNTsNCiAgbWFyZ2luLWJvdHRvbTogMTBweDsNCn0NCg0KLnRhYmxlLWhlYWRlciwNCi50YWJsZS1yb3cgew0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBwYWRkaW5nOiA4cHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjRUJFRUY1Ow0KfQ0KDQoudGFibGUtaGVhZGVyIHsNCiAgYmFja2dyb3VuZC1jb2xvcjogI0Y1RjdGQTsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQp9DQoNCi5jb2wgew0KICBmbGV4OiAxOw0KICBwYWRkaW5nOiAwIDVweDsNCiAgbWluLXdpZHRoOiAxMjBweDsNCn0NCg0KLmNvbDpmaXJzdC1jaGlsZCB7DQogIGZsZXg6IDAgMCA2MHB4Ow0KICBtaW4td2lkdGg6IDYwcHg7DQp9DQoNCi5jb2wtb3BlcmF0aW9uIHsNCiAgZmxleDogMCAwIDEyMHB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQp9DQoNCi5lbC1zZWxlY3Qgew0KICB3aWR0aDogMTAwJTsNCn0NCg0KLmVsLWlucHV0LW51bWJlciB7DQogIHdpZHRoOiAxMDAlOw0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA47CA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/DataAggregatorBackGround", "sourcesContent": ["<template>\r\n  <div class=\"data-aggregator\">\r\n    <el-row :gutter=\"20\">\r\n      <!-- 配置区域 - 左侧 -->\r\n      <el-col :span=\"showResult ? 10 : 10\">\r\n        <el-card class=\"config-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总配置</span>\r\n            </div>\r\n          </template>\r\n          <el-form class=\"edit\" label-width=\"80px\">\r\n            <!-- 速查名称 -->\r\n            <el-form-item label=\"速查名称\" required>\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"18\">\r\n                  <el-input v-model=\"config.name\" placeholder=\"请输入速查名称\"/>\r\n                </el-col>\r\n                <el-col :span=\"5\">\r\n                  <el-button size=\"small\" type=\"text\" @click=\"saveConfig\">[↗]</el-button>\r\n                  <el-button size=\"small\" type=\"text\" @click=\"loadConfigs\">[...]</el-button>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组依据 -->\r\n            <el-form-item label=\"分组依据\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.primaryField\" clearable filterable placeholder=\"操作单号\">\r\n                    <el-option\r\n                      v-for=\"field in availableFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.matchOptions.exact\">精确匹配</el-checkbox>\r\n                  <el-checkbox v-model=\"config.matchOptions.caseSensitive\">区分大小写</el-checkbox>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 分组日期 -->\r\n            <el-form-item label=\"分组日期\">\r\n              <el-row :gutter=\"10\">\r\n                <el-col :span=\"8\">\r\n                  <el-select v-model=\"config.dateField\" clearable filterable placeholder=\"分组日期\">\r\n                    <el-option\r\n                      v-for=\"field in dateFields\"\r\n                      :key=\"field\"\r\n                      :label=\"getFieldLabel(field)\"\r\n                      :value=\"field\"\r\n                    />\r\n                  </el-select>\r\n                </el-col>\r\n                <el-col :span=\"15\">\r\n                  <el-checkbox v-model=\"config.dateOptions.convertToNumber\">转换为数字</el-checkbox>\r\n                  <el-radio-group v-model=\"config.dateOptions.formatType\" style=\"display: flex;line-height: 26px\">\r\n                    <el-radio label=\"year\">按年</el-radio>\r\n                    <el-radio label=\"month\">按月</el-radio>\r\n                    <el-radio label=\"day\">按天</el-radio>\r\n                  </el-radio-group>\r\n                </el-col>\r\n              </el-row>\r\n            </el-form-item>\r\n\r\n            <!-- 数据筛选 -->\r\n            <!--<el-form-item label=\"数据筛选\">-->\r\n            <!--  <el-button type=\"primary\" plain size=\"small\" @click=\"openFilterDialog\">设置筛选条件</el-button>-->\r\n            <!--  <div v-if=\"config.filters && config.filters.length\" class=\"filter-tags\">-->\r\n            <!--    <el-tag-->\r\n            <!--      v-for=\"(filter, index) in config.filters\"-->\r\n            <!--      :key=\"index\"-->\r\n            <!--      closable-->\r\n            <!--      @close=\"removeFilter(index)\"-->\r\n            <!--    >-->\r\n            <!--      {{getFieldLabel(filter.field)}} {{getOperatorLabel(filter.operator)}} {{filter.value}}-->\r\n            <!--    </el-tag>-->\r\n            <!--  </div>-->\r\n            <!--</el-form-item>-->\r\n\r\n            <!-- 显示方式 -->\r\n            <el-form-item label=\"显示方式\">\r\n              <el-checkbox v-model=\"config.showDetails\" style=\"padding-left: 5px;\">含明细</el-checkbox>\r\n            </el-form-item>\r\n\r\n            <!-- 动态字段配置 -->\r\n            <el-table\r\n              :data=\"config.fields\"\r\n              border\r\n              style=\"width: 100%\"\r\n            >\r\n              <el-table-column\r\n                align=\"center\"\r\n                label=\"序号\"\r\n                type=\"index\"\r\n                width=\"60\"\r\n              />\r\n\r\n              <el-table-column label=\"表头名称\" min-width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.fieldKey\"\r\n                    filterable\r\n                    placeholder=\"选择字段\"\r\n                    style=\"width: 100%\"\r\n                    @change=\"handleFieldSelect(scope.$index)\"\r\n                  >\r\n                    <el-option\r\n                      v-for=\"(config, key) in fieldLabelMap\"\r\n                      :key=\"key\"\r\n                      :label=\"config.name\"\r\n                      :value=\"key\"\r\n                    />\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"排序\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.sort\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"∧\" value=\"asc\"/>\r\n                    <el-option label=\"∨ \" value=\"desc\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"汇总方式\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.aggregation\"\r\n                    :disabled=\"!isAggregatable(scope.row.fieldKey)\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <el-option label=\"计数\" value=\"count\"/>\r\n                    <el-option label=\"求和\" value=\"sum\"/>\r\n                    <el-option label=\"平均值\" value=\"avg\"/>\r\n                    <el-option label=\"最大值\" value=\"max\"/>\r\n                    <el-option label=\"最小值\" value=\"min\"/>\r\n                    <el-option label=\"方差\" value=\"variance\"/>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"显示格式\" width=\"100\">\r\n                <template #default=\"scope\">\r\n                  <el-select\r\n                    v-model=\"scope.row.format\"\r\n                    style=\"width: 100%\"\r\n                  >\r\n                    <el-option label=\"-\" value=\"none\"/>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'date'\">\r\n                      <el-option label=\"YYYYMM\" value=\"YYYYMM\"/>\r\n                      <el-option label=\"MM-DD\" value=\"MM-DD\"/>\r\n                      <el-option label=\"YYYY-MM-DD\" value=\"YYYY-MM-DD\"/>\r\n                    </template>\r\n                    <template v-if=\"getFieldDisplay(scope.row.fieldKey) === 'number'\">\r\n                      <el-option label=\"0.00\" value=\"decimal\"/>\r\n                      <el-option label=\"0.00%\" value=\"percent\"/>\r\n                      <el-option label=\"¥0.00\" value=\"currency\"/>\r\n                      <el-option label=\"$0.00\" value=\"usd\"/>\r\n                      <el-option label=\"0不显示\" value=\"hideZero\"/>\r\n                    </template>\r\n                  </el-select>\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column label=\"零值隐藏\" width=\"80\">\r\n                <template #default=\"scope\">\r\n                  <el-checkbox\r\n                    v-model=\"scope.row.hideZeroValues\"\r\n                    :disabled=\"getFieldDisplay(scope.row.fieldKey) !== 'number'\"\r\n                  />\r\n                </template>\r\n              </el-table-column>\r\n\r\n              <el-table-column align=\"center\" label=\"操作\" width=\"120\">\r\n                <template #default=\"scope\">\r\n                  <el-button-group>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === 0\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'up')\"\r\n                    >[∧]\r\n                    </el-button>\r\n                    <el-button\r\n                      :disabled=\"scope.$index === config.fields.length - 1\"\r\n                      size=\"mini\"\r\n                      type=\"text\"\r\n                      @click=\"moveField(scope.$index, 'down')\"\r\n                    >[∨]\r\n                    </el-button>\r\n                    <el-button\r\n                      icon=\"el-icon-delete\"\r\n                      size=\"mini\"\r\n                      style=\"color: red\"\r\n                      type=\"text\"\r\n                      @click=\"removeField(scope.$index)\"\r\n                    >\r\n                    </el-button>\r\n                  </el-button-group>\r\n                </template>\r\n              </el-table-column>\r\n            </el-table>\r\n\r\n            <div style=\"margin-top: 10px;\">\r\n              <el-button plain type=\"text\" @click=\"addField\">[ + ]</el-button>\r\n            </div>\r\n\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"handleServerAggregate\">分类汇总</el-button>\r\n              <el-button @click=\"resetConfig\">重置</el-button>\r\n            </el-form-item>\r\n          </el-form>\r\n        </el-card>\r\n      </el-col>\r\n\r\n      <!-- 结果展示 - 右侧 -->\r\n      <el-col v-if=\"showResult\" :span=\"14\">\r\n        <el-card class=\"result-card\">\r\n          <template #header>\r\n            <div class=\"header-with-operations\">\r\n              <span>汇总结果</span>\r\n              <div class=\"operations\">\r\n                <el-switch\r\n                  v-model=\"isLandscape\"\r\n                  active-text=\"横向\"\r\n                  inactive-text=\"纵向\"\r\n                  style=\"margin-right: 15px\"\r\n                />\r\n                <el-button size=\"small\" @click=\"printTable\">打印</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"exportToPDF\">导出PDF</el-button>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <el-table\r\n            ref=\"resultTable\"\r\n            v-loading=\"loading\"\r\n            :data=\"processedData\"\r\n            :summary-method=\"getSummary\"\r\n            border\r\n            show-summary\r\n            style=\"width: 100%\"\r\n          >\r\n            <!-- 分组字段列 -->\r\n            <el-table-column\r\n              :align=\"config.primaryField ? fieldLabelMap[config.primaryField].align : 'left'\"\r\n              :label=\"groupFieldName\"\r\n              :width=\"config.primaryField ? fieldLabelMap[config.primaryField].width : ''\"\r\n            >\r\n              <template #default=\"scope\">\r\n                {{ formatGroupKey(scope.row.groupKey) }}\r\n              </template>\r\n            </el-table-column>\r\n\r\n            <!-- 动态字段列 - 按照config.fields的顺序渲染 -->\r\n            <template v-for=\"(field, fieldIndex) in config.fields\">\r\n              <el-table-column\r\n                v-if=\"field.fieldKey\"\r\n                :key=\"field.fieldKey + '_' + fieldIndex\"\r\n                :align=\"getColumnAlign(field.fieldKey)\"\r\n                :label=\"getResultLabel(field)\"\r\n                :width=\"getColumnWidth(field.fieldKey)\"\r\n              >\r\n                <template #default=\"scope\">\r\n                  {{ formatCellValue(scope.row[getResultProp(field)], field) }}\r\n                </template>\r\n              </el-table-column>\r\n            </template>\r\n          </el-table>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 加载配置对话框 -->\r\n    <el-dialog :visible.sync=\"configDialogVisible\" append-to-body title=\"加载配置\" width=\"550px\">\r\n      <el-table\r\n        v-loading=\"configLoading\"\r\n        :data=\"savedConfigs\"\r\n        style=\"width: 100%\"\r\n        @row-click=\"handleConfigSelect\"\r\n      >\r\n        <el-table-column label=\"配置名称\" prop=\"name\"/>\r\n        <el-table-column label=\"创建时间\" prop=\"createTime\"/>\r\n        <el-table-column width=\"50\">\r\n          <template #default=\"scope\">\r\n            <el-button type=\"text\" @click.stop=\"deleteConfig(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n    </el-dialog>\r\n\r\n    <!-- 筛选条件对话框 -->\r\n    <el-dialog :visible.sync=\"filterDialogVisible\" title=\"设置筛选条件\" width=\"650px\">\r\n      <el-form :model=\"currentFilter\" label-width=\"100px\">\r\n        <el-form-item label=\"字段\">\r\n          <el-select v-model=\"currentFilter.field\" filterable placeholder=\"选择字段\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"(config, key) in fieldLabelMap\"\r\n              :key=\"key\"\r\n              :label=\"config.name\"\r\n              :value=\"key\"\r\n            />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"操作符\">\r\n          <el-select v-model=\"currentFilter.operator\" placeholder=\"选择操作符\" style=\"width: 100%\">\r\n            <el-option label=\"等于\" value=\"eq\"/>\r\n            <el-option label=\"不等于\" value=\"ne\"/>\r\n            <el-option label=\"大于\" value=\"gt\"/>\r\n            <el-option label=\"大于等于\" value=\"ge\"/>\r\n            <el-option label=\"小于\" value=\"lt\"/>\r\n            <el-option label=\"小于等于\" value=\"le\"/>\r\n            <el-option label=\"包含\" value=\"contains\"/>\r\n            <el-option label=\"开始于\" value=\"startsWith\"/>\r\n            <el-option label=\"结束于\" value=\"endsWith\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"值\">\r\n          <el-input v-model=\"currentFilter.value\" placeholder=\"输入筛选值\"/>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"addFilter\">添加筛选条件</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <div v-if=\"config.filters && config.filters.length\">\r\n        <h4>已添加的筛选条件</h4>\r\n        <el-table :data=\"config.filters\" border>\r\n          <el-table-column label=\"字段\" prop=\"field\">\r\n            <template #default=\"scope\">\r\n              {{ getFieldLabel(scope.row.field) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作符\" prop=\"operator\">\r\n            <template #default=\"scope\">\r\n              {{ getOperatorLabel(scope.row.operator) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"值\" prop=\"value\"/>\r\n          <el-table-column label=\"操作\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-button\r\n                circle\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                @click=\"removeFilter(scope.$index)\"\r\n              ></el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n\r\n      <div slot=\"footer\">\r\n        <el-button @click=\"filterDialogVisible = false\">关闭</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport moment from \"moment\"\r\nimport currency from \"currency.js\"\r\nimport {saveAggregatorConfig, loadAggregatorConfigs, deleteAggregatorConfig} from \"@/api/system/aggregator\"\r\nimport html2pdf from \"html2pdf.js\"\r\n\r\nexport default {\r\n  name: \"DataAggregatorBackGround\",\r\n  props: {\r\n    // 可用字段配置映射\r\n    fieldLabelMap: {\r\n      type: Object,\r\n      required: true,\r\n      default: () => ({})\r\n    },\r\n    // 指定数据来源类型，用于后端查询\r\n    dataSourceType: {\r\n      type: String,\r\n      required: true,\r\n      default: 'rct' // 默认是操作单数据\r\n    },\r\n    // 从父组件传入的汇总函数\r\n    aggregateFunction: {\r\n      type: Function,\r\n      required: true\r\n    },\r\n    configType: {\r\n      type: String,\r\n      required: false\r\n    }\r\n  },\r\n  data() {\r\n    return {\r\n      configName: \"\",\r\n      config: {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        filters: [] // 存储筛选条件\r\n      },\r\n      dateOptions: [\r\n        {label: \"按年\", value: \"year\"},\r\n        {label: \"按月\", value: \"month\"},\r\n        {label: \"按周\", value: \"week\"},\r\n        {label: \"按日\", value: \"day\"},\r\n        {label: \"按时\", value: \"hour\"},\r\n        {label: \"按分\", value: \"minute\"}\r\n      ],\r\n      aggregationOptions: [\r\n        {label: \"计数\", value: \"count\"},\r\n        {label: \"求和\", value: \"sum\"},\r\n        {label: \"平均值\", value: \"avg\"},\r\n        {label: \"方差\", value: \"variance\"},\r\n        {label: \"最大值\", value: \"max\"},\r\n        {label: \"最小值\", value: \"min\"}\r\n      ],\r\n      operatorOptions: [\r\n        {label: \"等于\", value: \"eq\"},\r\n        {label: \"不等于\", value: \"ne\"},\r\n        {label: \"大于\", value: \"gt\"},\r\n        {label: \"大于等于\", value: \"ge\"},\r\n        {label: \"小于\", value: \"lt\"},\r\n        {label: \"小于等于\", value: \"le\"},\r\n        {label: \"包含\", value: \"contains\"},\r\n        {label: \"开始于\", value: \"startsWith\"},\r\n        {label: \"结束于\", value: \"endsWith\"}\r\n      ],\r\n      loading: false,\r\n      configDialogVisible: false,\r\n      filterDialogVisible: false,\r\n      savedConfigs: [],\r\n      configLoading: false,\r\n      isLandscape: false,\r\n      showResult: false,\r\n      processedData: [],\r\n      currentFilter: {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n    }\r\n  },\r\n  computed: {\r\n    // 可用字段列表\r\n    availableFields() {\r\n      // 返回在 fieldLabelMap 中定义的所有字段\r\n      return Object.keys(this.fieldLabelMap)\r\n    },\r\n\r\n    // 日期类型字段列表\r\n    dateFields() {\r\n      return this.availableFields.filter(field => {\r\n        // 检查 fieldLabelMap 中的 display 属性\r\n        return this.fieldLabelMap[field] && this.fieldLabelMap[field].display === \"date\"\r\n      })\r\n    },\r\n\r\n    // 分组字段名称\r\n    groupFieldName() {\r\n      if (this.config.primaryField && this.config.dateField) {\r\n        return `${this.getFieldLabel(this.config.dateField)}+${this.getFieldLabel(this.config.primaryField)}`\r\n      } else if (this.config.primaryField) {\r\n        return this.getFieldLabel(this.config.primaryField)\r\n      } else if (this.config.dateField) {\r\n        return this.getFieldLabel(this.config.dateField)\r\n      }\r\n      return \"分组\"\r\n    }\r\n  },\r\n  methods: {\r\n    /**\r\n     * 根据字段键获取字段标签\r\n     * @param {String} field - 字段的键\r\n     * @returns {String} 字段的标签\r\n     */\r\n    getFieldLabel(field) {\r\n      return this.fieldLabelMap[field]?.name || field\r\n    },\r\n\r\n    /**\r\n     * 根据操作符代码获取操作符标签\r\n     * @param {String} op - 操作符代码\r\n     * @returns {String} 操作符标签\r\n     */\r\n    getOperatorLabel(op) {\r\n      const operator = this.operatorOptions.find(item => item.value === op)\r\n      return operator ? operator.label : op\r\n    },\r\n\r\n    /**\r\n     * 打开筛选条件对话框\r\n     */\r\n    openFilterDialog() {\r\n      this.currentFilter = {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n      this.filterDialogVisible = true\r\n    },\r\n\r\n    /**\r\n     * 添加筛选条件\r\n     */\r\n    addFilter() {\r\n      if (!this.currentFilter.field) {\r\n        this.$message.warning(\"请选择筛选字段\")\r\n        return\r\n      }\r\n\r\n      if (!this.currentFilter.value && this.currentFilter.value !== 0) {\r\n        this.$message.warning(\"请输入筛选值\")\r\n        return\r\n      }\r\n\r\n      // 初始化filters数组（如果不存在）\r\n      if (!this.config.filters) {\r\n        this.$set(this.config, 'filters', [])\r\n      }\r\n\r\n      // 添加新的筛选条件\r\n      this.config.filters.push({...this.currentFilter})\r\n\r\n      // 重置当前筛选条件\r\n      this.currentFilter = {\r\n        field: \"\",\r\n        operator: \"eq\",\r\n        value: \"\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 移除筛选条件\r\n     * @param {Number} index - 要移除的筛选条件索引\r\n     */\r\n    removeFilter(index) {\r\n      this.config.filters.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 获取字段的显示类型\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {string} 字段显示类型（text/number/date/boolean/custom）\r\n     */\r\n    getFieldDisplay(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      if (!fieldConfig) return \"text\"\r\n\r\n      // 检查是否是自定义方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        return \"custom\"\r\n      }\r\n\r\n      return fieldConfig.display || \"text\"\r\n    },\r\n\r\n    /**\r\n     * 判断字段是否可以进行汇总计算\r\n     * @param {string} fieldKey - 字段标识\r\n     * @returns {boolean} 是否可汇总\r\n     */\r\n    isAggregatable(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig?.aggregated || false\r\n    },\r\n\r\n    /**\r\n     * 添加新的字段配置行\r\n     */\r\n    addField() {\r\n      this.config.fields.push({\r\n        fieldKey: \"\",\r\n        aggregation: \"none\",\r\n        format: \"none\",\r\n        sort: \"none\",\r\n        hideZeroValues: false\r\n      })\r\n    },\r\n\r\n    /**\r\n     * 删除指定索引的字段配置行\r\n     * @param {number} index - 要删除的字段索引\r\n     */\r\n    removeField(index) {\r\n      this.config.fields.splice(index, 1)\r\n    },\r\n\r\n    /**\r\n     * 移动字段配置行的位置\r\n     * @param {number} index - 当前字段的索引\r\n     * @param {string} direction - 移动方向，'up' 或 'down'\r\n     */\r\n    moveField(index, direction) {\r\n      const fields = [...this.config.fields] // 创建数组副本\r\n\r\n      if (direction === \"up\" && index > 0) {\r\n        // 向上移动，与上一个元素交换位置\r\n        [fields[index], fields[index - 1]] = [fields[index - 1], fields[index]]\r\n      } else if (direction === \"down\" && index < fields.length - 1) {\r\n        // 向下移动，与下一个元素交换位置\r\n        [fields[index], fields[index + 1]] = [fields[index + 1], fields[index]]\r\n      }\r\n\r\n      // 使用整个新数组替换，确保响应式更新\r\n      this.$set(this.config, \"fields\", fields)\r\n    },\r\n\r\n    /**\r\n     * 处理字段选择变更事件\r\n     * @param {number} index - 变更的字段索引\r\n     */\r\n    handleFieldSelect(index) {\r\n      const field = this.config.fields[index]\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (fieldConfig) {\r\n        // 根据字段配置设置默认值\r\n        field.format = this.getDefaultFormat(fieldConfig.display)\r\n        field.aggregation = fieldConfig.aggregated ? \"sum\" : \"none\"\r\n        field.sort = \"none\" // 默认不排序\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 根据显示类型获取默认的格式化方式\r\n     * @param {string} displayType - 显示类型\r\n     * @returns {string} 默认格式\r\n     */\r\n    getDefaultFormat(displayType) {\r\n      switch (displayType) {\r\n        case \"date\":\r\n          return \"YYYY-MM-DD\"\r\n        case \"number\":\r\n          return \"decimal\"\r\n        default:\r\n          return \"none\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 重置配置到初始状态\r\n     */\r\n    resetConfig() {\r\n      this.config = {\r\n        name: \"\",\r\n        primaryField: \"\",\r\n        matchOptions: {\r\n          exact: true,\r\n          caseSensitive: false\r\n        },\r\n        dateField: \"\",\r\n        dateOptions: {\r\n          convertToNumber: false,\r\n          formatType: \"day\"\r\n        },\r\n        showDetails: false,\r\n        fields: [],\r\n        filters: []\r\n      }\r\n      this.showResult = false\r\n    },\r\n\r\n    /**\r\n     * 获取日期格式化模式\r\n     * @returns {String} 日期格式\r\n     */\r\n    getDateFormat() {\r\n      switch (this.config.dateOptions.formatType) {\r\n        case \"year\":\r\n          return \"YYYY\"\r\n        case \"month\":\r\n          return \"YYYY-MM\"\r\n        case \"day\":\r\n        default:\r\n          return \"YYYY-MM-DD\"\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 调用后端服务进行数据汇总\r\n     */\r\n    async handleServerAggregate() {\r\n      // 验证分组依据和分组日期至少填写一个\r\n      if (!this.config.primaryField && !this.config.dateField) {\r\n        this.$message.warning(\"请至少选择分组依据或分组日期其中之一\");\r\n        return;\r\n      }\r\n\r\n      // 验证是否有字段配置\r\n      if (!this.config.fields.length) {\r\n        this.$message.warning(\"请添加至少一个字段\");\r\n        return;\r\n      }\r\n\r\n      // 验证字段配置是否完整\r\n      const incompleteField = this.config.fields.find(field => !field.fieldKey);\r\n      if (incompleteField) {\r\n        this.$message.warning(\"请完成所有字段的配置\");\r\n        return;\r\n      }\r\n\r\n      // 确保汇总函数已经传入\r\n      if (!this.aggregateFunction) {\r\n        this.$message.error(\"汇总函数未定义\")\r\n        return\r\n      }\r\n\r\n      try {\r\n        this.loading = true\r\n\r\n        // 准备请求参数\r\n        const params = {\r\n          dataSourceType: this.dataSourceType,\r\n          config: {\r\n            primaryField: this.config.primaryField,\r\n            matchOptions: this.config.matchOptions,\r\n            dateField: this.config.dateField,\r\n            dateOptions: this.config.dateOptions,\r\n            showDetails: this.config.showDetails,\r\n            fields: this.config.fields,\r\n            filters: this.config.filters || []\r\n          }\r\n        }\r\n\r\n        // 调用从父组件传入的汇总函数\r\n        const response = await this.aggregateFunction(params)\r\n\r\n        if (response.code === 200) {\r\n          // 过滤零值记录\r\n          this.processedData = this.filterZeroValueRecords(response.data)\r\n          this.showResult = true\r\n        } else {\r\n          this.$message.error(response.msg || \"汇总数据失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"数据汇总失败:\", error)\r\n        this.$message.error(\"汇总处理失败：\" + (error.message || \"未知错误\"))\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 过滤零值记录\r\n     * @param {Array} data - 原始数据\r\n     * @returns {Array} 过滤后的数据\r\n     */\r\n    filterZeroValueRecords(data) {\r\n      // 找出设置了hideZeroValues为true的字段\r\n      const zeroFilterFields = this.config.fields\r\n        .filter(field => field.hideZeroValues === true)\r\n        .map(field => ({\r\n          key: field.fieldKey,\r\n          aggProp: this.getResultProp(field)\r\n        }));\r\n\r\n      // 如果没有需要过滤的字段，直接返回原始数据\r\n      if (zeroFilterFields.length === 0) {\r\n        return data;\r\n      }\r\n\r\n      // 过滤数据\r\n      return data.filter(record => {\r\n        // 检查每个需要过滤零值的字段\r\n        for (const field of zeroFilterFields) {\r\n          const value = record[field.aggProp];\r\n          // 如果字段值为0，过滤掉这条记录\r\n          if (value === 0 || value === \"0\" || value === \"0.00\") {\r\n            return false;\r\n          }\r\n        }\r\n        // 所有字段都不为零，保留这条记录\r\n        return true;\r\n      });\r\n    },\r\n\r\n    /**\r\n     * 获取结果数据的属性名\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 属性名\r\n     */\r\n    getResultProp(field) {\r\n      // 如果有汇总方式，属性名为 fieldKey_aggregation\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        return `${field.fieldKey}_${field.aggregation}`\r\n      }\r\n      return field.fieldKey\r\n    },\r\n\r\n    /**\r\n     * 获取结果表格的列标题\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 列标题\r\n     */\r\n    getResultLabel(field) {\r\n      const baseLabel = this.getFieldLabel(field.fieldKey)\r\n\r\n      // 如果有汇总方式，在标签中添加汇总方式信息\r\n      if (field.aggregation && field.aggregation !== \"none\") {\r\n        // 获取汇总方式的中文名称\r\n        const aggregationLabel = this.aggregationOptions.find(opt => opt.value === field.aggregation)?.label || field.aggregation\r\n        return `${baseLabel}(${aggregationLabel})`\r\n      }\r\n\r\n      return baseLabel\r\n    },\r\n\r\n    /**\r\n     * 格式化单元格的值\r\n     * @param {*} value - 原始值\r\n     * @param {Object} field - 字段配置\r\n     * @returns {string} 格式化后的值\r\n     */\r\n    formatCellValue(value, field) {\r\n      if (value == null) return \"-\"\r\n\r\n      const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n      if (!fieldConfig) return value\r\n\r\n      // 处理自定义 display 方法\r\n      if (fieldConfig.display && typeof this[fieldConfig.display] === \"function\") {\r\n        // 调用组件中定义的方法\r\n        return this[fieldConfig.display](value)\r\n      }\r\n\r\n      // 根据字段类型进行格式化\r\n      switch (fieldConfig.display) {\r\n        case \"number\":\r\n          const numValue = Number(value)\r\n          if (isNaN(numValue)) return \"-\"\r\n\r\n          switch (field.format) {\r\n            case \"decimal\":\r\n              return numValue.toFixed(2)\r\n            case \"percent\":\r\n              return (numValue * 100).toFixed(2) + \"%\"\r\n            case \"currency\":\r\n              return \"¥\" + numValue.toFixed(2)\r\n            case \"usd\":\r\n              return \"$\" + numValue.toFixed(2)\r\n            case \"hideZero\":\r\n              return numValue === 0 ? \"-\" : numValue.toFixed(2)\r\n            default:\r\n              return numValue.toFixed(2)\r\n          }\r\n\r\n        case \"date\":\r\n          return moment(value).format(field.format || \"YYYY-MM-DD\")\r\n\r\n        case \"boolean\":\r\n          if (field.aggregation === \"avg\") {\r\n            return (Number(value) * 100).toFixed(2) + \"%\"\r\n          }\r\n          return value ? \"是\" : \"否\"\r\n\r\n        default:\r\n          return value\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 格式化分组键\r\n     * @param {Object|string} groupKey - 分组键\r\n     * @returns {string} 格式化后的分组键\r\n     */\r\n    formatGroupKey(groupKey) {\r\n      if (typeof groupKey === \"object\" && groupKey !== null) {\r\n        if (groupKey.primary !== undefined && groupKey.date !== undefined) {\r\n          // 获取主分组字段的配置\r\n          const primaryFieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n          let primaryValue = groupKey.primary\r\n\r\n          // 如果主分组字段有自定义 display 方法，应用它\r\n          if (primaryFieldConfig && primaryFieldConfig.display &&\r\n            typeof this[primaryFieldConfig.display] === \"function\") {\r\n            primaryValue = this[primaryFieldConfig.display](primaryValue)\r\n          }\r\n\r\n          // 日期值在前，主值在后\r\n          return `${groupKey.date} ${primaryValue}`\r\n        }\r\n      }\r\n\r\n      // 如果是简单值，检查是否需要应用自定义 display 方法\r\n      if (this.config.primaryField) {\r\n        const fieldConfig = this.fieldLabelMap[this.config.primaryField]\r\n        if (fieldConfig && fieldConfig.display &&\r\n          typeof this[fieldConfig.display] === \"function\") {\r\n          return this[fieldConfig.display](groupKey)\r\n        }\r\n      }\r\n\r\n      return String(groupKey || \"\")\r\n    },\r\n\r\n    /**\r\n     * 计算表格合计行\r\n     * @param {Object} param0 - 包含列信息和数据的对象\r\n     * @returns {Array} 合计行数据\r\n     */\r\n    getSummary({columns, data}) {\r\n      const sums = []\r\n\r\n      columns.forEach((column, index) => {\r\n        // 第一列显示\"合计\"文本\r\n        if (index === 0) {\r\n          sums[index] = \"合计\"\r\n          return\r\n        }\r\n\r\n        // 使用索引获取当前列对应的字段配置\r\n        const fieldIndex = index - 1\r\n        const field = this.config.fields[fieldIndex]\r\n\r\n        if (!field || !field.fieldKey) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 检查字段是否配置了汇总方式\r\n        if (!field.aggregation || field.aggregation === \"none\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取字段配置\r\n        const fieldConfig = this.fieldLabelMap[field.fieldKey]\r\n\r\n        // 对于不是数字类型的字段但有percentage显示方法的特殊处理\r\n        if (!fieldConfig) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        if (fieldConfig.display !== \"number\" &&\r\n          fieldConfig.display !== \"percentage\" &&\r\n          typeof this[fieldConfig.display] !== \"function\") {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 获取列数据并转换为数字\r\n        const values = data.map(item => {\r\n          const prop = this.getResultProp(field)\r\n          const val = Number(item[prop])\r\n          return isNaN(val) ? 0 : val // 处理非数字值\r\n        }).filter(val => !isNaN(val))\r\n\r\n        if (values.length === 0) {\r\n          sums[index] = \"\"\r\n          return\r\n        }\r\n\r\n        // 根据汇总方式计算结果\r\n        let sum = 0\r\n        switch (field.aggregation) {\r\n          case \"sum\":\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n            break\r\n          case \"avg\":\r\n            sum = values.reduce((a, b) => a + b, 0) / values.length\r\n            break\r\n          case \"max\":\r\n            sum = Math.max(...values)\r\n            break\r\n          case \"min\":\r\n            sum = Math.min(...values)\r\n            break\r\n          case \"variance\":\r\n            const mean = values.reduce((a, b) => a + b, 0) / values.length\r\n            sum = values.reduce((a, b) => a + Math.pow(b - mean, 2), 0) / values.length\r\n            break\r\n          default:\r\n            sum = values.reduce((a, b) => a + b, 0)\r\n        }\r\n\r\n        // 根据字段显示类型和格式设置来格式化结果\r\n        if (fieldConfig.display === \"percentage\" || fieldConfig.display === \"percent\") {\r\n          // 使用percentage方法格式化\r\n          sums[index] = this.percentage(sum)\r\n        } else if (field.format === \"decimal\") {\r\n          sums[index] = sum.toFixed(2)\r\n        } else if (field.format === \"percent\") {\r\n          sums[index] = (sum * 100).toFixed(2) + \"%\"\r\n        } else if (field.format === \"currency\") {\r\n          sums[index] = \"¥\" + sum.toFixed(2)\r\n        } else if (field.format === \"usd\") {\r\n          sums[index] = \"$\" + sum.toFixed(2)\r\n        } else {\r\n          sums[index] = sum.toFixed(2)\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n\r\n    /**\r\n     * 保存当前配置\r\n     */\r\n    async saveConfig() {\r\n      try {\r\n        // 验证配置名称\r\n        if (!this.config.name) {\r\n          this.$message.warning(\"请输入速查名称\")\r\n          return\r\n        }\r\n\r\n        // 验证分组依据和分组日期至少填写一个\r\n        if (!this.config.primaryField && !this.config.dateField) {\r\n          this.$message.warning(\"请至少选择分组依据或分组日期其中之一\")\r\n          return\r\n        }\r\n\r\n        if (!this.config.fields.length) {\r\n          this.$message.warning(\"请添加至少一个字段\")\r\n          return\r\n        }\r\n\r\n        // 验证字段配置是否完整\r\n        const incompleteField = this.config.fields.find(field => !field.fieldKey)\r\n        if (incompleteField) {\r\n          this.$message.warning(\"请完成所有字段的配置\")\r\n          return\r\n        }\r\n\r\n        // 构造符合 AggregatorConfigDTO 的数据结构\r\n        const configToSave = {\r\n          name: this.config.name,\r\n          type: this.configType,\r\n          config: this.config\r\n        }\r\n\r\n        // 发送请求\r\n        await saveAggregatorConfig(configToSave)\r\n\r\n        this.$message.success(\"配置保存成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"保存配置失败：\" + (err.message || \"未知错误\"))\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 加载保存的配置列表\r\n     */\r\n    async loadConfigs() {\r\n      this.configLoading = true\r\n      this.configDialogVisible = true\r\n      try {\r\n        const result = await loadAggregatorConfigs({configType: this.configType})\r\n        this.savedConfigs = result.rows\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\r\n          err.response?.data?.message ||\r\n          err.message ||\r\n          \"加载配置列表失败，请稍后重试\"\r\n        )\r\n      } finally {\r\n        this.configLoading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 选择并加载配置\r\n     * @param {Object} row - 选中的配置行\r\n     */\r\n    async handleConfigSelect(row) {\r\n      try {\r\n        // 解析配置JSON\r\n        var config = JSON.parse(row.config)\r\n        config.name = row.name\r\n        this.config = config\r\n        // this.config.name = row.name\r\n\r\n        this.configDialogVisible = false\r\n        this.$message.success(\"配置加载成功\")\r\n      } catch (err) {\r\n        console.error(\"加载配置失败:\", err)\r\n        this.$message.error(\"加载配置失败：\" + err.message)\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 删除配置\r\n     * @param {Object} row - 要删除的配置行\r\n     */\r\n    async deleteConfig(row) {\r\n      try {\r\n        await this.$confirm(\"确认删除该配置？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n\r\n        await deleteAggregatorConfig(row.id)\r\n        this.savedConfigs = this.savedConfigs.filter(config => config.id !== row.id)\r\n        this.$message.success(\"配置删除成功\")\r\n      } catch (err) {\r\n        if (err !== \"cancel\") {\r\n          this.$message.error(\"删除配置失败：\" + err.message)\r\n        }\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 打印表格\r\n     */\r\n    printTable() {\r\n      // 实现与原组件相同的打印功能\r\n      const printWindow = window.open(\"\", \"_blank\")\r\n      const table = this.$refs.resultTable.$el.cloneNode(true)\r\n      const title = \"汇总数据\"\r\n      const date = new Date().toLocaleDateString()\r\n\r\n      // 公司标志和标题的HTML模板\r\n      const headerTemplate = `\r\n        <div class=\"company-header\">\r\n          <div class=\"company-logo\">\r\n            <img src=\"/logo.png\" alt=\"Rich Shipping Logo\" />\r\n            <div class=\"company-name\">\r\n              <div class=\"company-name-cn\">广州瑞旗国际货运代理有限公司</div>\r\n              <div class=\"company-name-en\">GUANGZHOU RICH SHIPPING INT'L CO.,LTD.</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"document-title\">\r\n            <div class=\"title-cn\"></div>\r\n            <div class=\"title-en\"></div>\r\n          </div>\r\n        </div>\r\n      `\r\n      printWindow.document.write(`\r\n        <html lang=\"\">\r\n          <head>\r\n            <title>${title}</title>\r\n            <style>\r\n              /* 基础样式 */\r\n              body {\r\n                margin: 0;\r\n                padding: 0;\r\n                font-family: Arial, sans-serif;\r\n              }\r\n\r\n              /* 打印样式 - 必须放在这里才能生效 */\r\n              @media print {\r\n                @page {\r\n                  size: ${this.isLandscape ? \"landscape\" : \"portrait\"};\r\n                  margin: 1.5cm 1cm 1cm 1cm;\r\n                }\r\n\r\n                /* 重要：使用重复表头技术 */\r\n                thead {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 页眉作为表格的一部分，放在thead中 */\r\n                .page-header {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 内容部分 */\r\n                .page-content {\r\n                  display: table-row-group;\r\n                }\r\n\r\n                /* 避免元素内部分页 */\r\n                .company-header, .header-content {\r\n                  page-break-inside: avoid;\r\n                }\r\n\r\n                /* 表格样式 */\r\n                table.main-table {\r\n                  width: 100%;\r\n                  border-collapse: collapse;\r\n                  border: none;\r\n                }\r\n\r\n                /* 确保表头在每页都显示 */\r\n                table.data-table thead {\r\n                  display: table-header-group;\r\n                }\r\n\r\n                /* 避免行内分页 */\r\n                table.data-table tr {\r\n                  page-break-inside: avoid;\r\n                }\r\n              }\r\n\r\n              /* 表格样式 */\r\n              table.data-table {\r\n                border-collapse: collapse;\r\n                width: 100%;\r\n                margin-top: 20px;\r\n                table-layout: fixed;\r\n              }\r\n\r\n              table.data-table th, table.data-table td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n                word-wrap: break-word;\r\n                word-break: break-all;\r\n                white-space: normal;\r\n              }\r\n\r\n              table.data-table th {\r\n                background-color: #f2f2f2;\r\n              }\r\n\r\n              /* Element UI 表格样式模拟 */\r\n              .el-table {\r\n                border-collapse: collapse;\r\n                width: 100%;\r\n                table-layout: fixed;\r\n              }\r\n\r\n              .el-table th, .el-table td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n                text-align: left;\r\n                font-size: 12px;\r\n                word-wrap: break-word;\r\n                word-break: break-all;\r\n                white-space: normal;\r\n              }\r\n\r\n              .el-table th {\r\n                background-color: #f2f2f2;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .el-table__footer {\r\n                background-color: #f8f8f9;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .el-table__footer td {\r\n                border: 1px solid #ddd;\r\n                padding: 8px;\r\n              }\r\n\r\n              /* 公司标题和标志样式 */\r\n              .company-header {\r\n                display: flex;\r\n                justify-content: space-between;\r\n                align-items: center;\r\n                border-bottom: 2px solid #000;\r\n                padding-bottom: 10px;\r\n                width: 100%;\r\n              }\r\n\r\n              .company-logo {\r\n                display: flex;\r\n                align-items: center;\r\n              }\r\n\r\n              .company-logo img {\r\n                height: 50px;\r\n                margin-right: 10px;\r\n              }\r\n\r\n              .company-name {\r\n                display: flex;\r\n                flex-direction: column;\r\n              }\r\n\r\n              .company-name-cn {\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n                color: #ff0000;\r\n              }\r\n\r\n              .company-name-en {\r\n                font-size: 14px;\r\n              }\r\n\r\n              .document-title {\r\n                text-align: right;\r\n              }\r\n\r\n              .title-cn {\r\n                font-size: 18px;\r\n                font-weight: bold;\r\n              }\r\n\r\n              .title-en {\r\n                font-size: 16px;\r\n                font-weight: bold;\r\n              }\r\n\r\n              /* 清除表格边框 */\r\n              table.main-table, table.main-table td {\r\n                border: none;\r\n              }\r\n\r\n              /* 页眉容器 */\r\n              .header-container {\r\n                width: 100%;\r\n                margin-bottom: 20px;\r\n              }\r\n            </style>\r\n          </head>\r\n          <body>\r\n            <!-- 使用表格布局确保页眉在每页重复 -->\r\n            <table class=\"main-table\">\r\n              <thead class=\"page-header\">\r\n                <tr>\r\n                  <td>\r\n                    <div class=\"header-container\">\r\n                      ${headerTemplate}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </thead>\r\n              <tbody class=\"page-content\">\r\n                <tr>\r\n                  <td>\r\n                    <!-- 保留原始表格的类名并添加data-table类 -->\r\n                    ${table.outerHTML.replace('<table', '<table class=\"el-table data-table\"')}\r\n                  </td>\r\n                </tr>\r\n              </tbody>\r\n            </table>\r\n          </body>\r\n        </html>\r\n      `)\r\n\r\n      printWindow.document.close()\r\n\r\n      setTimeout(() => {\r\n        try {\r\n          printWindow.focus();\r\n          printWindow.print();\r\n        } catch (e) {\r\n          console.error(\"打印过程中发生错误:\", e);\r\n        }\r\n      }, 1000)\r\n    },\r\n\r\n    /**\r\n     * 导出PDF\r\n     */\r\n    async exportToPDF() {\r\n      try {\r\n        this.loading = true\r\n        const element = this.$refs.resultTable.$el\r\n        const opt = {\r\n          margin: [0.8, 0.8, 0.8, 0.8],\r\n          filename: \"汇总数据.pdf\",\r\n          image: {type: \"jpeg\", quality: 0.98},\r\n          html2canvas: {scale: 2},\r\n          jsPDF: {\r\n            unit: \"in\",\r\n            format: \"a3\",\r\n            orientation: this.isLandscape ? \"landscape\" : \"portrait\"\r\n          },\r\n          pagebreak: {mode: [\"avoid-all\", \"css\", \"legacy\"]},\r\n          header: [\r\n            {text: \"汇总数据\", style: \"headerStyle\"},\r\n            {text: new Date().toLocaleDateString(), style: \"headerStyle\", alignment: \"right\"}\r\n          ],\r\n          footer: {\r\n            height: \"20px\",\r\n            contents: {\r\n              default: \"<span style=\\\"float:right\\\">{{page}}/{{pages}}</span>\"\r\n            }\r\n          }\r\n        }\r\n\r\n        await html2pdf().set(opt).from(element).save()\r\n        this.$message.success(\"PDF导出成功\")\r\n      } catch (error) {\r\n        this.$message.error(\"PDF导出失败：\" + error.message)\r\n      } finally {\r\n        this.loading = false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 获取人员姓名\r\n     * @param {Number} id - 人员ID\r\n     * @returns {String} 人员姓名\r\n     */\r\n    getName(id) {\r\n      if (id !== null) {\r\n        let staff = this.$store.state.data.allRsStaffList.filter(rsStaff => rsStaff.staffId == id)[0]\r\n        if (staff && staff !== undefined) {\r\n          return staff.staffShortName + staff.staffFamilyEnName\r\n        }\r\n      }\r\n      return \"\"\r\n    },\r\n\r\n    /**\r\n     * 格式化百分比值\r\n     * @param {*} value - 要格式化的值\r\n     * @returns {string} 格式化后的百分比\r\n     */\r\n    percentage(value) {\r\n      if (value == null || value === '') {\r\n        return '-';\r\n      }\r\n\r\n      // 处理已经带有%的情况\r\n      if (typeof value === 'string' && value.includes('%')) {\r\n        return value;\r\n      }\r\n\r\n      // 将数值转换为百分比格式\r\n      const numValue = Number(value);\r\n      if (isNaN(numValue)) {\r\n        return '-';\r\n      }\r\n\r\n      // 如果值已经是百分比形式(例如0.25表示25%)，则直接乘以100\r\n      // 如果值已经是整数形式(例如25表示25%)，则不需要乘以100\r\n      const isDecimal = numValue > 0 && numValue <= 1;\r\n      const percentValue = isDecimal ? numValue * 100 : numValue;\r\n\r\n      // 格式化为2位小数的百分比\r\n      return percentValue.toFixed(2) + '%';\r\n    },\r\n\r\n    /**\r\n     * 获取列对齐方式\r\n     * @param {string} fieldKey - 字段键\r\n     * @returns {string} 对齐方式\r\n     */\r\n    getColumnAlign(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig && fieldConfig.align ? fieldConfig.align : 'left'\r\n    },\r\n\r\n    /**\r\n     * 获取列宽度\r\n     * @param {string} fieldKey - 字段键\r\n     * @returns {string|number} 列宽度\r\n     */\r\n    getColumnWidth(fieldKey) {\r\n      const fieldConfig = this.fieldLabelMap[fieldKey]\r\n      return fieldConfig && fieldConfig.width ? fieldConfig.width : ''\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.data-aggregator {\r\n  padding: 20px;\r\n}\r\n\r\n.config-card, .result-card {\r\n  height: 100%;\r\n  overflow: auto;\r\n}\r\n\r\n.result-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.header-with-operations {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.operations {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.filter-tags {\r\n  margin-top: 10px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 5px;\r\n}\r\n\r\n.el-tag {\r\n  margin-right: 5px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.field-config-table {\r\n  border: 1px solid #EBEEF5;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.table-header,\r\n.table-row {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px;\r\n  border-bottom: 1px solid #EBEEF5;\r\n}\r\n\r\n.table-header {\r\n  background-color: #F5F7FA;\r\n  font-weight: bold;\r\n}\r\n\r\n.col {\r\n  flex: 1;\r\n  padding: 0 5px;\r\n  min-width: 120px;\r\n}\r\n\r\n.col:first-child {\r\n  flex: 0 0 60px;\r\n  min-width: 60px;\r\n}\r\n\r\n.col-operation {\r\n  flex: 0 0 120px;\r\n  text-align: center;\r\n}\r\n\r\n.el-select {\r\n  width: 100%;\r\n}\r\n\r\n.el-input-number {\r\n  width: 100%;\r\n}\r\n</style>\r\n\r\n"]}]}