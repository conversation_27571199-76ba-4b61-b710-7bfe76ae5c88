{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\generator\\render.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\utils\\generator\\render.js", "mtime": 1754876882559}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy93ZWIuZG9tLWNvbGxlY3Rpb25zLmZvci1lYWNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5wdXNoLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3Qua2V5cy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuanNvbi5zdHJpbmdpZnkuanMiKTsKdmFyIF9pbmRleCA9IHJlcXVpcmUoIkAvdXRpbHMvaW5kZXgiKTsKLy8g5Y+C6ICDaHR0cHM6Ly9naXRodWIuY29tL3Z1ZWpzL3Z1ZS9ibG9iL3YyLjYuMTAvc3JjL3BsYXRmb3Jtcy93ZWIvc2VydmVyL3V0aWwuanMKdmFyIGlzQXR0ciA9ICgwLCBfaW5kZXgubWFrZU1hcCkoJ2FjY2VwdCxhY2NlcHQtY2hhcnNldCxhY2Nlc3NrZXksYWN0aW9uLGFsaWduLGFsdCxhc3luYyxhdXRvY29tcGxldGUsJyArICdhdXRvZm9jdXMsYXV0b3BsYXksYXV0b3NhdmUsYmdjb2xvcixib3JkZXIsYnVmZmVyZWQsY2hhbGxlbmdlLGNoYXJzZXQsJyArICdjaGVja2VkLGNpdGUsY2xhc3MsY29kZSxjb2RlYmFzZSxjb2xvcixjb2xzLGNvbHNwYW4sY29udGVudCxodHRwLWVxdWl2LCcgKyAnbmFtZSxjb250ZW50ZWRpdGFibGUsY29udGV4dG1lbnUsY29udHJvbHMsY29vcmRzLGRhdGEsZGF0ZXRpbWUsZGVmYXVsdCwnICsgJ2RlZmVyLGRpcixkaXJuYW1lLGRpc2FibGVkLGRvd25sb2FkLGRyYWdnYWJsZSxkcm9wem9uZSxlbmN0eXBlLG1ldGhvZCxmb3IsJyArICdmb3JtLGZvcm1hY3Rpb24saGVhZGVycyxoZWlnaHQsaGlkZGVuLGhpZ2gsaHJlZixocmVmbGFuZyxodHRwLWVxdWl2LCcgKyAnaWNvbixpZCxpc21hcCxpdGVtcHJvcCxrZXl0eXBlLGtpbmQsbGFiZWwsbGFuZyxsYW5ndWFnZSxsaXN0LGxvb3AsbG93LCcgKyAnbWFuaWZlc3QsbWF4LG1heGxlbmd0aCxtZWRpYSxtZXRob2QsR0VULFBPU1QsbWluLG11bHRpcGxlLGVtYWlsLGZpbGUsJyArICdtdXRlZCxuYW1lLG5vdmFsaWRhdGUsb3BlbixvcHRpbXVtLHBhdHRlcm4scGluZyxwbGFjZWhvbGRlcixwb3N0ZXIsJyArICdwcmVsb2FkLHJhZGlvZ3JvdXAscmVhZG9ubHkscmVsLHJlcXVpcmVkLHJldmVyc2VkLHJvd3Mscm93c3BhbixzYW5kYm94LCcgKyAnc2NvcGUsc2NvcGVkLHNlYW1sZXNzLHNlbGVjdGVkLHNoYXBlLHNpemUsdHlwZSx0ZXh0LHBhc3N3b3JkLHNpemVzLHNwYW4sJyArICdzcGVsbGNoZWNrLHNyYyxzcmNkb2Msc3JjbGFuZyxzcmNzZXQsc3RhcnQsc3RlcCxzdHlsZSxzdW1tYXJ5LHRhYmluZGV4LCcgKyAndGFyZ2V0LHRpdGxlLHR5cGUsdXNlbWFwLHZhbHVlLHdpZHRoLHdyYXAnKTsKZnVuY3Rpb24gdk1vZGVsKHNlbGYsIGRhdGFPYmplY3QsIGRlZmF1bHRWYWx1ZSkgewogIGRhdGFPYmplY3QucHJvcHMudmFsdWUgPSBkZWZhdWx0VmFsdWU7CiAgZGF0YU9iamVjdC5vbi5pbnB1dCA9IGZ1bmN0aW9uICh2YWwpIHsKICAgIHNlbGYuJGVtaXQoJ2lucHV0JywgdmFsKTsKICB9Owp9CnZhciBjb21wb25lbnRDaGlsZCA9IHsKICAnZWwtYnV0dG9uJzogewogICAgZGVmYXVsdDogZnVuY3Rpb24gX2RlZmF1bHQoaCwgY29uZiwga2V5KSB7CiAgICAgIHJldHVybiBjb25mW2tleV07CiAgICB9CiAgfSwKICAnZWwtaW5wdXQnOiB7CiAgICBwcmVwZW5kOiBmdW5jdGlvbiBwcmVwZW5kKGgsIGNvbmYsIGtleSkgewogICAgICByZXR1cm4gaCgidGVtcGxhdGUiLCB7CiAgICAgICAgInNsb3QiOiAicHJlcGVuZCIKICAgICAgfSwgW2NvbmZba2V5XV0pOwogICAgfSwKICAgIGFwcGVuZDogZnVuY3Rpb24gYXBwZW5kKGgsIGNvbmYsIGtleSkgewogICAgICByZXR1cm4gaCgidGVtcGxhdGUiLCB7CiAgICAgICAgInNsb3QiOiAiYXBwZW5kIgogICAgICB9LCBbY29uZltrZXldXSk7CiAgICB9CiAgfSwKICAnZWwtc2VsZWN0JzogewogICAgb3B0aW9uczogZnVuY3Rpb24gb3B0aW9ucyhoLCBjb25mLCBrZXkpIHsKICAgICAgdmFyIGxpc3QgPSBbXTsKICAgICAgY29uZi5vcHRpb25zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICBsaXN0LnB1c2goaCgiZWwtb3B0aW9uIiwgewogICAgICAgICAgImF0dHJzIjogewogICAgICAgICAgICAibGFiZWwiOiBpdGVtLmxhYmVsLAogICAgICAgICAgICAidmFsdWUiOiBpdGVtLnZhbHVlLAogICAgICAgICAgICAiZGlzYWJsZWQiOiBpdGVtLmRpc2FibGVkCiAgICAgICAgICB9CiAgICAgICAgfSkpOwogICAgICB9KTsKICAgICAgcmV0dXJuIGxpc3Q7CiAgICB9CiAgfSwKICAnZWwtcmFkaW8tZ3JvdXAnOiB7CiAgICBvcHRpb25zOiBmdW5jdGlvbiBvcHRpb25zKGgsIGNvbmYsIGtleSkgewogICAgICB2YXIgbGlzdCA9IFtdOwogICAgICBjb25mLm9wdGlvbnMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgIGlmIChjb25mLm9wdGlvblR5cGUgPT0gJ2J1dHRvbicpIGxpc3QucHVzaChoKCJlbC1yYWRpby1idXR0b24iLCB7CiAgICAgICAgICAiYXR0cnMiOiB7CiAgICAgICAgICAgICJsYWJlbCI6IGl0ZW0udmFsdWUKICAgICAgICAgIH0KICAgICAgICB9LCBbaXRlbS5sYWJlbF0pKTtlbHNlIGxpc3QucHVzaChoKCJlbC1yYWRpbyIsIHsKICAgICAgICAgICJhdHRycyI6IHsKICAgICAgICAgICAgImxhYmVsIjogaXRlbS52YWx1ZSwKICAgICAgICAgICAgImJvcmRlciI6IGNvbmYuYm9yZGVyCiAgICAgICAgICB9CiAgICAgICAgfSwgW2l0ZW0ubGFiZWxdKSk7CiAgICAgIH0pOwogICAgICByZXR1cm4gbGlzdDsKICAgIH0KICB9LAogICdlbC1jaGVja2JveC1ncm91cCc6IHsKICAgIG9wdGlvbnM6IGZ1bmN0aW9uIG9wdGlvbnMoaCwgY29uZiwga2V5KSB7CiAgICAgIHZhciBsaXN0ID0gW107CiAgICAgIGNvbmYub3B0aW9ucy5mb3JFYWNoKGZ1bmN0aW9uIChpdGVtKSB7CiAgICAgICAgaWYgKGNvbmYub3B0aW9uVHlwZSA9PSAnYnV0dG9uJykgewogICAgICAgICAgbGlzdC5wdXNoKGgoImVsLWNoZWNrYm94LWJ1dHRvbiIsIHsKICAgICAgICAgICAgImF0dHJzIjogewogICAgICAgICAgICAgICJsYWJlbCI6IGl0ZW0udmFsdWUKICAgICAgICAgICAgfQogICAgICAgICAgfSwgW2l0ZW0ubGFiZWxdKSk7CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIGxpc3QucHVzaChoKCJlbC1jaGVja2JveCIsIHsKICAgICAgICAgICAgImF0dHJzIjogewogICAgICAgICAgICAgICJsYWJlbCI6IGl0ZW0udmFsdWUsCiAgICAgICAgICAgICAgImJvcmRlciI6IGNvbmYuYm9yZGVyCiAgICAgICAgICAgIH0KICAgICAgICAgIH0sIFtpdGVtLmxhYmVsXSkpOwogICAgICAgIH0KICAgICAgfSk7CiAgICAgIHJldHVybiBsaXN0OwogICAgfQogIH0sCiAgJ2VsLXVwbG9hZCc6IHsKICAgICdsaXN0LXR5cGUnOiBmdW5jdGlvbiBsaXN0VHlwZShoLCBjb25mLCBrZXkpIHsKICAgICAgdmFyIGxpc3QgPSBbXTsKICAgICAgaWYgKGNvbmZbJ2xpc3QtdHlwZSddID09ICdwaWN0dXJlLWNhcmQnKSB7CiAgICAgICAgbGlzdC5wdXNoKGgoImkiLCB7CiAgICAgICAgICAiY2xhc3MiOiAiZWwtaWNvbi1wbHVzIgogICAgICAgIH0pKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBsaXN0LnB1c2goaCgiZWwtYnV0dG9uIiwgewogICAgICAgICAgImF0dHJzIjogewogICAgICAgICAgICAic2l6ZSI6ICJtaW5pIiwKICAgICAgICAgICAgInR5cGUiOiAicHJpbWFyeSIsCiAgICAgICAgICAgICJpY29uIjogImVsLWljb24tdXBsb2FkIgogICAgICAgICAgfQogICAgICAgIH0sIFtjb25mLmJ1dHRvblRleHRdKSk7CiAgICAgIH0KICAgICAgaWYgKGNvbmYuc2hvd1RpcCkgewogICAgICAgIGxpc3QucHVzaChoKCJkaXYiLCB7CiAgICAgICAgICAic2xvdCI6ICJ0aXAiLAogICAgICAgICAgImNsYXNzIjogImVsLXVwbG9hZF9fdGlwIgogICAgICAgIH0sIFsiXHU1M0VBXHU4MEZEXHU0RTBBXHU0RjIwXHU0RTBEXHU4RDg1XHU4RkM3ICIsIGNvbmYuZmlsZVNpemUsIGNvbmYuc2l6ZVVuaXQsICIgXHU3Njg0IiwgY29uZi5hY2NlcHQsICJcdTY1ODdcdTRFRjYiXSkpOwogICAgICB9CiAgICAgIHJldHVybiBsaXN0OwogICAgfQogIH0KfTsKdmFyIF9kZWZhdWx0MiA9IHsKICByZW5kZXI6IGZ1bmN0aW9uIHJlbmRlcihoKSB7CiAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgdmFyIGRhdGFPYmplY3QgPSB7CiAgICAgIGF0dHJzOiB7fSwKICAgICAgcHJvcHM6IHt9LAogICAgICBvbjoge30sCiAgICAgIHN0eWxlOiB7fQogICAgfTsKICAgIHZhciBjb25mQ2xvbmUgPSBKU09OLnBhcnNlKEpTT04uc3RyaW5naWZ5KHRoaXMuY29uZikpOwogICAgdmFyIGNoaWxkcmVuID0gW107CiAgICB2YXIgY2hpbGRPYmpzID0gY29tcG9uZW50Q2hpbGRbY29uZkNsb25lLnRhZ107CiAgICBpZiAoY2hpbGRPYmpzKSB7CiAgICAgIE9iamVjdC5rZXlzKGNoaWxkT2JqcykuZm9yRWFjaChmdW5jdGlvbiAoa2V5KSB7CiAgICAgICAgdmFyIGNoaWxkRnVuYyA9IGNoaWxkT2Jqc1trZXldOwogICAgICAgIGlmIChjb25mQ2xvbmVba2V5XSkgewogICAgICAgICAgY2hpbGRyZW4ucHVzaChjaGlsZEZ1bmMoaCwgY29uZkNsb25lLCBrZXkpKTsKICAgICAgICB9CiAgICAgIH0pOwogICAgfQogICAgT2JqZWN0LmtleXMoY29uZkNsb25lKS5mb3JFYWNoKGZ1bmN0aW9uIChrZXkpIHsKICAgICAgdmFyIHZhbCA9IGNvbmZDbG9uZVtrZXldOwogICAgICBpZiAoa2V5ID09ICd2TW9kZWwnKSB7CiAgICAgICAgdk1vZGVsKF90aGlzLCBkYXRhT2JqZWN0LCBjb25mQ2xvbmUuZGVmYXVsdFZhbHVlKTsKICAgICAgfSBlbHNlIGlmIChkYXRhT2JqZWN0W2tleV0pIHsKICAgICAgICBkYXRhT2JqZWN0W2tleV0gPSB2YWw7CiAgICAgIH0gZWxzZSBpZiAoIWlzQXR0cihrZXkpKSB7CiAgICAgICAgZGF0YU9iamVjdC5wcm9wc1trZXldID0gdmFsOwogICAgICB9IGVsc2UgewogICAgICAgIGRhdGFPYmplY3QuYXR0cnNba2V5XSA9IHZhbDsKICAgICAgfQogICAgfSk7CiAgICByZXR1cm4gaCh0aGlzLmNvbmYudGFnLCBkYXRhT2JqZWN0LCBjaGlsZHJlbik7CiAgfSwKICBwcm9wczogWydjb25mJ10KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQyOw=="}, {"version": 3, "names": ["_index", "require", "isAttr", "makeMap", "vModel", "self", "dataObject", "defaultValue", "props", "value", "on", "input", "val", "$emit", "componentChild", "default", "_default", "h", "conf", "key", "prepend", "append", "options", "list", "for<PERSON>ach", "item", "push", "label", "disabled", "optionType", "border", "listType", "buttonText", "showTip", "fileSize", "sizeUnit", "accept", "_default2", "render", "_this", "attrs", "style", "confClone", "JSON", "parse", "stringify", "children", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tag", "Object", "keys", "childFunc", "exports"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/utils/generator/render.js"], "sourcesContent": ["import {makeMap} from '@/utils/index'\r\n\r\n// 参考https://github.com/vuejs/vue/blob/v2.6.10/src/platforms/web/server/util.js\r\nconst isAttr = makeMap(\r\n  'accept,accept-charset,accesskey,action,align,alt,async,autocomplete,'\r\n  + 'autofocus,autoplay,autosave,bgcolor,border,buffered,challenge,charset,'\r\n  + 'checked,cite,class,code,codebase,color,cols,colspan,content,http-equiv,'\r\n  + 'name,contenteditable,contextmenu,controls,coords,data,datetime,default,'\r\n  + 'defer,dir,dirname,disabled,download,draggable,dropzone,enctype,method,for,'\r\n  + 'form,formaction,headers,height,hidden,high,href,hreflang,http-equiv,'\r\n  + 'icon,id,ismap,itemprop,keytype,kind,label,lang,language,list,loop,low,'\r\n  + 'manifest,max,maxlength,media,method,GET,POST,min,multiple,email,file,'\r\n  + 'muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,'\r\n  + 'preload,radiogroup,readonly,rel,required,reversed,rows,rowspan,sandbox,'\r\n  + 'scope,scoped,seamless,selected,shape,size,type,text,password,sizes,span,'\r\n  + 'spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,'\r\n  + 'target,title,type,usemap,value,width,wrap'\r\n)\r\n\r\nfunction vModel(self, dataObject, defaultValue) {\r\n  dataObject.props.value = defaultValue\r\n\r\n  dataObject.on.input = val => {\r\n    self.$emit('input', val)\r\n  }\r\n}\r\n\r\nconst componentChild = {\r\n  'el-button': {\r\n    default(h, conf, key) {\r\n      return conf[key]\r\n    },\r\n  },\r\n  'el-input': {\r\n    prepend(h, conf, key) {\r\n      return <template slot=\"prepend\">{conf[key]}</template>\r\n    },\r\n    append(h, conf, key) {\r\n      return <template slot=\"append\">{conf[key]}</template>\r\n    }\r\n  },\r\n  'el-select': {\r\n    options(h, conf, key) {\r\n      const list = []\r\n      conf.options.forEach(item => {\r\n        list.push(<el-option label={item.label} value={item.value} disabled={item.disabled}></el-option>)\r\n      })\r\n      return list\r\n    }\r\n  },\r\n  'el-radio-group': {\r\n    options(h, conf, key) {\r\n      const list = []\r\n      conf.options.forEach(item => {\r\n        if (conf.optionType == 'button') list.push(<el-radio-button label={item.value}>{item.label}</el-radio-button>)\r\n        else list.push(<el-radio label={item.value} border={conf.border}>{item.label}</el-radio>)\r\n      })\r\n      return list\r\n    }\r\n  },\r\n  'el-checkbox-group': {\r\n    options(h, conf, key) {\r\n      const list = []\r\n      conf.options.forEach(item => {\r\n        if (conf.optionType == 'button') {\r\n          list.push(<el-checkbox-button label={item.value}>{item.label}</el-checkbox-button>)\r\n        } else {\r\n          list.push(<el-checkbox label={item.value} border={conf.border}>{item.label}</el-checkbox>)\r\n        }\r\n      })\r\n      return list\r\n    }\r\n  },\r\n  'el-upload': {\r\n    'list-type': (h, conf, key) => {\r\n      const list = []\r\n      if (conf['list-type'] == 'picture-card') {\r\n        list.push(<i class=\"el-icon-plus\"></i>)\r\n      } else {\r\n        list.push(<el-button size=\"mini\" type=\"primary\" icon=\"el-icon-upload\">{conf.buttonText}</el-button>)\r\n      }\r\n      if (conf.showTip) {\r\n        list.push(<div slot=\"tip\"\r\n                       class=\"el-upload__tip\">只能上传不超过 {conf.fileSize}{conf.sizeUnit} 的{conf.accept}文件</div>)\r\n      }\r\n      return list\r\n    }\r\n  }\r\n}\r\n\r\nexport default {\r\n  render(h) {\r\n    const dataObject = {\r\n      attrs: {},\r\n      props: {},\r\n      on: {},\r\n      style: {}\r\n    }\r\n    const confClone = JSON.parse(JSON.stringify(this.conf))\r\n    const children = []\r\n\r\n    const childObjs = componentChild[confClone.tag]\r\n    if (childObjs) {\r\n      Object.keys(childObjs).forEach(key => {\r\n        const childFunc = childObjs[key]\r\n        if (confClone[key]) {\r\n          children.push(childFunc(h, confClone, key))\r\n        }\r\n      })\r\n    }\r\n\r\n    Object.keys(confClone).forEach(key => {\r\n      const val = confClone[key]\r\n      if (key == 'vModel') {\r\n        vModel(this, dataObject, confClone.defaultValue)\r\n      } else if (dataObject[key]) {\r\n        dataObject[key] = val\r\n      } else if (!isAttr(key)) {\r\n        dataObject.props[key] = val\r\n      } else {\r\n        dataObject.attrs[key] = val\r\n      }\r\n    })\r\n    return h(this.conf.tag, dataObject, children)\r\n  },\r\n  props: ['conf']\r\n}\r\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAEA;AACA,IAAMC,MAAM,GAAG,IAAAC,cAAO,EACpB,sEAAsE,GACpE,wEAAwE,GACxE,yEAAyE,GACzE,yEAAyE,GACzE,4EAA4E,GAC5E,sEAAsE,GACtE,wEAAwE,GACxE,uEAAuE,GACvE,qEAAqE,GACrE,yEAAyE,GACzE,0EAA0E,GAC1E,yEAAyE,GACzE,2CACJ,CAAC;AAED,SAASC,MAAMA,CAACC,IAAI,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC9CD,UAAU,CAACE,KAAK,CAACC,KAAK,GAAGF,YAAY;EAErCD,UAAU,CAACI,EAAE,CAACC,KAAK,GAAG,UAAAC,GAAG,EAAI;IAC3BP,IAAI,CAACQ,KAAK,CAAC,OAAO,EAAED,GAAG,CAAC;EAC1B,CAAC;AACH;AAEA,IAAME,cAAc,GAAG;EACrB,WAAW,EAAE;IACXC,OAAO,WAAAC,SAACC,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,OAAOD,IAAI,CAACC,GAAG,CAAC;IAClB;EACF,CAAC;EACD,UAAU,EAAE;IACVC,OAAO,WAAAA,QAACH,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,OAAAF,CAAA;QAAA,QAAsB;MAAS,IAAEC,IAAI,CAACC,GAAG,CAAC;IAC5C,CAAC;IACDE,MAAM,WAAAA,OAACJ,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACnB,OAAAF,CAAA;QAAA,QAAsB;MAAQ,IAAEC,IAAI,CAACC,GAAG,CAAC;IAC3C;EACF,CAAC;EACD,WAAW,EAAE;IACXG,OAAO,WAAAA,QAACL,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,IAAMI,IAAI,GAAG,EAAE;MACfL,IAAI,CAACI,OAAO,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3BF,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAmBQ,IAAI,CAACE,KAAK;YAAA,SAASF,IAAI,CAAChB,KAAK;YAAA,YAAYgB,IAAI,CAACG;UAAQ;QAAA,EAAc,CAAC;MACnG,CAAC,CAAC;MACF,OAAOL,IAAI;IACb;EACF,CAAC;EACD,gBAAgB,EAAE;IAChBD,OAAO,WAAAA,QAACL,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,IAAMI,IAAI,GAAG,EAAE;MACfL,IAAI,CAACI,OAAO,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIP,IAAI,CAACW,UAAU,IAAI,QAAQ,EAAEN,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAyBQ,IAAI,CAAChB;UAAK;QAAA,IAAGgB,IAAI,CAACE,KAAK,EAAmB,CAAC,MACzGJ,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,SAAkBQ,IAAI,CAAChB,KAAK;YAAA,UAAUS,IAAI,CAACY;UAAM;QAAA,IAAGL,IAAI,CAACE,KAAK,EAAY,CAAC;MAC3F,CAAC,CAAC;MACF,OAAOJ,IAAI;IACb;EACF,CAAC;EACD,mBAAmB,EAAE;IACnBD,OAAO,WAAAA,QAACL,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAE;MACpB,IAAMI,IAAI,GAAG,EAAE;MACfL,IAAI,CAACI,OAAO,CAACE,OAAO,CAAC,UAAAC,IAAI,EAAI;QAC3B,IAAIP,IAAI,CAACW,UAAU,IAAI,QAAQ,EAAE;UAC/BN,IAAI,CAACG,IAAI,CAAAT,CAAA;YAAA;cAAA,SAA4BQ,IAAI,CAAChB;YAAK;UAAA,IAAGgB,IAAI,CAACE,KAAK,EAAsB,CAAC;QACrF,CAAC,MAAM;UACLJ,IAAI,CAACG,IAAI,CAAAT,CAAA;YAAA;cAAA,SAAqBQ,IAAI,CAAChB,KAAK;cAAA,UAAUS,IAAI,CAACY;YAAM;UAAA,IAAGL,IAAI,CAACE,KAAK,EAAe,CAAC;QAC5F;MACF,CAAC,CAAC;MACF,OAAOJ,IAAI;IACb;EACF,CAAC;EACD,WAAW,EAAE;IACX,WAAW,EAAE,SAAAQ,SAACd,CAAC,EAAEC,IAAI,EAAEC,GAAG,EAAK;MAC7B,IAAMI,IAAI,GAAG,EAAE;MACf,IAAIL,IAAI,CAAC,WAAW,CAAC,IAAI,cAAc,EAAE;QACvCK,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA,SAAU;QAAc,EAAK,CAAC;MACzC,CAAC,MAAM;QACLM,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA;YAAA,QAAiB,MAAM;YAAA,QAAM,SAAS;YAAA,QAAM;UAAgB;QAAA,IAAEC,IAAI,CAACc,UAAU,EAAa,CAAC;MACtG;MACA,IAAId,IAAI,CAACe,OAAO,EAAE;QAChBV,IAAI,CAACG,IAAI,CAAAT,CAAA;UAAA,QAAW,KAAK;UAAA,SACJ;QAAgB,mDAAUC,IAAI,CAACgB,QAAQ,EAAEhB,IAAI,CAACiB,QAAQ,aAAIjB,IAAI,CAACkB,MAAM,kBAAS,CAAC;MACtG;MACA,OAAOb,IAAI;IACb;EACF;AACF,CAAC;AAAA,IAAAc,SAAA,GAEc;EACbC,MAAM,WAAAA,OAACrB,CAAC,EAAE;IAAA,IAAAsB,KAAA;IACR,IAAMjC,UAAU,GAAG;MACjBkC,KAAK,EAAE,CAAC,CAAC;MACThC,KAAK,EAAE,CAAC,CAAC;MACTE,EAAE,EAAE,CAAC,CAAC;MACN+B,KAAK,EAAE,CAAC;IACV,CAAC;IACD,IAAMC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAAC3B,IAAI,CAAC,CAAC;IACvD,IAAM4B,QAAQ,GAAG,EAAE;IAEnB,IAAMC,SAAS,GAAGjC,cAAc,CAAC4B,SAAS,CAACM,GAAG,CAAC;IAC/C,IAAID,SAAS,EAAE;MACbE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACvB,OAAO,CAAC,UAAAL,GAAG,EAAI;QACpC,IAAMgC,SAAS,GAAGJ,SAAS,CAAC5B,GAAG,CAAC;QAChC,IAAIuB,SAAS,CAACvB,GAAG,CAAC,EAAE;UAClB2B,QAAQ,CAACpB,IAAI,CAACyB,SAAS,CAAClC,CAAC,EAAEyB,SAAS,EAAEvB,GAAG,CAAC,CAAC;QAC7C;MACF,CAAC,CAAC;IACJ;IAEA8B,MAAM,CAACC,IAAI,CAACR,SAAS,CAAC,CAAClB,OAAO,CAAC,UAAAL,GAAG,EAAI;MACpC,IAAMP,GAAG,GAAG8B,SAAS,CAACvB,GAAG,CAAC;MAC1B,IAAIA,GAAG,IAAI,QAAQ,EAAE;QACnBf,MAAM,CAACmC,KAAI,EAAEjC,UAAU,EAAEoC,SAAS,CAACnC,YAAY,CAAC;MAClD,CAAC,MAAM,IAAID,UAAU,CAACa,GAAG,CAAC,EAAE;QAC1Bb,UAAU,CAACa,GAAG,CAAC,GAAGP,GAAG;MACvB,CAAC,MAAM,IAAI,CAACV,MAAM,CAACiB,GAAG,CAAC,EAAE;QACvBb,UAAU,CAACE,KAAK,CAACW,GAAG,CAAC,GAAGP,GAAG;MAC7B,CAAC,MAAM;QACLN,UAAU,CAACkC,KAAK,CAACrB,GAAG,CAAC,GAAGP,GAAG;MAC7B;IACF,CAAC,CAAC;IACF,OAAOK,CAAC,CAAC,IAAI,CAACC,IAAI,CAAC8B,GAAG,EAAE1C,UAAU,EAAEwC,QAAQ,CAAC;EAC/C,CAAC;EACDtC,KAAK,EAAE,CAAC,MAAM;AAChB,CAAC;AAAA4C,OAAA,CAAArC,OAAA,GAAAsB,SAAA"}]}