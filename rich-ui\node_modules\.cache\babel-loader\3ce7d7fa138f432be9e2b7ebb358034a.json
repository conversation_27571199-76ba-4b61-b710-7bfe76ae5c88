{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\bankrecord.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\bankrecord.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": *************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listBankrecord", "query", "request", "url", "method", "params", "listAggregatorBankRecord", "getBankrecord", "bankRecordId", "addBankrecord", "data", "updateBankrecord", "delBankrecord", "changeStatus", "status", "delImg", "getAccountFundStatistics"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/bankrecord.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询记录公司账户出入账明细列表\r\nexport function listBankrecord(query) {\r\n  return request({\r\n    url: '/system/bankrecord/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\nexport function listAggregatorBankRecord(query) {\r\n  return request({\r\n    url: '/system/bankrecord/aggregator',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询记录公司账户出入账明细详细\r\nexport function getBankrecord(bankRecordId) {\r\n  return request({\r\n    url: '/system/bankrecord/' + bankRecordId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增记录公司账户出入账明细\r\nexport function addBankrecord(data) {\r\n  return request({\r\n    url: '/system/bankrecord',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改记录公司账户出入账明细\r\nexport function updateBankrecord(data) {\r\n  return request({\r\n    url: '/system/bankrecord',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除记录公司账户出入账明细\r\nexport function delBankrecord(bankRecordId) {\r\n  return request({\r\n    url: '/system/bankrecord/' + bankRecordId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(bankRecordId, status) {\r\n  const data = {\r\n    bankRecordId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/bankrecord/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\nexport function delImg(data) {\r\n  return request({\r\n    url: '/system/bankrecord/deleteImg',\r\n    method: 'delete',\r\n    params: data\r\n  })\r\n}\r\n\r\nexport function getAccountFundStatistics(data) {\r\n  return request({\r\n    url: '/system/bankrecord/statistics',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,cAAcA,CAACC,KAAK,EAAE;EACpC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;AAEO,SAASK,wBAAwBA,CAACL,KAAK,EAAE;EAC9C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,aAAaA,CAACC,YAAY,EAAE;EAC1C,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGK,YAAY;IACzCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,aAAaA,CAACC,IAAI,EAAE;EAClC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,gBAAgBA,CAACD,IAAI,EAAE;EACrC,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,aAAaA,CAACJ,YAAY,EAAE;EAC1C,OAAO,IAAAN,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGK,YAAY;IACzCJ,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASS,YAAYA,CAACL,YAAY,EAAEM,MAAM,EAAE;EACjD,IAAMJ,IAAI,GAAG;IACXF,YAAY,EAAZA,YAAY;IACZM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,iCAAiC;IACtCC,MAAM,EAAE,KAAK;IACbM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;AAEO,SAASK,MAAMA,CAACL,IAAI,EAAE;EAC3B,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,8BAA8B;IACnCC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAEK;EACV,CAAC,CAAC;AACJ;AAEO,SAASM,wBAAwBA,CAACN,IAAI,EAAE;EAC7C,OAAO,IAAAR,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,MAAM;IACdM,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}