{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue?vue&type=template&id=2d2bbdc2&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\SidebarItem.vue", "mtime": 1754876882543}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}