package com.rich.system.service;

import com.rich.common.core.domain.entity.RsVatInvoice;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;

/**
 * 发票登记Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface RsVatInvoiceService {
    /**
     * 查询发票登记
     *
     * @param invoiceId 发票登记主键
     * @return 发票登记
     */
    RsVatInvoice selectRsVatInvoiceByInvoiceId(Long invoiceId);

    /**
     * 查询发票登记列表
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记集合
     */
    List<RsVatInvoice> selectRsVatInvoiceList(RsVatInvoice rsVatInvoice);

    /**
     * 新增发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 发票登记对象（包含生成的invoiceId）
     */
    RsVatInvoice insertRsVatInvoice(RsVatInvoice rsVatInvoice);

    /**
     * 修改发票登记
     *
     * @param rsVatInvoice 发票登记
     * @return 结果
     */
    int updateRsVatInvoice(RsVatInvoice rsVatInvoice);

    /**
     * 批量删除发票登记
     *
     * @param invoiceIds 需要删除的发票登记主键集合
     * @return 结果
     */
    int deleteRsVatInvoiceByInvoiceIds(Long[] invoiceIds);

    /**
     * 删除发票登记信息
     *
     * @param invoiceId 发票登记主键
     * @return 结果
     */
    int deleteRsVatInvoiceByInvoiceId(Long invoiceId);

    int changeStatus(RsVatInvoice rsVatInvoice);

    /**
     * 根据rctId查询发票数量
     *
     * @param rctId RCT主键
     * @return 发票数量
     */
    int countRsVatInvoiceByRctId(Long rctId);

    /**
     * 根据rctId和cooperatorId生成发票编码
     * 编码规则：根据rctId的总数量决定第一个字母(A/B)，根据cooperatorId的数量决定后面的数字(1/2/3)
     *
     * @param rctId        RCT主键
     * @param cooperatorId 合作方ID
     * @return 生成的编码字符串
     */
    String generateInvoiceCode(Long rctId, Long cooperatorId);

    /**
     * 写入发票数据到Excel工作簿
     *
     * @param invoiceIds 发票ID列表
     * @param workbook   工作簿对象
     */
    void writeInvoiceData(List<Long> invoiceIds, Workbook workbook);
}
