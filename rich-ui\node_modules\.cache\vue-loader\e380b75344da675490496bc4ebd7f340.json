{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\cargoPrice.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\freight\\cargoPrice.vue", "mtime": 1754876882586}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJjYXJnb1ByaWNlIiwNCiAgcHJvcHM6IFsnc2NvcGUnXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2l6ZTogdGhpcy4kc3RvcmUuc3RhdGUuYXBwLnNpemUgfHwgJ21pbmknLA0KICAgIH0NCiAgfSwNCn0NCg=="}, {"version": 3, "sources": ["cargoPrice.vue"], "names": [], "mappings": ";;;;;;;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "cargoPrice.vue", "sourceRoot": "src/views/system/freight", "sourcesContent": ["<template>\r\n  <div>\r\n    <h6 style=\"margin: 0\">{{ scope.row.cargoPrice != 0 ? scope.row.cargoPrice + scope.row.cargoCurrency : '' }}</h6>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"cargoPrice\",\r\n  props: ['scope'],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || 'mini',\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}