<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="showLeft">
        <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="query"
                 label-width="68px"
                 size="mini"
        >
          <el-form-item label="发票流水号" prop="invoiceCodeNo">
            <el-input
                v-model="queryParams.invoiceCodeNo"
                clearable
                placeholder="发票流水号"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="发票号码" prop="invoiceOfficalNo">
            <el-input
                v-model="queryParams.invoiceOfficalNo"
                clearable
                placeholder="发票号码"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="所属公司" prop="invoiceBelongsTo">
            <el-input
                v-model="queryParams.invoiceBelongsTo"
                clearable
                placeholder="所属公司"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司账户简称" prop="richBankCode">
            <el-input
                v-model="queryParams.richBankCode"
                clearable
                placeholder="我司账户简称"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司发票抬头" prop="richCompanyTitle">
            <el-input
                v-model="queryParams.richCompanyTitle"
                clearable
                placeholder="我司发票抬头"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司纳税人识别号" prop="richVatSerialNo">
            <el-input
                v-model="queryParams.richVatSerialNo"
                clearable
                placeholder="我司纳税人识别号"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司账号" prop="richBankAccount">
            <el-input
                v-model="queryParams.richBankAccount"
                clearable
                placeholder="我司账号"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="我司银行全称" prop="richBankFullname">
            <el-input
                v-model="queryParams.richBankFullname"
                clearable
                placeholder="我司银行全称"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方公司ID" prop="cooperatorId">
            <el-input
                v-model="queryParams.cooperatorId"
                clearable
                placeholder="对方公司ID"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方公司简称" prop="cooperatorShortName">
            <el-input
                v-model="queryParams.cooperatorShortName"
                clearable
                placeholder="对方公司简称"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方账户简称" prop="cooperatorBankCode">
            <el-input
                v-model="queryParams.cooperatorBankCode"
                clearable
                placeholder="对方账户简称"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方发票抬头" prop="cooperatorCompanyTitle">
            <el-input
              v-model="queryParams.cooperatorCompanyTitle"
                clearable
                placeholder="对方发票抬头"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方纳税人识别号" prop="cooperatorVatSerialNo">
            <el-input
                v-model="queryParams.cooperatorVatSerialNo"
                clearable
                placeholder="对方纳税人识别号"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方账号" prop="cooperatorBankAccount">
            <el-input
                v-model="queryParams.cooperatorBankAccount"
                clearable
                placeholder="对方账号"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="对方银行全称" prop="cooperatorBankFullname">
            <el-input
                v-model="queryParams.cooperatorBankFullname"
                clearable
                placeholder="对方银行全称"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="结算币种" prop="chargeCurrencyCode">
            <el-input
                v-model="queryParams.chargeCurrencyCode"
                clearable
                placeholder="结算币种"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="账单应收总额" prop="dnSum">
            <el-input
                v-model="queryParams.dnSum"
                clearable
                placeholder="账单应收总额"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行已收" prop="dnRecieved">
            <el-input
                v-model="queryParams.dnRecieved"
                clearable
                placeholder="银行已收"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行未收" prop="dnBalance">
            <el-input
                v-model="queryParams.dnBalance"
                clearable
                placeholder="银行未收"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="账单应付总额" prop="cnSum">
            <el-input
                v-model="queryParams.cnSum"
                clearable
                placeholder="账单应付总额"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行已付" prop="cnPaid">
            <el-input
                v-model="queryParams.cnPaid"
                clearable
                placeholder="银行已付"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="银行未付" prop="cnBalance">
            <el-input
                v-model="queryParams.cnBalance"
                clearable
                placeholder="银行未付"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="期望支付日期" prop="expectedPayDate">
            <el-date-picker v-model="queryParams.expectedPayDate"
                            clearable
                            placeholder="期望支付日期"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="批复支付日期" prop="approvedPayDate">
            <el-date-picker v-model="queryParams.approvedPayDate"
                            clearable
                            placeholder="批复支付日期"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="实际支付日期" prop="actualPayDate">
            <el-date-picker v-model="queryParams.actualPayDate"
                            clearable
                            placeholder="实际支付日期"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="发票币种" prop="invoiceCurrencyCode">
            <el-input
                v-model="queryParams.invoiceCurrencyCode"
                clearable
                placeholder="发票币种"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="发票汇率" prop="invoiceExchangeRate">
            <el-input
                v-model="queryParams.invoiceExchangeRate"
                clearable
                placeholder="发票汇率"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="不含税金额" prop="invoiceNetAmount">
            <el-input
                v-model="queryParams.invoiceNetAmount"
                clearable
                placeholder="不含税金额"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="税金" prop="vatAmount">
            <el-input
                v-model="queryParams.vatAmount"
                clearable
                placeholder="税金"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="价税合计" prop="invoiceVatAmount">
            <el-input
                v-model="queryParams.invoiceVatAmount"
                clearable
                placeholder="价税合计"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="销项不含税" prop="saleNetSum">
            <el-input
                v-model="queryParams.saleNetSum"
                clearable
                placeholder="销项不含税"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="销项税金" prop="saleTax">
            <el-input
                v-model="queryParams.saleTax"
                clearable
                placeholder="销项税金"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="销项含税合计" prop="saleTaxTotal">
            <el-input
                v-model="queryParams.saleTaxTotal"
                clearable
                placeholder="销项含税合计"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="进项不含税" prop="buyNetSum">
            <el-input
                v-model="queryParams.buyNetSum"
                clearable
                placeholder="进项不含税"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="进项税金" prop="buyTax">
            <el-input
                v-model="queryParams.buyTax"
                clearable
                placeholder="进项税金"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="进项含税合计" prop="buyTaxTotal">
            <el-input
                v-model="queryParams.buyTaxTotal"
                clearable
                placeholder="进项含税合计"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="发票性质" prop="taxClass">
            <el-input
                v-model="queryParams.taxClass"
                clearable
                placeholder="发票性质"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="报税所属月份" prop="belongsToMonth">
            <el-input
                v-model="queryParams.belongsToMonth"
                clearable
                placeholder="报税所属月份"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="申请人ID" prop="applyStuffId">
            <el-input
                v-model="queryParams.applyStuffId"
                clearable
                placeholder="申请人ID"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="申请时间" prop="appliedTime">
            <el-date-picker v-model="queryParams.appliedTime"
                            clearable
                            placeholder="申请时间"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="开票人ID" prop="issuedStuffId">
            <el-input
                v-model="queryParams.issuedStuffId"
                clearable
                placeholder="开票人ID"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="开票时间" prop="issuedTime">
            <el-date-picker v-model="queryParams.issuedTime"
                            clearable
                            placeholder="开票时间"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="报税人ID" prop="taxStuffId">
            <el-input
                v-model="queryParams.taxStuffId"
                clearable
                placeholder="报税人ID"
                @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="报税时间" prop="taxDeclareTime">
            <el-date-picker v-model="queryParams.taxDeclareTime"
                            clearable
                            placeholder="报税时间"
                            type="date"
                            value-format="yyyy-MM-dd"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="showRight">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['system:vatinvoice:add']"
                icon="el-icon-plus"
                plain
                size="mini"
                type="primary"
                @click="handleAdd"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['system:vatinvoice:edit']"
                :disabled="single"
                icon="el-icon-edit"
                plain
                size="mini"
                type="success"
                @click="handleUpdate"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['system:vatinvoice:remove']"
                :disabled="multiple"
                icon="el-icon-delete"
                plain
                size="mini"
                type="danger"
                @click="handleDelete"
            >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['system:vatinvoice:export']"
                icon="el-icon-download"
                plain
                size="mini"
                type="warning"
                @click="handleExport"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:vatinvoice:export']"
              icon="el-icon-download"
              plain
              size="mini"
              type="warning"
              @click="handleExportTheInvoicingInformation"
            >导出开票资料
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="vatinvoiceList" @selection-change="handleSelectionChange"
                  @row-dblclick="handleRowClick">
          <el-table-column align="center" type="selection" width="28"/>
          <el-table-column align="center" label="发票流水号" prop="invoiceCodeNo" width="120"/>
          <el-table-column align="center" label="发票号码" prop="invoiceOfficalNo"/>
          <el-table-column align="center" label="进销标志" prop="saleBuy"/>
          <el-table-column align="center" label="所属公司" prop="invoiceBelongsTo"/>
          <el-table-column align="center" label="我司账户简称" prop="richBankCode"/>
          <el-table-column align="center" label="我司发票抬头" prop="richCompanyTitle" show-overflow-tooltip
                           width="120"/>
          <el-table-column align="center" label="我司纳税人识别号" prop="richVatSerialNo" show-overflow-tooltip
                           width="120"/>
          <el-table-column align="center" label="我司账号" prop="richBankAccount" show-overflow-tooltip width="120"/>
          <el-table-column align="center" label="我司银行全称" prop="richBankFullname" show-overflow-tooltip/>
          <el-table-column align="center" label="对方公司简称" prop="cooperatorShortName"/>
          <el-table-column align="center" label="对方账户简称" prop="cooperatorBankCode" show-overflow-tooltip/>
          <el-table-column align="center" label="对方发票抬头" prop="cooperatorCompanyTitle" show-overflow-tooltip
                           width="120"/>
          <el-table-column align="center" label="对方纳税人识别号" prop="cooperatorVatSerialNo" show-overflow-tooltip
                           width="120"/>
          <el-table-column align="center" label="对方账号" prop="cooperatorBankAccount" show-overflow-tooltip
                           width="120"/>
          <el-table-column align="center" label="对方银行全称" prop="cooperatorBankFullname"/>
          <el-table-column align="center" label="所属订单汇总" prop="rctNoSummary"/>
          <el-table-column align="center" label="对方单号汇总" prop="cooperatorReferNo"/>
          <el-table-column align="center" label="发票项目汇总" prop="officalChargeNameSummary"/>
          <el-table-column align="center" label="结算币种" prop="chargeCurrencyCode"/>
          <el-table-column align="center" label="账单应收总额" prop="dnSum"/>
          <el-table-column align="center" label="银行已收" prop="dnRecieved"/>
          <el-table-column align="center" label="银行未收" prop="dnBalance"/>
          <el-table-column align="center" label="账单应付总额" prop="cnSum"/>
          <el-table-column align="center" label="银行已付" prop="cnPaid"/>
          <el-table-column align="center" label="银行未付" prop="cnBalance"/>
          <el-table-column align="center" label="销账状态" prop="chargeClearStatus"/>
          <el-table-column align="center" label="期望支付日期" prop="expectedPayDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.expectedPayDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="批复支付日期" prop="approvedPayDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.approvedPayDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="实际支付日期" prop="actualPayDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.actualPayDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="对应银行流水" prop="sqdBankStatementList"/>
          <el-table-column align="center" label="发票币种" prop="invoiceCurrencyCode"/>
          <el-table-column align="center" label="发票汇率" prop="invoiceExchangeRate"/>
          <el-table-column align="center" label="不含税金额" prop="invoiceNetAmount"/>
          <el-table-column align="center" label="税金" prop="vatAmount"/>
          <el-table-column align="center" label="价税合计" prop="invoiceVatAmount"/>
          <el-table-column align="center" label="销项不含税" prop="saleNetSum"/>
          <el-table-column align="center" label="销项税金" prop="saleTax"/>
          <el-table-column align="center" label="销项含税合计" prop="saleTaxTotal"/>
          <el-table-column align="center" label="进项不含税" prop="buyNetSum"/>
          <el-table-column align="center" label="进项税金" prop="buyTax"/>
          <el-table-column align="center" label="进项含税合计" prop="buyTaxTotal"/>
          <el-table-column align="center" label="发票性质" prop="taxClass"/>
          <el-table-column align="center" label="发票类型" prop="invoiceType"/>
          <el-table-column align="center" label="报税所属月份" prop="belongsToMonth"/>
          <el-table-column align="center" label="发票状态" prop="invoiceStatus"/>
          <el-table-column align="center" label="备注" prop="invoiceRemark" show-overflow-tooltip/>
          <el-table-column align="center" label="申请人ID" prop="applyStuffId"/>
          <el-table-column align="center" label="申请时间" prop="appliedTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.appliedTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="开票人ID" prop="issuedStuffId"/>
          <el-table-column align="center" label="开票时间" prop="issuedTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.issuedTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="报税人ID" prop="taxStuffId"/>
          <el-table-column align="center" label="报税时间" prop="taxDeclareTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.taxDeclareTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
            <template slot-scope="scope">
              <el-button
                  v-hasPermi="['system:vatinvoice:edit']"
                  icon="el-icon-edit"
                  size="mini"
                  style="margin-right: -8px"
                  type="success"
                  @click="handleUpdate(scope.row)"
              >修改
              </el-button>
              <el-button
                  v-hasPermi="['system:vatinvoice:remove']"
                  icon="el-icon-delete"
                  size="mini"
                  style="margin-right: -8px"
                  type="danger"
                  @click="handleDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="total>0"
            :limit.sync="queryParams.pageSize"
            :page.sync="queryParams.pageNum"
            :total="total"
            @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加或修改发票登记对话框 -->
    <vatinvoice-dialog
      :form="form"
      :invoice-items="invoiceItemList"
      :bank-account-list="bankAccountList"
      :company-list="companyList"
      :rules="rules"
      :title="title"
      :visible.sync="open"
      @cancel="cancel"
      @submit="handleDialogSubmit"
    />
  </div>
</template>

<script>
import {
  addVatinvoice,
  changeStatus,
  delVatinvoice,
  getVatinvoice,
  listVatinvoice,
  updateVatinvoice,
  generateInvoiceExcel
} from "@/api/system/vatInvoice"
import VatinvoiceDialog from './components/VatinvoiceDialog'

export default {
  name: "Vatinvoice",
  components: {
    VatinvoiceDialog
  },
  data() {
    return {
      showLeft: 0,
      showRight: 24,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 发票登记表格数据
      vatinvoiceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        invoiceCodeNo: null,
        invoiceOfficalNo: null,
        saleBuy: null,
        invoiceBelongsTo: null,
        richBankCode: null,
        richCompanyTitle: null,
        richVatSerialNo: null,
        richBankAccount: null,
        richBankFullname: null,
        cooperatorId: null,
        cooperatorShortName: null,
        cooperatorBankCode: null,
        cooperatorFullname: null,
        cooperatorVatSerialNo: null,
        cooperatorBankAccount: null,
        cooperatorBankFullname: null,
        rctNoSummary: null,
        cooperatorReferNo: null,
        officalChargeNameSummary: null,
        chargeCurrencyCode: null,
        dnSum: null,
        dnRecieved: null,
        dnBalance: null,
        cnSum: null,
        cnPaid: null,
        cnBalance: null,
        chargeClearStatus: null,
        expectedPayDate: null,
        approvedPayDate: null,
        actualPayDate: null,
        sqdBankStatementList: null,
        invoiceCurrencyCode: null,
        invoiceExchangeRate: null,
        invoiceNetAmount: null,
        vatAmount: null,
        invoiceVatAmount: null,
        saleNetSum: null,
        saleTax: null,
        saleTaxTotal: null,
        buyNetSum: null,
        buyTax: null,
        buyTaxTotal: null,
        taxClass: null,
        invoiceType: null,
        belongsToMonth: null,
        invoiceStatus: null,
        invoiceRemark: null,
        applyStuffId: null,
        appliedTime: null,
        issuedStuffId: null,
        issuedTime: null,
        taxStuffId: null,
        taxDeclareTime: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        invoiceCodeNo: [
          {
            required: true,
            message: "发票流水号不能为空",
            trigger: "blur"
          }
        ],
        saleBuy: [
          {
            required: true,
            message: "进销标志：sale=销项，buy=进项不能为空",
            trigger: "blur"
          }
        ]
      },
      invoiceItemList: [],
      companyList: [],
      bankAccountList: [],
    }
  },
  watch: {
    showSearch(n) {
      if (n === true) {
        this.showRight = 21
        this.showLeft = 3
      } else {
        this.showRight = 24
        this.showLeft = 0
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    handleRowClick(row) {
      this.handleUpdate(row)
    },
    /** 查询发票登记列表 */
    getList() {
      this.loading = true
      listVatinvoice(this.queryParams).then(response => {
        this.vatinvoiceList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        invoiceId: null,
        invoiceCodeNo: null,
        invoiceOfficalNo: null,
        saleBuy: null,
        invoiceBelongsTo: null,
        richBankCode: null,
        richCompanyTitle: null,
        richVatSerialNo: null,
        richBankAccount: null,
        richBankFullname: null,
        cooperatorId: null,
        cooperatorShortName: null,
        cooperatorBankCode: null,
        cooperatorFullname: null,
        cooperatorVatSerialNo: null,
        cooperatorBankAccount: null,
        cooperatorBankFullname: null,
        rctNoSummary: null,
        cooperatorReferNo: null,
        officalChargeNameSummary: null,
        chargeCurrencyCode: null,
        dnSum: null,
        dnRecieved: null,
        dnBalance: null,
        cnSum: null,
        cnPaid: null,
        cnBalance: null,
        chargeClearStatus: "0",
        expectedPayDate: null,
        approvedPayDate: null,
        actualPayDate: null,
        sqdBankStatementList: null,
        invoiceCurrencyCode: null,
        invoiceExchangeRate: null,
        invoiceNetAmount: null,
        vatAmount: null,
        invoiceVatAmount: null,
        saleNetSum: null,
        saleTax: null,
        saleTaxTotal: null,
        buyNetSum: null,
        buyTax: null,
        buyTaxTotal: null,
        taxClass: null,
        invoiceType: null,
        belongsToMonth: null,
        invoiceStatus: "0",
        invoiceRemark: null,
        applyStuffId: null,
        appliedTime: null,
        issuedStuffId: null,
        issuedTime: null,
        taxStuffId: null,
        taxDeclareTime: null,
        createTime: null,
        updateTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用"
      this.$modal.confirm("确认要\"" + text + "吗？").then(function () {
        return changeStatus(row.invoiceId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0"
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.invoiceId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加发票登记"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const invoiceId = row.invoiceId || this.ids
      getVatinvoice(invoiceId).then(response => {
        this.form = response.data
        this.companyList = response.extCompanyList
        this.bankAccountList = response.basAccountList
        this.open = true
        this.title = "修改发票登记"
      })
    },
    /** 提交按钮 */
    submitForm() {
      // 已移至handleDialogSubmit方法
    },

    /** 处理对话框提交 */
    handleDialogSubmit(formData) {
      if (formData.invoiceId != null) {
        updateVatinvoice(formData).then(response => {
          this.$modal.msgSuccess("修改成功")
          this.open = false
          this.getList()
        })
      } else {
        addVatinvoice(formData).then(response => {
          this.$modal.msgSuccess("新增成功")
          this.open = false
          this.getList()
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const invoiceIds = row.invoiceId || this.ids
      this.$modal.confirm("是否确认删除发票登记编号为\"" + invoiceIds + "\"的数据项？").then(function () {
        return delVatinvoice(invoiceIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    /** 导出开票资料按钮操作 */
    handleExportTheInvoicingInformation() {
      // 检查是否有选中的发票
      if (this.ids.length === 0) {
        this.$modal.msgError("请先选择要导出的发票")
        return
      }

      generateInvoiceExcel(this.ids)
        .then(response => {
          // 获取文件的字节数组 (ArrayBuffer)
          const data = response

          // 生成文件名
          let fileName = `Invoices_${new Date().getTime()}.xlsx`

          // 创建一个 Blob 对象来存储文件
          const blob = new Blob([data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"  // Excel 文件类型
          })

          // 创建一个临时链接，模拟点击来下载文件
          const link = document.createElement("a")
          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象
          link.href = url
          link.download = fileName  // 设置下载的文件名

          // 模拟点击链接，触发下载
          document.body.appendChild(link)
          link.click()

          // 下载完成后移除链接，并释放 URL 对象
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          this.$modal.msgSuccess("Excel文件导出成功")
        })
        .catch(error => {
          console.error("文件下载失败:", error)
          this.$modal.msgError("Excel文件导出失败")
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download("system/vatinvoice/export", {
        ...this.queryParams
      }, `vatinvoice_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>
