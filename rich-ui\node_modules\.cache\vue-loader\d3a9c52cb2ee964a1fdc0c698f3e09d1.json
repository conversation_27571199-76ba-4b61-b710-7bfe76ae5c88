{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\index.vue?vue&type=template&id=33ec43fc&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1754876882544}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}