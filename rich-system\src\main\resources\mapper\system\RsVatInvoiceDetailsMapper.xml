<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.RsVatInvoiceDetailsMapper">
    
    <resultMap type="RsVatInvoiceDetails" id="RsVatInvoiceDetailsResult">
        <result property="invoiceDetailsId"    column="invoice_details_id"    />
        <result property="invoiceId"    column="invoice_id"    />
        <result property="chargeId"    column="charge_id"    />
        <result property="officalChargeName"    column="offical_charge_name"    />
        <result property="dutyCode"    column="duty_code"    />
        <result property="type"    column="type"    />
        <result property="unit"    column="unit"    />
        <result property="quantity"    column="quantity"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="subTotal"    column="sub_total"    />
        <result property="vatRate"    column="vat_rate"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectRsVatInvoiceDetailsVo">
        select invoice_details_id, invoice_id, charge_id, offical_charge_name, duty_code, type, unit, quantity, unit_price, sub_total, vat_rate, create_time, update_time from rs_vat_invoice_details
    </sql>

    <select id="selectRsVatInvoiceDetailsList" parameterType="RsVatInvoiceDetails" resultMap="RsVatInvoiceDetailsResult">
        <include refid="selectRsVatInvoiceDetailsVo"/>
        <where>  
            <if test="invoiceId != null "> and invoice_id = #{invoiceId}</if>
            <if test="chargeId != null "> and charge_id = #{chargeId}</if>
            <if test="officalChargeName != null  and officalChargeName != ''"> and offical_charge_name like concat('%', #{officalChargeName}, '%')</if>
            <if test="dutyCode != null  and dutyCode != ''"> and duty_code = #{dutyCode}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="unit != null  and unit != ''"> and unit = #{unit}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="unitPrice != null "> and unit_price = #{unitPrice}</if>
            <if test="subTotal != null "> and sub_total = #{subTotal}</if>
            <if test="vatRate != null "> and vat_rate = #{vatRate}</if>
        </where>
    </select>
    
    <select id="selectRsVatInvoiceDetailsByInvoiceDetailsId" parameterType="Long" resultMap="RsVatInvoiceDetailsResult">
        <include refid="selectRsVatInvoiceDetailsVo"/>
        where invoice_details_id = #{invoiceDetailsId}
    </select>
        
    <insert id="insertRsVatInvoiceDetails" parameterType="RsVatInvoiceDetails" useGeneratedKeys="true" keyProperty="invoiceDetailsId">
        insert into rs_vat_invoice_details
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null">invoice_id,</if>
            <if test="chargeId != null">charge_id,</if>
            <if test="officalChargeName != null and officalChargeName != ''">offical_charge_name,</if>
            <if test="dutyCode != null">duty_code,</if>
            <if test="type != null">type,</if>
            <if test="unit != null">unit,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="subTotal != null">sub_total,</if>
            <if test="vatRate != null">vat_rate,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="invoiceId != null">#{invoiceId},</if>
            <if test="chargeId != null">#{chargeId},</if>
            <if test="officalChargeName != null and officalChargeName != ''">#{officalChargeName},</if>
            <if test="dutyCode != null">#{dutyCode},</if>
            <if test="type != null">#{type},</if>
            <if test="unit != null">#{unit},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="subTotal != null">#{subTotal},</if>
            <if test="vatRate != null">#{vatRate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRsVatInvoiceDetails" parameterType="RsVatInvoiceDetails">
        update rs_vat_invoice_details
        <trim prefix="SET" suffixOverrides=",">
            <if test="invoiceId != null">invoice_id = #{invoiceId},</if>
            <if test="chargeId != null">charge_id = #{chargeId},</if>
            <if test="officalChargeName != null and officalChargeName != ''">offical_charge_name = #{officalChargeName},</if>
            <if test="dutyCode != null">duty_code = #{dutyCode},</if>
            <if test="type != null">type = #{type},</if>
            <if test="unit != null">unit = #{unit},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="subTotal != null">sub_total = #{subTotal},</if>
            <if test="vatRate != null">vat_rate = #{vatRate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where invoice_details_id = #{invoiceDetailsId}
    </update>

    <delete id="deleteRsVatInvoiceDetailsByInvoiceDetailsId" parameterType="Long">
        delete from rs_vat_invoice_details where invoice_details_id = #{invoiceDetailsId}
    </delete>

    <delete id="deleteRsVatInvoiceDetailsByInvoiceDetailsIds" parameterType="String">
        delete from rs_vat_invoice_details where invoice_details_id in 
        <foreach item="invoiceDetailsId" collection="array" open="(" separator="," close=")">
            #{invoiceDetailsId}
        </foreach>
    </delete>
</mapper>