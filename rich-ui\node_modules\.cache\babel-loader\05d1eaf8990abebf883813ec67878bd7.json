{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\design\\scale.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\print\\demo\\design\\scale.js", "mtime": 1737429728488}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5mdW5jdGlvbi5uYW1lLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5maW5kLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIik7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAudG8tc3RyaW5nLmpzIik7CnZhciBfZGVmYXVsdCA9IGZ1bmN0aW9uICgpIHsKICBmdW5jdGlvbiB0KCkgewogICAgLy8ganNvbuaooeadvyBvcHRpb25zIOWvueW6lOmUruWAvCBrZXkKICAgIHRoaXMubmFtZSA9ICJzY2FsZSI7CiAgfQoKICAvLyDmtonlj4rkv67mlLnlhYPntKDmoLflvI/vvIwg5re75Yqg5LiA5LiqIGNzcyDmlrnms5UKICAvLyB0OiDlhYPntKDlr7nosaHvvIwgZSDlj4LmlbDlgLwKICByZXR1cm4gdC5wcm90b3R5cGUuY3NzID0gZnVuY3Rpb24gKHQsIGUpIHsKICAgIGlmICh0ICYmIHQubGVuZ3RoKSB7CiAgICAgIGlmIChlKSByZXR1cm4gdC5jc3MoJ3RyYW5zZm9ybScsICdzY2FsZSgnICsgZSArICcpJyk7CiAgICB9CiAgICByZXR1cm4gbnVsbDsKICB9LAogIC8vIOWIm+W7uiBET00KICB0LnByb3RvdHlwZS5jcmVhdGVUYXJnZXQgPSBmdW5jdGlvbiAodCwgaSwgZSkgewogICAgLy8gIHQ6IOWFg+e0oOWvueixoe+8jGk6IOWFg+e0oG9wdGlvbnMsIGU6IOWFg+e0oHByaW50RWxlbWVudFR5cGUKICAgIHJldHVybiB0aGlzLnRhcmdldCA9ICQoJzxkaXYgY2xhc3M9ImhpcHJpbnQtb3B0aW9uLWl0ZW0iPlxuICAgICAgICA8ZGl2IGNsYXNzPSJoaXByaW50LW9wdGlvbi1pdGVtLWxhYmVsIj5cbiAgICAgICAg57yp5pS+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzPSJoaXByaW50LW9wdGlvbi1pdGVtLWZpZWxkIj5cbiAgICAgICAgPGlucHV0IHR5cGU9Im51bWJlciIgdmFsdWU9IjEiIHN0ZXA9IjAuMSIgbWluPSIwLjEiIG1heD0iMyIgY2xhc3M9ImF1dG8tc3VibWl0Ii8+XG4gICAgICAgIDwvZGl2PlxuICAgIDwvZGl2PicpLCB0aGlzLnRhcmdldDsKICB9LAogIC8vIOiOt+WPluWAvAogIHQucHJvdG90eXBlLmdldFZhbHVlID0gZnVuY3Rpb24gKCkgewogICAgdmFyIHQgPSB0aGlzLnRhcmdldC5maW5kKCJpbnB1dCIpLnZhbCgpOwogICAgaWYgKHQpIHJldHVybiBwYXJzZUZsb2F0KHQudG9TdHJpbmcoKSk7CiAgfSwKICAvLyDorr7nva7lgLwKICB0LnByb3RvdHlwZS5zZXRWYWx1ZSA9IGZ1bmN0aW9uICh0KSB7CiAgICAvLyAgdDogb3B0aW9ucyDlr7nlupTplK7nmoTlgLwKICAgIHRoaXMudGFyZ2V0LmZpbmQoImlucHV0IikudmFsKHQpOwogIH0sCiAgLy8g6ZSA5q+BIERPTQogIHQucHJvdG90eXBlLmRlc3Ryb3kgPSBmdW5jdGlvbiAoKSB7CiAgICB0aGlzLnRhcmdldC5yZW1vdmUoKTsKICB9LCB0Owp9KCk7CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "names": ["t", "name", "prototype", "css", "e", "length", "createTarget", "i", "target", "$", "getValue", "find", "val", "parseFloat", "toString", "setValue", "destroy", "remove", "exports", "default", "_default"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/views/print/demo/design/scale.js"], "sourcesContent": ["export default (function () {\r\n  function t() {\r\n    // json模板 options 对应键值 key\r\n    this.name = \"scale\";\r\n  }\r\n\r\n  // 涉及修改元素样式， 添加一个 css 方法\r\n  // t: 元素对象， e 参数值\r\n  return t.prototype.css = function (t, e) {\r\n    if (t && t.length) {\r\n      if (e) return t.css('transform', 'scale(' + e + ')');\r\n    }\r\n    return null;\r\n  },\r\n    // 创建 DOM\r\n    t.prototype.createTarget = function (t, i, e) { //  t: 元素对象，i: 元素options, e: 元素printElementType\r\n      return this.target = $('<div class=\"hiprint-option-item\">\\n        <div class=\"hiprint-option-item-label\">\\n        缩放\\n        </div>\\n        <div class=\"hiprint-option-item-field\">\\n        <input type=\"number\" value=\"1\" step=\"0.1\" min=\"0.1\" max=\"3\" class=\"auto-submit\"/>\\n        </div>\\n    </div>'), this.target;\r\n    },\r\n    // 获取值\r\n    t.prototype.getValue = function () {\r\n      var t = this.target.find(\"input\").val();\r\n      if (t) return parseFloat(t.toString());\r\n    },\r\n    // 设置值\r\n    t.prototype.setValue = function (t) { //  t: options 对应键的值\r\n      this.target.find(\"input\").val(t);\r\n    },\r\n    // 销毁 DOM\r\n    t.prototype.destroy = function () {\r\n      this.target.remove();\r\n    }, t;\r\n}())\r\n"], "mappings": ";;;;;;;;;;eAAgB,YAAY;EAC1B,SAASA,CAACA,CAAA,EAAG;IACX;IACA,IAAI,CAACC,IAAI,GAAG,OAAO;EACrB;;EAEA;EACA;EACA,OAAOD,CAAC,CAACE,SAAS,CAACC,GAAG,GAAG,UAAUH,CAAC,EAAEI,CAAC,EAAE;IACvC,IAAIJ,CAAC,IAAIA,CAAC,CAACK,MAAM,EAAE;MACjB,IAAID,CAAC,EAAE,OAAOJ,CAAC,CAACG,GAAG,CAAC,WAAW,EAAE,QAAQ,GAAGC,CAAC,GAAG,GAAG,CAAC;IACtD;IACA,OAAO,IAAI;EACb,CAAC;EACC;EACAJ,CAAC,CAACE,SAAS,CAACI,YAAY,GAAG,UAAUN,CAAC,EAAEO,CAAC,EAAEH,CAAC,EAAE;IAAE;IAC9C,OAAO,IAAI,CAACI,MAAM,GAAGC,CAAC,CAAC,wRAAwR,CAAC,EAAE,IAAI,CAACD,MAAM;EAC/T,CAAC;EACD;EACAR,CAAC,CAACE,SAAS,CAACQ,QAAQ,GAAG,YAAY;IACjC,IAAIV,CAAC,GAAG,IAAI,CAACQ,MAAM,CAACG,IAAI,CAAC,OAAO,CAAC,CAACC,GAAG,CAAC,CAAC;IACvC,IAAIZ,CAAC,EAAE,OAAOa,UAAU,CAACb,CAAC,CAACc,QAAQ,CAAC,CAAC,CAAC;EACxC,CAAC;EACD;EACAd,CAAC,CAACE,SAAS,CAACa,QAAQ,GAAG,UAAUf,CAAC,EAAE;IAAE;IACpC,IAAI,CAACQ,MAAM,CAACG,IAAI,CAAC,OAAO,CAAC,CAACC,GAAG,CAACZ,CAAC,CAAC;EAClC,CAAC;EACD;EACAA,CAAC,CAACE,SAAS,CAACc,OAAO,GAAG,YAAY;IAChC,IAAI,CAACR,MAAM,CAACS,MAAM,CAAC,CAAC;EACtB,CAAC,EAAEjB,CAAC;AACR,CAAC,CAAC,CAAC;AAAAkB,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}