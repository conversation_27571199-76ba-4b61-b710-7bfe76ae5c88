{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\commoninfo\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\commoninfo\\index.vue", "mtime": 1754876882576}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_commoninfo", "require", "name", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "commoninfoList", "title", "open", "queryParams", "pageNum", "pageSize", "infoQuery", "form", "rules", "watch", "n", "created", "getList", "methods", "_this", "listCommoninfo", "then", "response", "rows", "cancel", "reset", "infoId", "infoShortName", "infoLocalName", "infoEnName", "infoDefault", "infoTypeId", "roleTypeId", "orderNum", "status", "remark", "createBy", "createTime", "updateBy", "updateTime", "deleteBy", "deleteTime", "deleteStatus", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleSelectionChange", "selection", "map", "item", "length", "handleAdd", "handleUpdate", "row", "_this2", "getCommoninfo", "submitForm", "_this3", "$refs", "validate", "valid", "updateCommoninfo", "$modal", "msgSuccess", "addCommoninfo", "handleDelete", "_this4", "infoIds", "$confirm", "customClass", "delCommoninfo", "catch", "handleExport", "download", "_objectSpread2", "default", "concat", "Date", "getTime", "handleStatusChange", "_this5", "text", "changeStatus", "getInfoType", "val", "getRoleType", "getServiceTypeIds", "serviceTypeIds", "exports", "_default"], "sources": ["src/views/system/commoninfo/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"infoShortName\">\r\n            <el-input\r\n              v-model=\"queryParams.infoQuery\"\r\n              clearable\r\n              placeholder=\"中英文简称\"\r\n              style=\"width: 100%\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:commoninfo:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:commoninfo:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"commoninfoList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"left\" label=\"所属类型\" prop=\"infoType.infoTypeLocalName\" width=\"100\"/>\r\n          <el-table-column align=\"left\" label=\"信息名\" width=\"300\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.infoShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.infoLocalName }}</a>\r\n              {{ scope.row.infoEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"服务类型\" prop=\"serviceType\"/>\r\n          <el-table-column label=\"默认字段\" prop=\"infoDefault\"/>\r\n          <el-table-column align=\"center\" label=\"信息来源\" prop=\"roleType.roleTypeLocalName\" width=\"88\"/>\r\n          <el-table-column key=\"remark\" align=\"center\" label=\"备注\" prop=\"remark\" show-tooltip-when-overflow/>\r\n          <el-table-column key=\"orderNum\" align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"48\"/>\r\n          <el-table-column key=\"status\" align=\"center\" label=\"状态\" prop=\"status\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <el-switch\r\n                v-model=\"scope.row.status\"\r\n                active-value=\"0\"\r\n                inactive-value=\"1\"\r\n                @change=\"handleStatusChange(scope.row)\"\r\n              ></el-switch>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:commoninfo:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:commoninfo:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改通用信息对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"70px\" class=\"edit\">\r\n        <el-form-item label=\"简称\" prop=\"infoShortName\">\r\n          <el-input v-model=\"form.infoShortName\" placeholder=\"简称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"中文名\" prop=\"infoLocalName\">\r\n          <el-input v-model=\"form.infoLocalName\" placeholder=\"中文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文名\" prop=\"infoEnName\">\r\n          <el-input v-model=\"form.infoEnName\" placeholder=\"英文名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"默认字段\" prop=\"infoDefault\">\r\n          <el-select v-model=\"form.infoDefault\" style=\"width: 100%\">\r\n            <el-option label=\"Long\" value=\"Long\"/>\r\n            <el-option label=\"String\" value=\"String\"/>\r\n            <el-option label=\"Integer\" value=\"Integer\"/>\r\n            <el-option label=\"Double\" value=\"Double\"/>\r\n            <el-option label=\"BigDecimal\" value=\"BigDecimal\"/>\r\n            <el-option label=\"Date\" value=\"Date\"/>\r\n            <el-option label=\"Boolean\" value=\"Boolean\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"服务类型\" prop=\"serviceTypeIds\">\r\n          <tree-select :flat=\"false\" :multiple=\"true\" :pass=\"form.serviceTypeIds\"\r\n                       :placeholder=\"'服务类型'\" :type=\"'serviceType'\"\r\n                       @return=\"getServiceTypeIds\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属类型\" prop=\"infoTypeId\">\r\n          <tree-select :pass=\"form.infoTypeId\" :placeholder=\"'所属类型'\" :type=\"'commonInfoType'\"\r\n                       @return=\"getInfoType\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"信息来源\" prop=\"roleTypeId\">\r\n          <tree-select :pass=\"form.roleTypeId\" :placeholder=\"'信息来源'\" :type=\"'companyRoleType'\"\r\n                       @return=\"getRoleType\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" placeholder=\"排序\" style=\"width: 100%\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addCommoninfo,\r\n  changeStatus,\r\n  delCommoninfo,\r\n  getCommoninfo,\r\n  listCommoninfo,\r\n  updateCommoninfo\r\n} from \"@/api/system/commoninfo\";\r\n\r\nexport default {\r\n  name: \"Commoninfo\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 通用信息表格数据\r\n      commoninfoList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        infoQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {}\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询通用信息列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listCommoninfo(this.queryParams).then(response => {\r\n        this.commoninfoList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        infoId: null,\r\n        infoShortName: null,\r\n        infoLocalName: null,\r\n        infoEnName: null,\r\n        infoDefault: null,\r\n        infoTypeId: null,\r\n        roleTypeId: null,\r\n        orderNum: null,\r\n        status: \"0\",\r\n        remark: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateBy: null,\r\n        updateTime: null,\r\n        deleteBy: null,\r\n        deleteTime: null,\r\n        deleteStatus: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.infoId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加通用信息\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const infoId = row.infoId || this.ids\r\n      getCommoninfo(infoId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改通用信息\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.infoId != null) {\r\n            updateCommoninfo(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addCommoninfo(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const infoIds = row.infoId || this.ids;\r\n      this.$confirm('是否确认删除通用信息编号为\"' + infoIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delCommoninfo(infoIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/commoninfo/export', {\r\n        ...this.queryParams\r\n      }, `commoninfo_${new Date().getTime()}.xlsx`)\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status == \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '\"\"' + row.infoLocalName + '\"吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.infoId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status == \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    getInfoType(val) {\r\n      this.form.infoTypeId = val\r\n    },\r\n    getRoleType(val) {\r\n      this.form.roleTypeId = val\r\n    },\r\n    getServiceTypeIds(val) {\r\n      this.form.serviceTypeIds = val\r\n    },\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;AAkKA,IAAAA,WAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eASA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;IACA;EACA;EACAC,KAAA;IACAX,UAAA,WAAAA,WAAAY,CAAA;MACA,IAAAA,CAAA;QACA,KAAAjB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAmB,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,eACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAApB,OAAA;MACA,IAAAqB,0BAAA,OAAAZ,WAAA,EAAAa,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAd,cAAA,GAAAiB,QAAA,CAAAC,IAAA;QACAJ,KAAA,CAAAf,KAAA,GAAAkB,QAAA,CAAAlB,KAAA;QACAe,KAAA,CAAApB,OAAA;MACA;IACA;IACA;IACAyB,MAAA,WAAAA,OAAA;MACA,KAAAjB,IAAA;MACA,KAAAkB,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAb,IAAA;QACAc,MAAA;QACAC,aAAA;QACAC,aAAA;QACAC,UAAA;QACAC,WAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,YAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAApC,WAAA,CAAAC,OAAA;MACA,KAAAQ,OAAA;IACA;IACA,aACA4B,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA;IACAE,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA/C,GAAA,GAAA+C,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAvB,MAAA;MAAA;MACA,KAAAzB,MAAA,GAAA8C,SAAA,CAAAG,MAAA;MACA,KAAAhD,QAAA,IAAA6C,SAAA,CAAAG,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAA1B,KAAA;MACA,KAAAlB,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACA8C,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAA7B,KAAA;MACA,IAAAC,MAAA,GAAA2B,GAAA,CAAA3B,MAAA,SAAA1B,GAAA;MACA,IAAAuD,yBAAA,EAAA7B,MAAA,EAAAL,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAA1C,IAAA,GAAAU,QAAA,CAAA1B,IAAA;QACA0D,MAAA,CAAA/C,IAAA;QACA+C,MAAA,CAAAhD,KAAA;MACA;IACA;IACA,WACAkD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA7C,IAAA,CAAAc,MAAA;YACA,IAAAmC,4BAAA,EAAAJ,MAAA,CAAA7C,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAAxC,OAAA;YACA;UACA;YACA,IAAA+C,yBAAA,EAAAP,MAAA,CAAA7C,IAAA,EAAAS,IAAA,WAAAC,QAAA;cACAmC,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAlD,IAAA;cACAkD,MAAA,CAAAxC,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAgD,YAAA,WAAAA,aAAAZ,GAAA;MAAA,IAAAa,MAAA;MACA,IAAAC,OAAA,GAAAd,GAAA,CAAA3B,MAAA,SAAA1B,GAAA;MACA,KAAAoE,QAAA,oBAAAD,OAAA;QAAAE,WAAA;MAAA,GAAAhD,IAAA;QACA,WAAAiD,yBAAA,EAAAH,OAAA;MACA,GAAA9C,IAAA;QACA6C,MAAA,CAAAjD,OAAA;QACAiD,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAQ,KAAA,cACA;IACA;IACA,aACAC,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iCAAAC,cAAA,CAAAC,OAAA,MACA,KAAAnE,WAAA,iBAAAoE,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAC,kBAAA,WAAAA,mBAAA1B,GAAA;MAAA,IAAA2B,MAAA;MACA,IAAAC,IAAA,GAAA5B,GAAA,CAAAnB,MAAA;MACA,KAAAkC,QAAA,UAAAa,IAAA,UAAA5B,GAAA,CAAAzB,aAAA;QAAAyC,WAAA;MAAA,GAAAhD,IAAA;QACA,WAAA6D,wBAAA,EAAA7B,GAAA,CAAA3B,MAAA,EAAA2B,GAAA,CAAAnB,MAAA;MACA,GAAAb,IAAA;QACA2D,MAAA,CAAAlB,MAAA,CAAAC,UAAA,CAAAkB,IAAA;MACA,GAAAV,KAAA;QACAlB,GAAA,CAAAnB,MAAA,GAAAmB,GAAA,CAAAnB,MAAA;MACA;IACA;IACAiD,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAAxE,IAAA,CAAAmB,UAAA,GAAAqD,GAAA;IACA;IACAC,WAAA,WAAAA,YAAAD,GAAA;MACA,KAAAxE,IAAA,CAAAoB,UAAA,GAAAoD,GAAA;IACA;IACAE,iBAAA,WAAAA,kBAAAF,GAAA;MACA,KAAAxE,IAAA,CAAA2E,cAAA,GAAAH,GAAA;IACA;EACA;AACA;AAAAI,OAAA,CAAAb,OAAA,GAAAc,QAAA"}]}