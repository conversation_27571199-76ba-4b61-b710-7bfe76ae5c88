{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Settings\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\layout\\components\\Settings\\index.vue", "mtime": 1754876882542}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmFA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/layout/components/Settings", "sourcesContent": ["<template>\r\n  <el-drawer :append-to-body=\"true\" :show-close=\"false\" :visible=\"visible\" :with-header=\"false\" size=\"280px\">\r\n    <div class=\"drawer-container\">\r\n      <div>\r\n        <div class=\"setting-drawer-content\">\r\n          <div class=\"setting-drawer-title\">\r\n            <h3 class=\"drawer-title\">主题风格设置</h3>\r\n          </div>\r\n          <div class=\"setting-drawer-block-checbox\">\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-dark')\">\r\n              <img alt=\"dark\" src=\"@/assets/images/dark.svg\">\r\n              <div v-if=\"sideTheme == 'theme-dark'\" class=\"setting-drawer-block-checbox-selectIcon\"\r\n                   style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg :fill=\"theme\" aria-hidden=\"true\" class=\"\" data-icon=\"check\" focusable=\"false\"\r\n                       height=\"1em\" viewBox=\"64 64 896 896\" width=\"1em\">\r\n                    <path\r\n                      d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n            <div class=\"setting-drawer-block-checbox-item\" @click=\"handleTheme('theme-light')\">\r\n              <img alt=\"light\" src=\"@/assets/images/light.svg\">\r\n              <div v-if=\"sideTheme == 'theme-light'\" class=\"setting-drawer-block-checbox-selectIcon\"\r\n                   style=\"display: block;\">\r\n                <i aria-label=\"图标: check\" class=\"anticon anticon-check\">\r\n                  <svg :fill=\"theme\" aria-hidden=\"true\" class=\"\" data-icon=\"check\" focusable=\"false\"\r\n                       height=\"1em\" viewBox=\"64 64 896 896\" width=\"1em\">\r\n                    <path\r\n                      d=\"M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z\"/>\r\n                  </svg>\r\n                </i>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <div class=\"drawer-item\">\r\n            <span>主题颜色</span>\r\n            <theme-picker style=\"float: right;height: 26px;margin: -3px 8px 0 0;\" @change=\"themeChange\"/>\r\n          </div>\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <h3 class=\"drawer-title\">系统布局配置</h3>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 TopNav</span>\r\n          <el-switch v-model=\"topNav\" class=\"drawer-switch\"/>\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>开启 Tags-Views</span>\r\n          <el-switch v-model=\"tagsView\" class=\"drawer-switch\"/>\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>固定 Header</span>\r\n          <el-switch v-model=\"fixedHeader\" class=\"drawer-switch\"/>\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>显示 Logo</span>\r\n          <el-switch v-model=\"sidebarLogo\" class=\"drawer-switch\"/>\r\n        </div>\r\n\r\n        <div class=\"drawer-item\">\r\n          <span>动态标题</span>\r\n          <el-switch v-model=\"dynamicTitle\" class=\"drawer-switch\"/>\r\n        </div>\r\n\r\n        <el-divider/>\r\n\r\n        <el-button icon=\"el-icon-document-add\" plain size=\"mini\" type=\"primary\" @click=\"saveSetting\">保存配置\r\n        </el-button>\r\n        <el-button icon=\"el-icon-refresh\" plain size=\"mini\" @click=\"resetSetting\">重置配置</el-button>\r\n      </div>\r\n    </div>\r\n  </el-drawer>\r\n</template>\r\n\r\n<script>\r\nimport ThemePicker from '@/components/ThemePicker'\r\n\r\nexport default {\r\n  components: {ThemePicker},\r\n  data() {\r\n    return {\r\n      theme: this.$store.state.settings.theme,\r\n      sideTheme: this.$store.state.settings.sideTheme\r\n    };\r\n  },\r\n  computed: {\r\n    visible: {\r\n      get() {\r\n        return this.$store.state.settings.showSettings\r\n      }\r\n    },\r\n    fixedHeader: {\r\n      get() {\r\n        return this.$store.state.settings.fixedHeader\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'fixedHeader',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    topNav: {\r\n      get() {\r\n        return this.$store.state.settings.topNav\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'topNav',\r\n          value: val\r\n        })\r\n        if (!val) {\r\n          this.$store.dispatch('app/toggleSideBarHide', false);\r\n          this.$store.commit(\"SET_SIDEBAR_ROUTERS\", this.$store.state.permission.defaultRoutes);\r\n        }\r\n      }\r\n    },\r\n    tagsView: {\r\n      get() {\r\n        return this.$store.state.settings.tagsView\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'tagsView',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    sidebarLogo: {\r\n      get() {\r\n        return this.$store.state.settings.sidebarLogo\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'sidebarLogo',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n    dynamicTitle: {\r\n      get() {\r\n        return this.$store.state.settings.dynamicTitle\r\n      },\r\n      set(val) {\r\n        this.$store.dispatch('settings/changeSetting', {\r\n          key: 'dynamicTitle',\r\n          value: val\r\n        })\r\n      }\r\n    },\r\n  },\r\n  methods: {\r\n    themeChange(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'theme',\r\n        value: val\r\n      })\r\n      this.theme = val;\r\n    },\r\n    handleTheme(val) {\r\n      this.$store.dispatch('settings/changeSetting', {\r\n        key: 'sideTheme',\r\n        value: val\r\n      })\r\n      this.sideTheme = val;\r\n    },\r\n    saveSetting() {\r\n      this.$modal.loading(\"正在保存到本地，请稍候...\");\r\n      this.$cache.local.set(\r\n        \"layout-setting\",\r\n        `{\r\n            \"topNav\":${this.topNav},\r\n            \"tagsView\":${this.tagsView},\r\n            \"fixedHeader\":${this.fixedHeader},\r\n            \"sidebarLogo\":${this.sidebarLogo},\r\n            \"dynamicTitle\":${this.dynamicTitle},\r\n            \"sideTheme\":\"${this.sideTheme}\",\r\n            \"theme\":\"${this.theme}\"\r\n          }`\r\n      );\r\n      setTimeout(this.$modal.closeLoading(), 1000)\r\n    },\r\n    resetSetting() {\r\n      this.$modal.loading(\"正在清除设置缓存并刷新，请稍候...\");\r\n      this.$cache.local.remove(\"layout-setting\")\r\n      setTimeout(\"window.location.reload()\", 1000)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.setting-drawer-content {\r\n  .setting-drawer-title {\r\n    margin-bottom: 12px;\r\n    color: rgba(0, 0, 0, .85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .setting-drawer-block-checbox {\r\n    display: flex;\r\n    justify-content: flex-start;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n    margin-bottom: 20px;\r\n\r\n    .setting-drawer-block-checbox-item {\r\n      position: relative;\r\n      margin-right: 16px;\r\n      border-radius: 2px;\r\n      cursor: pointer;\r\n\r\n      img {\r\n        width: 48px;\r\n        height: 48px;\r\n      }\r\n\r\n      .setting-drawer-block-checbox-selectIcon {\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        width: 100%;\r\n        height: 100%;\r\n        padding-top: 15px;\r\n        padding-left: 24px;\r\n        color: #1890ff;\r\n        font-weight: 700;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.drawer-container {\r\n  padding: 20px;\r\n  font-size: 14px;\r\n  line-height: 1.5;\r\n  word-wrap: break-word;\r\n\r\n  .drawer-title {\r\n    margin-bottom: 12px;\r\n    color: rgba(0, 0, 0, .85);\r\n    font-size: 14px;\r\n    line-height: 22px;\r\n  }\r\n\r\n  .drawer-item {\r\n    color: rgba(0, 0, 0, .65);\r\n    font-size: 14px;\r\n    padding: 12px 0;\r\n  }\r\n\r\n  .drawer-switch {\r\n    float: right\r\n  }\r\n}\r\n</style>\r\n"]}]}