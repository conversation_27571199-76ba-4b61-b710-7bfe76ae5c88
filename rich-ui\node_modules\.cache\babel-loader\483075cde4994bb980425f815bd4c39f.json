{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\debitNodeList.vue", "mtime": 1754899468841}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_currency", "_interopRequireDefault", "require", "_debitNoteChargeList", "_VatinvoiceDialog", "_vatInvoice", "_debitnote", "name", "components", "debitNoteChargeList", "VatinvoiceDialog", "props", "data", "loading", "expandedRows", "localDebitNoteList", "selectedDebitNotes", "invoiceDialogVisible", "currentDebitNote", "invoiceForm", "invoiceItems", "watch", "debitNoteList", "immediate", "deep", "handler", "newVal", "$emit", "computed", "methods", "handleSelectionChange", "selection", "debitNoteDisabled", "row", "billStatus", "disabled", "handleInvoiceStatusClick", "_this", "_asyncToGenerator2", "default", "_regeneratorRuntime2", "mark", "_callee", "response", "wrap", "_callee$", "_context", "prev", "next", "invoiceId", "getVatinvoice", "sent", "code", "prepareInvoiceDataWithExisting", "prepareInvoiceData", "t0", "console", "error", "stop", "generateInvoiceCodeNo", "rctId", "cooperatorId", "_callee2", "_callee2$", "_context2", "generateInvoiceCode", "abrupt", "msg", "debitNote", "_this2", "_callee3", "invoiceCodeNo", "invoiceCode", "_callee3$", "_context3", "clearingCompanyId", "sqdRctNo", "saleBuy", "isRecievingOrPaying", "taxClass", "invoiceType", "mergeInvoice", "invoiceOfficalNo", "invoiceBelongsTo", "companyBelongsTo", "richBankCode", "bankAccountCode", "cooperatorBankCode", "clearingCompanyBankAccount", "richCompanyTitle", "bankAccountName", "cooperatorCompanyTitle", "clearingCompanyName", "officalChargeNameSummary", "relatedOrderNo", "richVatSerialNo", "cooperatorVatSerialNo", "richBankFullname", "cooperatorBankFullname", "rich<PERSON><PERSON><PERSON><PERSON><PERSON>unt", "cooperator<PERSON><PERSON>k<PERSON><PERSON>unt", "invoiceRemark", "expectedPayDate", "expectedPaymentDate", "approvedPayDate", "actualPayDate", "actualPaymentDate", "invoiceExchangeRate", "invoiceCurrencyCode", "dnCurrencyCode", "invoiceNetAmount", "isReceivable", "billReceivable", "bill<PERSON><PERSON><PERSON>", "invoiceStatus", "belongsToMonth", "formatCurrentMonth", "rsChargeList", "map", "charge", "billNo", "rctNo", "serviceType", "chargeName", "remark", "paymentFlag", "quoteCurrency", "currencyCode", "unitPrice", "quantity", "unit", "settlementRate", "exchangeRate", "settlementCurrency", "taxRate", "taxIncludedTotal", "subtotal", "invoiceItemName", "taxCode", "existingInvoice", "length", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "concat", "handleInvoiceSubmit", "formData", "_this3", "_callee4", "updateData", "updateResponse", "_callee4$", "_context4", "updateVatinvoice", "addVatinvoice", "debitNoteId", "updateDebitNote", "$message", "success", "warn", "t1", "handleInvoiceCancel", "applyUnlock", "setComplete", "_this4", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "for<PERSON>ach", "isAccountConfirmed", "message", "catch", "changeCurrency", "currency", "selectBankAccount", "bankAccount", "handleSelectCompany", "company", "companyId", "companyShortName", "addDebitNote", "handleExpandChange", "createDebitNote", "_this5", "_callee5", "_callee5$", "_context5", "deleteDebitNote", "_this6", "_callee6", "_callee6$", "_context6", "handleChargeDataChange", "chargeData", "item", "add", "toString", "handleChargeSelection", "selected<PERSON><PERSON>ges", "index", "findIndex", "handleCopyFreight", "handleDeleteItem", "handleDeleteAll", "getBillStatusType", "status", "statusMap", "getBillStatusText", "getInvoiceStatusType", "getInvoiceStatusText", "getWriteoffStatusType", "getWriteoffStatusText", "exports", "_default"], "sources": ["src/views/system/document/debitNodeList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"debit-note-list\">\r\n    <el-table\r\n      ref=\"debitNoteTable\"\r\n      :data=\"debitNoteList\"\r\n      border\r\n      style=\"width: 100%\"\r\n      @expand-change=\"handleExpandChange\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <!-- 可展开列 -->\r\n      <el-table-column type=\"expand\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <!-- 嵌套 chargeList 组件 -->\r\n          <debit-note-charge-list\r\n            :charge-data=\"scope.row.rsChargeList\"\r\n            :company-list=\"companyList\"\r\n            :debit-note=\"scope.row\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            :hidden-supplier=\"hiddenSupplier\"\r\n            :is-receivable=\"isReceivable\"\r\n            :open-charge-list=\"true\"\r\n            @copyFreight=\"handleCopyFreight\"\r\n            @deleteAll=\"handleDeleteAll\"\r\n            @deleteItem=\"scope.row.rsChargeList = scope.row.rsChargeList.filter(charge => charge !== $event)\"\r\n            @return=\"handleChargeDataChange(scope.row, $event)\"\r\n            @selectRow=\"handleChargeSelection(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <!-- 勾选列 -->\r\n      <el-table-column align=\"center\" type=\"selection\"></el-table-column>\r\n\r\n      <!-- 分账单基本信息列 -->\r\n      <el-table-column label=\"所属公司\" prop=\"sqdRctNo\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"scope.row.companyBelongsTo\" :placeholder=\"'收付路径'\"\r\n                       :type=\"'rsPaymentTitle'\" @return=\"scope.row.companyBelongsTo=$event\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"我司账户\" prop=\"companyName\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.bankAccountCode\" :placeholder=\"'我司账户'\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       :type=\"'companyAccount'\"\r\n                       @return=\"scope.row.bankAccountCode=$event\" @returnData=\"selectBankAccount(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"收付标志\" prop=\"isRecievingOrPaying\" width=\"50\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getBillStatusType(scope.row.billStatus)\"\r\n            size=\"mini\"\r\n          >\r\n            {{ scope.row.isRecievingOrPaying == 0 ? \"收\" : \"付\" }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算单位\" prop=\"dnCurrencyCode\" width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :custom-options=\"companyList\" :flat=\"false\" :multiple=\"false\"\r\n                       :pass=\"scope.row.clearingCompanyId\" :placeholder=\"'客户'\"\r\n                       :type=\"'clientCustom'\" @return=\"scope.row.clearingCompanyId=$event\"\r\n                       :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       @returnData=\"handleSelectCompany(scope.row, $event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"对方账户\" prop=\"billReceivable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-input v-model=\"scope.row.clearingCompanyBankAccount\"\r\n                    :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n                    :disabled=\"debitNoteDisabled(scope.row)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"结算币种\" prop=\"billPayable\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <tree-select :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n                       :pass=\"scope.row.dnCurrencyCode\"\r\n                       :type=\"'currency'\"\r\n                       @return=\"changeCurrency(scope.row,$event)\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"right\" label=\"账单应收\" v-if=\"isReceivable\" prop=\"billReceivable\" width=\"80\"/>\r\n      <el-table-column align=\"right\" label=\"账单应付\" v-if=\"!isReceivable\" prop=\"billPayable\" width=\"80\"/>\r\n\r\n      <el-table-column align=\"center\" label=\"账单状态\" prop=\"billStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getBillStatusType(scope.row.billStatus)\"\r\n            size=\"mini\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          >\r\n            {{ getBillStatusText(scope.row.billStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"发票状态\" prop=\"invoiceStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getInvoiceStatusType(scope.row.invoiceStatus)\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n            size=\"mini\" @click=\"handleInvoiceStatusClick(scope.row)\"\r\n          >\r\n            {{ getInvoiceStatusText(scope.row.invoiceStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column label=\"申请支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.requestPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"预计支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.expectedPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"实际支付\" prop=\"invoiceStatus\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-date-picker\r\n            v-model=\"scope.row.actualPaymentDate\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\"\r\n            :disabled=\"debitNoteDisabled(scope.row)\"\r\n            placeholder=\"选择日期\" type=\"date\"\r\n          />\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column align=\"center\" label=\"销账状态\" prop=\"writeoffStatus\" width=\"60\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag\r\n            :type=\"getWriteoffStatusType(scope.row.writeoffStatus)\"\r\n            size=\"mini\"\r\n            :class=\"debitNoteDisabled(scope.row)?'disable-form':''\" :disabled=\"debitNoteDisabled(scope.row)\"\r\n          >\r\n            {{ getWriteoffStatusText(scope.row.writeoffStatus) }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n\r\n      <el-table-column class-name=\"small-padding fixed-width\" fixed=\"right\" label=\"操作\" width=\"130\">\r\n        <template slot-scope=\"scope\">\r\n          <div style=\"display: flex; gap: 4px;\">\r\n            <el-button\r\n              v-if=\"scope.row.billStatus==='confirmed'\"\r\n              icon=\"el-icon-unlock\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"applyUnlock(scope.row)\"\r\n            >\r\n              申请解锁\r\n            </el-button>\r\n            <el-button\r\n              v-if=\"scope.row.billStatus==='draft'\"\r\n              icon=\"el-icon-check\"\r\n              size=\"mini\"\r\n              type=\"success\"\r\n              @click=\"setComplete(scope.row)\"\r\n            >\r\n              设置完成\r\n            </el-button>\r\n            <el-button\r\n              :disabled=\"scope.row.rsChargeList.length>0\"\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"deleteDebitNote(scope.row)\"\r\n            >\r\n              删除\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-button :disabled=\"disabled\" style=\"padding: 0\"\r\n               type=\"text\"\r\n               @click=\"addDebitNote\"\r\n    >[＋]\r\n    </el-button>\r\n\r\n    <!-- 发票对话框 -->\r\n    <vatinvoice-dialog\r\n      :company-list=\"companyList\"\r\n      :form=\"invoiceForm\"\r\n      :invoice-items=\"invoiceItems\"\r\n      :title=\"'增值税发票管理'\"\r\n      :visible.sync=\"invoiceDialogVisible\"\r\n      @cancel=\"handleInvoiceCancel\"\r\n      @submit=\"handleInvoiceSubmit\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport currency from \"currency.js\"\r\nimport debitNoteChargeList from \"@/views/system/document/debitNoteChargeList.vue\"\r\nimport VatinvoiceDialog from \"@/views/system/vatinvoice/components/VatinvoiceDialog.vue\"\r\nimport {\r\n  addVatinvoice,\r\n  updateVatinvoice,\r\n  getVatinvoice,\r\n  countVatinvoiceByRctId,\r\n  generateInvoiceCode\r\n} from \"@/api/system/vatInvoice\"\r\nimport {updateDebitNote} from \"@/api/system/debitnote\"\r\n\r\nexport default {\r\n  name: \"debitNoteList\",\r\n  components: {debitNoteChargeList, VatinvoiceDialog},\r\n  props: [\r\n    \"companyList\",\r\n    \"disabled\",\r\n    \"hiddenSupplier\",\r\n    \"rctId\",\r\n    \"debitNoteList\",\r\n    \"isReceivable\"\r\n  ],\r\n  data() {\r\n    return {\r\n      loading: false,\r\n      expandedRows: [],\r\n      localDebitNoteList: [],\r\n      selectedDebitNotes: [],\r\n      invoiceDialogVisible: false,\r\n      currentDebitNote: null,\r\n      invoiceForm: {},\r\n      invoiceItems: []\r\n    }\r\n  },\r\n  watch: {\r\n    debitNoteList: {\r\n      immediate: true,\r\n      deep: true,\r\n      handler(newVal) {\r\n        this.$emit(\"update:debitNoteList\", newVal)\r\n        this.$emit(\"return\", newVal)\r\n      }\r\n    }\r\n  },\r\n  computed: {},\r\n  methods: {\r\n    // 处理表格选择变化\r\n    handleSelectionChange(selection) {\r\n      this.selectedDebitNotes = selection\r\n      this.$emit(\"selection-change\", selection)\r\n    },\r\n    debitNoteDisabled(row) {\r\n      return row.billStatus === \"confirmed\" || this.disabled\r\n    },\r\n    async handleInvoiceStatusClick(row) {\r\n      // 申请开票\r\n      this.currentDebitNote = row\r\n\r\n      // 检查是否有invoiceId\r\n      if (row.invoiceId) {\r\n        try {\r\n          // 根据invoiceId查找现有发票信息\r\n          const response = await getVatinvoice(row.invoiceId)\r\n          if (response.code === 200 && response.data) {\r\n            // 使用现有发票信息准备数据\r\n            this.prepareInvoiceDataWithExisting(row, response.data)\r\n          } else {\r\n            // 查找失败，使用默认数据\r\n            await this.prepareInvoiceData(row)\r\n          }\r\n        } catch (error) {\r\n          console.error(\"获取发票信息失败:\", error)\r\n          // 出错时使用默认数据\r\n          await this.prepareInvoiceData(row)\r\n        }\r\n      } else {\r\n        // 没有invoiceId，创建新的发票\r\n        await this.prepareInvoiceData(row)\r\n      }\r\n\r\n      this.invoiceDialogVisible = true\r\n    },\r\n\r\n    // 生成发票流水号\r\n    async generateInvoiceCodeNo(rctId, cooperatorId) {\r\n      try {\r\n        // 调用API生成发票编码\r\n        const response = await generateInvoiceCode(rctId, cooperatorId)\r\n        if (response.code === 200) {\r\n          return response.msg\r\n        }\r\n      } catch (error) {\r\n        console.error(\"生成发票编码失败:\", error)\r\n      }\r\n      // 如果API调用失败，返回默认格式\r\n      return \"\"\r\n    },\r\n\r\n    // 准备发票对话框数据（新建发票）\r\n    async prepareInvoiceData(debitNote) {\r\n      // 生成发票流水号\r\n      let invoiceCodeNo\r\n      if (this.rctId && debitNote.clearingCompanyId) {\r\n        let invoiceCode = await this.generateInvoiceCodeNo(this.rctId, debitNote.clearingCompanyId)\r\n        invoiceCodeNo = debitNote.sqdRctNo + \"-\" + invoiceCode\r\n      }\r\n\r\n      // 设置发票表单数据\r\n      this.invoiceForm = {\r\n        // 基本发票信息\r\n        invoiceId: debitNote.invoiceId || null, // 发票ID\r\n        invoiceCodeNo: invoiceCodeNo,\r\n        saleBuy: debitNote.isRecievingOrPaying == 0 ? \"sale\" : \"buy\", // 根据收付标志设置进销项\r\n        taxClass: \"\",\r\n        invoiceType: \"增值税发票\",\r\n        mergeInvoice: debitNote.mergeInvoice || \"0\",\r\n        invoiceOfficalNo: debitNote.invoiceOfficalNo || \"\",\r\n\r\n        // 公司和账户信息\r\n        invoiceBelongsTo: debitNote.companyBelongsTo || \"\",\r\n        richBankCode: debitNote.bankAccountCode || \"\",\r\n        cooperatorId: debitNote.clearingCompanyId || \"\",\r\n        cooperatorBankCode: debitNote.clearingCompanyBankAccount || \"\",\r\n        richCompanyTitle: debitNote.bankAccountName || \"\",\r\n        cooperatorCompanyTitle: debitNote.clearingCompanyName || \"\",\r\n\r\n        // 项目和订单信息\r\n        officalChargeNameSummary: \"\",\r\n        relatedOrderNo: \"\",\r\n\r\n        // 税号和银行信息\r\n        richVatSerialNo: \"\",\r\n        cooperatorVatSerialNo: \"\",\r\n        richBankFullname: \"\",\r\n        cooperatorBankFullname: \"\",\r\n        richBankAccount: \"\",\r\n        cooperatorBankAccount: debitNote.clearingCompanyBankAccount || \"\",\r\n\r\n        // 备注和日期信息\r\n        invoiceRemark: \"\",\r\n        expectedPayDate: debitNote.expectedPaymentDate || \"\",\r\n        approvedPayDate: \"\",\r\n        actualPayDate: debitNote.actualPaymentDate || \"\",\r\n\r\n        // 发票金额信息\r\n        invoiceExchangeRate: \"1\",\r\n        invoiceCurrencyCode: debitNote.dnCurrencyCode || \"RMB\",\r\n        invoiceNetAmount: debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable,\r\n        invoiceStatus: debitNote.invoiceStatus === \"issued\" ? \"1\" : \"0\",\r\n        belongsToMonth: this.formatCurrentMonth(),\r\n\r\n        // RCT关联信息\r\n        rctId: this.rctId || null\r\n      }\r\n\r\n      // 准备发票明细项\r\n      this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {\r\n        return {\r\n          billNo: debitNote.billNo || \"\",\r\n          rctNo: \"\",  // 这里可能需要从父组件获取\r\n          serviceType: charge.chargeName || \"\",\r\n          chargeName: charge.chargeName || \"\",\r\n          remark: charge.remark || \"\",\r\n          paymentFlag: debitNote.isRecievingOrPaying == 0 ? \"收\" : \"付\",\r\n          quoteCurrency: charge.currencyCode || \"\",\r\n          unitPrice: charge.unitPrice || 0,\r\n          quantity: charge.quantity || 0,\r\n          unit: charge.unit || \"\",\r\n          settlementRate: charge.exchangeRate || 1,\r\n          settlementCurrency: debitNote.dnCurrencyCode || \"\",\r\n          taxRate: charge.taxRate || \"\",\r\n          taxIncludedTotal: charge.subtotal || 0,\r\n          invoiceItemName: charge.chargeName || \"\",\r\n          taxCode: \"\"\r\n        }\r\n      }) : []\r\n    },\r\n\r\n    // 准备发票对话框数据（使用现有发票信息）\r\n    prepareInvoiceDataWithExisting(debitNote, existingInvoice) {\r\n      // 使用现有发票信息设置表单数据\r\n      this.invoiceForm = {\r\n        // 基本发票信息\r\n        invoiceId: existingInvoice.invoiceId || debitNote.invoiceId || null,\r\n        invoiceCodeNo: existingInvoice.invoiceCodeNo || debitNote.invoiceCodeNo || \"\",\r\n        saleBuy: existingInvoice.saleBuy || (debitNote.isRecievingOrPaying == 0 ? \"sale\" : \"buy\"),\r\n        taxClass: existingInvoice.taxClass || \"\",\r\n        invoiceType: existingInvoice.invoiceType || \"增值税发票\",\r\n        mergeInvoice: existingInvoice.mergeInvoice || debitNote.mergeInvoice || \"0\",\r\n        invoiceOfficalNo: existingInvoice.invoiceOfficalNo || debitNote.invoiceOfficalNo || \"\",\r\n\r\n        // 公司和账户信息\r\n        invoiceBelongsTo: existingInvoice.invoiceBelongsTo || debitNote.companyBelongsTo || \"\",\r\n        richBankCode: existingInvoice.richBankCode || debitNote.bankAccountCode || \"\",\r\n        cooperatorId: existingInvoice.cooperatorId || debitNote.clearingCompanyId || \"\",\r\n        cooperatorBankCode: existingInvoice.cooperatorBankCode || debitNote.clearingCompanyBankAccount || \"\",\r\n        richCompanyTitle: existingInvoice.richCompanyTitle || debitNote.bankAccountName || \"\",\r\n        cooperatorCompanyTitle: existingInvoice.cooperatorCompanyTitle || debitNote.clearingCompanyName || \"\",\r\n\r\n        // 项目和订单信息\r\n        officalChargeNameSummary: existingInvoice.officalChargeNameSummary || \"\",\r\n        relatedOrderNo: existingInvoice.relatedOrderNo || \"\",\r\n\r\n        // 税号和银行信息\r\n        richVatSerialNo: existingInvoice.richVatSerialNo || \"\",\r\n        cooperatorVatSerialNo: existingInvoice.cooperatorVatSerialNo || \"\",\r\n        richBankFullname: existingInvoice.richBankFullname || \"\",\r\n        cooperatorBankFullname: existingInvoice.cooperatorBankFullname || \"\",\r\n        richBankAccount: existingInvoice.richBankAccount || \"\",\r\n        cooperatorBankAccount: existingInvoice.cooperatorBankAccount || debitNote.clearingCompanyBankAccount || \"\",\r\n\r\n        // 备注和日期信息\r\n        invoiceRemark: existingInvoice.invoiceRemark || \"\",\r\n        expectedPayDate: existingInvoice.expectedPayDate || debitNote.expectedPaymentDate || \"\",\r\n        approvedPayDate: existingInvoice.approvedPayDate || \"\",\r\n        actualPayDate: existingInvoice.actualPayDate || debitNote.actualPaymentDate || \"\",\r\n\r\n        // 发票金额信息\r\n        invoiceExchangeRate: existingInvoice.invoiceExchangeRate || \"1\",\r\n        invoiceCurrencyCode: existingInvoice.invoiceCurrencyCode || debitNote.dnCurrencyCode || \"RMB\",\r\n        invoiceNetAmount: existingInvoice.invoiceNetAmount || (debitNote.isReceivable ? debitNote.billReceivable : debitNote.billPayable),\r\n        invoiceStatus: existingInvoice.invoiceStatus || (debitNote.invoiceStatus === \"issued\" ? \"1\" : \"0\"),\r\n        belongsToMonth: existingInvoice.belongsToMonth || this.formatCurrentMonth(),\r\n\r\n        // RCT关联信息\r\n        rctId: existingInvoice.rctId || this.rctId || null\r\n      }\r\n\r\n      // 使用现有发票明细项，如果没有则使用debitNote的费用明细\r\n      if (existingInvoice.invoiceItems && existingInvoice.invoiceItems.length > 0) {\r\n        this.invoiceItems = existingInvoice.invoiceItems\r\n      } else {\r\n        // 准备发票明细项\r\n        this.invoiceItems = debitNote.rsChargeList ? debitNote.rsChargeList.map(charge => {\r\n          return {\r\n            billNo: debitNote.billNo || \"\",\r\n            rctNo: \"\",  // 这里可能需要从父组件获取\r\n            serviceType: charge.chargeName || \"\",\r\n            chargeName: charge.chargeName || \"\",\r\n            remark: charge.remark || \"\",\r\n            paymentFlag: debitNote.isRecievingOrPaying == 0 ? \"收\" : \"付\",\r\n            quoteCurrency: charge.currencyCode || \"\",\r\n            unitPrice: charge.unitPrice || 0,\r\n            quantity: charge.quantity || 0,\r\n            unit: charge.unit || \"\",\r\n            settlementRate: charge.exchangeRate || 1,\r\n            settlementCurrency: debitNote.dnCurrencyCode || \"\",\r\n            taxRate: charge.taxRate || \"\",\r\n            taxIncludedTotal: charge.subtotal || 0,\r\n            invoiceItemName: charge.chargeName || \"\",\r\n            taxCode: \"\"\r\n          }\r\n        }) : []\r\n      }\r\n    },\r\n\r\n    // 格式化当前月份为 yyyyMM 格式（如 202503）\r\n    formatCurrentMonth() {\r\n      const date = new Date()\r\n      const year = date.getFullYear()\r\n      const month = String(date.getMonth() + 1).padStart(2, \"0\")\r\n      return `${year}${month}`\r\n    },\r\n\r\n    // 处理发票对话框提交\r\n    async handleInvoiceSubmit(formData) {\r\n      try {\r\n        let response\r\n\r\n        // 根据是否有invoiceId决定是新增还是修改\r\n        if (formData.invoiceId) {\r\n          // 修改发票\r\n          response = await updateVatinvoice(formData)\r\n        } else {\r\n          // 新增发票\r\n          response = await addVatinvoice(formData)\r\n        }\r\n\r\n        if (response.code === 200) {\r\n          // 更新发票状态\r\n          if (this.currentDebitNote) {\r\n            this.currentDebitNote.invoiceStatus = formData.invoiceStatus === \"1\" ? \"issued\" : \"unissued\"\r\n\r\n            // 将发票ID写入到debitNote中\r\n            let invoiceId = null\r\n            if (response.data && response.data.invoiceId) {\r\n              invoiceId = response.data.invoiceId\r\n              this.currentDebitNote.invoiceId = invoiceId\r\n            } else if (formData.invoiceId) {\r\n              invoiceId = formData.invoiceId\r\n              this.currentDebitNote.invoiceId = invoiceId\r\n            }\r\n\r\n            // 更新发票相关字段\r\n            this.currentDebitNote.invoiceCodeNo = formData.invoiceCodeNo || \"\"\r\n            this.currentDebitNote.invoiceOfficalNo = formData.invoiceOfficalNo || \"\"\r\n            this.currentDebitNote.invoiceType = formData.invoiceType || \"\"\r\n            this.currentDebitNote.mergeInvoice = formData.mergeInvoice || \"0\"\r\n            // 更新分账单的发票状态为已申请\r\n            this.currentDebitNote.invoiceStatus = \"applied\"\r\n\r\n            // 调用接口更新debitNote中的invoiceId\r\n            if (invoiceId && this.currentDebitNote.debitNoteId) {\r\n              try {\r\n                const updateData = {\r\n                  debitNoteId: this.currentDebitNote.debitNoteId,\r\n                  invoiceId: invoiceId,\r\n                  invoiceCodeNo: formData.invoiceCodeNo || \"\",\r\n                  invoiceOfficalNo: formData.invoiceOfficalNo || \"\",\r\n                  invoiceType: formData.invoiceType || \"\",\r\n                  mergeInvoice: formData.mergeInvoice || \"0\",\r\n                  invoiceStatus: \"applied\"\r\n                }\r\n\r\n                // 更新分账单\r\n                const updateResponse = await updateDebitNote(updateData)\r\n                if (updateResponse.code === 200) {\r\n                  this.$message.success(\"发票信息已更新到分账单\")\r\n                } else {\r\n                  console.warn(\"更新分账单发票信息失败:\", updateResponse.msg)\r\n                }\r\n              } catch (updateError) {\r\n                console.error(\"更新分账单发票信息失败:\", updateError)\r\n              }\r\n            }\r\n\r\n            // 通知父组件数据变化\r\n            this.$emit(\"return\", this.debitNoteList)\r\n          }\r\n\r\n          this.$message.success(\"发票保存成功\")\r\n        } else {\r\n          this.$message.error(response.msg || \"发票保存失败\")\r\n        }\r\n      } catch (error) {\r\n        console.error(\"发票保存失败:\", error)\r\n        this.$message.error(\"发票保存失败，请重试\")\r\n      }\r\n\r\n      this.invoiceDialogVisible = false\r\n    },\r\n\r\n    // 处理发票对话框取消\r\n    handleInvoiceCancel() {\r\n      this.invoiceDialogVisible = false\r\n    },\r\n    applyUnlock(row) {\r\n      // 查看发票状态,已开票的不能申请解锁\r\n      if (row.invoiceStatus === \"issued\") {\r\n        this.$message.error(\"已开票的分账单不能申请解锁\")\r\n        return\r\n      }\r\n\r\n      this.$emit(\"applyUnlock\", row)\r\n    },\r\n    setComplete(row) {\r\n      try {\r\n        this.$confirm(\"确定要将该分账单设置为已确认状态吗？此操作将禁用整条数据及其费用明细。\", \"提示\", {\r\n          confirmButtonText: \"确定\",\r\n          cancelButtonText: \"取消\",\r\n          type: \"warning\"\r\n        }).then(() => {\r\n          // 修改状态为已确认\r\n          row.billStatus = \"confirmed\"\r\n\r\n          // 设置所有费用明细的isAccountConfirmed为'1'，使其被禁用\r\n          if (row.rsChargeList && row.rsChargeList.length > 0) {\r\n            row.rsChargeList.forEach(charge => {\r\n              charge.isAccountConfirmed = \"1\"\r\n            })\r\n          }\r\n\r\n          // 通知父组件状态变更\r\n          this.$emit(\"setComplete\", row)\r\n\r\n          // 提示用户\r\n          this.$message({\r\n            type: \"success\",\r\n            message: \"分账单已设置为已确认状态\"\r\n          })\r\n        }).catch(() => {\r\n          // 用户取消操作\r\n        })\r\n      } catch (error) {\r\n        console.error(\"设置分账单状态失败:\", error)\r\n        this.$message.error(\"设置分账单状态失败\")\r\n      }\r\n    },\r\n    changeCurrency(row, currency) {\r\n      row.dnCurrencyCode = currency\r\n    },\r\n    selectBankAccount(row, bankAccount) {\r\n      row.bankAccountCode = bankAccount.bankAccountCode\r\n      row.bankAccountName = bankAccount.bankAccountName\r\n    },\r\n    handleSelectCompany(row, company) {\r\n      row.clearingCompanyId = company.companyId\r\n      row.clearingCompanyName = company.companyShortName\r\n    },\r\n    addDebitNote() {\r\n      this.$emit(\"addDebitNote\")\r\n    },\r\n    currency,\r\n\r\n    // 展开/收起行\r\n    handleExpandChange(row, expandedRows) {\r\n      this.expandedRows = expandedRows\r\n    },\r\n\r\n    // 创建分账单\r\n    async createDebitNote(row) {\r\n      try {\r\n\r\n      } catch (error) {\r\n        console.error(\"创建分账单失败:\", error)\r\n        this.$message.error(\"创建分账单失败\")\r\n      }\r\n    },\r\n    // 删除分账单\r\n    async deleteDebitNote(row) {\r\n      try {\r\n        await this.$confirm(\"确定要删除该分账单吗？\", \"提示\", {\r\n          type: \"warning\"\r\n        })\r\n        this.$emit(\"deleteItem\", row)\r\n      } catch (error) {\r\n        if (error !== \"cancel\") {\r\n          console.error(\"删除分账单失败:\", error)\r\n          this.$message.error(\"删除分账单失败\")\r\n        }\r\n      }\r\n    },\r\n\r\n    // 处理费用数据变化\r\n    handleChargeDataChange(row, chargeData) {\r\n      let billReceivable = 0\r\n      let billPayable = 0\r\n\r\n      // 统计chargeData的费用\r\n      if (this.isReceivable) {\r\n        // 应收\r\n        chargeData.forEach(item => {\r\n          // 使用currency.js计算\r\n          billReceivable = currency(billReceivable).add(item.subtotal).toString()\r\n        })\r\n      } else {\r\n        // 应付\r\n        chargeData.forEach(item => {\r\n          billPayable = currency(billPayable).add(item.subtotal).toString()\r\n        })\r\n      }\r\n      row.billReceivable = billReceivable\r\n      row.billPayable = billPayable\r\n\r\n      // 通知父组件数据变化\r\n      this.$emit(\"return\", this.debitNoteList)\r\n    },\r\n\r\n    // 处理费用选择\r\n    handleChargeSelection(row, selectedCharges) {\r\n      const index = this.localDebitNoteList.findIndex(item => item === row)\r\n      if (index !== -1) {\r\n        this.localDebitNoteList[index].selectedCharges = selectedCharges\r\n        // 通知父组件数据变化\r\n        this.$emit(\"update:debitNoteList\", this.localDebitNoteList)\r\n      }\r\n    },\r\n\r\n    // 复制费用\r\n    handleCopyFreight(charge) {\r\n      this.$emit(\"copyFreight\", charge)\r\n    },\r\n\r\n    // 删除费用项\r\n    handleDeleteItem(charge) {\r\n      this.$emit(\"deleteItem\", charge)\r\n    },\r\n\r\n    // 删除所有费用\r\n    handleDeleteAll() {\r\n      this.$emit(\"deleteAll\")\r\n    },\r\n\r\n    // 获取账单状态类型\r\n    getBillStatusType(status) {\r\n      const statusMap = {\r\n        \"draft\": \"info\",\r\n        \"confirmed\": \"success\",\r\n        \"closed\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取账单状态文本\r\n    getBillStatusText(status) {\r\n      const statusMap = {\r\n        \"draft\": \"草稿\",\r\n        \"confirmed\": \"已确认\",\r\n        \"closed\": \"已关闭\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取发票状态类型\r\n    getInvoiceStatusType(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"info\",\r\n        \"issued\": \"success\",\r\n        \"applied\": \"warning\",\r\n        \"canceled\": \"danger\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取发票状态文本\r\n    getInvoiceStatusText(status) {\r\n      const statusMap = {\r\n        \"unissued\": \"未开票\",\r\n        \"issued\": \"已开票\",\r\n        \"applied\": \"已申请\",\r\n        \"canceled\": \"已作废\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    },\r\n\r\n    // 获取销账状态类型\r\n    getWriteoffStatusType(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"info\",\r\n        \"partial\": \"warning\",\r\n        \"written\": \"success\"\r\n      }\r\n      return statusMap[status] || \"info\"\r\n    },\r\n\r\n    // 获取销账状态文本\r\n    getWriteoffStatusText(status) {\r\n      const statusMap = {\r\n        \"unwritten\": \"未销账\",\r\n        \"partial\": \"部分销账\",\r\n        \"written\": \"已销账\"\r\n      }\r\n      return statusMap[status] || \"未知\"\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.expand-content {\r\n  padding: 20px;\r\n  background-color: #f9f9f9;\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  margin: 10px 0;\r\n}\r\n\r\n.charge-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n\r\n  span {\r\n    font-weight: bold;\r\n    font-size: 16px;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\ninput:focus {\r\n  outline: none;\r\n}\r\n\r\n.unHighlight-text {\r\n  color: #b7bbc2;\r\n  margin: 0;\r\n}\r\n\r\n// 覆盖 Element UI 表格样式\r\n:deep(.el-table) {\r\n  .el-table__expanded-cell {\r\n    padding: 0;\r\n\r\n    .expand-content {\r\n      margin: 0;\r\n      border: none;\r\n      background-color: transparent;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AA4NA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,iBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AAOA,IAAAI,UAAA,GAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,IAAA;EACAC,UAAA;IAAAC,mBAAA,EAAAA,4BAAA;IAAAC,gBAAA,EAAAA;EAAA;EACAC,KAAA,GACA,eACA,YACA,kBACA,SACA,iBACA,eACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,YAAA;MACAC,kBAAA;MACAC,kBAAA;MACAC,oBAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,YAAA;IACA;EACA;EACAC,KAAA;IACAC,aAAA;MACAC,SAAA;MACAC,IAAA;MACAC,OAAA,WAAAA,QAAAC,MAAA;QACA,KAAAC,KAAA,yBAAAD,MAAA;QACA,KAAAC,KAAA,WAAAD,MAAA;MACA;IACA;EACA;EACAE,QAAA;EACAC,OAAA;IACA;IACAC,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAAf,kBAAA,GAAAe,SAAA;MACA,KAAAJ,KAAA,qBAAAI,SAAA;IACA;IACAC,iBAAA,WAAAA,kBAAAC,GAAA;MACA,OAAAA,GAAA,CAAAC,UAAA,yBAAAC,QAAA;IACA;IACAC,wBAAA,WAAAA,yBAAAH,GAAA;MAAA,IAAAI,KAAA;MAAA,WAAAC,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAC,QAAA;QAAA,IAAAC,QAAA;QAAA,WAAAH,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACA;cACAX,KAAA,CAAAnB,gBAAA,GAAAe,GAAA;;cAEA;cAAA,KACAA,GAAA,CAAAgB,SAAA;gBAAAH,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAGA,IAAAE,yBAAA,EAAAjB,GAAA,CAAAgB,SAAA;YAAA;cAAAN,QAAA,GAAAG,QAAA,CAAAK,IAAA;cAAA,MACAR,QAAA,CAAAS,IAAA,YAAAT,QAAA,CAAA/B,IAAA;gBAAAkC,QAAA,CAAAE,IAAA;gBAAA;cAAA;cACA;cACAX,KAAA,CAAAgB,8BAAA,CAAApB,GAAA,EAAAU,QAAA,CAAA/B,IAAA;cAAAkC,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAGAX,KAAA,CAAAiB,kBAAA,CAAArB,GAAA;YAAA;cAAAa,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAS,EAAA,GAAAT,QAAA;cAGAU,OAAA,CAAAC,KAAA,cAAAX,QAAA,CAAAS,EAAA;cACA;cAAAT,QAAA,CAAAE,IAAA;cAAA,OACAX,KAAA,CAAAiB,kBAAA,CAAArB,GAAA;YAAA;cAAAa,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA,OAIAX,KAAA,CAAAiB,kBAAA,CAAArB,GAAA;YAAA;cAGAI,KAAA,CAAApB,oBAAA;YAAA;YAAA;cAAA,OAAA6B,QAAA,CAAAY,IAAA;UAAA;QAAA,GAAAhB,OAAA;MAAA;IACA;IAEA;IACAiB,qBAAA,WAAAA,sBAAAC,KAAA,EAAAC,YAAA;MAAA,WAAAvB,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAqB,SAAA;QAAA,IAAAnB,QAAA;QAAA,WAAAH,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAmB,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjB,IAAA,GAAAiB,SAAA,CAAAhB,IAAA;YAAA;cAAAgB,SAAA,CAAAjB,IAAA;cAAAiB,SAAA,CAAAhB,IAAA;cAAA,OAGA,IAAAiB,+BAAA,EAAAL,KAAA,EAAAC,YAAA;YAAA;cAAAlB,QAAA,GAAAqB,SAAA,CAAAb,IAAA;cAAA,MACAR,QAAA,CAAAS,IAAA;gBAAAY,SAAA,CAAAhB,IAAA;gBAAA;cAAA;cAAA,OAAAgB,SAAA,CAAAE,MAAA,WACAvB,QAAA,CAAAwB,GAAA;YAAA;cAAAH,SAAA,CAAAhB,IAAA;cAAA;YAAA;cAAAgB,SAAA,CAAAjB,IAAA;cAAAiB,SAAA,CAAAT,EAAA,GAAAS,SAAA;cAGAR,OAAA,CAAAC,KAAA,cAAAO,SAAA,CAAAT,EAAA;YAAA;cAAA,OAAAS,SAAA,CAAAE,MAAA,WAGA;YAAA;YAAA;cAAA,OAAAF,SAAA,CAAAN,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA;IACA;IAEA;IACAR,kBAAA,WAAAA,mBAAAc,SAAA;MAAA,IAAAC,MAAA;MAAA,WAAA/B,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA6B,SAAA;QAAA,IAAAC,aAAA,EAAAC,WAAA;QAAA,WAAAhC,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAA6B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3B,IAAA,GAAA2B,SAAA,CAAA1B,IAAA;YAAA;cAAA,MAGAqB,MAAA,CAAAT,KAAA,IAAAQ,SAAA,CAAAO,iBAAA;gBAAAD,SAAA,CAAA1B,IAAA;gBAAA;cAAA;cAAA0B,SAAA,CAAA1B,IAAA;cAAA,OACAqB,MAAA,CAAAV,qBAAA,CAAAU,MAAA,CAAAT,KAAA,EAAAQ,SAAA,CAAAO,iBAAA;YAAA;cAAAH,WAAA,GAAAE,SAAA,CAAAvB,IAAA;cACAoB,aAAA,GAAAH,SAAA,CAAAQ,QAAA,SAAAJ,WAAA;YAAA;cAGA;cACAH,MAAA,CAAAlD,WAAA;gBACA;gBACA8B,SAAA,EAAAmB,SAAA,CAAAnB,SAAA;gBAAA;gBACAsB,aAAA,EAAAA,aAAA;gBACAM,OAAA,EAAAT,SAAA,CAAAU,mBAAA;gBAAA;gBACAC,QAAA;gBACAC,WAAA;gBACAC,YAAA,EAAAb,SAAA,CAAAa,YAAA;gBACAC,gBAAA,EAAAd,SAAA,CAAAc,gBAAA;gBAEA;gBACAC,gBAAA,EAAAf,SAAA,CAAAgB,gBAAA;gBACAC,YAAA,EAAAjB,SAAA,CAAAkB,eAAA;gBACAzB,YAAA,EAAAO,SAAA,CAAAO,iBAAA;gBACAY,kBAAA,EAAAnB,SAAA,CAAAoB,0BAAA;gBACAC,gBAAA,EAAArB,SAAA,CAAAsB,eAAA;gBACAC,sBAAA,EAAAvB,SAAA,CAAAwB,mBAAA;gBAEA;gBACAC,wBAAA;gBACAC,cAAA;gBAEA;gBACAC,eAAA;gBACAC,qBAAA;gBACAC,gBAAA;gBACAC,sBAAA;gBACAC,eAAA;gBACAC,qBAAA,EAAAhC,SAAA,CAAAoB,0BAAA;gBAEA;gBACAa,aAAA;gBACAC,eAAA,EAAAlC,SAAA,CAAAmC,mBAAA;gBACAC,eAAA;gBACAC,aAAA,EAAArC,SAAA,CAAAsC,iBAAA;gBAEA;gBACAC,mBAAA;gBACAC,mBAAA,EAAAxC,SAAA,CAAAyC,cAAA;gBACAC,gBAAA,EAAA1C,SAAA,CAAA2C,YAAA,GAAA3C,SAAA,CAAA4C,cAAA,GAAA5C,SAAA,CAAA6C,WAAA;gBACAC,aAAA,EAAA9C,SAAA,CAAA8C,aAAA;gBACAC,cAAA,EAAA9C,MAAA,CAAA+C,kBAAA;gBAEA;gBACAxD,KAAA,EAAAS,MAAA,CAAAT,KAAA;cACA;;cAEA;cACAS,MAAA,CAAAjD,YAAA,GAAAgD,SAAA,CAAAiD,YAAA,GAAAjD,SAAA,CAAAiD,YAAA,CAAAC,GAAA,WAAAC,MAAA;gBACA;kBACAC,MAAA,EAAApD,SAAA,CAAAoD,MAAA;kBACAC,KAAA;kBAAA;kBACAC,WAAA,EAAAH,MAAA,CAAAI,UAAA;kBACAA,UAAA,EAAAJ,MAAA,CAAAI,UAAA;kBACAC,MAAA,EAAAL,MAAA,CAAAK,MAAA;kBACAC,WAAA,EAAAzD,SAAA,CAAAU,mBAAA;kBACAgD,aAAA,EAAAP,MAAA,CAAAQ,YAAA;kBACAC,SAAA,EAAAT,MAAA,CAAAS,SAAA;kBACAC,QAAA,EAAAV,MAAA,CAAAU,QAAA;kBACAC,IAAA,EAAAX,MAAA,CAAAW,IAAA;kBACAC,cAAA,EAAAZ,MAAA,CAAAa,YAAA;kBACAC,kBAAA,EAAAjE,SAAA,CAAAyC,cAAA;kBACAyB,OAAA,EAAAf,MAAA,CAAAe,OAAA;kBACAC,gBAAA,EAAAhB,MAAA,CAAAiB,QAAA;kBACAC,eAAA,EAAAlB,MAAA,CAAAI,UAAA;kBACAe,OAAA;gBACA;cACA;YAAA;YAAA;cAAA,OAAAhE,SAAA,CAAAhB,IAAA;UAAA;QAAA,GAAAY,QAAA;MAAA;IACA;IAEA;IACAjB,8BAAA,WAAAA,+BAAAe,SAAA,EAAAuE,eAAA;MACA;MACA,KAAAxH,WAAA;QACA;QACA8B,SAAA,EAAA0F,eAAA,CAAA1F,SAAA,IAAAmB,SAAA,CAAAnB,SAAA;QACAsB,aAAA,EAAAoE,eAAA,CAAApE,aAAA,IAAAH,SAAA,CAAAG,aAAA;QACAM,OAAA,EAAA8D,eAAA,CAAA9D,OAAA,KAAAT,SAAA,CAAAU,mBAAA;QACAC,QAAA,EAAA4D,eAAA,CAAA5D,QAAA;QACAC,WAAA,EAAA2D,eAAA,CAAA3D,WAAA;QACAC,YAAA,EAAA0D,eAAA,CAAA1D,YAAA,IAAAb,SAAA,CAAAa,YAAA;QACAC,gBAAA,EAAAyD,eAAA,CAAAzD,gBAAA,IAAAd,SAAA,CAAAc,gBAAA;QAEA;QACAC,gBAAA,EAAAwD,eAAA,CAAAxD,gBAAA,IAAAf,SAAA,CAAAgB,gBAAA;QACAC,YAAA,EAAAsD,eAAA,CAAAtD,YAAA,IAAAjB,SAAA,CAAAkB,eAAA;QACAzB,YAAA,EAAA8E,eAAA,CAAA9E,YAAA,IAAAO,SAAA,CAAAO,iBAAA;QACAY,kBAAA,EAAAoD,eAAA,CAAApD,kBAAA,IAAAnB,SAAA,CAAAoB,0BAAA;QACAC,gBAAA,EAAAkD,eAAA,CAAAlD,gBAAA,IAAArB,SAAA,CAAAsB,eAAA;QACAC,sBAAA,EAAAgD,eAAA,CAAAhD,sBAAA,IAAAvB,SAAA,CAAAwB,mBAAA;QAEA;QACAC,wBAAA,EAAA8C,eAAA,CAAA9C,wBAAA;QACAC,cAAA,EAAA6C,eAAA,CAAA7C,cAAA;QAEA;QACAC,eAAA,EAAA4C,eAAA,CAAA5C,eAAA;QACAC,qBAAA,EAAA2C,eAAA,CAAA3C,qBAAA;QACAC,gBAAA,EAAA0C,eAAA,CAAA1C,gBAAA;QACAC,sBAAA,EAAAyC,eAAA,CAAAzC,sBAAA;QACAC,eAAA,EAAAwC,eAAA,CAAAxC,eAAA;QACAC,qBAAA,EAAAuC,eAAA,CAAAvC,qBAAA,IAAAhC,SAAA,CAAAoB,0BAAA;QAEA;QACAa,aAAA,EAAAsC,eAAA,CAAAtC,aAAA;QACAC,eAAA,EAAAqC,eAAA,CAAArC,eAAA,IAAAlC,SAAA,CAAAmC,mBAAA;QACAC,eAAA,EAAAmC,eAAA,CAAAnC,eAAA;QACAC,aAAA,EAAAkC,eAAA,CAAAlC,aAAA,IAAArC,SAAA,CAAAsC,iBAAA;QAEA;QACAC,mBAAA,EAAAgC,eAAA,CAAAhC,mBAAA;QACAC,mBAAA,EAAA+B,eAAA,CAAA/B,mBAAA,IAAAxC,SAAA,CAAAyC,cAAA;QACAC,gBAAA,EAAA6B,eAAA,CAAA7B,gBAAA,KAAA1C,SAAA,CAAA2C,YAAA,GAAA3C,SAAA,CAAA4C,cAAA,GAAA5C,SAAA,CAAA6C,WAAA;QACAC,aAAA,EAAAyB,eAAA,CAAAzB,aAAA,KAAA9C,SAAA,CAAA8C,aAAA;QACAC,cAAA,EAAAwB,eAAA,CAAAxB,cAAA,SAAAC,kBAAA;QAEA;QACAxD,KAAA,EAAA+E,eAAA,CAAA/E,KAAA,SAAAA,KAAA;MACA;;MAEA;MACA,IAAA+E,eAAA,CAAAvH,YAAA,IAAAuH,eAAA,CAAAvH,YAAA,CAAAwH,MAAA;QACA,KAAAxH,YAAA,GAAAuH,eAAA,CAAAvH,YAAA;MACA;QACA;QACA,KAAAA,YAAA,GAAAgD,SAAA,CAAAiD,YAAA,GAAAjD,SAAA,CAAAiD,YAAA,CAAAC,GAAA,WAAAC,MAAA;UACA;YACAC,MAAA,EAAApD,SAAA,CAAAoD,MAAA;YACAC,KAAA;YAAA;YACAC,WAAA,EAAAH,MAAA,CAAAI,UAAA;YACAA,UAAA,EAAAJ,MAAA,CAAAI,UAAA;YACAC,MAAA,EAAAL,MAAA,CAAAK,MAAA;YACAC,WAAA,EAAAzD,SAAA,CAAAU,mBAAA;YACAgD,aAAA,EAAAP,MAAA,CAAAQ,YAAA;YACAC,SAAA,EAAAT,MAAA,CAAAS,SAAA;YACAC,QAAA,EAAAV,MAAA,CAAAU,QAAA;YACAC,IAAA,EAAAX,MAAA,CAAAW,IAAA;YACAC,cAAA,EAAAZ,MAAA,CAAAa,YAAA;YACAC,kBAAA,EAAAjE,SAAA,CAAAyC,cAAA;YACAyB,OAAA,EAAAf,MAAA,CAAAe,OAAA;YACAC,gBAAA,EAAAhB,MAAA,CAAAiB,QAAA;YACAC,eAAA,EAAAlB,MAAA,CAAAI,UAAA;YACAe,OAAA;UACA;QACA;MACA;IACA;IAEA;IACAtB,kBAAA,WAAAA,mBAAA;MACA,IAAAyB,IAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,UAAAC,MAAA,CAAAN,IAAA,EAAAM,MAAA,CAAAJ,KAAA;IACA;IAEA;IACAK,mBAAA,WAAAA,oBAAAC,QAAA;MAAA,IAAAC,MAAA;MAAA,WAAAlH,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAgH,SAAA;QAAA,IAAA9G,QAAA,EAAAM,SAAA,EAAAyG,UAAA,EAAAC,cAAA;QAAA,WAAAnH,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAgH,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA9G,IAAA,GAAA8G,SAAA,CAAA7G,IAAA;YAAA;cAAA6G,SAAA,CAAA9G,IAAA;cAAA,KAKAwG,QAAA,CAAAtG,SAAA;gBAAA4G,SAAA,CAAA7G,IAAA;gBAAA;cAAA;cAAA6G,SAAA,CAAA7G,IAAA;cAAA,OAEA,IAAA8G,4BAAA,EAAAP,QAAA;YAAA;cAAA5G,QAAA,GAAAkH,SAAA,CAAA1G,IAAA;cAAA0G,SAAA,CAAA7G,IAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA7G,IAAA;cAAA,OAGA,IAAA+G,yBAAA,EAAAR,QAAA;YAAA;cAAA5G,QAAA,GAAAkH,SAAA,CAAA1G,IAAA;YAAA;cAAA,MAGAR,QAAA,CAAAS,IAAA;gBAAAyG,SAAA,CAAA7G,IAAA;gBAAA;cAAA;cAAA,KAEAwG,MAAA,CAAAtI,gBAAA;gBAAA2I,SAAA,CAAA7G,IAAA;gBAAA;cAAA;cACAwG,MAAA,CAAAtI,gBAAA,CAAAgG,aAAA,GAAAqC,QAAA,CAAArC,aAAA;;cAEA;cACAjE,SAAA;cACA,IAAAN,QAAA,CAAA/B,IAAA,IAAA+B,QAAA,CAAA/B,IAAA,CAAAqC,SAAA;gBACAA,SAAA,GAAAN,QAAA,CAAA/B,IAAA,CAAAqC,SAAA;gBACAuG,MAAA,CAAAtI,gBAAA,CAAA+B,SAAA,GAAAA,SAAA;cACA,WAAAsG,QAAA,CAAAtG,SAAA;gBACAA,SAAA,GAAAsG,QAAA,CAAAtG,SAAA;gBACAuG,MAAA,CAAAtI,gBAAA,CAAA+B,SAAA,GAAAA,SAAA;cACA;;cAEA;cACAuG,MAAA,CAAAtI,gBAAA,CAAAqD,aAAA,GAAAgF,QAAA,CAAAhF,aAAA;cACAiF,MAAA,CAAAtI,gBAAA,CAAAgE,gBAAA,GAAAqE,QAAA,CAAArE,gBAAA;cACAsE,MAAA,CAAAtI,gBAAA,CAAA8D,WAAA,GAAAuE,QAAA,CAAAvE,WAAA;cACAwE,MAAA,CAAAtI,gBAAA,CAAA+D,YAAA,GAAAsE,QAAA,CAAAtE,YAAA;cACA;cACAuE,MAAA,CAAAtI,gBAAA,CAAAgG,aAAA;;cAEA;cAAA,MACAjE,SAAA,IAAAuG,MAAA,CAAAtI,gBAAA,CAAA8I,WAAA;gBAAAH,SAAA,CAAA7G,IAAA;gBAAA;cAAA;cAAA6G,SAAA,CAAA9G,IAAA;cAEA2G,UAAA;gBACAM,WAAA,EAAAR,MAAA,CAAAtI,gBAAA,CAAA8I,WAAA;gBACA/G,SAAA,EAAAA,SAAA;gBACAsB,aAAA,EAAAgF,QAAA,CAAAhF,aAAA;gBACAW,gBAAA,EAAAqE,QAAA,CAAArE,gBAAA;gBACAF,WAAA,EAAAuE,QAAA,CAAAvE,WAAA;gBACAC,YAAA,EAAAsE,QAAA,CAAAtE,YAAA;gBACAiC,aAAA;cACA,GAEA;cAAA2C,SAAA,CAAA7G,IAAA;cAAA,OACA,IAAAiH,0BAAA,EAAAP,UAAA;YAAA;cAAAC,cAAA,GAAAE,SAAA,CAAA1G,IAAA;cACA,IAAAwG,cAAA,CAAAvG,IAAA;gBACAoG,MAAA,CAAAU,QAAA,CAAAC,OAAA;cACA;gBACA3G,OAAA,CAAA4G,IAAA,iBAAAT,cAAA,CAAAxF,GAAA;cACA;cAAA0F,SAAA,CAAA7G,IAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA9G,IAAA;cAAA8G,SAAA,CAAAtG,EAAA,GAAAsG,SAAA;cAEArG,OAAA,CAAAC,KAAA,iBAAAoG,SAAA,CAAAtG,EAAA;YAAA;cAIA;cACAiG,MAAA,CAAA7H,KAAA,WAAA6H,MAAA,CAAAlI,aAAA;YAAA;cAGAkI,MAAA,CAAAU,QAAA,CAAAC,OAAA;cAAAN,SAAA,CAAA7G,IAAA;cAAA;YAAA;cAEAwG,MAAA,CAAAU,QAAA,CAAAzG,KAAA,CAAAd,QAAA,CAAAwB,GAAA;YAAA;cAAA0F,SAAA,CAAA7G,IAAA;cAAA;YAAA;cAAA6G,SAAA,CAAA9G,IAAA;cAAA8G,SAAA,CAAAQ,EAAA,GAAAR,SAAA;cAGArG,OAAA,CAAAC,KAAA,YAAAoG,SAAA,CAAAQ,EAAA;cACAb,MAAA,CAAAU,QAAA,CAAAzG,KAAA;YAAA;cAGA+F,MAAA,CAAAvI,oBAAA;YAAA;YAAA;cAAA,OAAA4I,SAAA,CAAAnG,IAAA;UAAA;QAAA,GAAA+F,QAAA;MAAA;IACA;IAEA;IACAa,mBAAA,WAAAA,oBAAA;MACA,KAAArJ,oBAAA;IACA;IACAsJ,WAAA,WAAAA,YAAAtI,GAAA;MACA;MACA,IAAAA,GAAA,CAAAiF,aAAA;QACA,KAAAgD,QAAA,CAAAzG,KAAA;QACA;MACA;MAEA,KAAA9B,KAAA,gBAAAM,GAAA;IACA;IACAuI,WAAA,WAAAA,YAAAvI,GAAA;MAAA,IAAAwI,MAAA;MACA;QACA,KAAAC,QAAA;UACAC,iBAAA;UACAC,gBAAA;UACAC,IAAA;QACA,GAAAC,IAAA;UACA;UACA7I,GAAA,CAAAC,UAAA;;UAEA;UACA,IAAAD,GAAA,CAAAoF,YAAA,IAAApF,GAAA,CAAAoF,YAAA,CAAAuB,MAAA;YACA3G,GAAA,CAAAoF,YAAA,CAAA0D,OAAA,WAAAxD,MAAA;cACAA,MAAA,CAAAyD,kBAAA;YACA;UACA;;UAEA;UACAP,MAAA,CAAA9I,KAAA,gBAAAM,GAAA;;UAEA;UACAwI,MAAA,CAAAP,QAAA;YACAW,IAAA;YACAI,OAAA;UACA;QACA,GAAAC,KAAA;UACA;QAAA,CACA;MACA,SAAAzH,KAAA;QACAD,OAAA,CAAAC,KAAA,eAAAA,KAAA;QACA,KAAAyG,QAAA,CAAAzG,KAAA;MACA;IACA;IACA0H,cAAA,WAAAA,eAAAlJ,GAAA,EAAAmJ,QAAA;MACAnJ,GAAA,CAAA4E,cAAA,GAAAuE,QAAA;IACA;IACAC,iBAAA,WAAAA,kBAAApJ,GAAA,EAAAqJ,WAAA;MACArJ,GAAA,CAAAqD,eAAA,GAAAgG,WAAA,CAAAhG,eAAA;MACArD,GAAA,CAAAyD,eAAA,GAAA4F,WAAA,CAAA5F,eAAA;IACA;IACA6F,mBAAA,WAAAA,oBAAAtJ,GAAA,EAAAuJ,OAAA;MACAvJ,GAAA,CAAA0C,iBAAA,GAAA6G,OAAA,CAAAC,SAAA;MACAxJ,GAAA,CAAA2D,mBAAA,GAAA4F,OAAA,CAAAE,gBAAA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAhK,KAAA;IACA;IACAyJ,QAAA,EAAAA,iBAAA;IAEA;IACAQ,kBAAA,WAAAA,mBAAA3J,GAAA,EAAAnB,YAAA;MACA,KAAAA,YAAA,GAAAA,YAAA;IACA;IAEA;IACA+K,eAAA,WAAAA,gBAAA5J,GAAA;MAAA,IAAA6J,MAAA;MAAA,WAAAxJ,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAAsJ,SAAA;QAAA,WAAAvJ,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAoJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAlJ,IAAA,GAAAkJ,SAAA,CAAAjJ,IAAA;YAAA;cACA,KAEA,SAAAS,KAAA;gBACAD,OAAA,CAAAC,KAAA,aAAAA,KAAA;gBACAqI,MAAA,CAAA5B,QAAA,CAAAzG,KAAA;cACA;YAAA;YAAA;cAAA,OAAAwI,SAAA,CAAAvI,IAAA;UAAA;QAAA,GAAAqI,QAAA;MAAA;IACA;IACA;IACAG,eAAA,WAAAA,gBAAAjK,GAAA;MAAA,IAAAkK,MAAA;MAAA,WAAA7J,kBAAA,CAAAC,OAAA,oBAAAC,oBAAA,CAAAD,OAAA,IAAAE,IAAA,UAAA2J,SAAA;QAAA,WAAA5J,oBAAA,CAAAD,OAAA,IAAAK,IAAA,UAAAyJ,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvJ,IAAA,GAAAuJ,SAAA,CAAAtJ,IAAA;YAAA;cAAAsJ,SAAA,CAAAvJ,IAAA;cAAAuJ,SAAA,CAAAtJ,IAAA;cAAA,OAEAmJ,MAAA,CAAAzB,QAAA;gBACAG,IAAA;cACA;YAAA;cACAsB,MAAA,CAAAxK,KAAA,eAAAM,GAAA;cAAAqK,SAAA,CAAAtJ,IAAA;cAAA;YAAA;cAAAsJ,SAAA,CAAAvJ,IAAA;cAAAuJ,SAAA,CAAA/I,EAAA,GAAA+I,SAAA;cAEA,IAAAA,SAAA,CAAA/I,EAAA;gBACAC,OAAA,CAAAC,KAAA,aAAA6I,SAAA,CAAA/I,EAAA;gBACA4I,MAAA,CAAAjC,QAAA,CAAAzG,KAAA;cACA;YAAA;YAAA;cAAA,OAAA6I,SAAA,CAAA5I,IAAA;UAAA;QAAA,GAAA0I,QAAA;MAAA;IAEA;IAEA;IACAG,sBAAA,WAAAA,uBAAAtK,GAAA,EAAAuK,UAAA;MACA,IAAAxF,cAAA;MACA,IAAAC,WAAA;;MAEA;MACA,SAAAF,YAAA;QACA;QACAyF,UAAA,CAAAzB,OAAA,WAAA0B,IAAA;UACA;UACAzF,cAAA,OAAAoE,iBAAA,EAAApE,cAAA,EAAA0F,GAAA,CAAAD,IAAA,CAAAjE,QAAA,EAAAmE,QAAA;QACA;MACA;QACA;QACAH,UAAA,CAAAzB,OAAA,WAAA0B,IAAA;UACAxF,WAAA,OAAAmE,iBAAA,EAAAnE,WAAA,EAAAyF,GAAA,CAAAD,IAAA,CAAAjE,QAAA,EAAAmE,QAAA;QACA;MACA;MACA1K,GAAA,CAAA+E,cAAA,GAAAA,cAAA;MACA/E,GAAA,CAAAgF,WAAA,GAAAA,WAAA;;MAEA;MACA,KAAAtF,KAAA,gBAAAL,aAAA;IACA;IAEA;IACAsL,qBAAA,WAAAA,sBAAA3K,GAAA,EAAA4K,eAAA;MACA,IAAAC,KAAA,QAAA/L,kBAAA,CAAAgM,SAAA,WAAAN,IAAA;QAAA,OAAAA,IAAA,KAAAxK,GAAA;MAAA;MACA,IAAA6K,KAAA;QACA,KAAA/L,kBAAA,CAAA+L,KAAA,EAAAD,eAAA,GAAAA,eAAA;QACA;QACA,KAAAlL,KAAA,8BAAAZ,kBAAA;MACA;IACA;IAEA;IACAiM,iBAAA,WAAAA,kBAAAzF,MAAA;MACA,KAAA5F,KAAA,gBAAA4F,MAAA;IACA;IAEA;IACA0F,gBAAA,WAAAA,iBAAA1F,MAAA;MACA,KAAA5F,KAAA,eAAA4F,MAAA;IACA;IAEA;IACA2F,eAAA,WAAAA,gBAAA;MACA,KAAAvL,KAAA;IACA;IAEA;IACAwL,iBAAA,WAAAA,kBAAAC,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAE,iBAAA,WAAAA,kBAAAF,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAG,oBAAA,WAAAA,qBAAAH,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAI,oBAAA,WAAAA,qBAAAJ,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAK,qBAAA,WAAAA,sBAAAL,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;IAEA;IACAM,qBAAA,WAAAA,sBAAAN,MAAA;MACA,IAAAC,SAAA;QACA;QACA;QACA;MACA;MACA,OAAAA,SAAA,CAAAD,MAAA;IACA;EACA;AACA;AAAAO,OAAA,CAAApL,OAAA,GAAAqL,QAAA"}]}