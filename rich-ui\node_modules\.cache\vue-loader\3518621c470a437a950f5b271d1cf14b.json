{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\index.vue", "mtime": 1754876882526}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-tabs type=\"border-card\">\r\n      <el-tab-pane v-if=\"shouldHide('second')\" label=\"秒\">\r\n        <CrontabSecond\r\n          ref=\"cronsecond\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          @update=\"updateCrontabValue\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane v-if=\"shouldHide('min')\" label=\"分钟\">\r\n        <CrontabMin\r\n          ref=\"cronmin\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          @update=\"updateCrontabValue\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane v-if=\"shouldHide('hour')\" label=\"小时\">\r\n        <CrontabHour\r\n          ref=\"cronhour\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          @update=\"updateCrontabValue\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane v-if=\"shouldHide('day')\" label=\"日\">\r\n        <CrontabDay\r\n          ref=\"cronday\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          @update=\"updateCrontabValue\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane v-if=\"shouldHide('month')\" label=\"月\">\r\n        <CrontabMonth\r\n          ref=\"cronmonth\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          @update=\"updateCrontabValue\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane v-if=\"shouldHide('week')\" label=\"周\">\r\n        <CrontabWeek\r\n          ref=\"cronweek\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          @update=\"updateCrontabValue\"\r\n        />\r\n      </el-tab-pane>\r\n\r\n      <el-tab-pane v-if=\"shouldHide('year')\" label=\"年\">\r\n        <CrontabYear\r\n          ref=\"cronyear\"\r\n          :check=\"checkNumber\"\r\n          :cron=\"crontabValueObj\"\r\n          @update=\"updateCrontabValue\"\r\n        />\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n\r\n    <div class=\"popup-main\">\r\n      <div class=\"popup-result\">\r\n        <p class=\"title\">时间表达式</p>\r\n        <table>\r\n          <thead>\r\n          <th v-for=\"item of tabTitles\" :key=\"item\" width=\"40\">{{ item }}</th>\r\n          <th>Cron 表达式</th>\r\n          </thead>\r\n          <tbody>\r\n          <td>\r\n            <span>{{ crontabValueObj.second }}</span>\r\n          </td>\r\n          <td>\r\n            <span>{{ crontabValueObj.min }}</span>\r\n          </td>\r\n          <td>\r\n            <span>{{ crontabValueObj.hour }}</span>\r\n          </td>\r\n          <td>\r\n            <span>{{ crontabValueObj.day }}</span>\r\n          </td>\r\n          <td>\r\n            <span>{{ crontabValueObj.month }}</span>\r\n          </td>\r\n          <td>\r\n            <span>{{ crontabValueObj.week }}</span>\r\n          </td>\r\n          <td>\r\n            <span>{{ crontabValueObj.year }}</span>\r\n          </td>\r\n          <td>\r\n            <span>{{ crontabValueString }}</span>\r\n          </td>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n      <CrontabResult :ex=\"crontabValueString\"></CrontabResult>\r\n\r\n      <div class=\"pop_btn\">\r\n        <el-button size=\"mini\" type=\"primary\" @click=\"submitFill\">确定</el-button>\r\n        <el-button size=\"mini\" type=\"warning\" @click=\"clearCron\">重置</el-button>\r\n        <el-button size=\"mini\" @click=\"hidePopup\">取消</el-button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport CrontabSecond from \"./second.vue\";\r\nimport CrontabMin from \"./min.vue\";\r\nimport CrontabHour from \"./hour.vue\";\r\nimport CrontabDay from \"./day.vue\";\r\nimport CrontabMonth from \"./month.vue\";\r\nimport CrontabWeek from \"./week.vue\";\r\nimport CrontabYear from \"./year.vue\";\r\nimport CrontabResult from \"./result.vue\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      tabTitles: [\"秒\", \"分钟\", \"小时\", \"日\", \"月\", \"周\", \"年\"],\r\n      tabActive: 0,\r\n      myindex: 0,\r\n      crontabValueObj: {\r\n        second: \"*\",\r\n        min: \"*\",\r\n        hour: \"*\",\r\n        day: \"*\",\r\n        month: \"*\",\r\n        week: \"?\",\r\n        year: \"\",\r\n      },\r\n    };\r\n  },\r\n  name: \"vcrontab\",\r\n  props: [\"expression\", \"hideComponent\"],\r\n  methods: {\r\n    shouldHide(key) {\r\n      if (this.hideComponent && this.hideComponent.includes(key)) return false;\r\n      return true;\r\n    },\r\n    resolveExp() {\r\n      // 反解析 表达式\r\n      if (this.expression) {\r\n        let arr = this.expression.split(\" \");\r\n        if (arr.length >= 6) {\r\n          //6 位以上是合法表达式\r\n          let obj = {\r\n            second: arr[0],\r\n            min: arr[1],\r\n            hour: arr[2],\r\n            day: arr[3],\r\n            month: arr[4],\r\n            week: arr[5],\r\n            year: arr[6] ? arr[6] : \"\",\r\n          };\r\n          this.crontabValueObj = {\r\n            ...obj,\r\n          };\r\n          for (let i in obj) {\r\n            if (obj[i]) this.changeRadio(i, obj[i]);\r\n          }\r\n        }\r\n      } else {\r\n        // 没有传入的表达式 则还原\r\n        this.clearCron();\r\n      }\r\n    },\r\n    // tab切换值\r\n    tabCheck(index) {\r\n      this.tabActive = index;\r\n    },\r\n    // 由子组件触发，更改表达式组成的字段值\r\n    updateCrontabValue(name, value, from) {\r\n      \"updateCrontabValue\", name, value, from;\r\n      this.crontabValueObj[name] = value;\r\n      if (from && from != name) {\r\n        console.log(`来自组件 ${from} 改变了 ${name} ${value}`);\r\n        this.changeRadio(name, value);\r\n      }\r\n    },\r\n    // 赋值到组件\r\n    changeRadio(name, value) {\r\n      let arr = [\"second\", \"min\", \"hour\", \"month\"],\r\n        refName = \"cron\" + name,\r\n        insValue;\r\n\r\n      if (!this.$refs[refName]) return;\r\n\r\n      if (arr.includes(name)) {\r\n        if (value == \"*\") {\r\n          insValue = 1;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          let indexArr = value.split(\"-\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].cycle01 = 0)\r\n            : (this.$refs[refName].cycle01 = indexArr[0]);\r\n          this.$refs[refName].cycle02 = indexArr[1];\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"/\") > -1) {\r\n          let indexArr = value.split(\"/\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].average01 = 0)\r\n            : (this.$refs[refName].average01 = indexArr[0]);\r\n          this.$refs[refName].average02 = indexArr[1];\r\n          insValue = 3;\r\n        } else {\r\n          insValue = 4;\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n        }\r\n      } else if (name == \"day\") {\r\n        if (value == \"*\") {\r\n          insValue = 1;\r\n        } else if (value == \"?\") {\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          let indexArr = value.split(\"-\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].cycle01 = 0)\r\n            : (this.$refs[refName].cycle01 = indexArr[0]);\r\n          this.$refs[refName].cycle02 = indexArr[1];\r\n          insValue = 3;\r\n        } else if (value.indexOf(\"/\") > -1) {\r\n          let indexArr = value.split(\"/\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].average01 = 0)\r\n            : (this.$refs[refName].average01 = indexArr[0]);\r\n          this.$refs[refName].average02 = indexArr[1];\r\n          insValue = 4;\r\n        } else if (value.indexOf(\"W\") > -1) {\r\n          let indexArr = value.split(\"W\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].workday = 0)\r\n            : (this.$refs[refName].workday = indexArr[0]);\r\n          insValue = 5;\r\n        } else if (value == \"L\") {\r\n          insValue = 6;\r\n        } else {\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n          insValue = 7;\r\n        }\r\n      } else if (name == \"week\") {\r\n        if (value == \"*\") {\r\n          insValue = 1;\r\n        } else if (value == \"?\") {\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          let indexArr = value.split(\"-\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].cycle01 = 0)\r\n            : (this.$refs[refName].cycle01 = indexArr[0]);\r\n          this.$refs[refName].cycle02 = indexArr[1];\r\n          insValue = 3;\r\n        } else if (value.indexOf(\"#\") > -1) {\r\n          let indexArr = value.split(\"#\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].average01 = 1)\r\n            : (this.$refs[refName].average01 = indexArr[0]);\r\n          this.$refs[refName].average02 = indexArr[1];\r\n          insValue = 4;\r\n        } else if (value.indexOf(\"L\") > -1) {\r\n          let indexArr = value.split(\"L\");\r\n          isNaN(indexArr[0])\r\n            ? (this.$refs[refName].weekday = 1)\r\n            : (this.$refs[refName].weekday = indexArr[0]);\r\n          insValue = 5;\r\n        } else {\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n          insValue = 6;\r\n        }\r\n      } else if (name == \"year\") {\r\n        if (value == \"\") {\r\n          insValue = 1;\r\n        } else if (value == \"*\") {\r\n          insValue = 2;\r\n        } else if (value.indexOf(\"-\") > -1) {\r\n          insValue = 3;\r\n        } else if (value.indexOf(\"/\") > -1) {\r\n          insValue = 4;\r\n        } else {\r\n          this.$refs[refName].checkboxList = value.split(\",\");\r\n          insValue = 5;\r\n        }\r\n      }\r\n      this.$refs[refName].radioValue = insValue;\r\n    },\r\n    // 表单选项的子组件校验数字格式（通过-props传递）\r\n    checkNumber(value, minLimit, maxLimit) {\r\n      // 检查必须为整数\r\n      value = Math.floor(value);\r\n      if (value < minLimit) {\r\n        value = minLimit;\r\n      } else if (value > maxLimit) {\r\n        value = maxLimit;\r\n      }\r\n      return value;\r\n    },\r\n    // 隐藏弹窗\r\n    hidePopup() {\r\n      this.$emit(\"hide\");\r\n    },\r\n    // 填充表达式\r\n    submitFill() {\r\n      this.$emit(\"fill\", this.crontabValueString);\r\n      this.hidePopup();\r\n    },\r\n    clearCron() {\r\n      // 还原选择项\r\n      (\"准备还原\");\r\n      this.crontabValueObj = {\r\n        second: \"*\",\r\n        min: \"*\",\r\n        hour: \"*\",\r\n        day: \"*\",\r\n        month: \"*\",\r\n        week: \"?\",\r\n        year: \"\",\r\n      };\r\n      for (let j in this.crontabValueObj) {\r\n        this.changeRadio(j, this.crontabValueObj[j]);\r\n      }\r\n    },\r\n  },\r\n  computed: {\r\n    crontabValueString: function () {\r\n      let obj = this.crontabValueObj;\r\n      let str =\r\n        obj.second +\r\n        \" \" +\r\n        obj.min +\r\n        \" \" +\r\n        obj.hour +\r\n        \" \" +\r\n        obj.day +\r\n        \" \" +\r\n        obj.month +\r\n        \" \" +\r\n        obj.week +\r\n        (obj.year == \"\" ? \"\" : \" \" + obj.year);\r\n      return str;\r\n    },\r\n  },\r\n  components: {\r\n    CrontabSecond,\r\n    CrontabMin,\r\n    CrontabHour,\r\n    CrontabDay,\r\n    CrontabMonth,\r\n    CrontabWeek,\r\n    CrontabYear,\r\n    CrontabResult,\r\n  },\r\n  watch: {\r\n    expression: \"resolveExp\",\r\n    hideComponent(value) {\r\n      // 隐藏部分组件\r\n    },\r\n  },\r\n  mounted: function () {\r\n    this.resolveExp();\r\n  },\r\n};\r\n</script>\r\n<style scoped>\r\n.pop_btn {\r\n  text-align: center;\r\n  margin-top: 20px;\r\n}\r\n\r\n.popup-main {\r\n  position: relative;\r\n  margin: 10px auto;\r\n  background: #fff;\r\n  border-radius: 5px;\r\n  font-size: 12px;\r\n  overflow: hidden;\r\n}\r\n\r\n.popup-title {\r\n  overflow: hidden;\r\n  line-height: 34px;\r\n  padding-top: 6px;\r\n  background: #f2f2f2;\r\n}\r\n\r\n.popup-result {\r\n  box-sizing: border-box;\r\n  line-height: 24px;\r\n  margin: 25px auto;\r\n  padding: 15px 10px 10px;\r\n  border: 1px solid #ccc;\r\n  position: relative;\r\n}\r\n\r\n.popup-result .title {\r\n  position: absolute;\r\n  top: -28px;\r\n  left: 50%;\r\n  width: 140px;\r\n  font-size: 14px;\r\n  margin-left: -70px;\r\n  text-align: center;\r\n  line-height: 30px;\r\n  background: #fff;\r\n}\r\n\r\n.popup-result table {\r\n  text-align: center;\r\n  width: 100%;\r\n  margin: 0 auto;\r\n}\r\n\r\n.popup-result table span {\r\n  display: block;\r\n  width: 100%;\r\n  font-family: arial;\r\n  line-height: 30px;\r\n  height: 30px;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  border: 1px solid #e8e8e8;\r\n}\r\n\r\n.popup-result-scroll {\r\n  font-size: 12px;\r\n  line-height: 24px;\r\n  height: 10em;\r\n  overflow-y: auto;\r\n}\r\n</style>\r\n"]}]}