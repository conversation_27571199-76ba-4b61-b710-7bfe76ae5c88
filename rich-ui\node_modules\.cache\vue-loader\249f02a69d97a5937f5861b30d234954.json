{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bookingList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\bookingList\\index.vue", "mtime": 1754876882574}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgYm9va2luZyBmcm9tICJAL3ZpZXdzL3N5c3RlbS9ib29raW5nL2luZGV4LnZ1ZSI7DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIkJvb2tpbmdMaXN0IiwNCiAgY29tcG9uZW50czoge2Jvb2tpbmd9LA0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;AAOA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/bookingList", "sourcesContent": ["<template>\r\n  <div>\r\n    <booking :type=\"'booking'\"/>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport booking from \"@/views/system/booking/index.vue\";\r\n\r\nexport default {\r\n  name: \"BookingList\",\r\n  components: {booking},\r\n}\r\n</script>\r\n"]}]}