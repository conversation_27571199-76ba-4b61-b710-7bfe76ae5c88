{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\SizeSelect\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\SizeSelect\\index.vue", "mtime": 1754876882534}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7CnJlcXVpcmUoImNvcmUtanMvbW9kdWxlcy9lcy5yZWdleHAuZXhlYy5qcyIpOwpyZXF1aXJlKCJjb3JlLWpzL21vZHVsZXMvZXMuc3RyaW5nLnJlcGxhY2UuanMiKTsKLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KdmFyIF9kZWZhdWx0ID0gewogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBzaXplT3B0aW9uczogW3sKICAgICAgICBsYWJlbDogJ0RlZmF1bHQnLAogICAgICAgIHZhbHVlOiAnZGVmYXVsdCcKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnTWVkaXVtJywKICAgICAgICB2YWx1ZTogJ21lZGl1bScKICAgICAgfSwgewogICAgICAgIGxhYmVsOiAnU21hbGwnLAogICAgICAgIHZhbHVlOiAnc21hbGwnCiAgICAgIH0sIHsKICAgICAgICBsYWJlbDogJ01pbmknLAogICAgICAgIHZhbHVlOiAnbWluaScKICAgICAgfV0KICAgIH07CiAgfSwKICBjb21wdXRlZDogewogICAgc2l6ZTogZnVuY3Rpb24gc2l6ZSgpIHsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMuc2l6ZTsKICAgIH0KICB9LAogIG1ldGhvZHM6IHsKICAgIGhhbmRsZVNldFNpemU6IGZ1bmN0aW9uIGhhbmRsZVNldFNpemUoc2l6ZSkgewogICAgICB0aGlzLiRFTEVNRU5ULnNpemUgPSBzaXplOwogICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgnYXBwL3NldFNpemUnLCBzaXplKTsKICAgICAgdGhpcy5yZWZyZXNoVmlldygpOwogICAgICB0aGlzLiRtZXNzYWdlKHsKICAgICAgICBtZXNzYWdlOiAnU3dpdGNoIFNpemUgU3VjY2VzcycsCiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnCiAgICAgIH0pOwogICAgfSwKICAgIHJlZnJlc2hWaWV3OiBmdW5jdGlvbiByZWZyZXNoVmlldygpIHsKICAgICAgdmFyIF90aGlzID0gdGhpczsKICAgICAgLy8gSW4gb3JkZXIgdG8gbWFrZSB0aGUgY2FjaGVkIHBhZ2UgcmUtcmVuZGVyZWQKICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goJ3RhZ3NWaWV3L2RlbEFsbENhY2hlZFZpZXdzJywgdGhpcy4kcm91dGUpOwogICAgICB2YXIgZnVsbFBhdGggPSB0aGlzLiRyb3V0ZS5mdWxsUGF0aDsKICAgICAgdGhpcy4kbmV4dFRpY2soZnVuY3Rpb24gKCkgewogICAgICAgIF90aGlzLiRyb3V0ZXIucmVwbGFjZSh7CiAgICAgICAgICBwYXRoOiAnL3JlZGlyZWN0JyArIGZ1bGxQYXRoCiAgICAgICAgfSk7CiAgICAgIH0pOwogICAgfQogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "names": ["data", "sizeOptions", "label", "value", "computed", "size", "$store", "getters", "methods", "handleSetSize", "$ELEMENT", "dispatch", "refresh<PERSON>iew", "$message", "message", "type", "_this", "$route", "fullPath", "$nextTick", "$router", "replace", "path", "exports", "default", "_default"], "sources": ["src/components/SizeSelect/index.vue"], "sourcesContent": ["<template>\r\n  <el-dropdown trigger=\"click\" @command=\"handleSetSize\">\r\n    <div>\r\n      <svg-icon class-name=\"size-icon\" icon-class=\"size\"/>\r\n    </div>\r\n    <el-dropdown-menu slot=\"dropdown\">\r\n      <el-dropdown-item v-for=\"item of sizeOptions\" :key=\"item.value\" :command=\"item.value\"\r\n                        :disabled=\"size==item.value\">\r\n        {{ item.label }}\r\n      </el-dropdown-item>\r\n    </el-dropdown-menu>\r\n  </el-dropdown>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      sizeOptions: [\r\n        {label: 'Default', value: 'default'},\r\n        {label: 'Medium', value: 'medium'},\r\n        {label: 'Small', value: 'small'},\r\n        {label: 'Mini', value: 'mini'}\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n    size() {\r\n      return this.$store.getters.size\r\n    }\r\n  },\r\n  methods: {\r\n    handleSetSize(size) {\r\n      this.$ELEMENT.size = size\r\n      this.$store.dispatch('app/setSize', size)\r\n      this.refreshView()\r\n      this.$message({\r\n        message: 'Switch Size Success',\r\n        type: 'success'\r\n      })\r\n    },\r\n    refreshView() {\r\n      // In order to make the cached page re-rendered\r\n      this.$store.dispatch('tagsView/delAllCachedViews', this.$route)\r\n\r\n      const {fullPath} = this.$route\r\n\r\n      this.$nextTick(() => {\r\n        this.$router.replace({\r\n          path: '/redirect' + fullPath\r\n        })\r\n      })\r\n    }\r\n  }\r\n\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;eAeA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,KAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EACAC,QAAA;IACAC,IAAA,WAAAA,KAAA;MACA,YAAAC,MAAA,CAAAC,OAAA,CAAAF,IAAA;IACA;EACA;EACAG,OAAA;IACAC,aAAA,WAAAA,cAAAJ,IAAA;MACA,KAAAK,QAAA,CAAAL,IAAA,GAAAA,IAAA;MACA,KAAAC,MAAA,CAAAK,QAAA,gBAAAN,IAAA;MACA,KAAAO,WAAA;MACA,KAAAC,QAAA;QACAC,OAAA;QACAC,IAAA;MACA;IACA;IACAH,WAAA,WAAAA,YAAA;MAAA,IAAAI,KAAA;MACA;MACA,KAAAV,MAAA,CAAAK,QAAA,oCAAAM,MAAA;MAEA,IAAAC,QAAA,QAAAD,MAAA,CAAAC,QAAA;MAEA,KAAAC,SAAA;QACAH,KAAA,CAAAI,OAAA,CAAAC,OAAA;UACAC,IAAA,gBAAAJ;QACA;MACA;IACA;EACA;AAEA;AAAAK,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}