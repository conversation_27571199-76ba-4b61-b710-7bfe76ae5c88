{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\report\\jimu\\view\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\report\\jimu\\view\\index.vue", "mtime": 1718100178829}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDsKdmFyIF9hdXRoID0gcmVxdWlyZSgiQC91dGlscy9hdXRoIik7CnZhciBfaW5kZXggPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvY29tcG9uZW50cy9pRnJhbWUvaW5kZXgiKSk7Ci8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAnSmltdVJlcG9ydFZpZXcnLAogIGNvbXBvbmVudHM6IHsKICAgIGlGcmFtZTogX2luZGV4LmRlZmF1bHQKICB9LAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICBvcGVuVXJsOiAnJwogICAgfTsKICB9LAogIGNyZWF0ZWQ6IGZ1bmN0aW9uIGNyZWF0ZWQoKSB7CiAgICB2YXIgcmVwb3J0SWQgPSB0aGlzLiRyb3V0ZS5wYXRoLnN1YnN0cmluZyh0aGlzLiRyb3V0ZS5wYXRoLmxhc3RJbmRleE9mKCIvIikgKyAxKTsKICAgIHRoaXMub3BlblVybCA9IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAnL2ptcmVwb3J0L3ZpZXcvJyArIHJlcG9ydElkICsgJz90b2tlbj1CZWFyZXIgJyArICgwLCBfYXV0aC5nZXRUb2tlbikoKTsKICB9Cn07CmV4cG9ydHMuZGVmYXVsdCA9IF9kZWZhdWx0Ow=="}, {"version": 3, "names": ["_auth", "require", "_index", "_interopRequireDefault", "name", "components", "iFrame", "data", "openUrl", "created", "reportId", "$route", "path", "substring", "lastIndexOf", "process", "env", "VUE_APP_BASE_API", "getToken", "exports", "default", "_default"], "sources": ["src/views/report/jimu/view/index.vue"], "sourcesContent": ["<template>\r\n  <i-frame :src=\"openUrl\"/>\r\n</template>\r\n\r\n<script>\r\nimport {getToken} from '@/utils/auth'\r\nimport iFrame from \"@/components/iFrame/index\";\r\n\r\nexport default {\r\n  name: 'JimuReportView',\r\n  components: {iFrame},\r\n  data() {\r\n    return {\r\n      openUrl: '',\r\n    }\r\n  },\r\n  created() {\r\n    const reportId = this.$route.path.substring(this.$route.path.lastIndexOf(\"/\") + 1)\r\n    this.openUrl = process.env.VUE_APP_BASE_API + '/jmreport/view/' + reportId + '?token=Bearer ' + getToken()\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;AAKA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;eAEA;EACAG,IAAA;EACAC,UAAA;IAAAC,MAAA,EAAAA;EAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,QAAA,QAAAC,MAAA,CAAAC,IAAA,CAAAC,SAAA,MAAAF,MAAA,CAAAC,IAAA,CAAAE,WAAA;IACA,KAAAN,OAAA,GAAAO,OAAA,CAAAC,GAAA,CAAAC,gBAAA,uBAAAP,QAAA,0BAAAQ,cAAA;EACA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}