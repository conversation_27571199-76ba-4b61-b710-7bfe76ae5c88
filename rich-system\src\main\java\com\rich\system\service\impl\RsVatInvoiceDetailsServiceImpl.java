package com.rich.system.service.impl;

import java.util.List;

import com.rich.common.core.domain.entity.RsVatInvoiceDetails;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rich.system.mapper.RsVatInvoiceDetailsMapper;
import com.rich.system.service.RsVatInvoiceDetailsService;

/**
 * 发票明细信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Service
public class RsVatInvoiceDetailsServiceImpl implements RsVatInvoiceDetailsService {
    @Autowired
    private RsVatInvoiceDetailsMapper rsVatInvoiceDetailsMapper;

    /**
     * 查询发票明细信息
     *
     * @param invoiceDetailsId 发票明细信息主键
     * @return 发票明细信息
     */
    @Override
    public RsVatInvoiceDetails selectRsVatInvoiceDetailsByInvoiceDetailsId(Long invoiceDetailsId) {
        return rsVatInvoiceDetailsMapper.selectRsVatInvoiceDetailsByInvoiceDetailsId(invoiceDetailsId);
    }

    /**
     * 查询发票明细信息列表
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 发票明细信息
     */
    @Override
    public List<RsVatInvoiceDetails> selectRsVatInvoiceDetailsList(RsVatInvoiceDetails rsVatInvoiceDetails) {
        return rsVatInvoiceDetailsMapper.selectRsVatInvoiceDetailsList(rsVatInvoiceDetails);
    }

    /**
     * 新增发票明细信息
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 结果
     */
    @Override
    public int insertRsVatInvoiceDetails(RsVatInvoiceDetails rsVatInvoiceDetails) {
        rsVatInvoiceDetails.setCreateTime(DateUtils.getNowDate());
        rsVatInvoiceDetails.setCreateBy(SecurityUtils.getUserId());
        return rsVatInvoiceDetailsMapper.insertRsVatInvoiceDetails(rsVatInvoiceDetails);
    }

    /**
     * 修改发票明细信息
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 结果
     */
    @Override
    public int updateRsVatInvoiceDetails(RsVatInvoiceDetails rsVatInvoiceDetails) {
        rsVatInvoiceDetails.setUpdateTime(DateUtils.getNowDate());
        rsVatInvoiceDetails.setUpdateBy(SecurityUtils.getUserId());
        return rsVatInvoiceDetailsMapper.updateRsVatInvoiceDetails(rsVatInvoiceDetails);
    }

    /**
     * 修改发票明细信息状态
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 发票明细信息
     */
    @Override
    public int changeStatus(RsVatInvoiceDetails rsVatInvoiceDetails) {
        return rsVatInvoiceDetailsMapper.updateRsVatInvoiceDetails(rsVatInvoiceDetails);
    }

    /**
     * 批量删除发票明细信息
     *
     * @param invoiceDetailsIds 需要删除的发票明细信息主键
     * @return 结果
     */
    @Override
    public int deleteRsVatInvoiceDetailsByInvoiceDetailsIds(Long[] invoiceDetailsIds) {
        return rsVatInvoiceDetailsMapper.deleteRsVatInvoiceDetailsByInvoiceDetailsIds(invoiceDetailsIds);
    }

    /**
     * 删除发票明细信息信息
     *
     * @param invoiceDetailsId 发票明细信息主键
     * @return 结果
     */
    @Override
    public int deleteRsVatInvoiceDetailsByInvoiceDetailsId(Long invoiceDetailsId) {
        return rsVatInvoiceDetailsMapper.deleteRsVatInvoiceDetailsByInvoiceDetailsId(invoiceDetailsId);
    }
}
