{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\serviceinstances.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\serviceinstances.js", "mtime": 1718100178756}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listServiceinstances", "query", "request", "url", "method", "params", "getServiceinstances", "serviceId", "addServiceinstances", "data", "updateServiceinstances", "delServiceinstances", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/serviceinstances.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询服务实例，记录着每一个订舱中的各种服务列表\r\nexport function listServiceinstances(query) {\r\n  return request({\r\n    url: '/system/serviceinstances/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询服务实例，记录着每一个订舱中的各种服务详细\r\nexport function getServiceinstances(serviceId) {\r\n  return request({\r\n    url: '/system/serviceinstances/' + serviceId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增服务实例，记录着每一个订舱中的各种服务\r\nexport function addServiceinstances(data) {\r\n  return request({\r\n    url: '/system/serviceinstances',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改服务实例，记录着每一个订舱中的各种服务\r\nexport function updateServiceinstances(data) {\r\n  return request({\r\n    url: '/system/serviceinstances',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除服务实例，记录着每一个订舱中的各种服务\r\nexport function delServiceinstances(serviceId) {\r\n  return request({\r\n    url: '/system/serviceinstances/' + serviceId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(serviceId, status) {\r\n  const data = {\r\n    serviceId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/serviceinstances/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAC1C,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,+BAA+B;IACpCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,mBAAmBA,CAACC,SAAS,EAAE;EAC7C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGI,SAAS;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,sBAAsBA,CAACD,IAAI,EAAE;EAC3C,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,mBAAmBA,CAACJ,SAAS,EAAE;EAC7C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGI,SAAS;IAC5CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,SAAS,EAAEM,MAAM,EAAE;EAC9C,IAAMJ,IAAI,GAAG;IACXF,SAAS,EAATA,SAAS;IACTM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,uCAAuC;IAC5CC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}