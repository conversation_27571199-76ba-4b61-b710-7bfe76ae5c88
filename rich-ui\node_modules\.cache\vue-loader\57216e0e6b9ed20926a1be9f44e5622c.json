{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\components\\icons\\index.vue?vue&type=template&id=279234be&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\components\\icons\\index.vue", "mtime": 1754876882562}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9Imljb25zLWNvbnRhaW5lciI+CiAgPGFzaWRlPgogICAgPGEgaHJlZj0iIyIgdGFyZ2V0PSJfYmxhbmsiPkFkZCBhbmQgdXNlCiAgICA8L2E+CiAgPC9hc2lkZT4KICA8ZWwtdGFicyB0eXBlPSJib3JkZXItY2FyZCI+CiAgICA8ZWwtdGFiLXBhbmUgbGFiZWw9Ikljb25zIj4KICAgICAgPGRpdiB2LWZvcj0iaXRlbSBvZiBzdmdJY29ucyIgOmtleT0iaXRlbSI+CiAgICAgICAgPGVsLXRvb2x0aXAgcGxhY2VtZW50PSJ0b3AiPgogICAgICAgICAgPGRpdiBzbG90PSJjb250ZW50Ij4KICAgICAgICAgICAge3sgZ2VuZXJhdGVJY29uQ29kZShpdGVtKSB9fQogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJpY29uLWl0ZW0iPgogICAgICAgICAgICA8c3ZnLWljb24gOmljb24tY2xhc3M9Iml0ZW0iIGNsYXNzLW5hbWU9ImRpc2FibGVkIi8+CiAgICAgICAgICAgIDxzcGFuPnt7IGl0ZW0gfX08L3NwYW4+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgIDwvZGl2PgogICAgPC9lbC10YWItcGFuZT4KICAgIDxlbC10YWItcGFuZSBsYWJlbD0iRWxlbWVudC1VSSBJY29ucyI+CiAgICAgIDxkaXYgdi1mb3I9Iml0ZW0gb2YgZWxlbWVudEljb25zIiA6a2V5PSJpdGVtIj4KICAgICAgICA8ZWwtdG9vbHRpcCBwbGFjZW1lbnQ9InRvcCI+CiAgICAgICAgICA8ZGl2IHNsb3Q9ImNvbnRlbnQiPgogICAgICAgICAgICB7eyBnZW5lcmF0ZUVsZW1lbnRJY29uQ29kZShpdGVtKSB9fQogICAgICAgICAgPC9kaXY+CiAgICAgICAgICA8ZGl2IGNsYXNzPSJpY29uLWl0ZW0iPgogICAgICAgICAgICA8aSA6Y2xhc3M9IidlbC1pY29uLScgKyBpdGVtIi8+CiAgICAgICAgICAgIDxzcGFuPnt7IGl0ZW0gfX08L3NwYW4+CiAgICAgICAgICA8L2Rpdj4KICAgICAgICA8L2VsLXRvb2x0aXA+CiAgICAgIDwvZGl2PgogICAgPC9lbC10YWItcGFuZT4KICA8L2VsLXRhYnM+CjwvZGl2Pgo="}, null]}