{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\dept\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\dept\\index.vue", "mtime": 1737429728535}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_dept", "require", "name", "dicts", "data", "showLeft", "showRight", "loading", "showSearch", "deptList", "title", "open", "isExpandAll", "refreshTable", "queryParams", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "form", "rules", "parentId", "required", "trigger", "deptLocalName", "orderNum", "email", "type", "message", "phone", "pattern", "watch", "n", "created", "getList", "methods", "_this", "listDept", "then", "response", "handleTree", "getParentId", "val", "cancel", "reset", "deptId", "undefined", "leader<PERSON><PERSON>", "status", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleAdd", "row", "toggleExpandAll", "_this2", "$nextTick", "handleUpdate", "_this3", "getDept", "listDept<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "deptOptions", "submitForm", "_this4", "$refs", "validate", "valid", "updateDept", "$modal", "msgSuccess", "addDept", "handleDelete", "_this5", "$confirm", "customClass", "delDept", "catch", "exports", "default", "_default"], "sources": ["src/views/system/dept/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" size=\"mini\">\r\n          <el-form-item label=\"搜索\" prop=\"deptQuery\">\r\n            <el-input\r\n              v-model=\"queryParams.deptQuery\"\r\n              clearable\r\n              placeholder=\"部门名称\"\r\n              style=\"width: 158px;\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:dept:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              icon=\"el-icon-sort\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"info\"\r\n              @click=\"toggleExpandAll\"\r\n            >展开/折叠\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n        <el-table\r\n          v-if=\"refreshTable\"\r\n          v-loading=\"loading\"\r\n          :data=\"deptList\"\r\n          :default-expand-all=\"isExpandAll\"\r\n          :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n          row-key=\"deptId\">\r\n          <el-table-column label=\"部门名称\" width=\"350\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.deptShortName }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.deptLocalName }}</a>\r\n              {{ scope.row.deptEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"备注\" prop=\"remark\"></el-table-column>\r\n          <el-table-column align=\"center\" label=\"排序\" prop=\"orderNum\" width=\"48\"></el-table-column>\r\n          <el-table-column align=\"center\" label=\"状态\" prop=\"status\" width=\"58\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.sys_normal_disable\" :value=\"scope.row.status\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"170\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:dept:add']\"\r\n                icon=\"el-icon-plus\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"primary\"\r\n                @click=\"handleAdd(scope.row)\"\r\n              >新增\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:dept:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-if=\"scope.row.parentId != 0\"\r\n                v-hasPermi=\"['system:dept:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 添加或修改部门对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"600px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-row>\r\n          <el-col v-if=\"form.parentId != 0\" :span=\"24\">\r\n            <el-form-item v-if=\"form.parentId!=0\" label=\"上级部门\" prop=\"parentId\">\r\n              <tree-select :multiple=\"false\" :pass=\"form.parentId\" :type=\"'dept'\" @return=\"getParentId\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"14\">\r\n            <el-form-item label=\"部门名称\" prop=\"deptLocalName\">\r\n              <el-input v-model=\"form.deptLocalName\" placeholder=\"部门名称\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"10\">\r\n            <el-form-item label=\"显示排序\" prop=\"orderNum\">\r\n              <el-input-number :controls=\"false\" v-model=\"form.orderNum\" :min=\"0\" controls-position=\"right\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"英文名\" prop=\"deptEnName\">\r\n              <el-input v-model=\"form.deptEnName\" placeholder=\"部门英文名\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"简称\" prop=\"deptShortName\">\r\n              <el-input v-model=\"form.deptShortName\" placeholder=\"部门简称\"/>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" :autosize=\"{ minRows: 10, maxRows: 20}\" maxlength=\"150\"\r\n                    placeholder=\"备注\"\r\n                    show-word-limit type=\"textarea\"/>\r\n        </el-form-item>\r\n        <el-row>\r\n          <el-col :span=\"12\">\r\n            <el-form-item label=\"部门状态\">\r\n              <el-radio-group v-model=\"form.status\">\r\n                <el-radio\r\n                  v-for=\"dict in dict.type.sys_normal_disable\"\r\n                  :key=\"dict.value\"\r\n                  :label=\"dict.value\"\r\n                >{{ dict.label }}\r\n                </el-radio>\r\n              </el-radio-group>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {addDept, delDept, getDept, listDept, listDeptExcludeChild, updateDept} from \"@/api/system/dept\";\r\n\r\nexport default {\r\n  name: \"Dept\",\r\n  dicts: ['sys_normal_disable'],\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 表格树数据\r\n      deptList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部展开\r\n      isExpandAll: true,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        deptQuery: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parentId: [\r\n          {required: true,trigger: \"blur\"}\r\n        ],\r\n        deptLocalName: [\r\n          {required: true,  trigger: \"blur\"}\r\n        ],\r\n        orderNum: [\r\n          {required: true,  trigger: \"blur\"}\r\n        ],\r\n        email: [\r\n          {\r\n            type: \"email\",\r\n            message: \"正确的邮箱地址\",\r\n            trigger: [\"blur\", \"change\"]\r\n          }\r\n        ],\r\n        phone: [\r\n          {\r\n            pattern: /^1[3|4|5|6|7|8|9][0-9]\\d{8}$/,\r\n            message: \"正确的手机号码\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    /** 查询部门列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDept(this.queryParams).then(response => {\r\n        this.deptList = this.handleTree(response.data, \"deptId\");\r\n        this.loading = false;\r\n      });\r\n    },\r\n    getParentId(val) {\r\n      this.form.parentId = val\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        deptId: undefined,\r\n        parentId: undefined,\r\n        deptLocalName: undefined,\r\n        orderNum: undefined,\r\n        leaderName: undefined,\r\n        phone: undefined,\r\n        email: undefined,\r\n        status: \"0\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      if (row != undefined) {\r\n        this.form.parentId = row.deptId;\r\n      }\r\n      this.open = true;\r\n      this.title = \"添加部门\";\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      getDept(row.deptId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改部门\";\r\n      });\r\n      listDeptExcludeChild(row.deptId).then(response => {\r\n        this.deptOptions = this.handleTree(response.data, \"deptId\");\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm: function () {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.deptId != undefined) {\r\n            updateDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDept(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$confirm('是否确认删除名称为\"' + row.deptLocalName + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delDept(row.deptId);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n"], "mappings": ";;;;;;AAwKA,IAAAA,KAAA,GAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,UAAA;MACA;MACAC,QAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;MACA;MACAC,YAAA;MACA;MACAC,WAAA;QACAC,SAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;QACAC,QAAA,GACA;UAAAC,QAAA;UAAAC,OAAA;QAAA,EACA;QACAC,aAAA,GACA;UAAAF,QAAA;UAAAC,OAAA;QAAA,EACA;QACAE,QAAA,GACA;UAAAH,QAAA;UAAAC,OAAA;QAAA,EACA;QACAG,KAAA,GACA;UACAC,IAAA;UACAC,OAAA;UACAL,OAAA;QACA,EACA;QACAM,KAAA,GACA;UACAC,OAAA;UACAF,OAAA;UACAL,OAAA;QACA;MAEA;IACA;EACA;EACAQ,KAAA;IACApB,UAAA,WAAAA,WAAAqB,CAAA;MACA,IAAAA,CAAA;QACA,KAAAvB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAyB,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA,aACAD,OAAA,WAAAA,QAAA;MAAA,IAAAE,KAAA;MACA,KAAA1B,OAAA;MACA,IAAA2B,cAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAAxB,QAAA,GAAAwB,KAAA,CAAAI,UAAA,CAAAD,QAAA,CAAAhC,IAAA;QACA6B,KAAA,CAAA1B,OAAA;MACA;IACA;IACA+B,WAAA,WAAAA,YAAAC,GAAA;MACA,KAAAvB,IAAA,CAAAE,QAAA,GAAAqB,GAAA;IACA;IACA;IACAC,MAAA,WAAAA,OAAA;MACA,KAAA7B,IAAA;MACA,KAAA8B,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAzB,IAAA;QACA0B,MAAA,EAAAC,SAAA;QACAzB,QAAA,EAAAyB,SAAA;QACAtB,aAAA,EAAAsB,SAAA;QACArB,QAAA,EAAAqB,SAAA;QACAC,UAAA,EAAAD,SAAA;QACAjB,KAAA,EAAAiB,SAAA;QACApB,KAAA,EAAAoB,SAAA;QACAE,MAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAhB,OAAA;IACA;IACA,aACAiB,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACA,aACAE,SAAA,WAAAA,UAAAC,GAAA;MACA,KAAAT,KAAA;MACA,IAAAS,GAAA,IAAAP,SAAA;QACA,KAAA3B,IAAA,CAAAE,QAAA,GAAAgC,GAAA,CAAAR,MAAA;MACA;MACA,KAAA/B,IAAA;MACA,KAAAD,KAAA;IACA;IACA,cACAyC,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAvC,YAAA;MACA,KAAAD,WAAA,SAAAA,WAAA;MACA,KAAAyC,SAAA;QACAD,MAAA,CAAAvC,YAAA;MACA;IACA;IACA,aACAyC,YAAA,WAAAA,aAAAJ,GAAA;MAAA,IAAAK,MAAA;MACA,KAAAd,KAAA;MACA,IAAAe,aAAA,EAAAN,GAAA,CAAAR,MAAA,EAAAP,IAAA,WAAAC,QAAA;QACAmB,MAAA,CAAAvC,IAAA,GAAAoB,QAAA,CAAAhC,IAAA;QACAmD,MAAA,CAAA5C,IAAA;QACA4C,MAAA,CAAA7C,KAAA;MACA;MACA,IAAA+C,0BAAA,EAAAP,GAAA,CAAAR,MAAA,EAAAP,IAAA,WAAAC,QAAA;QACAmB,MAAA,CAAAG,WAAA,GAAAH,MAAA,CAAAlB,UAAA,CAAAD,QAAA,CAAAhC,IAAA;MACA;IACA;IACA;IACAuD,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAH,MAAA,CAAA5C,IAAA,CAAA0B,MAAA,IAAAC,SAAA;YACA,IAAAqB,gBAAA,EAAAJ,MAAA,CAAA5C,IAAA,EAAAmB,IAAA,WAAAC,QAAA;cACAwB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAjD,IAAA;cACAiD,MAAA,CAAA7B,OAAA;YACA;UACA;YACA,IAAAoC,aAAA,EAAAP,MAAA,CAAA5C,IAAA,EAAAmB,IAAA,WAAAC,QAAA;cACAwB,MAAA,CAAAK,MAAA,CAAAC,UAAA;cACAN,MAAA,CAAAjD,IAAA;cACAiD,MAAA,CAAA7B,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqC,YAAA,WAAAA,aAAAlB,GAAA;MAAA,IAAAmB,MAAA;MACA,KAAAC,QAAA,gBAAApB,GAAA,CAAA7B,aAAA;QAAAkD,WAAA;MAAA,GAAApC,IAAA;QACA,WAAAqC,aAAA,EAAAtB,GAAA,CAAAR,MAAA;MACA,GAAAP,IAAA;QACAkC,MAAA,CAAAtC,OAAA;QACAsC,MAAA,CAAAJ,MAAA,CAAAC,UAAA;MACA,GAAAO,KAAA,cACA;IACA;EACA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}