{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\processtype.js", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\api\\system\\processtype.js", "mtime": 1685950070000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\@vue\\cli-plugin-eslint\\node_modules\\eslint-loader\\index.js", "mtime": 1690797455367}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKdmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKCJDOi9Vc2Vycy9BZG1pbmlzdHJhdG9yL0lkZWFQcm9qZWN0cy9yaWNoLXRlc3QvcmljaC11aS9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHQuanMiKS5kZWZhdWx0OwpPYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgIl9fZXNNb2R1bGUiLCB7CiAgdmFsdWU6IHRydWUKfSk7CmV4cG9ydHMuYWRkUHJvY2Vzc3R5cGUgPSBhZGRQcm9jZXNzdHlwZTsKZXhwb3J0cy5jaGFuZ2VTdGF0dXMgPSBjaGFuZ2VTdGF0dXM7CmV4cG9ydHMuZGVsUHJvY2Vzc3R5cGUgPSBkZWxQcm9jZXNzdHlwZTsKZXhwb3J0cy5nZXRQcm9jZXNzdHlwZSA9IGdldFByb2Nlc3N0eXBlOwpleHBvcnRzLmxpc3RQcm9jZXNzdHlwZSA9IGxpc3RQcm9jZXNzdHlwZTsKZXhwb3J0cy51cGRhdGVQcm9jZXNzdHlwZSA9IHVwZGF0ZVByb2Nlc3N0eXBlOwp2YXIgX3JlcXVlc3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoIkAvdXRpbHMvcmVxdWVzdCIpKTsKLy8g5p+l6K+i6L+b5bqm5YiG57G75YiX6KGoCmZ1bmN0aW9uIGxpc3RQcm9jZXNzdHlwZShxdWVyeSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9wcm9jZXNzdHlwZS9saXN0JywKICAgIG1ldGhvZDogJ2dldCcsCiAgICBwYXJhbXM6IHF1ZXJ5CiAgfSk7Cn0KCi8vIOafpeivoui/m+W6puWIhuexu+ivpue7hgpmdW5jdGlvbiBnZXRQcm9jZXNzdHlwZShwcm9jZXNzVHlwZUlkKSB7CiAgcmV0dXJuICgwLCBfcmVxdWVzdC5kZWZhdWx0KSh7CiAgICB1cmw6ICcvc3lzdGVtL3Byb2Nlc3N0eXBlLycgKyBwcm9jZXNzVHlwZUlkLAogICAgbWV0aG9kOiAnZ2V0JwogIH0pOwp9CgovLyDmlrDlop7ov5vluqbliIbnsbsKZnVuY3Rpb24gYWRkUHJvY2Vzc3R5cGUoZGF0YSkgewogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9wcm9jZXNzdHlwZScsCiAgICBtZXRob2Q6ICdwb3N0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5L+u5pS56L+b5bqm5YiG57G7CmZ1bmN0aW9uIHVwZGF0ZVByb2Nlc3N0eXBlKGRhdGEpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vcHJvY2Vzc3R5cGUnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQoKLy8g5Yig6Zmk6L+b5bqm5YiG57G7CmZ1bmN0aW9uIGRlbFByb2Nlc3N0eXBlKHByb2Nlc3NUeXBlSWQpIHsKICByZXR1cm4gKDAsIF9yZXF1ZXN0LmRlZmF1bHQpKHsKICAgIHVybDogJy9zeXN0ZW0vcHJvY2Vzc3R5cGUvJyArIHByb2Nlc3NUeXBlSWQsCiAgICBtZXRob2Q6ICdkZWxldGUnCiAgfSk7Cn0KCi8vIOeKtuaAgeS/ruaUuQpmdW5jdGlvbiBjaGFuZ2VTdGF0dXMocHJvY2Vzc1R5cGVJZCwgc3RhdHVzKSB7CiAgdmFyIGRhdGEgPSB7CiAgICBwcm9jZXNzVHlwZUlkOiBwcm9jZXNzVHlwZUlkLAogICAgc3RhdHVzOiBzdGF0dXMKICB9OwogIHJldHVybiAoMCwgX3JlcXVlc3QuZGVmYXVsdCkoewogICAgdXJsOiAnL3N5c3RlbS9wcm9jZXNzdHlwZS9jaGFuZ2VTdGF0dXMnLAogICAgbWV0aG9kOiAncHV0JywKICAgIGRhdGE6IGRhdGEKICB9KTsKfQ=="}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listProcesstype", "query", "request", "url", "method", "params", "getProcesstype", "processTypeId", "addProcesstype", "data", "updateProcesstype", "delProcesstype", "changeStatus", "status"], "sources": ["C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/api/system/processtype.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询进度分类列表\r\nexport function listProcesstype(query) {\r\n  return request({\r\n    url: '/system/processtype/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询进度分类详细\r\nexport function getProcesstype(processTypeId) {\r\n  return request({\r\n    url: '/system/processtype/' + processTypeId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增进度分类\r\nexport function addProcesstype(data) {\r\n  return request({\r\n    url: '/system/processtype',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改进度分类\r\nexport function updateProcesstype(data) {\r\n  return request({\r\n    url: '/system/processtype',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除进度分类\r\nexport function delProcesstype(processTypeId) {\r\n  return request({\r\n    url: '/system/processtype/' + processTypeId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 状态修改\r\nexport function changeStatus(processTypeId, status) {\r\n  const data = {\r\n      processTypeId,\r\n    status\r\n  }\r\n  return request({\r\n    url: '/system/processtype/changeStatus',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,eAAeA,CAACC,KAAK,EAAE;EACrC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,cAAcA,CAACC,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,aAAa;IAC3CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,cAAcA,CAACC,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,iBAAiBA,CAACD,IAAI,EAAE;EACtC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB;IAC1BC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,cAAcA,CAACJ,aAAa,EAAE;EAC5C,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,sBAAsB,GAAGI,aAAa;IAC3CH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,YAAYA,CAACL,aAAa,EAAEM,MAAM,EAAE;EAClD,IAAMJ,IAAI,GAAG;IACTF,aAAa,EAAbA,aAAa;IACfM,MAAM,EAANA;EACF,CAAC;EACD,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,kCAAkC;IACvCC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ"}]}