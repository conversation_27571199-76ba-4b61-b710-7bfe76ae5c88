package com.rich.system.service;

import com.rich.common.core.domain.entity.RsVatInvoiceDetails;

import java.util.List;

/**
 * 发票明细信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface RsVatInvoiceDetailsService {
    /**
     * 查询发票明细信息
     *
     * @param invoiceDetailsId 发票明细信息主键
     * @return 发票明细信息
     */
    RsVatInvoiceDetails selectRsVatInvoiceDetailsByInvoiceDetailsId(Long invoiceDetailsId);

    /**
     * 查询发票明细信息列表
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 发票明细信息集合
     */
    List<RsVatInvoiceDetails> selectRsVatInvoiceDetailsList(RsVatInvoiceDetails rsVatInvoiceDetails);

    /**
     * 新增发票明细信息
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 结果
     */
    int insertRsVatInvoiceDetails(RsVatInvoiceDetails rsVatInvoiceDetails);

    /**
     * 修改发票明细信息
     *
     * @param rsVatInvoiceDetails 发票明细信息
     * @return 结果
     */
    int updateRsVatInvoiceDetails(RsVatInvoiceDetails rsVatInvoiceDetails);

    /**
     * 批量删除发票明细信息
     *
     * @param invoiceDetailsIds 需要删除的发票明细信息主键集合
     * @return 结果
     */
    int deleteRsVatInvoiceDetailsByInvoiceDetailsIds(Long[] invoiceDetailsIds);

    /**
     * 删除发票明细信息信息
     *
     * @param invoiceDetailsId 发票明细信息主键
     * @return 结果
     */
    int deleteRsVatInvoiceDetailsByInvoiceDetailsId(Long invoiceDetailsId);

    int changeStatus(RsVatInvoiceDetails rsVatInvoiceDetails);
}
