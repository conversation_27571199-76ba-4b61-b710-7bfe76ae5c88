{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\BlackList\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\BlackList\\index.vue", "mtime": 1754876882524}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge2pvaW5JbkJsYWNrTGlzdH0gZnJvbSAiQC9hcGkvc3lzdGVtL2NvbXBhbnkiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJCbGFja0xpc3QiLA0KICBwcm9wczogWyJvcGVuIiwgImNvbXBhbnkiXSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgb3BlbkNvbnRlbnQ6IGZhbHNlLA0KICAgICAgZm9ybToge30sDQogICAgICAvLyDooajljZXmoKHpqowNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGJsYWNrbGlzdENvbnRlbnQ6IFsNCiAgICAgICAgICB7cmVxdWlyZWQ6IHRydWUsICB0cmlnZ2VyOiAiYmx1ciJ9DQogICAgICAgIF0sDQogICAgICB9LA0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICBvcGVuOiBmdW5jdGlvbiAodmFsKSB7DQogICAgICB0aGlzLm9wZW5Db250ZW50ID0gdmFsDQogICAgfSwNCiAgICBvcGVuQ29udGVudCh2YWwpIHsNCiAgICAgIGlmICh2YWwgPT0gZmFsc2UpIHsNCiAgICAgICAgdGhpcy4kZW1pdCgnb3BlbkJsYWNrTGlzdCcpDQogICAgICB9DQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGxldCBfdGhpcyA9IHRoaXMNCiAgICAgICAgICB0aGlzLmZvcm0uY29tcGFueUlkID0gdGhpcy5jb21wYW55LmNvbXBhbnlJZA0KICAgICAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruWumuaKiuWFrOWPuCInICsNCiAgICAgICAgICAgIHRoaXMuY29tcGFueS5jb21wYW55TG9jYWxOYW1lICsgJyAnICsgJygnICsgdGhpcy5jb21wYW55LmNvbXBhbnlTaG9ydE5hbWUgKyAnKScgKyAnICcgKyB0aGlzLmNvbXBhbnkuY29tcGFueUVuTmFtZQ0KICAgICAgICAgICAgKyAnIuaLieWFpem7keWQjeWNle+8nycsICfmj5DnpLonLCB7Y3VzdG9tQ2xhc3M6ICdtb2RhbC1jb25maXJtJ30pLnRoZW4oZnVuY3Rpb24gKCkgew0KICAgICAgICAgICAgcmV0dXJuIGpvaW5JbkJsYWNrTGlzdChfdGhpcy5mb3JtKTsNCiAgICAgICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgICAgIHRoaXMub3BlbkNvbnRlbnQgPSBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCk7DQogICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmi4npu5HmiJDlip8iKTsNCiAgICAgICAgICB9KS5jYXRjaCgoKSA9PiB7DQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuQ29udGVudCA9IGZhbHNlDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/BlackList", "sourcesContent": ["<template>\r\n  <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" :visible.sync=\"openContent\"\r\n             v-dialogDrag v-dialogDragWidth\r\n             class=\"blackList\"\r\n             title=\"加入黑名单\"\r\n             width=\"1000px\">\r\n    <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-position=\"right\" label-width=\"75px\">\r\n      <el-form-item label=\"拉黑者\">\r\n        {{ this.$store.state.user.name }}\r\n      </el-form-item>\r\n      <el-form-item label=\"被拉黑者\">\r\n        {{\r\n          this.company.companyLocalName + ' ' + '(' + this.company.companyShortName + ')' + ' ' + this.company.companyEnName\r\n        }}\r\n      </el-form-item>\r\n      <el-form-item label=\"拉黑原因\" prop=\"content\">\r\n        <el-input v-model=\"form.blacklistContent\" maxlength=\"200\" placeholder=\"拉黑原因\" show-word-limit\r\n                  type=\"textarea\"/>\r\n      </el-form-item>\r\n    </el-form>\r\n    <div slot=\"footer\" class=\"dialog-footer\">\r\n      <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n      <el-button @click=\"cancel\">取 消</el-button>\r\n    </div>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script>\r\nimport {joinInBlackList} from \"@/api/system/company\";\r\n\r\nexport default {\r\n  name: \"BlackList\",\r\n  props: [\"open\", \"company\"],\r\n  data() {\r\n    return {\r\n      openContent: false,\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        blacklistContent: [\r\n          {required: true,  trigger: \"blur\"}\r\n        ],\r\n      },\r\n    }\r\n  },\r\n  watch: {\r\n    open: function (val) {\r\n      this.openContent = val\r\n    },\r\n    openContent(val) {\r\n      if (val == false) {\r\n        this.$emit('openBlackList')\r\n      }\r\n    }\r\n  },\r\n  methods: {\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          let _this = this\r\n          this.form.companyId = this.company.companyId\r\n          this.$confirm('是否确定把公司\"' +\r\n            this.company.companyLocalName + ' ' + '(' + this.company.companyShortName + ')' + ' ' + this.company.companyEnName\r\n            + '\"拉入黑名单？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n            return joinInBlackList(_this.form);\r\n          }).then(() => {\r\n            this.openContent = false\r\n            this.getList();\r\n            this.$modal.msgSuccess(\"拉黑成功\");\r\n          }).catch(() => {\r\n          });\r\n        }\r\n      });\r\n    },\r\n    cancel() {\r\n      this.openContent = false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"]}]}