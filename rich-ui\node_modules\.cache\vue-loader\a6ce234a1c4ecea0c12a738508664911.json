{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\min.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Crontab\\min.vue", "mtime": 1754876882526}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["min.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "min.vue", "sourceRoot": "src/components/Crontab", "sourcesContent": ["<template>\r\n  <el-form size=\"mini\">\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"1\">\r\n        分钟，允许的通配符[, - * /]\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"2\">\r\n        周期从\r\n        <el-input-number v-model='cycle01' :max=\"58\" :min=\"0\"/>\r\n        -\r\n        <el-input-number v-model='cycle02' :max=\"59\" :min=\"cycle01 ? cycle01 + 1 : 1\"/>\r\n        分钟\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"3\">\r\n        从\r\n        <el-input-number v-model='average01' :max=\"58\" :min=\"0\"/>\r\n        分钟开始，每\r\n        <el-input-number v-model='average02' :max=\"59 - average01 || 0\" :min=\"1\"/>\r\n        分钟执行一次\r\n      </el-radio>\r\n    </el-form-item>\r\n\r\n    <el-form-item>\r\n      <el-radio v-model='radioValue' :label=\"4\">\r\n        指定\r\n        <el-select v-model=\"checkboxList\" clearable multiple placeholder=\"可多选\" style=\"width:100%\">\r\n          <el-option v-for=\"item in 60\" :key=\"item\" :value=\"item-1\">{{ item - 1 }}</el-option>\r\n        </el-select>\r\n      </el-radio>\r\n    </el-form-item>\r\n  </el-form>\r\n\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      radioValue: 1,\r\n      cycle01: 1,\r\n      cycle02: 2,\r\n      average01: 0,\r\n      average02: 1,\r\n      checkboxList: [],\r\n      checkNum: this.$options.propsData.check\r\n    }\r\n  },\r\n  name: 'crontab-min',\r\n  props: ['check', 'cron'],\r\n  methods: {\r\n    // 单选按钮值变化时\r\n    radioChange() {\r\n      switch (this.radioValue) {\r\n        case 1:\r\n          this.$emit('update', 'min', '*', 'min');\r\n          break;\r\n        case 2:\r\n          this.$emit('update', 'min', this.cycleTotal, 'min');\r\n          break;\r\n        case 3:\r\n          this.$emit('update', 'min', this.averageTotal, 'min');\r\n          break;\r\n        case 4:\r\n          this.$emit('update', 'min', this.checkboxString, 'min');\r\n          break;\r\n      }\r\n    },\r\n    // 周期两个值变化时\r\n    cycleChange() {\r\n      if (this.radioValue == '2') {\r\n        this.$emit('update', 'min', this.cycleTotal, 'min');\r\n      }\r\n    },\r\n    // 平均两个值变化时\r\n    averageChange() {\r\n      if (this.radioValue == '3') {\r\n        this.$emit('update', 'min', this.averageTotal, 'min');\r\n      }\r\n    },\r\n    // checkbox值变化时\r\n    checkboxChange() {\r\n      if (this.radioValue == '4') {\r\n        this.$emit('update', 'min', this.checkboxString, 'min');\r\n      }\r\n    },\r\n\r\n  },\r\n  watch: {\r\n    'radioValue': 'radioChange',\r\n    'cycleTotal': 'cycleChange',\r\n    'averageTotal': 'averageChange',\r\n    'checkboxString': 'checkboxChange',\r\n  },\r\n  computed: {\r\n    // 计算两个周期值\r\n    cycleTotal: function () {\r\n      const cycle01 = this.checkNum(this.cycle01, 0, 58)\r\n      const cycle02 = this.checkNum(this.cycle02, cycle01 ? cycle01 + 1 : 1, 59)\r\n      return cycle01 + '-' + cycle02;\r\n    },\r\n    // 计算平均用到的值\r\n    averageTotal: function () {\r\n      const average01 = this.checkNum(this.average01, 0, 58)\r\n      const average02 = this.checkNum(this.average02, 1, 59 - average01 || 0)\r\n      return average01 + '/' + average02;\r\n    },\r\n    // 计算勾选的checkbox值合集\r\n    checkboxString: function () {\r\n      let str = this.checkboxList.join();\r\n      return str == '' ? '*' : str;\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}