{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\accountInfo.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\accountInfo.vue", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": *************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_account", "require", "_index", "_interopRequireDefault", "_index2", "_store", "name", "computed", "confirmed", "components", "Confirmed", "dicts", "props", "data", "size", "$store", "state", "app", "loading", "openContent", "oopen", "title", "accountList", "form", "defaultCurrencyCode", "belongTo", "type", "belongToCompany", "company", "companyId", "queryParams", "pageNum", "pageSize", "bankAccId", "rules", "isOfficialAcc", "add", "staffList", "watch", "loadOptions", "open", "val", "$emit", "created", "loadAllStaffList", "methods", "getList", "_this", "accountOwnerId", "belongToStaff", "listAccount", "then", "response", "rows", "total", "cancel", "reset", "handleUpdate", "row", "_this2", "getAccount", "submitForm", "_this3", "$refs", "validate", "valid", "sqdBelongToCompanyCode", "companyIntlCode", "updateAccount", "$modal", "msgSuccess", "addAccount", "handleAdd", "handleDelete", "_this4", "accountIds", "ids", "$confirm", "customClass", "delAccount", "catch", "bankAccount", "accountOwnerid", "currencyId", "accountLocalName", "accountAddressLocalName", "accountEnName", "accountAddressEnName", "accountTaxcode", "accountSwiftcode", "accountIntlcode", "accountIsOfficial", "accountPriority", "remark", "salesConfirmed", "salesConfirmedId", "salesConfirmedName", "salesConfirmedDate", "psaConfirmed", "psaConfirmedId", "psaConfirmedName", "psaConfirmedDate", "accConfirmed", "accConfirmedId", "accConfirmedName", "accConfirmedDate", "opConfirmed", "opConfirmedId", "opConfirmedName", "opConfirmedDate", "resetForm", "getLocationId", "branchLocation", "getCurrencyId", "lock", "isLock", "handleClose", "done", "_", "_this5", "console", "log", "confirmState", "_this6", "allRsStaffList", "length", "redisList", "store", "dispatch", "getStaff", "id", "filter", "item", "staffId", "staffGivingEnName", "returnConfirmDept", "dept", "dept<PERSON>ock", "_this7", "user", "sid", "msgError", "financeLock", "_this8", "opLock", "_this9", "psaLock", "_this10", "exports", "default", "_default"], "sources": ["src/views/system/company/accountInfo.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      v-dialogDrag\r\n      v-dialogDragWidth\r\n      :close-on-click=\"false\"\r\n      :visible.sync=\"openContent\"\r\n      appear=\"fadeIn\"\r\n      class=\"accountInfo\"\r\n      title=\"银行信息\"\r\n      append-to-body\r\n      width=\"1800px\"\r\n    >\r\n      <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n          <el-button\r\n            v-hasPermi=\"['system:account:add']\"\r\n            :size=\"size\"\r\n            icon=\"el-icon-plus\"\r\n            plain\r\n            type=\"primary\"\r\n            @click=\"handleAdd\"\r\n          >新增\r\n          </el-button>\r\n        </el-col>\r\n      </el-row>\r\n      <!--账户列表-->\r\n      <el-table v-loading=\"loading\" :data=\"accountList\" border stripe>\r\n        <el-table-column align=\"center\" label=\"序号\" prop=\"bankAccId\" width=\"100px\"/>\r\n        <el-table-column align=\"center\" label=\"账号代码\" prop=\"bankAccCode\" width=\"100px\"/>\r\n        <el-table-column align=\"center\" label=\"账号归属\" prop=\"defaultCurrencyCode\" width=\"150px\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 class=\"column-text\">\r\n              {{ scope.row.benefitLocalName }}\r\n            </h6>\r\n            <h6 class=\"column-text\">\r\n              {{ scope.row.benefitTax }}\r\n            </h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"户名\" prop=\"benefitLocalName\" width=\"200px\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 class=\"column-text\">\r\n              {{ scope.row.benefitLocalName }}\r\n            </h6>\r\n            <h6 class=\"column-text\">\r\n              {{ scope.row.benefitEnName }}\r\n            </h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"币种账户\" prop=\"benefitEnName\" width=\"200px\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 class=\"column-text\">\r\n              {{\r\n                (scope.row.defaultCurrencyCode ? scope.row.defaultCurrencyCode : \"\") + (scope.row.isOfficialAcc == 0 ? \" 公\" : \" 私\") + \"户\"\r\n              }}\r\n            </h6>\r\n            <h6 class=\"column-text\">{{ scope.row.bankAccount }}</h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"开户行名址\" prop=\"bankName\" width=\"200px\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 class=\"column-text\">{{\r\n                scope.row.bankName ? scope.row.bankName + \":\" : \"\" + scope.row.bankBranchName\r\n              }} </h6>\r\n            <h6 class=\"column-text\">\r\n              {{ (scope.row.branchLocation ? (scope.row.branchLocation + \":\") : \"\") + scope.row.branchAddress }} </h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"汇款码\" prop=\"bankBranchName\" width=\"200px\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 class=\"column-text\">{{ scope.row.swiftCode }}</h6>\r\n            <h6 class=\"column-text\">{{ scope.row.intlExchangeCode }}</h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"账户状态\" prop=\"benefitTax\" width=\"100px\">\r\n          <template slot-scope=\"scope\">\r\n            {{ confirmState(scope.row) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"账户概要\" prop=\"bankAccSummary\" width=\"100px\">\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"录入人\" prop=\"branchAddress\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 class=\"column-text\">{{ scope.row.updateBy ? getStaff(scope.row.updateBy) : \"\" }}</h6>\r\n            <h6 class=\"column-text\">{{ scope.row.updateTime }}</h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"确认状态\" prop=\"isOfficialAcc\">\r\n          <template slot-scope=\"scope\">\r\n            <h6 class=\"column-text\">{{ returnConfirmDept(scope.row) }}</h6>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column align=\"center\" label=\"操作\" width=\"58px\">\r\n          <template slot-scope=\"scope\">\r\n            <div style=\"height: 15px\">\r\n              <el-button v-hasPermi=\"['system:account:edit']\" :size=\"size\" icon=\"el-icon-edit\"\r\n                         style=\"display: flex;padding: 0;margin: 0\"\r\n                         type=\"success\" @click=\"handleUpdate(scope.row)\"\r\n\r\n              >修改\r\n              </el-button>\r\n            </div>\r\n            <div style=\"height: 15px\">\r\n              <el-button v-hasPermi=\"['system:account:remove']\" :size=\"size\" icon=\"el-icon-delete\"\r\n                         style=\"display: flex;padding: 0;margin: 0\"\r\n                         type=\"danger\" @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n    <el-button type=\"primary\" @click=\"openContent = false\">确 定</el-button>\r\n    <el-button @click=\"openContent = false\">取 消</el-button>\r\n  </span>\r\n    </el-dialog>\r\n    <!-- 添加或修改银行账户对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag v-dialogDragWidth\r\n      :append-to-body=\"true\" :close-on-click-modal=\"false\" :title=\"title\" :visible.sync=\"oopen\" width=\"500px\"\r\n    >\r\n      <el-form ref=\"form\"\r\n               :disabled=\"this.form.salesConfirmed == 1 || this.form.accConfirmed == 1 || this.form.psaConfirmed == 1 || this.form.opConfirmed == 1\"\r\n               :model=\"form\" :rules=\"rules\" class=\"edit\"\r\n               label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"账户代码\" prop=\"bankAccCode\">\r\n          <el-input v-model=\"form.bankAccCode\" placeholder=\"银行账户代码\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"银行账号\" prop=\"bankAccount\">\r\n          <el-input v-model=\"form.bankAccount\" placeholder=\"银行账户号码\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"账户归属\" prop=\"accBelongsTo\">\r\n          {{\r\n            company.companyLocalName ? company.companyLocalName : company.companyShortName + \" \" + \" \" + \" \" + company.companyEnName\r\n          }}\r\n        </el-form-item>\r\n        <el-form-item label=\"中文户名\" prop=\"benefitLocalName\">\r\n          <el-input v-model=\"form.benefitLocalName\" placeholder=\"中文户名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文户名\" prop=\"benefitEnName\">\r\n          <el-input v-model=\"form.benefitEnName\" placeholder=\"中文户名\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"开户总行\" prop=\"bankName\">\r\n          <el-input v-model=\"form.bankName\" placeholder=\"开户总行\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"开户支行\" prop=\"bankBranchName\">\r\n          <el-input v-model=\"form.bankBranchName\" placeholder=\"开户支行\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"支行地区\">\r\n          <el-input v-model=\"form.bankBranchLocation\" placeholder=\"开户支行地区\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"支行详址\" prop=\"bankBranchAddress\">\r\n          <el-input v-model=\"form.bankBranchAddress\" placeholder=\"开户详址\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"英文地址\" prop=\"bankBranchAddressEnName\">\r\n          <el-input v-model=\"form.bankBranchAddressEnName\" placeholder=\"支行英文地址\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"公司税号\" prop=\"benefitTax\">\r\n          <el-input v-model=\"form.benefitTax\" placeholder=\"公司税号\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"SWIFT码\" prop=\"swiftCode\">\r\n          <el-input v-model=\"form.swiftCode\" placeholder=\"SWIFT编码\"/>\r\n        </el-form-item>\r\n        <!--<el-form-item label=\"国际汇码\" prop=\"intlExchangeCode\">\r\n          <el-input v-model=\"form.intlExchangeCode\" placeholder=\"国际汇款编码，用于非SWIFT的备用\"/>\r\n        </el-form-item>-->\r\n        <el-form-item label=\"币种\">\r\n          <tree-select\r\n            :disabled=\"this.form.salesConfirmed == 1 || this.form.accConfirmed == 1 || this.form.psaConfirmed == 1 || this.form.opConfirmed == 1\"\r\n            :pass=\"form.defaultCurrencyCode\" :type=\"'currency'\"\r\n            @return=\"getCurrencyId\"\r\n          />\r\n        </el-form-item>\r\n        <el-form-item label=\"是否公账\" prop=\"isOfficialAcc\">\r\n          <!--          <el-radio-group v-model=\"form.isOfficialAcc\">\r\n                      <el-radio label=\"0\">公账</el-radio>\r\n                      <el-radio label=\"1\">私账</el-radio>\r\n                      <el-radio label=\"2\">个人账</el-radio>\r\n                    </el-radio-group>-->\r\n          <el-switch\r\n            v-model=\"isOfficialAcc\"\r\n            active-text=\"公账\"\r\n            inactive-text=\"私账\"\r\n          >\r\n          </el-switch>\r\n        </el-form-item>\r\n        <el-form-item label=\"账户排序\" prop=\"accountPriority\">\r\n          <el-input v-model=\"form.accountPriority\" placeholder=\"银行卡优先级\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"账户概要\" prop=\"remark\">\r\n          <el-input v-model=\"form.bankAccSummary\" placeholder=\"账户概要\"/>\r\n        </el-form-item>\r\n        <el-row v-if=\"!add\">\r\n          <el-form-item label=\"是否已锁定\" prop=\"isLocked\">\r\n            <dict-tag :options=\"dict.type.sys_is_lock\" :value=\"form.isLocked\"/>\r\n          </el-form-item>\r\n        </el-row>\r\n      </el-form>\r\n      <div class=\"confirmed-list\">\r\n        <confirmed :id=\"'bankAccId'\" :confirmed=\"this.form.salesConfirmed==0\" :row=\"form\" :type=\"'sales'\"\r\n                   @lockMethod=\"updateAccount\"\r\n        />\r\n        <!--商务锁定-->\r\n        <confirmed :id=\"'bankAccId'\" :confirmed=\"this.form.psaConfirmed==0\" :row=\"form\" :type=\"'psa'\"\r\n                   @lockMethod=\"updateAccount\"\r\n        />\r\n        <!--操作锁定-->\r\n        <confirmed :id=\"'bankAccId'\" :confirmed=\"this.form.opConfirmed==0\" :row=\"form\" :type=\"'op'\"\r\n                   @lockMethod=\"updateAccount\"\r\n        />\r\n        <!--财务锁定-->\r\n        <confirmed :id=\"'bankAccId'\" :confirmed=\"this.form.accConfirmed==0\" :row=\"form\" :type=\"'acc'\"\r\n                   @lockMethod=\"updateAccount\"\r\n        />\r\n      </div>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nimport {addAccount, delAccount, getAccount, listAccount, updateAccount} from \"@/api/system/account\"\r\nimport company from \"@/views/system/company/index\"\r\nimport Confirmed from \"@/components/Confirmed/index.vue\"\r\nimport confirmed from \"@/components/Confirmed/index.vue\"\r\nimport store from \"@/store\"\r\n\r\nexport default {\r\n  name: \"accountInfo\",\r\n  computed: {\r\n    confirmed() {\r\n      return confirmed\r\n    }\r\n  },\r\n  components: {Confirmed},\r\n  dicts: [\"sys_account_type\", \"sys_is_confirm\", \"sys_is_lock\"],\r\n  props: [\"type\", \"open\", \"loadOptions\", \"company\", \"isLock\"],\r\n  data() {\r\n    return {\r\n      size: this.$store.state.app.size || \"mini\",\r\n      loading: true,\r\n      openContent: false,\r\n      oopen: false,\r\n      title: null,\r\n      accountList: [],\r\n      form: {\r\n        defaultCurrencyCode: \"RMB\"\r\n      },\r\n      belongTo: this.type == \"company\" ? 1 : this.type == \"staff\" ? 0 : \"\",\r\n      belongToCompany: this.company.companyId,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        bankAccId: this.bankAccId,\r\n        belongTo: this.belongTo\r\n      },\r\n      // 表单校验\r\n      rules: {},\r\n      isOfficialAcc: true,\r\n      add: true,\r\n      staffList: []\r\n    }\r\n  },\r\n  watch: {\r\n    loadOptions: function () {\r\n      this.accountList = this.loadOptions\r\n      this.loading = false\r\n    },\r\n    open: function (val) {\r\n      this.openContent = val\r\n    },\r\n    openContent(val) {\r\n      if (val == false) {\r\n        this.$emit(\"openAccounts\")\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.loadAllStaffList()\r\n  },\r\n  methods: {\r\n    /** 查询银行账户列表 */\r\n    getList() {\r\n      this.loading = true\r\n      this.queryParams.accountOwnerId = this.accountOwnerId\r\n      this.queryParams.belongTo = this.belongTo\r\n      this.belongTo === 1 ? this.queryParams.belongToCompany = this.company.companyId : this.queryParams.belongToStaff = this.company.companyId\r\n      listAccount(this.queryParams).then(response => {\r\n        this.accountList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.oopen = false\r\n      this.reset()\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const bankAccId = row.bankAccId\r\n      this.add = false\r\n      getAccount(bankAccId).then(response => {\r\n        this.form = response.data\r\n        this.isOfficialAcc = response.data.isOfficialAcc == 0 ? true : false\r\n        this.oopen = true\r\n        this.title = \"修改银行账户\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          this.form.belongToCompany = this.company.companyId\r\n          this.isOfficialAcc ? this.form.isOfficialAcc = 0 : this.form.isOfficialAcc = 1\r\n          this.form.sqdBelongToCompanyCode = this.company.companyIntlCode\r\n          if (this.form.bankAccId != null) {\r\n            updateAccount(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addAccount(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.oopen = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.oopen = true\r\n      this.form.isOfficialAcc = true\r\n      this.title = \"添加银行账户\"\r\n      this.add = true\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const accountIds = row.bankAccId || this.ids\r\n      this.$confirm(\"是否确认删除银行账户编号为\\\"\" + accountIds + \"\\\"的数据项？\", '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delAccount(accountIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankAccId: null,\r\n        bankAccount: null,\r\n        accountOwnerid: null,\r\n        currencyId: null,\r\n        accountLocalName: null,\r\n        accountAddressLocalName: null,\r\n        accountEnName: null,\r\n        accountAddressEnName: null,\r\n        accountTaxcode: null,\r\n        accountSwiftcode: null,\r\n        accountIntlcode: null,\r\n        accountIsOfficial: null,\r\n        accountPriority: null,\r\n        remark: null,\r\n        salesConfirmed: 0,\r\n        salesConfirmedId: null,\r\n        salesConfirmedName: null,\r\n        salesConfirmedDate: null,\r\n        psaConfirmed: 0,\r\n        psaConfirmedId: null,\r\n        psaConfirmedName: null,\r\n        psaConfirmedDate: null,\r\n        accConfirmed: 0,\r\n        accConfirmedId: null,\r\n        accConfirmedName: null,\r\n        accConfirmedDate: null,\r\n        opConfirmed: 0,\r\n        opConfirmedId: null,\r\n        opConfirmedName: null,\r\n        opConfirmedDate: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    getLocationId(val) {\r\n      this.form.branchLocation = val\r\n    },\r\n    getCurrencyId(val) {\r\n      this.form.defaultCurrencyCode = val\r\n    },\r\n    lock() {\r\n      if (this.isLock) {\r\n        return false\r\n      } else {\r\n        return true\r\n      }\r\n    },\r\n    handleClose(done) {\r\n      this.$confirm(\"确认关闭？\", '提示', {customClass: 'modal-confirm'})\r\n        .then(_ => {\r\n          done()\r\n        })\r\n        .catch(_ => {\r\n        })\r\n    },\r\n    updateAccount(form) {\r\n      console.log(form)\r\n      updateAccount(form).then(response => {\r\n        this.$modal.msgSuccess(\"修改成功\")\r\n        getAccount(this.form.bankAccId).then(response => {\r\n          this.form = response.data\r\n        })\r\n      })\r\n    },\r\n    confirmState(row) {\r\n      if (row.salesConfirmed == 1 && row.accConfirmed == 1 && row.psaConfirmed == 1 && row.opConfirmed == 1) {\r\n        return \"审核完成\"\r\n      } else if (row.salesConfirmed == 1 || row.accConfirmed == 1 || row.psaConfirmed == 1 || row.opConfirmed == 1) {\r\n        return \"审核中\"\r\n      } else {\r\n        return \"待审核\"\r\n      }\r\n    },\r\n    // 加载所有用户到缓存中\r\n    loadAllStaffList() {\r\n      if (this.$store.state.data.allRsStaffList.length == 0 || this.$store.state.data.redisList.allRsStaffList) {\r\n        store.dispatch(\"getAllRsStaffList\").then(() => {\r\n          this.staffList = this.$store.state.data.allRsStaffList\r\n        })\r\n      } else {\r\n        this.staffList = this.$store.state.data.allRsStaffList\r\n      }\r\n    },\r\n    // 根据id获取用户名\r\n    getStaff(id) {\r\n      return this.staffList.filter(item => item.staffId === id)[0] ? this.staffList.filter(item => item.staffId === id)[0].staffGivingEnName : \"\"\r\n    },\r\n    // 返回各部门确认状态\r\n    returnConfirmDept(row) {\r\n      let dept = \"\"\r\n      if (row.salesConfirmed == 1) {\r\n        dept ? dept += \"/\" : \"\"\r\n        dept += \"业务\"\r\n      }\r\n      if (row.accConfirmed == 1) {\r\n        dept ? dept += \"/\" : \"\"\r\n        dept += \"财务\"\r\n      }\r\n      if (row.psaConfirmed == 1) {\r\n        dept ? dept += \"/\" : \"\"\r\n        dept += \"商务\"\r\n      }\r\n      if (row.opConfirmed == 1) {\r\n        dept ? dept += \"/\" : \"\"\r\n        dept += \"操作\"\r\n      }\r\n      return dept\r\n    },\r\n    deptLock() {\r\n      if (this.form.bankAccId != null) {\r\n        this.form.salesConfirmed = this.form.salesConfirmed == 0 ? 1 : 0\r\n        this.form.salesConfirmedId = this.$store.state.user.sid\r\n        updateAccount(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.oopen = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    financeLock() {\r\n      if (this.form.bankAccId != null) {\r\n        this.form.accConfirmed = this.form.accConfirmed == 0 ? 1 : 0\r\n        this.form.accConfirmedId = this.$store.state.user.sid\r\n        updateAccount(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.oopen = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    opLock() {\r\n      if (this.form.bankAccId != null) {\r\n        this.form.accConfirmed = this.form.accConfirmed == 0 ? 1 : 0\r\n        this.form.accConfirmedId = this.$store.state.user.sid\r\n        updateAccount(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.oopen = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    },\r\n    psaLock() {\r\n      if (this.form.bankAccId != null) {\r\n        this.form.accConfirmed = this.form.accConfirmed == 0 ? 1 : 0\r\n        this.form.accConfirmedId = this.$store.state.user.sid\r\n        updateAccount(this.form).then(response => {\r\n          this.$modal.msgSuccess(\"修改成功\")\r\n          this.oopen = false\r\n          this.getList()\r\n        })\r\n      } else {\r\n        this.$modal.msgError(\"错误操作\")\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.confirmed-list {\r\n  display: flex;\r\n  flex-direction: row;\r\n  justify-content: space-between;\r\n  flex-wrap: nowrap;\r\n}\r\n\r\n.column-text {\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;AAsOA,IAAAA,QAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,OAAA,GAAAD,sBAAA,CAAAF,OAAA;AAEA,IAAAI,MAAA,GAAAF,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,IAAA;EACAC,QAAA;IACAC,SAAA,WAAAA,UAAA;MACA,OAAAA,eAAA;IACA;EACA;EACAC,UAAA;IAAAC,SAAA,EAAAA;EAAA;EACAC,KAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,OAAAC,MAAA,CAAAC,KAAA,CAAAC,GAAA,CAAAH,IAAA;MACAI,OAAA;MACAC,WAAA;MACAC,KAAA;MACAC,KAAA;MACAC,WAAA;MACAC,IAAA;QACAC,mBAAA;MACA;MACAC,QAAA,OAAAC,IAAA,yBAAAA,IAAA;MACAC,eAAA,OAAAC,OAAA,CAAAC,SAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,SAAA,OAAAA,SAAA;QACAR,QAAA,OAAAA;MACA;MACA;MACAS,KAAA;MACAC,aAAA;MACAC,GAAA;MACAC,SAAA;IACA;EACA;EACAC,KAAA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAjB,WAAA,QAAAiB,WAAA;MACA,KAAArB,OAAA;IACA;IACAsB,IAAA,WAAAA,KAAAC,GAAA;MACA,KAAAtB,WAAA,GAAAsB,GAAA;IACA;IACAtB,WAAA,WAAAA,YAAAsB,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,KAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACA,eACAC,OAAA,WAAAA,QAAA;MAAA,IAAAC,KAAA;MACA,KAAA7B,OAAA;MACA,KAAAY,WAAA,CAAAkB,cAAA,QAAAA,cAAA;MACA,KAAAlB,WAAA,CAAAL,QAAA,QAAAA,QAAA;MACA,KAAAA,QAAA,cAAAK,WAAA,CAAAH,eAAA,QAAAC,OAAA,CAAAC,SAAA,QAAAC,WAAA,CAAAmB,aAAA,QAAArB,OAAA,CAAAC,SAAA;MACA,IAAAqB,oBAAA,OAAApB,WAAA,EAAAqB,IAAA,WAAAC,QAAA;QACAL,KAAA,CAAAzB,WAAA,GAAA8B,QAAA,CAAAC,IAAA;QACAN,KAAA,CAAAO,KAAA,GAAAF,QAAA,CAAAE,KAAA;QACAP,KAAA,CAAA7B,OAAA;MACA;IACA;IACA;IACAqC,MAAA,WAAAA,OAAA;MACA,KAAAnC,KAAA;MACA,KAAAoC,KAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAH,KAAA;MACA,IAAAvB,SAAA,GAAAyB,GAAA,CAAAzB,SAAA;MACA,KAAAG,GAAA;MACA,IAAAwB,mBAAA,EAAA3B,SAAA,EAAAkB,IAAA,WAAAC,QAAA;QACAO,MAAA,CAAApC,IAAA,GAAA6B,QAAA,CAAAvC,IAAA;QACA8C,MAAA,CAAAxB,aAAA,GAAAiB,QAAA,CAAAvC,IAAA,CAAAsB,aAAA;QACAwB,MAAA,CAAAvC,KAAA;QACAuC,MAAA,CAAAtC,KAAA;MACA;IACA;IACA,WACAwC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,SAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAAvC,IAAA,CAAAI,eAAA,GAAAmC,MAAA,CAAAlC,OAAA,CAAAC,SAAA;UACAiC,MAAA,CAAA3B,aAAA,GAAA2B,MAAA,CAAAvC,IAAA,CAAAY,aAAA,OAAA2B,MAAA,CAAAvC,IAAA,CAAAY,aAAA;UACA2B,MAAA,CAAAvC,IAAA,CAAA2C,sBAAA,GAAAJ,MAAA,CAAAlC,OAAA,CAAAuC,eAAA;UACA,IAAAL,MAAA,CAAAvC,IAAA,CAAAU,SAAA;YACA,IAAAmC,sBAAA,EAAAN,MAAA,CAAAvC,IAAA,EAAA4B,IAAA,WAAAC,QAAA;cACAU,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAA1C,KAAA;cACA0C,MAAA,CAAAhB,OAAA;YACA;UACA;YACA,IAAAyB,mBAAA,EAAAT,MAAA,CAAAvC,IAAA,EAAA4B,IAAA,WAAAC,QAAA;cACAU,MAAA,CAAAO,MAAA,CAAAC,UAAA;cACAR,MAAA,CAAA1C,KAAA;cACA0C,MAAA,CAAAhB,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA0B,SAAA,WAAAA,UAAA;MACA,KAAAhB,KAAA;MACA,KAAApC,KAAA;MACA,KAAAG,IAAA,CAAAY,aAAA;MACA,KAAAd,KAAA;MACA,KAAAe,GAAA;IACA;IACA,aACAqC,YAAA,WAAAA,aAAAf,GAAA;MAAA,IAAAgB,MAAA;MACA,IAAAC,UAAA,GAAAjB,GAAA,CAAAzB,SAAA,SAAA2C,GAAA;MACA,KAAAC,QAAA,qBAAAF,UAAA;QAAAG,WAAA;MAAA,GAAA3B,IAAA;QACA,WAAA4B,mBAAA,EAAAJ,UAAA;MACA,GAAAxB,IAAA;QACAuB,MAAA,CAAA5B,OAAA;QACA4B,MAAA,CAAAL,MAAA,CAAAC,UAAA;MACA,GAAAU,KAAA,cACA;IACA;IACA;IACAxB,KAAA,WAAAA,MAAA;MACA,KAAAjC,IAAA;QACAU,SAAA;QACAgD,WAAA;QACAC,cAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,uBAAA;QACAC,aAAA;QACAC,oBAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC,eAAA;QACAC,MAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,eAAA;QACAC,eAAA;MACA;MACA,KAAAC,SAAA;IACA;IACAC,aAAA,WAAAA,cAAAtE,GAAA;MACA,KAAAlB,IAAA,CAAAyF,cAAA,GAAAvE,GAAA;IACA;IACAwE,aAAA,WAAAA,cAAAxE,GAAA;MACA,KAAAlB,IAAA,CAAAC,mBAAA,GAAAiB,GAAA;IACA;IACAyE,IAAA,WAAAA,KAAA;MACA,SAAAC,MAAA;QACA;MACA;QACA;MACA;IACA;IACAC,WAAA,WAAAA,YAAAC,IAAA;MACA,KAAAxC,QAAA;QAAAC,WAAA;MAAA,GACA3B,IAAA,WAAAmE,CAAA;QACAD,IAAA;MACA,GACArC,KAAA,WAAAsC,CAAA,GACA;IACA;IACAlD,aAAA,WAAAA,cAAA7C,IAAA;MAAA,IAAAgG,MAAA;MACAC,OAAA,CAAAC,GAAA,CAAAlG,IAAA;MACA,IAAA6C,sBAAA,EAAA7C,IAAA,EAAA4B,IAAA,WAAAC,QAAA;QACAmE,MAAA,CAAAlD,MAAA,CAAAC,UAAA;QACA,IAAAV,mBAAA,EAAA2D,MAAA,CAAAhG,IAAA,CAAAU,SAAA,EAAAkB,IAAA,WAAAC,QAAA;UACAmE,MAAA,CAAAhG,IAAA,GAAA6B,QAAA,CAAAvC,IAAA;QACA;MACA;IACA;IACA6G,YAAA,WAAAA,aAAAhE,GAAA;MACA,IAAAA,GAAA,CAAAoC,cAAA,SAAApC,GAAA,CAAA4C,YAAA,SAAA5C,GAAA,CAAAwC,YAAA,SAAAxC,GAAA,CAAAgD,WAAA;QACA;MACA,WAAAhD,GAAA,CAAAoC,cAAA,SAAApC,GAAA,CAAA4C,YAAA,SAAA5C,GAAA,CAAAwC,YAAA,SAAAxC,GAAA,CAAAgD,WAAA;QACA;MACA;QACA;MACA;IACA;IACA;IACA9D,gBAAA,WAAAA,iBAAA;MAAA,IAAA+E,MAAA;MACA,SAAA5G,MAAA,CAAAC,KAAA,CAAAH,IAAA,CAAA+G,cAAA,CAAAC,MAAA,cAAA9G,MAAA,CAAAC,KAAA,CAAAH,IAAA,CAAAiH,SAAA,CAAAF,cAAA;QACAG,cAAA,CAAAC,QAAA,sBAAA7E,IAAA;UACAwE,MAAA,CAAAtF,SAAA,GAAAsF,MAAA,CAAA5G,MAAA,CAAAC,KAAA,CAAAH,IAAA,CAAA+G,cAAA;QACA;MACA;QACA,KAAAvF,SAAA,QAAAtB,MAAA,CAAAC,KAAA,CAAAH,IAAA,CAAA+G,cAAA;MACA;IACA;IACA;IACAK,QAAA,WAAAA,SAAAC,EAAA;MACA,YAAA7F,SAAA,CAAA8F,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA,KAAAH,EAAA;MAAA,aAAA7F,SAAA,CAAA8F,MAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAC,OAAA,KAAAH,EAAA;MAAA,MAAAI,iBAAA;IACA;IACA;IACAC,iBAAA,WAAAA,kBAAA7E,GAAA;MACA,IAAA8E,IAAA;MACA,IAAA9E,GAAA,CAAAoC,cAAA;QACA0C,IAAA,GAAAA,IAAA;QACAA,IAAA;MACA;MACA,IAAA9E,GAAA,CAAA4C,YAAA;QACAkC,IAAA,GAAAA,IAAA;QACAA,IAAA;MACA;MACA,IAAA9E,GAAA,CAAAwC,YAAA;QACAsC,IAAA,GAAAA,IAAA;QACAA,IAAA;MACA;MACA,IAAA9E,GAAA,CAAAgD,WAAA;QACA8B,IAAA,GAAAA,IAAA;QACAA,IAAA;MACA;MACA,OAAAA,IAAA;IACA;IACAC,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,SAAAnH,IAAA,CAAAU,SAAA;QACA,KAAAV,IAAA,CAAAuE,cAAA,QAAAvE,IAAA,CAAAuE,cAAA;QACA,KAAAvE,IAAA,CAAAwE,gBAAA,QAAAhF,MAAA,CAAAC,KAAA,CAAA2H,IAAA,CAAAC,GAAA;QACA,IAAAxE,sBAAA,OAAA7C,IAAA,EAAA4B,IAAA,WAAAC,QAAA;UACAsF,MAAA,CAAArE,MAAA,CAAAC,UAAA;UACAoE,MAAA,CAAAtH,KAAA;UACAsH,MAAA,CAAA5F,OAAA;QACA;MACA;QACA,KAAAuB,MAAA,CAAAwE,QAAA;MACA;IACA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAxH,IAAA,CAAAU,SAAA;QACA,KAAAV,IAAA,CAAA+E,YAAA,QAAA/E,IAAA,CAAA+E,YAAA;QACA,KAAA/E,IAAA,CAAAgF,cAAA,QAAAxF,MAAA,CAAAC,KAAA,CAAA2H,IAAA,CAAAC,GAAA;QACA,IAAAxE,sBAAA,OAAA7C,IAAA,EAAA4B,IAAA,WAAAC,QAAA;UACA2F,MAAA,CAAA1E,MAAA,CAAAC,UAAA;UACAyE,MAAA,CAAA3H,KAAA;UACA2H,MAAA,CAAAjG,OAAA;QACA;MACA;QACA,KAAAuB,MAAA,CAAAwE,QAAA;MACA;IACA;IACAG,MAAA,WAAAA,OAAA;MAAA,IAAAC,MAAA;MACA,SAAA1H,IAAA,CAAAU,SAAA;QACA,KAAAV,IAAA,CAAA+E,YAAA,QAAA/E,IAAA,CAAA+E,YAAA;QACA,KAAA/E,IAAA,CAAAgF,cAAA,QAAAxF,MAAA,CAAAC,KAAA,CAAA2H,IAAA,CAAAC,GAAA;QACA,IAAAxE,sBAAA,OAAA7C,IAAA,EAAA4B,IAAA,WAAAC,QAAA;UACA6F,MAAA,CAAA5E,MAAA,CAAAC,UAAA;UACA2E,MAAA,CAAA7H,KAAA;UACA6H,MAAA,CAAAnG,OAAA;QACA;MACA;QACA,KAAAuB,MAAA,CAAAwE,QAAA;MACA;IACA;IACAK,OAAA,WAAAA,QAAA;MAAA,IAAAC,OAAA;MACA,SAAA5H,IAAA,CAAAU,SAAA;QACA,KAAAV,IAAA,CAAA+E,YAAA,QAAA/E,IAAA,CAAA+E,YAAA;QACA,KAAA/E,IAAA,CAAAgF,cAAA,QAAAxF,MAAA,CAAAC,KAAA,CAAA2H,IAAA,CAAAC,GAAA;QACA,IAAAxE,sBAAA,OAAA7C,IAAA,EAAA4B,IAAA,WAAAC,QAAA;UACA+F,OAAA,CAAA9E,MAAA,CAAAC,UAAA;UACA6E,OAAA,CAAA/H,KAAA;UACA+H,OAAA,CAAArG,OAAA;QACA;MACA;QACA,KAAAuB,MAAA,CAAAwE,QAAA;MACA;IACA;EACA;AACA;AAAAO,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}