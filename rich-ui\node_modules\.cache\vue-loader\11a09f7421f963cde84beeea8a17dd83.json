{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\booking\\psa.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\booking\\psa.vue", "mtime": 1754876882574}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KDQppbXBvcnQgYm9va2luZyBmcm9tICJAL3ZpZXdzL3N5c3RlbS9ib29raW5nL2luZGV4IjsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAicHNhIiwNCiAgY29tcG9uZW50czoge2Jvb2tpbmd9LA0KfQ0K"}, {"version": 3, "sources": ["psa.vue"], "names": [], "mappings": ";;;;;AAKA;;AAEA;AACA;AACA;AACA", "file": "psa.vue", "sourceRoot": "src/views/system/booking", "sourcesContent": ["<template>\r\n  <booking :type=\"'psa'\"/>\r\n</template>\r\n\r\n<script>\r\nimport booking from \"@/views/system/booking/index\";\r\n\r\nexport default {\r\n  name: \"psa\",\r\n  components: {booking},\r\n}\r\n</script>\r\n"]}]}