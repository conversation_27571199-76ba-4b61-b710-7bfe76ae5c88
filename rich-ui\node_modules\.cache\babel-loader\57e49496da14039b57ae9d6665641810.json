{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\imgPreview.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\rct\\imgPreview.vue", "mtime": 1754876882596}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiaW1nUHJldmlldyIsCiAgcHJvcHM6IFsic2NvcGUiXSwKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgcHJldmlld0ltZ09wZW46IGZhbHNlCiAgICB9OwogIH0KfTsKZXhwb3J0cy5kZWZhdWx0ID0gX2RlZmF1bHQ7"}, {"version": 3, "names": ["name", "props", "data", "previewImgOpen", "exports", "default", "_default"], "sources": ["src/views/system/rct/imgPreview.vue"], "sourcesContent": ["<template>\r\n  <div>\r\n    <el-button\r\n      v-if=\"scope.row.slipFile\"\r\n      size=\"mini\"\r\n      type=\"text\"\r\n      @click=\"previewImgOpen=true\"\r\n      icon=\"el-icon-search\"\r\n    >\r\n    </el-button>\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\"\r\n      :title=\"'银行水单'\" :visible.sync=\"previewImgOpen\"\r\n      append-to-body\r\n      height=\"50%\"\r\n      width=\"50%\"\r\n    >\r\n      <el-image :src=\"scope.row.slipFile\"/>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  name: \"imgPreview\",\r\n  props: [\"scope\"],\r\n  data() {\r\n    return {\r\n      previewImgOpen: false\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;eAuBA;EACAA,IAAA;EACAC,KAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,cAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}