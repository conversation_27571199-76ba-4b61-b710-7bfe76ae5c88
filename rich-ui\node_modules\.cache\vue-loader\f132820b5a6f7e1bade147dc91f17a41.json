{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Message\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\components\\Message\\index.vue", "mtime": 1754876882532}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+CA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/components/Message", "sourcesContent": ["<template>\r\n  <div class=\"header-search\">\r\n    <el-popover\r\n      placement=\"bottom\"\r\n      trigger=\"hover\"\r\n      width=\"400\"\r\n      @show=\"showMessage\">\r\n      <el-badge slot=\"reference\" :hidden=\"length==0\" :value=\"length>99?'···':length\">\r\n        <i :class=\"length==0?'el-icon-chat-round':'el-icon-chat-dot-round'\"/>\r\n      </el-badge>\r\n      <el-row>\r\n        <div style=\"display:flex;float: right\">\r\n          <h5 style=\"margin: 0;display: contents\">打开提示：</h5>\r\n          <el-switch\r\n            v-model=\"open\"\r\n            active-value=\"true\"\r\n            inactive-value=\"false\"\r\n            @change=\"setMessageNotice\"/>\r\n        </div>\r\n      </el-row>\r\n      <el-row v-infinite-scroll=\"load\" class=\"infinite-list\" style=\"overflow:auto;height:500px\">\r\n        <div\r\n          v-for=\"data of dataList\"\r\n          id=\"id\"\r\n          class=\"infinite-list-item hover-effect\"\r\n          style=\"cursor: pointer;\"\r\n          @click=\"toPath(data.messageFrom)\">\r\n          <el-row>\r\n            <div style=\"font-weight:bold;\">{{ data.messageTitle }}</div>\r\n          </el-row>\r\n          <el-row>\r\n            <div style=\"margin: 5px;margin-left: 10px\">{{ data.messageContent }}</div>\r\n          </el-row>\r\n          <el-row>\r\n            <div style=\"float: left;font-size: smaller;display: contents\">{{ data.messageTypeName }}</div>\r\n            <div style=\"float: right;color: lightgrey\">{{ formatDate(data.createTime) }}</div>\r\n          </el-row>\r\n        </div>\r\n        <el-button v-if=\"showHistory\" style=\"color: lightgrey;width: 100%\" @click=\"resolvePath\">\r\n          查看全部历史消息\r\n        </el-button>\r\n      </el-row>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {createSocket} from \"@/utils/webSocket\";\r\nimport {countNewMessage, listMessage} from \"@/api/system/message\";\r\nimport {formatDate, formatTime} from \"@/utils\";\r\n\r\nexport default {\r\n  name: \"Message\",\r\n  data() {\r\n    return {\r\n      dataList: [],\r\n      getSocketData: null,\r\n      length: 0,\r\n      showHistory: false,\r\n      open: false,\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        messageOwner: this.$store.state.user.sid,\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.initWebSocket();\r\n    this.countNewMessage();\r\n  },\r\n  destroyed() {\r\n    //移除监听器\r\n    window.removeEventListener('onmessageWS', this.getSocketData);\r\n  },\r\n  methods: {\r\n    formatDate(val) {\r\n      return formatTime(new Date(val));\r\n    },\r\n    resolvePath() {\r\n      this.$tab.openPage(\"查看全部历史\", '/internal/message', {pageNum: 1});\r\n    },\r\n    toPath(val) {\r\n      this.$tab.openPage(\"修改海运费\", '/enter/freight', {data: val, pageNum: 1});\r\n    },\r\n    showMessage() {\r\n      this.showHistory = false\r\n      localStorage.setItem(\"lastTimeTap\", formatDate(new Date()))\r\n      if (this.dataList.length == 0) {\r\n        this.dataList = []\r\n        listMessage(this.queryParams).then(response => {\r\n          for (const r of response.rows) {\r\n            this.dataList.push(r)\r\n          }\r\n          if (response.total <= 10)\r\n            this.showHistory = true\r\n          this.length = 0;\r\n        })\r\n      } else {\r\n        this.length = 0\r\n      }\r\n    },\r\n    load() {\r\n      if (this.queryParams.pageNum == 3 || this.dataList.length > 30 || this.isFull) {\r\n        this.showHistory = true\r\n      } else {\r\n        this.showHistory = false\r\n        this.queryParams.pageNum += 1\r\n        listMessage(this.queryParams).then(response => {\r\n          if (response.rows.length > 0) {\r\n            for (const r of response.rows) {\r\n              this.dataList.push(r)\r\n            }\r\n          }\r\n          const totalPage = response.total\r\n          if ((totalPage < 10 && this.queryParams.pageNum == 1) || (10 < totalPage <= 20 && this.queryParams.pageNum == 2) || (20 < totalPage <= 30 && this.queryParams.pageNum == 3)) {\r\n            this.showHistory = true\r\n            this.isFull = true\r\n          }\r\n        })\r\n      }\r\n    },\r\n    countNewMessage() {\r\n      const time = localStorage.getItem('lastTimeTap')\r\n      this.open = localStorage.getItem(\"messageNotice\")\r\n      let data = {\r\n        messageOwner: this.$store.state.user.sid,\r\n        createTime: time\r\n      }\r\n      countNewMessage(data).then(response => {\r\n        this.length = response.data\r\n      })\r\n    },\r\n    setMessageNotice() {\r\n      localStorage.setItem(\"messageNotice\", this.open)\r\n    },\r\n    initWebSocket() {\r\n      this.uid = this.$store.state.user.sid + this.$store.state.user.name.substring(0, 8);\r\n      createSocket((window.location.protocol === 'https:' ? 'wss://sys.richgz.com/' : 'ws://************:8088/') + 'websocket/' + this.uid)\r\n      // createSocket('wss://**********:8088/websocket/' + this.uid)\r\n      this.getSocketData = e => {\r\n        if (this.length == null) {\r\n          this.length = 1\r\n        } else {\r\n          ++this.length\r\n        }\r\n        if (this.open) {\r\n          this.$message.info(e.detail.data)\r\n        }\r\n      }\r\n      window.addEventListener('onmessageWS', this.getSocketData)\r\n    }\r\n  },\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.el-badge__content.is-fixed {\r\n  top: 15px;\r\n}\r\n\r\n.infinite-list-item {\r\n  &.hover-effect {\r\n    cursor: pointer;\r\n    transition: background .3s;\r\n\r\n    &:hover {\r\n      background: rgba(0, 0, 0, .125)\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}