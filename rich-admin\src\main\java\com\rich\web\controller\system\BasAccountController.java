package com.rich.web.controller.system;

import com.rich.common.annotation.Log;
import com.rich.common.constant.CacheConstants;
import com.rich.common.core.controller.BaseController;
import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.BasAccount;
import com.rich.common.core.domain.entity.BasCarrier;
import com.rich.common.core.page.TableDataInfo;
import com.rich.common.core.redis.RedisCache;
import com.rich.common.enums.BusinessType;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.poi.ExcelUtil;
import com.rich.system.service.BasAccountService;
import com.rich.system.service.impl.RedisCacheImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 银行账户Controller
 *
 * <AUTHOR>
 * @date 2022-10-09
 */
@RestController
@RequestMapping("/system/account")
public class BasAccountController extends BaseController {
    @Autowired
    private BasAccountService basAccountService;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private RedisCacheImpl RedisCache;

    /**
     * 查询银行账户列表
     */
    @PreAuthorize("@ss.hasAnyPermi('system:account:list,system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(BasAccount basAccount) {
        startPage();
        List<BasAccount> list = basAccountService.selectBasAccountList(basAccount);
        return getDataTable(list);
    }

    /**
     * 导出银行账户列表1
     */
    @PreAuthorize("@ss.hasPermi('system:account:export')")
    @Log(title = "银行账户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BasAccount basAccount) {
        List<BasAccount> list = basAccountService.selectBasAccountList(basAccount);
        ExcelUtil<BasAccount> util = new ExcelUtil<BasAccount>(BasAccount.class);
        util.exportExcel(response, list, "银行账户数据");
    }

    /**
     * 获取银行账户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:account:edit')")
    @GetMapping(value = "/{accountId}")
    public AjaxResult getInfo(@PathVariable("accountId") Long accountId) {
        return AjaxResult.success(basAccountService.selectBasAccountByAccountId(accountId));
    }

    /**
     * 新增银行账户
     */
    @PreAuthorize("@ss.hasPermi('system:account:add')")
    @Log(title = "银行账户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BasAccount basAccount) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyAccount");
        return toAjax(basAccountService.insertBasAccount(basAccount));
    }

    /**
     * 修改银行账户
     */
    @PreAuthorize("@ss.hasPermi('system:account:edit')")
    @Log(title = "银行账户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BasAccount basAccount) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyAccount");
        //如果确认有确认 更新为后端时间
        if (basAccount.getAccConfirmed().equals("1") && basAccount.getSalesConfirmed() != null) {
            basAccount.setAccConfirmedDate(DateUtils.getNowDate());
        }
        if (basAccount.getSalesConfirmed().equals("1") && basAccount.getAccConfirmed() != null) {
            basAccount.setSalesConfirmedDate(DateUtils.getNowDate());
        }
        if (basAccount.getOpConfirmed().equals("1") && basAccount.getSalesConfirmed() != null) {
            basAccount.setOpConfirmedDate(DateUtils.getNowDate());
        }
        if (basAccount.getPsaConfirmed().equals("1") && basAccount.getAccConfirmed() != null) {
            basAccount.setPsaConfirmedDate(DateUtils.getNowDate());
        }
        //如果有确认则锁定
        if (basAccount.getSalesConfirmed().equals("1") || basAccount.getAccConfirmed().equals("1") || basAccount.getOpConfirmed().equals("1") || basAccount.getPsaConfirmed().equals("1")) {
            basAccount.setIsLocked("1");
        } else {
            basAccount.setIsLocked("0");
        }
        return toAjax(basAccountService.updateBasAccount(basAccount));
    }

    /**
     * 删除银行账户
     */
    @PreAuthorize("@ss.hasPermi('system:account:remove')")
    @Log(title = "银行账户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{accountIds}")
    public AjaxResult remove(@PathVariable Long[] accountIds) {
        redisCache.deleteObject(CacheConstants.DATA_CACHE_KEY + "companyAccount");
        return toAjax(basAccountService.deleteBasAccountByAccountIds(accountIds));
    }

    @GetMapping("/companyAccount")
    public AjaxResult companyAccount() {
        List<BasCarrier> list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "companyAccount");
        if (list == null) {
            RedisCache.companyAccount();
            list = redisCache.getCacheObject(CacheConstants.DATA_CACHE_KEY + "companyAccount");
        }
        return AjaxResult.success(AjaxResult.DATA_TAG, list);
    }

    /**
     * 根据所属公司查询银行账户列表
     */
    @PreAuthorize("@ss.hasAnyPermi('system:account:list,system:user:list')")
    @GetMapping("/company/{companyId}")
    public AjaxResult listByCompany(@PathVariable("companyId") Long companyId) {
        BasAccount basAccount = new BasAccount();
        basAccount.setBelongToCompany(companyId);
        List<BasAccount> list = basAccountService.selectBasAccountList(basAccount);
        return AjaxResult.success(list);
    }
}
