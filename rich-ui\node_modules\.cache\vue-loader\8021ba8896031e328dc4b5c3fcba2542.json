{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\staffrole\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\staffrole\\index.vue", "mtime": 1754876882598}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRTdGFmZnJvbGUsDQogIGRlbFN0YWZmcm9sZSwNCiAgZ2V0U3RhZmZyb2xlLA0KICBsaXN0U3RhZmZyb2xlLA0KICBsaXN0U3RhZmZyb2xlTWVudSwNCiAgdXBkYXRlU3RhZmZyb2xlDQp9IGZyb20gIkAvYXBpL3N5c3RlbS9zdGFmZnJvbGUiOw0KaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiOw0KaW1wb3J0ICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdC9kaXN0L3Z1ZS10cmVlc2VsZWN0Lm1pbi5jc3MiOw0KaW1wb3J0IHtzZWxlY3RMaXN0VXNlcn0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJTdGFmZnJvbGUiLA0KICBkaWN0czogWydzeXNfeWVzX25vJ10sDQogIGNvbXBvbmVudHM6IHtUcmVlc2VsZWN0fSwNCiAgZGF0YSgpIHsNCiAgICByZXR1cm4gew0KICAgICAgc2hvd0xlZnQ6IDMsDQogICAgICBzaG93UmlnaHQ6IDIxLA0KICAgICAgdG90YWw6IDAsDQogICAgICB1c2VyTGlzdDogW10sDQogICAgICBkZWZhdWx0UHJvcHM6IHsNCiAgICAgICAgY2hpbGRyZW46ICJjaGlsZHJlbiIsDQogICAgICAgIGxhYmVsOiAibGFiZWwiDQogICAgICB9LA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgLy8g6YCJ5Lit5pWw57uEDQogICAgICBpZHM6IFtdLA0KICAgICAgLy8g6Z2e5Y2V5Liq56aB55SoDQogICAgICBzaW5nbGU6IHRydWUsDQogICAgICAvLyDpnZ7lpJrkuKrnpoHnlKgNCiAgICAgIG11bHRpcGxlOiB0cnVlLA0KICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2DQogICAgICBzaG93U2VhcmNoOiB0cnVlLA0KICAgICAgLy8g5p2D6ZmQ5YiG6YWN6KGo5qC85pWw5o2uDQogICAgICBzdGFmZnJvbGVMaXN0OiBbXSwNCiAgICAgIC8vIOW8ueWHuuWxguagh+mimA0KICAgICAgdGl0bGU6ICIiLA0KICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGCDQogICAgICBvcGVuOiBmYWxzZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDIwLA0KICAgICAgICByb2xlSWQ6IG51bGwsDQogICAgICAgIGRlcHRJZDogbnVsbCwNCiAgICAgICAgaXNNYWluOiBudWxsLA0KICAgICAgICBzdGFmZklkOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIC8vIOihqOWNleagoemqjA0KICAgICAgcnVsZXM6IHsNCiAgICAgICAgc3RhZmZJZDogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgdHJpZ2dlcjogImJsdXIiDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICByb2xlSWQ6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwNCiAgICAgICAgICAgIG1lc3NhZ2U6ICLmnYPpmZDkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciINCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIGlzTWFpbjogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLA0KICAgICAgICAgICAgbWVzc2FnZTogIuaYr+WQpuS4u+imgeadg+mZkOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIg0KICAgICAgICAgIH0NCiAgICAgICAgXSwNCiAgICAgICAgZGVwdElkOiBbDQogICAgICAgICAgew0KICAgICAgICAgICAgcmVxdWlyZWQ6IHRydWUsDQogICAgICAgICAgICBtZXNzYWdlOiAi6YOo6ZeoSUTkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciINCiAgICAgICAgICB9DQogICAgICAgIF0NCiAgICAgIH0NCiAgICB9Ow0KICB9LA0KICB3YXRjaDogew0KICAgIHNob3dTZWFyY2gobikgew0KICAgICAgaWYgKG4gPT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxDQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAzDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0DQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAwDQogICAgICB9DQogICAgfSwNCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICB0aGlzLmdldExpc3QoKTsNCiAgICBzZWxlY3RMaXN0VXNlcigpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgdGhpcy51c2VyTGlzdCA9IHJlc3BvbnNlLmRhdGENCiAgICB9KQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgLyoqIOafpeivouadg+mZkOWIhumFjeWIl+ihqCAqLw0KICAgIGdldExpc3QoKSB7DQogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOw0KICAgICAgbGlzdFN0YWZmcm9sZSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5zdGFmZnJvbGVMaXN0ID0gcmVzcG9uc2Uucm93czsNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsOw0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgbG9hZE1lbnUodmFsKSB7DQogICAgICBpZiAodmFsLm1lbnVMaXN0ID09IG51bGwpIHsNCiAgICAgICAgbGV0IHF1ZXJ5ID0gew0KICAgICAgICAgIHN0YWZmSWQ6IHZhbC5zdGFmZklkDQogICAgICAgIH0NCiAgICAgICAgbGlzdFN0YWZmcm9sZU1lbnUocXVlcnkpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgIHZhbC5tZW51TGlzdCA9IHJlc3BvbnNlLmRhdGENCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOWPlua2iOaMiemSrg0KICAgIGNhbmNlbCgpIHsNCiAgICAgIHRoaXMub3BlbiA9IGZhbHNlOw0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIHN0YWZmUm9sZURlcHRJZDogbnVsbCwNCiAgICAgICAgc3RhZmZJZDogbnVsbCwNCiAgICAgICAgcm9sZUlkOiBudWxsLA0KICAgICAgICBpc01haW46ICJOIiwNCiAgICAgICAgZGVwdElkOiBudWxsDQogICAgICB9Ow0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKTsNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDE7DQogICAgICB0aGlzLmdldExpc3QoKTsNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpOw0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpOw0KICAgIH0sDQogICAgLy8g5aSa6YCJ5qGG6YCJ5Lit5pWw5o2uDQogICAgaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgew0KICAgICAgdGhpcy5pZHMgPSBzZWxlY3Rpb24ubWFwKGl0ZW0gPT4gaXRlbS5zdGFmZlJvbGVEZXB0SWQpDQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5yZXNldCgpOw0KICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5p2D6ZmQ5YiG6YWNIjsNCiAgICB9LA0KICAgIC8qKiDkv67mlLnmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVVcGRhdGUocm93KSB7DQogICAgICB0aGlzLnJlc2V0KCk7DQogICAgICBjb25zdCBzdGFmZlJvbGVEZXB0SWQgPSByb3cuc3RhZmZSb2xlRGVwdElkIHx8IHRoaXMuaWRzDQogICAgICBnZXRTdGFmZnJvbGUoc3RhZmZSb2xlRGVwdElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMudXNlckxpc3QpIHsNCiAgICAgICAgICBpZiAoYS5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgIGZvciAoY29uc3QgYiBvZiBhLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgIGlmIChiLmNoaWxkcmVuICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgIGZvciAoY29uc3QgYyBvZiBiLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgICBpZiAoYy5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBkIG9mIGMuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICAgICAgICBpZiAoZC5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuc3RhZmZJZCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy5zdGFmZklkID0gZC5kZXB0SWQNCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgICAgaWYgKGQuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGUgb2YgZC5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZS5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuc3RhZmZJZCkgew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc3RhZmZJZCA9IGUuZGVwdElkDQogICAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YTsNCiAgICAgICAgdGhpcy5vcGVuID0gdHJ1ZTsNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnmnYPpmZDliIbphY0iOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvKiog5o+Q5Lqk5oyJ6ZKuICovDQogICAgc3VibWl0Rm9ybSgpIHsNCiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0uc3RhZmZSb2xlRGVwdElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZVN0YWZmcm9sZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGFkZFN0YWZmcm9sZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLmlrDlop7miJDlip8iKTsNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2U7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICAgICAgfSk7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8qKiDliKDpmaTmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVEZWxldGUocm93KSB7DQogICAgICBjb25zdCBzdGFmZlJvbGVEZXB0SWRzID0gcm93LnN0YWZmUm9sZURlcHRJZCB8fCB0aGlzLmlkczsNCiAgICAgIHRoaXMuJGNvbmZpcm0oJ+aYr+WQpuehruiupOWIoOmZpOadg+mZkOWIhumFjee8luWPt+S4uiInICsgc3RhZmZSb2xlRGVwdElkcyArICci55qE5pWw5o2u6aG577yfJywgJ+aPkOekuicsIHtjdXN0b21DbGFzczogJ21vZGFsLWNvbmZpcm0nfSkudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBkZWxTdGFmZnJvbGUNCiAgICAgICAgKHN0YWZmUm9sZURlcHRJZHMpOw0KICAgICAgfSkudGhlbigoKSA9PiB7DQogICAgICAgIHRoaXMuZ2V0TGlzdCgpOw0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKTsNCiAgICAgIH0pLmNhdGNoKCgpID0+IHsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ3N5c3RlbS9zdGFmZnJvbGUvZXhwb3J0Jywgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgc3RhZmZyb2xlXyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQ0KICAgIH0sDQogICAgZ2V0RGVwdElkKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLmRlcHRJZCA9IHZhbA0KICAgIH0sDQogICAgZ2V0Um9sZUlkKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLnJvbGVJZCA9IHZhbA0KICAgIH0sDQogICAgcXVlcnlEZXB0SWQodmFsKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmRlcHRJZCA9IHZhbA0KICAgIH0sDQogICAgcXVlcnlSb2xlSWQodmFsKSB7DQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnJvbGVJZCA9IHZhbA0KICAgIH0sDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/staffrole", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\" label-width=\"35px\"\r\n                 size=\"mini\">\r\n          <el-form-item label=\"员工\" prop=\"staffId\">\r\n            <el-select v-model=\"queryParams.staffId\" filterable placeholder=\"选择员工\" style=\"width: 100%\"\r\n                       @change=\"handleQuery\">\r\n              <el-option\r\n                v-for=\"staff in userList\"\r\n                :key=\"staff.staffId\"\r\n                :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n                :value=\"staff.staffId\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item label=\"角色\" prop=\"roleId\">\r\n            <tree-select :pass=\"queryParams.roleId\" :placeholder=\"'选择权限'\" :type=\"'role'\"\r\n                         style=\"width: 100%\" @return=\"queryRoleId\" :dbn=\"true\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"部门\" prop=\"deptId\">\r\n            <tree-select :multiple=\"false\" :pass=\"queryParams.deptId\"\r\n                         :placeholder=\"'选择部门'\" :type=\"'dept'\" style=\"width: 100%\" @return=\"queryDeptId\"/>\r\n          </el-form-item>\r\n          <el-form-item label=\"主职\" prop=\"isMain\">\r\n            <el-select v-model=\"queryParams.isMain\" placeholder=\"是否主职\" style=\"width: 100%\" @change=\"handleQuery\">\r\n              <el-option\r\n                v-for=\"dict in dict.type.sys_yes_no\"\r\n                :key=\"dict.value\"\r\n                :label=\"dict.label\"\r\n                :value=\"dict.value\"/>\r\n            </el-select>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:role:add']\"\r\n              icon=\"el-icon-plus\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleAdd\"\r\n            >新增\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:role:remove']\"\r\n              :disabled=\"multiple\"\r\n              icon=\"el-icon-delete\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"danger\"\r\n              @click=\"handleDelete\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:role:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"warning\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"staffroleList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"left\" label=\"员工\">\r\n            <template slot-scope=\"scope\">\r\n              {{ scope.row.staff.staffCode }}\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{\r\n                  scope.row.staff.staffFamilyLocalName + scope.row.staff.staffGivingLocalName\r\n                }}</a>\r\n              {{ scope.row.staff.staffGivingEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"left\" label=\"角色名称\">\r\n            <template slot-scope=\"scope\">\r\n              <a style=\"margin: 0;font-weight:bold;font-size: small\">{{ scope.row.role.roleLocalName }}</a>\r\n              {{ scope.row.role.roleEnName }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column key=\"role\" align=\"center\" label=\"权限概览\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"right\"\r\n                trigger=\"hover\"\r\n                width=\"400\">\r\n                <el-tree\r\n                  :ref=\"scope.row.roleId\"\r\n                  :check-strictly=\"!scope.row.role.menuCheckStrictly\"\r\n                  :data=\"scope.row.menuList\"\r\n                  :props=\"defaultProps\"\r\n                  class=\"tree-border\"\r\n                  empty-text=\"不存在权限\"\r\n                  node-key=\"id\"\r\n                ></el-tree>\r\n                <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\" @mouseenter.native=\"loadMenu(scope.row)\">查看\r\n                </el-button>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"所属部门\" prop=\"dept.deptLocalName\" width=\"100\"/>\r\n          <el-table-column align=\"center\" label=\"是否主职\" prop=\"isMain\" width=\"68\">\r\n            <template slot-scope=\"scope\">\r\n              <dict-tag :options=\"dict.type.sys_yes_no\" :value=\"scope.row.isMain\"/>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                v-hasPermi=\"['system:role:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                v-hasPermi=\"['system:role:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改权限分配对话框 -->\r\n    <el-dialog\r\n      :close-on-click-modal=\"false\"\r\n      :modal-append-to-body=\"false\" v-dialogDrag v-dialogDragWidth :title=\"title\" :visible.sync=\"open\" append-to-body\r\n      width=\"500px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\r\n        <el-form-item label=\"员工\" prop=\"staffId\">\r\n          <el-select v-model=\"form.staffId\" filterable placeholder=\"选择员工\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"staff in userList\"\r\n              :key=\"staff.staffId\"\r\n              :label=\"staff.staffCode+' '+staff.staffFamilyLocalName +staff.staffGivingLocalName + ' ' + staff.staffGivingEnName\"\r\n              :value=\"staff.staffId\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"角色名称\" prop=\"roleId\">\r\n          <tree-select :pass=\"form.roleId\" :type=\"'role'\" :dbn=\"true\" @return=\"getRoleId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"所属部门\" prop=\"deptId\">\r\n          <tree-select :multiple=\"false\" :pass=\"form.deptId\" :type=\"'dept'\" @return=\"getDeptId\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"是否主职\" prop=\"isMain\">\r\n          <el-select v-model=\"form.isMain\" placeholder=\"是否主职\" style=\"width: 100%\">\r\n            <el-option\r\n              v-for=\"dict in dict.type.sys_yes_no\"\r\n              :key=\"dict.value\"\r\n              :label=\"dict.label\"\r\n              :value=\"dict.value\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addStaffrole,\r\n  delStaffrole,\r\n  getStaffrole,\r\n  listStaffrole,\r\n  listStaffroleMenu,\r\n  updateStaffrole\r\n} from \"@/api/system/staffrole\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.min.css\";\r\nimport {selectListUser} from \"@/api/system/user\";\r\n\r\nexport default {\r\n  name: \"Staffrole\",\r\n  dicts: ['sys_yes_no'],\r\n  components: {Treeselect},\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      total: 0,\r\n      userList: [],\r\n      defaultProps: {\r\n        children: \"children\",\r\n        label: \"label\"\r\n      },\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 权限分配表格数据\r\n      staffroleList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        roleId: null,\r\n        deptId: null,\r\n        isMain: null,\r\n        staffId: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        staffId: [\r\n          {\r\n            required: true,\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        roleId: [\r\n          {\r\n            required: true,\r\n            message: \"权限不能为空\", trigger: \"blur\"\r\n          }\r\n        ],\r\n        isMain: [\r\n          {\r\n            required: true,\r\n            message: \"是否主要权限不能为空\", trigger: \"blur\"\r\n          }\r\n        ],\r\n        deptId: [\r\n          {\r\n            required: true,\r\n            message: \"部门ID不能为空\", trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n == true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    selectListUser().then(response => {\r\n      this.userList = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    /** 查询权限分配列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listStaffrole(this.queryParams).then(response => {\r\n        this.staffroleList = response.rows;\r\n        this.total = response.total;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    loadMenu(val) {\r\n      if (val.menuList == null) {\r\n        let query = {\r\n          staffId: val.staffId\r\n        }\r\n        listStaffroleMenu(query).then(response => {\r\n          val.menuList = response.data\r\n        })\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        staffRoleDeptId: null,\r\n        staffId: null,\r\n        roleId: null,\r\n        isMain: \"N\",\r\n        deptId: null\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.staffRoleDeptId)\r\n      this.single = selection.length != 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加权限分配\";\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset();\r\n      const staffRoleDeptId = row.staffRoleDeptId || this.ids\r\n      getStaffrole(staffRoleDeptId).then(response => {\r\n        for (const a of this.userList) {\r\n          if (a.children != undefined) {\r\n            for (const b of a.children) {\r\n              if (b.children != undefined) {\r\n                for (const c of b.children) {\r\n                  if (c.children != undefined) {\r\n                    for (const d of c.children) {\r\n                      if (d.staffId == response.data.staffId) {\r\n                        this.staffId = d.deptId\r\n                      }\r\n                      if (d.children != undefined) {\r\n                        for (const e of d.children) {\r\n                          if (e.staffId == response.data.staffId) {\r\n                            this.staffId = e.deptId\r\n                          }\r\n                        }\r\n                      }\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改权限分配\";\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.staffRoleDeptId != null) {\r\n            updateStaffrole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addStaffrole(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const staffRoleDeptIds = row.staffRoleDeptId || this.ids;\r\n      this.$confirm('是否确认删除权限分配编号为\"' + staffRoleDeptIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delStaffrole\r\n        (staffRoleDeptIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/staffrole/export', {\r\n        ...this.queryParams\r\n      }, `staffrole_${new Date().getTime()}.xlsx`)\r\n    },\r\n    getDeptId(val) {\r\n      this.form.deptId = val\r\n    },\r\n    getRoleId(val) {\r\n      this.form.roleId = val\r\n    },\r\n    queryDeptId(val) {\r\n      this.queryParams.deptId = val\r\n    },\r\n    queryRoleId(val) {\r\n      this.queryParams.roleId = val\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n</style>\r\n"]}]}