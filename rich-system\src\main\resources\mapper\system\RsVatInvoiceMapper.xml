<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.RsVatInvoiceMapper">
    
    <resultMap type="RsVatInvoice" id="RsVatInvoiceResult">
        <result property="invoiceId"    column="invoice_id"    />
        <result property="rctId"    column="rct_id"    />
        <result property="invoiceCodeNo"    column="invoice_code_no"    />
        <result property="invoiceOfficalNo"    column="invoice_offical_no"    />
        <result property="saleBuy"    column="sale_buy"    />
        <result property="invoiceBelongsTo"    column="invoice_belongs_to"    />
        <result property="richBankCode"    column="rich_bank_code"    />
        <result property="richCompanyTitle"    column="rich_company_title"    />
        <result property="richVatSerialNo"    column="rich_vat_serial_no"    />
        <result property="richBankAccount"    column="rich_bank_account"    />
        <result property="richBankFullname"    column="rich_bank_fullname"    />
        <result property="cooperatorId"    column="cooperator_id"    />
        <result property="cooperatorShortName"    column="cooperator_short_name"    />
        <result property="cooperatorBankCode"    column="cooperator_bank_code"    />
        <result property="cooperatorFullname"    column="cooperator_fullname"    />
        <result property="cooperatorVatSerialNo"    column="cooperator_vat_serial_no"    />
        <result property="cooperatorBankAccount"    column="cooperator_bank_account"    />
        <result property="cooperatorBankFullname"    column="cooperator_bank_fullname"    />
        <result property="rctNoSummary"    column="rct_no_summary"    />
        <result property="cooperatorReferNo"    column="cooperator_refer_no"    />
        <result property="officalChargeNameSummary"    column="offical_charge_name_summary"    />
        <result property="chargeCurrencyCode"    column="charge_currency_code"    />
        <result property="dnSum"    column="dn_sum"    />
        <result property="dnRecieved"    column="dn_recieved"    />
        <result property="dnBalance"    column="dn_balance"    />
        <result property="cnSum"    column="cn_sum"    />
        <result property="cnPaid"    column="cn_paid"    />
        <result property="cnBalance"    column="cn_balance"    />
        <result property="chargeClearStatus"    column="charge_clear_status"    />
        <result property="expectedPayDate"    column="expected_pay_date"    />
        <result property="approvedPayDate"    column="approved_pay_date"    />
        <result property="actualPayDate"    column="actual_pay_date"    />
        <result property="sqdBankStatementList"    column="sqd_bank_statement_list"    />
        <result property="invoiceCurrencyCode"    column="invoice_currency_code"    />
        <result property="invoiceExchangeRate"    column="invoice_exchange_rate"    />
        <result property="invoiceNetAmount"    column="invoice_net_amount"    />
        <result property="vatAmount"    column="vat_amount"    />
        <result property="invoiceVatAmount"    column="invoice_vat_amount"    />
        <result property="saleNetSum"    column="sale_net_sum"    />
        <result property="saleTax"    column="sale_tax"    />
        <result property="saleTaxTotal"    column="sale_tax_total"    />
        <result property="buyNetSum"    column="buy_net_sum"    />
        <result property="buyTax"    column="buy_tax"    />
        <result property="buyTaxTotal"    column="buy_tax_total"    />
        <result property="taxClass"    column="tax_class"    />
        <result property="invoiceType"    column="invoice_type"    />
        <result property="mergeInvoice"    column="merge_invoice"    />
        <result property="cooperatorCompanyTitle"    column="cooperator_company_title"    />
        <result property="relatedOrderNo"    column="related_order_no"    />
        <result property="taxLocked"    column="tax_locked"    />
        <result property="belongsToMonth"    column="belongs_to_month"    />
        <result property="invoiceStatus"    column="invoice_status"    />
        <result property="invoiceRemark"    column="invoice_remark"    />
        <result property="applyStuffId"    column="apply_stuff_id"    />
        <result property="appliedTime"    column="applied_time"    />
        <result property="issuedStuffId"    column="issued_stuff_id"    />
        <result property="issuedTime"    column="issued_time"    />
        <result property="taxStuffId"    column="tax_stuff_id"    />
        <result property="taxDeclareTime"    column="tax_declare_time"    />
        <result property="invoiceAttachment"    column="invoice_attachment"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <collection property="rsChargeList" resultMap="com.rich.system.mapper.RsChargeMapper.RsChargeResult"/>
    </resultMap>

    <sql id="selectRsVatInvoiceVo">
        select rvi.invoice_id,
               rvi.rct_id,
               rvi.invoice_code_no,
               rvi.invoice_offical_no,
               rvi.sale_buy,
               rvi.invoice_belongs_to,
               rvi.rich_bank_code,
               rvi.rich_company_title,
               rvi.rich_vat_serial_no,
               rvi.rich_bank_account,
               rvi.rich_bank_fullname,
               rvi.cooperator_id,
               rvi.cooperator_short_name,
               rvi.cooperator_bank_code,
               rvi.cooperator_fullname,
               rvi.cooperator_vat_serial_no,
               rvi.cooperator_bank_account,
               rvi.cooperator_bank_fullname,
               rvi.rct_no_summary,
               rvi.cooperator_refer_no,
               rvi.offical_charge_name_summary,
               rvi.charge_currency_code,
               rvi.dn_sum,
               rvi.dn_recieved,
               rvi.dn_balance,
               rvi.cn_sum,
               rvi.cn_paid,
               rvi.cn_balance,
               rvi.charge_clear_status,
               rvi.expected_pay_date,
               rvi.approved_pay_date,
               rvi.actual_pay_date,
               rvi.sqd_bank_statement_list,
               rvi.invoice_currency_code,
               rvi.invoice_exchange_rate,
               rvi.invoice_net_amount,
               rvi.vat_amount,
               rvi.invoice_vat_amount,
               rvi.sale_net_sum,
               rvi.sale_tax,
               rvi.sale_tax_total,
               rvi.buy_net_sum,
               rvi.buy_tax,
               rvi.buy_tax_total,
               rvi.tax_class,
               rvi.invoice_type,
               rvi.merge_invoice,
               rvi.cooperator_company_title,
               rvi.related_order_no,
               rvi.tax_locked,
               rvi.belongs_to_month,
               rvi.invoice_status,
               rvi.invoice_remark,
               rvi.apply_stuff_id,
               rvi.applied_time,
               rvi.issued_stuff_id,
               rvi.issued_time,
               rvi.tax_stuff_id,
               rvi.tax_declare_time,
               rvi.invoice_attachment,
               rvi.create_time,
               rvi.update_time,
               rc.charge_id,
               rc.service_id,
               rc.sqd_service_type_id,
               rc.sqd_rct_no,
               rc.related_freight_id,
               rc.is_recieving_or_paying,
               rc.clearing_company_id,
               rc.clearing_company_summary,
               rc.quotation_strategy_id,
               rc.dn_charge_name_id,
               rc.dn_currency_code,
               rc.dn_unit_rate,
               rc.dn_unit_code,
               rc.dn_amount,
               rc.basic_currency_rate,
               rc.duty_rate,
               rc.currency_rate_calculate_date,
               rc.subtotal,
               rc.charge_remark,
               rc.clearing_currency_code,
               rc.dn_currency_received,
               rc.dn_currency_paid,
               rc.dn_currency_balance,
               rc.account_received_id_list,
               rc.account_paid_id_list,
               rc.logistics_invoice_id_list,
               rc.sqd_invoice_issued,
               rc.sqd_invoice_balance,
               rc.is_account_confirmed,
               rc.sqd_dn_currency_paid,
               rc.sqd_dn_currency_balance,
               rc.sqd_writeoff_no_list,
               rc.payment_title_code,
               rc.invoicing_item,
               bc.charge_local_name       charge_name,
               bdst.service_local_name as service_local_name
        from rs_vat_invoice rvi
                 left join rs_debit_note rdn on rdn.invoice_id = rvi.invoice_id
                 left join rich.rs_charge rc on rc.debit_note_id = rdn.debit_note_id
                 left join bas_charge bc on rc.dn_charge_name_id = bc.charge_id
                 left join bas_dist_service_type bdst on rc.sqd_service_type_id = bdst.service_type_id
    </sql>

    <select id="selectRsVatInvoiceList" parameterType="RsVatInvoice" resultMap="RsVatInvoiceResult">
        <include refid="selectRsVatInvoiceVo"/>
        <where>  
            <if test="invoiceCodeNo != null  and invoiceCodeNo != ''"> and invoice_code_no = #{invoiceCodeNo}</if>
            <if test="invoiceOfficalNo != null  and invoiceOfficalNo != ''"> and invoice_offical_no = #{invoiceOfficalNo}</if>
            <if test="saleBuy != null  and saleBuy != ''"> and sale_buy = #{saleBuy}</if>
            <if test="invoiceBelongsTo != null  and invoiceBelongsTo != ''"> and invoice_belongs_to = #{invoiceBelongsTo}</if>
            <if test="richBankCode != null  and richBankCode != ''"> and rich_bank_code = #{richBankCode}</if>
            <if test="richCompanyTitle != null  and richCompanyTitle != ''"> and rich_company_title = #{richCompanyTitle}</if>
            <if test="richVatSerialNo != null  and richVatSerialNo != ''"> and rich_vat_serial_no = #{richVatSerialNo}</if>
            <if test="richBankAccount != null  and richBankAccount != ''"> and rich_bank_account = #{richBankAccount}</if>
            <if test="richBankFullname != null  and richBankFullname != ''"> and rich_bank_fullname like concat('%', #{richBankFullname}, '%')</if>
            <if test="cooperatorId != null "> and cooperator_id = #{cooperatorId}</if>
            <if test="cooperatorShortName != null  and cooperatorShortName != ''"> and cooperator_short_name like concat('%', #{cooperatorShortName}, '%')</if>
            <if test="cooperatorBankCode != null  and cooperatorBankCode != ''"> and cooperator_bank_code = #{cooperatorBankCode}</if>
            <if test="cooperatorFullname != null  and cooperatorFullname != ''"> and cooperator_fullname like concat('%', #{cooperatorFullname}, '%')</if>
            <if test="cooperatorVatSerialNo != null  and cooperatorVatSerialNo != ''"> and cooperator_vat_serial_no = #{cooperatorVatSerialNo}</if>
            <if test="cooperatorBankAccount != null  and cooperatorBankAccount != ''"> and cooperator_bank_account = #{cooperatorBankAccount}</if>
            <if test="cooperatorBankFullname != null  and cooperatorBankFullname != ''"> and cooperator_bank_fullname like concat('%', #{cooperatorBankFullname}, '%')</if>
            <if test="rctNoSummary != null  and rctNoSummary != ''"> and rct_no_summary = #{rctNoSummary}</if>
            <if test="cooperatorReferNo != null  and cooperatorReferNo != ''"> and cooperator_refer_no = #{cooperatorReferNo}</if>
            <if test="officalChargeNameSummary != null  and officalChargeNameSummary != ''"> and offical_charge_name_summary = #{officalChargeNameSummary}</if>
            <if test="chargeCurrencyCode != null  and chargeCurrencyCode != ''"> and charge_currency_code = #{chargeCurrencyCode}</if>
            <if test="dnSum != null "> and dn_sum = #{dnSum}</if>
            <if test="dnRecieved != null "> and dn_recieved = #{dnRecieved}</if>
            <if test="dnBalance != null "> and dn_balance = #{dnBalance}</if>
            <if test="cnSum != null "> and cn_sum = #{cnSum}</if>
            <if test="cnPaid != null "> and cn_paid = #{cnPaid}</if>
            <if test="cnBalance != null "> and cn_balance = #{cnBalance}</if>
            <if test="chargeClearStatus != null  and chargeClearStatus != ''"> and charge_clear_status = #{chargeClearStatus}</if>
            <if test="expectedPayDate != null "> and expected_pay_date = #{expectedPayDate}</if>
            <if test="approvedPayDate != null "> and approved_pay_date = #{approvedPayDate}</if>
            <if test="actualPayDate != null "> and actual_pay_date = #{actualPayDate}</if>
            <if test="sqdBankStatementList != null  and sqdBankStatementList != ''"> and sqd_bank_statement_list = #{sqdBankStatementList}</if>
            <if test="invoiceCurrencyCode != null  and invoiceCurrencyCode != ''"> and invoice_currency_code = #{invoiceCurrencyCode}</if>
            <if test="invoiceExchangeRate != null "> and invoice_exchange_rate = #{invoiceExchangeRate}</if>
            <if test="invoiceNetAmount != null "> and invoice_net_amount = #{invoiceNetAmount}</if>
            <if test="vatAmount != null "> and vat_amount = #{vatAmount}</if>
            <if test="invoiceVatAmount != null "> and invoice_vat_amount = #{invoiceVatAmount}</if>
            <if test="saleNetSum != null "> and sale_net_sum = #{saleNetSum}</if>
            <if test="saleTax != null "> and sale_tax = #{saleTax}</if>
            <if test="saleTaxTotal != null "> and sale_tax_total = #{saleTaxTotal}</if>
            <if test="buyNetSum != null "> and buy_net_sum = #{buyNetSum}</if>
            <if test="buyTax != null "> and buy_tax = #{buyTax}</if>
            <if test="buyTaxTotal != null "> and buy_tax_total = #{buyTaxTotal}</if>
            <if test="taxClass != null  and taxClass != ''"> and tax_class = #{taxClass}</if>
            <if test="invoiceType != null  and invoiceType != ''"> and invoice_type = #{invoiceType}</if>
            <if test="belongsToMonth != null  and belongsToMonth != ''"> and belongs_to_month = #{belongsToMonth}</if>
            <if test="invoiceStatus != null  and invoiceStatus != ''"> and invoice_status = #{invoiceStatus}</if>
            <if test="invoiceRemark != null  and invoiceRemark != ''"> and invoice_remark = #{invoiceRemark}</if>
            <if test="applyStuffId != null "> and apply_stuff_id = #{applyStuffId}</if>
            <if test="appliedTime != null "> and applied_time = #{appliedTime}</if>
            <if test="issuedStuffId != null "> and issued_stuff_id = #{issuedStuffId}</if>
            <if test="issuedTime != null "> and issued_time = #{issuedTime}</if>
            <if test="taxStuffId != null "> and tax_stuff_id = #{taxStuffId}</if>
            <if test="taxDeclareTime != null "> and tax_declare_time = #{taxDeclareTime}</if>
            <if test="invoiceAttachment != null  and invoiceAttachment != ''"> and invoice_attachment = #{invoiceAttachment}</if>
        </where>
    </select>
    
    <select id="selectRsVatInvoiceByInvoiceId" parameterType="Long" resultMap="RsVatInvoiceResult">
        <include refid="selectRsVatInvoiceVo"/>
        where rvi.invoice_id = #{invoiceId}
    </select>
    
    <select id="selectRsVatInvoiceByInvoiceIds" parameterType="java.util.List" resultMap="RsVatInvoiceResult">
        <include refid="selectRsVatInvoiceVo"/>
        where rvi.invoice_id in
        <foreach item="invoiceId" collection="invoiceIds" open="(" separator="," close=")">
            #{invoiceId}
        </foreach>
    </select>
        
    <insert id="insertRsVatInvoice" parameterType="RsVatInvoice" useGeneratedKeys="true" keyProperty="invoiceId">
        insert into rs_vat_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rctId != null ">rct_id,</if>
            <if test="invoiceCodeNo != null and invoiceCodeNo != ''">invoice_code_no,</if>
            <if test="invoiceOfficalNo != null">invoice_offical_no,</if>
            <if test="saleBuy != null and saleBuy != ''">sale_buy,</if>
            <if test="invoiceBelongsTo != null">invoice_belongs_to,</if>
            <if test="richBankCode != null">rich_bank_code,</if>
            <if test="richCompanyTitle != null">rich_company_title,</if>
            <if test="richVatSerialNo != null">rich_vat_serial_no,</if>
            <if test="richBankAccount != null">rich_bank_account,</if>
            <if test="richBankFullname != null">rich_bank_fullname,</if>
            <if test="cooperatorId != null">cooperator_id,</if>
            <if test="cooperatorShortName != null">cooperator_short_name,</if>
            <if test="cooperatorBankCode != null">cooperator_bank_code,</if>
            <if test="cooperatorFullname != null">cooperator_fullname,</if>
            <if test="cooperatorVatSerialNo != null">cooperator_vat_serial_no,</if>
            <if test="cooperatorBankAccount != null">cooperator_bank_account,</if>
            <if test="cooperatorBankFullname != null">cooperator_bank_fullname,</if>
            <if test="rctNoSummary != null">rct_no_summary,</if>
            <if test="cooperatorReferNo != null">cooperator_refer_no,</if>
            <if test="officalChargeNameSummary != null">offical_charge_name_summary,</if>
            <if test="chargeCurrencyCode != null">charge_currency_code,</if>
            <if test="dnSum != null">dn_sum,</if>
            <if test="dnRecieved != null">dn_recieved,</if>

            <if test="cnSum != null">cn_sum,</if>
            <if test="cnPaid != null">cn_paid,</if>

            <if test="chargeClearStatus != null">charge_clear_status,</if>
            <if test="expectedPayDate != null">expected_pay_date,</if>
            <if test="approvedPayDate != null">approved_pay_date,</if>
            <if test="actualPayDate != null">actual_pay_date,</if>
            <if test="sqdBankStatementList != null">sqd_bank_statement_list,</if>
            <if test="invoiceCurrencyCode != null">invoice_currency_code,</if>
            <if test="invoiceExchangeRate != null">invoice_exchange_rate,</if>
            <if test="invoiceNetAmount != null">invoice_net_amount,</if>
            <if test="vatAmount != null">vat_amount,</if>

            <if test="saleNetSum != null">sale_net_sum,</if>
            <if test="saleTax != null">sale_tax,</if>

            <if test="buyNetSum != null">buy_net_sum,</if>
            <if test="buyTax != null">buy_tax,</if>

            <if test="taxClass != null">tax_class,</if>
            <if test="invoiceType != null">invoice_type,</if>
            <if test="belongsToMonth != null">belongs_to_month,</if>
            <if test="invoiceStatus != null">invoice_status,</if>
            <if test="invoiceRemark != null">invoice_remark,</if>
            <if test="mergeInvoice != null">merge_invoice,</if>
            <if test="cooperatorCompanyTitle != null">cooperator_company_title,</if>
            <if test="relatedOrderNo != null">related_order_no,</if>
            <if test="taxLocked != null">tax_locked,</if>
            <if test="applyStuffId != null">apply_stuff_id,</if>
            <if test="appliedTime != null">applied_time,</if>
            <if test="issuedStuffId != null">issued_stuff_id,</if>
            <if test="issuedTime != null">issued_time,</if>
            <if test="taxStuffId != null">tax_stuff_id,</if>
            <if test="taxDeclareTime != null">tax_declare_time,</if>
            <if test="invoiceAttachment != null">invoice_attachment,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rctId != null">#{rctId},</if>
            <if test="invoiceCodeNo != null and invoiceCodeNo != ''">#{invoiceCodeNo},</if>
            <if test="invoiceOfficalNo != null">#{invoiceOfficalNo},</if>
            <if test="saleBuy != null and saleBuy != ''">#{saleBuy},</if>
            <if test="invoiceBelongsTo != null">#{invoiceBelongsTo},</if>
            <if test="richBankCode != null">#{richBankCode},</if>
            <if test="richCompanyTitle != null">#{richCompanyTitle},</if>
            <if test="richVatSerialNo != null">#{richVatSerialNo},</if>
            <if test="richBankAccount != null">#{richBankAccount},</if>
            <if test="richBankFullname != null">#{richBankFullname},</if>
            <if test="cooperatorId != null">#{cooperatorId},</if>
            <if test="cooperatorShortName != null">#{cooperatorShortName},</if>
            <if test="cooperatorBankCode != null">#{cooperatorBankCode},</if>
            <if test="cooperatorFullname != null">#{cooperatorFullname},</if>
            <if test="cooperatorVatSerialNo != null">#{cooperatorVatSerialNo},</if>
            <if test="cooperatorBankAccount != null">#{cooperatorBankAccount},</if>
            <if test="cooperatorBankFullname != null">#{cooperatorBankFullname},</if>
            <if test="rctNoSummary != null">#{rctNoSummary},</if>
            <if test="cooperatorReferNo != null">#{cooperatorReferNo},</if>
            <if test="officalChargeNameSummary != null">#{officalChargeNameSummary},</if>
            <if test="chargeCurrencyCode != null">#{chargeCurrencyCode},</if>
            <if test="dnSum != null">#{dnSum},</if>
            <if test="dnRecieved != null">#{dnRecieved},</if>

            <if test="cnSum != null">#{cnSum},</if>
            <if test="cnPaid != null">#{cnPaid},</if>

            <if test="chargeClearStatus != null">#{chargeClearStatus},</if>
            <if test="expectedPayDate != null">#{expectedPayDate},</if>
            <if test="approvedPayDate != null">#{approvedPayDate},</if>
            <if test="actualPayDate != null">#{actualPayDate},</if>
            <if test="sqdBankStatementList != null">#{sqdBankStatementList},</if>
            <if test="invoiceCurrencyCode != null">#{invoiceCurrencyCode},</if>
            <if test="invoiceExchangeRate != null">#{invoiceExchangeRate},</if>
            <if test="invoiceNetAmount != null">#{invoiceNetAmount},</if>
            <if test="vatAmount != null">#{vatAmount},</if>

            <if test="saleNetSum != null">#{saleNetSum},</if>
            <if test="saleTax != null">#{saleTax},</if>

            <if test="buyNetSum != null">#{buyNetSum},</if>
            <if test="buyTax != null">#{buyTax},</if>

            <if test="taxClass != null">#{taxClass},</if>
            <if test="invoiceType != null">#{invoiceType},</if>
            <if test="belongsToMonth != null">#{belongsToMonth},</if>
            <if test="invoiceStatus != null">#{invoiceStatus},</if>
            <if test="invoiceRemark != null">#{invoiceRemark},</if>
            <if test="mergeInvoice != null">#{mergeInvoice},</if>
            <if test="cooperatorCompanyTitle != null">#{cooperatorCompanyTitle},</if>
            <if test="relatedOrderNo != null">#{relatedOrderNo},</if>
            <if test="taxLocked != null">#{taxLocked},</if>
            <if test="applyStuffId != null">#{applyStuffId},</if>
            <if test="appliedTime != null">#{appliedTime},</if>
            <if test="issuedStuffId != null">#{issuedStuffId},</if>
            <if test="issuedTime != null">#{issuedTime},</if>
            <if test="taxStuffId != null">#{taxStuffId},</if>
            <if test="taxDeclareTime != null">#{taxDeclareTime},</if>
            <if test="invoiceAttachment != null">#{invoiceAttachment},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateRsVatInvoice" parameterType="RsVatInvoice">
        update rs_vat_invoice
        <trim prefix="SET" suffixOverrides=",">
            <if test="rctId != null">rct_id = #{rctId},</if>
            <if test="invoiceCodeNo != null and invoiceCodeNo != ''">invoice_code_no = #{invoiceCodeNo},</if>
            <if test="invoiceOfficalNo != null">invoice_offical_no = #{invoiceOfficalNo},</if>
            <if test="saleBuy != null and saleBuy != ''">sale_buy = #{saleBuy},</if>
            <if test="invoiceBelongsTo != null">invoice_belongs_to = #{invoiceBelongsTo},</if>
            <if test="richBankCode != null">rich_bank_code = #{richBankCode},</if>
            <if test="richCompanyTitle != null">rich_company_title = #{richCompanyTitle},</if>
            <if test="richVatSerialNo != null">rich_vat_serial_no = #{richVatSerialNo},</if>
            <if test="richBankAccount != null">rich_bank_account = #{richBankAccount},</if>
            <if test="richBankFullname != null">rich_bank_fullname = #{richBankFullname},</if>
            <if test="cooperatorId != null">cooperator_id = #{cooperatorId},</if>
            <if test="cooperatorShortName != null">cooperator_short_name = #{cooperatorShortName},</if>
            <if test="cooperatorBankCode != null">cooperator_bank_code = #{cooperatorBankCode},</if>
            <if test="cooperatorFullname != null">cooperator_fullname = #{cooperatorFullname},</if>
            <if test="cooperatorVatSerialNo != null">cooperator_vat_serial_no = #{cooperatorVatSerialNo},</if>
            <if test="cooperatorBankAccount != null">cooperator_bank_account = #{cooperatorBankAccount},</if>
            <if test="cooperatorBankFullname != null">cooperator_bank_fullname = #{cooperatorBankFullname},</if>
            <if test="rctNoSummary != null">rct_no_summary = #{rctNoSummary},</if>
            <if test="cooperatorReferNo != null">cooperator_refer_no = #{cooperatorReferNo},</if>
            <if test="officalChargeNameSummary != null">offical_charge_name_summary = #{officalChargeNameSummary},</if>
            <if test="chargeCurrencyCode != null">charge_currency_code = #{chargeCurrencyCode},</if>
            <if test="dnSum != null">dn_sum = #{dnSum},</if>
            <if test="dnRecieved != null">dn_recieved = #{dnRecieved},</if>

            <if test="cnSum != null">cn_sum = #{cnSum},</if>
            <if test="cnPaid != null">cn_paid = #{cnPaid},</if>

            <if test="chargeClearStatus != null">charge_clear_status = #{chargeClearStatus},</if>
            <if test="expectedPayDate != null">expected_pay_date = #{expectedPayDate},</if>
            <if test="approvedPayDate != null">approved_pay_date = #{approvedPayDate},</if>
            <if test="actualPayDate != null">actual_pay_date = #{actualPayDate},</if>
            <if test="sqdBankStatementList != null">sqd_bank_statement_list = #{sqdBankStatementList},</if>
            <if test="invoiceCurrencyCode != null">invoice_currency_code = #{invoiceCurrencyCode},</if>
            <if test="invoiceExchangeRate != null">invoice_exchange_rate = #{invoiceExchangeRate},</if>
            <if test="invoiceNetAmount != null">invoice_net_amount = #{invoiceNetAmount},</if>
            <if test="vatAmount != null">vat_amount = #{vatAmount},</if>

            <if test="saleNetSum != null">sale_net_sum = #{saleNetSum},</if>
            <if test="saleTax != null">sale_tax = #{saleTax},</if>

            <if test="buyNetSum != null">buy_net_sum = #{buyNetSum},</if>
            <if test="buyTax != null">buy_tax = #{buyTax},</if>

            <if test="taxClass != null">tax_class = #{taxClass},</if>
            <if test="invoiceType != null">invoice_type = #{invoiceType},</if>
            <if test="belongsToMonth != null">belongs_to_month = #{belongsToMonth},</if>
            <if test="invoiceStatus != null">invoice_status = #{invoiceStatus},</if>
            <if test="invoiceRemark != null">invoice_remark = #{invoiceRemark},</if>
            <if test="mergeInvoice != null">merge_invoice = #{mergeInvoice},</if>
            <if test="cooperatorCompanyTitle != null">cooperator_company_title = #{cooperatorCompanyTitle},</if>
            <if test="relatedOrderNo != null">related_order_no = #{relatedOrderNo},</if>
            <if test="taxLocked != null">tax_locked = #{taxLocked},</if>
            <if test="applyStuffId != null">apply_stuff_id = #{applyStuffId},</if>
            <if test="appliedTime != null">applied_time = #{appliedTime},</if>
            <if test="issuedStuffId != null">issued_stuff_id = #{issuedStuffId},</if>
            <if test="issuedTime != null">issued_time = #{issuedTime},</if>
            <if test="taxStuffId != null">tax_stuff_id = #{taxStuffId},</if>
            <if test="taxDeclareTime != null">tax_declare_time = #{taxDeclareTime},</if>
            <if test="invoiceAttachment != null">invoice_attachment = #{invoiceAttachment},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where invoice_id = #{invoiceId}
    </update>

    <delete id="deleteRsVatInvoiceByInvoiceId" parameterType="Long">
        delete from rs_vat_invoice where invoice_id = #{invoiceId}
    </delete>

    <delete id="deleteRsVatInvoiceByInvoiceIds" parameterType="String">
        delete from rs_vat_invoice where invoice_id in 
        <foreach item="invoiceId" collection="array" open="(" separator="," close=")">
            #{invoiceId}
        </foreach>
    </delete>

    <select id="countRsVatInvoiceByRctId" parameterType="Long" resultType="int">
        select count(1) from rs_vat_invoice where rct_id = #{rctId}
    </select>

    <select id="countRsVatInvoiceByRctIdAndCooperatorId" resultType="int">
        select count(1) from rs_vat_invoice where rct_id = #{rctId} and cooperator_id = #{cooperatorId}
    </select>
    
    <resultMap id="InvoiceCountMap" type="java.util.Map">
        <result column="totalCount" property="totalCount" javaType="java.lang.Integer"/>
        <result column="cooperatorCount" property="cooperatorCount" javaType="java.lang.Integer"/>
        <result column="cooperatorInvoiceCodeNo" property="cooperatorInvoiceCodeNo" javaType="java.lang.String"/>
    </resultMap>

    <select id="countBothInvoices" resultMap="InvoiceCountMap">
        SELECT
            COUNT(1) as totalCount,
            COALESCE(SUM(CASE WHEN cooperator_id = #{cooperatorId} THEN 1 ELSE 0 END), 0) as cooperatorCount,
            (SELECT invoice_code_no
             FROM rs_vat_invoice
             WHERE rct_id = #{rctId} AND cooperator_id = #{cooperatorId}
             LIMIT 1) as cooperatorInvoiceCodeNo
        FROM
            rs_vat_invoice
        WHERE
            rct_id = #{rctId}
    </select>
</mapper>