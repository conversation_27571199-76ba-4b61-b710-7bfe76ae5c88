{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\company.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\company\\company.vue", "mtime": 1737429728531}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:InVzZSBzdHJpY3QiOwoKT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICJfX2VzTW9kdWxlIiwgewogIHZhbHVlOiB0cnVlCn0pOwpleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7Ci8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCi8vCnZhciBfZGVmYXVsdCA9IHsKICBuYW1lOiAiY29tcGFueSIsCiAgcHJvcHM6IFsnc2NvcGUnXQp9OwpleHBvcnRzLmRlZmF1bHQgPSBfZGVmYXVsdDs="}, {"version": 3, "names": ["name", "props", "exports", "default", "_default"], "sources": ["src/views/system/company/company.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <el-popover\r\n      v-if=\"scope.row.isBlacklist=='1'\"\r\n      placement=\"right\"\r\n      trigger=\"click\"\r\n      width=\"400\">\r\n      <div>\r\n        {{ '拉黑人：' + scope.row.blacklistStaffLocalName + ' ' + scope.row.blacklistStaffEnName }}\r\n      </div>\r\n      <div>\r\n        {{ '拉黑原因：' + scope.row.blacklistContent }}\r\n      </div>\r\n      <el-button slot=\"reference\" style=\"padding: 2px;background-color: black;color: white\">黑名单</el-button>\r\n    </el-popover>\r\n    <!--    <el-tooltip :open-delay=\"500\"\r\n                    :disabled=\"scope.row.companyShortName.length<5||scope.row.companyLocalName!=null&&scope.row.companyLocalName.length<13\"\r\n                    :key=\"scope.row.companyId\"\r\n                    placement=\"top\">\r\n          <div slot=\"content\">\r\n            <h6 style=\"margin: 0;font-weight:bold;font-size: small\">{{\r\n                scope.row.companyShortName ? scope.row.companyShortName : scope.row.companyEnShortName\r\n              }}</h6>\r\n            &lt;!&ndash;<h6 style=\"margin: 0\">{{ scope.row.companyLocalName }}</h6>\r\n            <h6 style=\"margin: 0\">{{ scope.row.companyEnName }}</h6>&ndash;&gt;\r\n          </div>-->\r\n      <div>\r\n        <el-row style=\"display: flex\">\r\n          <el-col :span=\"11\">\r\n            <!--<h3 style=\"margin: 0;padding-right: 3px; color: darkgray;\">\r\n              {{\r\n                (scope.row.companyTaxCode != null ? scope.row.companyTaxCode : '') + ' ' + (scope.row.companyIntlCode != null ? scope.row.companyIntlCode : '')\r\n              }}\r\n            </h3>-->\r\n            <h3 style=\"margin: 0;padding-right: 3px; color: darkgray;\">\r\n              ({{\r\n                (scope.row.companyTaxCode != null ? scope.row.companyTaxCode : '')\r\n              }})\r\n            </h3>\r\n          </el-col>\r\n          <el-col :span=\"13\">\r\n            <h3\r\n              style=\"margin: 0;font-weight:bold;font-size: small;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">\r\n              {{ scope.row.companyEnShortName ? scope.row.companyEnShortName : scope.row.companyShortName }}\r\n            </h3>\r\n          </el-col>\r\n        </el-row>\r\n        <!--<h3 style=\"margin: 0;overflow:hidden;text-overflow:ellipsis;white-space: nowrap;\">{{\r\n            scope.row.companyLocalName != null ? scope.row.companyLocalName : scope.row.companyEnName != null ? scope.row.companyEnName : ''\r\n          }}</h3>-->\r\n      </div>\r\n    <!--</el-tooltip>-->\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: \"company\",\r\n  props: ['scope']\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAwDA;EACAA,IAAA;EACAC,KAAA;AACA;AAAAC,OAAA,CAAAC,OAAA,GAAAC,QAAA"}]}