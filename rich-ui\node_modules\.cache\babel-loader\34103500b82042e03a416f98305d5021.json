{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\distribute\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\distribute\\index.vue", "mtime": 1754876882581}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_distribute", "require", "_post", "_menu", "_permstype", "_store", "_interopRequireDefault", "name", "data", "showLeft", "showRight", "loading", "ids", "single", "multiple", "showSearch", "total", "distributeList", "menuOptions", "title", "open", "queryParams", "pageNum", "pageSize", "form", "rules", "postOptions", "persTypeOptions", "watch", "n", "created", "_this", "getList", "getMenuTreeselect", "listPost", "then", "response", "rows", "listPermstype", "methods", "getDeptList", "_this2", "$store", "state", "deptList", "length", "redisList", "dept", "store", "dispatch", "children", "_iterator", "_createForOfIteratorHelper2", "default", "_step", "s", "done", "d", "value", "err", "e", "f", "$refs", "getLoadOptions", "_iterator2", "_step2", "_this3", "listDistribute", "handleFlash", "_this4", "flashDistribute", "$message", "success", "_this5", "listMenu", "handleTree", "cancel", "reset", "distributeId", "distributeName", "deptIds", "menuIds", "menuList", "position", "positionId", "permsIds", "permsList", "orderNum", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "row", "_this6", "text", "status", "$confirm", "customClass", "changeStatus", "$modal", "msgSuccess", "catch", "handleSelectionChange", "selection", "map", "item", "handleAdd", "flashSelect", "handleUpdate", "_this7", "getDistribute", "submitForm", "_this8", "validate", "valid", "updateDistribute", "addDistribute", "handleDelete", "_this9", "distributeIds", "delDistribute", "handleExport", "download", "_objectSpread2", "concat", "Date", "getTime", "_this10", "$nextTick", "menu", "exports", "_default"], "sources": ["src/views/system/distribute/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form :model=\"queryParams\" class=\"query\" ref=\"queryForm\" size=\"mini\" :inline=\"true\" v-show=\"showSearch\"\r\n                 label-width=\"68px\">\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"primary\"\r\n              plain\r\n              icon=\"el-icon-plus\"\r\n              size=\"mini\"\r\n              @click=\"handleAdd\"\r\n              v-hasPermi=\"['system:distribute:add']\"\r\n            >新建权规\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"danger\"\r\n              plain\r\n              icon=\"el-icon-delete\"\r\n              size=\"mini\"\r\n              :disabled=\"multiple\"\r\n              @click=\"handleDelete\"\r\n              v-hasPermi=\"['system:distribute:remove']\"\r\n            >删除\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"warning\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleExport\"\r\n              v-hasPermi=\"['system:distribute:export']\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              type=\"success\"\r\n              plain\r\n              icon=\"el-icon-download\"\r\n              size=\"mini\"\r\n              @click=\"handleFlash\"\r\n              v-hasPermi=\"['system:distribute:flash']\"\r\n            >刷新权限\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <el-table v-loading=\"loading\" :data=\"distributeList\" @selection-change=\"handleSelectionChange\">\r\n          <el-table-column type=\"selection\" width=\"28\" align=\"center\"/>\r\n          <el-table-column align=\"center\" label=\"权规编号\" prop=\"distributeId\" width=\"68\"/>\r\n          <el-table-column align=\"left\" label=\"权规名称\" prop=\"distributeName\" width=\"170\"/>\r\n          <el-table-column align=\"left\" label=\"部门列表\" prop=\"deptList\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"center\" label=\"最低职级\" prop=\"position\" width=\"68\"/>\r\n          <el-table-column align=\"left\" label=\"菜单权限\" prop=\"menuList\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"left\" label=\"权限动作\" prop=\"permsList\" show-tooltip-when-overflow/>\r\n          <el-table-column align=\"left\" label=\"排序\" prop=\"orderNum\" width=\"48\"/>\r\n          <el-table-column align=\"left\" label=\"备注\" prop=\"remark\"/>\r\n          <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"success\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-edit\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n                v-hasPermi=\"['system:distribute:edit']\"\r\n              >修改\r\n              </el-button>\r\n              <el-button\r\n                size=\"mini\"\r\n                type=\"danger\"\r\n                style=\"margin-right: -8px\"\r\n                icon=\"el-icon-delete\"\r\n                @click=\"handleDelete(scope.row)\"\r\n                v-hasPermi=\"['system:distribute:remove']\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :total=\"total\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n    <!-- 添加或修改权限规划对话框 -->\r\n    <el-dialog :close-on-click-modal=\"false\"\r\n               :modal-append-to-body=\"false\"\r\n               v-dialogDrag v-dialogDragWidth\r\n               :title=\"title\" :visible.sync=\"open\"\r\n               append-to-body\r\n               width=\"400px\">\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"68px\">\r\n        <el-form-item label=\"权规名称\" prop=\"distributeName\">\r\n          <el-input v-model=\"form.distributeName\" placeholder=\"权限规划名称\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"部门列表\">\r\n          <tree-select ref=\"dept\" :multiple=\"true\" :pass=\"form.deptIds\" :type=\"'dept'\"\r\n                       @return=\"form.deptIds=$event\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"最低职级\">\r\n          <el-select v-model=\"form.positionId\" clearable filterable placeholder=\"选择权限授权的最低职级\"\r\n                     ref=\"post\" style=\"width: 100%;\">\r\n            <el-option-group>\r\n              <el-option\r\n                v-for=\"item in postOptions\"\r\n                :key=\"item.positionId\"\r\n                :disabled=\"item.status == 1\"\r\n                :label=\"item.positionLocalName\"\r\n                :value=\"item.positionId\"\r\n              ></el-option>\r\n            </el-option-group>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"菜单权限\">\r\n          <tree-select ref=\"menu\" :multiple=\"true\" :pass=\"form.menuIds\" :type=\"'menu'\"\r\n                       @return=\"form.menuIds=$event\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"权限动作\">\r\n          <el-select v-model=\"form.permsIds\" clearable filterable multiple\r\n                     placeholder=\"权限\" style=\"width: 100%;\">\r\n            <el-option\r\n              v-for=\"item in persTypeOptions\"\r\n              :key=\"item.permsId\"\r\n              :disabled=\"item.status == 1\"\r\n              :label=\"item.permsDetail\"\r\n              :value=\"item.permsType\"\r\n            ></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"备注\" prop=\"remark\">\r\n          <el-input type=\"textarea\" v-model=\"form.remark\" placeholder=\"备注\"/>\r\n        </el-form-item>\r\n        <el-form-item label=\"排序\" prop=\"orderNum\">\r\n          <el-input-number :controls=\"false\" v-model=\"form.orderNum\" :min=\"0\" controls-position=\"right\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\"\r\n                   v-loading=\"loading\"\r\n                   element-loading-text=\"拼命加载中，请稍候\"\r\n                   element-loading-spinner=\"el-icon-loading\"\r\n                   element-loading-background=\"rgba(0, 0, 0, 0.8)\">确 定\r\n        </el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addDistribute,\r\n  changeStatus,\r\n  delDistribute,\r\n  flashDistribute,\r\n  getDistribute,\r\n  listDistribute,\r\n  updateDistribute\r\n} from \"@/api/system/distribute\";\r\nimport {listPost} from \"@/api/system/post\";\r\nimport {listMenu} from \"@/api/system/menu\";\r\nimport {listPermstype} from \"@/api/system/permstype\";\r\nimport store from \"@/store\";\r\n\r\nexport default {\r\n  name: \"Distribute\",\r\n  data() {\r\n    return {\r\n      showLeft: 3,\r\n      showRight: 21,\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 权限规划表格数据\r\n      distributeList: [],\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {},\r\n      postOptions: [],\r\n      persTypeOptions: [],\r\n    };\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    },\r\n  },\r\n  created() {\r\n    this.getList();\r\n    this.getMenuTreeselect()\r\n    listPost().then(response => {\r\n      this.postOptions = response.rows\r\n    });\r\n    listPermstype().then(response => {\r\n      this.persTypeOptions = response.data\r\n    })\r\n  },\r\n  methods: {\r\n    getDeptList() {\r\n      if (this.$store.state.data.deptList.length == 0 || this.$store.state.data.redisList.dept) {\r\n        store.dispatch('getDeptList').then(() => {\r\n          let deptList = this.$store.state.data.deptList[0].children\r\n          for (let d of deptList[0].children) {\r\n            if (d.children) {\r\n              delete d.children\r\n            }\r\n          }\r\n          this.$refs.dept.getLoadOptions(deptList)\r\n        })\r\n      } else {\r\n        let deptList = this.$store.state.data.deptList[0].children\r\n        for (let d of deptList[0].children) {\r\n          if (d.children) {\r\n            delete d.children\r\n          }\r\n        }\r\n        this.$refs.dept.getLoadOptions(deptList)\r\n      }\r\n    },\r\n    /** 查询权限规划列表 */\r\n    getList() {\r\n      this.loading = true;\r\n      listDistribute(this.queryParams).then(response => {\r\n        this.distributeList = response.data;\r\n        this.loading = false;\r\n      });\r\n    },\r\n    handleFlash() {\r\n      this.loading = true\r\n      flashDistribute().then(response => {\r\n        this.loading = false;\r\n        this.$message.success(\"刷新成功\")\r\n      })\r\n    },\r\n    /** 查询菜单树结构 */\r\n    getMenuTreeselect() {\r\n      listMenu().then(response => {\r\n        this.menuOptions = (this.handleTree(response.data, \"menuId\"));\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        distributeId: null,\r\n        distributeName: null,\r\n        deptIds: [],\r\n        deptList: null,\r\n        menuIds: [],\r\n        menuList: null,\r\n        position: null,\r\n        positionId: null,\r\n        permsIds: [],\r\n        permsList: null,\r\n        orderNum: 0,\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1;\r\n      this.getList();\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\");\r\n      this.handleQuery();\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\";\r\n      this.$confirm('确认要\"' + text + '吗？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return changeStatus(row.distributeId, row.status);\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\");\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\";\r\n      });\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.distributeId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset();\r\n      this.open = true;\r\n      this.title = \"添加权限规划\";\r\n      this.flashSelect()\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      getDistribute(row.distributeId).then(response => {\r\n        this.form = response.data;\r\n        this.open = true;\r\n        this.title = \"修改权限规划\";\r\n        this.flashSelect()\r\n      });\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          this.loading = true\r\n          if (this.form.distributeId != null) {\r\n            updateDistribute(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\");\r\n              this.open = false;\r\n              this.loading = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            addDistribute(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\");\r\n              this.open = false;\r\n              this.loading = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const distributeIds = row.distributeId || this.ids;\r\n      this.$confirm('是否确认删除权限规划编号为\"' + distributeIds + '\"的数据项？', '提示', {customClass: 'modal-confirm'}).then(function () {\r\n        return delDistribute(distributeIds);\r\n      }).then(() => {\r\n        this.getList();\r\n        this.$modal.msgSuccess(\"删除成功\");\r\n      }).catch(() => {\r\n      });\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('system/distribute/export', {\r\n        ...this.queryParams\r\n      }, `distribute_${new Date().getTime()}.xlsx`)\r\n    },\r\n    flashSelect() {\r\n      this.$nextTick(() => {\r\n        this.$refs.menu.getLoadOptions(this.menuOptions)\r\n        this.getDeptList()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n"], "mappings": ";;;;;;;;;;AA0KA,IAAAA,WAAA,GAAAC,OAAA;AASA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AACA,IAAAG,UAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAC,sBAAA,CAAAL,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAM,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,cAAA;MACAC,WAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;MACA;MACA;MACAC,IAAA;MACA;MACAC,KAAA;MACAC,WAAA;MACAC,eAAA;IACA;EACA;EACAC,KAAA;IACAb,UAAA,WAAAA,WAAAc,CAAA;MACA,IAAAA,CAAA;QACA,KAAAnB,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAqB,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,KAAAC,OAAA;IACA,KAAAC,iBAAA;IACA,IAAAC,cAAA,IAAAC,IAAA,WAAAC,QAAA;MACAL,KAAA,CAAAL,WAAA,GAAAU,QAAA,CAAAC,IAAA;IACA;IACA,IAAAC,wBAAA,IAAAH,IAAA,WAAAC,QAAA;MACAL,KAAA,CAAAJ,eAAA,GAAAS,QAAA,CAAA5B,IAAA;IACA;EACA;EACA+B,OAAA;IACAC,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,SAAAC,MAAA,CAAAC,KAAA,CAAAnC,IAAA,CAAAoC,QAAA,CAAAC,MAAA,cAAAH,MAAA,CAAAC,KAAA,CAAAnC,IAAA,CAAAsC,SAAA,CAAAC,IAAA;QACAC,cAAA,CAAAC,QAAA,gBAAAd,IAAA;UACA,IAAAS,QAAA,GAAAH,MAAA,CAAAC,MAAA,CAAAC,KAAA,CAAAnC,IAAA,CAAAoC,QAAA,IAAAM,QAAA;UAAA,IAAAC,SAAA,OAAAC,2BAAA,CAAAC,OAAA,EACAT,QAAA,IAAAM,QAAA;YAAAI,KAAA;UAAA;YAAA,KAAAH,SAAA,CAAAI,CAAA,MAAAD,KAAA,GAAAH,SAAA,CAAAtB,CAAA,IAAA2B,IAAA;cAAA,IAAAC,CAAA,GAAAH,KAAA,CAAAI,KAAA;cACA,IAAAD,CAAA,CAAAP,QAAA;gBACA,OAAAO,CAAA,CAAAP,QAAA;cACA;YACA;UAAA,SAAAS,GAAA;YAAAR,SAAA,CAAAS,CAAA,CAAAD,GAAA;UAAA;YAAAR,SAAA,CAAAU,CAAA;UAAA;UACApB,MAAA,CAAAqB,KAAA,CAAAf,IAAA,CAAAgB,cAAA,CAAAnB,QAAA;QACA;MACA;QACA,IAAAA,QAAA,QAAAF,MAAA,CAAAC,KAAA,CAAAnC,IAAA,CAAAoC,QAAA,IAAAM,QAAA;QAAA,IAAAc,UAAA,OAAAZ,2BAAA,CAAAC,OAAA,EACAT,QAAA,IAAAM,QAAA;UAAAe,MAAA;QAAA;UAAA,KAAAD,UAAA,CAAAT,CAAA,MAAAU,MAAA,GAAAD,UAAA,CAAAnC,CAAA,IAAA2B,IAAA;YAAA,IAAAC,CAAA,GAAAQ,MAAA,CAAAP,KAAA;YACA,IAAAD,CAAA,CAAAP,QAAA;cACA,OAAAO,CAAA,CAAAP,QAAA;YACA;UACA;QAAA,SAAAS,GAAA;UAAAK,UAAA,CAAAJ,CAAA,CAAAD,GAAA;QAAA;UAAAK,UAAA,CAAAH,CAAA;QAAA;QACA,KAAAC,KAAA,CAAAf,IAAA,CAAAgB,cAAA,CAAAnB,QAAA;MACA;IACA;IACA,eACAZ,OAAA,WAAAA,QAAA;MAAA,IAAAkC,MAAA;MACA,KAAAvD,OAAA;MACA,IAAAwD,0BAAA,OAAA9C,WAAA,EAAAc,IAAA,WAAAC,QAAA;QACA8B,MAAA,CAAAjD,cAAA,GAAAmB,QAAA,CAAA5B,IAAA;QACA0D,MAAA,CAAAvD,OAAA;MACA;IACA;IACAyD,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAA1D,OAAA;MACA,IAAA2D,2BAAA,IAAAnC,IAAA,WAAAC,QAAA;QACAiC,MAAA,CAAA1D,OAAA;QACA0D,MAAA,CAAAE,QAAA,CAAAC,OAAA;MACA;IACA;IACA,cACAvC,iBAAA,WAAAA,kBAAA;MAAA,IAAAwC,MAAA;MACA,IAAAC,cAAA,IAAAvC,IAAA,WAAAC,QAAA;QACAqC,MAAA,CAAAvD,WAAA,GAAAuD,MAAA,CAAAE,UAAA,CAAAvC,QAAA,CAAA5B,IAAA;MACA;IACA;IACA;IACAoE,MAAA,WAAAA,OAAA;MACA,KAAAxD,IAAA;MACA,KAAAyD,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAArD,IAAA;QACAsD,YAAA;QACAC,cAAA;QACAC,OAAA;QACApC,QAAA;QACAqC,OAAA;QACAC,QAAA;QACAC,QAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC,QAAA;MACA;MACA,KAAAC,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAApE,WAAA,CAAAC,OAAA;MACA,KAAAU,OAAA;IACA;IACA,aACA0D,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,MAAA;MACA,KAAAC,QAAA,UAAAF,IAAA;QAAAG,WAAA;MAAA,GAAA9D,IAAA;QACA,WAAA+D,wBAAA,EAAAN,GAAA,CAAAd,YAAA,EAAAc,GAAA,CAAAG,MAAA;MACA,GAAA5D,IAAA;QACA0D,MAAA,CAAAM,MAAA,CAAAC,UAAA,CAAAN,IAAA;MACA,GAAAO,KAAA;QACAT,GAAA,CAAAG,MAAA,GAAAH,GAAA,CAAAG,MAAA;MACA;IACA;IACA;IACAO,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA3F,GAAA,GAAA2F,SAAA,CAAAC,GAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA3B,YAAA;MAAA;MACA,KAAAjE,MAAA,GAAA0F,SAAA,CAAA1D,MAAA;MACA,KAAA/B,QAAA,IAAAyF,SAAA,CAAA1D,MAAA;IACA;IACA,aACA6D,SAAA,WAAAA,UAAA;MACA,KAAA7B,KAAA;MACA,KAAAzD,IAAA;MACA,KAAAD,KAAA;MACA,KAAAwF,WAAA;IACA;IACA,aACAC,YAAA,WAAAA,aAAAhB,GAAA;MAAA,IAAAiB,MAAA;MACA,KAAAhC,KAAA;MACA,IAAAiC,yBAAA,EAAAlB,GAAA,CAAAd,YAAA,EAAA3C,IAAA,WAAAC,QAAA;QACAyE,MAAA,CAAArF,IAAA,GAAAY,QAAA,CAAA5B,IAAA;QACAqG,MAAA,CAAAzF,IAAA;QACAyF,MAAA,CAAA1F,KAAA;QACA0F,MAAA,CAAAF,WAAA;MACA;IACA;IACA,WACAI,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAlD,KAAA,SAAAmD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAF,MAAA,CAAArG,OAAA;UACA,IAAAqG,MAAA,CAAAxF,IAAA,CAAAsD,YAAA;YACA,IAAAqC,4BAAA,EAAAH,MAAA,CAAAxF,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACA4E,MAAA,CAAAb,MAAA,CAAAC,UAAA;cACAY,MAAA,CAAA5F,IAAA;cACA4F,MAAA,CAAArG,OAAA;cACAqG,MAAA,CAAAhF,OAAA;YACA;UACA;YACA,IAAAoF,yBAAA,EAAAJ,MAAA,CAAAxF,IAAA,EAAAW,IAAA,WAAAC,QAAA;cACA4E,MAAA,CAAAb,MAAA,CAAAC,UAAA;cACAY,MAAA,CAAA5F,IAAA;cACA4F,MAAA,CAAArG,OAAA;cACAqG,MAAA,CAAAhF,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAqF,YAAA,WAAAA,aAAAzB,GAAA;MAAA,IAAA0B,MAAA;MACA,IAAAC,aAAA,GAAA3B,GAAA,CAAAd,YAAA,SAAAlE,GAAA;MACA,KAAAoF,QAAA,oBAAAuB,aAAA;QAAAtB,WAAA;MAAA,GAAA9D,IAAA;QACA,WAAAqF,yBAAA,EAAAD,aAAA;MACA,GAAApF,IAAA;QACAmF,MAAA,CAAAtF,OAAA;QACAsF,MAAA,CAAAnB,MAAA,CAAAC,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACAoB,YAAA,WAAAA,aAAA;MACA,KAAAC,QAAA,iCAAAC,cAAA,CAAAtE,OAAA,MACA,KAAAhC,WAAA,iBAAAuG,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACAnB,WAAA,WAAAA,YAAA;MAAA,IAAAoB,OAAA;MACA,KAAAC,SAAA;QACAD,OAAA,CAAAjE,KAAA,CAAAmE,IAAA,CAAAlE,cAAA,CAAAgE,OAAA,CAAA7G,WAAA;QACA6G,OAAA,CAAAvF,WAAA;MACA;IACA;EACA;AACA;AAAA0F,OAAA,CAAA7E,OAAA,GAAA8E,QAAA"}]}