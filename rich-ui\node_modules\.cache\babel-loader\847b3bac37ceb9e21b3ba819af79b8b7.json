{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\tool\\build\\index.vue", "mtime": 1754876882605}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1754876882456}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_vuedraggable", "_interopRequireDefault", "require", "_jsBeautify", "_clipboard", "_render", "_RightPanel", "_config", "_utils", "_html", "_js", "_css", "_drawingDefault", "_logo", "_CodeTypeDialog", "_DraggableItem", "oldActiveId", "tempActiveData", "_default", "components", "draggable", "render", "RightPanel", "CodeTypeDialog", "DraggableItem", "data", "logo", "idGlobal", "formConf", "inputComponents", "selectComponents", "layoutComponents", "labelWidth", "drawingList", "<PERSON><PERSON><PERSON><PERSON>", "drawingData", "activeId", "formId", "drawerVisible", "formData", "dialogVisible", "generateConf", "showFileName", "activeData", "created", "document", "body", "ondrop", "event", "preventDefault", "stopPropagation", "watch", "activeDataLabel", "val", "oldVal", "placeholder", "undefined", "tag", "replace", "handler", "immediate", "mounted", "_this", "clipboard", "ClipboardJS", "text", "trigger", "codeStr", "generateCode", "$notify", "title", "message", "type", "on", "e", "$message", "error", "methods", "activeFormItem", "element", "onEnd", "obj", "a", "from", "to", "addComponent", "item", "clone", "cloneComponent", "push", "origin", "JSON", "parse", "stringify", "span", "<PERSON><PERSON><PERSON>", "Date", "layout", "vModel", "concat", "label", "componentName", "gutter", "AssembleFormData", "_objectSpread2", "default", "fields", "generate", "func", "titleCase", "operationType", "execRun", "execDownload", "blob", "Blob", "$download", "saveAs", "fileName", "execCopy", "getElementById", "click", "empty", "_this2", "$confirm", "customClass", "then", "drawingItemCopy", "parent", "createIdAndKey", "_this3", "Array", "isArray", "children", "map", "childItem", "drawingItemDelete", "index", "_this4", "splice", "$nextTick", "len", "length", "script", "vueScript", "makeUpJs", "html", "vueTemplate", "makeUpHtml", "css", "cssStyle", "makeUpCss", "beautifier", "beautifierConf", "download", "run", "copy", "tagChange", "newTag", "_this5", "tagIcon", "Object", "keys", "for<PERSON>ach", "key", "_typeof2", "updateDrawingList", "list", "_this6", "findIndex", "exports"], "sources": ["src/views/tool/build/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"container\">\r\n    <div class=\"left-board\">\r\n      <div class=\"logo-wrapper\">\r\n        <div class=\"logo\">\r\n          <img :src=\"logo\" alt=\"logo\"> Form Generator\r\n        </div>\r\n      </div>\r\n      <el-scrollbar class=\"left-scrollbar\">\r\n        <div class=\"components-list\">\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\"/>\r\n            输入型组件\r\n          </div>\r\n          <draggable\r\n            :clone=\"cloneComponent\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :list=\"inputComponents\"\r\n            :sort=\"false\"\r\n            class=\"components-draggable\"\r\n            draggable=\".components-item\"\r\n            @end=\"onEnd\">\r\n            <div\r\n              v-for=\"(element, index) in inputComponents\" :key=\"index\" class=\"components-item\"\r\n              @click=\"addComponent(element)\">\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\"/>\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\"/>\r\n            选择型组件\r\n          </div>\r\n          <draggable\r\n            :clone=\"cloneComponent\"\r\n            :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :list=\"selectComponents\"\r\n            :sort=\"false\"\r\n            class=\"components-draggable\"\r\n            draggable=\".components-item\"\r\n            @end=\"onEnd\">\r\n            <div\r\n              v-for=\"(element, index) in selectComponents\"\r\n              :key=\"index\"\r\n              class=\"components-item\"\r\n              @click=\"addComponent(element)\">\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\"/>\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n          <div class=\"components-title\">\r\n            <svg-icon icon-class=\"component\"/>\r\n            布局型组件\r\n          </div>\r\n          <draggable\r\n            :clone=\"cloneComponent\" :group=\"{ name: 'componentsGroup', pull: 'clone', put: false }\"\r\n            :list=\"layoutComponents\" :sort=\"false\" class=\"components-draggable\" draggable=\".components-item\"\r\n            @end=\"onEnd\">\r\n            <div v-for=\"(element, index) in layoutComponents\" :key=\"index\" class=\"components-item\"\r\n                 @click=\"addComponent(element)\">\r\n              <div class=\"components-body\">\r\n                <svg-icon :icon-class=\"element.tagIcon\"/>\r\n                {{ element.label }}\r\n              </div>\r\n            </div>\r\n          </draggable>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <div class=\"center-board\">\r\n      <div class=\"action-bar\">\r\n        <el-button icon=\"el-icon-download\" type=\"text\" @click=\"download\">\r\n          导出vue文件\r\n        </el-button>\r\n        <el-button class=\"copy-btn-main\" icon=\"el-icon-document-copy\" type=\"text\" @click=\"copy\">\r\n          复制代码\r\n        </el-button>\r\n        <el-button class=\"delete-btn\" icon=\"el-icon-delete\" type=\"text\" @click=\"empty\">\r\n          清空\r\n        </el-button>\r\n      </div>\r\n      <el-scrollbar class=\"center-scrollbar\">\r\n        <el-row :gutter=\"formConf.gutter\" class=\"center-board-row\">\r\n          <el-form\r\n            :disabled=\"formConf.disabled\"\r\n            :label-position=\"formConf.labelPosition\"\r\n            :label-width=\"formConf.labelWidth + 'px'\"\r\n            :size=\"formConf.size\"\r\n          >\r\n            <draggable :animation=\"340\" :list=\"drawingList\" class=\"drawing-board\" group=\"componentsGroup\">\r\n              <draggable-item\r\n                v-for=\"(element, index) in drawingList\"\r\n                :key=\"element.renderKey\"\r\n                :active-id=\"activeId\"\r\n                :drawing-list=\"drawingList\"\r\n                :element=\"element\"\r\n                :form-conf=\"formConf\"\r\n                :index=\"index\"\r\n                @activeItem=\"activeFormItem\"\r\n                @copyItem=\"drawingItemCopy\"\r\n                @deleteItem=\"drawingItemDelete\"\r\n              />\r\n            </draggable>\r\n            <div v-show=\"!drawingList.length\" class=\"empty-info\">\r\n              从左侧拖入或点选组件进行表单设计\r\n            </div>\r\n          </el-form>\r\n        </el-row>\r\n      </el-scrollbar>\r\n    </div>\r\n\r\n    <right-panel\r\n      :active-data=\"activeData\"\r\n      :form-conf=\"formConf\"\r\n      :show-field=\"!!drawingList.length\"\r\n      @tag-change=\"tagChange\"\r\n    />\r\n\r\n    <code-type-dialog\r\n      :show-file-name=\"showFileName\"\r\n      :visible.sync=\"dialogVisible\"\r\n      title=\"选择生成类型\"\r\n      @confirm=\"generate\"\r\n    />\r\n    <input id=\"copyNode\" type=\"hidden\">\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport draggable from 'vuedraggable'\r\nimport beautifier from 'js-beautify'\r\nimport ClipboardJS from 'clipboard'\r\nimport render from '@/utils/generator/render'\r\nimport RightPanel from './RightPanel'\r\nimport {formConf, inputComponents, layoutComponents, selectComponents} from '@/utils/generator/config'\r\nimport {beautifierConf, titleCase} from '@/utils'\r\nimport {cssStyle, makeUpHtml, vueScript, vueTemplate} from '@/utils/generator/html'\r\nimport {makeUpJs} from '@/utils/generator/js'\r\nimport {makeUpCss} from '@/utils/generator/css'\r\nimport drawingDefault from '@/utils/generator/drawingDefault'\r\nimport logo from '@/assets/logo/logo.png'\r\nimport CodeTypeDialog from './CodeTypeDialog'\r\nimport DraggableItem from './DraggableItem'\r\n\r\nlet oldActiveId\r\nlet tempActiveData\r\n\r\nexport default {\r\n  components: {\r\n    draggable,\r\n    render,\r\n    RightPanel,\r\n    CodeTypeDialog,\r\n    DraggableItem\r\n  },\r\n  data() {\r\n    return {\r\n      logo,\r\n      idGlobal: 100,\r\n      formConf,\r\n      inputComponents,\r\n      selectComponents,\r\n      layoutComponents,\r\n      labelWidth: 100,\r\n      drawingList: drawingDefault,\r\n      drawingData: {},\r\n      activeId: drawingDefault[0].formId,\r\n      drawerVisible: false,\r\n      formData: {},\r\n      dialogVisible: false,\r\n      generateConf: null,\r\n      showFileName: false,\r\n      activeData: drawingDefault[0]\r\n    }\r\n  },\r\n  created() {\r\n    // 防止 firefox 下 拖拽 会新打卡一个选项卡\r\n    document.body.ondrop = event => {\r\n      event.preventDefault()\r\n      event.stopPropagation()\r\n    }\r\n  },\r\n  watch: {\r\n    // eslint-disable-next-line func-names\r\n    'activeData.label': function (val, oldVal) {\r\n      if (\r\n        this.activeData.placeholder == undefined\r\n        || !this.activeData.tag\r\n        || oldActiveId != this.activeId\r\n      ) {\r\n        return\r\n      }\r\n      this.activeData.placeholder = this.activeData.placeholder.replace(oldVal, '') + val\r\n    },\r\n    activeId: {\r\n      handler(val) {\r\n        oldActiveId = val\r\n      },\r\n      immediate: true\r\n    }\r\n  },\r\n  mounted() {\r\n    const clipboard = new ClipboardJS('#copyNode', {\r\n      text: trigger => {\r\n        const codeStr = this.generateCode()\r\n        this.$notify({\r\n          title: '成功',\r\n          message: '代码已复制到剪切板，可粘贴。',\r\n          type: 'success'\r\n        })\r\n        return codeStr\r\n      }\r\n    })\r\n    clipboard.on('error', e => {\r\n      this.$message.error('代码复制失败')\r\n    })\r\n  },\r\n  methods: {\r\n    activeFormItem(element) {\r\n      this.activeData = element\r\n      this.activeId = element.formId\r\n    },\r\n    onEnd(obj, a) {\r\n      if (obj.from != obj.to) {\r\n        this.activeData = tempActiveData\r\n        this.activeId = this.idGlobal\r\n      }\r\n    },\r\n    addComponent(item) {\r\n      const clone = this.cloneComponent(item)\r\n      this.drawingList.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    cloneComponent(origin) {\r\n      const clone = JSON.parse(JSON.stringify(origin))\r\n      clone.formId = ++this.idGlobal\r\n      clone.span = formConf.span\r\n      clone.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件\r\n      if (!clone.layout) clone.layout = 'colFormItem'\r\n      if (clone.layout == 'colFormItem') {\r\n        clone.vModel = `field${this.idGlobal}`\r\n        clone.placeholder != undefined && (clone.placeholder += clone.label)\r\n        tempActiveData = clone\r\n      } else if (clone.layout == 'rowFormItem') {\r\n        delete clone.label\r\n        clone.componentName = `row${this.idGlobal}`\r\n        clone.gutter = this.formConf.gutter\r\n        tempActiveData = clone\r\n      }\r\n      return tempActiveData\r\n    },\r\n    AssembleFormData() {\r\n      this.formData = {\r\n        fields: JSON.parse(JSON.stringify(this.drawingList)),\r\n        ...this.formConf\r\n      }\r\n    },\r\n    generate(data) {\r\n      const func = this[`exec${titleCase(this.operationType)}`]\r\n      this.generateConf = data\r\n      func && func(data)\r\n    },\r\n    execRun(data) {\r\n      this.AssembleFormData()\r\n      this.drawerVisible = true\r\n    },\r\n    execDownload(data) {\r\n      const codeStr = this.generateCode()\r\n      const blob = new Blob([codeStr], {type: 'text/plain;charset=utf-8'})\r\n      this.$download.saveAs(blob, data.fileName)\r\n    },\r\n    execCopy(data) {\r\n      document.getElementById('copyNode').click()\r\n    },\r\n    empty() {\r\n      this.$confirm('确定要清空所有组件吗？', '提示', {type: 'warning', customClass: 'modal-confirm'}).then(\r\n        () => {\r\n          this.drawingList = []\r\n        }\r\n      )\r\n    },\r\n    drawingItemCopy(item, parent) {\r\n      let clone = JSON.parse(JSON.stringify(item))\r\n      clone = this.createIdAndKey(clone)\r\n      parent.push(clone)\r\n      this.activeFormItem(clone)\r\n    },\r\n    createIdAndKey(item) {\r\n      item.formId = ++this.idGlobal\r\n      item.renderKey = +new Date()\r\n      if (item.layout == 'colFormItem') {\r\n        item.vModel = `field${this.idGlobal}`\r\n      } else if (item.layout == 'rowFormItem') {\r\n        item.componentName = `row${this.idGlobal}`\r\n      }\r\n      if (Array.isArray(item.children)) {\r\n        item.children = item.children.map(childItem => this.createIdAndKey(childItem))\r\n      }\r\n      return item\r\n    },\r\n    drawingItemDelete(index, parent) {\r\n      parent.splice(index, 1)\r\n      this.$nextTick(() => {\r\n        const len = this.drawingList.length\r\n        if (len) {\r\n          this.activeFormItem(this.drawingList[len - 1])\r\n        }\r\n      })\r\n    },\r\n    generateCode() {\r\n      const {type} = this.generateConf\r\n      this.AssembleFormData()\r\n      const script = vueScript(makeUpJs(this.formData, type))\r\n      const html = vueTemplate(makeUpHtml(this.formData, type))\r\n      const css = cssStyle(makeUpCss(this.formData))\r\n      return beautifier.html(html + script + css, beautifierConf.html)\r\n    },\r\n    download() {\r\n      this.dialogVisible = true\r\n      this.showFileName = true\r\n      this.operationType = 'download'\r\n    },\r\n    run() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'run'\r\n    },\r\n    copy() {\r\n      this.dialogVisible = true\r\n      this.showFileName = false\r\n      this.operationType = 'copy'\r\n    },\r\n    tagChange(newTag) {\r\n      newTag = this.cloneComponent(newTag)\r\n      newTag.vModel = this.activeData.vModel\r\n      newTag.formId = this.activeId\r\n      newTag.span = this.activeData.span\r\n      delete this.activeData.tag\r\n      delete this.activeData.tagIcon\r\n      delete this.activeData.document\r\n      Object.keys(newTag).forEach(key => {\r\n        if (this.activeData[key] != undefined\r\n          && typeof this.activeData[key] == typeof newTag[key]) {\r\n          newTag[key] = this.activeData[key]\r\n        }\r\n      })\r\n      this.activeData = newTag\r\n      this.updateDrawingList(newTag, this.drawingList)\r\n    },\r\n    updateDrawingList(newTag, list) {\r\n      const index = list.findIndex(item => item.formId == this.activeId)\r\n      if (index > -1) {\r\n        list.splice(index, 1, newTag)\r\n      } else {\r\n        list.forEach(item => {\r\n          if (Array.isArray(item.children)) this.updateDrawingList(newTag, item.children)\r\n        })\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang='scss'>\r\n\r\n.editor-tabs {\r\n  background: #121315;\r\n\r\n  .el-tabs__header {\r\n    margin: 0;\r\n    border-bottom-color: #121315;\r\n\r\n    .el-tabs__nav {\r\n      border-color: #121315;\r\n    }\r\n  }\r\n\r\n  .el-tabs__item {\r\n    height: 32px;\r\n    line-height: 32px;\r\n    color: #888a8e;\r\n    border-left: 1px solid #121315 !important;\r\n    background: #363636;\r\n    margin-right: 5px;\r\n    user-select: none;\r\n  }\r\n\r\n  .el-tabs__item.is-active {\r\n    background: #1e1e1e;\r\n    border-bottom-color: #1e1e1e !important;\r\n    color: #fff;\r\n  }\r\n\r\n  .el-icon-edit {\r\n    color: #f1fa8c;\r\n  }\r\n\r\n  .el-icon-document {\r\n    color: #a95812;\r\n  }\r\n}\r\n\r\n// home\r\n.right-scrollbar {\r\n  .el-scrollbar__view {\r\n    padding: 12px 18px 15px 15px;\r\n  }\r\n}\r\n\r\n.left-scrollbar .el-scrollbar__wrap {\r\n  box-sizing: border-box;\r\n  overflow-x: hidden !important;\r\n  margin-bottom: 0 !important;\r\n}\r\n\r\n.center-tabs {\r\n  .el-tabs__header {\r\n    margin-bottom: 0 !important;\r\n  }\r\n\r\n  .el-tabs__item {\r\n    width: 50%;\r\n    text-align: center;\r\n  }\r\n\r\n  .el-tabs__nav {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.reg-item {\r\n  padding: 12px 6px;\r\n  background: #f8f8f8;\r\n  position: relative;\r\n  border-radius: 4px;\r\n\r\n  .close-btn {\r\n    position: absolute;\r\n    right: -6px;\r\n    top: -6px;\r\n    display: block;\r\n    width: 16px;\r\n    height: 16px;\r\n    line-height: 16px;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    border-radius: 50%;\r\n    color: #fff;\r\n    text-align: center;\r\n    z-index: 1;\r\n    cursor: pointer;\r\n    font-size: 12px;\r\n\r\n    &:hover {\r\n      background: rgba(210, 23, 23, 0.5)\r\n    }\r\n  }\r\n\r\n  & + .reg-item {\r\n    margin-top: 18px;\r\n  }\r\n}\r\n\r\n.action-bar {\r\n  & .el-button + .el-button {\r\n    margin-left: 15px;\r\n  }\r\n\r\n  & i {\r\n    font-size: 20px;\r\n    vertical-align: middle;\r\n    position: relative;\r\n    top: -1px;\r\n  }\r\n}\r\n\r\n.custom-tree-node {\r\n  width: 100%;\r\n  font-size: 14px;\r\n\r\n  .node-operation {\r\n    float: right;\r\n  }\r\n\r\n  i[class*=\"el-icon\"] + i[class*=\"el-icon\"] {\r\n    margin-left: 6px;\r\n  }\r\n\r\n  .el-icon-plus {\r\n    color: #409EFF;\r\n  }\r\n\r\n  .el-icon-delete {\r\n    color: #157a0c;\r\n  }\r\n}\r\n\r\n.left-scrollbar .el-scrollbar__view {\r\n  overflow-x: hidden;\r\n}\r\n\r\n.el-rate {\r\n  display: inline-block;\r\n  vertical-align: text-top;\r\n}\r\n\r\n.el-upload__tip {\r\n  line-height: 1.2;\r\n}\r\n\r\n$selectedColor: #f6f7ff;\r\n$lighterBlue: #409EFF;\r\n\r\n.container {\r\n  position: relative;\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.components-list {\r\n  padding: 8px;\r\n  box-sizing: border-box;\r\n  height: 100%;\r\n\r\n  .components-item {\r\n    display: inline-block;\r\n    width: 48%;\r\n    margin: 1%;\r\n    transition: transform 0ms !important;\r\n  }\r\n}\r\n\r\n.components-draggable {\r\n  padding-bottom: 20px;\r\n}\r\n\r\n.components-title {\r\n  font-size: 14px;\r\n  color: #222;\r\n  margin: 6px 2px;\r\n\r\n  .svg-icon {\r\n    color: #666;\r\n    font-size: 18px;\r\n  }\r\n}\r\n\r\n.components-body {\r\n  padding: 8px 10px;\r\n  background: $selectedColor;\r\n  font-size: 12px;\r\n  cursor: move;\r\n  border: 1px dashed $selectedColor;\r\n  border-radius: 3px;\r\n\r\n  .svg-icon {\r\n    color: #777;\r\n    font-size: 15px;\r\n  }\r\n\r\n  &:hover {\r\n    border: 1px dashed #787be8;\r\n    color: #787be8;\r\n\r\n    .svg-icon {\r\n      color: #787be8;\r\n    }\r\n  }\r\n}\r\n\r\n.left-board {\r\n  width: 260px;\r\n  position: absolute;\r\n  left: 0;\r\n  top: 0;\r\n  height: 100vh;\r\n}\r\n\r\n.left-scrollbar {\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n}\r\n\r\n.center-scrollbar {\r\n  height: calc(100vh - 42px);\r\n  overflow: hidden;\r\n  border-left: 1px solid #f1e8e8;\r\n  border-right: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.center-board {\r\n  height: 100vh;\r\n  width: auto;\r\n  margin: 0 350px 0 260px;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.empty-info {\r\n  position: absolute;\r\n  top: 46%;\r\n  left: 0;\r\n  right: 0;\r\n  text-align: center;\r\n  font-size: 18px;\r\n  color: #ccb1ea;\r\n  letter-spacing: 4px;\r\n}\r\n\r\n.action-bar {\r\n  position: relative;\r\n  height: 42px;\r\n  text-align: right;\r\n  padding: 0 15px;\r\n  box-sizing: border-box;;\r\n  border: 1px solid #f1e8e8;\r\n  border-top: none;\r\n  border-left: none;\r\n\r\n  .delete-btn {\r\n    color: #F56C6C;\r\n  }\r\n}\r\n\r\n.logo-wrapper {\r\n  position: relative;\r\n  height: 42px;\r\n  background: #fff;\r\n  border-bottom: 1px solid #f1e8e8;\r\n  box-sizing: border-box;\r\n}\r\n\r\n.logo {\r\n  position: absolute;\r\n  left: 12px;\r\n  top: 6px;\r\n  line-height: 30px;\r\n  color: #00afff;\r\n  font-weight: 600;\r\n  font-size: 17px;\r\n  white-space: nowrap;\r\n\r\n  > img {\r\n    width: 30px;\r\n    height: 30px;\r\n    vertical-align: top;\r\n  }\r\n\r\n  .github {\r\n    display: inline-block;\r\n    vertical-align: sub;\r\n    margin-left: 15px;\r\n\r\n    > img {\r\n      height: 22px;\r\n    }\r\n  }\r\n}\r\n\r\n.center-board-row {\r\n  padding: 12px 12px 15px 12px;\r\n  box-sizing: border-box;\r\n\r\n  & > .el-form {\r\n    // 69 = 12+15+42\r\n    height: calc(100vh - 69px);\r\n  }\r\n}\r\n\r\n.drawing-board {\r\n  height: 100%;\r\n  position: relative;\r\n\r\n  .components-body {\r\n    padding: 0;\r\n    margin: 0;\r\n    font-size: 0;\r\n  }\r\n\r\n  .sortable-ghost {\r\n    position: relative;\r\n    display: block;\r\n    overflow: hidden;\r\n\r\n    &::before {\r\n      content: \" \";\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      height: 3px;\r\n      background: rgb(89, 89, 223);\r\n      z-index: 2;\r\n    }\r\n  }\r\n\r\n  .components-item.sortable-ghost {\r\n    width: 100%;\r\n    height: 60px;\r\n    background-color: $selectedColor;\r\n  }\r\n\r\n  .active-from-item {\r\n    & > .el-form-item {\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n\r\n    & > .drawing-item-copy, & > .drawing-item-delete {\r\n      display: initial;\r\n    }\r\n\r\n    & > .component-name {\r\n      color: $lighterBlue;\r\n    }\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.drawing-item {\r\n  position: relative;\r\n  cursor: move;\r\n\r\n  &.unfocus-bordered:not(.activeFromItem) > div:first-child {\r\n    border: 1px dashed #ccc;\r\n  }\r\n\r\n  .el-form-item {\r\n    padding: 12px 10px;\r\n  }\r\n}\r\n\r\n.drawing-row-item {\r\n  position: relative;\r\n  cursor: move;\r\n  box-sizing: border-box;\r\n  border: 1px dashed #ccc;\r\n  border-radius: 3px;\r\n  padding: 0 2px;\r\n  margin-bottom: 15px;\r\n\r\n  .drawing-row-item {\r\n    margin-bottom: 2px;\r\n  }\r\n\r\n  .el-col {\r\n    margin-top: 22px;\r\n  }\r\n\r\n  .el-form-item {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .drag-wrapper {\r\n    min-height: 80px;\r\n  }\r\n\r\n  &.active-from-item {\r\n    border: 1px dashed $lighterBlue;\r\n  }\r\n\r\n  .component-name {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    font-size: 12px;\r\n    color: #bbb;\r\n    display: inline-block;\r\n    padding: 0 6px;\r\n  }\r\n}\r\n\r\n.drawing-item, .drawing-row-item {\r\n  &:hover {\r\n    & > .el-form-item {\r\n      background: $selectedColor;\r\n      border-radius: 6px;\r\n    }\r\n\r\n    & > .drawing-item-copy, & > .drawing-item-delete {\r\n      display: initial;\r\n    }\r\n  }\r\n\r\n  & > .drawing-item-copy, & > .drawing-item-delete {\r\n    display: none;\r\n    position: absolute;\r\n    top: -10px;\r\n    width: 22px;\r\n    height: 22px;\r\n    line-height: 22px;\r\n    text-align: center;\r\n    border-radius: 50%;\r\n    font-size: 12px;\r\n    border: 1px solid;\r\n    cursor: pointer;\r\n    z-index: 1;\r\n  }\r\n\r\n  & > .drawing-item-copy {\r\n    right: 56px;\r\n    border-color: $lighterBlue;\r\n    color: $lighterBlue;\r\n    background: #fff;\r\n\r\n    &:hover {\r\n      background: $lighterBlue;\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  & > .drawing-item-delete {\r\n    right: 24px;\r\n    border-color: #F56C6C;\r\n    color: #F56C6C;\r\n    background: #fff;\r\n\r\n    &:hover {\r\n      background: #F56C6C;\r\n      color: #fff;\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAsIA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,UAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,OAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,WAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,OAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAN,OAAA;AACA,IAAAO,KAAA,GAAAP,OAAA;AACA,IAAAQ,GAAA,GAAAR,OAAA;AACA,IAAAS,IAAA,GAAAT,OAAA;AACA,IAAAU,eAAA,GAAAX,sBAAA,CAAAC,OAAA;AACA,IAAAW,KAAA,GAAAZ,sBAAA,CAAAC,OAAA;AACA,IAAAY,eAAA,GAAAb,sBAAA,CAAAC,OAAA;AACA,IAAAa,cAAA,GAAAd,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAc,WAAA;AACA,IAAAC,cAAA;AAAA,IAAAC,QAAA,GAEA;EACAC,UAAA;IACAC,SAAA,EAAAA,qBAAA;IACAC,MAAA,EAAAA,eAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,cAAA,EAAAA,uBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,IAAA,EAAAA,aAAA;MACAC,QAAA;MACAC,QAAA,EAAAA,gBAAA;MACAC,eAAA,EAAAA,uBAAA;MACAC,gBAAA,EAAAA,wBAAA;MACAC,gBAAA,EAAAA,wBAAA;MACAC,UAAA;MACAC,WAAA,EAAAC,uBAAA;MACAC,WAAA;MACAC,QAAA,EAAAF,uBAAA,IAAAG,MAAA;MACAC,aAAA;MACAC,QAAA;MACAC,aAAA;MACAC,YAAA;MACAC,YAAA;MACAC,UAAA,EAAAT,uBAAA;IACA;EACA;EACAU,OAAA,WAAAA,QAAA;IACA;IACAC,QAAA,CAAAC,IAAA,CAAAC,MAAA,aAAAC,KAAA;MACAA,KAAA,CAAAC,cAAA;MACAD,KAAA,CAAAE,eAAA;IACA;EACA;EACAC,KAAA;IACA;IACA,6BAAAC,gBAAAC,GAAA,EAAAC,MAAA;MACA,IACA,KAAAX,UAAA,CAAAY,WAAA,IAAAC,SAAA,IACA,MAAAb,UAAA,CAAAc,GAAA,IACAzC,WAAA,SAAAoB,QAAA,EACA;QACA;MACA;MACA,KAAAO,UAAA,CAAAY,WAAA,QAAAZ,UAAA,CAAAY,WAAA,CAAAG,OAAA,CAAAJ,MAAA,QAAAD,GAAA;IACA;IACAjB,QAAA;MACAuB,OAAA,WAAAA,QAAAN,GAAA;QACArC,WAAA,GAAAqC,GAAA;MACA;MACAO,SAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,SAAA,OAAAC,kBAAA;MACAC,IAAA,WAAAA,KAAAC,OAAA;QACA,IAAAC,OAAA,GAAAL,KAAA,CAAAM,YAAA;QACAN,KAAA,CAAAO,OAAA;UACAC,KAAA;UACAC,OAAA;UACAC,IAAA;QACA;QACA,OAAAL,OAAA;MACA;IACA;IACAJ,SAAA,CAAAU,EAAA,oBAAAC,CAAA;MACAZ,KAAA,CAAAa,QAAA,CAAAC,KAAA;IACA;EACA;EACAC,OAAA;IACAC,cAAA,WAAAA,eAAAC,OAAA;MACA,KAAApC,UAAA,GAAAoC,OAAA;MACA,KAAA3C,QAAA,GAAA2C,OAAA,CAAA1C,MAAA;IACA;IACA2C,KAAA,WAAAA,MAAAC,GAAA,EAAAC,CAAA;MACA,IAAAD,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAG,EAAA;QACA,KAAAzC,UAAA,GAAA1B,cAAA;QACA,KAAAmB,QAAA,QAAAT,QAAA;MACA;IACA;IACA0D,YAAA,WAAAA,aAAAC,IAAA;MACA,IAAAC,KAAA,QAAAC,cAAA,CAAAF,IAAA;MACA,KAAArD,WAAA,CAAAwD,IAAA,CAAAF,KAAA;MACA,KAAAT,cAAA,CAAAS,KAAA;IACA;IACAC,cAAA,WAAAA,eAAAE,MAAA;MACA,IAAAH,KAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAH,MAAA;MACAH,KAAA,CAAAlD,MAAA,UAAAV,QAAA;MACA4D,KAAA,CAAAO,IAAA,GAAAlE,gBAAA,CAAAkE,IAAA;MACAP,KAAA,CAAAQ,SAAA,QAAAC,IAAA;MACA,KAAAT,KAAA,CAAAU,MAAA,EAAAV,KAAA,CAAAU,MAAA;MACA,IAAAV,KAAA,CAAAU,MAAA;QACAV,KAAA,CAAAW,MAAA,WAAAC,MAAA,MAAAxE,QAAA;QACA4D,KAAA,CAAAhC,WAAA,IAAAC,SAAA,KAAA+B,KAAA,CAAAhC,WAAA,IAAAgC,KAAA,CAAAa,KAAA;QACAnF,cAAA,GAAAsE,KAAA;MACA,WAAAA,KAAA,CAAAU,MAAA;QACA,OAAAV,KAAA,CAAAa,KAAA;QACAb,KAAA,CAAAc,aAAA,SAAAF,MAAA,MAAAxE,QAAA;QACA4D,KAAA,CAAAe,MAAA,QAAA1E,QAAA,CAAA0E,MAAA;QACArF,cAAA,GAAAsE,KAAA;MACA;MACA,OAAAtE,cAAA;IACA;IACAsF,gBAAA,WAAAA,iBAAA;MACA,KAAAhE,QAAA,OAAAiE,cAAA,CAAAC,OAAA;QACAC,MAAA,EAAAf,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,MAAA5D,WAAA;MAAA,GACA,KAAAL,QAAA,CACA;IACA;IACA+E,QAAA,WAAAA,SAAAlF,IAAA;MACA,IAAAmF,IAAA,eAAAT,MAAA,KAAAU,gBAAA,OAAAC,aAAA;MACA,KAAArE,YAAA,GAAAhB,IAAA;MACAmF,IAAA,IAAAA,IAAA,CAAAnF,IAAA;IACA;IACAsF,OAAA,WAAAA,QAAAtF,IAAA;MACA,KAAA8E,gBAAA;MACA,KAAAjE,aAAA;IACA;IACA0E,YAAA,WAAAA,aAAAvF,IAAA;MACA,IAAA0C,OAAA,QAAAC,YAAA;MACA,IAAA6C,IAAA,OAAAC,IAAA,EAAA/C,OAAA;QAAAK,IAAA;MAAA;MACA,KAAA2C,SAAA,CAAAC,MAAA,CAAAH,IAAA,EAAAxF,IAAA,CAAA4F,QAAA;IACA;IACAC,QAAA,WAAAA,SAAA7F,IAAA;MACAoB,QAAA,CAAA0E,cAAA,aAAAC,KAAA;IACA;IACAC,KAAA,WAAAA,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,QAAA;QAAAnD,IAAA;QAAAoD,WAAA;MAAA,GAAAC,IAAA,CACA;QACAH,MAAA,CAAAzF,WAAA;MACA,CACA;IACA;IACA6F,eAAA,WAAAA,gBAAAxC,IAAA,EAAAyC,MAAA;MACA,IAAAxC,KAAA,GAAAI,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,SAAA,CAAAP,IAAA;MACAC,KAAA,QAAAyC,cAAA,CAAAzC,KAAA;MACAwC,MAAA,CAAAtC,IAAA,CAAAF,KAAA;MACA,KAAAT,cAAA,CAAAS,KAAA;IACA;IACAyC,cAAA,WAAAA,eAAA1C,IAAA;MAAA,IAAA2C,MAAA;MACA3C,IAAA,CAAAjD,MAAA,UAAAV,QAAA;MACA2D,IAAA,CAAAS,SAAA,QAAAC,IAAA;MACA,IAAAV,IAAA,CAAAW,MAAA;QACAX,IAAA,CAAAY,MAAA,WAAAC,MAAA,MAAAxE,QAAA;MACA,WAAA2D,IAAA,CAAAW,MAAA;QACAX,IAAA,CAAAe,aAAA,SAAAF,MAAA,MAAAxE,QAAA;MACA;MACA,IAAAuG,KAAA,CAAAC,OAAA,CAAA7C,IAAA,CAAA8C,QAAA;QACA9C,IAAA,CAAA8C,QAAA,GAAA9C,IAAA,CAAA8C,QAAA,CAAAC,GAAA,WAAAC,SAAA;UAAA,OAAAL,MAAA,CAAAD,cAAA,CAAAM,SAAA;QAAA;MACA;MACA,OAAAhD,IAAA;IACA;IACAiD,iBAAA,WAAAA,kBAAAC,KAAA,EAAAT,MAAA;MAAA,IAAAU,MAAA;MACAV,MAAA,CAAAW,MAAA,CAAAF,KAAA;MACA,KAAAG,SAAA;QACA,IAAAC,GAAA,GAAAH,MAAA,CAAAxG,WAAA,CAAA4G,MAAA;QACA,IAAAD,GAAA;UACAH,MAAA,CAAA3D,cAAA,CAAA2D,MAAA,CAAAxG,WAAA,CAAA2G,GAAA;QACA;MACA;IACA;IACAxE,YAAA,WAAAA,aAAA;MACA,IAAAI,IAAA,QAAA/B,YAAA,CAAA+B,IAAA;MACA,KAAA+B,gBAAA;MACA,IAAAuC,MAAA,OAAAC,eAAA,MAAAC,YAAA,OAAAzG,QAAA,EAAAiC,IAAA;MACA,IAAAyE,IAAA,OAAAC,iBAAA,MAAAC,gBAAA,OAAA5G,QAAA,EAAAiC,IAAA;MACA,IAAA4E,GAAA,OAAAC,cAAA,MAAAC,cAAA,OAAA/G,QAAA;MACA,OAAAgH,mBAAA,CAAAN,IAAA,CAAAA,IAAA,GAAAH,MAAA,GAAAM,GAAA,EAAAI,qBAAA,CAAAP,IAAA;IACA;IACAQ,QAAA,WAAAA,SAAA;MACA,KAAAjH,aAAA;MACA,KAAAE,YAAA;MACA,KAAAoE,aAAA;IACA;IACA4C,GAAA,WAAAA,IAAA;MACA,KAAAlH,aAAA;MACA,KAAAE,YAAA;MACA,KAAAoE,aAAA;IACA;IACA6C,IAAA,WAAAA,KAAA;MACA,KAAAnH,aAAA;MACA,KAAAE,YAAA;MACA,KAAAoE,aAAA;IACA;IACA8C,SAAA,WAAAA,UAAAC,MAAA;MAAA,IAAAC,MAAA;MACAD,MAAA,QAAArE,cAAA,CAAAqE,MAAA;MACAA,MAAA,CAAA3D,MAAA,QAAAvD,UAAA,CAAAuD,MAAA;MACA2D,MAAA,CAAAxH,MAAA,QAAAD,QAAA;MACAyH,MAAA,CAAA/D,IAAA,QAAAnD,UAAA,CAAAmD,IAAA;MACA,YAAAnD,UAAA,CAAAc,GAAA;MACA,YAAAd,UAAA,CAAAoH,OAAA;MACA,YAAApH,UAAA,CAAAE,QAAA;MACAmH,MAAA,CAAAC,IAAA,CAAAJ,MAAA,EAAAK,OAAA,WAAAC,GAAA;QACA,IAAAL,MAAA,CAAAnH,UAAA,CAAAwH,GAAA,KAAA3G,SAAA,IACA,IAAA4G,QAAA,CAAA3D,OAAA,EAAAqD,MAAA,CAAAnH,UAAA,CAAAwH,GAAA,UAAAC,QAAA,CAAA3D,OAAA,EAAAoD,MAAA,CAAAM,GAAA;UACAN,MAAA,CAAAM,GAAA,IAAAL,MAAA,CAAAnH,UAAA,CAAAwH,GAAA;QACA;MACA;MACA,KAAAxH,UAAA,GAAAkH,MAAA;MACA,KAAAQ,iBAAA,CAAAR,MAAA,OAAA5H,WAAA;IACA;IACAoI,iBAAA,WAAAA,kBAAAR,MAAA,EAAAS,IAAA;MAAA,IAAAC,MAAA;MACA,IAAA/B,KAAA,GAAA8B,IAAA,CAAAE,SAAA,WAAAlF,IAAA;QAAA,OAAAA,IAAA,CAAAjD,MAAA,IAAAkI,MAAA,CAAAnI,QAAA;MAAA;MACA,IAAAoG,KAAA;QACA8B,IAAA,CAAA5B,MAAA,CAAAF,KAAA,KAAAqB,MAAA;MACA;QACAS,IAAA,CAAAJ,OAAA,WAAA5E,IAAA;UACA,IAAA4C,KAAA,CAAAC,OAAA,CAAA7C,IAAA,CAAA8C,QAAA,GAAAmC,MAAA,CAAAF,iBAAA,CAAAR,MAAA,EAAAvE,IAAA,CAAA8C,QAAA;QACA;MACA;IACA;EACA;AACA;AAAAqC,OAAA,CAAAhE,OAAA,GAAAvF,QAAA"}]}