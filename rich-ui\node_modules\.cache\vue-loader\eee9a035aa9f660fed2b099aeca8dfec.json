{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\characteristics\\index.vue?vue&type=template&id=438031f5&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\characteristics\\index.vue", "mtime": 1754876882575}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}